package models

type PickupStation struct {
	Id      int32  `xorm:"not null pk autoincr INT(10)"`
	Name    string `xorm:"not null comment('自提点名称') VARCHAR(128)"`
	Address string `xorm:"not null comment('详细地址') VARCHAR(256)"`
	Lng     string `xorm:"not null comment('经度') VARCHAR(50)"`
	Lat     string `xorm:"not null comment('纬度') VARCHAR(50)"`
	Status  int32  `xorm:"not null default 0 comment('0禁用、1启用') TINYINT(3)"`
}

type PickupStationWithDistance struct {
	*PickupStation `xorm:"extends"`
	Distance       float64 // 距离米
}

syntax = "proto3";
package oc;

// @Desc    	订阅消息服务
service SubscribeMessageService {
  // @Desc    	发送消息
  rpc PushTemplate(PushTemplateRequest) returns (PushTemplateResponse) {}
}

message PushTemplateRequest {
  //用户ID
  string openId = 1;
  //订单编号
  string orderSn = 2;
  //模板ID
  string templateId = 3;
  //类型(1=>发送退款成功通知, 2=>发送退款失败通知, 3=>发送退款状态通知, 4=>推送尾款支付提醒通知)
  int32  pushType = 4;
  //备注
  string remarks = 5;
  //发送退款成功通知
  RefundSuccess refundSuccess = 6;
  //发送退款失败通知
  RefundFail refundFail = 7;
  //发送退款状态通知
  RefundStatus refundStatus = 8;
  //预售尾款支付通知
  PreSalePay preSalePay = 9;
  //主体:1-阿闻，2-极宠家，3-福码购
  int32 org_id = 10;
}

//通用返回
message PushTemplateResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//发送退款成功通知
message RefundSuccess {
  //退款ID
  string refundId = 1;
  //退款类型
  string refundType = 2;
  //退款金额
  string refundAmount = 3;
  //退款时间
  string refundTime = 4;
}
//发送退款失败通知
message RefundFail {
  //退款ID
  string refundId = 1;
  //退款类型
  string refundType = 2;
  //退款订单
  string refundSn = 3;
  //状态
  string status = 4;
  // 退款金额
  string refundAmount = 5;
}
//发送退款状态通知
message RefundStatus {
  //退款ID
  string refundId = 1;
  //状态
  string status = 2;
  //退款类型
  string refundType = 3;
}
message PreSalePay{
    //尾款支付开始时间
    string startTime =1;
    //尾款支付结束时间
    string endTime = 2;
    //温馨提醒
    string remarks = 3;
    //是否虚拟订单 0 否 1是
    int32 isVirtual = 4;

}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: igc/goods.proto

package igc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GoodsIdRequest struct {
	// 商品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 前端不需要传
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsIdRequest) Reset()         { *m = GoodsIdRequest{} }
func (m *GoodsIdRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsIdRequest) ProtoMessage()    {}
func (*GoodsIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{0}
}

func (m *GoodsIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsIdRequest.Unmarshal(m, b)
}
func (m *GoodsIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsIdRequest.Marshal(b, m, deterministic)
}
func (m *GoodsIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsIdRequest.Merge(m, src)
}
func (m *GoodsIdRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsIdRequest.Size(m)
}
func (m *GoodsIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsIdRequest proto.InternalMessageInfo

func (m *GoodsIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsIdRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type GoodsResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsResponse) Reset()         { *m = GoodsResponse{} }
func (m *GoodsResponse) String() string { return proto.CompactTextString(m) }
func (*GoodsResponse) ProtoMessage()    {}
func (*GoodsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{1}
}

func (m *GoodsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsResponse.Unmarshal(m, b)
}
func (m *GoodsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsResponse.Marshal(b, m, deterministic)
}
func (m *GoodsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsResponse.Merge(m, src)
}
func (m *GoodsResponse) XXX_Size() int {
	return xxx_messageInfo_GoodsResponse.Size(m)
}
func (m *GoodsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsResponse proto.InternalMessageInfo

func (m *GoodsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GoodsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GoodsListRequest struct {
	// 上架 '0'表示下架 '1'表示上架，其他表示不过滤
	Show string `protobuf:"bytes,1,opt,name=show,proto3" json:"show"`
	// 推荐 '0'不推荐 '1'表示推荐，其他表示不过滤
	Commend string `protobuf:"bytes,2,opt,name=commend,proto3" json:"commend"`
	// 类型 '1'实物'2'虚拟'3'优惠券，其他表示不过滤
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type"`
	// 名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	// 每页数量
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex int32 `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//商品类型 1第三方商品 2自有商品3门店卷 4商城券 5:爱心币
	GoodType             int32    `protobuf:"varint,7,opt,name=good_type,json=goodType,proto3" json:"good_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListRequest) Reset()         { *m = GoodsListRequest{} }
func (m *GoodsListRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsListRequest) ProtoMessage()    {}
func (*GoodsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{2}
}

func (m *GoodsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListRequest.Unmarshal(m, b)
}
func (m *GoodsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListRequest.Marshal(b, m, deterministic)
}
func (m *GoodsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListRequest.Merge(m, src)
}
func (m *GoodsListRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsListRequest.Size(m)
}
func (m *GoodsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListRequest proto.InternalMessageInfo

func (m *GoodsListRequest) GetShow() string {
	if m != nil {
		return m.Show
	}
	return ""
}

func (m *GoodsListRequest) GetCommend() string {
	if m != nil {
		return m.Commend
	}
	return ""
}

func (m *GoodsListRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *GoodsListRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GoodsListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GoodsListRequest) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

type GoodsListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*GoodsList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListResponse) Reset()         { *m = GoodsListResponse{} }
func (m *GoodsListResponse) String() string { return proto.CompactTextString(m) }
func (*GoodsListResponse) ProtoMessage()    {}
func (*GoodsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{3}
}

func (m *GoodsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListResponse.Unmarshal(m, b)
}
func (m *GoodsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListResponse.Marshal(b, m, deterministic)
}
func (m *GoodsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListResponse.Merge(m, src)
}
func (m *GoodsListResponse) XXX_Size() int {
	return xxx_messageInfo_GoodsListResponse.Size(m)
}
func (m *GoodsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListResponse proto.InternalMessageInfo

func (m *GoodsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GoodsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GoodsListResponse) GetData() []*GoodsList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GoodsListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GoodsDetailResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string       `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *GoodsDetail `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GoodsDetailResponse) Reset()         { *m = GoodsDetailResponse{} }
func (m *GoodsDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GoodsDetailResponse) ProtoMessage()    {}
func (*GoodsDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{4}
}

func (m *GoodsDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsDetailResponse.Unmarshal(m, b)
}
func (m *GoodsDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsDetailResponse.Marshal(b, m, deterministic)
}
func (m *GoodsDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsDetailResponse.Merge(m, src)
}
func (m *GoodsDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GoodsDetailResponse.Size(m)
}
func (m *GoodsDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsDetailResponse proto.InternalMessageInfo

func (m *GoodsDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GoodsDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GoodsDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GoodsDetailResponse) GetData() *GoodsDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type AWGoodsDetailResponse struct {
	Code    int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string       `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data    *GoodsDetail `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	//额外计算好的信息
	Extra                *AWGoodsDetailResponse_Extra `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AWGoodsDetailResponse) Reset()         { *m = AWGoodsDetailResponse{} }
func (m *AWGoodsDetailResponse) String() string { return proto.CompactTextString(m) }
func (*AWGoodsDetailResponse) ProtoMessage()    {}
func (*AWGoodsDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{5}
}

func (m *AWGoodsDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWGoodsDetailResponse.Unmarshal(m, b)
}
func (m *AWGoodsDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWGoodsDetailResponse.Marshal(b, m, deterministic)
}
func (m *AWGoodsDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWGoodsDetailResponse.Merge(m, src)
}
func (m *AWGoodsDetailResponse) XXX_Size() int {
	return xxx_messageInfo_AWGoodsDetailResponse.Size(m)
}
func (m *AWGoodsDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWGoodsDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWGoodsDetailResponse proto.InternalMessageInfo

func (m *AWGoodsDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWGoodsDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWGoodsDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *AWGoodsDetailResponse) GetData() *GoodsDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AWGoodsDetailResponse) GetExtra() *AWGoodsDetailResponse_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

type AWGoodsDetailResponse_Extra struct {
	//用户积分状态 0积分不足 1积分正常
	IntegralState int32 `protobuf:"varint,1,opt,name=integral_state,json=integralState,proto3" json:"integral_state"`
	//会员vip状态 0用户不是会员 1用户是会员
	MemberVipState int32 `protobuf:"varint,2,opt,name=member_vip_state,json=memberVipState,proto3" json:"member_vip_state"`
	//状态 0结束 1即将开始 2进行中 3库存不足
	GoodsState           int32    `protobuf:"varint,3,opt,name=goods_state,json=goodsState,proto3" json:"goods_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWGoodsDetailResponse_Extra) Reset()         { *m = AWGoodsDetailResponse_Extra{} }
func (m *AWGoodsDetailResponse_Extra) String() string { return proto.CompactTextString(m) }
func (*AWGoodsDetailResponse_Extra) ProtoMessage()    {}
func (*AWGoodsDetailResponse_Extra) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{5, 0}
}

func (m *AWGoodsDetailResponse_Extra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWGoodsDetailResponse_Extra.Unmarshal(m, b)
}
func (m *AWGoodsDetailResponse_Extra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWGoodsDetailResponse_Extra.Marshal(b, m, deterministic)
}
func (m *AWGoodsDetailResponse_Extra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWGoodsDetailResponse_Extra.Merge(m, src)
}
func (m *AWGoodsDetailResponse_Extra) XXX_Size() int {
	return xxx_messageInfo_AWGoodsDetailResponse_Extra.Size(m)
}
func (m *AWGoodsDetailResponse_Extra) XXX_DiscardUnknown() {
	xxx_messageInfo_AWGoodsDetailResponse_Extra.DiscardUnknown(m)
}

var xxx_messageInfo_AWGoodsDetailResponse_Extra proto.InternalMessageInfo

func (m *AWGoodsDetailResponse_Extra) GetIntegralState() int32 {
	if m != nil {
		return m.IntegralState
	}
	return 0
}

func (m *AWGoodsDetailResponse_Extra) GetMemberVipState() int32 {
	if m != nil {
		return m.MemberVipState
	}
	return 0
}

func (m *AWGoodsDetailResponse_Extra) GetGoodsState() int32 {
	if m != nil {
		return m.GoodsState
	}
	return 0
}

type GoodsList struct {
	// 积分礼品主键
	Id int32 `protobuf:"varint,11,opt,name=id,proto3" json:"id"`
	// 积分礼品名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 类型1实物2虚拟3优惠券
	Type int32 `protobuf:"varint,16,opt,name=type,proto3" json:"type"`
	// 积分礼品兑换所需积分
	Points int32 `protobuf:"varint,3,opt,name=points,proto3" json:"points"`
	// 积分礼品库存数
	Storage int32 `protobuf:"varint,6,opt,name=storage,proto3" json:"storage"`
	// 积分礼品上架 0表示下架 1表示上架
	Show int32 `protobuf:"varint,7,opt,name=show,proto3" json:"show"`
	// 积分礼品推荐 0不推荐 1表示推荐
	Commend int32 `protobuf:"varint,8,opt,name=commend,proto3" json:"commend"`
	// 售出数
	SaleNum int32 `protobuf:"varint,9,opt,name=sale_num,json=saleNum,proto3" json:"sale_num"`
	// 创建时间
	AddTime              string   `protobuf:"bytes,10,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsList) Reset()         { *m = GoodsList{} }
func (m *GoodsList) String() string { return proto.CompactTextString(m) }
func (*GoodsList) ProtoMessage()    {}
func (*GoodsList) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{6}
}

func (m *GoodsList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsList.Unmarshal(m, b)
}
func (m *GoodsList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsList.Marshal(b, m, deterministic)
}
func (m *GoodsList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsList.Merge(m, src)
}
func (m *GoodsList) XXX_Size() int {
	return xxx_messageInfo_GoodsList.Size(m)
}
func (m *GoodsList) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsList.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsList proto.InternalMessageInfo

func (m *GoodsList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsList) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GoodsList) GetPoints() int32 {
	if m != nil {
		return m.Points
	}
	return 0
}

func (m *GoodsList) GetStorage() int32 {
	if m != nil {
		return m.Storage
	}
	return 0
}

func (m *GoodsList) GetShow() int32 {
	if m != nil {
		return m.Show
	}
	return 0
}

func (m *GoodsList) GetCommend() int32 {
	if m != nil {
		return m.Commend
	}
	return 0
}

func (m *GoodsList) GetSaleNum() int32 {
	if m != nil {
		return m.SaleNum
	}
	return 0
}

func (m *GoodsList) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

type GoodsDetail struct {
	// 积分礼品主键
	Id int32 `protobuf:"varint,19,opt,name=id,proto3" json:"id"`
	// 积分礼品名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 积分礼品原价
	Price float32 `protobuf:"fixed32,2,opt,name=price,proto3" json:"price"`
	// 积分礼品兑换所需积分
	Points int32 `protobuf:"varint,3,opt,name=points,proto3" json:"points"`
	// 积分礼品默认封面图片
	Image string `protobuf:"bytes,4,opt,name=image,proto3" json:"image"`
	// 积分礼品货号
	Serial string `protobuf:"bytes,5,opt,name=serial,proto3" json:"serial"`
	// 积分礼品库存数
	Storage int32 `protobuf:"varint,6,opt,name=storage,proto3" json:"storage"`
	// 积分礼品上架 0表示下架 1表示上架
	Show int32 `protobuf:"varint,7,opt,name=show,proto3" json:"show"`
	// 积分礼品推荐 0不推荐 1推荐
	Commend int32 `protobuf:"varint,8,opt,name=commend,proto3" json:"commend"`
	// 积分礼品详细内容
	Body string `protobuf:"bytes,9,opt,name=body,proto3" json:"body"`
	// 是否限制每会员兑换数量 0不限制 1限制
	IsLimit int32 `protobuf:"varint,10,opt,name=is_limit,json=isLimit,proto3" json:"is_limit"`
	// 每会员限制兑换数量
	LimitNum int32 `protobuf:"varint,11,opt,name=limit_num,json=limitNum,proto3" json:"limit_num"`
	// 是否限制兑换时间 0为不限制 1为限制
	IsLimitTime int32 `protobuf:"varint,12,opt,name=is_limit_time,json=isLimitTime,proto3" json:"is_limit_time"`
	// 限制参与兑换的会员级别 0不限制 1限制
	LimitMgrade int32 `protobuf:"varint,13,opt,name=limit_mgrade,json=limitMgrade,proto3" json:"limit_mgrade"`
	// 兑换开始时间
	StartTime string `protobuf:"bytes,14,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 兑换结束时间
	EndTime string `protobuf:"bytes,15,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 类型1实物2虚拟3优惠券 当type=2时good_type=0
	Type int32 `protobuf:"varint,16,opt,name=type,proto3" json:"type"`
	// 虚拟商品sku或scrm优惠券IDgo
	ScrmInfo int64 `protobuf:"varint,17,opt,name=scrm_info,json=scrmInfo,proto3" json:"scrm_info"`
	// 商品类型 1第三方商品(属type=1) 2自有商品(属type=1) 3门店卷(属type=3) 4:商城券（属type=4）
	GoodType int32 `protobuf:"varint,18,opt,name=good_type,json=goodType,proto3" json:"good_type"`
	//兑换数量
	SaleNum int32 `protobuf:"varint,20,opt,name=sale_num,json=saleNum,proto3" json:"sale_num"`
	// 会员等级数组
	UserLevelIds []int32 `protobuf:"varint,21,rep,packed,name=user_level_ids,json=userLevelIds,proto3" json:"user_level_ids"`
	// 新增的爱心币的数量
	CoinNum int32 `protobuf:"varint,22,opt,name=coin_num,json=coinNum,proto3" json:"coin_num"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,23,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsDetail) Reset()         { *m = GoodsDetail{} }
func (m *GoodsDetail) String() string { return proto.CompactTextString(m) }
func (*GoodsDetail) ProtoMessage()    {}
func (*GoodsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{7}
}

func (m *GoodsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsDetail.Unmarshal(m, b)
}
func (m *GoodsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsDetail.Marshal(b, m, deterministic)
}
func (m *GoodsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsDetail.Merge(m, src)
}
func (m *GoodsDetail) XXX_Size() int {
	return xxx_messageInfo_GoodsDetail.Size(m)
}
func (m *GoodsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsDetail proto.InternalMessageInfo

func (m *GoodsDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsDetail) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GoodsDetail) GetPoints() int32 {
	if m != nil {
		return m.Points
	}
	return 0
}

func (m *GoodsDetail) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GoodsDetail) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

func (m *GoodsDetail) GetStorage() int32 {
	if m != nil {
		return m.Storage
	}
	return 0
}

func (m *GoodsDetail) GetShow() int32 {
	if m != nil {
		return m.Show
	}
	return 0
}

func (m *GoodsDetail) GetCommend() int32 {
	if m != nil {
		return m.Commend
	}
	return 0
}

func (m *GoodsDetail) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *GoodsDetail) GetIsLimit() int32 {
	if m != nil {
		return m.IsLimit
	}
	return 0
}

func (m *GoodsDetail) GetLimitNum() int32 {
	if m != nil {
		return m.LimitNum
	}
	return 0
}

func (m *GoodsDetail) GetIsLimitTime() int32 {
	if m != nil {
		return m.IsLimitTime
	}
	return 0
}

func (m *GoodsDetail) GetLimitMgrade() int32 {
	if m != nil {
		return m.LimitMgrade
	}
	return 0
}

func (m *GoodsDetail) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GoodsDetail) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GoodsDetail) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GoodsDetail) GetScrmInfo() int64 {
	if m != nil {
		return m.ScrmInfo
	}
	return 0
}

func (m *GoodsDetail) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

func (m *GoodsDetail) GetSaleNum() int32 {
	if m != nil {
		return m.SaleNum
	}
	return 0
}

func (m *GoodsDetail) GetUserLevelIds() []int32 {
	if m != nil {
		return m.UserLevelIds
	}
	return nil
}

func (m *GoodsDetail) GetCoinNum() int32 {
	if m != nil {
		return m.CoinNum
	}
	return 0
}

func (m *GoodsDetail) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GoodsPatchRequest struct {
	// 商品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 上架 '0'表示下架 '1'表示上架，不更新不要传
	Show *wrappers.Int32Value `protobuf:"bytes,2,opt,name=show,proto3" json:"show"`
	// 推荐 '0'不推荐 '1'表示推荐，不更新不要传
	Commend              *wrappers.Int32Value `protobuf:"bytes,3,opt,name=commend,proto3" json:"commend"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GoodsPatchRequest) Reset()         { *m = GoodsPatchRequest{} }
func (m *GoodsPatchRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsPatchRequest) ProtoMessage()    {}
func (*GoodsPatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{8}
}

func (m *GoodsPatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsPatchRequest.Unmarshal(m, b)
}
func (m *GoodsPatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsPatchRequest.Marshal(b, m, deterministic)
}
func (m *GoodsPatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsPatchRequest.Merge(m, src)
}
func (m *GoodsPatchRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsPatchRequest.Size(m)
}
func (m *GoodsPatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsPatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsPatchRequest proto.InternalMessageInfo

func (m *GoodsPatchRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsPatchRequest) GetShow() *wrappers.Int32Value {
	if m != nil {
		return m.Show
	}
	return nil
}

func (m *GoodsPatchRequest) GetCommend() *wrappers.Int32Value {
	if m != nil {
		return m.Commend
	}
	return nil
}

type AWGoodsListRequest struct {
	// 排序类型 0最新 1积分从低到高 2积分从高到低
	Sort string `protobuf:"bytes,1,opt,name=sort,proto3" json:"sort"`
	// 推荐 '0'不推荐 '1'表示推荐
	Commend string `protobuf:"bytes,2,opt,name=commend,proto3" json:"commend"`
	// 兑换类型 0全部兑换 1我能兑换
	ShowType string `protobuf:"bytes,3,opt,name=show_type,json=showType,proto3" json:"show_type"`
	// 关键词查询
	Keyword string `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword"`
	//积分区间开始
	PointsStart string `protobuf:"bytes,5,opt,name=points_start,json=pointsStart,proto3" json:"points_start"`
	//积分区间结束
	PointsEnd string `protobuf:"bytes,6,opt,name=points_end,json=pointsEnd,proto3" json:"points_end"`
	// 每页数量
	PageSize int32 `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex int32 `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 前端不需要传
	ScrmId string `protobuf:"bytes,9,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//商品类型 1第三方商品 2自有商品3门店卷 4商城券 5:爱心币
	GoodType             int32    `protobuf:"varint,10,opt,name=good_type,json=goodType,proto3" json:"good_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWGoodsListRequest) Reset()         { *m = AWGoodsListRequest{} }
func (m *AWGoodsListRequest) String() string { return proto.CompactTextString(m) }
func (*AWGoodsListRequest) ProtoMessage()    {}
func (*AWGoodsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{9}
}

func (m *AWGoodsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWGoodsListRequest.Unmarshal(m, b)
}
func (m *AWGoodsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWGoodsListRequest.Marshal(b, m, deterministic)
}
func (m *AWGoodsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWGoodsListRequest.Merge(m, src)
}
func (m *AWGoodsListRequest) XXX_Size() int {
	return xxx_messageInfo_AWGoodsListRequest.Size(m)
}
func (m *AWGoodsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AWGoodsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AWGoodsListRequest proto.InternalMessageInfo

func (m *AWGoodsListRequest) GetSort() string {
	if m != nil {
		return m.Sort
	}
	return ""
}

func (m *AWGoodsListRequest) GetCommend() string {
	if m != nil {
		return m.Commend
	}
	return ""
}

func (m *AWGoodsListRequest) GetShowType() string {
	if m != nil {
		return m.ShowType
	}
	return ""
}

func (m *AWGoodsListRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *AWGoodsListRequest) GetPointsStart() string {
	if m != nil {
		return m.PointsStart
	}
	return ""
}

func (m *AWGoodsListRequest) GetPointsEnd() string {
	if m != nil {
		return m.PointsEnd
	}
	return ""
}

func (m *AWGoodsListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *AWGoodsListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *AWGoodsListRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *AWGoodsListRequest) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

type AWGoodsListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总数
	Total                int32          `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*GoodsDetail `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AWGoodsListResponse) Reset()         { *m = AWGoodsListResponse{} }
func (m *AWGoodsListResponse) String() string { return proto.CompactTextString(m) }
func (*AWGoodsListResponse) ProtoMessage()    {}
func (*AWGoodsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_03f944a24548cc29, []int{10}
}

func (m *AWGoodsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWGoodsListResponse.Unmarshal(m, b)
}
func (m *AWGoodsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWGoodsListResponse.Marshal(b, m, deterministic)
}
func (m *AWGoodsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWGoodsListResponse.Merge(m, src)
}
func (m *AWGoodsListResponse) XXX_Size() int {
	return xxx_messageInfo_AWGoodsListResponse.Size(m)
}
func (m *AWGoodsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWGoodsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWGoodsListResponse proto.InternalMessageInfo

func (m *AWGoodsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWGoodsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWGoodsListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *AWGoodsListResponse) GetData() []*GoodsDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*GoodsIdRequest)(nil), "proto.GoodsIdRequest")
	proto.RegisterType((*GoodsResponse)(nil), "proto.GoodsResponse")
	proto.RegisterType((*GoodsListRequest)(nil), "proto.GoodsListRequest")
	proto.RegisterType((*GoodsListResponse)(nil), "proto.GoodsListResponse")
	proto.RegisterType((*GoodsDetailResponse)(nil), "proto.GoodsDetailResponse")
	proto.RegisterType((*AWGoodsDetailResponse)(nil), "proto.AWGoodsDetailResponse")
	proto.RegisterType((*AWGoodsDetailResponse_Extra)(nil), "proto.AWGoodsDetailResponse.Extra")
	proto.RegisterType((*GoodsList)(nil), "proto.GoodsList")
	proto.RegisterType((*GoodsDetail)(nil), "proto.GoodsDetail")
	proto.RegisterType((*GoodsPatchRequest)(nil), "proto.GoodsPatchRequest")
	proto.RegisterType((*AWGoodsListRequest)(nil), "proto.AWGoodsListRequest")
	proto.RegisterType((*AWGoodsListResponse)(nil), "proto.AWGoodsListResponse")
}

func init() { proto.RegisterFile("igc/goods.proto", fileDescriptor_03f944a24548cc29) }

var fileDescriptor_03f944a24548cc29 = []byte{
	// 1053 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xcd, 0x6a, 0x23, 0x47,
	0x10, 0x5e, 0x69, 0x34, 0xfa, 0x29, 0xad, 0xb5, 0xde, 0x5e, 0x79, 0x3d, 0x96, 0xf3, 0xe3, 0x0c,
	0x9b, 0xa0, 0x93, 0x0c, 0x36, 0x81, 0x2c, 0x61, 0x0f, 0x0e, 0x59, 0x82, 0xc0, 0x09, 0x61, 0xbc,
	0xac, 0x21, 0x97, 0xa1, 0xad, 0x69, 0x8f, 0x9b, 0xcc, 0x4c, 0x4f, 0xba, 0x5b, 0xf6, 0x7a, 0x21,
	0x87, 0xdc, 0x02, 0x79, 0x96, 0xdc, 0x73, 0xcd, 0x63, 0xe4, 0x9c, 0x17, 0x09, 0x5d, 0xdd, 0x23,
	0x8f, 0x6c, 0x59, 0x01, 0x13, 0xc8, 0x49, 0x53, 0x5f, 0x7f, 0xd5, 0x5d, 0x5d, 0x55, 0x5d, 0x9f,
	0xe0, 0x09, 0x4f, 0x67, 0xfb, 0xa9, 0x10, 0x89, 0x9a, 0x94, 0x52, 0x68, 0x41, 0x7c, 0xfc, 0x19,
	0x7d, 0x94, 0x0a, 0x91, 0x66, 0x6c, 0x1f, 0xad, 0xb3, 0xf9, 0xf9, 0xfe, 0x95, 0xa4, 0x65, 0xc9,
	0xa4, 0xa3, 0x85, 0x2f, 0x61, 0xf0, 0x8d, 0xf1, 0x9a, 0x26, 0x11, 0xfb, 0x69, 0xce, 0x94, 0x26,
	0x03, 0x68, 0xf2, 0x24, 0x68, 0xec, 0x35, 0xc6, 0x7e, 0xd4, 0xe4, 0x09, 0xd9, 0x86, 0x8e, 0x9a,
	0xc9, 0x3c, 0xe6, 0x49, 0xd0, 0xdc, 0x6b, 0x8c, 0x7b, 0x51, 0xdb, 0x98, 0xd3, 0x24, 0x7c, 0x05,
	0x1b, 0xe8, 0x1a, 0x31, 0x55, 0x8a, 0x42, 0x31, 0x42, 0xa0, 0x35, 0x13, 0x09, 0x73, 0xbe, 0xf8,
	0x4d, 0x02, 0xe8, 0xe4, 0x4c, 0x29, 0x9a, 0x32, 0xe7, 0x5d, 0x99, 0xe1, 0x9f, 0x0d, 0xd8, 0x44,
	0xff, 0x63, 0xae, 0x74, 0x75, 0x38, 0x81, 0x96, 0xba, 0x10, 0x57, 0xb8, 0x45, 0x2f, 0xc2, 0x6f,
	0xb3, 0xc5, 0x4c, 0xe4, 0x39, 0x2b, 0xaa, 0x00, 0x2a, 0xd3, 0xb0, 0xf5, 0x75, 0xc9, 0x02, 0xcf,
	0xb2, 0xcd, 0xb7, 0xc1, 0x0a, 0x9a, 0xb3, 0xa0, 0x65, 0x31, 0xf3, 0x4d, 0x76, 0xa1, 0x57, 0xd2,
	0x94, 0xc5, 0x8a, 0xbf, 0x67, 0x81, 0x8f, 0xd1, 0x75, 0x0d, 0x70, 0xc2, 0xdf, 0x33, 0xf2, 0x21,
	0x00, 0x2e, 0xf2, 0x22, 0x61, 0xef, 0x82, 0x36, 0xae, 0x22, 0x7d, 0x6a, 0x00, 0xe3, 0x6b, 0xd2,
	0x1a, 0xe3, 0x41, 0x1d, 0xeb, 0x6b, 0x80, 0x37, 0xd7, 0x25, 0x0b, 0x7f, 0x86, 0xa7, 0xb5, 0x2b,
	0x3c, 0x24, 0x0d, 0xe4, 0x05, 0xb4, 0x12, 0xaa, 0x69, 0xe0, 0xed, 0x79, 0xe3, 0xfe, 0xc1, 0xa6,
	0x2d, 0xcb, 0xe4, 0x66, 0x57, 0x5c, 0x25, 0x43, 0xf0, 0xb5, 0xd0, 0x34, 0xc3, 0x6b, 0xf9, 0x91,
	0x35, 0xc2, 0x5f, 0x1a, 0xf0, 0x0c, 0x99, 0x5f, 0x33, 0x4d, 0x79, 0xf6, 0xc0, 0x08, 0x86, 0xe0,
	0x33, 0x29, 0x85, 0x74, 0x69, 0xb4, 0x06, 0xf9, 0xcc, 0xc5, 0x65, 0x0e, 0xec, 0x1f, 0x90, 0x7a,
	0x5c, 0xee, 0x34, 0x5c, 0x0f, 0xff, 0x68, 0xc2, 0xd6, 0xd1, 0xe9, 0xff, 0x1c, 0x05, 0xf9, 0x02,
	0x7c, 0xf6, 0x4e, 0x4b, 0x8a, 0xd5, 0xed, 0x1f, 0x84, 0x8e, 0xb8, 0x32, 0xb0, 0xc9, 0x6b, 0xc3,
	0x8c, 0xac, 0xc3, 0xe8, 0x1a, 0x7c, 0xb4, 0xc9, 0xa7, 0x30, 0xe0, 0x85, 0x66, 0xa9, 0xa4, 0x59,
	0xac, 0x34, 0xd5, 0x55, 0xe0, 0x1b, 0x15, 0x7a, 0x62, 0x40, 0x32, 0x86, 0xcd, 0x9c, 0xe5, 0x67,
	0x4c, 0xc6, 0x97, 0xbc, 0x74, 0xc4, 0x26, 0x12, 0x07, 0x16, 0x7f, 0xcb, 0x4b, 0xcb, 0xfc, 0x18,
	0xfa, 0xf8, 0x20, 0x1d, 0xc9, 0x43, 0x12, 0x20, 0x84, 0x84, 0xf0, 0xaf, 0x06, 0xf4, 0x16, 0x85,
	0x76, 0xef, 0xae, 0xbf, 0x78, 0x77, 0x55, 0x23, 0x37, 0x6a, 0x8d, 0x5c, 0x35, 0xfc, 0xa6, 0x4d,
	0x29, 0x36, 0xfc, 0x73, 0x68, 0x97, 0x82, 0x17, 0x5a, 0xb9, 0x13, 0x9c, 0x65, 0x52, 0xad, 0xb4,
	0x90, 0x26, 0xd5, 0xb6, 0xa9, 0x2b, 0x73, 0xf1, 0xc8, 0x6c, 0x37, 0xdf, 0x79, 0x64, 0x5d, 0xcb,
	0xae, 0x1e, 0xd9, 0x0e, 0x74, 0x15, 0xcd, 0x58, 0x5c, 0xcc, 0xf3, 0xa0, 0xe7, 0x36, 0xa2, 0x19,
	0xfb, 0x6e, 0x9e, 0x9b, 0x25, 0x9a, 0x24, 0xb1, 0xe6, 0x39, 0x0b, 0xc0, 0x96, 0x93, 0x26, 0xc9,
	0x1b, 0x9e, 0xb3, 0xf0, 0xef, 0x16, 0xf4, 0x6b, 0xb9, 0x77, 0xb7, 0x7b, 0xb6, 0xf6, 0x76, 0x43,
	0xf0, 0x4b, 0xc9, 0x67, 0x36, 0x9f, 0xcd, 0xc8, 0x1a, 0xf7, 0xde, 0x6f, 0x08, 0x3e, 0xcf, 0xcd,
	0xed, 0xec, 0x4b, 0xb7, 0x86, 0x61, 0x2b, 0x26, 0x39, 0xcd, 0xb0, 0x13, 0xcc, 0xb0, 0x42, 0xeb,
	0x3f, 0xcb, 0x06, 0x81, 0xd6, 0x99, 0x48, 0xae, 0x31, 0x13, 0xbd, 0x08, 0xbf, 0x4d, 0x1a, 0xb8,
	0x8a, 0x33, 0x9e, 0x73, 0x8d, 0x69, 0xf0, 0xa3, 0x0e, 0x57, 0xc7, 0xc6, 0x34, 0xd3, 0x03, 0x71,
	0xcc, 0x9e, 0xad, 0x6d, 0x17, 0x01, 0x93, 0xbe, 0x10, 0x36, 0x2a, 0x3f, 0x9b, 0xc3, 0xc7, 0x48,
	0xe8, 0x3b, 0x67, 0x93, 0x47, 0xf2, 0x09, 0x3c, 0xb6, 0x84, 0x3c, 0x95, 0x34, 0x61, 0xc1, 0x86,
	0xa5, 0x20, 0xf6, 0x2d, 0x42, 0x66, 0x80, 0x29, 0x4d, 0xa5, 0xdb, 0x63, 0x80, 0x81, 0xf5, 0x10,
	0xc1, 0x1d, 0x76, 0xa0, 0xcb, 0x0a, 0x57, 0xa4, 0x27, 0xb6, 0x48, 0xac, 0xc0, 0x22, 0xad, 0x6c,
	0xa7, 0x5d, 0xe8, 0xd9, 0x71, 0x5f, 0x9c, 0x8b, 0xe0, 0xe9, 0x5e, 0x63, 0xec, 0x45, 0x5d, 0x1c,
	0xf8, 0xc5, 0xb9, 0x58, 0x1e, 0x86, 0x64, 0x79, 0x18, 0x2e, 0x35, 0xca, 0x70, 0xb9, 0x51, 0x5e,
	0xc0, 0x60, 0xae, 0x98, 0x8c, 0x33, 0x76, 0xc9, 0xb2, 0x98, 0x27, 0x2a, 0xd8, 0xda, 0xf3, 0xc6,
	0x7e, 0xf4, 0xd8, 0xa0, 0xc7, 0x06, 0x9c, 0x26, 0xca, 0x6c, 0x30, 0x13, 0xbc, 0xc0, 0x0d, 0x9e,
	0x57, 0x69, 0xe7, 0x85, 0xd9, 0x60, 0x0b, 0xda, 0x42, 0xa6, 0x46, 0x83, 0xb6, 0xed, 0x00, 0x14,
	0x32, 0x9d, 0x26, 0xe1, 0x6f, 0x0d, 0x37, 0x80, 0xbf, 0xa7, 0x7a, 0x76, 0x71, 0x9f, 0x82, 0xed,
	0xbb, 0x0a, 0x37, 0x71, 0x36, 0xec, 0x4e, 0xac, 0x24, 0x4e, 0x2a, 0x49, 0x9c, 0x4c, 0x0b, 0x7d,
	0x78, 0xf0, 0x96, 0x66, 0x73, 0xe6, 0xca, 0xff, 0xf9, 0x4d, 0xf9, 0xbd, 0x7f, 0xf7, 0xa9, 0xb8,
	0xe1, 0xef, 0x4d, 0x20, 0x6e, 0xe2, 0xdc, 0xd6, 0x34, 0x21, 0xf5, 0x42, 0xd3, 0x84, 0xd4, 0x6b,
	0x34, 0xcd, 0xe4, 0xff, 0x42, 0x5c, 0xc5, 0x35, 0x61, 0xeb, 0x1a, 0x00, 0x53, 0x1c, 0x40, 0xe7,
	0x47, 0x76, 0x7d, 0x25, 0x64, 0xe2, 0xba, 0xbe, 0x32, 0x4d, 0x9f, 0xd8, 0x77, 0x11, 0x63, 0xe5,
	0x5d, 0xf7, 0xf7, 0x2d, 0x76, 0x62, 0x20, 0x14, 0x3a, 0x4b, 0x31, 0xc7, 0xb6, 0x6d, 0x9f, 0x58,
	0xe4, 0xb5, 0x3d, 0xf8, 0x46, 0x24, 0x3b, 0x6b, 0x45, 0xb2, 0x7b, 0x5b, 0x24, 0x6b, 0xff, 0x11,
	0x7a, 0xf5, 0xff, 0x08, 0xcb, 0x0d, 0x03, 0xb7, 0xd4, 0xd3, 0xc8, 0xd7, 0x52, 0xbe, 0x1e, 0x2a,
	0x1c, 0x56, 0x1a, 0xbd, 0x9a, 0x34, 0xd6, 0x84, 0xc3, 0x5b, 0x27, 0x1c, 0x07, 0xbf, 0x7a, 0x30,
	0x9c, 0xba, 0x01, 0x8f, 0xab, 0x27, 0x4c, 0x5e, 0x9a, 0xb1, 0xf3, 0x25, 0xb4, 0x70, 0x2c, 0x6f,
	0xdf, 0x51, 0x64, 0x5b, 0xd6, 0x51, 0x70, 0x77, 0xc1, 0xc6, 0x1f, 0x3e, 0x22, 0xaf, 0xa0, 0xed,
	0xe6, 0xde, 0x56, 0x9d, 0xb5, 0xf8, 0x93, 0x35, 0x1a, 0xad, 0x08, 0xe8, 0xc6, 0xfd, 0x10, 0xfc,
	0x13, 0x2d, 0x24, 0x23, 0x2b, 0xe2, 0x1e, 0x0d, 0xeb, 0x58, 0xcd, 0xe9, 0x25, 0xf8, 0xf8, 0x0a,
	0xc8, 0x52, 0x60, 0xf5, 0x87, 0x71, 0xaf, 0xeb, 0x11, 0xb4, 0x8f, 0x4e, 0xf1, 0xb6, 0x3b, 0xcb,
	0xc2, 0x59, 0xbf, 0xef, 0x68, 0xd5, 0x52, 0x6d, 0x8b, 0xee, 0xd1, 0xe9, 0xfa, 0x3b, 0x7f, 0xb0,
	0x4e, 0x94, 0xc3, 0x47, 0x5f, 0xf9, 0x3f, 0x78, 0x3c, 0x9d, 0x9d, 0xb5, 0x91, 0x75, 0xf8, 0x4f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xf0, 0xa6, 0xbd, 0x3d, 0xd2, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IntegralGoodsServiceClient is the client API for IntegralGoodsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IntegralGoodsServiceClient interface {
	// 获取商品信息列表
	List(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListResponse, error)
	// 详情
	Detail(ctx context.Context, in *GoodsIdRequest, opts ...grpc.CallOption) (*GoodsDetailResponse, error)
	// 编辑或增加
	Store(ctx context.Context, in *GoodsDetail, opts ...grpc.CallOption) (*GoodsResponse, error)
	// 上下架、推荐 积分商品（部分更新）
	Patch(ctx context.Context, in *GoodsPatchRequest, opts ...grpc.CallOption) (*GoodsResponse, error)
	//以下是阿闻小程序接口
	// 积分商品列表
	AWList(ctx context.Context, in *AWGoodsListRequest, opts ...grpc.CallOption) (*AWGoodsListResponse, error)
	// 积分商品详情
	AWDetail(ctx context.Context, in *GoodsIdRequest, opts ...grpc.CallOption) (*AWGoodsDetailResponse, error)
}

type integralGoodsServiceClient struct {
	cc *grpc.ClientConn
}

func NewIntegralGoodsServiceClient(cc *grpc.ClientConn) IntegralGoodsServiceClient {
	return &integralGoodsServiceClient{cc}
}

func (c *integralGoodsServiceClient) List(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListResponse, error) {
	out := new(GoodsListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralGoodsServiceClient) Detail(ctx context.Context, in *GoodsIdRequest, opts ...grpc.CallOption) (*GoodsDetailResponse, error) {
	out := new(GoodsDetailResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/Detail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralGoodsServiceClient) Store(ctx context.Context, in *GoodsDetail, opts ...grpc.CallOption) (*GoodsResponse, error) {
	out := new(GoodsResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/Store", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralGoodsServiceClient) Patch(ctx context.Context, in *GoodsPatchRequest, opts ...grpc.CallOption) (*GoodsResponse, error) {
	out := new(GoodsResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/Patch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralGoodsServiceClient) AWList(ctx context.Context, in *AWGoodsListRequest, opts ...grpc.CallOption) (*AWGoodsListResponse, error) {
	out := new(AWGoodsListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/AWList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralGoodsServiceClient) AWDetail(ctx context.Context, in *GoodsIdRequest, opts ...grpc.CallOption) (*AWGoodsDetailResponse, error) {
	out := new(AWGoodsDetailResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralGoodsService/AWDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntegralGoodsServiceServer is the server API for IntegralGoodsService service.
type IntegralGoodsServiceServer interface {
	// 获取商品信息列表
	List(context.Context, *GoodsListRequest) (*GoodsListResponse, error)
	// 详情
	Detail(context.Context, *GoodsIdRequest) (*GoodsDetailResponse, error)
	// 编辑或增加
	Store(context.Context, *GoodsDetail) (*GoodsResponse, error)
	// 上下架、推荐 积分商品（部分更新）
	Patch(context.Context, *GoodsPatchRequest) (*GoodsResponse, error)
	//以下是阿闻小程序接口
	// 积分商品列表
	AWList(context.Context, *AWGoodsListRequest) (*AWGoodsListResponse, error)
	// 积分商品详情
	AWDetail(context.Context, *GoodsIdRequest) (*AWGoodsDetailResponse, error)
}

// UnimplementedIntegralGoodsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedIntegralGoodsServiceServer struct {
}

func (*UnimplementedIntegralGoodsServiceServer) List(ctx context.Context, req *GoodsListRequest) (*GoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedIntegralGoodsServiceServer) Detail(ctx context.Context, req *GoodsIdRequest) (*GoodsDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (*UnimplementedIntegralGoodsServiceServer) Store(ctx context.Context, req *GoodsDetail) (*GoodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Store not implemented")
}
func (*UnimplementedIntegralGoodsServiceServer) Patch(ctx context.Context, req *GoodsPatchRequest) (*GoodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Patch not implemented")
}
func (*UnimplementedIntegralGoodsServiceServer) AWList(ctx context.Context, req *AWGoodsListRequest) (*AWGoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWList not implemented")
}
func (*UnimplementedIntegralGoodsServiceServer) AWDetail(ctx context.Context, req *GoodsIdRequest) (*AWGoodsDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWDetail not implemented")
}

func RegisterIntegralGoodsServiceServer(s *grpc.Server, srv IntegralGoodsServiceServer) {
	s.RegisterService(&_IntegralGoodsService_serviceDesc, srv)
}

func _IntegralGoodsService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).List(ctx, req.(*GoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralGoodsService_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).Detail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/Detail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).Detail(ctx, req.(*GoodsIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralGoodsService_Store_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).Store(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/Store",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).Store(ctx, req.(*GoodsDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralGoodsService_Patch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsPatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).Patch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/Patch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).Patch(ctx, req.(*GoodsPatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralGoodsService_AWList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWGoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).AWList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/AWList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).AWList(ctx, req.(*AWGoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralGoodsService_AWDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralGoodsServiceServer).AWDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralGoodsService/AWDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralGoodsServiceServer).AWDetail(ctx, req.(*GoodsIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _IntegralGoodsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "proto.IntegralGoodsService",
	HandlerType: (*IntegralGoodsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _IntegralGoodsService_List_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _IntegralGoodsService_Detail_Handler,
		},
		{
			MethodName: "Store",
			Handler:    _IntegralGoodsService_Store_Handler,
		},
		{
			MethodName: "Patch",
			Handler:    _IntegralGoodsService_Patch_Handler,
		},
		{
			MethodName: "AWList",
			Handler:    _IntegralGoodsService_AWList_Handler,
		},
		{
			MethodName: "AWDetail",
			Handler:    _IntegralGoodsService_AWDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "igc/goods.proto",
}

package services

import (
	"context"
	"errors"
	"fmt"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"
	"strconv"

	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/gogf/gf/os/glog"
	"github.com/ppkg/kit"
	"github.com/xuri/excelize/v2"
)

const (
	inLimit = 2
	// 最多导入的行数
	maxRows = 10000
	// 导入成功标记
	successRemark = "导入成功"
)

// 导入处理器
type ImportHandler struct {
	RedisConn         *redis.Client
	DcDBSession       *xorm.Session
	UpetDBSession     *xorm.Session
	SheetName         string     // 当前工作表名
	Data              [][]string // 文件内容，除表头
	OrderSns          []string   // 所有的订单号
	VirtualCardIdSli  []int64
	Extends           map[string]*models.PhysicalVipCardOrders2
	File              *excelize.File
	Writer            *excelize.StreamWriter
	Expresses         []*models.UpetExpress // 所有快递公司
	Task              *models.VipCardTask
	SuccessCount      int32  // 成功计数
	FailCount         int32  //失败计数
	LogPrefix         string //日志前缀
	VirtualCardIdMap  map[int64]int32
	VipCardVirtualMap map[int64]int32
	VipCardOrderMap   map[int64]int32
}

// ExpressImport 物流导入
func (h *ImportHandler) ExpressImport() (err error) {
	lockKey := "order-center:physical-vip-card-order:express-import"
	if lock, err := h.RedisConn.SetNX(lockKey, 1, time.Second*60).Result(); err != nil {
		glog.Errorf("%sExpressImport====获取锁失败：%s", h.LogPrefix, err.Error())
		return errors.New("获取锁失败 " + err.Error())
	} else if !lock {
		glog.Errorf("%sExpressImport-有正在执行的导入任务，请稍后重试", h.LogPrefix)
		return errors.New("有正在执行的导入任务，请稍后重试")
	}
	defer h.RedisConn.Del(lockKey)

	err = h.initImportHandler()
	if err != nil {
		return
	}

	_ = h.UpetDBSession.Begin()

	h.batchDelivery(h.UpetDBSession)

	if err = h.Writer.Flush(); err != nil {
		glog.Errorf("%sExpressImport====写入文件失败：%s", h.LogPrefix, err.Error())
		_ = h.UpetDBSession.Rollback()
		return
	}

	// 上传excel文件
	if h.Task.Url, err = utils.UploadExcelToQiNiu1(h.File, ""); err != nil {
		glog.Errorf("%ssExpressImport====上传七牛云失败：%s", h.LogPrefix, err.Error())
		_ = h.UpetDBSession.Rollback()
		return
	}
	// 插入导入记录
	if _, err = h.DcDBSession.Table("vip_card_task").Insert(h.Task); err != nil {
		glog.Errorf("%ssExpressImport====插入导入记录失败：%s", h.LogPrefix, err.Error())
		_ = h.UpetDBSession.Rollback()
		return
	}

	if err = h.UpetDBSession.Commit(); err != nil {
		glog.Errorf("%ssExpressImport====提交事务失败：%s", h.LogPrefix, err.Error())
		return errors.New("提交事务出错 " + err.Error())
	}

	return
}

// 初始化导入数据
func (h *ImportHandler) initImportHandler() (err error) {

	if err = h.readFile(); err != nil {
		return
	} else if len(h.Data) == 0 {
		return errors.New("上传的文件不存在数据")
	}

	// 分段查询订单信息
	if err = h.sliceQueryExtends(); err != nil {
		return err
	}

	if err = h.UpetDBSession.Table("upet_express").Select("e_name,e_code_kdniao,id").Where("e_state = '1'").
		OrderBy("e_order asc,e_letter asc").Find(&h.Expresses); err != nil {
		glog.Errorf("%sinitImportHandler====查询快递公司出错：%s", h.LogPrefix, err.Error())
		err = errors.New("查询快递公司出错 " + err.Error())
		return
	}
	var VirtualCardIdSli []int64
	var VipCardOrderSli []int64
	var VipCardVirtualSli []int64

	if err = h.UpetDBSession.Table("upet_orders").Cols("virtual_card_id").In("virtual_card_id", h.VirtualCardIdSli).Find(&VirtualCardIdSli); err != nil {
		glog.Errorf("%s,查询已经存在的会员卡号失败 ", err.Error())
		err = errors.New("查询已经存在的会员卡号失败 " + err.Error())
		return
	}

	if err = h.UpetDBSession.Table("datacenter.vip_card_order").Cols("virtual_card_id").In("virtual_card_id", h.VirtualCardIdSli).Find(&VipCardOrderSli); err != nil {
		glog.Errorf("%s,查询数据失败 ", err.Error())
		err = errors.New("查询数据失败 " + err.Error())
		return
	}

	if err = h.UpetDBSession.Table("datacenter.vip_card_virtual").Cols("card_id").In("card_id", h.VirtualCardIdSli).Where("status=?", 0).Find(&VipCardVirtualSli); err != nil {
		glog.Errorf("%s,查询数据失败 ", err.Error())
		err = errors.New("查询数据失败 " + err.Error())
		return
	}
	for _, v := range VirtualCardIdSli {
		h.VirtualCardIdMap[v] = 1
	}
	for _, v := range VipCardOrderSli {
		h.VipCardOrderMap[v] = 1
	}
	for _, v := range VipCardVirtualSli {
		h.VipCardVirtualMap[v] = 1
	}

	// 初始化excel写入器
	h.Writer, _ = h.File.NewStreamWriter(h.SheetName)
	_ = h.Writer.SetRow("A1", []interface{}{
		"订单号", "卡号", "快递公司名称", "快递单号", "导入结果",
	})

	return
}

// 获取excel文件内容
func (h *ImportHandler) readFile() (err error) {

	h.SheetName = h.File.GetSheetName(0)
	rows, err := h.File.Rows(h.SheetName)
	if err != nil {
		glog.Errorf("%sreadFile====获取行数据出错：%s", h.LogPrefix, err.Error())
		return errors.New("获取行数据出错 " + err.Error())
	}

	for i := 0; rows.Next(); i++ {
		// 注意这里一定要读取行，不然内容会附加到下一行
		row, err := rows.Columns()
		// 表头不处理
		if i == 0 || i == 1 || i == 2 {
			continue
		}
		if i > maxRows {
			glog.Errorf("%sreadFile====最多导入%v行数据", h.LogPrefix, maxRows)
			return fmt.Errorf("最多导入%v行数据", maxRows)
		}
		if err != nil {
			glog.Errorf("%sreadFile====获取列数据出错:%s", h.LogPrefix, err.Error())
			return errors.New("获取列数据出错 " + err.Error())
		}
		for k, v := range row {
			row[k] = strings.TrimSpace(v)
		}

		// 无效的空数忽略
		if len(row) == 0 || len(row[0]) < 1 {
			continue
		}
		// 补全数据 todo？？
		row = append(row, "", "", "")[0:4]

		h.Data = append(h.Data, row)
		h.OrderSns = append(h.OrderSns, row[0])
		VirtualCardIdStr := strings.Trim(row[1], "FY")
		VirtualCardId, _ := strconv.Atoi(VirtualCardIdStr)
		h.VirtualCardIdSli = append(h.VirtualCardIdSli, int64(VirtualCardId))
	}

	_ = rows.Close()
	return
}

// 分段查询订单
func (h *ImportHandler) sliceQueryExtends() (err error) {
	total := len(h.OrderSns)
	h.Extends = make(map[string]*models.PhysicalVipCardOrders2)

	// 分段查询
	for i := 0; i < total; i = i + inLimit {
		end := i + inLimit
		if end > total {
			end = total
		}
		in := h.OrderSns[i:end]

		var t []*models.PhysicalVipCardOrders2

		if err = h.UpetDBSession.Table("upet_orders").Alias("a").
			Join("inner", "upet_order_goods d", "a.order_id=d.order_id").
			Join("inner", "upet_order_common b", "a.order_id=b.order_id").
			Join("left", "upet_express c", "b.shipping_express_id=c.id").
			In("order_sn", in).OrderBy("a.order_id asc").Find(&t); err != nil {
			glog.Errorf("%ssliceQueryExtends====查订单数据出错：%s", h.LogPrefix, err.Error())
			return errors.New("查订单出错 " + err.Error())
		}

		for _, s := range t {
			h.Extends[strconv.Itoa(int(s.UpetOrders.OrderSn))] = s
		}
	}

	return
}

// 处理发货
func (h *ImportHandler) batchDelivery(session *xorm.Session) {
	// 导入成功的记录，用于避免重复的导入
	successes := make(map[string]bool)

	// []string 订单号、卡号、快递公司名称、快递单号
	for i, data := range h.Data {
		r := []interface{}{data[0], data[1], data[2], data[3], successRemark}
		if successes[data[0]] {
			r[4] = "重复导入"
			h.FailCount++
		} else if _, ok := h.Extends[data[0]]; !ok {
			r[4] = "订单号不存在"
			h.FailCount++
		} else if h.Extends[data[0]].UpetOrders.OrderType != 21 {
			r[4] = "该订单不是健康会员卡实体卡订单"
			h.FailCount++
		} else if h.Extends[data[0]].UpetOrders.OrderFather == 0 {
			r[4] = "该订单号不是拆单后的订单号"
			h.FailCount++
		} else if err := h.singleDelivery(session, h.Extends[data[0]], data); err != nil {
			r[4] = err.Error()
			h.FailCount++
		} else {
			successes[data[0]] = true
			h.SuccessCount++
		}
		_ = h.Writer.SetRow(fmt.Sprintf("A%d", i+2), r)
	}

	if h.SuccessCount > 0 {
		if h.FailCount > 0 {
			h.Task.Result = fmt.Sprintf("成功%v，失败%v", h.SuccessCount, h.FailCount)
		} else {
			h.Task.Result = fmt.Sprintf("全部成功%v", h.SuccessCount)
		}
	} else {
		h.Task.Result = fmt.Sprintf("全部失败%v", h.FailCount)
	}
}

// 单个处理发货
func (h *ImportHandler) singleDelivery(session *xorm.Session, extend *models.PhysicalVipCardOrders2, data []string) error {
	if extend == nil {
		return errors.New("订单号无效")
	} else if extend.UpetOrders.OrderState != models.OrderStatePayed {
		return errors.New("订单号已发货，不允许重复导入")
	} else if extend.UpetOrders.RefundState > 0 {
		return errors.New("该订单已退款，不能处理发货")
	} else if extend.UpetOrders.LockState > 0 {
		return errors.New("该订单正在退款中，不能处理发货")
	} else if len(data[1]) == 0 {
		return errors.New("会员卡号不能为空")
	} else if strings.Index(data[1], "FY") != 0 {
		return errors.New("会员卡号必须是FY开头,且10位数字")
	} else if len(strings.Trim(data[1], "FY")) != 10 {
		return errors.New("会员卡号必须是FY开头,且10位数字")
	} else if len(data[2]) == 0 {
		return errors.New("快递公司名称不能为空")
	} else if len(data[3]) == 0 {
		return errors.New("快递单号不能为空")
	}

	VirtualCardId, _ := strconv.Atoi(strings.Trim(data[1], "FY"))
	if _, ok := h.VirtualCardIdMap[int64(VirtualCardId)]; ok {
		return errors.New("该会员卡号已经绑定了实体订单")
	}

	if _, ok := h.VipCardOrderMap[int64(VirtualCardId)]; ok {
		return errors.New("该会员卡号已经被激活")
	}

	if _, ok := h.VipCardVirtualMap[int64(VirtualCardId)]; !ok {
		return errors.New("该会员卡号已被激活或已失效或已注销或不存在")
	}

	express := h.getExpressByName(data[2])
	if express == nil {
		return errors.New("快递公司名称无效")
	}
	omss := OmsService{}
	OmsOrderDeliveryRequest := &oc.OmsOrderDeliveryRequest{
		Omsorders: make([]*oc.OmsOrder, 0),
	}
	OmsOrderDeliveryRequest.Omsorders = append(OmsOrderDeliveryRequest.Omsorders, &oc.OmsOrder{
		Orderid:          strconv.Itoa(int(extend.UpetOrders.OrderSn)),
		DeliveryBillCode: "0000",
		DeliveryDate:     time.Now().Format("2006/01/02 15:04:05"),
		LogisticsCode:    data[3],
		LogisticsCompany: express.ECodeKdniao,
		Goodslist:        make([]*oc.OmsOrderGood, 0),
	})
	OmsOrderDeliveryRequest.Omsorders[0].Goodslist = append(OmsOrderDeliveryRequest.Omsorders[0].Goodslist, &oc.OmsOrderGood{
		Spu:   strconv.Itoa(extend.UpetOrderGoods.GoodsCommonid),
		Sku:   strconv.Itoa(int(extend.UpetOrderGoods.GoodsId)),
		Stock: 1,
	})

	if _, err := omss.OmsOrderDelivery(context.Background(), OmsOrderDeliveryRequest); err != nil {
		glog.Error(h.LogPrefix, "omss.OmsOrderDelivery错误：", err.Error(), ",参数：", kit.JsonEncode(OmsOrderDeliveryRequest))
		return errors.New(err.Error())
	}

	//绑定虚拟卡号
	if _, err := session.Table("upet_orders").Cols("virtual_card_id").Where("order_id=?", extend.UpetOrders.OrderId).Update(&models.UpetOrders{VirtualCardId: int64(VirtualCardId)}); err != nil {
		glog.Error(h.LogPrefix, "绑定虚拟卡号失败 ", err.Error())
		return errors.New(err.Error())
	}

	return nil
}

// 获取快递公司数据
func (h *ImportHandler) getExpressByName(name string) *models.UpetExpress {
	for _, express := range h.Expresses {
		// 公司名称包含或者等于编码
		if strings.Contains(express.EName, name) || express.ECodeKdniao == strings.ToUpper(name) {
			return express
		}
	}
	return nil
}

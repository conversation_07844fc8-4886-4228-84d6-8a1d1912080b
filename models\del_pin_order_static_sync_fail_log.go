package models

import (
	"time"
)

type PinOrderStaticSyncFailLog struct {
	Id          int32     `xorm:"not null pk autoincr INT(11)"`
	SyncType    int32     `xorm:"not null default 1 comment('同步类型1成团 2下单') TINYINT(1)"`
	Gid         int32     `xorm:"not null default 0 comment('拼团活动id') INT(11)"`
	SkuId       int32     `xorm:"not null default 0 comment('产品sku_id') INT(11)"`
	ProductId   int32     `xorm:"not null default 0 comment('产品id') INT(11)"`
	ChannelId   int32     `xorm:"not null default 5 comment('渠道') TINYINT(2)"`
	FinanceCode string    `xorm:"not null default '''' comment('财务编码 暂时没用') VARCHAR(30)"`
	Status      int32     `xorm:"not null default 0 comment('补偿状态 0未补偿1已补偿') TINYINT(1)"`
	Reason      string    `xorm:"not null default '''' comment('失败原因') VARCHAR(500)"`
	CreateTime  time.Time `xorm:"created not null DATETIME"`
}

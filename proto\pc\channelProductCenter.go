package pc

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type ChanelProductClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  DcChannelProductClient
}

func GetDcChannelProductClient(c ...echo.Context) *ChanelProductClient {
	var client ChanelProductClient
	var err error
	url := config.GetString("grpc.product-center")
	if url == "" {
		url = "127.0.0.1:11003"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewDcChannelProductClient(client.Conn)
		//client.Ctx = AppendToOutgoingContextLoginUserInfo(context.Background(), c...)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)
		return &client
	}
}

// 关闭链接
func (d *ChanelProductClient) Close() {
	d.Conn.Close()
	d.Cf()
}

type TaskProductClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  DcTaskProductClient
}

func GetDcTaskProductClient(c ...echo.Context) *TaskProductClient {
	var client TaskProductClient
	var err error
	url := config.GetString("grpc.product-center")
	if url == "" {
		url = "127.0.0.1:11003"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewDcTaskProductClient(client.Conn)
		//client.Ctx = AppendToOutgoingContextLoginUserInfo(context.Background(), c...)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)
		return &client
	}
}

// 关闭链接
func (d *TaskProductClient) Close() {
	d.Conn.Close()
	d.Cf()
}

package services

import (
	"context"
	"order-center/proto/oc"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

//生产订单批量完成
func Test_ProduceOrderAccomplishOrder(t *testing.T) {
	client := oc.NewClient("api.petrvet.com:11005")
	defer client.Close()

	for _, v := range []string{"87699752885757452", "88970862241799106", "85341722241799106", "85169502241799106", "87629672885757452", "98648942885757452", "85342072885757452", "85361972885757452", "96988442241799106", "85266182443969229", "85116562241799106", "85405442885757452", "104845292241799106", "98943752885757452", "87629382241799106", "85024723293728303", "85265972443969229", "85024532241799106", "85106972443969229", "99537072885757452", "85045212241799106", "85362412443969229", "85063122885757452", "85106312241799106", "85392252241799106", "85341892241799106", "85106482885757452", "85106882443969229", "85106672885757452", "87699762241799106", "85024492885757452", "85024422241799106", "87302982885757452", "85266582241799106", "85341752885757452", "85107232885757452", "92183452885757452", "62603492241799106", "87629202241799106", "85405112241799106", "87629722443969229", "85045052241799106", "98648942241799106", "85063262443969229", "85266572443969229", "85063262241799106", "85362612885757452", "85044962885757452", "92296382885757452", "85361572885757452", "88331511340679040", "97764572241799106", "94144383777955092", "87629942885757452", "85106542885757452", "96797722885757452", "85341952885757452", "85106183293728303", "85341062241799106", "85024422443969229", "85106283044649464"} {
		got, err := client.RPC.AccomplishOrder(context.Background(), &oc.AccomplishOrderRequest{
			OrderSn:     v,
			ConfirmTime: kit.GetTimeNow(),
		})
		if err != nil {
			t.Errorf("AccomplishOrder() error = %v", err)
			return
		}
		if got.Code != 200 {
			t.Log(v, "，", kit.JsonEncode(got))
		}
	}
}

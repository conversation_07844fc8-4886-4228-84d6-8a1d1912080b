package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/models"
	"order-center/utils"
)

type RpomsService struct {
	BaseService
}

var rpOmsApiUrl string

func initConfig() {
	/*appkeyCon = config.GetString("qqd.appkey")
	appsecretCon = config.GetString("qqd.appsecret")
	token = config.GetString("qqd.token")*/
	rpOmsApiUrl = config.GetString("rp.oms.api.url")
	//goodsurl = config.GetString("qqd.goodsUrl")
	//systemid = config.GetString("Systemid")
	/*appkeyConNew = config.GetString("qqd.new.appkey")
	appsecretConNew = config.GetString("qqd.new.appsecret")
	tokenNew = config.GetString("qqd.new.token")*/
}

func (c *RpomsService) OrderAdd(params *dto.AddOmsOrderHttpRequest) (httpCode int, res *dto.RpOmsBaseResponse, err error) {
	initConfig()
	bytesData := kit.JsonEncodeByte(params)
	postData := string(bytesData)
	ret := new(dto.RpOmsBaseResponse)
	path := "/open-api/order/awen/add"

	glog.Info(params.ChannelOrderSn, "-订单推送到瑞鹏OMS请求参数:"+postData, ",", rpOmsApiUrl+path)
	respBytes, httpCode, err := utils.RpomsHttpPost(rpOmsApiUrl+path, bytesData, "application/json;charset=UTF-8")
	glog.Info(params.ChannelOrderSn, "-订单推送到瑞鹏OMS返回结果:"+string(respBytes), httpCode, err)

	if err != nil {
		return 0, ret, err
	}
	if httpCode != 200 {
		return 0, ret, errors.New("推送oms失败 状态码不对" + cast.ToString(httpCode))
	}
	_ = json.Unmarshal(respBytes, ret)
	var pushState string
	if ret.Code == models.OmsOrderAddFail {
		pushState = "此订单未能成功从阿闻系统推送至OMS系统"
		return httpCode, ret, errors.New(pushState + ",失败原因:" + ret.Message)
	}
	return 200, ret, nil
}

func (c *RpomsService) Sign() (string, error) {
	return "", nil
}

// 退款单推送
func (c *RpomsService) OrderRefundAdd(params *dto.OrderRefundHttpRequest) (int, string) {
	initConfig()
	bytesData := kit.JsonEncodeByte(params)
	postData := string(bytesData)
	//获取验签字符串
	path := "/open-api/refund/awen/add"
	glog.Info(params.ChannelOrderSn, ",退款单推送到瑞鹏OMS请求参数:"+postData, ",", rpOmsApiUrl+path)
	ret, code, err := utils.RpomsHttpPost(rpOmsApiUrl+path, bytesData, "application/json;charset=UTF-8")
	glog.Info(params.ChannelOrderSn, "退款单推送到瑞鹏OMS返回结果:"+string(ret), code, err)
	if err != nil {
		return 400, fmt.Sprintf("http调用失败:%v", err)
	}
	return code, string(ret)
}

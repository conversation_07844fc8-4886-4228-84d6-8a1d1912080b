syntax = "proto3";

package mk;
import "mk/promotion.proto";

service InsuranceService {
  // 新增/编辑
  rpc Store (InsuranceReq) returns (baseResponse);
  // 列表
  rpc List(InsuranceListReq) returns(InsuranceListRes);
  // 详情
  rpc Detail(InsuranceDetailReq) returns(InsuranceDetailRes);
  // 撤销
  rpc Cancel(InsuranceCancelReq) returns(baseResponse);
  // 选择商品
  rpc ChoiceSku(InsuranceChoiceSkuReq) returns(InsuranceChoiceSkuRes);
}

message InsuranceChoiceSkuReq {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;

  // 开始日期
  string beginDate = 6;
  // 结束日期，留空表示长期
  string endDate = 7;

  // 当前活动id，更新时
  int32 promotionId = 8;

  // skuId
  int32 skuId = 3;
  // spuId
  int32 productId = 4;
  // 商品名称
  string name = 5;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 9;
}

message InsuranceChoiceSkuData {
  // skuId
  int32 skuId = 1;
  // spuId
  int32 productId = 2;
  // 商品名称
  string name = 3;
  // 状态，0活动时间冲突不可选 1可选
  int32 state = 4;
}

message InsuranceChoiceSkuRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  repeated InsuranceChoiceSkuData data = 3;
  // 总数
  int32 total = 4;
}

// 新增/更新
message InsuranceReq {
  //用户Id，即userno
  string userId=1;
  //用户Id，即登录人姓名
  string userName=2;
  // 赠险数据
  Insurance insurance = 3;
}

message Insurance {
  // 活动id，新增不用传
  int32 id = 1;
  // 活动名称
  string title = 3;
  // 开始日期
  string beginDate = 4;
  // 结束日期，留空表示长期
  string endDate = 5;
  // 状态 10 未开始，20进行中 30 已结束 31 已撤销
  int32 state = 6;
  // 协议微页面id
  string agreementWepageId = 8;
  // 协议微页面标题
  string agreementWepageTitle = 9;
  // 活动商品
  repeated InsuranceSku skus = 7;
}

message InsuranceSkuLinkShop {
  // 财务编码
  string financeCode = 1;
  // 门店名称
  string shopName = 2;
  // 层级
  int32 level = 3;
}

// 赠保险
message InsuranceSkuLink {
  // 链接
  string link = 1;
  // 适用地区/门店
  repeated InsuranceSkuLinkShop shops = 3;
}

// 赠险商品
message InsuranceSku {
  // 商品id
  int32 productId=5;
  // skuId
  int32 skuId = 2;
  // 商品名称
  string name = 3;
  // 赠品
  repeated InsuranceSkuLink links = 4;
}

message InsuranceListReq {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
}

message InsuranceList {
  // 活动id，新增不用传
  int32 id = 1;
  // 活动名称
  string title = 3;
  // 开始日期
  string beginDate = 4;
  // 结束日期，留空表示长期
  string endDate = 5;
  // 状态 10 未开始，20进行中 30 已结束 31 已撤销
  int32 state = 6;
}

message InsuranceListRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  repeated InsuranceList data = 3;
  // 总数
  int32 total = 4;
}

message InsuranceDetailReq {
  // 活动id
  int32 id = 3;
}

message InsuranceDetailRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  Insurance data = 3;
}

message InsuranceCancelReq {
  //用户Id，即userno
  string userId=1;
  //用户Id，即登录人姓名
  string userName=2;
  // 活动id
  int32 id = 3;
}
// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user.proto

package base

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type UserInfoBj struct {
	UserName             string   `protobuf:"bytes,1,opt,name=userName,proto3" json:"userName"`
	UserSex              int32    `protobuf:"varint,2,opt,name=userSex,proto3" json:"userSex"`
	UserMobile           string   `protobuf:"bytes,3,opt,name=userMobile,proto3" json:"userMobile"`
	UserAvatar           string   `protobuf:"bytes,4,opt,name=userAvatar,proto3" json:"userAvatar"`
	UserBirthday         string   `protobuf:"bytes,5,opt,name=userBirthday,proto3" json:"userBirthday"`
	FirstRaisesPet       string   `protobuf:"bytes,6,opt,name=firstRaisesPet,proto3" json:"firstRaisesPet"`
	City                 string   `protobuf:"bytes,7,opt,name=city,proto3" json:"city"`
	Country              string   `protobuf:"bytes,8,opt,name=country,proto3" json:"country"`
	Province             string   `protobuf:"bytes,9,opt,name=province,proto3" json:"province"`
	Area                 string   `protobuf:"bytes,10,opt,name=area,proto3" json:"area"`
	ThirdUserId          string   `protobuf:"bytes,11,opt,name=thirdUserId,proto3" json:"thirdUserId"`
	Source               int32    `protobuf:"varint,12,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoBj) Reset()         { *m = UserInfoBj{} }
func (m *UserInfoBj) String() string { return proto.CompactTextString(m) }
func (*UserInfoBj) ProtoMessage()    {}
func (*UserInfoBj) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}

func (m *UserInfoBj) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoBj.Unmarshal(m, b)
}
func (m *UserInfoBj) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoBj.Marshal(b, m, deterministic)
}
func (m *UserInfoBj) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoBj.Merge(m, src)
}
func (m *UserInfoBj) XXX_Size() int {
	return xxx_messageInfo_UserInfoBj.Size(m)
}
func (m *UserInfoBj) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoBj.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoBj proto.InternalMessageInfo

func (m *UserInfoBj) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *UserInfoBj) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *UserInfoBj) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *UserInfoBj) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *UserInfoBj) GetUserBirthday() string {
	if m != nil {
		return m.UserBirthday
	}
	return ""
}

func (m *UserInfoBj) GetFirstRaisesPet() string {
	if m != nil {
		return m.FirstRaisesPet
	}
	return ""
}

func (m *UserInfoBj) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *UserInfoBj) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *UserInfoBj) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *UserInfoBj) GetArea() string {
	if m != nil {
		return m.Area
	}
	return ""
}

func (m *UserInfoBj) GetThirdUserId() string {
	if m != nil {
		return m.ThirdUserId
	}
	return ""
}

func (m *UserInfoBj) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type UserInfoBjBody struct {
	Body                 *UserInfoBj `protobuf:"bytes,1,opt,name=body,proto3" json:"body"`
	Sn                   string      `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn"`
	Topic                string      `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UserInfoBjBody) Reset()         { *m = UserInfoBjBody{} }
func (m *UserInfoBjBody) String() string { return proto.CompactTextString(m) }
func (*UserInfoBjBody) ProtoMessage()    {}
func (*UserInfoBjBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{1}
}

func (m *UserInfoBjBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoBjBody.Unmarshal(m, b)
}
func (m *UserInfoBjBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoBjBody.Marshal(b, m, deterministic)
}
func (m *UserInfoBjBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoBjBody.Merge(m, src)
}
func (m *UserInfoBjBody) XXX_Size() int {
	return xxx_messageInfo_UserInfoBjBody.Size(m)
}
func (m *UserInfoBjBody) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoBjBody.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoBjBody proto.InternalMessageInfo

func (m *UserInfoBjBody) GetBody() *UserInfoBj {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *UserInfoBjBody) GetSn() string {
	if m != nil {
		return m.Sn
	}
	return ""
}

func (m *UserInfoBjBody) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type UserInfoBjRequest struct {
	Msg                  *UserInfoBjBody `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UserInfoBjRequest) Reset()         { *m = UserInfoBjRequest{} }
func (m *UserInfoBjRequest) String() string { return proto.CompactTextString(m) }
func (*UserInfoBjRequest) ProtoMessage()    {}
func (*UserInfoBjRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{2}
}

func (m *UserInfoBjRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoBjRequest.Unmarshal(m, b)
}
func (m *UserInfoBjRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoBjRequest.Marshal(b, m, deterministic)
}
func (m *UserInfoBjRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoBjRequest.Merge(m, src)
}
func (m *UserInfoBjRequest) XXX_Size() int {
	return xxx_messageInfo_UserInfoBjRequest.Size(m)
}
func (m *UserInfoBjRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoBjRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoBjRequest proto.InternalMessageInfo

func (m *UserInfoBjRequest) GetMsg() *UserInfoBjBody {
	if m != nil {
		return m.Msg
	}
	return nil
}

//宠物
type PetInfo struct {
	//用户id
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物种类名称
	TypeName string `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name"`
	//宠物名字
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	//宠物种类id
	TypeId int32 `protobuf:"varint,4,opt,name=type_id,json=typeId,proto3" json:"type_id"`
	//备注
	Remark string `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark"`
	//宠物头像
	AvatarUrl string `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url"`
	//性别
	Sex int32 `protobuf:"varint,7,opt,name=sex,proto3" json:"sex"`
	//生日
	Birthday string `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday"`
	//重量（g）
	Weight int32 `protobuf:"varint,9,opt,name=weight,proto3" json:"weight"`
	//驱虫状态 0：不详 1：未驱虫 2：已驱虫 3：体内驱虫 4：体外驱虫
	Insecticide int32 `protobuf:"varint,10,opt,name=insecticide,proto3" json:"insecticide"`
	//免疫状态 0：不详 1：部分免疫 2：已免疫 3：未免疫
	Immune int32 `protobuf:"varint,11,opt,name=immune,proto3" json:"immune"`
	//绝育状态 0：不详 1：已绝育 2：未绝育
	Sterilize int32 `protobuf:"varint,12,opt,name=sterilize,proto3" json:"sterilize"`
	//宠物性格('不详', '温顺', '凶猛')
	Disposition string `protobuf:"bytes,13,opt,name=disposition,proto3" json:"disposition"`
	//ua 渠道（15子龙，14小暖）
	ChannelId int32 `protobuf:"varint,14,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//写死为2
	PlatformId           int32    `protobuf:"varint,15,opt,name=platform_id,json=platformId,proto3" json:"platform_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetInfo) Reset()         { *m = PetInfo{} }
func (m *PetInfo) String() string { return proto.CompactTextString(m) }
func (*PetInfo) ProtoMessage()    {}
func (*PetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{3}
}

func (m *PetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetInfo.Unmarshal(m, b)
}
func (m *PetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetInfo.Marshal(b, m, deterministic)
}
func (m *PetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetInfo.Merge(m, src)
}
func (m *PetInfo) XXX_Size() int {
	return xxx_messageInfo_PetInfo.Size(m)
}
func (m *PetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PetInfo proto.InternalMessageInfo

func (m *PetInfo) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetInfo) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *PetInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PetInfo) GetTypeId() int32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

func (m *PetInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *PetInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *PetInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PetInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *PetInfo) GetWeight() int32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *PetInfo) GetInsecticide() int32 {
	if m != nil {
		return m.Insecticide
	}
	return 0
}

func (m *PetInfo) GetImmune() int32 {
	if m != nil {
		return m.Immune
	}
	return 0
}

func (m *PetInfo) GetSterilize() int32 {
	if m != nil {
		return m.Sterilize
	}
	return 0
}

func (m *PetInfo) GetDisposition() string {
	if m != nil {
		return m.Disposition
	}
	return ""
}

func (m *PetInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PetInfo) GetPlatformId() int32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

type UserInfo struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	//会员姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	//昵称
	NickName string `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	//头像
	AvatarUrl string `protobuf:"bytes,5,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url"`
	//性别
	Sex int32 `protobuf:"varint,6,opt,name=sex,proto3" json:"sex"`
	//省
	ProvinceId int32 `protobuf:"varint,7,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	//市
	CityId int32 `protobuf:"varint,8,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//区
	AreaId int32 `protobuf:"varint,9,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	//经度
	Longitude float32 `protobuf:"fixed32,10,opt,name=longitude,proto3" json:"longitude"`
	//维度
	Latitude float32 `protobuf:"fixed32,11,opt,name=latitude,proto3" json:"latitude"`
	//生日
	Birthday string `protobuf:"bytes,12,opt,name=birthday,proto3" json:"birthday"`
	//第三方用户id
	ThirdUserId string `protobuf:"bytes,13,opt,name=third_user_id,json=thirdUserId,proto3" json:"third_user_id"`
	//ua 渠道（15子龙，14小暖）
	ChannelId int32 `protobuf:"varint,14,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//用户来源 1.朋友介绍 2.外部开发 3.分院开发 4.网站 5.APP 6.展会开发 7.数据同步 8.大众点评/美团 9.老客户补录 10.路过 11.百度 12.体系内分院转诊 13.体系外医院转诊 14.微信推广 15.地推及活动 16.宠物店转诊 17.天猫/京东 18.阿闻宠物小程序 19.宠医云 20.其他
	UserSource int32 `protobuf:"varint,15,opt,name=user_source,json=userSource,proto3" json:"user_source"`
	//写死为2
	PlatformId int32 `protobuf:"varint,16,opt,name=platform_id,json=platformId,proto3" json:"platform_id"`
	//用户备注
	Remark string `protobuf:"bytes,17,opt,name=remark,proto3" json:"remark"`
	//身份证
	IdCard string `protobuf:"bytes,18,opt,name=id_card,json=idCard,proto3" json:"id_card"`
	//QQ号
	Qq string `protobuf:"bytes,19,opt,name=qq,proto3" json:"qq"`
	//微信号
	WxCode string `protobuf:"bytes,20,opt,name=wx_code,json=wxCode,proto3" json:"wx_code"`
	//电子邮件
	Email string `protobuf:"bytes,21,opt,name=email,proto3" json:"email"`
	//住址
	Address string `protobuf:"bytes,22,opt,name=address,proto3" json:"address"`
	//分院ID
	HospitalId int32 `protobuf:"varint,23,opt,name=hospital_id,json=hospitalId,proto3" json:"hospital_id"`
	//财务编码
	FinanceCode          string   `protobuf:"bytes,24,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{4}
}

func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (m *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(m, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserInfo) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *UserInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserInfo) GetProvinceId() int32 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *UserInfo) GetCityId() int32 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *UserInfo) GetAreaId() int32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *UserInfo) GetLongitude() float32 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *UserInfo) GetLatitude() float32 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *UserInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *UserInfo) GetThirdUserId() string {
	if m != nil {
		return m.ThirdUserId
	}
	return ""
}

func (m *UserInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserInfo) GetUserSource() int32 {
	if m != nil {
		return m.UserSource
	}
	return 0
}

func (m *UserInfo) GetPlatformId() int32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *UserInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *UserInfo) GetIdCard() string {
	if m != nil {
		return m.IdCard
	}
	return ""
}

func (m *UserInfo) GetQq() string {
	if m != nil {
		return m.Qq
	}
	return ""
}

func (m *UserInfo) GetWxCode() string {
	if m != nil {
		return m.WxCode
	}
	return ""
}

func (m *UserInfo) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *UserInfo) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *UserInfo) GetHospitalId() int32 {
	if m != nil {
		return m.HospitalId
	}
	return 0
}

func (m *UserInfo) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

type NewUserResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	UserId               string   `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewUserResponse) Reset()         { *m = NewUserResponse{} }
func (m *NewUserResponse) String() string { return proto.CompactTextString(m) }
func (*NewUserResponse) ProtoMessage()    {}
func (*NewUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{5}
}

func (m *NewUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewUserResponse.Unmarshal(m, b)
}
func (m *NewUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewUserResponse.Marshal(b, m, deterministic)
}
func (m *NewUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewUserResponse.Merge(m, src)
}
func (m *NewUserResponse) XXX_Size() int {
	return xxx_messageInfo_NewUserResponse.Size(m)
}
func (m *NewUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewUserResponse proto.InternalMessageInfo

func (m *NewUserResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewUserResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewUserResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewUserResponse) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type NewPetResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	PetId                string   `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPetResponse) Reset()         { *m = NewPetResponse{} }
func (m *NewPetResponse) String() string { return proto.CompactTextString(m) }
func (*NewPetResponse) ProtoMessage()    {}
func (*NewPetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{6}
}

func (m *NewPetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPetResponse.Unmarshal(m, b)
}
func (m *NewPetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPetResponse.Marshal(b, m, deterministic)
}
func (m *NewPetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPetResponse.Merge(m, src)
}
func (m *NewPetResponse) XXX_Size() int {
	return xxx_messageInfo_NewPetResponse.Size(m)
}
func (m *NewPetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPetResponse proto.InternalMessageInfo

func (m *NewPetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewPetResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewPetResponse) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

//我的会员卡响应
type MyVipResponse struct {
	Code                 int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Cards                []*MembershipCard `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MyVipResponse) Reset()         { *m = MyVipResponse{} }
func (m *MyVipResponse) String() string { return proto.CompactTextString(m) }
func (*MyVipResponse) ProtoMessage()    {}
func (*MyVipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{7}
}

func (m *MyVipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyVipResponse.Unmarshal(m, b)
}
func (m *MyVipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyVipResponse.Marshal(b, m, deterministic)
}
func (m *MyVipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyVipResponse.Merge(m, src)
}
func (m *MyVipResponse) XXX_Size() int {
	return xxx_messageInfo_MyVipResponse.Size(m)
}
func (m *MyVipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MyVipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MyVipResponse proto.InternalMessageInfo

func (m *MyVipResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MyVipResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MyVipResponse) GetCards() []*MembershipCard {
	if m != nil {
		return m.Cards
	}
	return nil
}

type MembershipCard struct {
	//会员卡ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	//会员卡类型
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//会员卡号
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//会员卡名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//生效时间
	EffectiveTime int64 `protobuf:"varint,5,opt,name=effective_time,json=effectiveTime,proto3" json:"effective_time"`
	//失效時間
	FailureTime int64 `protobuf:"varint,6,opt,name=failure_time,json=failureTime,proto3" json:"failure_time"`
	//状态    1 生效中   0 失效
	State int32 `protobuf:"varint,7,opt,name=state,proto3" json:"state"`
	//用户id
	MemberId string `protobuf:"bytes,8,opt,name=MemberId,proto3" json:"MemberId"`
	//批次号
	Batchcode            string   `protobuf:"bytes,9,opt,name=Batchcode,proto3" json:"Batchcode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MembershipCard) Reset()         { *m = MembershipCard{} }
func (m *MembershipCard) String() string { return proto.CompactTextString(m) }
func (*MembershipCard) ProtoMessage()    {}
func (*MembershipCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{8}
}

func (m *MembershipCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MembershipCard.Unmarshal(m, b)
}
func (m *MembershipCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MembershipCard.Marshal(b, m, deterministic)
}
func (m *MembershipCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MembershipCard.Merge(m, src)
}
func (m *MembershipCard) XXX_Size() int {
	return xxx_messageInfo_MembershipCard.Size(m)
}
func (m *MembershipCard) XXX_DiscardUnknown() {
	xxx_messageInfo_MembershipCard.DiscardUnknown(m)
}

var xxx_messageInfo_MembershipCard proto.InternalMessageInfo

func (m *MembershipCard) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MembershipCard) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MembershipCard) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *MembershipCard) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MembershipCard) GetEffectiveTime() int64 {
	if m != nil {
		return m.EffectiveTime
	}
	return 0
}

func (m *MembershipCard) GetFailureTime() int64 {
	if m != nil {
		return m.FailureTime
	}
	return 0
}

func (m *MembershipCard) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *MembershipCard) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *MembershipCard) GetBatchcode() string {
	if m != nil {
		return m.Batchcode
	}
	return ""
}

//开卡的参数
type NewCardRequest struct {
	//用户id
	Userid string `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	//卡类型
	Cardtype string `protobuf:"bytes,2,opt,name=cardtype,proto3" json:"cardtype"`
	//机构id-开卡机构，退卡机构
	HospitalCode string `protobuf:"bytes,3,opt,name=hospitalCode,proto3" json:"hospitalCode"`
	//机构名称
	HospitalName string `protobuf:"bytes,4,opt,name=hospitalName,proto3" json:"hospitalName"`
	//来源（0：子龙 1：小暖 2:瑞鹏）
	CreateSource int32 `protobuf:"varint,5,opt,name=createSource,proto3" json:"createSource"`
	//创建人id
	CreateId int32 `protobuf:"varint,6,opt,name=createId,proto3" json:"createId"`
	//创建人姓名
	CreateName string `protobuf:"bytes,7,opt,name=createName,proto3" json:"createName"`
	//结算单号-erp
	Orderid string `protobuf:"bytes,8,opt,name=orderid,proto3" json:"orderid"`
	//保障卡类别
	CategoryCode string `protobuf:"bytes,9,opt,name=categoryCode,proto3" json:"categoryCode"`
	//付款信息
	RecordsPay []*RecordsPay `protobuf:"bytes,10,rep,name=recordsPay,proto3" json:"recordsPay"`
	//卡编号
	Cardno string `protobuf:"bytes,11,opt,name=cardno,proto3" json:"cardno"`
	//添加卡的batch
	Batchcode string `protobuf:"bytes,12,opt,name=batchcode,proto3" json:"batchcode"`
	//退款时间 yyyy-MM-dd HH:mm:ss
	FreezeTime           string   `protobuf:"bytes,13,opt,name=freezeTime,proto3" json:"freezeTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewCardRequest) Reset()         { *m = NewCardRequest{} }
func (m *NewCardRequest) String() string { return proto.CompactTextString(m) }
func (*NewCardRequest) ProtoMessage()    {}
func (*NewCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{9}
}

func (m *NewCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCardRequest.Unmarshal(m, b)
}
func (m *NewCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCardRequest.Marshal(b, m, deterministic)
}
func (m *NewCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCardRequest.Merge(m, src)
}
func (m *NewCardRequest) XXX_Size() int {
	return xxx_messageInfo_NewCardRequest.Size(m)
}
func (m *NewCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewCardRequest proto.InternalMessageInfo

func (m *NewCardRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *NewCardRequest) GetCardtype() string {
	if m != nil {
		return m.Cardtype
	}
	return ""
}

func (m *NewCardRequest) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *NewCardRequest) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *NewCardRequest) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *NewCardRequest) GetCreateId() int32 {
	if m != nil {
		return m.CreateId
	}
	return 0
}

func (m *NewCardRequest) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *NewCardRequest) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

func (m *NewCardRequest) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *NewCardRequest) GetRecordsPay() []*RecordsPay {
	if m != nil {
		return m.RecordsPay
	}
	return nil
}

func (m *NewCardRequest) GetCardno() string {
	if m != nil {
		return m.Cardno
	}
	return ""
}

func (m *NewCardRequest) GetBatchcode() string {
	if m != nil {
		return m.Batchcode
	}
	return ""
}

func (m *NewCardRequest) GetFreezeTime() string {
	if m != nil {
		return m.FreezeTime
	}
	return ""
}

//续卡的参数
type RenewCardRequest struct {
	//机构id-开卡机构，退卡机构
	HospitalCode string `protobuf:"bytes,1,opt,name=hospitalCode,proto3" json:"hospitalCode"`
	//机构名称
	HospitalName string `protobuf:"bytes,2,opt,name=hospitalName,proto3" json:"hospitalName"`
	//来源（0：子龙 1：小暖 2:瑞鹏）
	CreateSource int32 `protobuf:"varint,3,opt,name=createSource,proto3" json:"createSource"`
	//卡号
	EnsureCode string `protobuf:"bytes,4,opt,name=ensureCode,proto3" json:"ensureCode"`
	//卡套餐编码（默认为原套餐类别）
	CategoryCode string `protobuf:"bytes,5,opt,name=categoryCode,proto3" json:"categoryCode"`
	//操作人ID
	OperatorId int32 `protobuf:"varint,6,opt,name=operatorId,proto3" json:"operatorId"`
	//操作人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operatorName,proto3" json:"operatorName"`
	//第三方用户ID
	Userid string `protobuf:"bytes,8,opt,name=userid,proto3" json:"userid"`
	//结算单号-erp
	Orderid string `protobuf:"bytes,9,opt,name=orderid,proto3" json:"orderid"`
	//付款信息
	RecordsPay           []*RecordsPay `protobuf:"bytes,10,rep,name=recordsPay,proto3" json:"recordsPay"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RenewCardRequest) Reset()         { *m = RenewCardRequest{} }
func (m *RenewCardRequest) String() string { return proto.CompactTextString(m) }
func (*RenewCardRequest) ProtoMessage()    {}
func (*RenewCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{10}
}

func (m *RenewCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RenewCardRequest.Unmarshal(m, b)
}
func (m *RenewCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RenewCardRequest.Marshal(b, m, deterministic)
}
func (m *RenewCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenewCardRequest.Merge(m, src)
}
func (m *RenewCardRequest) XXX_Size() int {
	return xxx_messageInfo_RenewCardRequest.Size(m)
}
func (m *RenewCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RenewCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RenewCardRequest proto.InternalMessageInfo

func (m *RenewCardRequest) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *RenewCardRequest) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *RenewCardRequest) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *RenewCardRequest) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *RenewCardRequest) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *RenewCardRequest) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *RenewCardRequest) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *RenewCardRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *RenewCardRequest) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

func (m *RenewCardRequest) GetRecordsPay() []*RecordsPay {
	if m != nil {
		return m.RecordsPay
	}
	return nil
}

type RecordsPay struct {
	//支付类型
	PayType int32 `protobuf:"varint,1,opt,name=payType,proto3" json:"payType"`
	//支付金额
	PayMoney             string   `protobuf:"bytes,2,opt,name=payMoney,proto3" json:"payMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordsPay) Reset()         { *m = RecordsPay{} }
func (m *RecordsPay) String() string { return proto.CompactTextString(m) }
func (*RecordsPay) ProtoMessage()    {}
func (*RecordsPay) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{11}
}

func (m *RecordsPay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordsPay.Unmarshal(m, b)
}
func (m *RecordsPay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordsPay.Marshal(b, m, deterministic)
}
func (m *RecordsPay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordsPay.Merge(m, src)
}
func (m *RecordsPay) XXX_Size() int {
	return xxx_messageInfo_RecordsPay.Size(m)
}
func (m *RecordsPay) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordsPay.DiscardUnknown(m)
}

var xxx_messageInfo_RecordsPay proto.InternalMessageInfo

func (m *RecordsPay) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *RecordsPay) GetPayMoney() string {
	if m != nil {
		return m.PayMoney
	}
	return ""
}

//会员卡列表请求参数
type MemberCardRequest struct {
	//用户id
	Userid string `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	//门店财务编码
	Financecode string `protobuf:"bytes,2,opt,name=financecode,proto3" json:"financecode"`
	//预售名称
	Presellname string `protobuf:"bytes,3,opt,name=presellname,proto3" json:"presellname"`
	//开始时间
	Startdate string `protobuf:"bytes,4,opt,name=startdate,proto3" json:"startdate"`
	//結束时间
	Enddate string `protobuf:"bytes,5,opt,name=enddate,proto3" json:"enddate"`
	//关键词
	Keyword string `protobuf:"bytes,6,opt,name=keyword,proto3" json:"keyword"`
	//起始页
	Pageindex int32 `protobuf:"varint,7,opt,name=pageindex,proto3" json:"pageindex"`
	//每页数据个数
	Pagesize             int32    `protobuf:"varint,8,opt,name=pagesize,proto3" json:"pagesize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCardRequest) Reset()         { *m = MemberCardRequest{} }
func (m *MemberCardRequest) String() string { return proto.CompactTextString(m) }
func (*MemberCardRequest) ProtoMessage()    {}
func (*MemberCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{12}
}

func (m *MemberCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCardRequest.Unmarshal(m, b)
}
func (m *MemberCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCardRequest.Marshal(b, m, deterministic)
}
func (m *MemberCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCardRequest.Merge(m, src)
}
func (m *MemberCardRequest) XXX_Size() int {
	return xxx_messageInfo_MemberCardRequest.Size(m)
}
func (m *MemberCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCardRequest proto.InternalMessageInfo

func (m *MemberCardRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *MemberCardRequest) GetFinancecode() string {
	if m != nil {
		return m.Financecode
	}
	return ""
}

func (m *MemberCardRequest) GetPresellname() string {
	if m != nil {
		return m.Presellname
	}
	return ""
}

func (m *MemberCardRequest) GetStartdate() string {
	if m != nil {
		return m.Startdate
	}
	return ""
}

func (m *MemberCardRequest) GetEnddate() string {
	if m != nil {
		return m.Enddate
	}
	return ""
}

func (m *MemberCardRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *MemberCardRequest) GetPageindex() int32 {
	if m != nil {
		return m.Pageindex
	}
	return 0
}

func (m *MemberCardRequest) GetPagesize() int32 {
	if m != nil {
		return m.Pagesize
	}
	return 0
}

//会员卡列表返回结果集
type MemberCardResponse struct {
	Code                 int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Cards                []*MemberCard `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MemberCardResponse) Reset()         { *m = MemberCardResponse{} }
func (m *MemberCardResponse) String() string { return proto.CompactTextString(m) }
func (*MemberCardResponse) ProtoMessage()    {}
func (*MemberCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{13}
}

func (m *MemberCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCardResponse.Unmarshal(m, b)
}
func (m *MemberCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCardResponse.Marshal(b, m, deterministic)
}
func (m *MemberCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCardResponse.Merge(m, src)
}
func (m *MemberCardResponse) XXX_Size() int {
	return xxx_messageInfo_MemberCardResponse.Size(m)
}
func (m *MemberCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCardResponse proto.InternalMessageInfo

func (m *MemberCardResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MemberCardResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MemberCardResponse) GetCards() []*MemberCard {
	if m != nil {
		return m.Cards
	}
	return nil
}

type MemberCard struct {
	//门店财务编码
	Financecode string `protobuf:"bytes,1,opt,name=financecode,proto3" json:"financecode"`
	//门店名称
	Hospitalname string `protobuf:"bytes,2,opt,name=hospitalname,proto3" json:"hospitalname"`
	//结算时间-关系创建时间
	Createdate string `protobuf:"bytes,3,opt,name=createdate,proto3" json:"createdate"`
	//客户信息
	Customer string `protobuf:"bytes,4,opt,name=customer,proto3" json:"customer"`
	//会员卡状态 vipstatus
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	//预售号
	Presellcode string `protobuf:"bytes,6,opt,name=presellcode,proto3" json:"presellcode"`
	//预售名称
	Presellname string `protobuf:"bytes,7,opt,name=presellname,proto3" json:"presellname"`
	//预售数量
	Presellcount int32 `protobuf:"varint,8,opt,name=presellcount,proto3" json:"presellcount"`
	//预售单位
	Presellunit string `protobuf:"bytes,9,opt,name=presellunit,proto3" json:"presellunit"`
	//订单id
	Orderid              string   `protobuf:"bytes,10,opt,name=orderid,proto3" json:"orderid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCard) Reset()         { *m = MemberCard{} }
func (m *MemberCard) String() string { return proto.CompactTextString(m) }
func (*MemberCard) ProtoMessage()    {}
func (*MemberCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{14}
}

func (m *MemberCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCard.Unmarshal(m, b)
}
func (m *MemberCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCard.Marshal(b, m, deterministic)
}
func (m *MemberCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCard.Merge(m, src)
}
func (m *MemberCard) XXX_Size() int {
	return xxx_messageInfo_MemberCard.Size(m)
}
func (m *MemberCard) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCard.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCard proto.InternalMessageInfo

func (m *MemberCard) GetFinancecode() string {
	if m != nil {
		return m.Financecode
	}
	return ""
}

func (m *MemberCard) GetHospitalname() string {
	if m != nil {
		return m.Hospitalname
	}
	return ""
}

func (m *MemberCard) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

func (m *MemberCard) GetCustomer() string {
	if m != nil {
		return m.Customer
	}
	return ""
}

func (m *MemberCard) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MemberCard) GetPresellcode() string {
	if m != nil {
		return m.Presellcode
	}
	return ""
}

func (m *MemberCard) GetPresellname() string {
	if m != nil {
		return m.Presellname
	}
	return ""
}

func (m *MemberCard) GetPresellcount() int32 {
	if m != nil {
		return m.Presellcount
	}
	return 0
}

func (m *MemberCard) GetPresellunit() string {
	if m != nil {
		return m.Presellunit
	}
	return ""
}

func (m *MemberCard) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

//查看会员抵扣记录
type DeductRequest struct {
	Criteria             []*Criteria `protobuf:"bytes,1,rep,name=Criteria,proto3" json:"Criteria"`
	Source               int32       `protobuf:"varint,2,opt,name=Source,proto3" json:"Source"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DeductRequest) Reset()         { *m = DeductRequest{} }
func (m *DeductRequest) String() string { return proto.CompactTextString(m) }
func (*DeductRequest) ProtoMessage()    {}
func (*DeductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{15}
}

func (m *DeductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductRequest.Unmarshal(m, b)
}
func (m *DeductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductRequest.Marshal(b, m, deterministic)
}
func (m *DeductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductRequest.Merge(m, src)
}
func (m *DeductRequest) XXX_Size() int {
	return xxx_messageInfo_DeductRequest.Size(m)
}
func (m *DeductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeductRequest proto.InternalMessageInfo

func (m *DeductRequest) GetCriteria() []*Criteria {
	if m != nil {
		return m.Criteria
	}
	return nil
}

func (m *DeductRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type Criteria struct {
	RecordsCode          string   `protobuf:"bytes,1,opt,name=RecordsCode,proto3" json:"RecordsCode"`
	EnsureCode           string   `protobuf:"bytes,2,opt,name=EnsureCode,proto3" json:"EnsureCode"`
	UserId               string   `protobuf:"bytes,3,opt,name=UserId,proto3" json:"UserId"`
	PetId                string   `protobuf:"bytes,4,opt,name=PetId,proto3" json:"PetId"`
	BatchCode            string   `protobuf:"bytes,5,opt,name=BatchCode,proto3" json:"BatchCode"`
	BatchDetailCode      string   `protobuf:"bytes,6,opt,name=BatchDetailCode,proto3" json:"BatchDetailCode"`
	OperateType          []int32  `protobuf:"varint,7,rep,packed,name=OperateType,proto3" json:"OperateType"`
	OperatorId           int32    `protobuf:"varint,8,opt,name=OperatorId,proto3" json:"OperatorId"`
	BatchStatus          string   `protobuf:"bytes,9,opt,name=BatchStatus,proto3" json:"BatchStatus"`
	StartTime            string   `protobuf:"bytes,10,opt,name=StartTime,proto3" json:"StartTime"`
	EndTime              string   `protobuf:"bytes,11,opt,name=EndTime,proto3" json:"EndTime"`
	OccurHospitalCode    string   `protobuf:"bytes,12,opt,name=OccurHospitalCode,proto3" json:"OccurHospitalCode"`
	CompanyCode          string   `protobuf:"bytes,13,opt,name=CompanyCode,proto3" json:"CompanyCode"`
	CardType             int32    `protobuf:"varint,14,opt,name=CardType,proto3" json:"CardType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Criteria) Reset()         { *m = Criteria{} }
func (m *Criteria) String() string { return proto.CompactTextString(m) }
func (*Criteria) ProtoMessage()    {}
func (*Criteria) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{16}
}

func (m *Criteria) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Criteria.Unmarshal(m, b)
}
func (m *Criteria) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Criteria.Marshal(b, m, deterministic)
}
func (m *Criteria) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Criteria.Merge(m, src)
}
func (m *Criteria) XXX_Size() int {
	return xxx_messageInfo_Criteria.Size(m)
}
func (m *Criteria) XXX_DiscardUnknown() {
	xxx_messageInfo_Criteria.DiscardUnknown(m)
}

var xxx_messageInfo_Criteria proto.InternalMessageInfo

func (m *Criteria) GetRecordsCode() string {
	if m != nil {
		return m.RecordsCode
	}
	return ""
}

func (m *Criteria) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *Criteria) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *Criteria) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *Criteria) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *Criteria) GetBatchDetailCode() string {
	if m != nil {
		return m.BatchDetailCode
	}
	return ""
}

func (m *Criteria) GetOperateType() []int32 {
	if m != nil {
		return m.OperateType
	}
	return nil
}

func (m *Criteria) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *Criteria) GetBatchStatus() string {
	if m != nil {
		return m.BatchStatus
	}
	return ""
}

func (m *Criteria) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *Criteria) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *Criteria) GetOccurHospitalCode() string {
	if m != nil {
		return m.OccurHospitalCode
	}
	return ""
}

func (m *Criteria) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *Criteria) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

type DeductResponse struct {
	Code                 int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Result               *DeductResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeductResponse) Reset()         { *m = DeductResponse{} }
func (m *DeductResponse) String() string { return proto.CompactTextString(m) }
func (*DeductResponse) ProtoMessage()    {}
func (*DeductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{17}
}

func (m *DeductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductResponse.Unmarshal(m, b)
}
func (m *DeductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductResponse.Marshal(b, m, deterministic)
}
func (m *DeductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductResponse.Merge(m, src)
}
func (m *DeductResponse) XXX_Size() int {
	return xxx_messageInfo_DeductResponse.Size(m)
}
func (m *DeductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeductResponse proto.InternalMessageInfo

func (m *DeductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DeductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DeductResponse) GetResult() *DeductResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type DeductResult struct {
	Result               []*DeductDetail `protobuf:"bytes,1,rep,name=result,proto3" json:"result"`
	Message              string          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	SystemError          string          `protobuf:"bytes,3,opt,name=systemError,proto3" json:"systemError"`
	BusinessError        string          `protobuf:"bytes,4,opt,name=businessError,proto3" json:"businessError"`
	StatusCode           int32           `protobuf:"varint,5,opt,name=statusCode,proto3" json:"statusCode"`
	Extensions           *Extensions     `protobuf:"bytes,6,opt,name=extensions,proto3" json:"extensions"`
	Success              bool            `protobuf:"varint,7,opt,name=success,proto3" json:"success"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DeductResult) Reset()         { *m = DeductResult{} }
func (m *DeductResult) String() string { return proto.CompactTextString(m) }
func (*DeductResult) ProtoMessage()    {}
func (*DeductResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{18}
}

func (m *DeductResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductResult.Unmarshal(m, b)
}
func (m *DeductResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductResult.Marshal(b, m, deterministic)
}
func (m *DeductResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductResult.Merge(m, src)
}
func (m *DeductResult) XXX_Size() int {
	return xxx_messageInfo_DeductResult.Size(m)
}
func (m *DeductResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductResult.DiscardUnknown(m)
}

var xxx_messageInfo_DeductResult proto.InternalMessageInfo

func (m *DeductResult) GetResult() []*DeductDetail {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *DeductResult) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DeductResult) GetSystemError() string {
	if m != nil {
		return m.SystemError
	}
	return ""
}

func (m *DeductResult) GetBusinessError() string {
	if m != nil {
		return m.BusinessError
	}
	return ""
}

func (m *DeductResult) GetStatusCode() int32 {
	if m != nil {
		return m.StatusCode
	}
	return 0
}

func (m *DeductResult) GetExtensions() *Extensions {
	if m != nil {
		return m.Extensions
	}
	return nil
}

func (m *DeductResult) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type DeductDetail struct {
	Id                   int32    `protobuf:"varint,1,opt,name=Id,proto3" json:"Id"`
	HospitalCode         string   `protobuf:"bytes,2,opt,name=HospitalCode,proto3" json:"HospitalCode"`
	RecordsCode          string   `protobuf:"bytes,3,opt,name=RecordsCode,proto3" json:"RecordsCode"`
	EnsureCode           string   `protobuf:"bytes,4,opt,name=EnsureCode,proto3" json:"EnsureCode"`
	BatchCode            string   `protobuf:"bytes,5,opt,name=BatchCode,proto3" json:"BatchCode"`
	BatchDetailCode      string   `protobuf:"bytes,6,opt,name=BatchDetailCode,proto3" json:"BatchDetailCode"`
	UserId               string   `protobuf:"bytes,7,opt,name=UserId,proto3" json:"UserId"`
	PetId                string   `protobuf:"bytes,8,opt,name=PetId,proto3" json:"PetId"`
	UserIdLists          []string `protobuf:"bytes,9,rep,name=UserIdLists,proto3" json:"UserIdLists"`
	PetIdLists           []string `protobuf:"bytes,10,rep,name=PetIdLists,proto3" json:"PetIdLists"`
	ThirdUserId          string   `protobuf:"bytes,11,opt,name=ThirdUserId,proto3" json:"ThirdUserId"`
	ThirdPetId           string   `protobuf:"bytes,12,opt,name=ThirdPetId,proto3" json:"ThirdPetId"`
	ThirdOrgId           string   `protobuf:"bytes,13,opt,name=ThirdOrgId,proto3" json:"ThirdOrgId"`
	OrderNumber          string   `protobuf:"bytes,14,opt,name=OrderNumber,proto3" json:"OrderNumber"`
	RmaNumber            string   `protobuf:"bytes,15,opt,name=RmaNumber,proto3" json:"RmaNumber"`
	OccurTime            string   `protobuf:"bytes,16,opt,name=OccurTime,proto3" json:"OccurTime"`
	OccurHospitalCode    string   `protobuf:"bytes,17,opt,name=OccurHospitalCode,proto3" json:"OccurHospitalCode"`
	OccurHospitalName    string   `protobuf:"bytes,18,opt,name=OccurHospitalName,proto3" json:"OccurHospitalName"`
	CorpusBeforeChange   float32  `protobuf:"fixed32,19,opt,name=CorpusBeforeChange,proto3" json:"CorpusBeforeChange"`
	CorpusChange         float32  `protobuf:"fixed32,20,opt,name=CorpusChange,proto3" json:"CorpusChange"`
	CorpusAfterChange    float32  `protobuf:"fixed32,21,opt,name=CorpusAfterChange,proto3" json:"CorpusAfterChange"`
	TimesBeforeChange    int32    `protobuf:"varint,22,opt,name=TimesBeforeChange,proto3" json:"TimesBeforeChange"`
	TimesChange          int32    `protobuf:"varint,23,opt,name=TimesChange,proto3" json:"TimesChange"`
	TimesAfterChange     int32    `protobuf:"varint,24,opt,name=TimesAfterChange,proto3" json:"TimesAfterChange"`
	OperateType          int32    `protobuf:"varint,25,opt,name=OperateType,proto3" json:"OperateType"`
	OperatorId           int32    `protobuf:"varint,26,opt,name=OperatorId,proto3" json:"OperatorId"`
	OperatorName         string   `protobuf:"bytes,27,opt,name=OperatorName,proto3" json:"OperatorName"`
	OperatorSource       int32    `protobuf:"varint,28,opt,name=OperatorSource,proto3" json:"OperatorSource"`
	Remark               string   `protobuf:"bytes,29,opt,name=Remark,proto3" json:"Remark"`
	ProductName          string   `protobuf:"bytes,30,opt,name=ProductName,proto3" json:"ProductName"`
	UnitPrice            float32  `protobuf:"fixed32,31,opt,name=UnitPrice,proto3" json:"UnitPrice"`
	EnsureCardStatus     int32    `protobuf:"varint,32,opt,name=EnsureCardStatus,proto3" json:"EnsureCardStatus"`
	HospitalName         string   `protobuf:"bytes,33,opt,name=HospitalName,proto3" json:"HospitalName"`
	PayTypeName          string   `protobuf:"bytes,34,opt,name=PayTypeName,proto3" json:"PayTypeName"`
	WrittenOffCode       string   `protobuf:"bytes,35,opt,name=WrittenOffCode,proto3" json:"WrittenOffCode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductDetail) Reset()         { *m = DeductDetail{} }
func (m *DeductDetail) String() string { return proto.CompactTextString(m) }
func (*DeductDetail) ProtoMessage()    {}
func (*DeductDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{19}
}

func (m *DeductDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductDetail.Unmarshal(m, b)
}
func (m *DeductDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductDetail.Marshal(b, m, deterministic)
}
func (m *DeductDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductDetail.Merge(m, src)
}
func (m *DeductDetail) XXX_Size() int {
	return xxx_messageInfo_DeductDetail.Size(m)
}
func (m *DeductDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeductDetail proto.InternalMessageInfo

func (m *DeductDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeductDetail) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *DeductDetail) GetRecordsCode() string {
	if m != nil {
		return m.RecordsCode
	}
	return ""
}

func (m *DeductDetail) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *DeductDetail) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *DeductDetail) GetBatchDetailCode() string {
	if m != nil {
		return m.BatchDetailCode
	}
	return ""
}

func (m *DeductDetail) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DeductDetail) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *DeductDetail) GetUserIdLists() []string {
	if m != nil {
		return m.UserIdLists
	}
	return nil
}

func (m *DeductDetail) GetPetIdLists() []string {
	if m != nil {
		return m.PetIdLists
	}
	return nil
}

func (m *DeductDetail) GetThirdUserId() string {
	if m != nil {
		return m.ThirdUserId
	}
	return ""
}

func (m *DeductDetail) GetThirdPetId() string {
	if m != nil {
		return m.ThirdPetId
	}
	return ""
}

func (m *DeductDetail) GetThirdOrgId() string {
	if m != nil {
		return m.ThirdOrgId
	}
	return ""
}

func (m *DeductDetail) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *DeductDetail) GetRmaNumber() string {
	if m != nil {
		return m.RmaNumber
	}
	return ""
}

func (m *DeductDetail) GetOccurTime() string {
	if m != nil {
		return m.OccurTime
	}
	return ""
}

func (m *DeductDetail) GetOccurHospitalCode() string {
	if m != nil {
		return m.OccurHospitalCode
	}
	return ""
}

func (m *DeductDetail) GetOccurHospitalName() string {
	if m != nil {
		return m.OccurHospitalName
	}
	return ""
}

func (m *DeductDetail) GetCorpusBeforeChange() float32 {
	if m != nil {
		return m.CorpusBeforeChange
	}
	return 0
}

func (m *DeductDetail) GetCorpusChange() float32 {
	if m != nil {
		return m.CorpusChange
	}
	return 0
}

func (m *DeductDetail) GetCorpusAfterChange() float32 {
	if m != nil {
		return m.CorpusAfterChange
	}
	return 0
}

func (m *DeductDetail) GetTimesBeforeChange() int32 {
	if m != nil {
		return m.TimesBeforeChange
	}
	return 0
}

func (m *DeductDetail) GetTimesChange() int32 {
	if m != nil {
		return m.TimesChange
	}
	return 0
}

func (m *DeductDetail) GetTimesAfterChange() int32 {
	if m != nil {
		return m.TimesAfterChange
	}
	return 0
}

func (m *DeductDetail) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *DeductDetail) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *DeductDetail) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *DeductDetail) GetOperatorSource() int32 {
	if m != nil {
		return m.OperatorSource
	}
	return 0
}

func (m *DeductDetail) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DeductDetail) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *DeductDetail) GetUnitPrice() float32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *DeductDetail) GetEnsureCardStatus() int32 {
	if m != nil {
		return m.EnsureCardStatus
	}
	return 0
}

func (m *DeductDetail) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *DeductDetail) GetPayTypeName() string {
	if m != nil {
		return m.PayTypeName
	}
	return ""
}

func (m *DeductDetail) GetWrittenOffCode() string {
	if m != nil {
		return m.WrittenOffCode
	}
	return ""
}

//查看会员权益记录
type RightsRequest struct {
	Criteria             []*RightsCriteria `protobuf:"bytes,1,rep,name=Criteria,proto3" json:"Criteria"`
	Source               int32             `protobuf:"varint,2,opt,name=Source,proto3" json:"Source"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RightsRequest) Reset()         { *m = RightsRequest{} }
func (m *RightsRequest) String() string { return proto.CompactTextString(m) }
func (*RightsRequest) ProtoMessage()    {}
func (*RightsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{20}
}

func (m *RightsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsRequest.Unmarshal(m, b)
}
func (m *RightsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsRequest.Marshal(b, m, deterministic)
}
func (m *RightsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsRequest.Merge(m, src)
}
func (m *RightsRequest) XXX_Size() int {
	return xxx_messageInfo_RightsRequest.Size(m)
}
func (m *RightsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RightsRequest proto.InternalMessageInfo

func (m *RightsRequest) GetCriteria() []*RightsCriteria {
	if m != nil {
		return m.Criteria
	}
	return nil
}

func (m *RightsRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type RightsCriteria struct {
	HospitalCode         string   `protobuf:"bytes,1,opt,name=hospitalCode,proto3" json:"hospitalCode"`
	UserId               string   `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	PetId                string   `protobuf:"bytes,3,opt,name=petId,proto3" json:"petId"`
	EnsureCode           string   `protobuf:"bytes,4,opt,name=ensureCode,proto3" json:"ensureCode"`
	BatchCode            string   `protobuf:"bytes,5,opt,name=batchCode,proto3" json:"batchCode"`
	CategoryCode         string   `protobuf:"bytes,6,opt,name=categoryCode,proto3" json:"categoryCode"`
	Status               int32    `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	CompanyCode          string   `protobuf:"bytes,8,opt,name=companyCode,proto3" json:"companyCode"`
	CardType             int32    `protobuf:"varint,9,opt,name=cardType,proto3" json:"cardType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RightsCriteria) Reset()         { *m = RightsCriteria{} }
func (m *RightsCriteria) String() string { return proto.CompactTextString(m) }
func (*RightsCriteria) ProtoMessage()    {}
func (*RightsCriteria) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{21}
}

func (m *RightsCriteria) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsCriteria.Unmarshal(m, b)
}
func (m *RightsCriteria) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsCriteria.Marshal(b, m, deterministic)
}
func (m *RightsCriteria) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsCriteria.Merge(m, src)
}
func (m *RightsCriteria) XXX_Size() int {
	return xxx_messageInfo_RightsCriteria.Size(m)
}
func (m *RightsCriteria) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsCriteria.DiscardUnknown(m)
}

var xxx_messageInfo_RightsCriteria proto.InternalMessageInfo

func (m *RightsCriteria) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *RightsCriteria) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RightsCriteria) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RightsCriteria) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *RightsCriteria) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *RightsCriteria) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *RightsCriteria) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *RightsCriteria) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *RightsCriteria) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

type RightsResponse struct {
	Code                 int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Result               *RightsResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RightsResponse) Reset()         { *m = RightsResponse{} }
func (m *RightsResponse) String() string { return proto.CompactTextString(m) }
func (*RightsResponse) ProtoMessage()    {}
func (*RightsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{22}
}

func (m *RightsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsResponse.Unmarshal(m, b)
}
func (m *RightsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsResponse.Marshal(b, m, deterministic)
}
func (m *RightsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsResponse.Merge(m, src)
}
func (m *RightsResponse) XXX_Size() int {
	return xxx_messageInfo_RightsResponse.Size(m)
}
func (m *RightsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RightsResponse proto.InternalMessageInfo

func (m *RightsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RightsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RightsResponse) GetResult() *RightsResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type RightsResult struct {
	Result               []*RightsDetail `protobuf:"bytes,1,rep,name=Result,proto3" json:"Result"`
	Message              string          `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message"`
	SystemError          string          `protobuf:"bytes,3,opt,name=SystemError,proto3" json:"SystemError"`
	BusinessError        string          `protobuf:"bytes,4,opt,name=BusinessError,proto3" json:"BusinessError"`
	StatusCode           int32           `protobuf:"varint,5,opt,name=StatusCode,proto3" json:"StatusCode"`
	Extensions           *Extensions     `protobuf:"bytes,6,opt,name=extensions,proto3" json:"extensions"`
	Success              bool            `protobuf:"varint,7,opt,name=Success,proto3" json:"Success"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RightsResult) Reset()         { *m = RightsResult{} }
func (m *RightsResult) String() string { return proto.CompactTextString(m) }
func (*RightsResult) ProtoMessage()    {}
func (*RightsResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{23}
}

func (m *RightsResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsResult.Unmarshal(m, b)
}
func (m *RightsResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsResult.Marshal(b, m, deterministic)
}
func (m *RightsResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsResult.Merge(m, src)
}
func (m *RightsResult) XXX_Size() int {
	return xxx_messageInfo_RightsResult.Size(m)
}
func (m *RightsResult) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsResult.DiscardUnknown(m)
}

var xxx_messageInfo_RightsResult proto.InternalMessageInfo

func (m *RightsResult) GetResult() []*RightsDetail {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *RightsResult) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RightsResult) GetSystemError() string {
	if m != nil {
		return m.SystemError
	}
	return ""
}

func (m *RightsResult) GetBusinessError() string {
	if m != nil {
		return m.BusinessError
	}
	return ""
}

func (m *RightsResult) GetStatusCode() int32 {
	if m != nil {
		return m.StatusCode
	}
	return 0
}

func (m *RightsResult) GetExtensions() *Extensions {
	if m != nil {
		return m.Extensions
	}
	return nil
}

func (m *RightsResult) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type RightsDetail struct {
	Id                   int32                `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	HospitalCode         string               `protobuf:"bytes,2,opt,name=hospitalCode,proto3" json:"hospitalCode"`
	UserId               string               `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId"`
	PetId                string               `protobuf:"bytes,4,opt,name=petId,proto3" json:"petId"`
	UserIdLists          []string             `protobuf:"bytes,5,rep,name=userIdLists,proto3" json:"userIdLists"`
	PetIdLists           []string             `protobuf:"bytes,6,rep,name=petIdLists,proto3" json:"petIdLists"`
	ThirdUserId          string               `protobuf:"bytes,7,opt,name=thirdUserId,proto3" json:"thirdUserId"`
	ThirdPetId           string               `protobuf:"bytes,8,opt,name=thirdPetId,proto3" json:"thirdPetId"`
	ThirdOrgId           string               `protobuf:"bytes,9,opt,name=thirdOrgId,proto3" json:"thirdOrgId"`
	EnsureCode           string               `protobuf:"bytes,10,opt,name=ensureCode,proto3" json:"ensureCode"`
	BatchCode            string               `protobuf:"bytes,11,opt,name=batchCode,proto3" json:"batchCode"`
	CategoryCode         string               `protobuf:"bytes,12,opt,name=categoryCode,proto3" json:"categoryCode"`
	CostMoney            float32              `protobuf:"fixed32,13,opt,name=costMoney,proto3" json:"costMoney"`
	CorpusBalance        float32              `protobuf:"fixed32,14,opt,name=corpusBalance,proto3" json:"corpusBalance"`
	StartDate            string               `protobuf:"bytes,15,opt,name=startDate,proto3" json:"startDate"`
	ExpiryDate           string               `protobuf:"bytes,16,opt,name=expiryDate,proto3" json:"expiryDate"`
	BatchStatus          int32                `protobuf:"varint,17,opt,name=batchStatus,proto3" json:"batchStatus"`
	RunOut               int32                `protobuf:"varint,18,opt,name=runOut,proto3" json:"runOut"`
	CreateSource         int32                `protobuf:"varint,19,opt,name=createSource,proto3" json:"createSource"`
	CreateId             int32                `protobuf:"varint,20,opt,name=createId,proto3" json:"createId"`
	CreateName           string               `protobuf:"bytes,21,opt,name=createName,proto3" json:"createName"`
	CreateTime           string               `protobuf:"bytes,22,opt,name=createTime,proto3" json:"createTime"`
	LastUpdateSource     int32                `protobuf:"varint,23,opt,name=lastUpdateSource,proto3" json:"lastUpdateSource"`
	LastUpdateUserId     int32                `protobuf:"varint,24,opt,name=lastUpdateUserId,proto3" json:"lastUpdateUserId"`
	LastUpdateUserName   string               `protobuf:"bytes,25,opt,name=lastUpdateUserName,proto3" json:"lastUpdateUserName"`
	LastUpdateTime       string               `protobuf:"bytes,26,opt,name=lastUpdateTime,proto3" json:"lastUpdateTime"`
	Remark               string               `protobuf:"bytes,27,opt,name=remark,proto3" json:"remark"`
	BatchDetails         []*BatchDetails      `protobuf:"bytes,28,rep,name=batchDetails,proto3" json:"batchDetails"`
	CategoryDiscounts    []*CategoryDiscounts `protobuf:"bytes,29,rep,name=categoryDiscounts,proto3" json:"categoryDiscounts"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *RightsDetail) Reset()         { *m = RightsDetail{} }
func (m *RightsDetail) String() string { return proto.CompactTextString(m) }
func (*RightsDetail) ProtoMessage()    {}
func (*RightsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{24}
}

func (m *RightsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RightsDetail.Unmarshal(m, b)
}
func (m *RightsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RightsDetail.Marshal(b, m, deterministic)
}
func (m *RightsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RightsDetail.Merge(m, src)
}
func (m *RightsDetail) XXX_Size() int {
	return xxx_messageInfo_RightsDetail.Size(m)
}
func (m *RightsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_RightsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_RightsDetail proto.InternalMessageInfo

func (m *RightsDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RightsDetail) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *RightsDetail) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RightsDetail) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RightsDetail) GetUserIdLists() []string {
	if m != nil {
		return m.UserIdLists
	}
	return nil
}

func (m *RightsDetail) GetPetIdLists() []string {
	if m != nil {
		return m.PetIdLists
	}
	return nil
}

func (m *RightsDetail) GetThirdUserId() string {
	if m != nil {
		return m.ThirdUserId
	}
	return ""
}

func (m *RightsDetail) GetThirdPetId() string {
	if m != nil {
		return m.ThirdPetId
	}
	return ""
}

func (m *RightsDetail) GetThirdOrgId() string {
	if m != nil {
		return m.ThirdOrgId
	}
	return ""
}

func (m *RightsDetail) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *RightsDetail) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *RightsDetail) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *RightsDetail) GetCostMoney() float32 {
	if m != nil {
		return m.CostMoney
	}
	return 0
}

func (m *RightsDetail) GetCorpusBalance() float32 {
	if m != nil {
		return m.CorpusBalance
	}
	return 0
}

func (m *RightsDetail) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *RightsDetail) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *RightsDetail) GetBatchStatus() int32 {
	if m != nil {
		return m.BatchStatus
	}
	return 0
}

func (m *RightsDetail) GetRunOut() int32 {
	if m != nil {
		return m.RunOut
	}
	return 0
}

func (m *RightsDetail) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *RightsDetail) GetCreateId() int32 {
	if m != nil {
		return m.CreateId
	}
	return 0
}

func (m *RightsDetail) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *RightsDetail) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *RightsDetail) GetLastUpdateSource() int32 {
	if m != nil {
		return m.LastUpdateSource
	}
	return 0
}

func (m *RightsDetail) GetLastUpdateUserId() int32 {
	if m != nil {
		return m.LastUpdateUserId
	}
	return 0
}

func (m *RightsDetail) GetLastUpdateUserName() string {
	if m != nil {
		return m.LastUpdateUserName
	}
	return ""
}

func (m *RightsDetail) GetLastUpdateTime() string {
	if m != nil {
		return m.LastUpdateTime
	}
	return ""
}

func (m *RightsDetail) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *RightsDetail) GetBatchDetails() []*BatchDetails {
	if m != nil {
		return m.BatchDetails
	}
	return nil
}

func (m *RightsDetail) GetCategoryDiscounts() []*CategoryDiscounts {
	if m != nil {
		return m.CategoryDiscounts
	}
	return nil
}

type BatchDetails struct {
	Id                   int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	EnsureCode           string                 `protobuf:"bytes,2,opt,name=ensureCode,proto3" json:"ensureCode"`
	BatchCode            string                 `protobuf:"bytes,3,opt,name=batchCode,proto3" json:"batchCode"`
	BatchDetailCode      string                 `protobuf:"bytes,4,opt,name=batchDetailCode,proto3" json:"batchDetailCode"`
	CategoryCode         string                 `protobuf:"bytes,5,opt,name=categoryCode,proto3" json:"categoryCode"`
	CategoryDetailCode   string                 `protobuf:"bytes,6,opt,name=categoryDetailCode,proto3" json:"categoryDetailCode"`
	CostMoney            float32                `protobuf:"fixed32,7,opt,name=costMoney,proto3" json:"costMoney"`
	ChargeTimes          int32                  `protobuf:"varint,8,opt,name=chargeTimes,proto3" json:"chargeTimes"`
	CorpusBalance        float32                `protobuf:"fixed32,9,opt,name=corpusBalance,proto3" json:"corpusBalance"`
	LeftTimes            int32                  `protobuf:"varint,10,opt,name=leftTimes,proto3" json:"leftTimes"`
	CreateSource         int32                  `protobuf:"varint,11,opt,name=createSource,proto3" json:"createSource"`
	CreateId             int32                  `protobuf:"varint,12,opt,name=createId,proto3" json:"createId"`
	CreateName           string                 `protobuf:"bytes,13,opt,name=createName,proto3" json:"createName"`
	CreateTime           string                 `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime"`
	LastUpdateSource     int32                  `protobuf:"varint,15,opt,name=lastUpdateSource,proto3" json:"lastUpdateSource"`
	LastUpdateUserId     int32                  `protobuf:"varint,16,opt,name=lastUpdateUserId,proto3" json:"lastUpdateUserId"`
	LastUpdateUserName   string                 `protobuf:"bytes,17,opt,name=lastUpdateUserName,proto3" json:"lastUpdateUserName"`
	LastUpdateTime       string                 `protobuf:"bytes,18,opt,name=lastUpdateTime,proto3" json:"lastUpdateTime"`
	CategoryDetail       *CategoryDetail        `protobuf:"bytes,19,opt,name=categoryDetail,proto3" json:"categoryDetail"`
	DiscountLevelVoList  []*DiscountLevelVoList `protobuf:"bytes,20,rep,name=discountLevelVoList,proto3" json:"discountLevelVoList"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchDetails) Reset()         { *m = BatchDetails{} }
func (m *BatchDetails) String() string { return proto.CompactTextString(m) }
func (*BatchDetails) ProtoMessage()    {}
func (*BatchDetails) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{25}
}

func (m *BatchDetails) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDetails.Unmarshal(m, b)
}
func (m *BatchDetails) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDetails.Marshal(b, m, deterministic)
}
func (m *BatchDetails) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDetails.Merge(m, src)
}
func (m *BatchDetails) XXX_Size() int {
	return xxx_messageInfo_BatchDetails.Size(m)
}
func (m *BatchDetails) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDetails.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDetails proto.InternalMessageInfo

func (m *BatchDetails) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchDetails) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *BatchDetails) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *BatchDetails) GetBatchDetailCode() string {
	if m != nil {
		return m.BatchDetailCode
	}
	return ""
}

func (m *BatchDetails) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *BatchDetails) GetCategoryDetailCode() string {
	if m != nil {
		return m.CategoryDetailCode
	}
	return ""
}

func (m *BatchDetails) GetCostMoney() float32 {
	if m != nil {
		return m.CostMoney
	}
	return 0
}

func (m *BatchDetails) GetChargeTimes() int32 {
	if m != nil {
		return m.ChargeTimes
	}
	return 0
}

func (m *BatchDetails) GetCorpusBalance() float32 {
	if m != nil {
		return m.CorpusBalance
	}
	return 0
}

func (m *BatchDetails) GetLeftTimes() int32 {
	if m != nil {
		return m.LeftTimes
	}
	return 0
}

func (m *BatchDetails) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *BatchDetails) GetCreateId() int32 {
	if m != nil {
		return m.CreateId
	}
	return 0
}

func (m *BatchDetails) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *BatchDetails) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *BatchDetails) GetLastUpdateSource() int32 {
	if m != nil {
		return m.LastUpdateSource
	}
	return 0
}

func (m *BatchDetails) GetLastUpdateUserId() int32 {
	if m != nil {
		return m.LastUpdateUserId
	}
	return 0
}

func (m *BatchDetails) GetLastUpdateUserName() string {
	if m != nil {
		return m.LastUpdateUserName
	}
	return ""
}

func (m *BatchDetails) GetLastUpdateTime() string {
	if m != nil {
		return m.LastUpdateTime
	}
	return ""
}

func (m *BatchDetails) GetCategoryDetail() *CategoryDetail {
	if m != nil {
		return m.CategoryDetail
	}
	return nil
}

func (m *BatchDetails) GetDiscountLevelVoList() []*DiscountLevelVoList {
	if m != nil {
		return m.DiscountLevelVoList
	}
	return nil
}

type CategoryDetail struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryDetailCode   string   `protobuf:"bytes,2,opt,name=categoryDetailCode,proto3" json:"categoryDetailCode"`
	CategoryCode         string   `protobuf:"bytes,3,opt,name=categoryCode,proto3" json:"categoryCode"`
	ProductName          string   `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	OriginalPrice        float32  `protobuf:"fixed32,5,opt,name=originalPrice,proto3" json:"originalPrice"`
	UnitPrice            float32  `protobuf:"fixed32,6,opt,name=unitPrice,proto3" json:"unitPrice"`
	CorpusMoney          float32  `protobuf:"fixed32,7,opt,name=corpusMoney,proto3" json:"corpusMoney"`
	CorpusTimes          int32    `protobuf:"varint,8,opt,name=corpusTimes,proto3" json:"corpusTimes"`
	CreateId             int32    `protobuf:"varint,9,opt,name=createId,proto3" json:"createId"`
	CreateName           string   `protobuf:"bytes,10,opt,name=createName,proto3" json:"createName"`
	CreateTime           string   `protobuf:"bytes,11,opt,name=createTime,proto3" json:"createTime"`
	LastUpdateUserId     int32    `protobuf:"varint,12,opt,name=lastUpdateUserId,proto3" json:"lastUpdateUserId"`
	LastUpdateUserName   string   `protobuf:"bytes,13,opt,name=lastUpdateUserName,proto3" json:"lastUpdateUserName"`
	LastUpdateTime       string   `protobuf:"bytes,14,opt,name=lastUpdateTime,proto3" json:"lastUpdateTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CategoryDetail) Reset()         { *m = CategoryDetail{} }
func (m *CategoryDetail) String() string { return proto.CompactTextString(m) }
func (*CategoryDetail) ProtoMessage()    {}
func (*CategoryDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{26}
}

func (m *CategoryDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryDetail.Unmarshal(m, b)
}
func (m *CategoryDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryDetail.Marshal(b, m, deterministic)
}
func (m *CategoryDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryDetail.Merge(m, src)
}
func (m *CategoryDetail) XXX_Size() int {
	return xxx_messageInfo_CategoryDetail.Size(m)
}
func (m *CategoryDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryDetail proto.InternalMessageInfo

func (m *CategoryDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CategoryDetail) GetCategoryDetailCode() string {
	if m != nil {
		return m.CategoryDetailCode
	}
	return ""
}

func (m *CategoryDetail) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *CategoryDetail) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CategoryDetail) GetOriginalPrice() float32 {
	if m != nil {
		return m.OriginalPrice
	}
	return 0
}

func (m *CategoryDetail) GetUnitPrice() float32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *CategoryDetail) GetCorpusMoney() float32 {
	if m != nil {
		return m.CorpusMoney
	}
	return 0
}

func (m *CategoryDetail) GetCorpusTimes() int32 {
	if m != nil {
		return m.CorpusTimes
	}
	return 0
}

func (m *CategoryDetail) GetCreateId() int32 {
	if m != nil {
		return m.CreateId
	}
	return 0
}

func (m *CategoryDetail) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *CategoryDetail) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CategoryDetail) GetLastUpdateUserId() int32 {
	if m != nil {
		return m.LastUpdateUserId
	}
	return 0
}

func (m *CategoryDetail) GetLastUpdateUserName() string {
	if m != nil {
		return m.LastUpdateUserName
	}
	return ""
}

func (m *CategoryDetail) GetLastUpdateTime() string {
	if m != nil {
		return m.LastUpdateTime
	}
	return ""
}

type CategoryDiscounts struct {
	Id                   int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryCode         string                 `protobuf:"bytes,2,opt,name=categoryCode,proto3" json:"categoryCode"`
	DictId               string                 `protobuf:"bytes,3,opt,name=dictId,proto3" json:"dictId"`
	ProductTypeName      string                 `protobuf:"bytes,4,opt,name=productTypeName,proto3" json:"productTypeName"`
	Discount             string                 `protobuf:"bytes,5,opt,name=discount,proto3" json:"discount"`
	OperatorTime         string                 `protobuf:"bytes,6,opt,name=operatorTime,proto3" json:"operatorTime"`
	OperatorId           int32                  `protobuf:"varint,7,opt,name=operatorId,proto3" json:"operatorId"`
	OperatorName         string                 `protobuf:"bytes,8,opt,name=operatorName,proto3" json:"operatorName"`
	DiscountLevelVoList  []*DiscountLevelVoList `protobuf:"bytes,9,rep,name=discountLevelVoList,proto3" json:"discountLevelVoList"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CategoryDiscounts) Reset()         { *m = CategoryDiscounts{} }
func (m *CategoryDiscounts) String() string { return proto.CompactTextString(m) }
func (*CategoryDiscounts) ProtoMessage()    {}
func (*CategoryDiscounts) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{27}
}

func (m *CategoryDiscounts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryDiscounts.Unmarshal(m, b)
}
func (m *CategoryDiscounts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryDiscounts.Marshal(b, m, deterministic)
}
func (m *CategoryDiscounts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryDiscounts.Merge(m, src)
}
func (m *CategoryDiscounts) XXX_Size() int {
	return xxx_messageInfo_CategoryDiscounts.Size(m)
}
func (m *CategoryDiscounts) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryDiscounts.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryDiscounts proto.InternalMessageInfo

func (m *CategoryDiscounts) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CategoryDiscounts) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *CategoryDiscounts) GetDictId() string {
	if m != nil {
		return m.DictId
	}
	return ""
}

func (m *CategoryDiscounts) GetProductTypeName() string {
	if m != nil {
		return m.ProductTypeName
	}
	return ""
}

func (m *CategoryDiscounts) GetDiscount() string {
	if m != nil {
		return m.Discount
	}
	return ""
}

func (m *CategoryDiscounts) GetOperatorTime() string {
	if m != nil {
		return m.OperatorTime
	}
	return ""
}

func (m *CategoryDiscounts) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *CategoryDiscounts) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *CategoryDiscounts) GetDiscountLevelVoList() []*DiscountLevelVoList {
	if m != nil {
		return m.DiscountLevelVoList
	}
	return nil
}

type DiscountLevelVoList struct {
	ProductCode          string   `protobuf:"bytes,1,opt,name=productCode,proto3" json:"productCode"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountLevelVoList) Reset()         { *m = DiscountLevelVoList{} }
func (m *DiscountLevelVoList) String() string { return proto.CompactTextString(m) }
func (*DiscountLevelVoList) ProtoMessage()    {}
func (*DiscountLevelVoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{28}
}

func (m *DiscountLevelVoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountLevelVoList.Unmarshal(m, b)
}
func (m *DiscountLevelVoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountLevelVoList.Marshal(b, m, deterministic)
}
func (m *DiscountLevelVoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountLevelVoList.Merge(m, src)
}
func (m *DiscountLevelVoList) XXX_Size() int {
	return xxx_messageInfo_DiscountLevelVoList.Size(m)
}
func (m *DiscountLevelVoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountLevelVoList.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountLevelVoList proto.InternalMessageInfo

func (m *DiscountLevelVoList) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *DiscountLevelVoList) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

//通知开卡退卡续卡等参数
type NotifyCardRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=UserId,proto3" json:"UserId"`
	HospitalId           string   `protobuf:"bytes,2,opt,name=HospitalId,proto3" json:"HospitalId"`
	HospitalName         string   `protobuf:"bytes,3,opt,name=HospitalName,proto3" json:"HospitalName"`
	CardNo               string   `protobuf:"bytes,4,opt,name=CardNo,proto3" json:"CardNo"`
	CardType             string   `protobuf:"bytes,5,opt,name=CardType,proto3" json:"CardType"`
	EffectedStartDate    string   `protobuf:"bytes,6,opt,name=EffectedStartDate,proto3" json:"EffectedStartDate"`
	EffectedEndDate      string   `protobuf:"bytes,7,opt,name=EffectedEndDate,proto3" json:"EffectedEndDate"`
	CardStatus           string   `protobuf:"bytes,8,opt,name=CardStatus,proto3" json:"CardStatus"`
	FinanceCode          string   `protobuf:"bytes,9,opt,name=FinanceCode,proto3" json:"FinanceCode"`
	Batchcode            string   `protobuf:"bytes,10,opt,name=Batchcode,proto3" json:"Batchcode"`
	Presellcode          string   `protobuf:"bytes,11,opt,name=Presellcode,proto3" json:"Presellcode"`
	Presellname          string   `protobuf:"bytes,12,opt,name=Presellname,proto3" json:"Presellname"`
	Presellcount         int32    `protobuf:"varint,13,opt,name=Presellcount,proto3" json:"Presellcount"`
	Presellunit          string   `protobuf:"bytes,14,opt,name=Presellunit,proto3" json:"Presellunit"`
	PayWay               int32    `protobuf:"varint,15,opt,name=PayWay,proto3" json:"PayWay"`
	AllAmount            string   `protobuf:"bytes,16,opt,name=AllAmount,proto3" json:"AllAmount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyCardRequest) Reset()         { *m = NotifyCardRequest{} }
func (m *NotifyCardRequest) String() string { return proto.CompactTextString(m) }
func (*NotifyCardRequest) ProtoMessage()    {}
func (*NotifyCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{29}
}

func (m *NotifyCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyCardRequest.Unmarshal(m, b)
}
func (m *NotifyCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyCardRequest.Marshal(b, m, deterministic)
}
func (m *NotifyCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyCardRequest.Merge(m, src)
}
func (m *NotifyCardRequest) XXX_Size() int {
	return xxx_messageInfo_NotifyCardRequest.Size(m)
}
func (m *NotifyCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyCardRequest proto.InternalMessageInfo

func (m *NotifyCardRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *NotifyCardRequest) GetHospitalId() string {
	if m != nil {
		return m.HospitalId
	}
	return ""
}

func (m *NotifyCardRequest) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *NotifyCardRequest) GetCardNo() string {
	if m != nil {
		return m.CardNo
	}
	return ""
}

func (m *NotifyCardRequest) GetCardType() string {
	if m != nil {
		return m.CardType
	}
	return ""
}

func (m *NotifyCardRequest) GetEffectedStartDate() string {
	if m != nil {
		return m.EffectedStartDate
	}
	return ""
}

func (m *NotifyCardRequest) GetEffectedEndDate() string {
	if m != nil {
		return m.EffectedEndDate
	}
	return ""
}

func (m *NotifyCardRequest) GetCardStatus() string {
	if m != nil {
		return m.CardStatus
	}
	return ""
}

func (m *NotifyCardRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *NotifyCardRequest) GetBatchcode() string {
	if m != nil {
		return m.Batchcode
	}
	return ""
}

func (m *NotifyCardRequest) GetPresellcode() string {
	if m != nil {
		return m.Presellcode
	}
	return ""
}

func (m *NotifyCardRequest) GetPresellname() string {
	if m != nil {
		return m.Presellname
	}
	return ""
}

func (m *NotifyCardRequest) GetPresellcount() int32 {
	if m != nil {
		return m.Presellcount
	}
	return 0
}

func (m *NotifyCardRequest) GetPresellunit() string {
	if m != nil {
		return m.Presellunit
	}
	return ""
}

func (m *NotifyCardRequest) GetPayWay() int32 {
	if m != nil {
		return m.PayWay
	}
	return 0
}

func (m *NotifyCardRequest) GetAllAmount() string {
	if m != nil {
		return m.AllAmount
	}
	return ""
}

//HIS开卡-ERP支付的参数体系
type NewCardPayRequest struct {
	HospitalCode           string                    `protobuf:"bytes,1,opt,name=HospitalCode,proto3" json:"HospitalCode"`
	EnsureCode             string                    `protobuf:"bytes,2,opt,name=EnsureCode,proto3" json:"EnsureCode"`
	BatchCode              string                    `protobuf:"bytes,3,opt,name=BatchCode,proto3" json:"BatchCode"`
	PaymentType            int32                     `protobuf:"varint,4,opt,name=PaymentType,proto3" json:"PaymentType"`
	WrittenOffCode         string                    `protobuf:"bytes,5,opt,name=WrittenOffCode,proto3" json:"WrittenOffCode"`
	OperatorSource         int32                     `protobuf:"varint,6,opt,name=OperatorSource,proto3" json:"OperatorSource"`
	OperatorId             int32                     `protobuf:"varint,7,opt,name=OperatorId,proto3" json:"OperatorId"`
	OperatorName           string                    `protobuf:"bytes,8,opt,name=OperatorName,proto3" json:"OperatorName"`
	EnsureCardsRecordsPays []*EnsureCardsRecordsPays `protobuf:"bytes,9,rep,name=EnsureCardsRecordsPays,proto3" json:"EnsureCardsRecordsPays"`
	OccurHospitalName      string                    `protobuf:"bytes,10,opt,name=OccurHospitalName,proto3" json:"OccurHospitalName"`
	CompanyCode            string                    `protobuf:"bytes,11,opt,name=CompanyCode,proto3" json:"CompanyCode"`
	CardType               int32                     `protobuf:"varint,12,opt,name=CardType,proto3" json:"CardType"`
	UserId                 string                    `protobuf:"bytes,13,opt,name=UserId,proto3" json:"UserId"`
	OrderId                string                    `protobuf:"bytes,14,opt,name=OrderId,proto3" json:"OrderId"`
	Presellcode            string                    `protobuf:"bytes,15,opt,name=Presellcode,proto3" json:"Presellcode"`
	Presellname            string                    `protobuf:"bytes,16,opt,name=Presellname,proto3" json:"Presellname"`
	Presellcount           int32                     `protobuf:"varint,17,opt,name=Presellcount,proto3" json:"Presellcount"`
	Presellunit            string                    `protobuf:"bytes,18,opt,name=Presellunit,proto3" json:"Presellunit"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *NewCardPayRequest) Reset()         { *m = NewCardPayRequest{} }
func (m *NewCardPayRequest) String() string { return proto.CompactTextString(m) }
func (*NewCardPayRequest) ProtoMessage()    {}
func (*NewCardPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{30}
}

func (m *NewCardPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCardPayRequest.Unmarshal(m, b)
}
func (m *NewCardPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCardPayRequest.Marshal(b, m, deterministic)
}
func (m *NewCardPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCardPayRequest.Merge(m, src)
}
func (m *NewCardPayRequest) XXX_Size() int {
	return xxx_messageInfo_NewCardPayRequest.Size(m)
}
func (m *NewCardPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCardPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewCardPayRequest proto.InternalMessageInfo

func (m *NewCardPayRequest) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *NewCardPayRequest) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *NewCardPayRequest) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *NewCardPayRequest) GetPaymentType() int32 {
	if m != nil {
		return m.PaymentType
	}
	return 0
}

func (m *NewCardPayRequest) GetWrittenOffCode() string {
	if m != nil {
		return m.WrittenOffCode
	}
	return ""
}

func (m *NewCardPayRequest) GetOperatorSource() int32 {
	if m != nil {
		return m.OperatorSource
	}
	return 0
}

func (m *NewCardPayRequest) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *NewCardPayRequest) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *NewCardPayRequest) GetEnsureCardsRecordsPays() []*EnsureCardsRecordsPays {
	if m != nil {
		return m.EnsureCardsRecordsPays
	}
	return nil
}

func (m *NewCardPayRequest) GetOccurHospitalName() string {
	if m != nil {
		return m.OccurHospitalName
	}
	return ""
}

func (m *NewCardPayRequest) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *NewCardPayRequest) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *NewCardPayRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *NewCardPayRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *NewCardPayRequest) GetPresellcode() string {
	if m != nil {
		return m.Presellcode
	}
	return ""
}

func (m *NewCardPayRequest) GetPresellname() string {
	if m != nil {
		return m.Presellname
	}
	return ""
}

func (m *NewCardPayRequest) GetPresellcount() int32 {
	if m != nil {
		return m.Presellcount
	}
	return 0
}

func (m *NewCardPayRequest) GetPresellunit() string {
	if m != nil {
		return m.Presellunit
	}
	return ""
}

type EnsureCardsRecordsPays struct {
	PayType              int32    `protobuf:"varint,1,opt,name=PayType,proto3" json:"PayType"`
	PayMoney             string   `protobuf:"bytes,2,opt,name=PayMoney,proto3" json:"PayMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnsureCardsRecordsPays) Reset()         { *m = EnsureCardsRecordsPays{} }
func (m *EnsureCardsRecordsPays) String() string { return proto.CompactTextString(m) }
func (*EnsureCardsRecordsPays) ProtoMessage()    {}
func (*EnsureCardsRecordsPays) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{31}
}

func (m *EnsureCardsRecordsPays) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnsureCardsRecordsPays.Unmarshal(m, b)
}
func (m *EnsureCardsRecordsPays) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnsureCardsRecordsPays.Marshal(b, m, deterministic)
}
func (m *EnsureCardsRecordsPays) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnsureCardsRecordsPays.Merge(m, src)
}
func (m *EnsureCardsRecordsPays) XXX_Size() int {
	return xxx_messageInfo_EnsureCardsRecordsPays.Size(m)
}
func (m *EnsureCardsRecordsPays) XXX_DiscardUnknown() {
	xxx_messageInfo_EnsureCardsRecordsPays.DiscardUnknown(m)
}

var xxx_messageInfo_EnsureCardsRecordsPays proto.InternalMessageInfo

func (m *EnsureCardsRecordsPays) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *EnsureCardsRecordsPays) GetPayMoney() string {
	if m != nil {
		return m.PayMoney
	}
	return ""
}

//HIS抵扣
type DeductRightRequest struct {
	Criteria             []*DeductRightCriteria `protobuf:"bytes,1,rep,name=Criteria,proto3" json:"Criteria"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *DeductRightRequest) Reset()         { *m = DeductRightRequest{} }
func (m *DeductRightRequest) String() string { return proto.CompactTextString(m) }
func (*DeductRightRequest) ProtoMessage()    {}
func (*DeductRightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{32}
}

func (m *DeductRightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductRightRequest.Unmarshal(m, b)
}
func (m *DeductRightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductRightRequest.Marshal(b, m, deterministic)
}
func (m *DeductRightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductRightRequest.Merge(m, src)
}
func (m *DeductRightRequest) XXX_Size() int {
	return xxx_messageInfo_DeductRightRequest.Size(m)
}
func (m *DeductRightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductRightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeductRightRequest proto.InternalMessageInfo

func (m *DeductRightRequest) GetCriteria() []*DeductRightCriteria {
	if m != nil {
		return m.Criteria
	}
	return nil
}

type DeductRightCriteria struct {
	EnsureCode           string   `protobuf:"bytes,1,opt,name=EnsureCode,proto3" json:"EnsureCode"`
	BatchCode            string   `protobuf:"bytes,2,opt,name=BatchCode,proto3" json:"BatchCode"`
	BatchDetailCode      string   `protobuf:"bytes,3,opt,name=BatchDetailCode,proto3" json:"BatchDetailCode"`
	CorpusChange         float32  `protobuf:"fixed32,4,opt,name=corpusChange,proto3" json:"corpusChange"`
	TimesChange          int32    `protobuf:"varint,5,opt,name=TimesChange,proto3" json:"TimesChange"`
	CategoryCode         string   `protobuf:"bytes,6,opt,name=CategoryCode,proto3" json:"CategoryCode"`
	OccurHospitalCode    string   `protobuf:"bytes,7,opt,name=OccurHospitalCode,proto3" json:"OccurHospitalCode"`
	OccurHospitalName    string   `protobuf:"bytes,8,opt,name=OccurHospitalName,proto3" json:"OccurHospitalName"`
	OrderNumber          string   `protobuf:"bytes,9,opt,name=OrderNumber,proto3" json:"OrderNumber"`
	OperateType          int32    `protobuf:"varint,10,opt,name=OperateType,proto3" json:"OperateType"`
	OperatorId           int32    `protobuf:"varint,11,opt,name=OperatorId,proto3" json:"OperatorId"`
	OperatorName         string   `protobuf:"bytes,12,opt,name=OperatorName,proto3" json:"OperatorName"`
	Source               int32    `protobuf:"varint,13,opt,name=Source,proto3" json:"Source"`
	Remark               string   `protobuf:"bytes,14,opt,name=Remark,proto3" json:"Remark"`
	ThirdUserId          string   `protobuf:"bytes,15,opt,name=ThirdUserId,proto3" json:"ThirdUserId"`
	ThirdPetId           string   `protobuf:"bytes,16,opt,name=ThirdPetId,proto3" json:"ThirdPetId"`
	ThirdOrgId           string   `protobuf:"bytes,17,opt,name=ThirdOrgId,proto3" json:"ThirdOrgId"`
	CompanyCode          string   `protobuf:"bytes,18,opt,name=CompanyCode,proto3" json:"CompanyCode"`
	CardType             int32    `protobuf:"varint,19,opt,name=CardType,proto3" json:"CardType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductRightCriteria) Reset()         { *m = DeductRightCriteria{} }
func (m *DeductRightCriteria) String() string { return proto.CompactTextString(m) }
func (*DeductRightCriteria) ProtoMessage()    {}
func (*DeductRightCriteria) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{33}
}

func (m *DeductRightCriteria) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductRightCriteria.Unmarshal(m, b)
}
func (m *DeductRightCriteria) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductRightCriteria.Marshal(b, m, deterministic)
}
func (m *DeductRightCriteria) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductRightCriteria.Merge(m, src)
}
func (m *DeductRightCriteria) XXX_Size() int {
	return xxx_messageInfo_DeductRightCriteria.Size(m)
}
func (m *DeductRightCriteria) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductRightCriteria.DiscardUnknown(m)
}

var xxx_messageInfo_DeductRightCriteria proto.InternalMessageInfo

func (m *DeductRightCriteria) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *DeductRightCriteria) GetBatchCode() string {
	if m != nil {
		return m.BatchCode
	}
	return ""
}

func (m *DeductRightCriteria) GetBatchDetailCode() string {
	if m != nil {
		return m.BatchDetailCode
	}
	return ""
}

func (m *DeductRightCriteria) GetCorpusChange() float32 {
	if m != nil {
		return m.CorpusChange
	}
	return 0
}

func (m *DeductRightCriteria) GetTimesChange() int32 {
	if m != nil {
		return m.TimesChange
	}
	return 0
}

func (m *DeductRightCriteria) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *DeductRightCriteria) GetOccurHospitalCode() string {
	if m != nil {
		return m.OccurHospitalCode
	}
	return ""
}

func (m *DeductRightCriteria) GetOccurHospitalName() string {
	if m != nil {
		return m.OccurHospitalName
	}
	return ""
}

func (m *DeductRightCriteria) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *DeductRightCriteria) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *DeductRightCriteria) GetOperatorId() int32 {
	if m != nil {
		return m.OperatorId
	}
	return 0
}

func (m *DeductRightCriteria) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *DeductRightCriteria) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *DeductRightCriteria) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DeductRightCriteria) GetThirdUserId() string {
	if m != nil {
		return m.ThirdUserId
	}
	return ""
}

func (m *DeductRightCriteria) GetThirdPetId() string {
	if m != nil {
		return m.ThirdPetId
	}
	return ""
}

func (m *DeductRightCriteria) GetThirdOrgId() string {
	if m != nil {
		return m.ThirdOrgId
	}
	return ""
}

func (m *DeductRightCriteria) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *DeductRightCriteria) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

//HIS抵扣响应
type DeductRightResponse struct {
	Code                 int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Result               *DeductRightResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *DeductRightResponse) Reset()         { *m = DeductRightResponse{} }
func (m *DeductRightResponse) String() string { return proto.CompactTextString(m) }
func (*DeductRightResponse) ProtoMessage()    {}
func (*DeductRightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{34}
}

func (m *DeductRightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductRightResponse.Unmarshal(m, b)
}
func (m *DeductRightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductRightResponse.Marshal(b, m, deterministic)
}
func (m *DeductRightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductRightResponse.Merge(m, src)
}
func (m *DeductRightResponse) XXX_Size() int {
	return xxx_messageInfo_DeductRightResponse.Size(m)
}
func (m *DeductRightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductRightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeductRightResponse proto.InternalMessageInfo

func (m *DeductRightResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DeductRightResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DeductRightResponse) GetResult() *DeductRightResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type DeductRightResult struct {
	Result               bool        `protobuf:"varint,1,opt,name=result,proto3" json:"result"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	SystemError          string      `protobuf:"bytes,3,opt,name=systemError,proto3" json:"systemError"`
	BusinessError        string      `protobuf:"bytes,4,opt,name=businessError,proto3" json:"businessError"`
	StatusCode           int32       `protobuf:"varint,5,opt,name=statusCode,proto3" json:"statusCode"`
	Extensions           *Extensions `protobuf:"bytes,6,opt,name=extensions,proto3" json:"extensions"`
	Success              bool        `protobuf:"varint,7,opt,name=success,proto3" json:"success"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DeductRightResult) Reset()         { *m = DeductRightResult{} }
func (m *DeductRightResult) String() string { return proto.CompactTextString(m) }
func (*DeductRightResult) ProtoMessage()    {}
func (*DeductRightResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{35}
}

func (m *DeductRightResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductRightResult.Unmarshal(m, b)
}
func (m *DeductRightResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductRightResult.Marshal(b, m, deterministic)
}
func (m *DeductRightResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductRightResult.Merge(m, src)
}
func (m *DeductRightResult) XXX_Size() int {
	return xxx_messageInfo_DeductRightResult.Size(m)
}
func (m *DeductRightResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductRightResult.DiscardUnknown(m)
}

var xxx_messageInfo_DeductRightResult proto.InternalMessageInfo

func (m *DeductRightResult) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

func (m *DeductRightResult) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DeductRightResult) GetSystemError() string {
	if m != nil {
		return m.SystemError
	}
	return ""
}

func (m *DeductRightResult) GetBusinessError() string {
	if m != nil {
		return m.BusinessError
	}
	return ""
}

func (m *DeductRightResult) GetStatusCode() int32 {
	if m != nil {
		return m.StatusCode
	}
	return 0
}

func (m *DeductRightResult) GetExtensions() *Extensions {
	if m != nil {
		return m.Extensions
	}
	return nil
}

func (m *DeductRightResult) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type Extensions struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Extensions) Reset()         { *m = Extensions{} }
func (m *Extensions) String() string { return proto.CompactTextString(m) }
func (*Extensions) ProtoMessage()    {}
func (*Extensions) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{36}
}

func (m *Extensions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Extensions.Unmarshal(m, b)
}
func (m *Extensions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Extensions.Marshal(b, m, deterministic)
}
func (m *Extensions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Extensions.Merge(m, src)
}
func (m *Extensions) XXX_Size() int {
	return xxx_messageInfo_Extensions.Size(m)
}
func (m *Extensions) XXX_DiscardUnknown() {
	xxx_messageInfo_Extensions.DiscardUnknown(m)
}

var xxx_messageInfo_Extensions proto.InternalMessageInfo

func init() {
	proto.RegisterType((*UserInfoBj)(nil), "proto.UserInfoBj")
	proto.RegisterType((*UserInfoBjBody)(nil), "proto.UserInfoBjBody")
	proto.RegisterType((*UserInfoBjRequest)(nil), "proto.UserInfoBjRequest")
	proto.RegisterType((*PetInfo)(nil), "proto.PetInfo")
	proto.RegisterType((*UserInfo)(nil), "proto.UserInfo")
	proto.RegisterType((*NewUserResponse)(nil), "proto.NewUserResponse")
	proto.RegisterType((*NewPetResponse)(nil), "proto.NewPetResponse")
	proto.RegisterType((*MyVipResponse)(nil), "proto.MyVipResponse")
	proto.RegisterType((*MembershipCard)(nil), "proto.MembershipCard")
	proto.RegisterType((*NewCardRequest)(nil), "proto.NewCardRequest")
	proto.RegisterType((*RenewCardRequest)(nil), "proto.RenewCardRequest")
	proto.RegisterType((*RecordsPay)(nil), "proto.RecordsPay")
	proto.RegisterType((*MemberCardRequest)(nil), "proto.MemberCardRequest")
	proto.RegisterType((*MemberCardResponse)(nil), "proto.MemberCardResponse")
	proto.RegisterType((*MemberCard)(nil), "proto.MemberCard")
	proto.RegisterType((*DeductRequest)(nil), "proto.DeductRequest")
	proto.RegisterType((*Criteria)(nil), "proto.Criteria")
	proto.RegisterType((*DeductResponse)(nil), "proto.DeductResponse")
	proto.RegisterType((*DeductResult)(nil), "proto.DeductResult")
	proto.RegisterType((*DeductDetail)(nil), "proto.DeductDetail")
	proto.RegisterType((*RightsRequest)(nil), "proto.RightsRequest")
	proto.RegisterType((*RightsCriteria)(nil), "proto.RightsCriteria")
	proto.RegisterType((*RightsResponse)(nil), "proto.RightsResponse")
	proto.RegisterType((*RightsResult)(nil), "proto.RightsResult")
	proto.RegisterType((*RightsDetail)(nil), "proto.RightsDetail")
	proto.RegisterType((*BatchDetails)(nil), "proto.BatchDetails")
	proto.RegisterType((*CategoryDetail)(nil), "proto.CategoryDetail")
	proto.RegisterType((*CategoryDiscounts)(nil), "proto.CategoryDiscounts")
	proto.RegisterType((*DiscountLevelVoList)(nil), "proto.DiscountLevelVoList")
	proto.RegisterType((*NotifyCardRequest)(nil), "proto.NotifyCardRequest")
	proto.RegisterType((*NewCardPayRequest)(nil), "proto.NewCardPayRequest")
	proto.RegisterType((*EnsureCardsRecordsPays)(nil), "proto.EnsureCardsRecordsPays")
	proto.RegisterType((*DeductRightRequest)(nil), "proto.DeductRightRequest")
	proto.RegisterType((*DeductRightCriteria)(nil), "proto.DeductRightCriteria")
	proto.RegisterType((*DeductRightResponse)(nil), "proto.DeductRightResponse")
	proto.RegisterType((*DeductRightResult)(nil), "proto.DeductRightResult")
	proto.RegisterType((*Extensions)(nil), "proto.Extensions")
}

func init() { proto.RegisterFile("user.proto", fileDescriptor_116e343673f7ffaf) }

var fileDescriptor_116e343673f7ffaf = []byte{
	// 3779 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5b, 0xcd, 0x6f, 0x1c, 0x47,
	0x76, 0x07, 0x67, 0x38, 0x33, 0x9c, 0x37, 0x1f, 0xe4, 0x34, 0x3f, 0xd4, 0xa2, 0xbe, 0xe8, 0xf6,
	0x97, 0x20, 0x1b, 0x52, 0xec, 0x00, 0x09, 0x60, 0x24, 0x07, 0x91, 0x92, 0x23, 0x22, 0x12, 0x45,
	0x34, 0x25, 0x1b, 0x70, 0xe0, 0x08, 0xcd, 0xe9, 0xe2, 0xb0, 0xa5, 0x99, 0xee, 0x51, 0x77, 0x8f,
	0xa4, 0xf1, 0x31, 0x47, 0x9f, 0x02, 0xe4, 0x16, 0x1f, 0x73, 0xcb, 0x35, 0xff, 0x85, 0xaf, 0x01,
	0xf6, 0xb2, 0xd7, 0xc5, 0x5e, 0xf6, 0xbe, 0x87, 0x35, 0xb0, 0x58, 0xbc, 0xf7, 0xaa, 0xba, 0xab,
	0xba, 0x9b, 0x1c, 0x52, 0xeb, 0xd3, 0x9e, 0xc4, 0xf7, 0xab, 0xea, 0xfa, 0x78, 0xdf, 0xf5, 0xde,
	0x08, 0x60, 0x96, 0x88, 0xf8, 0xee, 0x34, 0x8e, 0xd2, 0xc8, 0x6a, 0xd0, 0x3f, 0xdb, 0xd7, 0x47,
	0x51, 0x34, 0x1a, 0x8b, 0x7b, 0xde, 0x34, 0xb8, 0xe7, 0x85, 0x61, 0x94, 0x7a, 0x69, 0x10, 0x85,
	0x09, 0x4f, 0xda, 0xee, 0x8e, 0xc6, 0xd1, 0xb1, 0x37, 0x66, 0xca, 0xf9, 0x7d, 0x0d, 0xe0, 0x79,
	0x22, 0xe2, 0xfd, 0xf0, 0x24, 0xda, 0x7d, 0x69, 0x6d, 0xc3, 0x0a, 0xae, 0x77, 0xe0, 0x4d, 0x84,
	0xbd, 0xb4, 0xb3, 0x74, 0xbb, 0xed, 0x66, 0xb4, 0x65, 0x43, 0x0b, 0xff, 0x3e, 0x12, 0xef, 0xec,
	0xda, 0xce, 0xd2, 0xed, 0x86, 0xab, 0x48, 0xeb, 0x26, 0x9f, 0xe2, 0x49, 0x74, 0x1c, 0x8c, 0x85,
	0x5d, 0xa7, 0xef, 0x34, 0x44, 0x8d, 0xdf, 0x7f, 0xe3, 0xa5, 0x5e, 0x6c, 0x2f, 0xe7, 0xe3, 0x8c,
	0x58, 0x0e, 0x74, 0x91, 0xda, 0x0d, 0xe2, 0xf4, 0xd4, 0xf7, 0xe6, 0x76, 0x83, 0x66, 0x18, 0x98,
	0xf5, 0x09, 0xf4, 0x4f, 0x82, 0x38, 0x49, 0x5d, 0x2f, 0x48, 0x44, 0x72, 0x28, 0x52, 0xbb, 0x49,
	0xb3, 0x0a, 0xa8, 0x65, 0xc1, 0xf2, 0x30, 0x48, 0xe7, 0x76, 0x8b, 0x46, 0xe9, 0x6f, 0x3c, 0xf9,
	0x30, 0x9a, 0x85, 0x69, 0x3c, 0xb7, 0x57, 0x08, 0x56, 0x24, 0xde, 0x77, 0x1a, 0x47, 0x6f, 0x82,
	0x70, 0x28, 0xec, 0x36, 0xdf, 0x57, 0xd1, 0xb8, 0x92, 0x17, 0x0b, 0xcf, 0x06, 0x5e, 0x09, 0xff,
	0xb6, 0x76, 0xa0, 0x93, 0x9e, 0x06, 0xb1, 0x4f, 0x2c, 0xf3, 0xed, 0x0e, 0x0d, 0xe9, 0x90, 0xb5,
	0x05, 0xcd, 0x24, 0x9a, 0xc5, 0x43, 0x61, 0x77, 0x89, 0x49, 0x92, 0x72, 0xbe, 0x87, 0x7e, 0xce,
	0xe7, 0xdd, 0xc8, 0x9f, 0x5b, 0x1f, 0xc3, 0xf2, 0x71, 0xe4, 0xcf, 0x89, 0xcf, 0x9d, 0x2f, 0x07,
	0x2c, 0x90, 0xbb, 0xf9, 0x24, 0x97, 0x86, 0xad, 0x3e, 0xd4, 0x92, 0x90, 0x38, 0xde, 0x76, 0x6b,
	0x49, 0x68, 0x6d, 0x40, 0x23, 0x8d, 0xa6, 0xc1, 0x50, 0xf2, 0x99, 0x09, 0xe7, 0x9f, 0x60, 0xa0,
	0x7d, 0x29, 0x5e, 0xcf, 0x44, 0x92, 0x5a, 0x9f, 0x42, 0x7d, 0x92, 0x8c, 0xe4, 0x06, 0x9b, 0xa5,
	0x0d, 0xf0, 0x14, 0x2e, 0xce, 0x70, 0xfe, 0xa7, 0x0e, 0xad, 0x43, 0x91, 0x22, 0x6c, 0x5d, 0x61,
	0x31, 0xbf, 0x08, 0x7c, 0xa9, 0x01, 0xcd, 0x19, 0xdf, 0xec, 0x1a, 0xb4, 0xd3, 0xf9, 0x54, 0xbc,
	0x08, 0x51, 0x39, 0xf8, 0x3c, 0x2b, 0x08, 0x90, 0x72, 0x58, 0xb0, 0x4c, 0x38, 0x1f, 0x8a, 0xfe,
	0xc6, 0x95, 0xe8, 0x83, 0xc0, 0x27, 0x99, 0x37, 0xdc, 0x26, 0x92, 0xcc, 0xa3, 0x58, 0x4c, 0xbc,
	0xf8, 0x95, 0x94, 0xb4, 0xa4, 0xac, 0x1b, 0x00, 0x1e, 0x69, 0xc4, 0x8b, 0x59, 0x3c, 0x96, 0xf2,
	0x6d, 0x33, 0xf2, 0x3c, 0x1e, 0x5b, 0x6b, 0x50, 0x4f, 0xc4, 0x3b, 0x92, 0x6c, 0xc3, 0xc5, 0x3f,
	0x51, 0x7c, 0xc7, 0x4a, 0x69, 0x58, 0xb2, 0x19, 0x8d, 0x9b, 0xbc, 0x15, 0xc1, 0xe8, 0x34, 0x25,
	0xc1, 0x36, 0x5c, 0x49, 0xa1, 0x08, 0x83, 0x30, 0x11, 0xc3, 0x34, 0x18, 0x06, 0xbe, 0x20, 0xe9,
	0x36, 0x5c, 0x1d, 0xc2, 0x2f, 0x83, 0xc9, 0x64, 0x16, 0x0a, 0x92, 0x6f, 0xc3, 0x95, 0x94, 0x75,
	0x1d, 0xda, 0x49, 0x2a, 0xe2, 0x60, 0x1c, 0xfc, 0xa0, 0xa4, 0x9b, 0x03, 0xb8, 0xae, 0x1f, 0x24,
	0xd3, 0x28, 0x09, 0xd0, 0xda, 0xec, 0x1e, 0xab, 0x86, 0x06, 0xe1, 0xf5, 0x86, 0xa7, 0x5e, 0x18,
	0x8a, 0x31, 0xb2, 0xa4, 0xcf, 0x0b, 0x48, 0x64, 0xdf, 0xb7, 0x6e, 0x41, 0x67, 0x3a, 0xf6, 0xd2,
	0x93, 0x28, 0x9e, 0xe0, 0xf8, 0x2a, 0x8d, 0x83, 0x82, 0xf6, 0x7d, 0xe7, 0x3f, 0x1b, 0xb0, 0xa2,
	0xa4, 0x87, 0x6a, 0x91, 0x49, 0xa8, 0x16, 0xf8, 0x99, 0x00, 0x6a, 0x9a, 0x00, 0xb6, 0xa0, 0x39,
	0xd1, 0x6d, 0x52, 0x52, 0x28, 0xc9, 0x30, 0x18, 0xbe, 0x62, 0x49, 0xb2, 0x39, 0xae, 0x20, 0x40,
	0x92, 0x34, 0x85, 0xd0, 0x38, 0x43, 0x08, 0xcd, 0x5c, 0x08, 0x78, 0x6e, 0x69, 0x33, 0x78, 0xee,
	0x96, 0x3c, 0xb7, 0x84, 0xf6, 0x7d, 0xd4, 0x03, 0x34, 0x43, 0x1c, 0x5c, 0x61, 0x86, 0x22, 0xc9,
	0x03, 0x68, 0x55, 0x38, 0x20, 0x65, 0x84, 0xe4, 0xbe, 0x8f, 0x9c, 0x1e, 0x47, 0xe1, 0x28, 0x48,
	0x67, 0x52, 0x42, 0x35, 0x37, 0x07, 0x50, 0xea, 0x63, 0x2f, 0xe5, 0xc1, 0x0e, 0x0d, 0x66, 0xb4,
	0xa1, 0x11, 0xdd, 0x82, 0x46, 0x38, 0xd0, 0x23, 0x4b, 0x7d, 0xa1, 0xf4, 0xbb, 0x57, 0x36, 0xdf,
	0xc5, 0x32, 0xa2, 0x8f, 0xa5, 0x89, 0x4b, 0x19, 0x91, 0x1f, 0x24, 0xa4, 0x28, 0xc4, 0xb5, 0xa2,
	0x10, 0x35, 0xdd, 0x1f, 0x18, 0xba, 0x7f, 0x05, 0x5a, 0x81, 0xff, 0x62, 0xe8, 0xc5, 0xbe, 0x6d,
	0xf1, 0x40, 0xe0, 0xef, 0x79, 0xb1, 0x8f, 0x82, 0x7e, 0xfd, 0xda, 0x5e, 0x67, 0x41, 0xbf, 0x7e,
	0x8d, 0x13, 0xdf, 0xbe, 0x7b, 0x31, 0x8c, 0x7c, 0x61, 0x6f, 0xf0, 0xc4, 0xb7, 0xef, 0xf6, 0x22,
	0x5f, 0xa0, 0x63, 0x10, 0x13, 0x2f, 0x18, 0xdb, 0x9b, 0xec, 0x18, 0x88, 0x40, 0xdf, 0xe7, 0xf9,
	0x7e, 0x2c, 0x92, 0xc4, 0xde, 0x62, 0xdf, 0x27, 0x49, 0x3c, 0xea, 0x69, 0x94, 0x4c, 0x83, 0xd4,
	0xa3, 0xbb, 0x5e, 0xe1, 0xa3, 0x2a, 0x68, 0xdf, 0xb7, 0x3e, 0x80, 0xee, 0x49, 0x10, 0x7a, 0x28,
	0x57, 0xda, 0xce, 0x66, 0x76, 0x49, 0x0c, 0xf7, 0x74, 0x42, 0x58, 0x3d, 0x10, 0x6f, 0x91, 0x77,
	0xae, 0x48, 0xa6, 0x51, 0x98, 0x90, 0x27, 0xa0, 0xd9, 0x4b, 0xb4, 0x1e, 0xfd, 0x8d, 0x87, 0x98,
	0x88, 0x24, 0xf1, 0x46, 0x4a, 0x3f, 0x15, 0x49, 0x87, 0x8e, 0xe3, 0x28, 0x56, 0xde, 0x8c, 0x08,
	0xdd, 0x07, 0x2d, 0xeb, 0x3e, 0xc8, 0x79, 0x05, 0xfd, 0x03, 0xf1, 0xf6, 0x50, 0xa4, 0xbf, 0xea,
	0x76, 0x9b, 0xd0, 0x9c, 0x8a, 0x34, 0xdf, 0xad, 0x31, 0x15, 0xe9, 0xbe, 0xef, 0xbc, 0x84, 0xde,
	0x93, 0xf9, 0x37, 0xc1, 0xf4, 0x3d, 0xf7, 0xfa, 0x0c, 0x1a, 0x28, 0xce, 0xc4, 0xae, 0xef, 0xd4,
	0x35, 0xff, 0xfb, 0x44, 0x4c, 0x8e, 0x45, 0x9c, 0x9c, 0x06, 0x53, 0x14, 0xaf, 0xcb, 0x73, 0x9c,
	0x3f, 0x2e, 0x41, 0xdf, 0x1c, 0xa9, 0xb2, 0x70, 0xf4, 0x9f, 0x32, 0xf8, 0xd2, 0xdf, 0xd9, 0x89,
	0xa4, 0xdb, 0xa5, 0x13, 0x29, 0x4f, 0xb0, 0xac, 0x79, 0x82, 0x8f, 0xa1, 0x2f, 0x4e, 0x4e, 0xd0,
	0xc3, 0xbd, 0x11, 0x2f, 0xd2, 0x60, 0x22, 0xc8, 0xb0, 0xeb, 0x6e, 0x2f, 0x43, 0x9f, 0x05, 0x13,
	0x41, 0x12, 0xf7, 0x82, 0xf1, 0x2c, 0x96, 0x93, 0x9a, 0x34, 0xa9, 0x23, 0x31, 0x9a, 0xb2, 0x01,
	0x8d, 0x24, 0xf5, 0x52, 0x21, 0xed, 0x9c, 0x09, 0x34, 0x3b, 0x3e, 0xfd, 0xbe, 0xaf, 0x1c, 0xb1,
	0xa2, 0xd1, 0x98, 0x77, 0xbd, 0x74, 0x78, 0x4a, 0x07, 0xe5, 0x20, 0x9b, 0x03, 0xce, 0xff, 0xd5,
	0x49, 0xa4, 0xc4, 0x0b, 0x19, 0xb6, 0xb6, 0x80, 0xc4, 0x6d, 0x06, 0xa0, 0xc0, 0xc7, 0x4d, 0x90,
	0x59, 0x19, 0x13, 0xda, 0x6e, 0x46, 0x63, 0x0a, 0xa1, 0x34, 0x77, 0x2f, 0x67, 0x88, 0x81, 0xe9,
	0x73, 0x0e, 0x72, 0x06, 0x19, 0x18, 0xce, 0x19, 0xc6, 0xc2, 0x4b, 0x05, 0xdb, 0x33, 0xb1, 0xa9,
	0xe1, 0x1a, 0x18, 0x9d, 0x83, 0xe8, 0x7d, 0x5f, 0xfa, 0xc1, 0x8c, 0xc6, 0x54, 0x87, 0xff, 0xa6,
	0x1d, 0x38, 0x09, 0xd1, 0x10, 0x54, 0x97, 0x28, 0xf6, 0xe9, 0x72, 0x32, 0x15, 0x91, 0x24, 0xed,
	0xec, 0xa5, 0x62, 0x14, 0xc5, 0xf3, 0xbd, 0x9c, 0x53, 0x06, 0x66, 0x7d, 0x01, 0x10, 0x8b, 0x61,
	0x14, 0xfb, 0xc9, 0xa1, 0x37, 0xb7, 0x81, 0xf4, 0x4a, 0x25, 0x0e, 0x6e, 0x36, 0xe0, 0x6a, 0x93,
	0x90, 0x99, 0xc8, 0xa4, 0x30, 0x92, 0xc9, 0x8a, 0xa4, 0x50, 0x2a, 0xc7, 0x99, 0x54, 0xd8, 0x53,
	0xe6, 0x00, 0x5e, 0xe3, 0x24, 0x16, 0xe2, 0x07, 0x92, 0xb9, 0xf4, 0x93, 0x1a, 0xe2, 0xfc, 0xa1,
	0x06, 0x6b, 0xae, 0x08, 0x4d, 0xb9, 0x15, 0x65, 0xb0, 0x74, 0x01, 0x19, 0xd4, 0x2e, 0x20, 0x83,
	0x7a, 0x85, 0x0c, 0x6e, 0x02, 0x88, 0x30, 0x99, 0xc5, 0xe4, 0x86, 0x54, 0x4a, 0x99, 0x23, 0x25,
	0x6e, 0x36, 0x2a, 0xb8, 0x79, 0x13, 0x20, 0x9a, 0x8a, 0xd8, 0x4b, 0xa3, 0x38, 0x93, 0xa4, 0x86,
	0xe0, 0x1a, 0x8a, 0xd2, 0xa4, 0x69, 0x60, 0x9a, 0xae, 0xae, 0x18, 0xba, 0xaa, 0xc9, 0xb9, 0x6d,
	0xca, 0xf9, 0xf2, 0x32, 0x74, 0x76, 0x01, 0xf2, 0x11, 0x5c, 0x7a, 0xea, 0xcd, 0x9f, 0xa1, 0x15,
	0xb0, 0x23, 0x52, 0x24, 0x65, 0xb3, 0xde, 0xfc, 0x49, 0x14, 0x8a, 0xb9, 0x32, 0x10, 0x45, 0x3b,
	0xbf, 0x2c, 0xc1, 0x80, 0x4d, 0xf2, 0x22, 0xa6, 0xb6, 0x03, 0xca, 0xcd, 0x93, 0x7e, 0xd4, 0x0c,
	0xcf, 0x4f, 0x1a, 0xb2, 0x83, 0x51, 0x5f, 0x24, 0x62, 0x3c, 0xd6, 0xf2, 0x3e, 0x1d, 0xe2, 0x74,
	0xc9, 0x8b, 0x53, 0x1f, 0xbd, 0x05, 0x4b, 0x28, 0x07, 0xf0, 0x16, 0x22, 0xf4, 0x69, 0x8c, 0x65,
	0xa3, 0x48, 0x1c, 0x79, 0x25, 0xe6, 0x6f, 0xa3, 0xd8, 0x97, 0x29, 0xa0, 0x22, 0x71, 0xc5, 0xa9,
	0x37, 0x12, 0x41, 0xe8, 0x67, 0x69, 0x60, 0x0e, 0xf0, 0xed, 0x47, 0x22, 0xc1, 0xec, 0x8c, 0xf3,
	0x8c, 0x8c, 0x76, 0x5e, 0x81, 0xa5, 0x5f, 0xfe, 0xbd, 0xfc, 0xf9, 0xa7, 0xa6, 0x3f, 0x1f, 0x18,
	0xfe, 0x5c, 0xf7, 0xe5, 0x3f, 0xd7, 0x00, 0x72, 0xb4, 0xc8, 0xcb, 0xa5, 0x32, 0x2f, 0x35, 0xa3,
	0x08, 0x2b, 0x8c, 0x82, 0xb8, 0x99, 0x39, 0x16, 0x62, 0x59, 0x5d, 0x77, 0x2c, 0xbe, 0xf4, 0xc0,
	0xc3, 0x59, 0x92, 0x46, 0x13, 0xa1, 0x5e, 0x58, 0x19, 0x4d, 0x6f, 0x92, 0xd4, 0x4b, 0x67, 0x89,
	0x74, 0x67, 0x92, 0xd2, 0x64, 0x48, 0x27, 0x6b, 0x1a, 0x32, 0xac, 0x92, 0x72, 0xab, 0x2c, 0x65,
	0x07, 0xba, 0xd9, 0x07, 0xb3, 0x30, 0x95, 0x9c, 0x37, 0x30, 0x6d, 0x95, 0x59, 0x18, 0xa4, 0xd2,
	0x20, 0x74, 0x48, 0x37, 0x17, 0x30, 0xcc, 0xc5, 0x79, 0x06, 0xbd, 0x07, 0xc2, 0x9f, 0x0d, 0x53,
	0xa5, 0xb2, 0x9f, 0xc1, 0xca, 0x5e, 0x1c, 0x60, 0xda, 0xed, 0xd9, 0x4b, 0x24, 0x89, 0x55, 0x29,
	0x09, 0x05, 0xbb, 0xd9, 0x04, 0xbc, 0xb9, 0x74, 0x22, 0x1c, 0x35, 0x25, 0xe5, 0xfc, 0x5c, 0xcf,
	0x57, 0xc1, 0xe3, 0x49, 0xf3, 0xd2, 0xdc, 0x96, 0x0e, 0x21, 0xf3, 0x1f, 0xe6, 0xde, 0x86, 0xc5,
	0xa3, 0x21, 0xb8, 0x8d, 0x7c, 0x11, 0xca, 0x44, 0x5b, 0x66, 0x93, 0x1b, 0xd0, 0xc0, 0x67, 0x55,
	0x96, 0x57, 0x10, 0x91, 0x05, 0x44, 0xcd, 0x31, 0xe5, 0x80, 0x75, 0x1b, 0x56, 0x89, 0x78, 0x20,
	0x52, 0x2f, 0x60, 0x47, 0xca, 0x82, 0x29, 0xc2, 0x78, 0xee, 0xa7, 0xe4, 0x8b, 0x04, 0x39, 0x83,
	0xd6, 0x4e, 0x1d, 0x5f, 0x32, 0x1a, 0x84, 0xe7, 0x7e, 0x9a, 0x7b, 0x38, 0x16, 0x8d, 0x86, 0xe0,
	0x0a, 0xb4, 0xe8, 0x11, 0x6b, 0x87, 0x14, 0x8c, 0x06, 0xe1, 0x59, 0x8f, 0xd0, 0x66, 0x29, 0x0e,
	0xb0, 0x68, 0x72, 0x00, 0xc5, 0xf6, 0x30, 0xf4, 0x69, 0x8c, 0xa3, 0x8b, 0x22, 0xad, 0xcf, 0x61,
	0xf0, 0x74, 0x38, 0x9c, 0xc5, 0x8f, 0xf4, 0x80, 0xc0, 0x61, 0xa6, 0x3c, 0x80, 0xe7, 0xd8, 0x8b,
	0x26, 0x53, 0x2f, 0x64, 0x67, 0x2d, 0xf3, 0x72, 0x0d, 0x42, 0xf5, 0x46, 0x63, 0xa2, 0x8b, 0x72,
	0x56, 0x9e, 0xd1, 0x98, 0x14, 0x2a, 0x15, 0x79, 0xcf, 0x44, 0xad, 0x19, 0x8b, 0x64, 0x36, 0x4e,
	0x49, 0x7a, 0x9d, 0x2f, 0xd7, 0xa5, 0x3e, 0x65, 0x8b, 0xce, 0xc6, 0xa9, 0x2b, 0xa7, 0x38, 0x3f,
	0xd6, 0xa0, 0xab, 0x0f, 0x68, 0x5f, 0xb3, 0x36, 0x9a, 0x5f, 0xb3, 0xb8, 0xd4, 0xd7, 0xe7, 0x1c,
	0x62, 0x07, 0x3a, 0xc9, 0x3c, 0x49, 0xc5, 0xe4, 0xa1, 0x96, 0x9f, 0xea, 0x90, 0xf5, 0x11, 0xf4,
	0x8e, 0x67, 0x49, 0x10, 0x8a, 0x24, 0xe1, 0x39, 0xac, 0x54, 0x26, 0x88, 0x22, 0x67, 0xeb, 0xce,
	0xb4, 0xab, 0xe1, 0x6a, 0x08, 0x86, 0x1f, 0xf1, 0x2e, 0x15, 0x61, 0x12, 0x44, 0x61, 0x42, 0x9a,
	0x95, 0xbb, 0xb2, 0x87, 0xd9, 0x80, 0xab, 0x4d, 0xc2, 0x43, 0x27, 0xb3, 0xe1, 0x10, 0x9f, 0x10,
	0xe8, 0x00, 0x56, 0x5c, 0x45, 0x3a, 0xbf, 0x69, 0x2b, 0x66, 0xf0, 0x3d, 0x31, 0x67, 0xdd, 0xf7,
	0x25, 0xdb, 0x6b, 0x1c, 0x42, 0x0d, 0x0d, 0x90, 0x9e, 0xad, 0x28, 0x7c, 0xdd, 0xfc, 0xea, 0x8b,
	0xcc, 0x6f, 0xb9, 0x64, 0x7e, 0xbf, 0x96, 0x41, 0xe5, 0x66, 0xdc, 0xaa, 0x36, 0xe3, 0x15, 0xdd,
	0x8c, 0x77, 0xa0, 0xc3, 0xe3, 0x8f, 0x83, 0x24, 0x45, 0xe3, 0xa9, 0xe3, 0xb9, 0x35, 0x08, 0xcf,
	0x4d, 0x53, 0x79, 0x02, 0xd0, 0x04, 0x0d, 0xc1, 0x15, 0x9e, 0x95, 0xab, 0x49, 0x1a, 0x84, 0x2b,
	0x10, 0xc9, 0xdb, 0xb3, 0xfd, 0x68, 0x48, 0x36, 0xfe, 0x34, 0x1e, 0xed, 0xab, 0xf7, 0xac, 0x86,
	0x90, 0x8b, 0x40, 0x47, 0x7a, 0x30, 0xc3, 0x70, 0x44, 0x96, 0xd3, 0x76, 0x75, 0x08, 0x79, 0xe7,
	0x4e, 0x3c, 0x39, 0xbe, 0xca, 0xbc, 0xcb, 0x00, 0x1c, 0x25, 0x6b, 0x25, 0x13, 0x5f, 0xe3, 0xd1,
	0x0c, 0xa8, 0x36, 0xf2, 0xc1, 0x59, 0x46, 0x5e, 0x9c, 0x4d, 0x39, 0x95, 0x55, 0x31, 0x9b, 0x12,
	0xab, 0xbb, 0x60, 0xed, 0x45, 0xf1, 0x74, 0x96, 0xec, 0x8a, 0x93, 0x28, 0x16, 0x7b, 0xa7, 0x5e,
	0x38, 0x12, 0xf4, 0x0c, 0xae, 0xb9, 0x15, 0x23, 0xa8, 0x69, 0x8c, 0xca, 0x99, 0x1b, 0x34, 0xd3,
	0xc0, 0xf0, 0x04, 0x4c, 0xdf, 0x3f, 0x49, 0x45, 0x2c, 0x27, 0x6e, 0xd2, 0xc4, 0xf2, 0x00, 0xce,
	0xc6, 0x5b, 0x9a, 0x07, 0xd8, 0x22, 0xd5, 0x2e, 0x0f, 0x90, 0x2c, 0x11, 0x94, 0xf3, 0xf8, 0x35,
	0xad, 0x43, 0xd6, 0x1d, 0x58, 0x23, 0x52, 0xdf, 0xdc, 0xa6, 0x69, 0x25, 0xbc, 0xe8, 0xda, 0xaf,
	0xf2, 0x6a, 0x67, 0xbb, 0xf6, 0xed, 0x92, 0x6b, 0x77, 0xa0, 0xfb, 0x54, 0x4f, 0x5e, 0xaf, 0xb1,
	0xe5, 0xe9, 0x98, 0xf5, 0x09, 0xf4, 0x15, 0x2d, 0xa3, 0xe4, 0x75, 0x5a, 0xa7, 0x80, 0xa2, 0x5d,
	0xb8, 0x5c, 0xb3, 0xb8, 0xc1, 0x76, 0xc1, 0x14, 0x9e, 0xf2, 0x30, 0x8e, 0xd0, 0xfc, 0x69, 0x8b,
	0x9b, 0xac, 0x5d, 0x1a, 0x84, 0xfa, 0xf3, 0x3c, 0x0c, 0xd2, 0xc3, 0x38, 0x18, 0x0a, 0xfb, 0x16,
	0x17, 0x72, 0x32, 0x00, 0x39, 0x22, 0xad, 0xd8, 0x8b, 0x7d, 0x19, 0x83, 0x76, 0x98, 0x23, 0x45,
	0x5c, 0xf7, 0x24, 0xb4, 0xd9, 0x07, 0xa6, 0x27, 0xa1, 0xdd, 0xf0, 0x3c, 0x9c, 0x0a, 0xd3, 0x14,
	0x47, 0x9e, 0x27, 0x87, 0xf0, 0xc6, 0xdf, 0xc6, 0x41, 0x9a, 0x8a, 0xf0, 0xe9, 0xc9, 0x09, 0xa9,
	0xeb, 0x87, 0x5c, 0x45, 0x36, 0x51, 0xe7, 0x3b, 0xe8, 0xb9, 0xc1, 0xe8, 0x34, 0x4d, 0x54, 0xd6,
	0xf1, 0x45, 0x29, 0xeb, 0x50, 0xef, 0x79, 0x9e, 0x77, 0x89, 0xdc, 0xe3, 0xbf, 0x6b, 0xd0, 0x37,
	0x3f, 0xba, 0xd0, 0xcb, 0x49, 0xa6, 0xea, 0xfb, 0xbe, 0x74, 0xa2, 0xaa, 0x2c, 0xbb, 0x01, 0x5c,
	0xae, 0x50, 0x25, 0x8d, 0xa9, 0x72, 0x0c, 0xe7, 0xbe, 0x8f, 0xd4, 0xf3, 0x4f, 0x77, 0x99, 0x19,
	0x50, 0x7a, 0x3d, 0x35, 0x2b, 0x5e, 0x4f, 0x79, 0x52, 0xd9, 0x2a, 0x26, 0x95, 0x43, 0x2d, 0x96,
	0xb3, 0xcb, 0xd4, 0x21, 0xf5, 0x8e, 0x27, 0xcd, 0x6e, 0xcb, 0xf7, 0xb3, 0x16, 0xcb, 0x15, 0xe3,
	0x7f, 0xd5, 0x58, 0x9e, 0x2d, 0x5a, 0x8c, 0xe5, 0xfa, 0x00, 0x7e, 0xed, 0x56, 0xc5, 0x72, 0x9e,
	0xa4, 0x62, 0xb9, 0x9b, 0xc5, 0xf2, 0x27, 0xe6, 0x21, 0x9e, 0xe4, 0xb1, 0xfc, 0xa8, 0x1c, 0xcb,
	0x8f, 0xcc, 0x58, 0xbe, 0x5b, 0x15, 0xcb, 0x77, 0x8b, 0xb1, 0xfc, 0xa8, 0x14, 0xcb, 0x8f, 0xfe,
	0xda, 0x58, 0x7e, 0x64, 0xc6, 0x72, 0x49, 0x3a, 0xbf, 0xb4, 0x14, 0x33, 0xf2, 0x58, 0x1e, 0x64,
	0xb1, 0x9c, 0x0b, 0x14, 0xa7, 0x15, 0xb1, 0xfc, 0x0c, 0x25, 0xad, 0x57, 0x2b, 0xa9, 0x5e, 0x60,
	0x43, 0x3e, 0xcd, 0xb4, 0x08, 0xda, 0xe0, 0x08, 0x3a, 0x33, 0x23, 0xe8, 0x34, 0x8f, 0xa0, 0x4d,
	0x8e, 0xa0, 0x53, 0x23, 0x82, 0xea, 0xfd, 0x98, 0x56, 0xb9, 0xa0, 0x7b, 0x13, 0x20, 0xcd, 0x23,
	0x28, 0x6b, 0xa3, 0x86, 0x64, 0xe3, 0x1c, 0x41, 0xdb, 0xda, 0x38, 0x47, 0x50, 0xd3, 0x90, 0xe0,
	0x7c, 0x43, 0xea, 0x2c, 0x32, 0xa4, 0x6e, 0x85, 0x21, 0x5d, 0x87, 0xf6, 0x30, 0x4a, 0x52, 0x7e,
	0xb6, 0xf7, 0xd8, 0x47, 0x66, 0x00, 0x6a, 0xca, 0x90, 0xa3, 0x9d, 0x37, 0xc6, 0x07, 0x23, 0xc5,
	0xf0, 0x9a, 0x6b, 0x82, 0xd9, 0x5b, 0xfb, 0x01, 0x3e, 0x0e, 0x57, 0xb5, 0xb7, 0x36, 0x02, 0x74,
	0x87, 0x77, 0xd3, 0x20, 0x9e, 0xd3, 0xf0, 0x9a, 0xbc, 0x43, 0x86, 0x20, 0x17, 0x8f, 0xb5, 0x67,
	0xc0, 0x80, 0xa3, 0x8d, 0x06, 0x51, 0xd5, 0x7a, 0x16, 0x3e, 0x9d, 0xa5, 0x14, 0xb0, 0x1b, 0xae,
	0xa4, 0x4a, 0xa5, 0x9a, 0xf5, 0x05, 0xe5, 0xb2, 0x8d, 0x73, 0xcb, 0x65, 0x9b, 0xa5, 0x72, 0x59,
	0x36, 0x4e, 0x09, 0xc8, 0x96, 0x3e, 0x4e, 0x19, 0xc8, 0x1d, 0x58, 0x1b, 0x7b, 0x49, 0xfa, 0x7c,
	0xea, 0xe7, 0x67, 0xe0, 0xd0, 0x5b, 0xc2, 0xcd, 0xb9, 0x52, 0x61, 0xec, 0xe2, 0x5c, 0xa9, 0x35,
	0x77, 0xc1, 0x32, 0x31, 0x3a, 0xdf, 0x55, 0xda, 0xbf, 0x62, 0x04, 0xe3, 0x4a, 0x8e, 0xd2, 0x59,
	0xb7, 0x39, 0xae, 0x98, 0xa8, 0x56, 0xfd, 0xbf, 0x66, 0x54, 0xff, 0xff, 0x11, 0xba, 0xc7, 0x79,
	0x32, 0x9a, 0xd8, 0xd7, 0x0d, 0xf7, 0xa3, 0xe5, 0xa9, 0x89, 0x6b, 0x4c, 0xb4, 0xbe, 0x86, 0x81,
	0x52, 0xa6, 0x07, 0x41, 0x42, 0xcf, 0xed, 0xc4, 0xbe, 0x41, 0x5f, 0xdb, 0xea, 0x59, 0x5c, 0x1c,
	0x77, 0xcb, 0x9f, 0x38, 0x3f, 0x35, 0xa1, 0xab, 0x6f, 0x53, 0xb2, 0x7e, 0xd3, 0x0e, 0x6a, 0xe7,
	0xdb, 0x41, 0xbd, 0x68, 0x07, 0xb7, 0x61, 0xf5, 0xb8, 0x90, 0x83, 0xb3, 0x27, 0x28, 0xc2, 0x17,
	0x2a, 0xdc, 0xdd, 0x05, 0x2b, 0xbb, 0x41, 0x31, 0xa9, 0xaf, 0x18, 0x31, 0x2d, 0xac, 0x55, 0xb4,
	0x30, 0x0c, 0x58, 0xa7, 0x5e, 0x3c, 0x22, 0x09, 0x25, 0xf2, 0x95, 0xac, 0x43, 0x65, 0x1b, 0x6c,
	0x9f, 0x61, 0x83, 0x63, 0x71, 0x92, 0xf2, 0x2a, 0xdc, 0x56, 0xcc, 0x81, 0x92, 0xa5, 0x74, 0x16,
	0x58, 0x4a, 0xf7, 0x5c, 0x4b, 0xe9, 0x2d, 0xb0, 0x94, 0xfe, 0x85, 0x2c, 0x65, 0xf5, 0x12, 0x96,
	0xb2, 0x76, 0x29, 0x4b, 0x19, 0x5c, 0xc2, 0x52, 0xac, 0x4a, 0x4b, 0xf9, 0x67, 0xe8, 0x9b, 0x92,
	0x24, 0xdf, 0x92, 0xa7, 0x5d, 0x7b, 0xc6, 0xa0, 0x5b, 0x98, 0x6c, 0x3d, 0x86, 0x75, 0x5f, 0x2a,
	0xf7, 0x63, 0xf1, 0x46, 0x8c, 0xbf, 0x89, 0x30, 0x60, 0xd8, 0x1b, 0x64, 0x19, 0xdb, 0xea, 0x89,
	0x5e, 0x9e, 0xe1, 0x56, 0x7d, 0xe6, 0xfc, 0xb9, 0x0e, 0x7d, 0x73, 0xc3, 0x92, 0x7d, 0x54, 0xeb,
	0x64, 0xed, 0x4c, 0x9d, 0x2c, 0xea, 0x79, 0xbd, 0x42, 0xcf, 0xa9, 0x6e, 0x96, 0xe7, 0xd7, 0xcb,
	0xaa, 0x6e, 0x96, 0xe7, 0xd7, 0x1f, 0x41, 0x2f, 0x8a, 0x83, 0x51, 0x10, 0x7a, 0x63, 0xce, 0xb1,
	0x1b, 0xac, 0x99, 0x06, 0x88, 0x9a, 0x39, 0xcb, 0xb2, 0xf0, 0x26, 0xeb, 0x7f, 0x06, 0x70, 0xc2,
	0x86, 0x8a, 0xac, 0xdb, 0x87, 0x0e, 0xe5, 0x33, 0x4c, 0x0b, 0xc9, 0x21, 0x43, 0x73, 0xdb, 0xe7,
	0x6a, 0x2e, 0x2c, 0xd0, 0xdc, 0xce, 0xf9, 0x9a, 0x2b, 0xb5, 0xb1, 0x7b, 0x29, 0x6d, 0xec, 0x5d,
	0x42, 0x1b, 0xfb, 0x55, 0xda, 0xe8, 0xfc, 0xb6, 0x06, 0x83, 0x92, 0x1f, 0xad, 0xca, 0x90, 0x0c,
	0x99, 0xd6, 0xaa, 0xd3, 0x66, 0x3f, 0x18, 0xe6, 0xf9, 0xba, 0xa4, 0xd0, 0x43, 0x4a, 0xc1, 0x66,
	0xef, 0x17, 0xe9, 0x21, 0x0b, 0x30, 0xf2, 0x5a, 0xe9, 0xa8, 0xf4, 0x8e, 0x19, 0xad, 0xb7, 0x2c,
	0x9e, 0xa9, 0x06, 0x9e, 0xd6, 0xb2, 0x20, 0x7e, 0x9a, 0x6d, 0x8f, 0xd6, 0xc2, 0xb6, 0xc7, 0x4a,
	0x45, 0xdb, 0xe3, 0x0c, 0xf3, 0x6a, 0xbf, 0x9f, 0x79, 0xfd, 0x2b, 0xac, 0x57, 0xcc, 0xd5, 0xd4,
	0x5f, 0xaf, 0xcb, 0x6a, 0x50, 0x55, 0x4b, 0xd4, 0xf9, 0x69, 0x19, 0x06, 0x07, 0x51, 0x1a, 0x9c,
	0xcc, 0x0b, 0x8d, 0x0e, 0xa9, 0x3a, 0x4b, 0x46, 0x69, 0xe7, 0x26, 0xc0, 0xa3, 0xac, 0xe3, 0xad,
	0xc2, 0x5a, 0x8e, 0x94, 0x9e, 0x9d, 0xf5, 0x8a, 0x67, 0xe7, 0x16, 0x34, 0x71, 0xab, 0x83, 0x48,
	0x35, 0xab, 0x99, 0x32, 0x6a, 0x96, 0x52, 0x50, 0x8a, 0xb6, 0x3e, 0x87, 0xc1, 0x43, 0x6a, 0xbd,
	0x0a, 0xff, 0x28, 0x4b, 0xdc, 0x58, 0x5a, 0xe5, 0x01, 0x54, 0x0e, 0x05, 0x3e, 0x0c, 0xfd, 0x07,
	0xaa, 0xfd, 0xda, 0x76, 0x8b, 0x30, 0xde, 0x47, 0x7b, 0x4c, 0xcb, 0x74, 0x57, 0x7b, 0x46, 0xef,
	0x40, 0xe7, 0xeb, 0xbc, 0x7f, 0xaf, 0x2a, 0xbe, 0x1a, 0x64, 0xb6, 0x6b, 0xa1, 0xd0, 0xae, 0xe5,
	0x27, 0x7f, 0xde, 0x32, 0xe8, 0xa8, 0x27, 0xbf, 0xd1, 0x32, 0x38, 0xd4, 0x5a, 0x06, 0x5d, 0x63,
	0x86, 0x6a, 0x19, 0x1c, 0xea, 0x2d, 0x83, 0x1e, 0x87, 0xc2, 0xc3, 0x42, 0xcb, 0xe0, 0x50, 0x6b,
	0x19, 0xf4, 0x8d, 0x55, 0xa8, 0x65, 0xb0, 0x05, 0xcd, 0x43, 0x6f, 0xfe, 0xad, 0x37, 0x97, 0x61,
	0x4c, 0x52, 0x78, 0xfe, 0xfb, 0xe3, 0xf1, 0xfd, 0x09, 0x2d, 0x2d, 0x4b, 0x56, 0x19, 0xe0, 0xfc,
	0x6f, 0x03, 0x06, 0xb2, 0xdd, 0x7c, 0xe8, 0xcd, 0xb5, 0xce, 0xe5, 0xa3, 0x8a, 0xf7, 0xb7, 0x51,
	0xbe, 0x5a, 0xd4, 0x03, 0x30, 0x8a, 0x90, 0xf5, 0x62, 0x11, 0x92, 0x4b, 0x13, 0x13, 0x11, 0x92,
	0x1d, 0xcb, 0xdf, 0x43, 0xe9, 0x50, 0x45, 0x69, 0xa2, 0x51, 0x55, 0x9a, 0xa8, 0x28, 0xda, 0x34,
	0x2b, 0x8b, 0x36, 0x66, 0x81, 0xa8, 0xb5, 0xb0, 0x40, 0xb4, 0x52, 0x51, 0x20, 0x7a, 0x0e, 0x5b,
	0x79, 0x21, 0x26, 0xc9, 0x7b, 0x90, 0x89, 0xb4, 0xf4, 0x1b, 0xea, 0xb1, 0x59, 0x39, 0xc9, 0x3d,
	0xe3, 0xe3, 0xea, 0x4a, 0x20, 0x9c, 0x55, 0x09, 0x2c, 0x34, 0x07, 0x3a, 0xe7, 0x37, 0x07, 0xba,
	0x66, 0x73, 0x40, 0x33, 0xfc, 0x9e, 0x61, 0xf8, 0x36, 0xb4, 0xa8, 0x0c, 0xba, 0xef, 0x4b, 0xe5,
	0x52, 0x64, 0x51, 0xc5, 0x57, 0x17, 0xaa, 0xf8, 0xda, 0x62, 0x15, 0x1f, 0x2c, 0x56, 0x71, 0xab,
	0xa4, 0xe2, 0xce, 0xc1, 0x59, 0xec, 0xc7, 0xd3, 0x1f, 0x9a, 0x3d, 0xe0, 0xc3, 0xbc, 0x07, 0x7c,
	0x58, 0xe8, 0x01, 0x2b, 0xda, 0x79, 0x0c, 0x96, 0x6c, 0x5d, 0xe0, 0x3b, 0x5f, 0x29, 0xff, 0x3f,
	0x94, 0x4a, 0x5b, 0xdb, 0x66, 0x03, 0x04, 0x27, 0x97, 0xeb, 0x5b, 0xce, 0x4f, 0x0d, 0x58, 0xaf,
	0x98, 0x51, 0x30, 0x94, 0xa5, 0xf3, 0x0d, 0xa5, 0x76, 0x81, 0x6a, 0x7d, 0xbd, 0xba, 0x5a, 0x8f,
	0xd1, 0x56, 0xaf, 0xf8, 0x2e, 0x73, 0xc5, 0x57, 0xc7, 0x8a, 0x55, 0xd9, 0x46, 0xb9, 0x2a, 0xeb,
	0x40, 0x77, 0xaf, 0xa2, 0xd4, 0xa5, 0x63, 0xd5, 0x75, 0xee, 0xd6, 0xa5, 0xea, 0xdc, 0x2b, 0xe7,
	0x68, 0xb7, 0x5e, 0xa1, 0x6f, 0x97, 0x2b, 0xf4, 0x85, 0x5a, 0x30, 0x2c, 0xaa, 0x05, 0x77, 0x16,
	0x9a, 0x7a, 0xb7, 0xc2, 0xd4, 0xf3, 0x6a, 0x65, 0x4f, 0xaf, 0x56, 0x6a, 0xb5, 0xdf, 0x7e, 0xb1,
	0xf6, 0xab, 0xf7, 0x2e, 0x56, 0x17, 0xf5, 0x2e, 0xd6, 0x16, 0xf4, 0x2e, 0x06, 0x55, 0xbd, 0x0b,
	0xdd, 0xee, 0xad, 0xf3, 0xed, 0x7e, 0xbd, 0xd0, 0x14, 0x9c, 0x19, 0xca, 0xf9, 0x9e, 0xd5, 0xc4,
	0xbf, 0x2b, 0x54, 0x13, 0xed, 0xb2, 0x61, 0x14, 0x4a, 0x8a, 0x7f, 0x5a, 0x82, 0x41, 0x69, 0x94,
	0x9f, 0xfd, 0xb2, 0xae, 0xb8, 0x74, 0x7b, 0xe5, 0x6f, 0xb5, 0x1d, 0xd8, 0x05, 0xc8, 0xbf, 0xf9,
	0xf2, 0xc7, 0x2e, 0x37, 0xc8, 0x8e, 0x44, 0xfc, 0x06, 0xdf, 0x19, 0x4f, 0xa0, 0x25, 0x7f, 0x2b,
	0x68, 0xad, 0x16, 0x7e, 0x8b, 0xbc, 0xbd, 0x25, 0x81, 0xc2, 0x8f, 0x09, 0x9d, 0xab, 0xff, 0xf1,
	0xff, 0xbf, 0xfb, 0xaf, 0xda, 0xba, 0xd3, 0xbf, 0x77, 0xec, 0x25, 0xe2, 0xde, 0x2c, 0x11, 0xf1,
	0xbd, 0x50, 0xbc, 0xfd, 0x6a, 0xe9, 0x8e, 0xb5, 0x0f, 0x4d, 0xfe, 0x29, 0xa0, 0xd5, 0x97, 0x1f,
	0xcb, 0x5f, 0x30, 0x6f, 0x6f, 0xe6, 0x8b, 0x69, 0xbf, 0x14, 0x74, 0x6c, 0x5a, 0xcb, 0x72, 0x7a,
	0xbc, 0xd6, 0x54, 0xa4, 0x6a, 0xa9, 0x47, 0xd0, 0xa0, 0x1f, 0xfa, 0x59, 0xea, 0xe6, 0x07, 0xc1,
	0x58, 0x7a, 0xc7, 0xed, 0x0d, 0xf5, 0x33, 0x0f, 0xfd, 0x97, 0x80, 0xce, 0x16, 0xad, 0xb5, 0x66,
	0xe9, 0xe7, 0x7a, 0x13, 0x4c, 0xad, 0xe7, 0x74, 0x47, 0xfa, 0xd9, 0x87, 0x76, 0x0a, 0x2d, 0x11,
	0x3d, 0x63, 0xbd, 0x1b, 0xb4, 0xde, 0x15, 0xc7, 0x32, 0xef, 0x39, 0xf4, 0x62, 0x1f, 0x0f, 0xf8,
	0xef, 0x00, 0x79, 0xd2, 0x62, 0xd9, 0xe6, 0xca, 0x79, 0x1e, 0x73, 0xc6, 0xe2, 0x3b, 0xb4, 0xf8,
	0xb6, 0xb3, 0x59, 0x5e, 0x7c, 0xea, 0xcd, 0x71, 0x7d, 0x1f, 0x7a, 0x9c, 0x32, 0xab, 0xc3, 0x67,
	0x5b, 0x14, 0x13, 0xe9, 0x6c, 0x8b, 0x5d, 0x2f, 0x09, 0x86, 0xd9, 0x16, 0x1f, 0xd2, 0x16, 0x37,
	0x1c, 0x5b, 0xdf, 0x82, 0xbe, 0xd5, 0x6e, 0xf1, 0x6f, 0xd0, 0xce, 0x7e, 0x33, 0x66, 0x5d, 0xc9,
	0x7e, 0xf2, 0x14, 0x5e, 0x84, 0x41, 0xb7, 0x68, 0x83, 0xab, 0xce, 0x86, 0xb6, 0x41, 0x2c, 0xb4,
	0xc5, 0x4f, 0x61, 0x95, 0x0f, 0x9b, 0x6f, 0x71, 0xd9, 0x4b, 0x7c, 0x4c, 0x7b, 0xdc, 0x72, 0xb6,
	0x4b, 0x97, 0x30, 0x76, 0x1a, 0xc2, 0xea, 0xfd, 0xe9, 0x74, 0x3c, 0x77, 0x45, 0x3a, 0x8b, 0xc3,
	0xcb, 0xcb, 0xba, 0x8a, 0x57, 0x31, 0xad, 0x85, 0x3b, 0x04, 0xe1, 0x88, 0x79, 0x05, 0xef, 0xbb,
	0x7e, 0x95, 0xb8, 0xf3, 0xf5, 0x71, 0xf1, 0x11, 0xac, 0xb9, 0xe2, 0xa5, 0x18, 0xa6, 0xef, 0xbb,
	0xc5, 0x27, 0xb4, 0xc5, 0x8e, 0x73, 0xcd, 0xd8, 0x02, 0x57, 0x34, 0x37, 0x7a, 0x09, 0x6b, 0x4a,
	0x28, 0xd9, 0x46, 0x97, 0x95, 0x4a, 0xd5, 0x5e, 0x4a, 0x2a, 0xe6, 0xa5, 0x06, 0xff, 0x22, 0xd2,
	0xfc, 0x77, 0x57, 0xf4, 0x84, 0xb4, 0xcb, 0x3f, 0xd2, 0x92, 0x9b, 0x5d, 0xad, 0x18, 0x31, 0x8d,
	0xd1, 0xd2, 0x19, 0x38, 0xa1, 0x69, 0xb8, 0x97, 0xf5, 0x3d, 0xac, 0x1b, 0x1b, 0x71, 0xd3, 0xc4,
	0xda, 0x28, 0x74, 0x9a, 0x78, 0x9b, 0xcd, 0x62, 0xff, 0xc9, 0xf0, 0x6b, 0xd6, 0x40, 0x67, 0x20,
	0xaf, 0x73, 0x0c, 0x57, 0x8c, 0xe5, 0x39, 0x9a, 0xd0, 0x6d, 0x36, 0x0a, 0x3f, 0x4c, 0x31, 0xb7,
	0x30, 0x7f, 0x03, 0xe3, 0x6c, 0xd3, 0x16, 0x1b, 0x96, 0xee, 0x52, 0x7c, 0x9a, 0x92, 0x58, 0x53,
	0xd8, 0xce, 0x5f, 0x15, 0xa5, 0x9b, 0x5c, 0xad, 0x8a, 0x72, 0xe7, 0x89, 0xe8, 0x03, 0xda, 0xea,
	0x9a, 0xb3, 0x55, 0xda, 0x8a, 0xee, 0xf4, 0xd5, 0xd2, 0x9d, 0xdd, 0xd6, 0x77, 0xfc, 0x9f, 0x93,
	0x8e, 0x9b, 0xf4, 0xcf, 0xdf, 0xff, 0x25, 0x00, 0x00, 0xff, 0xff, 0x40, 0x73, 0xc4, 0x14, 0xb8,
	0x34, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserServiceClient interface {
	//新增用户
	NewUser(ctx context.Context, in *UserInfo, opts ...grpc.CallOption) (*NewUserResponse, error)
	//新增宠物
	NewPet(ctx context.Context, in *PetInfo, opts ...grpc.CallOption) (*NewPetResponse, error)
	//我的会员卡 - 暂时用不上
	MyVip(ctx context.Context, in *NilRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//开卡
	NewCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//开卡-支付
	NewCardPay(ctx context.Context, in *NewCardPayRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//北京开卡-通知
	NotifyNewCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error)
	//续费
	RenewCard(ctx context.Context, in *RenewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//北京续费-通知
	NotifyRenewCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error)
	//申请退卡
	ApplyReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//确认退卡
	ReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//驳回申请退卡
	RejectReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error)
	//北京退卡-通知
	NotifyReturnCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error)
	//ERP会员卡信息列表
	GetMemberCardList(ctx context.Context, in *MemberCardRequest, opts ...grpc.CallOption) (*MemberCardResponse, error)
	//指定用戶获取权益剩余
	GetMemberCardRights(ctx context.Context, in *RightsRequest, opts ...grpc.CallOption) (*RightsResponse, error)
	//指定用户的抵扣记录
	GetMemberCardDeductList(ctx context.Context, in *DeductRequest, opts ...grpc.CallOption) (*DeductResponse, error)
	//HIS结算抵扣权益
	WrittenOffMemberCardRights(ctx context.Context, in *DeductRightRequest, opts ...grpc.CallOption) (*BasicResponse, error)
}

type userServiceClient struct {
	cc *grpc.ClientConn
}

func NewUserServiceClient(cc *grpc.ClientConn) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) NewUser(ctx context.Context, in *UserInfo, opts ...grpc.CallOption) (*NewUserResponse, error) {
	out := new(NewUserResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NewUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NewPet(ctx context.Context, in *PetInfo, opts ...grpc.CallOption) (*NewPetResponse, error) {
	out := new(NewPetResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NewPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) MyVip(ctx context.Context, in *NilRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/MyVip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NewCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NewCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NewCardPay(ctx context.Context, in *NewCardPayRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NewCardPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NotifyNewCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error) {
	out := new(BasicResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NotifyNewCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RenewCard(ctx context.Context, in *RenewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/RenewCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NotifyRenewCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error) {
	out := new(BasicResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NotifyRenewCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ApplyReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/ApplyReturnCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/ReturnCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RejectReturnCard(ctx context.Context, in *NewCardRequest, opts ...grpc.CallOption) (*MyVipResponse, error) {
	out := new(MyVipResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/RejectReturnCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) NotifyReturnCard(ctx context.Context, in *NotifyCardRequest, opts ...grpc.CallOption) (*BasicResponse, error) {
	out := new(BasicResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/NotifyReturnCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetMemberCardList(ctx context.Context, in *MemberCardRequest, opts ...grpc.CallOption) (*MemberCardResponse, error) {
	out := new(MemberCardResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/GetMemberCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetMemberCardRights(ctx context.Context, in *RightsRequest, opts ...grpc.CallOption) (*RightsResponse, error) {
	out := new(RightsResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/GetMemberCardRights", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetMemberCardDeductList(ctx context.Context, in *DeductRequest, opts ...grpc.CallOption) (*DeductResponse, error) {
	out := new(DeductResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/GetMemberCardDeductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) WrittenOffMemberCardRights(ctx context.Context, in *DeductRightRequest, opts ...grpc.CallOption) (*BasicResponse, error) {
	out := new(BasicResponse)
	err := c.cc.Invoke(ctx, "/proto.UserService/WrittenOffMemberCardRights", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
type UserServiceServer interface {
	//新增用户
	NewUser(context.Context, *UserInfo) (*NewUserResponse, error)
	//新增宠物
	NewPet(context.Context, *PetInfo) (*NewPetResponse, error)
	//我的会员卡 - 暂时用不上
	MyVip(context.Context, *NilRequest) (*MyVipResponse, error)
	//开卡
	NewCard(context.Context, *NewCardRequest) (*MyVipResponse, error)
	//开卡-支付
	NewCardPay(context.Context, *NewCardPayRequest) (*MyVipResponse, error)
	//北京开卡-通知
	NotifyNewCard(context.Context, *NotifyCardRequest) (*BasicResponse, error)
	//续费
	RenewCard(context.Context, *RenewCardRequest) (*MyVipResponse, error)
	//北京续费-通知
	NotifyRenewCard(context.Context, *NotifyCardRequest) (*BasicResponse, error)
	//申请退卡
	ApplyReturnCard(context.Context, *NewCardRequest) (*MyVipResponse, error)
	//确认退卡
	ReturnCard(context.Context, *NewCardRequest) (*MyVipResponse, error)
	//驳回申请退卡
	RejectReturnCard(context.Context, *NewCardRequest) (*MyVipResponse, error)
	//北京退卡-通知
	NotifyReturnCard(context.Context, *NotifyCardRequest) (*BasicResponse, error)
	//ERP会员卡信息列表
	GetMemberCardList(context.Context, *MemberCardRequest) (*MemberCardResponse, error)
	//指定用戶获取权益剩余
	GetMemberCardRights(context.Context, *RightsRequest) (*RightsResponse, error)
	//指定用户的抵扣记录
	GetMemberCardDeductList(context.Context, *DeductRequest) (*DeductResponse, error)
	//HIS结算抵扣权益
	WrittenOffMemberCardRights(context.Context, *DeductRightRequest) (*BasicResponse, error)
}

// UnimplementedUserServiceServer can be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (*UnimplementedUserServiceServer) NewUser(ctx context.Context, req *UserInfo) (*NewUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewUser not implemented")
}
func (*UnimplementedUserServiceServer) NewPet(ctx context.Context, req *PetInfo) (*NewPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewPet not implemented")
}
func (*UnimplementedUserServiceServer) MyVip(ctx context.Context, req *NilRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyVip not implemented")
}
func (*UnimplementedUserServiceServer) NewCard(ctx context.Context, req *NewCardRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewCard not implemented")
}
func (*UnimplementedUserServiceServer) NewCardPay(ctx context.Context, req *NewCardPayRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewCardPay not implemented")
}
func (*UnimplementedUserServiceServer) NotifyNewCard(ctx context.Context, req *NotifyCardRequest) (*BasicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyNewCard not implemented")
}
func (*UnimplementedUserServiceServer) RenewCard(ctx context.Context, req *RenewCardRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewCard not implemented")
}
func (*UnimplementedUserServiceServer) NotifyRenewCard(ctx context.Context, req *NotifyCardRequest) (*BasicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyRenewCard not implemented")
}
func (*UnimplementedUserServiceServer) ApplyReturnCard(ctx context.Context, req *NewCardRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyReturnCard not implemented")
}
func (*UnimplementedUserServiceServer) ReturnCard(ctx context.Context, req *NewCardRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReturnCard not implemented")
}
func (*UnimplementedUserServiceServer) RejectReturnCard(ctx context.Context, req *NewCardRequest) (*MyVipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectReturnCard not implemented")
}
func (*UnimplementedUserServiceServer) NotifyReturnCard(ctx context.Context, req *NotifyCardRequest) (*BasicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyReturnCard not implemented")
}
func (*UnimplementedUserServiceServer) GetMemberCardList(ctx context.Context, req *MemberCardRequest) (*MemberCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberCardList not implemented")
}
func (*UnimplementedUserServiceServer) GetMemberCardRights(ctx context.Context, req *RightsRequest) (*RightsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberCardRights not implemented")
}
func (*UnimplementedUserServiceServer) GetMemberCardDeductList(ctx context.Context, req *DeductRequest) (*DeductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberCardDeductList not implemented")
}
func (*UnimplementedUserServiceServer) WrittenOffMemberCardRights(ctx context.Context, req *DeductRightRequest) (*BasicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WrittenOffMemberCardRights not implemented")
}

func RegisterUserServiceServer(s *grpc.Server, srv UserServiceServer) {
	s.RegisterService(&_UserService_serviceDesc, srv)
}

func _UserService_NewUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NewUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NewUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NewUser(ctx, req.(*UserInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NewPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NewPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NewPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NewPet(ctx, req.(*PetInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_MyVip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NilRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).MyVip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/MyVip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).MyVip(ctx, req.(*NilRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NewCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NewCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NewCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NewCard(ctx, req.(*NewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NewCardPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCardPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NewCardPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NewCardPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NewCardPay(ctx, req.(*NewCardPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NotifyNewCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NotifyNewCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NotifyNewCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NotifyNewCard(ctx, req.(*NotifyCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RenewCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RenewCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/RenewCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RenewCard(ctx, req.(*RenewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NotifyRenewCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NotifyRenewCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NotifyRenewCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NotifyRenewCard(ctx, req.(*NotifyCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ApplyReturnCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ApplyReturnCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/ApplyReturnCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ApplyReturnCard(ctx, req.(*NewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ReturnCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ReturnCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/ReturnCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ReturnCard(ctx, req.(*NewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RejectReturnCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RejectReturnCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/RejectReturnCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RejectReturnCard(ctx, req.(*NewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_NotifyReturnCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).NotifyReturnCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/NotifyReturnCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).NotifyReturnCard(ctx, req.(*NotifyCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetMemberCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetMemberCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/GetMemberCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetMemberCardList(ctx, req.(*MemberCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetMemberCardRights_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RightsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetMemberCardRights(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/GetMemberCardRights",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetMemberCardRights(ctx, req.(*RightsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetMemberCardDeductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetMemberCardDeductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/GetMemberCardDeductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetMemberCardDeductList(ctx, req.(*DeductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_WrittenOffMemberCardRights_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductRightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).WrittenOffMemberCardRights(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.UserService/WrittenOffMemberCardRights",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).WrittenOffMemberCardRights(ctx, req.(*DeductRightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "proto.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewUser",
			Handler:    _UserService_NewUser_Handler,
		},
		{
			MethodName: "NewPet",
			Handler:    _UserService_NewPet_Handler,
		},
		{
			MethodName: "MyVip",
			Handler:    _UserService_MyVip_Handler,
		},
		{
			MethodName: "NewCard",
			Handler:    _UserService_NewCard_Handler,
		},
		{
			MethodName: "NewCardPay",
			Handler:    _UserService_NewCardPay_Handler,
		},
		{
			MethodName: "NotifyNewCard",
			Handler:    _UserService_NotifyNewCard_Handler,
		},
		{
			MethodName: "RenewCard",
			Handler:    _UserService_RenewCard_Handler,
		},
		{
			MethodName: "NotifyRenewCard",
			Handler:    _UserService_NotifyRenewCard_Handler,
		},
		{
			MethodName: "ApplyReturnCard",
			Handler:    _UserService_ApplyReturnCard_Handler,
		},
		{
			MethodName: "ReturnCard",
			Handler:    _UserService_ReturnCard_Handler,
		},
		{
			MethodName: "RejectReturnCard",
			Handler:    _UserService_RejectReturnCard_Handler,
		},
		{
			MethodName: "NotifyReturnCard",
			Handler:    _UserService_NotifyReturnCard_Handler,
		},
		{
			MethodName: "GetMemberCardList",
			Handler:    _UserService_GetMemberCardList_Handler,
		},
		{
			MethodName: "GetMemberCardRights",
			Handler:    _UserService_GetMemberCardRights_Handler,
		},
		{
			MethodName: "GetMemberCardDeductList",
			Handler:    _UserService_GetMemberCardDeductList_Handler,
		},
		{
			MethodName: "WrittenOffMemberCardRights",
			Handler:    _UserService_WrittenOffMemberCardRights_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user.proto",
}

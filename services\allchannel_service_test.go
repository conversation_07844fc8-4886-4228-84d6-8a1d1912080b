package services

import (
	"context"
	"fmt"
	"github.com/golang-module/carbon/v2"
	kit "github.com/tricobbler/rp-kit"
	pt "order-center/proto/oc"
	"reflect"
	"testing"
	"time"
)

func TestAllChannelService_AfterOrderSynchronizeNew(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx    context.Context
		params *pt.AfterorderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.AfterorderResponse
		wantErr bool
	}{
		// TODO:  订单添加
		{name: "售后订单1"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &AllChannelService{
				BaseService: tt.fields.BaseService,
			}
			c := carbon.SetLocation(time.Local)
			CreateTime := "2023-01-15 15:13:24"
			nowTime := "2023-02-15 14:13:24"
			//判断是否在当前领取周期内的数据
			//订单创建时间的所在月
			orderMonth := c.Parse(CreateTime).Month()
			orderDay := c.Parse(CreateTime).ToDateTimeString()[8:19]
			//当前所在月
			nowMonth := c.Parse(nowTime).Month()
			nowDay := c.Parse(nowTime).ToDateTimeString()[8:19]
			diffMonth := nowMonth - orderMonth + 18
			beginadd := 0
			endadd := 0

			if nowDay <= orderDay {
				beginadd = -1
			} else {
				endadd = 1
			}

			beTime := c.Parse(CreateTime).AddMonthsNoOverflow(diffMonth + beginadd).ToDateTimeString()
			endTime := c.Parse(CreateTime).AddMonthsNoOverflow(diffMonth + endadd).ToDateTimeString()
			fmt.Println(beTime + endTime)

			nowtime := time.Date(2023, 1, 31, 0, 0, 0, 0, time.Local)
			yers := nowtime.AddDate(0, 1, 0)
			newtime := yers

			firstday := time.Date(nowtime.Year(), nowtime.Month()+2, 1, 0, 0, 0, 0, time.Local)
			lastday := firstday.Add(time.Nanosecond * -1)
			if nowtime.Day() > yers.Day() {
				newtime = time.Date(lastday.Year(), lastday.Month(), lastday.Day(), nowtime.Hour(), nowtime.Minute(), nowtime.Second(), 0, time.Local)
			}
			fmt.Println(newtime.Format(kit.DATETIME_LAYOUT))
			var AfterorderOrders = pt.AfterorderRequest{}
			var AfterorderOrderInfo = &pt.OrderAfterorder{}
			var AfterorderOrderDetails = &pt.OrderAfterorderDetails{}

			AfterorderOrderInfo.Rtid = "TH00000811"      //必填  业务系统传递的退款单号  自己随便定义的单号
			AfterorderOrderInfo.Tid = "1591752859191443" //必填， 原始订单号：网店订单号， 订单号信息 下单的单号
			//AfterorderOrderInfo.Total = "100"   //添加的时候不填写，后面审核的时候就需要手动填写。
			AfterorderOrderInfo.Privilege = "0"                                    //必填，商品明细上的订单均摊后的优惠金额  TODO 待确认
			AfterorderOrderInfo.Postfee = ""                                       //必填，商品明细上的订单均摊后的运费金额  TODO 待确认
			AfterorderOrderInfo.Created = time.Now().Format("2006/01/02 15:04:05") //必填，系统生成
			AfterorderOrderInfo.Status = "WaitAgree"                               //管家婆审核
			AfterorderOrderInfo.Aftsaletype = "JustRefund"                         //必填 售后单类型	JustRefund=仅退款	RefundAndGoods=退款退货
			AfterorderOrderInfo.Reasoncode = "01"                                  //必填 01=无理由退换货	02=质量问题	03=损坏	04=错发	05=漏发
			AfterorderOrderInfo.Logistbillcode = ""                                //物流单号
			AfterorderOrderInfo.Aftsaleremark = "备注"                               // 售后单备注

			AfterorderOrderDetails.Oid = "df6a0c390bc5434c9a9c35d948d8e2f0" //必填  商品SKU 订单添加里面的同字段，
			AfterorderOrderDetails.Eshopgoodsname = ""
			AfterorderOrderDetails.Eshopskuname = ""
			AfterorderOrderDetails.Backqty = "1"    //必填 退货数量（不能为0）
			AfterorderOrderDetails.Backtotal = "50" //必填 退款金额
			//AfterorderOrderDetails.Outeriid = "11001"
			AfterorderOrderInfo.Details = append(AfterorderOrderInfo.Details, AfterorderOrderDetails)
			AfterorderOrders.Orders = append(AfterorderOrders.Orders, AfterorderOrderInfo)
			got, err := ac.AfterOrderSynchronizeNew(context.Background(), &AfterorderOrders)
			if (err != nil) != tt.wantErr {
				t.Errorf("AllChannelService.AfterOrderSynchronizeNew() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AllChannelService.AfterOrderSynchronizeNew() = %v, want %v", got, tt.want)
			}
		})
	}
}

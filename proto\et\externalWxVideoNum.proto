syntax = "proto3";
import "google/protobuf/empty.proto";
package et;

service WxVideoService {
  //查询微信视频号类目详情
  rpc GetWxCategoryList (Empty) returns (WxCategoryListResponse);
  // 上传商品到微信审核
  rpc AddProductInfo (UploadProductInfoRequest) returns (AddProductInfoResponse);
  // 获取商品列表
  rpc ProductInfoList (ProductInfoListRequest) returns (ProductInfoListResponse);
  // 更新商品
  rpc UpdateProductInfo (UploadProductInfoRequest) returns (UpdateProductInfoResponse);
  // 免审核更新商品
  rpc FreeUpdateProductInfo (FreeUpdateProductInfoRequest) returns (UpdateProductInfoResponse);
  // 删除商品
  rpc DeleteProductInfo (ProductBaseRequest) returns (WxBaseResponse);
  // 撤回商品审核
  rpc WithdrawProductInfo (ProductBaseRequest) returns (WxBaseResponse);
  // 上架商品
  rpc UpProductInfo (ProductBaseRequest) returns (WxBaseResponse);
  // 下架商品
  rpc DownProductInfo (ProductBaseRequest) returns (WxBaseResponse);
  // 生成订单
  rpc OrderAdd (OrderAddRequest) returns (OrderAddResponse);
  // 生成支付参数
  rpc OrderGetPaymentParams(OrderGetPaymentParamsReq) returns(OrderGetPaymentParamsRes);

  // 同步订单支付结果
  rpc OrderPayStatus (OrderPayStatusRequest) returns (WxBaseResponse);
  // 获取快递公司列表
  rpc DeliveryCompanyList (Empty) returns (DeliveryCompanyListResponse);
  // 订单发货
  rpc DeliverySend (DeliverySendRequest) returns (WxBaseResponse);
  // 订单确认收货
  rpc DeliveryRecieve (DeliveryRecieveRequest) returns (WxBaseResponse);
  // 创建售后
  rpc AftersaleAdd (AftersaleAddRequest) returns (WxBaseResponse);
  // 更新售后
  rpc AftersaleUpdate (AftersaleUpdateRequest) returns (WxBaseResponse);
  // 获取售后详情
  rpc AftersaleGet (AftersaleGetRequest) returns (AftersaleGetResponse);
  // 获取订单
  rpc OrderGet (OrderListRequest) returns (OrderListResponse);
  // 上传图片
  rpc ShopImgUpload(ShopImgUploadRequest) returns(ShopImgUploadResponse);
}

message Empty {}

message WxBaseResponse {
  // 响应码
  int32 errcode = 1;
  // 响应信息
  string errmsg = 2;
}

// 商品通用请求参数
message ProductBaseRequest {
  // 商家自定义商品ID
  string  out_product_id = 1;
}

// 视频号类目详情响应
message WxCategoryListResponse {
  // 响应码
  int32 errcode = 1;
  // 响应信息
  string errmsg = 2;
  //  类目列表
  repeated WxCategoryList third_cat_list = 3;
}

// 视频号类目列表
message WxCategoryList {
  // 类目id
  int32 third_cat_id = 1;
  // 类目名称
  string third_cat_name = 2;
  // 类目资质
  string qualification = 3;
  // 类目资质类型,0:不需要,1:必填,2:选填
  int32 qualification_type = 4;
  // 商品资质
  string product_qualification = 5;
  // 商品资质类型,0:不需要,1:必填,2:选填
  int32 product_qualification_type = 6;
  // 二级类目ID
  int32 second_cat_id = 7;
  // 二级类目名称
  string second_cat_name = 8;
  // 一级类目ID
  int32 first_cat_id = 9;
  // 一级类目名称
  string first_cat_name = 10;
}

// 上传图片至微信
message UploadPicRequest {
  // 图片链接
  string url = 1;
  // 0:此参数返回media_id，目前只用于品牌申请品牌和类目，推荐使用1：返回临时链接
  int32 resp_type = 2;
}

// 图片上传微信响应
message UploadPicResponse {
  // 响应码
  int32 errcode = 1;
  // 响应信息
  string errmsg = 2;
  // 图片信息
  UploadPicData img_info = 3;
}

// 返回的图片信息
message UploadPicData {
  // 图片链接
  string temp_img_url = 1;
  // 图片id
  string media_id = 2;
}

// 上传品牌信息请求参数
message UploadBrandInfoRequest {
  // 请求
  LicenseAndBrandInfo audit_req = 1;
}

// 营业执照及品牌信息
message LicenseAndBrandInfo {
  // 营业执照图片
  repeated string license = 1;
  // 品牌信息
  BrandInfo brand_info = 2;
}

// 品牌信息
message BrandInfo {
  // 认证审核类型
  int32 brand_audit_type = 1;
  // 商标分类
  int32 trademark_type = 2;
  // 选择品牌经营类型
  int32 brand_management_type = 3;
  // 商品产地是否进口
  int32 commodity_origin_type = 4;
  // 商标/品牌词
  string brand_wording = 5;
  // 销售授权书（如商持人为自然人，还需提供有其签名的身份证正反面扫描件)，图片url/media_id
  repeated string sale_authorization = 6;
  // 商标注册证书，图片url/media_id
  repeated string  trademark_registration_certificate = 7;
  // 商标变更证明，图片url/media_id
  repeated string trademark_change_certificate = 8;
  // 	商标注册人姓名
  string trademark_registrant = 9;
  // 商标注册号/申请号
  string trademark_registrant_nu = 10;
  // 商标有效期，yyyy-MM-dd HH:mm:ss
  string trademark_authorization_period = 11;
  // 商标注册申请受理通知书，图片url/media_id
  repeated string trademark_registration_application = 12;
  // 商标申请人姓名
  string trademark_applicant = 13;
  // 商标申请时间, yyyy-MM-dd HH:mm:ss
  string trademark_application_time = 14;
  // 中华人民共和国海关进口货物报关单，图片url/media_id
  repeated string imported_goods_form = 15;
}

// 上传品牌信息响应
message UploadBrandInfoResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 审核单id
  string audit_id = 3;
}

// 上传(创建与更新)商品至微信视频号请求参数 (是否必填，以下填是或否)
message UploadProductInfoRequest {
  // 商家自定义商品ID(是)
  string out_product_id = 1;
  // 标题(是)
  string title = 2;
  // 绑定的小程序商品路径(是)
  string path = 3;
  // 主图,多张,列表(是)
  repeated string head_img = 4;
  // 商品资质图片(否)
  repeated string qualification_pics = 5;
  // 商品详情图文(否)
  DescInfo  desc_info = 6;
  // 第三级类目ID(是)
  int32 third_cat_id = 7;
  // 品牌id(是)
  int64 brand_id = 8;
  // 预留字段，用于版本控制(否)
  string info_version = 9;
  // sku数组(是)
  repeated SkuInfo skus = 10;
}

// 商品详情信息
message DescInfo {
  // 商品详情图文(否)
  string desc = 1;
  // 商品详情图片(否)
  repeated string imgs = 2;
}

// 商品sku
message SkuInfo {
  // 商家自定义商品ID(是)
  string out_product_id = 1;
  // 商家自定义skuID(是)
  string out_sku_id = 2;
  // sku小图(是)
  string thumb_img = 3;
  // 售卖价格,以分为单位(是)
  int32 sale_price = 4;
  // 市场价格,以分为单位(是)
  int32 market_price = 5;
  // 库存(是)
  int32 stock_num = 6;
  // 条形码(否)
  string barcode = 7;
  // 商品编码(否)
  string sku_code = 8;
  // 销售属性(是)
  repeated SkuAttrs sku_attrs = 9;
}

// 商品销售属性
message SkuAttrs {
  // 销售属性key（自定义）(是)
  string attr_key = 1;
  // 销售属性value（自定义）(是)
  string attr_value = 2;
}

// 添加商品响应
message AddProductInfoResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 商品响应数据
  AddProductInfoResponseData data = 3;
}

// 添加商品响应数据
message AddProductInfoResponseData {
  // 交易组件平台内部商品ID
  int64 product_id = 1;
  // 商家自定义商品ID
  string out_product_id = 2;
  // 创建时间
  string create_time = 3;
  // sku数组
  repeated ProductInfoResponseSkus skus = 4;
}

// 商品响应sku
message ProductInfoResponseSkus {
  // 交易组件平台自定义skuID
  int64 sku_id = 1;
  // 商家自定义skuID
  string out_sku_id = 2;
}

// 商品列表请求参数
message ProductInfoListRequest {
  // 选填，不填时获取所有状态商品
  int32 status = 1;
  // 创建开始时间 选填，与end_create_time成对 , yyyy-MM-dd HH:mm:ss
  string start_create_time = 2;
  // 创建结束时间 选填，与start_create_time成对 , yyyy-MM-dd HH:mm:ss
  string end_create_time = 3;
  // 更新开始时间 选填，与end_update_time成对 , yyyy-MM-dd HH:mm:ss
  string start_update_time = 4;
  // 更新结束时间 选填，与start_update_time成对 , yyyy-MM-dd HH:mm:ss
  string end_update_time = 5;
  // 页码
  int32 page = 6;
  // 每页数据量，不超过100
  int32 page_size = 7;
  // 默认0:获取线上数据, 1:获取草稿数据
  int32 need_edit_spu = 8;
}

// 商品列表响应
message ProductInfoListResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 总数
  int32 total_num = 3;
  // 商品信息列表
  repeated ProductInfo spus = 4;
}

// 商品数据
message ProductInfo {
  // 商家自定义商品ID
  string out_product_id = 1;
  // 标题(是)
  string title = 2;
  // 绑定的小程序商品路径
  string path = 3;
  // 主图,多张,列表
  repeated string head_img = 4;
  // 商品审核信息，可能为空
  AuditInfo audit_info = 5;
  // 商品详情图文
  DescInfo  desc_info = 6;
  // 第三级类目ID
  int32 third_cat_id = 7;
  // 品牌id
  int64 brand_id = 8;
  // 预留字段，用于版本控制
  string info_version = 9;
  // sku数组(是)
  repeated SkuInfo skus = 10;
  // 商品线上状态 0 初始值，5 上架，11 自主下架，13 违规下架/风控系统下架
  int32 status = 11;
  // 商品草稿状态  0 初始值，1 编辑中，2 审核中，3 审核失败，4 审核成功
  int32 edit_status = 12;
  // 创建时间
  string create_time = 13;
  // 更新时间
  string update_time = 14;
}

// 商品审核信息
message AuditInfo {
  // 上一次审核时间, yyyy-MM-dd HH:mm:ss
  string audit_time = 1;
  // 拒绝理由
  string reject_reason = 2;
}

// 更新商品响应
message UpdateProductInfoResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 更新商品响应
  UpdateProductInfoResponseData data = 3;
}

// 更新商品响应数据
message UpdateProductInfoResponseData {
  // 交易组件平台内部商品ID
  int64 product_id = 1;
  // 商家自定义商品ID
  string out_product_id = 2;
  // 创建时间
  string update_time = 3;
  // sku数组
  repeated ProductInfoResponseSkus skus = 4;
}

// 免审核更新商品请求参数
message FreeUpdateProductInfoRequest {
  // 商家自定义商品ID，与product_id二选一
  string out_product_id = 1;
  // 绑定的小程序商品路径
  string path = 2;
  // sku数组
  repeated FreeUpdateProductInfoSku skus = 3;
}

// 免审核商品sku
message FreeUpdateProductInfoSku {
  // 商家自定义skuID
  string out_sku_id = 1;
  // 售卖价格,以分为单位
  int32 sale_price = 2;
  // 市场价格,以分为单位
  int32 market_price = 3;
  // 库存
  int32 stock_num = 4;
  // 条形码
  string barcode = 5;
  // 商品编码
  string sku_code = 6;
}

// 生成订单并获取ticket,文档地址：https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/API/order/add_order.html
message OrderAddRequest {
  // 创建时间
  string create_time = 1;
  // 商家自定义订单id，即我们自己平台订单号
  string out_order_id = 2;
  // 用户openid
  string openid = 3;
  // 商家小程序该订单的页面path，用于微信侧订单中心跳转
  string path = 4;
  // 我们自己平台用户id，非必填
  string out_user_id = 6;
  // 订单类型：0，普通单，1，二级商户单
  int32 fund_type = 7;
  // unix秒级时间戳，订单超时时间，取值：[15min, 1d]
  int32 expire_time = 8;
  // 取值范围，[7，3 * 365]，单位：天
  int32 aftersale_duration = 9;
  // 会影响主播归因、分享员归因等，从下单前置检查获取
  string trace_id = 10;
  // 订单详情
  OrderDetail order_detail = 11;
  // 配送方式信息
  DeliveryDetail delivery_detail = 12;
  // 地址信息，delivery_type = 2 无需设置, delivery_type = 4 填写自提门店地址
  AddressInfo address_info = 13;
  // 默认退货地址，退货售后超时时，会让用户将货物寄往此地址。
  AddressInfo default_receiving_address = 14;
}
// 订单详情
message OrderDetail {
  repeated ProductInfos product_infos = 1;
  PayInfo pay_info = 2;
  PriceInfo price_info = 3;
}
// 产品详情
message ProductInfos {
  // 商家自定义商品ID
  string out_product_id = 1;
  // 商家自定义商品skuID，可填空字符串（如果这个product_id下没有sku）
  string out_sku_id = 2;
  // 购买的数量
  int32 product_cnt = 3;
  // 生成订单时商品的售卖价（单位：分），可以跟上传商品接口的价格不一致
  int32 sale_price = 4;
  // sku总实付价
  int32 sku_real_price = 5;
  // 绑定的小程序商品路径
  string path = 6;
  // 生成订单时商品的标题
  string title = 7;
  // 生成订单时商品的头图
  string head_img = 8;
}
// 支付信息
message PayInfo {
  // 支付方式，0，微信支付，1: 货到付款，2：商家会员储蓄卡（默认0）
  int32 pay_method_type = 1;
}
// 价格信息
message PriceInfo {
  // 该订单最终的金额（单位：分
  int32 order_price = 1;
  // 运费（单位：分）
  int32 freight = 2;
  // 优惠金额（单位：分））,不是必填
  int32 discounted_price = 3;
  // 附加金额（单位：分）,不是必填
  int32 additional_price = 4;
  // 附加金额备注,不是必填
  string additional_remarks = 5;
}
// 配送方式信息
message DeliveryDetail{
  // 1: 正常快递, 2: 无需快递, 3: 线下配送, 4: 用户自提 （默认1）
  int32 delivery_type = 1;
}
// 地址信息
message AddressInfo{
  // 收件人姓名
  string receiver_name = 1;
  // 详细收货地址信息
  string detailed_address = 2;
  // 收件人手机号码
  string tel_number = 3;
  // 国家,不是必填
  string country = 4;
  // 省份,不是必填
  string province = 5;
  // 城市,不是必填
  string city = 6;
  // 乡镇,不是必填
  string town = 7;
}

// 生成订单并获取ticket响应
message OrderAddResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 交易组件平台订单信息
  OrderAddDataResponse data = 3;
}
message OrderAddDataResponse{
  // 交易组件平台订单ID
  int64 order_id = 1;
  // 交易组件平台订单ID
  string out_order_id = 2;
}

message OrderGetPaymentParamsReq {
  // 微信侧订单id
  int32 order_id = 1;
  // 商家自定义订单ID
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
}

message OrderGetPaymentParamsRes {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;

  message Data {
    int32 timeStamp = 1;
    string nonceStr = 2;
    string package = 3;
    string paySign = 4;
    string signType = 5;
  }

  Data payment_params = 3;
}

// 同步订单支付结果请求
message OrderPayStatusRequest {
  // 订单ID
  int64 order_id = 1;
  // 商家自定义订单ID，与 order_id 二选一
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
  // 类型，默认1:支付成功,2:支付失败,3:用户取消,4:超时未支付;5:商家取消;10:其他原因取消
  int32 action_type = 4;
  // 其他具体原因，非必填
  string action_remark = 5;
  // 支付订单号，action_type=1且order/add时传的pay_method_type=0时必填，，非必填
  string transaction_id = 6;
  // 支付完成时间，action_type=1时必填
  string pay_time = 7;
}

// 获取订单请求
message OrderListRequest{
  // 订单ID
  int64 order_id = 1;
  // 商家自定义订单ID，与 order_id 二选一
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
}
message OrderListResponse{
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 交易组件平台订单信息
  OrderListDataResponse order = 3;
}
message OrderListDataResponse{
  int64 order_id = 1;
  string out_order_id = 2;
  int32 status = 3;
  string path = 4;
  OrderListDetail order_detail = 5;
}
message OrderListDetail{
  OrderListPromotionInfo promotion_info = 1;
  repeated OrderListProductInfos product_infos = 2;
  OrderListPayInfo pay_info = 3;
  OrderListPriceInfo price_info = 4;
  OrderListDeliveryDetail delivery_detail = 5;
}
message OrderListPromotionInfo{
  string finder_username = 1;
  string finder_nickname = 2;
}
message OrderListProductInfos{
  string out_product_id = 1;
  string out_sku_id = 2;
  int32 product_cnt = 3;
  int32 sale_price = 4;
  string path = 5;
  string title = 6;
  string head_image = 7;
  int32 real_price = 8;
}
message OrderListPayInfo{
  string pay_method = 1;
  string prepay_id = 2;
  string prepay_time = 3;
  string transaction_id = 4;
  string pay_time = 5;
  int32 pay_method_type = 6;
}
message OrderListPriceInfo{
  int32 order_price = 1;
  int32 freight = 2;
  int32 discounted_price = 3;
  int32 additional_price = 4;
  string additional_remarks = 5;
}
message OrderListDeliveryDetail{
  int32 delivery_type = 1;
  int32 finish_all_delivery = 2;
  repeated OrderListDeliveryDetailList delivery_list = 3;
}
message OrderListDeliveryDetailList{
  string waybill_id = 1;
  string delivery_id = 2;
}


// 获取快递公司列表响应
message DeliveryCompanyListResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 快递公司信息
  repeated DeliveryCompanyListData company_list = 3;
}
message DeliveryCompanyListData {
  // 快递公司id
  string delivery_id = 1;
  // 快递公司名称
  string delivery_name = 2;
}

// 订单发货请求
message DeliverySendRequest {
  // 订单ID
  int64 order_id = 1;
  // 商家自定义订单ID，与 order_id 二选一
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
  // 发货完成标志位, 0: 未发完, 1:已发完
  int32 finish_all_delivery = 4;
  // 快递信息
  repeated DeliveryListData delivery_list = 5;
}
message DeliveryListData{
  // 快递公司ID，通过获取快递公司列表获取
  string delivery_id = 1;
  // 快递单号
  string waybill_id = 2;
}

// 订单确认收货请求
message DeliveryRecieveRequest {
  // 订单ID
  int64 order_id = 1;
  // 商家自定义订单ID，与 order_id 二选一
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
}

// 创建售后请求
message AftersaleAddRequest {
  // 商家自定义订单ID
  string out_order_id = 1;
  // 商家自定义售后ID
  string out_aftersale_id = 2;
  // 用户的openid
  string openid = 3;
  // 售后类型，1:退款,2:退款退货,3:换货
  int32 type = 4;
  // 发起申请时间，yyyy-MM-dd HH:mm:ss
  string create_time = 5;
  // 0:未受理,1:用户取消,2:商家受理中,3:商家逾期未处理,4:商家拒绝退款,5:商家拒绝退货退款,6:待买家退货,7:退货退款关闭,8:待商家收货,11:商家退款中,12:商家逾期未退款,13:退款完成,14:退货退款完成,15:换货完成,16:待商家发货,17:待用户确认收货,18:商家拒绝换货,19:商家已收到货
  int32 status = 6;
  // 0:订单可继续售后, 1:订单无继续售后
  int32 finish_all_aftersale = 7;
  // 商家小程序该售后单的页面path，不存在则使用订单path
  string path = 8;
  // 退款金额,单位：分
  int32 refund = 9;
  // 退货相关商品列表
  repeated AfterSaleProductInfos product_infos = 10;
}
message AfterSaleProductInfos{
  // 商家自定义商品ID
  string out_product_id = 1;
  // 商家自定义sku ID, 如果没有则不填
  string out_sku_id = 2;
  // 参与售后的商品数量,product_infos存在时必填
  int32 product_cnt = 3;
}

// 更新售后
message AftersaleUpdateRequest{
  // 商家订单ID
  int64 order_id = 1;
  // 商家自定义订单ID，与 order_id 二选一
  string out_order_id = 2;
  // 用户的openid
  string openid = 3;
  // 商家自定义售后ID
  string out_aftersale_id = 4;
  // 0:未受理,1:用户取消,2:商家受理中,3:商家逾期未处理,4:商家拒绝退款,5:商家拒绝退货退款,6:待买家退货,7:退货退款关闭,8:待商家收货,11:商家退款中,12:商家逾期未退款,13:退款完成,14:退货退款完成,15:换货完成,16:待商家发货,17:待用户确认收货,18:商家拒绝换货,19:商家已收到货
  int32 status = 5;
  // 0:售后未结束, 1:售后结束且订单状态流转
  int32 finish_all_aftersale = 6;
}

// 获取售后详情
message AftersaleGetRequest {
  int64 aftersale_id = 1;
  string out_aftersale_id = 2;
}
message AftersaleGetResponse {
  int32 errcode = 1;
  string errmsg = 2;
  AftersaleGetData after_sales_order = 3;
}
message AftersaleGetData {
  // 外部售后单号
  string out_aftersale_id = 1;
  // 微信侧售后单号
  int64 aftersale_id = 2;
  // 外部订单号
  string out_order_id = 3;
  // 微信侧订单号
  string order_id = 4;
  // 售后商品信息
  AftersaleGetDataProductInfo product_info = 5;
  // 售后类型，1-仅退款，2-退货退款
  int32 type = 6;
  // 退货信息
  AftersaleGetDataReturnInfo return_info = 7;
  // 退款金额，单位分
  int64 orderamt = 8;
  // 申请售后的理由类型，1-商品无货，2-发货时间问题，3-不想要了，4-废弃，5-地址信息填写错误，6-买多/买错/不想要了，7-商品损坏/包装脏污，8-少/错商品/与页面描述不符,9-无效的物流单号，10-物流超72小时停滞，11-快递无法送到指定地点，12-显示签收但未收到商品，13-废弃，14-质量问题，15-其他
  int32 refund_reason_type = 9;
  // 申请售后的理由（补充描述）
  string refund_reason = 10;
  //售后状态，1-用户取消售后申请，2-商家处理退款申请中，4-商家拒绝退款，5-商家拒绝退货，6-待用户退货，7-售后单关闭，8-待商家收货，11-平台退款中，13-退款成功，21-平台处理退款申请中，22-废弃，23-商家处理退货申请中，24-平台处理退货申请中，25-平台退款失败
  int32 status = 11;
  // 申请时间,单位ms
  string create_time = 12;
  // 更新时间，单位ms
  string update_time = 13;
  // 用户OpenID
  string openid = 14;
  // 图片 or 视频附件，结构体，列表
  repeated AftersaleGetDataMediaList media_list = 15;
}
// 获取售后详情-售后商品信息
message AftersaleGetDataProductInfo {
  // 外部商品ID
  int64 out_product_id = 1;
  // 外部sku ID
  string out_sku_id = 2;
  // 商品数量
  int32 product_cnt = 3;
}
// 获取售后详情-退货信息
message AftersaleGetDataReturnInfo {
  // 退货时间
  string order_return_time = 1;
  // 快递公司id
  string delivery_id = 2;
  // 快递单号
  int32 waybill_id = 3;
  // 快递公司名字
  int32 delivery_name = 4;
}
// 获取售后详情-图片 or 视频附件
message AftersaleGetDataMediaList {
  // 固定为1
  int32 type = 1;
  // 图片url
  string url = 2;
  // 缩略图url
  string thumb_url = 3;
}

message ShopImgUploadRequest {
  // 0:此参数返回media_id，目前只用于品牌申请品牌和类目，推荐使用1：返回临时链接
  int32 resp_type = 1;
  // 0:图片流，1:图片url
  int32 upload_type = 2;
  // upload_type=1时必传
  string img_url = 3;
}

message ShopImgInfo {
  // media_id
  string media_id = 1;
  // 	临时链接
  string temp_img_url = 2;
}

message ShopImgUploadResponse {
  // 错误码
  int32 errcode = 1;
  // 	错误信息
  string errmsg = 2;
  ShopImgInfo img_info =3;
}

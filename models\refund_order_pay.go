package models

import (
	"time"
)

type RefundOrderPay struct {
	Id          int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	RefundSn    string    `xorm:"not null default '''' comment('退款单号') index VARCHAR(36)"`
	Paytypecode string    `xorm:"not null comment('支付方式代码') VARCHAR(20)"`
	Payment     string    `xorm:"not null comment('支付金额') VARCHAR(20)"`
	Paytime     time.Time `xorm:" default 'current_timestamp()' comment('支付时间') TIMESTAMP"`
}

package models

import (
	"time"
)

type OrderExpressProduct struct {
	Id          int64     `xorm:"not null pk autoincr INT(11)"`
	ExpressId   int64     `xorm:"default NULL comment('快递id') INT(11)"`
	ProductId   string    `xorm:"default NULL comment('商品id') string(50)"`
	ProductName string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	SkuId       string    `xorm:"default 'NULL' comment('商品sku') VARCHAR(30)"`
	SpecName    string    `xorm:"default 'NULL' comment('规格名称') VARCHAR(255)"`
	ThirdSpuId  string    `xorm:"default 'NULL' comment('第三方spu') VARCHAR(50)"`
	ThirdSkuId  string    `xorm:"default 'NULL' comment('第三方sku') VARCHAR(50)"`
	Count       int       `xorm:"default NULL comment('商品数量') INT(11)"`
	CreateDate  time.Time `xorm:"default 'current_timestamp()' DATETIME"`
}

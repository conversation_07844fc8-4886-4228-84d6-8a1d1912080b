package tasks

import (
	"fmt"
	"math"
	"order-center/models"
	"order-center/proto/dac"
	proto "order-center/proto/oc"
	"order-center/services"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//售后订单定时任务
func refundOrderTask() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "order-center:task:lock:refund_order"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	//初审超过12小时未审核 商家未处理（仅退款）
	timingProcessingRefundOrder(1, 1, 12)
	//初审超过1天未审核 商家未处理（退款退货）
	timingProcessingRefundOrder(1, 2, 1*24)
	//初审通过后7天 用户未处理（退款退货）
	timingProcessingRefundOrder(6, 2, 7*24)
	//终审通过10天 商家未处理（退款退货）
	timingProcessingRefundOrder(7, 2, 10*24)
}

//定时处理售后单
func timingProcessingRefundOrder(refundState, refundType int, hour int) bool {
	r := services.RefundOrderService{}

	switch refundState {
	case 1:
		reason := fmt.Sprintf("商家超过%d小时未处理，自动同意", hour)
		operationType := fmt.Sprintf("超过%d小时自动同意", hour)
		strSql := "SELECT a.* FROM refund_order a INNER JOIN `order_main` b ON a.order_sn=b.order_sn WHERE a.channel_id " +
			"in(1,9) AND a.order_source != 5 AND a.refund_state=? AND a.refund_type=? and b.delivery_type!=1"

		var refundOrder []models.RefundOrder
		if refundType == 1 { //仅退款
			updateRefundOrderTime(refundState, refundType, hour)
			strSql += " AND a.expect_refund_time>0 AND a.expect_refund_time<" + cast.ToString(time.Now().Unix()) + " "
			refundOrder = getRefundOrderByState(refundState, refundType, 0, strSql)
		} else { //退款退货
			strSql += " AND a.create_time < DATE_SUB(SYSDATE(3),INTERVAL ? HOUR) "
			refundOrder = getRefundOrderByState(refundState, refundType, hour, strSql)
		}
		if len(refundOrder) <= 0 {
			return false
		}
		for _, k := range refundOrder {
			answerRequest := new(proto.RefundOrderAnswerRequest)
			answerRequest.OrderId = k.OrderSn
			answerRequest.ExternalOrderId = k.OldOrderSn
			answerRequest.RefundOrderSn = k.RefundSn
			answerRequest.Reason = reason
			answerRequest.ResultType = 1
			answerRequest.ResultTypeNote = "超时未处理系统自动同意"
			answerRequest.OperationType = operationType
			out, _ := r.RefundOrderAnswer(nil, answerRequest)
			//如果发胜处理失败的会一直一直处理下去
			if out.Code != 200 {
				glog.Warning("超时未处理,商家超过"+cast.ToString(hour)+"小时未处理，自动同意,售后单错误:"+k.RefundSn, kit.JsonEncode(out))
			}
		}
	case 6:
		//初审通过后7天 用户未处理
		sqlStr := "SELECT  a.*   FROM  refund_order  a INNER  join  refund_order_log b  on a.`refund_sn`=b.`refund_sn`  LEFT  JOIN  (SELECT c.`refund_sn`  FROM refund_order_log  c WHERE c.`notify_type`='returnExpress') f  ON  f.refund_sn=a.`refund_sn` where  a.`channel_id` in (1,9)  and b.`notify_type`='first-agree' and f.refund_sn='' AND a.refund_state=? and refund_type=? AND b.`ctime`<=DATE_SUB(SYSDATE(3),INTERVAL ? HOUR)"
		refundOrder := getRefundOrderByState(refundState, refundType, hour, sqlStr)
		if len(refundOrder) <= 0 {
			return false
		}
		for _, k := range refundOrder {
			cancelRequest := new(proto.RefundOrderCancelRequest)
			cancelRequest.RefundOrderSn = k.RefundSn
			cancelRequest.ResType = "超时未处理系统自动取消"
			cancelRequest.OperationType = "超时未处理系统自动取消"
			cancelRequest.Reason = "用户超过7天未处理，自动取消"
			out, _ := r.RefundOrderCancel(nil, cancelRequest)
			if out.Code != 200 {
				glog.Warning("超时未处理,用户超过7天未处理，自动取消,售后单错误:"+k.RefundSn, kit.JsonEncode(out))
			}
		}
	case 7:
		//终审通过10天 商家未处理
		sqlStr := "SELECT  a.*  FROM  refund_order  a INNER  join  refund_order_log b  on a.`refund_sn`=b.`refund_sn` INNER JOIN `order_main` c ON a.order_sn=c.order_sn where  a.`channel_id` in (1,9) and c.delivery_type!=1 and a.order_source != 5 and b.`notify_type`='returnExpress' AND a.refund_state=?  and refund_type=? AND b.`ctime`<=DATE_SUB(SYSDATE(3),INTERVAL ? HOUR)"
		refundOrder := getRefundOrderByState(6, refundType, hour, sqlStr)
		if len(refundOrder) <= 0 {
			return false
		}
		for _, k := range refundOrder {
			answerRequest := new(proto.RefundOrderAnswerRequest)
			answerRequest.OrderId = k.OrderSn
			answerRequest.ExternalOrderId = k.OldOrderSn
			answerRequest.RefundOrderSn = k.RefundSn
			answerRequest.Reason = "商家超过10天未处理，自动同意"
			answerRequest.ResultType = 1
			answerRequest.ResultTypeNote = "超时未处理系统自动同意"
			answerRequest.OperationType = "超过10天自动同意"
			answerRequest.OperationUser = ""
			out, _ := r.RefundOrderAnswer(nil, answerRequest)
			if out.Code != 200 {
				glog.Warning("超时未处理,商家超过10天未处理，自动同意,售后单错误:"+k.RefundSn, kit.JsonEncode(out))
			}
		}
	}

	return true

}

//更新自动退款时间
func updateRefundOrderTime(refundState, refundType, hourInterval int) {
	//连接池勿关闭
	db := services.GetDBConn()

	var model []models.RefundOrder
	err := db.Select("id,create_time,shop_id").
		Where("refund_state=? and refund_type=? and expect_refund_time=?", refundState, refundType, 0).
		In("channel_id", services.ChannelDigitalHealth, services.ChannelAwenId).
		Find(&model)
	if err != nil {
		glog.Error(kit.RunFuncName(), "，查询数据库失败，", err)
		return
	}

	financeCodeSlice := []string{}
	financeCodeMap := map[string]int8{}
	for _, v := range model {
		if _, ok := financeCodeMap[v.ShopId]; ok {
			continue
		}
		financeCodeSlice = append(financeCodeSlice, v.ShopId)
		financeCodeMap[v.ShopId] = 1
	}

	dacClient := dac.GetDataCenterClient()

	financeCodeTimeMap := map[string]map[string]int64{}
	if res, err := dacClient.RPC.ShopDeliveryServiceList(dacClient.Ctx, &dac.ShopDeliveryServiceListRequest{
		ChannelId:    services.ChannelAwenId,
		FinanceCodes: strings.Join(financeCodeSlice, ","),
	}); err != nil {
		glog.Error("调用ShopDeliveryServiceList失败，", err)
		return
	} else if res.Code != 200 {
		glog.Error("调用ShopDeliveryServiceList失败，", res.Message)
		return
	} else {
		for _, v := range res.DataList {
			financeCodeTimeMap[v.FinanceCode] = map[string]int64{
				"open_time":  int64(v.BusinessOpentime),
				"close_time": int64(v.BusinessClosetime),
			}
		}
	}

	if len(financeCodeTimeMap) == 0 {
		glog.Error("没有查询到门店营业时间, ", kit.JsonEncode(financeCodeSlice))
		return
	}

	session := db.NewSession()
	defer session.Close()

	var expectRefundTime, createTime, openTime, closeTime int64
	//未审核自动同意间隔时间（单位秒）
	secondInterval := int64(hourInterval * 3600)
	for _, v := range model {
		createTime = v.CreateTime.Unix()
		openTime = financeCodeTimeMap[v.ShopId]["open_time"]
		closeTime = financeCodeTimeMap[v.ShopId]["close_time"]

		//自动同意时间计算规则（只计算营业时间内的时间）
		//1. 申请退款时还没营业，从当天开业时间开始算
		//2. 申请退款时在营业时间内，且自动同意时间也在营业时间内，从申请时间开始算
		//3. 自动同意时间超出了当天营业时间，超出的部分计入第二天的营业时间
		if createTime < openTime {
			expectRefundTime = time.Unix(openTime, 0).Add(time.Duration(hourInterval) * time.Hour).Unix()
		} else if closeTime-createTime > secondInterval {
			expectRefundTime = v.CreateTime.Add(time.Duration(hourInterval) * time.Hour).Unix()
		} else {
			timeGap := secondInterval - int64(math.Max(0, float64(closeTime-createTime)))
			expectRefundTime = time.Unix(openTime, 0).Add(24 * time.Hour).Add(time.Duration(timeGap) * time.Second).Unix()
		}

		if expectRefundTime == 0 {
			continue
		}

		if _, err = session.ID(v.Id).Update(&models.RefundOrder{
			ExpectRefundTime: expectRefundTime,
		}); err != nil {
			lastSql, sqlParams := session.LastSQL()
			glog.Error("更新自动退款时间失败，", err, lastSql, kit.JsonEncode(sqlParams))
		}
	}
}

// 按照状态获取需要处理的 售后单集合
func getRefundOrderByState(refundState, refundType, day int, sqlStr string) []models.RefundOrder {
	//连接池勿关闭
	db := services.GetDBConn()

	var err error
	refundOrder := make([]models.RefundOrder, 0)
	if day == 0 {
		err = db.SQL(sqlStr, refundState, refundType).Find(&refundOrder)
	} else {
		err = db.SQL(sqlStr, refundState, refundType, day).Find(&refundOrder)
	}
	if err != nil {
		glog.Error("定时处理获取售后单错误:" + err.Error())
	}
	return refundOrder
}

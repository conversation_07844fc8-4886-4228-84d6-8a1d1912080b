syntax = "proto3";
import "google/protobuf/empty.proto";
package cc;
service VirtualCardService {
  // 创建虚拟卡任务
  rpc CreateVirtualCard(CreateRequest) returns (VirtualBaseResponse);
  // 创建会员卡
  rpc GetEquityList(CreateRequest) returns (VirtualBaseResponse);
  // 申请注销虚拟卡券
  rpc CancelVirtualCard(CancelVirtualCardReq) returns (google.protobuf.Empty);
  // 获取申请注销虚拟卡券记录列表
  rpc VirtualCardCancelList(VirtualCardCancelListReq) returns (VirtualCardCancelListRes);
  // 获取申请注销虚拟卡券记录列表
  rpc VirtualCardList(VirtualCardListReq) returns (VirtualCardListRes);
}
message VirtualCardListReq{
  string  batch_id = 1;  //批次号
  int32   org_id = 2;   //组织ID
  int32   template_id = 3;  //卡模板ID
  string  user_id = 4;    //兑换用户id
  string  user_mobile = 5;     //兑换用户手机号
  string  use_time_start = 6;   //兑换时间
  string  use_time_end = 7;    //兑换时间
  string  create_time_start = 8; //创建时间
  string  create_time_end = 9;     //创建时间
  repeated int64 card_ids = 10; //卡号集合，选中导出的数据
  int32   page_index = 11;  //分页-当前页
  int32   page_size = 12;  //分页-页码
  string user_no = 13;   //销卡申请人编号
  int32 status = 14;   //卡状态,0-已卖出，1-已激活，2-已注销
  int64 card_id = 15;   //卡号
  string card_str = 16;   //卡号

}
message VirtualCardListRes{
  int64 page_count = 1;          //数据条数
  repeated VirtualCard list = 2; //数据集合
}
message VirtualCard{
  int64 card_id = 1; //卡号
  string batch_id = 2; //批次号
  int32 org_id = 3; //组织ID
  string org_name = 4; //组织名称
  int32 template_id = 5; //卡模板ID
  int32 status = 6; //卡状态,0已卖出，1已激活
  string user_id = 7; //兑换用户id
  string user_mobile = 8; //兑换用户手机号
  string use_time = 9; //兑换时间
  string expire_time = 10; //到期时间
  string create_time = 11; //创建时间
  string update_time = 12; //最后更新时间
  int32 sell_type = 13; //1内销 2外采
  string status_name = 14; //状态名称
  string en_user_mobile = 15;   //加密的手机号
}
message CancelVirtualCardReq{
  string file_url = 1;      //需要销卡的卡列表文件
  string cancel_remark = 2; //销卡备注
  int32 org_id = 3;         //组织ID
  string org_name = 4;      //组织名称
  string user_no = 5;       //销卡申请人编号
  string user_name = 6;     //销卡申请人名称
}
message VirtualCardCancelListReq{
  int32 page_index = 1; //分页下标
  int32 page_size = 2;  //分页页码
  string user_no = 3;   //销卡申请人编号
}
message VirtualCardCancelListRes{
  int64 page_count = 1;                //数据条数
  repeated VirtualCardCancel list = 2; //数据集合
}
message VirtualCardCancel{
  int64 id = 1;            // 自增、主键
  string file_url = 2;      // 需要销卡的卡列表文件
  string cancel_remark = 3; // 销卡备注
  int32 org_id = 4;        // 组织ID
  string org_name = 5;      // 组织名称
  string user_id = 6;       // 销卡申请人编号
  string user_name = 7;     // 销卡申请人名称
  int32 cancel_num = 8;    // 销卡数量
  int32 cancel_status = 9; // 销卡状态(1-待执行，2-已执行，3-执行中)
  string cancel_result = 10; // 执行结果
  string create_time = 11;   // 创建时间
  int32 error_num = 12;    // 执行失败的数量
}
message VirtualBaseResponse{
    int32 code = 1;
    string message =2;
}

message CreateRequest {
  //组织ID
  int32 org_id = 1;
  //组织名称
  string org_name = 2;
  //卡模板ID
  int32 template_id = 3;
  //生成卡券数量
  int32 card_count = 4;
  //单张金额
  int32 card_price = 5;
  //创建人ID
  string user_id = 6;
  //创建人名称
  string user_name = 7;
  //销售形式  1内销 2外采
  int32 sell_type = 8;
}

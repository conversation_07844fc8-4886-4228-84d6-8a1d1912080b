package tasks

import (
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services/export"
	"testing"
)

func Test_consumeOrderExportTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "消费订单导出任务",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			consumeOrderExportTask()
		})
	}
}

func Test_orderExport_dataExport(t *testing.T) {
	type fields struct {
		f          *excelize.File
		sheetName  string
		storeMap   map[string]*dac.StoreInfo
		taskParams *oc.AwenMaterOrderListRequest
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantNums int
		wantErr  bool
	}{
		{
			name: "Test_orderExport_dataExport1",
			args: args{
				taskParams: "{\"search_type\":1,\"keyword\":\"4100000014159690\",\"time_type\":0,\"start_time\":\"2021-11-18 00:00:00\",\"end_time\":\"2021-11-19 23:59:59\",\"product_name\":\"\",\"channel_id\":0,\"order_status\":0,\"order_type\":\"\",\"delivery_type\":0,\"pay_mode\":0,\"page_index\":0,\"page_size\":0,\"shopids\":null,\"user_no\":\"U_VXCF5C0\",\"app_channel\":0,\"ip\":\"**********\",\"ip_location\":\"\"}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &export.OrderExport{
				F:         excelize.NewFile(),
				SheetName: "Sheet1",
			}
			gotNums, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("dataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotNums != tt.wantNums {
				t.Errorf("dataExport() gotNums = %v, want %v", gotNums, tt.wantNums)
			}
		})
	}
}

func Test_virtualOrderProductExport_dataExport(t *testing.T) {
	type fields struct {
		f          *excelize.File
		sheetName  string
		storeMap   map[string]*dac.StoreInfo
		taskParams *oc.AwenVirtualOrderListRequest
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantNums int
		wantErr  bool
	}{
		{
			name: "Test_virtualOrderProductExport_dataExport",
			args: args{
				taskParams: "{\"search_type\":1,\"keyword\":\"\",\"start_time\":\"2022-07-04 00:00:00\",\"end_time\":\"2024-07-09 00:00:00\"}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &export.VirtualOrderProductExport{
				F:         excelize.NewFile(),
				SheetName: "Sheet1",
			}
			gotNums, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("dataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotNums != tt.wantNums {
				t.Errorf("dataExport() gotNums = %v, want %v", gotNums, tt.wantNums)
			}
		})
	}
}

func Test_orderExport_generateDownUrl(t *testing.T) {
	type fields struct {
		f          *excelize.File
		sheetName  string
		storeMap   map[string]*dac.StoreInfo
		taskParams *oc.AwenMaterOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		wantUrl string
		wantErr bool
	}{
		{name: "111222"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &export.OrderExport{
				F:         excelize.NewFile(),
				SheetName: "Sheet1",
			}

			//e:=NewOrderExportTask(4)
			gotUrl, err := e.GenerateDownUrl()
			if (err != nil) != tt.wantErr {
				t.Errorf("generateDownUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotUrl != tt.wantUrl {
				t.Errorf("generateDownUrl() gotUrl = %v, want %v", gotUrl, tt.wantUrl)
			}
		})
	}
}

func Test_virtualOrderExport_dataExport(t *testing.T) {
	type fields struct {
		f          *excelize.File
		sheetName  string
		storeMap   map[string]*dac.StoreInfo
		taskParams *oc.AwenVirtualOrderListRequest
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantNums int
		wantErr  bool
	}{
		{
			name: "Test_virtualOrderProductExport_dataExport",
			args: args{
				taskParams: "{\"search_type\":0,\"keyword\":\"4100000011661184\",\"time_type\":2,\"start_time\":\"2020-12-22 00:00:00\",\"end_time\":\"2021-12-23 23:59:59\",\"product_name\":\"\",\"order_status\":0,\"channel_id\":0,\"order_type\":\"\",\"delivery_type\":0,\"pay_mode\":0,\"app_channel\":0,\"paysn\":\"\",\"sale_channel\":0}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &export.VirtualOrderExport{
				F:         excelize.NewFile(),
				SheetName: "Sheet1",
			}
			gotNums, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("dataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotNums != tt.wantNums {
				t.Errorf("dataExport() gotNums = %v, want %v", gotNums, tt.wantNums)
			}
		})
	}
}

func Test_parentOrderExport_dataExport(t *testing.T) {
	type fields struct {
		f          *excelize.File
		sheetName  string
		storeMap   map[string]*dac.StoreInfo
		taskParams *oc.AwenParentOrderListRequest
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantNums int
		wantErr  bool
	}{
		{
			name: "Test_virtualOrderProductExport_dataExport",
			args: args{
				taskParams: "{\"search_type\":0,\"keyword\":\"4100000011661184\",\"time_type\":2,\"start_time\":\"2020-12-22 00:00:00\",\"end_time\":\"2021-12-23 23:59:59\",\"product_name\":\"\",\"order_status\":0,\"channel_id\":0,\"order_type\":\"\",\"delivery_type\":0,\"pay_mode\":0,\"app_channel\":0,\"paysn\":\"\",\"sale_channel\":0}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &export.ParentOrderExport{
				F:         excelize.NewFile(),
				SheetName: "Sheet1",
			}
			gotNums, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("dataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotNums != tt.wantNums {
				t.Errorf("dataExport() gotNums = %v, want %v", gotNums, tt.wantNums)
			}
		})
	}
}

func Test_manualExport(t *testing.T) {
	type args struct {
		TaskContent int
		TaskParams  string
	}

	//TaskContent10Params := &oc.CommunityGroupOrderListRequest{
	//	SearchType: "1",
	//	Keyword:    "9964128297246424",
	//	//TimeType:   "1",
	//	StartTime: "2024-08-26 00:00:00",
	//	EndTime:   "2024-08-28 23:59:59",
	//	PageSize:  10,
	//}

	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "",
			args: args{
				TaskContent: 3,
				TaskParams:  "{\"start_time\":\"2024-08-26 00:00:00\",\"end_time\":\"2024-08-28 23:59:59\",\"keyword\":\"9964128297246424\",\"channel_id\":0,\"order_type\":0,\"page_index\":1,\"page_size\":10,\"refund_state\":0,\"search_type\":1,\"shopids\":[],\"user_type\":0,\"app_channel\":0}",
			},
		},
		//{
		//	name: "",
		//	args: args{
		//		TaskContent: 10,
		//		TaskParams:  kit.JsonEncode(TaskContent10Params),
		//	},
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := manualExport(tt.args.TaskContent, tt.args.TaskParams, 6, "530219708465609002")
			if (err != nil) != tt.wantErr {
				t.Errorf("manualExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("manualExport() got = %v, want %v", got, tt.want)
			}
		})
	}
}

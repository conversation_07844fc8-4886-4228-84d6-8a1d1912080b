// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/lucky_draw.proto

package ac

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type MiniLuckyDrawRequest struct {
	//活动id
	ActivityId string `protobuf:"bytes,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	//scrm用户id
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	Mobile               string   `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiniLuckyDrawRequest) Reset()         { *m = MiniLuckyDrawRequest{} }
func (m *MiniLuckyDrawRequest) String() string { return proto.CompactTextString(m) }
func (*MiniLuckyDrawRequest) ProtoMessage()    {}
func (*MiniLuckyDrawRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{0}
}

func (m *MiniLuckyDrawRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckyDrawRequest.Unmarshal(m, b)
}
func (m *MiniLuckyDrawRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckyDrawRequest.Marshal(b, m, deterministic)
}
func (m *MiniLuckyDrawRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckyDrawRequest.Merge(m, src)
}
func (m *MiniLuckyDrawRequest) XXX_Size() int {
	return xxx_messageInfo_MiniLuckyDrawRequest.Size(m)
}
func (m *MiniLuckyDrawRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckyDrawRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckyDrawRequest proto.InternalMessageInfo

func (m *MiniLuckyDrawRequest) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *MiniLuckyDrawRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *MiniLuckyDrawRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type LuckyDrawActivityRequest struct {
	//活动id
	Id        int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PageSize  int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int64 `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//是否积分抽奖
	IsIntegral           int32    `protobuf:"varint,4,opt,name=is_integral,json=isIntegral,proto3" json:"is_integral"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivityRequest) Reset()         { *m = LuckyDrawActivityRequest{} }
func (m *LuckyDrawActivityRequest) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityRequest) ProtoMessage()    {}
func (*LuckyDrawActivityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{1}
}

func (m *LuckyDrawActivityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityRequest.Unmarshal(m, b)
}
func (m *LuckyDrawActivityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityRequest.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityRequest.Merge(m, src)
}
func (m *LuckyDrawActivityRequest) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityRequest.Size(m)
}
func (m *LuckyDrawActivityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityRequest proto.InternalMessageInfo

func (m *LuckyDrawActivityRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LuckyDrawActivityRequest) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *LuckyDrawActivityRequest) GetPageIndex() int64 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *LuckyDrawActivityRequest) GetIsIntegral() int32 {
	if m != nil {
		return m.IsIntegral
	}
	return 0
}

type LuckyDrawActivityListRes struct {
	Msg                  string                   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*LuckyDrawActivityList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *LuckyDrawActivityListRes) Reset()         { *m = LuckyDrawActivityListRes{} }
func (m *LuckyDrawActivityListRes) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityListRes) ProtoMessage()    {}
func (*LuckyDrawActivityListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{2}
}

func (m *LuckyDrawActivityListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityListRes.Unmarshal(m, b)
}
func (m *LuckyDrawActivityListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityListRes.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityListRes.Merge(m, src)
}
func (m *LuckyDrawActivityListRes) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityListRes.Size(m)
}
func (m *LuckyDrawActivityListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityListRes.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityListRes proto.InternalMessageInfo

func (m *LuckyDrawActivityListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *LuckyDrawActivityListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LuckyDrawActivityListRes) GetData() []*LuckyDrawActivityList {
	if m != nil {
		return m.Data
	}
	return nil
}

type LuckyDrawActivityListResponse struct {
	Data                 []*LuckyDrawActivityList `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *LuckyDrawActivityListResponse) Reset()         { *m = LuckyDrawActivityListResponse{} }
func (m *LuckyDrawActivityListResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityListResponse) ProtoMessage()    {}
func (*LuckyDrawActivityListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{3}
}

func (m *LuckyDrawActivityListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityListResponse.Unmarshal(m, b)
}
func (m *LuckyDrawActivityListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityListResponse.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityListResponse.Merge(m, src)
}
func (m *LuckyDrawActivityListResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityListResponse.Size(m)
}
func (m *LuckyDrawActivityListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityListResponse proto.InternalMessageInfo

func (m *LuckyDrawActivityListResponse) GetData() []*LuckyDrawActivityList {
	if m != nil {
		return m.Data
	}
	return nil
}

type LuckyDrawActivityList struct {
	//自增id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//类型 1 总计可抽 2 每天可抽
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//次数
	Number int32 `protobuf:"varint,3,opt,name=number,proto3" json:"number"`
	//活动开始时间
	StartTime string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//活动id
	ActivityId string `protobuf:"bytes,6,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 推荐积分商城时间
	RecommendTime string `protobuf:"bytes,7,opt,name=recommend_time,json=recommendTime,proto3" json:"recommend_time"`
	// 是否积分抽奖
	Integral             int32    `protobuf:"varint,8,opt,name=integral,proto3" json:"integral"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivityList) Reset()         { *m = LuckyDrawActivityList{} }
func (m *LuckyDrawActivityList) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityList) ProtoMessage()    {}
func (*LuckyDrawActivityList) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{4}
}

func (m *LuckyDrawActivityList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityList.Unmarshal(m, b)
}
func (m *LuckyDrawActivityList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityList.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityList.Merge(m, src)
}
func (m *LuckyDrawActivityList) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityList.Size(m)
}
func (m *LuckyDrawActivityList) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityList.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityList proto.InternalMessageInfo

func (m *LuckyDrawActivityList) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LuckyDrawActivityList) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LuckyDrawActivityList) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *LuckyDrawActivityList) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *LuckyDrawActivityList) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *LuckyDrawActivityList) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *LuckyDrawActivityList) GetRecommendTime() string {
	if m != nil {
		return m.RecommendTime
	}
	return ""
}

func (m *LuckyDrawActivityList) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

type LuckyDrawActivityInfoResponse struct {
	Data                 *LuckyDrawActivity `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LuckyDrawActivityInfoResponse) Reset()         { *m = LuckyDrawActivityInfoResponse{} }
func (m *LuckyDrawActivityInfoResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityInfoResponse) ProtoMessage()    {}
func (*LuckyDrawActivityInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{5}
}

func (m *LuckyDrawActivityInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityInfoResponse.Unmarshal(m, b)
}
func (m *LuckyDrawActivityInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityInfoResponse.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityInfoResponse.Merge(m, src)
}
func (m *LuckyDrawActivityInfoResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityInfoResponse.Size(m)
}
func (m *LuckyDrawActivityInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityInfoResponse proto.InternalMessageInfo

func (m *LuckyDrawActivityInfoResponse) GetData() *LuckyDrawActivity {
	if m != nil {
		return m.Data
	}
	return nil
}

type LuckyDrawActivity struct {
	//自增id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//奖项默认背景色值
	DefaultColor string `protobuf:"bytes,2,opt,name=default_color,json=defaultColor,proto3" json:"default_color"`
	//背景图
	BackgroundImg string `protobuf:"bytes,3,opt,name=background_img,json=backgroundImg,proto3" json:"background_img"`
	//类型 1 总计可抽 2 每天可抽
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	//次数
	Number int32 `protobuf:"varint,5,opt,name=number,proto3" json:"number"`
	//分享好友再抽一次 1开启 0关闭
	IsShare int32 `protobuf:"varint,6,opt,name=is_share,json=isShare,proto3" json:"is_share"`
	//奖品
	Prizes []*LuckyDrawPrize `protobuf:"bytes,7,rep,name=prizes,proto3" json:"prizes"`
	//点击抽奖按钮图像
	LuckyDrawButtonImage string `protobuf:"bytes,8,opt,name=lucky_draw_button_image,json=luckyDrawButtonImage,proto3" json:"lucky_draw_button_image"`
	//点击分享按钮
	ShareButtonImage string `protobuf:"bytes,9,opt,name=share_button_image,json=shareButtonImage,proto3" json:"share_button_image"`
	//活动已结束切图
	ActivityEndImage string `protobuf:"bytes,10,opt,name=activity_end_image,json=activityEndImage,proto3" json:"activity_end_image"`
	//次数已用完切图
	NumberRunOutImage string `protobuf:"bytes,11,opt,name=number_run_out_image,json=numberRunOutImage,proto3" json:"number_run_out_image"`
	//客服微信二维码
	ServiceCodeUrl string `protobuf:"bytes,12,opt,name=service_code_url,json=serviceCodeUrl,proto3" json:"service_code_url"`
	//活动开始时间
	StartTime string `protobuf:"bytes,13,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,14,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//活动规则
	ActivityRule string `protobuf:"bytes,15,opt,name=activity_rule,json=activityRule,proto3" json:"activity_rule"`
	//奖项选中背景色值
	SelectColor string `protobuf:"bytes,16,opt,name=select_color,json=selectColor,proto3" json:"select_color"`
	//分享按钮标题
	ShareButtonTitle string `protobuf:"bytes,17,opt,name=share_button_title,json=shareButtonTitle,proto3" json:"share_button_title"`
	//分享按钮跳转路径
	ShareButtonJump string `protobuf:"bytes,18,opt,name=share_button_jump,json=shareButtonJump,proto3" json:"share_button_jump"`
	//分享出去的图片路径
	ShareImageUrl string `protobuf:"bytes,19,opt,name=share_image_url,json=shareImageUrl,proto3" json:"share_image_url"`
	//抽奖按钮置灰图片
	LuckyButtonGrayImg string `protobuf:"bytes,20,opt,name=lucky_button_gray_img,json=luckyButtonGrayImg,proto3" json:"lucky_button_gray_img"`
	//是否允许积分抽奖 0 否1是
	Integral int32 `protobuf:"varint,21,opt,name=integral,proto3" json:"integral"`
	//每次消耗多少积分
	IntegralPay          int32    `protobuf:"varint,22,opt,name=integral_pay,json=integralPay,proto3" json:"integral_pay"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivity) Reset()         { *m = LuckyDrawActivity{} }
func (m *LuckyDrawActivity) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivity) ProtoMessage()    {}
func (*LuckyDrawActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{6}
}

func (m *LuckyDrawActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivity.Unmarshal(m, b)
}
func (m *LuckyDrawActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivity.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivity.Merge(m, src)
}
func (m *LuckyDrawActivity) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivity.Size(m)
}
func (m *LuckyDrawActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivity.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivity proto.InternalMessageInfo

func (m *LuckyDrawActivity) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LuckyDrawActivity) GetDefaultColor() string {
	if m != nil {
		return m.DefaultColor
	}
	return ""
}

func (m *LuckyDrawActivity) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *LuckyDrawActivity) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LuckyDrawActivity) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *LuckyDrawActivity) GetIsShare() int32 {
	if m != nil {
		return m.IsShare
	}
	return 0
}

func (m *LuckyDrawActivity) GetPrizes() []*LuckyDrawPrize {
	if m != nil {
		return m.Prizes
	}
	return nil
}

func (m *LuckyDrawActivity) GetLuckyDrawButtonImage() string {
	if m != nil {
		return m.LuckyDrawButtonImage
	}
	return ""
}

func (m *LuckyDrawActivity) GetShareButtonImage() string {
	if m != nil {
		return m.ShareButtonImage
	}
	return ""
}

func (m *LuckyDrawActivity) GetActivityEndImage() string {
	if m != nil {
		return m.ActivityEndImage
	}
	return ""
}

func (m *LuckyDrawActivity) GetNumberRunOutImage() string {
	if m != nil {
		return m.NumberRunOutImage
	}
	return ""
}

func (m *LuckyDrawActivity) GetServiceCodeUrl() string {
	if m != nil {
		return m.ServiceCodeUrl
	}
	return ""
}

func (m *LuckyDrawActivity) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *LuckyDrawActivity) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *LuckyDrawActivity) GetActivityRule() string {
	if m != nil {
		return m.ActivityRule
	}
	return ""
}

func (m *LuckyDrawActivity) GetSelectColor() string {
	if m != nil {
		return m.SelectColor
	}
	return ""
}

func (m *LuckyDrawActivity) GetShareButtonTitle() string {
	if m != nil {
		return m.ShareButtonTitle
	}
	return ""
}

func (m *LuckyDrawActivity) GetShareButtonJump() string {
	if m != nil {
		return m.ShareButtonJump
	}
	return ""
}

func (m *LuckyDrawActivity) GetShareImageUrl() string {
	if m != nil {
		return m.ShareImageUrl
	}
	return ""
}

func (m *LuckyDrawActivity) GetLuckyButtonGrayImg() string {
	if m != nil {
		return m.LuckyButtonGrayImg
	}
	return ""
}

func (m *LuckyDrawActivity) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *LuckyDrawActivity) GetIntegralPay() int32 {
	if m != nil {
		return m.IntegralPay
	}
	return 0
}

type LuckyDrawPrize struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//九宫格切图
	PrizeImageUrl string `protobuf:"bytes,2,opt,name=prize_image_url,json=prizeImageUrl,proto3" json:"prize_image_url"`
	//奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖 5 跳转链接
	PrizeType int32 `protobuf:"varint,3,opt,name=prize_type,json=prizeType,proto3" json:"prize_type"`
	//中奖图
	WinningImageUrl string `protobuf:"bytes,4,opt,name=winning_image_url,json=winningImageUrl,proto3" json:"winning_image_url"`
	//奖品数量
	PrizesNumber int32 `protobuf:"varint,5,opt,name=prizes_number,json=prizesNumber,proto3" json:"prizes_number"`
	//中奖概率
	Probability int32 `protobuf:"varint,6,opt,name=probability,proto3" json:"probability"`
	//券ID
	CouponId int32 `protobuf:"varint,7,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	// 跳转链接地址
	LinkUrl              string   `protobuf:"bytes,8,opt,name=link_url,json=linkUrl,proto3" json:"link_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawPrize) Reset()         { *m = LuckyDrawPrize{} }
func (m *LuckyDrawPrize) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawPrize) ProtoMessage()    {}
func (*LuckyDrawPrize) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{7}
}

func (m *LuckyDrawPrize) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawPrize.Unmarshal(m, b)
}
func (m *LuckyDrawPrize) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawPrize.Marshal(b, m, deterministic)
}
func (m *LuckyDrawPrize) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawPrize.Merge(m, src)
}
func (m *LuckyDrawPrize) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawPrize.Size(m)
}
func (m *LuckyDrawPrize) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawPrize.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawPrize proto.InternalMessageInfo

func (m *LuckyDrawPrize) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LuckyDrawPrize) GetPrizeImageUrl() string {
	if m != nil {
		return m.PrizeImageUrl
	}
	return ""
}

func (m *LuckyDrawPrize) GetPrizeType() int32 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *LuckyDrawPrize) GetWinningImageUrl() string {
	if m != nil {
		return m.WinningImageUrl
	}
	return ""
}

func (m *LuckyDrawPrize) GetPrizesNumber() int32 {
	if m != nil {
		return m.PrizesNumber
	}
	return 0
}

func (m *LuckyDrawPrize) GetProbability() int32 {
	if m != nil {
		return m.Probability
	}
	return 0
}

func (m *LuckyDrawPrize) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *LuckyDrawPrize) GetLinkUrl() string {
	if m != nil {
		return m.LinkUrl
	}
	return ""
}

type WinningRecordReq struct {
	//用户ID（前端不用传）
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//活动ID，活动列表返回的ID
	ActivityId           int32    `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex            int32    `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WinningRecordReq) Reset()         { *m = WinningRecordReq{} }
func (m *WinningRecordReq) String() string { return proto.CompactTextString(m) }
func (*WinningRecordReq) ProtoMessage()    {}
func (*WinningRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{8}
}

func (m *WinningRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningRecordReq.Unmarshal(m, b)
}
func (m *WinningRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningRecordReq.Marshal(b, m, deterministic)
}
func (m *WinningRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningRecordReq.Merge(m, src)
}
func (m *WinningRecordReq) XXX_Size() int {
	return xxx_messageInfo_WinningRecordReq.Size(m)
}
func (m *WinningRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_WinningRecordReq proto.InternalMessageInfo

func (m *WinningRecordReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *WinningRecordReq) GetActivityId() int32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *WinningRecordReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WinningRecordReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type WinningRecordRes struct {
	Msg                  string           `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32            `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*WinningRecord `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WinningRecordRes) Reset()         { *m = WinningRecordRes{} }
func (m *WinningRecordRes) String() string { return proto.CompactTextString(m) }
func (*WinningRecordRes) ProtoMessage()    {}
func (*WinningRecordRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{9}
}

func (m *WinningRecordRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningRecordRes.Unmarshal(m, b)
}
func (m *WinningRecordRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningRecordRes.Marshal(b, m, deterministic)
}
func (m *WinningRecordRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningRecordRes.Merge(m, src)
}
func (m *WinningRecordRes) XXX_Size() int {
	return xxx_messageInfo_WinningRecordRes.Size(m)
}
func (m *WinningRecordRes) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningRecordRes.DiscardUnknown(m)
}

var xxx_messageInfo_WinningRecordRes proto.InternalMessageInfo

func (m *WinningRecordRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *WinningRecordRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *WinningRecordRes) GetData() []*WinningRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type WinningRecord struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//中奖图
	WinningImageUrl string `protobuf:"bytes,2,opt,name=winning_image_url,json=winningImageUrl,proto3" json:"winning_image_url"`
	//奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖 5 跳转链接
	PrizeType int32 `protobuf:"varint,3,opt,name=prize_type,json=prizeType,proto3" json:"prize_type"`
	//抽奖时间
	PrizeTime string `protobuf:"bytes,4,opt,name=prize_time,json=prizeTime,proto3" json:"prize_time"`
	//门店券ID
	CouponId int32 `protobuf:"varint,5,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//收件地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address"`
	//中奖记录ID
	LuckyDrawRecodeId int32 `protobuf:"varint,7,opt,name=lucky_draw_recode_id,json=luckyDrawRecodeId,proto3" json:"lucky_draw_recode_id"`
	// 跳转链接
	LinkUrl              string   `protobuf:"bytes,8,opt,name=link_url,json=linkUrl,proto3" json:"link_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WinningRecord) Reset()         { *m = WinningRecord{} }
func (m *WinningRecord) String() string { return proto.CompactTextString(m) }
func (*WinningRecord) ProtoMessage()    {}
func (*WinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{10}
}

func (m *WinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningRecord.Unmarshal(m, b)
}
func (m *WinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningRecord.Marshal(b, m, deterministic)
}
func (m *WinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningRecord.Merge(m, src)
}
func (m *WinningRecord) XXX_Size() int {
	return xxx_messageInfo_WinningRecord.Size(m)
}
func (m *WinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_WinningRecord proto.InternalMessageInfo

func (m *WinningRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WinningRecord) GetWinningImageUrl() string {
	if m != nil {
		return m.WinningImageUrl
	}
	return ""
}

func (m *WinningRecord) GetPrizeType() int32 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *WinningRecord) GetPrizeTime() string {
	if m != nil {
		return m.PrizeTime
	}
	return ""
}

func (m *WinningRecord) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *WinningRecord) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *WinningRecord) GetLuckyDrawRecodeId() int32 {
	if m != nil {
		return m.LuckyDrawRecodeId
	}
	return 0
}

func (m *WinningRecord) GetLinkUrl() string {
	if m != nil {
		return m.LinkUrl
	}
	return ""
}

type LuckyDrawActivityInfoReq struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivityInfoReq) Reset()         { *m = LuckyDrawActivityInfoReq{} }
func (m *LuckyDrawActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityInfoReq) ProtoMessage()    {}
func (*LuckyDrawActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{11}
}

func (m *LuckyDrawActivityInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityInfoReq.Unmarshal(m, b)
}
func (m *LuckyDrawActivityInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityInfoReq.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityInfoReq.Merge(m, src)
}
func (m *LuckyDrawActivityInfoReq) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityInfoReq.Size(m)
}
func (m *LuckyDrawActivityInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityInfoReq proto.InternalMessageInfo

func (m *LuckyDrawActivityInfoReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type LuckyDrawActivityInfoRes struct {
	Msg                  string             `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *LuckyDrawActivity `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LuckyDrawActivityInfoRes) Reset()         { *m = LuckyDrawActivityInfoRes{} }
func (m *LuckyDrawActivityInfoRes) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityInfoRes) ProtoMessage()    {}
func (*LuckyDrawActivityInfoRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{12}
}

func (m *LuckyDrawActivityInfoRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityInfoRes.Unmarshal(m, b)
}
func (m *LuckyDrawActivityInfoRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityInfoRes.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityInfoRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityInfoRes.Merge(m, src)
}
func (m *LuckyDrawActivityInfoRes) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityInfoRes.Size(m)
}
func (m *LuckyDrawActivityInfoRes) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityInfoRes.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityInfoRes proto.InternalMessageInfo

func (m *LuckyDrawActivityInfoRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *LuckyDrawActivityInfoRes) GetData() *LuckyDrawActivity {
	if m != nil {
		return m.Data
	}
	return nil
}

type MiniLuckDrawInfoResponse struct {
	Msg                  string        `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *LuckDrawInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MiniLuckDrawInfoResponse) Reset()         { *m = MiniLuckDrawInfoResponse{} }
func (m *MiniLuckDrawInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MiniLuckDrawInfoResponse) ProtoMessage()    {}
func (*MiniLuckDrawInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{13}
}

func (m *MiniLuckDrawInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckDrawInfoResponse.Unmarshal(m, b)
}
func (m *MiniLuckDrawInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckDrawInfoResponse.Marshal(b, m, deterministic)
}
func (m *MiniLuckDrawInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckDrawInfoResponse.Merge(m, src)
}
func (m *MiniLuckDrawInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MiniLuckDrawInfoResponse.Size(m)
}
func (m *MiniLuckDrawInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckDrawInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckDrawInfoResponse proto.InternalMessageInfo

func (m *MiniLuckDrawInfoResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MiniLuckDrawInfoResponse) GetData() *LuckDrawInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type LuckDrawInfo struct {
	//可抽数量
	Number int32 `protobuf:"varint,1,opt,name=number,proto3" json:"number"`
	//抽奖类型 1 总共可抽 2 每天可抽
	NumberType int32 `protobuf:"varint,2,opt,name=number_type,json=numberType,proto3" json:"number_type"`
	//分享可抽数量
	ShareNumber int32 `protobuf:"varint,3,opt,name=share_number,json=shareNumber,proto3" json:"share_number"`
	//是否可分享 1可以 2不可以
	CanShare int32 `protobuf:"varint,4,opt,name=can_share,json=canShare,proto3" json:"can_share"`
	//状态 1抽奖次数用完 2 活动结束
	State int32 `protobuf:"varint,5,opt,name=state,proto3" json:"state"`
	//最大抽奖数
	MaxNumber int32 `protobuf:"varint,6,opt,name=max_number,json=maxNumber,proto3" json:"max_number"`
	//是否允许积分抽奖 0否 1是
	Integral int32 `protobuf:"varint,7,opt,name=integral,proto3" json:"integral"`
	//每次消耗多少积分
	IntegralPay int32 `protobuf:"varint,8,opt,name=integral_pay,json=integralPay,proto3" json:"integral_pay"`
	// 活动的id
	ActivityId           string   `protobuf:"bytes,9,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckDrawInfo) Reset()         { *m = LuckDrawInfo{} }
func (m *LuckDrawInfo) String() string { return proto.CompactTextString(m) }
func (*LuckDrawInfo) ProtoMessage()    {}
func (*LuckDrawInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{14}
}

func (m *LuckDrawInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckDrawInfo.Unmarshal(m, b)
}
func (m *LuckDrawInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckDrawInfo.Marshal(b, m, deterministic)
}
func (m *LuckDrawInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckDrawInfo.Merge(m, src)
}
func (m *LuckDrawInfo) XXX_Size() int {
	return xxx_messageInfo_LuckDrawInfo.Size(m)
}
func (m *LuckDrawInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckDrawInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LuckDrawInfo proto.InternalMessageInfo

func (m *LuckDrawInfo) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *LuckDrawInfo) GetNumberType() int32 {
	if m != nil {
		return m.NumberType
	}
	return 0
}

func (m *LuckDrawInfo) GetShareNumber() int32 {
	if m != nil {
		return m.ShareNumber
	}
	return 0
}

func (m *LuckDrawInfo) GetCanShare() int32 {
	if m != nil {
		return m.CanShare
	}
	return 0
}

func (m *LuckDrawInfo) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *LuckDrawInfo) GetMaxNumber() int32 {
	if m != nil {
		return m.MaxNumber
	}
	return 0
}

func (m *LuckDrawInfo) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *LuckDrawInfo) GetIntegralPay() int32 {
	if m != nil {
		return m.IntegralPay
	}
	return 0
}

func (m *LuckDrawInfo) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

type MiniLuckyDrawPrize struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//九宫格切图
	PrizeImageUrl string `protobuf:"bytes,2,opt,name=prize_image_url,json=prizeImageUrl,proto3" json:"prize_image_url"`
	//奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖
	PrizeType            int32    `protobuf:"varint,3,opt,name=prize_type,json=prizeType,proto3" json:"prize_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiniLuckyDrawPrize) Reset()         { *m = MiniLuckyDrawPrize{} }
func (m *MiniLuckyDrawPrize) String() string { return proto.CompactTextString(m) }
func (*MiniLuckyDrawPrize) ProtoMessage()    {}
func (*MiniLuckyDrawPrize) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{15}
}

func (m *MiniLuckyDrawPrize) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckyDrawPrize.Unmarshal(m, b)
}
func (m *MiniLuckyDrawPrize) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckyDrawPrize.Marshal(b, m, deterministic)
}
func (m *MiniLuckyDrawPrize) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckyDrawPrize.Merge(m, src)
}
func (m *MiniLuckyDrawPrize) XXX_Size() int {
	return xxx_messageInfo_MiniLuckyDrawPrize.Size(m)
}
func (m *MiniLuckyDrawPrize) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckyDrawPrize.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckyDrawPrize proto.InternalMessageInfo

func (m *MiniLuckyDrawPrize) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MiniLuckyDrawPrize) GetPrizeImageUrl() string {
	if m != nil {
		return m.PrizeImageUrl
	}
	return ""
}

func (m *MiniLuckyDrawPrize) GetPrizeType() int32 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

type MiniLuckDrawStartResponse struct {
	Msg                  string         `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *WinningRecord `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MiniLuckDrawStartResponse) Reset()         { *m = MiniLuckDrawStartResponse{} }
func (m *MiniLuckDrawStartResponse) String() string { return proto.CompactTextString(m) }
func (*MiniLuckDrawStartResponse) ProtoMessage()    {}
func (*MiniLuckDrawStartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{16}
}

func (m *MiniLuckDrawStartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckDrawStartResponse.Unmarshal(m, b)
}
func (m *MiniLuckDrawStartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckDrawStartResponse.Marshal(b, m, deterministic)
}
func (m *MiniLuckDrawStartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckDrawStartResponse.Merge(m, src)
}
func (m *MiniLuckDrawStartResponse) XXX_Size() int {
	return xxx_messageInfo_MiniLuckDrawStartResponse.Size(m)
}
func (m *MiniLuckDrawStartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckDrawStartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckDrawStartResponse proto.InternalMessageInfo

func (m *MiniLuckDrawStartResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MiniLuckDrawStartResponse) GetData() *WinningRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type MiniLuckyDrawActivityResponse struct {
	Msg                  string                 `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *MiniLuckyDrawActivity `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MiniLuckyDrawActivityResponse) Reset()         { *m = MiniLuckyDrawActivityResponse{} }
func (m *MiniLuckyDrawActivityResponse) String() string { return proto.CompactTextString(m) }
func (*MiniLuckyDrawActivityResponse) ProtoMessage()    {}
func (*MiniLuckyDrawActivityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{17}
}

func (m *MiniLuckyDrawActivityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckyDrawActivityResponse.Unmarshal(m, b)
}
func (m *MiniLuckyDrawActivityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckyDrawActivityResponse.Marshal(b, m, deterministic)
}
func (m *MiniLuckyDrawActivityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckyDrawActivityResponse.Merge(m, src)
}
func (m *MiniLuckyDrawActivityResponse) XXX_Size() int {
	return xxx_messageInfo_MiniLuckyDrawActivityResponse.Size(m)
}
func (m *MiniLuckyDrawActivityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckyDrawActivityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckyDrawActivityResponse proto.InternalMessageInfo

func (m *MiniLuckyDrawActivityResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MiniLuckyDrawActivityResponse) GetData() *MiniLuckyDrawActivity {
	if m != nil {
		return m.Data
	}
	return nil
}

type MiniLuckyDrawActivity struct {
	ActivityId string `protobuf:"bytes,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	//奖项默认背景色值
	DefaultColor string `protobuf:"bytes,2,opt,name=default_color,json=defaultColor,proto3" json:"default_color"`
	//背景图
	BackgroundImg string `protobuf:"bytes,3,opt,name=background_img,json=backgroundImg,proto3" json:"background_img"`
	//奖品
	Prizes []*MiniLuckyDrawPrize `protobuf:"bytes,4,rep,name=prizes,proto3" json:"prizes"`
	//点击抽奖按钮图像
	LuckyDrawButtonImage string `protobuf:"bytes,5,opt,name=lucky_draw_button_image,json=luckyDrawButtonImage,proto3" json:"lucky_draw_button_image"`
	//点击分享按钮
	ShareButtonImage string `protobuf:"bytes,6,opt,name=share_button_image,json=shareButtonImage,proto3" json:"share_button_image"`
	//活动已结束切图
	ActivityEndImage string `protobuf:"bytes,7,opt,name=activity_end_image,json=activityEndImage,proto3" json:"activity_end_image"`
	//次数已用完切图
	NumberRunOutImage string `protobuf:"bytes,8,opt,name=number_run_out_image,json=numberRunOutImage,proto3" json:"number_run_out_image"`
	//客服微信二维码
	ServiceCodeUrl string `protobuf:"bytes,9,opt,name=service_code_url,json=serviceCodeUrl,proto3" json:"service_code_url"`
	//活动开始时间
	StartTime string `protobuf:"bytes,10,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//活动规则
	ActivityRule string `protobuf:"bytes,12,opt,name=activity_rule,json=activityRule,proto3" json:"activity_rule"`
	//奖项选中背景色值
	SelectColor string `protobuf:"bytes,13,opt,name=select_color,json=selectColor,proto3" json:"select_color"`
	//主键ID
	Id int32 `protobuf:"varint,14,opt,name=id,proto3" json:"id"`
	//分享按钮标题
	ShareButtonTitle string `protobuf:"bytes,15,opt,name=share_button_title,json=shareButtonTitle,proto3" json:"share_button_title"`
	//分享按钮跳转路径
	ShareButtonJump string `protobuf:"bytes,16,opt,name=share_button_jump,json=shareButtonJump,proto3" json:"share_button_jump"`
	//分享出去的图片路径
	ShareImageUrl      string `protobuf:"bytes,17,opt,name=share_image_url,json=shareImageUrl,proto3" json:"share_image_url"`
	LuckyButtonGrayImg string `protobuf:"bytes,18,opt,name=lucky_button_gray_img,json=luckyButtonGrayImg,proto3" json:"lucky_button_gray_img"`
	//是否允许积分抽奖 0否 1是
	Integral int32 `protobuf:"varint,19,opt,name=integral,proto3" json:"integral"`
	//每次消耗多少积分
	IntegralPay          int32    `protobuf:"varint,20,opt,name=integral_pay,json=integralPay,proto3" json:"integral_pay"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiniLuckyDrawActivity) Reset()         { *m = MiniLuckyDrawActivity{} }
func (m *MiniLuckyDrawActivity) String() string { return proto.CompactTextString(m) }
func (*MiniLuckyDrawActivity) ProtoMessage()    {}
func (*MiniLuckyDrawActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{18}
}

func (m *MiniLuckyDrawActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniLuckyDrawActivity.Unmarshal(m, b)
}
func (m *MiniLuckyDrawActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniLuckyDrawActivity.Marshal(b, m, deterministic)
}
func (m *MiniLuckyDrawActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniLuckyDrawActivity.Merge(m, src)
}
func (m *MiniLuckyDrawActivity) XXX_Size() int {
	return xxx_messageInfo_MiniLuckyDrawActivity.Size(m)
}
func (m *MiniLuckyDrawActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniLuckyDrawActivity.DiscardUnknown(m)
}

var xxx_messageInfo_MiniLuckyDrawActivity proto.InternalMessageInfo

func (m *MiniLuckyDrawActivity) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetDefaultColor() string {
	if m != nil {
		return m.DefaultColor
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetPrizes() []*MiniLuckyDrawPrize {
	if m != nil {
		return m.Prizes
	}
	return nil
}

func (m *MiniLuckyDrawActivity) GetLuckyDrawButtonImage() string {
	if m != nil {
		return m.LuckyDrawButtonImage
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetShareButtonImage() string {
	if m != nil {
		return m.ShareButtonImage
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetActivityEndImage() string {
	if m != nil {
		return m.ActivityEndImage
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetNumberRunOutImage() string {
	if m != nil {
		return m.NumberRunOutImage
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetServiceCodeUrl() string {
	if m != nil {
		return m.ServiceCodeUrl
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetActivityRule() string {
	if m != nil {
		return m.ActivityRule
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetSelectColor() string {
	if m != nil {
		return m.SelectColor
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MiniLuckyDrawActivity) GetShareButtonTitle() string {
	if m != nil {
		return m.ShareButtonTitle
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetShareButtonJump() string {
	if m != nil {
		return m.ShareButtonJump
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetShareImageUrl() string {
	if m != nil {
		return m.ShareImageUrl
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetLuckyButtonGrayImg() string {
	if m != nil {
		return m.LuckyButtonGrayImg
	}
	return ""
}

func (m *MiniLuckyDrawActivity) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *MiniLuckyDrawActivity) GetIntegralPay() int32 {
	if m != nil {
		return m.IntegralPay
	}
	return 0
}

type LuckyDrawAddressReq struct {
	//用户ID（前端不用传）
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//中奖记录ID（中奖记录列表返回的ID）
	LuckyDrawRecodeId int32 `protobuf:"varint,2,opt,name=lucky_draw_recode_id,json=luckyDrawRecodeId,proto3" json:"lucky_draw_recode_id"`
	//收件人
	Receiver string `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver"`
	//手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	//地址
	Address              string   `protobuf:"bytes,5,opt,name=address,proto3" json:"address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawAddressReq) Reset()         { *m = LuckyDrawAddressReq{} }
func (m *LuckyDrawAddressReq) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawAddressReq) ProtoMessage()    {}
func (*LuckyDrawAddressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a2884ec33bfe827d, []int{19}
}

func (m *LuckyDrawAddressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawAddressReq.Unmarshal(m, b)
}
func (m *LuckyDrawAddressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawAddressReq.Marshal(b, m, deterministic)
}
func (m *LuckyDrawAddressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawAddressReq.Merge(m, src)
}
func (m *LuckyDrawAddressReq) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawAddressReq.Size(m)
}
func (m *LuckyDrawAddressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawAddressReq.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawAddressReq proto.InternalMessageInfo

func (m *LuckyDrawAddressReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *LuckyDrawAddressReq) GetLuckyDrawRecodeId() int32 {
	if m != nil {
		return m.LuckyDrawRecodeId
	}
	return 0
}

func (m *LuckyDrawAddressReq) GetReceiver() string {
	if m != nil {
		return m.Receiver
	}
	return ""
}

func (m *LuckyDrawAddressReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *LuckyDrawAddressReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func init() {
	proto.RegisterType((*MiniLuckyDrawRequest)(nil), "ac.MiniLuckyDrawRequest")
	proto.RegisterType((*LuckyDrawActivityRequest)(nil), "ac.LuckyDrawActivityRequest")
	proto.RegisterType((*LuckyDrawActivityListRes)(nil), "ac.LuckyDrawActivityListRes")
	proto.RegisterType((*LuckyDrawActivityListResponse)(nil), "ac.LuckyDrawActivityListResponse")
	proto.RegisterType((*LuckyDrawActivityList)(nil), "ac.LuckyDrawActivityList")
	proto.RegisterType((*LuckyDrawActivityInfoResponse)(nil), "ac.LuckyDrawActivityInfoResponse")
	proto.RegisterType((*LuckyDrawActivity)(nil), "ac.LuckyDrawActivity")
	proto.RegisterType((*LuckyDrawPrize)(nil), "ac.LuckyDrawPrize")
	proto.RegisterType((*WinningRecordReq)(nil), "ac.WinningRecordReq")
	proto.RegisterType((*WinningRecordRes)(nil), "ac.WinningRecordRes")
	proto.RegisterType((*WinningRecord)(nil), "ac.WinningRecord")
	proto.RegisterType((*LuckyDrawActivityInfoReq)(nil), "ac.LuckyDrawActivityInfoReq")
	proto.RegisterType((*LuckyDrawActivityInfoRes)(nil), "ac.LuckyDrawActivityInfoRes")
	proto.RegisterType((*MiniLuckDrawInfoResponse)(nil), "ac.MiniLuckDrawInfoResponse")
	proto.RegisterType((*LuckDrawInfo)(nil), "ac.LuckDrawInfo")
	proto.RegisterType((*MiniLuckyDrawPrize)(nil), "ac.MiniLuckyDrawPrize")
	proto.RegisterType((*MiniLuckDrawStartResponse)(nil), "ac.MiniLuckDrawStartResponse")
	proto.RegisterType((*MiniLuckyDrawActivityResponse)(nil), "ac.MiniLuckyDrawActivityResponse")
	proto.RegisterType((*MiniLuckyDrawActivity)(nil), "ac.MiniLuckyDrawActivity")
	proto.RegisterType((*LuckyDrawAddressReq)(nil), "ac.LuckyDrawAddressReq")
}

func init() { proto.RegisterFile("ac/lucky_draw.proto", fileDescriptor_a2884ec33bfe827d) }

var fileDescriptor_a2884ec33bfe827d = []byte{
	// 1318 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xdb, 0x6e, 0xdb, 0x46,
	0x13, 0x86, 0x0e, 0xd4, 0x61, 0x24, 0xd9, 0xd2, 0x5a, 0x4e, 0xe8, 0xfc, 0x08, 0xe2, 0x5f, 0x69,
	0x02, 0xd7, 0x68, 0x1c, 0x34, 0x45, 0x1f, 0xa0, 0x4d, 0x8b, 0x42, 0x41, 0x9a, 0x06, 0x8c, 0x83,
	0x5c, 0xb2, 0x2b, 0x72, 0xa3, 0x6c, 0xcd, 0x83, 0xbc, 0x24, 0x93, 0xc8, 0x2f, 0xd0, 0x9b, 0xde,
	0xb4, 0xd7, 0x7d, 0x80, 0xde, 0xf4, 0x35, 0xfa, 0x3e, 0x7d, 0x83, 0x62, 0x67, 0x97, 0x14, 0x29,
	0x52, 0xb6, 0x04, 0xb4, 0x77, 0xda, 0x99, 0xe1, 0xec, 0xee, 0x9c, 0xbe, 0x6f, 0x05, 0x07, 0xd4,
	0x79, 0xec, 0x25, 0xce, 0xc5, 0xd2, 0x76, 0x05, 0xfd, 0x70, 0xb6, 0x10, 0x61, 0x1c, 0x92, 0x3a,
	0x75, 0x26, 0xef, 0x60, 0xfc, 0x3d, 0x0f, 0xf8, 0x73, 0xa9, 0xfb, 0x46, 0xd0, 0x0f, 0x16, 0xbb,
	0x4c, 0x58, 0x14, 0x93, 0x7b, 0xd0, 0xa3, 0x4e, 0xcc, 0xdf, 0xf3, 0x78, 0x69, 0x73, 0xd7, 0xac,
	0x1d, 0xd7, 0x4e, 0xba, 0x16, 0xa4, 0xa2, 0xa9, 0x4b, 0x6e, 0x43, 0x3b, 0x89, 0x98, 0x90, 0xca,
	0x3a, 0x2a, 0x5b, 0x72, 0x39, 0x75, 0xc9, 0x2d, 0x68, 0xf9, 0xe1, 0x8c, 0x7b, 0xcc, 0x6c, 0x28,
	0xb9, 0x5a, 0x4d, 0x7e, 0xae, 0x81, 0x99, 0x6d, 0xf3, 0x95, 0x76, 0x94, 0x6e, 0xb7, 0x07, 0x75,
	0xbd, 0x4b, 0xc3, 0xaa, 0x73, 0x97, 0xfc, 0x0f, 0xba, 0x0b, 0x3a, 0x67, 0x76, 0xc4, 0xaf, 0x18,
	0xfa, 0x6f, 0x58, 0x1d, 0x29, 0x78, 0xc5, 0xaf, 0x18, 0xb9, 0x0b, 0x80, 0x4a, 0x1e, 0xb8, 0xec,
	0x23, 0xee, 0xd2, 0xb0, 0xd0, 0x7c, 0x2a, 0x05, 0xf2, 0xe8, 0x3c, 0xb2, 0x79, 0x10, 0xb3, 0xb9,
	0xa0, 0x9e, 0xd9, 0x3c, 0xae, 0x9d, 0x18, 0x16, 0xf0, 0x68, 0xaa, 0x25, 0x93, 0xcb, 0x8a, 0x83,
	0x3c, 0xe7, 0x51, 0x6c, 0xb1, 0x88, 0x0c, 0xa1, 0xe1, 0x47, 0x73, 0x7d, 0x5f, 0xf9, 0x93, 0x8c,
	0xc1, 0x88, 0xc3, 0x98, 0x7a, 0x78, 0x0c, 0xc3, 0x52, 0x0b, 0xf2, 0x08, 0x9a, 0x2e, 0x8d, 0xa9,
	0xd9, 0x38, 0x6e, 0x9c, 0xf4, 0x9e, 0x1c, 0x9d, 0x51, 0xe7, 0xac, 0xda, 0x27, 0x9a, 0x4d, 0x5e,
	0xc0, 0xdd, 0x4d, 0x5b, 0x2e, 0xc2, 0x20, 0x62, 0x99, 0xbf, 0xda, 0x76, 0xfe, 0xfe, 0xae, 0xc1,
	0x61, 0xa5, 0xbe, 0x14, 0x49, 0x02, 0xcd, 0x78, 0xb9, 0x60, 0xfa, 0xf4, 0xf8, 0x5b, 0xa6, 0x28,
	0x48, 0xfc, 0x19, 0x13, 0x18, 0x3c, 0xc3, 0xd2, 0x2b, 0x19, 0xd8, 0x28, 0xa6, 0x22, 0xb6, 0x63,
	0xee, 0x33, 0x0c, 0x5c, 0xd7, 0xea, 0xa2, 0xe4, 0x9c, 0xfb, 0x8c, 0x1c, 0x41, 0x87, 0x05, 0xae,
	0x52, 0x1a, 0xa8, 0x6c, 0xb3, 0xc0, 0x45, 0xd5, 0x5a, 0xb9, 0xb4, 0x4a, 0xe5, 0xf2, 0x00, 0xf6,
	0x04, 0x73, 0x42, 0xdf, 0xcf, 0x3c, 0xb4, 0xd1, 0x66, 0x90, 0x49, 0xd1, 0xcf, 0x1d, 0xe8, 0x64,
	0x89, 0xeb, 0xe0, 0xd9, 0xb2, 0xf5, 0xe4, 0x59, 0x45, 0x0c, 0xa7, 0xc1, 0xdb, 0x30, 0x8b, 0xe1,
	0xa7, 0x59, 0x0c, 0x6b, 0x27, 0xbd, 0x27, 0x87, 0x95, 0x31, 0xd4, 0xf1, 0xfb, 0xab, 0x05, 0xa3,
	0x92, 0xae, 0x14, 0xbb, 0xfb, 0x30, 0x70, 0xd9, 0x5b, 0x9a, 0x78, 0xb1, 0xed, 0x84, 0x5e, 0x28,
	0x74, 0xa5, 0xf7, 0xb5, 0xf0, 0xa9, 0x94, 0xc9, 0x9b, 0xcd, 0xa8, 0x73, 0x31, 0x17, 0x61, 0x12,
	0xb8, 0x36, 0xf7, 0xe7, 0xba, 0xee, 0x07, 0x2b, 0xe9, 0xd4, 0x9f, 0x67, 0x79, 0x68, 0x56, 0xe6,
	0xc1, 0x28, 0xe4, 0xe1, 0x08, 0x3a, 0x3c, 0xb2, 0xa3, 0x77, 0x54, 0x30, 0x0c, 0xa5, 0x61, 0xb5,
	0x79, 0xf4, 0x4a, 0x2e, 0xc9, 0x29, 0xb4, 0x16, 0x82, 0x5f, 0xb1, 0xc8, 0x6c, 0x63, 0xa5, 0x90,
	0xc2, 0x2d, 0x5f, 0x4a, 0x95, 0xa5, 0x2d, 0xc8, 0x97, 0x70, 0x7b, 0xd5, 0xf3, 0xf6, 0x2c, 0x89,
	0xe3, 0x30, 0xb0, 0xb9, 0x4f, 0xe7, 0x0c, 0x63, 0xdb, 0xb5, 0xc6, 0x5e, 0xfa, 0xe1, 0xd7, 0xa8,
	0x9c, 0x4a, 0x1d, 0xf9, 0x0c, 0x08, 0x6e, 0x5d, 0xfc, 0xa2, 0x8b, 0x5f, 0x0c, 0x51, 0xb3, 0x66,
	0x9d, 0x65, 0x9e, 0x61, 0x00, 0xa4, 0x35, 0x28, 0xeb, 0x54, 0xf3, 0xad, 0x8c, 0x81, 0xb4, 0x7e,
	0x0c, 0x63, 0x75, 0x47, 0x5b, 0x24, 0x81, 0x1d, 0x26, 0xb1, 0xb6, 0xef, 0xa1, 0xfd, 0x48, 0xe9,
	0xac, 0x24, 0xf8, 0x21, 0x89, 0xd5, 0x07, 0x27, 0x30, 0x8c, 0x98, 0x78, 0xcf, 0x1d, 0x66, 0x3b,
	0xa1, 0xcb, 0xec, 0x44, 0x78, 0x66, 0x1f, 0x8d, 0xf7, 0xb4, 0xfc, 0x69, 0xe8, 0xb2, 0xd7, 0xc2,
	0x5b, 0x2b, 0xde, 0xc1, 0x75, 0xc5, 0xbb, 0x57, 0x2c, 0xde, 0xfb, 0x30, 0xc8, 0xae, 0x20, 0x12,
	0x8f, 0x99, 0xfb, 0x2a, 0xcd, 0xa9, 0xd0, 0x4a, 0x3c, 0x46, 0xfe, 0x0f, 0xfd, 0x88, 0x79, 0xcc,
	0x49, 0x4b, 0x61, 0x88, 0x36, 0x3d, 0x25, 0x53, 0x95, 0xb0, 0x1e, 0xb8, 0x98, 0xc7, 0x1e, 0x33,
	0x47, 0xa5, 0xc0, 0x9d, 0x4b, 0x39, 0x39, 0x85, 0x51, 0xc1, 0xfa, 0xa7, 0xc4, 0x5f, 0x98, 0x04,
	0x8d, 0xf7, 0x73, 0xc6, 0xcf, 0x12, 0x7f, 0x41, 0x1e, 0x82, 0x12, 0xa9, 0x68, 0x61, 0x10, 0x0e,
	0x54, 0x91, 0xa1, 0x18, 0x43, 0x25, 0x63, 0xf0, 0x39, 0x1c, 0xaa, 0x8c, 0x6b, 0x9f, 0x73, 0x41,
	0x97, 0x58, 0x92, 0x63, 0xb4, 0x26, 0xa8, 0x54, 0x7e, 0xbf, 0x13, 0x74, 0x29, 0xeb, 0x32, 0xdf,
	0x71, 0x87, 0xc5, 0x8e, 0x93, 0x77, 0x4e, 0x7f, 0xdb, 0x0b, 0xba, 0x34, 0x6f, 0xa1, 0xbe, 0x97,
	0xca, 0x5e, 0xd2, 0xe5, 0xe4, 0xb7, 0x3a, 0xec, 0x15, 0xcb, 0xaf, 0xd4, 0x45, 0x0f, 0x61, 0x1f,
	0x0b, 0x32, 0x77, 0x78, 0xd5, 0x47, 0x03, 0x14, 0x67, 0x87, 0x97, 0x63, 0x1d, 0xed, 0xb0, 0x4f,
	0xd4, 0x64, 0xea, 0xa2, 0xe4, 0x5c, 0x36, 0xcb, 0x29, 0x8c, 0x3e, 0xf0, 0x20, 0xe0, 0xc1, 0x3c,
	0xe7, 0x48, 0xcd, 0xa8, 0x7d, 0xad, 0xc8, 0x5c, 0xdd, 0x07, 0xe5, 0x3b, 0xb2, 0x0b, 0xfd, 0xd5,
	0x57, 0xc2, 0x17, 0xaa, 0xcb, 0x8e, 0xa1, 0xb7, 0x10, 0xe1, 0x8c, 0xce, 0xb8, 0xc7, 0xe3, 0xa5,
	0x6e, 0xb4, 0xbc, 0x48, 0xa2, 0x90, 0x13, 0x26, 0x0b, 0xd9, 0x03, 0x2e, 0xce, 0x2b, 0xc3, 0xea,
	0x28, 0xc1, 0xd4, 0x95, 0x05, 0xe5, 0xf1, 0xe0, 0x02, 0x8f, 0xa1, 0xda, 0xa9, 0x2d, 0xd7, 0xaf,
	0x85, 0x37, 0xf9, 0xb5, 0x06, 0xc3, 0x37, 0xea, 0x48, 0x16, 0x73, 0x42, 0xe1, 0x5a, 0xec, 0x92,
	0x1c, 0x43, 0x3f, 0x72, 0x84, 0x6f, 0xa7, 0xa8, 0xa9, 0x21, 0x55, 0xca, 0x5e, 0x2b, 0xe4, 0x5c,
	0x1b, 0xa2, 0x6a, 0x62, 0xe7, 0x87, 0x68, 0x01, 0x15, 0x55, 0x80, 0x36, 0xa1, 0x62, 0x53, 0x87,
	0x2f, 0x45, 0xc5, 0x09, 0x2d, 0x1d, 0x69, 0x7b, 0xb0, 0x7b, 0x50, 0x00, 0xbb, 0x91, 0x1c, 0x39,
	0x45, 0x5f, 0x6a, 0xa8, 0xfe, 0x52, 0x87, 0x41, 0x41, 0x5e, 0x2a, 0x85, 0xca, 0x1c, 0xd6, 0xab,
	0x73, 0x78, 0x43, 0x39, 0xac, 0xd4, 0x39, 0xac, 0x52, 0x6a, 0xd9, 0xd3, 0x85, 0xd4, 0x19, 0x6b,
	0xa9, 0x33, 0xa1, 0x4d, 0x5d, 0x57, 0xb0, 0x28, 0xd2, 0x48, 0x95, 0x2e, 0xe5, 0x7c, 0xca, 0x8d,
	0x4c, 0x89, 0x4d, 0x2e, 0x5b, 0x25, 0x7f, 0xe4, 0xad, 0x68, 0x92, 0xd4, 0x5c, 0x5f, 0x05, 0xa7,
	0x15, 0x34, 0x43, 0xe1, 0xd5, 0xe5, 0x7a, 0x60, 0x26, 0x6f, 0x36, 0xda, 0x56, 0x65, 0x29, 0x05,
	0xba, 0xfa, 0xcd, 0x40, 0x67, 0x81, 0x99, 0xf2, 0x3b, 0xa9, 0x2d, 0xe0, 0x65, 0xd9, 0xf1, 0x27,
	0x05, 0xc7, 0xc3, 0xd4, 0x71, 0xf6, 0xa5, 0xf2, 0xf9, 0x7b, 0x1d, 0xfa, 0x79, 0x71, 0x0e, 0xc7,
	0x6a, 0x05, 0x1c, 0xbb, 0x07, 0x3d, 0x3d, 0xed, 0x73, 0x14, 0x04, 0x94, 0x08, 0x93, 0x28, 0x87,
	0x2a, 0xce, 0xb5, 0x02, 0x1d, 0xe9, 0xa1, 0x4c, 0x77, 0xa9, 0x4c, 0x24, 0x0d, 0x34, 0x18, 0x36,
	0x75, 0x22, 0x69, 0xa0, 0xd0, 0x70, 0x0c, 0x46, 0x14, 0xd3, 0x98, 0xe9, 0x0c, 0xab, 0x85, 0x2c,
	0x0d, 0x9f, 0x7e, 0x4c, 0x7d, 0xaa, 0xbe, 0xee, 0xfa, 0xf4, 0xa3, 0xf6, 0x98, 0x9f, 0x78, 0xed,
	0x1b, 0x26, 0x5e, 0xa7, 0x34, 0xf1, 0xd6, 0xbb, 0xb4, 0xbb, 0x4e, 0x75, 0x26, 0x17, 0x40, 0x0a,
	0x94, 0xfa, 0xbf, 0x9c, 0x8a, 0x93, 0x73, 0x38, 0xca, 0xe7, 0xf7, 0x95, 0xc4, 0xbb, 0x6b, 0x12,
	0xfc, 0xa0, 0x90, 0xe0, 0x8d, 0x9d, 0xfc, 0x23, 0xdc, 0x2d, 0x5c, 0x61, 0x45, 0xd7, 0x37, 0x7a,
	0x7e, 0x54, 0xf0, 0x8c, 0x04, 0xb6, 0xda, 0x85, 0xda, 0xe1, 0x8f, 0x16, 0x1c, 0x56, 0xea, 0x6f,
	0x7e, 0x79, 0xfc, 0x9b, 0xac, 0xec, 0x2c, 0xa3, 0x53, 0x4d, 0x9c, 0x6d, 0xb7, 0x4a, 0xe7, 0xde,
	0x9a, 0x52, 0x19, 0x3b, 0x53, 0xaa, 0xd6, 0x4e, 0x94, 0xaa, 0xbd, 0x23, 0xa5, 0xea, 0xec, 0x42,
	0xa9, 0xba, 0x5b, 0x50, 0x2a, 0xb8, 0x8e, 0x52, 0xf5, 0x6e, 0xa0, 0x54, 0xfd, 0x2d, 0x28, 0xd5,
	0xa0, 0x4c, 0xa9, 0x54, 0xd7, 0xec, 0x61, 0xd5, 0xcb, 0xae, 0xa9, 0xa6, 0x58, 0xfb, 0xbb, 0x50,
	0xac, 0xe1, 0xd6, 0x14, 0x6b, 0xb4, 0x13, 0xc5, 0x22, 0x5b, 0x51, 0xac, 0x83, 0x1b, 0x06, 0xce,
	0xb8, 0x4c, 0xb1, 0xfe, 0xac, 0xc1, 0xc1, 0xaa, 0x4d, 0x14, 0x50, 0x49, 0x0c, 0xc9, 0xbd, 0xc0,
	0x6b, 0x85, 0x17, 0xf8, 0x26, 0x10, 0xab, 0x6f, 0x02, 0xb1, 0x3b, 0xd0, 0x11, 0xcc, 0x61, 0xfc,
	0xbd, 0x1e, 0xc1, 0x5d, 0x2b, 0x5b, 0xe7, 0x9e, 0xf3, 0xcd, 0xfc, 0x73, 0x3e, 0x8f, 0xa1, 0x46,
	0x01, 0x43, 0x67, 0x2d, 0xfc, 0x77, 0xe1, 0x8b, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0xcb, 0xc0,
	0x8d, 0x64, 0x74, 0x10, 0x00, 0x00,
}

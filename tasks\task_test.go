package tasks

import (
	"context"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

func Test_autoFinishDeliveringAwenOrder(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "autoFinishDeliveringAwenOrder",
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			autoFinishDeliveringAwenOrder()
		})
	}
}

func TestOcTask_ChooseTaskRun(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.TaskRunRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{name: "手动定时任务",
			args: args{
				ctx: nil,
				in: &oc.TaskRunRequest{
					Data:  8,
					Param: "2023-07-19",
				},
			}},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &OcTask{}
			got, err := t.Choose<PERSON>(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t1.Errorf("ChooseTaskRun() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ChooseTaskRun() got = %v, want %v", got, tt.want)
			}
		})
	}
}

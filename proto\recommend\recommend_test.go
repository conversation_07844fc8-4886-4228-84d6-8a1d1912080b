package recommend

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"testing"
)

func TestGetRecommendProdsClient(t *testing.T) {
	client := GetRecommendClient()
	defer client.Close()

	if out, err := client.Prods.GetRcmdProds(client.Ctx, &ProdRequest{
		Userid:  "dfca620490544e00a506d0074f023a03",
		Chainid: "RP0231",
		Size:    100,
	}); err != nil {
		t.Error(err)
	} else {
		// fmt.Println(out.Message)
		ParseRecommendProductResult(out.Message)
	}
}
func TestGetRecommendTipsClient(t *testing.T) {
	client := GetRecommendClient()
	defer client.Close()

	if out, err := client.Tips.GetRmdtips(client.Ctx, &TipRequest{
		Userid: "",
		Size:   10,
	}); err != nil {
		t.Error(err)
	} else {
		fmt.Println(out.Message)
	}
}

func TestStr(t *testing.T) {
	str := "userid:00005f1787f242cda108fa93491eb72f;pettips:{0=[28, 53, 54, 55, 67]}"
	str = strings.ReplaceAll(str, " ", "")

	var result RecommendResult
	result.Tips = make(map[string][]int32)
	strs := strings.Split(str, "=")
	for i, v := range strs {
		if i == len(strs)-1 {
			break
		}

		var petid string
		if i == 0 {
			if len(v) > 80 {
				result.UserId = v[7:39]
				petid = v[len(v)-32:]
			} else if len(v) > 40 {
				result.UserId = v[7:39]
				petid = "0"
			}
		} else {
			if !strings.HasSuffix(v, "]}") {
				petid = v[len(v)-32:]
			}
		}

		for _, v2 := range strings.Split(strings.Split(strs[i+1], "]")[0][1:], ",") {
			id, _ := strconv.Atoi(v2)
			result.Tips[petid] = append(result.Tips[petid], int32(id))
		}
	}
	b, _ := json.Marshal(&result)
	fmt.Println(string(b))
}

func TestPage(t *testing.T) {
	arr := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
	for i := 1; i < 10; i++ {
		fmt.Println(i, PageInt32ArrayReverse(arr, i, 5))
	}
}

func TestParseRecommendProductResult(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want *RecommendResult
	}{
		{
			name: "",
			args: args{
				str: "userid:;petprods:{0={bbc=[105303,  108389,  113016,  107292,  1019533001,  105297,  114153,  107311,  111968,  106047054,  106610,  104778,  113160,  104781,  111035,  107463,  113373,  113143,  105215083,  109615,  105215182,  107318,  106147,  106778,  104963,  113146,  113139,  112207,  111397,  113154,  107584,  111971,  108410,  107989,  106047055,  111693,  1000001001,  109601,  108395,  109157,  108412,  107033,  105713,  1019714001,  108749,  105215042,  108397,  109259,  105215198,  106047003,  107920,  105708,  113190,  100386,  112635,  112399,  104525,  111991,  1019152001,  112473,  112608,  114157,  104773,  109202,  1008364001,  1019120001,  1019713001,  1001118001,  105215203,  1000850001,  104675,  105215184,  112474,  107575,  106734,  104984,  1000082002,  106047056,  1016816001,  104965,  112046,  108750,  105215084,  112402,  1018311001,  105061,  1008376001,  113194,  108997,  1016910001,  110021,  1000249001,  101461,  107784,  104772,  114179,  100562,  109548,  104780,  105215202], st ore=[100034, 100192, 100167, 100036, 100193, 100165, 100188, 100035, 100191, 100170, 100037, 100166, 100227, 100138, 100266, 100284, 100292, 100158, 100290, 100245, 100282, 100247, 100151, 100301, 100302, 100325, 100324, 100285, 100263, 100338, 100379, 100381, 100378, 100386, 100404, 100306, 100399, 100286, 100380, 100405, 100430, 100497, 100406, 100514, 100499, 100377, 100508, 100560, 100407, 100516, 100697, 100696, 100571, 100519, 100577, 100536, 100695, 100714, 100698, 101253, 100540, 101326, 101325, 101296, 101328, 101256, 101261, 101330, 100578, 101260, 101720, 101437, 101329, 101303, 101866, 101721, 101723, 101462, 101724, 101293, 102160, 101432, 101796, 102488, 102306, 102491, 101798, 102582, 102495, 101995, 101461, 102820, 101514, 102494, 102580, 103136, 103126, 102485, 102480, 103138]}}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ParseRecommendProductResult(tt.args.str); got != nil {
				b, _ := json.Marshal(&got)
				fmt.Println(string(b))
			}
		})
	}
}

func TestParseRecommendTipsResult(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want *RecommendResult
	}{
		{
			name: "",
			args: args{
				str: "userid:;pettips:{0=[32,40,22,2,1,182,4,18,33,48]}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ParseRecommendTipsResult(tt.args.str); got != nil {
				b, _ := json.Marshal(&got)
				t.Log(string(b))
			}
		})
	}
}

func TestJson(t *testing.T) {
	var dataType, petID, financeCode string

	dataType = "store"
	petID = "c40f4927ed2141e9885ff44f125ec68a"
	petID = "ce438890a6714dfa98fb2ab4e38191fa"
	// userID = "0010fdd3d23f4662bca6c44df9ccc069"
	financeCode = "AN0033"

	var result *RecommendResult
	err := json.Unmarshal([]byte(`{"UserId":"0010fdd3d23f4662bca6c44df9ccc069","Bbc":{"c40f4927ed2141e9885ff44f125ec68a":[106512,1037645001,109486,111721,1000140001,109478,105296,100519,1000127001,100528,102155,105285,1000099006,1000099003,105080,105512005,105134,1000103002,108855,100525,103557,105338,103661,1000099005,105804,105862,105604,106307,105551,112156,111837,109711,106932,106868,105453,111795,108874,108873,109715,107455,108955,111869,106406,1000340001,1001185001,112041,111798,105545,1000340002,111651,105615,1008155001,102939002,105444,110660,1008966001,105867,1000134001,105544,1001159001,105658,111797,111901,106923,1018339001,1013466001,1018504001,1013469001,1008156001,1019228001,1000251001,1018367001,1005790001,1011681001,107097,102697003,1019093001,1037063001,1008143001,1018896001,1000140002,1018365001,1004642001,100139004,1037276001,1018368001,1008836001,1000099007,1000099002,1000099004,1019091001,1001154001,105455,105456,105454,1011679001,105512002,105512003,108858,1018366001,1000318003,1000318001,1013467001,1019178001,106468,111763,105672,111748,105798,111765,105133,107276,109551,1018464001,111764,111767,100536,100139003,106467,105684,111769,105324,105850,112040,105853,105259,105489,104547,105055,105491,105183,105657,105337,105199,100514,105198,105280,105200,105275,1000127002,1000127003,1019533001,104778,113143,109615,104963,108410,108395,105215198,100386,112635,112399,1000903001,1018330001,105759,114163,107998,105263,106818,109378,107416,101328,108750,106039,101326,107172,101866,100247,107986,1000915001,114168,111780,108746,108982,109057,105003,1016826001,105322,101796,106661,1014841001,1016823001,100158,114153,114157,111935,105273,105645,112204,106448,105334,105202,102580,105204,105294,105201,105191,105866,1016850001,106485],"ce438890a6714dfa98fb2ab4e38191fa":[1006277001,103358,106515,109486,111721,1000140001,109478,105296,100519,1000127001,100528,102155,105285,1000099006,1000099003,105080,105512005,105134,1000103002,108855,100525,103557,105338,103661,1000099005,105804,105862,105604,106307,105551,112156,111837,109711,106932,106868,105453,111795,108874,108873,109715,107455,108955,111869,106406,1000340001,1001185001,112041,111798,105545,1000340002,111651,105615,1008155001,102939002,105444,110660,1008966001,105867,1000134001,105544,1001159001,105658,111797,111901,106923,1018339001,1013466001,1018504001,1013469001,1008156001,1019228001,1000251001,1018367001,1005790001,1011681001,107097,102697003,1019093001,1037063001,1008143001,1018896001,1000140002,1018365001,1004642001,100139004,1037276001,1018368001,1008836001,1000099007,1000099002,1000099004,1019091001,1001154001,105455,105456,105454,1011679001,105512002,105512003,108858,1018366001,1000318003,1000318001,1013467001,1019178001,106468,111763,105672,111748,105798,111765,105133,107276,109551,1018464001,111764,111767,100536,100139003,106467,105684,111769,105324,105850,112040,105853,105259,105489,104547,105055,105491,105183,105657,105337,105199,100514,105198,105280,105200,105275,1000127002,1000127003,1019533001,104778,113143,109615,104963,108412,108397,105215198,100386,112635,1000903001,1018330001,105759,114163,105263,109378,108005,101328,108750,112537,106039,101326,108765,107172,101866,100247,107986,1000915001,114168,111780,108746,109057,105003,1016826001,105322,101796,106661,103814,1014841001,1016823001,100158,114153,114157,111935,105273,105645,112204,106448,105334,105202,102580,105204,105294,105201,105191,105866,1016850001,106485]},"Store":{"c40f4927ed2141e9885ff44f125ec68a":null,"ce438890a6714dfa98fb2ab4e38191fa":[1000318002,1000140002,1005938001,105512005,105512002,105198,105199,1000099006,1000099005,1000099007,1000099002,1000099004,1000099003,111769,103661,100139003,1016818001,104547,1000340001,1000340002,103557,105853,105200,105798,102155,109711,1000262001,105280,105133,105259,1000140001,111748,105296,105183,100519,100514,100528,111353,1000762001,103804005,103804004,1000269001,100696,109041,1010977001,1000774001,109376,1012997001,1007348001,103709,1000773001,1001445001,1006768001,1000074004,1000074003,109386,1000074002,1000074001,1001009001,1000921001,100325,1007369001,1006998001,1000775001,1001250001,1003316001,1000797001,1003315001,1010976001,1001273001,1000201002,1000900001,1005982001,1009584001,100247,103516,112046,1000936001,1000284001,1001007001,1000902001,105518003,105518002,105518005,105518004,1000736001,1000770001,1001006001,1000771001,1000261001,1001111001,105737,1000199002,103255,1010978001,106653,105322,1000784001,1016920001,102638003,102638002,1000199001,1003528001,106500,109900,106995,1001005001,106504,105655,106503,1000772001,106748,105236,1000779001,105242,105243,105240,1010169001,105005,108750,105244,105245,1000263002,1000263001,105239,106783,106665,105332,1000938001,105206,105207,1000309001,1000068001,1000285003,1016814001,1000781001,111702,1000082003,105132,105015,105014,1000311001,1000780001,1016929001,105006,1000905001,105263,1000769001,1001118007,1001118006,1001118005,1001118004,1001118002,1000240001,1001118001,1000240002,1008392001,105197,1013157001,109553,105195,1001162001,1000788001,107262,1000105001,1000323002,1000787001,1013156001,1000765001,106392,1010975001,1000776001,1000766001,1000265001,1001275001,1000150001,1013158001,1000778001,1001161001,108004,100158,105334,105195004,105294,1000170001,105191,105190,105008,105208,1000738001,105201,105203,108701,102582,103638,103639,112204,105292,1000274001,109710,0]},"Tips":null}`), &result)
	if err != nil {
		fmt.Println(err.Error())
	}

	if dataType == "store" && result != nil && result.Store != nil {
		if v, has := result.Store[petID]; has {
			//判断是否有没有进行SKUID的有效过滤
			if v[len(v)-1] == 0 {
				result.Store[petID] = getHasStockSkuIDs(financeCode, v)

			}
			fmt.Println(result.Store[petID])
		}
	} else if dataType == "bbc" && result != nil && result.Bbc != nil {
		if v, has := result.Bbc[petID]; has {
			fmt.Println(v)
		}
	}
	fmt.Println("nil")
}

func TestGetRecommendPorduct(t *testing.T) {
	type args struct {
		userID      string
		financeCode string
		size        int32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "推荐商品",
			args: args{
				userID:      "********************************",
				financeCode: "RP0228",
				size:        200,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetRecommendPorduct(tt.args.userID, tt.args.financeCode, tt.args.size); got != tt.want {
				t.Errorf("GetRecommendPorduct() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPageInt32Array(t *testing.T) {
	arr := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
	type args struct {
		arr       []int32
		pageIndex int
		pageSize  int
	}
	tests := []struct {
		name string
		args args
		want []int32
	}{
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 4,
				pageSize:  5,
			},
		},
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 5,
				pageSize:  5,
			},
		},
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 6,
				pageSize:  5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := PageInt32Array(tt.args.arr, tt.args.pageIndex, tt.args.pageSize); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PageInt32Array() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPageInt32ArrayReverse(t *testing.T) {
	arr := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
	type args struct {
		arr       []int32
		pageIndex int
		pageSize  int
	}
	tests := []struct {
		name string
		args args
		want []int32
	}{
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 4,
				pageSize:  5,
			},
		},
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 5,
				pageSize:  5,
			},
		},
		{
			name: "",
			args: args{
				arr:       arr,
				pageIndex: 6,
				pageSize:  5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := PageInt32ArrayReverse(tt.args.arr, tt.args.pageIndex, tt.args.pageSize); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PageInt32ArrayReverse() = %v, want %v", got, tt.want)
			}
		})
	}
}
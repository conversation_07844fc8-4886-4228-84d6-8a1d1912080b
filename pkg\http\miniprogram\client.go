package miniprogram

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"io/ioutil"
	"net/http"
	"order-center/pkg/gredis"
	"order-center/utils"
	"strings"
	"time"
)

// Clients and Transports are safe for concurrent use by multiple goroutines and for efficiency should only be created once and re-used.
var httpClient *http.Client

// AWen 针对阿闻所有的请求token从北京获取
// 对于请求库，如果AppId或AppSecret,则从北京获取
var AWen = &MiniClient{}

// BaseUrl 基础url
const BaseUrl = "https://api.weixin.qq.com/"

const (
	ReqUseToken          = 0 // 需要token，从缓存获取
	ReqRefreshToken      = 1 // 需要token，刷新获取
	ReqForceRefreshToken = 2 // 需要token，针对北京强制刷新获取
	ReqNoNeedToken       = 9 // 不需要token，如获取token场景
)

// MiniClient 小程序请求客户端
type MiniClient struct {
	AppId     string
	AppSecret string
}

// MiniRequest 请求结构体
type MiniRequest struct {
	Data      map[string]interface{} // 请求数据结构
	Headers   map[string]string      // 请求头结构体
	TokenRule int                    // 请求使用token规则
}

// MiniResponse 请求响应体
type MiniResponse struct {
	Data        map[string]interface{} // json响应体
	ContentType string                 // 响应类型
	Bytes       []byte                 // 原始信息，当非json响应
}

func init() {
	httpClient = &http.Client{
		Timeout: time.Second * 30,
	}
}

func (mc *MiniClient) Post(path string, mr MiniRequest) (*MiniResponse, error) {
	return mc.Request("POST", path, mr)
}

func (mc *MiniClient) Get(path string, mr MiniRequest) (*MiniResponse, error) {
	return mc.Request("GET", path, mr)
}

// Request 通用请求处理
func (mc *MiniClient) Request(method string, path string, mr MiniRequest) (miniResp *MiniResponse, err error) {
	body, _ := json.Marshal(mr.Data)
	req, err := http.NewRequest(method, getFullUrl(path), bytes.NewBuffer(body))
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/json")
	// 自定义请求头设置
	for k, v := range mr.Headers {
		req.Header.Set(k, v)
	}

	// 如果因为accessToken错误，最多重试3次
	// 第一次本地token过期，第二次远程token过期，第三次强制重刷，仅北京接口可能走第3次
	var maxTries, errCode = 3, 0
	for i := 1; i <= maxTries; i++ {
		// 重置code
		errCode = 0
		miniResp, err = mc.pendingRequest(req, mr)
		// 强制刷新只执行一次
		if mr.TokenRule == ReqRefreshToken || mr.TokenRule == ReqForceRefreshToken {
			mr.TokenRule = ReqUseToken
		}
		if err == nil && miniResp.Data["errcode"] != nil {
			// json 反序列化数字默认为 float64
			errCode = cast.ToInt(miniResp.Data["errcode"])
			// 如果是accessToken挂了，刷新重试
			if errCode == 40001 || errCode == 40014 || errCode == 42001 {
				// 最后一次因为token失效重试时强制重刷，针对北京接口，强制重刷
				if i == (maxTries - 1) {
					mr.TokenRule = ReqForceRefreshToken
				} else {
					mr.TokenRule = ReqRefreshToken
				}
				continue
			}
		}
		break
	}

	if errCode != 0 {
		err = errors.New(fmt.Sprintf("%v %s", errCode, cast.ToString(miniResp.Data["errmsg"])))
	}
	// 出错时记录请求日志
	if err != nil {
		var respData string
		if miniResp != nil {
			respData = kit.JsonEncode(miniResp.Data)
		}
		glog.Error(fmt.Sprintf("调用微信接口(%s)出错，返回内容%s:%s，接口参数:%s", path, err.Error(), respData, string(body)))
	}
	return
}

// 请求处理
func (mc *MiniClient) pendingRequest(req *http.Request, mr MiniRequest) (miniResp *MiniResponse, err error) {
	if err = mc.setRequestAccessToken(req, mr.TokenRule); err != nil {
		err = errors.New("获取AccessToken出错 " + err.Error())
		return
	}
	httpResp, err := httpClient.Do(req)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	// 小程序接口不会将 http状态码错误 的具体错误信息放在body中，所有直接返回
	if httpResp.StatusCode >= 400 {
		err = errors.New("请求出错" + httpResp.Status)
		return
	}
	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		err = errors.New("读取响应body出错 " + err.Error())
		return
	}

	miniResp = &MiniResponse{
		ContentType: httpResp.Header.Get("Content-Type"),
	}
	// 如果响应是json
	if strings.Contains(miniResp.ContentType, "application/json") {
		if err = json.Unmarshal(resBody, &miniResp.Data); err != nil {
			err = errors.New("解析body出错 " + err.Error())
			return
		}
	} else {
		miniResp.Bytes = resBody
	}
	return
}

// 获取完整的链接路径
func getFullUrl(path string) string {
	// 是完整的链接不处理
	if strings.HasPrefix(path, "http") {
		return path
	}
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}
	return BaseUrl + path
}

// 设置请求accessToken
func (mc *MiniClient) setRequestAccessToken(req *http.Request, rule int) (err error) {
	var token string
	if rule == ReqUseToken {
		token, err = mc.getAccessToken()
	} else if rule == ReqRefreshToken || rule == ReqForceRefreshToken {
		token, err = mc.refreshAccessToken()
	} else {
		return // 不需要AccessToken的场景
	}
	if err != nil {
		return
	}

	q := req.URL.Query()
	q.Set("access_token", token)
	req.URL.RawQuery = q.Encode()
	return
}

// 获取小程序token缓存key
func (mc *MiniClient) getTokenCacheKey() string {
	return fmt.Sprintf("mini-program:%s:access-token", mc.AppId)
}

// 获取AccessToken
func (mc *MiniClient) getAccessToken() (token string, err error) {
	r := gredis.NewDC()
	defer r.Close()
	token = r.Get(mc.getTokenCacheKey()).Val()
	// 缓存失效, 更新
	if len(token) <= 5 {
		token, err = mc.refreshAccessToken()
	}
	return
}

// 刷新AccessToken
func (mc *MiniClient) refreshAccessToken() (token string, err error) {
	if mc.AppId == "" || mc.AppSecret == "" {
		token, err = utils.NewAuthCenterClient().GetAccessToken()
	} else {
		// 考虑扩展通用性，这里加入了自主获取机制，请注意，阿闻小程序应通过子龙接口获取
		var miniResp *MiniResponse
		path := fmt.Sprintf("cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", mc.AppId, mc.AppSecret)
		if miniResp, err = mc.Get(path, MiniRequest{TokenRule: ReqNoNeedToken}); err != nil {
			return
		}
		token = fmt.Sprintf("%v", miniResp.Data["access_token"])
	}
	if err == nil {
		redis := gredis.NewDC()
		defer redis.Close()
		redis.Set(mc.getTokenCacheKey(), token, 1*time.Hour)
	}
	return
}

package models

import (
	"time"
)

type OrderExpress struct {
	Id           int64     `xorm:"not null pk autoincr INT(11)"`
	OrderSn      string    `xorm:"default '''' comment('订单编号') VARCHAR(50)"`
	ExpressNo    string    `xorm:"default '''' comment('快递单号') VARCHAR(64)"`
	ExpressCode  string    `xorm:"default '''' comment('快递公司代码') VARCHAR(20)"`
	ExpressName  string    `xorm:"default '''' comment('快递公司名称') VARCHAR(50)"`
	DeliveryFrom int32     `xorm:"default 1 comment('发货来源 1:oms 2商家自配') INT(11)"`
	Num          int32     `xorm:"default 0 comment('商品数量') INT(11)"`
	DeliveryTime time.Time `xorm:"default null comment('发货时间') DATETIME"`
	CreateTime   time.Time `xorm:"default 'current_timestamp()' DATETIME"`
}

package models

import "time"

type PrinterManage struct {
	Id          int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	PrinterSn   string    `json:"printer_sn" xorm:"not null comment('打印机编码') VARCHAR(64) 'printer_sn'"`
	StoreId     string    `json:"store_id" xorm:"default '' comment('门店财务编码') VARCHAR(64) 'store_id'"`
	CreateDate  time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date'"`
	UpdateDate  time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date'"`
	PrinterName string    `json:"printer_name" xorm:"not null comment('打印机名称') VARCHAR(64) 'printer_name'"`
	PrinterKey  string    `json:"printer_key" xorm:"not null comment('打印机密钥') VARCHAR(100) 'printer_key'"`
}

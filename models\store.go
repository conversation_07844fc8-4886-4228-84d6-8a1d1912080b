package models

import "time"

// Store 店铺基本信息Model
type Store struct {
	Id          int    `xorm:"not null pk autoincr comment('自增id') INT(10)"`
	Name        string `xorm:"default '''' comment('门店名称') VARCHAR(50)"`
	FinanceCode string `xorm:"default 'NULL' comment('财务编码') VARCHAR(50)"`
	ZilongId    string `xorm:"default '''' comment('子龙门店id，systemid') VARCHAR(50)"`
	Shortname   string `xorm:"default '''' comment('门店简称') VARCHAR(56)"`
	StoreCode   string `xorm:"default '''' comment('店铺编码') VARCHAR(56) 'StoreCode'"`
	PointX      string `xorm:"default 'NULL' comment('定位信息经度') float 'PointX'"`
	PointY      string `xorm:"default 'NULL' comment('定位信息纬度') float 'PointY'"`
	Province    string `xorm:"default 'NULL' comment('省份') VARCHAR(56)"`
	City        string `xorm:"default 'NULL' comment('市') VARCHAR(56)"`
	County      string `xorm:"default 'NULL' comment('县区') VARCHAR(56)"`
	Address     string `xorm:"default 'NULL' comment('店铺地址') VARCHAR(128)"`
	Desc        string `xorm:"default 'NULL' comment('店铺简介') VARCHAR(256)"`
	CustomCode  string `xorm:"default 'NULL' comment('全渠道往来单位，A8') VARCHAR(50)"`
	ElmDelivery int    `xorm:"default 'NULL' comment('饿了么商户配送方式 9：蜂鸟快送，11：星火众包') int(3)"`
	Bigregion   string `xorm:"default 'NULL' comment('大区') VARCHAR(56)"`
	AppChannel  int32  `xorm:"default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	ChainId     int64  `xorm:"default 'NULL' comment('连锁id') VARCHAR(128)" json:"chain_id"`
	OrgId       int32  `xorm:"default 'NULL' comment('主体id') VARCHAR(128)" json:"org_id"`
}

// 店铺主体（appChannel）配置表
type StoreMaster struct {
	AppChannel        int       `xorm:"not null pk autoincr comment('店铺主体（类型）id 0.未选择 1.阿闻自有 2.TP代运营') INT(11)"`
	Name              string    `xorm:"not null comment('店铺类型名称') VARCHAR(56)"`
	ElmAppId          string    `xorm:"not null comment('饿了么appid') VARCHAR(56)"`
	ElmAppSecret      string    `xorm:"not null comment('饿了么appsecret') VARCHAR(56)"`
	MtAppId           string    `xorm:"not null comment('美团appid') VARCHAR(56)"`
	MtAppSecret       string    `xorm:"not null comment('美团appsecret') VARCHAR(56)"`
	JddjAppId         string    `xorm:"not null comment('京东到家 应用的 token') VARCHAR(56)"`
	JddjAppSecret     string    `xorm:"not null comment('京东到家 应用的app_key') VARCHAR(56)"`
	JddjAppMerchantId string    `xorm:"not null comment('京东到家 应用的商家ID，用于京东到家回调token的时候区分应用') VARCHAR(56)"`
	UpdateUserNo      string    `xorm:"not null comment('更新店铺主体信息的用户') VARCHAR(56)"`
	CreateUserNo      string    `xorm:"not null comment('创建店铺主体的用户') VARCHAR(56)"`
	IsDeleted         int       `xorm:"default 0 comment('是否删除 0.未删除 1.已删除') TINYINT(1)"`
	UpdateTime        time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	CreateTime        time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
}

type StoreAndRelation struct {
	FinanceCode    string `json:"finance_code"`
	AppChannel     string `json:"app_channel"`
	ChannelStoreId string `json:"channel_store_id"`
}

package models

import (
	"time"
)

type OrderOriginData struct {
	Id         int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	ChannelId  int32     `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家') INT(11)"`
	OldOrderSn string    `xorm:"not null default '''' comment('渠道订单号') VARCHAR(50)"`
	BodyJson   string    `xorm:"not null default '''' comment('原始内容') TEXT"`
	CreateTime time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
}

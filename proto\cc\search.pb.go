// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/search.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//搜索历史数据——创建——Request
type CreateSearchHistoryRequest struct {
	//用户id
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//关键字
	KeyWord              string   `protobuf:"bytes,2,opt,name=key_word,json=keyWord,proto3" json:"key_word"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSearchHistoryRequest) Reset()         { *m = CreateSearchHistoryRequest{} }
func (m *CreateSearchHistoryRequest) String() string { return proto.CompactTextString(m) }
func (*CreateSearchHistoryRequest) ProtoMessage()    {}
func (*CreateSearchHistoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a139c76743598f9e, []int{0}
}

func (m *CreateSearchHistoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSearchHistoryRequest.Unmarshal(m, b)
}
func (m *CreateSearchHistoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSearchHistoryRequest.Marshal(b, m, deterministic)
}
func (m *CreateSearchHistoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSearchHistoryRequest.Merge(m, src)
}
func (m *CreateSearchHistoryRequest) XXX_Size() int {
	return xxx_messageInfo_CreateSearchHistoryRequest.Size(m)
}
func (m *CreateSearchHistoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSearchHistoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSearchHistoryRequest proto.InternalMessageInfo

func (m *CreateSearchHistoryRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreateSearchHistoryRequest) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

//搜索历史数据——创建——Response
type CreateSearchHistoryResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSearchHistoryResponse) Reset()         { *m = CreateSearchHistoryResponse{} }
func (m *CreateSearchHistoryResponse) String() string { return proto.CompactTextString(m) }
func (*CreateSearchHistoryResponse) ProtoMessage()    {}
func (*CreateSearchHistoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a139c76743598f9e, []int{1}
}

func (m *CreateSearchHistoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSearchHistoryResponse.Unmarshal(m, b)
}
func (m *CreateSearchHistoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSearchHistoryResponse.Marshal(b, m, deterministic)
}
func (m *CreateSearchHistoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSearchHistoryResponse.Merge(m, src)
}
func (m *CreateSearchHistoryResponse) XXX_Size() int {
	return xxx_messageInfo_CreateSearchHistoryResponse.Size(m)
}
func (m *CreateSearchHistoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSearchHistoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSearchHistoryResponse proto.InternalMessageInfo

func (m *CreateSearchHistoryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreateSearchHistoryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreateSearchHistoryResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*CreateSearchHistoryRequest)(nil), "cc.CreateSearchHistoryRequest")
	proto.RegisterType((*CreateSearchHistoryResponse)(nil), "cc.CreateSearchHistoryResponse")
}

func init() { proto.RegisterFile("cc/search.proto", fileDescriptor_a139c76743598f9e) }

var fileDescriptor_a139c76743598f9e = []byte{
	// 208 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x90, 0xcf, 0x4a, 0xc4, 0x40,
	0x0c, 0x87, 0xd9, 0xd5, 0xdd, 0x6a, 0x40, 0x84, 0x28, 0x38, 0xae, 0xa0, 0xb2, 0x27, 0x4f, 0x15,
	0xf4, 0x11, 0xbc, 0xe8, 0x4d, 0x5a, 0xd0, 0x63, 0x19, 0x33, 0xa1, 0x96, 0xa2, 0xa9, 0xc9, 0x54,
	0xe9, 0xdb, 0x8b, 0x53, 0xf7, 0xd6, 0xde, 0xf2, 0xcb, 0x07, 0x5f, 0xfe, 0xc0, 0x31, 0xd1, 0xad,
	0xb1, 0x57, 0x7a, 0xcf, 0x3b, 0x95, 0x28, 0xb8, 0x24, 0xda, 0x3e, 0xc3, 0xe6, 0x41, 0xd9, 0x47,
	0x2e, 0x13, 0x79, 0x6c, 0x2c, 0x8a, 0x0e, 0x05, 0x7f, 0xf5, 0x6c, 0x11, 0xcf, 0x20, 0xeb, 0x8d,
	0xb5, 0x6a, 0x82, 0x5b, 0x5c, 0x2f, 0x6e, 0x0e, 0x8b, 0xf5, 0x5f, 0x7c, 0x0a, 0x78, 0x0e, 0x07,
	0x2d, 0x0f, 0xd5, 0x8f, 0x68, 0x70, 0xcb, 0x44, 0xb2, 0x96, 0x87, 0x57, 0xd1, 0xb0, 0xf5, 0x70,
	0x31, 0x69, 0xb4, 0x4e, 0x3e, 0x8d, 0x11, 0x61, 0x9f, 0x24, 0x70, 0xf2, 0xad, 0x8a, 0x54, 0xa3,
	0x83, 0xec, 0x83, 0xcd, 0x7c, 0xcd, 0x3b, 0xd9, 0x7f, 0xc4, 0x53, 0x58, 0xb1, 0xaa, 0xa8, 0xdb,
	0x4b, 0xfd, 0x31, 0xdc, 0xd5, 0x70, 0x34, 0xca, 0x4b, 0xd6, 0xef, 0x86, 0x18, 0x5f, 0xe0, 0x64,
	0x62, 0x26, 0x5e, 0xe6, 0x44, 0xf9, 0xfc, 0x79, 0x9b, 0xab, 0x59, 0x3e, 0x2e, 0xfb, 0xb6, 0x4e,
	0x8f, 0xba, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x76, 0xdd, 0x4e, 0x19, 0x3b, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SearchServiceClient is the client API for SearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SearchServiceClient interface {
	//搜索历史数据——创建
	CreateSearchHistory(ctx context.Context, in *CreateSearchHistoryRequest, opts ...grpc.CallOption) (*CreateSearchHistoryResponse, error)
}

type searchServiceClient struct {
	cc *grpc.ClientConn
}

func NewSearchServiceClient(cc *grpc.ClientConn) SearchServiceClient {
	return &searchServiceClient{cc}
}

func (c *searchServiceClient) CreateSearchHistory(ctx context.Context, in *CreateSearchHistoryRequest, opts ...grpc.CallOption) (*CreateSearchHistoryResponse, error) {
	out := new(CreateSearchHistoryResponse)
	err := c.cc.Invoke(ctx, "/cc.SearchService/CreateSearchHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SearchServiceServer is the server API for SearchService service.
type SearchServiceServer interface {
	//搜索历史数据——创建
	CreateSearchHistory(context.Context, *CreateSearchHistoryRequest) (*CreateSearchHistoryResponse, error)
}

// UnimplementedSearchServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSearchServiceServer struct {
}

func (*UnimplementedSearchServiceServer) CreateSearchHistory(ctx context.Context, req *CreateSearchHistoryRequest) (*CreateSearchHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSearchHistory not implemented")
}

func RegisterSearchServiceServer(s *grpc.Server, srv SearchServiceServer) {
	s.RegisterService(&_SearchService_serviceDesc, srv)
}

func _SearchService_CreateSearchHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSearchHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).CreateSearchHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.SearchService/CreateSearchHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).CreateSearchHistory(ctx, req.(*CreateSearchHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SearchService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.SearchService",
	HandlerType: (*SearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSearchHistory",
			Handler:    _SearchService_CreateSearchHistory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/search.proto",
}

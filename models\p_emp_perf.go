package models

import "time"

type PEmpPerf struct {
	Id             int64     `json:"id" xorm:"pk not null comment('唯一数据ID') BIGINT 'id'"`
	TenantId       int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	EmployeeId     int64     `json:"employee_id" xorm:"not null default 0 comment('员工ID') BIGINT 'employee_id'"`
	EmployeeNo     string    `json:"employee_no" xorm:"not null comment('员工编码') VARCHAR(20) 'employee_no'"`
	EmployeeMobile string    `json:"employee_mobile" xorm:"not null comment('员工手机号') VARCHAR(11) 'employee_mobile'"`
	EmployeeName   string    `json:"employee_name" xorm:"default '' comment('员工姓名') VARCHAR(50) 'employee_name'"`
	RoleId         int64     `json:"role_id" xorm:"not null default 0 comment('员工角色ID') BIGINT 'role_id'"`
	OrderTime      time.Time `json:"order_time" xorm:"not null default '0000-01-01 00:00:00' comment('订单时间') DATETIME 'order_time'"`
	CustomerId     int64     `json:"customer_id" xorm:"not null default 0 comment('客户ID') BIGINT 'customer_id'"`
	CustomerName   string    `json:"customer_name" xorm:"default '' comment('客户姓名') VARCHAR(50) 'customer_name'"`
	CustomerMobile string    `json:"customer_mobile" xorm:"not null default '' comment('客户手机号') VARCHAR(11) 'customer_mobile'"`
	OrderId        int64     `json:"order_id" xorm:"not null default 0 comment('订单ID') BIGINT 'order_id'"`
	OrderNo        string    `json:"order_no" xorm:"not null default '' comment('订单编号') VARCHAR(32) 'order_no'"`
	OrderDetailId  int64     `json:"order_detail_id" xorm:"not null default 0 comment('订单详情ID') BIGINT 'order_detail_id'"`
	RefundType     string    `json:"refund_type" xorm:"not null default '' comment('退款类型: NONE:非退款, MERCHANT_REFUND:商家退款, REFUND: 退款') VARCHAR(16) 'refund_type'"`
	RefundId       int64     `json:"refund_id" xorm:"not null default 0 comment('退款关联ID: 商家退款ID或者退款单ID') BIGINT 'refund_id'"`
	RefundDetailId int64     `json:"refund_detail_id" xorm:"not null default 0 comment('退款关联ID: 商家退款详情ID或者退款单详情ID') BIGINT 'refund_detail_id'"`
	ProductId      int64     `json:"product_id" xorm:"not null default 0 comment('商品ID') BIGINT 'product_id'"`
	ProductType    string    `json:"product_type" xorm:"not null default '' comment('商品类型：GOODS:实物, SER:服务,LIVE:活体,ROOM房间,CARD卡') VARCHAR(16) 'product_type'"`
	SnapshotId     int64     `json:"snapshot_id" xorm:"not null default 0 comment('商品快照ID') BIGINT 'snapshot_id'"`
	SkuId          int64     `json:"sku_id" xorm:"not null default 0 comment('商品SKU ID') BIGINT 'sku_id'"`
	ProductName    string    `json:"product_name" xorm:"not null default '' comment('商品名称') VARCHAR(255) 'product_name'"`
	DiscountPrice  float64   `json:"discount_price" xorm:"not null default '0.0000' comment('折扣价') DECIMAL(18) 'discount_price'"`
	BuyCount       int       `json:"buy_count" xorm:"not null default 0 comment('明细购买总数') INT 'buy_count'"`
	SubtotalAmount float64   `json:"subtotal_amount" xorm:"not null comment('小计') DECIMAL(18) 'subtotal_amount'"`
	PaymentAmount  float64   `json:"payment_amount" xorm:"not null default '0.0000' comment('实付金额') DECIMAL(18) 'payment_amount'"`
	CommisionRate  float64   `json:"commision_rate" xorm:"not null default '0.0000' comment('提成率') DECIMAL(18) 'commision_rate'"`
	CommisionType  string    `json:"commision_type" xorm:"not null default '' comment('提成类型: SELL_PRICE售价, PAY_PRICE实收金额') VARCHAR(16) 'commision_type'"`
	Performance    float64   `json:"performance" xorm:"not null default '0.0000' comment('业绩') DECIMAL(18) 'performance'"`
	Commision      float64   `json:"commision" xorm:"not null default '0.0000' comment('提成') DECIMAL(18) 'commision'"`
	IsDeleted      byte      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}

package tasks

import (
	"context"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/services"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

// 定时任务
func InitTask() {
	if kit.EnvCanCron() {
		glog.Info("task run...")

		var runInterval string
		c := cron.New()

		//每日凌晨自动完成前一日22点前的配送中阿闻订单并同步至子龙，并且配送时间已过，非自提单，已成功推送子龙
		c.AddFunc("0 0 * * *", autoFinishDeliveringAwenOrder)

		//每日凌晨自动完成打标签
		c.AddFunc("0 0 * * *", UpdateIsNewCustomer)

		//售后订单定时任务
		c.AddFunc("@every 5m", refundOrderTask)

		c.AddFunc("@every 5m", refundDeliveryTask)

		c.AddFunc("@every 1m", TaskMonthGrant)
		//核销码到期自动退款
		runInterval = "30m"
		if kit.IsDebug {
			runInterval = "10s"
		}
		c.AddFunc("@every "+runInterval, handleExpireVerifyCode)
		c.AddFunc("0 9 * * *", verifyCodeExpireNotice)

		////到时间取消配送   走延时队列处理
		//runInterval = "20s"
		//if kit.IsDebug {
		//	runInterval = "10s"
		//}
		//
		//c.AddFunc("@every "+runInterval, cancelNoAcceptMTDelivery)
		//
		//c.AddFunc("@every "+runInterval, cancelNoAcceptSSDelivery)

		//社区拼团活动定时任务
		c.AddFunc("@every 10m", groupAactivityDisTask)
		c.AddFunc("@every 15m", groupAactivityCountTask)

		c.Start()

	}

	s := redoTask{}
	s.PushOrderToJyOMSTask()
	s.PushRefundOrderToJyOMSTask()
	s.PushWasteCouponTask()
	s.PushDiscountCardRefundTask()

	//MQ任务
	InitMqTask()

}

func InitMqTask() {
	//消费订单导出任务
	go consumeOrderExportTask()
	//消费订单超时未支付任务
	go consumeOrderNopayCancelTask()
	//消费配送单没接单自动重推
	go consumeDeliveryNoPickCancelTask()
	//订阅支付通知消息（组合商品功能上线后下面任务不再需要）
	//go subPayMQ()
	//电商订单发货推送消息
	go subConfirmDeliveryMq()
	// 订单推送子龙失败重试
	go RePushToZiLongMqTask()
	// 订单推送互联网医疗
	go PushDigitalHealthOrderMqTask()
	//重新推送订单到巨益OMS

}

// 自动完成前一日22点前的配送中阿闻订单并同步至子龙
func autoFinishDeliveringAwenOrder() {
	defer kit.CatchPanic()

	glog.Info(kit.RunFuncName(), " start")

	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:autoFinishDeliveringAwenOrder"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	//连接池勿关闭
	db := services.GetDBConn()

	var model []*models.OrderMain
	//查询前一天22点前配送中的订单,并且配送时间已过,非自提单,已成功推送子龙 -- 去掉订单来源的判断 and source = 3
	//物流配送、商家自配送，接单后10天自动确认收货
	//services.ChannelAwenId
	createTime := time.Now().Add(-2 * time.Hour).Format(kit.DATETIME_LAYOUT)
	expectedTime := time.Now().Add(-2 * time.Hour).Format(kit.DATETIME_LAYOUT)
	deliverTime := time.Now().Add(-10 * 24 * time.Hour).Format(kit.DATETIME_LAYOUT)

	if err := db.SQL(`
		SELECT order_main.order_sn
		FROM order_main INNER JOIN order_detail ON order_main.order_sn = order_detail.order_sn
		WHERE channel_id in (1,9)
		AND parent_order_sn>0
		AND is_virtual=0
		AND push_third_order = 1 
		AND delivery_type != 3 
		AND order_main.parent_order_sn!='' 
		AND order_main.create_time<=? 
		AND ((order_main.order_status_child IN(20103, 20102) AND order_main.delivery_type IN(2,4) AND order_detail.expected_time < ?) 
 OR (order_main.order_status_child IN(20201, 20202, 20204) AND order_main.delivery_type = 1 AND order_detail.accept_time < ?) 
 OR (order_main.order_status_child IN(20102, 20103) AND order_main.delivery_type = 5 AND order_detail.accept_time < ?))
		ORDER BY order_main.id DESC
	`, createTime, expectedTime, deliverTime, deliverTime).
		Find(&model); err != nil {
		glog.Error("查询前一天22点前的配送中订单失败, ", err)
		return
	}

	//没有可更新数据
	if len(model) == 0 {
		return
	}

	orderService := new(services.OrderService)
	ctx := context.Background()
	for _, v := range model {
		if res, err := orderService.AccomplishOrder(ctx, &oc.AccomplishOrderRequest{
			OrderSn:     v.OrderSn,
			ConfirmTime: kit.GetTimeNow(),
		}); err != nil {
			glog.Error(v.OrderSn, "，自动订单完成失败：", err.Error())
		} else if res.Code != 200 {
			glog.Error(v.OrderSn, "，自动订单完成失败：", res.Message)
		}
	}
}

type OcTask struct {
}

func TaskAutoOrderComplete() {
	service := TaskOrderComplete{}
	service.TaskAutoOrderComplete()
}

// 调用定时任务的grpc方法，方便测试使用
func (t *OcTask) ChooseTaskRun(ctx context.Context, in *oc.TaskRunRequest) (*oc.BaseResponse, error) {
	response := &oc.BaseResponse{
		Code:    200,
		Message: "",
		Error:   "",
	}

	mapData := make(map[int32]func(), 0)
	mapData[1] = autoFinishDeliveringAwenOrder                   // 自动完成
	mapData[2] = TaskOrderStatusFix                              //fix漏掉的部分自动完成
	mapData[3] = verifyCodeExpireNotice                          //核销码过期通知
	mapData[4] = vipExpire                                       //会员卡过期
	mapData[5] = vipOrderComplete                                //会员卡订单7天自动完成
	mapData[6] = func() { autoUpdateElmOrderBillInfo(in.Param) } //自动更新饿了么的对账单信息
	mapData[7] = func() { autoUpdateMtOrderBillInfo(in.Param) }  //自动更新美团的对账单信息
	mapData[8] = func() { autoUpdateJdOrderBillInfo(in.Param) }  //自动更新京东的对账单信息
	mapData[9] = TaskAutoOrderComplete

	glog.Info("手动启动定时任务：", in.Data)
	if fun, ok := mapData[in.Data]; ok {
		fun()
	}
	return response, nil
}

package models

import (
	"time"
)

//在线问诊系统配置表
type DiagnoseConfig struct {
	Id int32
	//医生排班时间内不接单开关：0关，1开
	WorkOnOff int32 `json:"work_on_off"`
	//快速图文问诊费用（单位分）
	QuickImageTextPrice int32 `json:"quick_image_text_price"`
	//免费图文问诊时长（单位分钟）
	FreeImageTextDuration int32 `json:"free_image_text_duration"`
	//快速图文问诊时长（单位分钟）
	QuickImageTextDuration int32 `json:"quick_image_text_duration"`
	//找医生图文问诊时长（单位分钟）
	FindImageTextDuration int32 `json:"find_image_text_duration"`
	//找医生电话问诊时长（单位分钟）
	FindPhoneDuration int32 `json:"find_phone_duration"`
	//找医生视频问诊时长（单位分钟）
	FindVideoDuration int32 `json:"find_video_duration"`
	//创建日期
	CreateTime time.Time `json:"create_time"`
	//最后更新时间
	UpdateTime time.Time `json:"update_time"`
}

////医生排班时间内不接单开关：0关，1开
const (
	DiagnoseConfigWorkOff = iota
	DiagnoseConfigWorkOn
)

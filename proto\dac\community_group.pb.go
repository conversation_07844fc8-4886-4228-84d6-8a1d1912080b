// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dac/community_group.proto

package dac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GroupActivityGetRequest struct {
	// 店铺财务编码
	StoreFinanceCode string `protobuf:"bytes,1,opt,name=store_finance_code,json=storeFinanceCode,proto3" json:"store_finance_code"`
	// 状态 0未开始 1进行中 2已结束 3已终止
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityGetRequest) Reset()         { *m = GroupActivityGetRequest{} }
func (m *GroupActivityGetRequest) String() string { return proto.CompactTextString(m) }
func (*GroupActivityGetRequest) ProtoMessage()    {}
func (*GroupActivityGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_60531de67fa03d33, []int{0}
}

func (m *GroupActivityGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityGetRequest.Unmarshal(m, b)
}
func (m *GroupActivityGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityGetRequest.Marshal(b, m, deterministic)
}
func (m *GroupActivityGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityGetRequest.Merge(m, src)
}
func (m *GroupActivityGetRequest) XXX_Size() int {
	return xxx_messageInfo_GroupActivityGetRequest.Size(m)
}
func (m *GroupActivityGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityGetRequest proto.InternalMessageInfo

func (m *GroupActivityGetRequest) GetStoreFinanceCode() string {
	if m != nil {
		return m.StoreFinanceCode
	}
	return ""
}

func (m *GroupActivityGetRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GroupActivityGetResponse struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 创建人
	UserNo string `protobuf:"bytes,2,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 店铺财务编码
	StoreFinanceCode string `protobuf:"bytes,3,opt,name=store_finance_code,json=storeFinanceCode,proto3" json:"store_finance_code"`
	// 状态 0未开始 1进行中 2已结束 3已终止
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	// 开始时间
	StartAt string `protobuf:"bytes,5,opt,name=start_at,json=startAt,proto3" json:"start_at"`
	// 结束时间
	EndAt string `protobuf:"bytes,6,opt,name=end_at,json=endAt,proto3" json:"end_at"`
	// 待收方式 0团长决定是否代收 1团长必须代收
	TakeType int32 `protobuf:"varint,7,opt,name=take_type,json=takeType,proto3" json:"take_type"`
	// 是否包邮 0否 1是
	IsFreeFee int32 `protobuf:"varint,8,opt,name=is_free_fee,json=isFreeFee,proto3" json:"is_free_fee"`
	// 前台显示 0否 1是
	IsShow int32 `protobuf:"varint,9,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	// 成团金额(单位:分)
	MinAmount int64 `protobuf:"varint,10,opt,name=min_amount,json=minAmount,proto3" json:"min_amount"`
	// 开团后N小时内必须成团
	MustHours int32 `protobuf:"varint,11,opt,name=must_hours,json=mustHours,proto3" json:"must_hours"`
	// 最晚N日内送达
	DeliverDays          int32    `protobuf:"varint,12,opt,name=deliver_days,json=deliverDays,proto3" json:"deliver_days"`
	CreatedAt            string   `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt            string   `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityGetResponse) Reset()         { *m = GroupActivityGetResponse{} }
func (m *GroupActivityGetResponse) String() string { return proto.CompactTextString(m) }
func (*GroupActivityGetResponse) ProtoMessage()    {}
func (*GroupActivityGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_60531de67fa03d33, []int{1}
}

func (m *GroupActivityGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityGetResponse.Unmarshal(m, b)
}
func (m *GroupActivityGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityGetResponse.Marshal(b, m, deterministic)
}
func (m *GroupActivityGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityGetResponse.Merge(m, src)
}
func (m *GroupActivityGetResponse) XXX_Size() int {
	return xxx_messageInfo_GroupActivityGetResponse.Size(m)
}
func (m *GroupActivityGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityGetResponse proto.InternalMessageInfo

func (m *GroupActivityGetResponse) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivityGetResponse) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *GroupActivityGetResponse) GetStoreFinanceCode() string {
	if m != nil {
		return m.StoreFinanceCode
	}
	return ""
}

func (m *GroupActivityGetResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivityGetResponse) GetStartAt() string {
	if m != nil {
		return m.StartAt
	}
	return ""
}

func (m *GroupActivityGetResponse) GetEndAt() string {
	if m != nil {
		return m.EndAt
	}
	return ""
}

func (m *GroupActivityGetResponse) GetTakeType() int32 {
	if m != nil {
		return m.TakeType
	}
	return 0
}

func (m *GroupActivityGetResponse) GetIsFreeFee() int32 {
	if m != nil {
		return m.IsFreeFee
	}
	return 0
}

func (m *GroupActivityGetResponse) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *GroupActivityGetResponse) GetMinAmount() int64 {
	if m != nil {
		return m.MinAmount
	}
	return 0
}

func (m *GroupActivityGetResponse) GetMustHours() int32 {
	if m != nil {
		return m.MustHours
	}
	return 0
}

func (m *GroupActivityGetResponse) GetDeliverDays() int32 {
	if m != nil {
		return m.DeliverDays
	}
	return 0
}

func (m *GroupActivityGetResponse) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *GroupActivityGetResponse) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func init() {
	proto.RegisterType((*GroupActivityGetRequest)(nil), "dac.GroupActivityGetRequest")
	proto.RegisterType((*GroupActivityGetResponse)(nil), "dac.GroupActivityGetResponse")
}

func init() { proto.RegisterFile("dac/community_group.proto", fileDescriptor_60531de67fa03d33) }

var fileDescriptor_60531de67fa03d33 = []byte{
	// 398 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0x51, 0x6f, 0xd3, 0x30,
	0x10, 0xc7, 0xd5, 0x96, 0xa6, 0x8d, 0x3b, 0xa6, 0xc9, 0xd2, 0x98, 0x07, 0x0c, 0x8d, 0x3d, 0xed,
	0x01, 0x15, 0x09, 0x3e, 0x41, 0x34, 0xd4, 0xf1, 0x04, 0x52, 0xc6, 0xbb, 0x65, 0xec, 0x2b, 0xb5,
	0x20, 0x76, 0xf0, 0x9d, 0x3b, 0xe5, 0x5b, 0xf1, 0x11, 0x51, 0x2e, 0xa9, 0x84, 0x40, 0x15, 0x8f,
	0xf7, 0xfb, 0x25, 0x7f, 0xdf, 0xe9, 0x4e, 0x5c, 0x3a, 0x63, 0xdf, 0xda, 0xd8, 0x34, 0x39, 0x78,
	0xea, 0xf4, 0xb7, 0x14, 0x73, 0xbb, 0x6e, 0x53, 0xa4, 0x28, 0x67, 0xce, 0xd8, 0x1b, 0x2d, 0x2e,
	0xee, 0x7b, 0x56, 0x59, 0xf2, 0x7b, 0x4f, 0xdd, 0x3d, 0x50, 0x0d, 0x3f, 0x33, 0x20, 0xc9, 0x37,
	0x42, 0x22, 0xc5, 0x04, 0x7a, 0xeb, 0x83, 0x09, 0x16, 0xb4, 0x8d, 0x0e, 0xd4, 0xe4, 0x7a, 0x72,
	0x5b, 0xd6, 0x67, 0x6c, 0x36, 0x83, 0xb8, 0x8b, 0x0e, 0xe4, 0x33, 0x51, 0x20, 0x19, 0xca, 0xa8,
	0xa6, 0xd7, 0x93, 0xdb, 0x79, 0x3d, 0x56, 0x37, 0xbf, 0x66, 0x42, 0xfd, 0xfb, 0x02, 0xb6, 0x31,
	0x20, 0xc8, 0x53, 0x31, 0xf5, 0x8e, 0x23, 0x67, 0xf5, 0xd4, 0x3b, 0x79, 0x21, 0x16, 0x19, 0x21,
	0xe9, 0x10, 0x39, 0xa5, 0xac, 0x8b, 0xbe, 0xfc, 0x14, 0x8f, 0xf4, 0x32, 0xfb, 0x6f, 0x2f, 0x4f,
	0xfe, 0xec, 0x45, 0x5e, 0x8a, 0x25, 0x92, 0x49, 0xa4, 0x0d, 0xa9, 0x39, 0xff, 0xbb, 0xe0, 0xba,
	0x22, 0x79, 0x2e, 0x0a, 0x08, 0xae, 0x17, 0x05, 0x8b, 0x39, 0x04, 0x57, 0x91, 0x7c, 0x21, 0x4a,
	0x32, 0xdf, 0x41, 0x53, 0xd7, 0x82, 0x5a, 0x70, 0xd8, 0xb2, 0x07, 0x5f, 0xba, 0x16, 0xe4, 0x2b,
	0xb1, 0xf2, 0xa8, 0xb7, 0x09, 0x40, 0x6f, 0x01, 0xd4, 0x92, 0x75, 0xe9, 0x71, 0x93, 0x00, 0x36,
	0x00, 0xfd, 0x34, 0x1e, 0x35, 0xee, 0xe2, 0xa3, 0x2a, 0x87, 0x3e, 0x3c, 0x3e, 0xec, 0xe2, 0xa3,
	0xbc, 0x12, 0xa2, 0xf1, 0x41, 0x9b, 0x26, 0xe6, 0x40, 0x4a, 0xf0, 0xf8, 0x65, 0xe3, 0x43, 0xc5,
	0x80, 0x75, 0x46, 0xd2, 0xbb, 0x98, 0x13, 0xaa, 0xd5, 0x10, 0xdb, 0x93, 0x8f, 0x3d, 0x90, 0xaf,
	0xc5, 0x89, 0x83, 0x1f, 0x7e, 0x0f, 0x49, 0x3b, 0xd3, 0xa1, 0x3a, 0xe1, 0x0f, 0x56, 0x23, 0xfb,
	0x60, 0x3a, 0xec, 0x13, 0x6c, 0x02, 0x43, 0xc0, 0x13, 0x3d, 0xe5, 0x89, 0xca, 0x91, 0x54, 0xfc,
	0x40, 0x6e, 0xdd, 0x41, 0x9f, 0x0e, 0x7a, 0x24, 0x15, 0xbd, 0xdb, 0x89, 0xf3, 0xbb, 0xc3, 0xc5,
	0xf0, 0xea, 0x1e, 0x20, 0xed, 0xbd, 0x05, 0xf9, 0x59, 0x9c, 0xfd, 0xbd, 0x4a, 0xf9, 0x72, 0xed,
	0x8c, 0x5d, 0x1f, 0xb9, 0xa1, 0xe7, 0x57, 0x47, 0xec, 0xb0, 0xff, 0xaf, 0x05, 0x5f, 0xe2, 0xfb,
	0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe2, 0x31, 0xfb, 0x9e, 0xa6, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CommunityGroupServiceClient is the client API for CommunityGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CommunityGroupServiceClient interface {
	//  社区团购活动设置
	GroupActivityGet(ctx context.Context, in *GroupActivityGetRequest, opts ...grpc.CallOption) (*GroupActivityGetResponse, error)
}

type communityGroupServiceClient struct {
	cc *grpc.ClientConn
}

func NewCommunityGroupServiceClient(cc *grpc.ClientConn) CommunityGroupServiceClient {
	return &communityGroupServiceClient{cc}
}

func (c *communityGroupServiceClient) GroupActivityGet(ctx context.Context, in *GroupActivityGetRequest, opts ...grpc.CallOption) (*GroupActivityGetResponse, error) {
	out := new(GroupActivityGetResponse)
	err := c.cc.Invoke(ctx, "/dac.CommunityGroupService/GroupActivityGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommunityGroupServiceServer is the server API for CommunityGroupService service.
type CommunityGroupServiceServer interface {
	//  社区团购活动设置
	GroupActivityGet(context.Context, *GroupActivityGetRequest) (*GroupActivityGetResponse, error)
}

// UnimplementedCommunityGroupServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCommunityGroupServiceServer struct {
}

func (*UnimplementedCommunityGroupServiceServer) GroupActivityGet(ctx context.Context, req *GroupActivityGetRequest) (*GroupActivityGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupActivityGet not implemented")
}

func RegisterCommunityGroupServiceServer(s *grpc.Server, srv CommunityGroupServiceServer) {
	s.RegisterService(&_CommunityGroupService_serviceDesc, srv)
}

func _CommunityGroupService_GroupActivityGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupActivityGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).GroupActivityGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.CommunityGroupService/GroupActivityGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).GroupActivityGet(ctx, req.(*GroupActivityGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CommunityGroupService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dac.CommunityGroupService",
	HandlerType: (*CommunityGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GroupActivityGet",
			Handler:    _CommunityGroupService_GroupActivityGet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dac/community_group.proto",
}

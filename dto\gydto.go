package dto

//获取库存接口
type SignaureParam struct {
	// 请求管易那边的Appkey
	Appkey string `json:"appkey"`
	// 请求管易那边的Sessionkey
	Sessionkey string `json:"sessionkey"`
	// 请求方式   赋值通常为管易接口链接
	Method string `json:"method"`
	// 加密后的请求Sign   类似于Token
	Sign string `json:"sign,omitempty"`
}

// gy.erp.trade.add
// 订单新增
type Item struct {
	//商品代码    必镇
	Item_code string `json:"item_code,omitempty"`
	// 规格代码
	Sku_code string `json:"sku_code,omitempty"`
	// 标准单价
	Origin_price string `json:"origin_price,omitempty"`
	// 实际单价    必镇
	Price string `json:"price,omitempty"`
	// 商品数量
	Qty int32 `json:"qty,omitempty"`
	// 退款状态
	Refund int32 `json:"refund,omitempty"`
	// 赠品  默认false
	Is_gift bool `json:"is_gift,omitempty"`
	// 备注
	Note string `json:"note,omitempty"`
	// 子订单ID
	Oid string `json:"oid,omitempty"`
	// 预计发货日期
	Plan_delivery_date string `json:"plan_delivery_date,omitempty"` //  string
	// 是否为预售
	Presale bool `json:"presale,omitempty"`
}

// gy.erp.trade.add
// 订单新增
type Payment struct {
	//  支付方式   必镇
	Pay_type_code string `json:"pay_type_code,omitempty"`
	// 支付金额    必镇
	Payment float64 `json:"payment,omitempty"`
	// 支付时间
	Paytime string `json:"paytime,omitempty"` // time
	// 交易号
	Pay_code string `json:"pay_code,omitempty"`
	// 账号
	Account string `json:"account,omitempty"`
}

// gy.erp.trade.add
// 订单新增
type Invoice struct {
	// 发票种类   必镇
	Invoice_type int64 `json:"invoice_type,omitempty"`
	// 发票抬头类型
	Invoice_title_type int `json:"invoice_title_type,omitempty"`
	// 发票类型
	Invoice_type_name int `json:"invoice_type_name,omitempty"`
	// 发票抬头    必镇
	Invoice_title string `json:"invoice_title,omitempty"`
	// 发票内容
	Invoice_content string `json:"invoice_content,omitempty"`
	// 纳税人识别号
	Invoice_tex_payer_number string `json:"invoice_tex_payer_number,omitempty"`
	// 开户行
	Invoice_bank_name string `json:"invoice_bank_name,omitempty"`
	// 账号
	Invoice_bank_account string `json:"invoice_bank_account,omitempty"`
	// 地址
	Invoice_address string `json:"invoice_address,omitempty"`
	// 电话
	Invoice_phone string `json:"invoice_phone,omitempty"`
	// 发票金额
	Invoice_amount float64 `json:"invoice_amount,omitempty"`
}

// gy.erp.trade.add
// 订单新增
type PaymentParam struct {
	// 订单新增  头参
	SignaureParam
	// 店铺代码     必镇
	Shop_code string `json:"shop_code,omitempty"`
	// 会员代码    必镇
	Vip_code string `json:"vip_code,omitempty"`
	// 平台单号
	Platform_code string `json:"platform_code,omitempty"`

	Repeat_allowed bool `json:"repeat_allowed,omitempty"`
	// 仓库代码  未提供自动分配
	Warehouse_code string `json:"warehouse_code,omitempty"`
	// 业务员
	Business_man_code string `json:"business_man_code,omitempty"`
	// 物流公司
	Express_code string `json:"express_code,omitempty"`
	// 物流费用
	Post_fee float64 `json:"post_fee,omitempty"`
	// 币别代码
	Currency_code string `json:"currency_code,omitempty"`
	// 卖家备注
	Seller_memo string `json:"seller_memo,omitempty"`
	//  是否货到付款
	Cod bool `json:"cod,omitempty"`
	// 拍单时间     必镇
	Deal_datetime string `json:"deal_datetime,omitempty"` // time
	// 订单类型     必镇
	Order_type_code string `json:"order_type_code,omitempty"`
	// 预计发货时间
	Plan_delivery_date string `json:"plan_delivery_date,omitempty"`
	// 买家到付服务费
	Cod_fee float64 `json:"cod_fee,omitempty"`
	// 其他服务费
	Other_service_fee float64 `json:"other_service_fee,omitempty"`
	// 买家留言
	Buyer_memo string `json:"buyer_memo,omitempty"`
	// 二次备注
	Seller_memo_late string `json:"seller_memo_late,omitempty"`
	// 附加信息
	Extend_memo string `json:"extend_memo,omitempty"`
	// 是否淘宝家装订单
	Jz bool `json:"jz,omitempty"`
	// 是否手工指定批次
	Manual_specify_batch_no bool `json:"manual_specify_batch_no,omitempty"`
	// 是否强制指定为分销商订单
	Distribution_order bool `json:"distribution_order,omitempty"`
	// 商品明细
	Details []Item `json:"details,omitempty"`
	// 支付明细
	Payments []Payment `json:"payments,omitempty"`
	// 发票信息
	Invoices []Invoice `json:"invoices,omitempty"`
	// 收货人     必镇
	Receiver_name string `json:"receiver_name,omitempty"`
	// 固定电话    必镇  和下一字段二选一必镇
	Receiver_phone string `json:"receiver_phone,omitempty"`
	// 手机号码    必镇  和上一字段二选一必镇
	Receiver_mobile string `json:"receiver_mobile,omitempty"`
	// 邮政编码
	Receiver_zip string `json:"receiver_zip,omitempty"`
	// 省名称      必镇
	Receiver_province string `json:"receiver_province,omitempty"`
	// 市名称     必镇
	Receiver_city string `json:"receiver_city,omitempty"`
	// 区名称     必镇
	Receiver_district string `json:"receiver_district,omitempty"`
	// 收货地址    必镇
	Receiver_address string `json:"receiver_address,omitempty"`
	// 真实姓名
	VipRealName string `json:"vipRealName,omitempty"`
	//  身份证号
	VipIdCard string `json:"vipIdCard,omitempty"`
	// 电子邮箱
	VipEmail string `json:"vipEmail,omitempty"`
	// 订单标记
	Tag_code string `json:"tag_code,omitempty"`
}

// gy.erp.trade.return.add
// 退货单新增
type Refund struct {
	//  支付方式代码  必镇
	Pay_type_code string `json:"pay_type_code,omitempty"`
	// 支付金额   必镇
	Payment string `json:"payment,omitempty"`
	// 支付时间
	Pay_time string `json:"pay_time,omitempty"` // time
	// 账号
	Account string `json:"account,omitempty"`
}

// gy.erp.trade.return.add
// 退货单新增
type Item_Deail struct {
	// 商品条码 必镇
	Barcode string `json:"barcode,omitempty"`
	// 商品代码  必镇
	Item_code string `json:"item_code,omitempty"`
	// 规格代码
	Sku_code string `json:"sku_code,omitempty"`
	// 数量  必镇
	Qty int32 `json:"qty,omitempty"`
	// 标准单价
	OriginPrice string `json:"originPrice,omitempty"`
	// 实际单价
	Price string `json:"price,omitempty"`
}

// gy.erp.trade.return.add
// 退货单新增
type ReturnOrderAddParam struct {
	// 退货单新增 请求头参
	SignaureParam
	// 店铺代码 必镇
	Shop_code string `json:"shop_code,omitempty"`
	// 会员代码 必镇
	Vip_code string `json:"vip_code,omitempty"`
	// 销售订单单据编号
	Trade_code string `json:"trade_code,omitempty"`
	// 销售订单平台单号
	Trade_platform_code string `json:"trade_platform_code,omitempty"`
	// 业务员代码
	Businessman_code string `json:"businessman_code,omitempty"`
	// 售后阶段1：买家确认收货前 2：买家确认收货后
	Refund_phase int `json:"refund_phase,omitempty"`
	// 售后类型
	Return_type int `json:"return_type,omitempty"`
	// 退货原因代码  必镇
	Type_code string `json:"type_code,omitempty"`
	// 退回仓库代码
	Warehousein_code string `json:"warehousein_code,omitempty"`
	// 退回快递名称
	Express_name string `json:"express_name,omitempty"`
	// 退回运单号
	Express_num string `json:"express_num,omitempty"`
	// 退入商品明细   必镇
	Item_detail []Item_Deail `json:"item_detail,omitempty"`
	// 收货人
	Receiver_name string `json:"receiver_name,omitempty"`
	// 收货人电话
	Receiver_phone string `json:"receiver_phone,omitempty"`
	// 收货人手机
	Receiver_mobile string `json:"receiver_mobile,omitempty"`
	// 收货人邮编
	Receiver_zip string `json:"receiver_zip,omitempty"`
	// 收货人省信息
	Receiver_province string `json:"receiver_province,omitempty"`
	// 收货人市信息
	Receiver_city string `json:"receiver_city,omitempty"`
	// 收货人区信息
	Receiver_district string `json:"receiver_district,omitempty"`
	// 收货人地址
	Receiver_address string `json:"receiver_address,omitempty"`
	// 退款明细
	Refund_detail []Refund `json:"refund_detail"`
	// 备注
	Note string `json:"note,omitempty"`
}

//gy.erp.trade.refund.add
// 退款单新增
type ReturnOrderRefundAddItem struct {
	// 商品条码
	Barcode string `json:"barcode,omitempty"`
	// 数量
	Qty int32 `json:"qty,omitempty"`
	// 单价
	Price string `json:"price,omitempty"`
	//备注
	Note string `json:"note,omitempty"`
}

//gy.erp.trade.refund.add
// 退款单新增
type ReturnOrderRefundAddParam struct {
	// 退款单新增 请求参数
	SignaureParam
	// 退款单单号   仅支持数字
	Refund_code string `json:"refund_code,omitempty"`
	// 退款单种类 0-仅退款 1-退货退款    必镇
	Refund_type int `json:"refund_type"`
	// 退款原因
	Refund_reason string `json:"refund_reason,omitempty"`
	// 关联订单单号
	Trade_code string `json:"trade_code,omitempty"`
	// 店铺代码  必镇
	Shop_code string `json:"shop_code,omitempty"`
	// 会员代码   必镇
	Vip_code string `json:"vip_code,omitempty"`
	// 单据类型代码
	Type_code string `json:"type_code,omitempty"`
	// 退款支付方式代码
	Payment_type_code string `json:"payment_type_code,omitempty"`
	// 退款金额
	Amount string `json:"amount,omitempty"`
	// 备注
	Note string `json:"note,omitempty"`
	// 退款商品列表
	Item_detail []ReturnOrderRefundAddItem `json:"item_detail,omitempty"`
}

// 退货退款请求参数
type ReturnRefundParam struct {
	ReturnOrderMsg ReturnOrderAddParam       // 退货
	RefundOrderMsg ReturnOrderRefundAddParam // 退款
	RequestType    int                       // 请求类型   1表示退货  2表示退款  3表示退货退款
}

//发货单信息
type DispatchResponse struct {
	// 订单id
	OrderId string `json:"order_id"`
	//物流名称简写
	LogisticsCode string `json:"logistics_code"`
	//物流单号
	LogisticsNum string `json:"logistics_num"`
	//发货时间
	SendDate string `json:"send_date"`
	//运费
	Freight string `json:"freight"`
	PackageInfo
}
type PackageInfo struct {
	// 包裹编号
	PackageCode string `json:"package_code"`
	// 包裹名称
	PackageName string `json:"package_name"`
	// 包裹明细
	PackageDetail []PackageDetail `json:"package_detail"`
}
type PackageDetail struct {
	//商品编号
	GoodsID string `json:"goods_id"`
	// 商品数量
	Num string `json:"num"`
}

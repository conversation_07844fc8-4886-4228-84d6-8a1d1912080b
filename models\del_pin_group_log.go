package models

import (
	"time"
)

type PinGroupLog struct {
	Id         int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	SkuId      string    `xorm:"default 'NULL' comment('商品skuId') index VARCHAR(55)"`
	GroupId    int       `xorm:"default NULL comment('活动id') INT(11)"`
	OpenId     string    `xorm:"default 'NULL' comment('小程序ID') VARCHAR(55)"`
	UserId     string    `xorm:"default 'NULL' comment('用户ID') VARCHAR(55)"`
	OpenName   string    `xorm:"default 'NULL' comment('用户昵称') VARCHAR(55)"`
	Portrait   string    `xorm:"default 'NULL' comment('头像链接') VARCHAR(255)"`
	Type       int       `xorm:"default 0 comment('类型 1发起拼团 2参团成功 3拼团成功') INT(255)"`
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

syntax = "proto3";
package oc;

// @Desc        广告MP对接服务
service AdvertisementMpService {
  // @Desc    	新增广告MP记录
  rpc AddAdvertisementMpRecord(AddAdvertisementMpRecordRequest) returns (MpBaseResponse) {}
}

message AddAdvertisementMpRecordRequest {
  //订单编号
  string order_sn = 1;
  //用户ID
  string user_id = 2;
  //转化行为 1 首页访问 2 下单 3 付费
  int32 action_type = 3;
  //转化Url
  string url =4;
  //落地页Click_id
  string click_id = 5;
  //用户来源渠道
  int32 user_agent = 6;
  //行为发生时
  int64 action_time = 7;
  //渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 8;
  //订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划订单,7保险订单,8积分订单 9秒杀订单
  int32 order_type = 9;
}

//通用返回
message MpBaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}


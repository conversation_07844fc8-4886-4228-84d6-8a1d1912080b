// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/weimob_cloud.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type WarehouseListPageResponse struct {
	List []*SimpleWarehouse `protobuf:"bytes,1,rep,name=List,proto3" json:"List"`
	// 每页包含的数据条数
	PageSize int32 `protobuf:"varint,2,opt,name=PageSize,proto3" json:"PageSize"`
	// 分页页码
	PageNum int32 `protobuf:"varint,3,opt,name=PageNum,proto3" json:"PageNum"`
	// 查询返回的总数据条数
	TotalCount           int64    `protobuf:"varint,4,opt,name=TotalCount,proto3" json:"TotalCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseListPageResponse) Reset()         { *m = WarehouseListPageResponse{} }
func (m *WarehouseListPageResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseListPageResponse) ProtoMessage()    {}
func (*WarehouseListPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{0}
}

func (m *WarehouseListPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListPageResponse.Unmarshal(m, b)
}
func (m *WarehouseListPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListPageResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseListPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListPageResponse.Merge(m, src)
}
func (m *WarehouseListPageResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseListPageResponse.Size(m)
}
func (m *WarehouseListPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListPageResponse proto.InternalMessageInfo

func (m *WarehouseListPageResponse) GetList() []*SimpleWarehouse {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *WarehouseListPageResponse) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WarehouseListPageResponse) GetPageNum() int32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *WarehouseListPageResponse) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type WarehouseListPageRequest struct {
	// 每页包含的数据条数
	PageSize int32 `protobuf:"varint,1,opt,name=PageSize,proto3" json:"PageSize"`
	// 分页页码
	PageNum int32 `protobuf:"varint,2,opt,name=PageNum,proto3" json:"PageNum"`
	// 仓库类型,0:全部；1-商家仓；2-门店仓；3-外部仓
	WarehouseType int32 `protobuf:"varint,3,opt,name=WarehouseType,proto3" json:"WarehouseType"`
	// 搜索类型,1-根据仓库名称搜索；2-根据仓库编号搜索
	SearchType int32 `protobuf:"varint,4,opt,name=SearchType,proto3" json:"SearchType"`
	// 搜索关键字
	Keyword string `protobuf:"bytes,5,opt,name=Keyword,proto3" json:"Keyword"`
	// 微盟组织ID
	OrgId                int64    `protobuf:"varint,6,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseListPageRequest) Reset()         { *m = WarehouseListPageRequest{} }
func (m *WarehouseListPageRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseListPageRequest) ProtoMessage()    {}
func (*WarehouseListPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{1}
}

func (m *WarehouseListPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListPageRequest.Unmarshal(m, b)
}
func (m *WarehouseListPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListPageRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseListPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListPageRequest.Merge(m, src)
}
func (m *WarehouseListPageRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseListPageRequest.Size(m)
}
func (m *WarehouseListPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListPageRequest proto.InternalMessageInfo

func (m *WarehouseListPageRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WarehouseListPageRequest) GetPageNum() int32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *WarehouseListPageRequest) GetWarehouseType() int32 {
	if m != nil {
		return m.WarehouseType
	}
	return 0
}

func (m *WarehouseListPageRequest) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *WarehouseListPageRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *WarehouseListPageRequest) GetOrgId() int64 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type SimpleWarehouse struct {
	// 配送方式，1-按订单销售门店的物流设置；2-仅商家配送
	DeliveryType  int32  `protobuf:"varint,1,opt,name=DeliveryType,proto3" json:"DeliveryType"`
	WarehouseName string `protobuf:"bytes,2,opt,name=WarehouseName,proto3" json:"WarehouseName"`
	WarehouseCode string `protobuf:"bytes,3,opt,name=WarehouseCode,proto3" json:"WarehouseCode"`
	WarehouseId   int32  `protobuf:"varint,4,opt,name=WarehouseId,proto3" json:"WarehouseId"`
	// 仓库子配送类型，1-商家配送；2-同城限时达；3-到店自提。当 deliveryType=2
	// 时，根据当前字段进行判断
	SubDeliveryTypeList  string   `protobuf:"bytes,5,opt,name=SubDeliveryTypeList,proto3" json:"SubDeliveryTypeList"`
	Address              string   `protobuf:"bytes,6,opt,name=Address,proto3" json:"Address"`
	ManagerName          string   `protobuf:"bytes,7,opt,name=ManagerName,proto3" json:"ManagerName"`
	Phone                string   `protobuf:"bytes,8,opt,name=Phone,proto3" json:"Phone"`
	Remark               string   `protobuf:"bytes,9,opt,name=Remark,proto3" json:"Remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleWarehouse) Reset()         { *m = SimpleWarehouse{} }
func (m *SimpleWarehouse) String() string { return proto.CompactTextString(m) }
func (*SimpleWarehouse) ProtoMessage()    {}
func (*SimpleWarehouse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{2}
}

func (m *SimpleWarehouse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleWarehouse.Unmarshal(m, b)
}
func (m *SimpleWarehouse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleWarehouse.Marshal(b, m, deterministic)
}
func (m *SimpleWarehouse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleWarehouse.Merge(m, src)
}
func (m *SimpleWarehouse) XXX_Size() int {
	return xxx_messageInfo_SimpleWarehouse.Size(m)
}
func (m *SimpleWarehouse) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleWarehouse.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleWarehouse proto.InternalMessageInfo

func (m *SimpleWarehouse) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *SimpleWarehouse) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *SimpleWarehouse) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *SimpleWarehouse) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *SimpleWarehouse) GetSubDeliveryTypeList() string {
	if m != nil {
		return m.SubDeliveryTypeList
	}
	return ""
}

func (m *SimpleWarehouse) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *SimpleWarehouse) GetManagerName() string {
	if m != nil {
		return m.ManagerName
	}
	return ""
}

func (m *SimpleWarehouse) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SimpleWarehouse) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type PushStockRequest struct {
	// 微盟仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode"`
	// 微盟仓库ID
	WarehouseId int32            `protobuf:"varint,2,opt,name=WarehouseId,proto3" json:"WarehouseId"`
	List        []*SimpleProduct `protobuf:"bytes,3,rep,name=List,proto3" json:"List"`
	// 库存变更类别，0-覆盖更新；1-增量更新
	AlterType int32 `protobuf:"varint,4,opt,name=AlterType,proto3" json:"AlterType"`
	// 微盟组织ID
	OrgId                int64    `protobuf:"varint,5,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushStockRequest) Reset()         { *m = PushStockRequest{} }
func (m *PushStockRequest) String() string { return proto.CompactTextString(m) }
func (*PushStockRequest) ProtoMessage()    {}
func (*PushStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{3}
}

func (m *PushStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushStockRequest.Unmarshal(m, b)
}
func (m *PushStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushStockRequest.Marshal(b, m, deterministic)
}
func (m *PushStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushStockRequest.Merge(m, src)
}
func (m *PushStockRequest) XXX_Size() int {
	return xxx_messageInfo_PushStockRequest.Size(m)
}
func (m *PushStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushStockRequest proto.InternalMessageInfo

func (m *PushStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *PushStockRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *PushStockRequest) GetList() []*SimpleProduct {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *PushStockRequest) GetAlterType() int32 {
	if m != nil {
		return m.AlterType
	}
	return 0
}

func (m *PushStockRequest) GetOrgId() int64 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type SimpleProduct struct {
	ProductId            int64        `protobuf:"varint,1,opt,name=ProductId,proto3" json:"ProductId"`
	List                 []*SimpleSku `protobuf:"bytes,2,rep,name=List,proto3" json:"List"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SimpleProduct) Reset()         { *m = SimpleProduct{} }
func (m *SimpleProduct) String() string { return proto.CompactTextString(m) }
func (*SimpleProduct) ProtoMessage()    {}
func (*SimpleProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{4}
}

func (m *SimpleProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleProduct.Unmarshal(m, b)
}
func (m *SimpleProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleProduct.Marshal(b, m, deterministic)
}
func (m *SimpleProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleProduct.Merge(m, src)
}
func (m *SimpleProduct) XXX_Size() int {
	return xxx_messageInfo_SimpleProduct.Size(m)
}
func (m *SimpleProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleProduct.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleProduct proto.InternalMessageInfo

func (m *SimpleProduct) GetProductId() int64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SimpleProduct) GetList() []*SimpleSku {
	if m != nil {
		return m.List
	}
	return nil
}

type SimpleSku struct {
	// 微盟skuId
	SkuId int64 `protobuf:"varint,1,opt,name=SkuId,proto3" json:"SkuId"`
	// 库存数量
	Stock                int32    `protobuf:"varint,2,opt,name=Stock,proto3" json:"Stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleSku) Reset()         { *m = SimpleSku{} }
func (m *SimpleSku) String() string { return proto.CompactTextString(m) }
func (*SimpleSku) ProtoMessage()    {}
func (*SimpleSku) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{5}
}

func (m *SimpleSku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleSku.Unmarshal(m, b)
}
func (m *SimpleSku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleSku.Marshal(b, m, deterministic)
}
func (m *SimpleSku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleSku.Merge(m, src)
}
func (m *SimpleSku) XXX_Size() int {
	return xxx_messageInfo_SimpleSku.Size(m)
}
func (m *SimpleSku) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleSku.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleSku proto.InternalMessageInfo

func (m *SimpleSku) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SimpleSku) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

type CommonResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Error                string   `protobuf:"bytes,2,opt,name=error,proto3" json:"error"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonResponse) Reset()         { *m = CommonResponse{} }
func (m *CommonResponse) String() string { return proto.CompactTextString(m) }
func (*CommonResponse) ProtoMessage()    {}
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{6}
}

func (m *CommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResponse.Unmarshal(m, b)
}
func (m *CommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResponse.Marshal(b, m, deterministic)
}
func (m *CommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResponse.Merge(m, src)
}
func (m *CommonResponse) XXX_Size() int {
	return xxx_messageInfo_CommonResponse.Size(m)
}
func (m *CommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResponse proto.InternalMessageInfo

func (m *CommonResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommonResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CommonResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type WeiMengOrderLogisticsUpdateRequest struct {
	//订单编号。可以通过 weimob_shop/order/list/search 接口获取该 ID。
	OrderNo int64 `protobuf:"varint,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//是否拆包发货。该字段为 true 时，fulfillItems 必填，此时会拆包发货。
	IsSplitPackage bool `protobuf:"varint,2,opt,name=is_split_package,json=isSplitPackage,proto3" json:"is_split_package"`
	//履约细分类型，订单配送类型为商家配送 1 对应：快递物流 1、无需物流 2。当履约细分类型为快递物流时，logistics 物流信息必填。
	FulfillMethod int32 `protobuf:"varint,3,opt,name=fulfill_method,json=fulfillMethod,proto3" json:"fulfill_method"`
	//发货单号
	DeliveryNo string `protobuf:"bytes,4,opt,name=delivery_no,json=deliveryNo,proto3" json:"delivery_no"`
	//发货公司 code，详情可参见快递公司编码列表
	DeliveryCompanyCode string `protobuf:"bytes,5,opt,name=delivery_company_code,json=deliveryCompanyCode,proto3" json:"delivery_company_code"`
	//发货公司名称
	DeliveryCompanyName string `protobuf:"bytes,6,opt,name=delivery_company_name,json=deliveryCompanyName,proto3" json:"delivery_company_name"`
	//发货门店组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
	Vid int64 `protobuf:"varint,7,opt,name=vid,proto3" json:"vid"`
	//发货门店类型，同商家枚举定义。[1-集团，2-品牌，3-区域，5-商场，10-门店，100-自提点
	VidType              int32    `protobuf:"varint,8,opt,name=vid_type,json=vidType,proto3" json:"vid_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengOrderLogisticsUpdateRequest) Reset()         { *m = WeiMengOrderLogisticsUpdateRequest{} }
func (m *WeiMengOrderLogisticsUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderLogisticsUpdateRequest) ProtoMessage()    {}
func (*WeiMengOrderLogisticsUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{7}
}

func (m *WeiMengOrderLogisticsUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest.Unmarshal(m, b)
}
func (m *WeiMengOrderLogisticsUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderLogisticsUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest.Merge(m, src)
}
func (m *WeiMengOrderLogisticsUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest.Size(m)
}
func (m *WeiMengOrderLogisticsUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderLogisticsUpdateRequest proto.InternalMessageInfo

func (m *WeiMengOrderLogisticsUpdateRequest) GetOrderNo() int64 {
	if m != nil {
		return m.OrderNo
	}
	return 0
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetIsSplitPackage() bool {
	if m != nil {
		return m.IsSplitPackage
	}
	return false
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetFulfillMethod() int32 {
	if m != nil {
		return m.FulfillMethod
	}
	return 0
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetDeliveryNo() string {
	if m != nil {
		return m.DeliveryNo
	}
	return ""
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetDeliveryCompanyCode() string {
	if m != nil {
		return m.DeliveryCompanyCode
	}
	return ""
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetDeliveryCompanyName() string {
	if m != nil {
		return m.DeliveryCompanyName
	}
	return ""
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetVid() int64 {
	if m != nil {
		return m.Vid
	}
	return 0
}

func (m *WeiMengOrderLogisticsUpdateRequest) GetVidType() int32 {
	if m != nil {
		return m.VidType
	}
	return 0
}

type WeiMengOrderDetailRequest struct {
	//订单查询范围。支持的范围类型包括：1-订单信息；2-履约信息；3-售后信息。不传则只展示订单信息，不展示订单项列表。
	OrderDomains []int32 `protobuf:"varint,1,rep,packed,name=order_domains,json=orderDomains,proto3" json:"order_domains"`
	//订单号
	OrderNo              int64    `protobuf:"varint,2,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengOrderDetailRequest) Reset()         { *m = WeiMengOrderDetailRequest{} }
func (m *WeiMengOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderDetailRequest) ProtoMessage()    {}
func (*WeiMengOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{8}
}

func (m *WeiMengOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderDetailRequest.Unmarshal(m, b)
}
func (m *WeiMengOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderDetailRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderDetailRequest.Merge(m, src)
}
func (m *WeiMengOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderDetailRequest.Size(m)
}
func (m *WeiMengOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderDetailRequest proto.InternalMessageInfo

func (m *WeiMengOrderDetailRequest) GetOrderDomains() []int32 {
	if m != nil {
		return m.OrderDomains
	}
	return nil
}

func (m *WeiMengOrderDetailRequest) GetOrderNo() int64 {
	if m != nil {
		return m.OrderNo
	}
	return 0
}

type WeiMengOrderDetailResponse struct {
	//返回码 200成功 400失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//订单详情数据
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengOrderDetailResponse) Reset()         { *m = WeiMengOrderDetailResponse{} }
func (m *WeiMengOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*WeiMengOrderDetailResponse) ProtoMessage()    {}
func (*WeiMengOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{9}
}

func (m *WeiMengOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengOrderDetailResponse.Unmarshal(m, b)
}
func (m *WeiMengOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *WeiMengOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengOrderDetailResponse.Merge(m, src)
}
func (m *WeiMengOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_WeiMengOrderDetailResponse.Size(m)
}
func (m *WeiMengOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengOrderDetailResponse proto.InternalMessageInfo

func (m *WeiMengOrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WeiMengOrderDetailResponse) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WeiMengOrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type WeiMengRefundDetailRequest struct {
	//退款单号
	RightsId             int64    `protobuf:"varint,1,opt,name=rightsId,proto3" json:"rightsId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengRefundDetailRequest) Reset()         { *m = WeiMengRefundDetailRequest{} }
func (m *WeiMengRefundDetailRequest) String() string { return proto.CompactTextString(m) }
func (*WeiMengRefundDetailRequest) ProtoMessage()    {}
func (*WeiMengRefundDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{10}
}

func (m *WeiMengRefundDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengRefundDetailRequest.Unmarshal(m, b)
}
func (m *WeiMengRefundDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengRefundDetailRequest.Marshal(b, m, deterministic)
}
func (m *WeiMengRefundDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengRefundDetailRequest.Merge(m, src)
}
func (m *WeiMengRefundDetailRequest) XXX_Size() int {
	return xxx_messageInfo_WeiMengRefundDetailRequest.Size(m)
}
func (m *WeiMengRefundDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengRefundDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengRefundDetailRequest proto.InternalMessageInfo

func (m *WeiMengRefundDetailRequest) GetRightsId() int64 {
	if m != nil {
		return m.RightsId
	}
	return 0
}

type WeiMengRefundDetailResponse struct {
	//返回码 200成功 400失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//订单详情数据
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeiMengRefundDetailResponse) Reset()         { *m = WeiMengRefundDetailResponse{} }
func (m *WeiMengRefundDetailResponse) String() string { return proto.CompactTextString(m) }
func (*WeiMengRefundDetailResponse) ProtoMessage()    {}
func (*WeiMengRefundDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{11}
}

func (m *WeiMengRefundDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMengRefundDetailResponse.Unmarshal(m, b)
}
func (m *WeiMengRefundDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMengRefundDetailResponse.Marshal(b, m, deterministic)
}
func (m *WeiMengRefundDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMengRefundDetailResponse.Merge(m, src)
}
func (m *WeiMengRefundDetailResponse) XXX_Size() int {
	return xxx_messageInfo_WeiMengRefundDetailResponse.Size(m)
}
func (m *WeiMengRefundDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMengRefundDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMengRefundDetailResponse proto.InternalMessageInfo

func (m *WeiMengRefundDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WeiMengRefundDetailResponse) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WeiMengRefundDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 微盟创建商品接口
type WmShopGoodSCreateDto struct {
	//    基础信息
	BasicInfo *BasicInfo `protobuf:"bytes,1,opt,name=basicInfo,proto3" json:"basicInfo"`
	//    商品品牌 ID
	BrandId int64 `protobuf:"varint,2,opt,name=brandId,proto3" json:"brandId"`
	//    商品类目 ID
	CategoryId      int64  `protobuf:"varint,3,opt,name=categoryId,proto3" json:"categoryId"`
	DeductStockType int64  `protobuf:"varint,4,opt,name=deductStockType,proto3" json:"deductStockType"`
	DefaultImageUrl string `protobuf:"bytes,5,opt,name=defaultImageUrl,proto3" json:"defaultImageUrl"`
	//    商品分组 ID
	GoodsClassifyIdList []int64 `protobuf:"varint,6,rep,packed,name=goodsClassifyIdList,proto3" json:"goodsClassifyIdList"`
	//    商品描述吧啦吧啦
	GoodsDesc string `protobuf:"bytes,7,opt,name=goodsDesc,proto3" json:"goodsDesc"`
	//    商品图片
	GoodsImageUrl []string `protobuf:"bytes,8,rep,name=goodsImageUrl,proto3" json:"goodsImageUrl"`
	// 商品模板
	GoodsTemplateId int64 `protobuf:"varint,9,opt,name=goodsTemplateId,proto3" json:"goodsTemplateId"`
	//    商品类型。类型包括：1-普通商品；2-虚拟商品。
	GoodsType int64 `protobuf:"varint,10,opt,name=goodsType,proto3" json:"goodsType"`
	//    商品视频图片链接
	GoodsVideoImageUrl string `protobuf:"bytes,11,opt,name=goodsVideoImageUrl,proto3" json:"goodsVideoImageUrl"`
	//    商品视频链接
	GoodsVideoUrl string `protobuf:"bytes,12,opt,name=goodsVideoUrl,proto3" json:"goodsVideoUrl"`
	//    初始销量
	InitSales int64 `protobuf:"varint,13,opt,name=initSales,proto3" json:"initSales"`
	//    发票名称
	InvoiceTitle string `protobuf:"bytes,14,opt,name=invoiceTitle,proto3" json:"invoiceTitle"`
	//    商品开票名称类型。类型包括：1-同商品名称； 2-自定义名称。
	InvoiceTitleType int64 `protobuf:"varint,15,opt,name=invoiceTitleType,proto3" json:"invoiceTitleType"`
	//    商品是否可售。false-禁售；true-可售。
	IsCanSell bool `protobuf:"varint,16,opt,name=isCanSell,proto3" json:"isCanSell"`
	//    商品是否多规格。 false-单规格；true-多规格。
	IsMultiSku bool `protobuf:"varint,17,opt,name=isMultiSku,proto3" json:"isMultiSku"`
	IsOnline   bool `protobuf:"varint,18,opt,name=isOnline,proto3" json:"isOnline"`
	//    限购开关。false-关闭； true-开启。
	LimitSwitch bool `protobuf:"varint,19,opt,name=limitSwitch,proto3" json:"limitSwitch"`
	// 商品编码
	OuterGoodsCode string `protobuf:"bytes,20,opt,name=outerGoodsCode,proto3" json:"outerGoodsCode"`
	//    履约方式
	PerformanceWay *PerformanceWay `protobuf:"bytes,21,opt,name=performanceWay,proto3" json:"performanceWay"`
	SellUnitId     int64           `protobuf:"varint,22,opt,name=sellUnitId,proto3" json:"sellUnitId"`
	//    商品规格 SKU 列表
	SkuList []*SkuList `protobuf:"bytes,23,rep,name=skuList,proto3" json:"skuList"`
	//    商品子类型。类型包括：101-普通商品；102-海淘商品；103-无需物流实物商品；201-普通虚拟商品；202-付费卷虚拟商品。
	SubGoodsType int64 `protobuf:"varint,24,opt,name=subGoodsType,proto3" json:"subGoodsType"`
	//    商品子标题
	SubTitle string `protobuf:"bytes,25,opt,name=subTitle,proto3" json:"subTitle"`
	//    商品标签 ID，可以通过 weimob_shop/goods/tag/getList 接口获取该 ID。
	TagId int64 `protobuf:"varint,26,opt,name=tagId,proto3" json:"tagId"`
	//    商品名称
	Title string `protobuf:"bytes,27,opt,name=title,proto3" json:"title"`
	Wid   int64  `protobuf:"varint,28,opt,name=wid,proto3" json:"wid"`
	//    交付方式。0-单次交付；1-多次交付（只支持线上且现货）。周期购商品必填。
	GoodsDeliveryMode int64 `protobuf:"varint,29,opt,name=goodsDeliveryMode,proto3" json:"goodsDeliveryMode"`
	//    积分抵扣规则设置。
	PointDeductRule      *PointDeductRule `protobuf:"bytes,30,opt,name=pointDeductRule,proto3" json:"pointDeductRule"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WmShopGoodSCreateDto) Reset()         { *m = WmShopGoodSCreateDto{} }
func (m *WmShopGoodSCreateDto) String() string { return proto.CompactTextString(m) }
func (*WmShopGoodSCreateDto) ProtoMessage()    {}
func (*WmShopGoodSCreateDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{12}
}

func (m *WmShopGoodSCreateDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WmShopGoodSCreateDto.Unmarshal(m, b)
}
func (m *WmShopGoodSCreateDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WmShopGoodSCreateDto.Marshal(b, m, deterministic)
}
func (m *WmShopGoodSCreateDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WmShopGoodSCreateDto.Merge(m, src)
}
func (m *WmShopGoodSCreateDto) XXX_Size() int {
	return xxx_messageInfo_WmShopGoodSCreateDto.Size(m)
}
func (m *WmShopGoodSCreateDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WmShopGoodSCreateDto.DiscardUnknown(m)
}

var xxx_messageInfo_WmShopGoodSCreateDto proto.InternalMessageInfo

func (m *WmShopGoodSCreateDto) GetBasicInfo() *BasicInfo {
	if m != nil {
		return m.BasicInfo
	}
	return nil
}

func (m *WmShopGoodSCreateDto) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetDeductStockType() int64 {
	if m != nil {
		return m.DeductStockType
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetDefaultImageUrl() string {
	if m != nil {
		return m.DefaultImageUrl
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetGoodsClassifyIdList() []int64 {
	if m != nil {
		return m.GoodsClassifyIdList
	}
	return nil
}

func (m *WmShopGoodSCreateDto) GetGoodsDesc() string {
	if m != nil {
		return m.GoodsDesc
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetGoodsImageUrl() []string {
	if m != nil {
		return m.GoodsImageUrl
	}
	return nil
}

func (m *WmShopGoodSCreateDto) GetGoodsTemplateId() int64 {
	if m != nil {
		return m.GoodsTemplateId
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetGoodsType() int64 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetGoodsVideoImageUrl() string {
	if m != nil {
		return m.GoodsVideoImageUrl
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetGoodsVideoUrl() string {
	if m != nil {
		return m.GoodsVideoUrl
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetInitSales() int64 {
	if m != nil {
		return m.InitSales
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetInvoiceTitle() string {
	if m != nil {
		return m.InvoiceTitle
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetInvoiceTitleType() int64 {
	if m != nil {
		return m.InvoiceTitleType
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetIsCanSell() bool {
	if m != nil {
		return m.IsCanSell
	}
	return false
}

func (m *WmShopGoodSCreateDto) GetIsMultiSku() bool {
	if m != nil {
		return m.IsMultiSku
	}
	return false
}

func (m *WmShopGoodSCreateDto) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *WmShopGoodSCreateDto) GetLimitSwitch() bool {
	if m != nil {
		return m.LimitSwitch
	}
	return false
}

func (m *WmShopGoodSCreateDto) GetOuterGoodsCode() string {
	if m != nil {
		return m.OuterGoodsCode
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetPerformanceWay() *PerformanceWay {
	if m != nil {
		return m.PerformanceWay
	}
	return nil
}

func (m *WmShopGoodSCreateDto) GetSellUnitId() int64 {
	if m != nil {
		return m.SellUnitId
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetSkuList() []*SkuList {
	if m != nil {
		return m.SkuList
	}
	return nil
}

func (m *WmShopGoodSCreateDto) GetSubGoodsType() int64 {
	if m != nil {
		return m.SubGoodsType
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetTagId() int64 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *WmShopGoodSCreateDto) GetWid() int64 {
	if m != nil {
		return m.Wid
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetGoodsDeliveryMode() int64 {
	if m != nil {
		return m.GoodsDeliveryMode
	}
	return 0
}

func (m *WmShopGoodSCreateDto) GetPointDeductRule() *PointDeductRule {
	if m != nil {
		return m.PointDeductRule
	}
	return nil
}

type BasicInfo struct {
	Vid                  int64    `protobuf:"varint,1,opt,name=vid,proto3" json:"vid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BasicInfo) Reset()         { *m = BasicInfo{} }
func (m *BasicInfo) String() string { return proto.CompactTextString(m) }
func (*BasicInfo) ProtoMessage()    {}
func (*BasicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{13}
}

func (m *BasicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BasicInfo.Unmarshal(m, b)
}
func (m *BasicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BasicInfo.Marshal(b, m, deterministic)
}
func (m *BasicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BasicInfo.Merge(m, src)
}
func (m *BasicInfo) XXX_Size() int {
	return xxx_messageInfo_BasicInfo.Size(m)
}
func (m *BasicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BasicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BasicInfo proto.InternalMessageInfo

func (m *BasicInfo) GetVid() int64 {
	if m != nil {
		return m.Vid
	}
	return 0
}

//    履约方式
type PerformanceWay struct {
	DeliveryList         []*DeliveryList `protobuf:"bytes,1,rep,name=deliveryList,proto3" json:"deliveryList"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PerformanceWay) Reset()         { *m = PerformanceWay{} }
func (m *PerformanceWay) String() string { return proto.CompactTextString(m) }
func (*PerformanceWay) ProtoMessage()    {}
func (*PerformanceWay) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{14}
}

func (m *PerformanceWay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PerformanceWay.Unmarshal(m, b)
}
func (m *PerformanceWay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PerformanceWay.Marshal(b, m, deterministic)
}
func (m *PerformanceWay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PerformanceWay.Merge(m, src)
}
func (m *PerformanceWay) XXX_Size() int {
	return xxx_messageInfo_PerformanceWay.Size(m)
}
func (m *PerformanceWay) XXX_DiscardUnknown() {
	xxx_messageInfo_PerformanceWay.DiscardUnknown(m)
}

var xxx_messageInfo_PerformanceWay proto.InternalMessageInfo

func (m *PerformanceWay) GetDeliveryList() []*DeliveryList {
	if m != nil {
		return m.DeliveryList
	}
	return nil
}

//    配送方式。配送方式与商品类型关联，普通实物商品：只能设置商家配送、同城限时达、到底自提。 虚拟商品：只能设置无需物流配送方式。
type DeliveryList struct {
	//    配送方式 ID。可以通过 weimob_shop/fulfill/goods/fulfilltype/getList 接口获取该 ID。
	DeliveryId int64 `protobuf:"varint,1,opt,name=deliveryId,proto3" json:"deliveryId"`
	//    配送方式关系节点 ID。可以通过 weimob_shop/fulfill/goods/fulfilltype/getList 接口获取该 ID。
	DeliveryNodeShipId int64 `protobuf:"varint,2,opt,name=deliveryNodeShipId,proto3" json:"deliveryNodeShipId"`
	//    配送类型。类型包括：1-商家配送；2-同城限时达；3-到店自提；4-门店交易；5-无需物流；6-门店自助。
	DeliveryType int64 `protobuf:"varint,3,opt,name=deliveryType,proto3" json:"deliveryType"`
	//    配送模板 ID
	TemplateId           int64    `protobuf:"varint,4,opt,name=templateId,proto3" json:"templateId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryList) Reset()         { *m = DeliveryList{} }
func (m *DeliveryList) String() string { return proto.CompactTextString(m) }
func (*DeliveryList) ProtoMessage()    {}
func (*DeliveryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{15}
}

func (m *DeliveryList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryList.Unmarshal(m, b)
}
func (m *DeliveryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryList.Marshal(b, m, deterministic)
}
func (m *DeliveryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryList.Merge(m, src)
}
func (m *DeliveryList) XXX_Size() int {
	return xxx_messageInfo_DeliveryList.Size(m)
}
func (m *DeliveryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryList.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryList proto.InternalMessageInfo

func (m *DeliveryList) GetDeliveryId() int64 {
	if m != nil {
		return m.DeliveryId
	}
	return 0
}

func (m *DeliveryList) GetDeliveryNodeShipId() int64 {
	if m != nil {
		return m.DeliveryNodeShipId
	}
	return 0
}

func (m *DeliveryList) GetDeliveryType() int64 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *DeliveryList) GetTemplateId() int64 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type SkuList struct {
	//    图片 URL
	ImageUrl  string `protobuf:"bytes,1,opt,name=imageUrl,proto3" json:"imageUrl"`
	CostPrice int64  `protobuf:"varint,2,opt,name=costPrice,proto3" json:"costPrice"`
	//    SKU 库存数量
	SkuStockNum int64 `protobuf:"varint,3,opt,name=skuStockNum,proto3" json:"skuStockNum"`
	//marketPrice 市场价
	MarketPrice int64 `protobuf:"varint,4,opt,name=marketPrice,proto3" json:"marketPrice"`
	//    销售价
	SalePrice int64 `protobuf:"varint,5,opt,name=salePrice,proto3" json:"salePrice"`
	//    体积
	Volume int64 `protobuf:"varint,6,opt,name=volume,proto3" json:"volume"`
	//    重量
	Weight int64 `protobuf:"varint,7,opt,name=weight,proto3" json:"weight"`
	//    商家编码
	OuterSkuCode         string              `protobuf:"bytes,8,opt,name=outerSkuCode,proto3" json:"outerSkuCode"`
	SkuSpecValueList     []*SkuSpecValueList `protobuf:"bytes,9,rep,name=skuSpecValueList,proto3" json:"skuSpecValueList"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SkuList) Reset()         { *m = SkuList{} }
func (m *SkuList) String() string { return proto.CompactTextString(m) }
func (*SkuList) ProtoMessage()    {}
func (*SkuList) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{16}
}

func (m *SkuList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuList.Unmarshal(m, b)
}
func (m *SkuList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuList.Marshal(b, m, deterministic)
}
func (m *SkuList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuList.Merge(m, src)
}
func (m *SkuList) XXX_Size() int {
	return xxx_messageInfo_SkuList.Size(m)
}
func (m *SkuList) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuList.DiscardUnknown(m)
}

var xxx_messageInfo_SkuList proto.InternalMessageInfo

func (m *SkuList) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *SkuList) GetCostPrice() int64 {
	if m != nil {
		return m.CostPrice
	}
	return 0
}

func (m *SkuList) GetSkuStockNum() int64 {
	if m != nil {
		return m.SkuStockNum
	}
	return 0
}

func (m *SkuList) GetMarketPrice() int64 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *SkuList) GetSalePrice() int64 {
	if m != nil {
		return m.SalePrice
	}
	return 0
}

func (m *SkuList) GetVolume() int64 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *SkuList) GetWeight() int64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *SkuList) GetOuterSkuCode() string {
	if m != nil {
		return m.OuterSkuCode
	}
	return ""
}

func (m *SkuList) GetSkuSpecValueList() []*SkuSpecValueList {
	if m != nil {
		return m.SkuSpecValueList
	}
	return nil
}

//    SKU 规格值列表
type SkuSpecValueList struct {
	SpecId               int64    `protobuf:"varint,1,opt,name=specId,proto3" json:"specId"`
	SpecValueId          int64    `protobuf:"varint,2,opt,name=specValueId,proto3" json:"specValueId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuSpecValueList) Reset()         { *m = SkuSpecValueList{} }
func (m *SkuSpecValueList) String() string { return proto.CompactTextString(m) }
func (*SkuSpecValueList) ProtoMessage()    {}
func (*SkuSpecValueList) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{17}
}

func (m *SkuSpecValueList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuSpecValueList.Unmarshal(m, b)
}
func (m *SkuSpecValueList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuSpecValueList.Marshal(b, m, deterministic)
}
func (m *SkuSpecValueList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuSpecValueList.Merge(m, src)
}
func (m *SkuSpecValueList) XXX_Size() int {
	return xxx_messageInfo_SkuSpecValueList.Size(m)
}
func (m *SkuSpecValueList) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuSpecValueList.DiscardUnknown(m)
}

var xxx_messageInfo_SkuSpecValueList proto.InternalMessageInfo

func (m *SkuSpecValueList) GetSpecId() int64 {
	if m != nil {
		return m.SpecId
	}
	return 0
}

func (m *SkuSpecValueList) GetSpecValueId() int64 {
	if m != nil {
		return m.SpecValueId
	}
	return 0
}

//    积分抵扣规则设置。
type PointDeductRule struct {
	//    是否开启积分抵扣。0-不开启；1-开启。
	OpenPointDeduct int64 `protobuf:"varint,1,opt,name=openPointDeduct,proto3" json:"openPointDeduct"`
	//    使用条件。0：不限；1：应收金额/销售价 >= useDiscount 折可用
	UseCondition int64 `protobuf:"varint,2,opt,name=useCondition,proto3" json:"useCondition"`
	//    使用折扣
	UseDiscount int64 `protobuf:"varint,3,opt,name=useDiscount,proto3" json:"useDiscount"`
	//    积分抵扣额度。0：不限；1：应收金额减抵扣额度/销售价 <= deductDiscount折；2：应收金额减抵扣额度/应收金额 <= deductDiscount折。
	DeductAmount int64 `protobuf:"varint,4,opt,name=deductAmount,proto3" json:"deductAmount"`
	//    抵扣折扣
	DeductDiscount int64 `protobuf:"varint,5,opt,name=deductDiscount,proto3" json:"deductDiscount"`
	//    积分抵扣显示类型：1-按比例显示；2-按金额显示。
	DeductionType        int64    `protobuf:"varint,6,opt,name=deductionType,proto3" json:"deductionType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PointDeductRule) Reset()         { *m = PointDeductRule{} }
func (m *PointDeductRule) String() string { return proto.CompactTextString(m) }
func (*PointDeductRule) ProtoMessage()    {}
func (*PointDeductRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{18}
}

func (m *PointDeductRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointDeductRule.Unmarshal(m, b)
}
func (m *PointDeductRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointDeductRule.Marshal(b, m, deterministic)
}
func (m *PointDeductRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointDeductRule.Merge(m, src)
}
func (m *PointDeductRule) XXX_Size() int {
	return xxx_messageInfo_PointDeductRule.Size(m)
}
func (m *PointDeductRule) XXX_DiscardUnknown() {
	xxx_messageInfo_PointDeductRule.DiscardUnknown(m)
}

var xxx_messageInfo_PointDeductRule proto.InternalMessageInfo

func (m *PointDeductRule) GetOpenPointDeduct() int64 {
	if m != nil {
		return m.OpenPointDeduct
	}
	return 0
}

func (m *PointDeductRule) GetUseCondition() int64 {
	if m != nil {
		return m.UseCondition
	}
	return 0
}

func (m *PointDeductRule) GetUseDiscount() int64 {
	if m != nil {
		return m.UseDiscount
	}
	return 0
}

func (m *PointDeductRule) GetDeductAmount() int64 {
	if m != nil {
		return m.DeductAmount
	}
	return 0
}

func (m *PointDeductRule) GetDeductDiscount() int64 {
	if m != nil {
		return m.DeductDiscount
	}
	return 0
}

func (m *PointDeductRule) GetDeductionType() int64 {
	if m != nil {
		return m.DeductionType
	}
	return 0
}

type WmShopGoodSCreateResponse struct {
	Code                 *WMCode  `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Data                 *WMData  `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WmShopGoodSCreateResponse) Reset()         { *m = WmShopGoodSCreateResponse{} }
func (m *WmShopGoodSCreateResponse) String() string { return proto.CompactTextString(m) }
func (*WmShopGoodSCreateResponse) ProtoMessage()    {}
func (*WmShopGoodSCreateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{19}
}

func (m *WmShopGoodSCreateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WmShopGoodSCreateResponse.Unmarshal(m, b)
}
func (m *WmShopGoodSCreateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WmShopGoodSCreateResponse.Marshal(b, m, deterministic)
}
func (m *WmShopGoodSCreateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WmShopGoodSCreateResponse.Merge(m, src)
}
func (m *WmShopGoodSCreateResponse) XXX_Size() int {
	return xxx_messageInfo_WmShopGoodSCreateResponse.Size(m)
}
func (m *WmShopGoodSCreateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WmShopGoodSCreateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WmShopGoodSCreateResponse proto.InternalMessageInfo

func (m *WmShopGoodSCreateResponse) GetCode() *WMCode {
	if m != nil {
		return m.Code
	}
	return nil
}

func (m *WmShopGoodSCreateResponse) GetData() *WMData {
	if m != nil {
		return m.Data
	}
	return nil
}

type WMCode struct {
	Errcode              string   `protobuf:"bytes,1,opt,name=errcode,proto3" json:"errcode"`
	Errmsg               string   `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMCode) Reset()         { *m = WMCode{} }
func (m *WMCode) String() string { return proto.CompactTextString(m) }
func (*WMCode) ProtoMessage()    {}
func (*WMCode) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{20}
}

func (m *WMCode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMCode.Unmarshal(m, b)
}
func (m *WMCode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMCode.Marshal(b, m, deterministic)
}
func (m *WMCode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMCode.Merge(m, src)
}
func (m *WMCode) XXX_Size() int {
	return xxx_messageInfo_WMCode.Size(m)
}
func (m *WMCode) XXX_DiscardUnknown() {
	xxx_messageInfo_WMCode.DiscardUnknown(m)
}

var xxx_messageInfo_WMCode proto.InternalMessageInfo

func (m *WMCode) GetErrcode() string {
	if m != nil {
		return m.Errcode
	}
	return ""
}

func (m *WMCode) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

// 业务返回数据
type WMData struct {
	SkuList              []*WMSkuList `protobuf:"bytes,1,rep,name=skuList,proto3" json:"skuList"`
	GoodsId              int64        `protobuf:"varint,2,opt,name=goodsId,proto3" json:"goodsId"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WMData) Reset()         { *m = WMData{} }
func (m *WMData) String() string { return proto.CompactTextString(m) }
func (*WMData) ProtoMessage()    {}
func (*WMData) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{21}
}

func (m *WMData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMData.Unmarshal(m, b)
}
func (m *WMData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMData.Marshal(b, m, deterministic)
}
func (m *WMData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMData.Merge(m, src)
}
func (m *WMData) XXX_Size() int {
	return xxx_messageInfo_WMData.Size(m)
}
func (m *WMData) XXX_DiscardUnknown() {
	xxx_messageInfo_WMData.DiscardUnknown(m)
}

var xxx_messageInfo_WMData proto.InternalMessageInfo

func (m *WMData) GetSkuList() []*WMSkuList {
	if m != nil {
		return m.SkuList
	}
	return nil
}

func (m *WMData) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

type WMSkuList struct {
	OuterSkuCode         string   `protobuf:"bytes,1,opt,name=outerSkuCode,proto3" json:"outerSkuCode"`
	SkuId                int64    `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMSkuList) Reset()         { *m = WMSkuList{} }
func (m *WMSkuList) String() string { return proto.CompactTextString(m) }
func (*WMSkuList) ProtoMessage()    {}
func (*WMSkuList) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{22}
}

func (m *WMSkuList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMSkuList.Unmarshal(m, b)
}
func (m *WMSkuList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMSkuList.Marshal(b, m, deterministic)
}
func (m *WMSkuList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMSkuList.Merge(m, src)
}
func (m *WMSkuList) XXX_Size() int {
	return xxx_messageInfo_WMSkuList.Size(m)
}
func (m *WMSkuList) XXX_DiscardUnknown() {
	xxx_messageInfo_WMSkuList.DiscardUnknown(m)
}

var xxx_messageInfo_WMSkuList proto.InternalMessageInfo

func (m *WMSkuList) GetOuterSkuCode() string {
	if m != nil {
		return m.OuterSkuCode
	}
	return ""
}

func (m *WMSkuList) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

type WMQueryUploadImgDto struct {
	//    图片名称，长度不能大于50个字符
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//    类型 File 图片文件（支持的文件类型：gif,jpg,jpeg,png）
	File                 string   `protobuf:"bytes,2,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMQueryUploadImgDto) Reset()         { *m = WMQueryUploadImgDto{} }
func (m *WMQueryUploadImgDto) String() string { return proto.CompactTextString(m) }
func (*WMQueryUploadImgDto) ProtoMessage()    {}
func (*WMQueryUploadImgDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{23}
}

func (m *WMQueryUploadImgDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMQueryUploadImgDto.Unmarshal(m, b)
}
func (m *WMQueryUploadImgDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMQueryUploadImgDto.Marshal(b, m, deterministic)
}
func (m *WMQueryUploadImgDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMQueryUploadImgDto.Merge(m, src)
}
func (m *WMQueryUploadImgDto) XXX_Size() int {
	return xxx_messageInfo_WMQueryUploadImgDto.Size(m)
}
func (m *WMQueryUploadImgDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WMQueryUploadImgDto.DiscardUnknown(m)
}

var xxx_messageInfo_WMQueryUploadImgDto proto.InternalMessageInfo

func (m *WMQueryUploadImgDto) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WMQueryUploadImgDto) GetFile() string {
	if m != nil {
		return m.File
	}
	return ""
}

type WMQueryUploadImgResponse struct {
	Data                 *WMUrlData `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
	Code                 *WMCode    `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *WMQueryUploadImgResponse) Reset()         { *m = WMQueryUploadImgResponse{} }
func (m *WMQueryUploadImgResponse) String() string { return proto.CompactTextString(m) }
func (*WMQueryUploadImgResponse) ProtoMessage()    {}
func (*WMQueryUploadImgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{24}
}

func (m *WMQueryUploadImgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMQueryUploadImgResponse.Unmarshal(m, b)
}
func (m *WMQueryUploadImgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMQueryUploadImgResponse.Marshal(b, m, deterministic)
}
func (m *WMQueryUploadImgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMQueryUploadImgResponse.Merge(m, src)
}
func (m *WMQueryUploadImgResponse) XXX_Size() int {
	return xxx_messageInfo_WMQueryUploadImgResponse.Size(m)
}
func (m *WMQueryUploadImgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WMQueryUploadImgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WMQueryUploadImgResponse proto.InternalMessageInfo

func (m *WMQueryUploadImgResponse) GetData() *WMUrlData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WMQueryUploadImgResponse) GetCode() *WMCode {
	if m != nil {
		return m.Code
	}
	return nil
}

type WMUrlData struct {
	Size                 int64      `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	UrlInfo              *WMUrlInfo `protobuf:"bytes,2,opt,name=urlInfo,proto3" json:"urlInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *WMUrlData) Reset()         { *m = WMUrlData{} }
func (m *WMUrlData) String() string { return proto.CompactTextString(m) }
func (*WMUrlData) ProtoMessage()    {}
func (*WMUrlData) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{25}
}

func (m *WMUrlData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMUrlData.Unmarshal(m, b)
}
func (m *WMUrlData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMUrlData.Marshal(b, m, deterministic)
}
func (m *WMUrlData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMUrlData.Merge(m, src)
}
func (m *WMUrlData) XXX_Size() int {
	return xxx_messageInfo_WMUrlData.Size(m)
}
func (m *WMUrlData) XXX_DiscardUnknown() {
	xxx_messageInfo_WMUrlData.DiscardUnknown(m)
}

var xxx_messageInfo_WMUrlData proto.InternalMessageInfo

func (m *WMUrlData) GetSize() int64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *WMUrlData) GetUrlInfo() *WMUrlInfo {
	if m != nil {
		return m.UrlInfo
	}
	return nil
}

type WMUrlInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	LegalStatus          int64    `protobuf:"varint,3,opt,name=legalStatus,proto3" json:"legalStatus"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMUrlInfo) Reset()         { *m = WMUrlInfo{} }
func (m *WMUrlInfo) String() string { return proto.CompactTextString(m) }
func (*WMUrlInfo) ProtoMessage()    {}
func (*WMUrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{26}
}

func (m *WMUrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMUrlInfo.Unmarshal(m, b)
}
func (m *WMUrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMUrlInfo.Marshal(b, m, deterministic)
}
func (m *WMUrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMUrlInfo.Merge(m, src)
}
func (m *WMUrlInfo) XXX_Size() int {
	return xxx_messageInfo_WMUrlInfo.Size(m)
}
func (m *WMUrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WMUrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WMUrlInfo proto.InternalMessageInfo

func (m *WMUrlInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WMUrlInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *WMUrlInfo) GetLegalStatus() int64 {
	if m != nil {
		return m.LegalStatus
	}
	return 0
}

type WMQueryCategoryTreeResponse struct {
	Code                 *WMCode           `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Data                 []*WMCategoryList `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *WMQueryCategoryTreeResponse) Reset()         { *m = WMQueryCategoryTreeResponse{} }
func (m *WMQueryCategoryTreeResponse) String() string { return proto.CompactTextString(m) }
func (*WMQueryCategoryTreeResponse) ProtoMessage()    {}
func (*WMQueryCategoryTreeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{27}
}

func (m *WMQueryCategoryTreeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMQueryCategoryTreeResponse.Unmarshal(m, b)
}
func (m *WMQueryCategoryTreeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMQueryCategoryTreeResponse.Marshal(b, m, deterministic)
}
func (m *WMQueryCategoryTreeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMQueryCategoryTreeResponse.Merge(m, src)
}
func (m *WMQueryCategoryTreeResponse) XXX_Size() int {
	return xxx_messageInfo_WMQueryCategoryTreeResponse.Size(m)
}
func (m *WMQueryCategoryTreeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WMQueryCategoryTreeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WMQueryCategoryTreeResponse proto.InternalMessageInfo

func (m *WMQueryCategoryTreeResponse) GetCode() *WMCode {
	if m != nil {
		return m.Code
	}
	return nil
}

func (m *WMQueryCategoryTreeResponse) GetData() []*WMCategoryList {
	if m != nil {
		return m.Data
	}
	return nil
}

type WMCategoryList struct {
	CategoryId           int64    `protobuf:"varint,1,opt,name=categoryId,proto3" json:"categoryId"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMCategoryList) Reset()         { *m = WMCategoryList{} }
func (m *WMCategoryList) String() string { return proto.CompactTextString(m) }
func (*WMCategoryList) ProtoMessage()    {}
func (*WMCategoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{28}
}

func (m *WMCategoryList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMCategoryList.Unmarshal(m, b)
}
func (m *WMCategoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMCategoryList.Marshal(b, m, deterministic)
}
func (m *WMCategoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMCategoryList.Merge(m, src)
}
func (m *WMCategoryList) XXX_Size() int {
	return xxx_messageInfo_WMCategoryList.Size(m)
}
func (m *WMCategoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_WMCategoryList.DiscardUnknown(m)
}

var xxx_messageInfo_WMCategoryList proto.InternalMessageInfo

func (m *WMCategoryList) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *WMCategoryList) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type WMQueryChildrenCategoryDto struct {
	CategoryId           int64    `protobuf:"varint,1,opt,name=categoryId,proto3" json:"categoryId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMQueryChildrenCategoryDto) Reset()         { *m = WMQueryChildrenCategoryDto{} }
func (m *WMQueryChildrenCategoryDto) String() string { return proto.CompactTextString(m) }
func (*WMQueryChildrenCategoryDto) ProtoMessage()    {}
func (*WMQueryChildrenCategoryDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{29}
}

func (m *WMQueryChildrenCategoryDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMQueryChildrenCategoryDto.Unmarshal(m, b)
}
func (m *WMQueryChildrenCategoryDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMQueryChildrenCategoryDto.Marshal(b, m, deterministic)
}
func (m *WMQueryChildrenCategoryDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMQueryChildrenCategoryDto.Merge(m, src)
}
func (m *WMQueryChildrenCategoryDto) XXX_Size() int {
	return xxx_messageInfo_WMQueryChildrenCategoryDto.Size(m)
}
func (m *WMQueryChildrenCategoryDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WMQueryChildrenCategoryDto.DiscardUnknown(m)
}

var xxx_messageInfo_WMQueryChildrenCategoryDto proto.InternalMessageInfo

func (m *WMQueryChildrenCategoryDto) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type WMQueryChildrenCategoryResponse struct {
	Code                 *WMCode           `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	CategoryList         []*WMCategoryList `protobuf:"bytes,2,rep,name=categoryList,proto3" json:"categoryList"`
	CategoryId           int64             `protobuf:"varint,3,opt,name=categoryId,proto3" json:"categoryId"`
	Title                string            `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *WMQueryChildrenCategoryResponse) Reset()         { *m = WMQueryChildrenCategoryResponse{} }
func (m *WMQueryChildrenCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*WMQueryChildrenCategoryResponse) ProtoMessage()    {}
func (*WMQueryChildrenCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{30}
}

func (m *WMQueryChildrenCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMQueryChildrenCategoryResponse.Unmarshal(m, b)
}
func (m *WMQueryChildrenCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMQueryChildrenCategoryResponse.Marshal(b, m, deterministic)
}
func (m *WMQueryChildrenCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMQueryChildrenCategoryResponse.Merge(m, src)
}
func (m *WMQueryChildrenCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_WMQueryChildrenCategoryResponse.Size(m)
}
func (m *WMQueryChildrenCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WMQueryChildrenCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WMQueryChildrenCategoryResponse proto.InternalMessageInfo

func (m *WMQueryChildrenCategoryResponse) GetCode() *WMCode {
	if m != nil {
		return m.Code
	}
	return nil
}

func (m *WMQueryChildrenCategoryResponse) GetCategoryList() []*WMCategoryList {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

func (m *WMQueryChildrenCategoryResponse) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *WMQueryChildrenCategoryResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type WMFindFreightTemplateListDto struct {
	//  非必填  商品id，如果传入，额外返回该商品使用的模板
	GoodsId              int64    `protobuf:"varint,1,opt,name=goodsId,proto3" json:"goodsId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMFindFreightTemplateListDto) Reset()         { *m = WMFindFreightTemplateListDto{} }
func (m *WMFindFreightTemplateListDto) String() string { return proto.CompactTextString(m) }
func (*WMFindFreightTemplateListDto) ProtoMessage()    {}
func (*WMFindFreightTemplateListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{31}
}

func (m *WMFindFreightTemplateListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMFindFreightTemplateListDto.Unmarshal(m, b)
}
func (m *WMFindFreightTemplateListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMFindFreightTemplateListDto.Marshal(b, m, deterministic)
}
func (m *WMFindFreightTemplateListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMFindFreightTemplateListDto.Merge(m, src)
}
func (m *WMFindFreightTemplateListDto) XXX_Size() int {
	return xxx_messageInfo_WMFindFreightTemplateListDto.Size(m)
}
func (m *WMFindFreightTemplateListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WMFindFreightTemplateListDto.DiscardUnknown(m)
}

var xxx_messageInfo_WMFindFreightTemplateListDto proto.InternalMessageInfo

func (m *WMFindFreightTemplateListDto) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

// 查询在售商品详情
type WMShopGoodsGetDto struct {
	Vid                  int64    `protobuf:"varint,1,opt,name=vid,proto3" json:"vid"`
	GoodsId              int64    `protobuf:"varint,2,opt,name=goodsId,proto3" json:"goodsId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMShopGoodsGetDto) Reset()         { *m = WMShopGoodsGetDto{} }
func (m *WMShopGoodsGetDto) String() string { return proto.CompactTextString(m) }
func (*WMShopGoodsGetDto) ProtoMessage()    {}
func (*WMShopGoodsGetDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{32}
}

func (m *WMShopGoodsGetDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMShopGoodsGetDto.Unmarshal(m, b)
}
func (m *WMShopGoodsGetDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMShopGoodsGetDto.Marshal(b, m, deterministic)
}
func (m *WMShopGoodsGetDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMShopGoodsGetDto.Merge(m, src)
}
func (m *WMShopGoodsGetDto) XXX_Size() int {
	return xxx_messageInfo_WMShopGoodsGetDto.Size(m)
}
func (m *WMShopGoodsGetDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WMShopGoodsGetDto.DiscardUnknown(m)
}

var xxx_messageInfo_WMShopGoodsGetDto proto.InternalMessageInfo

func (m *WMShopGoodsGetDto) GetVid() int64 {
	if m != nil {
		return m.Vid
	}
	return 0
}

func (m *WMShopGoodsGetDto) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

//    查询商品列表
type WeiMShopGoodsGetListDto struct {
	//    分页页码
	PageNum int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum"`
	//    每页包含的数据条数
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//    查询条件
	QueryParameter *QueryParameter `protobuf:"bytes,3,opt,name=queryParameter,proto3" json:"queryParameter"`
	//    基础信息
	BasicInfo            *BasicInfo `protobuf:"bytes,4,opt,name=basicInfo,proto3" json:"basicInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *WeiMShopGoodsGetListDto) Reset()         { *m = WeiMShopGoodsGetListDto{} }
func (m *WeiMShopGoodsGetListDto) String() string { return proto.CompactTextString(m) }
func (*WeiMShopGoodsGetListDto) ProtoMessage()    {}
func (*WeiMShopGoodsGetListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{33}
}

func (m *WeiMShopGoodsGetListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeiMShopGoodsGetListDto.Unmarshal(m, b)
}
func (m *WeiMShopGoodsGetListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeiMShopGoodsGetListDto.Marshal(b, m, deterministic)
}
func (m *WeiMShopGoodsGetListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeiMShopGoodsGetListDto.Merge(m, src)
}
func (m *WeiMShopGoodsGetListDto) XXX_Size() int {
	return xxx_messageInfo_WeiMShopGoodsGetListDto.Size(m)
}
func (m *WeiMShopGoodsGetListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WeiMShopGoodsGetListDto.DiscardUnknown(m)
}

var xxx_messageInfo_WeiMShopGoodsGetListDto proto.InternalMessageInfo

func (m *WeiMShopGoodsGetListDto) GetPageNum() int64 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *WeiMShopGoodsGetListDto) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WeiMShopGoodsGetListDto) GetQueryParameter() *QueryParameter {
	if m != nil {
		return m.QueryParameter
	}
	return nil
}

func (m *WeiMShopGoodsGetListDto) GetBasicInfo() *BasicInfo {
	if m != nil {
		return m.BasicInfo
	}
	return nil
}

//    查询条件
type QueryParameter struct {
	//    商品状态。状态包括：0-上架；1-下架；2-已售罄。
	GoodsStatus int64 `protobuf:"varint,1,opt,name=goodsStatus,proto3" json:"goodsStatus"`
	//    商品分组 ID，传入二级分组 ID 可以通过 weimob_shop/goods/classify/getList 接口获取该 ID。
	ClassifyId int64 `protobuf:"varint,2,opt,name=classifyId,proto3" json:"classifyId"`
	//    查询商品的搜索内容，支持商品名称搜索。
	Search string `protobuf:"bytes,3,opt,name=search,proto3" json:"search"`
	//    排序方式，取值范围[1,4]。1-商品销量销量 2-上下架时间 3-商品价格 4-商品排序值。
	Sort                 int64    `protobuf:"varint,4,opt,name=sort,proto3" json:"sort"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryParameter) Reset()         { *m = QueryParameter{} }
func (m *QueryParameter) String() string { return proto.CompactTextString(m) }
func (*QueryParameter) ProtoMessage()    {}
func (*QueryParameter) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{34}
}

func (m *QueryParameter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryParameter.Unmarshal(m, b)
}
func (m *QueryParameter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryParameter.Marshal(b, m, deterministic)
}
func (m *QueryParameter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryParameter.Merge(m, src)
}
func (m *QueryParameter) XXX_Size() int {
	return xxx_messageInfo_QueryParameter.Size(m)
}
func (m *QueryParameter) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryParameter.DiscardUnknown(m)
}

var xxx_messageInfo_QueryParameter proto.InternalMessageInfo

func (m *QueryParameter) GetGoodsStatus() int64 {
	if m != nil {
		return m.GoodsStatus
	}
	return 0
}

func (m *QueryParameter) GetClassifyId() int64 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *QueryParameter) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *QueryParameter) GetSort() int64 {
	if m != nil {
		return m.Sort
	}
	return 0
}

// 微盟所有的字符串返回
type WMCommonStrResponse struct {
	//    http的code
	HttpCode int64 `protobuf:"varint,1,opt,name=httpCode,proto3" json:"httpCode"`
	// 返回的错误信息
	Error string `protobuf:"bytes,2,opt,name=error,proto3" json:"error"`
	// 请求的数据
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WMCommonStrResponse) Reset()         { *m = WMCommonStrResponse{} }
func (m *WMCommonStrResponse) String() string { return proto.CompactTextString(m) }
func (*WMCommonStrResponse) ProtoMessage()    {}
func (*WMCommonStrResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_790d6c5aeba894aa, []int{35}
}

func (m *WMCommonStrResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WMCommonStrResponse.Unmarshal(m, b)
}
func (m *WMCommonStrResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WMCommonStrResponse.Marshal(b, m, deterministic)
}
func (m *WMCommonStrResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WMCommonStrResponse.Merge(m, src)
}
func (m *WMCommonStrResponse) XXX_Size() int {
	return xxx_messageInfo_WMCommonStrResponse.Size(m)
}
func (m *WMCommonStrResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WMCommonStrResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WMCommonStrResponse proto.InternalMessageInfo

func (m *WMCommonStrResponse) GetHttpCode() int64 {
	if m != nil {
		return m.HttpCode
	}
	return 0
}

func (m *WMCommonStrResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WMCommonStrResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func init() {
	proto.RegisterType((*WarehouseListPageResponse)(nil), "et.WarehouseListPageResponse")
	proto.RegisterType((*WarehouseListPageRequest)(nil), "et.WarehouseListPageRequest")
	proto.RegisterType((*SimpleWarehouse)(nil), "et.SimpleWarehouse")
	proto.RegisterType((*PushStockRequest)(nil), "et.PushStockRequest")
	proto.RegisterType((*SimpleProduct)(nil), "et.SimpleProduct")
	proto.RegisterType((*SimpleSku)(nil), "et.SimpleSku")
	proto.RegisterType((*CommonResponse)(nil), "et.CommonResponse")
	proto.RegisterType((*WeiMengOrderLogisticsUpdateRequest)(nil), "et.WeiMengOrderLogisticsUpdateRequest")
	proto.RegisterType((*WeiMengOrderDetailRequest)(nil), "et.WeiMengOrderDetailRequest")
	proto.RegisterType((*WeiMengOrderDetailResponse)(nil), "et.WeiMengOrderDetailResponse")
	proto.RegisterType((*WeiMengRefundDetailRequest)(nil), "et.WeiMengRefundDetailRequest")
	proto.RegisterType((*WeiMengRefundDetailResponse)(nil), "et.WeiMengRefundDetailResponse")
	proto.RegisterType((*WmShopGoodSCreateDto)(nil), "et.WmShopGoodSCreateDto")
	proto.RegisterType((*BasicInfo)(nil), "et.BasicInfo")
	proto.RegisterType((*PerformanceWay)(nil), "et.PerformanceWay")
	proto.RegisterType((*DeliveryList)(nil), "et.DeliveryList")
	proto.RegisterType((*SkuList)(nil), "et.SkuList")
	proto.RegisterType((*SkuSpecValueList)(nil), "et.SkuSpecValueList")
	proto.RegisterType((*PointDeductRule)(nil), "et.PointDeductRule")
	proto.RegisterType((*WmShopGoodSCreateResponse)(nil), "et.WmShopGoodSCreateResponse")
	proto.RegisterType((*WMCode)(nil), "et.WMCode")
	proto.RegisterType((*WMData)(nil), "et.WMData")
	proto.RegisterType((*WMSkuList)(nil), "et.WMSkuList")
	proto.RegisterType((*WMQueryUploadImgDto)(nil), "et.WMQueryUploadImgDto")
	proto.RegisterType((*WMQueryUploadImgResponse)(nil), "et.WMQueryUploadImgResponse")
	proto.RegisterType((*WMUrlData)(nil), "et.WMUrlData")
	proto.RegisterType((*WMUrlInfo)(nil), "et.WMUrlInfo")
	proto.RegisterType((*WMQueryCategoryTreeResponse)(nil), "et.WMQueryCategoryTreeResponse")
	proto.RegisterType((*WMCategoryList)(nil), "et.WMCategoryList")
	proto.RegisterType((*WMQueryChildrenCategoryDto)(nil), "et.WMQueryChildrenCategoryDto")
	proto.RegisterType((*WMQueryChildrenCategoryResponse)(nil), "et.WMQueryChildrenCategoryResponse")
	proto.RegisterType((*WMFindFreightTemplateListDto)(nil), "et.WMFindFreightTemplateListDto")
	proto.RegisterType((*WMShopGoodsGetDto)(nil), "et.WMShopGoodsGetDto")
	proto.RegisterType((*WeiMShopGoodsGetListDto)(nil), "et.WeiMShopGoodsGetListDto")
	proto.RegisterType((*QueryParameter)(nil), "et.QueryParameter")
	proto.RegisterType((*WMCommonStrResponse)(nil), "et.WMCommonStrResponse")
}

func init() { proto.RegisterFile("et/weimob_cloud.proto", fileDescriptor_790d6c5aeba894aa) }

var fileDescriptor_790d6c5aeba894aa = []byte{
	// 2197 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x6e, 0xdc, 0xc8,
	0x11, 0xc6, 0x0c, 0xf5, 0x37, 0x2d, 0x59, 0x96, 0xdb, 0xb2, 0x97, 0x1e, 0xd9, 0xb2, 0x96, 0x89,
	0xbd, 0x42, 0x12, 0xc8, 0x81, 0x13, 0x24, 0x9b, 0x45, 0x16, 0x89, 0x57, 0x5a, 0x3b, 0xc2, 0x7a,
	0xec, 0x59, 0x8e, 0xec, 0x41, 0xb0, 0x48, 0x04, 0x8a, 0x6c, 0xcd, 0x34, 0x44, 0xb2, 0xb9, 0xcd,
	0xa6, 0x04, 0xe5, 0x90, 0x77, 0xc8, 0x35, 0xc7, 0xbc, 0x40, 0x4e, 0x01, 0x02, 0xe4, 0x90, 0xdb,
	0xbe, 0x49, 0x9e, 0x20, 0x2f, 0x10, 0x54, 0x75, 0xb3, 0xf9, 0x33, 0x33, 0x5a, 0x1f, 0x72, 0x9a,
	0xa9, 0xaf, 0x9a, 0xd5, 0x55, 0xd5, 0xf5, 0xd7, 0x4d, 0xee, 0x31, 0xf5, 0xec, 0x8a, 0xf1, 0x44,
	0x9c, 0x9d, 0x86, 0xb1, 0x28, 0xa2, 0x83, 0x4c, 0x0a, 0x25, 0x68, 0x97, 0xa9, 0xfe, 0xce, 0x44,
	0x88, 0x49, 0xcc, 0x9e, 0x21, 0x72, 0x56, 0x9c, 0x3f, 0x63, 0x49, 0xa6, 0xae, 0xf5, 0x02, 0xef,
	0xaf, 0x1d, 0xf2, 0x60, 0x1c, 0x48, 0x36, 0x15, 0x45, 0xce, 0x5e, 0xf3, 0x5c, 0x0d, 0x83, 0x09,
	0xf3, 0x59, 0x9e, 0x89, 0x34, 0x67, 0xf4, 0x13, 0xb2, 0x04, 0x98, 0xdb, 0xd9, 0x73, 0xf6, 0xd7,
	0x9f, 0xdf, 0x3d, 0x60, 0xea, 0x60, 0xc4, 0x93, 0x2c, 0x66, 0xf6, 0x13, 0x1f, 0x17, 0xd0, 0x3e,
	0x59, 0x83, 0x0f, 0x47, 0xfc, 0x4f, 0xcc, 0xed, 0xee, 0x75, 0xf6, 0x97, 0x7d, 0x4b, 0x53, 0x97,
	0xac, 0xc2, 0xff, 0x37, 0x45, 0xe2, 0x3a, 0xc8, 0x2a, 0x49, 0xba, 0x4b, 0xc8, 0x89, 0x50, 0x41,
	0x7c, 0x28, 0x8a, 0x54, 0xb9, 0x4b, 0x7b, 0x9d, 0x7d, 0xc7, 0xaf, 0x21, 0xde, 0x77, 0x1d, 0xe2,
	0xce, 0x51, 0xee, 0xdb, 0x82, 0xb5, 0xb6, 0xec, 0x2c, 0xde, 0xb2, 0xdb, 0xdc, 0xf2, 0x87, 0xe4,
	0x96, 0x95, 0x78, 0x72, 0x9d, 0x31, 0xa3, 0x52, 0x13, 0x04, 0xc5, 0x46, 0x2c, 0x90, 0xe1, 0x14,
	0x97, 0x2c, 0xe1, 0x92, 0x1a, 0x02, 0xf2, 0xbf, 0x62, 0xd7, 0x57, 0x42, 0x46, 0xee, 0xf2, 0x5e,
	0x67, 0xbf, 0xe7, 0x97, 0x24, 0xdd, 0x26, 0xcb, 0x6f, 0xe5, 0xe4, 0x38, 0x72, 0x57, 0xd0, 0x1a,
	0x4d, 0x78, 0xff, 0xee, 0x92, 0xdb, 0x2d, 0xc7, 0x51, 0x8f, 0x6c, 0x1c, 0xb1, 0x98, 0x5f, 0x32,
	0x79, 0x8d, 0xbb, 0x68, 0x1b, 0x1a, 0x58, 0x43, 0xdb, 0x37, 0x41, 0xa2, 0x7d, 0xdb, 0xf3, 0x9b,
	0x60, 0x63, 0xd5, 0xa1, 0x88, 0xb4, 0x4d, 0xf5, 0x55, 0x00, 0xd2, 0x3d, 0xb2, 0x6e, 0x81, 0xe3,
	0xc8, 0x18, 0x55, 0x87, 0xe8, 0x4f, 0xc9, 0xdd, 0x51, 0x71, 0x56, 0x57, 0x00, 0x0f, 0x5f, 0x5b,
	0x38, 0x8f, 0x05, 0x7e, 0x78, 0x11, 0x45, 0x92, 0xe5, 0x39, 0xda, 0xdb, 0xf3, 0x4b, 0x12, 0x76,
	0x1b, 0x04, 0x69, 0x30, 0x61, 0x12, 0xf5, 0x5e, 0x45, 0x6e, 0x1d, 0x02, 0x4f, 0x0d, 0xa7, 0x22,
	0x65, 0xee, 0x1a, 0xf2, 0x34, 0x41, 0xef, 0x93, 0x15, 0x9f, 0x25, 0x81, 0xbc, 0x70, 0x7b, 0x08,
	0x1b, 0xca, 0xfb, 0x47, 0x87, 0x6c, 0x0d, 0x8b, 0x7c, 0x3a, 0x52, 0x22, 0xbc, 0x28, 0x43, 0x60,
	0xc6, 0xf0, 0xce, 0x07, 0x18, 0xde, 0x9d, 0x35, 0xfc, 0x89, 0x09, 0x73, 0x07, 0xc3, 0xfc, 0x4e,
	0x15, 0xe6, 0x43, 0x29, 0xa2, 0x22, 0x54, 0x26, 0xc8, 0x1f, 0x92, 0xde, 0x8b, 0x58, 0x31, 0x59,
	0x0b, 0x8a, 0x0a, 0xa8, 0x4e, 0x7e, 0xb9, 0x7e, 0xf2, 0x43, 0x72, 0xab, 0x21, 0x0a, 0x84, 0x98,
	0xbf, 0xc7, 0x11, 0xea, 0xeb, 0xf8, 0x15, 0x40, 0x3f, 0x36, 0x9a, 0x74, 0x51, 0x93, 0x5b, 0x95,
	0x26, 0xa3, 0x8b, 0x42, 0x6b, 0xe1, 0xfd, 0x92, 0xf4, 0x2c, 0x04, 0x9b, 0x8e, 0x2e, 0x0a, 0x2b,
	0x49, 0x13, 0x88, 0x82, 0x9f, 0x8c, 0xad, 0x9a, 0xf0, 0x4e, 0xc8, 0xe6, 0xa1, 0x48, 0x12, 0x91,
	0xda, 0xf4, 0xa6, 0x64, 0x29, 0x2c, 0xdd, 0xb6, 0xec, 0xe3, 0x7f, 0xf8, 0x96, 0x49, 0x29, 0xa4,
	0x09, 0x35, 0x4d, 0xc0, 0x41, 0x27, 0x2c, 0xcf, 0x83, 0x49, 0x19, 0x5c, 0x25, 0xe9, 0x7d, 0xd7,
	0x25, 0xde, 0x98, 0xf1, 0x01, 0x4b, 0x27, 0x6f, 0x65, 0xc4, 0xe4, 0x6b, 0x31, 0xe1, 0xb9, 0xe2,
	0x61, 0xfe, 0x2e, 0x8b, 0x02, 0x65, 0xb3, 0xf5, 0x01, 0x59, 0x13, 0xc0, 0x3e, 0x4d, 0x85, 0xd1,
	0x75, 0x15, 0xe9, 0x37, 0x82, 0xee, 0x93, 0x2d, 0x9e, 0x9f, 0xe6, 0x59, 0xcc, 0xd5, 0x69, 0x16,
	0x84, 0x17, 0xb0, 0x09, 0x6c, 0xbe, 0xe6, 0x6f, 0xf2, 0x7c, 0x04, 0xf0, 0x50, 0xa3, 0xf4, 0x09,
	0xd9, 0x3c, 0x2f, 0xe2, 0x73, 0x1e, 0xc7, 0xa7, 0x09, 0x53, 0x53, 0x11, 0x95, 0xd9, 0x6b, 0xd0,
	0x01, 0x82, 0xf4, 0x31, 0x59, 0x8f, 0x4c, 0xa4, 0xc2, 0x76, 0x4b, 0xa8, 0x30, 0x29, 0xa1, 0x37,
	0x82, 0x3e, 0x27, 0xf7, 0xec, 0x82, 0x50, 0x24, 0x59, 0x90, 0xc2, 0x6f, 0xc4, 0xca, 0x50, 0x2f,
	0x99, 0x87, 0x9a, 0x87, 0x51, 0x34, 0xef, 0x9b, 0x14, 0x42, 0x7b, 0x65, 0xee, 0x37, 0x18, 0xe2,
	0x5b, 0xc4, 0xb9, 0xe4, 0x11, 0x06, 0xbf, 0xe3, 0xc3, 0x5f, 0x70, 0xc3, 0x25, 0x8f, 0x4e, 0x15,
	0x44, 0xd0, 0x9a, 0xae, 0x4c, 0x97, 0x3c, 0x82, 0xf8, 0xf1, 0xbe, 0x21, 0x0f, 0xea, 0x7e, 0x3c,
	0x62, 0x2a, 0xe0, 0x71, 0xe9, 0xbe, 0x1f, 0x90, 0x5b, 0xda, 0x7d, 0x91, 0x48, 0x02, 0x9e, 0xe6,
	0x58, 0x91, 0x97, 0xfd, 0x0d, 0x04, 0x8f, 0x34, 0xd6, 0xf0, 0x71, 0xb7, 0xe1, 0x63, 0xef, 0x8f,
	0xa4, 0x3f, 0x4f, 0xf8, 0x0d, 0x71, 0x40, 0xc9, 0x52, 0x14, 0xa8, 0x00, 0x05, 0x6d, 0xf8, 0xf8,
	0xff, 0x86, 0x28, 0xf8, 0xd4, 0xca, 0xf7, 0xd9, 0x79, 0x91, 0x46, 0x4d, 0xed, 0xfb, 0x64, 0x4d,
	0xf2, 0xc9, 0x54, 0xe5, 0x36, 0x50, 0x2d, 0xed, 0x9d, 0x92, 0x9d, 0xb9, 0x5f, 0xfe, 0xdf, 0x54,
	0xfb, 0xcf, 0x1a, 0xd9, 0x1e, 0x27, 0xa3, 0xa9, 0xc8, 0x5e, 0x09, 0x11, 0x8d, 0x0e, 0x25, 0x0b,
	0x14, 0x3b, 0x52, 0x82, 0xfe, 0x98, 0xf4, 0xce, 0x82, 0x9c, 0x87, 0xc7, 0xe9, 0xb9, 0x8e, 0x49,
	0x93, 0x70, 0x5f, 0x94, 0xa0, 0x5f, 0xf1, 0x41, 0xfe, 0x99, 0x0c, 0xd2, 0xc8, 0x14, 0x10, 0xc7,
	0x2f, 0x49, 0xe8, 0x15, 0x61, 0xa0, 0xd8, 0x44, 0xc8, 0xeb, 0x63, 0x1d, 0x90, 0x8e, 0x5f, 0x43,
	0xe8, 0x3e, 0xb9, 0x1d, 0x31, 0x48, 0x6f, 0xcc, 0x42, 0x5b, 0x3b, 0x1c, 0xbf, 0x0d, 0xeb, 0x95,
	0xe7, 0x41, 0x11, 0xab, 0xe3, 0x24, 0x98, 0xb0, 0x77, 0x32, 0x36, 0x01, 0xd9, 0x86, 0xa1, 0x52,
	0x4f, 0x84, 0x88, 0xf2, 0xc3, 0x38, 0xc8, 0x73, 0x7e, 0x7e, 0x7d, 0x1c, 0x61, 0xd5, 0x58, 0xd9,
	0x73, 0xf6, 0x1d, 0x7f, 0x1e, 0x0b, 0xca, 0x0e, 0xc2, 0x47, 0x2c, 0x0f, 0x4d, 0x35, 0xae, 0x00,
	0x28, 0xa4, 0x48, 0xd8, 0x7d, 0xd7, 0xf6, 0x1c, 0x28, 0xa4, 0x0d, 0x10, 0xf4, 0x43, 0xe0, 0x84,
	0x25, 0x59, 0x1c, 0x28, 0x28, 0xa6, 0x3d, 0x6d, 0x49, 0x0b, 0xb6, 0xbb, 0xa1, 0xb5, 0x44, 0x17,
	0x39, 0x0b, 0xd0, 0x03, 0x42, 0x91, 0x78, 0xcf, 0x23, 0x26, 0xec, 0x96, 0xeb, 0xa8, 0xd4, 0x1c,
	0x8e, 0xd5, 0x0e, 0x51, 0x58, 0xba, 0xa1, 0xcb, 0x7c, 0x03, 0x84, 0x3d, 0x79, 0xca, 0xd5, 0x28,
	0x88, 0x59, 0xee, 0xde, 0xd2, 0x7b, 0x5a, 0x00, 0xba, 0x2d, 0x4f, 0x2f, 0x05, 0x0f, 0xd9, 0x09,
	0x57, 0x31, 0x73, 0x37, 0x51, 0x44, 0x03, 0xa3, 0x3f, 0x22, 0x5b, 0x75, 0x1a, 0x95, 0xbf, 0x8d,
	0x82, 0x66, 0x70, 0xdc, 0x2d, 0x3f, 0x0c, 0xd2, 0x11, 0x8b, 0x63, 0x77, 0x0b, 0xab, 0x55, 0x05,
	0x40, 0x4c, 0xf0, 0x7c, 0x50, 0xc4, 0x8a, 0x8f, 0x2e, 0x0a, 0xf7, 0x0e, 0xb2, 0x6b, 0x08, 0x24,
	0x04, 0xcf, 0xdf, 0xa6, 0x31, 0x4f, 0x99, 0x4b, 0x91, 0x6b, 0x69, 0x68, 0x57, 0x31, 0x4f, 0xb8,
	0x1a, 0x5d, 0x71, 0x15, 0x4e, 0xdd, 0xbb, 0xc8, 0xae, 0x43, 0xf4, 0x29, 0xd9, 0x14, 0x85, 0x62,
	0xf2, 0x15, 0x9e, 0x33, 0x64, 0xc7, 0x36, 0x5a, 0xd3, 0x42, 0xe9, 0x67, 0x64, 0x33, 0x63, 0xf2,
	0x5c, 0xc8, 0x24, 0x48, 0x43, 0x36, 0x0e, 0xae, 0xdd, 0x7b, 0x18, 0xe5, 0x14, 0xa2, 0x7c, 0xd8,
	0xe0, 0xf8, 0xad, 0x95, 0x60, 0x41, 0xce, 0xe2, 0xf8, 0x5d, 0xca, 0xa1, 0x4f, 0xdd, 0xd7, 0x51,
	0x5d, 0x21, 0xf4, 0x09, 0x59, 0xcd, 0x2f, 0x0a, 0x8c, 0xba, 0x8f, 0xb0, 0x57, 0xad, 0x63, 0xaf,
	0xd2, 0x90, 0x5f, 0xf2, 0xc0, 0xed, 0x79, 0x71, 0xf6, 0xca, 0xc6, 0x82, 0x8b, 0x82, 0x1a, 0x18,
	0x38, 0x23, 0x2f, 0xce, 0xf4, 0xb1, 0x3c, 0x40, 0x43, 0x2c, 0x0d, 0xdd, 0x48, 0x05, 0xd0, 0x54,
	0xfb, 0xba, 0xbf, 0x21, 0x81, 0x28, 0x2e, 0xdf, 0xd1, 0x3d, 0x0a, 0x09, 0xa8, 0xb6, 0x57, 0x3c,
	0x72, 0x1f, 0xea, 0x6a, 0x7b, 0xc5, 0x23, 0xfa, 0x13, 0x72, 0xc7, 0xc4, 0xb8, 0xae, 0xcd, 0x03,
	0xf0, 0xd5, 0x23, 0xe4, 0xcf, 0x32, 0xe8, 0xe7, 0xe4, 0x76, 0x26, 0x78, 0xaa, 0x8e, 0x30, 0x2d,
	0xfd, 0x22, 0x66, 0xee, 0x2e, 0xfa, 0x0b, 0xe7, 0xde, 0x61, 0x93, 0xe5, 0xb7, 0xd7, 0x7a, 0x8f,
	0x48, 0xcf, 0x56, 0x8e, 0xb2, 0xf2, 0x77, 0x6c, 0xe5, 0xf7, 0x5e, 0x92, 0xcd, 0xa6, 0xcb, 0xe9,
	0xcf, 0xc9, 0x46, 0xd9, 0x34, 0x6a, 0x43, 0xf6, 0x16, 0x6c, 0x76, 0x54, 0xc3, 0xfd, 0xc6, 0x2a,
	0xef, 0x6f, 0x9d, 0x6a, 0x6e, 0x44, 0x17, 0xef, 0x12, 0xdb, 0xda, 0x6c, 0x79, 0xad, 0x21, 0x90,
	0x6d, 0x55, 0xeb, 0x8b, 0xd8, 0x68, 0xca, 0x33, 0x5b, 0xc4, 0xe6, 0x70, 0xe0, 0xc8, 0xa2, 0xfa,
	0x5c, 0xaa, 0x2b, 0x5a, 0x03, 0x83, 0x3d, 0x55, 0x55, 0x04, 0xcc, 0xe0, 0x5e, 0x21, 0xde, 0xbf,
	0xba, 0x64, 0xd5, 0xc4, 0x02, 0xc6, 0x7a, 0x99, 0xe3, 0x7a, 0x3e, 0xb3, 0x34, 0x64, 0x51, 0x28,
	0x72, 0x35, 0x94, 0x3c, 0x64, 0x46, 0xa5, 0x0a, 0x80, 0x4c, 0xc8, 0x2f, 0x0a, 0xac, 0x8f, 0xe5,
	0xe5, 0xc1, 0xf1, 0xeb, 0x10, 0xac, 0x80, 0xe9, 0x90, 0x19, 0x09, 0x5a, 0x91, 0x3a, 0x04, 0x3b,
	0xe4, 0x01, 0x4c, 0x5f, 0xc0, 0xd7, 0x93, 0x59, 0x05, 0xc0, 0xb4, 0x79, 0x29, 0xe2, 0xc2, 0x74,
	0x71, 0xc7, 0x37, 0x14, 0xe0, 0x57, 0x0c, 0x3a, 0x94, 0xe9, 0xdd, 0x86, 0x02, 0xdf, 0x60, 0x8e,
	0x8d, 0x2e, 0x0a, 0xcc, 0x3b, 0x3d, 0xba, 0x36, 0x30, 0xfa, 0x5b, 0xb2, 0x05, 0x2a, 0x66, 0x2c,
	0x7c, 0x1f, 0xc4, 0x85, 0x1e, 0xa1, 0x7b, 0x78, 0xb4, 0xdb, 0x26, 0x45, 0x1a, 0x3c, 0x7f, 0x66,
	0xb5, 0xf7, 0x9a, 0x6c, 0xb5, 0x57, 0x81, 0x46, 0x79, 0xc6, 0x42, 0x7b, 0xc2, 0x86, 0x42, 0x1f,
	0x95, 0x0b, 0xed, 0xb1, 0xd6, 0x21, 0xef, 0xbf, 0x1d, 0x72, 0xbb, 0x15, 0xbc, 0x50, 0xc9, 0x45,
	0xc6, 0xd2, 0x1a, 0x6c, 0xc4, 0xb6, 0x61, 0xb0, 0x18, 0xe7, 0xe8, 0x34, 0xe2, 0x8a, 0x8b, 0xd4,
	0x6c, 0xd0, 0xc0, 0x40, 0x87, 0x22, 0x67, 0x47, 0x3c, 0x0f, 0xf1, 0x1e, 0x67, 0xce, 0xa9, 0x06,
	0xe9, 0x98, 0x02, 0x79, 0x2f, 0x92, 0xda, 0x55, 0xaf, 0x81, 0x41, 0x55, 0xd3, 0xb4, 0x15, 0xa4,
	0x8f, 0xab, 0x85, 0x42, 0x37, 0xd0, 0x08, 0x17, 0x29, 0x06, 0xa8, 0x3e, 0xba, 0x26, 0x88, 0xd3,
	0x54, 0xbb, 0xe9, 0xdb, 0xa1, 0x62, 0xb7, 0x36, 0x54, 0xac, 0x3f, 0x27, 0x70, 0x2c, 0xe3, 0x01,
	0x1c, 0x9e, 0x19, 0x30, 0x76, 0xcd, 0x80, 0xe1, 0xd4, 0xf9, 0x47, 0x81, 0x0a, 0xf4, 0xb0, 0xe1,
	0x7d, 0x46, 0x56, 0xf4, 0x7a, 0x18, 0x0b, 0x98, 0x94, 0x61, 0x75, 0xf7, 0x28, 0x49, 0x38, 0x30,
	0x26, 0x65, 0x92, 0x4f, 0xcc, 0x20, 0x6d, 0x28, 0xef, 0x2b, 0xf8, 0x16, 0x64, 0xd1, 0x4f, 0xaa,
	0x12, 0xda, 0xa9, 0xc6, 0xfd, 0xf1, 0x60, 0xa6, 0x88, 0xba, 0x64, 0x55, 0x37, 0x62, 0x3b, 0x7b,
	0x18, 0xd2, 0xfb, 0x92, 0xf4, 0xec, 0xfa, 0x99, 0xe0, 0xec, 0xcc, 0x09, 0xce, 0x6d, 0xb2, 0x9c,
	0xe3, 0x7d, 0x41, 0x0b, 0xd2, 0x84, 0xf7, 0x39, 0xb9, 0x3b, 0x1e, 0x7c, 0x5d, 0x30, 0x79, 0xfd,
	0x2e, 0x8b, 0x45, 0x10, 0x1d, 0x27, 0x13, 0x18, 0x90, 0x28, 0x59, 0xc2, 0x09, 0x57, 0x0b, 0xc2,
	0xff, 0x80, 0x9d, 0xf3, 0xb8, 0xbc, 0x88, 0xe2, 0x7f, 0xef, 0x0f, 0xc4, 0x6d, 0x7f, 0x6e, 0x5d,
	0xfd, 0xb1, 0x71, 0x65, 0x6d, 0xbe, 0x1a, 0x0f, 0xde, 0xc9, 0xb8, 0xf2, 0xa6, 0x3d, 0x8d, 0xee,
	0xfc, 0xd3, 0xf0, 0x7e, 0x07, 0x46, 0x9a, 0x4f, 0x60, 0xff, 0xbc, 0xbc, 0xf1, 0x3b, 0x3e, 0xfe,
	0x07, 0x47, 0x16, 0x32, 0xc6, 0x31, 0xae, 0xdb, 0xda, 0x06, 0xc7, 0xb8, 0x92, 0xeb, 0x8d, 0x8c,
	0x24, 0x2c, 0xd1, 0xf3, 0xac, 0xdb, 0x22, 0x4e, 0x21, 0x63, 0x63, 0x1c, 0xfc, 0xc5, 0x6e, 0xcc,
	0x26, 0x41, 0x3c, 0x52, 0x81, 0x2a, 0xf2, 0x32, 0xb6, 0x6b, 0x90, 0xc7, 0xc8, 0x8e, 0xb1, 0xfe,
	0xd0, 0x0c, 0x7d, 0x27, 0x92, 0x7d, 0x78, 0xac, 0x3d, 0xb5, 0xc3, 0xac, 0x53, 0xb6, 0xe6, 0xf1,
	0xa0, 0x94, 0x84, 0x71, 0xa0, 0x63, 0xee, 0x25, 0xd9, 0x6c, 0xe2, 0xad, 0xc1, 0xb3, 0x33, 0x33,
	0x78, 0xda, 0x2e, 0xd9, 0xad, 0x75, 0x49, 0xef, 0xd7, 0xa4, 0x5f, 0xaa, 0x3b, 0xe5, 0x71, 0x24,
	0x59, 0x5a, 0x0a, 0x85, 0x23, 0xff, 0x1e, 0x99, 0xde, 0xdf, 0x3b, 0xe4, 0xf1, 0x82, 0xcf, 0x3f,
	0xd8, 0xe2, 0x5f, 0x90, 0x8d, 0xb0, 0x66, 0xc7, 0x0d, 0x96, 0x37, 0xd6, 0x7d, 0xef, 0xa0, 0x6d,
	0xed, 0x5d, 0xaa, 0xdb, 0xfb, 0x29, 0x79, 0x38, 0x1e, 0xbc, 0xe4, 0x69, 0xf4, 0x52, 0x62, 0x0d,
	0x2f, 0xa7, 0x54, 0x10, 0x09, 0x16, 0xd7, 0x92, 0xab, 0xd3, 0x4c, 0xae, 0xdf, 0x90, 0x3b, 0xe3,
	0x41, 0x59, 0x42, 0xf2, 0x57, 0x0c, 0x97, 0xcf, 0x34, 0xf6, 0x1b, 0xb2, 0xf3, 0x9f, 0x1d, 0xf2,
	0x11, 0xdc, 0x6d, 0xea, 0x32, 0x6a, 0xdb, 0x66, 0xe6, 0x85, 0xca, 0x6c, 0x6b, 0x48, 0xe8, 0x97,
	0x59, 0xfd, 0x29, 0xcd, 0xf1, 0x2d, 0x0d, 0x13, 0xdd, 0xb7, 0xe0, 0xfb, 0x61, 0x20, 0x83, 0x84,
	0x29, 0x26, 0x4d, 0x89, 0x42, 0xe7, 0x7d, 0xdd, 0xe0, 0xf8, 0xad, 0x95, 0xcd, 0xeb, 0xce, 0xd2,
	0xcd, 0xd7, 0x1d, 0xef, 0xcf, 0x64, 0xb3, 0x29, 0x0e, 0x12, 0x01, 0xed, 0x32, 0x89, 0xa0, 0x95,
	0xae, 0x43, 0x78, 0x3e, 0xf6, 0xd2, 0x61, 0x54, 0xaf, 0x21, 0xd8, 0xc2, 0xf0, 0x09, 0xcd, 0xdc,
	0xd0, 0x0c, 0x85, 0x29, 0x2d, 0x64, 0xd9, 0x14, 0xf0, 0xbf, 0xf7, 0x0d, 0x54, 0x24, 0xfd, 0x5a,
	0x31, 0x52, 0xd2, 0x86, 0x56, 0x9f, 0xac, 0x4d, 0x95, 0xca, 0x6c, 0x79, 0x73, 0x7c, 0x4b, 0x2f,
	0x78, 0xb8, 0xa0, 0xb5, 0x52, 0xde, 0xd3, 0xa9, 0xf4, 0xfc, 0x2f, 0xcb, 0x84, 0x8e, 0xf1, 0xad,
	0xf4, 0x30, 0x16, 0x45, 0x34, 0x62, 0xf2, 0x12, 0x86, 0x81, 0x5f, 0x91, 0x9e, 0x7d, 0x61, 0xa2,
	0xd8, 0xab, 0xdb, 0x0f, 0x4e, 0xfd, 0xfb, 0x07, 0xfa, 0x2d, 0xf5, 0xa0, 0x7c, 0x4b, 0x3d, 0xf8,
	0x32, 0xc9, 0xd4, 0x35, 0x1d, 0x92, 0x3b, 0x33, 0xef, 0x94, 0xf4, 0x21, 0x46, 0xf4, 0x82, 0xe7,
	0xcb, 0xfe, 0xa3, 0x05, 0x5c, 0x63, 0xe9, 0x7b, 0x72, 0xef, 0x15, 0x53, 0xb3, 0x77, 0x76, 0xaa,
	0xbf, 0x5b, 0xf4, 0x50, 0xd0, 0xdf, 0x5d, 0xc4, 0x36, 0x72, 0x7f, 0x4f, 0xee, 0x57, 0x72, 0xeb,
	0x37, 0x6e, 0x5a, 0xff, 0x72, 0xce, 0x25, 0xbe, 0xff, 0x78, 0x21, 0xdf, 0x8a, 0xde, 0xb9, 0xe1,
	0x21, 0x88, 0x3e, 0x6d, 0x6b, 0x36, 0xff, 0xa5, 0xa8, 0x8f, 0xb1, 0xdc, 0x7a, 0xa8, 0x7a, 0x4d,
	0xb6, 0xe7, 0x25, 0x12, 0xdd, 0x29, 0x65, 0xce, 0x49, 0xb1, 0xfe, 0x47, 0x65, 0xe5, 0x69, 0x47,
	0xd1, 0x1b, 0x72, 0xb7, 0xf1, 0x8d, 0x9e, 0x0e, 0xa8, 0x8b, 0xeb, 0xe7, 0xbc, 0x14, 0x98, 0xb3,
	0x5a, 0x38, 0x4e, 0x7c, 0x41, 0xb6, 0xda, 0x3a, 0xd0, 0x7b, 0xa6, 0x97, 0x37, 0xcb, 0xc7, 0x42,
	0x9d, 0xce, 0x56, 0x30, 0xa2, 0x7e, 0xf6, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x1b, 0x51, 0x84,
	0x92, 0xc8, 0x17, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// WeimobCloudServiceClient is the client API for WeimobCloudService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WeimobCloudServiceClient interface {
	//推送库存
	PushStock(ctx context.Context, in *PushStockRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	//分页查询仓库列表
	WarehouseListPage(ctx context.Context, in *WarehouseListPageRequest, opts ...grpc.CallOption) (*WarehouseListPageResponse, error)
	//获取订单详情
	GetWeiMengOrderDetail(ctx context.Context, in *WeiMengOrderDetailRequest, opts ...grpc.CallOption) (*WeiMengOrderDetailResponse, error)
	//获取退款单详情
	GetWeiMengRefundDetail(ctx context.Context, in *WeiMengRefundDetailRequest, opts ...grpc.CallOption) (*WeiMengRefundDetailResponse, error)
	//订单物流信息更新
	WeiMengOrderLogisticsUpdate(ctx context.Context, in *WeiMengOrderLogisticsUpdateRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 查询商品列表接口
	WeiMShopGoodsGetList(ctx context.Context, in *WeiMShopGoodsGetListDto, opts ...grpc.CallOption) (*WMCommonStrResponse, error)
	// 商品创建接口
	WeiMShopGoodsCreate(ctx context.Context, in *WmShopGoodSCreateDto, opts ...grpc.CallOption) (*WmShopGoodSCreateResponse, error)
	// 查询微盟商品在售详情
	WeiMShopGoodsGet(ctx context.Context, in *WMShopGoodsGetDto, opts ...grpc.CallOption) (*WMCommonStrResponse, error)
}

type weimobCloudServiceClient struct {
	cc *grpc.ClientConn
}

func NewWeimobCloudServiceClient(cc *grpc.ClientConn) WeimobCloudServiceClient {
	return &weimobCloudServiceClient{cc}
}

func (c *weimobCloudServiceClient) PushStock(ctx context.Context, in *PushStockRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/PushStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) WarehouseListPage(ctx context.Context, in *WarehouseListPageRequest, opts ...grpc.CallOption) (*WarehouseListPageResponse, error) {
	out := new(WarehouseListPageResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/WarehouseListPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) GetWeiMengOrderDetail(ctx context.Context, in *WeiMengOrderDetailRequest, opts ...grpc.CallOption) (*WeiMengOrderDetailResponse, error) {
	out := new(WeiMengOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/GetWeiMengOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) GetWeiMengRefundDetail(ctx context.Context, in *WeiMengRefundDetailRequest, opts ...grpc.CallOption) (*WeiMengRefundDetailResponse, error) {
	out := new(WeiMengRefundDetailResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/GetWeiMengRefundDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) WeiMengOrderLogisticsUpdate(ctx context.Context, in *WeiMengOrderLogisticsUpdateRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/WeiMengOrderLogisticsUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) WeiMShopGoodsGetList(ctx context.Context, in *WeiMShopGoodsGetListDto, opts ...grpc.CallOption) (*WMCommonStrResponse, error) {
	out := new(WMCommonStrResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/WeiMShopGoodsGetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) WeiMShopGoodsCreate(ctx context.Context, in *WmShopGoodSCreateDto, opts ...grpc.CallOption) (*WmShopGoodSCreateResponse, error) {
	out := new(WmShopGoodSCreateResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/WeiMShopGoodsCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weimobCloudServiceClient) WeiMShopGoodsGet(ctx context.Context, in *WMShopGoodsGetDto, opts ...grpc.CallOption) (*WMCommonStrResponse, error) {
	out := new(WMCommonStrResponse)
	err := c.cc.Invoke(ctx, "/et.WeimobCloudService/WeiMShopGoodsGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WeimobCloudServiceServer is the server API for WeimobCloudService service.
type WeimobCloudServiceServer interface {
	//推送库存
	PushStock(context.Context, *PushStockRequest) (*empty.Empty, error)
	//分页查询仓库列表
	WarehouseListPage(context.Context, *WarehouseListPageRequest) (*WarehouseListPageResponse, error)
	//获取订单详情
	GetWeiMengOrderDetail(context.Context, *WeiMengOrderDetailRequest) (*WeiMengOrderDetailResponse, error)
	//获取退款单详情
	GetWeiMengRefundDetail(context.Context, *WeiMengRefundDetailRequest) (*WeiMengRefundDetailResponse, error)
	//订单物流信息更新
	WeiMengOrderLogisticsUpdate(context.Context, *WeiMengOrderLogisticsUpdateRequest) (*CommonResponse, error)
	// 查询商品列表接口
	WeiMShopGoodsGetList(context.Context, *WeiMShopGoodsGetListDto) (*WMCommonStrResponse, error)
	// 商品创建接口
	WeiMShopGoodsCreate(context.Context, *WmShopGoodSCreateDto) (*WmShopGoodSCreateResponse, error)
	// 查询微盟商品在售详情
	WeiMShopGoodsGet(context.Context, *WMShopGoodsGetDto) (*WMCommonStrResponse, error)
}

// UnimplementedWeimobCloudServiceServer can be embedded to have forward compatible implementations.
type UnimplementedWeimobCloudServiceServer struct {
}

func (*UnimplementedWeimobCloudServiceServer) PushStock(ctx context.Context, req *PushStockRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushStock not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) WarehouseListPage(ctx context.Context, req *WarehouseListPageRequest) (*WarehouseListPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseListPage not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) GetWeiMengOrderDetail(ctx context.Context, req *WeiMengOrderDetailRequest) (*WeiMengOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWeiMengOrderDetail not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) GetWeiMengRefundDetail(ctx context.Context, req *WeiMengRefundDetailRequest) (*WeiMengRefundDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWeiMengRefundDetail not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) WeiMengOrderLogisticsUpdate(ctx context.Context, req *WeiMengOrderLogisticsUpdateRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeiMengOrderLogisticsUpdate not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) WeiMShopGoodsGetList(ctx context.Context, req *WeiMShopGoodsGetListDto) (*WMCommonStrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeiMShopGoodsGetList not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) WeiMShopGoodsCreate(ctx context.Context, req *WmShopGoodSCreateDto) (*WmShopGoodSCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeiMShopGoodsCreate not implemented")
}
func (*UnimplementedWeimobCloudServiceServer) WeiMShopGoodsGet(ctx context.Context, req *WMShopGoodsGetDto) (*WMCommonStrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeiMShopGoodsGet not implemented")
}

func RegisterWeimobCloudServiceServer(s *grpc.Server, srv WeimobCloudServiceServer) {
	s.RegisterService(&_WeimobCloudService_serviceDesc, srv)
}

func _WeimobCloudService_PushStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).PushStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/PushStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).PushStock(ctx, req.(*PushStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_WarehouseListPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseListPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).WarehouseListPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/WarehouseListPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).WarehouseListPage(ctx, req.(*WarehouseListPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_GetWeiMengOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).GetWeiMengOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/GetWeiMengOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).GetWeiMengOrderDetail(ctx, req.(*WeiMengOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_GetWeiMengRefundDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengRefundDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).GetWeiMengRefundDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/GetWeiMengRefundDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).GetWeiMengRefundDetail(ctx, req.(*WeiMengRefundDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_WeiMengOrderLogisticsUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMengOrderLogisticsUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).WeiMengOrderLogisticsUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/WeiMengOrderLogisticsUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).WeiMengOrderLogisticsUpdate(ctx, req.(*WeiMengOrderLogisticsUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_WeiMShopGoodsGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeiMShopGoodsGetListDto)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/WeiMShopGoodsGetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsGetList(ctx, req.(*WeiMShopGoodsGetListDto))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_WeiMShopGoodsCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WmShopGoodSCreateDto)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/WeiMShopGoodsCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsCreate(ctx, req.(*WmShopGoodSCreateDto))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeimobCloudService_WeiMShopGoodsGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WMShopGoodsGetDto)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WeimobCloudService/WeiMShopGoodsGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeimobCloudServiceServer).WeiMShopGoodsGet(ctx, req.(*WMShopGoodsGetDto))
	}
	return interceptor(ctx, in, info, handler)
}

var _WeimobCloudService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.WeimobCloudService",
	HandlerType: (*WeimobCloudServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushStock",
			Handler:    _WeimobCloudService_PushStock_Handler,
		},
		{
			MethodName: "WarehouseListPage",
			Handler:    _WeimobCloudService_WarehouseListPage_Handler,
		},
		{
			MethodName: "GetWeiMengOrderDetail",
			Handler:    _WeimobCloudService_GetWeiMengOrderDetail_Handler,
		},
		{
			MethodName: "GetWeiMengRefundDetail",
			Handler:    _WeimobCloudService_GetWeiMengRefundDetail_Handler,
		},
		{
			MethodName: "WeiMengOrderLogisticsUpdate",
			Handler:    _WeimobCloudService_WeiMengOrderLogisticsUpdate_Handler,
		},
		{
			MethodName: "WeiMShopGoodsGetList",
			Handler:    _WeimobCloudService_WeiMShopGoodsGetList_Handler,
		},
		{
			MethodName: "WeiMShopGoodsCreate",
			Handler:    _WeimobCloudService_WeiMShopGoodsCreate_Handler,
		},
		{
			MethodName: "WeiMShopGoodsGet",
			Handler:    _WeimobCloudService_WeiMShopGoodsGet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/weimob_cloud.proto",
}

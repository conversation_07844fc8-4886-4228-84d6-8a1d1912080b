package models

import (
	"time"
)

//重试状态 0：未重试 1重试中 2重试成功 3重试失败
const (
	TaskStatusRedoWait = iota
	TaskStatusRedoing
	TaskStatusRedid
	TaskStatusRedoFail
)

// 重试类型 1：正向订单重推巨益OMS,2逆向推送OMS， 3 退子龙打折扣，4退子龙门店券
const (
	RedoTypeOMSOrder = iota + 1
	RedoTypeOMSOrderRefund
	RedoTypeDiscountCardRefund
	RedoTypeWasteCoupon
)

// 重试日志表
type OrderRedoTask struct {
	Id           int       `xorm:"not null pk autoincr comment('自增id') INT(11)"`
	OrderSn      string    `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	RedoType     int       `xorm:"default comment('业务类型')  "` // 重试类型 1：正向订单重推巨益OMS
	Params       string    `xorm:"default comment('请求参数')"`   // 重试参数
	TaskStatus   int       `xorm:"default comment('重试状态') "`  // 重试状态 0：未重试 1重试中 2重试成功 3重试失败
	FailInfo     string    `xorm:"default comment('失败原因') "`
	RedoCount    int       `xorm:"default comment('已重试次数') "`
	NextRedoTime time.Time `xorm:"default comment('下次执行时间') "`
	CreateTime   time.Time `xorm:"default comment('创建日期') DATETIME created"`
	UpdateTime   time.Time `xorm:"default 'NULL' comment('修改日期') DATETIME updated"`
}

func (m *OrderRedoTask) TableName() string {
	return "order_redo_task"
}

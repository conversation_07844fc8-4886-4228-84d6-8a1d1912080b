package models

import (
	"time"
)

type PinOrderGroup struct {
	Id               int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	PinOrderSn       string    `xorm:"default 'NULL' comment('拼团订单ID（取的是订单中心订单编号逻辑）') VARCHAR(55)"`
	ParentPinOrderSn string    `xorm:"default 'NULL' comment('团长订单ID') VARCHAR(55)"`
	IsPinHead        int       `xorm:"default 0 comment('是否团长 1是 0 否') INT(1)"`
	IsMachine        int       `xorm:"default 0 comment('是否凑团 1是 0 否') INT(1)"`
	OpenId           string    `xorm:"default 'NULL' comment('小程序的用户id') VARCHAR(55)"`
	OpenName         string    `xorm:"default 'NULL' comment('用户昵称') VARCHAR(55)"`
	UserId           string    `xorm:"default 'NULL' comment('用户id') VARCHAR(55)"`
	Portrait         string    `xorm:"default 'NULL' comment('头像链接') VARCHAR(255)"`
	TotalPrice       int       `xorm:"default 0 comment('总价格') INT(11)"`
	PayPrice         int       `xorm:"default 0 comment('支付价格') INT(11)"`
	BusinessJson     string    `xorm:"default 'NULL' comment('业务JSON') TEXT"`
	SubmitJson       string    `xorm:"default 'NULL' comment('提交到订单中心json') TEXT"`
	Status           int       `xorm:"default 0 comment('订单状态：0 已取消 10未支付 20 拼团进行中 30拼团成功 40拼团失败') INT(2)"`
	IsVirtual        int       `xorm:"default 0 comment('是否虚拟订单 0 否 1 是') INT(1)"`
	CancelReason     string    `xorm:"default '''' comment('取消原因') VARCHAR(255)"`
	CancelTime       time.Time `xorm:"default 'current_timestamp()' comment('取消时间') DATETIME"`
	ChannelId        int       `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	UserAgent        int       `xorm:"default 0 comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它') INT(1)"`
	CreateTime       time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime       time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
	IsCoupon         int       `xorm:"default 0 comment('0默认 1已发优惠券') TINYINT(2)"`
	PinGroupId       int       `xorm:"default -1 comment('拼团的活动id') INT(11)"`
	OrderPayType     string    `xorm:"not null default '''' comment('交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)') VARCHAR(10)"`
}

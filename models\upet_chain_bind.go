package models

import (
	"time"
)

type UpetChainBind struct {
	Id                int32     `xorm:"not null pk autoincr INT(10)"`
	MemberId          int32     `xorm:"not null comment('分销员id') index INT(10)"`
	ChainId           int32     `xorm:"not null comment('门店id') INT(10)"`
	CashRatio         float64   `xorm:"not null default 0.10 DECIMAL(4,2)"`
	ChainMemberMobile string    `xorm:"not null comment('门店手机号') index VARCHAR(11)"`
	BindingTime       time.Time `xorm:"default 'current_timestamp()' comment('绑定时间') DATETIME"`
	CreateTime        time.Time `xorm:"default 'current_timestamp()' DATETIME"`
	UpdateTime        time.Time `xorm:"default 'current_timestamp()' DATETIME"`
}

package models

import (
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"time"
)

type OrderProduct struct {
	Id                   int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn              string    `xorm:"not null default '''' comment('主订单号') index VARCHAR(50)"`
	SkuId                string    `xorm:"not null default '''' comment('商品skuid') VARCHAR(50)"`
	ParentSkuId          string    `xorm:"not null default '''' comment('组合商品父级skuid') VARCHAR(50)"`
	ChildrenSku          string    `xorm:"not null default '''' comment('组合商品子商品集合') TEXT"`
	ProductId            string    `xorm:"not null default '''' comment('商品id') VARCHAR(50)"`
	ProductName          string    `xorm:"not null default '''' comment('商品名称') VARCHAR(200)"`
	ProductType          int32     `xorm:"not null default 1 comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）') TINYINT(4)"`
	CombineType          int32     `xorm:"not null default 0 comment('组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟') TINYINT(4)"`
	ChannelCategoryName  string    `xorm:"default '' comment('渠道分类名称') varchar(100) 'channel_category_name'"`
	Image                string    `xorm:"not null default '''' comment('商品图片') VARCHAR(500)"`
	BarCode              string    `xorm:"not null default '''' comment('商品编码') VARCHAR(50)"`
	MarkingPrice         int32     `xorm:"not null default 0 comment('商品原单价') INT(11)"`
	DiscountPrice        int32     `xorm:"not null default 0 comment('商品优惠单价') INT(11)"`
	VipPrice             int32     `xorm:"not null default 0 comment('vip单价') INT(11)"`
	PayPrice             int32     `xorm:"not null default 0 comment('商品均摊后实际支付单价') INT(11)"`
	Number               int32     `xorm:"not null default 0 comment('数量') INT(11)"`
	Specs                string    `xorm:"not null default '''' comment('规格') VARCHAR(50)"`
	PaymentTotal         int32     `xorm:"not null default 0 comment('sku实付总金额，discount_price*number') INT(11)"`
	SkuPayTotal          int32     `xorm:"not null default 0 comment('包含平台优惠的sku实付总金额，payment_total+privilege_pt') INT(11)"`
	Privilege            int32     `xorm:"not null default 0 comment('商家优惠金额') INT(11)"`
	PrivilegePt          int32     `xorm:"not null default 0 comment('平台优惠金额') INT(11)"`
	PrivilegeTotal       int32     `xorm:"not null default 0 comment('总优惠金额') INT(11)"`
	Freight              int32     `xorm:"not null default 0 comment('sku分摊运费') INT(11)"`
	DeliverStatus        int32     `xorm:"not null default 0 comment('发货状态0未发货  1已发货') TINYINT(4)"`
	DeliverNum           int32     `xorm:"not null default 0 comment('发货数量') INT(11)"`
	RefundNum            int32     `xorm:"not null default 0 comment('退货数量') INT(11)"`
	GroupItemNum         int32     `xorm:"not null default 0 comment('组合子商品在单份组合中的售卖数量') INT(11)"`
	SubBizOrderId        string    `xorm:"not null default '''' comment('饿了么子订单ID') VARCHAR(200)"`
	ThirdSkuId           string    `xorm:"not null default '''' comment('第三方货号') VARCHAR(50)"`
	PromotionId          int64     `xorm:"not null default 0 comment('限时折扣活动id') INT(11)"`
	PromotionType        int32     `xorm:"not null default 1 comment('商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,8:第二件N折,9:拼团促销)') INT(11)"`
	IsDistribute         int32     `xorm:"not null default 0 comment('是否分销') TINYINT(4)"`
	IsSettlement         int       `xorm:"default NULL comment('是否生成佣金记录 1：是  0：否') TINYINT(1)"`
	TermType             int32     `xorm:"not null default 0 comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') TINYINT(4)"`
	TermValue            int32     `xorm:"not null default 0 comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT(11)"`
	VirtualInvalidRefund int32     `xorm:"not null default 0 comment('是否支持过期退款 1：是  0：否') TINYINT(4)"`
	CommissionRate       int32     `xorm:"not null default 0 comment('佣金比例，单位%') TINYINT(4)"`
	MallOrderProductId   int64     `xorm:"not null default 0 comment('商城订单商品主键id') INT(11)"`
	CreateTime           time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime           time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	WarehouseType        int32     `xorm:"not null default 0 comment('药品仓类型：0:默认否, 1:巨星药品仓') INT(11)"`
	IsPrescribedDrug     int32     `xorm:"default 0 comment('是否处方药 0否，1是') TINYINT(4)"`
	// 库位码
	LocationCode string `json:"location_code"`
	//购买类型：0 普通购买，1次卡，2赠品   101 买储值卡 102续储值卡 103买次卡 104续次卡
	BuyType int32 `json:"buy_type"`
	//重量
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
}

type OrderProductExd struct {
	OrderProduct `xorm:"extends"`
	Quantity     int32 `xorm:"not null default 0 comment('sku申请退款数量') INT(11)"`
}

func (op *OrderProduct) TableName() string {
	return "dc_order.order_product"
}

type OrderProductThirdSku struct {
	Id          int64  `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn     string `xorm:"not null default '''' comment('主订单号') index VARCHAR(50)"`
	SkuId       string `xorm:"not null default '''' comment('商品skuid') VARCHAR(50)"`
	ThirdSkuId  string `xorm:"not null default '''' comment('第三方货号') VARCHAR(50)"`
	CombineType int64  `xorm:"not null default '''' comment('组合商品类型') INT(11)"`
	PrivilegePt int64  `xorm:"not null default '''' comment('平台补贴') INT(11)"`
}

type OrderProductExt struct {
	OrderProduct  `xorm:"extends"`
	ParentOrderSn string `xorm:"not null default '''' comment('拆单前父订单号') index VARCHAR(50)"`
	IsVirtual     int32  `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(1)"`
}

func (product *OrderProduct) ToUpetDjOrderProductDto() *oc.UpetDjOrderProductDto {
	var dto = new(oc.UpetDjOrderProductDto)
	dto.ProductId = product.ProductId
	dto.ProductType = product.ProductType
	dto.ParentSkuId = product.ParentSkuId
	dto.SkuId = product.SkuId
	dto.ProductName = product.ProductName
	dto.ProductCount = product.Number
	dto.ProductPic = product.Image
	dto.ProductPrice = kit.FenToYuan(product.DiscountPrice)
	dto.ProductActualPrice = kit.FenToYuan(product.PayPrice)

	dto.ProductActaulMoney = kit.FenToYuan(product.PaymentTotal)
	dto.ProductSpecifica = product.Specs
	dto.PromotionId = int32(product.PromotionId)
	dto.IsPrescribedDrug = product.IsPrescribedDrug
	dto.MarkingPrice = kit.FenToYuan(product.MarkingPrice)
	if product.PromotionType == 4 {
		dto.PromotionId = 1
	} else if product.PromotionType == 1 {
		if product.VipPrice > 0 {
			dto.ProductActualPrice = kit.FenToYuan(product.VipPrice)
		} else {
			dto.PromotionId = 0
		}
	}
	return dto
}

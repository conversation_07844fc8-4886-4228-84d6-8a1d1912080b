package services

import "strings"

//转换成oms的退货单状态
func changeAfterOrderStatusToOms(status string) string {
	omsStatus := "UNKNOWN" //未知
	switch strings.ToLower(status) {
	case "waitagree": //待审核
		omsStatus = "WAIT_SELLER_AGREE"
	case "agree": //审核同意
		omsStatus = "WAIT_BUYER_RETURN_GOODS"
	case "refuse": //拒绝
		omsStatus = "SELLER_REFUSE_BUYER"
	case "invalid": //作废
		omsStatus = "CLOSED"
	case "unfinish": //待处理完成
		omsStatus = "WAIT_SELLER_CONFIRM_GOODS"
	case "finished": //处理完成
		omsStatus = "SUCCESS"
	case "agreeinvalid": //同意作废
		omsStatus = "CLOSED"
	case "refuseinvalid": //拒绝作废
		omsStatus = "CLOSED"
	default:
		omsStatus = ""
	}
	return omsStatus
}

//转换退货原因编码成退货原因
func changeAfterOrderReasonToOms(reasoncode, remark string) string {
	omsReason := ""
	switch reasoncode {
	case "01": //待审核
		omsReason = "无理由退换货"
	case "02": //审核同意
		omsReason = "质量问题"
	case "03": //拒绝
		omsReason = "损坏"
	case "04": //作废
		omsReason = "错发"
	case "05": //待处理完成
		omsReason = "漏发"
	default:
		omsReason = ""
	}

	//加上备注
	if len(omsReason) > 0 {
		if len(remark) > 0 {
			omsReason = omsReason + "," + remark
		} else {
			omsReason = omsReason
		}
	} else {
		omsReason = remark
	}
	return omsReason
}

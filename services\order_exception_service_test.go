package services

import (
	"context"
	"encoding/json"
	kit "github.com/tricobbler/rp-kit"
	pt "order-center/proto/oc"
	"reflect"
	"testing"
)

func TestOrderExceptionService_OrderStatusException(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pt.ExceptionOrderStatusRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *pt.ExceptionOrderStatusResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "OrderStatusException"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			app := pt.ExceptionOrderStatusRequest{}
			app.CourierPhone = "13826142230"
			app.CourierName = "ZX"
			app.DeliveryId = "183656"
			app.Status = 3
			got, err := ac.OrderStatusException(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderExceptionService.OrderStatusException() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderExceptionService.OrderStatusException() = %v, want %v", got, tt.want)
			}
		})
	}
}

//取消订单
func TestOrderExceptionService_OrderCancel(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx    context.Context
		params *pt.ExceptionOrderStatusRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.ExceptionOrderStatusResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "OrderCancel"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			app := pt.ExceptionOrderStatusRequest{}
			//app.OrderId = ""
			app.DeliveryId = "27055212301957422"
			app.MtPeisongId = "27055212301957422"
			app.CancelReason = "取货地址超区"
			app.CancelType = "系统取消"
			app.Status = 0
			app.ShopId = ""
			got, err := ac.OrderCancel(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderExceptionService.OrderCancel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderExceptionService.OrderCancel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderExceptionService_DistributionAgain(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pt.ExceptionOrderStatusRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "DistributionAgain",
			args: args{
				ctx: nil,
				params: &pt.ExceptionOrderStatusRequest{
					OrderId:    "4000000000507356",
					DeliveryId: "1061845396",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			got, err := ac.DistributionAgain(context.Background(), tt.args.params)
			if err != nil {
				t.Errorf("OrderExceptionService.DistributionAgain() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestOrderExceptionService_GoodArrive(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pt.ExceptionOrderStatusRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "送达",
			args: args{
				ctx: context.Background(),
				params: &pt.ExceptionOrderStatusRequest{
					CourierName:  "李四",
					CourierPhone: "13487456321",
					OrderId:      "4000000001096726",
					ShopId:       "AA0051",
					DeliveryId:   "1707465028",
					MtPeisongId:  "1617075203335001319",
					Status:       4,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			got, err := ac.GoodArrive(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("OrderExceptionService.GoodArrive() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestOrderExceptionService_OrderOwnDeliver(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx    context.Context
		params *pt.OrderExceptionRequest
	}
	str := `{"delivery_id":"1888953348","mt_peisong_id":"","order_id":"4100000012358609","exception_id":"","exception_code":0,"exception_descr":"配送平台的配送订单被取消","exception_time":"2022-01-13 11:46:18","courier_name":"yangyang","courier_phone":"131****8240","source":1,"order_status":2,"oid":"","old_order_sn":"","order_time":"","distribution_mode":"","channel_id":0,"store_master_id":0}`
	parem := new(pt.OrderExceptionRequest)
	_ = json.Unmarshal([]byte(str), parem)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.OrderExceptionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "OrderOwnDeliver",
			args: args{
				ctx:    context.Background(),
				params: parem,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			got, err := ac.OrderOwnDeliver(context.Background(), parem)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderExceptionService.OrderOwnDeliver() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderExceptionService.OrderOwnDeliver() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderExceptionService_OrderIsShowException(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx    context.Context
		params *pt.ExceptionOrderStatusRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.ExceptionOrderStatusResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			got, err := ac.OrderIsShowException(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderExceptionService.OrderIsShowException() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderExceptionService.OrderIsShowException() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderExceptionService_OrderExceptionList(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pt.OrderExceptionListRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "异常订单列表",
			args: args{
				ctx: context.Background(),
				params: &pt.OrderExceptionListRequest{
					PageSize:  10,
					Page:      1,
					Remarks:   "",
					OrderId:   "",
					MtOrderSn: "",
					Shopids:   []string{"CX0013"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			got, err := ac.OrderExceptionList(context.Background(), tt.args.params)
			if err != nil {
				t.Errorf("OrderExceptionService.OrderExceptionList() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestOrderExceptionService_OrderExceptionAdd(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx    context.Context
		params *pt.OrderExceptionRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.OrderExceptionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "异常添加"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := &OrderExceptionService{}
			app := pt.OrderExceptionRequest{}
			app.DeliveryId = "100010000620923920"
			app.MtPeisongId = "1750323628494001053"
			app.OrderId = "9964128299527887"
			app.ExceptionId = "1750323732"
			app.ExceptionCode = 10001
			app.ExceptionDescr = "顾客电话关机"
			app.ExceptionTime = "2025-06-19 17:02:12"
			app.CourierName = "张三"
			app.CourierPhone = "0000"
			app.Source = 3
			app.OrderStatus = 0
			app.IsHide = false
			got, err := ac.OrderExceptionAdd(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderExceptionService.OrderExceptionAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderExceptionService.OrderExceptionAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

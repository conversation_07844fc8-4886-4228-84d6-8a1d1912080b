syntax = "proto3";

package mk;

/////////////////////////////////// Request  //////////////////////////////////////////////////////////////////////

// 活动列表请求
message activityIdsRequest{
    // 编辑使用查询ID
    int32 id = 1;
    // 搜索SKU
    int32 sku = 2;
    // 搜索活动名
    string name = 3;
    // 搜索活动状态 -3待提交，-2待审核 1未开始 2进行中 3已结束 4已禁用
    int32 state = 4;

    int32 page_index = 5;
    int32 page_size  = 6;
}

// 活动删除请求
message IdRequest{
    // 活动ID
    string id = 1;
}

// 活动状态更新请求
message statusFreshActivityRequest{
    // 活动ID
    int32 id = 1;
    // 活动状态 1-启用 2-禁用
    int32 state = 2;
    // 操作人
    string operator = 3;
}

// 活动新增修改请求
message newActivityRequest{
  // 活动ID
  int32  id = 1;
  // 活动名称
  string ma_name = 2;
  // 活动开始时间
  string ma_start_date = 3;
  // 活动结束时间
  string ma_end_date = 4;
  // 分享开关：1-开 2-关
  int32 ma_share_status = 5;
  // 分享标题
  string ma_share_title = 6;
  // 分享图
  string ma_share_img = 7;
  // 操作人
  string ma_creater = 8;
  // 活动商品
  repeated maProduct ma_product = 9;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 10;
}

message maProduct {
  int32  id = 1;
  // 序号
  int32  ma_product_group = 2;
  // 商品skuid
  string  ma_product_sku = 3;
  // 助力价格、元
  string ma_product_price = 4;
  // 市场价、元
  string market_price = 8;
  // 采购价，元
  string purchase_price = 21;
  // 助力达标人数
  int32  ma_success_num = 5;
  // 助力有效时间(小时) 0-不限 1-限制
  int32  ma_valid_hour_limit = 6;
  // 助力有效时间(小时)
  int32  ma_valid_hour = 7;
  // 折扣率
  string discount_rate = 22;
  // 是否异常 1:异常 0：正常
  int32 is_exception = 19;
  // 标记状态 0不显示按钮、1显示标记非异常、2显示取消标记
  int32 mark_state = 20;
}

// 活动配置查询
message querySettingActivityRequest {
    string ma_setting_key = 1;
}

// 活动配置修改
message settingActivityRequest {
    //单人每日助力最大上限
    int32 ma_day_max = 1;
}


/////////////////////////////////// Response  //////////////////////////////////////////////////////////////////////

// 活动列表返回
message activityIdsResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated Activity details = 4;
    //返回总数，用于分页
    int32 total_count = 5;
}

// 活动列表数据集
message Activity {
  // 活动ID
  int32  id = 1;
  // 活动名称
  string ma_name = 2;
  // 活动开始时间
  string ma_start_date = 3;
  // 活动结束时间
  string ma_end_date = 4;
  // 执行人
  string ma_creater = 5;
  // 启用状态：-3待提交，-2待审核 1未开始 2进行中 3已结束 4已禁用
  int32 ma_status = 6;
  // 活动创建时间
  string ma_create_date = 7;
  // 分享开关：1-开 2-关
  int32 ma_share_status = 8;
  // 分享标题
  string ma_share_title = 9;
  // 分享图
  string ma_share_img = 10;
  // 是否过期：1-未过期 2-已过期
  int32 ma_expire = 11;

  // 异常商品计数
  int32 exception_count = 14;
  // 总商品数量
  int32 total_count = 15;

  // 审核原因
  string check_reason = 16;

  // 活动商品SKU拼接
  string ma_product_sku = 12;
  // 活动详情活动商品
  repeated maProduct ma_product = 13;
}

// 活动配置返回
message settingActivityResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated ActivitySetting details = 4;
}

message ActivitySetting {
    int32 ma_day_max = 1;
}

// 优惠券新增修改请求
message CouponActivityNewRequest{
    // 活动ID
    int32  id = 1;
    // 业务类型：1-预约挂号
    int32 business_type = 2;
    //发券场景：1-预约挂号成功,2-商城支付成功,3-会员卡续费成功
    int32 coupon_scene = 3;
    //优惠券类型：1-商城券,2-门店券,3-本地生活券,4-阿闻平台券
    int32 coupon_type = 4;
    //优惠券ID
    string coupon_id = 5;
    // 活动开始时间
    string start_date = 6;
    // 活动结束时间
    string end_date = 7;
    // 适用渠道
    string channel = 8;
    // 操作人
    string ca_creater = 9;
    // 启用状态：0-未开始 1-已开始 2-已结束 3-已终止
    int32 ca_status = 10;
    // 弹窗背景图
    string ca_img_url = 11;
    // 微页面路径
    string ca_path = 12;
}

// 优惠券列表请求
message couponListRequest{
    //优惠券ID
    int32 id = 1;
    // 优惠券类型
    int32 coupon_type = 2;
    // 发券场景
    int32 coupon_scene = 3;
    //业务类型
    int32 business_type = 4;
    // 启用状态：1-未开始 2-已开始 3-已结束 4-已终止
    int32 state = 5;
    //开始时间
    string stime = 6;
    //结束时间
    string etime = 7;
    //适用渠道
    string channel = 8;

    int32 page_index = 9;
    int32 page_size = 10;
}

// 优惠券列表返回
message couponListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated CouponData details = 4;
    //返回总数，用于分页
    int32 total_count = 5;
}

// 优惠券列表数据集
message CouponData {
    // 索引ID
    int32  id = 1;
    // 业务类型
    string ca_business_type = 2;
    //业务类型文字说明
    string business_type_text = 3;
    // 发券场景
    string ca_coupon_scene = 4;
    // 发券场景文字说明
    string coupon_scene_text = 5;
    // 优惠券类型
    string ca_coupon_type = 6;
    // 优惠券类型文字说明
    string coupon_type_text = 7;
    // 优惠券ID
    string ca_coupon_id = 8;
    // 活动开始时间
    string ca_start_date = 9;
    // 活动结束时间
    string ca_end_date = 10;
    // 活动创建时间
    string ca_create_date = 11;
    // 适用渠道
    string ca_channel = 12;
    // 创建者
    string ca_creater = 13;
    // 启用状态：1-未开始 2-已开始 3-已结束 4-已终止
    int32 ca_status = 14;
    // 发放数量
    int32 ca_numbers = 15;
   // 弹窗背景图
    string ca_img_url = 16;
    // 微页面路径
    string ca_path = 17;
}

//优惠列表字段
message VoucherList {
    //优惠券id
    int32 VoucherId = 1;
    //开始时间
    int32 VoucherStartDate = 2;
    //结束时间
    int32 VoucherEndDate = 3;
}
// 领取优惠券列表请求
message VoucherListRequest {
    //用户名
    string username = 1;
    // 券类型
    int32 type = 2;
    //开始时间
    string stime = 3;
    //结束时间
    string etime = 4;
    // 手机号
    string mobile = 5;
    // 优惠券ID
    string couponid = 6;

    int32 page_index = 7;
    int32 page_size = 8;
}
// 领取优惠券列表返回
message VoucherListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated VoucherData details = 4;
    //返回总数，用于分页
    int32 total_count = 5;
}
// 领取优惠券列表数据集
message VoucherData {
    // 索引ID
    int32 VoucherId = 1;
    // 用户名
    string MemberName = 2;
    // 手机号
    string MemberMobile = 3;
    // 领取时间
    string VoucherActiveDate = 4;
    // 券类型
    string VoucherFrom = 5;
    // 券ID
    int32 VoucherTId = 6;
}
//流浪救助用户列表
message userListRequest {
    // 手机号
    string  member_phone = 1;
    // 用户uuid
    int32 member_id = 2;
   // 查询状态：0-黑名单 1-全部
    int32 member_state = 3;
    // 分页参数
    int32 page_index = 4;
    int32 page_size = 5;

}

// 流浪救助用户列表
message userListResponse {

     int32 code = 1;
        string message = 2;
        string error = 3;
        repeated userData details = 4;
        //返回总数，用于分页
        int32 total_count = 5;

}
//流浪救助用户列表
message userData{
    int32 member_id = 1;
    string nick_name = 2;
    string member_mobile = 3;
    string donated_weight =4;
    int32 free_donated =5;
    int32 state = 6;
    string state_text = 7;

}

//流浪救助用户修改
message userRequest {
    //id
    int32  member_id = 1;
    // 状态
    int32 member_state = 2;//10正常，20黑名单
}

// 流浪救助用户修改
message userResponse {
     int32 code = 1;
     string message = 2;
}

//二维码列表
message qrcodeRequest {

    int32 page_index = 1;
    int32 page_size = 2;
    int32   id = 3;

}

// 二维码
message qrcodeResponse {

     int32 code = 1;
        string message = 2;
        string error = 3;
        repeated qrcodeData details = 4;
        //返回总数，用于分页
        int32 total_count = 5;
        string qrcode_name =6;
        int32 qr_id=7;//待启用id
        string start_name=8;//待启用id

}
//二维码
message qrcodeData{
    int32 id = 1;
    string name = 2;
    string qrcode = 3;
    string add_time =4;
    string donate_path =5;
    string donate_mini_qrcode = 6;
    int32  state = 7;
    string  state_text = 8;

}

//二维码新增,编辑
message qrcodeAddRequest {
    string name =1;
    string qrcode=2;
    string donate_path =3;
    int32 id=4;


}

message qrcodeAddResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
}

//二维码启用
message qrcodeSetRequest {
    int32 id =1;

}
// 二维码启用
message qrcodeSetResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
}

//救助站列表
message salvationRequest {

    // 分页参数
    int32 page_index = 1;
    int32 page_size = 2;
    int32 id = 3;

}
message salvationResponse {

     int32 code = 1;
        string message = 2;
        string error = 3;
        repeated salvationData details = 4;
        //返回总数，用于分页
        int32 total_count = 5;

}

message salvationData{
    int32 id = 1;
    string name = 2;
    string weight = 3;
    string current_weight =4;
    string add_time =5;
    string finish_time = 6;
    string stop_time = 7;
    int32 state =8;
    string percent =9;
    string state_text =10;
    string percentage = 11;
}
//救助站新增、编辑
message salvationAddRequest {
    int32 id =1;
    string name = 2;
    string weight=3;

}
message salvationAddResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
}
//设置救助站停止募集
message salvationSetRequest {
    int32 id =1;
    int32 status = 2;//status =1,停止募集，2重新启用募集

}
message salvationSetResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
}

//查看收货凭证
message salvationReceivingRequest {
    int32 id =1;
}
message salvationReceivingResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
     string receipt_certs =4;
}


//救助站上传、编辑收货凭证
message salvationReceivingAddRequest {
    int32 id =1;
    string receipt_certs = 2;
    string type = 3;

}
message salvationReceivingAddResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
     string receipt_certs = 4;
}

//设置用户捐赠限制
message donateSetRequest {
    string free_donate_weight = 1;
    string  donate_rate= 2;
    int32 type = 3;
     string goods_id =4;
     string activity_start_time =5;
     string activity_pause_time =6;
     string activity_end_time =7;//活动结束时间

}
message donateSetResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
     string free_donate_weight = 4;
     string  donate_rate= 5;
     string  goods_id= 6;
     string activity_start_time =7;
     string activity_pause_time =8;
     string activity_end_time =9;
}
//访问限制
message qrcodeLimitRequest {
        int32 id = 1;

}
message qrcodeLimitResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
       repeated qrcodeLimitData details = 4;
       int32 id = 5;

}
message qrcodeLimitData {
    int32 id = 1;
     int32 day_show_chat_qrcode_limit = 2;
     int32 show_chat_qrcode_limit = 3;
     int32 type = 4;
     string  type_text= 5;
}

//设置用户访问二维码个数限制
message qrcodeLimitEditRequest {
    int32 day_show_chat_qrcode_limit = 1;
    int32 show_chat_qrcode_limit = 2;
    int32 id = 3;
    int32 status = 4;
}
message qrcodeLimitEditResponse {
     int32 code = 1;
     string message = 2;
     string error = 3;
     int32 day_show_chat_qrcode_limit = 4;
     int32  show_chat_qrcode_limit= 5;
     int32 id = 6;
}
package services

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/et"
	"order-center/proto/oc"
	"strconv"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type OrderExceptionService struct {
	CommonService
}

// 异常配送修改状态
func (oe OrderExceptionService) OrderStatusException(ctx context.Context, params *oc.ExceptionOrderStatusRequest) (*oc.ExceptionOrderStatusResponse, error) {
	glog.Info("修改异常订单状态进入:" + kit.JsonEncode(params))
	var out oc.ExceptionOrderStatusResponse
	myEngine := GetDBConn()

	var buffer bytes.Buffer
	buffer.WriteString("update order_exception set order_status=? ")
	if params.CourierPhone != "" {
		buffer.WriteString(",courier_name='")
		buffer.WriteString(params.CourierName + "'")
	}
	if params.CourierPhone != "" {
		buffer.WriteString(",courier_phone='")
		buffer.WriteString(params.CourierPhone + "'")
	}
	if params.Status == 3 {
		buffer.WriteString(",exception_descr='商家自配'")
	}
	if params.Status == 4 {
		buffer.WriteString(",is_show=0")
	}
	buffer.WriteString(" where delivery_id = ? ")

	_, err := myEngine.Exec(buffer.String(), params.Status, params.DeliveryId)
	if err != nil {
		glog.Error("修改异常订单状态异常:" + params.OrderId + err.Error())
		out.Code = 400
		out.Error = err.Error()
		return &out, err
	}
	out.Code = 200
	out.Error = ""
	return &out, nil
}

// 取消配送  OK
func (oe OrderExceptionService) OrderCancel(ctx context.Context, params *oc.ExceptionOrderStatusRequest) (*oc.ExceptionOrderStatusResponse, error) {
	glog.Info("异常订单取消:" + kit.JsonEncode(params))
	var out oc.ExceptionOrderStatusResponse
	//更新状态
	db := GetDBConn()

	orderException := new(models.OrderException)
	ok, err := db.SQL("select delivery_id,order_sn,distribution_mode from order_exception where delivery_id=?", params.DeliveryId).Get(orderException)
	if err != nil {
		out.Error = err.Error() + "订单查询错误"
		out.Code = 400
		return &out, nil
	}

	etClient := et.GetExternalClient()

	inpar := et.MpOrderDeleteRequest{}
	//是否是美团配送
	isMTLogistics := false
	//zxadd 判断如果是美团配送的，直接重新发起美团配送

	if orderException.DistributionMode == "美团专配" {
		if !ok {
			out.Message = "该订单不存在"
			out.Code = 400
			return &out, nil
		}

		storeMasterId, err := GetAppChannelByOrderSn(orderException.DeliveryId)
		if err != nil {
			glog.Error("order-center-OrderCancel,", "GetAppChannelByOrderSn", orderException.DeliveryId, err)
			out.Code = 400
			out.Message = "获取店铺主体信息失败"
			return &out, nil
		}

		//如果在这些里面，说明他属于美团配送，否则就还是走原来的自配
		inpar.DeliveryId = cast.ToInt64(orderException.DeliveryId)
		isMTLogistics = true
		mtOrderReques := et.MtOrderStatusRequest{
			OrderId:       orderException.DeliveryId,
			StoreMasterId: storeMasterId,
		}

		res, err := etClient.MtOrder.MtLogisticsCancel(etClient.Ctx, &mtOrderReques)
		if err != nil {
			glog.Error("取消美团配送出错 ", orderException.DeliveryId, " err:", err.Error())
			out.Code = 400
			out.Message = res.Error
			return &out, nil
		}
		if res.Code != 200 {
			glog.Error("取消美团配送出错 ", orderException.DeliveryId, " err:", res.Message+res.Error)
			out.Code = 400
			out.Message = res.Message + res.Error
			return &out, nil
		}
	}
	//自己定义美团跑腿ID  todo 发起美团跑腿的话，修改订单配送ID为8888
	if orderException.DistributionMode == "美团跑腿" {
		isMTLogistics = true
	}

	appChannel := 0
	db.Table("dc_order.order_main").Select("app_channel").Where("order_sn=? or order_sn=?", orderException.OrderSn, orderException.OrderSn).Get(&appChannel)

	if !isMTLogistics {
		//调用美团的取消接口
		inpar.DeliveryId = cast.ToInt64(params.DeliveryId)
		inpar.MtPeisongId = params.MtPeisongId
		inpar.CancelReasonId = "399"
		inpar.CancelReason = params.CancelType + " " + params.CancelReason
		inpar.AppChannel = cast.ToString(appChannel)
		inpar.OrderSn = orderException.OrderSn
		res, err := etClient.MPServer.MpOrderDelete(etClient.Ctx, &inpar)
		if err != nil {
			out.Code = 400
			out.Error = err.Error()
			return &out, nil
		}
		if res.Code != 200 {
			out.Code = 400
			out.Message = res.Error
			return &out, nil
		}
	}

	upsql := "UPDATE dc_order.order_exception SET order_status = 2,exception_descr='取消配送',exception_code=0 WHERE delivery_id = ? AND order_status = 1;"
	_, err = db.Exec(upsql, orderException.DeliveryId)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error("异常订单取消出错:" + kit.JsonEncode(params) + err.Error())
		return &out, nil
	}
	out.Code = 200
	return &out, nil
}

// 再次发起配送
// boss后台异常单里 点击重新发单
func (oe OrderExceptionService) DistributionAgain(ctx context.Context, params *oc.ExceptionOrderStatusRequest) (*oc.ExceptionOrderStatusResponse, error) {
	glog.Info("再次发起配送:" + kit.JsonEncode(params))
	var out oc.ExceptionOrderStatusResponse

	oe.orderMain = GetOrderMainByOrderSn(params.OrderId)
	if oe.orderMain.Id == 0 {
		out.Message = "该订单不存在"
		out.Code = 400
		return &out, nil
	}
	oe.session = GetDBConn().NewSession()
	defer oe.session.Close()

	//获取实物子订单
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
		session:   oe.session,
	}
	if oe.IsThirdOrder() && oe.orderMain.ParentOrderSn == "" {
		mainOrderSn := oe.orderMain.OrderSn
		has, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, "第三方推闪送查询实物子单出错，", err)
			out.Code = 400
			return &out, errors.New("第三方订单推闪送查询实物子单出错")
		}
		if has == false {
			glog.Error(mainOrderSn, "第三方订单推闪送未查询到实物子订单，", err)
			out.Code = 400
			return &out, errors.New("第三方订单推闪送未到查询实物子单")
		}
	} else {
		realOrder.orderMain = oe.orderMain
	}

	realOrder.orderDetail = GetOrderDetailByOrderSn(realOrder.orderMain.OrderSn)

	var err error
	DeliveryInfo := dto.DeliverPriceRes{}
	DeliveryInfo.SelDeliverType = cast.ToString(params.DeliveryType)
	realOrder.DeliverInfo = &DeliveryInfo
	//if params.DeliveryType == 1 {
	//	out.Code = 400
	//	//return &out, errors.New("闪送配送已停用，请使用其他配送方式")
	//	err = realOrder.PushShanSong()
	//} else {
	//	err = realOrder.MpOrderCreate()
	//}
	glog.Info("发送美团配送：数据：", kit.JsonEncode(realOrder))
	err = realOrder.MpOrderCreate()

	if err != nil {
		out.Code = 400
		out.Error = "发起配送失败" + err.Error()
		out.Message = "发起配送失败：" + err.Error()
		glog.Info("再次发起配送失败:" + params.OrderId + err.Error())

		// 配送异常返回信息优化
		errInfo := err.Error()
		err = func() (err error) {
			if strings.Contains(errInfo, "goods_weight") {
				errInfo = "订单内商品重量不符合规范美团无法配送，请呼叫跑腿配送"
			}
			return errors.New(errInfo)
		}()

		_, err = oe.session.Table("order_exception").Where("delivery_id = ?", params.DeliveryId).Cols("exception_descr").Update(&models.OrderException{
			ExceptionDescr: err.Error(),
		})
		if err != nil {
			glog.Error("修改异常订单出错" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return &out, err
		}
		return &out, nil

	}

	itempar := &oc.ExceptionOrderStatusRequest{
		DeliveryId: params.DeliveryId,
	}
	//原来的异常订单直接不显示
	ret, err := oe.OrderIsShowException(nil, itempar)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error(params.OrderId + "再次发起配送出错" + err.Error())
		return &out, nil
	}
	if ret.Code == 400 {
		out.Code = 400
		out.Error = ret.Error
		out.Message = ret.Message
		return &out, nil
	}

	out.Code = 200
	out.Error = ""
	return &out, nil
}

// 已送达 已测试
// boss后台调用
func (oe OrderExceptionService) GoodArrive(ctx context.Context, params *oc.ExceptionOrderStatusRequest) (*oc.ExceptionOrderStatusResponse, error) {
	glog.Info("异常订单已送达:" + kit.JsonEncode(params))
	var out oc.ExceptionOrderStatusResponse
	orderMain := GetOrderMainByOrderSn(params.OrderId)
	o := new(OrderService)
	ysdpar := oc.DeliveryNodeRequest{
		OrderSn:       params.OrderId,
		Status:        50,
		StoreMasterId: params.StoreMasterId,
		IsException:   1,
	}
	//查询配送员
	lastNode, err := GetLastDeliveryNodeByOrderSn(params.OrderId)
	if err != nil {
		glog.Error(params.OrderId, "异常单送达查询配送信息出错", err)
	} else {
		ysdpar.CourierPhone = lastNode.CourierPhone
		ysdpar.CourierName = lastNode.CourierName
	}
	//获取订单详情中的经纬度
	orderDetail := GetOrderDetailByOrderSn(params.OrderId, "latitude,longitude")
	if orderDetail.Latitude > 0 {
		ysdpar.Latitude = cast.ToString(orderDetail.Latitude)
		ysdpar.Longitude = cast.ToString(orderDetail.Longitude)
	}

	re, err := o.DeliveryNode(nil, &ysdpar)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error(params.OrderId + "已送达出错" + err.Error())
		return &out, nil
	}
	if re.Code != 200 {
		out.Code = 400
		out.Message = re.Error
		return &out, nil
	}

	//自配送也写入node
	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     lastNode.DeliveryId,
		OrderSn:        params.OrderId,
		DeliveryStatus: 30,
		Content:        "后台异常配送单自配送到达",
		CourierName:    ysdpar.CourierName,
		CourierPhone:   ysdpar.CourierPhone,
		CreateTime:     time.Now(),
	}

	if orderMain.DeliveryType == 5 {
		deliveryModel.Content = "商家自配送到达"
	}

	_, err = o.session.Insert(&deliveryModel)
	if err != nil {
		glog.Error(params.OrderId, "发起自配写入配送节点数据出错："+err.Error())
	}

	params.Status = 4 //已经送达了就不显示了
	//原来的异常订单直接不显示
	ret, err := oe.OrderStatusException(nil, params)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error(params.OrderId + "已送达出错" + err.Error())
		return &out, err
	}
	if ret.Code == 400 {
		out.Code = 400
		out.Error = ret.Error
		out.Message = ret.Message
		return &out, nil
	}

	out.Code = 200
	return &out, nil
}

func SimulateDeliveryNode(ysdParam oc.DeliveryNodeRequest) {
	glog.Info("饿了么模拟骑手到店进入")
	time.Sleep(1 * time.Minute) // 睡眠1分钟
	o := new(OrderService)
	glog.Info("饿了么模拟骑手到店开始")
	ysdParam.Status = 30

	//判断订单是否存在
	orderModel := GetOrderMainByOrderSn(ysdParam.OrderSn, "*")
	if len(orderModel.OrderSn) == 0 || orderModel.OrderStatus == 0 {
		return
	}

	res, err := o.DeliveryNode(nil, &ysdParam)
	glog.Info("饿了么模拟骑手到店完成", res, err)
	if err != nil {
		glog.Error(ysdParam.OrderSn + "模拟骑手到店出错" + err.Error())
	}
	if res.Code != 200 {
		glog.Error(ysdParam.OrderSn + "模拟骑手到店出错" + res.Message + res.Error)
	}
}

// 发起自配
// 后台发起自配送
func (oe OrderExceptionService) OrderOwnDeliver(ctx context.Context, params *oc.OrderExceptionRequest) (*oc.OrderExceptionResponse, error) {
	glog.Info("异常单发起自配:", kit.JsonEncode(params))
	var out oc.OrderExceptionResponse

	//判断订单是否存在
	orderModel := GetOrderMainByOrderSn(params.OrderId, "*")
	if len(orderModel.OrderSn) == 0 {
		out.Code = 400
		out.Error = "订单不存在"
		return &out, nil
	}

	o := new(OrderService)

	ysdParam := oc.DeliveryNodeRequest{
		OrderSn:       params.OrderId,
		Status:        20,
		CourierName:   params.CourierName,
		CourierPhone:  params.CourierPhone,
		DeliveryType:  2,
		StoreMasterId: params.StoreMasterId,
		IsException:   1,
	}
	if len(params.DeliveryId) > 0 {
		ysdParam.DeliveryId = cast.ToInt64(params.DeliveryId)
	}

	//商家自配的配送方式，不认为是配送异常
	if orderModel.DeliveryType == 5 {
		ysdParam.IsException = 0
		ysdParam.DeliveryType = 3
	}

	//发起自配送的时候 配送员的经纬度设置为门店的经纬度
	dataCenterConn := dac.GetDataCenterClient()
	if response, err := dataCenterConn.RPC.ShopStoreGet(dataCenterConn.Ctx, &dac.ShopStoreGetRequest{
		Finance_Code: orderModel.ShopId,
	}); err != nil {
		glog.Error(params.OrderId, ",发起异常自配送调用ShopStoreGet出错", err)
	} else if response != nil && response.Code == 200 {
		ysdParam.Latitude = response.Data.PointY
		ysdParam.Longitude = response.Data.PointX
	} else {
		glog.Error(params.OrderId, ",发起异常自配送调用ShopStoreGet失败")
	}

	res, err := o.DeliveryNode(nil, &ysdParam)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error(params.OrderId + "发起自配出错" + err.Error())
		return &out, nil
	}
	if res.Code != 200 {
		out.Code = 400
		out.Message = "更新配送信息失败"
		return &out, nil
	}
	//模拟骑手已经到店
	go SimulateDeliveryNode(ysdParam)
	//自配送也写入node
	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     cast.ToInt64(params.DeliveryId),
		OrderSn:        params.OrderId,
		DeliveryStatus: 30,
		Content:        "后台异常配送单自配送",
		CourierName:    params.CourierName,
		CourierPhone:   params.CourierPhone,
		CreateTime:     time.Now(),
	}

	if orderModel.DeliveryType == 5 {
		deliveryModel.Content = "商家自配"
	}

	_, err = o.session.Insert(&deliveryModel)
	if err != nil {
		glog.Error(params.OrderId, "发起自配写入配送节点数据出错："+err.Error())
	}
	itemParam := &oc.ExceptionOrderStatusRequest{
		DeliveryId:   params.DeliveryId,
		Status:       3,
		CourierName:  params.CourierName,
		CourierPhone: params.CourierPhone,
	}
	//更新异常单信息
	ret, err := oe.OrderStatusException(nil, itemParam)
	if err != nil {
		out.Code = 400
		out.Message = "发起自配更新异常单出错:" + err.Error()
		out.Error = err.Error()
		glog.Error(params.OrderId + "发起自配更新异常单出错" + err.Error())
		return nil, err
	}
	if ret.Code == 400 {
		out.Code = 400
		out.Error = ret.Error
		out.Message = "发起自配更新异常单失败" + ret.Message
		return &out, nil
	}

	myEngine := GetDBConn()
	upsql := "UPDATE `order_main` SET order_status_child=20103 WHERE order_sn IN('" + params.OrderId + "','" + orderModel.ParentOrderSn + "')"
	if orderModel.DeliveryType == 5 { //商家自配送时需记录发货时间
		upsql = "UPDATE `order_main` SET order_status_child=20103, deliver_time = NOW() WHERE order_sn IN('" + params.OrderId + "','" + orderModel.ParentOrderSn + "')"
	}
	_, err = myEngine.Exec(upsql)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = "发起自配更新订单状态失败" + ret.Message
		glog.Error(params.DeliveryId + "发起自配出错,修改订单状态失败" + err.Error())
		return nil, err
	}
	//改成自配送

	out.Code = 200
	return &out, nil
}

// 异常配送修改为不显示 测试
func (oe OrderExceptionService) OrderIsShowException(ctx context.Context, params *oc.ExceptionOrderStatusRequest) (*oc.ExceptionOrderStatusResponse, error) {
	glog.Info("修改异常订单是否显示进入:", kit.JsonEncode(params))
	var out oc.ExceptionOrderStatusResponse
	myEngine := GetDBConn()

	_, err := myEngine.Exec("update order_exception set is_show = 0 where delivery_id = ?", params.DeliveryId)
	if err != nil {
		glog.Error("修改异常订单是否显示:" + err.Error())
		out.Code = 400
		out.Error = err.Error()
		return &out, err
	}
	out.Code = 200
	out.Error = ""
	return &out, nil
}

// 异常订单查询
func (oe OrderExceptionService) OrderExceptionList(ctx context.Context, params *oc.OrderExceptionListRequest) (*oc.OrderExceptionListResponse, error) {
	oe.session = GetDBConn().NewSession()
	defer oe.session.Close()

	var out oc.OrderExceptionListResponse
	startTime := time.Now().AddDate(0, -2, 0).Format(kit.DATETIME_LAYOUT)
	session := oe.session.Table("order_exception").Alias("oe").
		Join("INNER", "order_main", "order_main.order_sn = `oe`.order_sn").
		Join("INNER", "order_detail", "order_main.order_sn = `order_detail`.order_sn").
		Where("order_main.order_status=20 and oe.is_show=? and oe.create_time>?", 1, startTime)
	if params.OrderId != "" {
		session.And("oe.order_sn = ?", params.OrderId)
	}

	//add by csf@通过渠道过滤
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", strconv.Itoa(int(params.ChannelId)))
		} else {

			if params.ChannelId == 4 {
				session.And("order_main.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("order_main.channel_id = ?", params.ChannelId)
			}
		}
	}

	if params.MtOrderSn != "" {
		session.And("order.old_order_sn = ?", params.MtOrderSn)
	}

	if params.Remarks != "" {
		session.And("oe.exception_descr like ?", "%"+params.Remarks+"%")
	}

	session.In("order_main.shop_id", params.Shopids)

	countSession := session.Clone()
	defer countSession.Close()
	total, err := countSession.Count()
	if err != nil {
		out.Code = 400
		out.TotalCount = 0
		out.Error = err.Error()
		glog.Error("查询异常订单列表出错:" + err.Error())
		return &out, err
	}

	out.TotalCount = int32(total)
	out.Code = 200
	if out.TotalCount == 0 {
		out.List = []*oc.OrderExceptionRequest{}
		return &out, nil
	}

	if params.PageSize < 1 {
		params.PageSize = 5
	}
	if params.Page < 1 {
		params.Page = 1
	}

	var item []dto.OrderExceptionDto
	session.Select("oe.*,oe.order_sn order_id,order_main.id oid,order_main.old_order_sn,order_main.create_time as order_time,case order_detail.child_channel_id when '' then order_main.channel_id else order_detail.child_channel_id end channel_id")
	err = session.Desc("order_main.create_time").Limit(int(params.PageSize), int((params.Page-1)*params.PageSize)).Find(&item)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error("查询异常订单列表出错:" + err.Error())
		return &out, err
	}

	out.List = make([]*oc.OrderExceptionRequest, len(item))
	for k := range item {
		out.List[k] = &oc.OrderExceptionRequest{
			CourierName:      item[k].CourierName,
			CourierPhone:     item[k].CourierPhone,
			DeliveryId:       item[k].DeliveryId,
			ExceptionCode:    item[k].ExceptionCode,
			ExceptionDescr:   item[k].ExceptionDescr,
			OrderId:          item[k].OrderSn,
			ExceptionTime:    item[k].ExceptionTime,
			MtPeisongId:      item[k].MtPeisongId,
			Source:           item[k].Source,
			OrderStatus:      item[k].OrderStatus,
			Oid:              cast.ToString(item[k].Oid),
			OldOrderSn:       item[k].OldOrderSn,
			OrderTime:        item[k].OrderTime,
			DistributionMode: item[k].DistributionMode,
			ChannelId:        item[k].ChannelId,
		}
	}

	return &out, nil
}

// 异常配送添加订单(如果是已经存在的，则修改是否取消，否则添加) 测试过
func (oe OrderExceptionService) OrderExceptionAdd(ctx context.Context, params *oc.OrderExceptionRequest) (*oc.OrderExceptionResponse, error) {
	glog.Info("异常配送添加订单:" + kit.JsonEncode(params))
	var out oc.OrderExceptionResponse

	oe.session = GetDBConn().NewSession()
	defer oe.session.Close()

	//美团专配只有自己的美团单号
	if params.OrderId == "" {
		oe.orderMain = GetOrderMainByOldOrderSn(params.OldOrderSn)
		if oe.orderMain.Id > 0 {
			params.OrderId = oe.orderMain.OrderSn
		}
	} else {
		oe.orderMain = GetOrderMainByOrderSn(params.OrderId)
	}

	//查询子订单1
	realOrder := new(models.OrderMain)
	if oe.orderMain.ParentOrderSn == "" {
		_, err := GetDBConn().SQL("SELECT id,order_sn,parent_order_sn FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", oe.orderMain.OrderSn).Get(realOrder)
		if err != nil {
			out.Message = "子订单不存在"
			glog.Error(oe.orderMain.OrderSn, ", 更新配送信息接口，子订单不存在")
			return &out, nil
		}
	} else {
		realOrder = oe.orderMain
	}

	exceptioninfo := &models.OrderException{
		CourierName:      params.CourierName,
		CourierPhone:     params.CourierPhone,
		DeliveryId:       params.DeliveryId,
		ExceptionDescr:   params.ExceptionDescr,
		ExceptionCode:    params.ExceptionCode,
		OrderSn:          params.OrderId,
		ExceptionTime:    params.ExceptionTime,
		MtPeisongId:      params.MtPeisongId,
		Source:           params.Source,
		IsShow:           1,
		DistributionMode: "商家自配",
	}
	if params.IsHide {
		exceptioninfo.IsShow = 0
	}

	if _, ok := exceptionml[params.ExceptionCode]; ok {
		exceptioninfo.ExceptionDescr += exceptionml[params.ExceptionCode]
	}
	if params.DistributionMode != "" {
		exceptioninfo.DistributionMode = params.DistributionMode
	}
	if params.Source == 3 {
		exceptioninfo.OrderStatus = 1
	} else {
		exceptioninfo.OrderStatus = 2
	}

	ishave := 0
	oe.session.SQL("select 1 from order_exception where delivery_id = ?", exceptioninfo.DeliveryId).Get(&ishave)

	//异常订单恢复
	if params.OrderStatus == 5 {
		exceptioninfo.IsShow = 0
		_, err := oe.session.Table("order_exception").Where("delivery_id = ?", exceptioninfo.DeliveryId).Cols("order_status,is_show,exception_code,exception_descr").Update(exceptioninfo)
		if err != nil {
			glog.Error("修改异常订单出错" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return &out, err
		}
		out.Code = 200
		return &out, err
	}

	//订单流转日志
	var orderLogs []*models.OrderLog
	orderLogs = append(orderLogs, []*models.OrderLog{
		{
			OrderSn:         realOrder.ParentOrderSn,
			LogType:         models.MtOrderLogException,
			OperateTypeName: "美团",
		},
		{
			OrderSn:         realOrder.OrderSn,
			LogType:         models.MtOrderLogException,
			OperateTypeName: "美团",
		},
	}...)
	glog.Info("插入订单异常日志！", realOrder.OrderSn, realOrder.ParentOrderSn)
	//记录订单流转日志
	SaveOrderLog(orderLogs)

	if ishave == 1 {
		_, err := oe.session.Table("order_exception").Where("delivery_id = ?", exceptioninfo.DeliveryId).Cols("order_status,is_show,exception_code,exception_descr").Update(exceptioninfo)
		if err != nil {
			glog.Error("修改异常订单出错" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return &out, err
		}
	} else {
		ishave := 0
		oe.session.SQL("select 1 from order_exception where order_sn = ? and is_show = 1", exceptioninfo.OrderSn).Get(&ishave)
		if ishave == 1 {
			out.Code = 200
			out.Error = ""
			return &out, nil
		}

		icount, err := oe.session.Table("order_exception").Insert(exceptioninfo)
		if err != nil {
			glog.Error("插入异常订单出错" + err.Error())
			out.Code = 400
			out.Error = err.Error()
			return &out, err
		}
		if icount == 0 {
			glog.Error("插入异常订单0条")
			out.Code = 400
			out.Message = "插入异常订单0条"
			return &out, nil
		}

		if oe.orderMain.OrderStatus == 20 && exceptioninfo.IsShow == 1 {
			//通知数据中心
			message := &models.Message{
				OrderId:     oe.orderMain.ParentOrderSn,
				MessageType: 5, //只要不是预订单，则不推送消息给用户
				FinanceCode: oe.orderMain.ShopId,
				Msg:         fmt.Sprintf("【配送异常】订单：%s，请重点关注！", oe.orderMain.OrderSn),
			}
			go MessageCreate(message)
		}

	}

	out.Code = 200
	out.Error = ""
	return &out, nil
}

package services

import (
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

/*
//退款相关的查询 查询订单的退款金额  订单商品的退款金额
阿闻的退款 会写入refund_order refund_order_product  阿闻是按照子订单申请退款的  所以refund_order中的order_sn 是子订单号
第三方的退款 会写入refund_order refund_order_product 第三方是按照主订单申请退款 所以refund_order中的order_sn 是主订单号
第三方的退款商品会拆到refund_order_third_product 并在该表冗余退款商品所在的子订单号
todo 后期优化：第三方退款单拆成子单插入写入refund_order 与 refund_order_product 方便与阿闻保持一致 查询退款 也不会像下面这些代码那样麻烦
并用单独另外的表记录第三方的申请记录即可
*/

//每次in 的条数
var sliceLen = 10000

//订单退款金额
type OrderRefundAmountRows struct {
	OrderSn      string
	RefundAmount string
}

//阿闻主单
type AwenMainOrderSn []string

//退款订单号
type RefundOrderSn []string

//第三方虚拟子订单
type ThirdVirtualChildOrderSn []string

//第三方实物子订单
type ThirdRealChildOrderSn struct {
	ThirdOrderMap map[string]string
	OrderSn       []string
}

//实物订单
type RealOrderRefund struct {
	AwenOrderSn  RefundOrderSn
	ThirdOrderSn ThirdRealChildOrderSn
}

//主订单
type MainOrderRefund struct {
	AwenOrderSn  AwenMainOrderSn
	ThirdOrderSn RefundOrderSn
}

//虚拟订单
type VirtualOrderRefund struct {
	AwenOrderSn  RefundOrderSn
	ThirdOrderSn ThirdVirtualChildOrderSn
}

//实物子订单退款金额查询
// 阿闻的子订单 直接查询refund_order 表即可
// 第三方渠道的 需要通过refund_order_third_product查出所有实物订单关联退款单 ，并用退款单退款金额- 虚拟商品的退款金额得出
// 返回 订单号 与 退款金额的map关系
func (o *RealOrderRefund) CalRefundAmount() map[string]float64 {
	res := make(map[string]float64)
	if len(o.AwenOrderSn) > 0 {
		refundList := o.AwenOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}
	if len(o.ThirdOrderSn.OrderSn) > 0 {
		refundList := o.ThirdOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}
	return res
}

// 实物主订单退款金额查询
// 直接查询refund_order 表即可，但是阿闻在refund_order表中写入的是子订单 所以需要关联order_main表才能关联出主单
// 返回 订单号 与 退款金额的map关系
func (o *MainOrderRefund) CalRefundAmount() map[string]float64 {
	res := make(map[string]float64)
	if len(o.AwenOrderSn) > 0 {
		refundList := o.AwenOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}
	if len(o.ThirdOrderSn) > 0 {
		refundList := o.ThirdOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}
	return res
}

// 实物虚拟子单退款金额查询
// 直接查询refund_order 表即可，但是阿闻在refund_order表中写入的是子订单 所以需要关联order_main表才能关联出主单
// 返回 订单号 与 退款金额的map关系
func (o *VirtualOrderRefund) CalRefundAmount() map[string]float64 {
	res := make(map[string]float64)
	if len(o.AwenOrderSn) > 0 {
		refundList := o.AwenOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}

	if len(o.ThirdOrderSn) > 0 {
		refundList := o.ThirdOrderSn.QueryRefundAmount()
		SumOrderRefundAmount(refundList, res)
	}
	return res
}

//阿闻 通过主单查询退款金额
//orderSn 主订单号
func (s AwenMainOrderSn) QueryRefundAmount() []*OrderRefundAmountRows {
	var res []*OrderRefundAmountRows
	orderSn := s
	if len(orderSn) == 0 {
		return res
	}
	for {
		if len(orderSn) < sliceLen {
			sliceLen = len(orderSn)
		}
		querySlice := orderSn[:sliceLen]
		var tmpRefundOrder []*OrderRefundAmountRows

		err := GetDBConn().Table("refund_order").
			Alias("a").
			Join("inner", "order_main b", "a.order_sn = b.order_sn").
			Select("b.parent_order_sn order_sn,a.refund_amount").
			Where("a.refund_state = 3").
			In("b.parent_order_sn", querySlice).Find(&tmpRefundOrder)
		if err != nil {
			glog.Error(querySlice, ", AwenQueryRefundAmountByMainOrderSn查询订单退款金额出错, ", err)
			return res
		}
		if len(tmpRefundOrder) > 0 {
			res = append(res, tmpRefundOrder...)
		}
		//更新 原始切片 切掉已经查询过的数据
		orderSn = orderSn[sliceLen:]
		if len(orderSn) == 0 {
			break
		}
	}
	return res
}

//根据订单号查询退款金额
//orderSn 阿闻子订单号 或者 第三方主订单
func (s RefundOrderSn) QueryRefundAmount() []*OrderRefundAmountRows {
	var res []*OrderRefundAmountRows
	orderSn := s
	if len(orderSn) == 0 {
		return res
	}
	for {
		if len(orderSn) < sliceLen {
			sliceLen = len(orderSn)
		}
		querySlice := orderSn[:sliceLen]
		var tmpRefundOrder []*OrderRefundAmountRows

		err := GetDBConn().Table("refund_order").
			Select("order_sn,refund_amount").
			Where("refund_state = 3").
			In("order_sn", querySlice).Find(&tmpRefundOrder)
		if err != nil {
			glog.Error(querySlice, ", AwenQueryRefundAmountByMainOrderSn查询订单退款金额出错, ", err)
			return res
		}
		if len(tmpRefundOrder) > 0 {
			res = append(res, tmpRefundOrder...)
		}
		//更新 原始切片 切掉已经查询过的数据
		orderSn = orderSn[sliceLen:]
		if len(orderSn) == 0 {
			break
		}
	}
	return res
}

// 根据虚拟子订单订单号查询退款金额
// orderSn 第三方子订单订单
// 第三方子订单  区分实物子订单与虚拟子订单  退款金额都需要查询 refund_order_third_product 表 因为refund_order 不能区分
// 实物订单还是虚拟订单
// 虚拟订单 总退款金额 为所有虚拟退的虚拟商品总额
func (s ThirdVirtualChildOrderSn) QueryRefundAmount() []*OrderRefundAmountRows {
	var res []*OrderRefundAmountRows
	orderSn := s
	if len(orderSn) == 0 {
		return res
	}
	for {
		if len(orderSn) < sliceLen {
			sliceLen = len(orderSn)
		}
		querySlice := orderSn[:sliceLen]
		var tmpRefundOrder []*OrderRefundAmountRows

		err := GetDBConn().Table("refund_order_third_product").Alias("a").
			Join("inner", "refund_order b", "a.refund_sn = b.refund_sn").
			Select("a.order_sn,a.refund_amount").
			Where("b.refund_state = 3").
			In("a.order_sn", querySlice).Find(&tmpRefundOrder)
		if err != nil {
			glog.Error(querySlice, ", AwenQueryRefundAmountByMainOrderSn查询订单退款金额出错, ", err)
			return res
		}
		if len(tmpRefundOrder) > 0 {
			res = append(res, tmpRefundOrder...)
		}
		//更新 原始切片 切掉已经查询过的数据
		orderSn = orderSn[sliceLen:]
		if len(orderSn) == 0 {
			break
		}
	}
	return res
}

// 根据父订单订单号查询实物订单退款金额
// orderSn 第三方订单子订单号
//orderMap 实物子单与主单的map表
// 第三方子订单  区分实物子订单与虚拟子订单  退款金额都需要查询 refund_order_third_product 表 因为refund_order 不能区分
// 实物订单 总退款金额 = 订单退款金额 - 虚拟子订单退款金额  为什么要使用减法：因为实物订单如果是最后一笔 商品的退款金额不等于实际退款金额 还可能包括运费等
func (s *ThirdRealChildOrderSn) QueryRefundAmount() []*OrderRefundAmountRows {
	var res []*OrderRefundAmountRows
	orderSn := s.OrderSn
	if len(orderSn) == 0 {
		return res
	}
	for {
		if len(orderSn) < sliceLen {
			sliceLen = len(orderSn)
		}
		querySlice := orderSn[:sliceLen]

		type thirdOrderRefundAmount struct {
			OrderSn           string
			RefundAmount      string
			OrderRefundAmount string
			MainOrderSn       string
			ProductType       int32
			RefundSn          string
		}
		var tmpRefundOrder []thirdOrderRefundAmount
		//先查出相应的退款单
		var refundSn []string
		err := GetDBConn().Table("refund_order_third_product").
			Select("refund_sn").
			In("order_sn", querySlice).Find(&refundSn)

		if err != nil {
			glog.Error(querySlice, ", AwenQueryRefundAmountByMainOrderSn查询订单退款金额出错, ", err)
			return res
		}
		//第二步 再根据退款单 查询所有退款商品
		err = GetDBConn().Table("refund_order_third_product").
			Alias("a").
			Join("inner", "refund_order b", "a.refund_sn = b.refund_sn").
			Select("a.order_sn,a.refund_amount,a.product_type,b.refund_amount order_refund_amount,b.order_sn main_order_sn,b.refund_sn").
			Where("b.refund_state = 3").
			In("a.refund_sn", refundSn).
			Find(&tmpRefundOrder)
		if err != nil {
			glog.Error(querySlice, ", AwenQueryRefundAmountByMainOrderSn查询订单退款金额出错, ", err)
			return res
		}
		//第三步 用订单的总退款金额 - 虚拟订单的退款金额
		if len(tmpRefundOrder) > 0 {

			orderRefundAmountMap := make(map[string]float64)   //记录订单所有的退款记录的退款金额加总 = 该订单下所有的退款单退款总额加总
			productRefundAmountMap := make(map[string]float64) //记录退款单虚拟商品退款金额
			mainOrderRefundSnMap := make(map[string]struct{})  //退款主单与退款单的关联关系

			for _, v := range tmpRefundOrder {
				if _, ok := mainOrderRefundSnMap[v.MainOrderSn+"-"+v.RefundSn]; !ok {
					mainOrderRefundSnMap[v.MainOrderSn+"-"+v.RefundSn] = struct{}{}
					amount, _ := decimal.NewFromFloat(cast.ToFloat64(v.OrderRefundAmount)).Float64()
					if _, check := orderRefundAmountMap[v.MainOrderSn]; !check {
						orderRefundAmountMap[v.MainOrderSn] = amount
						productRefundAmountMap[v.MainOrderSn] = 0
					} else {
						orderRefundAmountMap[v.MainOrderSn] += amount
					}
				}

				if v.ProductType == 2 {
					amount, _ := decimal.NewFromFloat(cast.ToFloat64(v.RefundAmount)).Float64()
					productRefundAmountMap[v.MainOrderSn] += amount
				}
			}

			for mainOrderSn, refundAmount := range orderRefundAmountMap {
				//主单金额-虚拟订单金额
				virtualAmount := decimal.NewFromFloat(productRefundAmountMap[mainOrderSn])
				realRefundAmount, _ := decimal.NewFromFloat(refundAmount).Sub(virtualAmount).Float64()
				realOrderSn := s.ThirdOrderMap[mainOrderSn]

				res = append(res, &OrderRefundAmountRows{
					OrderSn:      realOrderSn,
					RefundAmount: cast.ToString(realRefundAmount),
				})
			}
		}
		//更新 原始切片 切掉已经查询过的数据
		orderSn = orderSn[sliceLen:]
		if len(orderSn) == 0 {
			break
		}
	}
	return res
}

//订单退款数量
func SumOrderRefundAmount(refundList []*OrderRefundAmountRows, res map[string]float64) {
	if len(refundList) > 0 {
		for _, v := range refundList {
			amount := decimal.NewFromFloat(cast.ToFloat64(v.RefundAmount))
			if _, ok := res[v.OrderSn]; ok {
				res[v.OrderSn], _ = decimal.NewFromFloat(res[v.OrderSn]).Add(amount).Round(2).Float64()
			} else {
				res[v.OrderSn], _ = amount.Round(2).Float64()
			}
		}
	}
}

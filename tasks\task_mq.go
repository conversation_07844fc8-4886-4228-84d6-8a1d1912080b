package tasks

import (
	"strconv"
	"sync"
	"time"

	"order-center/models"
	"order-center/services"
	"order-center/utils"

	"github.com/go-redis/redis"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0/1 * * * * *", func() {
		//连接池勿关闭
		redisConn := services.GetRedisConn()

		lockCard := "task:lock:syncmq"
		delRedisSetNx := DelRedisSetNx(redisConn, lockCard, 1) //5分钟的判断
		if delRedisSetNx {
			lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 0).Val()
			if !lockRes {
				return
			}
		} else {
			return
		}
		//释放锁
		defer redisConn.Del(lockCard)

		engine := services.GetDBConn()

		//获取1000条数据
		sql := `SELECT id,exchange,quene,content,ispush,lastdate FROM dc_order.mq_info WHERE ispush = 0 ORDER BY lastdate ASC LIMIT 3000;`
		list := make([]models.MqInfo, 0)
		if err := engine.SQL(sql).Find(&list); err != nil {
			glog.Error(err)
		}
		if len(list) == 0 {
			return
		}
		l := 100
		//写协程
		var wg = new(sync.WaitGroup)
		for {
			wg.Add(1)
			if len(list) < l {
				l = len(list)
			}
			_list := list[:l]
			go GetNoSyncMqMessage(_list, wg)
			list = list[l:]
			if len(list) == 0 {
				break
			}
		}
		//Wait阻塞等待所有的写通道协程结束,待计数值变成零，Wait才会返回
		wg.Wait()

	}); err != nil {
		//time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func GetNoSyncMqMessage(list []models.MqInfo, wg *sync.WaitGroup) bool {
	defer wg.Done()
	var mapMQId = make([]int64, 0)

	for _, v := range list {
		if resBool := utils.PublishRabbitMQ(v.Quene, v.Content, v.Exchange); resBool {
			mapMQId = append(mapMQId, v.Id)
		}
	}
	if len(mapMQId) > 0 {
		services.GetDBConn().In("id", mapMQId).Cols("ispush").Update(&models.MqInfo{Ispush: 1})
	}
	return true
}

//处理redis的setnx的返回结果。如果锁定时间已经超过默认时间5分钟，则自动删除。默认时间可更改
func DelRedisSetNx(redisConn *redis.Client, redisKey string, timeMinute int32) bool {
	if redisConn.Exists(redisKey).Val() > 0 {
		timeUnix, _ := strconv.Atoi(redisConn.Get(redisKey).Val())
		//与当前时间比较
		timeNowUnix := time.Now().Add(-1 * time.Minute * 5).Unix() // 5分钟
		if timeMinute > 0 {
			timeDuration := time.Duration(-1*timeMinute) * time.Minute
			timeNowUnix = time.Now().Add(timeDuration).Unix()
		}
		if timeNowUnix >= int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(redisKey)
			return true
		}
		return false
	}
	return true
}

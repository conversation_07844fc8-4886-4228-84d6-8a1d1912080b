syntax = "proto3";
package oc;

service CardService {
  // 开卡
  rpc New (CardNewReq) returns (CardNewRes);
  // 通过激活码开卡
  rpc NewByCode(CardNewByCodeReq) returns(CardBaseResponse);
  // 通过门店开卡（充储值卡送会员卡）
  rpc NewByStore(CardNewByStoreReq) returns(CardNewByStoreRes);
  // 支付通知
  rpc PayNotify(CardPayNotifyReq) returns(CardBaseResponse);
  // 服务包创建
  rpc ServicePackCreate(CardServicePackCreateReq) returns(CardServicePackCreateRes);
  // 服务包激活
  rpc ServicePackActivity(CardServicePackActivityReq) returns(CardBaseResponse);
  // 卡退款处理
  rpc Refund(CardRefundReq) returns(CardBaseResponse);
  // 权益领取
  rpc EquityReceive(CardEquityReceiveReq) returns(CardBaseResponse);
  // 查询签约id
  rpc QuerySignId(QuerySignIdReq) returns(QuerySignIdRes);
  // 检测卡id是否存在
  rpc CheckCardId(CheckCardIdReq) returns(CheckCardIdRes);
  // VIP卡退款审核
  rpc RefundExamine(RefundExamineReq) returns(CardBaseResponse);
  // 子龙储值卡退款通知
  rpc CardReturnByStore(CardPayNotifyReq) returns(CardBaseResponse);
}

message RefundExamineReq {
  // 退款id
  int32 refund_id = 1;
  //备注或者拒绝原因
  string reason = 2;
  // 退款金额
  int32 refund_amount = 3;
  //审核状态:1为待审核,2为同意,3为不同意
  int32 state = 4;
  // 分销id
  string user_name = 5;
  // 分销类型 1-链接 2-扫码
  string user_no = 6;
  //审核类型：0-默认人工 1-系统自动
  int32 admin_type=7;
}

message CardBaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
}

message CardNewReq {
  // 卡id
  int32 card_id = 2;
  // 前端不用传，用户id
  string scrm_id = 3;
  // 前端不用传，用户名
  string user_name = 4;
  // 前端不用传，从请求头解析
  int32 user_agent = 5;
  // 分销id
  int32 dis_id = 6;
  // 分销类型 1-链接 2-扫码
  int32 dis_type = 7;
  // 来源，0默认，1兑换码激活，2门店开卡
  int32 source = 8;
}

message CardNewRes {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  // 订单号
  string order_sn = 3;
  // 支付截止时间戳
  int32 deadline = 4;
}

message CardNewByCodeReq {
  // 兑换码
  string code = 1;
  // 前端不用传，用户id
  string scrm_id = 3;
  // 前端不用传，用户名
  string user_name = 4;
  // 前端不用传，从请求头解析
  int32 user_agent = 5;
}

message CardNewByStoreReq {
  // 子龙门店id
  int32 store_id = 1;
  // 用户id
  string scrm_id = 3;
  // 卡模板id
  int32 card_id = 4;
}

message CardNewByStoreRes {
  // 状态码，200成功，400失败
  int32 code = 1;
  // 消息
  string message = 2;
  message Data {
    // 卡订单号
    string order_sn = 3;
  }
  Data data =3;
}

message CardPayNotifyReq {
  // 订单号
  string order_sn = 1;
  // 来源，0默认，1兑换码激活，2门店开卡
  int32 source = 2;
}

message CardServicePackCreateReq {
  string aw_order_sn = 1;
  string member_id = 2;
  string member_mobile = 3;
  string member_name = 4;
  int32 order_source = 5;
  int32 order_source_channel = 6;
  int32 original_price = 7;
  int32 pack_buy_way = 8;
  int32 pay_price = 9;
  int32 sku_id = 10;
}

message CardServicePackCreateRes {
  int32 code = 1;
  message Data {
    int32 sign_id = 1;
  }
  Data data = 2;
  string message = 3;
}

message CardServicePackActivityReq {
  // 前端不用传，用户id
  string member_id = 1;
  // 宠物年龄，记录宠物当时的年龄
  string pet_age = 2;
  // 宠物头像
  string pet_avatar = 3;
  // 生日例如：2021-10-01 00:00:00
  string pet_birthday = 4;
  // 宠物ID
  string pet_id = 5;
  // 宠物种类大分类
  string pet_kind_of = 6;
  // 宠物名字
  string pet_name = 7;
  // 性别：1GG,2MM
  int32 pet_sex = 8;
  // 种类
  string pet_variety = 9;
  // 签约id
  int32 sign_id = 10;
}

message CardRefundReq {
  // 退款单号
  string refund_sn = 1;
}

message CardEquityReceiveReq {
  // 家庭医生服务包 签约id
  int32 sign_id = 1;
  // 会员卡权益领取 卡订单号
  string order_sn= 2;
  // 权益类型：1商城优惠券，2子龙门店券，8打折卡，与type、equity_id二选一
  int32 type = 3;
  // 权益id，type、equity_id二选一
  int32 equity_id = 6;
  // 权益值 优惠券id
  string id = 4;
  // 前端不用传，用户id
  string scrm_id = 5;
  // 0开卡发券， 1月度领券
  int32 is_month = 7;
  // 创建时间
  string create_time = 8;

}

message QuerySignIdReq {
  // 订单号
  string order_sn = 1;
  // 前端不用传，用户id
  string scrm_id = 5;
}

message QuerySignIdRes {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  // 签约id
  int32 sign_id = 3;
}

message CheckCardIdReq {
  // 卡id
  int32 card_id = 1;
}

message CheckCardIdRes {
  // 状态码，200成功，400失败
  int32 code = 1;
  //消息
  string message = 2;
  message Data {
    // true 有效，false无效
    bool result = 3;
    // 有效时返回 卡名称
    string name = 4;
  }
  Data data = 3;
}
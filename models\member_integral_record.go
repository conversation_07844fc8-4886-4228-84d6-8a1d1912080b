package models

import (
	"time"
)

type MemberIntegralRecord struct {
	Integralid           string    `xorm:"not null pk comment('积分记录id') CHAR(50)"`
	Orderid              string    `xorm:"default 'NULL' comment('订单id') CHAR(80)"`
	Memberid             string    `xorm:"default 'NULL' comment('用户id') VARCHAR(50)"`
	Payamount            int       `xorm:"default NULL comment('实付金额') INT(10)"`
	Integralcount        int64     `xorm:"default NULL comment('积分变化值') INT(11)"`
	Surplusintegralcount int64     `xorm:"default NULL comment('剩余积分') INT(11)"`
	Integraltype         int       `xorm:"default NULL comment('添加积分类型') TINYINT(11)"`
	Integralreason       string    `xorm:"default 'NULL' comment('添加积分原因') VARCHAR(50)"`
	Ischeck              int       `xorm:"default NULL comment('积分反查状态(1--已反查，0--未反查)') TINYINT(1)"`
	Createdate           time.Time `xorm:"default 'current_timestamp()' comment('创建时间') TIMESTAMP"`
	Lasttime             time.Time `xorm:"not null default 'current_timestamp()' comment('最后操作时间') TIMESTAMP"`
	OrgId                int       `xorm:"comment('主体ID') INT(11)"`
	UserLevelId          int32     `xorm:"default 0 comment('用户会员等级') INT(11)"`
}

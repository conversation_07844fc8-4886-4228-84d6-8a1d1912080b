package sn

import (
	"fmt"
	"github.com/spf13/cast"
	"time"
)

type UPetOrderSN struct {
	PayId int32 //支付表自增ID
}

/*Generate
翻译自商城方法makeOrderSn
* 商城订单编号生成规则，n(n>=1)个订单表对应一个支付表，
* 生成订单编号(年取1位 + PayId取13位 + 第N个子订单取2位)
* 1000个会员同一微秒提订单，重复机率为1/100
* @version v2.9.10 秒杀版本添加
*/
func (s UPetOrderSN) Generate() string {
	nowYear := time.Now().Format("2006")
	//年取1位
	segOne := cast.ToString(cast.ToInt32(nowYear)%100%9 + 1)
	//支付id取13位
	segTwo := fmt.Sprintf("%013d", s.PayId)
	//第N个子订单取2位 N表示商城的运营商家 目前仅有自营，写死为1
	segThree := fmt.Sprintf("%02d", 1)

	return segOne + segTwo + segThree
}

package models

import "time"

type PinOrderPaymentRecords struct {
	Id               int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	PinOrderSn           string    `xorm:"not null default '''' comment('拼团订单ID（取的是订单中心订单编号逻辑）') VARCHAR(55)"`
	PayTradeNo           string    `xorm:"not null default '''' comment('支付中心支付流水号') VARCHAR(32)"`
	PayAmount         int32    `xorm:"not null default 0 comment('支付金额') VARCHAR(11)"`
	PayTime       time.Time `xorm:"comment('支付时间') DATETIME"`
	RefundTradeNo           string    `xorm:"not null default '''' comment('支付中心支付流水号') VARCHAR(32)"`
	RefundAmount         int32    `xorm:"not null default 0 comment('支付金额') VARCHAR(11)"`
	RefundTime       time.Time `xorm:"comment('支付时间') DATETIME"`
	CreateTime       time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	UpdateTime       time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
}
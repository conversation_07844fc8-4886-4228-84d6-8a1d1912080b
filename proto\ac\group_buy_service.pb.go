// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/group_buy_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//拼团活动列表的请求数据
type GroupBuyListRequest struct {
	//活动名称
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	//活动状态 0所有 1未开始（包括未开始与预告中） 2预告中 3显示中（包括预告中与进行中） 4已开始 5已结束 6已终止
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	//业务渠道 1阿文到家 5电商
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//拼团类型 1普通拼团
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	//当前多少页 从1开始
	BeginTimeDuring *DuringTime `protobuf:"bytes,5,opt,name=beginTimeDuring,proto3" json:"beginTimeDuring"`
	//结束时间段
	EndTimeDuring *DuringTime `protobuf:"bytes,6,opt,name=endTimeDuring,proto3" json:"endTimeDuring"`
	//分页参数
	Pagination           *Pagination `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GroupBuyListRequest) Reset()         { *m = GroupBuyListRequest{} }
func (m *GroupBuyListRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyListRequest) ProtoMessage()    {}
func (*GroupBuyListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{0}
}

func (m *GroupBuyListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyListRequest.Unmarshal(m, b)
}
func (m *GroupBuyListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyListRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyListRequest.Merge(m, src)
}
func (m *GroupBuyListRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyListRequest.Size(m)
}
func (m *GroupBuyListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyListRequest proto.InternalMessageInfo

func (m *GroupBuyListRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GroupBuyListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupBuyListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupBuyListRequest) GetBeginTimeDuring() *DuringTime {
	if m != nil {
		return m.BeginTimeDuring
	}
	return nil
}

func (m *GroupBuyListRequest) GetEndTimeDuring() *DuringTime {
	if m != nil {
		return m.EndTimeDuring
	}
	return nil
}

func (m *GroupBuyListRequest) GetPagination() *Pagination {
	if m != nil {
		return m.Pagination
	}
	return nil
}

//时间区间
type DuringTime struct {
	//开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=beginTime,proto3" json:"beginTime"`
	//结束时间 -M-D H:i:s格式
	EndTime              string   `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DuringTime) Reset()         { *m = DuringTime{} }
func (m *DuringTime) String() string { return proto.CompactTextString(m) }
func (*DuringTime) ProtoMessage()    {}
func (*DuringTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{1}
}

func (m *DuringTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DuringTime.Unmarshal(m, b)
}
func (m *DuringTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DuringTime.Marshal(b, m, deterministic)
}
func (m *DuringTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DuringTime.Merge(m, src)
}
func (m *DuringTime) XXX_Size() int {
	return xxx_messageInfo_DuringTime.Size(m)
}
func (m *DuringTime) XXX_DiscardUnknown() {
	xxx_messageInfo_DuringTime.DiscardUnknown(m)
}

var xxx_messageInfo_DuringTime proto.InternalMessageInfo

func (m *DuringTime) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *DuringTime) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

//分页参数
type Pagination struct {
	//当前多少页 从1开始 必传且必须大于0
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	//每页多少条数据 必传且必须大于0
	PageSize             int32    `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Pagination) Reset()         { *m = Pagination{} }
func (m *Pagination) String() string { return proto.CompactTextString(m) }
func (*Pagination) ProtoMessage()    {}
func (*Pagination) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{2}
}

func (m *Pagination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Pagination.Unmarshal(m, b)
}
func (m *Pagination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Pagination.Marshal(b, m, deterministic)
}
func (m *Pagination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Pagination.Merge(m, src)
}
func (m *Pagination) XXX_Size() int {
	return xxx_messageInfo_Pagination.Size(m)
}
func (m *Pagination) XXX_DiscardUnknown() {
	xxx_messageInfo_Pagination.DiscardUnknown(m)
}

var xxx_messageInfo_Pagination proto.InternalMessageInfo

func (m *Pagination) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *Pagination) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//团购活动列表
type GroupBuyListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total int32  `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//拼团活动信息
	Data                 []*GroupBuyData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GroupBuyListResponse) Reset()         { *m = GroupBuyListResponse{} }
func (m *GroupBuyListResponse) String() string { return proto.CompactTextString(m) }
func (*GroupBuyListResponse) ProtoMessage()    {}
func (*GroupBuyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{3}
}

func (m *GroupBuyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyListResponse.Unmarshal(m, b)
}
func (m *GroupBuyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyListResponse.Marshal(b, m, deterministic)
}
func (m *GroupBuyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyListResponse.Merge(m, src)
}
func (m *GroupBuyListResponse) XXX_Size() int {
	return xxx_messageInfo_GroupBuyListResponse.Size(m)
}
func (m *GroupBuyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyListResponse proto.InternalMessageInfo

func (m *GroupBuyListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupBuyListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupBuyListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GroupBuyListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GroupBuyListResponse) GetData() []*GroupBuyData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupBuyData struct {
	//活动的id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	BeginDate string `protobuf:"bytes,3,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	EndDate string `protobuf:"bytes,4,opt,name=endDate,proto3" json:"endDate"`
	//业务渠道 1阿文到家 5电商
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//拼团类型 1普通拼团
	Type int32 `protobuf:"varint,6,opt,name=type,proto3" json:"type"`
	//状态描述
	TypeDesc string `protobuf:"bytes,7,opt,name=typeDesc,proto3" json:"typeDesc"`
	//终止状态 0未终止 1已终止
	Stop int32 `protobuf:"varint,8,opt,name=stop,proto3" json:"stop"`
	//拼团活动信息 活动状态 1:未开始 2:预告中 3:进行中 4:已结束
	Status int32 `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	//状态描述 该状态描述包括自然状态 未开始 进行中 已结束 ；人为状态：已终止
	StatusDesc string `protobuf:"bytes,10,opt,name=statusDesc,proto3" json:"statusDesc"`
	//拼团的有效期 日
	ExpirationDay int32 `protobuf:"varint,11,opt,name=expirationDay,proto3" json:"expirationDay"`
	//拼团的有效期 小时
	ExpirationHour int32 `protobuf:"varint,12,opt,name=expirationHour,proto3" json:"expirationHour"`
	//拼团的有效期 分钟
	ExpirationMinute int32 `protobuf:"varint,13,opt,name=expirationMinute,proto3" json:"expirationMinute"`
	//是否可叠加其他活动 0不可叠加 1可叠加
	AdditionPromotionAble int32 `protobuf:"varint,14,opt,name=additionPromotionAble,proto3" json:"additionPromotionAble"`
	//叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
	AdditionPromotionType string `protobuf:"bytes,15,opt,name=additionPromotionType,proto3" json:"additionPromotionType"`
	//是否开启预告 0不开启 1开启
	PreviewSwitch int32 `protobuf:"varint,16,opt,name=previewSwitch,proto3" json:"previewSwitch"`
	//预告提前时间，单位为小时
	PreviewHour int32 `protobuf:"varint,17,opt,name=previewHour,proto3" json:"previewHour"`
	//预告开始时间，当开启预告时计算并写入，避免查询时在begin_date上使用时间函数计算
	PreviewBeginTime string `protobuf:"bytes,18,opt,name=previewBeginTime,proto3" json:"previewBeginTime"`
	//是否模拟成团 0否1是
	MockSuccess int32 `protobuf:"varint,19,opt,name=mockSuccess,proto3" json:"mockSuccess"`
	//模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
	MockSuccessTarget int32 `protobuf:"varint,20,opt,name=mockSuccessTarget,proto3" json:"mockSuccessTarget"`
	//模拟成团最低参团人数
	MockSuccessMember int32 `protobuf:"varint,21,opt,name=mockSuccessMember,proto3" json:"mockSuccessMember"`
	//是否免邮费 0否1是
	DeliveryFree int32 `protobuf:"varint,22,opt,name=deliveryFree,proto3" json:"deliveryFree"`
	//'门店财务编码 仅在channel_id=1时（活即阿文到家的拼团动）该字段的值才有意义
	FinanceCode string `protobuf:"bytes,23,opt,name=financeCode,proto3" json:"financeCode"`
	//创建时间
	CreateTime string `protobuf:"bytes,24,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,25,opt,name=updateTime,proto3" json:"updateTime"`
	//成团用户数（去重）
	SuccessMemberCount int32 `protobuf:"varint,26,opt,name=successMemberCount,proto3" json:"successMemberCount"`
	//成团真人订单数
	SuccessOrderCount int32 `protobuf:"varint,27,opt,name=successOrderCount,proto3" json:"successOrderCount"`
	//参加活动的商品数量
	ProductCount int32 `protobuf:"varint,28,opt,name=ProductCount,proto3" json:"ProductCount"`
	//异常数量统计
	ExceptionCount int32 `protobuf:"varint,29,opt,name=exception_count,json=exceptionCount,proto3" json:"exception_count"`
	// 审核原因
	CheckReason          string   `protobuf:"bytes,30,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyData) Reset()         { *m = GroupBuyData{} }
func (m *GroupBuyData) String() string { return proto.CompactTextString(m) }
func (*GroupBuyData) ProtoMessage()    {}
func (*GroupBuyData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{4}
}

func (m *GroupBuyData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyData.Unmarshal(m, b)
}
func (m *GroupBuyData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyData.Marshal(b, m, deterministic)
}
func (m *GroupBuyData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyData.Merge(m, src)
}
func (m *GroupBuyData) XXX_Size() int {
	return xxx_messageInfo_GroupBuyData.Size(m)
}
func (m *GroupBuyData) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyData.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyData proto.InternalMessageInfo

func (m *GroupBuyData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GroupBuyData) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *GroupBuyData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *GroupBuyData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupBuyData) GetTypeDesc() string {
	if m != nil {
		return m.TypeDesc
	}
	return ""
}

func (m *GroupBuyData) GetStop() int32 {
	if m != nil {
		return m.Stop
	}
	return 0
}

func (m *GroupBuyData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupBuyData) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

func (m *GroupBuyData) GetExpirationDay() int32 {
	if m != nil {
		return m.ExpirationDay
	}
	return 0
}

func (m *GroupBuyData) GetExpirationHour() int32 {
	if m != nil {
		return m.ExpirationHour
	}
	return 0
}

func (m *GroupBuyData) GetExpirationMinute() int32 {
	if m != nil {
		return m.ExpirationMinute
	}
	return 0
}

func (m *GroupBuyData) GetAdditionPromotionAble() int32 {
	if m != nil {
		return m.AdditionPromotionAble
	}
	return 0
}

func (m *GroupBuyData) GetAdditionPromotionType() string {
	if m != nil {
		return m.AdditionPromotionType
	}
	return ""
}

func (m *GroupBuyData) GetPreviewSwitch() int32 {
	if m != nil {
		return m.PreviewSwitch
	}
	return 0
}

func (m *GroupBuyData) GetPreviewHour() int32 {
	if m != nil {
		return m.PreviewHour
	}
	return 0
}

func (m *GroupBuyData) GetPreviewBeginTime() string {
	if m != nil {
		return m.PreviewBeginTime
	}
	return ""
}

func (m *GroupBuyData) GetMockSuccess() int32 {
	if m != nil {
		return m.MockSuccess
	}
	return 0
}

func (m *GroupBuyData) GetMockSuccessTarget() int32 {
	if m != nil {
		return m.MockSuccessTarget
	}
	return 0
}

func (m *GroupBuyData) GetMockSuccessMember() int32 {
	if m != nil {
		return m.MockSuccessMember
	}
	return 0
}

func (m *GroupBuyData) GetDeliveryFree() int32 {
	if m != nil {
		return m.DeliveryFree
	}
	return 0
}

func (m *GroupBuyData) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupBuyData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GroupBuyData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *GroupBuyData) GetSuccessMemberCount() int32 {
	if m != nil {
		return m.SuccessMemberCount
	}
	return 0
}

func (m *GroupBuyData) GetSuccessOrderCount() int32 {
	if m != nil {
		return m.SuccessOrderCount
	}
	return 0
}

func (m *GroupBuyData) GetProductCount() int32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

func (m *GroupBuyData) GetExceptionCount() int32 {
	if m != nil {
		return m.ExceptionCount
	}
	return 0
}

func (m *GroupBuyData) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

//添加活动
type GroupBuyCreateRequest struct {
	// 名称
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	// 业务渠道 1阿文到家 5电商
	ChannelId int32 `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId"`
	// 门店财务编码 仅在channel_id=1时（即阿文到家的团购活动）才有值
	FinanceCode string `protobuf:"bytes,3,opt,name=financeCode,proto3" json:"financeCode"`
	// 开始时间
	BeginDate string `protobuf:"bytes,4,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束时间
	EndDate string `protobuf:"bytes,5,opt,name=endDate,proto3" json:"endDate"`
	// 拼团类型 1普通拼团，
	Type int32 `protobuf:"varint,6,opt,name=type,proto3" json:"type"`
	// 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟），用户开团后，需要在1日内成团，超时则拼团失败
	ExpirationMinute int32 `protobuf:"varint,7,opt,name=expirationMinute,proto3" json:"expirationMinute"`
	// 是否可叠加其他活动，默认不可以 0不可叠加 1可叠加
	AdditionPromotionAble int32 `protobuf:"varint,8,opt,name=additionPromotionAble,proto3" json:"additionPromotionAble"`
	// 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
	AdditionPromotionType string `protobuf:"bytes,9,opt,name=additionPromotionType,proto3" json:"additionPromotionType"`
	// 是否开启活动预告 0不开启 1开启
	PreviewSwitch int32 `protobuf:"varint,10,opt,name=previewSwitch,proto3" json:"previewSwitch"`
	// 预告提前时间，单位为小时
	PreviewHour int32 `protobuf:"varint,11,opt,name=previewHour,proto3" json:"previewHour"`
	// 是否模拟成团 0否1是
	MockSuccess int32 `protobuf:"varint,12,opt,name=mockSuccess,proto3" json:"mockSuccess"`
	// 模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
	MockSuccessTarget int32 `protobuf:"varint,13,opt,name=mockSuccessTarget,proto3" json:"mockSuccessTarget"`
	// 模拟成团最低参团人数，≥1的整数
	MockSuccessMember int32 `protobuf:"varint,14,opt,name=mockSuccessMember,proto3" json:"mockSuccessMember"`
	// 是否免邮费 0否1是
	DeliveryFree int32 `protobuf:"varint,15,opt,name=deliveryFree,proto3" json:"deliveryFree"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,16,opt,name=userId,proto3" json:"userId"`
	//用户名称，即登录人姓名
	UserName             string   `protobuf:"bytes,17,opt,name=userName,proto3" json:"userName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyCreateRequest) Reset()         { *m = GroupBuyCreateRequest{} }
func (m *GroupBuyCreateRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyCreateRequest) ProtoMessage()    {}
func (*GroupBuyCreateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{5}
}

func (m *GroupBuyCreateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyCreateRequest.Unmarshal(m, b)
}
func (m *GroupBuyCreateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyCreateRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyCreateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyCreateRequest.Merge(m, src)
}
func (m *GroupBuyCreateRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyCreateRequest.Size(m)
}
func (m *GroupBuyCreateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyCreateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyCreateRequest proto.InternalMessageInfo

func (m *GroupBuyCreateRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetExpirationMinute() int32 {
	if m != nil {
		return m.ExpirationMinute
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetAdditionPromotionAble() int32 {
	if m != nil {
		return m.AdditionPromotionAble
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetAdditionPromotionType() string {
	if m != nil {
		return m.AdditionPromotionType
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetPreviewSwitch() int32 {
	if m != nil {
		return m.PreviewSwitch
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetPreviewHour() int32 {
	if m != nil {
		return m.PreviewHour
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetMockSuccess() int32 {
	if m != nil {
		return m.MockSuccess
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetMockSuccessTarget() int32 {
	if m != nil {
		return m.MockSuccessTarget
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetMockSuccessMember() int32 {
	if m != nil {
		return m.MockSuccessMember
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetDeliveryFree() int32 {
	if m != nil {
		return m.DeliveryFree
	}
	return 0
}

func (m *GroupBuyCreateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GroupBuyCreateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

//更新活动
type GroupBuyUpdateRequest struct {
	//ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 业务渠道 1阿文到家 5电商
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	// 门店财务编码 仅在channel_id=1时（即阿文到家的团购活动）才有值
	FinanceCode string `protobuf:"bytes,4,opt,name=financeCode,proto3" json:"financeCode"`
	// 开始时间
	BeginDate string `protobuf:"bytes,5,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束时间
	EndDate string `protobuf:"bytes,6,opt,name=endDate,proto3" json:"endDate"`
	// 拼团类型 1普通拼团，
	Type int32 `protobuf:"varint,7,opt,name=type,proto3" json:"type"`
	// 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟），用户开团后，需要在1日内成团，超时则拼团失败
	ExpirationMinute int32 `protobuf:"varint,8,opt,name=expirationMinute,proto3" json:"expirationMinute"`
	// 是否可叠加其他活动，默认不可以 0不可叠加 1可叠加
	AdditionPromotionAble int32 `protobuf:"varint,9,opt,name=additionPromotionAble,proto3" json:"additionPromotionAble"`
	// 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
	AdditionPromotionType string `protobuf:"bytes,10,opt,name=additionPromotionType,proto3" json:"additionPromotionType"`
	// 是否开启活动预告 0不开启 1开启
	PreviewSwitch int32 `protobuf:"varint,11,opt,name=previewSwitch,proto3" json:"previewSwitch"`
	// 预告提前时间，单位为小时
	PreviewHour int32 `protobuf:"varint,12,opt,name=previewHour,proto3" json:"previewHour"`
	// 是否模拟成团 0否1是
	MockSuccess int32 `protobuf:"varint,13,opt,name=mockSuccess,proto3" json:"mockSuccess"`
	// 模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
	MockSuccessTarget int32 `protobuf:"varint,14,opt,name=mockSuccessTarget,proto3" json:"mockSuccessTarget"`
	// 模拟成团最低参团人数，≥1的整数
	MockSuccessMember int32 `protobuf:"varint,15,opt,name=mockSuccessMember,proto3" json:"mockSuccessMember"`
	// 是否免邮费 0否1是
	DeliveryFree int32 `protobuf:"varint,16,opt,name=deliveryFree,proto3" json:"deliveryFree"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,17,opt,name=userId,proto3" json:"userId"`
	//用户名称，即登录人姓名
	UserName             string   `protobuf:"bytes,18,opt,name=userName,proto3" json:"userName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyUpdateRequest) Reset()         { *m = GroupBuyUpdateRequest{} }
func (m *GroupBuyUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyUpdateRequest) ProtoMessage()    {}
func (*GroupBuyUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{6}
}

func (m *GroupBuyUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyUpdateRequest.Unmarshal(m, b)
}
func (m *GroupBuyUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyUpdateRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyUpdateRequest.Merge(m, src)
}
func (m *GroupBuyUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyUpdateRequest.Size(m)
}
func (m *GroupBuyUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyUpdateRequest proto.InternalMessageInfo

func (m *GroupBuyUpdateRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetExpirationMinute() int32 {
	if m != nil {
		return m.ExpirationMinute
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetAdditionPromotionAble() int32 {
	if m != nil {
		return m.AdditionPromotionAble
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetAdditionPromotionType() string {
	if m != nil {
		return m.AdditionPromotionType
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetPreviewSwitch() int32 {
	if m != nil {
		return m.PreviewSwitch
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetPreviewHour() int32 {
	if m != nil {
		return m.PreviewHour
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetMockSuccess() int32 {
	if m != nil {
		return m.MockSuccess
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetMockSuccessTarget() int32 {
	if m != nil {
		return m.MockSuccessTarget
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetMockSuccessMember() int32 {
	if m != nil {
		return m.MockSuccessMember
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetDeliveryFree() int32 {
	if m != nil {
		return m.DeliveryFree
	}
	return 0
}

func (m *GroupBuyUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GroupBuyUpdateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

//拼团商品列表的请求数据
type GetGroupBuyProductListRequest struct {
	// 拼团活动id
	Gid []int32 `protobuf:"varint,1,rep,packed,name=gid,proto3" json:"gid"`
	//产品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	//  商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//  商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//  商品的产品id
	ChannelId int32 `protobuf:"varint,5,opt,name=ChannelId,proto3" json:"ChannelId"`
	//活动状态 0所有  1未开始（包括未开始与预告中） 2预告中 3显示中（包括预告中与进行中） 4已开始 5已结束 6已终止
	//说明：2与3是特殊的状态 一般用于用户端（小程序 app）,后台只返回 1 4 5 6的状态，
	//如果前端需要区分2 3状态时，在请求方将状态通过字段判断改为2或者3
	Status int32 `protobuf:"varint,6,opt,name=Status,proto3" json:"Status"`
	//排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
	OrderBy int32 `protobuf:"varint,7,opt,name=orderBy,proto3" json:"orderBy"`
	//分页参数
	Pagination *Pagination `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination"`
	//是否导出 1导出
	Export int32 `protobuf:"varint,9,opt,name=export,proto3" json:"export"`
	// 1异常商品
	Type int32 `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,11,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupBuyProductListRequest) Reset()         { *m = GetGroupBuyProductListRequest{} }
func (m *GetGroupBuyProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyProductListRequest) ProtoMessage()    {}
func (*GetGroupBuyProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{7}
}

func (m *GetGroupBuyProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyProductListRequest.Unmarshal(m, b)
}
func (m *GetGroupBuyProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyProductListRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyProductListRequest.Merge(m, src)
}
func (m *GetGroupBuyProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyProductListRequest.Size(m)
}
func (m *GetGroupBuyProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyProductListRequest proto.InternalMessageInfo

func (m *GetGroupBuyProductListRequest) GetGid() []int32 {
	if m != nil {
		return m.Gid
	}
	return nil
}

func (m *GetGroupBuyProductListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetGroupBuyProductListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetPagination() *Pagination {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetGroupBuyProductListRequest) GetExport() int32 {
	if m != nil {
		return m.Export
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetGroupBuyProductListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//拼团商品数据
type GroupBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 拼团活动信息 所属拼团id
	Gid int32 `protobuf:"varint,2,opt,name=gid,proto3" json:"gid"`
	// 成团人数
	SuccessNum int32 `protobuf:"varint,3,opt,name=successNum,proto3" json:"successNum"`
	// 拼团价 单位分
	Price int32 `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	// 排序
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort"`
	// 限购类型 1：每人限购n件 2：每人限购<=n件 n为buy_limit_num字段返回的数字
	BuyLimitType int32 `protobuf:"varint,6,opt,name=buyLimitType,proto3" json:"buyLimitType"`
	// 限购的数量，与buy_limit_type字段配合使用
	BuyLimitNum int32 `protobuf:"varint,7,opt,name=buyLimitNum,proto3" json:"buyLimitNum"`
	// 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次
	OpenNum int32 `protobuf:"varint,8,opt,name=openNum,proto3" json:"openNum"`
	// 每人参团次数 0表示不限制，>=0表示每个用户最多可参团少次
	PartNum int32 `protobuf:"varint,9,opt,name=partNum,proto3" json:"partNum"`
	// 初始参团人数
	InitPartNum int32 `protobuf:"varint,10,opt,name=initPartNum,proto3" json:"initPartNum"`
	// 拼团成功送的优惠券ID，多个用，隔开
	SuccessCoupon string `protobuf:"bytes,11,opt,name=successCoupon,proto3" json:"successCoupon"`
	// 拼团成功每人最多送多少个优惠券 -1表示不限制 默认为1
	SuccessCouponLimitNum int32 `protobuf:"varint,12,opt,name=successCouponLimitNum,proto3" json:"successCouponLimitNum"`
	// 拼团失败送的优惠券ID，多个用，隔开
	FailCoupon string `protobuf:"bytes,13,opt,name=failCoupon,proto3" json:"failCoupon"`
	// 拼团失败每人最多送多少个优惠券  -1表示不限制 默认为1
	FailCouponLimitNum int32 `protobuf:"varint,14,opt,name=failCouponLimitNum,proto3" json:"failCouponLimitNum"`
	// 分享卡片图片地址
	ShareImgUrl string `protobuf:"bytes,15,opt,name=shareImgUrl,proto3" json:"shareImgUrl"`
	//商品sku id
	SkuId int32 `protobuf:"varint,16,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,17,opt,name=productId,proto3" json:"productId"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,18,opt,name=channelId,proto3" json:"channelId"`
	// 市场价格 单位分
	MarketPrice int32 `protobuf:"varint,19,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 商品图片
	Pic string `protobuf:"bytes,20,opt,name=pic,proto3" json:"pic"`
	// 商品库存
	Stock int32 `protobuf:"varint,21,opt,name=stock,proto3" json:"stock"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,22,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,23,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品skuI
	ChildSkuIds []*ChildRen `protobuf:"bytes,24,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	//商品名称
	ProductName string `protobuf:"bytes,25,opt,name=productName,proto3" json:"productName"`
	// 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
	CanBeEdited int32 `protobuf:"varint,26,opt,name=canBeEdited,proto3" json:"canBeEdited"`
	// 是否可被删除 0 不可删除 1 可删除 用于boss后台
	CanBeDeleted int32 `protobuf:"varint,27,opt,name=canBeDeleted,proto3" json:"canBeDeleted"`
	//参团人数 包括拼主与参团人数
	PartUserCount int32 `protobuf:"varint,28,opt,name=PartUserCount,proto3" json:"PartUserCount"`
	//订单数
	TotalOrderCount int32 `protobuf:"varint,29,opt,name=totalOrderCount,proto3" json:"totalOrderCount"`
	//活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
	Status int32 `protobuf:"varint,30,opt,name=status,proto3" json:"status"`
	//活动终止状态 0未终止 1已终止
	Stop int32 `protobuf:"varint,31,opt,name=stop,proto3" json:"stop"`
	//创建时间
	CreateTime string `protobuf:"bytes,32,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,33,opt,name=updateTime,proto3" json:"updateTime"`
	//商品折扣率
	PriceRatio float64 `protobuf:"fixed64,34,opt,name=priceRatio,proto3" json:"priceRatio"`
	// 是否异常 1:异常 0：正常
	IsNormal int32 `protobuf:"varint,35,opt,name=is_normal,json=isNormal,proto3" json:"is_normal"`
	// 是否标记 1:是 0：否
	IsMark int32 `protobuf:"varint,36,opt,name=is_mark,json=isMark,proto3" json:"is_mark"`
	//R1集采价 单位分
	R1PurchasePrice int32 `protobuf:"varint,37,opt,name=R1PurchasePrice,proto3" json:"R1PurchasePrice"`
	// 标记为正常的异常折扣
	MarkDiscount float64 `protobuf:"fixed64,38,opt,name=MarkDiscount,proto3" json:"MarkDiscount"`
	// 标记为正常的采购价(分)
	MarkPurchasePrice    int32    `protobuf:"varint,39,opt,name=MarkPurchasePrice,proto3" json:"MarkPurchasePrice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductData) Reset()         { *m = GroupBuyProductData{} }
func (m *GroupBuyProductData) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductData) ProtoMessage()    {}
func (*GroupBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{8}
}

func (m *GroupBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductData.Unmarshal(m, b)
}
func (m *GroupBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductData.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductData.Merge(m, src)
}
func (m *GroupBuyProductData) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductData.Size(m)
}
func (m *GroupBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductData proto.InternalMessageInfo

func (m *GroupBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyProductData) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyProductData) GetSuccessNum() int32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

func (m *GroupBuyProductData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GroupBuyProductData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *GroupBuyProductData) GetBuyLimitType() int32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *GroupBuyProductData) GetBuyLimitNum() int32 {
	if m != nil {
		return m.BuyLimitNum
	}
	return 0
}

func (m *GroupBuyProductData) GetOpenNum() int32 {
	if m != nil {
		return m.OpenNum
	}
	return 0
}

func (m *GroupBuyProductData) GetPartNum() int32 {
	if m != nil {
		return m.PartNum
	}
	return 0
}

func (m *GroupBuyProductData) GetInitPartNum() int32 {
	if m != nil {
		return m.InitPartNum
	}
	return 0
}

func (m *GroupBuyProductData) GetSuccessCoupon() string {
	if m != nil {
		return m.SuccessCoupon
	}
	return ""
}

func (m *GroupBuyProductData) GetSuccessCouponLimitNum() int32 {
	if m != nil {
		return m.SuccessCouponLimitNum
	}
	return 0
}

func (m *GroupBuyProductData) GetFailCoupon() string {
	if m != nil {
		return m.FailCoupon
	}
	return ""
}

func (m *GroupBuyProductData) GetFailCouponLimitNum() int32 {
	if m != nil {
		return m.FailCouponLimitNum
	}
	return 0
}

func (m *GroupBuyProductData) GetShareImgUrl() string {
	if m != nil {
		return m.ShareImgUrl
	}
	return ""
}

func (m *GroupBuyProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuyProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuyProductData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *GroupBuyProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *GroupBuyProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *GroupBuyProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *GroupBuyProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *GroupBuyProductData) GetChildSkuIds() []*ChildRen {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

func (m *GroupBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupBuyProductData) GetCanBeEdited() int32 {
	if m != nil {
		return m.CanBeEdited
	}
	return 0
}

func (m *GroupBuyProductData) GetCanBeDeleted() int32 {
	if m != nil {
		return m.CanBeDeleted
	}
	return 0
}

func (m *GroupBuyProductData) GetPartUserCount() int32 {
	if m != nil {
		return m.PartUserCount
	}
	return 0
}

func (m *GroupBuyProductData) GetTotalOrderCount() int32 {
	if m != nil {
		return m.TotalOrderCount
	}
	return 0
}

func (m *GroupBuyProductData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupBuyProductData) GetStop() int32 {
	if m != nil {
		return m.Stop
	}
	return 0
}

func (m *GroupBuyProductData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GroupBuyProductData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *GroupBuyProductData) GetPriceRatio() float64 {
	if m != nil {
		return m.PriceRatio
	}
	return 0
}

func (m *GroupBuyProductData) GetIsNormal() int32 {
	if m != nil {
		return m.IsNormal
	}
	return 0
}

func (m *GroupBuyProductData) GetIsMark() int32 {
	if m != nil {
		return m.IsMark
	}
	return 0
}

func (m *GroupBuyProductData) GetR1PurchasePrice() int32 {
	if m != nil {
		return m.R1PurchasePrice
	}
	return 0
}

func (m *GroupBuyProductData) GetMarkDiscount() float64 {
	if m != nil {
		return m.MarkDiscount
	}
	return 0
}

func (m *GroupBuyProductData) GetMarkPurchasePrice() int32 {
	if m != nil {
		return m.MarkPurchasePrice
	}
	return 0
}

//团购商品列表
type GroupBuyProductCustomListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//拼团商品信息
	Data                 []*GroupBuyProductCustomList `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GroupBuyProductCustomListResponse) Reset()         { *m = GroupBuyProductCustomListResponse{} }
func (m *GroupBuyProductCustomListResponse) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductCustomListResponse) ProtoMessage()    {}
func (*GroupBuyProductCustomListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{9}
}

func (m *GroupBuyProductCustomListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductCustomListResponse.Unmarshal(m, b)
}
func (m *GroupBuyProductCustomListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductCustomListResponse.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductCustomListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductCustomListResponse.Merge(m, src)
}
func (m *GroupBuyProductCustomListResponse) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductCustomListResponse.Size(m)
}
func (m *GroupBuyProductCustomListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductCustomListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductCustomListResponse proto.InternalMessageInfo

func (m *GroupBuyProductCustomListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupBuyProductCustomListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupBuyProductCustomListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GroupBuyProductCustomListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GroupBuyProductCustomListResponse) GetData() []*GroupBuyProductCustomList {
	if m != nil {
		return m.Data
	}
	return nil
}

//拼团商品列表客户端返回数据
type GroupBuyProductCustomList struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 拼团活动信息 所属拼团id
	Gid int32 `protobuf:"varint,2,opt,name=gid,proto3" json:"gid"`
	// 拼团价 单位分
	Price int32 `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,6,opt,name=channelId,proto3" json:"channelId"`
	// 市场价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 商品图片
	Pic string `protobuf:"bytes,8,opt,name=pic,proto3" json:"pic"`
	//商品名称
	ProductName string `protobuf:"bytes,9,opt,name=productName,proto3" json:"productName"`
	//参团人数 包括拼主与参团人数
	PartUserCount int32 `protobuf:"varint,10,opt,name=partUserCount,proto3" json:"partUserCount"`
	// 初始参团人数
	InitPartNum          int32    `protobuf:"varint,11,opt,name=initPartNum,proto3" json:"initPartNum"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductCustomList) Reset()         { *m = GroupBuyProductCustomList{} }
func (m *GroupBuyProductCustomList) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductCustomList) ProtoMessage()    {}
func (*GroupBuyProductCustomList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{10}
}

func (m *GroupBuyProductCustomList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductCustomList.Unmarshal(m, b)
}
func (m *GroupBuyProductCustomList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductCustomList.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductCustomList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductCustomList.Merge(m, src)
}
func (m *GroupBuyProductCustomList) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductCustomList.Size(m)
}
func (m *GroupBuyProductCustomList) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductCustomList.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductCustomList proto.InternalMessageInfo

func (m *GroupBuyProductCustomList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *GroupBuyProductCustomList) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupBuyProductCustomList) GetPartUserCount() int32 {
	if m != nil {
		return m.PartUserCount
	}
	return 0
}

func (m *GroupBuyProductCustomList) GetInitPartNum() int32 {
	if m != nil {
		return m.InitPartNum
	}
	return 0
}

//拼团商品详细数据包含部分拼团信息
type GroupBuyProductDetailData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 拼团活动信息 所属拼团id
	Gid int32 `protobuf:"varint,2,opt,name=gid,proto3" json:"gid"`
	// 成团人数
	SuccessNum int32 `protobuf:"varint,3,opt,name=successNum,proto3" json:"successNum"`
	// 拼团价 单位分
	Price int32 `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	// 排序
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort"`
	// 限购类型 1：每人限购n件 2：每人限购<=n件 n为buy_limit_num字段返回的数字
	BuyLimitType int32 `protobuf:"varint,6,opt,name=buyLimitType,proto3" json:"buyLimitType"`
	// 限购的数量，与buy_limit_type字段配合使用
	BuyLimitNum int32 `protobuf:"varint,7,opt,name=buyLimitNum,proto3" json:"buyLimitNum"`
	// 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次
	OpenNum int32 `protobuf:"varint,8,opt,name=openNum,proto3" json:"openNum"`
	// 每人参团次数 0表示不限制，>=0表示每个用户最多可参团少次
	PartNum int32 `protobuf:"varint,9,opt,name=partNum,proto3" json:"partNum"`
	// 初始参团人数
	InitPartNum int32 `protobuf:"varint,10,opt,name=initPartNum,proto3" json:"initPartNum"`
	// 拼团成功送的优惠券ID，多个用，隔开
	SuccessCoupon string `protobuf:"bytes,11,opt,name=successCoupon,proto3" json:"successCoupon"`
	// 拼团成功每人最多送多少个优惠券 -1表示不限制 默认为1
	SuccessCouponLimitNum int32 `protobuf:"varint,12,opt,name=successCouponLimitNum,proto3" json:"successCouponLimitNum"`
	// 拼团失败送的优惠券ID，多个用，隔开
	FailCoupon string `protobuf:"bytes,13,opt,name=failCoupon,proto3" json:"failCoupon"`
	// 拼团失败每人最多送多少个优惠券  -1表示不限制 默认为1
	FailCouponLimitNum int32 `protobuf:"varint,14,opt,name=failCouponLimitNum,proto3" json:"failCouponLimitNum"`
	// 分享卡片图片地址
	ShareImgUrl string `protobuf:"bytes,15,opt,name=shareImgUrl,proto3" json:"shareImgUrl"`
	//商品sku id
	SkuId int32 `protobuf:"varint,16,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,17,opt,name=productId,proto3" json:"productId"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,18,opt,name=channelId,proto3" json:"channelId"`
	// 拼团活动信息 预告开关
	PreviewSwitch int32 `protobuf:"varint,19,opt,name=previewSwitch,proto3" json:"previewSwitch"`
	//拼团活动信息 预告提前时间，单位为小时
	PreviewHour int32 `protobuf:"varint,20,opt,name=previewHour,proto3" json:"previewHour"`
	// 拼团活动信息 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	BeginDate string `protobuf:"bytes,21,opt,name=beginDate,proto3" json:"beginDate"`
	// 拼团活动信息 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	EndDate string `protobuf:"bytes,22,opt,name=endDate,proto3" json:"endDate"`
	//拼团活动信息 终止状态 0未终止 1已终止
	Stop int32 `protobuf:"varint,23,opt,name=stop,proto3" json:"stop"`
	//拼团活动信息 拼团类型 1普通拼团，目前仅有普通拼团
	Type int32 `protobuf:"varint,24,opt,name=type,proto3" json:"type"`
	//拼团活动信息 是否可叠加其他活动， 0不可叠加 1可叠加
	AdditionPromotionAble int32 `protobuf:"varint,25,opt,name=additionPromotionAble,proto3" json:"additionPromotionAble"`
	//拼团活动信息 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
	AdditionPromotionType string `protobuf:"bytes,26,opt,name=additionPromotionType,proto3" json:"additionPromotionType"`
	//拼团活动信息 活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
	Status int32 `protobuf:"varint,27,opt,name=status,proto3" json:"status"`
	// 拼团活动信息 预告开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	PreviewBeginTime string `protobuf:"bytes,28,opt,name=PreviewBeginTime,proto3" json:"PreviewBeginTime"`
	// 拼团活动信息 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟）
	ExpirationMinute int32 `protobuf:"varint,29,opt,name=ExpirationMinute,proto3" json:"ExpirationMinute"`
	// 市场价格 单位分
	MarketPrice int32 `protobuf:"varint,30,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 商品图片
	Pic string `protobuf:"bytes,31,opt,name=pic,proto3" json:"pic"`
	//商品名称
	ProductName string `protobuf:"bytes,32,opt,name=productName,proto3" json:"productName"`
	// 商品库存
	Stock int32 `protobuf:"varint,33,opt,name=stock,proto3" json:"stock"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,34,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,35,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品skuI
	ChildSkuIds []*ChildRen `protobuf:"bytes,36,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	//创建时间
	CreateTime string `protobuf:"bytes,37,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime           string   `protobuf:"bytes,38,opt,name=updateTime,proto3" json:"updateTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductDetailData) Reset()         { *m = GroupBuyProductDetailData{} }
func (m *GroupBuyProductDetailData) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductDetailData) ProtoMessage()    {}
func (*GroupBuyProductDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{11}
}

func (m *GroupBuyProductDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductDetailData.Unmarshal(m, b)
}
func (m *GroupBuyProductDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductDetailData.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductDetailData.Merge(m, src)
}
func (m *GroupBuyProductDetailData) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductDetailData.Size(m)
}
func (m *GroupBuyProductDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductDetailData proto.InternalMessageInfo

func (m *GroupBuyProductDetailData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetSuccessNum() int32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetBuyLimitType() int32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetBuyLimitNum() int32 {
	if m != nil {
		return m.BuyLimitNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetOpenNum() int32 {
	if m != nil {
		return m.OpenNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPartNum() int32 {
	if m != nil {
		return m.PartNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetInitPartNum() int32 {
	if m != nil {
		return m.InitPartNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetSuccessCoupon() string {
	if m != nil {
		return m.SuccessCoupon
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetSuccessCouponLimitNum() int32 {
	if m != nil {
		return m.SuccessCouponLimitNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetFailCoupon() string {
	if m != nil {
		return m.FailCoupon
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetFailCouponLimitNum() int32 {
	if m != nil {
		return m.FailCouponLimitNum
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetShareImgUrl() string {
	if m != nil {
		return m.ShareImgUrl
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPreviewSwitch() int32 {
	if m != nil {
		return m.PreviewSwitch
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPreviewHour() int32 {
	if m != nil {
		return m.PreviewHour
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetStop() int32 {
	if m != nil {
		return m.Stop
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetAdditionPromotionAble() int32 {
	if m != nil {
		return m.AdditionPromotionAble
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetAdditionPromotionType() string {
	if m != nil {
		return m.AdditionPromotionType
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPreviewBeginTime() string {
	if m != nil {
		return m.PreviewBeginTime
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetExpirationMinute() int32 {
	if m != nil {
		return m.ExpirationMinute
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *GroupBuyProductDetailData) GetChildSkuIds() []*ChildRen {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

func (m *GroupBuyProductDetailData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GroupBuyProductDetailData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

//团购商品列表
type GetGroupBuyProductListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//拼团商品信息
	Data []*GroupBuyProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	//异常商品数
	NonormalNum int32 `protobuf:"varint,6,opt,name=nonormal_num,json=nonormalNum,proto3" json:"nonormal_num"`
	//正常商品数
	NormalNum            int32    `protobuf:"varint,7,opt,name=normal_num,json=normalNum,proto3" json:"normal_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupBuyProductListResponse) Reset()         { *m = GetGroupBuyProductListResponse{} }
func (m *GetGroupBuyProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyProductListResponse) ProtoMessage()    {}
func (*GetGroupBuyProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{12}
}

func (m *GetGroupBuyProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyProductListResponse.Unmarshal(m, b)
}
func (m *GetGroupBuyProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyProductListResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyProductListResponse.Merge(m, src)
}
func (m *GetGroupBuyProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyProductListResponse.Size(m)
}
func (m *GetGroupBuyProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyProductListResponse proto.InternalMessageInfo

func (m *GetGroupBuyProductListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupBuyProductListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupBuyProductListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupBuyProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGroupBuyProductListResponse) GetData() []*GroupBuyProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetGroupBuyProductListResponse) GetNonormalNum() int32 {
	if m != nil {
		return m.NonormalNum
	}
	return 0
}

func (m *GetGroupBuyProductListResponse) GetNormalNum() int32 {
	if m != nil {
		return m.NormalNum
	}
	return 0
}

//新增拼团活动商品
type CreateGroupBuyProductRequest struct {
	//  商品sku id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//  商品的产品id
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveGroupBuyProductData `protobuf:"bytes,4,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupBuyProductRequest) Reset()         { *m = CreateGroupBuyProductRequest{} }
func (m *CreateGroupBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*CreateGroupBuyProductRequest) ProtoMessage()    {}
func (*CreateGroupBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{13}
}

func (m *CreateGroupBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupBuyProductRequest.Unmarshal(m, b)
}
func (m *CreateGroupBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *CreateGroupBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupBuyProductRequest.Merge(m, src)
}
func (m *CreateGroupBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_CreateGroupBuyProductRequest.Size(m)
}
func (m *CreateGroupBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupBuyProductRequest proto.InternalMessageInfo

func (m *CreateGroupBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CreateGroupBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CreateGroupBuyProductRequest) GetSaveData() *SaveGroupBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *CreateGroupBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//更新拼团商品
type UpdateGroupBuyProductRequest struct {
	// 需要更新的记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//  商品sku id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//  商品的产品id
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveGroupBuyProductData `protobuf:"bytes,5,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupBuyProductRequest) Reset()         { *m = UpdateGroupBuyProductRequest{} }
func (m *UpdateGroupBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupBuyProductRequest) ProtoMessage()    {}
func (*UpdateGroupBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{14}
}

func (m *UpdateGroupBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupBuyProductRequest.Unmarshal(m, b)
}
func (m *UpdateGroupBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *UpdateGroupBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupBuyProductRequest.Merge(m, src)
}
func (m *UpdateGroupBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupBuyProductRequest.Size(m)
}
func (m *UpdateGroupBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupBuyProductRequest proto.InternalMessageInfo

func (m *UpdateGroupBuyProductRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateGroupBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *UpdateGroupBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateGroupBuyProductRequest) GetSaveData() *SaveGroupBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *UpdateGroupBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//拼团商品只需要id的请求
type GroupBuyProductIdRequest struct {
	// 活动商品信息记录id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductIdRequest) Reset()         { *m = GroupBuyProductIdRequest{} }
func (m *GroupBuyProductIdRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductIdRequest) ProtoMessage()    {}
func (*GroupBuyProductIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{15}
}

func (m *GroupBuyProductIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductIdRequest.Unmarshal(m, b)
}
func (m *GroupBuyProductIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductIdRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductIdRequest.Merge(m, src)
}
func (m *GroupBuyProductIdRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductIdRequest.Size(m)
}
func (m *GroupBuyProductIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductIdRequest proto.InternalMessageInfo

func (m *GroupBuyProductIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//获取拼团商品信息
type GetGroupBuyProductDetailResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//拼团商品信息
	Data                 *GroupBuyProductDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetGroupBuyProductDetailResponse) Reset()         { *m = GetGroupBuyProductDetailResponse{} }
func (m *GetGroupBuyProductDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyProductDetailResponse) ProtoMessage()    {}
func (*GetGroupBuyProductDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{16}
}

func (m *GetGroupBuyProductDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyProductDetailResponse.Unmarshal(m, b)
}
func (m *GetGroupBuyProductDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyProductDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyProductDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyProductDetailResponse.Merge(m, src)
}
func (m *GetGroupBuyProductDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyProductDetailResponse.Size(m)
}
func (m *GetGroupBuyProductDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyProductDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyProductDetailResponse proto.InternalMessageInfo

func (m *GetGroupBuyProductDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupBuyProductDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupBuyProductDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupBuyProductDetailResponse) GetData() *GroupBuyProductDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

//添加/编辑拼团商品
type SaveGroupBuyProductData struct {
	//所属拼团id
	Gid int32 `protobuf:"varint,1,opt,name=gid,proto3" json:"gid"`
	// required 成团人数 开团的人也算在内 必须大于等于2
	SuccessNum int32 `protobuf:"varint,2,opt,name=successNum,proto3" json:"successNum"`
	// required 拼团价 单位分 售价需要在0.01元到商品价格之间
	Price int32 `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	// 排序 默认为1
	Sort int32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort"`
	// 限购类型 1:每人限购n件 2:每人限购<=n件 n使用buy_limit_num字段保存 默认为1
	BuyLimitType int32 `protobuf:"varint,5,opt,name=buyLimitType,proto3" json:"buyLimitType"`
	// required 限购的数量，与buy_limit_type字段配合使用
	BuyLimitNum int32 `protobuf:"varint,6,opt,name=buyLimitNum,proto3" json:"buyLimitNum"`
	// 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次，默认为0
	OpenNum int32 `protobuf:"varint,7,opt,name=openNum,proto3" json:"openNum"`
	// 每人参团次数 默认为0，0表示不限制，>=0表示每个用户最多可参团少次
	PartNum int32 `protobuf:"varint,8,opt,name=partNum,proto3" json:"partNum"`
	// 初始参团人数 虚拟数据 只用于显示
	InitPartNum int32 `protobuf:"varint,9,opt,name=initPartNum,proto3" json:"initPartNum"`
	// 拼团成功送的优惠券ID，多个用，隔开
	SuccessCoupon string `protobuf:"bytes,10,opt,name=successCoupon,proto3" json:"successCoupon"`
	// 拼团成功每人最多送多少个优惠券 传-1表示不限制 默认为1
	//注意：前端界面显示为0时表示不限制，此时传值传-1 因为这种情况下后端区分不了是前端传的0还是没有传而被go设置了默认值0
	SuccessCouponLimitNum int32 `protobuf:"varint,11,opt,name=successCouponLimitNum,proto3" json:"successCouponLimitNum"`
	// 拼团失败送的优惠券ID，多个用，隔开
	FailCoupon string `protobuf:"bytes,12,opt,name=failCoupon,proto3" json:"failCoupon"`
	// 拼团失败每人最多送多少个优惠券  传-1表示不限制 默认为1
	//注意：前端界面显示为0时表示不限制，此时传值传-1 因为这种情况下后端区分不了是前端传的0还是没有传而被go设置了默认值0
	FailCouponLimitNum int32 `protobuf:"varint,13,opt,name=failCouponLimitNum,proto3" json:"failCouponLimitNum"`
	// 分享卡片图片地址
	ShareImgUrl          string   `protobuf:"bytes,14,opt,name=shareImgUrl,proto3" json:"shareImgUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveGroupBuyProductData) Reset()         { *m = SaveGroupBuyProductData{} }
func (m *SaveGroupBuyProductData) String() string { return proto.CompactTextString(m) }
func (*SaveGroupBuyProductData) ProtoMessage()    {}
func (*SaveGroupBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{17}
}

func (m *SaveGroupBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveGroupBuyProductData.Unmarshal(m, b)
}
func (m *SaveGroupBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveGroupBuyProductData.Marshal(b, m, deterministic)
}
func (m *SaveGroupBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveGroupBuyProductData.Merge(m, src)
}
func (m *SaveGroupBuyProductData) XXX_Size() int {
	return xxx_messageInfo_SaveGroupBuyProductData.Size(m)
}
func (m *SaveGroupBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveGroupBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_SaveGroupBuyProductData proto.InternalMessageInfo

func (m *SaveGroupBuyProductData) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetSuccessNum() int32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetBuyLimitType() int32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetBuyLimitNum() int32 {
	if m != nil {
		return m.BuyLimitNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetOpenNum() int32 {
	if m != nil {
		return m.OpenNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetPartNum() int32 {
	if m != nil {
		return m.PartNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetInitPartNum() int32 {
	if m != nil {
		return m.InitPartNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetSuccessCoupon() string {
	if m != nil {
		return m.SuccessCoupon
	}
	return ""
}

func (m *SaveGroupBuyProductData) GetSuccessCouponLimitNum() int32 {
	if m != nil {
		return m.SuccessCouponLimitNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetFailCoupon() string {
	if m != nil {
		return m.FailCoupon
	}
	return ""
}

func (m *SaveGroupBuyProductData) GetFailCouponLimitNum() int32 {
	if m != nil {
		return m.FailCouponLimitNum
	}
	return 0
}

func (m *SaveGroupBuyProductData) GetShareImgUrl() string {
	if m != nil {
		return m.ShareImgUrl
	}
	return ""
}

//拼团商品只需要id的请求
type GroupBuyProductDetailRequest struct {
	// 活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Gid int32 `protobuf:"varint,2,opt,name=gid,proto3" json:"gid"`
	// 商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	// 渠道 1电商 5商城
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductDetailRequest) Reset()         { *m = GroupBuyProductDetailRequest{} }
func (m *GroupBuyProductDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductDetailRequest) ProtoMessage()    {}
func (*GroupBuyProductDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{18}
}

func (m *GroupBuyProductDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductDetailRequest.Unmarshal(m, b)
}
func (m *GroupBuyProductDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductDetailRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductDetailRequest.Merge(m, src)
}
func (m *GroupBuyProductDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductDetailRequest.Size(m)
}
func (m *GroupBuyProductDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductDetailRequest proto.InternalMessageInfo

func (m *GroupBuyProductDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupBuyProductDetailRequest) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyProductDetailRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuyProductDetailRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuyProductDetailRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyProductDetailRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//批量ids的请求
type GroupBuyProductIdsRequest struct {
	// 活动商品信息记录id
	Ids                  []int32  `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyProductIdsRequest) Reset()         { *m = GroupBuyProductIdsRequest{} }
func (m *GroupBuyProductIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyProductIdsRequest) ProtoMessage()    {}
func (*GroupBuyProductIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{19}
}

func (m *GroupBuyProductIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyProductIdsRequest.Unmarshal(m, b)
}
func (m *GroupBuyProductIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyProductIdsRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyProductIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyProductIdsRequest.Merge(m, src)
}
func (m *GroupBuyProductIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyProductIdsRequest.Size(m)
}
func (m *GroupBuyProductIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyProductIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyProductIdsRequest proto.InternalMessageInfo

func (m *GroupBuyProductIdsRequest) GetIds() []int32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

//拼团详情请求
type GroupBuyIdRequest struct {
	// 活动id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyIdRequest) Reset()         { *m = GroupBuyIdRequest{} }
func (m *GroupBuyIdRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyIdRequest) ProtoMessage()    {}
func (*GroupBuyIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{20}
}

func (m *GroupBuyIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyIdRequest.Unmarshal(m, b)
}
func (m *GroupBuyIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyIdRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyIdRequest.Merge(m, src)
}
func (m *GroupBuyIdRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyIdRequest.Size(m)
}
func (m *GroupBuyIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyIdRequest proto.InternalMessageInfo

func (m *GroupBuyIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//拼团详情响应
type GroupBuyDetailResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 拼团详情
	GroupBuyData         *GroupBuyData `protobuf:"bytes,4,opt,name=groupBuyData,proto3" json:"groupBuyData"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupBuyDetailResponse) Reset()         { *m = GroupBuyDetailResponse{} }
func (m *GroupBuyDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GroupBuyDetailResponse) ProtoMessage()    {}
func (*GroupBuyDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{21}
}

func (m *GroupBuyDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyDetailResponse.Unmarshal(m, b)
}
func (m *GroupBuyDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyDetailResponse.Marshal(b, m, deterministic)
}
func (m *GroupBuyDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyDetailResponse.Merge(m, src)
}
func (m *GroupBuyDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GroupBuyDetailResponse.Size(m)
}
func (m *GroupBuyDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyDetailResponse proto.InternalMessageInfo

func (m *GroupBuyDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupBuyDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupBuyDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GroupBuyDetailResponse) GetGroupBuyData() *GroupBuyData {
	if m != nil {
		return m.GroupBuyData
	}
	return nil
}

//阿闻电商参加拼团活动的商品
type GetGroupBuyUPetProductSelectListRequest struct {
	//活动id
	Gid int32 `protobuf:"varint,1,opt,name=gid,proto3" json:"gid"`
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	//分页参数
	Pagination *Pagination `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupBuyUPetProductSelectListRequest) Reset() {
	*m = GetGroupBuyUPetProductSelectListRequest{}
}
func (m *GetGroupBuyUPetProductSelectListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyUPetProductSelectListRequest) ProtoMessage()    {}
func (*GetGroupBuyUPetProductSelectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{22}
}

func (m *GetGroupBuyUPetProductSelectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest.Unmarshal(m, b)
}
func (m *GetGroupBuyUPetProductSelectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyUPetProductSelectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest.Merge(m, src)
}
func (m *GetGroupBuyUPetProductSelectListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest.Size(m)
}
func (m *GetGroupBuyUPetProductSelectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyUPetProductSelectListRequest proto.InternalMessageInfo

func (m *GetGroupBuyUPetProductSelectListRequest) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GetGroupBuyUPetProductSelectListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetGroupBuyUPetProductSelectListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetGroupBuyUPetProductSelectListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetGroupBuyUPetProductSelectListRequest) GetPagination() *Pagination {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetGroupBuyUPetProductSelectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//阿闻电商参加拼团活动的商品
type GetGroupBuyUPetProductSelectListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//拼团商品信息
	Data                 []*GroupBuySelectUPetProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetGroupBuyUPetProductSelectListResponse) Reset() {
	*m = GetGroupBuyUPetProductSelectListResponse{}
}
func (m *GetGroupBuyUPetProductSelectListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyUPetProductSelectListResponse) ProtoMessage()    {}
func (*GetGroupBuyUPetProductSelectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{23}
}

func (m *GetGroupBuyUPetProductSelectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse.Unmarshal(m, b)
}
func (m *GetGroupBuyUPetProductSelectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyUPetProductSelectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse.Merge(m, src)
}
func (m *GetGroupBuyUPetProductSelectListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse.Size(m)
}
func (m *GetGroupBuyUPetProductSelectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyUPetProductSelectListResponse proto.InternalMessageInfo

func (m *GetGroupBuyUPetProductSelectListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupBuyUPetProductSelectListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupBuyUPetProductSelectListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupBuyUPetProductSelectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGroupBuyUPetProductSelectListResponse) GetData() []*GroupBuySelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupBuySelectUPetProductData struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	// 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
	TimeConflict int32 `protobuf:"varint,4,opt,name=timeConflict,proto3" json:"timeConflict"`
	//商品图片
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	//库存
	Stock int32 `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	//价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	//是否时虚拟产品 1是 0 否
	IsVirtual int32 `protobuf:"varint,8,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,9,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品信息
	ChildSkuIds          []*ChildRen `protobuf:"bytes,10,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GroupBuySelectUPetProductData) Reset()         { *m = GroupBuySelectUPetProductData{} }
func (m *GroupBuySelectUPetProductData) String() string { return proto.CompactTextString(m) }
func (*GroupBuySelectUPetProductData) ProtoMessage()    {}
func (*GroupBuySelectUPetProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{24}
}

func (m *GroupBuySelectUPetProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuySelectUPetProductData.Unmarshal(m, b)
}
func (m *GroupBuySelectUPetProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuySelectUPetProductData.Marshal(b, m, deterministic)
}
func (m *GroupBuySelectUPetProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuySelectUPetProductData.Merge(m, src)
}
func (m *GroupBuySelectUPetProductData) XXX_Size() int {
	return xxx_messageInfo_GroupBuySelectUPetProductData.Size(m)
}
func (m *GroupBuySelectUPetProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuySelectUPetProductData.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuySelectUPetProductData proto.InternalMessageInfo

func (m *GroupBuySelectUPetProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupBuySelectUPetProductData) GetTimeConflict() int32 {
	if m != nil {
		return m.TimeConflict
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *GroupBuySelectUPetProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *GroupBuySelectUPetProductData) GetChildSkuIds() []*ChildRen {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

//拼团订单回调
type GroupBuyOrderStaticRequest struct {
	// required 回调场景 1 成团 2：下单支付成功 3：创建订单
	SyncType int32 `protobuf:"varint,1,opt,name=syncType,proto3" json:"syncType"`
	//拼团活动的产品记录id 关联dc.activity中的group_buy_product表
	ProductRecordId int32 `protobuf:"varint,2,opt,name=productRecordId,proto3" json:"productRecordId"`
	// required 拼团id
	Gid int32 `protobuf:"varint,3,opt,name=gid,proto3" json:"gid"`
	// required skuId
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//  商品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
	ChannelId int32 `protobuf:"varint,6,opt,name=channelId,proto3" json:"channelId"`
	//成团后的真人订单数 type为1时 该参数为必填
	SuccessRealOrder int32 `protobuf:"varint,7,opt,name=successRealOrder,proto3" json:"successRealOrder"`
	//成团后的订单人数（去重） type为1时 该参数为必填
	SuccessDistinctUserCount int32    `protobuf:"varint,8,opt,name=successDistinctUserCount,proto3" json:"successDistinctUserCount"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *GroupBuyOrderStaticRequest) Reset()         { *m = GroupBuyOrderStaticRequest{} }
func (m *GroupBuyOrderStaticRequest) String() string { return proto.CompactTextString(m) }
func (*GroupBuyOrderStaticRequest) ProtoMessage()    {}
func (*GroupBuyOrderStaticRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{25}
}

func (m *GroupBuyOrderStaticRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyOrderStaticRequest.Unmarshal(m, b)
}
func (m *GroupBuyOrderStaticRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyOrderStaticRequest.Marshal(b, m, deterministic)
}
func (m *GroupBuyOrderStaticRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyOrderStaticRequest.Merge(m, src)
}
func (m *GroupBuyOrderStaticRequest) XXX_Size() int {
	return xxx_messageInfo_GroupBuyOrderStaticRequest.Size(m)
}
func (m *GroupBuyOrderStaticRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyOrderStaticRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyOrderStaticRequest proto.InternalMessageInfo

func (m *GroupBuyOrderStaticRequest) GetSyncType() int32 {
	if m != nil {
		return m.SyncType
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetProductRecordId() int32 {
	if m != nil {
		return m.ProductRecordId
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetSuccessRealOrder() int32 {
	if m != nil {
		return m.SuccessRealOrder
	}
	return 0
}

func (m *GroupBuyOrderStaticRequest) GetSuccessDistinctUserCount() int32 {
	if m != nil {
		return m.SuccessDistinctUserCount
	}
	return 0
}

//组合商品子商品讯息
type ChildRen struct {
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//规则
	RuleNum int32 `protobuf:"varint,2,opt,name=ruleNum,proto3" json:"ruleNum"`
	//是否为虚拟 0:不是 1：是虚拟
	IsVirtual int32 `protobuf:"varint,3,opt,name=isVirtual,proto3" json:"isVirtual"`
	//0不是药品仓 1药品仓
	StockWarehouse       int32    `protobuf:"varint,7,opt,name=stockWarehouse,proto3" json:"stockWarehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChildRen) Reset()         { *m = ChildRen{} }
func (m *ChildRen) String() string { return proto.CompactTextString(m) }
func (*ChildRen) ProtoMessage()    {}
func (*ChildRen) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{26}
}

func (m *ChildRen) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChildRen.Unmarshal(m, b)
}
func (m *ChildRen) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChildRen.Marshal(b, m, deterministic)
}
func (m *ChildRen) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChildRen.Merge(m, src)
}
func (m *ChildRen) XXX_Size() int {
	return xxx_messageInfo_ChildRen.Size(m)
}
func (m *ChildRen) XXX_DiscardUnknown() {
	xxx_messageInfo_ChildRen.DiscardUnknown(m)
}

var xxx_messageInfo_ChildRen proto.InternalMessageInfo

func (m *ChildRen) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ChildRen) GetRuleNum() int32 {
	if m != nil {
		return m.RuleNum
	}
	return 0
}

func (m *ChildRen) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *ChildRen) GetStockWarehouse() int32 {
	if m != nil {
		return m.StockWarehouse
	}
	return 0
}

type GetProductDetailForCustomResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//拼团商品信息
	Data                 *ProductDetailForCustom `protobuf:"bytes,5,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetProductDetailForCustomResponse) Reset()         { *m = GetProductDetailForCustomResponse{} }
func (m *GetProductDetailForCustomResponse) String() string { return proto.CompactTextString(m) }
func (*GetProductDetailForCustomResponse) ProtoMessage()    {}
func (*GetProductDetailForCustomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{27}
}

func (m *GetProductDetailForCustomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductDetailForCustomResponse.Unmarshal(m, b)
}
func (m *GetProductDetailForCustomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductDetailForCustomResponse.Marshal(b, m, deterministic)
}
func (m *GetProductDetailForCustomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductDetailForCustomResponse.Merge(m, src)
}
func (m *GetProductDetailForCustomResponse) XXX_Size() int {
	return xxx_messageInfo_GetProductDetailForCustomResponse.Size(m)
}
func (m *GetProductDetailForCustomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductDetailForCustomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductDetailForCustomResponse proto.InternalMessageInfo

func (m *GetProductDetailForCustomResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetProductDetailForCustomResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetProductDetailForCustomResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetProductDetailForCustomResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetProductDetailForCustomResponse) GetData() *ProductDetailForCustom {
	if m != nil {
		return m.Data
	}
	return nil
}

type ProductDetailForCustom struct {
	//商品参加的拼团活动ID
	Gid int32 `protobuf:"varint,1,opt,name=Gid,proto3" json:"Gid"`
	// required skuId
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//  商品id
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	//required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
	ChannelId int32 `protobuf:"varint,4,opt,name=channelId,proto3" json:"channelId"`
	//拼团价
	Price int32 `protobuf:"varint,5,opt,name=Price,proto3" json:"Price"`
	//原市场价
	MarketPrice int32 `protobuf:"varint,6,opt,name=MarketPrice,proto3" json:"MarketPrice"`
	// required 成团人数 开团的人也算在内 必须大于等于2
	SuccessNum int32 `protobuf:"varint,7,opt,name=SuccessNum,proto3" json:"SuccessNum"`
	//是否开启预告 0不开启 1开启
	PreviewSwitch int32 `protobuf:"varint,8,opt,name=PreviewSwitch,proto3" json:"PreviewSwitch"`
	// 拼团活动信息 预告开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	PreviewBeginTime string `protobuf:"bytes,9,opt,name=PreviewBeginTime,proto3" json:"PreviewBeginTime"`
	//拼团活动信息 活动状态  1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
	Status int32 `protobuf:"varint,10,opt,name=Status,proto3" json:"Status"`
	// 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	BeginDate string `protobuf:"bytes,11,opt,name=BeginDate,proto3" json:"BeginDate"`
	// 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
	EndDate string `protobuf:"bytes,12,opt,name=EndDate,proto3" json:"EndDate"`
	//终止状态 0未终止 1已终止
	Stop int32 `protobuf:"varint,13,opt,name=Stop,proto3" json:"Stop"`
	//拼团类型 1普通拼团，目前仅有普通拼团
	Type int32 `protobuf:"varint,14,opt,name=Type,proto3" json:"Type"`
	//是否可叠加其他活动， 0不可叠加 1可叠加
	AdditionPromotionAble int32 `protobuf:"varint,15,opt,name=AdditionPromotionAble,proto3" json:"AdditionPromotionAble"`
	//叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费
	AdditionPromotionType string `protobuf:"bytes,16,opt,name=AdditionPromotionType,proto3" json:"AdditionPromotionType"`
	//限购类型 1每人限购n件 2每人限购<=n件 n使用buy_limit_num字段保存
	BuyLimitType int32 `protobuf:"varint,17,opt,name=BuyLimitType,proto3" json:"BuyLimitType"`
	//限购的数量，与buy_limit_type字段配合使用
	BuyLimitNum int32 `protobuf:"varint,18,opt,name=BuyLimitNum,proto3" json:"BuyLimitNum"`
	//分享卡片图片地址
	ShareImgUrl string `protobuf:"bytes,19,opt,name=ShareImgUrl,proto3" json:"ShareImgUrl"`
	//参团人数 包括拼主与参团人数
	PartUserCount int32 `protobuf:"varint,20,opt,name=PartUserCount,proto3" json:"PartUserCount"`
	//活动商品绑定记录id
	GroupBuyProductId    int32    `protobuf:"varint,21,opt,name=GroupBuyProductId,proto3" json:"GroupBuyProductId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductDetailForCustom) Reset()         { *m = ProductDetailForCustom{} }
func (m *ProductDetailForCustom) String() string { return proto.CompactTextString(m) }
func (*ProductDetailForCustom) ProtoMessage()    {}
func (*ProductDetailForCustom) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{28}
}

func (m *ProductDetailForCustom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductDetailForCustom.Unmarshal(m, b)
}
func (m *ProductDetailForCustom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductDetailForCustom.Marshal(b, m, deterministic)
}
func (m *ProductDetailForCustom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductDetailForCustom.Merge(m, src)
}
func (m *ProductDetailForCustom) XXX_Size() int {
	return xxx_messageInfo_ProductDetailForCustom.Size(m)
}
func (m *ProductDetailForCustom) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductDetailForCustom.DiscardUnknown(m)
}

var xxx_messageInfo_ProductDetailForCustom proto.InternalMessageInfo

func (m *ProductDetailForCustom) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *ProductDetailForCustom) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductDetailForCustom) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductDetailForCustom) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ProductDetailForCustom) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ProductDetailForCustom) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *ProductDetailForCustom) GetSuccessNum() int32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

func (m *ProductDetailForCustom) GetPreviewSwitch() int32 {
	if m != nil {
		return m.PreviewSwitch
	}
	return 0
}

func (m *ProductDetailForCustom) GetPreviewBeginTime() string {
	if m != nil {
		return m.PreviewBeginTime
	}
	return ""
}

func (m *ProductDetailForCustom) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProductDetailForCustom) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *ProductDetailForCustom) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *ProductDetailForCustom) GetStop() int32 {
	if m != nil {
		return m.Stop
	}
	return 0
}

func (m *ProductDetailForCustom) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProductDetailForCustom) GetAdditionPromotionAble() int32 {
	if m != nil {
		return m.AdditionPromotionAble
	}
	return 0
}

func (m *ProductDetailForCustom) GetAdditionPromotionType() string {
	if m != nil {
		return m.AdditionPromotionType
	}
	return ""
}

func (m *ProductDetailForCustom) GetBuyLimitType() int32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *ProductDetailForCustom) GetBuyLimitNum() int32 {
	if m != nil {
		return m.BuyLimitNum
	}
	return 0
}

func (m *ProductDetailForCustom) GetShareImgUrl() string {
	if m != nil {
		return m.ShareImgUrl
	}
	return ""
}

func (m *ProductDetailForCustom) GetPartUserCount() int32 {
	if m != nil {
		return m.PartUserCount
	}
	return 0
}

func (m *ProductDetailForCustom) GetGroupBuyProductId() int32 {
	if m != nil {
		return m.GroupBuyProductId
	}
	return 0
}

//通过spuId获取拼团sku规格信息请求参数
type GetGroupBuySkusListBySkuIdRequest struct {
	//产品sku id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//业务渠道 1阿文到家 5电商
	ChannelId int32 `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId"`
	//团号：拼团主的订单号
	PinOrderSn string `protobuf:"bytes,3,opt,name=pinOrderSn,proto3" json:"pinOrderSn"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupBuySkusListBySkuIdRequest) Reset()         { *m = GetGroupBuySkusListBySkuIdRequest{} }
func (m *GetGroupBuySkusListBySkuIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuySkusListBySkuIdRequest) ProtoMessage()    {}
func (*GetGroupBuySkusListBySkuIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{29}
}

func (m *GetGroupBuySkusListBySkuIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest.Unmarshal(m, b)
}
func (m *GetGroupBuySkusListBySkuIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupBuySkusListBySkuIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest.Merge(m, src)
}
func (m *GetGroupBuySkusListBySkuIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest.Size(m)
}
func (m *GetGroupBuySkusListBySkuIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuySkusListBySkuIdRequest proto.InternalMessageInfo

func (m *GetGroupBuySkusListBySkuIdRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetGroupBuySkusListBySkuIdRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetGroupBuySkusListBySkuIdRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *GetGroupBuySkusListBySkuIdRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//通过spuId获取拼团sku规格信息返回参数
type GetGroupBuySkusListBySkuIdResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//拼团商品信息
	Data                 []*GroupBuySkusList `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroupBuySkusListBySkuIdResponse) Reset()         { *m = GetGroupBuySkusListBySkuIdResponse{} }
func (m *GetGroupBuySkusListBySkuIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuySkusListBySkuIdResponse) ProtoMessage()    {}
func (*GetGroupBuySkusListBySkuIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{30}
}

func (m *GetGroupBuySkusListBySkuIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse.Unmarshal(m, b)
}
func (m *GetGroupBuySkusListBySkuIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupBuySkusListBySkuIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse.Merge(m, src)
}
func (m *GetGroupBuySkusListBySkuIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse.Size(m)
}
func (m *GetGroupBuySkusListBySkuIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuySkusListBySkuIdResponse proto.InternalMessageInfo

func (m *GetGroupBuySkusListBySkuIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupBuySkusListBySkuIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupBuySkusListBySkuIdResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupBuySkusListBySkuIdResponse) GetData() []*GroupBuySkusList {
	if m != nil {
		return m.Data
	}
	return nil
}

//拼团sku规格信息
type GroupBuySkusList struct {
	// 拼团活动信息 所属拼团id
	Gid int32 `protobuf:"varint,1,opt,name=gid,proto3" json:"gid"`
	// 拼团价 单位分
	Price int32 `protobuf:"varint,2,opt,name=price,proto3" json:"price"`
	//商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	// 市场价格 单位分
	MarketPrice int32 `protobuf:"varint,5,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 商品图片
	Pic string `protobuf:"bytes,6,opt,name=pic,proto3" json:"pic"`
	//商品名称
	ProductName string `protobuf:"bytes,7,opt,name=productName,proto3" json:"productName"`
	// 规格名称
	SpecName string `protobuf:"bytes,8,opt,name=specName,proto3" json:"specName"`
	// 规格值
	SpecValue string `protobuf:"bytes,9,opt,name=specValue,proto3" json:"specValue"`
	//限购类型 1每人限购n件 2每人限购<=n件 n使用buy_limit_num字段保存
	BuyLimitType int32 `protobuf:"varint,10,opt,name=BuyLimitType,proto3" json:"BuyLimitType"`
	//限购的数量，与buy_limit_type字段配合使用
	BuyLimitNum int32 `protobuf:"varint,11,opt,name=BuyLimitNum,proto3" json:"BuyLimitNum"`
	//渠道
	ChannelId            int32    `protobuf:"varint,12,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuySkusList) Reset()         { *m = GroupBuySkusList{} }
func (m *GroupBuySkusList) String() string { return proto.CompactTextString(m) }
func (*GroupBuySkusList) ProtoMessage()    {}
func (*GroupBuySkusList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{31}
}

func (m *GroupBuySkusList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuySkusList.Unmarshal(m, b)
}
func (m *GroupBuySkusList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuySkusList.Marshal(b, m, deterministic)
}
func (m *GroupBuySkusList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuySkusList.Merge(m, src)
}
func (m *GroupBuySkusList) XXX_Size() int {
	return xxx_messageInfo_GroupBuySkusList.Size(m)
}
func (m *GroupBuySkusList) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuySkusList.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuySkusList proto.InternalMessageInfo

func (m *GroupBuySkusList) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuySkusList) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GroupBuySkusList) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GroupBuySkusList) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GroupBuySkusList) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *GroupBuySkusList) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *GroupBuySkusList) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupBuySkusList) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *GroupBuySkusList) GetSpecValue() string {
	if m != nil {
		return m.SpecValue
	}
	return ""
}

func (m *GroupBuySkusList) GetBuyLimitType() int32 {
	if m != nil {
		return m.BuyLimitType
	}
	return 0
}

func (m *GroupBuySkusList) GetBuyLimitNum() int32 {
	if m != nil {
		return m.BuyLimitNum
	}
	return 0
}

func (m *GroupBuySkusList) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

//根据拼团活动id获取活动的状态
type GetGroupBuyStatusRequest struct {
	// 拼团id
	Gids                 []int32  `protobuf:"varint,1,rep,packed,name=gids,proto3" json:"gids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupBuyStatusRequest) Reset()         { *m = GetGroupBuyStatusRequest{} }
func (m *GetGroupBuyStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyStatusRequest) ProtoMessage()    {}
func (*GetGroupBuyStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{32}
}

func (m *GetGroupBuyStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyStatusRequest.Unmarshal(m, b)
}
func (m *GetGroupBuyStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyStatusRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyStatusRequest.Merge(m, src)
}
func (m *GetGroupBuyStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyStatusRequest.Size(m)
}
func (m *GetGroupBuyStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyStatusRequest proto.InternalMessageInfo

func (m *GetGroupBuyStatusRequest) GetGids() []int32 {
	if m != nil {
		return m.Gids
	}
	return nil
}

//通过spuId获取拼团sku规格信息返回参数
type GetGroupBuyStatusResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//拼团商品信息
	Data                 []*GroupBuyStatus `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGroupBuyStatusResponse) Reset()         { *m = GetGroupBuyStatusResponse{} }
func (m *GetGroupBuyStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupBuyStatusResponse) ProtoMessage()    {}
func (*GetGroupBuyStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{33}
}

func (m *GetGroupBuyStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupBuyStatusResponse.Unmarshal(m, b)
}
func (m *GetGroupBuyStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupBuyStatusResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupBuyStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupBuyStatusResponse.Merge(m, src)
}
func (m *GetGroupBuyStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupBuyStatusResponse.Size(m)
}
func (m *GetGroupBuyStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupBuyStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupBuyStatusResponse proto.InternalMessageInfo

func (m *GetGroupBuyStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupBuyStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupBuyStatusResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupBuyStatusResponse) GetData() []*GroupBuyStatus {
	if m != nil {
		return m.Data
	}
	return nil
}

//拼团sku规格信息
type GroupBuyStatus struct {
	// 拼团活动信息 所属拼团id
	Gid int32 `protobuf:"varint,1,opt,name=gid,proto3" json:"gid"`
	// 活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	//是否终止 0未终止 1已终止
	Stop int32 `protobuf:"varint,3,opt,name=stop,proto3" json:"stop"`
	//开始时间
	BeginDate string `protobuf:"bytes,4,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	//结束时间
	EndDate              string   `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupBuyStatus) Reset()         { *m = GroupBuyStatus{} }
func (m *GroupBuyStatus) String() string { return proto.CompactTextString(m) }
func (*GroupBuyStatus) ProtoMessage()    {}
func (*GroupBuyStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_6c0f67580a1e27a3, []int{34}
}

func (m *GroupBuyStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBuyStatus.Unmarshal(m, b)
}
func (m *GroupBuyStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBuyStatus.Marshal(b, m, deterministic)
}
func (m *GroupBuyStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBuyStatus.Merge(m, src)
}
func (m *GroupBuyStatus) XXX_Size() int {
	return xxx_messageInfo_GroupBuyStatus.Size(m)
}
func (m *GroupBuyStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBuyStatus.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBuyStatus proto.InternalMessageInfo

func (m *GroupBuyStatus) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GroupBuyStatus) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupBuyStatus) GetStop() int32 {
	if m != nil {
		return m.Stop
	}
	return 0
}

func (m *GroupBuyStatus) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *GroupBuyStatus) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func init() {
	proto.RegisterType((*GroupBuyListRequest)(nil), "ac.GroupBuyListRequest")
	proto.RegisterType((*DuringTime)(nil), "ac.DuringTime")
	proto.RegisterType((*Pagination)(nil), "ac.Pagination")
	proto.RegisterType((*GroupBuyListResponse)(nil), "ac.GroupBuyListResponse")
	proto.RegisterType((*GroupBuyData)(nil), "ac.GroupBuyData")
	proto.RegisterType((*GroupBuyCreateRequest)(nil), "ac.GroupBuyCreateRequest")
	proto.RegisterType((*GroupBuyUpdateRequest)(nil), "ac.GroupBuyUpdateRequest")
	proto.RegisterType((*GetGroupBuyProductListRequest)(nil), "ac.GetGroupBuyProductListRequest")
	proto.RegisterType((*GroupBuyProductData)(nil), "ac.GroupBuyProductData")
	proto.RegisterType((*GroupBuyProductCustomListResponse)(nil), "ac.GroupBuyProductCustomListResponse")
	proto.RegisterType((*GroupBuyProductCustomList)(nil), "ac.GroupBuyProductCustomList")
	proto.RegisterType((*GroupBuyProductDetailData)(nil), "ac.GroupBuyProductDetailData")
	proto.RegisterType((*GetGroupBuyProductListResponse)(nil), "ac.GetGroupBuyProductListResponse")
	proto.RegisterType((*CreateGroupBuyProductRequest)(nil), "ac.CreateGroupBuyProductRequest")
	proto.RegisterType((*UpdateGroupBuyProductRequest)(nil), "ac.UpdateGroupBuyProductRequest")
	proto.RegisterType((*GroupBuyProductIdRequest)(nil), "ac.GroupBuyProductIdRequest")
	proto.RegisterType((*GetGroupBuyProductDetailResponse)(nil), "ac.GetGroupBuyProductDetailResponse")
	proto.RegisterType((*SaveGroupBuyProductData)(nil), "ac.SaveGroupBuyProductData")
	proto.RegisterType((*GroupBuyProductDetailRequest)(nil), "ac.GroupBuyProductDetailRequest")
	proto.RegisterType((*GroupBuyProductIdsRequest)(nil), "ac.GroupBuyProductIdsRequest")
	proto.RegisterType((*GroupBuyIdRequest)(nil), "ac.GroupBuyIdRequest")
	proto.RegisterType((*GroupBuyDetailResponse)(nil), "ac.GroupBuyDetailResponse")
	proto.RegisterType((*GetGroupBuyUPetProductSelectListRequest)(nil), "ac.GetGroupBuyUPetProductSelectListRequest")
	proto.RegisterType((*GetGroupBuyUPetProductSelectListResponse)(nil), "ac.GetGroupBuyUPetProductSelectListResponse")
	proto.RegisterType((*GroupBuySelectUPetProductData)(nil), "ac.GroupBuySelectUPetProductData")
	proto.RegisterType((*GroupBuyOrderStaticRequest)(nil), "ac.GroupBuyOrderStaticRequest")
	proto.RegisterType((*ChildRen)(nil), "ac.ChildRen")
	proto.RegisterType((*GetProductDetailForCustomResponse)(nil), "ac.GetProductDetailForCustomResponse")
	proto.RegisterType((*ProductDetailForCustom)(nil), "ac.ProductDetailForCustom")
	proto.RegisterType((*GetGroupBuySkusListBySkuIdRequest)(nil), "ac.GetGroupBuySkusListBySkuIdRequest")
	proto.RegisterType((*GetGroupBuySkusListBySkuIdResponse)(nil), "ac.GetGroupBuySkusListBySkuIdResponse")
	proto.RegisterType((*GroupBuySkusList)(nil), "ac.GroupBuySkusList")
	proto.RegisterType((*GetGroupBuyStatusRequest)(nil), "ac.GetGroupBuyStatusRequest")
	proto.RegisterType((*GetGroupBuyStatusResponse)(nil), "ac.GetGroupBuyStatusResponse")
	proto.RegisterType((*GroupBuyStatus)(nil), "ac.GroupBuyStatus")
}

func init() { proto.RegisterFile("ac/group_buy_service.proto", fileDescriptor_6c0f67580a1e27a3) }

var fileDescriptor_6c0f67580a1e27a3 = []byte{
	// 2929 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5b, 0xbd, 0x6f, 0xe4, 0xc6,
	0x15, 0xc7, 0x7e, 0x6a, 0xf7, 0xed, 0xea, 0x8b, 0xa7, 0x0f, 0x6a, 0x4f, 0x92, 0x57, 0xf4, 0x9d,
	0x2c, 0x9c, 0x1d, 0x19, 0xbe, 0x38, 0x70, 0x12, 0x20, 0x85, 0x25, 0xf9, 0x14, 0xc1, 0xb9, 0xb3,
	0xb0, 0xba, 0x73, 0x4a, 0x81, 0x22, 0xe7, 0x56, 0x84, 0x76, 0xc9, 0x0d, 0x3f, 0xe4, 0x53, 0x80,
	0x00, 0xe9, 0x92, 0x2a, 0x85, 0x81, 0x04, 0x48, 0x9b, 0x54, 0x29, 0x12, 0x04, 0x6e, 0x53, 0x06,
	0x48, 0x91, 0x26, 0x6d, 0x3a, 0x03, 0xe9, 0x5c, 0xe5, 0x4f, 0x48, 0xf0, 0x66, 0x86, 0xe4, 0x0c,
	0x39, 0xe4, 0xae, 0x84, 0xb3, 0x2b, 0x57, 0xe2, 0xbc, 0x19, 0xce, 0xc7, 0x9b, 0xdf, 0x7b, 0xef,
	0xf7, 0x1e, 0x57, 0xd0, 0x33, 0xad, 0x77, 0x87, 0xbe, 0x17, 0x4d, 0xce, 0x2f, 0xa2, 0x9b, 0xf3,
	0x80, 0xf8, 0xd7, 0x8e, 0x45, 0xf6, 0x27, 0xbe, 0x17, 0x7a, 0x5a, 0xd5, 0xb4, 0x7a, 0xeb, 0xa6,
	0xf5, 0xae, 0x69, 0x85, 0xce, 0xb5, 0x13, 0xde, 0x9c, 0x8f, 0x3d, 0x9b, 0x8c, 0x58, 0xa7, 0xf1,
	0xdb, 0x2a, 0xdc, 0x3b, 0xc6, 0x17, 0x0f, 0xa2, 0x9b, 0x9f, 0x38, 0x41, 0x38, 0x20, 0x3f, 0x8b,
	0x48, 0x10, 0x6a, 0x2b, 0xd0, 0x08, 0x9d, 0x70, 0x44, 0xf4, 0x4a, 0xbf, 0xb2, 0xd7, 0x1e, 0xb0,
	0x86, 0xb6, 0x06, 0xcd, 0x20, 0x34, 0xc3, 0x28, 0xd0, 0xab, 0xfd, 0xca, 0x5e, 0x63, 0xc0, 0x5b,
	0xda, 0x26, 0xb4, 0xad, 0x4b, 0xd3, 0x75, 0xc9, 0xe8, 0xc4, 0xd6, 0x6b, 0xb4, 0x2b, 0x15, 0x68,
	0x1a, 0xd4, 0xc3, 0x9b, 0x09, 0xd1, 0xeb, 0xb4, 0x83, 0x3e, 0x6b, 0xdf, 0x87, 0xc5, 0x0b, 0x32,
	0x74, 0xdc, 0xe7, 0xce, 0x98, 0x1c, 0x45, 0xbe, 0xe3, 0x0e, 0xf5, 0x46, 0xbf, 0xb2, 0xd7, 0x79,
	0xbc, 0xb0, 0x6f, 0x5a, 0xfb, 0x4c, 0x82, 0x7d, 0x83, 0xec, 0x30, 0xed, 0x7d, 0x98, 0x27, 0xae,
	0x2d, 0xbc, 0xd7, 0x54, 0xbe, 0x27, 0x0f, 0xd2, 0xf6, 0x01, 0x26, 0xe6, 0xd0, 0x71, 0xcd, 0xd0,
	0xf1, 0x5c, 0x7d, 0x2e, 0x7d, 0xe5, 0x34, 0x91, 0x0e, 0x84, 0x11, 0xc6, 0x11, 0x40, 0x3a, 0x19,
	0x9e, 0x2f, 0xd9, 0x06, 0xd7, 0x48, 0x2a, 0xd0, 0x74, 0x98, 0xe3, 0x8b, 0x51, 0xb5, 0xb4, 0x07,
	0x71, 0xd3, 0x78, 0x02, 0x90, 0xce, 0x8f, 0xb3, 0x4c, 0xcc, 0x21, 0x39, 0x71, 0x6d, 0xf2, 0x8a,
	0xce, 0xd2, 0x18, 0xa4, 0x02, 0xad, 0x07, 0x2d, 0x6c, 0x9c, 0x39, 0x3f, 0x27, 0x5c, 0xbb, 0x49,
	0xdb, 0xf8, 0x5d, 0x05, 0x56, 0xe4, 0x5b, 0x0a, 0x26, 0x9e, 0x1b, 0x10, 0x54, 0xad, 0xe5, 0xd9,
	0x84, 0xcf, 0x46, 0x9f, 0x71, 0x3b, 0x63, 0x12, 0x04, 0xe6, 0x30, 0xd9, 0x0e, 0x6f, 0xe2, 0xa5,
	0x12, 0xdf, 0xf7, 0x7c, 0x7a, 0x45, 0xed, 0x01, 0x6b, 0xd0, 0xab, 0xf6, 0x42, 0x73, 0xc4, 0xef,
	0x87, 0x35, 0xb4, 0x07, 0x50, 0xb7, 0xcd, 0xd0, 0xd4, 0x1b, 0xfd, 0xda, 0x5e, 0xe7, 0xf1, 0x12,
	0xaa, 0x2a, 0xde, 0xc1, 0x91, 0x19, 0x9a, 0x03, 0xda, 0x6b, 0x7c, 0xd1, 0x82, 0xae, 0x28, 0xd6,
	0x16, 0xa0, 0xea, 0xd8, 0x7c, 0x3b, 0x55, 0xc7, 0x4e, 0x71, 0x54, 0x15, 0x71, 0x14, 0xeb, 0xf3,
	0xc8, 0x0c, 0x09, 0xdf, 0x4c, 0x2a, 0xe0, 0xfa, 0xa4, 0x7d, 0xf5, 0x44, 0x9f, 0xb4, 0x47, 0xc2,
	0x59, 0xa3, 0x08, 0x67, 0x4d, 0x01, 0x67, 0x3d, 0x68, 0xe1, 0xdf, 0x23, 0x12, 0x58, 0xf4, 0xd6,
	0xdb, 0x83, 0xa4, 0x8d, 0xe3, 0x83, 0xd0, 0x9b, 0xe8, 0x2d, 0x36, 0x1e, 0x9f, 0x05, 0x84, 0xb7,
	0x25, 0x84, 0x6f, 0x03, 0xb0, 0x27, 0x3a, 0x13, 0xd0, 0x99, 0x04, 0x89, 0xf6, 0x00, 0xe6, 0xc9,
	0xab, 0x89, 0xe3, 0xd3, 0x9b, 0x3e, 0x32, 0x6f, 0xf4, 0x0e, 0x7d, 0x5d, 0x16, 0x6a, 0xbb, 0xb0,
	0x90, 0x0a, 0x7e, 0xec, 0x45, 0xbe, 0xde, 0xa5, 0xc3, 0x32, 0x52, 0xed, 0x11, 0x2c, 0xa5, 0x92,
	0xa7, 0x8e, 0x1b, 0x85, 0x44, 0x9f, 0xa7, 0x23, 0x73, 0x72, 0xed, 0x7d, 0x58, 0x35, 0x6d, 0xdb,
	0x41, 0xc9, 0xa9, 0xef, 0x8d, 0x3d, 0x7c, 0xf8, 0xf0, 0x62, 0x44, 0xf4, 0x05, 0xfa, 0x82, 0xba,
	0x53, 0xf9, 0xd6, 0x73, 0x54, 0xde, 0x22, 0x3d, 0x9a, 0xba, 0x13, 0x4f, 0x39, 0xf1, 0xc9, 0xb5,
	0x43, 0x3e, 0x3b, 0xfb, 0xcc, 0x09, 0xad, 0x4b, 0x7d, 0x89, 0x9d, 0x52, 0x12, 0x6a, 0x7d, 0xe8,
	0x70, 0x01, 0x3d, 0xe2, 0x32, 0x1d, 0x23, 0x8a, 0xf0, 0x7c, 0xbc, 0x79, 0x90, 0x98, 0x95, 0x46,
	0x17, 0xce, 0xc9, 0x71, 0xb6, 0xb1, 0x67, 0x5d, 0x9d, 0x45, 0x96, 0x45, 0x82, 0x40, 0xbf, 0xc7,
	0x66, 0x13, 0x44, 0xda, 0x3b, 0xb0, 0x2c, 0x34, 0x9f, 0x9b, 0xfe, 0x90, 0x84, 0xfa, 0x0a, 0x1d,
	0x97, 0xef, 0xc8, 0x8c, 0x7e, 0x4a, 0xc6, 0x17, 0xc4, 0xd7, 0x57, 0x73, 0xa3, 0x59, 0x87, 0x66,
	0x40, 0xd7, 0x26, 0x23, 0xe7, 0x9a, 0xf8, 0x37, 0x4f, 0x7c, 0x42, 0xf4, 0x35, 0x3a, 0x50, 0x92,
	0xe1, 0x0e, 0x5f, 0x3a, 0xae, 0xe9, 0x5a, 0xe4, 0x10, 0x6d, 0x71, 0x9d, 0x1e, 0x44, 0x14, 0x21,
	0x7a, 0x2c, 0x9f, 0x98, 0x21, 0xa1, 0x27, 0xd5, 0x19, 0x7a, 0x52, 0x09, 0xf6, 0x47, 0x13, 0x3b,
	0xee, 0xdf, 0x60, 0xfd, 0xa9, 0x44, 0xdb, 0x07, 0x2d, 0x10, 0xb7, 0x75, 0xe8, 0x45, 0x6e, 0xa8,
	0xf7, 0xe8, 0x5e, 0x14, 0x3d, 0x78, 0x46, 0x2e, 0xfd, 0xc4, 0xb7, 0xe3, 0xe1, 0xf7, 0xd9, 0x19,
	0x73, 0x1d, 0x78, 0xc6, 0x53, 0xdf, 0xb3, 0x23, 0x2b, 0x64, 0x03, 0x37, 0xd9, 0x19, 0x45, 0x99,
	0xf6, 0x16, 0x2c, 0x92, 0x57, 0x16, 0x99, 0x20, 0x14, 0xce, 0x2d, 0x3a, 0x6c, 0x2b, 0x86, 0x2e,
	0x17, 0xb3, 0x81, 0x3b, 0xd0, 0xb5, 0x2e, 0x89, 0x75, 0x75, 0xee, 0x13, 0x33, 0xf0, 0x5c, 0x7d,
	0x9b, 0x69, 0x83, 0xca, 0x06, 0x54, 0x64, 0xfc, 0xb7, 0x0e, 0xab, 0xb1, 0xd3, 0x38, 0xa4, 0x4a,
	0x28, 0x8f, 0x3a, 0x92, 0xd5, 0x57, 0xb3, 0x56, 0x9f, 0xd1, 0x7e, 0x2d, 0xaf, 0x7d, 0xc9, 0xdb,
	0xd4, 0x4b, 0xbc, 0x4d, 0x43, 0xf6, 0x36, 0x2a, 0x7f, 0xa2, 0xb2, 0xcc, 0xb9, 0xdb, 0x5a, 0x66,
	0xeb, 0x4e, 0x96, 0xd9, 0xbe, 0x95, 0x65, 0xc2, 0x0c, 0x96, 0xd9, 0xc9, 0x5b, 0x66, 0xc6, 0xda,
	0xba, 0x33, 0x5a, 0xdb, 0xfc, 0xad, 0xac, 0x6d, 0x61, 0x56, 0x6b, 0x5b, 0x54, 0x58, 0xdb, 0x1a,
	0x34, 0xa3, 0x80, 0xf8, 0x27, 0x36, 0x75, 0x3e, 0xed, 0x01, 0x6f, 0xa1, 0xa7, 0xc7, 0xa7, 0x67,
	0xe6, 0x98, 0x50, 0x97, 0xd3, 0x1e, 0x24, 0x6d, 0xe3, 0x7f, 0x02, 0xe2, 0x5e, 0x50, 0xb3, 0x8a,
	0x11, 0x37, 0x73, 0xbc, 0x2a, 0xe1, 0x37, 0x19, 0x04, 0xd6, 0xa7, 0x20, 0xb0, 0x51, 0x82, 0xc0,
	0xa6, 0x1a, 0x81, 0x73, 0x53, 0x10, 0xd8, 0xba, 0x2d, 0x02, 0xdb, 0x77, 0x42, 0x20, 0xdc, 0x0a,
	0x81, 0x9d, 0x19, 0x10, 0xd8, 0x9d, 0x8a, 0xc0, 0xf9, 0x19, 0x11, 0xb8, 0x70, 0x2b, 0x04, 0x2e,
	0xce, 0x8a, 0xc0, 0xa5, 0x52, 0x04, 0x2e, 0x17, 0x22, 0x50, 0xcb, 0x20, 0xf0, 0x9f, 0x55, 0xd8,
	0x3a, 0x26, 0x61, 0x0c, 0x42, 0xee, 0x5b, 0x45, 0xc6, 0xbd, 0x04, 0xb5, 0x21, 0x85, 0x62, 0x6d,
	0xaf, 0x31, 0xc0, 0x47, 0xa6, 0x2b, 0x3a, 0x8e, 0x4e, 0xc9, 0x10, 0x29, 0x8a, 0x10, 0xad, 0xc1,
	0x55, 0x94, 0x60, 0x92, 0x35, 0x28, 0xcf, 0x64, 0x83, 0x4e, 0x6c, 0x4e, 0xea, 0x52, 0x01, 0xf6,
	0x1e, 0x66, 0x39, 0x54, 0x22, 0xc0, 0xb3, 0x9d, 0x31, 0xfe, 0xc3, 0xbc, 0x1e, 0x6f, 0x21, 0x46,
	0x3d, 0x8c, 0x18, 0x07, 0x37, 0x1c, 0x8c, 0x71, 0x33, 0xc3, 0xac, 0x5b, 0xd3, 0x98, 0x35, 0xae,
	0x40, 0x5e, 0x4d, 0x3c, 0x3f, 0x8c, 0x19, 0x16, 0x6b, 0x25, 0x58, 0x07, 0x01, 0xeb, 0xab, 0xd0,
	0xf4, 0xfc, 0xe1, 0xb9, 0x63, 0x73, 0x30, 0x35, 0x3c, 0x7f, 0x78, 0x62, 0x1b, 0x5f, 0xb5, 0xd3,
	0xa4, 0x85, 0x6b, 0x52, 0x49, 0x3e, 0xb9, 0x4a, 0x59, 0xc8, 0xa0, 0x2a, 0x45, 0x1a, 0xc7, 0xee,
	0xfb, 0x59, 0x34, 0xe6, 0x5a, 0x13, 0x24, 0xa8, 0xd0, 0x89, 0xef, 0x58, 0x71, 0xae, 0xc2, 0x1a,
	0x94, 0x28, 0xe2, 0x86, 0x1b, 0x9c, 0x28, 0xe2, 0x76, 0x0d, 0xe8, 0x5e, 0x20, 0x19, 0x1f, 0x3b,
	0xe1, 0xf3, 0x34, 0x48, 0x48, 0x32, 0xbc, 0xc0, 0xb8, 0x8d, 0xcb, 0x31, 0xc5, 0x89, 0x22, 0xaa,
	0xd6, 0x09, 0x71, 0xb1, 0xb7, 0xc5, 0xd5, 0xca, 0x9a, 0xd8, 0x33, 0x31, 0x7d, 0xfa, 0x1e, 0xd3,
	0x53, 0xdc, 0xc4, 0x59, 0x1d, 0xd7, 0x09, 0x4f, 0x79, 0x2f, 0xd3, 0x97, 0x28, 0x42, 0x53, 0xe4,
	0x67, 0x3a, 0xf4, 0xa2, 0x89, 0xe7, 0x52, 0xed, 0xb5, 0x07, 0xb2, 0x10, 0xcd, 0x5c, 0x12, 0x24,
	0xfb, 0x64, 0x46, 0xa9, 0xee, 0x44, 0x0d, 0xbe, 0x34, 0x9d, 0x11, 0x9f, 0x78, 0x9e, 0x51, 0x95,
	0x54, 0x82, 0x54, 0x25, 0x6d, 0x25, 0x53, 0x32, 0xeb, 0x54, 0xf4, 0xe0, 0x69, 0x82, 0x4b, 0xd3,
	0x27, 0x27, 0xe3, 0xe1, 0x0b, 0x7f, 0xc4, 0xe9, 0xa7, 0x28, 0x4a, 0x41, 0xbe, 0x54, 0x08, 0xf2,
	0x65, 0x05, 0xc8, 0x53, 0x87, 0xad, 0x29, 0x1c, 0xf6, 0xd8, 0xf4, 0xaf, 0x48, 0x78, 0x4a, 0xef,
	0x3a, 0xa6, 0x94, 0xa9, 0x08, 0x91, 0x33, 0x71, 0x2c, 0x4a, 0x22, 0xdb, 0x03, 0x7c, 0xa4, 0xbb,
	0x08, 0x3d, 0xeb, 0x8a, 0x53, 0x45, 0xd6, 0xc0, 0x75, 0x9c, 0xe0, 0x53, 0xc7, 0x0f, 0x23, 0x73,
	0xc4, 0xb9, 0x61, 0x2a, 0xc0, 0xde, 0xa1, 0xe7, 0xd9, 0x01, 0x05, 0xc8, 0x3a, 0xeb, 0x4d, 0x04,
	0xda, 0x3e, 0x74, 0xac, 0x4b, 0x67, 0x64, 0x9f, 0xe1, 0x79, 0x02, 0x5d, 0xa7, 0x89, 0x56, 0x17,
	0x2d, 0xe7, 0x10, 0xc5, 0x03, 0xe2, 0x0e, 0xc4, 0x01, 0x59, 0x77, 0xb0, 0x91, 0x77, 0x07, 0x7d,
	0xe8, 0x58, 0xa6, 0x7b, 0x40, 0x3e, 0xb2, 0x9d, 0x90, 0xd8, 0x9c, 0x1f, 0x8a, 0x22, 0x44, 0x2d,
	0x6d, 0x1e, 0x91, 0x11, 0xc1, 0x21, 0x8c, 0x13, 0x4a, 0x32, 0x44, 0x0f, 0x02, 0xe9, 0x45, 0x10,
	0x13, 0x47, 0xc6, 0x07, 0x65, 0xa1, 0xb6, 0x07, 0x8b, 0x34, 0x51, 0x14, 0x08, 0x26, 0x23, 0x84,
	0x59, 0xb1, 0x90, 0x52, 0x6d, 0x4b, 0x29, 0x55, 0x9c, 0x7e, 0xbd, 0x21, 0xa4, 0x5f, 0x32, 0x51,
	0xee, 0x4f, 0x21, 0xca, 0x3b, 0x39, 0xa2, 0xbc, 0x0d, 0x40, 0x4d, 0x76, 0x80, 0xbe, 0x46, 0x37,
	0xfa, 0x95, 0xbd, 0xca, 0x40, 0x90, 0x68, 0xf7, 0xf1, 0xbe, 0xce, 0x5d, 0xcf, 0x1f, 0x9b, 0x23,
	0xfd, 0x4d, 0x96, 0x65, 0x3b, 0xc1, 0x33, 0xda, 0xd6, 0xd6, 0x61, 0xce, 0x09, 0xce, 0x11, 0x06,
	0xfa, 0x03, 0xb6, 0x53, 0x27, 0x78, 0x6a, 0xfa, 0x57, 0x78, 0xd6, 0xc1, 0x7b, 0xa7, 0x91, 0x6f,
	0x5d, 0x9a, 0x01, 0x61, 0x98, 0x79, 0xc8, 0xce, 0x9a, 0x11, 0xa3, 0x7e, 0xf1, 0x8d, 0x23, 0x27,
	0x60, 0x1c, 0x79, 0x97, 0xee, 0x40, 0x92, 0x61, 0x40, 0xc2, 0xb6, 0x3c, 0xdf, 0x5b, 0x2c, 0x20,
	0xe5, 0x3a, 0x8c, 0xbf, 0x56, 0x60, 0x27, 0xe3, 0xeb, 0x0e, 0xa3, 0x20, 0xf4, 0xc6, 0xdf, 0x50,
	0x1d, 0xe0, 0x3d, 0xa9, 0x0e, 0xb0, 0x25, 0xd6, 0x01, 0xf2, 0xdb, 0x61, 0x45, 0x81, 0xbf, 0x57,
	0x61, 0xa3, 0x70, 0xcc, 0x0c, 0x4e, 0x3a, 0x71, 0xc2, 0x35, 0xd1, 0x09, 0x27, 0x6e, 0xa0, 0x5e,
	0xe8, 0x06, 0x1a, 0xa5, 0x6e, 0xa0, 0x39, 0xc5, 0x0d, 0xcc, 0x15, 0xba, 0x81, 0x56, 0xea, 0x06,
	0x32, 0x46, 0xd8, 0xce, 0x1b, 0x21, 0xf2, 0x20, 0xc9, 0x7c, 0x62, 0x26, 0x2e, 0x99, 0x4f, 0xc6,
	0x89, 0x77, 0x72, 0x4e, 0xdc, 0xf8, 0xb2, 0x9d, 0xd3, 0xe2, 0x11, 0x09, 0x4d, 0x67, 0xf4, 0x6d,
	0xa8, 0xfb, 0x36, 0xd4, 0xcd, 0x1c, 0xea, 0x72, 0xac, 0xfc, 0xde, 0x0c, 0xac, 0x7c, 0x25, 0xcf,
	0xca, 0xa5, 0x0c, 0x66, 0xb5, 0x24, 0x83, 0x59, 0xcb, 0x65, 0x30, 0xd4, 0xc9, 0xaf, 0x0b, 0x4e,
	0x3e, 0x66, 0x7a, 0xba, 0xc0, 0xf4, 0x0a, 0x33, 0x95, 0x8d, 0x3b, 0x65, 0x2a, 0xbd, 0xb2, 0x4c,
	0x25, 0x0d, 0x48, 0xf7, 0xa5, 0x80, 0xf4, 0x08, 0x96, 0x4e, 0xb3, 0x55, 0xa9, 0x4d, 0x56, 0x95,
	0xca, 0xca, 0x71, 0xec, 0x47, 0xd9, 0x2c, 0x8c, 0xc5, 0xbf, 0x9c, 0x3c, 0xeb, 0x67, 0xb6, 0x0b,
	0xfd, 0xcc, 0x1b, 0x85, 0x7e, 0xa6, 0xaf, 0xe6, 0xfe, 0x94, 0x90, 0xec, 0x14, 0x12, 0x12, 0xa3,
	0x94, 0x90, 0xbc, 0x39, 0x85, 0x90, 0x3c, 0x98, 0x46, 0x48, 0xe4, 0x60, 0xfd, 0x70, 0x4a, 0xb0,
	0xde, 0xcd, 0x06, 0x6b, 0xe3, 0xab, 0x0a, 0x6c, 0x17, 0xe5, 0x44, 0x5f, 0x7b, 0x5c, 0x7b, 0x5b,
	0x8a, 0x6b, 0xeb, 0x8a, 0xb8, 0x96, 0x96, 0xb9, 0xb5, 0x1d, 0xe8, 0xba, 0x1e, 0x63, 0x0d, 0xe7,
	0x6e, 0x34, 0xe6, 0x1e, 0xb0, 0x13, 0xcb, 0xd0, 0xb8, 0xb7, 0x00, 0x84, 0x01, 0xcc, 0xff, 0xb5,
	0x93, 0x6e, 0xe3, 0x0f, 0x15, 0xd8, 0x64, 0xb5, 0xae, 0xcc, 0x2a, 0x42, 0xe9, 0x8b, 0x99, 0x7e,
	0xa5, 0xd0, 0xf4, 0xab, 0x59, 0xd3, 0xff, 0x00, 0x5a, 0x81, 0x79, 0x4d, 0x70, 0xa3, 0xf4, 0x70,
	0x9d, 0xc7, 0xf7, 0xf1, 0x1c, 0x67, 0xe6, 0x35, 0x51, 0x9d, 0x25, 0x19, 0x2c, 0xe4, 0x55, 0x0d,
	0x31, 0xaf, 0xfa, 0xa2, 0x02, 0x9b, 0xac, 0x3c, 0x52, 0xb0, 0x49, 0x45, 0xb5, 0x84, 0x6d, 0xba,
	0x5a, 0xb8, 0xe9, 0x5a, 0xd9, 0xa6, 0x1b, 0x77, 0xdb, 0x74, 0x53, 0xdc, 0xf4, 0x23, 0xd0, 0x33,
	0xef, 0x9d, 0xd8, 0x05, 0xfb, 0x35, 0x7e, 0x5f, 0x81, 0x7e, 0x1e, 0x71, 0x2c, 0xac, 0xbe, 0x56,
	0xcc, 0xc5, 0xac, 0x89, 0xdd, 0x8a, 0x8a, 0x35, 0xa5, 0xb1, 0x9c, 0xb3, 0xa6, 0x2f, 0x6b, 0xb0,
	0x5e, 0xa0, 0x84, 0xb4, 0x36, 0x50, 0x10, 0xdd, 0xab, 0xc5, 0xd1, 0xbd, 0xa6, 0x8a, 0xee, 0xf5,
	0x92, 0xe8, 0xde, 0x98, 0x1e, 0xdd, 0x9b, 0xa5, 0xd1, 0x7d, 0xae, 0x30, 0xba, 0xb7, 0x4a, 0xa3,
	0x7b, 0x7b, 0x86, 0xe8, 0x0e, 0xb7, 0x8a, 0xee, 0x9d, 0xd9, 0xa3, 0x7b, 0x77, 0xc6, 0xe8, 0x3e,
	0x3f, 0x6b, 0x74, 0x5f, 0xc8, 0x45, 0x77, 0xe3, 0x4f, 0x15, 0xd8, 0x2c, 0x80, 0x9e, 0xda, 0xbc,
	0x94, 0xd4, 0xf8, 0x2e, 0x05, 0x9f, 0x92, 0x8f, 0x66, 0x05, 0x56, 0xf5, 0x9d, 0x1c, 0xf9, 0x3c,
	0xb1, 0x03, 0xa1, 0x54, 0xe5, 0xd8, 0x41, 0x5c, 0xaa, 0x72, 0xec, 0xc0, 0x78, 0x13, 0x96, 0xe3,
	0xe1, 0xc5, 0xd6, 0xf7, 0x79, 0x05, 0xd6, 0x92, 0x8f, 0x85, 0xaf, 0xdf, 0xe6, 0xde, 0x87, 0xee,
	0x50, 0xf8, 0x14, 0xc9, 0x6d, 0x2f, 0xff, 0xe5, 0x52, 0x1a, 0x65, 0xfc, 0xbb, 0x02, 0x6f, 0x09,
	0x2e, 0xe1, 0xc5, 0x29, 0xc6, 0x64, 0x7a, 0xe0, 0x33, 0x32, 0x22, 0x05, 0x25, 0xba, 0xfc, 0x7d,
	0xdc, 0xc2, 0x01, 0x66, 0x42, 0x7b, 0x3d, 0x1f, 0xda, 0xe5, 0x92, 0x5a, 0x63, 0x6a, 0x49, 0xad,
	0xe0, 0x0e, 0xff, 0x56, 0x81, 0xbd, 0xe9, 0x47, 0xfb, 0xda, 0x23, 0xed, 0xf7, 0xa4, 0x48, 0xbb,
	0x23, 0xde, 0x07, 0xdb, 0x87, 0xb0, 0x31, 0xc1, 0x1f, 0xfe, 0xab, 0x0a, 0x5b, 0xa5, 0xe3, 0xee,
	0x14, 0x32, 0x33, 0xca, 0xaf, 0xe5, 0x95, 0x6f, 0x40, 0x37, 0x74, 0xc6, 0xe4, 0xd0, 0x73, 0x5f,
	0x8e, 0x1c, 0x2b, 0xf6, 0x95, 0x92, 0x2c, 0xe6, 0x6b, 0x0d, 0x45, 0x79, 0xa8, 0x29, 0xb2, 0xb1,
	0xe9, 0x19, 0xa6, 0xc4, 0xd7, 0x5a, 0xa5, 0x7c, 0xad, 0x3d, 0x85, 0xaf, 0xc1, 0x14, 0xbe, 0x66,
	0xfc, 0xa5, 0x0a, 0xbd, 0x58, 0xa3, 0xb4, 0x3e, 0x73, 0x16, 0x9a, 0xa1, 0x63, 0xc5, 0xe8, 0xee,
	0x41, 0x2b, 0xb8, 0x71, 0x2d, 0xba, 0x16, 0xd3, 0x68, 0xd2, 0xd6, 0xf6, 0x60, 0x71, 0x12, 0x53,
	0x01, 0xcb, 0xf3, 0xed, 0x44, 0xb5, 0x59, 0x71, 0x6c, 0x23, 0x35, 0x85, 0x8d, 0xbc, 0xb6, 0xc4,
	0xfd, 0x11, 0x2c, 0x71, 0x9f, 0x3e, 0x20, 0xbc, 0xd4, 0x14, 0x7f, 0x84, 0xcb, 0xca, 0xb5, 0x1f,
	0x82, 0xce, 0x65, 0x47, 0x4e, 0x10, 0x3a, 0xae, 0x25, 0x64, 0xe6, 0x4c, 0xdf, 0x85, 0xfd, 0xc6,
	0x2f, 0x2b, 0xd0, 0x8a, 0x55, 0x59, 0x80, 0x36, 0x1d, 0xe6, 0xfc, 0x68, 0x44, 0xd2, 0x20, 0x1c,
	0x37, 0xe5, 0x9b, 0xad, 0x65, 0x6f, 0x76, 0x17, 0x16, 0x28, 0x44, 0x7e, 0x6a, 0xfa, 0xe4, 0xd2,
	0x8b, 0x82, 0x18, 0x1c, 0x19, 0xa9, 0xf1, 0xe7, 0x0a, 0xec, 0x1c, 0xa7, 0xb0, 0xa7, 0x5e, 0xf3,
	0x89, 0xe7, 0xb3, 0x7a, 0xca, 0x37, 0x60, 0xbc, 0xfb, 0x89, 0xf1, 0xa2, 0x13, 0xea, 0x51, 0x27,
	0xa4, 0xde, 0x0b, 0xb3, 0xda, 0x7f, 0x34, 0x60, 0x4d, 0x3d, 0x00, 0x91, 0x71, 0x9c, 0x7a, 0xcf,
	0xe3, 0x3b, 0x7a, 0x4f, 0x09, 0x19, 0xf5, 0x2c, 0x32, 0x56, 0xa0, 0xc1, 0x4c, 0x8d, 0xf3, 0x5a,
	0x66, 0x64, 0x7d, 0xe8, 0x3c, 0x15, 0xcc, 0x90, 0xd3, 0x17, 0x41, 0x84, 0x64, 0xe0, 0x2c, 0xa5,
	0x53, 0xec, 0x2a, 0x04, 0x09, 0xad, 0x89, 0x4a, 0x69, 0x74, 0x8b, 0xd7, 0x44, 0xa5, 0x34, 0x5a,
	0x95, 0x40, 0xb6, 0x0b, 0x12, 0xc8, 0xf4, 0x43, 0x0b, 0x48, 0x1f, 0x5a, 0x36, 0xa1, 0x7d, 0x90,
	0x24, 0xda, 0xac, 0x98, 0x91, 0x0a, 0xf0, 0x52, 0x3f, 0xe2, 0x89, 0x36, 0x63, 0x2c, 0x71, 0x13,
	0x21, 0x70, 0x86, 0x89, 0x36, 0x23, 0x28, 0xf4, 0x19, 0x65, 0xd4, 0x9a, 0x59, 0x49, 0x82, 0x3e,
	0x23, 0x59, 0xfa, 0x50, 0x99, 0x68, 0xb3, 0x4f, 0x62, 0xea, 0x4e, 0xe5, 0x5b, 0x74, 0x6a, 0xf6,
	0x0d, 0x56, 0xdd, 0x89, 0xae, 0xf4, 0x40, 0xa4, 0x96, 0xac, 0x76, 0x21, 0xc9, 0xf0, 0x6e, 0x0e,
	0x04, 0x6a, 0xc9, 0x0a, 0x18, 0xa2, 0x08, 0x47, 0x9c, 0x09, 0xc4, 0xea, 0x1e, 0x73, 0xd9, 0x82,
	0x28, 0x5f, 0xb1, 0x5e, 0x51, 0x55, 0xac, 0xdf, 0x49, 0x39, 0x4a, 0x42, 0x69, 0xe2, 0x1f, 0x7e,
	0xe4, 0x3a, 0x8c, 0xdf, 0x30, 0xc3, 0x4b, 0x22, 0xd0, 0x55, 0x14, 0x60, 0xbc, 0x3c, 0xb8, 0xa1,
	0xce, 0x74, 0x6a, 0xd6, 0x56, 0xf2, 0x83, 0x85, 0x6d, 0x80, 0x89, 0xe3, 0x32, 0x07, 0xec, 0x72,
	0x1b, 0x14, 0x24, 0x42, 0x34, 0xaf, 0x8b, 0xd1, 0xfc, 0xf3, 0x0a, 0x18, 0x65, 0x1b, 0x7a, 0x8d,
	0xae, 0x60, 0x2f, 0xc9, 0x5e, 0x30, 0xa2, 0xac, 0x48, 0x11, 0x9b, 0x2f, 0xcb, 0xcd, 0xfd, 0x3f,
	0x55, 0x58, 0xca, 0x76, 0xa9, 0x69, 0x12, 0xcb, 0x46, 0xaa, 0xca, 0x8a, 0xee, 0x2d, 0xc8, 0x6c,
	0x26, 0xa2, 0x36, 0x0a, 0x6b, 0x29, 0xcd, 0xc2, 0x5a, 0xca, 0x5c, 0x3e, 0xe6, 0x63, 0xe8, 0x9b,
	0x10, 0x8b, 0x76, 0xb3, 0x62, 0x6f, 0xd2, 0xc6, 0xfd, 0xe0, 0xf3, 0xa7, 0xe6, 0x28, 0x8a, 0xad,
	0x39, 0x15, 0xe4, 0x20, 0x0e, 0xd3, 0x21, 0xde, 0xc9, 0x43, 0x5c, 0x02, 0x4c, 0x37, 0x03, 0x18,
	0x63, 0x1f, 0x74, 0xf1, 0xe2, 0xa9, 0x9f, 0x88, 0x01, 0xa8, 0x41, 0x7d, 0x98, 0x72, 0x71, 0xfa,
	0x6c, 0xfc, 0xaa, 0x02, 0x1b, 0x8a, 0x17, 0x5e, 0x23, 0x40, 0x76, 0x25, 0x80, 0x68, 0x12, 0x40,
	0xd8, 0x6a, 0x0c, 0x1e, 0xbf, 0xae, 0xc0, 0x82, 0xdc, 0xa1, 0x00, 0x47, 0xd1, 0x8f, 0x4a, 0xe3,
	0xd2, 0x61, 0x4d, 0x28, 0x1d, 0x6e, 0x01, 0xd0, 0xaa, 0xe3, 0xb9, 0xad, 0xfc, 0x2d, 0xcf, 0x06,
	0xb4, 0x88, 0x6b, 0xb3, 0x4e, 0xf9, 0xc7, 0x3c, 0x8f, 0xff, 0xd8, 0x81, 0xc5, 0x94, 0x4e, 0xd2,
	0xdf, 0xc7, 0x6a, 0x47, 0xb0, 0x28, 0xe8, 0x89, 0x62, 0x57, 0x2a, 0x04, 0x09, 0xdc, 0xbf, 0xa7,
	0xe7, 0x3b, 0xb8, 0x42, 0x7f, 0x04, 0x0b, 0x72, 0x65, 0x47, 0xdb, 0x10, 0xc7, 0x4a, 0xbf, 0x70,
	0xea, 0xd1, 0x74, 0xe4, 0xc2, 0x0c, 0x88, 0xf8, 0xba, 0x5c, 0x73, 0x91, 0x5f, 0x97, 0x7e, 0xae,
	0xa2, 0x78, 0xfd, 0x09, 0x2c, 0x0b, 0x67, 0x60, 0x31, 0x57, 0x5b, 0x15, 0x67, 0x48, 0xbc, 0x55,
	0xaf, 0x27, 0xe5, 0x42, 0x72, 0x06, 0xf6, 0x03, 0x58, 0x60, 0x1f, 0x00, 0x93, 0x6d, 0x14, 0x4c,
	0x92, 0xdf, 0xc2, 0x07, 0xd0, 0xc5, 0x70, 0x73, 0xfb, 0x17, 0xcf, 0x61, 0x4d, 0x5d, 0xff, 0xd3,
	0x58, 0x96, 0x50, 0xf6, 0x7b, 0x89, 0x9e, 0x51, 0x36, 0x84, 0x2f, 0x70, 0x05, 0x6f, 0xa8, 0x47,
	0xa4, 0xac, 0x64, 0x86, 0x95, 0x1e, 0x96, 0x7f, 0xf4, 0x8a, 0x17, 0xfb, 0x18, 0x56, 0x95, 0x15,
	0x3e, 0xad, 0x4f, 0x29, 0x79, 0x49, 0xf1, 0x4f, 0xa1, 0x9a, 0x8f, 0x61, 0x55, 0x59, 0x89, 0x63,
	0x93, 0x95, 0x15, 0xe9, 0x14, 0x93, 0x5d, 0x48, 0x0e, 0x44, 0xa2, 0x67, 0x6c, 0xbe, 0xb2, 0xaa,
	0x44, 0xef, 0x81, 0x5a, 0x43, 0x19, 0xfc, 0x8c, 0xa4, 0x70, 0x59, 0x40, 0x01, 0xa7, 0x2f, 0xf6,
	0x90, 0x2f, 0x36, 0x85, 0xf0, 0x1e, 0xc3, 0xaa, 0x8c, 0xd6, 0x58, 0x3d, 0x9b, 0x8a, 0x15, 0xca,
	0x20, 0xf8, 0x0b, 0xa9, 0x20, 0xa8, 0x4c, 0x91, 0xb5, 0xb7, 0x33, 0x0a, 0x28, 0xab, 0x11, 0xf4,
	0xde, 0x99, 0x6d, 0x30, 0x5f, 0xfe, 0x13, 0xb8, 0xaf, 0xc8, 0xc8, 0x0e, 0xcd, 0xd1, 0xe8, 0xc0,
	0xb4, 0xae, 0xb4, 0x6d, 0xf1, 0x34, 0xf9, 0x94, 0x4d, 0x71, 0x9e, 0x2b, 0xe8, 0x15, 0x93, 0x04,
	0xed, 0x61, 0x66, 0x73, 0x6a, 0x56, 0xd3, 0xdb, 0x9d, 0x36, 0x8c, 0x2f, 0x76, 0x2a, 0xf9, 0x9e,
	0x98, 0xc0, 0x66, 0x5f, 0x16, 0xe3, 0x55, 0x6f, 0xab, 0xa0, 0x97, 0xcd, 0x78, 0xd1, 0xa4, 0xff,
	0x95, 0xf0, 0xdd, 0xff, 0x07, 0x00, 0x00, 0xff, 0xff, 0x31, 0xdf, 0x34, 0xc4, 0xd0, 0x30, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GroupBuyServiceClient is the client API for GroupBuyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GroupBuyServiceClient interface {
	//团购活动列表
	GetGroupBuyList(ctx context.Context, in *GroupBuyListRequest, opts ...grpc.CallOption) (*GroupBuyListResponse, error)
	//创建拼团活动
	CreateGroupBuy(ctx context.Context, in *GroupBuyCreateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新拼团活动
	UpdateGroupBuy(ctx context.Context, in *GroupBuyUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//拼团活动详情
	GetGroupBuyDetail(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*GroupBuyDetailResponse, error)
	//删除拼团活动
	DeleteGroupBuy(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//终止拼团活动
	StopGroupBuy(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取拼团商品列表 boss
	GetGroupBuyProductList(ctx context.Context, in *GetGroupBuyProductListRequest, opts ...grpc.CallOption) (*GetGroupBuyProductListResponse, error)
	// 获取拼团商品列表 客户端
	GetGroupBuyProductListForCustom(ctx context.Context, in *GetGroupBuyProductListRequest, opts ...grpc.CallOption) (*GroupBuyProductCustomListResponse, error)
	// 创建拼团商品
	CreateGroupBuyProduct(ctx context.Context, in *CreateGroupBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新拼团商品
	UpdateGroupBuyProduct(ctx context.Context, in *UpdateGroupBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取拼团商品详情
	GetGroupBuyProductDetail(ctx context.Context, in *GroupBuyProductDetailRequest, opts ...grpc.CallOption) (*GetGroupBuyProductDetailResponse, error)
	// 获取拼团商品详情 -用于客户端请求
	GetGroupBuyProductDetailForCustom(ctx context.Context, in *GroupBuyProductDetailRequest, opts ...grpc.CallOption) (*GetProductDetailForCustomResponse, error)
	// 删除拼团商品
	DeleteGroupBuyProduct(ctx context.Context, in *GroupBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取可以参加拼团活动的阿闻电商渠道的商品
	GetGroupBuyUPetProductSelectList(ctx context.Context, in *GetGroupBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetGroupBuyUPetProductSelectListResponse, error)
	// 拼团订单下单与成团等行为给活动中心订单统计的回调
	GroupBuyOrderStaticCallBack(ctx context.Context, in *GroupBuyOrderStaticRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除拼团商品
	GetGroupBuySkusListBySkuId(ctx context.Context, in *GetGroupBuySkusListBySkuIdRequest, opts ...grpc.CallOption) (*GetGroupBuySkusListBySkuIdResponse, error)
	//根据活动id活动的状态相关字段
	GetGroupBuyStatus(ctx context.Context, in *GetGroupBuyStatusRequest, opts ...grpc.CallOption) (*GetGroupBuyStatusResponse, error)
}

type groupBuyServiceClient struct {
	cc *grpc.ClientConn
}

func NewGroupBuyServiceClient(cc *grpc.ClientConn) GroupBuyServiceClient {
	return &groupBuyServiceClient{cc}
}

func (c *groupBuyServiceClient) GetGroupBuyList(ctx context.Context, in *GroupBuyListRequest, opts ...grpc.CallOption) (*GroupBuyListResponse, error) {
	out := new(GroupBuyListResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) CreateGroupBuy(ctx context.Context, in *GroupBuyCreateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/CreateGroupBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) UpdateGroupBuy(ctx context.Context, in *GroupBuyUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/UpdateGroupBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyDetail(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*GroupBuyDetailResponse, error) {
	out := new(GroupBuyDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) DeleteGroupBuy(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/DeleteGroupBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) StopGroupBuy(ctx context.Context, in *GroupBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/StopGroupBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyProductList(ctx context.Context, in *GetGroupBuyProductListRequest, opts ...grpc.CallOption) (*GetGroupBuyProductListResponse, error) {
	out := new(GetGroupBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyProductListForCustom(ctx context.Context, in *GetGroupBuyProductListRequest, opts ...grpc.CallOption) (*GroupBuyProductCustomListResponse, error) {
	out := new(GroupBuyProductCustomListResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyProductListForCustom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) CreateGroupBuyProduct(ctx context.Context, in *CreateGroupBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/CreateGroupBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) UpdateGroupBuyProduct(ctx context.Context, in *UpdateGroupBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/UpdateGroupBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyProductDetail(ctx context.Context, in *GroupBuyProductDetailRequest, opts ...grpc.CallOption) (*GetGroupBuyProductDetailResponse, error) {
	out := new(GetGroupBuyProductDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyProductDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyProductDetailForCustom(ctx context.Context, in *GroupBuyProductDetailRequest, opts ...grpc.CallOption) (*GetProductDetailForCustomResponse, error) {
	out := new(GetProductDetailForCustomResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyProductDetailForCustom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) DeleteGroupBuyProduct(ctx context.Context, in *GroupBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/DeleteGroupBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyUPetProductSelectList(ctx context.Context, in *GetGroupBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetGroupBuyUPetProductSelectListResponse, error) {
	out := new(GetGroupBuyUPetProductSelectListResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyUPetProductSelectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GroupBuyOrderStaticCallBack(ctx context.Context, in *GroupBuyOrderStaticRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GroupBuyOrderStaticCallBack", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuySkusListBySkuId(ctx context.Context, in *GetGroupBuySkusListBySkuIdRequest, opts ...grpc.CallOption) (*GetGroupBuySkusListBySkuIdResponse, error) {
	out := new(GetGroupBuySkusListBySkuIdResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuySkusListBySkuId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupBuyServiceClient) GetGroupBuyStatus(ctx context.Context, in *GetGroupBuyStatusRequest, opts ...grpc.CallOption) (*GetGroupBuyStatusResponse, error) {
	out := new(GetGroupBuyStatusResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupBuyService/GetGroupBuyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupBuyServiceServer is the server API for GroupBuyService service.
type GroupBuyServiceServer interface {
	//团购活动列表
	GetGroupBuyList(context.Context, *GroupBuyListRequest) (*GroupBuyListResponse, error)
	//创建拼团活动
	CreateGroupBuy(context.Context, *GroupBuyCreateRequest) (*BaseResponse, error)
	//更新拼团活动
	UpdateGroupBuy(context.Context, *GroupBuyUpdateRequest) (*BaseResponse, error)
	//拼团活动详情
	GetGroupBuyDetail(context.Context, *GroupBuyIdRequest) (*GroupBuyDetailResponse, error)
	//删除拼团活动
	DeleteGroupBuy(context.Context, *GroupBuyIdRequest) (*BaseResponse, error)
	//终止拼团活动
	StopGroupBuy(context.Context, *GroupBuyIdRequest) (*BaseResponse, error)
	// 获取拼团商品列表 boss
	GetGroupBuyProductList(context.Context, *GetGroupBuyProductListRequest) (*GetGroupBuyProductListResponse, error)
	// 获取拼团商品列表 客户端
	GetGroupBuyProductListForCustom(context.Context, *GetGroupBuyProductListRequest) (*GroupBuyProductCustomListResponse, error)
	// 创建拼团商品
	CreateGroupBuyProduct(context.Context, *CreateGroupBuyProductRequest) (*BaseResponse, error)
	// 更新拼团商品
	UpdateGroupBuyProduct(context.Context, *UpdateGroupBuyProductRequest) (*BaseResponse, error)
	// 获取拼团商品详情
	GetGroupBuyProductDetail(context.Context, *GroupBuyProductDetailRequest) (*GetGroupBuyProductDetailResponse, error)
	// 获取拼团商品详情 -用于客户端请求
	GetGroupBuyProductDetailForCustom(context.Context, *GroupBuyProductDetailRequest) (*GetProductDetailForCustomResponse, error)
	// 删除拼团商品
	DeleteGroupBuyProduct(context.Context, *GroupBuyProductIdRequest) (*BaseResponse, error)
	// 获取可以参加拼团活动的阿闻电商渠道的商品
	GetGroupBuyUPetProductSelectList(context.Context, *GetGroupBuyUPetProductSelectListRequest) (*GetGroupBuyUPetProductSelectListResponse, error)
	// 拼团订单下单与成团等行为给活动中心订单统计的回调
	GroupBuyOrderStaticCallBack(context.Context, *GroupBuyOrderStaticRequest) (*BaseResponse, error)
	// 删除拼团商品
	GetGroupBuySkusListBySkuId(context.Context, *GetGroupBuySkusListBySkuIdRequest) (*GetGroupBuySkusListBySkuIdResponse, error)
	//根据活动id活动的状态相关字段
	GetGroupBuyStatus(context.Context, *GetGroupBuyStatusRequest) (*GetGroupBuyStatusResponse, error)
}

// UnimplementedGroupBuyServiceServer can be embedded to have forward compatible implementations.
type UnimplementedGroupBuyServiceServer struct {
}

func (*UnimplementedGroupBuyServiceServer) GetGroupBuyList(ctx context.Context, req *GroupBuyListRequest) (*GroupBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyList not implemented")
}
func (*UnimplementedGroupBuyServiceServer) CreateGroupBuy(ctx context.Context, req *GroupBuyCreateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroupBuy not implemented")
}
func (*UnimplementedGroupBuyServiceServer) UpdateGroupBuy(ctx context.Context, req *GroupBuyUpdateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroupBuy not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyDetail(ctx context.Context, req *GroupBuyIdRequest) (*GroupBuyDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyDetail not implemented")
}
func (*UnimplementedGroupBuyServiceServer) DeleteGroupBuy(ctx context.Context, req *GroupBuyIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupBuy not implemented")
}
func (*UnimplementedGroupBuyServiceServer) StopGroupBuy(ctx context.Context, req *GroupBuyIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopGroupBuy not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyProductList(ctx context.Context, req *GetGroupBuyProductListRequest) (*GetGroupBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyProductList not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyProductListForCustom(ctx context.Context, req *GetGroupBuyProductListRequest) (*GroupBuyProductCustomListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyProductListForCustom not implemented")
}
func (*UnimplementedGroupBuyServiceServer) CreateGroupBuyProduct(ctx context.Context, req *CreateGroupBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroupBuyProduct not implemented")
}
func (*UnimplementedGroupBuyServiceServer) UpdateGroupBuyProduct(ctx context.Context, req *UpdateGroupBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroupBuyProduct not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyProductDetail(ctx context.Context, req *GroupBuyProductDetailRequest) (*GetGroupBuyProductDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyProductDetail not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyProductDetailForCustom(ctx context.Context, req *GroupBuyProductDetailRequest) (*GetProductDetailForCustomResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyProductDetailForCustom not implemented")
}
func (*UnimplementedGroupBuyServiceServer) DeleteGroupBuyProduct(ctx context.Context, req *GroupBuyProductIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupBuyProduct not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyUPetProductSelectList(ctx context.Context, req *GetGroupBuyUPetProductSelectListRequest) (*GetGroupBuyUPetProductSelectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyUPetProductSelectList not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GroupBuyOrderStaticCallBack(ctx context.Context, req *GroupBuyOrderStaticRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupBuyOrderStaticCallBack not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuySkusListBySkuId(ctx context.Context, req *GetGroupBuySkusListBySkuIdRequest) (*GetGroupBuySkusListBySkuIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuySkusListBySkuId not implemented")
}
func (*UnimplementedGroupBuyServiceServer) GetGroupBuyStatus(ctx context.Context, req *GetGroupBuyStatusRequest) (*GetGroupBuyStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupBuyStatus not implemented")
}

func RegisterGroupBuyServiceServer(s *grpc.Server, srv GroupBuyServiceServer) {
	s.RegisterService(&_GroupBuyService_serviceDesc, srv)
}

func _GroupBuyService_GetGroupBuyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyList(ctx, req.(*GroupBuyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_CreateGroupBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).CreateGroupBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/CreateGroupBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).CreateGroupBuy(ctx, req.(*GroupBuyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_UpdateGroupBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).UpdateGroupBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/UpdateGroupBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).UpdateGroupBuy(ctx, req.(*GroupBuyUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyDetail(ctx, req.(*GroupBuyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_DeleteGroupBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).DeleteGroupBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/DeleteGroupBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).DeleteGroupBuy(ctx, req.(*GroupBuyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_StopGroupBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).StopGroupBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/StopGroupBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).StopGroupBuy(ctx, req.(*GroupBuyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductList(ctx, req.(*GetGroupBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyProductListForCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductListForCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyProductListForCustom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductListForCustom(ctx, req.(*GetGroupBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_CreateGroupBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).CreateGroupBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/CreateGroupBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).CreateGroupBuyProduct(ctx, req.(*CreateGroupBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_UpdateGroupBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).UpdateGroupBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/UpdateGroupBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).UpdateGroupBuyProduct(ctx, req.(*UpdateGroupBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyProductDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyProductDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductDetail(ctx, req.(*GroupBuyProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyProductDetailForCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductDetailForCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyProductDetailForCustom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyProductDetailForCustom(ctx, req.(*GroupBuyProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_DeleteGroupBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyProductIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).DeleteGroupBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/DeleteGroupBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).DeleteGroupBuyProduct(ctx, req.(*GroupBuyProductIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyUPetProductSelectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupBuyUPetProductSelectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyUPetProductSelectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyUPetProductSelectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyUPetProductSelectList(ctx, req.(*GetGroupBuyUPetProductSelectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GroupBuyOrderStaticCallBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBuyOrderStaticRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GroupBuyOrderStaticCallBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GroupBuyOrderStaticCallBack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GroupBuyOrderStaticCallBack(ctx, req.(*GroupBuyOrderStaticRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuySkusListBySkuId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupBuySkusListBySkuIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuySkusListBySkuId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuySkusListBySkuId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuySkusListBySkuId(ctx, req.(*GetGroupBuySkusListBySkuIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupBuyService_GetGroupBuyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupBuyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupBuyServiceServer).GetGroupBuyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupBuyService/GetGroupBuyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupBuyServiceServer).GetGroupBuyStatus(ctx, req.(*GetGroupBuyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GroupBuyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.GroupBuyService",
	HandlerType: (*GroupBuyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroupBuyList",
			Handler:    _GroupBuyService_GetGroupBuyList_Handler,
		},
		{
			MethodName: "CreateGroupBuy",
			Handler:    _GroupBuyService_CreateGroupBuy_Handler,
		},
		{
			MethodName: "UpdateGroupBuy",
			Handler:    _GroupBuyService_UpdateGroupBuy_Handler,
		},
		{
			MethodName: "GetGroupBuyDetail",
			Handler:    _GroupBuyService_GetGroupBuyDetail_Handler,
		},
		{
			MethodName: "DeleteGroupBuy",
			Handler:    _GroupBuyService_DeleteGroupBuy_Handler,
		},
		{
			MethodName: "StopGroupBuy",
			Handler:    _GroupBuyService_StopGroupBuy_Handler,
		},
		{
			MethodName: "GetGroupBuyProductList",
			Handler:    _GroupBuyService_GetGroupBuyProductList_Handler,
		},
		{
			MethodName: "GetGroupBuyProductListForCustom",
			Handler:    _GroupBuyService_GetGroupBuyProductListForCustom_Handler,
		},
		{
			MethodName: "CreateGroupBuyProduct",
			Handler:    _GroupBuyService_CreateGroupBuyProduct_Handler,
		},
		{
			MethodName: "UpdateGroupBuyProduct",
			Handler:    _GroupBuyService_UpdateGroupBuyProduct_Handler,
		},
		{
			MethodName: "GetGroupBuyProductDetail",
			Handler:    _GroupBuyService_GetGroupBuyProductDetail_Handler,
		},
		{
			MethodName: "GetGroupBuyProductDetailForCustom",
			Handler:    _GroupBuyService_GetGroupBuyProductDetailForCustom_Handler,
		},
		{
			MethodName: "DeleteGroupBuyProduct",
			Handler:    _GroupBuyService_DeleteGroupBuyProduct_Handler,
		},
		{
			MethodName: "GetGroupBuyUPetProductSelectList",
			Handler:    _GroupBuyService_GetGroupBuyUPetProductSelectList_Handler,
		},
		{
			MethodName: "GroupBuyOrderStaticCallBack",
			Handler:    _GroupBuyService_GroupBuyOrderStaticCallBack_Handler,
		},
		{
			MethodName: "GetGroupBuySkusListBySkuId",
			Handler:    _GroupBuyService_GetGroupBuySkusListBySkuId_Handler,
		},
		{
			MethodName: "GetGroupBuyStatus",
			Handler:    _GroupBuyService_GetGroupBuyStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/group_buy_service.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dac/pickup.proto

package dac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PickupResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupResponse) Reset()         { *m = PickupResponse{} }
func (m *PickupResponse) String() string { return proto.CompactTextString(m) }
func (*PickupResponse) ProtoMessage()    {}
func (*PickupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{0}
}

func (m *PickupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupResponse.Unmarshal(m, b)
}
func (m *PickupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupResponse.Marshal(b, m, deterministic)
}
func (m *PickupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupResponse.Merge(m, src)
}
func (m *PickupResponse) XXX_Size() int {
	return xxx_messageInfo_PickupResponse.Size(m)
}
func (m *PickupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PickupResponse proto.InternalMessageInfo

func (m *PickupResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PickupResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 自提活动列表
type PickupListReq struct {
	// 关联门店财务编码，多个用逗号分隔
	ShopCodes string `protobuf:"bytes,1,opt,name=shop_codes,json=shopCodes,proto3" json:"shop_codes"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex            int32    `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupListReq) Reset()         { *m = PickupListReq{} }
func (m *PickupListReq) String() string { return proto.CompactTextString(m) }
func (*PickupListReq) ProtoMessage()    {}
func (*PickupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{1}
}

func (m *PickupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupListReq.Unmarshal(m, b)
}
func (m *PickupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupListReq.Marshal(b, m, deterministic)
}
func (m *PickupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupListReq.Merge(m, src)
}
func (m *PickupListReq) XXX_Size() int {
	return xxx_messageInfo_PickupListReq.Size(m)
}
func (m *PickupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PickupListReq proto.InternalMessageInfo

func (m *PickupListReq) GetShopCodes() string {
	if m != nil {
		return m.ShopCodes
	}
	return ""
}

func (m *PickupListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PickupListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type PickupListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*PickupListResponse_Pickup `protobuf:"bytes,6,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupListResponse) Reset()         { *m = PickupListResponse{} }
func (m *PickupListResponse) String() string { return proto.CompactTextString(m) }
func (*PickupListResponse) ProtoMessage()    {}
func (*PickupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{2}
}

func (m *PickupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupListResponse.Unmarshal(m, b)
}
func (m *PickupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupListResponse.Marshal(b, m, deterministic)
}
func (m *PickupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupListResponse.Merge(m, src)
}
func (m *PickupListResponse) XXX_Size() int {
	return xxx_messageInfo_PickupListResponse.Size(m)
}
func (m *PickupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PickupListResponse proto.InternalMessageInfo

func (m *PickupListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PickupListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PickupListResponse) GetData() []*PickupListResponse_Pickup {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PickupListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PickupListResponse_Pickup struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动类型
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	// 成团金额
	StationDailyMin float32 `protobuf:"fixed32,3,opt,name=station_daily_min,json=stationDailyMin,proto3" json:"station_daily_min"`
	// 截止时间
	EndTime string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 送货天数
	DeliverDays int32 `protobuf:"varint,5,opt,name=deliver_days,json=deliverDays,proto3" json:"deliver_days"`
	// 门店名称
	ShopName string `protobuf:"bytes,6,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	// 财务编码
	ShopCodes string `protobuf:"bytes,7,opt,name=shop_codes,json=shopCodes,proto3" json:"shop_codes"`
	// 分享海报
	ShareImage           string   `protobuf:"bytes,8,opt,name=share_image,json=shareImage,proto3" json:"share_image"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupListResponse_Pickup) Reset()         { *m = PickupListResponse_Pickup{} }
func (m *PickupListResponse_Pickup) String() string { return proto.CompactTextString(m) }
func (*PickupListResponse_Pickup) ProtoMessage()    {}
func (*PickupListResponse_Pickup) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{2, 0}
}

func (m *PickupListResponse_Pickup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupListResponse_Pickup.Unmarshal(m, b)
}
func (m *PickupListResponse_Pickup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupListResponse_Pickup.Marshal(b, m, deterministic)
}
func (m *PickupListResponse_Pickup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupListResponse_Pickup.Merge(m, src)
}
func (m *PickupListResponse_Pickup) XXX_Size() int {
	return xxx_messageInfo_PickupListResponse_Pickup.Size(m)
}
func (m *PickupListResponse_Pickup) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupListResponse_Pickup.DiscardUnknown(m)
}

var xxx_messageInfo_PickupListResponse_Pickup proto.InternalMessageInfo

func (m *PickupListResponse_Pickup) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PickupListResponse_Pickup) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *PickupListResponse_Pickup) GetStationDailyMin() float32 {
	if m != nil {
		return m.StationDailyMin
	}
	return 0
}

func (m *PickupListResponse_Pickup) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PickupListResponse_Pickup) GetDeliverDays() int32 {
	if m != nil {
		return m.DeliverDays
	}
	return 0
}

func (m *PickupListResponse_Pickup) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *PickupListResponse_Pickup) GetShopCodes() string {
	if m != nil {
		return m.ShopCodes
	}
	return ""
}

func (m *PickupListResponse_Pickup) GetShareImage() string {
	if m != nil {
		return m.ShareImage
	}
	return ""
}

// 自提活动列表
type PickupStoreReq struct {
	// 关联门店财务编码，多个用逗号分隔
	ShopCodes string `protobuf:"bytes,1,opt,name=shop_codes,json=shopCodes,proto3" json:"shop_codes"`
	// 成团金额
	StationDailyMin float32 `protobuf:"fixed32,2,opt,name=station_daily_min,json=stationDailyMin,proto3" json:"station_daily_min"`
	// 截止时间
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 送货天数
	DeliverDays int32 `protobuf:"varint,4,opt,name=deliver_days,json=deliverDays,proto3" json:"deliver_days"`
	// 操作人no，前端不需要传
	UserNo string `protobuf:"bytes,6,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 操作人名称，前端不需要传
	UserName string `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 分享海报
	ShareImage           string   `protobuf:"bytes,8,opt,name=share_image,json=shareImage,proto3" json:"share_image"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupStoreReq) Reset()         { *m = PickupStoreReq{} }
func (m *PickupStoreReq) String() string { return proto.CompactTextString(m) }
func (*PickupStoreReq) ProtoMessage()    {}
func (*PickupStoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{3}
}

func (m *PickupStoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupStoreReq.Unmarshal(m, b)
}
func (m *PickupStoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupStoreReq.Marshal(b, m, deterministic)
}
func (m *PickupStoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupStoreReq.Merge(m, src)
}
func (m *PickupStoreReq) XXX_Size() int {
	return xxx_messageInfo_PickupStoreReq.Size(m)
}
func (m *PickupStoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupStoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_PickupStoreReq proto.InternalMessageInfo

func (m *PickupStoreReq) GetShopCodes() string {
	if m != nil {
		return m.ShopCodes
	}
	return ""
}

func (m *PickupStoreReq) GetStationDailyMin() float32 {
	if m != nil {
		return m.StationDailyMin
	}
	return 0
}

func (m *PickupStoreReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PickupStoreReq) GetDeliverDays() int32 {
	if m != nil {
		return m.DeliverDays
	}
	return 0
}

func (m *PickupStoreReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *PickupStoreReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *PickupStoreReq) GetShareImage() string {
	if m != nil {
		return m.ShareImage
	}
	return ""
}

type PickupDeleteReq struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 操作人no，前端不需要传
	UserNo string `protobuf:"bytes,6,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 操作人名称，前端不需要传
	UserName             string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupDeleteReq) Reset()         { *m = PickupDeleteReq{} }
func (m *PickupDeleteReq) String() string { return proto.CompactTextString(m) }
func (*PickupDeleteReq) ProtoMessage()    {}
func (*PickupDeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{4}
}

func (m *PickupDeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupDeleteReq.Unmarshal(m, b)
}
func (m *PickupDeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupDeleteReq.Marshal(b, m, deterministic)
}
func (m *PickupDeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupDeleteReq.Merge(m, src)
}
func (m *PickupDeleteReq) XXX_Size() int {
	return xxx_messageInfo_PickupDeleteReq.Size(m)
}
func (m *PickupDeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupDeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_PickupDeleteReq proto.InternalMessageInfo

func (m *PickupDeleteReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PickupDeleteReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *PickupDeleteReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type StationImportTemplateReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportTemplateReq) Reset()         { *m = StationImportTemplateReq{} }
func (m *StationImportTemplateReq) String() string { return proto.CompactTextString(m) }
func (*StationImportTemplateReq) ProtoMessage()    {}
func (*StationImportTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{5}
}

func (m *StationImportTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportTemplateReq.Unmarshal(m, b)
}
func (m *StationImportTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportTemplateReq.Marshal(b, m, deterministic)
}
func (m *StationImportTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportTemplateReq.Merge(m, src)
}
func (m *StationImportTemplateReq) XXX_Size() int {
	return xxx_messageInfo_StationImportTemplateReq.Size(m)
}
func (m *StationImportTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportTemplateReq proto.InternalMessageInfo

type StationImportTemplateResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	Template             []byte   `protobuf:"bytes,3,opt,name=template,proto3" json:"template"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportTemplateResponse) Reset()         { *m = StationImportTemplateResponse{} }
func (m *StationImportTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*StationImportTemplateResponse) ProtoMessage()    {}
func (*StationImportTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{6}
}

func (m *StationImportTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportTemplateResponse.Unmarshal(m, b)
}
func (m *StationImportTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportTemplateResponse.Marshal(b, m, deterministic)
}
func (m *StationImportTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportTemplateResponse.Merge(m, src)
}
func (m *StationImportTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_StationImportTemplateResponse.Size(m)
}
func (m *StationImportTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportTemplateResponse proto.InternalMessageInfo

func (m *StationImportTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StationImportTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StationImportTemplateResponse) GetTemplate() []byte {
	if m != nil {
		return m.Template
	}
	return nil
}

type StationImportReq struct {
	// 文件字节流
	File                 []byte   `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportReq) Reset()         { *m = StationImportReq{} }
func (m *StationImportReq) String() string { return proto.CompactTextString(m) }
func (*StationImportReq) ProtoMessage()    {}
func (*StationImportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{7}
}

func (m *StationImportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportReq.Unmarshal(m, b)
}
func (m *StationImportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportReq.Marshal(b, m, deterministic)
}
func (m *StationImportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportReq.Merge(m, src)
}
func (m *StationImportReq) XXX_Size() int {
	return xxx_messageInfo_StationImportReq.Size(m)
}
func (m *StationImportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportReq proto.InternalMessageInfo

func (m *StationImportReq) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

type StationImportResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportResponse) Reset()         { *m = StationImportResponse{} }
func (m *StationImportResponse) String() string { return proto.CompactTextString(m) }
func (*StationImportResponse) ProtoMessage()    {}
func (*StationImportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{8}
}

func (m *StationImportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportResponse.Unmarshal(m, b)
}
func (m *StationImportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportResponse.Marshal(b, m, deterministic)
}
func (m *StationImportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportResponse.Merge(m, src)
}
func (m *StationImportResponse) XXX_Size() int {
	return xxx_messageInfo_StationImportResponse.Size(m)
}
func (m *StationImportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportResponse proto.InternalMessageInfo

func (m *StationImportResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StationImportResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type StationImportHistoryListReq struct {
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportHistoryListReq) Reset()         { *m = StationImportHistoryListReq{} }
func (m *StationImportHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*StationImportHistoryListReq) ProtoMessage()    {}
func (*StationImportHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{9}
}

func (m *StationImportHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportHistoryListReq.Unmarshal(m, b)
}
func (m *StationImportHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportHistoryListReq.Marshal(b, m, deterministic)
}
func (m *StationImportHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportHistoryListReq.Merge(m, src)
}
func (m *StationImportHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_StationImportHistoryListReq.Size(m)
}
func (m *StationImportHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportHistoryListReq proto.InternalMessageInfo

func (m *StationImportHistoryListReq) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *StationImportHistoryListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type StationImportHistoryListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*StationImportHistoryListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportHistoryListResponse) Reset()         { *m = StationImportHistoryListResponse{} }
func (m *StationImportHistoryListResponse) String() string { return proto.CompactTextString(m) }
func (*StationImportHistoryListResponse) ProtoMessage()    {}
func (*StationImportHistoryListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{10}
}

func (m *StationImportHistoryListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportHistoryListResponse.Unmarshal(m, b)
}
func (m *StationImportHistoryListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportHistoryListResponse.Marshal(b, m, deterministic)
}
func (m *StationImportHistoryListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportHistoryListResponse.Merge(m, src)
}
func (m *StationImportHistoryListResponse) XXX_Size() int {
	return xxx_messageInfo_StationImportHistoryListResponse.Size(m)
}
func (m *StationImportHistoryListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportHistoryListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportHistoryListResponse proto.InternalMessageInfo

func (m *StationImportHistoryListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StationImportHistoryListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StationImportHistoryListResponse) GetData() []*StationImportHistoryListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *StationImportHistoryListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type StationImportHistoryListResponse_List struct {
	// 导入id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 导入时间
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 导入结果
	Result string `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	// 导入结果url
	ResultUrl            string   `protobuf:"bytes,4,opt,name=result_url,json=resultUrl,proto3" json:"result_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationImportHistoryListResponse_List) Reset()         { *m = StationImportHistoryListResponse_List{} }
func (m *StationImportHistoryListResponse_List) String() string { return proto.CompactTextString(m) }
func (*StationImportHistoryListResponse_List) ProtoMessage()    {}
func (*StationImportHistoryListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{10, 0}
}

func (m *StationImportHistoryListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationImportHistoryListResponse_List.Unmarshal(m, b)
}
func (m *StationImportHistoryListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationImportHistoryListResponse_List.Marshal(b, m, deterministic)
}
func (m *StationImportHistoryListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationImportHistoryListResponse_List.Merge(m, src)
}
func (m *StationImportHistoryListResponse_List) XXX_Size() int {
	return xxx_messageInfo_StationImportHistoryListResponse_List.Size(m)
}
func (m *StationImportHistoryListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_StationImportHistoryListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_StationImportHistoryListResponse_List proto.InternalMessageInfo

func (m *StationImportHistoryListResponse_List) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StationImportHistoryListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *StationImportHistoryListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *StationImportHistoryListResponse_List) GetResultUrl() string {
	if m != nil {
		return m.ResultUrl
	}
	return ""
}

type StationsReq struct {
	//名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//状态 "0"禁用、"1"启用 其他全部
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationsReq) Reset()         { *m = StationsReq{} }
func (m *StationsReq) String() string { return proto.CompactTextString(m) }
func (*StationsReq) ProtoMessage()    {}
func (*StationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{11}
}

func (m *StationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationsReq.Unmarshal(m, b)
}
func (m *StationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationsReq.Marshal(b, m, deterministic)
}
func (m *StationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationsReq.Merge(m, src)
}
func (m *StationsReq) XXX_Size() int {
	return xxx_messageInfo_StationsReq.Size(m)
}
func (m *StationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationsReq proto.InternalMessageInfo

func (m *StationsReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StationsReq) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *StationsReq) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *StationsReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type StationsResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*StationsResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationsResponse) Reset()         { *m = StationsResponse{} }
func (m *StationsResponse) String() string { return proto.CompactTextString(m) }
func (*StationsResponse) ProtoMessage()    {}
func (*StationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{12}
}

func (m *StationsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationsResponse.Unmarshal(m, b)
}
func (m *StationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationsResponse.Marshal(b, m, deterministic)
}
func (m *StationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationsResponse.Merge(m, src)
}
func (m *StationsResponse) XXX_Size() int {
	return xxx_messageInfo_StationsResponse.Size(m)
}
func (m *StationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StationsResponse proto.InternalMessageInfo

func (m *StationsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StationsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StationsResponse) GetData() []*StationsResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *StationsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type StationsResponse_List struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//详细地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	//状态 0禁用、1启用
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationsResponse_List) Reset()         { *m = StationsResponse_List{} }
func (m *StationsResponse_List) String() string { return proto.CompactTextString(m) }
func (*StationsResponse_List) ProtoMessage()    {}
func (*StationsResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{12, 0}
}

func (m *StationsResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationsResponse_List.Unmarshal(m, b)
}
func (m *StationsResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationsResponse_List.Marshal(b, m, deterministic)
}
func (m *StationsResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationsResponse_List.Merge(m, src)
}
func (m *StationsResponse_List) XXX_Size() int {
	return xxx_messageInfo_StationsResponse_List.Size(m)
}
func (m *StationsResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_StationsResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_StationsResponse_List proto.InternalMessageInfo

func (m *StationsResponse_List) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StationsResponse_List) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StationsResponse_List) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *StationsResponse_List) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type StationStoreReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//详细地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	//维度
	Lng string `protobuf:"bytes,4,opt,name=lng,proto3" json:"lng"`
	//纬度
	Lat                  string   `protobuf:"bytes,5,opt,name=lat,proto3" json:"lat"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationStoreReq) Reset()         { *m = StationStoreReq{} }
func (m *StationStoreReq) String() string { return proto.CompactTextString(m) }
func (*StationStoreReq) ProtoMessage()    {}
func (*StationStoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{13}
}

func (m *StationStoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationStoreReq.Unmarshal(m, b)
}
func (m *StationStoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationStoreReq.Marshal(b, m, deterministic)
}
func (m *StationStoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationStoreReq.Merge(m, src)
}
func (m *StationStoreReq) XXX_Size() int {
	return xxx_messageInfo_StationStoreReq.Size(m)
}
func (m *StationStoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationStoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationStoreReq proto.InternalMessageInfo

func (m *StationStoreReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StationStoreReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StationStoreReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *StationStoreReq) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *StationStoreReq) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

type StationPatchReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 状态 0禁用、1启用，不更新不要传
	Status               *wrappers.Int32Value `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StationPatchReq) Reset()         { *m = StationPatchReq{} }
func (m *StationPatchReq) String() string { return proto.CompactTextString(m) }
func (*StationPatchReq) ProtoMessage()    {}
func (*StationPatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{14}
}

func (m *StationPatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationPatchReq.Unmarshal(m, b)
}
func (m *StationPatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationPatchReq.Marshal(b, m, deterministic)
}
func (m *StationPatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationPatchReq.Merge(m, src)
}
func (m *StationPatchReq) XXX_Size() int {
	return xxx_messageInfo_StationPatchReq.Size(m)
}
func (m *StationPatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationPatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationPatchReq proto.InternalMessageInfo

func (m *StationPatchReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StationPatchReq) GetStatus() *wrappers.Int32Value {
	if m != nil {
		return m.Status
	}
	return nil
}

type StationDetailReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationDetailReq) Reset()         { *m = StationDetailReq{} }
func (m *StationDetailReq) String() string { return proto.CompactTextString(m) }
func (*StationDetailReq) ProtoMessage()    {}
func (*StationDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{15}
}

func (m *StationDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationDetailReq.Unmarshal(m, b)
}
func (m *StationDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationDetailReq.Marshal(b, m, deterministic)
}
func (m *StationDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationDetailReq.Merge(m, src)
}
func (m *StationDetailReq) XXX_Size() int {
	return xxx_messageInfo_StationDetailReq.Size(m)
}
func (m *StationDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StationDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_StationDetailReq proto.InternalMessageInfo

func (m *StationDetailReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type StationDetailResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *StationDetailResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *StationDetailResponse) Reset()         { *m = StationDetailResponse{} }
func (m *StationDetailResponse) String() string { return proto.CompactTextString(m) }
func (*StationDetailResponse) ProtoMessage()    {}
func (*StationDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{16}
}

func (m *StationDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationDetailResponse.Unmarshal(m, b)
}
func (m *StationDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationDetailResponse.Marshal(b, m, deterministic)
}
func (m *StationDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationDetailResponse.Merge(m, src)
}
func (m *StationDetailResponse) XXX_Size() int {
	return xxx_messageInfo_StationDetailResponse.Size(m)
}
func (m *StationDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StationDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StationDetailResponse proto.InternalMessageInfo

func (m *StationDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StationDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StationDetailResponse) GetData() *StationDetailResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type StationDetailResponse_Data struct {
	//id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//详细地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	Lng     string `protobuf:"bytes,4,opt,name=lng,proto3" json:"lng"`
	Lat     string `protobuf:"bytes,5,opt,name=lat,proto3" json:"lat"`
	//状态 0禁用、1启用
	Status               int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationDetailResponse_Data) Reset()         { *m = StationDetailResponse_Data{} }
func (m *StationDetailResponse_Data) String() string { return proto.CompactTextString(m) }
func (*StationDetailResponse_Data) ProtoMessage()    {}
func (*StationDetailResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{16, 0}
}

func (m *StationDetailResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationDetailResponse_Data.Unmarshal(m, b)
}
func (m *StationDetailResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationDetailResponse_Data.Marshal(b, m, deterministic)
}
func (m *StationDetailResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationDetailResponse_Data.Merge(m, src)
}
func (m *StationDetailResponse_Data) XXX_Size() int {
	return xxx_messageInfo_StationDetailResponse_Data.Size(m)
}
func (m *StationDetailResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_StationDetailResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_StationDetailResponse_Data proto.InternalMessageInfo

func (m *StationDetailResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StationDetailResponse_Data) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StationDetailResponse_Data) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *StationDetailResponse_Data) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *StationDetailResponse_Data) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *StationDetailResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type AWStationsReq struct {
	// 地址经度
	Lng string `protobuf:"bytes,1,opt,name=lng,proto3" json:"lng"`
	// 地址纬度
	Lat string `protobuf:"bytes,2,opt,name=lat,proto3" json:"lat"`
	// 门店财务编码
	FinanceCode string `protobuf:"bytes,3,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 搜索关键字
	Keyword string `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword"`
	// 每页数量
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex            int32    `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationsReq) Reset()         { *m = AWStationsReq{} }
func (m *AWStationsReq) String() string { return proto.CompactTextString(m) }
func (*AWStationsReq) ProtoMessage()    {}
func (*AWStationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{17}
}

func (m *AWStationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationsReq.Unmarshal(m, b)
}
func (m *AWStationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationsReq.Marshal(b, m, deterministic)
}
func (m *AWStationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationsReq.Merge(m, src)
}
func (m *AWStationsReq) XXX_Size() int {
	return xxx_messageInfo_AWStationsReq.Size(m)
}
func (m *AWStationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationsReq proto.InternalMessageInfo

func (m *AWStationsReq) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *AWStationsReq) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *AWStationsReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *AWStationsReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *AWStationsReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *AWStationsReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type AWStationsResponse struct {
	Code    int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*AWStationsResponse_Station `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationsResponse) Reset()         { *m = AWStationsResponse{} }
func (m *AWStationsResponse) String() string { return proto.CompactTextString(m) }
func (*AWStationsResponse) ProtoMessage()    {}
func (*AWStationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{18}
}

func (m *AWStationsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationsResponse.Unmarshal(m, b)
}
func (m *AWStationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationsResponse.Marshal(b, m, deterministic)
}
func (m *AWStationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationsResponse.Merge(m, src)
}
func (m *AWStationsResponse) XXX_Size() int {
	return xxx_messageInfo_AWStationsResponse.Size(m)
}
func (m *AWStationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationsResponse proto.InternalMessageInfo

func (m *AWStationsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWStationsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWStationsResponse) GetData() []*AWStationsResponse_Station {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AWStationsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AWStationsResponse_Station struct {
	// 站点id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 站点名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 站点地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	// 距离，自带单位，如km
	Distance             string   `protobuf:"bytes,4,opt,name=distance,proto3" json:"distance"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationsResponse_Station) Reset()         { *m = AWStationsResponse_Station{} }
func (m *AWStationsResponse_Station) String() string { return proto.CompactTextString(m) }
func (*AWStationsResponse_Station) ProtoMessage()    {}
func (*AWStationsResponse_Station) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{18, 0}
}

func (m *AWStationsResponse_Station) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationsResponse_Station.Unmarshal(m, b)
}
func (m *AWStationsResponse_Station) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationsResponse_Station.Marshal(b, m, deterministic)
}
func (m *AWStationsResponse_Station) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationsResponse_Station.Merge(m, src)
}
func (m *AWStationsResponse_Station) XXX_Size() int {
	return xxx_messageInfo_AWStationsResponse_Station.Size(m)
}
func (m *AWStationsResponse_Station) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationsResponse_Station.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationsResponse_Station proto.InternalMessageInfo

func (m *AWStationsResponse_Station) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AWStationsResponse_Station) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AWStationsResponse_Station) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *AWStationsResponse_Station) GetDistance() string {
	if m != nil {
		return m.Distance
	}
	return ""
}

type AWStationStateReq struct {
	// 门店财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 站点id
	StationId            int32    `protobuf:"varint,2,opt,name=station_id,json=stationId,proto3" json:"station_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationStateReq) Reset()         { *m = AWStationStateReq{} }
func (m *AWStationStateReq) String() string { return proto.CompactTextString(m) }
func (*AWStationStateReq) ProtoMessage()    {}
func (*AWStationStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{19}
}

func (m *AWStationStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationStateReq.Unmarshal(m, b)
}
func (m *AWStationStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationStateReq.Marshal(b, m, deterministic)
}
func (m *AWStationStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationStateReq.Merge(m, src)
}
func (m *AWStationStateReq) XXX_Size() int {
	return xxx_messageInfo_AWStationStateReq.Size(m)
}
func (m *AWStationStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationStateReq proto.InternalMessageInfo

func (m *AWStationStateReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *AWStationStateReq) GetStationId() int32 {
	if m != nil {
		return m.StationId
	}
	return 0
}

type AWStationStateResponse struct {
	Code                 int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *AWStationStateResponse_State `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *AWStationStateResponse) Reset()         { *m = AWStationStateResponse{} }
func (m *AWStationStateResponse) String() string { return proto.CompactTextString(m) }
func (*AWStationStateResponse) ProtoMessage()    {}
func (*AWStationStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{20}
}

func (m *AWStationStateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationStateResponse.Unmarshal(m, b)
}
func (m *AWStationStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationStateResponse.Marshal(b, m, deterministic)
}
func (m *AWStationStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationStateResponse.Merge(m, src)
}
func (m *AWStationStateResponse) XXX_Size() int {
	return xxx_messageInfo_AWStationStateResponse.Size(m)
}
func (m *AWStationStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationStateResponse proto.InternalMessageInfo

func (m *AWStationStateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWStationStateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWStationStateResponse) GetData() *AWStationStateResponse_State {
	if m != nil {
		return m.Data
	}
	return nil
}

type AWStationStateResponse_State struct {
	// 站点id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 站点名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 预计送达时间
	ExpectedTime string `protobuf:"bytes,3,opt,name=expected_time,json=expectedTime,proto3" json:"expected_time"`
	// 预计送达时间描述，如成团当日
	ExpectedDesc string `protobuf:"bytes,4,opt,name=expected_desc,json=expectedDesc,proto3" json:"expected_desc"`
	// 进度提醒 已支付x单，16:00前还需成交y单才可成团
	ProgressNotice       string   `protobuf:"bytes,5,opt,name=progress_notice,json=progressNotice,proto3" json:"progress_notice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationStateResponse_State) Reset()         { *m = AWStationStateResponse_State{} }
func (m *AWStationStateResponse_State) String() string { return proto.CompactTextString(m) }
func (*AWStationStateResponse_State) ProtoMessage()    {}
func (*AWStationStateResponse_State) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{20, 0}
}

func (m *AWStationStateResponse_State) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationStateResponse_State.Unmarshal(m, b)
}
func (m *AWStationStateResponse_State) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationStateResponse_State.Marshal(b, m, deterministic)
}
func (m *AWStationStateResponse_State) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationStateResponse_State.Merge(m, src)
}
func (m *AWStationStateResponse_State) XXX_Size() int {
	return xxx_messageInfo_AWStationStateResponse_State.Size(m)
}
func (m *AWStationStateResponse_State) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationStateResponse_State.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationStateResponse_State proto.InternalMessageInfo

func (m *AWStationStateResponse_State) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AWStationStateResponse_State) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AWStationStateResponse_State) GetExpectedTime() string {
	if m != nil {
		return m.ExpectedTime
	}
	return ""
}

func (m *AWStationStateResponse_State) GetExpectedDesc() string {
	if m != nil {
		return m.ExpectedDesc
	}
	return ""
}

func (m *AWStationStateResponse_State) GetProgressNotice() string {
	if m != nil {
		return m.ProgressNotice
	}
	return ""
}

type AWStationNearestReq struct {
	// 地址经度
	Lng string `protobuf:"bytes,1,opt,name=lng,proto3" json:"lng"`
	// 地址纬度
	Lat string `protobuf:"bytes,2,opt,name=lat,proto3" json:"lat"`
	// 门店财务编码
	FinanceCode          string   `protobuf:"bytes,3,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationNearestReq) Reset()         { *m = AWStationNearestReq{} }
func (m *AWStationNearestReq) String() string { return proto.CompactTextString(m) }
func (*AWStationNearestReq) ProtoMessage()    {}
func (*AWStationNearestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{21}
}

func (m *AWStationNearestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationNearestReq.Unmarshal(m, b)
}
func (m *AWStationNearestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationNearestReq.Marshal(b, m, deterministic)
}
func (m *AWStationNearestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationNearestReq.Merge(m, src)
}
func (m *AWStationNearestReq) XXX_Size() int {
	return xxx_messageInfo_AWStationNearestReq.Size(m)
}
func (m *AWStationNearestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationNearestReq.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationNearestReq proto.InternalMessageInfo

func (m *AWStationNearestReq) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *AWStationNearestReq) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *AWStationNearestReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

type AWStationNearestResponse struct {
	Code                 int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *AWStationNearestResponse_Nearest `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *AWStationNearestResponse) Reset()         { *m = AWStationNearestResponse{} }
func (m *AWStationNearestResponse) String() string { return proto.CompactTextString(m) }
func (*AWStationNearestResponse) ProtoMessage()    {}
func (*AWStationNearestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{22}
}

func (m *AWStationNearestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationNearestResponse.Unmarshal(m, b)
}
func (m *AWStationNearestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationNearestResponse.Marshal(b, m, deterministic)
}
func (m *AWStationNearestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationNearestResponse.Merge(m, src)
}
func (m *AWStationNearestResponse) XXX_Size() int {
	return xxx_messageInfo_AWStationNearestResponse.Size(m)
}
func (m *AWStationNearestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationNearestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationNearestResponse proto.InternalMessageInfo

func (m *AWStationNearestResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWStationNearestResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWStationNearestResponse) GetData() *AWStationNearestResponse_Nearest {
	if m != nil {
		return m.Data
	}
	return nil
}

type AWStationNearestResponse_Nearest struct {
	// 站点id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 站点名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 已支付订单计数
	PaidCount int32 `protobuf:"varint,3,opt,name=paid_count,json=paidCount,proto3" json:"paid_count"`
	// 还需数量
	Remain int32 `protobuf:"varint,4,opt,name=remain,proto3" json:"remain"`
	// 预计送达时间
	ExpectedTime string `protobuf:"bytes,5,opt,name=expected_time,json=expectedTime,proto3" json:"expected_time"`
	// 预计送达时间描述，如成团当日
	ExpectedDesc string `protobuf:"bytes,6,opt,name=expected_desc,json=expectedDesc,proto3" json:"expected_desc"`
	// 进度提醒 已支付x单，16:00前还需成交y单才可成团
	ProgressNotice string `protobuf:"bytes,7,opt,name=progress_notice,json=progressNotice,proto3" json:"progress_notice"`
	// 距离，自带单位，如km
	Distance             string   `protobuf:"bytes,8,opt,name=distance,proto3" json:"distance"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWStationNearestResponse_Nearest) Reset()         { *m = AWStationNearestResponse_Nearest{} }
func (m *AWStationNearestResponse_Nearest) String() string { return proto.CompactTextString(m) }
func (*AWStationNearestResponse_Nearest) ProtoMessage()    {}
func (*AWStationNearestResponse_Nearest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{22, 0}
}

func (m *AWStationNearestResponse_Nearest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWStationNearestResponse_Nearest.Unmarshal(m, b)
}
func (m *AWStationNearestResponse_Nearest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWStationNearestResponse_Nearest.Marshal(b, m, deterministic)
}
func (m *AWStationNearestResponse_Nearest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWStationNearestResponse_Nearest.Merge(m, src)
}
func (m *AWStationNearestResponse_Nearest) XXX_Size() int {
	return xxx_messageInfo_AWStationNearestResponse_Nearest.Size(m)
}
func (m *AWStationNearestResponse_Nearest) XXX_DiscardUnknown() {
	xxx_messageInfo_AWStationNearestResponse_Nearest.DiscardUnknown(m)
}

var xxx_messageInfo_AWStationNearestResponse_Nearest proto.InternalMessageInfo

func (m *AWStationNearestResponse_Nearest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AWStationNearestResponse_Nearest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AWStationNearestResponse_Nearest) GetPaidCount() int32 {
	if m != nil {
		return m.PaidCount
	}
	return 0
}

func (m *AWStationNearestResponse_Nearest) GetRemain() int32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *AWStationNearestResponse_Nearest) GetExpectedTime() string {
	if m != nil {
		return m.ExpectedTime
	}
	return ""
}

func (m *AWStationNearestResponse_Nearest) GetExpectedDesc() string {
	if m != nil {
		return m.ExpectedDesc
	}
	return ""
}

func (m *AWStationNearestResponse_Nearest) GetProgressNotice() string {
	if m != nil {
		return m.ProgressNotice
	}
	return ""
}

func (m *AWStationNearestResponse_Nearest) GetDistance() string {
	if m != nil {
		return m.Distance
	}
	return ""
}

//社区团购
type GroupActivityListReq struct {
	//活动状态，0未开始 1进行中 2已结束 3已终止 -1全部
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	//活动时间开始日期
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//活动时间结束日期
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//门店财务编码，多个财务编码用逗号分隔
	FinanceCode string `protobuf:"bytes,4,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//创建人
	CreatedBy string `protobuf:"bytes,5,opt,name=created_by,json=createdBy,proto3" json:"created_by"`
	//每页数量
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//当前页码
	PageIndex            int32    `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityListReq) Reset()         { *m = GroupActivityListReq{} }
func (m *GroupActivityListReq) String() string { return proto.CompactTextString(m) }
func (*GroupActivityListReq) ProtoMessage()    {}
func (*GroupActivityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{23}
}

func (m *GroupActivityListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityListReq.Unmarshal(m, b)
}
func (m *GroupActivityListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityListReq.Marshal(b, m, deterministic)
}
func (m *GroupActivityListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityListReq.Merge(m, src)
}
func (m *GroupActivityListReq) XXX_Size() int {
	return xxx_messageInfo_GroupActivityListReq.Size(m)
}
func (m *GroupActivityListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityListReq proto.InternalMessageInfo

func (m *GroupActivityListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivityListReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GroupActivityListReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GroupActivityListReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupActivityListReq) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

func (m *GroupActivityListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GroupActivityListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type GroupActivityListResponse struct {
	Code    int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*GroupActivityListResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	//总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityListResponse) Reset()         { *m = GroupActivityListResponse{} }
func (m *GroupActivityListResponse) String() string { return proto.CompactTextString(m) }
func (*GroupActivityListResponse) ProtoMessage()    {}
func (*GroupActivityListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{24}
}

func (m *GroupActivityListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityListResponse.Unmarshal(m, b)
}
func (m *GroupActivityListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityListResponse.Marshal(b, m, deterministic)
}
func (m *GroupActivityListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityListResponse.Merge(m, src)
}
func (m *GroupActivityListResponse) XXX_Size() int {
	return xxx_messageInfo_GroupActivityListResponse.Size(m)
}
func (m *GroupActivityListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityListResponse proto.InternalMessageInfo

func (m *GroupActivityListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupActivityListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupActivityListResponse) GetData() []*GroupActivityListResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GroupActivityListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GroupActivityListResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 门店财务编码
	StoreFinanceCode string `protobuf:"bytes,13,opt,name=store_finance_code,json=storeFinanceCode,proto3" json:"store_finance_code"`
	// 门店名称
	StoreName string `protobuf:"bytes,14,opt,name=store_name,json=storeName,proto3" json:"store_name"`
	//活动开始时间
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//活动结束时间
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//代收方式 0团长决定是否代收 1团长必须代收
	DeliveryMode int32 `protobuf:"varint,4,opt,name=delivery_mode,json=deliveryMode,proto3" json:"delivery_mode"`
	//是否包邮 1是 0否
	IsFree int32 `protobuf:"varint,5,opt,name=is_free,json=isFree,proto3" json:"is_free"`
	//拼团区域是否显示 1是 0否
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//成团金额，单位分
	GroupMoney int32 `protobuf:"varint,7,opt,name=group_money,json=groupMoney,proto3" json:"group_money"`
	//拼团时间（小时）
	GroupDay int32 `protobuf:"varint,8,opt,name=group_day,json=groupDay,proto3" json:"group_day"`
	//送达时间（天）
	DeliveryDay int32 `protobuf:"varint,9,opt,name=delivery_day,json=deliveryDay,proto3" json:"delivery_day"`
	//活动状态，0未开始 1进行中 2已结束 3已终止
	Status int32 `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	//创建时间
	CreatedAt string `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	//创建人
	CreatedBy            string   `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityListResponse_Data) Reset()         { *m = GroupActivityListResponse_Data{} }
func (m *GroupActivityListResponse_Data) String() string { return proto.CompactTextString(m) }
func (*GroupActivityListResponse_Data) ProtoMessage()    {}
func (*GroupActivityListResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{24, 0}
}

func (m *GroupActivityListResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityListResponse_Data.Unmarshal(m, b)
}
func (m *GroupActivityListResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityListResponse_Data.Marshal(b, m, deterministic)
}
func (m *GroupActivityListResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityListResponse_Data.Merge(m, src)
}
func (m *GroupActivityListResponse_Data) XXX_Size() int {
	return xxx_messageInfo_GroupActivityListResponse_Data.Size(m)
}
func (m *GroupActivityListResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityListResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityListResponse_Data proto.InternalMessageInfo

func (m *GroupActivityListResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetStoreFinanceCode() string {
	if m != nil {
		return m.StoreFinanceCode
	}
	return ""
}

func (m *GroupActivityListResponse_Data) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *GroupActivityListResponse_Data) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GroupActivityListResponse_Data) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GroupActivityListResponse_Data) GetDeliveryMode() int32 {
	if m != nil {
		return m.DeliveryMode
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetGroupMoney() int32 {
	if m != nil {
		return m.GroupMoney
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetGroupDay() int32 {
	if m != nil {
		return m.GroupDay
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetDeliveryDay() int32 {
	if m != nil {
		return m.DeliveryDay
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivityListResponse_Data) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *GroupActivityListResponse_Data) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

type GroupActivityQueryReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityQueryReq) Reset()         { *m = GroupActivityQueryReq{} }
func (m *GroupActivityQueryReq) String() string { return proto.CompactTextString(m) }
func (*GroupActivityQueryReq) ProtoMessage()    {}
func (*GroupActivityQueryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{25}
}

func (m *GroupActivityQueryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityQueryReq.Unmarshal(m, b)
}
func (m *GroupActivityQueryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityQueryReq.Marshal(b, m, deterministic)
}
func (m *GroupActivityQueryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityQueryReq.Merge(m, src)
}
func (m *GroupActivityQueryReq) XXX_Size() int {
	return xxx_messageInfo_GroupActivityQueryReq.Size(m)
}
func (m *GroupActivityQueryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityQueryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityQueryReq proto.InternalMessageInfo

func (m *GroupActivityQueryReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GroupActivityQueryResponse struct {
	Code                 int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *GroupActivityQueryResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GroupActivityQueryResponse) Reset()         { *m = GroupActivityQueryResponse{} }
func (m *GroupActivityQueryResponse) String() string { return proto.CompactTextString(m) }
func (*GroupActivityQueryResponse) ProtoMessage()    {}
func (*GroupActivityQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{26}
}

func (m *GroupActivityQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityQueryResponse.Unmarshal(m, b)
}
func (m *GroupActivityQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityQueryResponse.Marshal(b, m, deterministic)
}
func (m *GroupActivityQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityQueryResponse.Merge(m, src)
}
func (m *GroupActivityQueryResponse) XXX_Size() int {
	return xxx_messageInfo_GroupActivityQueryResponse.Size(m)
}
func (m *GroupActivityQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityQueryResponse proto.InternalMessageInfo

func (m *GroupActivityQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupActivityQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupActivityQueryResponse) GetData() *GroupActivityQueryResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupActivityQueryResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//活动开始时间
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//活动结束时间
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//代收方式 0团长决定是否代收 1团长必须代收
	DeliveryMode int32 `protobuf:"varint,4,opt,name=delivery_mode,json=deliveryMode,proto3" json:"delivery_mode"`
	//是否包邮 1是 0否
	IsFree int32 `protobuf:"varint,5,opt,name=is_free,json=isFree,proto3" json:"is_free"`
	//拼团区域是否显示 1是 0否
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//成团金额，单位分
	GroupMoney int32 `protobuf:"varint,7,opt,name=group_money,json=groupMoney,proto3" json:"group_money"`
	//拼团时间（小时）
	GroupDay int32 `protobuf:"varint,8,opt,name=group_day,json=groupDay,proto3" json:"group_day"`
	//送达时间（天）
	DeliveryDay int32 `protobuf:"varint,9,opt,name=delivery_day,json=deliveryDay,proto3" json:"delivery_day"`
	//活动状态，0未开始 1进行中 2已结束 3已终止
	Status int32 `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	//门店财务编码
	FinanceCode string `protobuf:"bytes,11,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 店铺推广海报地址
	PosterUrl            string   `protobuf:"bytes,12,opt,name=poster_url,json=posterUrl,proto3" json:"poster_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityQueryResponse_Data) Reset()         { *m = GroupActivityQueryResponse_Data{} }
func (m *GroupActivityQueryResponse_Data) String() string { return proto.CompactTextString(m) }
func (*GroupActivityQueryResponse_Data) ProtoMessage()    {}
func (*GroupActivityQueryResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{26, 0}
}

func (m *GroupActivityQueryResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityQueryResponse_Data.Unmarshal(m, b)
}
func (m *GroupActivityQueryResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityQueryResponse_Data.Marshal(b, m, deterministic)
}
func (m *GroupActivityQueryResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityQueryResponse_Data.Merge(m, src)
}
func (m *GroupActivityQueryResponse_Data) XXX_Size() int {
	return xxx_messageInfo_GroupActivityQueryResponse_Data.Size(m)
}
func (m *GroupActivityQueryResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityQueryResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityQueryResponse_Data proto.InternalMessageInfo

func (m *GroupActivityQueryResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GroupActivityQueryResponse_Data) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GroupActivityQueryResponse_Data) GetDeliveryMode() int32 {
	if m != nil {
		return m.DeliveryMode
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetGroupMoney() int32 {
	if m != nil {
		return m.GroupMoney
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetGroupDay() int32 {
	if m != nil {
		return m.GroupDay
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetDeliveryDay() int32 {
	if m != nil {
		return m.DeliveryDay
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivityQueryResponse_Data) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupActivityQueryResponse_Data) GetPosterUrl() string {
	if m != nil {
		return m.PosterUrl
	}
	return ""
}

type GroupActivityEditReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//活动开始时间
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//活动结束时间
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//代收方式 0团长决定是否代收 1团长必须代收
	DeliveryMode int32 `protobuf:"varint,4,opt,name=delivery_mode,json=deliveryMode,proto3" json:"delivery_mode"`
	//是否包邮 1是 0否
	IsFree int32 `protobuf:"varint,5,opt,name=is_free,json=isFree,proto3" json:"is_free"`
	//拼团区域是否显示 1是 0否
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//成团金额，单位分
	GroupMoney int32 `protobuf:"varint,7,opt,name=group_money,json=groupMoney,proto3" json:"group_money"`
	//拼团时间（小时）
	GroupDay int32 `protobuf:"varint,8,opt,name=group_day,json=groupDay,proto3" json:"group_day"`
	//送达时间（天）
	DeliveryDay int32 `protobuf:"varint,9,opt,name=delivery_day,json=deliveryDay,proto3" json:"delivery_day"`
	//操作人
	CreatedBy string `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by"`
	//门店财务编码
	FinanceCode string `protobuf:"bytes,11,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 店铺推广海报地址
	PosterUrl            string   `protobuf:"bytes,12,opt,name=poster_url,json=posterUrl,proto3" json:"poster_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityEditReq) Reset()         { *m = GroupActivityEditReq{} }
func (m *GroupActivityEditReq) String() string { return proto.CompactTextString(m) }
func (*GroupActivityEditReq) ProtoMessage()    {}
func (*GroupActivityEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{27}
}

func (m *GroupActivityEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityEditReq.Unmarshal(m, b)
}
func (m *GroupActivityEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityEditReq.Marshal(b, m, deterministic)
}
func (m *GroupActivityEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityEditReq.Merge(m, src)
}
func (m *GroupActivityEditReq) XXX_Size() int {
	return xxx_messageInfo_GroupActivityEditReq.Size(m)
}
func (m *GroupActivityEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityEditReq proto.InternalMessageInfo

func (m *GroupActivityEditReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivityEditReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GroupActivityEditReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GroupActivityEditReq) GetDeliveryMode() int32 {
	if m != nil {
		return m.DeliveryMode
	}
	return 0
}

func (m *GroupActivityEditReq) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

func (m *GroupActivityEditReq) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *GroupActivityEditReq) GetGroupMoney() int32 {
	if m != nil {
		return m.GroupMoney
	}
	return 0
}

func (m *GroupActivityEditReq) GetGroupDay() int32 {
	if m != nil {
		return m.GroupDay
	}
	return 0
}

func (m *GroupActivityEditReq) GetDeliveryDay() int32 {
	if m != nil {
		return m.DeliveryDay
	}
	return 0
}

func (m *GroupActivityEditReq) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

func (m *GroupActivityEditReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GroupActivityEditReq) GetPosterUrl() string {
	if m != nil {
		return m.PosterUrl
	}
	return ""
}

type GroupActivityStatusReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//活动状态，0未开始 1进行中 2已结束 3已终止
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	//操作人
	CreatedBy            string   `protobuf:"bytes,3,opt,name=created_by,json=createdBy,proto3" json:"created_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivityStatusReq) Reset()         { *m = GroupActivityStatusReq{} }
func (m *GroupActivityStatusReq) String() string { return proto.CompactTextString(m) }
func (*GroupActivityStatusReq) ProtoMessage()    {}
func (*GroupActivityStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{28}
}

func (m *GroupActivityStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivityStatusReq.Unmarshal(m, b)
}
func (m *GroupActivityStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivityStatusReq.Marshal(b, m, deterministic)
}
func (m *GroupActivityStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivityStatusReq.Merge(m, src)
}
func (m *GroupActivityStatusReq) XXX_Size() int {
	return xxx_messageInfo_GroupActivityStatusReq.Size(m)
}
func (m *GroupActivityStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivityStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivityStatusReq proto.InternalMessageInfo

func (m *GroupActivityStatusReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivityStatusReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivityStatusReq) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

type CommissionCashoutListReq struct {
	//状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	//分销员身份 1内部 2外部
	Identity int32 `protobuf:"varint,2,opt,name=identity,proto3" json:"identity"`
	//提现人信息 1收款人姓名 2收款账号 3提现编码 4提现人手机
	SearchType int32 `protobuf:"varint,3,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	//提现人信息搜索关键字
	SearchKeyword string `protobuf:"bytes,4,opt,name=search_keyword,json=searchKeyword,proto3" json:"search_keyword"`
	//申请开始时间
	StartTime string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//申请结束时间
	EndTime string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//每页数量
	PageSize int32 `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//当前页码
	PageIndex int32 `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//会员ID
	MemberId             string   `protobuf:"bytes,9,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutListReq) Reset()         { *m = CommissionCashoutListReq{} }
func (m *CommissionCashoutListReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutListReq) ProtoMessage()    {}
func (*CommissionCashoutListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{29}
}

func (m *CommissionCashoutListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutListReq.Unmarshal(m, b)
}
func (m *CommissionCashoutListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutListReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutListReq.Merge(m, src)
}
func (m *CommissionCashoutListReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutListReq.Size(m)
}
func (m *CommissionCashoutListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutListReq proto.InternalMessageInfo

func (m *CommissionCashoutListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommissionCashoutListReq) GetIdentity() int32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *CommissionCashoutListReq) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *CommissionCashoutListReq) GetSearchKeyword() string {
	if m != nil {
		return m.SearchKeyword
	}
	return ""
}

func (m *CommissionCashoutListReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CommissionCashoutListReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CommissionCashoutListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CommissionCashoutListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CommissionCashoutListReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type CommissionCashoutListResponse struct {
	Code    int32                                 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                                `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*CommissionCashoutListResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	//总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutListResponse) Reset()         { *m = CommissionCashoutListResponse{} }
func (m *CommissionCashoutListResponse) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutListResponse) ProtoMessage()    {}
func (*CommissionCashoutListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{30}
}

func (m *CommissionCashoutListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutListResponse.Unmarshal(m, b)
}
func (m *CommissionCashoutListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutListResponse.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutListResponse.Merge(m, src)
}
func (m *CommissionCashoutListResponse) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutListResponse.Size(m)
}
func (m *CommissionCashoutListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutListResponse proto.InternalMessageInfo

func (m *CommissionCashoutListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommissionCashoutListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommissionCashoutListResponse) GetData() []*CommissionCashoutListResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CommissionCashoutListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CommissionCashoutListResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//提现编号
	CashOutBn string `protobuf:"bytes,2,opt,name=cash_out_bn,json=cashOutBn,proto3" json:"cash_out_bn"`
	//提现手机
	CashMobile string `protobuf:"bytes,3,opt,name=cash_mobile,json=cashMobile,proto3" json:"cash_mobile"`
	//申请金额
	CashOut int32 `protobuf:"varint,4,opt,name=cash_out,json=cashOut,proto3" json:"cash_out"`
	//收款人
	CashName string `protobuf:"bytes,5,opt,name=cash_name,json=cashName,proto3" json:"cash_name"`
	//佣金提现收款账户
	CashAccount string `protobuf:"bytes,6,opt,name=cash_account,json=cashAccount,proto3" json:"cash_account"`
	//拼佣金提现开户行
	CashBank string `protobuf:"bytes,7,opt,name=cash_bank,json=cashBank,proto3" json:"cash_bank"`
	//佣金提现开户支行
	CashBranch string `protobuf:"bytes,8,opt,name=cash_branch,json=cashBranch,proto3" json:"cash_branch"`
	//分销员身份 1内部 2外部
	Identity int32 `protobuf:"varint,9,opt,name=identity,proto3" json:"identity"`
	//门店
	Store string `protobuf:"bytes,10,opt,name=store,proto3" json:"store"`
	//财务编码
	FinanceCode string `protobuf:"bytes,11,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//品牌
	Brand string `protobuf:"bytes,12,opt,name=brand,proto3" json:"brand"`
	//城市
	City string `protobuf:"bytes,13,opt,name=city,proto3" json:"city"`
	//大区
	BigRegion string `protobuf:"bytes,14,opt,name=big_region,json=bigRegion,proto3" json:"big_region"`
	//状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
	Status int32 `protobuf:"varint,15,opt,name=status,proto3" json:"status"`
	//申请时间
	CreateTime string `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//审核人
	AuditUser string `protobuf:"bytes,17,opt,name=audit_user,json=auditUser,proto3" json:"audit_user"`
	//审核驳回的原因
	Memo string `protobuf:"bytes,18,opt,name=memo,proto3" json:"memo"`
	//审核时间
	UpdateTime           string   `protobuf:"bytes,19,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutListResponse_Data) Reset()         { *m = CommissionCashoutListResponse_Data{} }
func (m *CommissionCashoutListResponse_Data) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutListResponse_Data) ProtoMessage()    {}
func (*CommissionCashoutListResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{30, 0}
}

func (m *CommissionCashoutListResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutListResponse_Data.Unmarshal(m, b)
}
func (m *CommissionCashoutListResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutListResponse_Data.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutListResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutListResponse_Data.Merge(m, src)
}
func (m *CommissionCashoutListResponse_Data) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutListResponse_Data.Size(m)
}
func (m *CommissionCashoutListResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutListResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutListResponse_Data proto.InternalMessageInfo

func (m *CommissionCashoutListResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommissionCashoutListResponse_Data) GetCashOutBn() string {
	if m != nil {
		return m.CashOutBn
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCashMobile() string {
	if m != nil {
		return m.CashMobile
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCashOut() int32 {
	if m != nil {
		return m.CashOut
	}
	return 0
}

func (m *CommissionCashoutListResponse_Data) GetCashName() string {
	if m != nil {
		return m.CashName
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCashAccount() string {
	if m != nil {
		return m.CashAccount
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCashBank() string {
	if m != nil {
		return m.CashBank
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCashBranch() string {
	if m != nil {
		return m.CashBranch
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetIdentity() int32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *CommissionCashoutListResponse_Data) GetStore() string {
	if m != nil {
		return m.Store
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetBrand() string {
	if m != nil {
		return m.Brand
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetBigRegion() string {
	if m != nil {
		return m.BigRegion
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommissionCashoutListResponse_Data) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetAuditUser() string {
	if m != nil {
		return m.AuditUser
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetMemo() string {
	if m != nil {
		return m.Memo
	}
	return ""
}

func (m *CommissionCashoutListResponse_Data) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type CommissionCashoutAuditReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//状态 1审核通过待打款 2已驳回 3已打款
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	//审核驳回的原因
	Memo                 string   `protobuf:"bytes,3,opt,name=memo,proto3" json:"memo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutAuditReq) Reset()         { *m = CommissionCashoutAuditReq{} }
func (m *CommissionCashoutAuditReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutAuditReq) ProtoMessage()    {}
func (*CommissionCashoutAuditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{31}
}

func (m *CommissionCashoutAuditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutAuditReq.Unmarshal(m, b)
}
func (m *CommissionCashoutAuditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutAuditReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutAuditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutAuditReq.Merge(m, src)
}
func (m *CommissionCashoutAuditReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutAuditReq.Size(m)
}
func (m *CommissionCashoutAuditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutAuditReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutAuditReq proto.InternalMessageInfo

func (m *CommissionCashoutAuditReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommissionCashoutAuditReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommissionCashoutAuditReq) GetMemo() string {
	if m != nil {
		return m.Memo
	}
	return ""
}

type CommissionCashoutQueryReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutQueryReq) Reset()         { *m = CommissionCashoutQueryReq{} }
func (m *CommissionCashoutQueryReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutQueryReq) ProtoMessage()    {}
func (*CommissionCashoutQueryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{32}
}

func (m *CommissionCashoutQueryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutQueryReq.Unmarshal(m, b)
}
func (m *CommissionCashoutQueryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutQueryReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutQueryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutQueryReq.Merge(m, src)
}
func (m *CommissionCashoutQueryReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutQueryReq.Size(m)
}
func (m *CommissionCashoutQueryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutQueryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutQueryReq proto.InternalMessageInfo

func (m *CommissionCashoutQueryReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CommissionCashoutQueryResponse struct {
	Code                 int32                                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CommissionCashoutQueryResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *CommissionCashoutQueryResponse) Reset()         { *m = CommissionCashoutQueryResponse{} }
func (m *CommissionCashoutQueryResponse) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutQueryResponse) ProtoMessage()    {}
func (*CommissionCashoutQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{33}
}

func (m *CommissionCashoutQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutQueryResponse.Unmarshal(m, b)
}
func (m *CommissionCashoutQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutQueryResponse.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutQueryResponse.Merge(m, src)
}
func (m *CommissionCashoutQueryResponse) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutQueryResponse.Size(m)
}
func (m *CommissionCashoutQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutQueryResponse proto.InternalMessageInfo

func (m *CommissionCashoutQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommissionCashoutQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommissionCashoutQueryResponse) GetData() *CommissionCashoutQueryResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommissionCashoutQueryResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//提现编号
	CashOutBn string `protobuf:"bytes,2,opt,name=cash_out_bn,json=cashOutBn,proto3" json:"cash_out_bn"`
	//提现手机
	CashMobile string `protobuf:"bytes,3,opt,name=cash_mobile,json=cashMobile,proto3" json:"cash_mobile"`
	//申请金额
	CashOut int32 `protobuf:"varint,4,opt,name=cash_out,json=cashOut,proto3" json:"cash_out"`
	//收款人
	CashName string `protobuf:"bytes,5,opt,name=cash_name,json=cashName,proto3" json:"cash_name"`
	//佣金提现收款账户
	CashAccount string `protobuf:"bytes,6,opt,name=cash_account,json=cashAccount,proto3" json:"cash_account"`
	//佣金提现开户行
	CashBank string `protobuf:"bytes,7,opt,name=cash_bank,json=cashBank,proto3" json:"cash_bank"`
	//佣金提现开户支行
	CashBranch string `protobuf:"bytes,8,opt,name=cash_branch,json=cashBranch,proto3" json:"cash_branch"`
	//分销员身份 1内部 2外部
	Identity int32 `protobuf:"varint,9,opt,name=identity,proto3" json:"identity"`
	//门店
	Store string `protobuf:"bytes,10,opt,name=store,proto3" json:"store"`
	//财务编码
	FinanceCode string `protobuf:"bytes,11,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//品牌
	Brand string `protobuf:"bytes,12,opt,name=brand,proto3" json:"brand"`
	//城市
	City string `protobuf:"bytes,13,opt,name=city,proto3" json:"city"`
	//大区
	BigRegion string `protobuf:"bytes,14,opt,name=big_region,json=bigRegion,proto3" json:"big_region"`
	//状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
	Status int32 `protobuf:"varint,15,opt,name=status,proto3" json:"status"`
	//申请时间
	CreateTime string `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//审核人
	AuditUser string `protobuf:"bytes,17,opt,name=audit_user,json=auditUser,proto3" json:"audit_user"`
	//审核驳回的原因
	Memo string `protobuf:"bytes,18,opt,name=memo,proto3" json:"memo"`
	//审核时间
	UpdateTime           string   `protobuf:"bytes,19,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutQueryResponse_Data) Reset()         { *m = CommissionCashoutQueryResponse_Data{} }
func (m *CommissionCashoutQueryResponse_Data) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutQueryResponse_Data) ProtoMessage()    {}
func (*CommissionCashoutQueryResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{33, 0}
}

func (m *CommissionCashoutQueryResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutQueryResponse_Data.Unmarshal(m, b)
}
func (m *CommissionCashoutQueryResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutQueryResponse_Data.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutQueryResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutQueryResponse_Data.Merge(m, src)
}
func (m *CommissionCashoutQueryResponse_Data) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutQueryResponse_Data.Size(m)
}
func (m *CommissionCashoutQueryResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutQueryResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutQueryResponse_Data proto.InternalMessageInfo

func (m *CommissionCashoutQueryResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommissionCashoutQueryResponse_Data) GetCashOutBn() string {
	if m != nil {
		return m.CashOutBn
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCashMobile() string {
	if m != nil {
		return m.CashMobile
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCashOut() int32 {
	if m != nil {
		return m.CashOut
	}
	return 0
}

func (m *CommissionCashoutQueryResponse_Data) GetCashName() string {
	if m != nil {
		return m.CashName
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCashAccount() string {
	if m != nil {
		return m.CashAccount
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCashBank() string {
	if m != nil {
		return m.CashBank
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCashBranch() string {
	if m != nil {
		return m.CashBranch
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetIdentity() int32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *CommissionCashoutQueryResponse_Data) GetStore() string {
	if m != nil {
		return m.Store
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetBrand() string {
	if m != nil {
		return m.Brand
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetBigRegion() string {
	if m != nil {
		return m.BigRegion
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommissionCashoutQueryResponse_Data) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetAuditUser() string {
	if m != nil {
		return m.AuditUser
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetMemo() string {
	if m != nil {
		return m.Memo
	}
	return ""
}

func (m *CommissionCashoutQueryResponse_Data) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type CommissionCashoutImportReq struct {
	ExcelUrl             string   `protobuf:"bytes,1,opt,name=excel_url,json=excelUrl,proto3" json:"excel_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutImportReq) Reset()         { *m = CommissionCashoutImportReq{} }
func (m *CommissionCashoutImportReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutImportReq) ProtoMessage()    {}
func (*CommissionCashoutImportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{34}
}

func (m *CommissionCashoutImportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutImportReq.Unmarshal(m, b)
}
func (m *CommissionCashoutImportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutImportReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutImportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutImportReq.Merge(m, src)
}
func (m *CommissionCashoutImportReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutImportReq.Size(m)
}
func (m *CommissionCashoutImportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutImportReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutImportReq proto.InternalMessageInfo

func (m *CommissionCashoutImportReq) GetExcelUrl() string {
	if m != nil {
		return m.ExcelUrl
	}
	return ""
}

type CommissionCashoutImportListReq struct {
	//每页数量
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//当前页码
	PageIndex            int32    `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutImportListReq) Reset()         { *m = CommissionCashoutImportListReq{} }
func (m *CommissionCashoutImportListReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutImportListReq) ProtoMessage()    {}
func (*CommissionCashoutImportListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{35}
}

func (m *CommissionCashoutImportListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutImportListReq.Unmarshal(m, b)
}
func (m *CommissionCashoutImportListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutImportListReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutImportListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutImportListReq.Merge(m, src)
}
func (m *CommissionCashoutImportListReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutImportListReq.Size(m)
}
func (m *CommissionCashoutImportListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutImportListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutImportListReq proto.InternalMessageInfo

func (m *CommissionCashoutImportListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CommissionCashoutImportListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type CommissionCashoutImportListResponse struct {
	Code    int32                                       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*CommissionCashoutImportListResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	//总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutImportListResponse) Reset()         { *m = CommissionCashoutImportListResponse{} }
func (m *CommissionCashoutImportListResponse) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutImportListResponse) ProtoMessage()    {}
func (*CommissionCashoutImportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{36}
}

func (m *CommissionCashoutImportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutImportListResponse.Unmarshal(m, b)
}
func (m *CommissionCashoutImportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutImportListResponse.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutImportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutImportListResponse.Merge(m, src)
}
func (m *CommissionCashoutImportListResponse) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutImportListResponse.Size(m)
}
func (m *CommissionCashoutImportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutImportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutImportListResponse proto.InternalMessageInfo

func (m *CommissionCashoutImportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommissionCashoutImportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommissionCashoutImportListResponse) GetData() []*CommissionCashoutImportListResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CommissionCashoutImportListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CommissionCashoutImportListResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//导入结果
	ImportResult string `protobuf:"bytes,2,opt,name=import_result,json=importResult,proto3" json:"import_result"`
	//导入结果文件下载地址
	ImportResultUrl string `protobuf:"bytes,3,opt,name=import_result_url,json=importResultUrl,proto3" json:"import_result_url"`
	//导入时间
	CreateTime           string   `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutImportListResponse_Data) Reset() {
	*m = CommissionCashoutImportListResponse_Data{}
}
func (m *CommissionCashoutImportListResponse_Data) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutImportListResponse_Data) ProtoMessage()    {}
func (*CommissionCashoutImportListResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{36, 0}
}

func (m *CommissionCashoutImportListResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutImportListResponse_Data.Unmarshal(m, b)
}
func (m *CommissionCashoutImportListResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutImportListResponse_Data.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutImportListResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutImportListResponse_Data.Merge(m, src)
}
func (m *CommissionCashoutImportListResponse_Data) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutImportListResponse_Data.Size(m)
}
func (m *CommissionCashoutImportListResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutImportListResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutImportListResponse_Data proto.InternalMessageInfo

func (m *CommissionCashoutImportListResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommissionCashoutImportListResponse_Data) GetImportResult() string {
	if m != nil {
		return m.ImportResult
	}
	return ""
}

func (m *CommissionCashoutImportListResponse_Data) GetImportResultUrl() string {
	if m != nil {
		return m.ImportResultUrl
	}
	return ""
}

func (m *CommissionCashoutImportListResponse_Data) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type CommissionInfoReq struct {
	//会员ID
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionInfoReq) Reset()         { *m = CommissionInfoReq{} }
func (m *CommissionInfoReq) String() string { return proto.CompactTextString(m) }
func (*CommissionInfoReq) ProtoMessage()    {}
func (*CommissionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{37}
}

func (m *CommissionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionInfoReq.Unmarshal(m, b)
}
func (m *CommissionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionInfoReq.Marshal(b, m, deterministic)
}
func (m *CommissionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionInfoReq.Merge(m, src)
}
func (m *CommissionInfoReq) XXX_Size() int {
	return xxx_messageInfo_CommissionInfoReq.Size(m)
}
func (m *CommissionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionInfoReq proto.InternalMessageInfo

func (m *CommissionInfoReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type CommissionInfoResponse struct {
	Code                 int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CommissionInfoResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *CommissionInfoResponse) Reset()         { *m = CommissionInfoResponse{} }
func (m *CommissionInfoResponse) String() string { return proto.CompactTextString(m) }
func (*CommissionInfoResponse) ProtoMessage()    {}
func (*CommissionInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{38}
}

func (m *CommissionInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionInfoResponse.Unmarshal(m, b)
}
func (m *CommissionInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionInfoResponse.Marshal(b, m, deterministic)
}
func (m *CommissionInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionInfoResponse.Merge(m, src)
}
func (m *CommissionInfoResponse) XXX_Size() int {
	return xxx_messageInfo_CommissionInfoResponse.Size(m)
}
func (m *CommissionInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionInfoResponse proto.InternalMessageInfo

func (m *CommissionInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommissionInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommissionInfoResponse) GetData() *CommissionInfoResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommissionInfoResponse_Data struct {
	//可提现佣金(不包括冻结中)
	CommissionCash int32 `protobuf:"varint,1,opt,name=commission_cash,json=commissionCash,proto3" json:"commission_cash"`
	//已申请提现(冻结中)
	CommissionFrozen int32 `protobuf:"varint,2,opt,name=commission_frozen,json=commissionFrozen,proto3" json:"commission_frozen"`
	//提现人
	CashName string `protobuf:"bytes,3,opt,name=cash_name,json=cashName,proto3" json:"cash_name"`
	//佣金提现收款账户
	CashAccount string `protobuf:"bytes,4,opt,name=cash_account,json=cashAccount,proto3" json:"cash_account"`
	//佣金提现开户行
	CashBank string `protobuf:"bytes,5,opt,name=cash_bank,json=cashBank,proto3" json:"cash_bank"`
	//佣金提现开户支行
	CashBranch           string   `protobuf:"bytes,6,opt,name=cash_branch,json=cashBranch,proto3" json:"cash_branch"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionInfoResponse_Data) Reset()         { *m = CommissionInfoResponse_Data{} }
func (m *CommissionInfoResponse_Data) String() string { return proto.CompactTextString(m) }
func (*CommissionInfoResponse_Data) ProtoMessage()    {}
func (*CommissionInfoResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{38, 0}
}

func (m *CommissionInfoResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionInfoResponse_Data.Unmarshal(m, b)
}
func (m *CommissionInfoResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionInfoResponse_Data.Marshal(b, m, deterministic)
}
func (m *CommissionInfoResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionInfoResponse_Data.Merge(m, src)
}
func (m *CommissionInfoResponse_Data) XXX_Size() int {
	return xxx_messageInfo_CommissionInfoResponse_Data.Size(m)
}
func (m *CommissionInfoResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionInfoResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionInfoResponse_Data proto.InternalMessageInfo

func (m *CommissionInfoResponse_Data) GetCommissionCash() int32 {
	if m != nil {
		return m.CommissionCash
	}
	return 0
}

func (m *CommissionInfoResponse_Data) GetCommissionFrozen() int32 {
	if m != nil {
		return m.CommissionFrozen
	}
	return 0
}

func (m *CommissionInfoResponse_Data) GetCashName() string {
	if m != nil {
		return m.CashName
	}
	return ""
}

func (m *CommissionInfoResponse_Data) GetCashAccount() string {
	if m != nil {
		return m.CashAccount
	}
	return ""
}

func (m *CommissionInfoResponse_Data) GetCashBank() string {
	if m != nil {
		return m.CashBank
	}
	return ""
}

func (m *CommissionInfoResponse_Data) GetCashBranch() string {
	if m != nil {
		return m.CashBranch
	}
	return ""
}

type CommissionCashoutReq struct {
	//申请提现金额
	CashOut int32 `protobuf:"varint,1,opt,name=cash_out,json=cashOut,proto3" json:"cash_out"`
	//提现人
	CashName string `protobuf:"bytes,2,opt,name=cash_name,json=cashName,proto3" json:"cash_name"`
	//佣金提现收款账户
	CashAccount string `protobuf:"bytes,3,opt,name=cash_account,json=cashAccount,proto3" json:"cash_account"`
	//佣金提现开户行
	CashBank string `protobuf:"bytes,4,opt,name=cash_bank,json=cashBank,proto3" json:"cash_bank"`
	//佣金提现开户支行
	CashBranch string `protobuf:"bytes,5,opt,name=cash_branch,json=cashBranch,proto3" json:"cash_branch"`
	//会员ID
	MemberId             string   `protobuf:"bytes,6,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionCashoutReq) Reset()         { *m = CommissionCashoutReq{} }
func (m *CommissionCashoutReq) String() string { return proto.CompactTextString(m) }
func (*CommissionCashoutReq) ProtoMessage()    {}
func (*CommissionCashoutReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_23265cd3eba665c6, []int{39}
}

func (m *CommissionCashoutReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommissionCashoutReq.Unmarshal(m, b)
}
func (m *CommissionCashoutReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommissionCashoutReq.Marshal(b, m, deterministic)
}
func (m *CommissionCashoutReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionCashoutReq.Merge(m, src)
}
func (m *CommissionCashoutReq) XXX_Size() int {
	return xxx_messageInfo_CommissionCashoutReq.Size(m)
}
func (m *CommissionCashoutReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionCashoutReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionCashoutReq proto.InternalMessageInfo

func (m *CommissionCashoutReq) GetCashOut() int32 {
	if m != nil {
		return m.CashOut
	}
	return 0
}

func (m *CommissionCashoutReq) GetCashName() string {
	if m != nil {
		return m.CashName
	}
	return ""
}

func (m *CommissionCashoutReq) GetCashAccount() string {
	if m != nil {
		return m.CashAccount
	}
	return ""
}

func (m *CommissionCashoutReq) GetCashBank() string {
	if m != nil {
		return m.CashBank
	}
	return ""
}

func (m *CommissionCashoutReq) GetCashBranch() string {
	if m != nil {
		return m.CashBranch
	}
	return ""
}

func (m *CommissionCashoutReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func init() {
	proto.RegisterType((*PickupResponse)(nil), "dac.PickupResponse")
	proto.RegisterType((*PickupListReq)(nil), "dac.PickupListReq")
	proto.RegisterType((*PickupListResponse)(nil), "dac.PickupListResponse")
	proto.RegisterType((*PickupListResponse_Pickup)(nil), "dac.PickupListResponse.Pickup")
	proto.RegisterType((*PickupStoreReq)(nil), "dac.PickupStoreReq")
	proto.RegisterType((*PickupDeleteReq)(nil), "dac.PickupDeleteReq")
	proto.RegisterType((*StationImportTemplateReq)(nil), "dac.StationImportTemplateReq")
	proto.RegisterType((*StationImportTemplateResponse)(nil), "dac.StationImportTemplateResponse")
	proto.RegisterType((*StationImportReq)(nil), "dac.StationImportReq")
	proto.RegisterType((*StationImportResponse)(nil), "dac.StationImportResponse")
	proto.RegisterType((*StationImportHistoryListReq)(nil), "dac.StationImportHistoryListReq")
	proto.RegisterType((*StationImportHistoryListResponse)(nil), "dac.StationImportHistoryListResponse")
	proto.RegisterType((*StationImportHistoryListResponse_List)(nil), "dac.StationImportHistoryListResponse.List")
	proto.RegisterType((*StationsReq)(nil), "dac.StationsReq")
	proto.RegisterType((*StationsResponse)(nil), "dac.StationsResponse")
	proto.RegisterType((*StationsResponse_List)(nil), "dac.StationsResponse.List")
	proto.RegisterType((*StationStoreReq)(nil), "dac.StationStoreReq")
	proto.RegisterType((*StationPatchReq)(nil), "dac.StationPatchReq")
	proto.RegisterType((*StationDetailReq)(nil), "dac.StationDetailReq")
	proto.RegisterType((*StationDetailResponse)(nil), "dac.StationDetailResponse")
	proto.RegisterType((*StationDetailResponse_Data)(nil), "dac.StationDetailResponse.Data")
	proto.RegisterType((*AWStationsReq)(nil), "dac.AWStationsReq")
	proto.RegisterType((*AWStationsResponse)(nil), "dac.AWStationsResponse")
	proto.RegisterType((*AWStationsResponse_Station)(nil), "dac.AWStationsResponse.Station")
	proto.RegisterType((*AWStationStateReq)(nil), "dac.AWStationStateReq")
	proto.RegisterType((*AWStationStateResponse)(nil), "dac.AWStationStateResponse")
	proto.RegisterType((*AWStationStateResponse_State)(nil), "dac.AWStationStateResponse.State")
	proto.RegisterType((*AWStationNearestReq)(nil), "dac.AWStationNearestReq")
	proto.RegisterType((*AWStationNearestResponse)(nil), "dac.AWStationNearestResponse")
	proto.RegisterType((*AWStationNearestResponse_Nearest)(nil), "dac.AWStationNearestResponse.Nearest")
	proto.RegisterType((*GroupActivityListReq)(nil), "dac.GroupActivityListReq")
	proto.RegisterType((*GroupActivityListResponse)(nil), "dac.GroupActivityListResponse")
	proto.RegisterType((*GroupActivityListResponse_Data)(nil), "dac.GroupActivityListResponse.Data")
	proto.RegisterType((*GroupActivityQueryReq)(nil), "dac.GroupActivityQueryReq")
	proto.RegisterType((*GroupActivityQueryResponse)(nil), "dac.GroupActivityQueryResponse")
	proto.RegisterType((*GroupActivityQueryResponse_Data)(nil), "dac.GroupActivityQueryResponse.Data")
	proto.RegisterType((*GroupActivityEditReq)(nil), "dac.GroupActivityEditReq")
	proto.RegisterType((*GroupActivityStatusReq)(nil), "dac.GroupActivityStatusReq")
	proto.RegisterType((*CommissionCashoutListReq)(nil), "dac.CommissionCashoutListReq")
	proto.RegisterType((*CommissionCashoutListResponse)(nil), "dac.CommissionCashoutListResponse")
	proto.RegisterType((*CommissionCashoutListResponse_Data)(nil), "dac.CommissionCashoutListResponse.Data")
	proto.RegisterType((*CommissionCashoutAuditReq)(nil), "dac.CommissionCashoutAuditReq")
	proto.RegisterType((*CommissionCashoutQueryReq)(nil), "dac.CommissionCashoutQueryReq")
	proto.RegisterType((*CommissionCashoutQueryResponse)(nil), "dac.CommissionCashoutQueryResponse")
	proto.RegisterType((*CommissionCashoutQueryResponse_Data)(nil), "dac.CommissionCashoutQueryResponse.Data")
	proto.RegisterType((*CommissionCashoutImportReq)(nil), "dac.CommissionCashoutImportReq")
	proto.RegisterType((*CommissionCashoutImportListReq)(nil), "dac.CommissionCashoutImportListReq")
	proto.RegisterType((*CommissionCashoutImportListResponse)(nil), "dac.CommissionCashoutImportListResponse")
	proto.RegisterType((*CommissionCashoutImportListResponse_Data)(nil), "dac.CommissionCashoutImportListResponse.Data")
	proto.RegisterType((*CommissionInfoReq)(nil), "dac.CommissionInfoReq")
	proto.RegisterType((*CommissionInfoResponse)(nil), "dac.CommissionInfoResponse")
	proto.RegisterType((*CommissionInfoResponse_Data)(nil), "dac.CommissionInfoResponse.Data")
	proto.RegisterType((*CommissionCashoutReq)(nil), "dac.CommissionCashoutReq")
}

func init() { proto.RegisterFile("dac/pickup.proto", fileDescriptor_23265cd3eba665c6) }

var fileDescriptor_23265cd3eba665c6 = []byte{
	// 2451 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x1a, 0x4d, 0x8f, 0x1b, 0x49,
	0x55, 0xfe, 0xb6, 0xdf, 0x7c, 0x57, 0x26, 0x13, 0xa7, 0x47, 0x33, 0x99, 0xf4, 0x10, 0x12, 0x65,
	0xc1, 0x81, 0x04, 0x04, 0x11, 0x68, 0xa5, 0x49, 0x26, 0x81, 0x61, 0xc9, 0x90, 0xf5, 0x24, 0x1b,
	0x29, 0x0a, 0x6a, 0x95, 0xbb, 0x6b, 0x66, 0x6a, 0xe3, 0xee, 0xb6, 0xbb, 0xdb, 0x49, 0x3a, 0x27,
	0xb4, 0x27, 0x7e, 0x00, 0x37, 0xa4, 0xbd, 0xf3, 0x71, 0xe7, 0xc6, 0x91, 0x13, 0x7f, 0x00, 0x8e,
	0x08, 0x21, 0x71, 0x43, 0xec, 0x69, 0x0f, 0x48, 0xa8, 0x3e, 0xba, 0xdd, 0xd5, 0x5f, 0xb6, 0x1c,
	0x38, 0x6d, 0x4e, 0xee, 0x7a, 0x55, 0xf5, 0xde, 0xab, 0xf7, 0x5d, 0xf5, 0x0c, 0xeb, 0x16, 0x36,
	0x6f, 0x8d, 0xa8, 0xf9, 0x72, 0x32, 0xea, 0x8d, 0x3c, 0x37, 0x70, 0x51, 0xcd, 0xc2, 0xa6, 0xb6,
	0x7b, 0xe6, 0xba, 0x67, 0x43, 0x72, 0x8b, 0x83, 0x06, 0x93, 0xd3, 0x5b, 0xaf, 0x3d, 0x3c, 0x1a,
	0x11, 0xcf, 0x17, 0x8b, 0xb4, 0xed, 0xf4, 0x3c, 0xb1, 0x47, 0x41, 0x28, 0x26, 0xf5, 0x0f, 0x61,
	0xf5, 0x31, 0xc7, 0xd8, 0x27, 0xfe, 0xc8, 0x75, 0x7c, 0x82, 0x10, 0xd4, 0x4d, 0xd7, 0x22, 0xdd,
	0xca, 0x5e, 0xe5, 0x46, 0xa3, 0xcf, 0xbf, 0x51, 0x17, 0x5a, 0x36, 0xf1, 0x7d, 0x7c, 0x46, 0xba,
	0xd5, 0xbd, 0xca, 0x8d, 0x4e, 0x3f, 0x1a, 0xea, 0x9f, 0xc2, 0x8a, 0xd8, 0xff, 0x53, 0xea, 0x07,
	0x7d, 0x32, 0x46, 0x3b, 0x00, 0xfe, 0xb9, 0x3b, 0x32, 0xd8, 0x3e, 0x9f, 0x23, 0xe9, 0xf4, 0x3b,
	0x0c, 0x72, 0x9f, 0x01, 0xd0, 0x36, 0x74, 0x46, 0xf8, 0x8c, 0x18, 0x3e, 0x7d, 0x2b, 0x70, 0x35,
	0xfa, 0x6d, 0x06, 0x38, 0xa1, 0x6f, 0x09, 0xdb, 0xcb, 0x27, 0xa9, 0x63, 0x91, 0x37, 0xdd, 0x1a,
	0x9f, 0xe5, 0xcb, 0x8f, 0x18, 0x40, 0xff, 0xac, 0x06, 0x28, 0x49, 0x6c, 0x11, 0x86, 0xd1, 0x6d,
	0xa8, 0x5b, 0x38, 0xc0, 0xdd, 0xe6, 0x5e, 0xed, 0xc6, 0xd2, 0xed, 0xdd, 0x9e, 0x85, 0xcd, 0x5e,
	0x16, 0xa9, 0x04, 0xf5, 0xf9, 0x5a, 0xb4, 0x09, 0x8d, 0xc0, 0x0d, 0xf0, 0xb0, 0x5b, 0xe7, 0x24,
	0xc4, 0x40, 0xfb, 0xa2, 0x02, 0x4d, 0xb1, 0x0c, 0xad, 0x42, 0x95, 0x5a, 0x92, 0x81, 0x2a, 0xb5,
	0x18, 0x4b, 0x41, 0x38, 0x8a, 0x68, 0xf3, 0x6f, 0x74, 0x13, 0x36, 0xfc, 0x00, 0x07, 0xd4, 0x75,
	0x0c, 0x0b, 0xd3, 0x61, 0x68, 0xd8, 0xd4, 0xe1, 0x67, 0xac, 0xf6, 0xd7, 0xe4, 0xc4, 0x21, 0x83,
	0x3f, 0xa2, 0x0e, 0xba, 0x0c, 0x6d, 0xe2, 0x58, 0x46, 0x40, 0x6d, 0xc2, 0x69, 0x76, 0xfa, 0x2d,
	0xe2, 0x58, 0x4f, 0xa8, 0x4d, 0xd0, 0x55, 0x58, 0xb6, 0xc8, 0x90, 0xbe, 0x22, 0x9e, 0x61, 0xe1,
	0xd0, 0xef, 0x36, 0x38, 0xd1, 0x25, 0x09, 0x3b, 0xc4, 0x21, 0x97, 0x31, 0x57, 0x81, 0x83, 0x6d,
	0xd2, 0x6d, 0xf2, 0xed, 0x6d, 0x06, 0x38, 0xc6, 0x36, 0x49, 0xe9, 0xa7, 0x95, 0xd6, 0xcf, 0x15,
	0x58, 0xf2, 0xcf, 0xb1, 0x47, 0x0c, 0x6a, 0x33, 0xe1, 0xb5, 0xf9, 0x3c, 0x70, 0xd0, 0x11, 0x83,
	0xe8, 0xff, 0xaa, 0x44, 0x16, 0x73, 0x12, 0xb8, 0x1e, 0x99, 0x43, 0xe5, 0xb9, 0x07, 0xaf, 0xce,
	0x3e, 0x78, 0xad, 0xfc, 0xe0, 0xf5, 0xec, 0xc1, 0x2f, 0x41, 0x6b, 0xe2, 0x13, 0xcf, 0x70, 0x5c,
	0x79, 0xec, 0x26, 0x1b, 0x1e, 0xbb, 0x4c, 0x22, 0x62, 0x82, 0x49, 0x44, 0x9c, 0xb9, 0xcd, 0xa7,
	0x98, 0x44, 0x66, 0x1e, 0xf9, 0x19, 0xac, 0x89, 0x13, 0x1f, 0x92, 0x21, 0x09, 0xf8, 0x91, 0xd3,
	0x0a, 0x5f, 0x88, 0xb2, 0xae, 0x41, 0xf7, 0x44, 0x08, 0xe0, 0xc8, 0x1e, 0xb9, 0x5e, 0xf0, 0x84,
	0xd8, 0xa3, 0x21, 0xe6, 0x14, 0x74, 0x0a, 0x3b, 0x05, 0x73, 0x0b, 0x99, 0xbd, 0x06, 0xed, 0x40,
	0x62, 0xe0, 0x82, 0x5d, 0xee, 0xc7, 0x63, 0xfd, 0xeb, 0xb0, 0xae, 0x90, 0x62, 0x07, 0x44, 0x50,
	0x3f, 0xa5, 0x43, 0x81, 0x7d, 0xb9, 0xcf, 0xbf, 0xf5, 0x07, 0x70, 0x31, 0xb5, 0x6e, 0xa1, 0x90,
	0x71, 0x0c, 0xdb, 0x0a, 0x9a, 0x1f, 0x53, 0x3f, 0x70, 0xbd, 0x30, 0x0a, 0x20, 0x08, 0xea, 0xcc,
	0xe5, 0x23, 0x64, 0xec, 0xbb, 0x34, 0x6a, 0xe8, 0xbf, 0xaa, 0xc2, 0x5e, 0x31, 0xc2, 0x85, 0xa4,
	0xf5, 0xa1, 0x0c, 0x12, 0x35, 0x1e, 0x24, 0x6e, 0xf2, 0x20, 0x31, 0x8b, 0x44, 0x8f, 0x0f, 0xca,
	0x02, 0xc6, 0x10, 0xea, 0x6c, 0x4d, 0xc6, 0x78, 0x76, 0x00, 0x4c, 0x8f, 0xe0, 0x80, 0x58, 0x06,
	0x0e, 0x24, 0x2b, 0x1d, 0x09, 0x39, 0x08, 0xd0, 0x16, 0x34, 0x3d, 0xe2, 0x4f, 0x86, 0x81, 0xf4,
	0x08, 0x39, 0x62, 0xdb, 0xc4, 0x97, 0x31, 0xf1, 0x86, 0x32, 0x4c, 0x74, 0x04, 0xe4, 0xa9, 0x37,
	0xd4, 0x3f, 0x85, 0x25, 0xc9, 0xb2, 0x2f, 0xc5, 0xca, 0x6d, 0x50, 0xb8, 0x27, 0xff, 0x66, 0x98,
	0x99, 0x03, 0x4e, 0x7c, 0x49, 0x54, 0x8e, 0x62, 0x15, 0xd4, 0x8a, 0x54, 0x50, 0x4f, 0xa9, 0xe0,
	0xef, 0x95, 0xd8, 0x84, 0xfc, 0x05, 0x45, 0xde, 0x53, 0x44, 0xae, 0x25, 0x45, 0xee, 0xcf, 0x2f,
	0xe2, 0x17, 0x05, 0x22, 0x8e, 0x4e, 0x5f, 0x4d, 0x9c, 0xbe, 0x0b, 0x2d, 0x6c, 0x59, 0x1e, 0xf1,
	0xfd, 0x28, 0xd4, 0xc8, 0x61, 0x42, 0x2e, 0x02, 0xb9, 0x1c, 0xe9, 0x3e, 0xac, 0x49, 0x96, 0xe2,
	0xd8, 0xf7, 0x6e, 0x84, 0xd6, 0xa1, 0x36, 0x74, 0xce, 0xa4, 0xee, 0xd8, 0x27, 0x87, 0xe0, 0x80,
	0x47, 0x75, 0x06, 0xc1, 0x81, 0xfe, 0x49, 0x4c, 0xf4, 0x31, 0x0e, 0xcc, 0xf3, 0x3c, 0xa2, 0x77,
	0x14, 0x3d, 0x2e, 0xdd, 0xde, 0xee, 0x89, 0x94, 0xdf, 0x8b, 0x52, 0x7e, 0xef, 0xc8, 0x09, 0xee,
	0xdc, 0xfe, 0x04, 0x0f, 0x27, 0x24, 0x3e, 0x8c, 0x1e, 0xab, 0xec, 0x90, 0x04, 0x98, 0x0e, 0x73,
	0x10, 0xeb, 0xff, 0xa9, 0xc4, 0x2e, 0x1f, 0x2d, 0x5a, 0x48, 0xb9, 0x77, 0x62, 0xe5, 0x32, 0xf6,
	0xae, 0x24, 0x95, 0xab, 0xe2, 0xed, 0x1d, 0xe2, 0x00, 0x0b, 0x0d, 0x6b, 0xbf, 0xa8, 0x40, 0x9d,
	0x0d, 0xff, 0xff, 0x32, 0x4e, 0x28, 0xbc, 0xa9, 0x28, 0xfc, 0xf7, 0x15, 0x58, 0x39, 0x78, 0x96,
	0x74, 0x23, 0x89, 0xad, 0x92, 0xc1, 0x56, 0x9d, 0x62, 0xbb, 0x0a, 0xcb, 0xa7, 0xd4, 0xc1, 0x8e,
	0x49, 0x78, 0x4a, 0x94, 0x0c, 0x2d, 0x49, 0xd8, 0x7d, 0x29, 0xaa, 0x97, 0x24, 0x7c, 0xed, 0x7a,
	0x56, 0x94, 0xdf, 0xe5, 0x50, 0xf5, 0xb3, 0x46, 0x69, 0x81, 0xd4, 0x4c, 0x17, 0x48, 0xff, 0xae,
	0x00, 0x4a, 0xb2, 0xfb, 0x8e, 0xba, 0xaa, 0xc5, 0xba, 0xca, 0x22, 0x8d, 0xd4, 0x57, 0xea, 0x8d,
	0x26, 0xb4, 0xe4, 0xb2, 0x77, 0xd4, 0xa1, 0x06, 0x6d, 0x8b, 0xfa, 0x01, 0x13, 0x9f, 0x94, 0x57,
	0x3c, 0xd6, 0x9f, 0xc2, 0x46, 0xcc, 0x1e, 0xfb, 0xe1, 0x6e, 0x99, 0x56, 0x41, 0x25, 0xab, 0x02,
	0x56, 0xb5, 0xc8, 0xb2, 0x84, 0x5a, 0x32, 0xa9, 0x74, 0x24, 0xe4, 0xc8, 0xd2, 0x3f, 0xaf, 0xc2,
	0x56, 0x1a, 0xef, 0x42, 0xf2, 0xfc, 0xae, 0x62, 0xfb, 0x57, 0x55, 0x79, 0x2a, 0x88, 0x7b, 0x62,
	0x24, 0xac, 0xff, 0xf3, 0x0a, 0x34, 0xf8, 0x78, 0x2e, 0xd1, 0xed, 0xc3, 0x0a, 0x79, 0x33, 0x22,
	0x26, 0xcb, 0x21, 0x89, 0xe2, 0x69, 0x39, 0x02, 0xf2, 0x0a, 0x2a, 0xb9, 0xc8, 0x22, 0xbe, 0x29,
	0x45, 0x19, 0x2f, 0x3a, 0x24, 0xbe, 0x89, 0xae, 0xc3, 0xda, 0xc8, 0x73, 0xcf, 0x98, 0xd8, 0x0d,
	0xc7, 0x0d, 0xa8, 0x49, 0xa4, 0xa3, 0xac, 0x46, 0xe0, 0x63, 0x0e, 0xd5, 0x5f, 0xc0, 0x85, 0xf8,
	0x18, 0xc7, 0x04, 0x7b, 0x44, 0xa4, 0xef, 0xff, 0x8d, 0x83, 0xe8, 0x5f, 0x56, 0xa1, 0x9b, 0x45,
	0xbf, 0x90, 0x02, 0xee, 0x2a, 0x0a, 0xb8, 0xa6, 0x2a, 0x20, 0x85, 0xba, 0x17, 0x8d, 0x85, 0x12,
	0xbe, 0xa8, 0x40, 0x4b, 0x42, 0xe6, 0x52, 0x03, 0xf7, 0x4f, 0x6a, 0x19, 0xa6, 0x3b, 0x71, 0x82,
	0xe9, 0x05, 0x86, 0x5a, 0xf7, 0x19, 0x40, 0x64, 0x72, 0x1b, 0x53, 0x27, 0xca, 0x2b, 0x62, 0x94,
	0xd5, 0x5e, 0x63, 0x1e, 0xed, 0x35, 0xe7, 0xd3, 0x5e, 0x2b, 0x4f, 0x7b, 0x8a, 0x47, 0xb5, 0x53,
	0x1e, 0xf5, 0x8f, 0x0a, 0x6c, 0xfe, 0xc8, 0x73, 0x27, 0xa3, 0x03, 0x33, 0xa0, 0xaf, 0x68, 0x10,
	0x97, 0x66, 0xd3, 0x30, 0x59, 0x49, 0x86, 0x49, 0xe9, 0x4a, 0x5e, 0x20, 0x98, 0x97, 0x05, 0x0c,
	0x87, 0x70, 0xce, 0xcb, 0x8b, 0x7a, 0xc5, 0x12, 0xea, 0xb9, 0x7e, 0x1a, 0x55, 0x47, 0x83, 0x50,
	0x4a, 0x26, 0xaa, 0x8e, 0xee, 0x85, 0x6a, 0xbc, 0x6c, 0x96, 0xc6, 0xcb, 0x56, 0x3a, 0x5e, 0xfe,
	0xa9, 0x0e, 0x97, 0x73, 0x0e, 0xba, 0x90, 0x95, 0x7d, 0x4f, 0x09, 0x9b, 0xfb, 0xdc, 0xca, 0x0a,
	0x71, 0x27, 0xd2, 0x5c, 0x41, 0xe8, 0xfc, 0x5d, 0xad, 0x20, 0xf9, 0x7d, 0x03, 0x10, 0x2b, 0x3d,
	0x89, 0xa1, 0xc8, 0x6d, 0x85, 0x33, 0xb3, 0xce, 0x67, 0x1e, 0xa6, 0x83, 0x1c, 0x5b, 0xcd, 0x4d,
	0x75, 0x35, 0xd2, 0x8c, 0xeb, 0x91, 0xf8, 0x32, 0xb8, 0x98, 0xe2, 0xf6, 0x61, 0x45, 0xde, 0xbc,
	0x42, 0xc3, 0x8e, 0x34, 0xd7, 0xe8, 0x47, 0x57, 0xb4, 0xf0, 0x11, 0xa3, 0x7e, 0x09, 0x5a, 0xd4,
	0x37, 0x4e, 0x3d, 0x12, 0x65, 0xb2, 0x26, 0xf5, 0x1f, 0x7a, 0x24, 0x9a, 0xf0, 0xcf, 0xdd, 0xd7,
	0x51, 0xc2, 0xa5, 0xfe, 0xc9, 0xb9, 0xfb, 0x9a, 0xdd, 0xc5, 0xce, 0x98, 0xd0, 0x0c, 0xdb, 0x75,
	0x48, 0x28, 0x35, 0x06, 0x1c, 0xf4, 0x88, 0x41, 0x98, 0xba, 0xc5, 0x02, 0x0b, 0x87, 0xdc, 0x70,
	0x1b, 0xfd, 0x36, 0x07, 0x1c, 0xe2, 0x30, 0x71, 0x45, 0x0c, 0xf9, 0x7c, 0x47, 0xb9, 0x22, 0x86,
	0x6c, 0xc9, 0xd4, 0x84, 0x21, 0x6d, 0xc2, 0x89, 0x1a, 0x7c, 0x29, 0x5d, 0x83, 0xab, 0x46, 0xb8,
	0x9c, 0x32, 0x42, 0xfd, 0x3a, 0x5c, 0x54, 0x74, 0xfd, 0xf1, 0x84, 0x78, 0x61, 0x5e, 0x41, 0xf5,
	0xcf, 0x1a, 0x68, 0x79, 0x2b, 0x17, 0x32, 0xb9, 0xef, 0x2b, 0x81, 0xed, 0x6b, 0x59, 0x93, 0x53,
	0x90, 0x27, 0x4b, 0xab, 0xbf, 0x55, 0x0b, 0xac, 0xeb, 0xbd, 0x41, 0x44, 0x06, 0x91, 0x8e, 0x4c,
	0x4b, 0xb9, 0x91, 0x69, 0xe4, 0xfa, 0x01, 0xf1, 0xf8, 0x05, 0x4c, 0x1a, 0x85, 0x80, 0xb0, 0x0b,
	0xd8, 0x97, 0xd5, 0x54, 0x18, 0x7d, 0x60, 0xd1, 0x20, 0xaf, 0x7c, 0xff, 0xea, 0x09, 0x5d, 0x75,
	0x27, 0x48, 0xc7, 0xf4, 0x77, 0x97, 0xbd, 0x01, 0x5b, 0x8a, 0xe8, 0x4f, 0xb8, 0x52, 0xf3, 0x84,
	0xaf, 0xde, 0x81, 0x73, 0x03, 0xc2, 0x20, 0x94, 0x72, 0x4f, 0x78, 0xfc, 0x6f, 0xaa, 0xd0, 0xbd,
	0xef, 0xda, 0x36, 0xf5, 0x7d, 0xea, 0x3a, 0xf7, 0xb1, 0x7f, 0xee, 0x4e, 0x82, 0x59, 0x79, 0x52,
	0x83, 0x36, 0xb5, 0x88, 0x13, 0xd0, 0x20, 0x8c, 0x5e, 0x31, 0xa2, 0x31, 0x7f, 0x85, 0x22, 0xd8,
	0x33, 0xcf, 0x0d, 0xfe, 0x72, 0x28, 0x6a, 0x07, 0x10, 0xa0, 0x27, 0xe1, 0x88, 0xa0, 0x6b, 0xb0,
	0x2a, 0x17, 0xa8, 0x37, 0x87, 0x15, 0x01, 0xfd, 0x48, 0xde, 0x1f, 0x54, 0x63, 0x6a, 0x94, 0x19,
	0x53, 0x53, 0x35, 0x26, 0x25, 0x93, 0xb6, 0x4a, 0x33, 0x69, 0x3b, 0x95, 0x49, 0xd9, 0x5e, 0x9b,
	0xd8, 0x03, 0xe2, 0xb1, 0x5a, 0xba, 0x23, 0xea, 0x09, 0x01, 0x38, 0xb2, 0xf4, 0x3f, 0x36, 0x60,
	0xa7, 0x40, 0x56, 0x0b, 0xc5, 0xbd, 0x1f, 0x28, 0xa9, 0xf6, 0x3a, 0x8f, 0x7b, 0xa5, 0xf8, 0x67,
	0xa7, 0xdb, 0x5f, 0xd7, 0x0b, 0x02, 0xe2, 0x2e, 0x2c, 0x99, 0xd8, 0x3f, 0x37, 0xdc, 0x49, 0x60,
	0x0c, 0x9c, 0xf8, 0x71, 0x06, 0xfb, 0xe7, 0x3f, 0x9b, 0x04, 0xf7, 0x1c, 0xa6, 0x36, 0x3e, 0x6f,
	0xbb, 0x03, 0x3a, 0x8c, 0xfc, 0x13, 0x18, 0xe8, 0x11, 0x87, 0x30, 0x81, 0x47, 0x08, 0x24, 0xc9,
	0x96, 0xdc, 0xcd, 0x84, 0xc6, 0xa7, 0x78, 0x6e, 0x16, 0x9a, 0xe2, 0x6b, 0x79, 0x6a, 0xbe, 0x0a,
	0xcb, 0x7c, 0x12, 0x9b, 0xa2, 0x98, 0x14, 0xca, 0xe2, 0xc4, 0x0e, 0x04, 0x28, 0xde, 0x3f, 0xc0,
	0xce, 0xcb, 0xe8, 0x6d, 0x91, 0x01, 0xee, 0x61, 0xe7, 0x65, 0xcc, 0xd8, 0xc0, 0xc3, 0x8e, 0x79,
	0x1e, 0xbd, 0x6a, 0xf2, 0x69, 0x0e, 0x51, 0x8c, 0xb1, 0x93, 0x32, 0xc6, 0x4d, 0x68, 0xf0, 0x22,
	0x41, 0xba, 0xa6, 0x18, 0xcc, 0xe3, 0x96, 0x9b, 0xd0, 0x60, 0x04, 0x2d, 0xe9, 0x91, 0x62, 0xc0,
	0xd5, 0xcb, 0xc8, 0x88, 0x2a, 0x85, 0x7f, 0x33, 0x83, 0x1a, 0xd0, 0x33, 0xc3, 0x23, 0x67, 0xd4,
	0x75, 0xa2, 0xca, 0x64, 0x40, 0xcf, 0xfa, 0x1c, 0x90, 0x70, 0xa1, 0x35, 0xc5, 0x85, 0xd8, 0xb1,
	0xb8, 0x13, 0x0a, 0x13, 0x5e, 0x97, 0xc7, 0xe2, 0x20, 0x6e, 0xc5, 0x3b, 0x00, 0x78, 0x62, 0xd1,
	0xc0, 0x98, 0xf8, 0xc4, 0xeb, 0x6e, 0x08, 0xbc, 0x1c, 0xf2, 0xd4, 0x27, 0x1e, 0x63, 0xc5, 0x26,
	0xb6, 0xdb, 0x45, 0x82, 0x15, 0xf6, 0xcd, 0x70, 0x4e, 0x46, 0x56, 0x8c, 0xf3, 0x82, 0xc0, 0x29,
	0x40, 0x0c, 0xa7, 0xfe, 0x0c, 0x2e, 0x67, 0xec, 0xeb, 0x60, 0x52, 0x10, 0xcd, 0x8b, 0x02, 0x4a,
	0x44, 0xb9, 0x36, 0xa5, 0xac, 0x7f, 0x90, 0x83, 0xb8, 0xb0, 0x76, 0xf8, 0x6d, 0x03, 0x76, 0x8b,
	0x56, 0x2f, 0xe4, 0x47, 0x3f, 0x54, 0xea, 0x87, 0x1b, 0xf9, 0x7e, 0x54, 0x58, 0x43, 0xbc, 0x77,
	0x99, 0xf7, 0x2e, 0x53, 0xec, 0x32, 0x77, 0x41, 0xcb, 0x98, 0xd2, 0xb4, 0xbb, 0xb0, 0x0d, 0x1d,
	0xf2, 0xc6, 0x24, 0x43, 0x9e, 0xbc, 0xc5, 0x53, 0x41, 0x9b, 0x03, 0x58, 0xee, 0x7e, 0x91, 0x63,
	0xe6, 0x62, 0x6b, 0x94, 0x5f, 0x95, 0x4c, 0x55, 0x29, 0xcd, 0x54, 0xd5, 0xf4, 0x9d, 0xef, 0x0f,
	0x55, 0xd8, 0x2f, 0x45, 0xbf, 0x90, 0x2b, 0x1d, 0x28, 0x29, 0xe9, 0x9b, 0xf9, 0xae, 0x94, 0xa5,
	0x32, 0x3b, 0x31, 0xfd, 0xb2, 0xe8, 0x11, 0x74, 0x1f, 0x56, 0x28, 0xc7, 0x67, 0xc8, 0xe6, 0x80,
	0xe0, 0x68, 0x99, 0x46, 0xad, 0x99, 0xc9, 0x30, 0x40, 0x37, 0x61, 0x43, 0x59, 0xc4, 0xe5, 0x2d,
	0x1c, 0x6e, 0x2d, 0xb9, 0xf0, 0xa9, 0x37, 0x4c, 0x9b, 0x49, 0x3d, 0x6d, 0x26, 0xfa, 0xb7, 0x60,
	0x63, 0x7a, 0xa4, 0x23, 0xe7, 0xd4, 0x95, 0xaa, 0x98, 0x26, 0xfe, 0x4a, 0x2a, 0xf1, 0xff, 0xa5,
	0x0a, 0x5b, 0xe9, 0x2d, 0x0b, 0x89, 0xf7, 0x3b, 0x4a, 0xa4, 0xda, 0x4b, 0x89, 0x37, 0x89, 0x38,
	0x19, 0xa1, 0xfe, 0x1a, 0xc9, 0xee, 0x3a, 0xac, 0x99, 0xf1, 0x6a, 0x83, 0x39, 0xb4, 0xa4, 0xbb,
	0x6a, 0x2a, 0x3a, 0x42, 0x1f, 0xc0, 0x46, 0x62, 0xe1, 0xa9, 0xe7, 0xbe, 0x25, 0x8e, 0x34, 0xa1,
	0xf5, 0xe9, 0xc4, 0x43, 0x0e, 0x57, 0x63, 0x51, 0x6d, 0x46, 0x2c, 0xaa, 0xcf, 0x88, 0x45, 0x8d,
	0xf2, 0x58, 0xd4, 0x4c, 0xc7, 0x22, 0xfd, 0xcf, 0x15, 0xd8, 0xcc, 0x58, 0x18, 0xd3, 0x48, 0x32,
	0x7a, 0x56, 0x4a, 0xa2, 0x67, 0x75, 0x06, 0xc7, 0xb5, 0x19, 0x1c, 0xd7, 0xcb, 0x39, 0x6e, 0x64,
	0xa2, 0xa7, 0x62, 0x2a, 0x4d, 0xd5, 0x54, 0x6e, 0x7f, 0xb6, 0x1a, 0xfd, 0x91, 0xe0, 0x84, 0x78,
	0xaf, 0xa8, 0xc9, 0xdf, 0xa1, 0x79, 0x2b, 0x07, 0x65, 0x5a, 0xf4, 0x63, 0xed, 0x52, 0x41, 0xdb,
	0x1e, 0x7d, 0x1b, 0x1a, 0xbc, 0x35, 0x83, 0x2e, 0x24, 0x56, 0x44, 0xcd, 0x1a, 0x2d, 0x09, 0x8c,
	0xb7, 0xdc, 0x81, 0xa6, 0xe8, 0xeb, 0xa2, 0xcd, 0xc4, 0x74, 0xdc, 0xea, 0x2d, 0xda, 0xd4, 0x8e,
	0x5e, 0xc4, 0xd1, 0x7a, 0xaa, 0x57, 0x35, 0xd6, 0x2e, 0xe6, 0x76, 0xaf, 0xd0, 0x5d, 0x58, 0x4e,
	0xb6, 0x8f, 0x24, 0xbd, 0x54, 0x47, 0x29, 0x9f, 0xde, 0x74, 0x2b, 0x6f, 0x02, 0xa9, 0x5b, 0xa3,
	0xbe, 0x50, 0xfe, 0xd6, 0x7b, 0xb0, 0xa2, 0xb4, 0x5a, 0xd0, 0xc5, 0xbc, 0xf6, 0xcb, 0x58, 0xd3,
	0x8a, 0xbb, 0x32, 0xe8, 0x79, 0xaa, 0xf3, 0x1b, 0x35, 0xa3, 0xd1, 0x4e, 0xb6, 0x35, 0x9a, 0x68,
	0x62, 0x6b, 0x7a, 0xd9, 0x74, 0x86, 0x3f, 0xb1, 0x40, 0xe5, 0x2f, 0xce, 0x19, 0x2a, 0x7f, 0xa9,
	0x06, 0x34, 0x86, 0xad, 0x9c, 0xf6, 0x2c, 0x25, 0x3e, 0xda, 0x9b, 0xd1, 0xbb, 0x1d, 0x6b, 0xd7,
	0xe6, 0xea, 0xee, 0xa2, 0xbb, 0x00, 0xd3, 0x2e, 0x88, 0x34, 0x4a, 0xa5, 0x35, 0x24, 0x8d, 0x32,
	0xa7, 0xff, 0xf2, 0x00, 0x56, 0xd5, 0x07, 0x7f, 0xb4, 0x95, 0xdb, 0x05, 0x18, 0x6b, 0xdb, 0x25,
	0xdd, 0x01, 0xf4, 0x11, 0xac, 0xa7, 0x9f, 0xad, 0x51, 0xb7, 0xe0, 0x35, 0x7b, 0xac, 0xed, 0x94,
	0xbe, 0x73, 0xa3, 0x63, 0xd8, 0xc8, 0xbc, 0x4e, 0xa2, 0xcb, 0x45, 0xaf, 0x96, 0x63, 0x6d, 0xb7,
	0xfc, 0x41, 0x13, 0x7d, 0x0c, 0x28, 0xfb, 0xf4, 0x84, 0xb4, 0xc2, 0x37, 0xa9, 0xb1, 0x76, 0x65,
	0xc6, 0x7b, 0x15, 0x7a, 0x98, 0x62, 0xf1, 0x81, 0x45, 0x73, 0x59, 0x94, 0xcf, 0x2a, 0xda, 0x56,
	0xa6, 0xeb, 0xf9, 0xc0, 0x1e, 0x05, 0x21, 0xfa, 0x09, 0x5c, 0xc8, 0x79, 0x0b, 0x40, 0xdb, 0x59,
	0x4c, 0xf1, 0x2b, 0x41, 0x21, 0xae, 0xe7, 0x70, 0x31, 0xf7, 0xa6, 0x29, 0x1d, 0xa1, 0xe8, 0x45,
	0x40, 0x3a, 0x42, 0xf9, 0x25, 0xf8, 0x71, 0x32, 0x59, 0x26, 0x6f, 0x19, 0x68, 0x37, 0x7f, 0x77,
	0x74, 0x05, 0x29, 0xe4, 0xf6, 0xe7, 0x39, 0x18, 0x85, 0x62, 0x76, 0x4b, 0x8b, 0xfd, 0xb1, 0xb6,
	0x3f, 0xc7, 0x65, 0x00, 0xf5, 0xe1, 0x52, 0x41, 0x8d, 0x83, 0xae, 0x94, 0x55, 0x40, 0x65, 0x2c,
	0x0f, 0x61, 0xbb, 0xa4, 0x6e, 0x42, 0xfb, 0xb3, 0x2b, 0xab, 0xb1, 0x76, 0x63, 0xde, 0xf2, 0x8b,
	0x79, 0xa6, 0x5a, 0x46, 0x48, 0xcf, 0xcc, 0xd4, 0x39, 0xd2, 0x33, 0x0b, 0x8a, 0x99, 0x87, 0xc9,
	0xca, 0x48, 0x52, 0x93, 0x96, 0x9a, 0x97, 0xa2, 0x8b, 0x0e, 0x7f, 0xaf, 0xf1, 0xbc, 0x66, 0x61,
	0x73, 0xd0, 0xe4, 0xe0, 0x3b, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x8a, 0x1e, 0x74, 0x94, 0xf0,
	0x27, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PickupServiceClient is the client API for PickupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PickupServiceClient interface {
	// 活动列表
	List(ctx context.Context, in *PickupListReq, opts ...grpc.CallOption) (*PickupListResponse, error)
	// 新增/修改活动
	Store(ctx context.Context, in *PickupStoreReq, opts ...grpc.CallOption) (*PickupResponse, error)
	// 撤销活动
	Delete(ctx context.Context, in *PickupDeleteReq, opts ...grpc.CallOption) (*PickupResponse, error)
	// 自提站点列表
	Stations(ctx context.Context, in *StationsReq, opts ...grpc.CallOption) (*StationsResponse, error)
	// 自提站点新增或保存
	StationStore(ctx context.Context, in *StationStoreReq, opts ...grpc.CallOption) (*PickupResponse, error)
	// 自提站点禁用或启用
	StationPatch(ctx context.Context, in *StationPatchReq, opts ...grpc.CallOption) (*PickupResponse, error)
	// 自提站点详情
	StationDetail(ctx context.Context, in *StationDetailReq, opts ...grpc.CallOption) (*StationDetailResponse, error)
	// 自提点导入模板
	StationImportTemplate(ctx context.Context, in *StationImportTemplateReq, opts ...grpc.CallOption) (*StationImportTemplateResponse, error)
	// 批量导入自提点
	StationImport(ctx context.Context, in *StationImportReq, opts ...grpc.CallOption) (*StationImportResponse, error)
	// 自提点导入历史
	StationImportHistories(ctx context.Context, in *StationImportHistoryListReq, opts ...grpc.CallOption) (*StationImportHistoryListResponse, error)
	// --------以下是小程序的接口-----------
	// 自提站点查询
	AWStations(ctx context.Context, in *AWStationsReq, opts ...grpc.CallOption) (*AWStationsResponse, error)
	// 站点状态查询
	AWStationState(ctx context.Context, in *AWStationStateReq, opts ...grpc.CallOption) (*AWStationStateResponse, error)
	// 最近的站点
	AWStationNearest(ctx context.Context, in *AWStationNearestReq, opts ...grpc.CallOption) (*AWStationNearestResponse, error)
	//社区团购
	//活动列表
	GroupActivityList(ctx context.Context, in *GroupActivityListReq, opts ...grpc.CallOption) (*GroupActivityListResponse, error)
	//活动详情
	GroupActivityQuery(ctx context.Context, in *GroupActivityQueryReq, opts ...grpc.CallOption) (*GroupActivityQueryResponse, error)
	//活动新增编辑
	GroupActivityEdit(ctx context.Context, in *GroupActivityEditReq, opts ...grpc.CallOption) (*empty.Empty, error)
	//活动状态更新
	GroupActivityStatus(ctx context.Context, in *GroupActivityStatusReq, opts ...grpc.CallOption) (*empty.Empty, error)
	//佣金提现列表
	CommissionCashoutList(ctx context.Context, in *CommissionCashoutListReq, opts ...grpc.CallOption) (*CommissionCashoutListResponse, error)
	//佣金提现列表审核\确认打款
	CommissionCashoutAudit(ctx context.Context, in *CommissionCashoutAuditReq, opts ...grpc.CallOption) (*empty.Empty, error)
	//佣金提现列表查看详情
	CommissionCashoutQuery(ctx context.Context, in *CommissionCashoutQueryReq, opts ...grpc.CallOption) (*CommissionCashoutQueryResponse, error)
	//佣金提现导入已打款记录
	CommissionCashoutImport(ctx context.Context, in *CommissionCashoutImportReq, opts ...grpc.CallOption) (*empty.Empty, error)
	//佣金提现查看导入历史
	CommissionCashoutImportList(ctx context.Context, in *CommissionCashoutImportListReq, opts ...grpc.CallOption) (*CommissionCashoutImportListResponse, error)
	//佣金信息
	CommissionInfo(ctx context.Context, in *CommissionInfoReq, opts ...grpc.CallOption) (*CommissionInfoResponse, error)
	//佣金提现申请
	CommissionCashout(ctx context.Context, in *CommissionCashoutReq, opts ...grpc.CallOption) (*empty.Empty, error)
}

type pickupServiceClient struct {
	cc *grpc.ClientConn
}

func NewPickupServiceClient(cc *grpc.ClientConn) PickupServiceClient {
	return &pickupServiceClient{cc}
}

func (c *pickupServiceClient) List(ctx context.Context, in *PickupListReq, opts ...grpc.CallOption) (*PickupListResponse, error) {
	out := new(PickupListResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) Store(ctx context.Context, in *PickupStoreReq, opts ...grpc.CallOption) (*PickupResponse, error) {
	out := new(PickupResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/Store", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) Delete(ctx context.Context, in *PickupDeleteReq, opts ...grpc.CallOption) (*PickupResponse, error) {
	out := new(PickupResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) Stations(ctx context.Context, in *StationsReq, opts ...grpc.CallOption) (*StationsResponse, error) {
	out := new(StationsResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/Stations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationStore(ctx context.Context, in *StationStoreReq, opts ...grpc.CallOption) (*PickupResponse, error) {
	out := new(PickupResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationPatch(ctx context.Context, in *StationPatchReq, opts ...grpc.CallOption) (*PickupResponse, error) {
	out := new(PickupResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationPatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationDetail(ctx context.Context, in *StationDetailReq, opts ...grpc.CallOption) (*StationDetailResponse, error) {
	out := new(StationDetailResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationImportTemplate(ctx context.Context, in *StationImportTemplateReq, opts ...grpc.CallOption) (*StationImportTemplateResponse, error) {
	out := new(StationImportTemplateResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationImportTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationImport(ctx context.Context, in *StationImportReq, opts ...grpc.CallOption) (*StationImportResponse, error) {
	out := new(StationImportResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) StationImportHistories(ctx context.Context, in *StationImportHistoryListReq, opts ...grpc.CallOption) (*StationImportHistoryListResponse, error) {
	out := new(StationImportHistoryListResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/StationImportHistories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) AWStations(ctx context.Context, in *AWStationsReq, opts ...grpc.CallOption) (*AWStationsResponse, error) {
	out := new(AWStationsResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/AWStations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) AWStationState(ctx context.Context, in *AWStationStateReq, opts ...grpc.CallOption) (*AWStationStateResponse, error) {
	out := new(AWStationStateResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/AWStationState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) AWStationNearest(ctx context.Context, in *AWStationNearestReq, opts ...grpc.CallOption) (*AWStationNearestResponse, error) {
	out := new(AWStationNearestResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/AWStationNearest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) GroupActivityList(ctx context.Context, in *GroupActivityListReq, opts ...grpc.CallOption) (*GroupActivityListResponse, error) {
	out := new(GroupActivityListResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/GroupActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) GroupActivityQuery(ctx context.Context, in *GroupActivityQueryReq, opts ...grpc.CallOption) (*GroupActivityQueryResponse, error) {
	out := new(GroupActivityQueryResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/GroupActivityQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) GroupActivityEdit(ctx context.Context, in *GroupActivityEditReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dac.PickupService/GroupActivityEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) GroupActivityStatus(ctx context.Context, in *GroupActivityStatusReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dac.PickupService/GroupActivityStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashoutList(ctx context.Context, in *CommissionCashoutListReq, opts ...grpc.CallOption) (*CommissionCashoutListResponse, error) {
	out := new(CommissionCashoutListResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashoutList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashoutAudit(ctx context.Context, in *CommissionCashoutAuditReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashoutAudit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashoutQuery(ctx context.Context, in *CommissionCashoutQueryReq, opts ...grpc.CallOption) (*CommissionCashoutQueryResponse, error) {
	out := new(CommissionCashoutQueryResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashoutQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashoutImport(ctx context.Context, in *CommissionCashoutImportReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashoutImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashoutImportList(ctx context.Context, in *CommissionCashoutImportListReq, opts ...grpc.CallOption) (*CommissionCashoutImportListResponse, error) {
	out := new(CommissionCashoutImportListResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashoutImportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionInfo(ctx context.Context, in *CommissionInfoReq, opts ...grpc.CallOption) (*CommissionInfoResponse, error) {
	out := new(CommissionInfoResponse)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pickupServiceClient) CommissionCashout(ctx context.Context, in *CommissionCashoutReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dac.PickupService/CommissionCashout", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PickupServiceServer is the server API for PickupService service.
type PickupServiceServer interface {
	// 活动列表
	List(context.Context, *PickupListReq) (*PickupListResponse, error)
	// 新增/修改活动
	Store(context.Context, *PickupStoreReq) (*PickupResponse, error)
	// 撤销活动
	Delete(context.Context, *PickupDeleteReq) (*PickupResponse, error)
	// 自提站点列表
	Stations(context.Context, *StationsReq) (*StationsResponse, error)
	// 自提站点新增或保存
	StationStore(context.Context, *StationStoreReq) (*PickupResponse, error)
	// 自提站点禁用或启用
	StationPatch(context.Context, *StationPatchReq) (*PickupResponse, error)
	// 自提站点详情
	StationDetail(context.Context, *StationDetailReq) (*StationDetailResponse, error)
	// 自提点导入模板
	StationImportTemplate(context.Context, *StationImportTemplateReq) (*StationImportTemplateResponse, error)
	// 批量导入自提点
	StationImport(context.Context, *StationImportReq) (*StationImportResponse, error)
	// 自提点导入历史
	StationImportHistories(context.Context, *StationImportHistoryListReq) (*StationImportHistoryListResponse, error)
	// --------以下是小程序的接口-----------
	// 自提站点查询
	AWStations(context.Context, *AWStationsReq) (*AWStationsResponse, error)
	// 站点状态查询
	AWStationState(context.Context, *AWStationStateReq) (*AWStationStateResponse, error)
	// 最近的站点
	AWStationNearest(context.Context, *AWStationNearestReq) (*AWStationNearestResponse, error)
	//社区团购
	//活动列表
	GroupActivityList(context.Context, *GroupActivityListReq) (*GroupActivityListResponse, error)
	//活动详情
	GroupActivityQuery(context.Context, *GroupActivityQueryReq) (*GroupActivityQueryResponse, error)
	//活动新增编辑
	GroupActivityEdit(context.Context, *GroupActivityEditReq) (*empty.Empty, error)
	//活动状态更新
	GroupActivityStatus(context.Context, *GroupActivityStatusReq) (*empty.Empty, error)
	//佣金提现列表
	CommissionCashoutList(context.Context, *CommissionCashoutListReq) (*CommissionCashoutListResponse, error)
	//佣金提现列表审核\确认打款
	CommissionCashoutAudit(context.Context, *CommissionCashoutAuditReq) (*empty.Empty, error)
	//佣金提现列表查看详情
	CommissionCashoutQuery(context.Context, *CommissionCashoutQueryReq) (*CommissionCashoutQueryResponse, error)
	//佣金提现导入已打款记录
	CommissionCashoutImport(context.Context, *CommissionCashoutImportReq) (*empty.Empty, error)
	//佣金提现查看导入历史
	CommissionCashoutImportList(context.Context, *CommissionCashoutImportListReq) (*CommissionCashoutImportListResponse, error)
	//佣金信息
	CommissionInfo(context.Context, *CommissionInfoReq) (*CommissionInfoResponse, error)
	//佣金提现申请
	CommissionCashout(context.Context, *CommissionCashoutReq) (*empty.Empty, error)
}

// UnimplementedPickupServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPickupServiceServer struct {
}

func (*UnimplementedPickupServiceServer) List(ctx context.Context, req *PickupListReq) (*PickupListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedPickupServiceServer) Store(ctx context.Context, req *PickupStoreReq) (*PickupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Store not implemented")
}
func (*UnimplementedPickupServiceServer) Delete(ctx context.Context, req *PickupDeleteReq) (*PickupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPickupServiceServer) Stations(ctx context.Context, req *StationsReq) (*StationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Stations not implemented")
}
func (*UnimplementedPickupServiceServer) StationStore(ctx context.Context, req *StationStoreReq) (*PickupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationStore not implemented")
}
func (*UnimplementedPickupServiceServer) StationPatch(ctx context.Context, req *StationPatchReq) (*PickupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationPatch not implemented")
}
func (*UnimplementedPickupServiceServer) StationDetail(ctx context.Context, req *StationDetailReq) (*StationDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationDetail not implemented")
}
func (*UnimplementedPickupServiceServer) StationImportTemplate(ctx context.Context, req *StationImportTemplateReq) (*StationImportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationImportTemplate not implemented")
}
func (*UnimplementedPickupServiceServer) StationImport(ctx context.Context, req *StationImportReq) (*StationImportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationImport not implemented")
}
func (*UnimplementedPickupServiceServer) StationImportHistories(ctx context.Context, req *StationImportHistoryListReq) (*StationImportHistoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StationImportHistories not implemented")
}
func (*UnimplementedPickupServiceServer) AWStations(ctx context.Context, req *AWStationsReq) (*AWStationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWStations not implemented")
}
func (*UnimplementedPickupServiceServer) AWStationState(ctx context.Context, req *AWStationStateReq) (*AWStationStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWStationState not implemented")
}
func (*UnimplementedPickupServiceServer) AWStationNearest(ctx context.Context, req *AWStationNearestReq) (*AWStationNearestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWStationNearest not implemented")
}
func (*UnimplementedPickupServiceServer) GroupActivityList(ctx context.Context, req *GroupActivityListReq) (*GroupActivityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupActivityList not implemented")
}
func (*UnimplementedPickupServiceServer) GroupActivityQuery(ctx context.Context, req *GroupActivityQueryReq) (*GroupActivityQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupActivityQuery not implemented")
}
func (*UnimplementedPickupServiceServer) GroupActivityEdit(ctx context.Context, req *GroupActivityEditReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupActivityEdit not implemented")
}
func (*UnimplementedPickupServiceServer) GroupActivityStatus(ctx context.Context, req *GroupActivityStatusReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupActivityStatus not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashoutList(ctx context.Context, req *CommissionCashoutListReq) (*CommissionCashoutListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashoutList not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashoutAudit(ctx context.Context, req *CommissionCashoutAuditReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashoutAudit not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashoutQuery(ctx context.Context, req *CommissionCashoutQueryReq) (*CommissionCashoutQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashoutQuery not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashoutImport(ctx context.Context, req *CommissionCashoutImportReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashoutImport not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashoutImportList(ctx context.Context, req *CommissionCashoutImportListReq) (*CommissionCashoutImportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashoutImportList not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionInfo(ctx context.Context, req *CommissionInfoReq) (*CommissionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionInfo not implemented")
}
func (*UnimplementedPickupServiceServer) CommissionCashout(ctx context.Context, req *CommissionCashoutReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommissionCashout not implemented")
}

func RegisterPickupServiceServer(s *grpc.Server, srv PickupServiceServer) {
	s.RegisterService(&_PickupService_serviceDesc, srv)
}

func _PickupService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PickupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).List(ctx, req.(*PickupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_Store_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PickupStoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).Store(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/Store",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).Store(ctx, req.(*PickupStoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PickupDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).Delete(ctx, req.(*PickupDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_Stations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).Stations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/Stations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).Stations(ctx, req.(*StationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationStoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationStore(ctx, req.(*StationStoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationPatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationPatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationPatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationPatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationPatch(ctx, req.(*StationPatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationDetail(ctx, req.(*StationDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationImportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationImportTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationImportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationImportTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationImportTemplate(ctx, req.(*StationImportTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationImportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationImport(ctx, req.(*StationImportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_StationImportHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StationImportHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).StationImportHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/StationImportHistories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).StationImportHistories(ctx, req.(*StationImportHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_AWStations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWStationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).AWStations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/AWStations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).AWStations(ctx, req.(*AWStationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_AWStationState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWStationStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).AWStationState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/AWStationState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).AWStationState(ctx, req.(*AWStationStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_AWStationNearest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWStationNearestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).AWStationNearest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/AWStationNearest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).AWStationNearest(ctx, req.(*AWStationNearestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_GroupActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupActivityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).GroupActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/GroupActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).GroupActivityList(ctx, req.(*GroupActivityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_GroupActivityQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupActivityQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).GroupActivityQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/GroupActivityQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).GroupActivityQuery(ctx, req.(*GroupActivityQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_GroupActivityEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupActivityEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).GroupActivityEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/GroupActivityEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).GroupActivityEdit(ctx, req.(*GroupActivityEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_GroupActivityStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupActivityStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).GroupActivityStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/GroupActivityStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).GroupActivityStatus(ctx, req.(*GroupActivityStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashoutList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashoutList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashoutList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashoutList(ctx, req.(*CommissionCashoutListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashoutAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutAuditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashoutAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashoutAudit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashoutAudit(ctx, req.(*CommissionCashoutAuditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashoutQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashoutQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashoutQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashoutQuery(ctx, req.(*CommissionCashoutQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashoutImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutImportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashoutImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashoutImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashoutImport(ctx, req.(*CommissionCashoutImportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashoutImportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutImportListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashoutImportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashoutImportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashoutImportList(ctx, req.(*CommissionCashoutImportListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionInfo(ctx, req.(*CommissionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PickupService_CommissionCashout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommissionCashoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PickupServiceServer).CommissionCashout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.PickupService/CommissionCashout",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PickupServiceServer).CommissionCashout(ctx, req.(*CommissionCashoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PickupService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dac.PickupService",
	HandlerType: (*PickupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _PickupService_List_Handler,
		},
		{
			MethodName: "Store",
			Handler:    _PickupService_Store_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PickupService_Delete_Handler,
		},
		{
			MethodName: "Stations",
			Handler:    _PickupService_Stations_Handler,
		},
		{
			MethodName: "StationStore",
			Handler:    _PickupService_StationStore_Handler,
		},
		{
			MethodName: "StationPatch",
			Handler:    _PickupService_StationPatch_Handler,
		},
		{
			MethodName: "StationDetail",
			Handler:    _PickupService_StationDetail_Handler,
		},
		{
			MethodName: "StationImportTemplate",
			Handler:    _PickupService_StationImportTemplate_Handler,
		},
		{
			MethodName: "StationImport",
			Handler:    _PickupService_StationImport_Handler,
		},
		{
			MethodName: "StationImportHistories",
			Handler:    _PickupService_StationImportHistories_Handler,
		},
		{
			MethodName: "AWStations",
			Handler:    _PickupService_AWStations_Handler,
		},
		{
			MethodName: "AWStationState",
			Handler:    _PickupService_AWStationState_Handler,
		},
		{
			MethodName: "AWStationNearest",
			Handler:    _PickupService_AWStationNearest_Handler,
		},
		{
			MethodName: "GroupActivityList",
			Handler:    _PickupService_GroupActivityList_Handler,
		},
		{
			MethodName: "GroupActivityQuery",
			Handler:    _PickupService_GroupActivityQuery_Handler,
		},
		{
			MethodName: "GroupActivityEdit",
			Handler:    _PickupService_GroupActivityEdit_Handler,
		},
		{
			MethodName: "GroupActivityStatus",
			Handler:    _PickupService_GroupActivityStatus_Handler,
		},
		{
			MethodName: "CommissionCashoutList",
			Handler:    _PickupService_CommissionCashoutList_Handler,
		},
		{
			MethodName: "CommissionCashoutAudit",
			Handler:    _PickupService_CommissionCashoutAudit_Handler,
		},
		{
			MethodName: "CommissionCashoutQuery",
			Handler:    _PickupService_CommissionCashoutQuery_Handler,
		},
		{
			MethodName: "CommissionCashoutImport",
			Handler:    _PickupService_CommissionCashoutImport_Handler,
		},
		{
			MethodName: "CommissionCashoutImportList",
			Handler:    _PickupService_CommissionCashoutImportList_Handler,
		},
		{
			MethodName: "CommissionInfo",
			Handler:    _PickupService_CommissionInfo_Handler,
		},
		{
			MethodName: "CommissionCashout",
			Handler:    _PickupService_CommissionCashout_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dac/pickup.proto",
}

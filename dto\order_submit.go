package dto

//下单响应参数
type OrderSubmitResponse struct {
	//状态码
	Code int32 `json:"code"`
	//消息
	Message string `json:"message"`
	//错误信息
	Error string `json:"error"`
	//无法下单商品
	CannotProducts []*CannotSumbitProduct `json:"cannot_products"`
	//提交订单成功后，返回订单ID
	OrderSn string `json:"order_sn"`
}

//无法下单的商品
type CannotSumbitProduct struct {
	//商品skuid
	SkuId string `json:"sku_id"`
	//1:无货 2:失效 3:下架
	Status int32 `json:"status"`
}
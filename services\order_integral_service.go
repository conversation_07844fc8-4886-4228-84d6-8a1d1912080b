package services

import (
	"context"
	"errors"
	"net/http"
	utils2 "order-center/utils"
	"strings"
	"time"

	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// @Desc		阿闻积分服务
// <AUTHOR>
// @Date		2020-08-18
type OrderIntegralService struct{}

// 阿闻完成订单同步
func (p OrderIntegralService) AwHistoryOrderIntegralSync(ctx context.Context, request *oc.HistoryOrderRequest) (*oc.OrderIntegralSynchronizeResponse, error) {
	resp := oc.OrderIntegralSynchronizeResponse{
		Result:  true,
		Message: "SUCCESS",
	}
	go func() {
		defer kit.CatchPanic()

		db := GetDBConn()

		var orders []*models.OrderMain
		// 添加积分
		add := db.Table("order_main").Where("channel_id in (1,9) AND ISNULL(confirm_time)=0")
		if len(request.StartTime) > 0 && len(request.EndTime) > 0 {
			add.And("confirm_time BETWEEN '" + request.StartTime + "' AND '" + request.EndTime + "'")
		} else if len(request.StartTime) > 0 && len(request.EndTime) == 0 {
			add.And("confirm_time >= '" + request.StartTime + "'")
		} else if len(request.StartTime) == 0 && len(request.EndTime) > 0 {
			add.And("confirm_time <= '" + request.EndTime + "'")
		}
		if len(request.OrderSn) > 0 {
			add.And("order_sn IN('" + strings.Join(strings.Split(request.OrderSn, ","), "','") + "')")
		}
		if len(request.MemberTle) > 0 {
			add.And("member_tel = '" + request.MemberTle + "'")
		}
		add.OrderBy("confirm_time asc").Find(&orders)
		for _, model := range orders {
			var queryIntegral models.OrderIntegral
			db.Table("order_integral").Where("order_sn='" + model.OldOrderSn + "'").Get(&queryIntegral)
			if queryIntegral.Id == 0 {
				IntegralOperation(model.OrderSn, true)
			}
		}
		// 退积分
		var refundOrder []*models.RefundOrder
		var builder strings.Builder
		builder.WriteString("channel_id in (1,9) AND refund_state = 3 ")
		builder.WriteString(" AND order_sn IN(SELECT old_order_sn FROM `dc_order`.`order_main` WHERE ISNULL(confirm_time)=0")
		if len(request.StartTime) > 0 && len(request.EndTime) > 0 {
			builder.WriteString(" AND confirm_time BETWEEN '" + request.StartTime + "' AND '" + request.EndTime + "'")
		} else if len(request.StartTime) > 0 && len(request.EndTime) == 0 {
			builder.WriteString(" AND confirm_time >= '" + request.StartTime + "'")
		} else if len(request.StartTime) == 0 && len(request.EndTime) > 0 {
			builder.WriteString(" AND confirm_time <= '" + request.EndTime + "'")
		}
		if len(request.OrderSn) > 0 {
			builder.WriteString(" AND order_sn IN('" + strings.Join(strings.Split(request.OrderSn, ","), "','") + "')")
		}
		if len(request.MemberTle) > 0 {
			builder.WriteString(" AND member_tel = '" + request.MemberTle + "'")
		}
		builder.WriteString(")")
		refund := db.Table("refund_order").Where(builder.String())
		refund.OrderBy("id ASC").Find(&refundOrder)
		for _, model := range refundOrder {
			var queryIntegral models.OrderIntegral
			db.Table("order_integral").Where("order_sn='" + model.RefundSn + "'").Get(&queryIntegral)
			if queryIntegral.Id == 0 {
				IntegralOperation(model.RefundSn, false)
			}
		}
	}()

	return &resp, nil
}

// @Desc            订单积分同步
// <AUTHOR>
// @Date		 	2020-08-27
// @Param<ctx>      Context
// @Param<req>      请求参数
// @Return			请求返回的内容
func (p OrderIntegralService) IntegralSynchronize(ctx context.Context, request *oc.MemberIntegralRecordRequest) (*oc.OrderIntegralSynchronizeResponse, error) {
	glog.Info("订单积分同步参数, ", kit.JsonEncode(request))

	var resp oc.OrderIntegralSynchronizeResponse
	db := GetDBConn()

	var order models.OrderMain
	if result, err := db.Table("order_main").Where("old_order_sn = ?", request.OldOrderSn).Get(&order); err != nil || !result {
		glog.Error("订单积分同步查询原始订单错误 IntegralSynchronize：", kit.JsonEncode(request), err)
		resp.Message = err.Error()
		resp.Result = false
		return &resp, nil
	}
	orderIntegral := models.OrderIntegral{
		OrderSn:       request.OrderSn,            // 订单号
		OldOrderSn:    request.OldOrderSn,         // 原父订单号
		OrderAmount:   int(request.OrderAmount),   // 订单金额
		UserName:      order.MemberName,           // 用户名
		UserTle:       order.MemberTel,            // 用户电话
		EncryptMobile: order.EnMemberTel,          // 用户电话
		ShopName:      order.ShopName,             // 门店名称
		ShopCode:      request.ShopCode,           // 门店编码
		Integral:      int(request.Integral),      // 积分
		CurrentStatus: int(request.CurrentStatus), //当前状态: 0未同步、1已同步
		IntegralType:  int(request.IntegralType),  // 积分类型
	}
	createTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, request.CreateTime, time.Local)
	orderIntegral.CreateTime = createTime // 创建时间

	client := dac.GetDataCenterClient()
	result, _ := client.RPC.QueryStoreInfo(context.Background(), &dac.StoreInfoRequest{
		FinanceCode: strings.Split(orderIntegral.ShopCode, ","),
	})
	if len(result.Details) > 0 {
		for _, d := range result.Details {
			if d.FinanceCode == orderIntegral.ShopCode {
				orderIntegral.ShopCity = d.City
				break
			}
		}
	}
	if rows, err := db.Insert(&orderIntegral); err != nil || rows == 0 {
		glog.Error("订单积分插入数据库失败 IntegralOperation：", orderIntegral, " 错误信息：", err)
		resp.Message = err.Error()
		resp.Result = false
	} else {
		resp.Result = true
	}
	return &resp, nil
}

// @Desc            阿闻积分查询
// <AUTHOR>
// @Date		 	2020-08-18
// @Param<ctx>      Context
// @Param<req>      请求参数
// @Return			请求返回的内容
func (p OrderIntegralService) OrderIntegralQuery(ctx context.Context, request *oc.OrderIntegralQueryRequest) (*oc.OrderIntegralResponse, error) {
	var (
		err  error
		resp oc.OrderIntegralResponse
	)
	db := GetDBConn()

	query := db.Table("order_integral").Where("current_status=1")
	if request.StartTime != "" && request.EndTime != "" {
		query.And("create_time BETWEEN '" + request.StartTime + "' AND '" + request.EndTime + "'")
	} else if request.StartTime != "" && request.EndTime == "" {
		query.And("create_time >= '" + request.StartTime + "'")
	} else if request.StartTime == "" && request.EndTime != "" {
		query.And("create_time <= '" + request.EndTime + "'")
	}
	if request.ShopName != "" {
		query.And("shop_name like '%" + request.ShopName + "%'")
	}
	if request.Code != "" {
		query.And("shop_code IN('" + request.Code + "')")
	}
	if request.City != "" {
		query.And("shop_city like '%" + request.City + "%'")
	}
	if request.MobileTel != "" {
		query.And("encrypt_mobile = ?", utils2.MobileEncrypt(request.MobileTel))
	}
	if request.UserName != "" {
		query.And("user_name like '%" + request.UserName + "%'")
	}
	// 获取总条数
	queryTotalCount := *query
	if resp.Totalcount, err = queryTotalCount.Count(); err != nil {
		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		return &resp, err
	}
	// 统计积分
	queryTotalIntegral := *query
	var SumIntegral struct{}
	if resp.Totalintegral, err = queryTotalIntegral.SumInt(&SumIntegral, "CASE WHEN integral_type%2 = 1 THEN integral ELSE -integral END"); err != nil {
		resp.Totalcount = 0
		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		return &resp, err
	}
	if request.PageIndex > 0 && request.PageSize > 0 {
		query.Limit(int(request.PageSize), int((request.PageIndex*request.PageSize)-request.PageSize))
	}
	// 查询分页数据
	queryField := "create_time AS Ordertime,order_sn AS Orderon,old_order_sn AS Oldordersn,CONCAT_WS('-',user_name,user_tle) AS Userinfo,shop_name AS Shopname"
	queryField += ",shop_code AS `Code`,shop_city AS City,CASE WHEN integral_type%2 = 1 THEN integral ELSE -integral END AS Integral"
	if err = query.Select(queryField).OrderBy("create_time desc").Find(&resp.Data); err != nil {
		resp.Totalcount = 0
		resp.Totalintegral = 0
		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		return &resp, err
	}
	resp.Message = "SUCCESS"
	resp.Code = http.StatusOK

	for _, value := range resp.Data {
		userArry := strings.Split(value.Userinfo, "-")
		if len(userArry) < 2 || len(userArry[len(userArry)-1]) < 11 {
			continue
		}
		userArry[len(userArry)-1] = userArry[len(userArry)-1][:3] + "****" + userArry[len(userArry)-1][7:]
		value.Userinfo = strings.Join(userArry, "-")
	}

	return &resp, nil
}

//驳回退款，帮客户回退退款金额。后续改成退款申请冻结积分，则不需要该方法了。
func (s *OrderIntegralService) RecoverUserIntegral(ctx context.Context, req *oc.RecoverUserIntegralReq) (*oc.BaseRes, error) {

	out := &oc.BaseRes{
		Code: 400,
	}
	if req.OrderSn == "" {
		out.Message = "订单号不能为空"
		return out, errors.New("参数不合法")
	}
	if req.RefundOrderSn == "" {
		out.Message = "退款订单号不能为空"
		return out, errors.New("参数不合法")
	}
	if req.MemberId == "" {
		out.Message = "用户不能为空"
		return out, errors.New("参数不合法")
	}
	//业务逻辑开始
	integralOrderId := "1-" + req.OrderSn
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()
	//1、查询是否存在
	var memberIntegralRecord models.MemberIntegralRecord
	sql := `SELECT integralid,orderid,memberid,payamount,integralcount,surplusintegralcount,integraltype,integralreason,ischeck,createdate,lasttime FROM datacenter.member_integral_record 
		WHERE memberid = ? AND orderid=? AND MOD(integraltype,2) = 0 AND integralreason LIKE ? ORDER BY createdate DESC;`
	_, err := session.SQL(sql, req.MemberId, integralOrderId, "%:"+req.RefundOrderSn).Get(&memberIntegralRecord)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}
	if memberIntegralRecord.Integralcount == 0 {
		out.Message = "该退款单的积分额度为0或未查到有效的退款积分单，不允许退款"
		return out, err
	}
	//2、将申请的退款进行驳回

	var memberIntegralRecords []models.MemberIntegralRecord
	sql = `SELECT integralid,orderid,memberid,payamount,integralcount,surplusintegralcount,integraltype,integralreason,ischeck,createdate,lasttime FROM datacenter.member_integral_record 
	WHERE memberid = ? AND  MOD(integraltype,2) = 1	AND orderid = ? AND (integralcount - surplusintegralcount) >0  ORDER BY createdate DESC`
	err = session.SQL(sql, req.MemberId, integralOrderId).Find(&memberIntegralRecords)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}
	if len(memberIntegralRecords) == 0 {
		out.Message = "没有需要恢复的积分记录"
		return out, err
	}
	//3、处理数据
	err = session.Begin()
	if err != nil {
		out.Message = err.Error()
		return out, err
	}
	//增加总积分
	memberIntegralCount := memberIntegralRecord.Integralcount
	sql = "UPDATE datacenter.member_integral_info SET integral = integral+? WHERE memberid = ? and org_id=?;"
	session.Exec(sql, memberIntegralCount, req.MemberId, req.OrgId)
	//删除需要退款的积分记录
	sql = "DELETE FROM datacenter.member_integral_record WHERE integralid=?;"
	session.Exec(sql, memberIntegralRecord.Integralid)
	//补积分到数据上去
	for _, v := range memberIntegralRecords {
		if memberIntegralRecord.Integralcount > 0 {
			//计算每一张单据需要补录的积分数量
			needIntegralCount := v.Integralcount - v.Surplusintegralcount
			//是否超出需要补录的积分数量
			if memberIntegralRecord.Integralcount-needIntegralCount < 0 {
				needIntegralCount = memberIntegralRecord.Integralcount
			} else {
				needIntegralCount = v.Integralcount - v.Surplusintegralcount
			}
			sql = "UPDATE datacenter.member_integral_record SET surplusintegralcount = surplusintegralcount + ? WHERE integralid = ?;"
			session.Exec(sql, needIntegralCount, v.Integralid)
		}
	}
	err = session.Commit()
	if err != nil {
		session.Rollback()
		out.Message = err.Error()
		return out, err
	}
	out.Code = 200
	return out, nil
}

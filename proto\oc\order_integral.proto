syntax = "proto3";
package oc;

// @Desc    	订单积分服务
// <AUTHOR>
// @Date		2020-08-18
service OrderIntegralService {
  // @Desc    	订单积分查询
  // <AUTHOR>
  // @Date		2020-08-18
  rpc OrderIntegralQuery(OrderIntegralQueryRequest) returns (OrderIntegralResponse) {}

  // @Desc    	积分同步
  // <AUTHOR>
  // @Date		2020-08-27
  rpc IntegralSynchronize(MemberIntegralRecordRequest) returns (OrderIntegralSynchronizeResponse) {}

  // @Desc    	阿闻历史订单积分同步
  // <AUTHOR>
  // @Date		2020-08-18
  rpc AwHistoryOrderIntegralSync(HistoryOrderRequest) returns (OrderIntegralSynchronizeResponse) {}
}

// 积分处理通知
message IntegralNotify{
	// 父订单
  string Oldordersn = 1;
	// 订单号
  string Ordersn = 2;
	// 积分类型
  int32  Integraltype = 3;
	// 通知创建时间
  string Notifytime = 4;
	// 订单来源
  int32  Orderfrom = 5;
	// 买家手机号
  string Mobile = 6;
	// 退款单号
  string Refundsn = 7;
	// 父订单总金额
	int32  TotalAmount  = 8;
	// 退款金额
	int32  RefundAmount = 9;
	// 用户编号
	string MemberId     = 10;
	// 门店编码
	string ShopCode     = 11;
	// 本次加/减积分的数量
	int32 integral = 12;
	// 本次加/减积分的数量
	int32 OrgId = 13;
}

// @Desc    	积分查询请求
// <AUTHOR>
// @Date		2020-08-18
message OrderIntegralQueryRequest{
	string StartTime   = 1;       // 起始时间
	string EndTime     = 2;       // 结束时间
	string ShopName    = 3;       // 门店名称
	string Code        = 4;       // 财务编码
	string City        = 5;       // 城市
	string MobileTel   = 6;       // 手机号码
	string UserName    = 7;       // 用户姓名
	int32  PageIndex   = 8;       // 分页索引
    int32  PageSize    = 9;       // 分页大小
}

// @Desc    	积分导出请求
// <AUTHOR>
// @Date		2020-08-18
message OrderIntegralExportRequest{
	string StartTime   = 1;       // 起始时间
	string EndTime     = 2;       // 结束时间
	string ShopName    = 3;       // 门店名称
	string Code        = 4;       // 财务编码
	string City        = 5;       // 城市
	string MobileTel   = 6;       // 手机号码
	string UserName    = 7;       // 用户姓名
}

// @Desc    	积分响应
// <AUTHOR>
// @Date		2020-08-18
message OrderIntegralResponse{
    int32   code            = 1;    // 状态码
    string  message         = 2;    // 消息
    int64   totalcount      = 3;    // 总数
    int64   totalintegral   = 4;    // 总积分
    repeated OrderIntegralResponseData data = 5;	// 结果
}

// @Desc    	积分查询响应结果
// <AUTHOR>
// @Date		2020-08-18
message OrderIntegralResponseData{
    string ordertime    = 1;    // 下单时间
    string orderon      = 2;    // 订单编号
    string oldordersn   = 3;    // 原电商父订单号
    string userinfo     = 4;    // 用户信息
    string shopname     = 5;    // 门店名称
    string code         = 6;    // 财务编码
    string city         = 7;    // 城市
    int64  integral     = 8;    // 积分
}

// @Desc    	订单积分同步请求
// <AUTHOR>
// @Date		2020-08-27
message MemberIntegralRecordRequest{
	string  OrderSn         = 1;    // 订单号
	int32   OrderAmount     = 2;    // 订单金额
	string  OldOrderSn      = 3;    // 原父订单号
	string  ShopCode        = 4;    // 门店编码
	int32   Integral        = 5;    // 积分
	int32   CurrentStatus   = 6;    // 当前状态: 0未同步、1已同步
	int32   IntegralType    = 7;    // 积分类型
	string  CreateTime      = 8;    // 创建时间
}

// @Desc    	订单积分同步响应
// <AUTHOR>
// @Date		2020-08-27
message OrderIntegralSynchronizeResponse{
    string  Message         = 1;    // 消息
    bool    Result          = 2;    // 结果
}

// @Desc    	历史订单请求
// <AUTHOR>
// @Date		2020-08-27
message HistoryOrderRequest{
	string StartTime   = 1;       // 起始时间
	string EndTime     = 2;       // 结束时间
	string OrderSn     = 3;       // 订单号
	string MemberTle   = 4;      // 用户手机号码
}
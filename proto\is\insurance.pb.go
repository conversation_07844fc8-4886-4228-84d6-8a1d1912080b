// Code generated by protoc-gen-go. DO NOT EDIT.
// source: is/insurance.proto

package is

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type InsuranceOnLineRequest struct {
	//外部业务流水号
	BusinessNo string `protobuf:"bytes,1,opt,name=businessNo,proto3" json:"businessNo"`
	//保单号
	PolicyNo string `protobuf:"bytes,2,opt,name=policy_no,json=policyNo,proto3" json:"policy_no"`
	//支付申请号
	PayApplyNo string `protobuf:"bytes,3,opt,name=pay_apply_no,json=payApplyNo,proto3" json:"pay_apply_no"`
	//支付金额
	PayAmount int32 `protobuf:"varint,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付时间
	PayTime string `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//电子保单地址
	ElecPolicyUrl string `protobuf:"bytes,6,opt,name=elec_policy_url,json=elecPolicyUrl,proto3" json:"elec_policy_url"`
	//总保额
	TotalInsuredAmt int32 `protobuf:"varint,7,opt,name=total_insured_amt,json=totalInsuredAmt,proto3" json:"total_insured_amt"`
	//实付总保费
	ActualPremiumAmt int32 `protobuf:"varint,8,opt,name=ActualPremiumAmt,proto3" json:"ActualPremiumAmt"`
	//保险起始时间
	InsuredBgnTime string `protobuf:"bytes,9,opt,name=InsuredBgnTime,proto3" json:"InsuredBgnTime"`
	//保险截止时间
	InsuredEndTime string `protobuf:"bytes,10,opt,name=InsuredEndTime,proto3" json:"InsuredEndTime"`
	//投保人
	Holder *Holder `protobuf:"bytes,11,opt,name=holder,proto3" json:"holder"`
	//被保人
	Insureds             []*Insureds `protobuf:"bytes,12,rep,name=insureds,proto3" json:"insureds"`
	Target               *Target     `protobuf:"bytes,13,opt,name=target,proto3" json:"target"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InsuranceOnLineRequest) Reset()         { *m = InsuranceOnLineRequest{} }
func (m *InsuranceOnLineRequest) String() string { return proto.CompactTextString(m) }
func (*InsuranceOnLineRequest) ProtoMessage()    {}
func (*InsuranceOnLineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{0}
}

func (m *InsuranceOnLineRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceOnLineRequest.Unmarshal(m, b)
}
func (m *InsuranceOnLineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceOnLineRequest.Marshal(b, m, deterministic)
}
func (m *InsuranceOnLineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceOnLineRequest.Merge(m, src)
}
func (m *InsuranceOnLineRequest) XXX_Size() int {
	return xxx_messageInfo_InsuranceOnLineRequest.Size(m)
}
func (m *InsuranceOnLineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceOnLineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceOnLineRequest proto.InternalMessageInfo

func (m *InsuranceOnLineRequest) GetBusinessNo() string {
	if m != nil {
		return m.BusinessNo
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetPolicyNo() string {
	if m != nil {
		return m.PolicyNo
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetPayApplyNo() string {
	if m != nil {
		return m.PayApplyNo
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *InsuranceOnLineRequest) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetElecPolicyUrl() string {
	if m != nil {
		return m.ElecPolicyUrl
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetTotalInsuredAmt() int32 {
	if m != nil {
		return m.TotalInsuredAmt
	}
	return 0
}

func (m *InsuranceOnLineRequest) GetActualPremiumAmt() int32 {
	if m != nil {
		return m.ActualPremiumAmt
	}
	return 0
}

func (m *InsuranceOnLineRequest) GetInsuredBgnTime() string {
	if m != nil {
		return m.InsuredBgnTime
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetInsuredEndTime() string {
	if m != nil {
		return m.InsuredEndTime
	}
	return ""
}

func (m *InsuranceOnLineRequest) GetHolder() *Holder {
	if m != nil {
		return m.Holder
	}
	return nil
}

func (m *InsuranceOnLineRequest) GetInsureds() []*Insureds {
	if m != nil {
		return m.Insureds
	}
	return nil
}

func (m *InsuranceOnLineRequest) GetTarget() *Target {
	if m != nil {
		return m.Target
	}
	return nil
}

//请求中报返回结果
type YangZiJiangResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	Results              []*YangZiJiangResults `protobuf:"bytes,4,rep,name=results,proto3" json:"results"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *YangZiJiangResponse) Reset()         { *m = YangZiJiangResponse{} }
func (m *YangZiJiangResponse) String() string { return proto.CompactTextString(m) }
func (*YangZiJiangResponse) ProtoMessage()    {}
func (*YangZiJiangResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{1}
}

func (m *YangZiJiangResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YangZiJiangResponse.Unmarshal(m, b)
}
func (m *YangZiJiangResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YangZiJiangResponse.Marshal(b, m, deterministic)
}
func (m *YangZiJiangResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YangZiJiangResponse.Merge(m, src)
}
func (m *YangZiJiangResponse) XXX_Size() int {
	return xxx_messageInfo_YangZiJiangResponse.Size(m)
}
func (m *YangZiJiangResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_YangZiJiangResponse.DiscardUnknown(m)
}

var xxx_messageInfo_YangZiJiangResponse proto.InternalMessageInfo

func (m *YangZiJiangResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *YangZiJiangResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *YangZiJiangResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *YangZiJiangResponse) GetResults() []*YangZiJiangResults {
	if m != nil {
		return m.Results
	}
	return nil
}

//请求中报返回结果
type YangZiJiangResults struct {
	//投保单号
	PolicyAppNo string `protobuf:"bytes,1,opt,name=policy_app_no,json=policyAppNo,proto3" json:"policy_app_no"`
	//保单号
	PolicyNo string `protobuf:"bytes,2,opt,name=policy_no,json=policyNo,proto3" json:"policy_no"`
	//电子保单地址
	ElecAddr             string   `protobuf:"bytes,3,opt,name=elec_addr,json=elecAddr,proto3" json:"elec_addr"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YangZiJiangResults) Reset()         { *m = YangZiJiangResults{} }
func (m *YangZiJiangResults) String() string { return proto.CompactTextString(m) }
func (*YangZiJiangResults) ProtoMessage()    {}
func (*YangZiJiangResults) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{2}
}

func (m *YangZiJiangResults) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YangZiJiangResults.Unmarshal(m, b)
}
func (m *YangZiJiangResults) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YangZiJiangResults.Marshal(b, m, deterministic)
}
func (m *YangZiJiangResults) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YangZiJiangResults.Merge(m, src)
}
func (m *YangZiJiangResults) XXX_Size() int {
	return xxx_messageInfo_YangZiJiangResults.Size(m)
}
func (m *YangZiJiangResults) XXX_DiscardUnknown() {
	xxx_messageInfo_YangZiJiangResults.DiscardUnknown(m)
}

var xxx_messageInfo_YangZiJiangResults proto.InternalMessageInfo

func (m *YangZiJiangResults) GetPolicyAppNo() string {
	if m != nil {
		return m.PolicyAppNo
	}
	return ""
}

func (m *YangZiJiangResults) GetPolicyNo() string {
	if m != nil {
		return m.PolicyNo
	}
	return ""
}

func (m *YangZiJiangResults) GetElecAddr() string {
	if m != nil {
		return m.ElecAddr
	}
	return ""
}

//扬子江投保请求数据
type YangZiJiangRequest struct {
	//渠道信息
	SaleInfo *SaleInfo `protobuf:"bytes,1,opt,name=saleInfo,proto3" json:"saleInfo"`
	//产品信息
	Product *Product `protobuf:"bytes,2,opt,name=product,proto3" json:"product"`
	//投保信息
	Policy *Policy `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy"`
	//投保人
	Holder *Holder `protobuf:"bytes,4,opt,name=holder,proto3" json:"holder"`
	//被保人
	Insureds []*Insureds `protobuf:"bytes,5,rep,name=insureds,proto3" json:"insureds"`
	//MAP数据结构，数据数据参考yangzijiangdto 里面的 Target
	Target *Target `protobuf:"bytes,6,opt,name=target,proto3" json:"target"`
	//商品的SKUID，因为现在的保险期限，保额等都是写死的，需要SKUID来匹配
	Skuid string `protobuf:"bytes,7,opt,name=skuid,proto3" json:"skuid"`
	//内部订单号
	OrderId              string   `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YangZiJiangRequest) Reset()         { *m = YangZiJiangRequest{} }
func (m *YangZiJiangRequest) String() string { return proto.CompactTextString(m) }
func (*YangZiJiangRequest) ProtoMessage()    {}
func (*YangZiJiangRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{3}
}

func (m *YangZiJiangRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YangZiJiangRequest.Unmarshal(m, b)
}
func (m *YangZiJiangRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YangZiJiangRequest.Marshal(b, m, deterministic)
}
func (m *YangZiJiangRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YangZiJiangRequest.Merge(m, src)
}
func (m *YangZiJiangRequest) XXX_Size() int {
	return xxx_messageInfo_YangZiJiangRequest.Size(m)
}
func (m *YangZiJiangRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_YangZiJiangRequest.DiscardUnknown(m)
}

var xxx_messageInfo_YangZiJiangRequest proto.InternalMessageInfo

func (m *YangZiJiangRequest) GetSaleInfo() *SaleInfo {
	if m != nil {
		return m.SaleInfo
	}
	return nil
}

func (m *YangZiJiangRequest) GetProduct() *Product {
	if m != nil {
		return m.Product
	}
	return nil
}

func (m *YangZiJiangRequest) GetPolicy() *Policy {
	if m != nil {
		return m.Policy
	}
	return nil
}

func (m *YangZiJiangRequest) GetHolder() *Holder {
	if m != nil {
		return m.Holder
	}
	return nil
}

func (m *YangZiJiangRequest) GetInsureds() []*Insureds {
	if m != nil {
		return m.Insureds
	}
	return nil
}

func (m *YangZiJiangRequest) GetTarget() *Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *YangZiJiangRequest) GetSkuid() string {
	if m != nil {
		return m.Skuid
	}
	return ""
}

func (m *YangZiJiangRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

//渠道信息
type SaleInfo struct {
	//接入用户
	AccessUser string `protobuf:"bytes,1,opt,name=access_user,json=accessUser,proto3" json:"access_user"`
	//渠道编码
	ChannelCode string `protobuf:"bytes,2,opt,name=channelCode,proto3" json:"channelCode"`
	//接入密码
	AccessPassword       string   `protobuf:"bytes,3,opt,name=accessPassword,proto3" json:"accessPassword"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaleInfo) Reset()         { *m = SaleInfo{} }
func (m *SaleInfo) String() string { return proto.CompactTextString(m) }
func (*SaleInfo) ProtoMessage()    {}
func (*SaleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{4}
}

func (m *SaleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaleInfo.Unmarshal(m, b)
}
func (m *SaleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaleInfo.Marshal(b, m, deterministic)
}
func (m *SaleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaleInfo.Merge(m, src)
}
func (m *SaleInfo) XXX_Size() int {
	return xxx_messageInfo_SaleInfo.Size(m)
}
func (m *SaleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SaleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SaleInfo proto.InternalMessageInfo

func (m *SaleInfo) GetAccessUser() string {
	if m != nil {
		return m.AccessUser
	}
	return ""
}

func (m *SaleInfo) GetChannelCode() string {
	if m != nil {
		return m.ChannelCode
	}
	return ""
}

func (m *SaleInfo) GetAccessPassword() string {
	if m != nil {
		return m.AccessPassword
	}
	return ""
}

//产品信息
type Product struct {
	//产品代码
	ProductCode string `protobuf:"bytes,1,opt,name=product_code,json=productCode,proto3" json:"product_code"`
	//套餐代码
	PackageCode string `protobuf:"bytes,2,opt,name=package_code,json=packageCode,proto3" json:"package_code"`
	//保司编码
	InsurCode            string   `protobuf:"bytes,3,opt,name=insur_code,json=insurCode,proto3" json:"insur_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Product) Reset()         { *m = Product{} }
func (m *Product) String() string { return proto.CompactTextString(m) }
func (*Product) ProtoMessage()    {}
func (*Product) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{5}
}

func (m *Product) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Product.Unmarshal(m, b)
}
func (m *Product) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Product.Marshal(b, m, deterministic)
}
func (m *Product) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Product.Merge(m, src)
}
func (m *Product) XXX_Size() int {
	return xxx_messageInfo_Product.Size(m)
}
func (m *Product) XXX_DiscardUnknown() {
	xxx_messageInfo_Product.DiscardUnknown(m)
}

var xxx_messageInfo_Product proto.InternalMessageInfo

func (m *Product) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *Product) GetPackageCode() string {
	if m != nil {
		return m.PackageCode
	}
	return ""
}

func (m *Product) GetInsurCode() string {
	if m != nil {
		return m.InsurCode
	}
	return ""
}

//投保信息
type Policy struct {
	//外部业务流水号
	BusinessNo string `protobuf:"bytes,1,opt,name=BusinessNo,proto3" json:"BusinessNo"`
	//实付总保费
	ActualPremiumAmt int32 `protobuf:"varint,2,opt,name=ActualPremiumAmt,proto3" json:"ActualPremiumAmt"`
	//投保份数
	InsuredQuantity int32 `protobuf:"varint,3,opt,name=InsuredQuantity,proto3" json:"InsuredQuantity"`
	//原始总保费
	OriginalPremiumAmt int32 `protobuf:"varint,4,opt,name=OriginalPremiumAmt,proto3" json:"OriginalPremiumAmt"`
	//总保额
	TotalInsuredAmt int32 `protobuf:"varint,5,opt,name=TotalInsuredAmt,proto3" json:"TotalInsuredAmt"`
	//保险起始时间
	InsuredBgnTime string `protobuf:"bytes,6,opt,name=InsuredBgnTime,proto3" json:"InsuredBgnTime"`
	//保险截止时间
	InsuredEndTime string `protobuf:"bytes,7,opt,name=InsuredEndTime,proto3" json:"InsuredEndTime"`
	//投保时间
	AppTime              string   `protobuf:"bytes,8,opt,name=AppTime,proto3" json:"AppTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Policy) Reset()         { *m = Policy{} }
func (m *Policy) String() string { return proto.CompactTextString(m) }
func (*Policy) ProtoMessage()    {}
func (*Policy) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{6}
}

func (m *Policy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Policy.Unmarshal(m, b)
}
func (m *Policy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Policy.Marshal(b, m, deterministic)
}
func (m *Policy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Policy.Merge(m, src)
}
func (m *Policy) XXX_Size() int {
	return xxx_messageInfo_Policy.Size(m)
}
func (m *Policy) XXX_DiscardUnknown() {
	xxx_messageInfo_Policy.DiscardUnknown(m)
}

var xxx_messageInfo_Policy proto.InternalMessageInfo

func (m *Policy) GetBusinessNo() string {
	if m != nil {
		return m.BusinessNo
	}
	return ""
}

func (m *Policy) GetActualPremiumAmt() int32 {
	if m != nil {
		return m.ActualPremiumAmt
	}
	return 0
}

func (m *Policy) GetInsuredQuantity() int32 {
	if m != nil {
		return m.InsuredQuantity
	}
	return 0
}

func (m *Policy) GetOriginalPremiumAmt() int32 {
	if m != nil {
		return m.OriginalPremiumAmt
	}
	return 0
}

func (m *Policy) GetTotalInsuredAmt() int32 {
	if m != nil {
		return m.TotalInsuredAmt
	}
	return 0
}

func (m *Policy) GetInsuredBgnTime() string {
	if m != nil {
		return m.InsuredBgnTime
	}
	return ""
}

func (m *Policy) GetInsuredEndTime() string {
	if m != nil {
		return m.InsuredEndTime
	}
	return ""
}

func (m *Policy) GetAppTime() string {
	if m != nil {
		return m.AppTime
	}
	return ""
}

//投保人
type Holder struct {
	//投保人名称
	HolderName string `protobuf:"bytes,1,opt,name=HolderName,proto3" json:"HolderName"`
	//证件类型
	IdcartType string `protobuf:"bytes,2,opt,name=IdcartType,proto3" json:"IdcartType"`
	//证件号码
	IdcartNo string `protobuf:"bytes,3,opt,name=IdcartNo,proto3" json:"IdcartNo"`
	//投保人类型  1：个人  2：团体
	HolderType string `protobuf:"bytes,4,opt,name=HolderType,proto3" json:"HolderType"`
	//手机号
	Mobile string `protobuf:"bytes,5,opt,name=Mobile,proto3" json:"Mobile"`
	//出生日期
	BornDate string `protobuf:"bytes,6,opt,name=BornDate,proto3" json:"BornDate"`
	//性别
	//M	男
	//F	女
	Sex string `protobuf:"bytes,7,opt,name=Sex,proto3" json:"Sex"`
	//固话
	Telphone string `protobuf:"bytes,8,opt,name=Telphone,proto3" json:"Telphone"`
	//邮箱
	Mail                 string   `protobuf:"bytes,9,opt,name=Mail,proto3" json:"Mail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Holder) Reset()         { *m = Holder{} }
func (m *Holder) String() string { return proto.CompactTextString(m) }
func (*Holder) ProtoMessage()    {}
func (*Holder) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{7}
}

func (m *Holder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Holder.Unmarshal(m, b)
}
func (m *Holder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Holder.Marshal(b, m, deterministic)
}
func (m *Holder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Holder.Merge(m, src)
}
func (m *Holder) XXX_Size() int {
	return xxx_messageInfo_Holder.Size(m)
}
func (m *Holder) XXX_DiscardUnknown() {
	xxx_messageInfo_Holder.DiscardUnknown(m)
}

var xxx_messageInfo_Holder proto.InternalMessageInfo

func (m *Holder) GetHolderName() string {
	if m != nil {
		return m.HolderName
	}
	return ""
}

func (m *Holder) GetIdcartType() string {
	if m != nil {
		return m.IdcartType
	}
	return ""
}

func (m *Holder) GetIdcartNo() string {
	if m != nil {
		return m.IdcartNo
	}
	return ""
}

func (m *Holder) GetHolderType() string {
	if m != nil {
		return m.HolderType
	}
	return ""
}

func (m *Holder) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *Holder) GetBornDate() string {
	if m != nil {
		return m.BornDate
	}
	return ""
}

func (m *Holder) GetSex() string {
	if m != nil {
		return m.Sex
	}
	return ""
}

func (m *Holder) GetTelphone() string {
	if m != nil {
		return m.Telphone
	}
	return ""
}

func (m *Holder) GetMail() string {
	if m != nil {
		return m.Mail
	}
	return ""
}

//被保人
type Insureds struct {
	//投保人名称
	InsuredName string `protobuf:"bytes,1,opt,name=InsuredName,proto3" json:"InsuredName"`
	//证件类型
	// 01	身份证
	//02	护照
	//03	军人证
	//04	学生证
	//05	台胞证
	//06	港澳返乡证
	//07	出生证
	//08	出生日期（未成年人使用）
	//09	统一社会信用代码
	//13	纳税人识别号
	//14	外国人永久居留身份证
	//99	其他
	IdcartType string `protobuf:"bytes,2,opt,name=IdcartType,proto3" json:"IdcartType"`
	//证件号码
	IdcartNo string `protobuf:"bytes,3,opt,name=IdcartNo,proto3" json:"IdcartNo"`
	//被保人类型 1：个人  2：团体
	InsuredType string `protobuf:"bytes,4,opt,name=InsuredType,proto3" json:"InsuredType"`
	//出生日期
	BornDate string `protobuf:"bytes,5,opt,name=BornDate,proto3" json:"BornDate"`
	//性别
	//M	男
	//F	女
	Sex string `protobuf:"bytes,6,opt,name=Sex,proto3" json:"Sex"`
	//被保人与投保人关系
	//0	本人
	//1	配偶
	//2	父母
	//3	子女
	//4	兄弟姐妹
	//5	雇佣
	//6	监护人
	//7	被监护人
	//8	抚养
	//9	赡养
	//10	朋友
	//11	亲属
	//12	法定继承人
	//13	身故受益人
	//14	其他
	RelationWithHolder string `protobuf:"bytes,7,opt,name=RelationWithHolder,proto3" json:"RelationWithHolder"`
	//手机号 TE
	Mobile               string   `protobuf:"bytes,8,opt,name=Mobile,proto3" json:"Mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Insureds) Reset()         { *m = Insureds{} }
func (m *Insureds) String() string { return proto.CompactTextString(m) }
func (*Insureds) ProtoMessage()    {}
func (*Insureds) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{8}
}

func (m *Insureds) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Insureds.Unmarshal(m, b)
}
func (m *Insureds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Insureds.Marshal(b, m, deterministic)
}
func (m *Insureds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Insureds.Merge(m, src)
}
func (m *Insureds) XXX_Size() int {
	return xxx_messageInfo_Insureds.Size(m)
}
func (m *Insureds) XXX_DiscardUnknown() {
	xxx_messageInfo_Insureds.DiscardUnknown(m)
}

var xxx_messageInfo_Insureds proto.InternalMessageInfo

func (m *Insureds) GetInsuredName() string {
	if m != nil {
		return m.InsuredName
	}
	return ""
}

func (m *Insureds) GetIdcartType() string {
	if m != nil {
		return m.IdcartType
	}
	return ""
}

func (m *Insureds) GetIdcartNo() string {
	if m != nil {
		return m.IdcartNo
	}
	return ""
}

func (m *Insureds) GetInsuredType() string {
	if m != nil {
		return m.InsuredType
	}
	return ""
}

func (m *Insureds) GetBornDate() string {
	if m != nil {
		return m.BornDate
	}
	return ""
}

func (m *Insureds) GetSex() string {
	if m != nil {
		return m.Sex
	}
	return ""
}

func (m *Insureds) GetRelationWithHolder() string {
	if m != nil {
		return m.RelationWithHolder
	}
	return ""
}

func (m *Insureds) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

//宠物等信息
type Target struct {
	//养犬许可证号码
	DogLicenseCode string `protobuf:"bytes,1,opt,name=DogLicenseCode,proto3" json:"DogLicenseCode"`
	//犬类免疫证号码
	ImmunityCertifiCode string `protobuf:"bytes,2,opt,name=ImmunityCertifiCode,proto3" json:"ImmunityCertifiCode"`
	//宠物犬种类
	PetDogBreed string `protobuf:"bytes,3,opt,name=PetDogBreed,proto3" json:"PetDogBreed"`
	//所在城市
	HouseCity string `protobuf:"bytes,4,opt,name=HouseCity,proto3" json:"HouseCity"`
	//详细地址
	HouseAddress string `protobuf:"bytes,5,opt,name=HouseAddress,proto3" json:"HouseAddress"`
	//宠物名称
	PetName string `protobuf:"bytes,6,opt,name=PetName,proto3" json:"PetName"`
	//1-猫，2-犬
	Category string `protobuf:"bytes,7,opt,name=Category,proto3" json:"Category"`
	//小的分类名称 种类
	CategoryName string `protobuf:"bytes,8,opt,name=CategoryName,proto3" json:"CategoryName"`
	//出生日期
	Birthday string `protobuf:"bytes,9,opt,name=Birthday,proto3" json:"Birthday"`
	//宠物性别
	Gender string `protobuf:"bytes,10,opt,name=Gender,proto3" json:"Gender"`
	//是否绝育
	Sterilization bool `protobuf:"varint,11,opt,name=Sterilization,proto3" json:"Sterilization"`
	//是否免疫
	Immune bool `protobuf:"varint,12,opt,name=Immune,proto3" json:"Immune"`
	//投保照片ZIP压缩包的base64格式
	Base64Str string `protobuf:"bytes,13,opt,name=Base64Str,proto3" json:"Base64Str"`
	//业务模式
	BizMode              int32    `protobuf:"varint,14,opt,name=BizMode,proto3" json:"BizMode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Target) Reset()         { *m = Target{} }
func (m *Target) String() string { return proto.CompactTextString(m) }
func (*Target) ProtoMessage()    {}
func (*Target) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ed77b1088311b8e, []int{9}
}

func (m *Target) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Target.Unmarshal(m, b)
}
func (m *Target) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Target.Marshal(b, m, deterministic)
}
func (m *Target) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Target.Merge(m, src)
}
func (m *Target) XXX_Size() int {
	return xxx_messageInfo_Target.Size(m)
}
func (m *Target) XXX_DiscardUnknown() {
	xxx_messageInfo_Target.DiscardUnknown(m)
}

var xxx_messageInfo_Target proto.InternalMessageInfo

func (m *Target) GetDogLicenseCode() string {
	if m != nil {
		return m.DogLicenseCode
	}
	return ""
}

func (m *Target) GetImmunityCertifiCode() string {
	if m != nil {
		return m.ImmunityCertifiCode
	}
	return ""
}

func (m *Target) GetPetDogBreed() string {
	if m != nil {
		return m.PetDogBreed
	}
	return ""
}

func (m *Target) GetHouseCity() string {
	if m != nil {
		return m.HouseCity
	}
	return ""
}

func (m *Target) GetHouseAddress() string {
	if m != nil {
		return m.HouseAddress
	}
	return ""
}

func (m *Target) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *Target) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *Target) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *Target) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *Target) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *Target) GetSterilization() bool {
	if m != nil {
		return m.Sterilization
	}
	return false
}

func (m *Target) GetImmune() bool {
	if m != nil {
		return m.Immune
	}
	return false
}

func (m *Target) GetBase64Str() string {
	if m != nil {
		return m.Base64Str
	}
	return ""
}

func (m *Target) GetBizMode() int32 {
	if m != nil {
		return m.BizMode
	}
	return 0
}

func init() {
	proto.RegisterType((*InsuranceOnLineRequest)(nil), "is.InsuranceOnLineRequest")
	proto.RegisterType((*YangZiJiangResponse)(nil), "is.YangZiJiangResponse")
	proto.RegisterType((*YangZiJiangResults)(nil), "is.YangZiJiangResults")
	proto.RegisterType((*YangZiJiangRequest)(nil), "is.YangZiJiangRequest")
	proto.RegisterType((*SaleInfo)(nil), "is.SaleInfo")
	proto.RegisterType((*Product)(nil), "is.Product")
	proto.RegisterType((*Policy)(nil), "is.Policy")
	proto.RegisterType((*Holder)(nil), "is.Holder")
	proto.RegisterType((*Insureds)(nil), "is.Insureds")
	proto.RegisterType((*Target)(nil), "is.Target")
}

func init() { proto.RegisterFile("is/insurance.proto", fileDescriptor_8ed77b1088311b8e) }

var fileDescriptor_8ed77b1088311b8e = []byte{
	// 1131 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0x4f, 0x73, 0x1b, 0x35,
	0x14, 0x1f, 0x3b, 0xf1, 0xbf, 0x67, 0x27, 0x29, 0x6a, 0x27, 0x2c, 0x29, 0x05, 0xb3, 0x03, 0x1d,
	0x4f, 0x0f, 0xa1, 0x13, 0x18, 0xee, 0x76, 0xc2, 0x50, 0x43, 0x93, 0x9a, 0x4d, 0x0a, 0x03, 0x17,
	0x8f, 0xb2, 0xab, 0x3a, 0x9a, 0xee, 0x4a, 0x8b, 0xa4, 0x05, 0xdc, 0x13, 0x77, 0x3e, 0x07, 0x37,
	0x3e, 0x05, 0x47, 0xbe, 0x0e, 0x37, 0x4e, 0x8c, 0x9e, 0xb4, 0xf6, 0xfa, 0x4f, 0x86, 0xcc, 0xf4,
	0xa6, 0xf7, 0x7b, 0x4f, 0x4f, 0xd2, 0xef, 0xf7, 0xf4, 0x24, 0x20, 0x5c, 0x7f, 0xca, 0x85, 0x2e,
	0x14, 0x15, 0x31, 0x3b, 0xce, 0x95, 0x34, 0x92, 0xd4, 0xb9, 0x0e, 0xff, 0xd9, 0x81, 0xc3, 0x71,
	0x89, 0xbf, 0x10, 0xcf, 0xb9, 0x60, 0x11, 0xfb, 0xa9, 0x60, 0xda, 0x90, 0x0f, 0x00, 0xae, 0x0b,
	0xcd, 0x05, 0xd3, 0xfa, 0x42, 0x06, 0xb5, 0x7e, 0x6d, 0xd0, 0x89, 0x2a, 0x08, 0x79, 0x08, 0x9d,
	0x5c, 0xa6, 0x3c, 0x9e, 0x4f, 0x85, 0x0c, 0xea, 0xe8, 0x6e, 0x3b, 0xe0, 0x42, 0x92, 0x3e, 0xf4,
	0x72, 0x3a, 0x9f, 0xd2, 0x3c, 0x4f, 0xd1, 0xbf, 0xe3, 0xa6, 0xe7, 0x74, 0x3e, 0xb4, 0xd0, 0x85,
	0x24, 0x8f, 0x00, 0x30, 0x22, 0x93, 0x85, 0x30, 0xc1, 0x6e, 0xbf, 0x36, 0x68, 0x44, 0x1d, 0xeb,
	0x47, 0x80, 0xbc, 0x07, 0x6d, 0xeb, 0x36, 0x3c, 0x63, 0x41, 0x03, 0x27, 0xb7, 0x72, 0x3a, 0xbf,
	0xe2, 0x19, 0x23, 0x8f, 0xe1, 0x80, 0xa5, 0x2c, 0x9e, 0xfa, 0xd5, 0x0b, 0x95, 0x06, 0x4d, 0x8c,
	0xd8, 0xb3, 0xf0, 0x04, 0xd1, 0x97, 0x2a, 0x25, 0x4f, 0xe0, 0x1d, 0x23, 0x0d, 0x4d, 0xa7, 0x78,
	0x70, 0x96, 0x4c, 0x69, 0x66, 0x82, 0x16, 0x2e, 0x74, 0x80, 0x8e, 0xb1, 0xc3, 0x87, 0x99, 0x21,
	0x4f, 0xe0, 0xde, 0x30, 0x36, 0x05, 0x4d, 0x27, 0x8a, 0x65, 0xbc, 0xc8, 0x86, 0x99, 0x09, 0xda,
	0x18, 0xba, 0x81, 0x93, 0xc7, 0xb0, 0xef, 0x67, 0x8e, 0x66, 0xc2, 0xee, 0x28, 0xe8, 0xe0, 0xf2,
	0x6b, 0x68, 0x25, 0xee, 0x4b, 0x91, 0x60, 0x1c, 0xac, 0xc4, 0x79, 0x94, 0x84, 0xd0, 0xbc, 0x91,
	0x69, 0xc2, 0x54, 0xd0, 0xed, 0xd7, 0x06, 0xdd, 0x13, 0x38, 0xe6, 0xfa, 0xf8, 0x19, 0x22, 0x91,
	0xf7, 0x90, 0x01, 0xb4, 0xfd, 0x29, 0x74, 0xd0, 0xeb, 0xef, 0x0c, 0xba, 0x27, 0x3d, 0x1b, 0xe5,
	0x33, 0xe9, 0x68, 0xe1, 0xb5, 0xd9, 0x0c, 0x55, 0x33, 0x66, 0x82, 0xbd, 0x65, 0xb6, 0x2b, 0x44,
	0x22, 0xef, 0x09, 0x7f, 0xaf, 0xc1, 0xfd, 0x1f, 0xa8, 0x98, 0xfd, 0xc8, 0xbf, 0xe6, 0x54, 0xcc,
	0x22, 0xa6, 0x73, 0x29, 0x34, 0x23, 0x04, 0x76, 0x63, 0x99, 0x30, 0x14, 0xbb, 0x11, 0xe1, 0x98,
	0x04, 0xd0, 0xca, 0x98, 0xd6, 0x74, 0xc6, 0xbc, 0xc8, 0xa5, 0x49, 0x1e, 0x40, 0x83, 0x29, 0x25,
	0x95, 0x17, 0xd7, 0x19, 0xe4, 0x29, 0xb4, 0x14, 0xd3, 0x45, 0x6a, 0x74, 0xb0, 0x8b, 0x1b, 0x3d,
	0xb4, 0x1b, 0x58, 0x5d, 0xcd, 0x7a, 0xa3, 0x32, 0x2c, 0x54, 0x40, 0x36, 0xdd, 0x24, 0x84, 0x3d,
	0x2f, 0x30, 0xcd, 0x73, 0x5b, 0x42, 0xae, 0x02, 0xbb, 0x0e, 0x1c, 0xe6, 0xf9, 0xff, 0x95, 0xe0,
	0x43, 0xe8, 0x60, 0x99, 0xd0, 0x24, 0x29, 0xb7, 0xd8, 0xb6, 0xc0, 0x30, 0x49, 0x54, 0xf8, 0x67,
	0x7d, 0x6d, 0x51, 0x57, 0xf3, 0x03, 0x68, 0x6b, 0x9a, 0xb2, 0xb1, 0x78, 0xe5, 0xd6, 0xf3, 0x34,
	0x5f, 0x7a, 0x2c, 0x5a, 0x78, 0xc9, 0x27, 0xd0, 0xca, 0x95, 0x4c, 0x8a, 0xd8, 0xe0, 0xc2, 0xdd,
	0x93, 0xae, 0x0d, 0x9c, 0x38, 0x28, 0x2a, 0x7d, 0x56, 0x0d, 0xb7, 0x21, 0xdc, 0x81, 0x57, 0xc3,
	0x95, 0x68, 0xe4, 0x3d, 0x15, 0xfd, 0x77, 0xef, 0xa4, 0x7f, 0xe3, 0x8e, 0xfa, 0x37, 0x6f, 0xd3,
	0xdf, 0x2a, 0xa7, 0x5f, 0x17, 0x3c, 0xc1, 0xdb, 0xd0, 0x89, 0x9c, 0x61, 0xaf, 0x9c, 0x54, 0x09,
	0x53, 0x53, 0x9e, 0x60, 0xed, 0x77, 0xa2, 0x16, 0xda, 0xe3, 0x24, 0x2c, 0xa0, 0x5d, 0x72, 0x40,
	0x3e, 0x84, 0x2e, 0x8d, 0x63, 0xa6, 0xf5, 0xb4, 0xd0, 0x4c, 0x95, 0x8d, 0xc1, 0x41, 0x2f, 0x35,
	0x53, 0xa4, 0x0f, 0xdd, 0xf8, 0x86, 0x0a, 0xc1, 0xd2, 0x53, 0x5b, 0x4c, 0x4e, 0x97, 0x2a, 0x64,
	0x6f, 0x86, 0x8b, 0x9f, 0x50, 0xad, 0x7f, 0x91, 0x2a, 0xf1, 0xfa, 0xac, 0xa1, 0xa1, 0x80, 0x96,
	0x67, 0x94, 0x7c, 0x04, 0x3d, 0xcf, 0xe9, 0x74, 0x51, 0xa2, 0xb6, 0x1a, 0x1c, 0x86, 0x59, 0x6d,
	0x08, 0x8d, 0x5f, 0xd3, 0x19, 0x73, 0x21, 0x7e, 0x61, 0x8f, 0x61, 0xc8, 0x23, 0x00, 0x24, 0xca,
	0x05, 0xb8, 0x45, 0x3b, 0x88, 0x58, 0x77, 0xf8, 0x57, 0x1d, 0x9a, 0x4e, 0x1c, 0xdb, 0xfd, 0x46,
	0x1b, 0xdd, 0x6f, 0x89, 0x6c, 0x6d, 0x18, 0xf5, 0x5b, 0x1a, 0xc6, 0x00, 0x0e, 0xbc, 0x50, 0xdf,
	0x16, 0x54, 0x18, 0x6e, 0x5c, 0x35, 0x34, 0xa2, 0x75, 0x98, 0x1c, 0x03, 0x79, 0xa1, 0xf8, 0x8c,
	0x8b, 0x95, 0xbc, 0xae, 0x39, 0x6e, 0xf1, 0xd8, 0xcc, 0x57, 0xab, 0x9d, 0x0c, 0x9b, 0x65, 0x23,
	0x5a, 0x87, 0xb7, 0x34, 0xad, 0xe6, 0x1d, 0x9b, 0x56, 0x6b, 0x6b, 0xd3, 0x0a, 0xa0, 0x35, 0xcc,
	0x73, 0x0c, 0xf0, 0xb5, 0xe2, 0xcd, 0xf0, 0xdf, 0x1a, 0x34, 0x5d, 0xf5, 0x5a, 0x12, 0xdd, 0xe8,
	0x82, 0x66, 0xa5, 0x64, 0x15, 0xc4, 0xfa, 0xc7, 0x49, 0x4c, 0x95, 0xb9, 0x9a, 0xe7, 0xa5, 0x5e,
	0x15, 0x84, 0x1c, 0x41, 0xdb, 0x59, 0x17, 0xe5, 0x0b, 0xb2, 0xb0, 0x97, 0xb9, 0x71, 0xee, 0x6e,
	0x35, 0x37, 0xce, 0x3d, 0x84, 0xe6, 0xb9, 0xbc, 0xe6, 0x69, 0xf9, 0x7c, 0x78, 0xcb, 0xe6, 0x1c,
	0x49, 0x25, 0xce, 0xa8, 0x29, 0x29, 0x58, 0xd8, 0xe4, 0x1e, 0xec, 0x5c, 0xb2, 0x5f, 0xfd, 0x89,
	0xed, 0xd0, 0x46, 0x5f, 0xb1, 0x34, 0xbf, 0x91, 0xa2, 0x3c, 0xe7, 0xc2, 0xb6, 0xdd, 0xf2, 0x9c,
	0xf2, 0xd4, 0x77, 0x7f, 0x1c, 0x87, 0xbf, 0xd5, 0xa1, 0x5d, 0x5e, 0x4a, 0x7b, 0x11, 0xfc, 0xb8,
	0x72, 0xfe, 0x2a, 0xf4, 0x56, 0x04, 0x2c, 0xb3, 0x57, 0x18, 0xa8, 0x42, 0x2b, 0x47, 0x6d, 0x6c,
	0x3f, 0x6a, 0x73, 0x79, 0xd4, 0x63, 0x20, 0x11, 0x4b, 0xa9, 0xe1, 0x52, 0x7c, 0xcf, 0xcd, 0x8d,
	0xa3, 0xd2, 0x73, 0xb1, 0xc5, 0x53, 0x21, 0xb8, 0x5d, 0x25, 0x38, 0xfc, 0x7b, 0x07, 0x9a, 0xae,
	0xdf, 0xd8, 0x62, 0x3a, 0x93, 0xb3, 0xe7, 0x3c, 0x66, 0x42, 0xe3, 0x05, 0xf4, 0x1c, 0xac, 0xa1,
	0xe4, 0x29, 0xdc, 0x1f, 0x67, 0x59, 0x21, 0xb8, 0x99, 0x9f, 0x32, 0x65, 0xf8, 0x2b, 0x5e, 0xe9,
	0x1c, 0xdb, 0x5c, 0xf6, 0xf0, 0x13, 0x66, 0xce, 0xe4, 0x6c, 0xa4, 0x18, 0x2b, 0xdb, 0x47, 0x15,
	0x22, 0xef, 0x43, 0xe7, 0x99, 0x2c, 0x34, 0x3b, 0xb5, 0xd7, 0xcd, 0x91, 0xb3, 0x04, 0x48, 0x08,
	0x3d, 0x34, 0xec, 0x63, 0xc0, 0xb4, 0xf6, 0xf4, 0xac, 0x60, 0xb6, 0xc4, 0x27, 0xcc, 0xa0, 0x74,
	0x8e, 0xa6, 0xd2, 0xb4, 0xc4, 0x9e, 0x52, 0xc3, 0x66, 0x52, 0xcd, 0x3d, 0x41, 0x0b, 0xdb, 0x66,
	0x2e, 0xc7, 0x38, 0xd5, 0x91, 0xb3, 0x82, 0xa1, 0x30, 0x5c, 0x99, 0x9b, 0x84, 0xce, 0x7d, 0xf5,
	0x2c, 0x6c, 0x4b, 0xeb, 0x57, 0x4c, 0x58, 0xea, 0xdd, 0x6f, 0xc1, 0x5b, 0xe4, 0x63, 0xd8, 0xbb,
	0x34, 0x4c, 0xf1, 0x94, 0xbf, 0x41, 0x25, 0xf0, 0xb3, 0xd0, 0x8e, 0x56, 0x41, 0x3b, 0x1b, 0xe9,
	0x62, 0x41, 0x0f, 0xdd, 0xde, 0xb2, 0x6c, 0x8c, 0xa8, 0x66, 0x5f, 0x7c, 0x7e, 0x69, 0x14, 0x7e,
	0x0c, 0x3a, 0xd1, 0x12, 0xb0, 0x27, 0x1d, 0xf1, 0x37, 0xe7, 0x96, 0xf3, 0x7d, 0x6c, 0x1f, 0xa5,
	0x79, 0xf2, 0xc7, 0xea, 0x4f, 0xe1, 0x92, 0xa9, 0x9f, 0x79, 0xcc, 0x34, 0x19, 0xc2, 0xfe, 0x77,
	0x34, 0xe5, 0x09, 0x35, 0xcc, 0x37, 0xcc, 0xcd, 0x67, 0x1e, 0x9f, 0xd4, 0xa3, 0x77, 0x37, 0x9f,
	0x7f, 0xf7, 0xd9, 0xf8, 0x06, 0x1e, 0x2c, 0x7f, 0x9e, 0xf6, 0x9d, 0x71, 0xdf, 0x4f, 0x72, 0xb4,
	0x78, 0xd8, 0x36, 0xfe, 0xa4, 0xb7, 0x26, 0xbb, 0x6e, 0xe2, 0x97, 0xf6, 0xb3, 0xff, 0x02, 0x00,
	0x00, 0xff, 0xff, 0x22, 0xa1, 0xec, 0x28, 0xe8, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// YangZiJiangServicesClient is the client API for YangZiJiangServices service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type YangZiJiangServicesClient interface {
	//扬子江核保承保接口
	ValidatePolicy(ctx context.Context, in *YangZiJiangRequest, opts ...grpc.CallOption) (*YangZiJiangResponse, error)
	InsuranceOrderOnLine(ctx context.Context, in *InsuranceOnLineRequest, opts ...grpc.CallOption) (*YangZiJiangResponse, error)
}

type yangZiJiangServicesClient struct {
	cc *grpc.ClientConn
}

func NewYangZiJiangServicesClient(cc *grpc.ClientConn) YangZiJiangServicesClient {
	return &yangZiJiangServicesClient{cc}
}

func (c *yangZiJiangServicesClient) ValidatePolicy(ctx context.Context, in *YangZiJiangRequest, opts ...grpc.CallOption) (*YangZiJiangResponse, error) {
	out := new(YangZiJiangResponse)
	err := c.cc.Invoke(ctx, "/is.YangZiJiangServices/ValidatePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yangZiJiangServicesClient) InsuranceOrderOnLine(ctx context.Context, in *InsuranceOnLineRequest, opts ...grpc.CallOption) (*YangZiJiangResponse, error) {
	out := new(YangZiJiangResponse)
	err := c.cc.Invoke(ctx, "/is.YangZiJiangServices/InsuranceOrderOnLine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// YangZiJiangServicesServer is the server API for YangZiJiangServices service.
type YangZiJiangServicesServer interface {
	//扬子江核保承保接口
	ValidatePolicy(context.Context, *YangZiJiangRequest) (*YangZiJiangResponse, error)
	InsuranceOrderOnLine(context.Context, *InsuranceOnLineRequest) (*YangZiJiangResponse, error)
}

// UnimplementedYangZiJiangServicesServer can be embedded to have forward compatible implementations.
type UnimplementedYangZiJiangServicesServer struct {
}

func (*UnimplementedYangZiJiangServicesServer) ValidatePolicy(ctx context.Context, req *YangZiJiangRequest) (*YangZiJiangResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePolicy not implemented")
}
func (*UnimplementedYangZiJiangServicesServer) InsuranceOrderOnLine(ctx context.Context, req *InsuranceOnLineRequest) (*YangZiJiangResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsuranceOrderOnLine not implemented")
}

func RegisterYangZiJiangServicesServer(s *grpc.Server, srv YangZiJiangServicesServer) {
	s.RegisterService(&_YangZiJiangServices_serviceDesc, srv)
}

func _YangZiJiangServices_ValidatePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YangZiJiangRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YangZiJiangServicesServer).ValidatePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/is.YangZiJiangServices/ValidatePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YangZiJiangServicesServer).ValidatePolicy(ctx, req.(*YangZiJiangRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _YangZiJiangServices_InsuranceOrderOnLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceOnLineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YangZiJiangServicesServer).InsuranceOrderOnLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/is.YangZiJiangServices/InsuranceOrderOnLine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YangZiJiangServicesServer).InsuranceOrderOnLine(ctx, req.(*InsuranceOnLineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _YangZiJiangServices_serviceDesc = grpc.ServiceDesc{
	ServiceName: "is.YangZiJiangServices",
	HandlerType: (*YangZiJiangServicesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ValidatePolicy",
			Handler:    _YangZiJiangServices_ValidatePolicy_Handler,
		},
		{
			MethodName: "InsuranceOrderOnLine",
			Handler:    _YangZiJiangServices_InsuranceOrderOnLine_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "is/insurance.proto",
}

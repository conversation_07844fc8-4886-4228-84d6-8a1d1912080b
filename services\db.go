package services

import (
	"order-center/utils"
	"os"
	"strings"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

var (
	xormEngine       *kit.DBEngine
	xormUPetEngine   *kit.DBEngine
	redisHandle      *kit.DBEngine
	ZlHospitalEngine *kit.DBEngine
	DatacenterEngine *kit.DBEngine

	// 互联网医疗
	medicalEngine *kit.DBEngine
)

func SetupDbConn() {
	xormEngine = kit.NewDBEngine(getDBDsn())
	//数据库定时探活
	go xormEngine.DBEngineCheck(xormEngine.NewXormEngineInterface, 3, 3)

	xormUPetEngine = kit.NewDBEngine(getUPetDBDsn())
	//数据库定时探活
	go xormUPetEngine.DBEngineCheck(xormUPetEngine.NewXormEngineInterface, 3, 3)

	redisHandle = kit.NewRedisEngine(getRedisDsn())
	//redis定时探活
	go redisHandle.DBEngineCheck(redisHandle.NewRedisConnInterface, 3, 3)

	ZlHospitalEngine = kit.NewDBEngine(GetZlHospitalDBDsn())
	//数据库定时探活
	go ZlHospitalEngine.DBEngineCheck(ZlHospitalEngine.NewXormEngineInterface, 3, 3)
}

// 关闭数据库连接
func CloseDbConn() {
	xormEngine.Engine.(*xorm.Engine).Close()
	DatacenterEngine.Engine.(*xorm.Engine).Close()
	xormUPetEngine.Engine.(*xorm.Engine).Close()
	redisHandle.Engine.(*redis.Client).Close()
	ZlHospitalEngine.Engine.(*xorm.Engine).Close()
}

func getDBDsn(mySqlStr ...string) string {
	//return "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_order?charset=utf8mb4"
	//return "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_order?charset=utf8mb4"
	//return "root:d&!89iCEGKOuVHkT@(39.107.46.194:13306)/dc_order?charset=utf8mb4"
	//return "root:Rp000000@(10.1.1.245:3306)/dc_order?charset=utf8mb4&parseTime=true&loc=Local"
	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("mysql.dc_order")) //append(mySqlStr,"dbuser:ZQA!2sxQQ@(10.1.1.242:5532)/dc_order?charset=utf8mb4")
	}
	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql dsn")
		panic("can't find mysql url")
	}
	//glog.Info(mySqlStr[0])
	return mySqlStr[0]
}

// 建立数据库连接
func GetDBConn(mySqlStr ...string) *xorm.Engine {
	if xormEngine == nil || xormEngine.Engine == nil {
		xormEngine = kit.NewDBEngine(getDBDsn(mySqlStr...))
	}

	engine := xormEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}
	return engine
}

// GetDcDBConn 建立datacemter阿闻数据库连接
func GetDcDBConn(mySqlStr ...string) *xorm.Engine {
	if DatacenterEngine == nil || DatacenterEngine.Engine == nil {
		DatacenterEngine = kit.NewDBEngine(getDcDBDsn(mySqlStr...))
	}
	return DatacenterEngine.Engine.(*xorm.Engine)
}

// 获取阿闻电商的数据库链接
func getDcDBDsn(mySqlStr ...string) string {
	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("mysql.datacenter"))
	}
	//mySqlStr[0] = "s2b2c:9iIJth3tJzhmSk5w@(172.30.2.14:23306)/datacenter?charset=utf8mb4" //sit1

	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql dsn")
		panic("can't find mysql url")
	}
	return mySqlStr[0]
}

// GetUPetDBConn
// 建立阿闻数据库连接
// 拼团版本新建，因为需要查询电商的产品信息
func GetUPetDBConn(mySqlStr ...string) *xorm.Engine {
	if xormUPetEngine == nil || xormUPetEngine.Engine == nil {
		xormUPetEngine = kit.NewDBEngine(getUPetDBDsn(mySqlStr...))
	}
	return xormUPetEngine.Engine.(*xorm.Engine)
}

// 获取阿闻电商的数据库链接
func getUPetDBDsn(mySqlStr ...string) string {

	// return "s2b2c:9iIJth3tJzhmSk5w@(172.30.2.14:23306)/testupetmart?charset=utf8mb4"

	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("mysql.upetmart"))
	}
	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql dsn")
		panic("can't find mysql url")
	}

	return mySqlStr[0]
}

// 建立从数据库连接
func NewSlaveDbConn() *xorm.Engine {

	mySqlStr := config.GetString("mysql.dc_order.slave")
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_order?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		mySqlStr = config.GetString("mysql.dc_order")
	}
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_order?charset=utf8mb4"
	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/dc_order?charset=utf8mb4"
	//mySqlStr = "readonly:dsax45677uDHR3gGGFJU-@(211.154.155.93:3366)/dc_order?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		glog.Fatal("can't find mysql url")
		panic("can't find mysql url")
	}

	return kit.NewDBEngine(mySqlStr).Engine.(*xorm.Engine)
}

func getRedisDsn(dsn ...string) string {
	if len(dsn) > 0 {
		return dsn[0]
	}

	dsnSlice := []string{
		config.GetString("redis.Addr"),
		config.GetString("redis.Password"),
		config.GetString("redis.DB"),
	}
	//dsnSlice = []string{"172.30.128.56:6379", "MkdGH*3ldf", "0"} //sit1

	//glog.Info(fmt.Sprintf("redis连接参数:%s,%s,%s", config.GetString("redis.Addr"), config.GetString("redis.Password"), config.GetString("redis.DB")))

	return strings.Join(dsnSlice, "|")
}

// 连接池勿关闭
// 获取redis集群客户端
func GetRedisConn(dsn ...string) *redis.Client {
	redisDns := getRedisDsn(dsn...)

	startTime := time.Now()
	defer func() {
		if p := recover(); p != nil {
			glog.Errorf("GetRedisConn panic:%+v  redisDns:%s 耗时:%s \n %s", p, redisDns, time.Since(startTime), utils.PanicTrace())
			panic(p)
		}
	}()

	if redisHandle != nil {
		_, err := redisHandle.Engine.(*redis.Client).Ping().Result()
		if err == nil {
			return redisHandle.Engine.(*redis.Client)
		}
	}

	redisHandle = kit.NewRedisEngine(redisDns)

	return redisHandle.Engine.(*redis.Client)
}

// 通过key获取hash的元素值
func HashGet(key, field string) string {
	//连接池勿关闭
	client := GetRedisConn()

	val, err := client.HGet(key, field).Result()
	if err != nil && err != redis.Nil {
		glog.Error(key, ", hashget失败，", err)
		return ""
	}
	return val
}

func GetZlHospitalDBDsn(mySqlStr ...string) string {
	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("mysql.zl_hospital_db"))
	}
	//mySqlStr[0] = "read_only:AAAsklapdadf@(172.28.24.210:13308)/hill_organ?charset=utf8mb4"
	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql dsn")
		panic("can't find mysql url")
	}
	return mySqlStr[0]
}

func GetZlHospitalConn(mySqlStr ...string) *xorm.Engine {
	if ZlHospitalEngine == nil || ZlHospitalEngine.Engine == nil {
		ZlHospitalEngine = kit.NewDBEngine(GetZlHospitalDBDsn(mySqlStr...))
	}
	engine := ZlHospitalEngine.Engine.(*xorm.Engine)
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" { // 开发环境显示sql日志
		engine.ShowSQL(true)
	}
	return engine
}

func GetMedicalDBConn() *xorm.Engine {
	if medicalEngine == nil || medicalEngine.Engine == nil {
		medicalEngine = kit.NewDBEngine(config.GetString("mysql.pet-medical"))
	}

	xe := medicalEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		xe.ShowSQL(true)
	}
	return xe
}

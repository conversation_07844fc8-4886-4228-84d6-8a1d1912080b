package ps

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  PsRecommendProductClient
}

func GetPsRecommendProductClient(c ...echo.Context) *Client {
	var client Client
	var err error
	url := config.GetString("grpc.personalized-service")
	if url == "" {
		url = "127.0.0.1:7061"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewPsRecommendProductClient(client.Conn)
		//client.Ctx = AppendToOutgoingContextLoginUserInfo(context.Background(), c...)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return &client
	}
}

//关闭链接
func (d *Client) Close() {
	d.Conn.Close()
	d.Cf()
}

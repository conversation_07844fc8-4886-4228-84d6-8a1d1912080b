package models

import (
	"strings"
	"time"
)

type OrderMain struct {
	Id                 int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn            string    `xorm:"not null default '''' comment('订单号') unique VARCHAR(50)"`
	OldOrderSn         string    `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
	ParentOrderSn      string    `xorm:"not null default '''' comment('拆单前父订单号') index VARCHAR(50)"`
	OrderStatus        int32     `xorm:"not null default 0 comment('订单状态：0已取消,10(默认)未付款,20已付款,30已完成') INT(11)"`
	OrderStatusChild   int32     `xorm:"not null default 0 comment('子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;虚拟订单专用,30101待核销,30102部分核销,30103已核销') INT(11)"`
	ShopId             string    `xorm:"not null default '''' comment('商户或门店id(财务编码)') index VARCHAR(80)"`
	ShopName           string    `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	WarehouseId        int32     `xorm:"not null default 0 comment('仓库id') INT(11)"`
	WarehouseCode      string    `xorm:"not null default '''' comment('仓库代码') VARCHAR(255)"`
	WarehouseName      string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(255)"`
	MemberId           string    `xorm:"not null default '''' comment('会员id') index VARCHAR(50)"`
	MemberName         string    `xorm:"not null default '''' comment('会员名称') VARCHAR(50)"`
	MemberTel          string    `xorm:"not null default '''' comment('会员手机号') VARCHAR(20)"`
	EnMemberTel        string    `xorm:"not null default '''' comment('加密手机号') VARCHAR(20)"`
	ReceiverName       string    `xorm:"not null default '''' comment('收件人') VARCHAR(50)"`
	ReceiverState      string    `xorm:"not null default '''' comment('收件省') VARCHAR(50)"`
	ReceiverCity       string    `xorm:"not null default '''' comment('收件市') VARCHAR(50)"`
	ReceiverDistrict   string    `xorm:"not null default '''' comment('收件区') VARCHAR(50)"`
	ReceiverAddress    string    `xorm:"not null default '''' comment('收件地址') VARCHAR(255)"`
	ReceiverPhone      string    `xorm:"not null default '''' comment('收件电话') VARCHAR(30)"`
	EnReceiverPhone    string    `xorm:"not null default '''' comment('加密收件电话') VARCHAR(30)"`
	ReceiverMobile     string    `xorm:"not null default '''' comment('收件手机') VARCHAR(30)"`
	EnReceiverMobile   string    `xorm:"not null default '''' comment('加密收件手机') VARCHAR(30)"`
	Total              int32     `xorm:"not null default 0 comment('实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）') INT(11)"`
	PayTotal           int32     `xorm:"not null default 0 comment('当前订单实付金额') INT(10)"`
	GoodsTotal         int32     `xorm:"not null default 0 comment('商品总金额（去掉优惠，运费，包装费，服务费等的金额）') INT(11)"`
	GoodsPayTotal      int32     `xorm:"not null default 0 comment('商品实付总金额') INT(10)"`
	Privilege          int32     `xorm:"not null default 0 comment('总优惠金额(分)') INT(11)"`
	CombinePrivilege   int32     `xorm:"not null default 0 comment('组合商品总优惠金额(分)') INT(11)"`
	Freight            int32     `xorm:"not null default 0 comment('总运费(分)') INT(11)"`
	FreightPrivilege   int32     `xorm:"default 0 comment('商家运费优惠金额(分)') INT(11)"`
	PackingCost        int32     `xorm:"not null default 0 comment('包装费(分)') INT(11)"`
	ServiceCharge      int32     `xorm:"not null default 0 comment('服务费(分)') INT(11)"`
	ContractFee        int32     `xorm:"not null default 0 comment('履约费(分)') INT(11)"`
	PoiChargeTotal     int32     `xorm:"not null default 0 comment('商家补贴') INT(10)"`
	PtChargeTotal      int32     `xorm:"not null default 0 comment('平台补贴') INT(10)"`
	PtFreightPrivilege int32     `xorm:"not null default 0 comment('平台配送费优惠') INT(10)"`
	ActualReceiveTotal int32     `xorm:"not null default 0 comment('商家预计收入') INT(10)"`
	RefundAmount       int32     `xorm:"not null default 0 comment('已退款金额(分)') INT(11)"`
	TotalWeight        int32     `xorm:"not null default 0 comment('总重量(克)') INT(11)"`
	IsPay              int32     `xorm:"not null default 0 comment('是否支付，0否1是') TINYINT(4)"`
	PayTime            time.Time `xorm:"default 'NULL' comment('支付时间') DATETIME"`
	PaySn              string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	PayMode            int32     `xorm:"not null default 0 comment('支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付,8储值卡支付') TINYINT(4)"`
	PayAmount          int32     `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	ConfirmTime        time.Time `xorm:"default 'NULL' comment('完成时间') DATETIME"`
	DeliverTime        time.Time `xorm:"default 'NULL' comment('发货时间') DATETIME"`
	CancelTime         time.Time `xorm:"default 'NULL' comment('取消订单时间') DATETIME"`
	CancelReason       string    `xorm:"not null default '''' comment('取消订单原因') VARCHAR(255)"`
	OrderType          int32     `xorm:"not null default 1 comment('订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购 99助力订单') INT(11)"`
	Source             int32     `xorm:"not null default 0 comment('仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）') TINYINT(4)"`
	DeliveryType       int32     `xorm:"not null default 1 comment('配送类型,1快递,2外卖,3自提,4同城送,5商家自配') TINYINT(4)"`
	LogisticsCode      string    `xorm:"not null default '''' comment('配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送') VARCHAR(20)"`
	ChannelId          int32     `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	UserAgent          int32     `xorm:"not null default 0 comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它') INT(11)"`
	IsVirtual          int32     `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	CreateTime         time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	UpdateTime         time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
	IsPushTencent      int32     `xorm:"not null default 0 comment('1.已经推送到腾讯有数') INT(11)"`
	AppChannel         int32     `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	OrderPayType       string    `xorm:"not null default '''' comment('交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)') VARCHAR(10)"`
	Lng                string    `xorm:"not null default '''' comment('下单经度') VARCHAR(56)"`
	Lat                string    `xorm:"not null default '''' comment('下单纬度') VARCHAR(56)"`
	OrgId              int32     `xorm:"not null default 1 comment('主体ID，对应organization_info主键') INT(11)"`
	LogisticsName      string    `xorm:"not null default '''' comment('SAAS对应配送方式名称') VARCHAR(255)"`
}

func (om *OrderMain) TableName() string {
	return "dc_order.order_main"
}

type OrderMainDetails struct {
	OrderMain   `xorm:"extends"`
	OrderDetail `xorm:"extends"`
}

type OrderRefundExt struct {
	OrderMain   `xorm:"extends"`
	RefundOrder `xorm:"extends"`
}

var DeliveryTypeMap = map[int32]string{
	1: "", //快递
	2: "", //外卖
	3: "到店自提",
	4: "同城配送",
	5: "", //商家自配
}

// GetDeliveryTypeText 获取配送方式文本描述 如果返回空的字符串，需前端根据 DeliveryType的值做匹配显示
func (o OrderMain) GetDeliveryTypeText() string {
	t, ok := DeliveryTypeMap[o.DeliveryType]
	if ok {
		return t
	}
	return ""
}

// GetOrderSourceChannel 获取source对于的渠道名称
func GetOrderSourceChannel(source int32) string {
	return map[int32]string{
		1: "全渠道", 3: "子龙", 4: "自研OMS", 5: "巨益OMS",
	}[source]
}

// GetParentOrderSn 获取父订单号
func (o OrderMain) GetParentOrderSn() string {
	if len(o.ParentOrderSn) == 0 {
		return o.OrderSn
	}
	return o.ParentOrderSn
}

// GetActualReceiveTotal 获取父订单商家预计收入
func (o OrderMain) GetActualReceiveTotal() int32 {
	if o.ChannelId == 5 {
		return o.Total
	}
	// noSelfDistribution：1.非自配;0.自配;
	noSelfDistribution := 0
	if o.DeliveryType != 3 {
		if o.ChannelId == 3 {
			if o.LogisticsCode != "6" {
				noSelfDistribution = 1
			}
		} else if o.ChannelId == 4 {
			if o.LogisticsCode != "2938" {
				noSelfDistribution = 1
			}
		} else if o.ChannelId == 2 {
			// 美团专配
			if strings.Contains("2002,1001,1004,2010,3001,1007", o.LogisticsCode) {
				noSelfDistribution = 1
			}
		}
	}
	// 商家预计收入=商品金额+配送费(自配送)+包装费-商家补贴（商品和配送费）- 平台服务费
	income := o.GoodsTotal + o.PackingCost - o.PoiChargeTotal - o.ServiceCharge
	if noSelfDistribution == 0 {
		income += o.Freight
	}
	return income
}

package services

import (
	"context"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/protobuf/types/known/structpb"
	"order-center/models"
	"order-center/proto/ac"
	"time"
)

//通过订单号与重做类型查询是否存在记录
//一个单号只允许有一条某种重试类型的数据
func CheckExist(orderSn string, redoType int) bool {
	row := models.OrderRedoTask{}
	db := GetDBConn()

	isok, err := db.SQL("SELECT id FROM order_redo_task WHERE order_sn = ? AND redo_type = ?", orderSn, redoType).Get(&row)
	if err != nil {
		glog.Error("检测重试任务是否存在失败", err.Error())
		return true
	}
	return isok
}

//写入重推任务
func ReDoTaskAdd(data *models.OrderRedoTask) error {
	db := GetDBConn()
	_, err := db.Insert(data)
	if err != nil {
		glog.Error("添加重试任务失败:", err)
		return err
	}
	return nil
}

//获取未执行的任务
//查询所有未执行或者执行失败 且下次重试时间小于当前时间的任务
func GetWaitRedoTask(redoLimit int, taskType int) ([]*models.OrderRedoTask, error) {
	var rows []*models.OrderRedoTask
	now := time.Now().Format(kit.DATETIME_LAYOUT)
	db := GetDBConn()

	err := db.Where(" task_status IN(0,1) AND redo_count <? AND redo_type = ? AND next_redo_time <= ? ", redoLimit, taskType, now).Asc("id").Find(&rows)

	if err != nil {
		glog.Error("获取任务失败", err.Error())
		return rows, errors.New("获取任务失败")
	}
	return rows, err
}

func SendWebhookNotify(task *models.OrderRedoTask, RedoLimit int) {

	nowRedoCount := task.RedoCount + 1
	//如果是打折卡或门店券，失败5次后，需要加入机器人提醒
	if (task.RedoType == models.RedoTypeDiscountCardRefund || task.RedoType == models.RedoTypeWasteCoupon) && nowRedoCount == RedoLimit {

		typeName := "退打折卡"
		if task.RedoType == models.RedoTypeWasteCoupon {
			typeName = "退门店券"
		}
		glog.Info("VIP打折卡退卡重试达到次数，发送机器人:", task.OrderSn, " "+typeName)
		acCenter := ac.GetActivityCenterClient()
		data, _ := structpb.NewStruct(map[string]interface{}{
			"msgtype": "markdown",
			"markdown": map[string]interface{}{
				"content": fmt.Sprintf("## VIP退卡推送子龙异常：\n"+
					"> 异常类型：**<font color=\"red\">%s</font>**\n"+
					"> 异常单号：**<font color=\"red\">%s</font>**",
					typeName, task.OrderSn),
			},
		})

		key, _ := config.Get("awen.exception.robot")
		in := &ac.SendWebhookNotifyReq{
			Key:  key,
			Data: data,
		}

		ret, err := acCenter.Base.SendWebhookNotify(context.Background(), in)
		if err != nil {
			glog.Error("发送机器人提报警出错：", task.OrderSn, err.Error())
		}
		if ret.Code != 200 {
			glog.Error("发送机器人提报警出错：", task.OrderSn, ret.Message)
		}
	}

}

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/ac"
	"order-center/proto/common"
	"order-center/proto/dac"
	"order-center/proto/dgc"
	"order-center/proto/et"
	"order-center/proto/ext"
	"order-center/proto/mc"
	"order-center/proto/oc"
	"order-center/proto/pc"
	"order-center/utils"
	"order-center/utils/sn"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

// 获取外部订单号
func FindOldOrderSnInParentOrderSn(conn *xorm.Engine, parentOrderSn []string) (map[string]string, error) {
	var oldOrderSnList []models.OrderMain
	if err := conn.Table("order_main").In("order_sn", parentOrderSn).Cols("order_sn", "old_order_sn").Find(&oldOrderSnList); err != nil {
		err = errors.New("查询外部订单号失败" + err.Error())
		return nil, err
	}
	oldOrderSnMap := make(map[string]string)
	for _, v := range oldOrderSnList {
		oldOrderSnMap[v.OrderSn] = v.OldOrderSn
	}
	return oldOrderSnMap, nil
}

// 阿闻管家实物订单-导出订单数据
func AwenOrderExport(params *oc.AwenMaterOrderListRequest) (details []*oc.AwenOrdeExport, err error) {
	//glog.Info("阿闻管家实物订单-导出订单数据：", kit.JsonEncode(params))
	//defer glog.Info("QUIT", "阿闻管家实物订单-导出订单数据：", kit.JsonEncode(params))

	dbConn := NewSlaveDbConn()
	defer dbConn.Close()
	dbConn.ShowSQL()
	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Where("order_main.parent_order_sn != ?", "")
	session.And("`order_main`.is_virtual = 0")

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //子订单号
			session.And("`order_main`.order_sn like ?", "%"+params.Keyword+"%")
		case 2: //父订单
			session.And("`order_main`.parent_order_sn like ?", "%"+params.Keyword+"%")
		case 3: //外部订单
			session.And("`order_main`.old_order_sn like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
		default: //default case
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua",
			"`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}

	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		session.And("order_main.pay_mode = ?", params.PayMode)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}
	//自提点
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}

	if err = session.Select(`CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_detail.buyer_memo,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.accept_time,
		order_detail.is_picking,
		order_detail.picking_time,
		order_detail.extras,
		order_detail.pickup_code serial_number,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
		order_detail.performance_operator_time,
		order_detail.expected_time,
		order_product.channel_category_name,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		sum(order_product.pay_price) as product_actual_receive_total,
        sum(order_product.privilege_pt) as privilege_pt
		`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1), `order_main`.`create_time` DESC ").
		Find(&details); err != nil {
		glog.Error("zx订单导出报20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(details) == 0 {
		return
	}

	orderSns := make([]string, len(details))
	var parentOrderSn []string
	var iParentOrderSn []string //所有的父订单号
	for k := range details {
		orderSns[k] = details[k].OrderSn
		if isThirdChannel(details[k].ChannelId) {
			parentOrderSn = append(parentOrderSn, details[k].ParentOrderSn)
		}
		iParentOrderSn = append(iParentOrderSn, details[k].ParentOrderSn)
	}
	oldOrderSnMap, err1 := FindOldOrderSnInParentOrderSn(dbConn, parentOrderSn)
	if err1 != nil {
		err = errors.New("查询外部订单号失败" + err1.Error())
		return
	}

	var performanceChainId []int32
	orderPerformanceMap, err1 := FindOrderPerformanceMapByOrderSn(dbConn, iParentOrderSn)
	if err1 != nil {
		err = errors.New("查询业绩归属信息失败" + err1.Error())
		return
	}
	for _, v := range orderPerformanceMap {
		if v.PerformanceChainId > 0 {
			performanceChainId = append(performanceChainId, v.PerformanceChainId)
		}
	}
	upetChainMap, err1 := FindUpetChainMap(performanceChainId)
	if err1 != nil {
		err = errors.New("查询分销门店信息失败" + err1.Error())
		return
	}

	//查询订单优惠信息,优惠信息是以父订单的去存的，需要关联到父订单号
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	if err = func() error {
		var orderPromotion []*models.OrderPromotion
		if err = dbConn.Table("order_promotion").Alias("a").Join("inner", "order_main o", "o.parent_order_sn = a.order_sn").
			Select("o.order_sn,a.promotion_type,a.pt_charge").In("o.order_sn", orderSns).Find(&orderPromotion); err != nil {
			return err
		}

		//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
		for k := range orderPromotion {
			orderPromotionMap[orderPromotion[k].OrderSn] = append(orderPromotionMap[orderPromotion[k].OrderSn], orderPromotion[k])
		}

		return nil
	}(); err != nil {
		err = errors.New("查询订单优惠信息失败, " + err.Error())
		return
	}

	var deliveryRecords []*models.OrderDeliveryRecord

	//取配送流程最新一条记录并且骑手姓名不为空的数据
	if err = dbConn.Table("order_delivery_record").
		Select("order_delivery_record.*").
		Join("inner", "order_delivery_node", "order_delivery_record.delivery_id = order_delivery_node.delivery_id").
		Where("order_delivery_node.courier_name != '' AND order_delivery_node.delivery_status !=99").
		In("order_delivery_record.order_sn", orderSns).
		GroupBy("order_delivery_record.order_sn").
		OrderBy("order_delivery_record.id DESC,order_delivery_node.create_time DESC").
		Find(&deliveryRecords); err != nil {
		glog.Error("订单导出查询订单列表错误！ ", err.Error())
	}
	//查询订单优惠信息
	deliveryRecordsMap := make(map[string]*models.OrderDeliveryRecord, len(deliveryRecords))

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	for _, v := range deliveryRecords {
		deliveryRecordsMap[v.OrderSn] = v
	}
	//查询退款金额 阿文使用子订单 第三方使用主订单
	var awenOrderSn, thirdOrderSn []string
	thirdOrderMap := make(map[string]string) //第三方实物子单与主单的关联关系
	for _, v := range details {
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
			if _, ok := thirdOrderMap[v.ParentOrderSn]; !ok {
				thirdOrderMap[v.ParentOrderSn] = v.OrderSn
			}
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
	}
	realOrderRefund := &RealOrderRefund{
		AwenOrderSn: awenOrderSn,
		ThirdOrderSn: ThirdRealChildOrderSn{
			OrderSn:       thirdOrderSn,
			ThirdOrderMap: thirdOrderMap,
		},
	}
	//计算退款金额
	refundAmountMap := realOrderRefund.CalRefundAmount()

	wg := sync.WaitGroup{}
	ch := make(chan int8, runtime.NumCPU())

	for _, v := range details {
		ch <- 1
		wg.Add(1)
		//v := v
		go func(v *oc.AwenOrdeExport) {
			defer func() {
				<-ch
				wg.Done()
			}()

			var activityList []string
			var platformPayedAmount int32
			//配送费优惠
			var deliveryPlatformPayedAmount int32

			//优惠活动解析
			for _, vv := range orderPromotionMap[v.OrderSn] {
				activityList = append(activityList, dto.OrderPrivilegeActiveType(vv.PromotionType).String())
				platformPayedAmount += vv.PtCharge

				if vv.PromotionType == 25 || vv.PromotionType == 4011 || vv.PromotionType == 30 {
					deliveryPlatformPayedAmount += vv.PtCharge
				}
			}

			v.PlatformFreightPrivilege = deliveryPlatformPayedAmount

			v.PlatformPayedAmount = platformPayedAmount

			if len(activityList) > 0 {
				v.ActivityType = strings.Join(activityList, ";")
			} else {
				v.ActivityType = ""
			}
		}(v)

		switch v.Source {
		case 1, 4:
			v.Source = 4
		case 5:
			v.Source = 1
		default:
			v.Source = 3
		}
		if isThirdChannel(v.ChannelId) {
			v.OldOrderSn = oldOrderSnMap[v.ParentOrderSn]
		} else {
			v.OldOrderSn = "--"
		}
		v.Category = Category[v.Source]

		if len(deliveryRecordsMap) > 0 {
			//配送费转成元，没有配送费则为空
			if deliveryInfo, has := deliveryRecordsMap[v.OrderSn]; has {
				if len(deliveryInfo.TotalFeeAfter) > 0 {
					v.StorePayDeliveryAmount = cast.ToString(kit.FenToYuan(cast.ToInt32(deliveryInfo.TotalFeeAfter)))
				}
			}
		}
		if refundAmount, ok := refundAmountMap[v.OrderSn]; ok {
			v.RefundAmount = cast.ToString(refundAmount)
		}

		if v1, ok := orderPerformanceMap[v.ParentOrderSn]; ok {
			v.PerformanceStaffName = v1.StaffName
			v.PerformanceOperatorName = v1.OperatorName
			v.PerformanceOperatorTime = v1.CreateTime.Format(kit.DATETIME_LAYOUT)
			if v2, ok := upetChainMap[v1.PerformanceChainId]; ok {
				v.PerformanceChainName = v2.ChainName
				v.PerformanceFinanceCode = v2.AccountId
			} else if v.PerformanceOperatorName != "系统分配" {
				v.PerformanceChainName = v.ShopName
				v.PerformanceFinanceCode = v.ShopId
			}
		}

	}
	wg.Wait()
	close(ch)

	return details, nil
}

// 阿闻管家实物订单-导出(含商品明细)数据
func AwenOrderExportProduct(params *oc.AwenMaterOrderListRequest) (details []*oc.AwenOrderProductExport, combinedProduct map[string]string, err error) {
	glog.Info("阿闻管家实物订单商品列表导出数据参数：", kit.JsonEncode(params))
	defer glog.Info("QUIT", "阿闻管家实物订单商品列表导出数据参数：", kit.JsonEncode(params))

	conn := NewSlaveDbConn()
	defer conn.Close()

	session := conn.Select(`	order_main.order_sn,
		order_main.order_status,
		order_main.old_order_sn,
		order_main.app_channel,
		order_main.parent_order_sn,
		order_main.channel_id,
		order_main.shop_id,
		order_main.shop_name,
		order_main.order_status_child,
		order_main.total,
		order_main.pay_time,
		order_main.pay_mode,
		order_main.receiver_name,
		order_main.receiver_address,
		order_main.receiver_mobile,
		order_main.create_time,
		refund_order.refund_state,
		order_product.sku_id,
		order_product.parent_sku_id,
		order_product.product_id,
		order_product.product_name,
		order_product.number,
		order_product.pay_price,
		order_product.payment_total,
		order_product.sku_pay_total,
		order_product.product_type,
	order_product.privilege_pt,
		order_detail.buyer_memo,
		order_detail.performance_staff_name,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		order_detail.expected_time,
		order_product.id as order_product_id`).
		Table("order_product").
		Join("left", "`order_main`", "order_main.order_sn = order_product.order_sn").
		Join("left", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Join("left", "`refund_order`", "order_main.order_sn = refund_order.order_sn").
		Where("1=1").GroupBy("order_product.id").Limit(int(params.PageSize), int((params.PageIndex-1)*params.PageSize))
	session.And("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn != ?", "")
	session.And("`order_main`.is_virtual = 0")

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //子订单号
			session.And("`order_main`.order_sn like ?", "%"+params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.parent_order_sn like ?", "%"+params.Keyword+"%")
		case 3: //外部订单
			session.And("`order_main`.old_order_sn like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
		default: //default case
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua", "order_main.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == ChannelJddjId {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	// 订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		session.And("order_main.pay_mode = ?", params.PayMode)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}

	err = session.Find(&details)
	if err != nil {
		err = errors.New("订单导出查询订单列表失败" + err.Error())
		return
	}

	var (
		orderSns        = make([]string, len(details))
		channelProducts = map[int32]map[int32]int32{}
	)
	combinedProduct = make(map[string]string)
	var parentOrderSn []string
	for k, v := range details {
		if v.ProductType == 3 {
			combinedProduct[v.SkuId] = v.ProductName
		}
		orderSns[k] = details[k].OrderSn
		if _, ok := channelProducts[details[k].ChannelId]; !ok {
			channelProducts[details[k].ChannelId] = map[int32]int32{}
		}
		channelProducts[details[k].ChannelId][cast.ToInt32(details[k].ProductId)] = 0
		if isThirdChannel(v.ChannelId) {
			parentOrderSn = append(parentOrderSn, details[k].ParentOrderSn)
		}
	}
	oldOrderSnMap, err1 := FindOldOrderSnInParentOrderSn(conn, parentOrderSn)
	if err1 != nil {
		err = errors.New("查询外部订单号失败" + err1.Error())
		return
	}

	//查询商品店内分类
	/*if err = func() error {
		pcClient := pc.GetDcProductClient()
		defer pcClient.Close()

		ctx := context.Background()
		errG := errgroup.Group{}
		for channelId, products := range channelProducts {
			channelId := channelId
			products := products
			errG.Go(func() error {
				var productIds []int32
				for pId := range products {
					productIds = append(productIds, pId)
				}

				res, pcErr := pcClient.RPC.QueryChannelProductDetail(ctx, &pc.OneofIdRequest{ChannelId: channelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: productIds}}})
				if pcErr != nil {
					glog.Warningf("%v, 查询渠道商品库信息失败, err：%v, channel_id：%v, productIds：%v", params.UserNo, pcErr.Error(), channelId, productIds)
					return nil
				}
				if res.Code != 200 {
					glog.Warningf("%v, 查询渠道商品库信息失败, err：%v, channel_id：%v, skus：%v", params.UserNo, res.Message, channelId, productIds)
					return nil
				}
				if len(res.Details) == 0 {
					glog.Warningf("%v, 查询渠道商品库信息失败, 没有查到商品信息, channel_id：%v, skus：%v", params.UserNo, channelId, productIds)
					return nil
				}

				products = map[int32]int32{}
				channelCategoryIds := make([]int32, len(res.Details))
				for k := range res.Details {
					channelCategoryIds[k] = res.Details[k].Product.ChannelCategoryId
					products[res.Details[k].Product.Id] = res.Details[k].Product.ChannelCategoryId
				}

				//通过分类id查询分类名称
				var catRes *pc.CategoryResponse
				catRes, pcErr = pcClient.RPC.QueryChannelCategory(ctx, &pc.CategoryRequest{
					Where: &pc.Category{
						ChannelId: ChannelAwenId,
					},
					PageIndex: 1,
					PageSize:  9999,
				})
				if pcErr != nil {
					glog.Warningf("%v, 查询渠道商品库分类信息失败, err：%v, channel_id：%v, skus：%v", params.UserNo, pcErr.Error(), channelId, productIds)
					return nil
				}
				if catRes.Code != 200 {
					glog.Warningf("%v, 查询渠道商品库分类信息失败, err：%v, channel_id：%v, skus：%v", params.UserNo, res.Message, channelId, productIds)
					return nil
				}
				if len(catRes.Details) == 0 {
					glog.Warningf("%v, 查询渠道商品库分类信息失败, 没有查到分类信息, channel_id：%v, skus：%v", params.UserNo, channelId, productIds)
					return nil
				}

				categoryMap := map[int32]string{}
				for k := range catRes.Details {
					categoryMap[catRes.Details[k].Id] = catRes.Details[k].Name
				}

				for k := range details {
					if details[k].ChannelId != channelId {
						continue
					}
					details[k].ChannelCategoryName = categoryMap[products[cast.ToInt32(details[k].ProductId)]]
				}

				return nil
			})
		}
		if err = errG.Wait(); err != nil {
			return err
		}

		return nil
	}(); err != nil {
		return
	}*/

	// 查询商品退货信息
	refundGoodsMap := map[string][]*dto.RefundOrderGoods{}
	if err = func() error {

		var refundGoods []*dto.RefundOrderGoods
		if err = conn.Select("a.order_sn, b.sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price,b.order_product_id").Table("refund_order").Alias("a").
			Join("left", "refund_order_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("a.order_sn", orderSns).
			In("a.channel_id", ChannelAwenId, ChannelDigitalHealth).
			Find(&refundGoods); err != nil {
			return err
		}

		//第三方订单退款数据跟阿闻不一致，需要另外取
		var thirdRefundGoos []*dto.RefundOrderGoods
		if err = conn.Select("o.order_sn, b.sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price,b.order_product_id").Table("refund_order").Alias("a").
			Join("inner", "order_main o", "a.order_sn = o.parent_order_sn").
			Join("left", "refund_order_third_product b", "o.order_sn = b.order_sn").
			Where("a.refund_state = 3").In("o.order_sn", orderSns).
			In("o.channel_id", ChannelMtId, ChannelElmId, ChannelJddjId).Distinct("b.id").
			Find(&thirdRefundGoos); err != nil {
			return err
		}
		for k := range refundGoods {
			refundGoodsMap[refundGoods[k].OrderSn] = append(refundGoodsMap[refundGoods[k].OrderSn], refundGoods[k])
		}
		for k := range thirdRefundGoos {
			refundGoodsMap[thirdRefundGoos[k].OrderSn] = append(refundGoodsMap[thirdRefundGoos[k].OrderSn], thirdRefundGoos[k])
		}

		return nil
	}(); err != nil {
		return
	}

	if len(refundGoodsMap) == 0 {
		return
	}

	for i := range details {
		if isThirdChannel(details[i].ChannelId) {
			details[i].OldOrderSn = oldOrderSnMap[details[i].ParentOrderSn]
		} else {
			details[i].OldOrderSn = "--"
		}
		for j := range refundGoodsMap[details[i].OrderSn] {
			if details[i].OrderSn == refundGoodsMap[details[i].OrderSn][j].OrderSn &&
				details[i].SkuId == refundGoodsMap[details[i].OrderSn][j].SkuId &&
				details[i].OrderProductId == refundGoodsMap[details[i].OrderSn][j].OrderProductId {
				details[i].RefundNumber += refundGoodsMap[details[i].OrderSn][j].Tkcount
				details[i].RefundTotal += cast.ToInt32(kit.YuanToFen(cast.ToFloat64(refundGoodsMap[details[i].OrderSn][j].RefundAmount)))
			}
		}
	}

	return
}

// 阿闻管家父订单-导出订单数据
func AwenParentOrderExport(params *oc.AwenParentOrderListRequest) (details []*oc.AwenParentOrderExport, err error) {
	glog.Info("阿闻管家父订单-导出订单数据：", kit.JsonEncode(params))
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()

	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Join("left", "order_main zd", "zd.parent_order_sn = order_main.order_sn").
		Join("left", "order_exception oe", "zd.order_sn = oe.order_sn AND zd.order_status=20 and oe.is_show=1").
		Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ''")

	session.OrderBy("`order_main`.`create_time` DESC")
	if params.CombineType == 1 || params.CombineType == 2 || params.CombineType == 3 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("`order_product`.combine_type = ? and `order_product`.product_type = ?  ", params.CombineType, 3)
	} else if params.CombineType == 4 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("NOT EXISTS(SELECT op.order_sn FROM order_product op WHERE op.order_sn=`order_main`.order_sn and op.product_type = 3) ")
	} else {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
	}

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //订单号
			session.And("`order_main`.order_sn like ?", params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
		case 3: //收货人姓名
			session.And("`order_main`.receiver_name like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_mobile = ?", utils.MobileEncrypt(params.Keyword))
		case 5: //买家手机号
			session.And("`order_main`.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
		case 6: //店铺名称
			session.And("`order_main`.shop_name like ?", "%"+params.Keyword+"%")
		case 7: //子订单号
			session.And("order_main.order_sn in (select parent_order_sn from order_main where order_sn = ?)", params.Keyword)
		default: //default case
		}
	}

	session.And("order_main.org_id = ?", params.Orgid)
	if params.Orgid == 6 {
		session.In("order_main.shop_id", params.Shopids)
	} else {
		//筛选用户权限门店
		if len(params.UserNo) > 0 {
			session.Join("inner", "datacenter.`store_user_authority` sua", "`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
		}

		//登录用户有权限的所有门店id(财务编码)
		if len(params.Shopids) > 0 {
			session.In("`order_main`.shop_id", params.Shopids)
		}
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}
	//销售渠道
	if params.SaleChannel > 0 {
		session.And("`order_main`.user_agent = ?", params.SaleChannel)
	}
	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	if params.OrderDeliveryFilter != 0 {
		if params.OrderDeliveryFilter == 1 {
			session.And("oe.delivery_id is null")
		} else if params.OrderDeliveryFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("oe.delivery_id is not null")
		}
	}
	//支付方式
	if params.PayMode > 0 {
		if params.Orgid == 6 {
			session.Join("left", "order_payment", "order_main.order_sn=order_payment.order_sn").Where("order_payment.pay_type=?", params.PayMode)
		} else {
			session.And("order_main.pay_mode = ?", params.PayMode)
		}

	}

	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}

	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}
	//异常订单搜索
	if params.OrderFilter > 0 {
		if params.OrderFilter == 1 {
			session.And("(order_detail.push_delivery_reason='' or order_detail.push_delivery=1) and (order_detail.push_third_order_reason='' or order_detail.push_third_order=1) and (order_detail.split_order_fail_reason='' or order_detail.split_order_result in(0,1))")
		} else if params.OrderFilter == 2 {
			session.And("(order_detail.push_delivery_reason<>'' and order_detail.push_delivery=0) or (order_detail.push_third_order_reason<>'' and order_detail.push_third_order=0) or (order_detail.split_order_fail_reason<>'' and order_detail.split_order_result=2)")
		}
	}
	if err = session.Select(`CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_main.old_order_sn gy_order_sn,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.accept_time,
		order_detail.delivery_remark,
		order_detail.buyer_memo,
		order_detail.is_picking,
		order_detail.picking_time,
		order_detail.extras,
		order_detail.pickup_code serial_number,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
		order_detail.is_new_customer,
		order_detail.performance_operator_time,
		order_detail.expected_time,
		order_detail.bill_completed_time,
		order_detail.bill_canceled_time,
		order_detail.trade_created_time,
		order_detail.trade_payment_time,
		order_detail.trade_time,
		order_detail.pick_user_id,
		order_detail.pick_user_name,
		order_product.product_id,
        MAX(order_product.combine_type) group_type,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
(order_main.pt_charge_total-order_main.pt_freight_privilege) privilege_pt
		`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1)").
		Find(&details); err != nil {
		glog.Error("zx订单导出报20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(details) == 0 {
		return
	}
	//查询出来的是主单的退款
	var orderSns []string
	var shopIds []int64
	var awenOrderSn, thirdOrderSn []string
	for _, v := range details {
		orderSns = append(orderSns, v.OrderSn)
		shopIds = append(shopIds, cast.ToInt64(v.ShopId))
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
	}

	var performanceChainId []int32
	orderPerformanceMap, err1 := FindOrderPerformanceMapByOrderSn(dbConn, orderSns)
	if err1 != nil {
		err = errors.New("查询业绩归属信息失败" + err1.Error())
		return
	}
	for _, v := range orderPerformanceMap {
		if v.PerformanceChainId > 0 {
			performanceChainId = append(performanceChainId, v.PerformanceChainId)
		}
	}
	upetChainMap, err1 := FindUpetChainMap(performanceChainId)
	if err1 != nil {
		err = errors.New("查询分销门店信息失败" + err1.Error())
		return
	}

	//查询退款信息
	mainOrderRefund := &MainOrderRefund{
		AwenOrderSn:  awenOrderSn,
		ThirdOrderSn: thirdOrderSn,
	}
	//计算退款金额
	refundAmountMap := mainOrderRefund.CalRefundAmount()

	//查询订单优惠信息
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	if err = func() error {
		var orderPromotion []*models.OrderPromotion
		if err = dbConn.Select("order_sn,promotion_type,pt_charge,promotion_title").In("order_sn", orderSns).Find(&orderPromotion); err != nil {
			return err
		}

		//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
		for k := range orderPromotion {
			orderPromotionMap[orderPromotion[k].OrderSn] = append(orderPromotionMap[orderPromotion[k].OrderSn], orderPromotion[k])
		}

		return nil
	}(); err != nil {
		err = errors.New("查询订单优惠信息失败, " + err.Error())
		return
	}
	chainIds := make([]int64, 0)
	chainMap := make(map[int64]models.TChain)
	chainList := make([]models.TChain, 0)
	tenantList := make([]models.TTenant, 0)
	tenantMap := make(map[int64]models.TTenant)
	tenantRetailCfgList := make([]models.TTenantRetailCfg, 0)
	tenantRetailCfgMap := make(map[int64]models.TTenantRetailCfg)
	orderPaymentMap := make(map[string][]models.OrderPayment)
	if params.Orgid == 6 {
		if e := dbConn.Table("eshop_saas.t_tenant").In("id", shopIds).Find(&tenantList); e != nil {
			err = errors.New("查询店铺信息失败" + e.Error())
			return
		}
		for _, tenant := range tenantList {
			tenantMap[tenant.Id] = tenant
			chainIds = append(chainIds, tenant.ChainId)
		}

		if e := dbConn.Table("eshop_saas.t_tenant_retail_cfg").In("tenant_id", shopIds).Find(&tenantRetailCfgList); e != nil {
			err = errors.New("店铺新零售运营配置失败" + e.Error())
			return
		}
		for _, retailcfg := range tenantRetailCfgList {
			tenantRetailCfgMap[retailcfg.TenantId] = retailcfg

		}

		if e := dbConn.Table("eshop_saas.t_chain").In("id", chainIds).Find(&chainList); e != nil {
			err = errors.New("查询连锁信息失败" + e.Error())
			return
		}
		for _, chain := range chainList {
			chainMap[chain.Id] = chain
		}

		_, orderPaymentMap, err = new(models.OrderPayment).GetOrderPayment(dbConn, models.OrderPaymentRequest{
			OrderSns: orderSns,
		})
		if err != nil {
			err = errors.New("查询支付方式失败" + err.Error())
			return
		}

	}
	//查询订单优惠信息
	var deliveryRecords []*models.OrderDeliveryRecord

	var allChildOrderSn []string
	parentAndChildRelationMap := make(map[string][]string)
	//运费信息中的订单号为子订单号 需要根据子订单查询运费
	//查询子订单
	var childOrders []*models.OrderMain
	err = dbConn.Select("order_sn,parent_order_sn,old_order_sn").In("parent_order_sn", orderSns).Find(&childOrders)
	if err == nil {
		if len(childOrders) > 0 {
			for _, v := range childOrders {
				allChildOrderSn = append(allChildOrderSn, v.OrderSn)
				if _, has := parentAndChildRelationMap[v.ParentOrderSn]; has {
					parentAndChildRelationMap[v.ParentOrderSn] = append(parentAndChildRelationMap[v.ParentOrderSn], v.OldOrderSn)
				} else {
					parentAndChildRelationMap[v.ParentOrderSn] = []string{v.OrderSn}
				}
			}
		}
	}

	if len(allChildOrderSn) > 0 {
		//取配送流程最新一条记录并且骑手姓名不为空且状态不为99的数据
		if err := dbConn.Table("order_delivery_record").
			Select("order_delivery_record.*").
			Join("inner", "order_delivery_node", "order_delivery_record.delivery_id = order_delivery_node.delivery_id").
			Where("order_delivery_node.courier_name != '' AND order_delivery_node.delivery_status !=99").
			In("order_delivery_record.order_sn", allChildOrderSn).
			GroupBy("order_delivery_record.order_sn").
			OrderBy("order_delivery_record.id DESC,order_delivery_node.create_time DESC").
			Find(&deliveryRecords); err != nil {
			glog.Error("订单导出查询订单列表错误！ ", err.Error())
		}
	}

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	deliveryRecordsMap := make(map[string]*models.OrderDeliveryRecord, len(deliveryRecords))

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	for _, v := range deliveryRecords {
		deliveryRecordsMap[v.OrderSn] = v
	}

	wg := sync.WaitGroup{}
	ch := make(chan int8, runtime.NumCPU())
	for _, v := range details {
		ch <- 1
		wg.Add(1)
		go func(v *oc.AwenParentOrderExport) {
			defer func() {
				<-ch
				wg.Done()
			}()

			var activityList []string
			var platformPayedAmount int32

			//配送费优惠
			var deliveryPlatformPayedAmount int32

			//优惠活动解析
			for _, promotion := range orderPromotionMap[v.OrderSn] {
				if v.ChannelId == 1 || v.ChannelId == 100 {
					activityList = append(activityList, promotion.PromotionTitle)
				} else {
					activityList = append(activityList, dto.OrderPrivilegeActiveType(promotion.PromotionType).String())
				}
				platformPayedAmount += promotion.PtCharge

				if promotion.PromotionType == 25 || promotion.PromotionType == 4011 || promotion.PromotionType == 30 {
					deliveryPlatformPayedAmount += promotion.PtCharge
				}

			}

			// 添加平台补贴
			v.PlatformPayedAmount = platformPayedAmount

			//平台配送费优惠金额 = 配送费优惠金额-商家配送费优惠金额
			v.PlatformFreightPrivilege = deliveryPlatformPayedAmount

			if len(activityList) > 0 {
				v.ActivityType = strings.Join(activityList, ";")
			} else {
				v.ActivityType = ""
			}

			if tenant, ok := tenantMap[cast.ToInt64(v.ShopId)]; ok {
				//店铺类型：1-直营店、2-加盟店、3-其他
				if tenant.Type == 1 {
					v.TenantType = "直营店"
				} else if tenant.Type == 2 {
					v.TenantType = "加盟店"
				}

				if chain, ok := chainMap[tenant.ChainId]; ok {
					v.ChainName = chain.Name
				}
			}

			if cfg, ok := tenantRetailCfgMap[cast.ToInt64(v.ShopId)]; ok {
				if cfg.OperationType == 1 {
					v.TenantRetailOperationType = "独立运营"
				} else if cfg.OperationType == 2 {
					v.TenantRetailOperationType = "代运营"
				}
			}

			if orderPayment, ok := orderPaymentMap[v.OrderSn]; ok {
				for _, payment := range orderPayment {
					v.PayModeText += "," + models.PayTypeTextMap[payment.PayType]
				}
			}

		}(v)

		switch v.Source {
		case 1, 4:
			v.Source = 4
		case 5:
			v.Source = 1
		default:
			v.Source = 3
		}

		v.Category = Category[v.Source]
		if params.Orgid == 6 && v.Category == "前置仓" {
			v.Category = "加盟仓"
		}
		if params.CombineType == 1 || params.CombineType == 2 {
			v.GroupType = params.CombineType
		} else if params.CombineType == 4 {
			v.GroupType = 0
		}

		childInfo, _ := parentAndChildRelationMap[v.OrderSn]

		if len(deliveryRecordsMap) > 0 {
			//配送费转成元，没有则为空
			for _, childOrderSn := range childInfo {
				if deliveryInfo, has := deliveryRecordsMap[childOrderSn]; has {
					if len(deliveryInfo.TotalFeeAfter) > 0 {
						intStorePayDeliveryAmount := cast.ToFloat64(v.StorePayDeliveryAmount)
						intTotalFeeAfter := kit.FenToYuan(cast.ToInt32(deliveryInfo.TotalFeeAfter))
						v.StorePayDeliveryAmount = cast.ToString(intStorePayDeliveryAmount + intTotalFeeAfter)
					}
				}
			}
		}

		if refundAmount, ok := refundAmountMap[v.OrderSn]; ok {
			v.RefundAmount = cast.ToString(refundAmount)
		}

		if v1, ok := orderPerformanceMap[v.OrderSn]; ok {
			v.PerformanceStaffName = v1.StaffName
			v.PerformanceOperatorName = v1.OperatorName
			v.PerformanceOperatorTime = v1.CreateTime.Format(kit.DATETIME_LAYOUT)
			if v2, ok := upetChainMap[v1.PerformanceChainId]; ok {
				v.PerformanceChainName = v2.ChainName
				v.PerformanceFinanceCode = v2.AccountId
			} else if v.PerformanceOperatorName != "系统分配" {
				v.PerformanceChainName = v.ShopName
				v.PerformanceFinanceCode = v.ShopId
			}
		}
	}
	wg.Wait()
	close(ch)

	return details, nil
}

// 阿闻管家父订单-导出(含商品明细)数据
func AwenParentOrderProductExport(params *oc.AwenParentOrderListRequest) (details []*oc.AwenParentOrderProductExport, combinedProduct map[string]string, err error) {
	glog.Info("阿闻管家父订单商品列表导出数据参数：", kit.JsonEncode(params))
	defer glog.Info("QUIT", "阿闻管家父订单商品列表导出数据参数：", kit.JsonEncode(params))

	conn := NewSlaveDbConn()
	defer conn.Close()

	var baseOrder []*oc.AwenParentOrderExport
	//先把符合要求的订单查出来
	//订单表，商品表，品牌表, 业绩表关联查询
	session := conn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Join("left", "order_main zd", "zd.parent_order_sn = order_main.order_sn").
		Join("left", "order_exception oe", "zd.order_sn = oe.order_sn AND zd.order_status=20 and oe.is_show=1").
		Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ?", "")

	session.OrderBy("`order_main`.`create_time` DESC")
	if params.CombineType == 1 || params.CombineType == 2 || params.CombineType == 3 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("`order_product`.combine_type = ? and `order_product`.product_type = ?  ", params.CombineType, 3)
	} else if params.CombineType == 4 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("NOT EXISTS(SELECT op.order_sn FROM order_product op WHERE op.order_sn=`order_main`.order_sn and op.product_type = 3) ")
	} else {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
	}

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //订单号
			session.And("`order_main`.order_sn like ?", params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
		case 3: //收货人姓名
			session.And("`order_main`.receiver_name like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_mobile = ?", utils.MobileEncrypt(params.Keyword))
		case 5: //买家手机号
			session.And("`order_main`.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
		case 6: //店铺名称
			session.And("`order_main`.shop_name like ?", "%"+params.Keyword+"%")
		case 7: //子订单号
			session.And("order_main.order_sn in (select parent_order_sn from order_main where order_sn = ?)", params.Keyword)
		default: //default case
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua", "`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}
	//销售渠道
	if params.SaleChannel > 0 {
		session.And("`order_main`.user_agent = ?", params.SaleChannel)
	}
	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		session.And("order_main.pay_mode = ?", params.PayMode)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	if params.OrderDeliveryFilter != 0 {
		if params.OrderDeliveryFilter == 1 {
			session.And("oe.delivery_id is null")
		} else if params.OrderDeliveryFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("oe.delivery_id is not null")
		}
	}
	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}
	//异常订单搜索
	if params.OrderFilter > 0 {
		if params.OrderFilter == 1 {
			session.And("(order_detail.push_delivery_reason='' or order_detail.push_delivery=1) and (order_detail.push_third_order_reason='' or order_detail.push_third_order=1) and (order_detail.split_order_fail_reason='' or order_detail.split_order_result in(0,1))")
		} else if params.OrderFilter == 2 {
			session.And("(order_detail.push_delivery_reason<>'' and order_detail.push_delivery=0) or (order_detail.push_third_order_reason<>'' and order_detail.push_third_order=0) or (order_detail.split_order_fail_reason<>'' and order_detail.split_order_result=2)")
		}
	}
	if err = session.Select(`order_main.order_sn,order_main.channel_id,MAX(order_product.combine_type) group_type`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1)").
		Find(&baseOrder); err != nil {
		glog.Info("zx订单导出报错20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(baseOrder) == 0 {
		return
	}

	var (
		orderSns   = make([]string, len(baseOrder))
		orderGroup = make(map[string]int32)
	)

	for k := range baseOrder {
		orderSns[k] = baseOrder[k].OrderSn
		orderGroup[baseOrder[k].OrderSn] = baseOrder[k].GroupType
	}

	session = conn.Select(`	order_main.order_sn,
		order_main.old_order_sn,
		order_main.parent_order_sn as gy_order_sn,
		order_main.app_channel,
		order_main.channel_id,
		order_main.shop_id,
		order_main.member_id,
		order_main.shop_name,
		order_main.order_status_child,
		order_main.total,
		order_main.pay_time,
		order_main.pay_mode,
		order_main.receiver_name,
		order_main.receiver_address,
		order_main.receiver_mobile,
		order_main.create_time,
		refund_order.refund_amount,
		refund_order.refund_state,
		order_product.sku_id,
		order_product.parent_sku_id,
		order_product.product_id,
		order_product.product_name,
		order_product.number,
		order_product.pay_price,
		order_product.payment_total,
		order_product.sku_pay_total,
		order_product.product_type,
		order_detail.performance_staff_name,
		order_detail.buyer_memo,
        order_product.combine_type group_type,
		order_product.channel_category_name channel_category_name,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		order_detail.expected_time`).
		Table("order_product").
		Join("left", "`order_main`", "order_main.order_sn = order_product.order_sn").
		Join("left", "refund_order", "order_main.order_sn=refund_order.order_sn").
		Join("left", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		OrderBy("`order_main`.`create_time` DESC").In("order_product.order_sn", orderSns).GroupBy("order_product.id")

	err = session.Find(&details)
	if err != nil {
		glog.Info("zx订单导出报错20221124")
		err = errors.New("订单导出查询订单列表失败" + err.Error())
		return
	}

	// 查询商品退货信息
	refundGoodsMap := map[string][]*dto.RefundOrderGoods{}
	if err = func() error {
		var refundGoods []*dto.RefundOrderGoods
		if err = conn.Select("o.parent_order_sn order_sn, b.sku_id,b.tkcount,b.parent_sku_id, b.refund_amount, b.product_price,b.refund_price").Table("refund_order").Alias("a").
			Join("inner", "order_main o", "o.order_sn = a.order_sn").
			Join("left", "refund_order_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("o.parent_order_sn", orderSns).
			In("a.channel_id", ChannelAwenId, ChannelDigitalHealth).
			Find(&refundGoods); err != nil {
			glog.Info("zx订单导出报错20221124")
			return err
		}

		//第三方订单退款数据跟阿闻不一致，需要另外取
		var thirdRefundGoos []*dto.RefundOrderGoods
		if err = conn.Select("a.order_sn, b.sku_id,b.parent_sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price").Table("refund_order").Alias("a").
			Join("inner", "order_main o", "a.order_sn = o.order_sn").
			Join("left", "refund_order_third_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("o.order_sn", orderSns).
			In("o.channel_id", ChannelMtId, ChannelElmId, ChannelJddjId).
			Find(&thirdRefundGoos); err != nil {
			glog.Info("zx订单导出报错20221124")
			return err
		}
		for k := range refundGoods {
			refundGoodsMap[refundGoods[k].OrderSn] = append(refundGoodsMap[refundGoods[k].OrderSn], refundGoods[k])
		}
		for k := range thirdRefundGoos {
			refundGoodsMap[thirdRefundGoos[k].OrderSn] = append(refundGoodsMap[thirdRefundGoos[k].OrderSn], thirdRefundGoos[k])
		}

		return nil
	}(); err != nil {
		return
	}

	for i := range details {
		if params.CombineType == 1 || params.CombineType == 2 {
			details[i].GroupType = params.CombineType
		} else if params.CombineType == 4 {
			details[i].GroupType = 0
		}

		if _, ok := orderGroup[details[i].OrderSn]; ok {
			details[i].GroupType = orderGroup[details[i].OrderSn]
		}

		if len(refundGoodsMap) > 0 {
			for j := range refundGoodsMap[details[i].OrderSn] {
				if details[i].OrderSn == refundGoodsMap[details[i].OrderSn][j].OrderSn &&
					details[i].SkuId == refundGoodsMap[details[i].OrderSn][j].SkuId &&
					details[i].PayPrice == refundGoodsMap[details[i].OrderSn][j].RefundPrice &&
					details[i].ParentSkuId == refundGoodsMap[details[i].OrderSn][j].ParentSkuId {
					details[i].RefundNumber += refundGoodsMap[details[i].OrderSn][j].Tkcount
					details[i].RefundTotal += cast.ToInt32(kit.YuanToFen(cast.ToFloat64(refundGoodsMap[details[i].OrderSn][j].RefundAmount)))
				}
			}
		}
	}

	return
}

// 阿闻管家虚拟订单-导出订单数据
func AwenVirtualOrderExport(params *oc.AwenVirtualOrderListRequest) (details []*oc.AwenVirtualOrderExport, err error) {
	glog.Info("阿闻管家虚拟订单-导出订单数据：", kit.JsonEncode(params))
	defer glog.Info("QUIT", "阿闻管家虚拟订单-导出订单数据：", kit.JsonEncode(params))
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()
	dbConn.ShowSQL(true)
	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
		Join("inner", "order_verify_code", "order_main.order_sn=order_verify_code.order_sn").
		Where("order_main.parent_order_sn != ?", "")
	session.And("`order_main`.is_virtual = 1")

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //子订单号
			session.And("`order_main`.order_sn like ?", "%"+params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.parent_order_sn like ?", "%"+params.Keyword+"%")
		case 3: //外部订单
			session.And("`order_main`.old_order_sn like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
		default: //default case
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua", "`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else if params.TimeType == 2 { //核销时间
		session.And("order_verify_code.verify_status = 1")
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_verify_code`.verify_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_verify_code`.verify_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}

	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}

	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 30100:
			session.And("`order_main`.order_status = 0")
		case 30101:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30102:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30103:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	//订单类型
	if len(params.OrderType) > 0 {
		session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
	}

	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}

	if err = session.Select(`CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.accept_time,
		order_detail.is_picking,
		order_detail.picking_time,
		order_detail.extras,
		order_detail.is_new_customer,
		order_detail.pickup_code serial_number,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
  max(order_product.privilege_pt) as privilege_pt,
		order_detail.performance_operator_time `).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1), `order_main`.`create_time` DESC ").
		Find(&details); err != nil {
		glog.Error("zx订单导出报20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(details) == 0 {
		return
	}

	orderSns := make([]string, len(details))
	var awenOrderSn, thirdOrderSn []string
	var parentOrderSn []string
	var iParentOrderSn []string
	for k, v := range details {
		orderSns[k] = details[k].OrderSn
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
			parentOrderSn = append(parentOrderSn, v.ParentOrderSn)
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
		iParentOrderSn = append(iParentOrderSn, v.ParentOrderSn)
	}
	oldOrderSnMap, err1 := FindOldOrderSnInParentOrderSn(dbConn, parentOrderSn)
	if err1 != nil {
		err = errors.New("查询外部订单号失败" + err1.Error())
		return
	}

	var performanceChainId []int32
	orderPerformanceMap, err1 := FindOrderPerformanceMapByOrderSn(dbConn, iParentOrderSn)
	if err1 != nil {
		err = errors.New("查询业绩归属信息失败" + err1.Error())
		return
	}
	for _, v := range orderPerformanceMap {
		if v.PerformanceChainId > 0 {
			performanceChainId = append(performanceChainId, v.PerformanceChainId)
		}
	}
	upetChainMap, err1 := FindUpetChainMap(performanceChainId)
	if err1 != nil {
		err = errors.New("查询分销门店信息失败" + err1.Error())
		return
	}

	virtualOrderRefund := &VirtualOrderRefund{
		AwenOrderSn:  awenOrderSn,
		ThirdOrderSn: thirdOrderSn,
	}
	//计算退款金额
	refundAmountMap := virtualOrderRefund.CalRefundAmount()

	//查询订单优惠信息
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	if err = func() error {
		var orderPromotion []*models.OrderPromotion
		if err = dbConn.Select("order_sn,promotion_type,pt_charge").In("order_sn", orderSns).Find(&orderPromotion); err != nil {
			return err
		}

		//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
		for k := range orderPromotion {
			orderPromotionMap[orderPromotion[k].OrderSn] = append(orderPromotionMap[orderPromotion[k].OrderSn], orderPromotion[k])
		}

		return nil
	}(); err != nil {
		err = errors.New("查询订单优惠信息失败, " + err.Error())
		return
	}

	wg := sync.WaitGroup{}
	ch := make(chan int8, runtime.NumCPU())
	for _, v := range details {
		ch <- 1
		wg.Add(1)
		//v := v
		go func(v *oc.AwenVirtualOrderExport) {
			defer func() {
				<-ch
				wg.Done()
			}()

			var activityList []string
			var platformPayedAmount int32

			//优惠活动解析
			for _, promotion := range orderPromotionMap[v.OrderSn] {
				activityList = append(activityList, dto.OrderPrivilegeActiveType(promotion.PromotionType).String())
				platformPayedAmount += promotion.PtCharge
			}
			if len(activityList) > 0 {
				v.ActivityType = strings.Join(activityList, ";")
			} else {
				v.ActivityType = ""
			}

			//前端的商品总额是不包含所有其他优惠运费服务费的, 而后端的商品总额是减去运费的, 所以要加上
			//门店营收金额 = 客户实付金额+(优惠明细)美团承担的成本-平台服务费
			v.ActualReceiveTotal = v.Total + int32(platformPayedAmount) - v.ServiceCharge
		}(v)

		switch v.Source {
		case 1, 4:
			v.Source = 4
		case 5:
			v.Source = 1
		default:
			v.Source = 3
		}

		v.Category = Category[v.Source]
		if isThirdChannel(v.ChannelId) {
			v.OldOrderSn = oldOrderSnMap[v.ParentOrderSn]
		} else {
			v.OldOrderSn = "--"
		}

		if refundAmount, ok := refundAmountMap[v.OrderSn]; ok {
			v.RefundAmount = cast.ToString(refundAmount)
		}

		if v1, ok := orderPerformanceMap[v.ParentOrderSn]; ok {
			v.PerformanceStaffName = v1.StaffName
			v.PerformanceOperatorName = v1.OperatorName
			v.PerformanceOperatorTime = v1.CreateTime.Format(kit.DATETIME_LAYOUT)
			if v2, ok := upetChainMap[v1.PerformanceChainId]; ok {
				v.PerformanceChainName = v2.ChainName
				v.PerformanceFinanceCode = v2.AccountId
			} else if v.PerformanceOperatorName != "系统分配" {
				v.PerformanceChainName = v.ShopName
				v.PerformanceFinanceCode = v.ShopId
			}
		}
	}
	wg.Wait()
	close(ch)

	return details, nil
}

// 阿闻管家虚拟订单-导出(含商品明细)数据
func AwenVirtualOrderProductExport(params *oc.AwenVirtualOrderListRequest) (details []*oc.AwenVirtualOrderProductExport, combinedProduct map[string]string, err error) {
	glog.Info("AwenVirtualOrderProductExport阿闻管家订单商品列表导出数据参数：", kit.JsonEncode(params))
	defer glog.Info("QUIT", "阿闻管家订单商品列表导出数据参数：", kit.JsonEncode(params))

	conn := NewSlaveDbConn()
	defer conn.Close()
	session := conn.Select(`	
		order_verify_code.verify_code,
		order_verify_code.verify_time,
		order_verify_code.verify_shop,
		order_verify_code.verify_status,
		order_main.order_sn,
		order_main.order_status,
		order_main.old_order_sn,
		order_main.parent_order_sn,
		order_main.channel_id,
		order_main.shop_id,
		order_main.shop_name,
		order_main.order_status_child,
		order_main.order_status,
		order_main.total,
		order_main.pay_time,
		order_main.pay_mode,
		order_product.sku_id,
		order_product.parent_sku_id,
		order_product.product_id,
		order_product.product_name,
		order_product.number,
		order_product.pay_price,
		order_product.payment_total,
		order_product.sku_pay_total,
		order_product.product_type,
order_product.privilege_pt,
		order_product.channel_category_name,
		order_detail.performance_staff_name`).
		Table("order_product").
		Join("left", "`order_main`", "order_main.order_sn = order_product.order_sn").
		Join("left", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "order_verify_code", "order_main.order_sn=order_verify_code.order_sn").
		OrderBy("order_product.order_sn").
		Limit(int(params.PageSize), int((params.PageIndex-1)*params.PageSize))
	session.Where("order_main.parent_order_sn != ?", "")
	session.And("`order_main`.is_virtual = 1")

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //子订单号
			session.And("`order_main`.order_sn like ?", "%"+params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.parent_order_sn like ?", "%"+params.Keyword+"%")
		case 3: //外部订单
			session.And("`order_main`.old_order_sn like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
		default: //default case
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua", "order_main.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else if params.TimeType == 2 {
		//核销时间
		if len(params.StartTime) > 0 {
			session.And("`order_verify_code`.verify_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_verify_code`.verify_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}

	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 30100:
			session.And("`order_main`.order_status = 0")
		case 30101:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30102:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30103:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	// 订单类型
	if len(params.OrderType) > 0 {
		session.In("order_main.order_type", strings.Split(params.OrderType, ","))
	}

	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}

	err = session.GroupBy("order_verify_code.verify_code").Find(&details)
	if err != nil {
		err = errors.New("订单导出查询订单列表失败" + err.Error())
		return
	}

	var (
		orderSns        = make([]string, len(details))
		thirdOrderSn    []string
		awenOrderSn     []string
		channelProducts = map[int32]map[int32]int32{}
	)
	combinedProduct = make(map[string]string)
	var parentOrderSn []string
	for k, v := range details {
		if v.ProductType == 3 {
			combinedProduct[v.SkuId] = v.ProductName
		}
		orderSns[k] = details[k].OrderSn
		if _, ok := channelProducts[details[k].ChannelId]; !ok {
			channelProducts[details[k].ChannelId] = map[int32]int32{}
		}
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
			parentOrderSn = append(parentOrderSn, v.ParentOrderSn)
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
		channelProducts[details[k].ChannelId][cast.ToInt32(details[k].ProductId)] = 0
	}
	oldOrderSnMap, err1 := FindOldOrderSnInParentOrderSn(conn, parentOrderSn)
	if err1 != nil {
		err = errors.New("查询外部订单号失败" + err1.Error())
		return
	}

	storeMap := make(map[string]*models.Store)
	var stores []*models.Store
	if err = conn.Table("datacenter.store").Select("zilong_id,finance_code,name").Find(&stores); err != nil {
		return
	}
	for _, store := range stores {
		storeMap[store.ZilongId] = store
	}

	// 查询商品退货信息
	refundGoodsCountMap := make(map[string]int32)
	refundGoodsAmountMap := make(map[string]int32)
	if err = func() error {
		var refundGoods, thirdRefundGoods []*dto.RefundOrderGoods
		//阿闻的退款商品
		if err = conn.Select("a.order_sn, b.sku_id,b.parent_sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price ").
			Table("refund_order").
			Alias("a").
			Join("left", "refund_order_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("a.order_sn", awenOrderSn).
			Find(&refundGoods); err != nil {
			return err
		}
		for k := range refundGoods {
			refundGoodsCountMap[refundGoods[k].OrderSn] += refundGoods[k].Tkcount
			refundGoodsAmountMap[refundGoods[k].OrderSn] += int32(kit.YuanToFen(cast.ToFloat64(refundGoods[k].RefundAmount)))
		}

		//第三方的退款商品 //退款金额
		if err = conn.Select("b.order_sn, b.sku_id,b.parent_sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price ").
			Table("refund_order").
			Alias("a").
			Join("left", "refund_order_third_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("b.order_sn", thirdOrderSn).
			Find(&thirdRefundGoods); err != nil {
			return err
		}
		for k := range thirdRefundGoods {
			refundGoodsCountMap[thirdRefundGoods[k].OrderSn] += thirdRefundGoods[k].Tkcount
			refundGoodsAmountMap[thirdRefundGoods[k].OrderSn] += int32(kit.YuanToFen(cast.ToFloat64(thirdRefundGoods[k].RefundAmount)))
		}

		return nil
	}(); err != nil {
		return
	}

	refundCodes := make(map[string]int32)
	refundCodesAmount := make(map[string]int32)
	for i, detail := range details {
		detail.PayPrice = cast.ToInt32(details[i].PaymentTotal / details[i].Number) //总的支付金额 /除以数量
		verifyTime := "--"

		if detail.VerifyStatus == "1" {
			detail.VerifyStatus = "已核销"
			if store, has := storeMap[detail.VerifyShop]; has {
				detail.VerifyShop = store.Name
				detail.FinanceCodes = store.FinanceCode
			}
			verifyTime = detail.VerifyTime
		} else if detail.VerifyStatus == "2" {
			detail.VerifyStatus = "已退款"
			if refundCodes[detail.OrderSn] < refundGoodsCountMap[detail.OrderSn]-1 {
				details[i].RefundAmountSku = cast.ToString(details[i].PayPrice)
				refundCodesAmount[detail.OrderSn] += details[i].PayPrice
				refundCodes[detail.OrderSn] += 1
			} else {
				detail.RefundAmountSku = cast.ToString(refundGoodsAmountMap[detail.OrderSn] - refundCodesAmount[detail.OrderSn])
			}
		} else {
			detail.VerifyCode = detail.VerifyCode[0:3] + "*****" + detail.VerifyCode[8:]
			detail.VerifyStatus = "未核销"
		}
		if isThirdChannel(detail.ChannelId) {
			detail.OldOrderSn = oldOrderSnMap[detail.ParentOrderSn]
		} else {
			detail.OldOrderSn = "--"
		}

		detail.VerifyTime = verifyTime
	}
	return
}

// 导出退款订单列表
func RefundOrderExport(in *oc.RefundOrderInfoRequest) (out []*oc.RefundOrderExport, err error) {
	glog.Info("阿闻管家退款订单列表-导出数据参数：", kit.JsonEncode(in))
	defer glog.Info("QUIT", "阿闻管家退款订单列表导出数据参数：", kit.JsonEncode(in))

	Db := NewSlaveDbConn()
	//Db.ShowSQL(true)
	defer Db.Close()
	var RefundOrders []models.RefundOrderLists

	session := Db.Table("refund_order").
		Join("inner", "`order_main`", "`order_main`.order_sn=refund_order.order_sn").
		Join("left", "refund_order_third_product", "refund_order.refund_sn = refund_order_third_product.refund_sn").
		Join("left", "order_detail", "`order_main`.order_sn=order_detail.order_sn").
		Where("1=1")

	//订单搜索
	if len(in.Keyword) > 0 {
		switch in.SearchType {
		case 1: //原订单号
			session.And("`refund_order`.order_sn like ?", "%"+in.Keyword+"%")
		case 2: //退款单号
			session.And("`refund_order`.refund_sn like ?", "%"+in.Keyword+"%")
		default: //default case
		}
	}

	//退款类型:1用户 2商家 3客服
	if in.UserType > 0 {
		session.And("`refund_order`.apply_op_user_type = ?", in.UserType)
	}

	//如果是逍宠，，只查询当前的门店订单
	session.And("order_main.org_id = ?", in.Orgid)
	if in.Orgid == 6 {
		session.And("order_main.shop_id = ?", in.FinancialCode)
	}

	//退款类型:1为仅退款,2为退款退货
	switch in.RefundType {
	case 1:
		session.And("`refund_order`.refund_type = 1")
	case 2:
		session.And("`refund_order`.refund_type = 2")
	}

	//店铺类型
	if in.AppChannel > 0 {
		if in.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}

	//订单类型:1实物  2虚拟
	if in.OrderType > 0 {
		switch in.OrderType {
		case 1: //1实物
			session.And("refund_order_third_product.product_type=1 OR (`refund_order`.channel_id = 1 AND `order_main`.is_virtual = 0)")
		case 2: //2虚拟
			session.And("refund_order_third_product.product_type=2 OR (`refund_order`.channel_id = 1 AND `order_main`.is_virtual = 1 )")
		default: //default case
		}
	}

	//订单退款状态  1:退款中 2:退款关闭 3:退款成功
	if in.RefundState != 0 {
		if in.RefundState == 2 {
			session.And("`refund_order`.refund_state IN (2, 9)")
		} else {
			session.And("`refund_order`.refund_state = ?", in.RefundState)
		}
	}

	if in.ChannelId > 0 {
		if in.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(in.ChannelId))
		} else {
			if in.ChannelId == ChannelJddjId {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", in.ChannelId)
			}
		}
	} else {
		session.And("`order_main`.channel_id in (1,2,3,4,9)")
	}

	//下单时间
	if len(in.StartTime) > 0 {
		session.And("`refund_order`.create_time > ?", in.StartTime)
	}
	if len(in.EndTime) > 0 {
		session.And("`refund_order`.create_time <= ?", in.EndTime)
	}

	if len(in.Shopids) > 0 {
		session.In("`order_main`.shop_id", in.Shopids)
	}

	if in.PushThirdState > 0 {
		if in.PushThirdState == 1 {
			session.And("refund_order.push_third = 1 or refund_order.refund_state != 3")
		} else {
			session.And("refund_order.push_third = 0 and refund_order.refund_state = 3")
		}
	}

	/*if len(in.Refundsn) > 0 {
		session.And("`refund_order`.refund_sn=?", in.Refundsn)
	}*/

	err = session.Select("refund_order.*, refund_order.order_sn as refund_order_sn, IFNULL(refund_order_third_product.order_sn,'') AS child_order_sn,"+
		"IFNULL(refund_order_third_product.product_type,0) AS product_type,"+
		"IFNULL(SUM(refund_order_third_product.refund_amount),0) AS child_refund_amount,"+
		"order_main.parent_order_sn,order_main.user_agent,order_main.is_virtual order_is_virtual,order_main.shop_name, order_main.source,order_main.freight,order_main.freight_privilege,order_main.packing_cost,"+
		" order_main.warehouse_name,order_main.warehouse_code, order_detail.child_channel_name").
		GroupBy("refund_order.refund_sn,refund_order_third_product.order_sn").
		Desc("id").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		Find(&RefundOrders)
	if err != nil {
		err = errors.New("退款订单导出查询订单列表错误, " + err.Error())
		return out, err
	}

	refundProductMap := map[string]string{}
	thirdRefundProductMap := map[string]string{}
	if err = func() error {
		refundSns := make([]string, len(RefundOrders))
		//6.0虚实版本之前 第三方的退款单没有拆单 所以退款商品只关联了主单号 之后有关联子单号
		//所以之后第三发的且是6.0版本之后的数据才能查询出主单号
		var thirdRefundSns []string
		for k, v := range RefundOrders {

			refundSns[k] = RefundOrders[k].RefundSn
			//第三方退款且存在子单号（v6.0版本之前不存在子单号） 单使用子订单号 以及子订单下的退款金额
			if isThirdChannel(v.ChannelId) && v.ChildOrderSn != "" {
				//第三方订单显示订单订单号
				thirdRefundSns = append(thirdRefundSns, RefundOrders[k].RefundSn)
				RefundOrders[k].OrderSn = v.ChildOrderSn
				RefundOrders[k].OldOrderSn = v.OldOrderSn
				RefundOrders[k].RefundAmount = v.ChildRefundAmount
				//第三方最后一笔实物退款 需要加上运费与包装费
				//退款商品上加总的退款总费用是不包含运费于包装费的 只有主单上的退款数量是包含了包装非于运费的
				//所以从退款商品拆了之后加总出来的退款费需要加上运费与包装费
				var isReal bool
				if v.ProductType == 1 {
					isReal = true
				}
				//全单退的实物订单需要加上运费与包装费
				if v.FullRefund == 1 && isReal {
					//查询是否有配送优惠,有的话需要去掉邮费
					newFreightTotalRefund := kit.YuanToFen(cast.ToFloat64(v.ChildRefundAmount))
					newFreightTotalRefund += int(v.Freight - v.FreightPrivilege + v.PackingCost)
					RefundOrders[k].RefundAmount = cast.ToString(kit.FenToYuan(newFreightTotalRefund))
				}
			}
		}

		//按照退款单分组
		var refundProducts []models.RefundOrderProduct
		var refundProductsThird []models.RefundOrderThirdProduct
		err = Db.Table("refund_order_product").
			In("refund_sn", refundSns).
			Select("refund_sn, group_concat(product_name) product_name").
			GroupBy("refund_sn").
			Find(&refundProducts)
		if err != nil {
			return err
		}
		err = Db.Table("refund_order_third_product").
			In("refund_sn", thirdRefundSns).
			Select("refund_sn,order_sn,group_concat(product_name) product_name").
			GroupBy("refund_sn,order_sn").
			Find(&refundProductsThird)
		if err != nil {
			return err
		}
		for _, v := range refundProducts {
			refundProductMap[v.RefundSn] = v.ProductName
		}
		for _, v := range refundProductsThird {
			thirdRefundProductMap[v.RefundSn+v.OrderSn] = v.ProductName
		}
		return nil
	}(); err != nil {
		err = errors.New("退款订单导出查询退款商品错误, " + err.Error())
		return out, err
	}

	for _, i2 := range RefundOrders {
		if i2.RefundState == 9 {
			i2.RefundState = 2
		}
		// 转换通道类型
		if i2.ChannelId == ChannelJddjId && len(i2.ChildChannelId) > 0 {
			i2.ChannelId = cast.ToInt32(i2.ChildChannelId)
		}
		if isThirdChannel(i2.ChannelId) && i2.ChildOrderSn != "" {
			//第三方订单显示订单订单号
			i2.OrderSn = i2.ChildOrderSn
		}

		////退款单号
		//		e.f.SetCellValue(e.sheetName, "A"+n, refundOrderList[k].RefundSn)
		//		//订单类型 实物订单还是虚拟订单
		//		e.f.SetCellValue(e.sheetName, "B"+n, refundOrderList[k].RefundSn)
		//		//子订单号
		//		e.f.SetCellValue(e.sheetName, "C"+n, refundOrderList[k].OrderSn)
		//		//父订单号
		//		e.f.SetCellValue(e.sheetName, "D"+n, refundOrderList[k].OldOrderSn)
		//		//外部订单号
		//		e.f.SetCellValue(e.sheetName, "E"+n, refundOrderList[k].GyOrderSn)
		refundOrderExportSlice := &oc.RefundOrderExport{
			RefundSn:      i2.RefundSn,
			Source:        i2.Source,
			OrderSn:       i2.OrderSn,
			OldOrderSn:    i2.OldOrderSn,
			GyOrderSn:     i2.OldOrderSn,
			RefundType:    i2.RefundType,
			RefundAmount:  i2.RefundAmount,
			ExpressNum:    i2.ExpressNum,
			ChannelId:     i2.ChannelId,
			RefundState:   i2.RefundState,
			CreateTime:    i2.CreateTime.Format(kit.DATE_LAYOUT),
			UpdateTime:    i2.UpdateTime.Format(kit.DATE_LAYOUT),
			RefundRemark:  i2.RefundRemark,
			ShopId:        i2.ShopId,
			ShopName:      i2.ShopName,
			WarehouseName: i2.WarehouseName,
			ProductName:   refundProductMap[i2.RefundSn],
			AppChannel:    i2.AppChannel,
			ParentOrderSn: i2.ParentOrderSn,
			RefundOrderSn: i2.RefundOrderSn,
		}

		if isThirdChannel(i2.ChannelId) && i2.ChildOrderSn != "" {
			refundOrderExportSlice.ProductName = thirdRefundProductMap[i2.RefundSn+i2.ChildOrderSn]
		}

		switch refundOrderExportSlice.Source {
		case 1, 4:
			refundOrderExportSlice.Source = 4
		case 5:
			refundOrderExportSlice.Source = 1
		default:
			refundOrderExportSlice.Source = 3
		}

		refundOrderExportSlice.Category = Category[refundOrderExportSlice.Source]
		//第三方订单
		if isThirdChannel(i2.ChannelId) && i2.ChildOrderSn != "" {
			//第三方订单显示订单订单号
			if i2.ProductType == 1 {
				refundOrderExportSlice.OrderTypeName = "实物订单"
			} else {
				refundOrderExportSlice.OrderTypeName = "虚拟订单"
			}
		} else {
			if i2.OrderIsVirtual == 0 {
				refundOrderExportSlice.OrderTypeName = "实物订单"
			} else {
				refundOrderExportSlice.OrderTypeName = "虚拟订单"
			}
		}

		out = append(out, refundOrderExportSlice)
	}

	return out, nil
}

// 拣货完成且商家自送接口
func JddjOrderSerllerDelivery(OrderSn string, storeMasterId int32) error {
	client := et.GetExternalClient()

	request := et.JddjOrderSerllerDeliveryRequest{
		OrderId:       OrderSn,
		StoreMasterId: storeMasterId,
	}

	res, err := client.JddjOrder.JddjOrderSerllerDelivery(client.Ctx, &request)
	if err != nil {
		glog.Error("调用JddjOrderSerllerDelivery失败, ", err, ", 参数：", kit.JsonEncode(request))
		return err
	}
	if res.Code != 200 {
		return errors.New(res.Message)
	}
	return nil
}

// 拣货完成且顾客自提接口
func JddjOrderSelfMention(OrderSn string, storeMasterId int32) error {
	client := et.GetExternalClient()

	request := et.JddjOrderSelfMentionRequest{
		OrderId:       OrderSn,
		Operator:      "rp",
		StoreMasterId: storeMasterId,
	}

	res, err := client.JddjOrder.JddjOrderSelfMention(client.Ctx, &request)
	if err != nil {
		glog.Error("调用JddjOrderSelfMention失败, ", err, ", 参数：", kit.JsonEncode(request))
		return err
	}
	if res.Code != 200 {
		return errors.New(res.Message)
	}
	return nil
}

// 拣货完成且众包配送接口
func JddjOrderJDZBDelivery(OrderSn string, storeMasterId int32) error {
	client := et.GetExternalClient()

	request := et.JddjOrderSerllerDeliveryRequest{
		OrderId:       OrderSn,
		StoreMasterId: storeMasterId,
	}

	res, err := client.JddjOrder.JddjOrderJDZBDelivery(client.Ctx, &request)
	if err != nil {
		glog.Error("调用JddjOrderJDZBDelivery失败, ", err, ", 参数：", kit.JsonEncode(request))
		return err
	}
	if res.Code != 200 {
		return errors.New(res.Message)
	}
	return nil
}

// 新订单通知数据中心
func MessageCreate(message *models.Message) error {
	dacClient := dac.GetDataCenterMessgeClient()

	content := kit.JsonEncode(message)
	glog.Info("通知数据中心run...", content)
	res, err := dacClient.RPC.MessageCreate(dacClient.Ctx, &dac.MessageCreateRequest{
		ShopId:      message.FinanceCode,
		Content:     content,
		MessageType: int32(message.MessageType),
		OrderId:     message.OrderId,
	})
	if err != nil {
		glog.Warning("通知数据中心错误", err.Error(), " ", content)
		return err
	}
	if res.Code != 200 {
		glog.Warning("通知数据中心发送消息错误：", res.Message, ", 请求参数：", content)
		return errors.New(res.Message)
	}

	params := &dac.MessageSendRequest{
		ObjectId: message.FinanceCode,
		Msg:      content,
	}
	res2, err := dacClient.RPC.MessageSend(dacClient.Ctx, params)
	glog.Info("MessageSendResponse：", kit.JsonEncode(res2))
	if err != nil {
		glog.Warning("通知数据中心发送消息错误, 发送失败：", err, ", 请求参数：", kit.JsonEncode(params))
		return err
	} else if res2.Code != 200 {
		glog.Warning("通知数据中心发送消息错误, 发送失败：", res2.Message, ", 请求参数：", kit.JsonEncode(params))
		return errors.New(res2.Message)
	}

	return nil
}

// 通知后台状态修改
func MessageUpdate(orderSn string) {
	dataCenterConn := dac.GetDataCenterClient()
	res, err := dataCenterConn.RPC.MessageUpdate(dataCenterConn.Ctx, &dac.MessageUpdateRequest{OrderId: orderSn})
	if err != nil {
		glog.Error(orderSn, ", 数据中心通知状态修改错误, ", err, ", ", kit.RunFuncName(2))
	} else if res.Code != 200 {
		glog.Error(orderSn, ", 数据中心通知状态修改错误返回参, ", ", ", kit.JsonEncode(res), ", ", kit.RunFuncName(2))
	}
}

// 是否时第三方渠道
// 包括美团 京东 饿了么
func isThirdChannel(channelId int32) bool {
	if channelId == ChannelMtId || channelId == ChannelJddjId || channelId == ChannelElmId {
		return true
	} else {
		return false
	}
}

//第三方渠道接单

func MtOrderConfirm(OrderSn string, channelId int32, appChannel int32) (string, error) {
	glog.Info("第三方渠道接单：", OrderSn)
	etClient := et.GetExternalClient()

	if channelId == ChannelMtId {
		request := et.MtOrderConfirmRequest{
			OrderId: OrderSn,
		}
		if channelId == ChannelMtId {
			request.AppChannel = appChannel
		}

		res, err := etClient.MtOrder.MtOrderConfirm(etClient.Ctx, &request)
		glog.Info("美团确认接单通知结果", res, OrderSn)
		if err != nil {
			glog.Error("调用美团接单失败，", err, "，参数：", kit.JsonEncode(request))
			return "", err
		}
		if res.ExternalCode == "808" {
			return res.ExternalCode, nil
		}
		if res.Code != 200 {
			return res.ExternalCode, errors.New(res.Message)
		}
	} else if channelId == ChannelElmId {
		//饿了么接单
		request := et.ELMOrderConfirmRequest{
			OrderId:    OrderSn,
			AppChannel: appChannel,
		}
		res, err := etClient.ELMORDER.ElmOrderConfirm(etClient.Ctx, &request)
		if err != nil {
			glog.Error("调用饿了么接单失败，", err, "，参数：", kit.JsonEncode(request))
			return "", err
		}
		if res.ExternalCode == "808" {
			return res.ExternalCode, nil
		}
		if res.Code != 200 {
			return res.ExternalCode, errors.New(res.Error)
		}
	} else if channelId == ChannelJddjId {
		request := et.JddjOrderConfirmRequest{
			OrderId:       OrderSn,
			IsAgreed:      true,
			StoreMasterId: appChannel,
		}

		res, err := etClient.JddjOrder.JddjOrderAcceptOperate(etClient.Ctx, &request)
		if err != nil {
			glog.Error("调用京东到家接单失败，", err, "，参数：", kit.JsonEncode(request))
			return "", err
		}
		if res.Code != 200 {
			return "", errors.New(res.Error)
		}
	}

	return "", nil
}

// 生成各种编号（订单，售后单，etc...）
func GetSn(kind string, number ...int) []string {
	loop := 1
	if len(number) > 0 && number[0] > 0 {
		loop = number[0]
	}

	var snArr []string

	db := GetDBConn()
	redis := GetRedisConn()
	for i := 0; i < loop; i++ {
		snArr = append(snArr, sn.NewSN(kind, db, redis).Generate())
	}

	return snArr
}

// 代替verifyCode
func GetNewVerifyCode(number ...int) []string {
	loop := 1
	if len(number) > 0 && number[0] > 0 {
		loop = number[0]
	}

	var snArr []string

	redis := GetRedisConn()
	for i := 0; i < loop; i++ {
		snArr = append(snArr, genVerifyCode(redis))
	}

	return snArr
}

/*
X X 周2 X 周1 X 年2 X 年1 X X 校验位
*/
func genVerifyCode(redisDb *redis.Client) string {
	var result string
	buffer := make([]string, 12)
	for i := 0; i < 12; i++ {
		buffer[i] = "0"
	}
	index := 8
	year, week := YearAndWeek()
	n := len(year)
	for n > 0 {
		buffer[index] = year[n-1 : n]
		n--
		index = index - 2
	}
	n = len(week)
	for n > 0 {
		buffer[index] = week[n-1 : n]
		n--
		index = index - 2
	}

	//填充另外7位
	preKey := "order-center:verify-code-number"
	preKeyLock := "order-center:verify-code-lock"

	isOk := redisDb.Exists(preKey)
	if isOk.Val() <= 0 {
		redisDb.Set(preKey, 1, 0)
	}

again:
	value := cast.ToInt32(redisDb.Get(preKey).Val())
	if value >= 9999999 {
		lockRes := redisDb.SetNX(preKeyLock, time.Now().Unix(), 5*time.Second).Val()
		if lockRes {
			temp := cast.ToInt32(redisDb.Get(preKey).Val())
			if temp >= 9999999 {
				redisDb.Set(preKey, 1, 0)
			}
			redisDb.Del(preKeyLock)
		} else {
			goto again
		}
	}

	sn, err := redisDb.Incr(preKey).Result()
	if err != nil {
		panic(err)
	}

	snStr := cast.ToString(sn)
	for len(snStr) < 7 {
		snStr = "0" + snStr
	}
	leftIndex := []int{0, 1, 3, 5, 7, 9, 10}
	n = 0
	for n < len(snStr) {
		buffer[leftIndex[n]] = snStr[n : n+1]
		n++
	}

	num1 := cast.ToInt32(year)
	num2 := cast.ToInt32(week)
	calc := cast.ToString((int32(sn) * num1 * 2) / num2)
	buffer[11] = calc[len(calc)-1 : len(calc)]
	for _, v := range buffer {
		result += v
	}

	return result
}

func YearAndWeek() (string, string) {
	t := time.Now()
	yearDay := t.YearDay()

	yearFirstDay := t.AddDate(0, 0, -yearDay+1)
	firstDayInWeek := int(yearFirstDay.Weekday())

	firstWeekDays := 1

	if firstDayInWeek != 0 {
		firstWeekDays = 7 - firstDayInWeek + 1
	}

	var week int

	if yearDay <= firstWeekDays {
		week = 1
	} else {
		week = (yearDay-firstWeekDays)/7 + 2
	}

	return cast.ToString(t.Year())[2:], cast.ToString(week)
}

// 生成核销码有效期
func GetCheckoffExpireDate(termType, termValue int32) time.Time {
	switch termType {
	case 1:
		return time.Unix(int64(termValue), 0)
	case 2:
		return time.Now().AddDate(0, 0, int(termValue))
	default:
		t := "1990-01-01 00:00:00"
		defalutTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, t, time.Local)
		return defalutTime
	}
}

// 将float64转成精确的int64
func Wrap(num float64, retain int) int64 {
	return int64(num * math.Pow10(retain))
}

// 拆分后子订单入库（VIP的订单拆单）
func saveVipSplitOrder(mainOrder *models.OrderMain) error {
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()
	var (
		orderProducts []*models.OrderProduct
	)

	//主单商品
	err := session.Where("order_sn = ?", mainOrder.OrderSn).Find(&orderProducts)
	if err != nil {
		glog.Error("查询订单商品错误：", mainOrder.OrderSn, " ", err.Error())
		return err
	}
	//拆分后的订单入库保存
	var (
		childOrders        []*models.OrderMain       //子单
		childOrderDetails  []*models.OrderDetail     //子单详情
		childOrderProducts []*models.OrderProduct    //子单商品
		orderVerifyCodes   []*models.OrderVerifyCode //子单虚拟商品核销码
	)

	//VIP卡的商品一次只会有一个
	num := cast.ToInt(orderProducts[0].Number)
	price := mainOrder.Total / cast.ToInt32(num)
	//有多少个商品就要生成多少个子订单
	for i := 0; i < num; i++ {
		orderSn := GetSn("order")[0]
		//添加子单
		itemOrder := *mainOrder
		itemOrder.OrderSn = orderSn
		itemOrder.Id = 0
		itemOrder.ParentOrderSn = mainOrder.OrderSn
		itemOrder.OldOrderSn = orderSn
		itemOrder.Total = price
		itemOrder.PayTotal = price
		itemOrder.GoodsTotal = price
		itemOrder.GoodsPayTotal = price
		itemOrder.ActualReceiveTotal = price
		itemOrder.PayAmount = price
		itemOrder.OrgId = mainOrder.OrgId
		childOrders = append(childOrders, &itemOrder)
		//添加订单明细
		orderDetail := &models.OrderDetail{
			OrderSn:          orderSn,
			SplitOrderResult: 1,
		}
		childOrderDetails = append(childOrderDetails, orderDetail)
		//添加商品信息
		itemOrderProduct := *orderProducts[0]
		itemOrderProduct.Id = 0
		itemOrderProduct.OrderSn = orderSn
		itemOrderProduct.Number = 1
		itemOrderProduct.MarkingPrice = price
		itemOrderProduct.DiscountPrice = price
		itemOrderProduct.PayPrice = price
		itemOrderProduct.PaymentTotal = price
		itemOrderProduct.SkuPayTotal = price
		childOrderProducts = append(childOrderProducts, &itemOrderProduct)

		if mainOrder.OrderType == 17 {
			checkoffCodes := GetNewVerifyCode(int(1)) //生成购买数量的核销码
			glog.Info(mainOrder.OrderSn, " 生成核销码", kit.JsonEncode(checkoffCodes))
			for _, code := range checkoffCodes {
				verifyCodeExpiryDate := time.Now().AddDate(1, 0, 0)

				var checkOff = &models.OrderVerifyCode{
					OrderSn:              orderSn,
					VerifyCode:           code,
					SkuId:                orderProducts[0].SkuId,
					ParentSkuId:          "",
					GroupItemNum:         0,
					VerifyCodeExpiryDate: verifyCodeExpiryDate,
					VerifyStatus:         0, //默认未核销
				}
				orderVerifyCodes = append(orderVerifyCodes, checkOff)
			}
		}

	}

	//子订单
	if len(childOrders) > 0 {
		if _, err = session.Insert(&childOrders); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单失败, ", err, ", ", kit.JsonEncode(childOrders))
			_ = session.Rollback()
			return err
		}
	}
	//子订单详情
	if len(childOrderDetails) > 0 {
		if _, err = session.Insert(&childOrderDetails); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单详情失败, ", err, ", ", kit.JsonEncode(childOrderDetails))
			_ = session.Rollback()
			return err
		}
	}
	//商品
	if len(childOrderProducts) > 0 {
		if _, err = session.Insert(&childOrderProducts); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单商品失败, ", err, ", ", kit.JsonEncode(childOrderProducts))
			_ = session.Rollback()
			return err
		}
	}
	//核销码
	if len(orderVerifyCodes) > 0 {
		if _, err = session.Insert(&orderVerifyCodes); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入订单核销码失败, ", err, ", ", kit.JsonEncode(orderVerifyCodes))
			_ = session.Rollback()
			return err
		}
	}
	//更新拆单状态
	if _, err = session.Exec("update order_detail set split_order_result=1 where order_sn=?", mainOrder.OrderSn); err != nil {
		glog.Error(mainOrder.OrderSn, ", 更新订单拆单状态失败, ", err)
		_ = session.Rollback()
		return err
	}

	session.Commit()
	// 必要的延迟，避免线上的主从延迟导致的问题，拆单后马上就要去查询，怕主从还没有同步过去
	time.Sleep(100 * time.Microsecond)
	return nil
}

// 拆分后子订单入库
func saveSplitOrder(mainOrder *models.OrderMain) error {
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()

	var (
		orderDetail     models.OrderDetail //主订单明细
		orderProducts   []*models.OrderProduct
		orderPromotions []*models.OrderPromotion //主订单活动

	)

	//主单详情
	_, err := session.Where("order_sn = ?", mainOrder.OrderSn).Get(&orderDetail)

	if err != nil {
		glog.Error("查询订单详情错误：", mainOrder.OrderSn, " ", err.Error())
		return err
	}

	// 社区团购佣金查询
	var groupCommissionsRate int32 = 0
	if mainOrder.OrderType == 15 {
		_, err := session.SQL("SELECT oga.commission_rate FROM dc_order.order_group_activity oga LEFT JOIN dc_order.order_main_group omg "+
			"ON oga.id = omg.order_group_activity_id WHERE omg.parent_order_sn = ? ", mainOrder.OrderSn).Get(&groupCommissionsRate)
		glog.Error("拆单入库saveSplitOrder查询社区团购佣金:", groupCommissionsRate, mainOrder, err)
	}

	//主单商品
	err = session.Where("order_sn = ?", mainOrder.OrderSn).Find(&orderProducts)
	if err != nil {
		glog.Error("查询订单商品错误：", mainOrder.OrderSn, " ", err.Error())
		return err
	}

	//积分兑换的实物商品不走拆单逻辑
	if mainOrder.ChannelId == ChannelMallId && mainOrder.OrderType == 8 && mainOrder.IsVirtual == 0 {
		//不拆单，直接返回
		return nil
	}

	//主订单商品
	orderProductsMap := make(map[string]*models.OrderProduct, 0)
	//主订单商品sku-数量（电商拆单之后没有价格，所以通过数量分摊到各个订单）
	orderProductNumberMap := make(map[string]int32, 0)

	for _, p := range orderProducts {
		key := p.ParentSkuId + "|" + p.SkuId + "|" + cast.ToString(p.PayPrice)
		orderProductsMap[key] = p
		orderProductNumberMap[p.SkuId] += p.Number
	}

	//主单活动
	err = session.Where("order_sn = ?", mainOrder.OrderSn).Find(&orderPromotions)
	if err != nil {
		glog.Error("查询订单活动错误：", mainOrder.OrderSn, " ", err.Error())
		return err
	}

	var req []dto.SplitOrderReq     // 原来的订单不动
	var reqDrug []dto.SplitOrderReq // 药品仓标识的订单
	for _, item := range orderProducts {
		//调拆单接口时要过滤掉组合商品 组合商品的组合商品已经放出来 此时的组合商品只是一个虚的标志 没有实际意义 不需要拆单
		if item.ProductType == 3 {
			continue
		}

		var preq dto.SplitOrderReq
		preq.Skuid = cast.ToInt32(item.SkuId)
		preq.Groupskuid = cast.ToInt32(item.ParentSkuId)
		preq.Stock = item.Number   //所需库存
		preq.Price = item.PayPrice //均摊后支付单价
		//虚拟商品
		if item.ProductType == 2 {
			preq.Isvirtual = 1
		}
		if len(item.ParentSkuId) > 0 {
			preq.Isgroup = 1
		}
		if item.WarehouseType == 1 {
			reqDrug = append(reqDrug, preq) // 加入是药品仓标识的订单集合
		} else {
			req = append(req, preq) // 加入原来的订单集合不变
		}
	}

	comeFrom := 0
	if mainOrder.ChannelId != ChannelMallId { //非商城 都按照本地生活逻辑进行拆单
		comeFrom = 1
	}

	//调用拆单接口
	splitOrders := []dto.SplitOrderResp{}
	if len(req) > 0 {
		glog.Info(fmt.Sprintf("拆单OrderSn:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;参数:%s", mainOrder.OrderSn, mainOrder.WarehouseCode, mainOrder.ReceiverState, comeFrom, kit.JsonEncode(req)))
		splitOrders, err = SplitOrder(req, mainOrder, comeFrom, false, mainOrder.ChannelId)
		if err != nil {
			return err
		}
		glog.Info(fmt.Sprintf("拆单OrderSn:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;拆单结果%s", mainOrder.OrderSn, mainOrder.WarehouseCode, mainOrder.ReceiverState, comeFrom, kit.JsonEncode(splitOrders)))
	}

	// 药品仓调用拆单接口
	orderDrugs := []dto.SplitOrderResp{}
	if len(reqDrug) > 0 {
		glog.Info(fmt.Sprintf("药品仓的拆单OrderSn参数:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;参数:%s", mainOrder.OrderSn, mainOrder.WarehouseCode, mainOrder.ReceiverState, comeFrom, kit.JsonEncode(reqDrug)))
		orderDrugs, err = SplitOrder(reqDrug, mainOrder, comeFrom, true, mainOrder.ChannelId) // 是否药品仓库的标识
		if err != nil {
			return err
		}
		glog.Info(fmt.Sprintf("药品仓的拆单OrderSn:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;拆单结果%s", mainOrder.OrderSn, mainOrder.WarehouseCode, mainOrder.ReceiverState, comeFrom, kit.JsonEncode(orderDrugs)))
	}

	//合并两个订单的接口
	splitOrders = append(splitOrders, orderDrugs...)

	if len(splitOrders) <= 0 {
		return errors.New("调用拆单接口返回子订单数量为0")
	}

	req = append(req, reqDrug...)
	//将拆单合并的商品拆分开(电商这边拆单方法返回会合并，所以这里重新拆开)
	var newSplitOrders []dto.SplitOrderResp

	// 虚拟子订单合计，计算实物订单实付金额
	var virtualTotal int32

	//拆单后一个子订单里的商品数据匹配req中的数据进行赋值
	//匹配条件：
	//1:sku+组合商品sku
	//2:阿闻:匹配价格 不同价格会拆成多个单
	//非阿闻:不匹配价格 v6.0之后如果在非组合商品里存在同个sku不同价格时可能存在问题 diffSku(标记请勿删除)
	for _, order := range splitOrders {
		splitOrder := dto.SplitOrderResp{
			Ordersn:       order.Ordersn,
			Warehousecode: order.Warehousecode,
		}
		var realGoods []dto.SplitOrderGoods

		for _, good := range order.SplitOrderGoods {

			for _, re := range req {
				//所有渠道都按照sku+组合组合进行判断
				//同个组合里不可能存在同个sku不同价格的情况 所以使用组合过滤即可解决不同组合相同sku不同价格的情况 非组合可能存在这个可能
				//阿闻到家判断价格后过滤不会出问题 其他渠道如果存在这种情况则有可能出问题
				if re.Skuid == good.Skuid && re.Groupskuid == good.Groupskuid {
					//阿闻到家与第三方需要取价格相等的 所有实物订单都在同一个子订单里 阿闻到家实物按照仓库拆单
					//目前商城非组合商品存在同一个sku有不同的价格 所以不需要判断价格 如果以后存在这种情况 则需要拆单方法需要将价格带出来，如果判断价格是否会导致bug?
					//if (mainOrder.ChannelId != ChannelMallId && mainOrder.ChannelId != ChannelDigitalHealth && re.Price == good.Price) || mainOrder.ChannelId == ChannelMallId {
					//	var item dto.SplitOrderGoods
					//	item.Skuid = re.Skuid
					//	item.Stock = re.Stock
					//	item.Isgroup = re.Isgroup
					//	item.Isvirtual = re.Isvirtual
					//	item.Groupskuid = re.Groupskuid
					//	item.Price = re.Price //支付单价
					//	realGoods = append(realGoods, item)
					//}
					//电商和互联网医疗订单不校验价格，本地生活订单如果价格不一致，则跳出
					if mainOrder.ChannelId != ChannelMallId && re.Price != good.Price {
						continue
					}
					var item dto.SplitOrderGoods
					item.Skuid = re.Skuid
					item.Stock = re.Stock
					item.Isgroup = re.Isgroup
					item.Isvirtual = re.Isvirtual
					item.Groupskuid = re.Groupskuid
					item.Price = re.Price //支付单价
					realGoods = append(realGoods, item)
				}
			}
		}
		//一个订单下的商品
		splitOrder.SplitOrderGoods = realGoods
		newSplitOrders = append(newSplitOrders, splitOrder)
	}

	//拆分后的订单入库保存,如果是虚拟子订单还需为虚拟商品生成核销码，
	var (
		childOrders          []*models.OrderMain       //子单
		childOrderDetails    []*models.OrderDetail     //子单详情
		childOrderProducts   []*models.OrderProduct    //子单商品
		orderVerifyCodes     []*models.OrderVerifyCode //子单虚拟商品核销码
		childOrderPromotions []*models.OrderPromotion  //子单活动
	)

	//获取指定数量订单号
	orderSns := GetSn("order", len(splitOrders))

	ordersLen := int32(len(newSplitOrders))
	for index, childOrder := range newSplitOrders {
		if index == 2 {
		}
		//子订单
		child := *mainOrder        //从主单拷贝一份到子单
		child.Id = 0               //清空主键
		child.Freight = 0          //子订单运费先置0
		child.FreightPrivilege = 0 //子订单运费优惠先置0
		child.OrderSn = orderSns[index]
		child.OldOrderSn = child.OrderSn //子订单的渠道订单号与订单号是一样的 也就是子订单的渠道订单号没有意义
		child.ParentOrderSn = mainOrder.OrderSn
		child.WarehouseCode = childOrder.Warehousecode
		childDetail := orderDetail //订单详情 从主单拷贝一份到子单
		childDetail.OrderSn = child.OrderSn
		childDetail.SplitOrderResult = 1         //拆单成功
		newSplitOrdersLen := len(newSplitOrders) // 子单的数量用于运费的计算均摊

		var (
			itemTotalSum            int32 //子订单实付金额
			itemPrivilegeSum        int32 //子订单优惠金额
			itemCombinePrivilegeSum int32 //子订单组合商品优惠金额
			itemGoodsTotalSum       int32 //子订单商品金额

			itemMallPrivilegeSum    int32                                //统计电商子商品优惠
			childOrderPromotionsMap = map[int64]*models.OrderPromotion{} //子单活动
			product                 *models.OrderProduct
			ok                      bool
		)

		skuPayTotalSum := map[string]int32{}
		//处理子订单的商品 计算子订单中的每个sku的实付金额
		for _, goods := range childOrder.SplitOrderGoods {
			skuId := cast.ToString(goods.Skuid)
			parentSkuId := ""
			if goods.Groupskuid > 0 {
				parentSkuId = cast.ToString(goods.Groupskuid)
			}
			key := parentSkuId + "|" + skuId + "|" + cast.ToString(goods.Price)
			product, ok = orderProductsMap[key]
			if ok {
				switch mainOrder.ChannelId {
				case ChannelAwenId, ChannelDigitalHealth:
					skuPayTotalSum[product.SkuId] = product.SkuPayTotal
				default: //单个sku可能会拆成多个单
					skuPayTotalSum[product.SkuId] += product.SkuPayTotal
				}
			}
		}

		vipPrivilege := int32(0)

		//子商品
		for _, goods := range childOrder.SplitOrderGoods {
			//子订单商品
			skuId := cast.ToString(goods.Skuid)
			parentSkuId := ""
			if goods.Groupskuid > 0 {
				parentSkuId = cast.ToString(goods.Groupskuid)
			}

			key := parentSkuId + "|" + skuId + "|" + cast.ToString(goods.Price)
			product, ok = orderProductsMap[key]
			if ok {

				childProduct := *product //从主单商品复制一份 原商品

				//社区团购佣金记录
				if groupCommissionsRate > 0 {
					childProduct.CommissionRate = groupCommissionsRate
					childProduct.IsDistribute = 1
				}

				childProduct.Id = 0 //清空主键
				childProduct.OrderSn = child.OrderSn
				childProduct.Number = product.Number                          //数量
				childProduct.SkuPayTotal = skuPayTotalSum[childProduct.SkuId] //记录的是sku的支付总金额  不一定是这一个订单的
				childOrderProducts = append(childOrderProducts, &childProduct)

				//虚拟商品生成核销码
				if product.ProductType == 2 {
					//有虚拟商品是虚拟订单
					child.IsVirtual = 1
					checkoffCodes := GetNewVerifyCode(int(childProduct.Number)) //生成购买数量的核销码
					glog.Info(mainOrder.OrderSn, " 生成核销码", kit.JsonEncode(checkoffCodes))
					for _, code := range checkoffCodes {
						verifyCodeExpiryDate := GetCheckoffExpireDate(product.TermType, product.TermValue)

						var checkOff = &models.OrderVerifyCode{
							OrderSn:              child.OrderSn,
							VerifyCode:           code,
							SkuId:                skuId,
							ParentSkuId:          parentSkuId,
							GroupItemNum:         childProduct.GroupItemNum,
							VerifyCodeExpiryDate: verifyCodeExpiryDate,
							VerifyStatus:         0, //默认未核销
						}
						orderVerifyCodes = append(orderVerifyCodes, checkOff)
					}
				}

				//子订单内的商品有参与活动要把父订单的活动复制到子订单去
				if product.PromotionType > 0 {
					if product.VipPrice > 0 {
						vipPrivilege += (product.DiscountPrice - product.VipPrice) * product.Number
					}
					for _, promotion := range orderPromotions {
						if promotion.PromotionId != product.PromotionId || product.PromotionId == 0 {
							continue
						}
						privilege := product.Privilege
						if product.VipPrice > 0 {
							privilege -= (product.DiscountPrice - product.VipPrice) * product.Number
						}
						//主订单的优惠复制到子订单中，优惠金额分摊
						//todo 字段优惠金额错误导致支付后订单无法显示满减优惠金额问题
						if _, ok = childOrderPromotionsMap[promotion.Id]; !ok {
							promotionCp := *promotion
							childOrderPromotionsMap[promotion.Id] = &promotionCp
							childOrderPromotionsMap[promotion.Id].Id = 0 //清空原主键
							childOrderPromotionsMap[promotion.Id].OrderSn = child.OrderSn
							childOrderPromotionsMap[promotion.Id].PoiCharge = privilege
							childOrderPromotionsMap[promotion.Id].PromotionFee = privilege
						} else {
							childOrderPromotionsMap[promotion.Id].PoiCharge += privilege
							childOrderPromotionsMap[promotion.Id].PromotionFee += privilege
						}
						break
					}
				}

				//统计订单实付金额，商品原总金额，优惠金额
				itemTotalSum += product.PaymentTotal
				itemGoodsTotalSum += product.Number * product.MarkingPrice
				itemCombinePrivilegeSum += product.Number * (product.MarkingPrice - product.DiscountPrice) //数量*（原价-组合价）
				itemMallPrivilegeSum += product.PrivilegeTotal
				itemPrivilegeSum += product.Number*product.MarkingPrice - product.PaymentTotal //所有优惠 包含着商品优惠，平台优惠 组合优惠 PaymentTotal 里包含着运费
			}
		}

		if vipPrivilege > 0 {
			childOrderPromotionsMap[0] = &models.OrderPromotion{
				OrderSn:        child.OrderSn,
				PromotionType:  4,
				PromotionTitle: "会员优惠",
				PoiCharge:      vipPrivilege,
				PtCharge:       0,
				PromotionFee:   vipPrivilege,
			}
		}

		if child.IsVirtual == 1 { //虚拟订单付款后主状态变成完成，子状态为待核销
			child.OrderStatus = 30
			child.OrderStatusChild = 30101 //待核销
			ordersLen = ordersLen - 1
		} else { //含实物商品的订单，就是实物单，将运费,打包费，服务费放到实物单
			child.Freight = mainOrder.Freight / int32(newSplitOrdersLen)
			child.FreightPrivilege = mainOrder.FreightPrivilege
			child.PackingCost = mainOrder.PackingCost
			child.ServiceCharge = mainOrder.ServiceCharge

			//如果有运费活动，复制到实物子订单中
			for _, promotion := range orderPromotions {
				if _, ok = FreightType[promotion.PromotionType]; ok || promotion.PromotionType == 3 {
					promotion.Id = 0 //清空原主键
					promotion.OrderSn = child.OrderSn
					childOrderPromotions = append(childOrderPromotions, promotion)
				}
			}
		}

		if len(childOrderPromotionsMap) > 0 {
			for _, promotion := range childOrderPromotionsMap {
				childOrderPromotions = append(childOrderPromotions, promotion)
			}
		}

		if mainOrder.ChannelId == ChannelAwenId || mainOrder.ChannelId == ChannelDigitalHealth {
			child.GoodsTotal = itemGoodsTotalSum
			child.CombinePrivilege = itemCombinePrivilegeSum
			child.Privilege = itemPrivilegeSum - itemCombinePrivilegeSum //活动优惠=所有优惠-组合优惠 所有优惠包含了  平台优惠 上架优惠 不包含运费优惠
			//child.Total = itemGoodsTotalSum + child.Freight - itemPrivilegeSum - child.FreightPrivilege //实付金额=商品总价+运费-优惠-运费优惠
			//itemGoodsTotalSum - itemPrivilegeSum = 包含运费在内的费用
			child.Total = itemGoodsTotalSum - itemPrivilegeSum - child.FreightPrivilege //+ child.Freight//实付金额=商品总价-所有商品优惠 -运费优惠

			// 虚拟订单合计叠加
			if child.IsVirtual == 1 {
				virtualTotal += child.Total
				child.PayTotal = child.Total
				child.ActualReceiveTotal = child.Total
			} else {
				// 实付总金额，等于父订单-所有虚拟订单total合计
				child.PayTotal = mainOrder.Total - virtualTotal
				child.ActualReceiveTotal = mainOrder.ActualReceiveTotal - virtualTotal
			}
		} else {
			//电商
			child.PayAmount = itemTotalSum
			child.Total = child.PayAmount //+ child.Freight
			child.ActualReceiveTotal = child.Total
			child.PayTotal = child.PayAmount //+ child.Freight
			child.Privilege = itemMallPrivilegeSum
			child.GoodsTotal = child.PayAmount + child.Privilege // 商品金额
		}

		// 商品实付总金额
		child.GoodsPayTotal = itemTotalSum
		childOrders = append(childOrders, &child)
		childOrderDetails = append(childOrderDetails, &childDetail)
	}

	if len(childOrders) >= 1 && ordersLen > 0 {
		for _, v := range childOrders {
			//如果是实物  则子订单total
			if v.IsVirtual == 0 {
				v.Freight = mainOrder.Freight / ordersLen
				v.Total = v.Total + v.Freight
				if mainOrder.ChannelId == ChannelMallId {
					v.PayTotal = v.Total
				}
			}
		}
	}

	_ = session.Begin()

	//子订单
	if len(childOrders) > 0 {
		if _, err = session.Insert(&childOrders); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单失败, ", err, ", ", kit.JsonEncode(childOrders))
			_ = session.Rollback()
			return err
		}
	}
	//子订单详情
	if len(childOrderDetails) > 0 {
		if _, err = session.Insert(&childOrderDetails); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单详情失败, ", err, ", ", kit.JsonEncode(childOrderDetails))
			_ = session.Rollback()
			return err
		}
	}
	//商品
	if len(childOrderProducts) > 0 {
		if _, err = session.Insert(&childOrderProducts); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单商品失败, ", err, ", ", kit.JsonEncode(childOrderProducts))
			_ = session.Rollback()
			return err
		}
	}
	//核销码
	if len(orderVerifyCodes) > 0 {
		if _, err = session.Insert(&orderVerifyCodes); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入订单核销码失败, ", err, ", ", kit.JsonEncode(orderVerifyCodes))
			_ = session.Rollback()
			return err
		}
	}
	//优惠活动
	if len(childOrderPromotions) > 0 {
		if _, err = session.Insert(&childOrderPromotions); err != nil {
			glog.Error(mainOrder.OrderSn, ", 插入子订单优惠活动失败, ", err, ", ", kit.JsonEncode(childOrderPromotions))
			_ = session.Rollback()
			return err
		}
	}

	//更新拆单状态
	if _, err = session.Exec("update order_detail set split_order_result=1 where order_sn=?", mainOrder.OrderSn); err != nil {
		glog.Error(mainOrder.OrderSn, ", 更新订单拆单状态失败, ", err)
		_ = session.Rollback()
		return err
	}

	//电商只有一个商品的订单要判断是否有保障卡即买即生效
	if mainOrder.ChannelId == ChannelMallId && len(childOrderProducts) == 1 {
		err = CardEffectiveUponPurchase(session, mainOrder, childOrderProducts[0].SkuId)
		if err != nil {
			glog.Error(mainOrder.OrderSn, ", 电商保障卡即买即生效错误, ", err)
			_ = session.Rollback()
			return err
		}
	}
	//提交事务失败回滚
	err = session.Commit()
	if err != nil {
		_ = session.Rollback()
		return err
	}

	//阿闻订单记录订单节点日志
	if mainOrder.ChannelId == ChannelAwenId || mainOrder.ChannelId == ChannelDigitalHealth {
		go func() {
			//记录子订单落库和支付节点
			orderLogs := make([]*models.OrderLog, len(orderSns)*2)
			for k, orderSn := range orderSns {
				orderLogs[k*2] = &models.OrderLog{
					OrderSn: orderSn,
					LogType: models.OrderLogSubmitOrder,
				}
				orderLogs[k*2+1] = &models.OrderLog{
					OrderSn: orderSn,
					LogType: models.OrderLogPayedOrder,
				}
			}

			//记录主节点拆单节点
			orderLogs = append(orderLogs, &models.OrderLog{
				OrderSn: mainOrder.OrderSn,
				LogType: models.OrderLogSplitedOrder,
			})

			SaveOrderLog(orderLogs)
		}()
	}

	return nil
}

// 下单赠送幸运码
func luckyNumberForOrder(orderMain *models.OrderMain) {
	glog.Info("luckyNumberForOrder，下单赠送幸运码入口：", orderMain.OrderSn, orderMain.MemberId)
	// 1、活动获取和校验
	redisClient := common.GetRedisUpetConn()
	res, _ := redisClient.Get("upet_activity920:latest").Result()
	if res == "" {
		glog.Error("luckyNumberForOrder，活动不存在")
		return
	}
	var activity920 dto.Activity920Latest
	if err := json.Unmarshal([]byte(res), &activity920); err != nil {
		glog.Error("luckyNumberForOrder，解析json失败", res)
		return
	}
	if activity920.Datas.LotteryInfo.LotteryState != "1" {
		glog.Error("luckyNumberForOrder，活动未开始", res)
		return
	}
	if cast.ToInt32(activity920.Datas.LotteryInfo.LotteryId) == 0 {
		glog.Error("luckyNumberForOrder，活动id不能0", res)
		return
	}

	// 2、赠送幸运码
	requestUrl := config.GetString("pay-center-url") + "/mall/activity/920/lucky-num"
	requestParams := dto.LuckyNumGetRequest{
		LotteryId:  cast.ToInt32(activity920.Datas.LotteryInfo.LotteryId),
		Type:       4,
		OrderSn:    orderMain.OrderSn,
		ScrmUserId: orderMain.MemberId,
	}
	respBytes, err := utils.HttpPost(requestUrl, kit.JsonEncodeByte(requestParams), "application/json;charset=UTF-8")
	if err != nil {
		glog.Info("luckyNumberForOrder，调用LuckyNumMake失败：", err, requestUrl, string(respBytes))
		return
	}
	var resp dto.LuckyNumGetResponse
	if err := json.Unmarshal(respBytes, &resp); err != nil {
		glog.Info("luckyNumberForOrder，赠送幸运码解析json失败：", string(respBytes))
		return
	}
	if resp.Code != 200 {
		glog.Info("luckyNumberForOrder，赠送幸运码失败：", string(respBytes))
		return
	}
	glog.Info("luckyNumberForOrder，下单赠送幸运码成功：", orderMain.OrderSn, orderMain.MemberId, string(respBytes))
}

// 处理小程序订单支付通知
// 1:更新处理次数及主订单支付状态
// 2:拆单，拆单成功；如果是电商单推电商
// 3:发配送
func DealOrderPayNotify(in *models.OrderPayNotify) {
	logFix := fmt.Sprintf("DealOrderPayNotify====电商订单编号：%s", in.OrderSn)
	glog.Info(logFix, "入参：", utils.InterfaceToJSON(in))
	//长连接无需关闭
	redisConn := GetRedisConn()

	lockCard := "lock:order_" + in.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		//未取到锁
		glog.Info(logFix, "未取到锁")
		return
	}
	defer redisConn.Del(lockCard)

	c := &CommonService{
		orderMain: new(models.OrderMain),
	}
	c.session = GetDBConn().NewSession()
	defer c.session.Close()

	//更新处理次数
	if _, err := c.session.Exec("update order_pay_notify set deal_num = deal_num + 1 where order_sn = ?", in.OrderSn); err != nil {
		glog.Error(logFix, "执行数据库错误：", err.Error())
	}

	c.orderMain = GetOrderMainByOldOrderSn(in.OrderSn)

	//如果是电商竖屏虚拟订单，则用是旧订单号走后面的流程
	if c.orderMain.IsVirtual == 1 && c.orderMain.UserAgent == 7 {
		in.OrderSn = c.orderMain.OldOrderSn
	}

	c.orderDetail = GetOrderDetailByOrderSn(in.OrderSn)

	if c.orderMain.Id == 0 {
		glog.Error("订单支付通知,订单不存在！：", in.OrderSn)
		return
	}

	// 920锦鲤活动，幸运码赠送，只针对微信小程序内的
	if c.orderMain.UserAgent == 3 && (c.orderMain.ChannelId == 1 || c.orderMain.ChannelId == 5) && c.orderMain.OrgId != 6 {
		go luckyNumberForOrder(c.orderMain)
	}

	// 阿闻公益 0.01元购买10个爱心币
	var err error
	if c.orderMain.OrderType == 16 {
		num := cast.ToInt32(config.GetString("charityActivity.pay.coin"))
		if num == 0 {
			num = 10
		}
		client := ac.GetActivityCenterClient()
		_, err = client.UC.GiveCoin(client.Ctx, &ac.GiveCoinRequest{
			ScrmId: c.orderMain.MemberId,
			Mobile: utils.MobileDecrypt(c.orderMain.EnMemberTel),
			Num:    num,
			Remark: fmt.Sprintf("0.01元购买%d个爱心币", num),
		})
		if err != nil {
			glog.Errorf("0.01元购买%d个爱心币异常:%+v %s", num, err, in.OrderSn)
			return
		}
	}

	if c.orderMain.OrderStatus == 10 {
		//支付完，立即修改订单支付状态 mod by csf
		if c.orderMain.OrgId == 6 {
			c.orderMain.OrderStatus = 20
			c.orderMain.PayTime = in.PayTime
			c.orderMain.IsPay = 1
			c.orderMain.PaySn = in.PaySn
			c.orderMain.PayAmount = in.PayAmount
			c.orderMain.PayMode = in.PayMode
			item := "order_status,pay_time,pay_sn,is_pay,pay_mode"
			if c.orderMain.ChannelId == 100 {
				item += ",confirm_time"
			}
			c.orderMain.ConfirmTime = in.PayTime.Add(2 * time.Second)

			parentOrder := GetOrderByParentOrderSn(c.orderMain.OrderSn)
			if len(parentOrder) > 0 {
				_, err := c.session.ID(parentOrder[0].Id).Cols(item).Update(c.orderMain)
				if err != nil {
					glog.Error("订单支付通知,更新订单状态失败！：", in.OrderSn, " ", err.Error())
					return
				}
			}

			_, err := c.session.ID(c.orderMain.Id).Cols(item).Update(c.orderMain)
			if err != nil {
				glog.Error("订单支付通知,更新订单状态失败！：", in.OrderSn, " ", err.Error())
				return
			}

		} else {
			//支付完，立即修改订单支付状态 mod by csf
			c.orderMain.OrderStatus = 20
			c.orderMain.PayTime = in.PayTime
			c.orderMain.IsPay = 1
			c.orderMain.PaySn = in.PaySn
			c.orderMain.PayAmount = in.PayAmount
			c.orderMain.PayMode = in.PayMode
			_, err := c.session.ID(c.orderMain.Id).Update(c.orderMain)
			if err != nil {
				glog.Error("订单支付通知,更新订单状态失败！：", in.OrderSn, " ", err.Error())
				return
			}
		}

		//竖屏电商订单，更新支付状态
		if c.orderMain.UserAgent == 7 && c.orderMain.ChannelId == 5 {
			TableName := "upet_orders"
			if c.orderMain.IsVirtual == 1 {
				TableName = "upet_vr_order"
			}
			db := GetUPetDBConn()
			upetOrders := new(models.UpetOrdersPaynotice)
			if has, err := db.Table(TableName).Where("order_sn = ?", in.OrderSn).Select("/*FORCE_MASTER*/ *").Get(upetOrders); err != nil {
				glog.Error("订单支付通知,查询竖屏电商订单失败！：", in.OrderSn, " ", err.Error())
				return
			} else if !has {
				glog.Error("订单支付通知,查询竖屏电商订单无记录！：", in.OrderSn, " ", err.Error())
				return
			}

			if in.PayMode == 1 {
				upetOrders.PaymentCode = "ali_native"
			} else if in.PayMode == 2 {
				upetOrders.PaymentCode = "wx_jsapi"
			}
			upetOrders.PaymentTime = int(time.Now().Unix())
			upetOrders.OrderState = 20
			upetOrders.TradeNo = in.PaySn
			upetOrders.PaymentFrom = 1
			_, err = db.Table(TableName).Where("order_id = ?", upetOrders.OrderId).Cols("payment_code,payment_time,order_state,trade_no,payment_from").Update(upetOrders)
			if err != nil {
				glog.Error("订单支付通知,更新竖屏电商订单失败！：", in.OrderSn, " ", err.Error())
				return
			}
		}

		//记录已支付节点
		go SaveOrderLog([]*models.OrderLog{{
			OrderSn: c.orderMain.OrderSn,
			LogType: models.OrderLogPayedOrder,
		}})

		//电商积分订单需要扣减积分明细
		if c.orderMain.ChannelId == ChannelMallId && c.orderMain.OrderType == 8 {
			deductBool, err := c.DeductIntegral(c.orderMain.MemberId, c.orderMain.OldOrderSn, c.orderMain.OrgId)
			if !deductBool {
				glog.Error("积分订单扣减积分失败：", in.OrderSn, " ", err.Error(), err)
				return
			}
		}
	} else if c.orderMain.OrderStatus == 0 {
		glog.Error("订单支付通知查询错误：订单已取消", in.OrderSn)
		return
	}

	parentOrderModel := GetOrderByOldOrderSn(in.OrderSn, "order_main.*,order_detail.split_order_result,order_detail.pickup_station_id")
	//查询出错可能导致两次拆单的问题
	if parentOrderModel.Id > 0 && parentOrderModel.SplitOrderResult != 1 {

		if c.orderMain.OrderType == 17 || c.orderMain.OrderType == 21 {
			//VIP订单拆单
			err = saveVipSplitOrder(c.orderMain)
		} else {
			//1:拆分订单入库
			err = saveSplitOrder(c.orderMain)
		}
		if err != nil {
			glog.Error(in.OrderSn, ", 拆单失败！", err)
			//拆分失败更新主订单状态为异常单
			_, err = c.session.Exec("update order_detail inner join order_main on order_detail.order_sn=order_main.order_sn set split_order_result = ?,split_order_fail_reason = ? where order_main.old_order_sn = ?", 2, err.Error(), in.OrderSn)
			if err != nil {
				glog.Error("拆单失败状态更新失败！：", in.OrderSn, " ", err.Error())
				return
			}
		}
	}

	//如果是电商单，推拆单给电商；如果是小程序订单，推送第三方并发配送
	if c.orderMain.ChannelId == ChannelMallId {
		//拆单结果推回电商
		err := PushSplitResultToMall(c.orderMain)
		if err != nil {
			return
		}
		if c.orderMain.OrderType == 17 || c.orderMain.OrderType == 18 {

			var m []*models.OrderMain
			if err := GetDBConn().Table("order_main").
				Where("parent_order_sn=?", c.orderMain.OrderSn).Select("/*FORCE_MASTER*/ *").Find(&m); err != nil {
				glog.Error("VIP拆单后查询子单失败！：", c.orderMain.OrderSn, " ", err.Error())
				return
			}
			for _, x := range m {
				//用子订单循环去通知支付处理
				out, err := CardService{}.PayNotify(context.Background(), &oc.CardPayNotifyReq{OrderSn: x.OrderSn, Source: in.Source})
				if err != nil {
					glog.Error("VIP子单支付通知失败！：", x.OrderSn, " ", err.Error())
					return
				}
				if out.Code != 200 {
					glog.Error("VIP子单支付通知失败！：", x.OrderSn, " ", out.Message)
					return
				}
			}

		}
	} else {
		//实物子订单推送第三方
		realOrder := &CommonService{
			orderMain: new(models.OrderMain),
		}
		realOrder.session = GetDBConn().NewSession()
		defer realOrder.session.Close()

		//需要发配送的实物子订单（查询强制走主库）
		ok, err := realOrder.session.Select("/*FORCE_MASTER*/ *").Where("parent_order_sn = ? and is_virtual = 0", in.OrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error("实物子订单查询错误：", in.OrderSn, " ", err.Error())
			return
		}
		glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "需要发配送的实物子订单zx", ok)

		//小程序订单，如果存在需要发配送的实物单(目前只存在一个子实物订单)，发配送；电商我们这边不发配送，电商自己发
		// 存在社区团购的站点的不自动接单，有定时任务处理接单，社区团购订单order_type = 15过滤
		if c.orderMain.OrderType == 15 {
			// 社区团购金额计算更新
			go UpdateCommunityGroupPayAmount(in.OrderSn, in.PayAmount, true)
		}
		if c.orderDetail.PerformanceOperatorName == "系统分配" {
			//系统分配业绩的订单，更新业绩状态为有效
			_, _ = c.session.Where("order_sn = ? AND performance_status = 0 AND operator_name='系统分配'", in.OrderSn).
				Cols("performance_status").Update(&models.OrderPerformance{PerformanceStatus: 1})
		}
		if ok && parentOrderModel.PickupStationId == 0 && c.orderMain.OrderType != 15 {
			glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单支付通知,处理子订单1（查询门店配置）！")
			//根据门店id取是否自动接单
			shopSet, err := realOrder.GetShopSet()
			if err != nil {
				glog.Error("订单支付通知店铺自动接单查询查询错误：", realOrder.orderMain.OrderSn, " ", err.Error())
				return
			}
			glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "是否自动接单zx", shopSet.RetInfo.IsAotuOrder)
			realOrder.session.Begin()
			if shopSet.RetInfo.IsAotuOrder == 1 || c.orderMain.ChannelId == 100 {

				glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单支付通知,处理子订单2（自动接单）！")
				upOrderMain := new(models.OrderMain) //用于修改配送状态
				upOrderDetail := new(models.OrderDetail)

				upOrderMain.OrderStatusChild = 20102  //改接单状态
				upOrderDetail.AcceptTime = time.Now() //接单时间
				if c.orderMain.DeliveryType == 1 {
					// 快递配送订单流转 20101未接单=>20201待发货=>20202全部发货（待收货）=>20106已完成
					upOrderMain.OrderStatusChild = 20201 //待发货
				}
				if c.orderMain.ChannelId == 100 {
					upOrderMain.OrderStatusChild = 20106 //订单完成
					upOrderMain.OrderStatus = 30
				}

				if c.orderMain.OrgId == 6 {
					//冻结积分
					server := OrderService{}
					//接单后调用打印打印小票
					go server.PrintOrderDetail(realOrder.orderMain.ParentOrderSn)
				}

				//自提
				if realOrder.orderMain.DeliveryType == 3 && (realOrder.orderMain.ChannelId == ChannelAwenId || realOrder.orderMain.ChannelId == ChannelDigitalHealth) {
					dacClient := dac.GetDataCenterClient()
					setup, _ := dacClient.RPC.ShopBusinessSetupGet(dacClient.Ctx, &dac.ShopBusinessSetupGetRequest{
						Finance_Code: realOrder.orderMain.ShopId,
						Channel_Id:   realOrder.orderMain.ChannelId,
					})
					var stockTime int32 = 15
					if setup != nil && setup.Data.StockUpTime > 0 {
						stockTime = setup.Data.StockUpTime
					}
					if realOrder.orderMain.OrgId != cast.ToInt32(SAASMainId) {
						redisConn.ZAdd(SelfCollectionPicking, redis.Z{Score: float64(stockTime), Member: realOrder.orderMain.OrderSn})
					}

				}

				//推送第三方（子龙 or 全渠道）
				realOrderDetail := GetOrderDetailByOrderSn(realOrder.orderMain.OrderSn, "push_third_order")
				glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "推送第三方状态zx", realOrderDetail.PushThirdOrder)
				if realOrderDetail.PushThirdOrder == 0 {
					glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单支付通知,处理子订单2（推送第三方）！")
					//记录接单节点
					realOrder.SaveOrderLog([]*models.OrderLog{
						{
							OrderSn: realOrder.orderMain.ParentOrderSn,
							LogType: models.OrderLogAcceptedOrder,
						},
						{
							OrderSn: realOrder.orderMain.OrderSn,
							LogType: models.OrderLogAcceptedOrder,
						},
					})

					//推送第三方（子龙 or 全渠道）
					err = realOrder.PushThirdOrder(false)
					glog.Info(realOrder.orderMain.OldOrderSn, ", 推送第三方成功状态：", err)
					if err != nil {
						upOrderDetail.PushThirdOrder = 0
						upOrderDetail.PushThirdOrderReason = err.Error()
					} else {

						if c.orderMain.OrgId != 6 {
							//删除在途库存
							go DeleteTransportationInventory(realOrder.orderMain.ParentOrderSn, realOrder.orderMain.ChannelId, true)
						} else {
							res := Freeze(realOrder.orderMain.ParentOrderSn, realOrder.orderMain.ShopId, 2)
							if res.Code != 200 {
								glog.Info(realOrder.orderMain.ParentOrderSn, ", SAAS扣减库存失败：", res.Msg)
							} else {
								glog.Info(realOrder.orderMain.ParentOrderSn, ", SAAS扣减库存成功：")
							}
						}

						upOrderDetail.PushThirdOrder = 1

						if realOrder.orderMain.OrderStatus != 30 && realOrder.orderMain.DeliveryType != 3 && realOrder.orderMain.OrderType != 2 && realOrder.orderMain.ChannelId != 100 {
							//美团配送
							realOrder.PushMpOrder()
						}
					}
				}
				//如果是门店的订单直接完成
				if c.orderMain.ChannelId == 100 && c.orderMain.OrgId == 6 {
					upOrderMain.OrderStatusChild = 20106
					upOrderMain.OrderStatus = 30
				}

				_, err = realOrder.session.In("order_sn", realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn).Update(upOrderMain)
				if err != nil {
					glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, ", 订单支付通知,更新订单配送状态失败！", err)
					realOrder.session.Rollback()
					return
				}

				_, err = realOrder.session.In("order_sn", realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn).Update(upOrderDetail)
				if err != nil {
					glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单支付通知,更新订单配送状态失败！", err.Error())
					realOrder.session.Rollback()
					return
				}
				realOrder.session.Commit()
			}
		} else {
			glog.Error(in.OrderSn, "订单支付通知,查询拆单后子订单失败！")
			return
		}

		go func() {
			defer kit.CatchPanic()

			//记录订单支付状态
			GetRedisConn().Set(AwenPayedOrderKey+c.orderMain.OrderSn, time.Now().Unix(), 1*time.Hour)
			// 线下门店渠道 订单 不需要有新订单通知
			if c.orderMain.ChannelId != ChannelIdOfflineShop {
				//通知数据中心
				message := &models.Message{
					OrderId:     c.orderMain.OrderSn,
					MessageType: 1,
					FinanceCode: c.orderMain.ShopId,
					Msg:         fmt.Sprintf("【客户下单】您有一个新的订单：%s，请及时处理！", in.OrderSn),
				}
				MessageCreate(message)
			}

			cs := CommonService{
				orderMain: c.orderMain,
			}
			cs.session = GetDBConn().NewSession()
			defer cs.session.Close()

			// 下单增加商品销量
			productList := cs.GetOrderProduct()
			if len(productList) == 0 {
				return
			}

			var proNum []*pc.ProductNumber
			for _, v := range productList {
				proNum = append(proNum, &pc.ProductNumber{
					ProductId: cast.ToInt32(v.ProductId),
					Num:       v.Number,
				})
			}
			//todo 商品无需修改
			pcClient := pc.GetDcProductClient()
			defer pcClient.Close()

			pcClient.RPC.UpdateStoreProductSalesVolume(pcClient.Ctx, &pc.UpdateSalesVolumeRequest{
				ChannelId:   c.orderMain.ChannelId,
				ShopId:      c.orderMain.ShopId,
				ProductList: proNum,
			})

			if c.orderDetail.ConsultOrderSn != "" && c.orderMain.OrderType != 17 && c.orderMain.OrderType != 18 {
				_ = PushDigitalHealthOrder(realOrder.orderMain.OrderSn, c.orderDetail.ConsultOrderSn, 1, 0)
			}
		}()
	}

	//更新处理状态
	c.session.Exec("update order_pay_notify set deal_status = 1,update_time =? where order_sn = ?", kit.GetTimeNow(), in.OrderSn)

	//推送广告转化
	go PushAdvertisementMp(c.orderMain.OrderSn)
	//推送订单状态到 腾讯有数
	go PushOrderStatusToTencent(in.OrderSn, 0)
	//更新用户健康值(冻结)
	/*healthDetail := models.HealthDetail{
		Type:       3,
		Title:      "线上消费",
		Content:    "小程序支付获得",
		HealthType: 1,
	}
	go UpdateUserHealth(in.OrderSn, healthDetail)*/
}

// 更新order_group_activity表已支付金额加减，24小时后的退款不减
func UpdateCommunityGroupPayAmount(orderSn string, payAmount int32, updateType bool) {
	glog.Info("UpdateCommunityGroupPayAmount更新查询：", orderSn, payAmount)
	db := GetDBConn()
	var orderGroupActivityId int32
	if _, err := db.SQL("select order_group_activity_id from order_main_group where parent_order_sn = ?", orderSn).Get(&orderGroupActivityId); err != nil {
		glog.Error("UpdateCommunityGroupPayAmount更新查询order_main_group表失败：", orderSn, err.Error())
		return
	}
	if orderGroupActivityId < 1 {
		glog.Error("UpdateCommunityGroupPayAmount更新查询order_main_group表失败，未查询到数据：", orderSn)
		return
	}
	if updateType { //增加
		redisConn := GetRedisConn()
		key := "order-center:UpdateCommunityGroupPayAmount:" + orderSn
		if !redisConn.SetNX(key, 1, 24*time.Hour).Val() {
			glog.Error("UpdateCommunityGroupPayAmount更新查询order_main_group表失败，未查询到数据：", orderSn)
			return
		}
		if _, err := db.Exec("update order_group_activity set pay_amount = pay_amount + ?  where id = ?", payAmount, orderGroupActivityId); err != nil {
			redisConn.Del(key) //如果失败了则删除掉这个key
			glog.Error("UpdateCommunityGroupPayAmount更新失败：", orderSn, orderGroupActivityId, err.Error())
		} else {
			glog.Info("UpdateCommunityGroupPayAmount更新order_group_activity成功：", orderSn, orderGroupActivityId, payAmount)
		}
		if _, err := db.Exec("update order_main_group set pay_amount = pay_amount + ?  where order_group_activity_id = ? and parent_order_sn = ?", payAmount, orderGroupActivityId, orderSn); err != nil {
			redisConn.Del(key) //如果失败了则删除掉这个key
			glog.Error("UpdateCommunityGroupPayAmount更新失败：", orderSn, orderGroupActivityId, err.Error())
		} else {
			glog.Info("UpdateCommunityGroupPayAmount更新order_main_group成功：", orderSn, orderGroupActivityId, payAmount)
		}
	} else { //减少
		if _, err := db.Exec("update order_group_activity set pay_amount = pay_amount - ?  where id = ?", payAmount, orderGroupActivityId); err != nil {
			glog.Error("UpdateCommunityGroupPayAmount更新失败：", orderSn, orderGroupActivityId, err.Error())
		} else {
			glog.Info("UpdateCommunityGroupPayAmount更新order_group_activity成功：", orderSn, orderGroupActivityId, -payAmount)
		}
		if _, err := db.Exec("update order_main_group set pay_amount = pay_amount - ?  where order_group_activity_id = ? and parent_order_sn = ?", payAmount, orderGroupActivityId, orderSn); err != nil {
			glog.Error("UpdateCommunityGroupPayAmount更新失败：", orderSn, orderGroupActivityId, err.Error())
		} else {
			glog.Info("UpdateCommunityGroupPayAmount更新order_main_group成功：", orderSn, orderGroupActivityId, -payAmount)
		}
	}
}

// 团长拼团 退款需要减去对应的已支付金额
// price 需要退款的金额 正数
func UpdateCommunityGroupRefundPayAmount(OrderSn string, price int32) {
	orderMain := &models.OrderMain{}
	has, err := GetDBConn().Where("order_sn=?", OrderSn).Get(orderMain)
	if err != nil || !has {
		return
	}
	if orderMain.OrderType == 15 { //订单完成后24小时不减金额
		if orderMain.ConfirmTime.IsZero() || orderMain.ConfirmTime.Add(24*time.Hour).After(time.Now()) {
			UpdateCommunityGroupPayAmount(orderMain.ParentOrderSn, price, false)
		}
	}
}

func PushAdvertisementMp(orderSn string) {
	db := GetDBConn()

	var ad models.AdvertisementMpRecord
	ok, err := db.Table("advertisement_mp_record").Where("order_sn = ? and action_type = 2", orderSn).Get(&ad)
	if err != nil {
		glog.Error(orderSn, "推送广告MP（pushAdvertisementMp）转化获取数据异常！", err.Error())
		return
	}

	if ok {
		rep := oc.AddAdvertisementMpRecordRequest{
			OrderSn:    orderSn,
			UserId:     ad.UserId,
			ActionType: 3,
			Url:        "",
			ClickId:    ad.ClickId,
			//现在广告渠道只开通有WEB
			UserAgent:  5,
			ActionTime: time.Now().Local().Unix(),
			ChannelId:  ad.ChannelId,
		}
		res, err := AdvertisementMpService{}.AddAdvertisementMpRecord(context.Background(), &rep)
		if err != nil {
			glog.Error(orderSn, "推送广告MP（pushAdvertisementMp）转化添加数据异常！", err.Error())
			return
		}
		if res.Code != 200 {
			glog.Error(orderSn, "推送广告MP（pushAdvertisementMp）转化添加数据失败！", kit.JsonEncode(res))
			return
		}
	}
}

// 推送拆单结果给电商
// 如果有子单，推所有子单，如果没有子单，推主单
func PushSplitResultToMall(parentOrderMain *models.OrderMain) error {
	if len(parentOrderMain.OrderSn) == 0 {
		glog.Warning("主订单不存在, ", kit.RunFuncName(2))
		return errors.New("主订单不存在")
	}
	var childOrderList []*models.Order
	//if parentOrderMain.IsVirtual == 1 && parentOrderMain.UserAgent == 7 {
	//	childOrderList = GetUpetOrderByOldOrderSn(parentOrderMain.OldOrderSn)
	//} else {
	childOrderList = GetOrderByParentOrderSn(parentOrderMain.OrderSn)
	//}
	if len(childOrderList) == 0 {
		glog.Error(parentOrderMain.OrderSn, ", 主订单的子订单未查询到")
		return errors.New("主订单的子订单未查询到")
	}

	glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 1")

	var childOrderSns []string
	for _, childOrder := range childOrderList {
		childOrderSns = append(childOrderSns, childOrder.OrderSn)
	}

	db := GetDBConn()

	pushChildOrderProductMap := map[string][]dto.SyncMallOrderGoods{}          //推送的商品map
	childOrderProductVerifyCodeMap := map[string][]dto.SyncMallOrderCodeinfo{} //推送的核销码map
	if err := func() error {
		var childOrderProductList []*models.OrderProduct
		err := db.In("order_sn", childOrderSns).Find(&childOrderProductList)
		if err != nil {
			return err
		}

		var childOrderProductVerifyCodeList []*models.OrderVerifyCode
		err = db.In("order_sn", childOrderSns).Find(&childOrderProductVerifyCodeList)
		if err != nil {
			return err
		}

		//订单商品
		for _, v := range childOrderProductList {
			syncMallOrderGoods := dto.SyncMallOrderGoods{
				RecID:         v.MallOrderProductId,
				GoodsID:       v.SkuId,
				GoodsNum:      v.Number,
				GoodsPrice:    v.MarkingPrice,
				GoodsPayPrice: v.PaymentTotal,
				DiscountPrice: v.DiscountPrice, // 价格翻倍bugfix
				GoodsName:     v.ProductName,
				OcId:          v.Id,
				//VoucherInfo:   VoucherInfo //商品优惠信息
			}

			//商品优惠信息
			var SyncMallOrderGoodsVoucherInfoList []dto.SyncMallOrderGoodsVoucherInfo
			singlePrivilege := cast.ToInt32(math.Ceil(cast.ToFloat64(v.Privilege) / cast.ToFloat64(v.Number)))
			itemSumPrivilege := int32(0)
			itemSumPayment := int32(0)
			for i := int32(1); i <= v.Number; i++ {
				var SyncMallOrderGoodsVoucherInfo dto.SyncMallOrderGoodsVoucherInfo
				if i == v.Number {
					//最后一个倒减
					SyncMallOrderGoodsVoucherInfo = dto.SyncMallOrderGoodsVoucherInfo{
						VoucherPrice:  v.PaymentTotal - itemSumPayment,
						VoucherAmount: v.Privilege - itemSumPrivilege,
					}
				} else {
					SyncMallOrderGoodsVoucherInfo = dto.SyncMallOrderGoodsVoucherInfo{
						VoucherPrice:  v.PayPrice,
						VoucherAmount: singlePrivilege,
					}
					itemSumPayment += v.PayPrice
					itemSumPrivilege += singlePrivilege
				}
				SyncMallOrderGoodsVoucherInfoList = append(SyncMallOrderGoodsVoucherInfoList, SyncMallOrderGoodsVoucherInfo)
			}
			if len(SyncMallOrderGoodsVoucherInfoList) > 0 {
				//syncMallOrderGoods.VoucherInfo = kit.JsonEncode(SyncMallOrderGoodsVoucherInfoList)
				syncMallOrderGoods.VoucherInfo = SyncMallOrderGoodsVoucherInfoList
			}

			pushChildOrderProductMap[v.OrderSn] = append(pushChildOrderProductMap[v.OrderSn], syncMallOrderGoods)
		}
		//订单核销码
		for _, v := range childOrderProductVerifyCodeList {
			syncMallOrderVerifyCode := dto.SyncMallOrderCodeinfo{
				ErpOrderID: v.OrderSn,
				VrCode:     v.VerifyCode,
			}
			childOrderProductVerifyCodeMap[v.OrderSn] = append(childOrderProductVerifyCodeMap[v.OrderSn], syncMallOrderVerifyCode)
		}

		return nil
	}(); err != nil {
		glog.Error("查询子订单商品信息失败, ", err, ", ", kit.JsonEncode(childOrderSns))
		return err
	}

	glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 2")

	//推oms实物子订单
	var pushOmsOrders []*dto.DeliveryOrderCreateRequest
	syncMallOrderResults := make([]dto.SyncMallOrderResult, 0)
	for _, childOrder := range childOrderList {
		syncMallOrderResult := dto.SyncMallOrderResult{
			OrderInfo: dto.SyncMallOrderInfo{
				OldOrderSn:     parentOrderMain.OldOrderSn,
				OrderSn:        childOrder.OrderSn,
				OrderAmount:    childOrder.Total,
				ShippingFee:    childOrder.Freight,
				GoodsAmount:    childOrder.GoodsTotal,
				IsVirtual:      childOrder.IsVirtual,
				PromotionTotal: childOrder.Privilege,
				PowerId:        childOrder.PowerId,
				//v2.9.10商城秒杀版本修改 推送给电商时 因为平台的orderType与电商的orderType不对应 需要做一个转换
				OrderType:     childOrder.OrderType, //
				WarehouseCode: childOrder.OrderMain.WarehouseCode,
			},
			OrderGoods:    pushChildOrderProductMap[childOrder.OrderSn],
			OrderCodeinfo: childOrderProductVerifyCodeMap[childOrder.OrderSn],
		}

		syncMallOrderResults = append(syncMallOrderResults, syncMallOrderResult)

		//非实物订单跳过
		if childOrder.IsVirtual != 0 {
			continue
		}

		glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 3, ", childOrder.OrderSn)

		//订单积分只计算实物的
		IntegralOperation(childOrder.OrderSn, true)

		if parentOrderMain.OrderType != 20 {

			code := childOrder.OrderMain.WarehouseCode
			pushOmsOrder := &dto.DeliveryOrderCreateRequest{
				DeliveryOrderRequest: &dto.DeliveryOrderRequest{
					DeliveryOrderCode: childOrder.OrderSn,
					OrderType:         dto.CommonlyDeliveryOrder,
					WarehouseCode:     code,
					CreateTime:        kit.GetTimeNow(),
					PlaceOrderTime:    kit.GetTimeNow(childOrder.CreateTime),
					PayTime:           kit.GetTimeNow(childOrder.PayTime),
					PayNo:             childOrder.PaySn,
					OperateTime:       kit.GetTimeNow(),
					// ShopCode:          "JD01",
					LogisticsCode: "OTHER",
					Remark:        childOrder.BuyerMemo,
					ReceiverInfo: dto.ReceiverInfo{
						Name:          childOrder.ReceiverName,
						Mobile:        childOrder.EnReceiverMobile,
						Tel:           childOrder.EnReceiverPhone,
						Province:      childOrder.ReceiverState,
						City:          childOrder.ReceiverCity,
						Area:          childOrder.ReceiverDistrict,
						DetailAddress: childOrder.ReceiverAddress,
					},
				},
			}
			// if parentOrderMain.ChannelId == ChannelMallId && parentOrderMain.ShopId == CSYMainId {
			// 	pushOmsOrder.DeliveryOrderRequest.ShopCode = config.GetString("ShopCode")
			// }
			pushOmsOrder.DeliveryOrderRequest.ShopCode = GetShopCodeByOrder(parentOrderMain)

			for _, goods := range pushChildOrderProductMap[childOrder.OrderSn] {
				glog.Info("推送oms商品, ", kit.JsonEncode(goods))
				// oms 订单列表
				var orderLine = &dto.OrderLineRequest{
					OwnerCode:   goods.GoodsID,
					ItemId:      goods.GoodsID,
					PlanQty:     goods.GoodsNum,
					RetailPrice: fmt.Sprintf("%.2f", kit.FenToYuan(goods.DiscountPrice)),
					ActualPrice: fmt.Sprintf("%.2f", kit.FenToYuan(goods.DiscountPrice)),
					ItemName:    goods.GoodsName,
				}

				// oms 订单列表
				pushOmsOrder.OrderLines = append(pushOmsOrder.OrderLines, orderLine)
			}

			pushOmsOrders = append(pushOmsOrders, pushOmsOrder)
		}
	}
	glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 4")

	data := kit.JsonEncode(syncMallOrderResults)
	stringA := kit.GetMd5("data=" + data)
	signsignValue := strings.ToUpper(kit.GetMd5(stringA + config.GetString("express_key")))

	dsparam := make(map[string]interface{})
	dsparam["data"] = data
	dsparam["sign"] = signsignValue
	dsurl := config.GetString("mall_api") + "/mobile/index.php?act=openapi&op=syncDatacenterOrder"
	code, body := utils.HttpPostFormToMall(dsurl, "", dsparam)
	glog.Info(fmt.Sprintf("调用电商推送子订单接口(%s)返回结果：code:%s,返回内容:%s,接口参数:%s", dsurl, strconv.Itoa(code), body, data))

	if code != 200 {
		glog.Error("拆分订单同步电商出错：" + parentOrderMain.OrderSn)
		return errors.New("推送电商出错")
	}

	glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 5")

	dsRes := new(dto.MallBaseResponse)
	err := json.Unmarshal([]byte(body), dsRes)
	if err != nil {
		glog.Error(parentOrderMain.OrderSn, ", 解析电商接口返回失败, ", err, ", ", body)
		return err
	} else if dsRes.Code != 200 {
		glog.Error(parentOrderMain.OrderSn, ", 拆分订单同步电商出错, ", dsRes.Datas)
		return errors.New("推送电商出错")
	} else {
		//电商实物订单推oms
		if len(pushOmsOrders) > 0 && parentOrderMain.OrderType != 21 {
			glog.Info(parentOrderMain.OrderSn, ", 推送拆单结果到电商, 6")

			omsService := &OmsService{}
			var res *dto.DeliveryOrderCreateResponse
			for _, pushOmsOrder := range pushOmsOrders {
				res, err = omsService.OmsOrderSynchronize(pushOmsOrder)
				if err != nil {
					glog.Error(parentOrderMain.OldOrderSn, ", 电商订单推送oms失败, ", err, ",", kit.JsonEncode(pushOmsOrder))
				} else if cast.ToInt(res.Code) != 0 {
					glog.Error(parentOrderMain.OldOrderSn, ", 电商订单推送oms失败, ", res.Message, ",", kit.JsonEncode(pushOmsOrder))
				} else {
					glog.Info(parentOrderMain.OldOrderSn, ", 电商订单推送oms成功, ", kit.JsonEncode(pushOmsOrder))
					//推送第三方成功
					GetDBConn().In("order_sn", []string{pushOmsOrder.DeliveryOrderRequest.DeliveryOrderCode, parentOrderMain.OrderSn}).Update(&models.OrderDetail{
						PushThirdOrder: 1,
					})

					//删除在途库存
					go DeleteTransportationInventory(parentOrderMain.OrderSn, parentOrderMain.ChannelId, true)
				}
			}
		}
	}
	return nil
}

// 全渠道，管易发货状态修改  全渠道，管易发货调用
func OrderStatus(DeliverParam *dto.DeliverParam) (bool, error) {
	logPrefix := fmt.Sprintf("OrderStatus-order-center==== 订单编号：%s", DeliverParam.OrderSn)

	glog.Info(logPrefix, fmt.Sprintf("全渠道，管易发货状态修改 order_sn : %s %s", DeliverParam.OrderSn, kit.JsonEncode(DeliverParam)))

	c := CommonService{
		session:   GetDBConn().NewSession(),
		orderMain: new(models.OrderMain),
	}
	defer c.session.Close()

	ok, err := c.session.Where("order_sn = ?", DeliverParam.OrderSn).Get(c.orderMain)
	if err != nil {
		glog.Error(logPrefix, fmt.Sprintf("全渠道，管易发货状态修改 order_sn : %s 数据库错误：%s", DeliverParam.OrderSn, err.Error()))
		return false, err
	}
	if !ok {
		glog.Error(logPrefix, fmt.Sprintf("全渠道，管易发货状态修改 order_sn : %s 订单不存在!", DeliverParam.OrderSn))
		return false, errors.New("订单不存在！")
	}

	if c.orderMain.ChannelId == ChannelMtId || c.orderMain.ChannelId == ChannelElmId {
		glog.Error(logPrefix, fmt.Sprintf("全渠道，管易发货状态修改 order_sn : %s 美团OR饿了么订单不能用电商接口!", DeliverParam.OrderSn))
		return true, nil
	}

	if c.orderMain.OrderStatusChild == 20202 {
		glog.Error(logPrefix, fmt.Sprintf("全渠道，管易发货状态修改 order_sn : %s 订单已全部发货!", DeliverParam.OrderSn))
		return true, nil
	}

	IsEntire := 0
	if DeliverParam.IsEntire == 1 {
		orderDetailUp := models.OrderDetail{}
		if DeliverParam.Source == 1 {
			//order.GjpStatus = "Sended"
		} else {
			orderDetailUp.GyDeliverStatus = 1
		}

		_, err = c.session.Where("order_sn = ?", DeliverParam.OrderSn).Update(orderDetailUp)
		if err != nil {
			glog.Error(logPrefix, "更新订单详情失败:", err.Error())
			c.session.Rollback()
			return false, err
		}

		orderMainUp := &models.OrderMain{
			OrderStatus:      20,
			OrderStatusChild: 20202,
		}
		IsEntire = 1
		if c.orderMain.DeliveryType == 1 && c.orderMain.DeliverTime.IsZero() {
			orderMainUp.DeliverTime = time.Now()
		}
		_, err = c.session.Where("order_sn = ?", DeliverParam.OrderSn).Update(orderMainUp)
		if err != nil {
			glog.Error(logPrefix, "更新订单失败:", err.Error())
			c.session.Rollback()
			return false, err
		}

		_, err = c.session.Exec("update order_product set deliver_num = number,deliver_status = 1 where order_sn = ? ", DeliverParam.OrderSn)
		if err != nil {
			glog.Error(logPrefix, "更新订单商品发货数量和发货状态失败:", err.Error())
			c.session.Rollback()
			return false, err
		}

		// 如果是快递配送，也一并更新父单状态
		if c.orderMain.DeliveryType == 1 && len(c.orderMain.ParentOrderSn) > 0 {
			_, err = c.session.Where("order_sn = ?", c.orderMain.ParentOrderSn).Update(orderDetailUp)
			if err != nil {
				glog.Error(logPrefix, "更新父订单详情失败:", err.Error())
				c.session.Rollback()
				return false, err
			}
			_, err = c.session.Where("order_sn = ?", c.orderMain.ParentOrderSn).Update(orderMainUp)
			if err != nil {
				glog.Error(logPrefix, "更新父订单失败:", err.Error())
				c.session.Rollback()
				return false, err
			}
			_, err = c.session.Exec("update order_product set deliver_num = number,deliver_status = 1 where order_sn = ? ", c.orderMain.ParentOrderSn)
			if err != nil {
				glog.Error(logPrefix, "更新父订单商品发货数量失败:", err.Error())
				c.session.Rollback()
				return false, err
			}
		}
	} else {
		for _, i2 := range DeliverParam.DeliverDetail {
			orderProduct := models.OrderProduct{}
			productCount, _ := c.session.Where("order_sn = ? and sku_id = ? ", DeliverParam.OrderSn, i2.GoodsSku).Sum(orderProduct, "number")
			deliver_num, _ := c.session.Where("order_sn = ? and sku_id = ?", DeliverParam.OrderSn, i2.GoodsSku).Sum(orderProduct, "deliver_num")
			//修改订单商品表状态
			if deliver_num < productCount {
				var DeliverNum int32
				if DeliverParam.Source == 1 {
					DeliverNum = int32(productCount)
				} else {
					DeliverNum = int32(deliver_num) + i2.Num
				}
				_, err = c.session.Where("order_sn = ? and sku_id = ? and discount_price > 0", DeliverParam.OrderSn, i2.GoodsSku).Limit(1).Update(models.OrderProduct{
					DeliverNum:    DeliverNum,
					DeliverStatus: 1,
				})
				if err != nil {
					glog.Error(logPrefix, "更新订单商品发货数量失败:", err.Error())
					c.session.Rollback()
					return false, err
				}
				//物流配送，一并更新父订单
				if c.orderMain.DeliveryType == 1 && len(c.orderMain.ParentOrderSn) > 0 {
					_, err = c.session.Where("order_sn = ? and sku_id = ? and discount_price > 0", c.orderMain.ParentOrderSn, i2.GoodsSku).Limit(1).Update(models.OrderProduct{
						DeliverNum:    DeliverNum,
						DeliverStatus: 1,
					})
					if err != nil {
						glog.Error(logPrefix, "更新父订单商品发货失败:", err.Error())
						c.session.Rollback()
						return false, err
					}
				}
			}
		}

		//查找有没有全部发货
		OrderProductCount := new(models.OrderProduct)
		productCount, err := c.session.Where("order_sn = ?", DeliverParam.OrderSn).Sum(OrderProductCount, "number")
		//已發貨
		deliver_num, err := c.session.Where("order_sn = ?", DeliverParam.OrderSn).Sum(OrderProductCount, "deliver_num")

		orderMain := models.OrderMain{}
		orderDetail := models.OrderDetail{}
		if deliver_num < productCount {
			orderMain.OrderStatus = 20
			orderMain.OrderStatusChild = 20204
			if DeliverParam.Source == 1 {
				//order.GjpStatus = "PartSend"
			} else {
				orderDetail.GyDeliverStatus = 2
			}
		} else {
			orderDetail.GyDeliverStatus = 1
			orderMain.OrderStatus = 20
			orderMain.OrderStatusChild = 20202
			IsEntire = 1
		}

		if c.orderMain.DeliveryType == 1 && c.orderMain.DeliverTime.IsZero() {
			orderMain.DeliverTime = time.Now()
		}

		//修改订单表状态
		_, err = c.session.Where("order_sn = ?", DeliverParam.OrderSn).Update(orderMain)
		if err != nil {
			c.session.Rollback()
			return false, err
		}

		//修改订单表状态
		_, err = c.session.Where("order_sn = ?", DeliverParam.OrderSn).Update(orderDetail)
		if err != nil {
			c.session.Rollback()
			return false, err
		}

		//快递配送，一并更新父订单
		if c.orderMain.DeliveryType == 1 && len(c.orderMain.ParentOrderSn) > 0 {
			//修改父订单表状态
			_, err = c.session.Where("order_sn = ?", c.orderMain.ParentOrderSn).Update(orderMain)
			if err != nil {
				c.session.Rollback()
				return false, err
			}
			//修改父订单表状态
			_, err = c.session.Where("order_sn = ?", c.orderMain.ParentOrderSn).Update(orderDetail)
			if err != nil {
				c.session.Rollback()
				return false, err
			}
		}
	}
	glog.Infof("全渠道，管易发货状态修改 order_sn %s 全部发货: %d", DeliverParam.OrderSn, IsEntire)
	//保存mq信息
	/*if IsEntire == 1 {
		var productModel []models.OrderProduct
		c.session.Where("order_sn = ?", DeliverParam.OrderSn).Find(&productModel)
		deliverLog := dto.DeliverLog{
			Orderid:  c.orderMain.OldOrderSn, //添加数据，便于释放库存
			Code:     c.orderMain.WarehouseCode,
			Source:   c.orderMain.Source,
			Isfinish: "1",
		}
		for _, pro := range productModel {
			deliverLog.Goodslist = append(deliverLog.Goodslist, struct{ GoodsId string }{GoodsId: pro.SkuId})
		}
		_, err = c.session.Insert(models.MqInfo{
			Exchange: "ordercenter",
			Quene:    "dc_sz_stock_update",
			Content:  kit.JsonEncode(deliverLog),
			Ispush:   0,
			Lastdate: time.Time{},
		})
		if err != nil {
			c.session.Rollback()
			return false, err
		}
	}*/

	err = c.session.Commit()
	if err != nil {
		return false, err
	}

	return true, nil
}

// 发布消费或退货积分变化mq
func PublishIntegralChange(integral oc.IntegralNotify) {
	exchange := "ordercenter"
	queue := "oc-sz-integral-notify"

	// 错误日志
	orderSn := integral.Ordersn
	if len(orderSn) == 0 {
		orderSn = integral.Oldordersn
	}
	var errStr strings.Builder
	errStr.WriteString("发布积分变化mq失败; 订单号：")
	errStr.WriteString(orderSn)
	errStr.WriteString(";积分类型：")
	errStr.WriteString(strconv.Itoa(int(integral.Integraltype)))
	errStr.WriteString("; err: ")

	integralJson := kit.JsonEncode(integral)
	glog.Info("积分变化mq参数; content: ", integralJson)

	result := utils.PublishRabbitMQ(queue, integralJson, exchange)
	var mqInfo models.MqInfo
	mqInfo.Content = integralJson
	mqInfo.Exchange = exchange
	mqInfo.Quene = queue
	mqInfo.Ispush = 1
	if !result {
		mqInfo.Ispush = 0
	}
	_, err := GetDBConn().Insert(&mqInfo)
	if err != nil {
		errStr.WriteString(err.Error())
		glog.Error(errStr.String())
	}
}

// SetAfterOrderRequest 设置AfterApplyOrderRequest 实体 全渠道
func SetAfterOrderRequest(params *oc.AfterApplyOrderRequest) *oc.AfterorderRequest {
	model := new(oc.AfterorderRequest)
	afterOrderOrderInfo := new(oc.OrderAfterorder)
	afterOrderOrderInfo.Rtid = params.RefundSn              //必填  业务系统传递的退款单号  自己随便定义的单号
	afterOrderOrderInfo.Tid = params.OrderSn                //必填， 原始订单号：网店订单号， 订单号信息 下单的单号
	afterOrderOrderInfo.Privilege = params.DiscountAmount   //必填，商品明细上的订单均摊后的优惠金额  TODO 待确认
	afterOrderOrderInfo.Postfee = params.PostFee            //必填，商品明细上的订单均摊后的运费金额  TODO 待确认
	afterOrderOrderInfo.Created = params.CreateTime         //必填，系统生成
	afterOrderOrderInfo.Status = params.Status              //管家婆审核
	afterOrderOrderInfo.Aftsaletype = params.RefundTypeSn   //必填 售后单类型	JustRefund=仅退款	RefundAndGoods=退款退货
	afterOrderOrderInfo.Reasoncode = params.ReasonCode      //必填 01=无理由退换货	02=质量问题	03=损坏	04=错发	05=漏发
	afterOrderOrderInfo.Logistbillcode = ""                 //物流单号
	afterOrderOrderInfo.Aftsaleremark = params.RefundRemark // 售后单备注

	refundAmount, _ := strconv.ParseFloat(params.RefundAmount, 64)
	discountAmount, _ := strconv.ParseFloat(params.DiscountAmount, 64)
	postFee, _ := strconv.ParseFloat(params.PostFee, 64)

	total := refundAmount + discountAmount + postFee
	strTotal := strconv.FormatFloat(total, 'f', -1, 64)

	afterOrderOrderInfo.Total = strTotal //退款金额

	if len(params.RefundGoodsOrders) > 0 {
		for _, item := range params.RefundGoodsOrders {
			detail := new(oc.OrderAfterorderDetails)
			detail.Oid = item.OcId
			detail.Backqty = strconv.Itoa(int(item.Quantity))
			detail.Backtotal = item.RefundAmount
			detail.Outeriid = item.GoodsId
			afterOrderOrderInfo.Details = append(afterOrderOrderInfo.Details, detail)
		}
	}

	model.Orders = append(model.Orders, afterOrderOrderInfo)
	return model
}

// 判断订单是否拆单中
func OrderIsSplit(orderSn []string) map[string]int32 {
	result := make(map[string]int32)
	if len(orderSn) == 0 {
		return result
	}

	dbConn := NewSlaveDbConn()
	defer dbConn.Close()

	var ret []models.OrderDetail
	if err := dbConn.Select("order_sn, split_order_result").
		In("order_sn", orderSn).
		Find(&ret); err != nil {
		glog.Error(err)
		return result
	}

	for _, v := range ret {
		result[v.OrderSn] = v.SplitOrderResult
	}
	return result
}

// 删除在途库存
// upStock 是否更新warehouse_goods库存
func DeleteTransportationInventory(orderSn string, channelId int32, upStock ...bool) {
	defer kit.CatchPanic()

	session := GetDBConn().NewSession()
	defer session.Close()

	glog.Info(orderSn, ", 订单清理在途库存1, ", kit.JsonEncode(upStock))

	var freezeStocks []*models.OrderFreezeStock
	//查询强制走主库
	err := session.Select("/*FORCE_MASTER*/ *").Where("order_sn=?", orderSn).Find(&freezeStocks)
	if err != nil {
		glog.Error(orderSn, ", 查询订单在途库存失败, ", err, ", ", kit.RunFuncName(2))
		return
	}

	if len(freezeStocks) == 0 {
		return
	}

	glog.Info(orderSn, ", 订单清理在途库存2")

	session.Begin()

	_, err = session.Where("order_sn=?", orderSn).Delete(&models.OrderFreezeStock{})
	if err != nil {
		glog.Error(orderSn, ", 清除在途库存失败, ", err, ", ", kit.RunFuncName(2))
		session.Rollback()
		return
	}

	if len(upStock) > 0 && upStock[0] {
		if channelId == ChannelMallId {
			var skuWarehouse []dto.OrderSkuWarehouseId
			if err = session.Table("order_main").Alias("m").
				Join("inner", "order_product op", "op.order_sn = m.order_sn").
				Join("inner", "dc_dispatch.warehouse w", "w.code = m.warehouse_code").
				Select("op.sku_id,op.number,w.id as warehouse_id").
				Where("is_virtual=0").Where("parent_order_sn=? and channel_id=?", orderSn, channelId).
				Find(&skuWarehouse); err != nil {
				glog.Info(orderSn, ", 查询子订单商品信息失败, ", err)
				return
			}

			glog.Info(orderSn, ", 电商回写warehouse_goods, ", kit.JsonEncode(skuWarehouse))

			for _, v := range skuWarehouse {
				//减去
				_, err = session.Exec("update warehouse_goods set stock=if(stock > ?,stock-?,0) where goodsid=? and warehouse_id=?",
					v.Number, v.Number, v.SkuId, v.WarehouseId)
				if err != nil {
					glog.Error(orderSn, ", 更新warehouse_goods库存失败, ", err, ", ", kit.RunFuncName(2))
					session.Rollback()
					return
				} else {
					go dealProductStockNoticeParam(v.WarehouseId, v.SkuId, v.Number)
				}
			}
		} else {
			//减去warehouse_goods表库存
			for _, v := range freezeStocks {
				//减去
				_, err = session.Exec("update warehouse_goods set stock=stock-"+cast.ToString(v.Stock)+" where goodsid=? and warehouse_id=?", v.SkuId, v.WarehouseId)
				if err != nil {
					glog.Error(orderSn, ", 更新warehouse_goods库存失败, ", err, ", ", kit.RunFuncName(2))
					session.Rollback()
					return
				} else {
					go dealProductStockNoticeParam(v.WarehouseId, v.SkuId, v.Stock)
				}
			}
		}
	}

	session.Commit()
}

// skuId转productId
func SkuIdsToProductIds(skuId []int32) map[int32]int32 {
	client := pc.GetDcChannelProductClient()
	defer client.Close()

	if res, err := client.RPC.GetProductIdBySkuId(client.Ctx, &pc.GetProductIdBySkuIdRequest{
		SkuId: skuId,
	}); err != nil {
		glog.Error(err)
		return map[int32]int32{}
	} else {
		return res.Data
	}
}

// @Desc					积分操作
// @Param<orderId>			添加积分传父订单号，退款时传退款订单号
// @Param<integralType>		积分类型（true 为添加、false 为减积分）
func IntegralOperation(orderId string, integralType bool) {
	c := CommonService{
		session: GetDBConn().NewSession(),
	}
	defer c.session.Close()

	var integral oc.IntegralNotify
	// 添加积分
	if integralType {
		c.orderMain = GetOrderMainByOrderSn(orderId)
		if c.orderMain.Id == 0 {
			glog.Error("添加积分未查到订单, ", orderId)
			return
		}

		//用户为v0等级不加积分
		member := models.UpetMember{}
		b, err := GetUPetDBConn().Table("upet_member").Where("scrm_user_id=?", c.orderMain.MemberId).Get(&member)
		if err != nil {
			glog.Error("积分操作查询用户信息失败：", err.Error(), "用户ID：", member.ScrmUserId)
			return
		}

		if b && member.UserLevelId <= 0 {
			glog.Info("会员等级为0不送积分，会员ID：", member.ScrmUserId)
			return
		}

		//虚拟订单不做积分操作
		if c.orderMain.IsVirtual == 1 {
			return
		}
		if c.orderMain.ChannelId != ChannelAwenId && c.orderMain.ChannelId != ChannelMallId && c.orderMain.ChannelId != ChannelDigitalHealth {
			return
		}
		integral.Oldordersn = c.orderMain.OldOrderSn
		integral.Mobile = utils.MobileDecrypt(c.orderMain.EnMemberTel)
		integral.MemberId = GetMemberUserId(utils.MobileDecrypt(c.orderMain.EnMemberTel), c.orderMain.MemberId, c.orderMain.OrderType)
		integral.ShopCode = c.orderMain.ShopId
		integral.Orderfrom = c.orderMain.ChannelId
		var totalAmount int32
		if integral.Orderfrom == ChannelAwenId || integral.Orderfrom == ChannelDigitalHealth {
			integral.Integraltype = 71
			c.session.SQL("select IFNULL(sum(op.pay_price*(op.number-op.refund_num)),0) from order_product op where op.order_sn=?", c.orderMain.OrderSn).Get(&totalAmount) //商品实收,去掉中途退掉的

			//查询并计算优惠信息
			PromotionFee := c.FreightCal()
			//使用实付运费
			totalAmount += c.orderMain.Freight - PromotionFee
			integral.Notifytime = kit.GetTimeNow(c.orderMain.ConfirmTime)
		} else if integral.Orderfrom == ChannelMallId {
			integral.Notifytime = kit.GetTimeNow()
			integral.MemberId = ""
			integral.Integraltype = 41
			//实际支付金额
			totalAmount = c.orderMain.Total
		}
		integral.TotalAmount = totalAmount
		glog.Info("订单添加积分：", kit.JsonEncode(integral))
	} else {
		// 退款减积分
		var refundOrder models.RefundOrder
		result, err := c.session.Where("refund_sn = ?", orderId).Get(&refundOrder)
		if err != nil || !result || (refundOrder.ChannelId != ChannelAwenId && refundOrder.ChannelId != ChannelMallId && refundOrder.ChannelId != ChannelDigitalHealth) {
			return
		}

		c.orderMain = GetOrderMainByOrderSn(refundOrder.OrderSn)
		//虚拟订单不做积分操作
		if c.orderMain.IsVirtual == 1 {
			return
		}
		refundAmount := int32(kit.YuanToFen(cast.ToFloat64(refundOrder.RefundAmount)))

		integral.Oldordersn = c.orderMain.OldOrderSn
		integral.Ordersn = c.orderMain.OldOrderSn

		integral.Notifytime = kit.GetTimeNow(refundOrder.CreateTime)
		integral.MemberId = GetMemberUserId(utils.MobileDecrypt(c.orderMain.EnMemberTel), c.orderMain.MemberId, c.orderMain.OrderType)
		integral.Mobile = utils.MobileDecrypt(c.orderMain.EnMemberTel)
		integral.ShopCode = c.orderMain.ShopId
		integral.Orderfrom = c.orderMain.ChannelId
		integral.Refundsn = refundOrder.RefundSn
		if c.orderMain.ChannelId == ChannelAwenId || integral.Orderfrom == ChannelDigitalHealth {
			// 查询父订单下的所有子订单
			integral.Integraltype = 72
			integral.RefundAmount = refundAmount
		} else if c.orderMain.ChannelId == ChannelMallId {
			integral.Integraltype = 42
			integral.MemberId = ""
			if c.orderMain.OrderStatusChild == 20201 && c.orderMain.Source == 1 {
				refundAmount -= c.orderMain.Freight
			}
			integral.RefundAmount = refundAmount
		}
		glog.Info("订单退款减积分：", kit.JsonEncode(integral))
	}
	integral.OrgId = c.orderMain.OrgId
	PublishIntegralChange(integral)
}

func GetMemberUserId(tel, memberId string, orderType int32) string {
	//if orderType == 8 {
	db := GetDBConn()
	var userId string
	_, err := db.SQL("SELECT user_id userId FROM scrm_organization_db.t_scrm_user_info WHERE user_mobile =?", tel).Get(&userId)
	if err != nil {
		glog.Error("getMemberUserId,查询数据错误，手机号码：" + tel)
	}
	if len(userId) > 0 {
		return userId
	}
	return memberId
	//}
	//return memberId
}

// 推送美配订单
func PushMpOrder(session *xorm.Session, orderMain *models.OrderMain) error {
	c := CommonService{
		session:   session,
		orderMain: orderMain,
	}

	return c.PushMpOrder()
}

// 释放库存
func FreedStock(orderSn string) error {
	c := &CommonService{
		session:   GetDBConn().NewSession(),
		orderMain: GetOrderMainByOrderSn(orderSn),
	}
	defer c.session.Close()

	return c.FreedStock(nil)
}

// 释放当日库存限制
func FreedDailyStock(orderSn string) {
	c := &CommonService{
		session:   GetDBConn().NewSession(),
		orderMain: GetOrderMainByOrderSn(orderSn),
	}
	defer c.session.Close()

	c.FreedDailyStock()
}

// 流程日志落地
func SaveOrderLog(orderLog []*models.OrderLog) {
	if len(orderLog) == 0 {
		return
	}

	c := &CommonService{
		session: GetDBConn().NewSession(),
	}
	defer c.session.Close()

	c.SaveOrderLog(orderLog)
}

// 获取核销码集合
func GetValidOrderVerifyCodes(orderSn string, status ...int32) (orderVerifyCodes []models.OrderVerifyCode) {
	//没传状态查全部
	if len(status) == 0 {
		status = append(status, 0)
	}

	//查询退款数量小于购买数量的
	orderVerifyCodes = make([]models.OrderVerifyCode, 0)
	session := GetDBConn().Table("order_verify_code").Where("order_sn = ?", orderSn)
	now := kit.GetTimeNow()
	switch status[0] {
	case 1:
		//未核销
		session.And("verify_status = 0 and verify_code_expiry_date > ?", now)
	case 2:
		//已核销
		session.And("verify_status = 1")
	case 3:
		//已退款
		session.And("verify_status = 2")
	case 4:
		//已核销和已过期
		session.And("verify_status = 1 or (verify_status = 0 and verify_code_expiry_date < ?)", now)
	case 5:
		//已失效
		session.And("verify_status in (1,2) or (verify_status = 0 and verify_code_expiry_date < ?)", now)
	case 6:
		//未核销不管有没有过期
		session.And("verify_status = 0")
	}
	err := session.Find(&orderVerifyCodes)
	if err != nil {
		glog.Error(orderSn, "，获取核销码集合出错，", err.Error())
	}

	return orderVerifyCodes
}

//	根据父订单号与组合商品skuId查询子订单中的核销码
//
// 应用场景：第三方的订单的组合商品中的虚拟核销码的查询 因为根据组合进行了拆单 所以必须加上组合商品skuId进行过滤 否则会查出所有
func GetVerifyCodesByGroupSkuId(orderSn string, groupSkuId string, status ...int32) (orderVerifyCodes []models.OrderVerifyCode) {
	//没传状态查全部
	glog.Info("GetVerifyCodesByGroupSkuId", orderSn, groupSkuId, status)
	if len(status) == 0 {
		status = append(status, 0)
	}

	//查询退款数量小于购买数量的
	orderVerifyCodes = make([]models.OrderVerifyCode, 0)
	session := GetDBConn().Table("order_main").Alias("a").Select("b.*").
		Join("inner", "order_verify_code b", "a.order_sn=b.order_sn").
		Where("a.parent_order_sn = ?", orderSn)
	now := kit.GetTimeNow()
	if groupSkuId != "" {
		session.And("b.parent_sku_id = ?", groupSkuId)
	}
	switch status[0] {
	case 1:
		//未核销
		session.And("b.verify_status = 0 AND b.verify_code_expiry_date > ?", now)
	case 2:
		//已核销
		session.And("b.verify_status = 1")
	case 3:
		//已退款
		session.And("b.verify_status = 2")
	case 4:
		//已核销和已过期
		session.And("b.verify_status = 1 OR (verify_status = 0 AND verify_code_expiry_date < ?)", now)
	case 5:
		//已失效
		session.And("b.verify_status in (1,2) OR (verify_status = 0 AND verify_code_expiry_date < ?)", now)
	case 6:
		//未核销不管有没有过期
		session.And("b.verify_status = 0")
	}
	err := session.Find(&orderVerifyCodes)
	if err != nil {
		glog.Error(orderSn, "，根据父订单获取核销码集合出错，", err.Error())
	}

	return orderVerifyCodes
}

// 某个虚拟订单的所有核销码核销状态
// @return 1:全部核销 2：全部退款 3：部分核销 4：全部未核销  5：部分退款 0:未查询到信息
func GetVerifyCodesStatus(orderSn []string) map[string]int32 {
	var records []models.OrderVerifyCode
	err := GetDBConn().Table("order_verify_code").Select("order_sn,verify_status").In("order_sn", orderSn).Find(&records)
	if err != nil {
		glog.Error("根据子订单号查询核销码状态失败", err)
		return nil
	}
	codeLen := len(records)
	if codeLen == 0 {
		return nil
	}
	type count struct {
		hasRefunded int32
		verified    int32
		notVerified int32
		total       int32
	}
	orderSnResMap := make(map[string]*count)
	res := make(map[string]int32)
	for _, v := range records {
		if _, ok := orderSnResMap[v.OrderSn]; !ok {
			orderSnResMap[v.OrderSn] = new(count)
		}
		if v.VerifyStatus == 2 {
			orderSnResMap[v.OrderSn].hasRefunded += 1
		}
		if v.VerifyStatus == 1 {
			orderSnResMap[v.OrderSn].verified += 1
		}
		if v.VerifyStatus == 0 {
			orderSnResMap[v.OrderSn].notVerified += 1
		}
		orderSnResMap[v.OrderSn].total += 1
	}

	for childOrderSn, cnt := range orderSnResMap {
		//全部退款
		if cnt.hasRefunded == cnt.total {
			res[childOrderSn] = 2
			continue
		}
		//全部核销
		if cnt.verified == cnt.total {
			res[childOrderSn] = 1
			continue
		}
		//全部未核销
		if cnt.notVerified == cnt.total {
			res[childOrderSn] = 4
			continue
		}
		//部分退款
		if cnt.verified > 0 {
			res[childOrderSn] = 3
			continue
		}
		res[childOrderSn] = 5
	}

	return res
}

// 保障卡会员卡即买即生效逻辑
func CardEffectiveUponPurchase(session *xorm.Session, orderMain *models.OrderMain, productSkuId string) error {
	mealListStr := config.GetString("SetMeal.Key")
	// 检查当前商品是否是会员卡商品
	if isCard, _ := regexp.MatchString("(^|,)"+productSkuId+":", mealListStr); !isCard {
		return nil
	}
	memberCardRelation := &models.MemberCardRelation{
		Userid:          orderMain.MemberId,
		Createsource:    5,                    //订单渠道
		Mallorderid:     orderMain.OldOrderSn, //订单号
		Cardtype:        0,
		Cardlevel:       0,
		Vipstatus:       1,
		Expirystartdate: orderMain.PayTime, //支付时间
		Expiryenddate:   orderMain.PayTime.AddDate(1, 0, -1),
		Presellname:     "", //商品名称
		Presellcount:    1,
		Presellunit:     "", //商品单位
		Createdate:      time.Now(),
	}
	for _, v := range strings.Split(mealListStr, ",") {
		var kv = strings.Split(v, ":")
		var key = kv[0]
		if key == productSkuId {
			memberCardRelation.Cardlevel = cast.ToInt(kv[3])
			memberCardRelation.Cardtype = cast.ToInt(kv[2])
			break
		}
	}
	oldMemberCard := new(models.MemberCardRelation)
	isOk, err := session.Table("datacenter.member_card_relation").Where("cardtype=? and userid = ? ", memberCardRelation.Cardtype, orderMain.MemberId).OrderBy("expiryenddate desc").Get(oldMemberCard)
	if err != nil {
		glog.Error(orderMain.OrderSn, ", 保障卡关联关系查询保障卡信息错误, ", err.Error())
		return err
	}
	if isOk && oldMemberCard.Id > 0 {
		return nil
		memberCardRelation.Expirystartdate = oldMemberCard.Expiryenddate.AddDate(0, 0, 1)
		memberCardRelation.Expiryenddate = memberCardRelation.Expirystartdate.AddDate(1, 0, -1)
	}

	glog.Info(orderMain.OrderSn, ", 保障卡关联关系插入数据库, ", kit.JsonEncode(memberCardRelation))
	_, err = session.Table("datacenter.member_card_relation").Insert(memberCardRelation)
	if err != nil {
		glog.Error(orderMain.OrderSn, ", 保障卡关联关系插入数据库错误, ", err.Error())
		return err
	}
	return nil
}

// 处理冻结库存到0的时候通知商品中心，不要显示库存为0的商品
// ",stock:skuid,stock:skuid,stock:skuid" 或者 ""
func dealProductStockNoticeParam(warehouseId int32, skuIdStr string, stock int32) {
	param := pc.StockNoticeRequest{}
	stockStore := &pc.StockStore{
		FinanceCode: cast.ToString(warehouseId),
	}
	if len(skuIdStr) == 0 {
		return
	} else {
		stockSkuIdStrArray := strings.Split(skuIdStr, ",")
		for _, v := range stockSkuIdStrArray {
			if v == "" {
				continue
			}
			stockSkuIdArray := strings.Split(v, ":")
			if len(stockSkuIdArray) == 1 {
				continue
			}
			skuId, _ := strconv.Atoi(stockSkuIdArray[1])
			if skuId == 0 {
				return
			}
			storeSku := &pc.StoreSku{
				SkuId: int32(skuId),
				Stock: stock,
			}
			stockStore.Skus = append(stockStore.Skus, storeSku)
		}
	}
	param.StockInfo = append(param.StockInfo, stockStore)
	//组合商品查询库存二次通知
	groupProductStockNotice(param)
}

// 通知组合商品的库存信息
func groupProductStockNotice(param pc.StockNoticeRequest) {
	//todo 商品无需修改
	pcClient := pc.GetDcProductClient()
	defer pcClient.Close()

	pcClient.RPC.GroupProductStockNotice(pcClient.Ctx, &param)
}

// 推送订单状态到腾讯有数
func PushOrderStatusToTencent(orderSn string, cancelType int) {
	order := GetOrderByOrderSn(orderSn, "order_main.old_order_sn,order_main.channel_id,order_main.order_status_child,order_main.is_push_tencent")
	glog.Info("PushOrderStatusToTencent:"+orderSn, kit.JsonEncode(order))
	var orderStatus = ""
	if order.IsPushTencent == 1 {
		if order.ChannelId == ChannelAwenId || order.ChannelId == ChannelDigitalHealth {
			switch order.OrderStatusChild {
			case 20102:
				orderStatus = "1150"
				break
			case 20103:
				orderStatus = "1160"
				break
			case 20106:
				orderStatus = "1180"
				break
			case 20107:
				//1140已支付未发货取消
				if cancelType == 1 {
					orderStatus = "1140"
				} else {
					//1130未支付取消
					orderStatus = "1130"
				}
				break
			default:
				orderStatus = ""
			}
		} else if order.ChannelId == ChannelMallId {
			switch order.OrderStatusChild {
			case 20101:
				orderStatus = "1150"
				break
			case 20201:
				orderStatus = "1150"
				break
			case 20202:
				orderStatus = "1160"
				break
			case 20203:
				orderStatus = "1180"
				break
			case 20107:
				//1140已支付未发货取消
				if cancelType == 1 {
					orderStatus = "1140"
				} else {
					//1130未支付取消
					orderStatus = "1130"
				}
				break
			case 20205:
				//1140已支付未发货取消
				if cancelType == 1 {
					orderStatus = "1140"
				} else {
					//1130未支付取消
					orderStatus = "1130"
				}
				break
			default:
				orderStatus = ""
			}

			glog.Info("PushOrderStatusToTencent:1:" + orderSn + ":" + orderStatus)
		}
		glog.Info("PushOrderStatusToTencent:"+orderSn+":xxx0001", orderStatus)
		if orderStatus != "" {
			if order.ChannelId == ChannelMallId {
				orderSn = order.OldOrderSn
			}
			extClient := ext.GetExternalTencentClient()
			defer extClient.Close()
			orderStatusInfo := ext.OrderStatusInfo{
				ExternalOrderId:  orderSn,
				OrderStatus:      orderStatus,
				StatusChangeTime: cast.ToString(time.Now().UnixNano() / 1e6),
			}
			pushOrderStatus := ext.PushOrderStatusReq{}
			pushOrderStatus.Orders = append(pushOrderStatus.Orders, &orderStatusInfo)
			_, err := extClient.RPC.PushOrderStatus(context.Background(), &pushOrderStatus)
			if err != nil {
				glog.Error("PushOrderStatusToTencent", kit.JsonEncode(pushOrderStatus), err)
			}
		} else {
			glog.Info("PushOrderStatusToTencent:"+orderSn, "orderStatus is null")
		}
	}
}

// 取消配送
func CancelDeliveryCommon(orderSn, cancelReason string, deliveryId int64, addException int, args ...interface{}) error {
	db := GetDBConn()

	//配送记录
	var record models.OrderDeliveryRecord

	ok, err := db.Where("delivery_id = ?", deliveryId).Get(&record)
	if err != nil {
		glog.Error("更新配送信息接口，配送单不存在：", orderSn)
		return errors.New("配送单查询错误")
	}
	if !ok {
		glog.Error("更新配送信息接口，配送单不存在，", orderSn)
		return errors.New("配送单不存在")
	}

	appChannel := 0
	db.Table("dc_order.order_main").Select("app_channel").Where("order_sn=? or old_order_sn=?", record.OrderSn, record.OrderSn).Get(&appChannel)
	//通知美团配送取消配送
	etClient := et.GetExternalClient()
	defer etClient.Close()

	//5001为闪送
	//if record.DeliveryServiceCode != 5001 {

	//是否成功取消
	IsCancel := false

	switch record.DeliveryType {
	case 0:
		CancelReasonId := "399"
		if cancelReason == "阿闻超时未接单自动取消" {
			CancelReasonId = "202"
		}

		inpar := et.MpOrderDeleteRequest{
			DeliveryId:          record.DeliveryId,
			MtPeisongId:         record.MtPeisongId,
			CancelReasonId:      CancelReasonId,
			CancelReason:        cancelReason,
			DeliveryServiceCode: cast.ToInt32(record.DeliveryServiceCode),
			AppChannel:          cast.ToString(appChannel),
			OrderSn:             orderSn,
		}
		glog.Info("取消美配,美团配送请求：", "配送订单号:"+record.OrderSn, kit.JsonEncode(inpar))
		res, err := etClient.MPServer.MpOrderDelete(etClient.Ctx, &inpar)
		if err != nil {
			glog.Error("取消美配调用错误", record.OrderSn, " ", err)
			return errors.New("取消美配调用错误")
		} else if res.Code != 200 {
			glog.Error("取消美配调用错误 ", record.OrderSn, " ", kit.JsonEncode(res))
			return errors.New(res.Message)
		} else {
			glog.Info("取消美配,美团配送返回：", record.OrderSn, " ", kit.JsonEncode(res))
			IsCancel = true
			//deliveryModel := models.OrderDeliveryNode{
			//	DeliveryId:     record.DeliveryId,
			//	OrderSn:        record.OrderSn,
			//	DeliveryStatus: 98,
			//	Content:        "商家取消",
			//	CourierName:    "",
			//	CourierPhone:   "",
			//	CancelReason:   cancelReason,
			//	CreateTime:     time.Now(),
			//}
			//_, err := db.Insert(&deliveryModel)
			//if err != nil {
			//	glog.Error(record.OrderSn, ", 取消配送，信息保存失败, ", err)
			//}
		}
		break
	case 3:
		par := et.DaDaformalCancelRequst{}

		cancelReasonId := int32(10000)
		if len(args) > 0 {
			cancelReasonId = cast.ToInt32(args[0])
		}
		par.OrderId = record.OrderSn
		par.CancelReasonId = cancelReasonId
		par.CancelReason = cancelReason
		glog.Info("取消达达,达达配送请求：", "配送订单号:"+record.OrderSn, kit.JsonEncode(par))
		res, err := etClient.DaDa.FormalCancel(etClient.Ctx, &par)
		if err != nil {
			glog.Error("取消达达调用错误", record.OrderSn, " ", err)
			return errors.New("取消达达调用错误")
		} else if res.Code != 0 {
			glog.Error("取消达达调用错误 ", record.OrderSn, " ", kit.JsonEncode(res))
			return errors.New(res.Message)
		} else {
			glog.Info("取消达达,达达配送返回：", record.OrderSn, " ", kit.JsonEncode(res))
			IsCancel = true

		}
		break
	case 4: //蜂鸟取消

		cancelReasonId := int32(0)
		if len(args) > 0 {
			cancelReasonId = cast.ToInt32(args[0])
		}
		params := et.FnCancelOrderRequst{}
		params.OrderCancelRole = 1
		params.OrderCancelOtherReason = cancelReason
		params.PartnerOrderCode = record.OrderSn
		params.OrderCancelCode = cancelReasonId
		glog.Info("取消蜂鸟,配送请求：", "配送订单号:"+record.OrderSn, kit.JsonEncode(params))
		res, err := etClient.Fn.CancelOrder(etClient.Ctx, &params)
		if err != nil {
			glog.Error("取消蜂鸟调用错误", record.OrderSn, " ", err)
			return errors.New("取消蜂鸟调用错误")
		} else if res.Code != "200" {
			glog.Error("取消蜂鸟调用错误 ", record.OrderSn, " ", kit.JsonEncode(res))
			return errors.New(res.Msg)
		} else {
			glog.Info("取消蜂鸟,蜂鸟配送返回：", record.OrderSn, " ", kit.JsonEncode(res))
			IsCancel = true
		}
		break
	case 5: //麦芽田

		orderMain := models.OrderMain{}
		db.Table("dc_order.order_main").Where("order_sn=? ", record.OrderSn).Get(&orderMain)
		//调用取消订单
		deliveryConfig := new(models.DeliveryConfig)
		//如果是SAAS并且是小程序订单的话，就要判断是不是麦芽田订单，是的话就要调用完成接口
		if orderMain.ChannelId == 1 && orderMain.OrgId == 6 {
			_, err = db.Where("finance_code = ?", orderMain.ShopId).
				Where("channel_id = ?", orderMain.ChannelId).
				Where("org_id = ?", orderMain.OrgId).
				Get(deliveryConfig)

			if err != nil {
				glog.Error("查询配送配置出错", record.OrderSn, err.Error())
			}

			if deliveryConfig.ID == 0 {
			}

			//如果是平台配送或者是第三方配送，我们不需要发配送直接返回成功
			if deliveryConfig.ThirdType == 1 && deliveryConfig.DeliveryMethod == 2 {
				etClient := et.GetExternalClient()
				//调用麦芽田订单完成
				par := et.MytOrderConfirmRequest{}
				par.OrderId = record.OrderSn
				par.ShopId = deliveryConfig.StoreID
				par.UpdateTime = time.Now().Unix()
				//下单成功后调用确认订单
				res, err := etClient.Myt.CanceledOrder(etClient.Ctx, &par)
				if err != nil {
					glog.Error("麦芽田取消订单错误！ ", record.OrderSn, err.Error())
					return errors.New("麦芽田取消订单错误" + err.Error())
				}
				if res.Code != 200 {
					glog.Error("麦芽田取消订单接口错误！ ", record.OrderSn, res.Message)
					return errors.New("麦芽田取消订单错误" + res.Message + res.Error)
				}

			}
			IsCancel = true
		}

	}

	if IsCancel {
		deliveryModel := models.OrderDeliveryNode{
			DeliveryId:     record.DeliveryId,
			OrderSn:        record.OrderSn,
			DeliveryStatus: 98,
			Content:        "商家取消",
			CourierName:    "",
			CourierPhone:   "",
			CancelReason:   cancelReason,
			CreateTime:     time.Now(),
		}
		_, err := db.Insert(&deliveryModel)
		if err != nil {
			glog.Error(record.OrderSn, ", 取消配送，信息保存失败, ", err)
		}
	}

	//else {
	//	glog.Info("取消闪送,通知闪送请求：", record.OrderSn, " ", record.MtPeisongId)
	//	err = ShanSongCancel(record.MtPeisongId, record.OrderSn)
	//	if err != nil {
	//		glog.Error("取消闪送调用错误", record.OrderSn, " ", err)
	//		return err
	//	} else {
	//		glog.Info("取消闪送,通知闪送请求返回：", record.OrderSn, " ", record.MtPeisongId)
	//		deliveryModel := models.OrderDeliveryNode{
	//			DeliveryId:     record.DeliveryId,
	//			OrderSn:        record.OrderSn,
	//			DeliveryStatus: 98,
	//			Content:        "商家取消",
	//			CourierName:    "",
	//			CourierPhone:   "",
	//			CancelReason:   "商家取消",
	//			CreateTime:     time.Now(),
	//		}
	//		_, err = db.Insert(&deliveryModel)
	//		if err != nil {
	//			glog.Error(record.OrderSn, ", 取消配送，信息保存失败, ", err)
	//		}
	//	}
	//}
	if addException == 1 {
		OrderException := OrderExceptionService{}
		ExceptionRequest := oc.OrderExceptionRequest{
			DeliveryId:     fmt.Sprintf("%d", deliveryId),
			MtPeisongId:    record.MtPeisongId,
			OrderId:        record.OrderSn,
			ExceptionDescr: cancelReason,
			ExceptionTime:  kit.GetTimeNow(),
			Source:         1,
			OrderStatus:    2,
		}
		res, err := OrderException.OrderExceptionAdd(nil, &ExceptionRequest)
		if err != nil || res.Code != 200 {
			glog.Error("异常单保存失败！", record.OrderSn)
		}
	}
	return nil
}

func ShanSongCancel(orderNumber string, orderSn string) error {
	etClient := et.GetExternalClient()
	defer etClient.Close()

	iss := et.IssOrderNoRequest{
		IssOrderNo: orderNumber,
	}

	res, err := etClient.ShanSong.AbortOrder(etClient.Ctx, &iss)
	if err != nil {
		glog.Error("推送闪送取消配送单错误！ ", orderSn, err.Error())
		return errors.New("推送闪送取消配送单失败！")
	}
	if res.Code != 200 {
		return errors.New("Msg:" + res.Msg + ",error:" + res.Error)
	}
	return err
}

func PushSubscribeMessage(param mc.SubscribeMessageRequest) error {
	client := mc.GetMessageCenterClient()
	defer client.Close()
	res, err := client.RPC.SubscribeMessage(client.Ctx, &param)
	glog.Info(fmt.Sprintf("推送订阅消息返回结果：返回内容:%s,接口参数:%s", kit.JsonEncode(res), kit.JsonEncode(param)))
	if err != nil {
		return errors.New("推送订阅消息失败！" + err.Error())
	}
	if res.Code != 200 {
		return errors.New(res.Message)
	}
	return nil
}

// CheckMallStock 检测秒杀活动的库存是否满足
// 1:检测活动商品的虚拟库存是否满足，如果虚拟库存满足 继续判断实际库存是否满足，都满足的情况 执行虚拟库存的减扣后返回
// 2: 秒杀活动当前仅支持电商，所以商品的库存查询仅有商城商品查询的逻辑 如果到家需要做秒杀 则这部分逻辑需要扩展
// benchmark 有虚拟库存情况下23ms/op   无虚拟内存或者虚拟库存为9ms/op
// @version v2.9.10
func CheckMallStock(sku string, number int32) (bool, error, string) {
	//电商仓
	warehouseId := "DSC001"
	redisConn := GetRedisConn()
	keys := []string{"stock:" + sku}

	scriptStr := `
	local number = tonumber(ARGV[1])

	--判断仓库库存
	local warehouseId = ARGV[2]
	local warehouse_stock_key = KEYS[1]
	local warehouse_stock_redis_value = tonumber(redis.call('HGET', warehouse_stock_key, warehouseId))
	if warehouse_stock_redis_value == nil then
		return 2
	elseif (warehouse_stock_redis_value < number) then
		return 2
	end
	return 1
	`
	result := redisConn.Eval(scriptStr, keys, number, warehouseId)
	resultVal := result.Val()

	if resultVal == nil {
		glog.Error("freedSecKillStock-秒杀下单查询库存失败，lua脚本有报错，错误信息："+result.Err().Error(), keys)
		return false, result.Err(), "秒杀下单查询库存失败，lua脚本有报错，错误信息：" + result.Err().Error()
	}
	res, ok := resultVal.(int64)
	if !ok {
		glog.Error("freedSecKillStock-秒杀下单判断结果处理失败", keys)
		return false, errors.New("秒杀下单库存查询失败"), "判断结果处理失败"
	}
	if res == 2 {
		return false, nil, "商品" + sku + "库存不足"
	}
	return true, nil, ""
}

// FreedSecKillStock 释放秒杀库存 将扣除掉的秒杀库存扣除掉
// benchmark 平均在17ms/op
// @version v2.9.10
func FreedSecKillStock(sku string, secKillId int32, number int32) (err error) {
	strSecKillId := cast.ToString(secKillId)
	redisConn := GetRedisConn()
	keys := []string{"stock:seckill:" + strSecKillId + ":" + sku}
	scriptStr := `
	local number = tonumber(ARGV[1])
	local sec_kill_stock_key = KEYS[1]

	-- 判断虚拟库存是否存在 不存在返回 存在则增加虚拟库存
	local sec_kill_stock_value = tonumber(redis.call('GET',sec_kill_stock_key))
	if sec_kill_stock_value == nil then 
		return 2
	end

	-- 扣减虚拟库存
	redis.call('INCRBY',sec_kill_stock_key,number)
	return 1
	`
	result := redisConn.Eval(scriptStr, keys, number)
	resultVal := result.Val()

	if resultVal == nil {
		glog.Error("freedSecKillStock-释放库存失败，lua脚本有报错，错误内容："+result.Err().Error(), keys)
		return errors.New("freedSecKillStock-释放库存失败，lua脚本有报错，错误内容：" + result.Err().Error())
	}

	res, ok := resultVal.(int64)
	if !ok {
		return errors.New("freedSecKillStock-释放库存失败")
	}
	if res == 2 {
		glog.Info("freedSecKillStock-释放库存时未获取到缓存信息", keys)
		return errors.New("freedSecKillStock-释放库存时未获取到缓存信息")
	}
	return nil
}

// FreedSecKillStockByOrderSn
// 秒杀订单逆向流程释放秒杀库存 通过订单号释放秒杀活动库存 取消订单 退款通过时调用
// benchmark 平均在17ms/op
// @version v2.9.10
func FreedSecKillStockByOrderSn(orderSn string) {
	//todo 让前端写入promotion数据 当前前端下单没有写入promotion数据
	/*promotionInfo := GetOrderPromotionByOrderSn(orderSn, 11, "promotion_id")
	if len(promotionInfo) == 0 {
		glog.Error(orderSn, "订单逆向流程-释放虚拟库存未获取到活动信息")
		return
	}*/
	productInfo := GetOrderProductByOrderSn(orderSn, "sku_id,number,promotion_id")
	if len(productInfo) == 0 {
		glog.Error(orderSn, "订单逆向流程-释放虚拟库存未获取到商品信息")
		return
	}
	//获取订单商品信息
	err := FreedSecKillStock(productInfo[0].SkuId, int32(productInfo[0].PromotionId), productInfo[0].Number)
	if err != nil {
		glog.Error(orderSn, "订单逆向流程-释放虚拟库存失败", err)
	}
}

// 在线问诊订单推送逻辑处理
func DiagnosePush(orderSn string, diagnoseProject int32) {
	glog.Info("免费义诊订单推送到医生：", orderSn)
	dgcClient := dgc.GetDiagnoseServiceClient()
	if diagnoseProject == 1 {
		sendAcceptsMessageRequest := new(dgc.SendAcceptsMessageRequest)
		sendAcceptsMessageRequest.Scenario = 2
		ext := `{"order_sn":"%s"}`
		sendAcceptsMessageRequest.Ext = fmt.Sprintf(ext, orderSn)
		dgcClient.Im.SendAcceptsMessage(context.Background(), sendAcceptsMessageRequest)

		//调环信 发宠物信息、问诊信息 （蔡辉）
		sendAcceptsMessageRequest.Scenario = 1
		dgcClient.Im.SendAcceptsMessage(context.Background(), sendAcceptsMessageRequest)
	}
}

// QueryChildProducts 查询组合商品的子商品 ，
// 主要应用于第三方订单中的组合商品
// 第三方没有组合商品明细 所以没有分摊到组合商品的明细商品上 所以此处只需要对组合商品进行均摊就好
// step1:查询出组合商品的子商品信息
// step2:进行均摊
// WarehouseType 4,5前置仓 3 门店仓
// parent 组合商品父商品信息
func QueryChildProducts(parent *oc.OrderProductModel, channelId int, WarehouseType int32, financeCode string) (children []*oc.OrderProductModel) {
	//todo 商品无需修改
	clientPc := pc.GetDcProductClient()
	defer clientPc.Close()
	erp := int32(4) //2:A8 4:子龙

	//查询子商品的第三方skuId时需要使用erp参数  因为相同的skuId在不同的货物渠道拥有不同的第三方skuId
	if WarehouseType == 4 || WarehouseType == 5 {
		erp = 2
	}
	var childRes *pc.ChildProductsResponse
	requestParam := &pc.QueryChildProductsRequest{
		ParentSkuId:     parent.Sku,
		ChannelId:       int32(channelId),
		Erp:             erp,
		FinanceCode:     financeCode,
		ParentProductId: cast.ToInt32(parent.ProductId),
	}
	childRes, err := clientPc.RPC.QueryChildProducts(context.Background(), requestParam)
	if err != nil {
		glog.Error(parent.OrderId, parent.OrderSn, parent.Sku, "查询组合商品子商品失败 ", err.Error())
	}
	if childRes == nil {
		glog.Error(parent.OrderId, parent.OrderSn, parent.Sku, "未查询到子商品")
		return
	}
	if childRes.Code == 400 {
		return
	}
	children = ChildProductJunTan(parent, childRes.Products)
	return
}

// ChildProductJunTan
// 第三方订单组合商品均摊子商品
// orderProducts:商品列表
// @param parent 父商品
// @param childProducts 子商品
// @version v6.0
// @return []*oc.OrderProductModel 已经均摊好的子商品信息
func ChildProductJunTan(parent *oc.OrderProductModel, childProducts []*pc.ChildProduct) (children []*oc.OrderProductModel) {
	//均摊组合商品实付金额
	var (
		combinePrivilege int32 //此组合商品总优惠金额=组合商品原总金额-组合商品实付金额
		ptPrivilege      int32
		sumTotal         int32 //子商品总价
	)
	//构建均摊商品实体，且统计原总金额
	for _, child := range childProducts {
		//组合商品
		combineChildProduct := TransChildProductToProtoData(child, parent)
		//2:创建均摊好的子商品 按折扣价格搭着
		if child.DiscountType == 1 { //按折扣优惠
			var discountPrice = decimal.NewFromInt(int64(combineChildProduct.Price)).
				Mul(decimal.NewFromInt(int64(child.DiscountValue))).
				DivRound(decimal.NewFromInt(100), 0).IntPart()
			combineChildProduct.Price = int32(discountPrice) //折扣价 对应DiscountPrice
			combineChildProduct.PayPrice = combineChildProduct.Price
		} else if child.DiscountType == 2 { //按固定价格优惠
			combineChildProduct.Price = child.DiscountValue //组合价 对应DiscountPrice
			combineChildProduct.PayPrice = combineChildProduct.Price
		}
		sumTotal += combineChildProduct.PayPrice * combineChildProduct.Number
		children = append(children, combineChildProduct)
	}
	//Privilege 代表代表商家的各种优惠活动
	//第三方订单的优惠活动来自于商家在第三方后台的设置
	combinePrivilege = parent.Privilege
	ptPrivilege = parent.PrivilegePt
	totalPayment := parent.PaymentTotal
	//均摊,补充实付价格
	//均摊率 = （单个商品数量*单个商品价格 / 支付总价 ）
	var sumItemAmount int32 //统计已减金额
	var sumPtAmount int32
	var sumPrivilege int32
	for index, child := range children {
		if len(children)-1 == index {
			//最后一个需要倒减
			//todo 如果出现0 的情况需要递归处理
			child.PaymentTotal = totalPayment - sumItemAmount
			if child.PaymentTotal < 0 {
				child.PaymentTotal = 0
			}
			child.Privilege = combinePrivilege - sumPrivilege
			if child.Privilege < 0 {
				child.Privilege = 0
			}
			child.PrivilegePt = ptPrivilege - sumPtAmount
			if child.PrivilegePt == 0 {
				child.PrivilegePt = 0
			}
			child.PrivilegeTotal = child.Privilege + child.PrivilegePt
		} else {
			//商品项优惠 = 总优惠*优惠占比（商品项原金额原金额/总优惠）
			//商品项实付金额 =商品项原金额（原价*数量）-商品项优惠
			ratio := math.Ceil(cast.ToFloat64(child.PayPrice*child.Number)) / cast.ToFloat64(sumTotal)
			child.Privilege = cast.ToInt32(ratio * cast.ToFloat64(combinePrivilege))
			child.PrivilegePt = cast.ToInt32(ratio * cast.ToFloat64(ptPrivilege))
			child.PrivilegeTotal = child.Privilege + child.PrivilegePt
			child.PaymentTotal = cast.ToInt32(ratio * cast.ToFloat64(totalPayment))

			sumItemAmount += child.PaymentTotal
			sumPtAmount += child.PrivilegePt
			sumPrivilege += child.Privilege
		}
		child.PayPrice = cast.ToInt32(cast.ToFloat64(child.PaymentTotal) / cast.ToFloat64(child.Number)) //实付单价
		//sku支付价格 = 实际支付价格+ 平台优惠
		child.SkuPayTotal = child.PaymentTotal + child.PrivilegePt
	}
	return
}

// TransToProtoData
// 将组合商品子商品信息转换成oc.OrderProductModel数据结构
func TransChildProductToProtoData(o *pc.ChildProduct, parent *oc.OrderProductModel) *oc.OrderProductModel {
	var product = new(oc.OrderProductModel)

	product.Sku = o.SkuId
	product.ProductId = o.ProductId
	product.ProductName = o.ProductName
	product.ProductType = o.ProductType
	product.ParentSkuId = o.ParentSkuId
	product.ArticleNumber = o.ThirdSkuId
	product.CombineType = parent.CombineType
	product.Specs = o.Specs
	product.Image = o.Image
	product.BarCode = o.BarCode
	product.Number = parent.Number * o.Number
	product.GroupItemNum = o.Number

	product.Price = o.MarketPrice
	product.PayPrice = o.MarketPrice
	product.MarkingPrice = o.MarketPrice

	product.TermType = o.TermType
	product.TermValue = o.TermValue
	product.VirtualInvalidRefund = o.VirtualInvalidRefund

	return product
}

// 获取退款金额
func GetRefundAmount(orderSn string) float64 {
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()

	var RefundOrder models.RefundOrder
	refundAmount, _ := dbConn.Table("refund_order").Where("order_sn=? and refund_state=3", orderSn).Sum(RefundOrder, "refund_amount")

	return refundAmount
}

// 显示该父订单里面的商品所对应的总退款金额（如果该商品有多次退款，显示多次退款的总金额）
func GetSkuRefundAmount(orderSn string) map[string]string {
	dbConn := NewSlaveDbConn()
	dbConn.ShowSQL(true)
	defer dbConn.Close()

	result := map[string]string{}
	refundOrder := []*models.RefundOrderProduct{}

	err := dbConn.Table("order_main").Alias("o").
		Select("sum(p.refund_amount) as refund_amount, p.sku_id").
		Join("left", "refund_order as r", "o.order_sn=r.order_sn").
		Join("left", "refund_order_product as p", "r.refund_sn=p.refund_sn").
		Where("r.refund_state=3 and (o.order_sn=? or o.parent_order_sn=?)", orderSn, orderSn).
		GroupBy("p.sku_id").Find(&refundOrder)

	if err != nil {
		glog.Error("查询SKU退款金额出错： ", err.Error())
		return result
	}
	if len(refundOrder) > 0 {
		for _, v := range refundOrder {
			result[v.SkuId] = v.RefundAmount
		}
	}

	return result
}

// 显示该父订单里面的商品所对应的总退款金额（如果该商品有多次退款，显示多次退款的总金额）
func GetSkuRefundAmountCount(orderSn string) float64 {
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()

	var RefundOrder models.RefundOrderProduct
	refundAmount, _ := dbConn.Table("order_main").Alias("o").
		Join("left", "refund_order as r", "o.order_sn=r.order_sn").
		Join("left", "refund_order_product as p", "r.refund_sn=p.refund_sn").
		Where("r.refund_state=3 and (o.order_sn=? or o.parent_order_sn=?)", orderSn, orderSn).
		Sum(RefundOrder, "p.refund_amount")

	return refundAmount
}

// 实物子订单子查询实物的退款商品信息 虚拟子订单只查询虚拟的订单信息
func OrderRetrunGetList(orderSn string) map[string]float64 {
	list := map[string]float64{}
	model := new(AfterSaleService)
	result, err := model.OrderRetrunGetList(context.TODO(), &oc.RetrunOrderListRequest{OrderSn: orderSn})
	if err != nil || len(result.Data) == 0 {
		return list
	}

	for _, data := range result.Data {
		for _, v := range data.RefundGoodsOrders {
			key := v.ProductName + v.SkuId + cast.ToString(v.PaymentTotal)
			list[key], _ = decimal.NewFromFloat(cast.ToFloat64(list[key])).Add(decimal.NewFromFloat(cast.ToFloat64(v.RefundAmount))).Float64()
			//list[key] = v.RefundAmount
		}
	}

	return list
}

// 实物订单-导出订单数据（退款金额）
// orderSn 是子订单
func OrderRetrunListAmount(orderSn string) float64 {
	model := new(AfterSaleService)
	result, err := model.OrderRetrunGetList(context.TODO(), &oc.RetrunOrderListRequest{OrderSn: orderSn})
	if err != nil || len(result.Data) == 0 {
		return 0.00
	}

	var amount float64
	for _, v := range result.Data {
		amount, _ = decimal.NewFromFloat(amount).Add(decimal.NewFromFloat(cast.ToFloat64(v.RefundAmount))).Float64()
	}

	return amount
}

// 获取实际退款数量 tkcount
func GetRefundOrderTkcount(orderSn string) float64 {
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()
	model := models.RefundOrderThirdProduct{}
	tkcount, _ := dbConn.Table("refund_order_third_product").Where("order_sn=?", orderSn).Sum(model, "tkcount")
	return tkcount
}

// 获取订单下的虚拟商品的实付金额
func RealPaymentVirtualGoods(id string) float64 {
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()
	sql := `select ifnull(sum(total), 0) from dc_order.order_main om where parent_order_sn = ? and is_virtual =1;`

	var virtualTotal float64 = 0
	dbConn.SQL(sql, id).Get(&virtualTotal)

	return virtualTotal
}

func GetParentOrder(orderSn string) *models.OrderMain {
	dbConn := NewSlaveDbConn()
	defer dbConn.Close()
	sql := `select * from dc_order.order_main om where order_sn = ?`

	var order models.OrderMain
	dbConn.SQL(sql, orderSn).Get(&order)

	return &order
}

// 获取用户头像
func GetUserAvatar(avatarUrl string) string {
	if avatarUrl == "" { // 默认头像
		avatarUrl = "https://file.vetscloud.com/awen/logo/default-avata.png"
	}
	return avatarUrl
}

// 阿闻管家团长制拼团订单-导出团员订单数据, t 0-默认父订单，1-实物子订单，2-虚拟子订单
func AwenCommunityGroupMemberOrderExport(params *oc.CommunityGroupOrderListRequest, t int32) (details []*oc.AwenCommunityGroupMemberOrderExport, err error) {
	glog.Info("阿闻管家团长制拼团订单-导出团员订单数据：", kit.JsonEncode(params))
	dbConn := NewSlaveDbConn()
	//dbConn.ShowSQL(true)
	defer dbConn.Close()
	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "datacenter.pickup_station", "pickup_station.id=order_detail.pickup_station_id")
	if t == 1 || t == 2 {
		session.Join("inner", "order_main_group", "order_main_group.parent_order_sn = order_main.parent_order_sn")
	} else {
		session.Join("inner", "order_main_group", "order_main_group.parent_order_sn = order_main.order_sn")
	}
	session.Join("inner", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id").
		Join("left", "order_product", "order_main.order_sn=order_product.order_sn").
		Where("order_main.order_type = 15")

	// 导出类型
	switch t {
	case 1: // 实物子订单
		session.Where("order_main.parent_order_sn != order_main.order_sn and order_main.is_virtual = 0")
	case 2: // 虚拟子订单
		session.Where("order_main.parent_order_sn != order_main.order_sn and order_main.is_virtual = 1")
	default:
		session.Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ''")
	}

	session.OrderBy("order_main.create_time DESC")

	if params.GroupStatus != "" {
		session.Where("order_group_activity.status=?", params.GroupStatus)
	}
	params.Keyword = strings.Trim(params.Keyword, " ")
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case "1": //团长手机
			var scrmUserIds []string
			err = GetUPetDBConn().Table("upet_member").
				Select("scrm_user_id").
				Where("member_mobile like ?", "%"+params.Keyword+"%").
				Where("scrm_user_id != ?", "").
				Find(&scrmUserIds)
			if err != nil {
				err = errors.New("订单导出查询订单列表错误, " + err.Error())
				return
			}
			if len(scrmUserIds) > 0 {
				session.In("order_group_activity.member_id", scrmUserIds)
			} else {
				session.Where("1=0")
			}
		case "2": //团编码
			session.Where("order_group_activity.id = ?", params.Keyword)
		case "3": //财务编码
			session.Where("order_group_activity.dis_chain_finance_code like ?", "%"+params.Keyword+"%")
		case "4": //团长单位名
			session.Where("order_group_activity.dis_chain_name like ?", "%"+params.Keyword+"%")
		}
	}
	if params.GroupTakeType != "" {
		session.Where("order_group_activity.final_take_type = ?", params.GroupTakeType)
	}
	switch params.TimeType {
	case "1":
		session.Where("order_group_activity.created_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
	case "2":
		session.Where("order_group_activity.status = 1 and order_group_activity.end_time BETWEEN ? AND ?", params.StartTime, params.EndTime)
	case "3":
		session.Where("order_group_activity.status = 2 and order_group_activity.end_time BETWEEN ? AND ?", params.StartTime, params.EndTime)
	}
	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.store_user_authority sua", "order_main.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	//登录用户有权限的所有门店id(财务编码)
	if len(params.ShopIds) > 0 {
		session.In("order_main.shop_id", params.ShopIds)
	}
	if err = session.Select(`CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_main.old_order_sn gy_order_sn,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.accept_time,
		order_detail.delivery_remark,
		order_detail.buyer_memo,
		order_detail.is_picking,
		order_detail.picking_time,
		order_detail.extras,
		order_detail.pickup_code serial_number,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
		order_detail.is_new_customer,
		order_detail.performance_operator_time,
		order_detail.expected_time,
		order_product.product_id,
        MAX(order_product.combine_type) group_type,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		order_group_activity.id as order_group_activity_id,
		order_group_activity.member_id as order_group_member_id,
		order_detail.shop_dis_member_id,
		order_detail.shop_dis_chain_id,
		order_main_group.receiver_name as group_member_receiver_name,
		order_main_group.receiver_mobile as group_member_receiver_mobile,
		order_main_group.receiver_address as group_member_receiver_address
		`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("order_main.id").
		OrderBy("if(order_main.order_status_child=20101, 0, 1), if(order_detail.push_third_order=0 and order_main.order_status=20, 0, 1), if(order_main.order_type in (2,3) and order_main.order_status NOT IN (0, 30), 0, 1)").
		Find(&details); err != nil {
		glog.Error("zx订单导出报20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(details) == 0 {
		return
	}
	//查询出来的是主单的退款
	var orderSns []string
	var orderGroupActivityIds []int32
	var shopDisMemberIds []string
	var awenOrderSn, thirdOrderSn []string
	var shopDisChainId []int32
	for _, v := range details {
		orderSns = append(orderSns, v.OrderSn)
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
		if v.OrderGroupActivityId > 0 {
			orderGroupActivityIds = append(orderGroupActivityIds, v.OrderGroupActivityId)
		}
		if len(v.ShopDisMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.ShopDisMemberId)
		}
		if len(v.OrderGroupMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.OrderGroupMemberId)
		}
		if v.ShopDisChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.ShopDisChainId)
		}
	}

	//查询退款信息
	mainOrderRefund := &MainOrderRefund{
		AwenOrderSn:  awenOrderSn,
		ThirdOrderSn: thirdOrderSn,
	}
	//计算退款金额
	refundAmountMap := mainOrderRefund.CalRefundAmount()

	//查询订单优惠信息
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	if err = func() error {
		var orderPromotion []*models.OrderPromotion
		if err = dbConn.Select("order_sn,promotion_type,pt_charge").In("order_sn", orderSns).Find(&orderPromotion); err != nil {
			return err
		}

		//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
		for k := range orderPromotion {
			orderPromotionMap[orderPromotion[k].OrderSn] = append(orderPromotionMap[orderPromotion[k].OrderSn], orderPromotion[k])
		}

		return nil
	}(); err != nil {
		err = errors.New("查询订单优惠信息失败, " + err.Error())
		return
	}

	orderGroupActivityMap := map[int32]*models.OrderGroupActivity{}
	upetMemberChainMap := map[string]*models.UpetMemberChain{}
	disCommissionMap := map[string]*models.OrderDis{}
	upetChainMap := map[int32]*models.UpetChain{}

	//查询订单优惠信息
	var deliveryRecords []*models.OrderDeliveryRecord

	var allChildOrderSn []string
	parentAndChildRelationMap := make(map[string][]string)
	//运费信息中的订单号为子订单号 需要根据子订单查询运费
	//查询子订单
	var childOrders []*models.OrderMain

	wg := sync.WaitGroup{}
	wg.Add(5)
	// 补全团长制拼团的信息
	go func() {
		defer wg.Done()
		if len(orderGroupActivityIds) > 0 {
			var orderGroupActivity []*models.OrderGroupActivity
			if err = dbConn.Select("").
				Table(&models.OrderGroupActivity{}).
				In("id", orderGroupActivityIds).
				Find(&orderGroupActivity); err != nil {
				glog.Error("订单列表查询团长制拼团的信息失败，", err.Error())
			}
			for k, v := range orderGroupActivity {
				orderGroupActivityMap[cast.ToInt32(v.Id)] = orderGroupActivity[k]
			}
		}
	}()

	// 补全业绩 与 绑定门店的信息
	go func() {
		defer wg.Done()
		if len(shopDisMemberIds) > 0 {
			upetMemberChain := &models.UpetMemberChain{}
			upetMemberChainList, err := upetMemberChain.FindInScrmUserId(GetUPetDBConn(), shopDisMemberIds)
			if err != nil {
				glog.Error("订单列表查询业绩会员信息查询失败，", err.Error())
			}
			for k, v := range upetMemberChainList {
				upetMemberChainMap[v.ScrmUserId] = upetMemberChainList[k]
			}
		}
	}()

	// 补全佣金信息
	go func() {
		defer wg.Done()
		var orderDis []*models.OrderDis
		if err = dbConn.Table("order_dis").
			In("parent_order_sn", orderSns).
			Select("parent_order_sn, sum(commission) as commission").
			GroupBy("parent_order_sn").
			Find(&orderDis); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}
		for k, v := range orderDis {
			disCommissionMap[v.ParentOrderSn] = orderDis[k]
		}
	}()

	go func() {
		defer wg.Done()
		err = dbConn.Select("order_sn,parent_order_sn,old_order_sn").In("parent_order_sn", orderSns).Find(&childOrders)
		if err == nil {
			if len(childOrders) > 0 {
				for _, v := range childOrders {
					allChildOrderSn = append(allChildOrderSn, v.OrderSn)
					if _, has := parentAndChildRelationMap[v.ParentOrderSn]; has {
						parentAndChildRelationMap[v.ParentOrderSn] = append(parentAndChildRelationMap[v.ParentOrderSn], v.OldOrderSn)
					} else {
						parentAndChildRelationMap[v.ParentOrderSn] = []string{v.OrderSn}
					}
				}
			}
		}
	}()

	go func() {
		defer wg.Done()
		var upetChainList []*models.UpetChain
		err := GetUPetDBConn().Table("upet_chain").
			Select("chain_id,chain_name,account_id").
			In("chain_id", shopDisChainId).
			Find(&upetChainList)
		if err == nil {
			for i := 0; i < len(upetChainList); i++ {
				upetChainMap[upetChainList[i].ChainId] = upetChainList[i]
			}
		}
	}()

	wg.Wait()

	if len(allChildOrderSn) > 0 {
		//取配送流程最新一条记录并且骑手姓名不为空且状态不为99的数据
		if err := dbConn.Table("order_delivery_record").
			Select("order_delivery_record.*").
			Join("inner", "order_delivery_node", "order_delivery_record.delivery_id = order_delivery_node.delivery_id").
			Where("order_delivery_node.courier_name != '' AND order_delivery_node.delivery_status !=99").
			In("order_delivery_record.order_sn", allChildOrderSn).
			GroupBy("order_delivery_record.order_sn").
			OrderBy("order_delivery_record.id DESC,order_delivery_node.create_time DESC").
			Find(&deliveryRecords); err != nil {
			glog.Error("订单导出查询订单列表错误！ ", err.Error())
		}
	}

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	deliveryRecordsMap := make(map[string]*models.OrderDeliveryRecord, len(deliveryRecords))

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	for _, v := range deliveryRecords {
		deliveryRecordsMap[v.OrderSn] = v
	}

	var activityList []string
	var platformPayedAmount int32
	for _, v := range details {
		activityList = activityList[:0]
		platformPayedAmount = 0
		//优惠活动解析
		for _, promotion := range orderPromotionMap[v.OrderSn] {
			activityList = append(activityList, dto.OrderPrivilegeActiveType(promotion.PromotionType).String())
			platformPayedAmount += promotion.PtCharge
		}
		// 添加平台补贴
		v.PlatformPayedAmount = platformPayedAmount
		if len(activityList) > 0 {
			v.ActivityType = strings.Join(activityList, ";")
		} else {
			v.ActivityType = ""
		}
		//前端的商品总额是不包含所有其他优惠运费服务费的, 而后端的商品总额是减去运费的, 所以要加上
		//门店营收金额 = 客户实付金额+(优惠明细)美团承担的成本-平台服务费
		v.ActualReceiveTotal = v.Total + platformPayedAmount - v.ServiceCharge
		switch v.Source {
		case 1, 4:
			v.Source = 4
		case 5:
			v.Source = 1
		default:
			v.Source = 3
		}
		v.Category = Category[v.Source]
		//if params.CombineType == 1 || params.CombineType == 2 {
		//	v.GroupType = params.CombineType
		//} else if params.CombineType == 4 {
		//	v.GroupType = 0
		//}
		childInfo, _ := parentAndChildRelationMap[v.OrderSn]
		if len(deliveryRecordsMap) > 0 {
			//配送费转成元，没有则为空
			for _, childOrderSn := range childInfo {
				if deliveryInfo, has := deliveryRecordsMap[childOrderSn]; has {
					if len(deliveryInfo.TotalFeeAfter) > 0 {
						intStorePayDeliveryAmount := cast.ToFloat64(v.StorePayDeliveryAmount)
						intTotalFeeAfter := kit.FenToYuan(cast.ToInt32(deliveryInfo.TotalFeeAfter))
						v.StorePayDeliveryAmount = cast.ToString(intStorePayDeliveryAmount + intTotalFeeAfter)
					}
				}
			}
		}
		if refundAmount, ok := refundAmountMap[v.OrderSn]; ok {
			v.RefundAmount = cast.ToString(refundAmount)
		}

		//团长拼团制的一些信息
		v.GroupActivityModel = &oc.SimpleOrderGroupActivity{}
		if v1, ok := orderGroupActivityMap[v.OrderGroupActivityId]; ok {
			v.GroupActivityModel.FinalTakeType = v1.FinalTakeType
			v.GroupActivityModel.Status = v1.Status
			v.GroupActivityModel.DisChainName = v1.DisChainName
			v.GroupActivityModel.MemberName = v1.MemberName
			v.GroupActivityModel.ReceiverAddress = v1.ReceiverAddress
			//导出的时候手机带星
			v.GroupActivityModel.ReceiverMobile = v1.ReceiverMobile
			v.GroupActivityModel.EnReceiverMobile = v1.EnReceiverMobile
		}
		if v1, ok := disCommissionMap[v.OrderSn]; ok {
			v.GroupActivityModel.DisCommission = cast.ToInt32(v1.Commission)
		}
		if v1, ok := upetMemberChainMap[v.OrderGroupMemberId]; ok {
			if len(v1.BillUserName) > 0 {
				v.GroupActivityModel.StaffName = v1.BillUserName
			} else {
				v.GroupActivityModel.StaffName = MobileReplaceWithStar(v1.MemberMobile)
			}
		}
		//业绩的一些信息
		v.ShopDisModel = &oc.SimpleOrderShopDis{}
		if v1, ok := upetMemberChainMap[v.ShopDisMemberId]; ok {
			if len(v1.BillUserName) > 0 {
				v.ShopDisModel.StaffName = v1.BillUserName
			} else {
				v.ShopDisModel.StaffName = MobileReplaceWithStar(v1.MemberMobile)
			}
			v.ShopDisModel.MemberAreaName = v1.MemberAreaName
		}
		if v1, ok := upetChainMap[v.ShopDisChainId]; ok {
			v.ShopDisModel.ChainName = v1.ChainName
			v.ShopDisModel.FinanceCode = v1.AccountId
		}

		//如果非团长待收，处理一下团员地址
		if v.GroupActivityModel.FinalTakeType == 0 {
			v.GroupMemberReceiverName = v.ReceiverName
			v.GroupMemberReceiverMobile = v.ReceiverMobile
			v.GroupMemberReceiverAddress = v.ReceiverAddress
		}
	}
	return
}

// 手机号星号代替
func MobileReplaceWithStar(mobile string) string {
	if len(mobile) == 0 {
		return mobile
	}
	if len(mobile) < 8 {
		return mobile + strings.Repeat("*", 11-len(mobile))
	}
	return mobile[0:3] + "****" + mobile[7:]
}

// 手机号星号代替
func CardReplaceWithStar(card string) string {
	if len(card) == 0 {
		return card
	}

	return card[0:3] + "****" + card[7:]
}

func PushDigitalHealthOrder(orderSn, consultOrderSn string, orderStatus, count int32) error {
	if consultOrderSn == "" {
		return nil
	}
	if count > 5 {
		glog.Errorf("互联网医疗订单回调重试次数过多：order_sn：%s，consult_order_sn：%s", orderSn, consultOrderSn)

		return errors.New("互联网医疗订单回调重试次数过多")
	}
	healthOrderCallback := dto.OrderCallback{
		AwOrderSn:      orderSn,
		ConsultOrderSn: consultOrderSn,
		OrderStatus:    orderStatus,
	}
	statusStr := "下单回调"
	switch orderStatus {
	case 1:
		statusStr = "支付回调"
		break
	case 2:
		statusStr = "退款中回调"
		break
	case 3:
		statusStr = "退款成功回调"
		break
	case 4:
		statusStr = "取消回调"
		break
	}
	//获取结果
	code, body := utils.HttpPostToDigitalHealth("/nuclei-api/callback/order-info", kit.JsonEncodeByte(healthOrderCallback), "")
	glog.Infof("%s-互联网医疗订单回调：order_sn：%s，consult_order_sn：%s，参数：%s，返回结果%s", statusStr, orderSn, consultOrderSn, kit.JsonEncode(healthOrderCallback), body)
	if code != 200 {
		glog.Errorf("%s-互联网医疗订单回调错误：order_sn：%s，consult_order_sn：%s，error：%s", statusStr, orderSn, consultOrderSn, body)

		mqContent := dto.PushDigitalHealthOrder{
			Count:          count + 1,
			AwOrderSn:      orderSn,
			ConsultOrderSn: consultOrderSn,
			OrderStatus:    orderStatus,
		}
		mqContentJson := kit.JsonEncode(mqContent)
		logHead := "PushDigitalHealthOrder:" + mqContent.AwOrderSn
		glog.Info(logHead, "PublishRabbitMQ:", mqContentJson)
		if ok := utils.PublishRabbitMQ(QueuePushDigitalHealthOrder, mqContentJson, MQExchange); !ok {
			glog.Error(logHead, "PublishRabbitMQ.failed:", mqContentJson)
			return errors.New("推送mq失败")
		}
		return errors.New("推送mq失败")
	}
	return nil
}

// 冻结或解冻健康值
// Type 1收入  2支出  3冻结
func UpdateUserHealth(orderSn string, params models.HealthDetail) {
	glog.Info("冻结或解冻健康值，订单号：", orderSn, "操作类型：", params.Type)
	if orderSn == "" || (params.Type != 1 && params.Type != 2 && params.Type != 3) {
		glog.Error("冻结或解冻健康值参数错误：", kit.JsonEncode(params))
		return
	}

	dbConn := NewSlaveDbConn().NewSession()
	defer dbConn.Close()
	//判断是否操作过，避免重复操作
	has, _ := dbConn.Table("datacenter.health_detail").Where("order_sn=? and type=?", orderSn, params.Type).Exist()
	if has {
		return
	}

	orderMain := models.OrderMain{}
	exist, _ := dbConn.Table("order_main").Where("order_sn=?", orderSn).Get(&orderMain)
	if !exist {
		glog.Error("冻结或解冻健康值，订单号不存在。", orderSn)
		return
	}
	//查询店铺信息
	/*store := models.Store{}
	shop, _ := dbConn.Table("datacenter.store").Where("finance_code=?", orderMain.ShopId).Get(&store)
	if !shop {
		glog.Error("冻结或解冻健康值，店铺不存在。", orderSn)
		return
	}*/

	//插入一条健康值明细记录
	///healthVal := utils.Round(kit.FenToYuan(orderMain.PayAmount * 8 / 10)) //四舍五入取整
	healthVal := utils.CalculateHealthVal(kit.FenToYuan(orderMain.PayAmount))
	healthDetail := models.HealthDetail{
		UserId:       orderMain.MemberId,
		Type:         params.Type,
		Title:        params.Title,
		Content:      params.Content,
		PayAmount:    kit.FenToYuan(orderMain.PayAmount),
		RefundAmount: kit.FenToYuan(orderMain.RefundAmount),
		OrderSn:      orderSn,
		ShopId:       orderMain.ShopId,
		ShopName:     orderMain.ShopName,
		HealthVal:    int32(healthVal),
		HealthType:   params.HealthType,
		EffectTime:   params.EffectTime,
	}

	//开启事务
	dbConn.Begin()
	_, err := dbConn.Table("datacenter.health_detail").Insert(&healthDetail)
	if err != nil {
		glog.Error("冻结或解冻健康值，插入健康值明细记录失败：", err.Error())
		dbConn.Rollback()
		return
	}

	//更新用户健康值
	if params.Type == 1 {
		_, err := dbConn.Exec("update datacenter.member_integral_info set health_val=health_val+? where memberid=? and org_id=? ", healthVal, orderMain.MemberId, orderMain.OrgId)
		if err != nil {
			glog.Error("增加健康值失败，订单号：", orderSn, " 错误信息：", err.Error())
			dbConn.Rollback()
			return
		}
	} else if params.Type == 2 {
		_, err := dbConn.Exec("update datacenter.member_integral_info set health_val=health_val-?  where memberid=? and org_id=?", healthVal, orderMain.MemberId, orderMain.OrgId)
		if err != nil {
			glog.Error("减少健康值失败，订单号：", orderSn, " 错误信息：", err.Error())
			dbConn.Rollback()
			return
		}
	} else {
		_, err := dbConn.Exec("update datacenter.member_integral_info set freeze_health_val=freeze_health_val+? where memberid=? and org_id=?", healthVal, orderMain.MemberId, orderMain.OrgId)
		if err != nil {
			glog.Error("冻结健康值失败，订单号：", orderSn, " 错误信息：", err.Error())
			dbConn.Rollback()
			return
		}
	}

	if err := dbConn.Commit(); err != nil {
		glog.Error("冻结或解冻健康值失败，参数：", kit.JsonEncode(params), err.Error())
		return
	}

	return
}

// MobileDecrypt 手机号rc4解密
func MobileDecrypt(ciphertext, userid, Ip string) string {
	//if len(ciphertext) == 0 {
	//	return ""
	//}
	//c, _ := rc4.NewCipher([]byte("IamYourDaddy"))
	//src, _ := base64.StdEncoding.DecodeString(ciphertext)
	//dst := make([]byte, len(src))
	//c.XORKeyStream(dst, src)
	mobile := utils.MobileDecrypt(ciphertext)
	// 添加操作人日志
	if mobile != "" {
		go func() {
			logs := models.DecryptHistory{}
			logs.AppId = 10
			logs.Ip = Ip
			logs.Operator = userid
			logs.OperationTime = time.Now().Format("2006-01-02 15:04:05")
			logs.Ciphertext = ciphertext
			if _, err := GetDcDBConn().Insert(&logs); err != nil {
				glog.Error("MobileDecrypt 手机号解密失败,密文:", ciphertext, ",", err.Error())
			}
		}()
	}
	return mobile
}

// 根据参数获取处方单缓存key
func prescribeCacheKey(scrmId, financeCode string, skus []*oc.OrderPrescribeSkuNum) string {
	// id 升序一波
	sort.Slice(skus, func(i, j int) bool {
		return skus[i].SkuId < skus[j].SkuId
	})
	return "order-center:prescribe:" + kit.GetMd5(scrmId+"."+financeCode+"."+kit.JsonEncode(skus))
}

type DistributeInfo struct {
	DisMemberId         int32   // 分销员id
	OutMemberId         int32   // 上级分销员id
	ChainId             int32   // 分销门店id
	DisType             int32   // 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单
	CustomerServiceId   int32   // 客服id
	CustomerServiceRate float64 // 客服分销比例
}

// 电商分销处理
func distribute(member *models.UpetMember, disId, disType int32) (info *DistributeInfo, err error) {
	defer func() {
		glog.Info("CardService Distribute ", kit.JsonEncode(map[string]interface{}{
			"memberId": member.MemberId,
			"disId":    disId,
			"disInfo":  info,
		}), err)
	}()

	db := GetUPetDBConn()
	disMember, err := member.GetDisMember(db, disId)
	if err != nil {
		return nil, err
	}

	info = &DistributeInfo{
		DisMemberId: disMember.MemberId,
		ChainId:     disMember.DistriChainid,
		DisType:     disType,
	}
	// 如果没有分销id，那就是自主访问，通过粉丝关系找的分销员
	if disId == 0 {
		info.DisType = 5
	}

	// 外部分销员查上级
	if disMember.DistriChainid == 0 {
		outMember := new(models.UpetDistriOutsideMember)
		if has, err := db.Where("member_id = ? and state = 1", disMember.MemberId).Get(outMember); err != nil {
			return nil, err
		} else if has {
			info.OutMemberId = int32(outMember.DisMemberId)
			info.ChainId = int32(outMember.ChainId)
		}
	} else { // 内部分销员，还需要查关联客服
		chainBind := new(models.UpetChainBind)
		if has, err := db.Where("chain_member_mobile = ?", disMember.MemberMobile).
			Select("member_id,cash_ratio").Get(chainBind); err != nil {
			return nil, errors.New("查询客服绑定关系出错 " + err.Error())
		} else if has && chainBind.MemberId != disMember.MemberId {
			info.CustomerServiceId = chainBind.MemberId
			info.CustomerServiceRate = chainBind.CashRatio
		}
	}

	if disId > 0 {
		go func() {
			// 如果客户是外部分销员，同时分销员是内部分销员，同步外部代理人
			if member.DistriState == 2 && member.DistriChainid == 0 && disMember.DistriChainid > 0 {
				if err := member.RefreshOutsideMember(db, disMember); err != nil {
					glog.Error("Distribute RefreshOutsideMember ", err.Error())
				}
			}

			// 客户不是分销员
			// 或者客户是外部分销员同时分销员是内部分销员
			if member.DistriState != 2 || (member.DistriChainid == 0 && disMember.DistriChainid > 0) {
				if err := member.RefreshFans(db, disMember.MemberId, disType); err != nil {
					glog.Error("Distribute RefreshFans ", err.Error())
				}
			}
		}()
	}

	return info, nil
}

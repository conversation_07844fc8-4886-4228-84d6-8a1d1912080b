package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/maybgit/glog"
	"order-center/models"
	pt "order-center/proto/oc"
	"order-center/services"
)

//正向订单重推R1
type redoOMSRefundOrder struct {
}

//重新推送到巨益OMS
func (e *redoOMSRefundOrder) redoTask(taskData *models.OrderRedoTask, redo *redoTask) (err error) {

	requestOrder := new(pt.AfterorderRequest)
	err = json.Unmarshal([]byte(taskData.Params), requestOrder)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskData.Params)
		return
	}
	glog.Info(requestOrder, "-自动重试推送订单至巨益OMS")

	omss := &services.OmsService{}
	_, err = omss.AfterOrderSynchronizeToOms(context.Background(), requestOrder)
	if err != nil {
		return err
	}

	return nil
}

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
	"order-center/dto"
	"order-center/proto/dc"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

func toBase26(n int64) string {
	if n == 0 {
		return "A"
	}

	result := ""
	for n > 0 {
		n-- // 为了使得结果从 1 开始而不是 0，我们需要减 1
		remainder := n % 26
		result = string(rune('A'+remainder)) + result
		n /= 26
	}

	return result
}

func TestCartService_MtSubmitOrder(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.MtAddOrderRequest
	}
	kit.IsDebug = true
	MtAddOrderRequest := &oc.MtAddOrderRequest{}
	jsonstr := `{"order_sn":"","order_status":10,"order_status_child":20101,"shop_id":"CX0004","shop_name":"宠颐生北京爱之都","receiver_name":"杨阳","receiver_state":"","receiver_city":"","receiver_district":"","receiver_address":"广东省 深圳市 福田区KK ONE商场(滨河大道9289号)1101","receiver_phone":"133****0406","privilege":1000,"pay_type":"","receiver_mobile":"133****0406","gjp_status":"","total":1000,"goods_total":2000,"is_pay":0,"create_time":"","pay_time":"","pay_sn":"","order_type":1,"freight":0,"invoice":"","buyer_memo":"","seller_memo":"","delivery_type":2,"delivery_remark":"","extras":"","packing_cost":0,"service_charge":0,"OrderProductModel":[{"id":"","order_id":"","order_sn":"","sku":"1031500001","parent_sku_id":"","product_id":"1031500","product_name":"248宠物狗粮50g","product_type":1,"combine_type":0,"bar_code":"","price":2000,"number":1,"specs":"","payment_total":1000,"privilege":1000,"freight":0,"marking_price":2000,"image":"http://file.vetscloud.com/65b9e0aec31cae9c184cb01a5e6e22ec|equal_proportion","deliver_num":0,"refund_num":0,"privilege_pt":0,"privilege_total":1000,"pay_price":1000,"sub_biz_order_id":"","promotion_id":307421,"article_number":"","promotion_type":1,"sku_pay_total":1000,"used_num":0,"term_type":0,"term_value":0,"mall_order_product_id":0,"virtual_invalid_refund":1,"is_third_product":0,"warehouse_type":0,"group_item_num":0}],"PayInfo":null,"expected_time":"2021-11-16 13:27:38","latitude":22.528873,"longitude":114.02644,"pickup_code":"","total_weight":100,"logistics_code":"0000","member_id":"895e3b3a541241a6b9a5c04a4e0f6ec2","member_name":"133****0406","member_tel":"133****0406","is_adjust":0,"is_virtual":0,"is_split":0,"orderChannelSrcType":null,"orderPromotion":[{"promotion_id":307421,"promotion_type":1,"promotion_title":"满减优惠","poi_charge":1000,"pt_charge":0,"promotion_fee":1000}],"power_id":0,"combine_privilege":0,"is_push_tencent":1,"app_channel":0,"warehouse_id":233,"order_pay_type":"","dis_type":0,"receiver_date_msg":"","tel_phone":"","source":0,"first_order":0,"open_id":"","dis_id":0,"address_id":0,"order_id":0,"diagnose_data":null,"lng":"","lat":""}`
	err := json.Unmarshal([]byte(jsonstr), MtAddOrderRequest)
	if err != nil {
		t.Fatal("Umarshal failed:", err)
	}

	grpcContext := oc.GrpcContext{
		Channel: oc.PlatformChannel{
			ChannelId: 3,
			UserAgent: 3,
		},
	}
	ctx := metadata.AppendToOutgoingContext(context.Background(), "grpc_context", kit.JsonEncode(grpcContext))

	tests := []struct {
		name    string
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "11",
			args: args{
				ctx:    ctx,
				params: MtAddOrderRequest,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CartService{}

			nameList := []string{
				"订单号", "外部订单号", "商品筛选", "下单时间", "支付流水号", "支付时间", "用户实付", "商家预计收入", "商品总额", "商家补贴", "退款金额",
				"商品实付总金额", "配送费", "包装费", "商家配送费优惠", "平台补贴", "平台配送费优惠", "平台服务费", "履约服务费",
				"支付方式", "大区",
				"城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "用户ID", "顾客类型", "收货人", "收货人联系方式", "收货人地址", "配送备注", "配送方式", "订单状态", "活动类型",
				"业绩归属人", "业绩分配人", "业绩归属人所属门店编码", "业绩归属人所属门店名称", "业绩分配时间",
				"仓库类型", "仓库名称", "门店支付配送费", "店铺类型", "提货点名称", "提货点地址", "预计送达时间", "账单订单完成时间", "账单订单取消时间", "账户时间", "入账时间", "账单日期",
				"平台实际分摊补贴",
			}

			for i := 0; i < len(nameList); i++ {

				AAA := toBase26(cast.ToInt64(i+1)) + "1"
				bbb := nameList[i]
				fmt.Println(AAA, bbb)

			}
			grpcContext1 := oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 3, UserAgent: 6}}
			ctx1 := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext1))
			jsstr := `{"order_sn":"4001820084078610094","order_status":20,"order_status_child":20101,"shop_id":"100000288783","shop_name":"test_685202_49101183","receiver_name":"周","receiver_state":"北京","receiver_city":"北京市","receiver_district":"海淀区","receiver_address":"上地街道中国电子彩虹集团(东门)1001","receiver_phone":"132****6304,238","privilege":0,"pay_type":"Cod","receiver_mobile":"132****6304,238","gjp_status":"Payed","total":172,"goods_total":21,"is_pay":1,"create_time":"2024-08-26 14:29:53","pay_time":"2024-08-26 14:30:06","pay_sn":"","order_type":1,"freight":150,"invoice":"{\"发票抬头\":\"\",\"纳税人识别号\":\"\"}","buyer_memo":"(缺货时电话与我联系)","seller_memo":"","delivery_type":2,"delivery_remark":"","extras":"","packing_cost":1,"service_charge":1,"OrderProductModel":[{"id":"","order_id":"","order_sn":"","sku":"68","parent_sku_id":"","product_id":"72","product_name":"【周翔专业】蒙贝 宠物零食成犬 香酥牛棒骨L","product_type":1,"combine_type":0,"bar_code":"","price":20,"number":1,"specs":"","payment_total":20,"privilege":0,"freight":0,"marking_price":20,"image":"","deliver_num":0,"refund_num":0,"privilege_pt":0,"privilege_total":0,"pay_price":20,"sub_biz_order_id":"4003879863416110182","promotion_id":0,"article_number":"","promotion_type":0,"sku_pay_total":20,"used_num":0,"term_type":0,"term_value":0,"mall_order_product_id":0,"virtual_invalid_refund":0,"is_third_product":0,"warehouse_type":0,"group_item_num":0,"use_virtual_stock":0,"channel_category_name":"","is_prescribed_drug":0,"vip_price":0},{"id":"","order_id":"","order_sn":"","sku":"56","parent_sku_id":"","product_id":"60","product_name":"梵米派 【周翔专业】 宠物狗狗可发声耐咬毛绒互动玩具","product_type":1,"combine_type":0,"bar_code":"","price":1,"number":1,"specs":"","payment_total":1,"privilege":0,"freight":0,"marking_price":1,"image":"","deliver_num":0,"refund_num":0,"privilege_pt":0,"privilege_total":0,"pay_price":1,"sub_biz_order_id":"4003879863416120182","promotion_id":0,"article_number":"","promotion_type":0,"sku_pay_total":1,"used_num":0,"term_type":0,"term_value":0,"mall_order_product_id":0,"virtual_invalid_refund":0,"is_third_product":0,"warehouse_type":0,"group_item_num":0,"use_virtual_stock":0,"channel_category_name":"","is_prescribed_drug":0,"vip_price":0}],"PayInfo":[{"pay_id":"","order_sn":"4001820084078610094","pay_sn":"","pay_mode":5,"pay_amount":172,"pay_time":"2024-08-26 14:30:06"}],"expected_time":"2024-08-26 15:12:05","latitude":40.044553249929,"longitude":116.31551332556764,"pickup_code":"","total_weight":102,"logistics_code":"6","member_id":"","member_name":"","member_tel":"","is_adjust":0,"is_virtual":0,"is_split":0,"orderChannelSrcType":null,"orderPromotion":null,"power_id":0,"combine_privilege":0,"is_push_tencent":0,"app_channel":0,"warehouse_id":0,"order_pay_type":"","dis_type":0,"receiver_date_msg":"","tel_phone":"","source":0,"first_order":0,"open_id":"","dis_id":0,"address_id":0,"order_id":0,"diagnose_data":null,"lng":"","lat":"","pickup_station_id":0,"contract_fee":0,"GroupId":0,"GroupMobile":"","GroupName":"","GroupAddress":"","nick_name":"","avatar_url":"","consult_order_sn":"","shop_dis_member_id":"","shop_dis_member_from":0,"bill_completed_time":"","bill_canceled_time":"","trade_created_time":"","trade_payment_time":"","trade_time":"","org_id":0}`
			in := oc.MtAddOrderRequest{}
			json.Unmarshal([]byte(jsstr), &in)

			got, err := s.MtSubmitOrder(ctx1, &in)
			if err != nil {
				t.Errorf("MtSubmitOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test(t *testing.T) {
	FinanceCode := HashGet("store:relation:mttodc", "4889_2701008")

	t.Log(FinanceCode)

	client := GetRedisConn()
	val := client.Get("stock:000f844c49664ad0af95cc68d47e8922:4").Val()

	t.Log(val)
}

//func Test_saveMtOrder(t *testing.T) {
//	type args struct {
//		session       *xorm.Session
//		params        *oc.MtAddOrderRequest
//		WarehouseInfo *dc.WarehouseList
//		GrpcContext   *oc.GrpcContext
//	}
//	jsonStr := `{"order_sn":"2137739096821791965","order_status":20,"order_status_child":20101,"shop_id":"6824006998089739852","shop_name":"宠颐生","receiver_name":"吕****","receiver_state":"","receiver_city":"深圳市","receiver_district":"","receiver_address":"上地街道北京市海淀区上地三街9号5号楼","receiver_phone":"15624995688,344","privilege":400,"pay_type":"Cod","receiver_mobile":"15624995688,344","gjp_status":"Payed","total":360,"goods_total":550,"is_pay":1,"create_time":"2020-11-11 11:04:00","pay_time":"2020-11-11 11:07:05","pay_sn":"","order_type":1,"freight":200,"invoice":"{\"发票抬头\":\"\",\"纳税人识别号\":\"\"}","buyer_memo":"","seller_memo":"","delivery_type":2,"delivery_remark":"","extras":"","packing_cost":10,"service_charge":18,"OrderProductModel":[{"id":"","order_id":"","order_sn":"","sku":"1020728001","product_id":"1020728","product_name":"test爱福测试商品002","bar_code":"","price":200,"number":2,"specs":"","payment_total":255,"privilege":145,"freight":0,"marking_price":200,"image":"","deliver_num":0,"refund_num":0,"privilege_pt":0,"privilege_total":145,"pay_price":127,"sub_biz_order_id":"1359695268579945639","promotion_id":0,"article_number":"","promotion_type":0},{"id":"","order_id":"","order_sn":"","sku":"1020727001","product_id":"1020727","product_name":"test爱福测试商品001","bar_code":"","price":150,"number":1,"specs":"","payment_total":95,"privilege":55,"freight":0,"marking_price":150,"image":"","deliver_num":0,"refund_num":0,"privilege_pt":0,"privilege_total":55,"pay_price":95,"sub_biz_order_id":"1359695268581945639","promotion_id":0,"article_number":"","promotion_type":0}],"PayInfo":[{"pay_id":"","order_sn":"2137739096821791965","pay_sn":"","pay_mode":5,"pay_amount":360,"pay_time":"2020-11-11 11:07:05"}],"expected_time":"2020-11-11 11:29:11","latitude":40.04258173060768,"longitude":116.3155104843532,"pickup_code":"","total_weight":2500,"logistics_code":"6","member_id":"","member_name":"","member_tel":"","is_adjust":0,"orderChannelSrcType":null,"orderPromotion":[{"promotion_id":0,"promotion_type":401,"promotion_title":"全店满减","poi_charge":200,"pt_charge":0,"promotion_fee":200},{"promotion_id":0,"promotion_type":4011,"promotion_title":"配送费满减","poi_charge":200,"pt_charge":0,"promotion_fee":200}]}`
//	params := new(oc.MtAddOrderRequest)
//	json.Unmarshal([]byte(jsonStr), params)
//	tests := []struct {
//		name    string
//		args    args
//		want    models.Order
//		want1   []models.OrderProduct
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			args: args{
//				session:       GetDBConn().NewSession(),
//				params:        params,
//				WarehouseInfo: &dc.WarehouseList{},
//				GrpcContext:   &oc.GrpcContext{Channel: oc.PlatformChannel{ChannelId: 3, UserAgent: 6}},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			_, _, err := saveMtOrder(tt.args.session, tt.args.params, tt.args.WarehouseInfo, tt.args.GrpcContext)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("saveMtOrder() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//		})
//	}
//}

func Test_splitGift(t *testing.T) {
	type args struct {
		grpcRes *dc.DemolitionOrderResponse
		params  *oc.NewAddOrderRequest
	}
	tests := []struct {
		name string
		args args
		want []dto.DianShangOrder
	}{
		{name: ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			json.Unmarshal([]byte(`{"code":200,"message":"Success","error":"","WarehouseToGoodsList":[{"warehouseid":2,"orderid":"3000000028100901","Quantity":1,"goodsid":"NPBBLKY025","thirdid":"0005000011","warehousecode":"010AWDSBJCK"},{"warehouseid":4,"orderid":"3000000028100901","Quantity":1,"goodsid":"BJHG003","thirdid":"**********","warehousecode":"0579AWDSYWCK"}]}`), &tt.args.grpcRes)
			json.Unmarshal([]byte(`{"order":{"order_sn":"3000000028100901","pay_sn":"120660215968686143","store_id":1,"store_name":"阿闻宠物","buyer_id":"1211143","buyer_name":"upet_aw45bae203","buyer_email":"","buyer_phone":"18500027632","add_time":1606871968,"payment_code":"online","order_state":10,"order_amount":11400,"shipping_fee":0,"goods_amount":11400,"order_from":7,"order_type":1,"chain_id":1146,"rpt_amount":0,"order_id":350386,"is_dis":1},"common":{"order_id":350386,"store_id":1,"order_message":"","promotion_total":1000,"reciver_info":{"phone":"18500027632","mob_phone":"18500027632","tel_phone":"","address":"北京 北京市 海淀区 玉海园五里(北京市海淀区采石路1号)8号楼三单元101","area":"北京 北京市 海淀区","street":"玉海园五里(北京市海淀区采石路1号)8号楼三单元101"},"reciver_name":"王海鹏","reciver_city_id":36,"invoice_info":"a:0:{}","promotion_info":"a:1:{i:0;a:2:{i:0;s:15:\"店铺代金券\";i:1;s:48:\"使用10元代金券 编码：357780660215627143\";}}","reciver_date_msg":"","reciver_info_serialize":"a:6:{s:5:\"phone\";s:11:\"18500027632\";s:9:\"mob_phone\";s:11:\"18500027632\";s:9:\"tel_phone\";s:0:\"\";s:7:\"address\";s:94:\"北京 北京市 海淀区 玉海园五里(北京市海淀区采石路1号)8号楼三单元101\";s:4:\"area\";s:26:\"北京 北京市 海淀区\";s:6:\"street\";s:67:\"玉海园五里(北京市海淀区采石路1号)8号楼三单元101\";}"},"order_goods":[{"order_id":350386,"goods_id":"105713","store_id":1,"goods_name":"红狗 犬猫通用综合营养膏120g","goods_price":5900,"goods_num":1,"goods_image":"2020/1_06425257562138522.jpg","goods_spec":"","buyer_id":"1211143","goods_commonid":"102985","add_time":1606871968,"goods_type":1,"chain_id":0,"promotions_id":0,"commis_rate":200,"gc_id":"1180","goods_contractid":"1,2,3,4,5","goods_pay_price":5430,"is_dis":1,"dis_member_id":172055,"dis_commis_rate":1,"sku":"BJHG003","rec_id":"479935","source":1,"oc_id":"","out_member_id":"0"},{"order_id":350386,"goods_id":"105009","store_id":1,"goods_name":"宠儿香-犬肠乐宝 5g*7袋/盒","goods_price":6500,"goods_num":1,"goods_image":"2020/1_06572315833937248.jpg","goods_spec":"种　　类：5g*7袋/盒","buyer_id":"1211143","goods_commonid":"102466","add_time":1606871968,"goods_type":12,"chain_id":0,"promotions_id":0,"commis_rate":200,"gc_id":"1085","goods_contractid":"1,2,3,4,5","goods_pay_price":5970,"is_dis":1,"dis_member_id":172055,"dis_commis_rate":5,"sku":"NPBBLKY025","rec_id":"479936","source":1,"oc_id":"","out_member_id":"0"}]}`), &tt.args.params)
			//if got := splitGift(tt.args.grpcRes, tt.args.params); !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("splitGift() = %v, want %v", got, tt.want)
			//	t.Log(kit.JsonEncode(got))
			//}
		})
	}
}

func TestCartService_splitSep3(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		CommonService CommonService
	}
	type args struct {
		session   *xorm.Session
		demoorder []dto.DianShangOrder
		params    *oc.NewAddOrderRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*oc.DemoOrder
		want1  string
	}{
		{name: ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			json.Unmarshal([]byte(`{"order":{"order_sn":"3000000028100901","pay_sn":"120660215968686143","store_id":1,"store_name":"阿闻宠物","buyer_id":"1211143","buyer_name":"upet_aw45bae203","buyer_email":"","buyer_phone":"18500027632","add_time":1606871968,"payment_code":"online","order_state":10,"order_amount":11400,"shipping_fee":0,"goods_amount":11400,"order_from":7,"order_type":1,"chain_id":1146,"rpt_amount":0,"order_id":350386,"is_dis":1},"common":{"order_id":350386,"store_id":1,"order_message":"","promotion_total":1000,"reciver_info":{"phone":"18500027632","mob_phone":"18500027632","tel_phone":"","address":"北京 北京市 海淀区 玉海园五里(北京市海淀区采石路1号)8号楼三单元101","area":"北京 北京市 海淀区","street":"玉海园五里(北京市海淀区采石路1号)8号楼三单元101"},"reciver_name":"王海鹏","reciver_city_id":36,"invoice_info":"a:0:{}","promotion_info":"a:1:{i:0;a:2:{i:0;s:15:\"店铺代金券\";i:1;s:48:\"使用10元代金券 编码：357780660215627143\";}}","reciver_date_msg":"","reciver_info_serialize":"a:6:{s:5:\"phone\";s:11:\"18500027632\";s:9:\"mob_phone\";s:11:\"18500027632\";s:9:\"tel_phone\";s:0:\"\";s:7:\"address\";s:94:\"北京 北京市 海淀区 玉海园五里(北京市海淀区采石路1号)8号楼三单元101\";s:4:\"area\";s:26:\"北京 北京市 海淀区\";s:6:\"street\";s:67:\"玉海园五里(北京市海淀区采石路1号)8号楼三单元101\";}"},"order_goods":[{"order_id":350386,"goods_id":"105713","store_id":1,"goods_name":"红狗 犬猫通用综合营养膏120g","goods_price":5900,"goods_num":1,"goods_image":"2020/1_06425257562138522.jpg","goods_spec":"","buyer_id":"1211143","goods_commonid":"102985","add_time":1606871968,"goods_type":1,"chain_id":0,"promotions_id":0,"commis_rate":200,"gc_id":"1180","goods_contractid":"1,2,3,4,5","goods_pay_price":5430,"is_dis":1,"dis_member_id":172055,"dis_commis_rate":1,"sku":"BJHG003","rec_id":"479935","source":1,"oc_id":"","out_member_id":"0"},{"order_id":350386,"goods_id":"105009","store_id":1,"goods_name":"宠儿香-犬肠乐宝 5g*7袋/盒","goods_price":6500,"goods_num":1,"goods_image":"2020/1_06572315833937248.jpg","goods_spec":"种　　类：5g*7袋/盒","buyer_id":"1211143","goods_commonid":"102466","add_time":1606871968,"goods_type":12,"chain_id":0,"promotions_id":0,"commis_rate":200,"gc_id":"1085","goods_contractid":"1,2,3,4,5","goods_pay_price":5970,"is_dis":1,"dis_member_id":172055,"dis_commis_rate":5,"sku":"NPBBLKY025","rec_id":"479936","source":1,"oc_id":"","out_member_id":"0"}]}`), &tt.args.params)
			tt.args.session = GetDBConn().NewSession()
			defer tt.args.session.Close()
			json.Unmarshal([]byte(`[{"WarehouseId":2,"WarehouseCode":"010AWDSBJCK","Orderid":"3000000028100901","Num":1,"Thirdid":"0005000011","Goods":[{"Id":"NPBBLKY025","Num":1,"Allot":0,"RecId":"479936"}]},{"WarehouseId":4,"WarehouseCode":"0579AWDSYWCK","Orderid":"3000000028100901","Num":1,"Thirdid":"**********","Goods":[{"Id":"BJHG003","Num":1,"Allot":0,"RecId":"479935"}]}]`), &tt.args.demoorder)
			//got, got1 := splitSep3(tt.args.session, tt.args.demoorder, tt.args.params)
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("splitSep3() got = %v, want %v", got, tt.want)
			//}
			//if got1 != tt.want1 {
			//	t.Errorf("splitSep3() got1 = %v, want %v", got1, tt.want1)
			//}
		})
	}
}

func TestCartService_HealthSubOrder(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.HealthSubOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.HealthSubOrderResponse
		wantErr bool
	}{
		{name: "HealthSubOrder", args: args{
			ctx: nil,
			request: &oc.HealthSubOrderRequest{
				ScrmUserId:   "f6a939c4bbf545d1994889db5a2a1f12",
				ScrmPetId:    "f89ddfd23f514a2dbf1666d813bbb9b6",
				PayMoney:     100,
				CategoryCode: "SKUID001",
				CategoryName: "大大泡泡糖",
				MemberName:   "测试",
				MemberTel:    "18124695582",
			},
		}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CartService{}
			got, err := s.HealthSubOrder(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("HealthSubOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HealthSubOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCartService_UpdateOrderMeal(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.UpdateOrderMealRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{name: "", args: args{request: &oc.UpdateOrderMealRequest{
			OrderSn:    "1615471760461329",
			EnsureCode: "111",
			BatchCode:  "2222",
		}}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CartService{}
			got, err := s.UpdateOrderMeal(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateOrderMeal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateOrderMeal() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCartService_ThirdSplitOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				CommonService: CommonService{
					session:   GetDBConn().NewSession(),
					orderMain: GetOrderMainByOldOrderSn("27010242866770045"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := CartService{
				CommonService: tt.fields.CommonService,
			}
			if _, err := s.ThirdSplitOrder(); (err != nil) != tt.wantErr {
				t.Errorf("ThirdSplitOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

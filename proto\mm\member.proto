syntax = "proto3";
import "google/api/annotations.proto";
package mm;

message MemberMergeRequest {
  string memberPhone = 1;
}

service MemberMergeService{
  rpc MemberMergeSearchList (MemberMergeRequest) returns (MemberMergeResponse);
  rpc GetMemberGoodBrowseList (GetMemberGoodBrowseListRequest) returns (GetMemberGoodBrowseListResponse);
  rpc StoreMemberGoodBrowseInfo (StoreMemberGoodBrowseInfoRequest) returns (StoreMemberGoodBrowseInfoResponse);
  rpc ClearMemberGoodBrowse (ClearMemberGoodBrowseRequest) returns (ClearMemberGoodBrowseResponse);
}

message StoreMemberGoodBrowseInfoRequest {
  string member_id = 1;
  int32 sku_id = 2;
  int32 spu_id = 3;
  int32 org_id = 4;
}
message StoreMemberGoodBrowseInfoResponse {
  int32 code = 1;
  //消息提示
  string message = 2;
}

message GetMemberGoodBrowseListRequest{
  string member_id = 1;
  int32 org_id = 2;
}
message GetMemberGoodBrowseListResponse{
  int32 code = 1;
  //消息提示
  string message = 2;
  repeated MemberGoodBrowseInfo data = 3;
}
message MemberGoodBrowseInfo {
  string create_time = 1;
  int32 sku_id = 2;
  int32 spu_id = 3;
  string product_name = 4;
  //商品价格 单位分
  int32 price = 5;
  string spec = 6;
  string pic = 7;
  //上下架状态
  int32 up_down = 8;
  int32 goods_promotion_type = 9;
  int32 goods_promotion_price = 10;
  // 会员价
  int32 member_price1 = 11;
  // 新人专享价格
  int32 new_people_price = 12;
  // 会员价是否生效
  int32 is_member_price = 13;
  // 日期
  string date = 14;
}
message ClearMemberGoodBrowseRequest {
  //用户id
  string member_id = 1;
}
message ClearMemberGoodBrowseResponse {
  //code
  int32 code = 1;
  //消息提示
  string message = 2;
}
message MemberMergeErrorResponse{
  //code
  int32 code = 1;
  //消息提示
  string message = 2;
}

message MemberMergeResponse{
  //code
  int32 code = 1;
  //消息提示
  string message = 2;
  repeated MemberMergelList data = 3;
}
//用户合并记录列表
message MemberMergelList {
  //处理时间
  int32 mml_dealtime = 1;
  //旧手机号
  string mml_old_mobile = 2;
  //新手机号
  string mml_new_mobile = 3;
  //处理信息
  string mml_notes = 4;
  //处理人
  string create_user = 5;
  //申请人
  string applicant = 6;
  //部门
  string department = 7;
  //申请时间
  string application_date = 8;
  //审批记录
  string approval_record = 9;
}

package models

type OrderMeal struct {
	Id           int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	OrderSn      string `xorm:"not null comment('订单号') VARCHAR(50)"`
	EnsureCode   string `xorm:"default ''''  comment('卡号（健康订阅）') VARCHAR(50) "` //
	BatchCode    string `xorm:"default '''' comment('批次编码（健康订阅）') VARCHAR(50)"`
	CreateTime   string `xorm:"<-" json:"createdate"`
	PetId        string `xorm:"default '''' comment('套餐对应的宠物scrm pet Id') VARCHAR(50)"`
	UserId       string `xorm:"default '''' comment('套餐对应的宠物scrm user Id') VARCHAR(50)"`
	CategoryCode string `xorm:"default '''' comment('套餐号') VARCHAR(50)"`
	IsRenew      int    `xorm:"not null default 0 comment('是否续卡 1是,0否') INT(11)"`
}

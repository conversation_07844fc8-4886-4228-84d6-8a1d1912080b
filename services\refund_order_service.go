package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"order-center/proto/cc"
	"order-center/proto/ext"
	"order-center/proto/pay"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"

	"github.com/xuri/excelize/v2"
	"google.golang.org/grpc/metadata"

	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"

	jsoniter "github.com/json-iterator/go"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

// 售后模块
type RefundOrderService struct {
	CommonService
}

// 申请售后单
func (r RefundOrderService) RefundOrderApply(ctx context.Context, params *oc.RefundOrderApplyRequest) (*oc.RefundOrderApplyResponse, error) {
	out := oc.RefundOrderApplyResponse{Code: 400}
	glog.Info("售后单申请入参：", kit.JsonEncode(params))

	//如果是阿闻 检测一下是否存在同一个order_product_id下有多条记录
	//理论上一次退款申请  一个order_product_id只有一个条记录
	//但是线上会发生一个order_product_id有多条的记录的情况 从而导致bug 前端传过来就重复了  目前暂时无法重现 先通过下面的这个去重的方法解决
	if params.ChannelId == ChannelAwenId || params.ChannelId == ChannelDigitalHealth {
		var refundGoods []*oc.RefundOrderGoodsData
		orderProductIdMap := make(map[int64]struct{})
		var duplicated bool
		for _, v := range params.RefundOrderGoodsData {
			//如果已经有记录 则调过
			if _, ok := orderProductIdMap[v.OrderProductId]; ok {
				duplicated = true
				continue
			}
			orderProductIdMap[v.OrderProductId] = struct{}{}
			refundGoods = append(refundGoods, v)
		}
		//如果有重复 则重新赋值
		if duplicated {
			params.RefundOrderGoodsData = refundGoods
		}
	}

	r.session = GetDBConn().NewSession()
	defer r.session.Close()

	r.orderMain = new(models.OrderMain)
	//查询订单信息
	//第三方传过来的 主订单id与第三方订单id
	//阿闻传过来的是子订单id
	var (
		orderIsOk bool
		err       error
	)
	if params.ChannelId != ChannelAwenId && params.ChannelId != ChannelMallId && params.ChannelId != ChannelDigitalHealth {
		orderIsOk, err = r.session.SQL("SELECT * FROM `order_main` WHERE old_order_sn=? AND order_status>0", params.ExternalOrderId).Get(r.orderMain)
	} else {
		orderIsOk, err = r.session.SQL("select * FROM `order_main` WHERE (order_sn=? OR old_order_sn=?) AND order_status>0", params.OrderId, params.ExternalOrderId).Get(r.orderMain)
	}
	if err != nil || !orderIsOk {
		if err != nil {
			glog.Error("售后单申请,查询不到对应的订单:" + params.ExternalOrderId + err.Error())
		}
		out.Message = "查询不到对应的订单,或者是支付时间没到2分钟！"
		out.Error = "查询不到对应的订单,或者是支付时间没到2分钟！" + params.ExternalOrderId
		return &out, nil
	}

	//如果订单是saas订单，则需要查询主订单
	if r.orderMain.AppChannel == SaasAppChannel && r.orderMain.ChannelId == 1 && r.orderMain.ParentOrderSn == "" {
		out.Message = "订单拆单中，请稍后再重试"
		out.Error = "订单拆单中，请稍后再重试"
		glog.Errorf("订单拆单中，请稍后再重试,订单sn:%s,外部订单id:%s", r.orderMain.OrderSn, params.ExternalOrderId)
		return &out, nil
	}

	params.ChannelId = r.orderMain.ChannelId

	if r.orderMain.ChannelId == ChannelJddjId && params.IsCancalOrder != 1 {
		if r.orderMain.OrderStatus != 30 || r.orderMain.OrderStatusChild != 20106 {
			out.Message = "京东到家订单，部分退款只能是订单完结状态才能进行。"
			out.Error = "京东到家订单，部分退款只能是订单完结状态才能进行！"
			return &out, nil
		}
	}

	if r.orderMain.OrderStatus == 10 {
		out.Message = "该订单是未支付订单OR订单已取消"
		out.Error = out.Message
		return &out, nil
	}
	if r.orderMain.OrderStatus == 0 {
		out.Message = "该订单是未支付订单OR订单已取消"
		out.Error = out.Message
		return &out, nil
	}

	//第三方回调有可能会调多次，如果已经存在第三方退款单，就不进行处理
	if len(params.OldRefundSn) > 0 {
		var refundOrder models.RefundOrder
		_, err = r.session.Where("old_refund_sn = ?", params.OldRefundSn).Get(&refundOrder)
		if err != nil {
			glog.Error(params.OrderId, "RefundOrderApply 查询退款记录出错", err)
		}
		if refundOrder.Id > 0 {
			out.Code = 200
			return &out, nil
		}
	}

	//长连接，勿关闭
	redisConn := GetRedisConn()

	lockKey := "lock:order_" + r.orderMain.OrderSn //加锁
	lockRes := redisConn.SetNX(lockKey, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		glog.Error("取消订单,取锁失败：", params.ExternalOrderId)
		out.Message = "取锁失败"
		return &out, nil
	}
	defer redisConn.Del(lockKey)

	//生成退款单
	if len(params.RefundOrderSn) <= 0 {
		params.RefundOrderSn = GetSn("refund")[0]
	}

	var refundOrder []models.RefundOrder
	//非商城订单 部分退款一个子订单只能有一个在途退款订单
	if r.orderMain.ChannelId != ChannelMallId {
		r.session.Where("order_sn = ? and refund_state in(1,5,6,7)", r.orderMain.OrderSn).Find(&refundOrder)
		if len(refundOrder) > 0 {
			out.Message = "请把所有退款处理完再发起退款！"
			out.Error = "请把所有退款处理完再发起退款！"
			return &out, nil
		}
	}

	if params.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelDigitalHealth {
		if params.ApplyOpUserType == "1" && r.orderMain.DeliveryType != 1 {
			var refundOrder2 []models.RefundOrder
			r.session.SQL(" SELECT  a.*  FROM  refund_order a  JOIN refund_order_log b ON a.`refund_sn`= b.`refund_sn` "+
				"WHERE a.`full_refund`=1  AND a.`refund_type`=1  AND  a.`refund_state`=2  AND  b.`notify_type` ='apply' "+
				"AND b.`reason` IN('计划有变，我不想要了','我买错了/填错了')  AND a.`order_sn`=?", r.orderMain.OrderSn).Find(&refundOrder2)
			if len(refundOrder2) > 0 {
				out.Message = "因买家原因全单退款被拒绝后，不能再发起退款申请"
				out.Error = "因买家原因全单退款被拒绝后，不能再发起退款申请"
				return &out, nil
			}
		}

		now := time.Now()
		d, _ := time.ParseDuration("-2m")
		d1 := now.Add(d)
		if r.orderMain.PayTime.After(d1) {
			out.Code = 400
			out.Message = "申请售后单需要支付时间大于2分钟才能申请，请稍候"
			out.Error = "申请售后单需要支付时间大于2分钟才能申请，请稍候！"
			return &out, nil
		}
	}

	//查询退款日志是否存在，是不是已经执行过本次操作了
	logIsHave := 0
	_, _ = r.session.SQL("select 1 from refund_order_log where refund_sn=? and res_type=?", params.RefundOrderSn, params.ResType).Get(&logIsHave)

	//已经操作过的，就直接返回成功了
	if logIsHave == 1 {
		out.Code = 200
		glog.Info("订单的本次操作已经处理过了" + params.RefundOrderSn)
		return &out, nil
	}

	//设置退款主表
	refundOrderModel := SetRefundOrder(params, r.orderMain.OrderSn)
	refundOrderModel.ShopId = r.orderMain.ShopId
	refundOrderModel.AppChannel = r.orderMain.AppChannel
	refundOrderModel.OrderSource = r.orderMain.Source

	// todo v6.3.3
	// 美团急速退款 请求过来的金额是0  需要重新计算
	//退款日志
	refundOrderLog := SetRefundOrderLog(params.ExternalOrderId, params.Reason, params.Pictures, params.ApplyOpUserType, params.OperationType,
		"apply", params.ResType, params.RefundOrderSn, params.OperationUser)

	//查询退款成功的订单
	r.session.Where("order_sn = ? and refund_state = 3", r.orderMain.OrderSn).Find(&refundOrder)

	r.orderDetail = GetOrderDetailByOrderSn(r.orderMain.OrderSn, "push_third_order")
	//全渠道
	if r.orderDetail.PushThirdOrder > 0 && r.orderMain.Source == 1 {
		refundOrderModel.ReasonCode = "01"
		if params.RefundType == 1 {
			refundOrderModel.RefundTypeSn = "仅退款"
		} else {
			refundOrderModel.RefundTypeSn = "退款退货"
		}
	}

	//订单商品集合
	//第三方订单 比如美团传过来的是主订单号
	//v6.0之后对虚实组合进行了拆单 组合商品不再只是一个商品入库 所以在查询退款商品的时候不能查询子商品
	orderGoods := r.GetRefundApplyOrderProduct()
	if len(orderGoods) == 0 {
		out.Message = "售后单获取购买商品出错"
		out.Error = out.Message
		return &out, nil
	}
	glog.Info("GetRefundApplyOrderProduct,订单商品集合,", kit.JsonEncode(orderGoods))

	//由于饿了么的全部退款走的是部分退款的回调接口，我们这边就当做部分退款，所以要重新计算饿了么是全部退款还是部分退款
	if params.ChannelId == ChannelElmId && params.FullRefund == 2 {
		//计算这次退款的商品数量是否等于没退的商品数量，是的话就是全部退款

		//这次退款的商品总数，饿了么的只计算主商品，也就是组合商品的个数，不统计组合商品里面的子商品
		sumGoods := int32(0)
		sumRefundGoods := int32(0)
		for _, item := range params.RefundOrderGoodsData {
			sumGoods += item.Quantity
		}

		//剩下没有退款的数量
		for _, item1 := range orderGoods {
			sumRefundGoods += item1.Number - item1.RefundNum
		}
		if sumGoods >= sumRefundGoods {
			params.FullRefund = 1
			refundOrderModel.FullRefund = 1
		}

	}

	//退款商品集合
	refundOrderGoods := make([]*models.RefundOrderProduct, 0)
	if params.FullRefund == 2 { //部分退
		var totalRefundAmount float64
		for _, x := range params.RefundOrderGoodsData {
			var good models.OrderProduct
			var orderVerifyCodes []models.OrderVerifyCode
			refundAmount := x.RefundAmount
			if r.orderMain.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelMallId || r.orderMain.ChannelId == ChannelDigitalHealth {
				for _, f := range orderGoods {
					//如果有相同SKU不同价格的话，退款数量不一致可能会导致商品退不了 diffSku
					if f.Id == x.OrderProductId {
						//f.Id == x.OrderProductId || (f.SkuId == x.SkuId && f.PayPrice == int32(kit.YuanToFen(x.RefundRealityPrice)))
						good = f
						if f.ProductType == 2 {
							orderVerifyCodes = GetValidOrderVerifyCodes(f.OrderSn, 1)
							if x.Quantity > int32(len(orderVerifyCodes)) {
								out.Message = "售后单退款虚拟商品不能大于有效商品数量，请刷新页面重新发起"
								out.Error = out.Message
								return &out, nil
							}
						} else {
							if x.Quantity > (f.Number - f.RefundNum) {
								out.Message = "售后单退款商品不能大于购买时商品数量，请刷新页面重新发起"
								out.Error = out.Message
								return &out, nil
							}
						}
					}
				}
				//如果剩下的商品全部退，根据实付总价减去已经退的数量
				if x.Quantity == good.Number-good.RefundNum {
					refundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(good.PaymentTotal)-(kit.FenToYuan(good.PayPrice)*(float64(good.RefundNum))))
				}
			} else {
				//第三方也可能存在虚拟订单 目前第三方订单只有组合商品存在虚拟商品 所以在部分退的时候 需要核销该订单下的所有核销码
				for _, f := range orderGoods {
					//如果有相同SKU不同价格的话，退款数量不一致可能会导致商品退不了
					//v6.0第三方订单进行了拆单 但是传过来的是主单 所以暂时跟之前一样 不考虑有相同的sku不同价格的情况 diffSku(标记请勿删除)
					//todo 如果第三方可以售卖虚拟商品之后 此处同样需要像阿闻渠道一样处理productType=2的情形
					if f.SkuId == x.SkuId {
						//组合商品
						//todo 通过combineType判断
						if f.ProductType == 3 {
							orderVerifyCodes = GetVerifyCodesByGroupSkuId(f.OrderSn, f.SkuId, 1)
						}
						good = f
						break
					}
				}
			}

			refundOrderGood := &models.RefundOrderProduct{
				RefundSn:       params.RefundOrderSn, //退款单号
				SkuId:          x.SkuId,
				OrderProductId: x.OrderProductId,
				ParentSkuId:    good.ParentSkuId,
				ProductName:    x.GoodsName,
				ProductType:    good.ProductType,
				ProductPrice:   int32(kit.YuanToFen(x.RefundPrice)),
				Quantity:       x.Quantity,
				Tkcount:        x.Quantity,
				RefundAmount:   refundAmount,
				RefundPrice:    int32(kit.YuanToFen(x.RefundRealityPrice)),
				Spec:           x.Spec,
				SubBizOrderId:  x.SubBizOrderId,
				MarkingPrice:   good.MarkingPrice,
			}
			//获取需要退掉的虚拟商品的核销码 此处只是查询记录  实际上执行退款退的并并不是直接读的这些，而是退款时用同样的方法获取的
			if len(orderVerifyCodes) > 0 {
				var verifyCodes strings.Builder
				SkuNumMap := make(map[string]int)
				if good.ProductType == 2 {
					for i, v := range orderVerifyCodes {
						//申请了几个退几个
						//todo 处理部分退款的情况
						quantity := int(x.Quantity)
						//第三方订单 且是组合的情况下 组合下的每个虚拟订单 都需要退掉组合商品的数量* 该sku在该组合中的数量（x.Quantity * group_item_num）
						if good.ProductType == 3 && r.IsThirdOrder() {
							quantity = int(x.Quantity * v.GroupItemNum)
						}
						if _, has := SkuNumMap[v.SkuId]; has {
							SkuNumMap[v.SkuId] += 1
						} else {
							SkuNumMap[v.SkuId] = 1
						}
						if SkuNumMap[v.SkuId] > quantity {
							continue
						}
						verifyCodes.WriteString(v.VerifyCode)
						if i != len(orderVerifyCodes)-1 {
							verifyCodes.WriteString(",")
						}
					}
				}
				refundOrderGood.VerifyCodes += verifyCodes.String()
			}

			totalRefundAmount += cast.ToFloat64(refundOrderGood.RefundAmount)
			refundOrderGoods = append(refundOrderGoods, refundOrderGood)
		}
		if r.orderMain.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelMallId || r.orderMain.ChannelId == ChannelDigitalHealth {
			refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", totalRefundAmount)
		}
	} else {
		//FullRefund如果不是2 都会走全额退  如果请求端该值是0 也会走全额退
		// 全额退  没有商品信息
		//用户原因驳回后不可再次发起全额退款：
		//已经退款的总金额
		var sumRefundAmount int32
		//全额退款得自己去计算这次退的商品
		//第三方查询每个sku过往的退款总额 diffSku
		//如果一个组合有多个价格 那么退款的时候 只管退款的个数 与 总金额
		thirdSkuRefundAmount := make(map[string]int32)
		if r.IsThirdOrder() {
			//查询过往的退款总额
			var refundDetails []struct {
				SkuId         string
				TotalRefunded float32
			}
			err = r.session.SQL(
				`SELECT sku_id,SUM(b.refund_amount) as total_refunded FROM refund_order a JOIN refund_order_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 GROUP BY sku_id`, r.orderMain.OrderSn).Find(&refundDetails)
			if err != nil {
				glog.Error("申请全部退款，查询sku已退款总额明细出错", r.orderMain.OrderSn, err)
				out.Message = "获取已退款总额失败，请刷新页面"
				out.Error = out.Message
				return &out, nil
			}
			if len(refundDetails) > 0 {
				for _, k := range refundDetails {
					thirdSkuRefundAmount[k.SkuId] = int32(kit.YuanToFen(k.TotalRefunded))
					sumRefundAmount += int32(kit.YuanToFen(k.TotalRefunded))
				}
			}
		}

		//todo 下面第三方的过往退款金额是用单价计算的 可能有出入 因为现在进行了倒减 所以退款商品的总金额还是对得上的
		//todo 但是一个商品过往退款金额会超过下单时的金额 加上退完时使用sku总支付金额倒减的方式获取退款商品
		//todo 会导致一个商品退的金额是负数 从而导致推送子龙报错
		thirdSkuPayAmount := make(map[string]int32)
		if r.IsThirdOrder() {
			for _, t := range orderGoods {
				thirdSkuPayAmount[t.SkuId] += t.PaymentTotal
			}
			glog.Info(params.OrderId, ",第三方订单paymentTotal", thirdSkuPayAmount)
		}

		for _, x := range orderGoods {
			//可退数量小于购买数量的才进行退款计算 如果已经退完的 直接使用实际支付金额PaymentTotal既可
			var hasRefundAmount int32
			var nowRefundAmount int32
			if x.Number <= x.RefundNum {
				hasRefundAmount = x.PaymentTotal
				//阿闻渠道加总
				if !r.IsThirdOrder() {
					sumRefundAmount += hasRefundAmount //全部退时，不能用单价*数量，因为单价可能已经经过四舍五入;也不能使用sku_pay_amount,因为一个sku可能拆成多条
				}
				glog.Info("可退数量小于购买数量")
				continue
			} else {
				//如果是第三方 已经退款的金额不能用单价乘 要使用过往的退款金额总和
				if r.IsThirdOrder() {
					if _, ok := thirdSkuRefundAmount[x.SkuId]; ok {
						hasRefundAmount = thirdSkuRefundAmount[x.SkuId]
					}
					//前面有过退款的情况 才使用 处理第三方单个商品多个价格时退款按照平均数退的问题
					if paymentTotal, ok := thirdSkuPayAmount[x.SkuId]; ok && x.RefundNum > 0 {
						nowRefundAmount = paymentTotal - hasRefundAmount
					} else {
						nowRefundAmount = x.PaymentTotal - hasRefundAmount
					}
				} else {
					//阿闻的退款金额
					hasRefundAmount = x.RefundNum * x.PayPrice
					nowRefundAmount = x.PaymentTotal - hasRefundAmount
					sumRefundAmount += hasRefundAmount //已退部分，则计算已退部分金额用乘
				}

			}

			refundGood := &models.RefundOrderProduct{
				RefundSn:       params.RefundOrderSn, //退款单号
				SkuId:          x.SkuId,
				OrderProductId: x.Id,
				ParentSkuId:    x.ParentSkuId,
				ProductName:    x.ProductName,
				ProductType:    x.ProductType,
				ProductPrice:   x.DiscountPrice,
				Quantity:       x.Number - x.RefundNum,                              //剩余的全退
				Tkcount:        x.Number - x.RefundNum,                              //剩余的全退
				RefundAmount:   fmt.Sprintf("%.2f", kit.FenToYuan(nowRefundAmount)), //整单退时，计算已经退了的金额了使用了核销码的金额，不能用商品支付价*可剩数量的方法，因为单价已经四舍五入
				RefundPrice:    x.PayPrice,
				Spec:           x.Specs,
				SubBizOrderId:  x.SubBizOrderId,
				MarkingPrice:   x.MarkingPrice,
				OcId:           cast.ToString(x.Id),
			}

			//只有虚拟订单时会使用到
			if x.ProductType == 2 || x.ProductType == 3 {
				var orderVerifyCodes []models.OrderVerifyCode
				//第三方组合商品 因为第三方退款为主订单号 所以查询虚拟商品的核销码方法不一样 第三方订单通过主订单查 阿闻渠道直接通过子订单查
				if x.ProductType == 3 && r.IsThirdOrder() {
					if x.VirtualInvalidRefund == 1 {
						orderVerifyCodes = GetVerifyCodesByGroupSkuId(x.OrderSn, x.SkuId, 6)
					} else {
						orderVerifyCodes = GetVerifyCodesByGroupSkuId(x.OrderSn, x.SkuId, 1)
					}
				} else if x.ProductType == 2 {
					if x.VirtualInvalidRefund == 1 {
						orderVerifyCodes = GetValidOrderVerifyCodes(x.OrderSn, 6)
					} else {
						orderVerifyCodes = GetValidOrderVerifyCodes(x.OrderSn, 1)
					}
					//允许过期退
					if len(orderVerifyCodes) <= 0 {
						glog.Info("允许过期退")
						continue
					}
				}
				var verifyCodes strings.Builder
				for i, v := range orderVerifyCodes {
					verifyCodes.WriteString(v.VerifyCode)
					if i != len(orderVerifyCodes)-1 {
						verifyCodes.WriteString(",")
					}
				}
				refundGood.VerifyCodes = verifyCodes.String()
				//v6.0 修改
				//非第三方的组合商品下的虚拟订单 需要根据虚拟商品的数量重置退款数量
				//因为阿闻的虚拟订单已经进行了拆单 针对的是子订单 重置退款数量逻辑才成立

				if !(x.ProductType == 3 && r.IsThirdOrder()) {
					//目前只有阿闻的虚拟订单会走到这里来
					codeRefundNum := int32(len(orderVerifyCodes))
					refundGood.Quantity = codeRefundNum
					refundGood.Tkcount = codeRefundNum

					//核销码入坑金额等于支付总价-已退款，已核销，已过期数量X支付金额
					sumRefundAmount = x.PayPrice * (x.Number - codeRefundNum)
					refundGood.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(x.PaymentTotal-sumRefundAmount))
				}
			}

			//计算退款单这次退款的总价，用订单实际价格减去已经退款的价格
			refundOrderGoods = append(refundOrderGoods, refundGood)
		}
		if len(refundOrderGoods) <= 0 {
			out.Message = "没有可退商品，请刷新页面"
			out.Error = out.Message
			return &out, nil
		}

		//查询满减运费优惠
		// saas-v1.0 todo
		orderPromotion := new(models.OrderPromotion)
		r.session.Where("order_sn=? and promotion_type=3", r.orderMain.OrderSn).Get(orderPromotion)
		//todo v6.0 第三方过往退款总额计算错误 不能使用单价 乘数量的方式
		var itemRefundAmount int32
		for i, x := range refundOrderGoods {
			if i != len(refundOrderGoods)-1 {
				itemAmount := x.Quantity * x.RefundPrice
				if x.Quantity >= x.Tkcount { //退款数量>可退数量,即全部退
					itemAmount = int32(kit.YuanToFen(cast.ToFloat64(x.RefundAmount))) //全部退时，不能用单价*数量，因为单价可能已经经过四舍五入;也不能使用sku_pay_amount,因为一个sku可能拆成多条
				}
				itemRefundAmount += itemAmount
			} else {
				if params.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelMallId || r.orderMain.ChannelId == ChannelDigitalHealth {
					refundOrderGoods[i].RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total-sumRefundAmount-itemRefundAmount-(r.orderMain.Freight-orderPromotion.PromotionFee)))
				}
				//京东 饿了么 最后一个商品不倒减吗？？？
				//是否是京东与饿了么 不退运费？
				if params.ChannelId == ChannelMtId {
					var freight int32
					//去掉优惠的邮费
					FreightFloat := r.FreightAll()
					freight = r.orderMain.Freight - FreightFloat
					refundOrderGoods[i].RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total-sumRefundAmount-itemRefundAmount-freight-r.orderMain.PackingCost))
				}
			}
		}

		var totals []float64
		var RefundOrder models.RefundOrder
		if r.orderMain.ParentOrderSn == "" {
			totals, _ = r.session.Where("order_sn=? and refund_state=3", r.orderMain.OrderSn).Sums(RefundOrder, "activity_pt_amount", "delivery_price")
			//deliveryPrice, _ = r.session.Where("order_sn=? and refund_state=3", r.orderMain.OrderSn).Sums(RefundOrder, "delivery_price")
		} else {
			totals, _ = r.session.Where("order_sn=? and refund_state=3", r.orderMain.ParentOrderSn).Sums(RefundOrder, "activity_pt_amount", "delivery_price")
			//deliveryPrice, _ = r.session.Where("order_sn=? and refund_state=3", r.orderMain.OrderSn).Sums(RefundOrder, "delivery_price")
		}

		//来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
		//todo 贺林
		if params.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelMallId || r.orderMain.ChannelId == ChannelDigitalHealth {
			//查询这个订单的付款总金额
			//计算出本次全额退款的金额，用订单实际付款金额减去已经退款商品的金额
			refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total-sumRefundAmount-(r.orderMain.Freight-orderPromotion.PromotionFee)))
			//20101(美团默认)未接单;
			//20102已接单; 未发货-需要退运费
			if r.orderMain.OrderStatusChild == 20101 || r.orderMain.OrderStatusChild == 20102 {
				//sumRefundAmount 已经退款的金额
				if sumRefundAmount <= 0 {
					refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total))
				} else {
					refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total-sumRefundAmount))
				}
			}
		} else {
			//todo 美团Or饿了么
			//查询这个订单的付款总金额
			//计算出本次全额退款的金额，用订单实际付款金额减去已经退款商品的金额
			//todo v6.3.3 退款金额 是不退打包费的

			refundAmount, _ := r.session.Where("order_sn=? and refund_state=3", r.orderMain.OrderSn).Sum(RefundOrder, "refund_amount")
			//退款单的退款总金额这么算是正确的 第三方就一定会退运费吗？
			refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Total-int32(kit.YuanToFen(refundAmount))))
			//计算最后一次的退款单的平台优惠金额(有可能是取消订单自己生成的退款单)
			//activityPtAmount, _ := r.session.Where("order_sn=? and refund_state=3", r.orderMain.OrderSn).Sum(RefundOrder, "activity_pt_amount")
			//var activityPtAmount float64
			//var deliveryPrice float64

			refundOrderModel.ActivityPtAmount = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.PtChargeTotal-int32(kit.YuanToFen(totals[0]))))

		}
		refundOrderModel.DeliveryPrice = fmt.Sprintf("%.2f", kit.FenToYuan(r.orderMain.Freight-int32(kit.YuanToFen(totals[1]))))
	}
	glog.Info("售后申请单商品集合："+refundOrderModel.OrderSn, kit.JsonEncode(refundOrderGoods))

	//电商订单的售后需要推送OMS 实物商品推送，非实物商品不推送
	if params.OrderFrom == 1 && (params.ChannelId == ChannelMallId || r.orderMain.Source == 5) && r.orderMain.IsVirtual == 0 && r.orderMain.OrderType != 21 {
		_params := oc.AfterApplyOrderRequest{
			RefundSn:       refundOrderModel.RefundSn,
			OrderSn:        refundOrderModel.OrderSn,
			CreateTime:     refundOrderModel.CreateTime.Format("2006-01-02 15:03:04"),
			Status:         "waitAgree",
			RefundTypeSn:   refundOrderModel.RefundTypeSn,
			ReasonCode:     refundOrderModel.ReasonCode,
			RefundRemark:   refundOrderModel.RefundRemark,
			RefundType:     refundOrderModel.RefundType,
			DiscountAmount: refundOrderModel.DiscountAmount,
			RefundReason:   refundOrderModel.RefundReason,
			ExpressName:    refundOrderModel.ExpressName,
			ExpressNum:     refundOrderModel.ExpressNum,
			RefundAmount:   refundOrderModel.RefundAmount,
			OrderSource:    refundOrderModel.OrderSource,
		}
		for _, v := range refundOrderGoods {
			_refundGoodsOrder := oc.RefundGoodsOrder{
				Id:            strconv.FormatInt(v.Id, 10),
				GoodsId:       v.SkuId,
				Quantity:      v.Quantity,
				RefundAmount:  v.RefundAmount,
				OcId:          v.OcId,
				Barcode:       v.Barcode,
				Refundorderid: v.RefundSn,
				FoodName:      v.ProductName,
				Spec:          v.Spec,
				RefundPrice:   float64(v.RefundPrice),
				BoxPrice:      float64(v.BoxPrice),
				BoxNum:        v.BoxNum,
				Tkcount:       v.Tkcount,
				FoodPrice:     float32(v.ProductPrice),
			}
			_params.RefundGoodsOrders = append(_params.RefundGoodsOrders, &_refundGoodsOrder)
		}

		model := SetAfterOrderRequest(&_params)
		omsService := OmsService{}
		grpcRes, err := omsService.AfterOrderSynchronizeToOms(ctx, model)
		glog.Info("申请售后推送OMS结果：", kit.JsonEncode(grpcRes), err)
		if err != nil {
			out.Code = 400
			out.Message = "调用oms接口失败" + err.Error()
			glog.Error("调用oms接口失败:" + refundOrderModel.OrderSn + ", err: " + err.Error())
			return &out, nil
		}
		if grpcRes.Code != 0 {
			out.Code = grpcRes.Code
			out.Message = grpcRes.Message
			glog.Error("调用oms接口失败:" + out.Message)
			return &out, nil
		}
	}
	//如果是VIP订单的话，传过来退多少就是多少
	if r.orderMain.OrderType == 17 {
		refundOrderModel.RefundAmount = cast.ToString(params.RefundAmount)
	}

	r.session.Begin()
	//写入售后单主表 refund_order
	isOk, err := r.session.Table("refund_order").Insert(refundOrderModel)
	if err != nil {
		r.session.Rollback()
		glog.Error(refundOrderModel.OrderSn, ", 退款回调插入出错"+err.Error())
		out.Error = "退款回调插入出错" + err.Error()
		return &out, nil
	}
	if isOk == 0 {
		r.session.Rollback()
		out.Error = "插入退款单0条数据" + refundOrderModel.RefundSn
		return &out, nil
	}
	//写入售后单商品表 refund_order_product
	isOk, err = r.session.Insert(refundOrderGoods)
	if err != nil {
		r.session.Rollback()
		glog.Error("退款回调插入商品信息出错:" + refundOrderModel.RefundSn + err.Error())
		out.Message = "退款回调插入商品信息出错"
		out.Error = "退款回调插入商品信息出错" + err.Error()
		return &out, nil
	}
	if isOk == 0 {
		r.session.Rollback()
		out.Message = "插入退款商品信息0条"
		out.Error = "插入退款商品信息0条" + refundOrderModel.RefundSn
		return &out, nil
	}

	//写入售后单记录表 refund_order_log
	isOk, err = r.session.Insert(refundOrderLog)
	if err != nil {
		r.session.Rollback()
		glog.Error("退款回调插入记录出错:" + refundOrderModel.OrderSn + err.Error())
		out.Message = "退款回调插入记录出错"
		out.Error = "退款回调插入记录出错" + refundOrderModel.OrderSn + err.Error()
		return &out, nil
	}
	if isOk == 0 {
		r.session.Rollback()
		out.Message = "退款回调log插入0条记录"
		out.Error = "退款回调log插入0条记录" + refundOrderModel.OrderSn
		return &out, nil
	}

	//第三方退款单进行退款单拆单均摊
	if isThirdChannel(params.ChannelId) {
		//对退款商品进行处理
		var splitRefundGood []*models.RefundOrderThirdProduct
		glog.Info("申请售后单拆单前的数据", kit.JsonEncode(refundOrderGoods))
		splitRefundGood, err = r.ThirdRefundGoodSplit(refundOrderGoods)
		glog.Info("申请售后单拆单后的数据", kit.JsonEncode(refundOrderGoods))
		if err != nil {
			glog.Error("第三方售后单拆单出错", err, refundOrderModel.OrderSn)
			r.session.Rollback()
			out.Message = "第三方售后单拆单出错"
			return &out, nil
		}
		_, err = r.session.Insert(splitRefundGood)
		if err != nil {
			glog.Error("售后单拆单结果落库失败", err)
			r.session.Rollback()
			glog.Error("售后单拆单结果落库失败:" + refundOrderModel.OrderSn + err.Error())
			out.Message = "售后单拆单结果落库失败"
			return &out, nil
		}
	}

	r.session.Commit()

	go func() {
		if r.orderMain.ChannelId == ChannelMallId {
			//实物订单才操作积分
			if r.orderMain.IsVirtual == 0 {
				// 发布退货扣减积分mq
				IntegralOperation(params.RefundOrderSn, false)
			}
		} else {
			//通知数据中心
			MessageCreate(&models.Message{
				OrderId:     params.RefundOrderSn,
				MessageType: 3,
				FinanceCode: r.orderMain.ShopId,
				Msg:         fmt.Sprintf("【客户退款】您有一个新的退款订单：%s，请及时处理！", params.RefundOrderSn),
			})
		}

		//退款流转日志记录
		if len(params.ApplyOpUserType) > 0 {
			orderLog := &models.OrderLog{
				OrderSn: params.OrderId,
			}
			switch params.ApplyOpUserType {
			case "1":
				orderLog.LogType = models.UserApplyForRefund
			case "2":
				orderLog.LogType = models.StoreApplyForRefund
			case "3":
				orderLog.LogType = models.CustomerServiceApplyForRefund
			case "4":
				orderLog.LogType = models.BDApplyForRefund
			case "5":
				orderLog.LogType = models.SystemApplyForRefund
			case "6":
				orderLog.LogType = models.OpenPlatformApplyForRefund
			}

			//记录订单流转日志
			SaveOrderLog([]*models.OrderLog{orderLog})
		}

		glog.Info("申请售后时 插入日志1", refundOrderModel.RefundSn)
		// 阿闻渠道完成超过24小时记录日志
		if (params.ChannelId == ChannelAwenId || params.ChannelId == ChannelDigitalHealth) &&
			!r.orderMain.ConfirmTime.IsZero() &&
			r.orderMain.ConfirmTime.Add(24*time.Hour).Before(time.Now()) {

			record := &models.AbnormalOrderRecord{
				OperateType:   1,
				OrderSn:       params.OrderId,
				RefundOrderSn: refundOrderModel.RefundSn,
				RefundAmount:  refundOrderModel.RefundAmount,
				OrderDescribe: fmt.Sprintf("订单完成后%d天退款", int(time.Now().Sub(r.orderMain.ConfirmTime).Hours()/24)),
				OperateReason: refundOrderModel.RefundReason,
				Status:        3,
				OrgId:         int64(r.orderMain.OrgId),
			}
			if md, success := metadata.FromIncomingContext(ctx); success {
				if len(md.Get("user-no")) > 0 {
					record.UserNo = md.Get("user-no")[0]
				}
				if len(md.Get("user-name")) > 0 {
					record.UserName = md.Get("user-name")[0]
				}
				if len(md.Get("ip")) > 0 {
					record.IpAddr = md.Get("ip")[0]
				}
			}
			glog.Info("申请售后时 插入日志2", refundOrderModel.RefundSn)
			if _, err := r.session.Table("dc_order.abnormal_order_record").Insert(record); err != nil {
				glog.Warning("申请售后时，插入退款记录出错：", err.Error(), "，记录数据：", kit.JsonEncode(record))
			}
		}

		//清理掉redis 锁1
		//DelRedisValue("apply", params.ExternalOrderId)
		//如果是全额退款，并且还没有配送的话，就直接同意退款
		if params.FullRefund == 1 && r.orderMain.ChannelId != ChannelMallId && (r.orderMain.OrderType == 1 || r.orderMain.OrderType == 2) && params.ApplyOpUserType == "1" && r.orderMain.DeliveryType == 2 {

			//orderDetail := GetOrderDetailByOrderSn(r.orderMain.OrderSn, "push_delivery")

			//glog.Info(r.orderMain.OrderSn, "未发配送订单全额退款进入判断") // 记录日志
			DeliveryOrder := ""
			if r.orderMain.ChannelId == 1 {
				DeliveryOrder = r.orderMain.OrderSn
			} else {
				chorder := models.OrderMain{}
				_, err = r.session.Where("parent_order_sn=? and is_virtual=0", r.orderMain.OrderSn).Get(&chorder)
				if err != nil {
					glog.Error(r.orderMain.OrderSn, "未发配送订单全额退款，自动同意失败", err.Error()) // 记录日志
				}
				DeliveryOrder = chorder.OrderSn
			}

			deliveryRecord := new(models.OrderDeliveryRecord)
			has, err := r.session.Where("order_sn=?", DeliveryOrder).Get(deliveryRecord)
			if err != nil {
				glog.Error("美团专送，查询配送单出错："+DeliveryOrder, err)
			}

			//没有发过配送，直接同意全部退款
			if !has && r.orderMain.OrgId != 6 {

				model := new(oc.MtOrderRefundRequest)
				model.OrderId = params.ExternalOrderId
				model.Reason = "未发配送订单，全额退款自动同意"
				model.Refundsn = params.RefundOrderSn
				model.StoreMasterId = r.orderMain.AppChannel
				et := AfterSaleService{}
				time.Sleep(1 * time.Second) //防止主从还没同步
				grpcRes, err := et.MtOrderRefundAgree(context.Background(), model)
				//grpcRes, err := r.RefundOrderAnswer(context.Background(), &InAnswer)
				if err != nil {
					glog.Error(r.orderMain.OrderSn, "未发配送订单全额退款，自动同意失败", err.Error()) // 记录日志
				}
				if grpcRes.Code != 200 {
					glog.Info(r.orderMain.OrderSn, "未发配送订单全额退款，自动同意失败", grpcRes.Error) // 记录日志
				}
			}

		}

	}()

	out.Code = 200
	out.RefundOrderSn = refundOrderModel.RefundSn
	out.Message = "申请售后单成功"
	out.CreateTime = r.orderMain.CreateTime.UnixNano() / 1e6
	return &out, nil
}

// 退款均摊时将商品相关字段copy到RefundOrderThirdProduct数据结构中
func parseToRefundOrderThirdProduct(item *models.RefundOrderThirdProduct, child models.OrderProduct) {
	item.SkuId = child.SkuId
	item.ParentSkuId = child.ParentSkuId
	item.ProductName = child.ProductName
	item.ProductType = child.ProductType
	item.Itemcode = child.ThirdSkuId
	item.Barcode = child.BarCode //是否会出问题 ？？
	item.Number = child.Number
	item.OrderProductId = child.Id //是否会出问题 ？？

	item.PaymentTotal = child.PaymentTotal
	item.ProductPrice = child.DiscountPrice
	item.PayPrice = child.PayPrice
	item.MarkingPrice = child.MarkingPrice
	item.PrivilegeTotal = child.PrivilegeTotal //是否会出问题 ？？

	item.OrderSn = child.OrderSn
}

// 第三方退款单拆单均摊
func (r CommonService) ThirdRefundGoodSplit(refundOrderGoods []*models.RefundOrderProduct) ([]*models.RefundOrderThirdProduct, error) {
	var splitRefundGood []*models.RefundOrderThirdProduct
	for _, v := range refundOrderGoods {
		//非组和商品 直接append 不需要均摊
		if v.ProductType != 3 {
			item := new(models.RefundOrderThirdProduct)
			if err := utils.MapTo(v, item); err != nil {
				return nil, err
			}
			var orderProduct models.OrderProduct
			//查询子商品信息
			var has bool
			var err error
			switch r.orderMain.ChannelId {
			case ChannelJddjId:
				has, err = r.session.Table("order_product").Alias("a").
					Join("inner", "order_main b", "a.order_sn=b.order_sn").
					Where("b.parent_order_sn=? AND a.sku_id=? AND a.parent_sku_id = '' AND a.discount_price = ? ", r.orderMain.OrderSn, v.SkuId, v.ProductPrice).
					Get(&orderProduct)
			case ChannelMtId:
				has, err = r.session.Table("order_product").Alias("a").
					Join("inner", "order_main b", "a.order_sn=b.order_sn").
					Where("b.parent_order_sn=? AND a.sku_id=? AND a.parent_sku_id = ''", r.orderMain.OrderSn, v.SkuId).
					Get(&orderProduct)
			case ChannelElmId:
				has, err = r.session.Table("order_product").Alias("a").
					Join("inner", "order_main b", "a.order_sn=b.order_sn").
					Where("b.parent_order_sn=? AND a.sku_id=? AND a.parent_sku_id = '' AND a.sub_biz_order_id = ?", r.orderMain.OrderSn, v.SkuId, v.SubBizOrderId).
					Get(&orderProduct)
			}

			if err != nil {
				return nil, err
			}
			if has == false {
				return nil, errors.New("退款单拆单未查询到商品信息" + v.SkuId)
			}
			parseToRefundOrderThirdProduct(item, orderProduct)
			splitRefundGood = append(splitRefundGood, item)
		} else {
			//组合商品进行均摊
			//有1种情况不用作均摊计算
			//1:子商品的总支付价格 == 退款金额 此时直接用下单时的均摊数据既可  这种情况一般来自于整单退款
			//只有当退款金额 不满足上面两种情况是需要再次进行均摊

			//组合商品子商品
			var childProducts []models.OrderProduct
			//组合商品信息
			var groupProduct models.OrderProduct //组合商品

			//查询组合商品信息
			has, err := r.session.Table("order_product").
				Where("order_sn=? AND sku_id=? AND parent_sku_id = ''", r.orderMain.OrderSn, v.SkuId).Get(&groupProduct)
			if err != nil {
				return nil, err
			}
			if has == false {
				return nil, errors.New("退款单拆单未查询到商品信息" + v.SkuId)
			}

			//通过子单商品查询组合商品子商品信息
			err = r.session.Table("order_product").Select("a.*").Alias("a").
				Join("inner", "order_main b", "a.order_sn=b.order_sn").
				Where("b.parent_order_sn=? AND a.parent_sku_id=?", r.orderMain.OrderSn, v.SkuId).
				Find(&childProducts)
			if err != nil {
				return nil, err
			}
			if len(childProducts) == 0 {
				return nil, errors.New("退款单拆单未查询到商品信息" + v.SkuId)
			}

			nowRefundAmount := kit.YuanToFen(cast.ToFloat64(v.RefundAmount))

			if nowRefundAmount == int(groupProduct.PaymentTotal) {
				for _, child := range childProducts {
					item := new(models.RefundOrderThirdProduct)
					if err = utils.MapTo(v, item); err != nil {
						return nil, err
					}
					//分情况
					//总金额相等 则取总金额 累加相等则取单价乘以数量后的值
					//因为如果是因为总金额相等时 退款金额取单价相乘的结果的话，就会出现对不上的情况
					item.RefundPrice = child.PayPrice
					item.RefundAmount = cast.ToString(kit.FenToYuan(child.PaymentTotal))

					item.Quantity = child.GroupItemNum * v.Quantity
					item.Tkcount = child.GroupItemNum * v.Quantity
					parseToRefundOrderThirdProduct(item, child)

					splitRefundGood = append(splitRefundGood, item)
				}
			} else {
				//均摊总的退款金额
				//为了避免多次部分退款直到退完后实物中每个sku的总金额实物总金额与下单时推送子龙的不相等
				//此处需要进行检测，检测该sku是否是在本次进行退完 如果没有退完 则正常均摊，
				//如果是在本次进行退完，则根据过往该组合退款总额加上这次的退款总额分来两种情况
				//如果相等：则针对组合里面的所有sku需要进行sku在组合里的总支付金额进行倒减
				//这种情况有一种情况会出现问题：当前面的退款单还没有完成时 第二张全退的退款单就到了 这个时候计算前面组合退款的总额不对
				//如果不相等：则按照正常的均摊逻辑进行均摊

				var reduceCal bool //
				//如果是本组合最后一次退款（已经退款的数量+本次退款 = 组合商品的总数量）
				if (groupProduct.RefundNum + v.Quantity) == groupProduct.Number {
					//查询该组合商品过往退款总金额
					reduceCal = true
					/*var refundedAmount float32
					//此处只能查询实物的退款单总额
					_, err = r.session.SQL(
						`SELECT IFNULL(SUM(b.refund_amount),0) b FROM refund_order a JOIN refund_order_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 AND b.sku_id=?`, r.orderMain.OrderSn, v.SkuId).Get(&refundedAmount)
					intRefundedAmount := kit.YuanToFen(refundedAmount)
					//如果查询出错 则可能导致金额出错 所以返回空数据
					glog.Info(r.orderMain.OrderSn, "查询组合商品之前已退款总额", intRefundedAmount, v.SkuId)
					if err != nil {
						glog.Error(r.orderMain.OrderSn, "退款单均摊 查询组合商品过往退款总额失败", r.orderMain.ParentOrderSn, err)
						return nil, err
					}
					//如果该组合之前退款总额加这次的退款额度 等于总的支付金额 则走倒减均摊逻辑
					//
					if (intRefundedAmount + nowRefundAmount) == int(groupProduct.PaymentTotal) {
						reduceCal = true
					}*/
				}

				//不走总支付价格倒减逻辑的均摊
				if !reduceCal {
					var sumChildTotal int32
					for _, child := range childProducts {
						sumChildTotal += child.PayPrice * child.Number
					}
					refundAmount := int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount)))
					var sumItemAmount int32 //统计已减金额
					for index, child := range childProducts {
						item := new(models.RefundOrderThirdProduct)
						if err = utils.MapTo(v, item); err != nil {
							return nil, err
						}
						var itemRefundAmount int32
						if len(childProducts)-1 == index {
							//最后一个需要倒减
							itemRefundAmount = refundAmount - sumItemAmount
							if itemRefundAmount < 0 {
								itemRefundAmount = 0
							}
							item.RefundAmount = cast.ToString(kit.FenToYuan(itemRefundAmount))
						} else {
							//商品项优惠 = 总优惠*优惠占比（商品项原金额原金额/总优惠）
							//向下取整
							ratio := cast.ToFloat64(child.PayPrice*child.Number) / cast.ToFloat64(sumChildTotal)
							itemRefundAmount = cast.ToInt32(ratio * cast.ToFloat64(refundAmount))
							item.RefundAmount = cast.ToString(kit.FenToYuan(itemRefundAmount))
							sumItemAmount += itemRefundAmount
						}
						floatPrice := cast.ToFloat64(itemRefundAmount) / cast.ToFloat64(child.GroupItemNum*v.Quantity)
						item.RefundPrice = cast.ToInt32(floatPrice) //与下单时计算价格一致 单价采取向下取整
						item.Quantity = child.GroupItemNum * v.Quantity
						item.Tkcount = child.GroupItemNum * v.Quantity

						parseToRefundOrderThirdProduct(item, child)

						splitRefundGood = append(splitRefundGood, item)
					}
				} else {
					//倒减的均摊逻辑
					//查询组合里每个组合商品在过往退了多少钱
					//查询过往的退款总额
					thirdSkuRefundAmount := make(map[string]int32)
					var refundDetails []struct {
						SkuId         string
						ParentSkuId   string
						TotalRefunded float32
					}
					err = r.session.SQL(
						`SELECT sku_id,parent_sku_id,SUM(b.refund_amount) AS total_refunded FROM refund_order a JOIN refund_order_third_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 AND b.parent_sku_id=? GROUP BY sku_id`, r.orderMain.OrderSn, v.SkuId).Find(&refundDetails)
					if err != nil {
						return nil, errors.New("查询第三方sku已退款总额明细出错:" + v.SkuId + err.Error())
					}
					if len(refundDetails) > 0 {
						for _, k := range refundDetails {
							thirdSkuRefundAmount[k.SkuId] = int32(kit.YuanToFen(k.TotalRefunded))
						}
					}

					for _, child := range childProducts {
						item := new(models.RefundOrderThirdProduct)
						if err = utils.MapTo(v, item); err != nil {
							return nil, err
						}
						var itemRefundAmount int32
						itemRefundAmount = child.PaymentTotal - thirdSkuRefundAmount[child.SkuId]
						floatPrice := cast.ToFloat64(itemRefundAmount) / cast.ToFloat64(child.GroupItemNum*v.Quantity)
						item.RefundAmount = cast.ToString(kit.FenToYuan(itemRefundAmount))

						item.RefundPrice = cast.ToInt32(floatPrice) //与下单时计算价格一致 单价采取向下取整
						item.Quantity = child.GroupItemNum * v.Quantity
						item.Tkcount = child.GroupItemNum * v.Quantity

						parseToRefundOrderThirdProduct(item, child)
						splitRefundGood = append(splitRefundGood, item)
					}
					//查询该组合商品过往退款金额
				}
			}
		}
	}
	return splitRefundGood, nil
}

// 应答售后单（外部：美团，饿了么）
func (r RefundOrderService) RefundOrderAnswer(ctx context.Context, params *oc.RefundOrderAnswerRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}

	redisConn := GetRedisConn()
	lockKey := "order-center:RefundOrderAnswer:" + params.RefundOrderSn
	if !redisConn.SetNX(lockKey, 1, 120*time.Second).Val() {
		out.Message = fmt.Sprintf("正在处理退款任务，请%.0f秒后再试", redisConn.TTL(lockKey).Val().Seconds())
		out.Error = out.Message
		return &out, nil
	}
	defer redisConn.Del(lockKey)

	glog.Info("应答售后单入参：", kit.JsonEncode(params))
	r.session = GetDBConn().NewSession()
	defer r.session.Close()

	refundOrder := models.RefundOrder{}
	//查询日志
	isOk, err := r.session.SQL("select  * from `refund_order` where (old_refund_sn=? or refund_sn=?) and refund_state in (1,5,6,7)",
		params.RefundOrderSn, params.RefundOrderSn).Get(&refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error(params.RefundOrderSn, "-售后单还不存在就接到了其他的回调处理:"+err.Error())
		}
		glog.Info("打印参数zx ：", params)
		out.Message = "退款单还不存在就接到了其他的回调处理" + "|" + params.RefundOrderSn
		out.Error = out.Message
		return &out, nil
	}

	refundStatus := 3
	notifyType := "agree"

	//订单信息
	r.orderMain = new(models.OrderMain)
	//refundOrder.OrderSn 第三方订单为主单号 阿闻订单为子单号
	orderIsOk, err := r.session.SQL("select  * from `order_main` where order_sn= ? ", refundOrder.OrderSn).Get(r.orderMain)
	if err != nil || !orderIsOk {
		if err != nil {
			glog.Error(params.ExternalOrderId, "-，售后单申请，查询不到对应的订单：", err)
		}
		out.Message = "售后单申请，查询不到对应的订单"
		out.Error = out.Message
		return &out, nil
	}
	//退款状态“退款中” 更新为 “退款成功”及退款完成时间
	if r.orderMain.OrderStatus == 0 && refundOrder.RefundState == 1 {
		_, err = r.session.Exec("update refund_order set refund_state=3,refunded_time=? where id=?", time.Now().Format(kit.DATETIME_LAYOUT), refundOrder.Id)
		glog.Error(kit.JsonEncode(params), "，应答售后单，更新退款状态失败：", err)
	}

	if r.orderMain.OrderStatus == 10 {
		out.Message = "该订单是未支付订单"
		out.Error = out.Message
		return &out, nil
	}

	if r.orderMain.OrderStatus == 0 {
		//美团急速退款 会直接调用取消订单接口 导致确认请求过来时 无法处理 所以需要让美团的全额退款通过
		if !(refundOrder.RefundState == 1 && refundOrder.FullRefund == 1 && refundOrder.ChannelId == ChannelMtId) {
			out.Message = "该订单订单已取消"
			out.Error = out.Message
			return &out, nil
		}
	}

	//电商的订单商家可以选择弃货，所以终审不需要判断物流信息
	//美团平台退货不用输入物流信息
	if params.ResultType == 1 && refundOrder.RefundState == 6 && len(refundOrder.ExpressNum) <= 0 &&
		refundOrder.ChannelId != ChannelJddjId && refundOrder.ChannelId != ChannelMallId && refundOrder.ChannelId != ChannelMtId &&
		r.orderMain.IsVirtual == 0 {
		out.Message = "请确认顾客在小程序填写快递单号后再审核"
		out.Error = out.Message
		return &out, nil
	}

	//查询退款日志是否存在，是不是已经执行过本次操作了
	if refundOrder.RefundState != 6 && r.orderMain.ChannelId != ChannelMtId && r.orderMain.ChannelId != ChannelJddjId {
		logIsHave := 0
		r.session.SQL("select 1 from refund_order_log where refund_sn=? and res_type=?", refundOrder.RefundSn, params.ResultTypeNote).Get(&logIsHave)
		//已经操作过的，就直接返回成功了
		if logIsHave == 1 {
			glog.Info(params.RefundOrderSn, "，订单的本次操作已经处理过了")
			out.Message = "订单的本次操作已经处理过了"
			out.Code = 200
			return &out, nil
		}
	}
	if params.ActivityPtAmount > 0 {
		_, err = r.session.Exec("update refund_order set activity_pt_amount=? where id=?", params.ActivityPtAmount, refundOrder.Id)
		if err != nil {
			r.session.Rollback()
			glog.Error(kit.JsonEncode(params), "，更新退款单平台补贴失败：", err)
			out.Message = "更新平台补贴失败"
			out.Code = 200
			return &out, nil
		}

	}

	r.session.Begin()

	//申退款状态类型，参考值：1-同意；2-拒绝；3-只记录日志不做操作

	//同意退款
	if params.ResultType == 1 {
		//1整单退款
		if refundOrder.FullRefund == 1 {
			//申请类型:1为退款,2为退货，默认为1
			//整单退款 或者退货且初审已经通过
			if refundOrder.RefundType == 1 || (refundOrder.RefundType == 2 && refundOrder.RefundState == 6) {
				//渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家)
				//非第三方 退货且已经通过初审 则状态变为终审通过
				if (refundOrder.ChannelId == ChannelAwenId || refundOrder.ChannelId == ChannelMallId || refundOrder.ChannelId == ChannelDigitalHealth) && (refundOrder.RefundType == 2 && refundOrder.RefundState == 6) {
					refundStatus = 7
				}
				//如果已经支付的情况下
				if r.orderMain.OrderStatus > 10 {
					orderService := OrderService{}
					cancelOrderRequest := oc.CancelOrderRequest{
						CancelReason: "商家全额退款自动取消",
						OrderSn:      r.orderMain.OldOrderSn,
						IsRefund:     1,
						Refundsn:     refundOrder.RefundSn,
						OldRefundSn:  refundOrder.OldRefundSn,
					}
					//调用的是取消订单
					res, err := orderService.CancelOrder(context.Background(), &cancelOrderRequest)
					if err != nil {
						out.Message = "全额退款取消订单失败"
						out.Error = "全额退款取消订单失败，" + r.orderMain.OrderSn + "，" + err.Error()
						glog.Error(out.Error)
						return &out, nil
					}
					if res.Code != 200 {
						out.Message = "全额退款取消订单失败" + res.Error + res.Message
						out.Error = out.Message
						glog.Error(out.Error)
						return &out, nil
					}
				}
			}

			if refundOrder.RefundType == 2 && (refundOrder.RefundState == 1 || refundOrder.RefundState == 5) {
				glog.Info("应答售后单入参1.2, ", refundStatus, ", ", kit.JsonEncode(params))
				refundStatus = 6
				notifyType = "first-agree"
			}
			//2:部分退款
		} else {
			if refundOrder.RefundType == 1 || (refundOrder.RefundType == 2 && refundOrder.RefundState == 6) {
				//渠道id(1-阿闻到家，2-美团，3-饿了么,4-京东到家)
				if (refundOrder.ChannelId == ChannelAwenId || refundOrder.ChannelId == ChannelDigitalHealth) &&
					(refundOrder.RefundType == 2 && refundOrder.RefundState == 6) {
					refundStatus = 7
				}

				if refundOrder.RefundState == 6 {
					refundStatus = 3
				}

				//订单商品集合
				//对第三方 查询的是主单商品
				orderGoods := r.GetOrderProduct()
				if len(orderGoods) == 0 {
					out.Message = "售后单获取购买商品出错"
					out.Error = out.Message
					return &out, nil
				}
				//查询售后单商品 第三方也是主单商品
				refundOrderGoods := r.GetRefundOrderProduct(refundOrder.RefundSn)
				if len(refundOrderGoods) == 0 {
					out.Message = "售后单获取购买商品出错"
					out.Error = out.Message
					return &out, nil
				}

				//匹配商品
				for i, k := range orderGoods {
					for _, j := range refundOrderGoods {
						//不相等 直接跳过  如果相等往下走 如果是组合商品 怎么处理 diffSku(标识请勿删除)
						if k.SkuId != j.SkuId {
							continue
						}

						isChange := false
						switch refundOrder.ChannelId {
						//todo 美团存在DiscountPrice相等的情况
						case ChannelMtId:
							if k.SkuId == j.SkuId {
								isChange = true
							}
						case ChannelAwenId, ChannelMallId, ChannelDigitalHealth:
							if k.Id == j.OrderProductId {
								isChange = true
							}
						case ChannelElmId:
							if k.SubBizOrderId == j.SubBizOrderId {
								isChange = true
							}
							//如果是物竞天择的，京东走相同的逻辑
						case ChannelJddjId, 420:
							isChange = true
							//如果是物竞天择的，京东走相同的逻辑
						}

						if isChange {
							orderGoods[i].RefundNum += j.Quantity
							//虚拟商品 部分退款时只有阿闻渠道存在订单为虚拟商品的情况 第三方的虚拟订单会在组合商品里
							//如果第三方有虚拟订单的时候 同样得拆成单个虚拟单 以下判断只有阿闻会生效
							if orderGoods[i].ProductType == 2 {
								codes := GetValidOrderVerifyCodes(r.orderMain.OrderSn, 1)
								for index, code := range codes {
									//顺序的处理退款 如果超过了数量 则不再处理
									if index >= int(j.Quantity) {
										break
									}
									if _, err = r.session.ID(code.Id).Update(&models.OrderVerifyCode{
										VerifyStatus: 2,
									}); err != nil {
										r.session.Rollback()
										glog.Error(params.RefundOrderSn, ", 退款更新核销码状态出错1, ", err.Error())
										out.Message = "更新核销码退款状态失败"
										out.Error = err.Error()
										return &out, nil
									}
								}

								//剩余核销码全部退完时订单更新为取消状态
								//在部分被核销 剩余部分退款的情况  订单改为取消是否有问题？
								if len(codes) == int(j.Quantity) {
									if _, err = r.session.Where("order_sn=?", r.orderMain.OrderSn).Update(&models.OrderMain{
										OrderStatus: 0,
									}); err != nil {
										r.session.Rollback()
										glog.Error(params.RefundOrderSn, ", ", r.orderMain.OrderSn, ", 更新虚拟订单退款状态失败, ", err.Error())
										out.Message = "更新虚拟订单退款状态失败"
										out.Error = err.Error()
										return &out, nil
									}
								}
							}

							//第三方组合商品 下面所有的虚拟商品进行状态更新
							//todo 通过combineType去判断
							if k.ProductType == 3 && r.IsThirdOrder() {
								codes := GetVerifyCodesByGroupSkuId(k.OrderSn, k.SkuId, 1)
								if len(codes) > 0 {
									var ids []int64
									var childOrderSn []string
									//第三方订单需要退掉的虚拟商品数量 = 组合商品数量*虚拟商品在组合商品里的数量
									SkuNumMap := make(map[string]int)
									childOrderMap := make(map[string]struct{})
									for _, code := range codes {
										//第三方订单 且是组合的情况下 组合下的每个虚拟订单 都需要退掉组合商品的数量* 该sku在该组合中的数量（x.Quantity * group_item_num）
										quantity := int(j.Quantity * code.GroupItemNum)
										if _, has := SkuNumMap[code.SkuId]; has {
											SkuNumMap[code.SkuId] += 1
										} else {
											SkuNumMap[code.SkuId] = 1
										}
										if SkuNumMap[code.SkuId] > quantity {
											continue
										}
										ids = append(ids, code.Id)
										if _, ok := childOrderMap[code.OrderSn]; !ok {
											childOrderSn = append(childOrderSn, code.OrderSn)
											childOrderMap[code.OrderSn] = struct{}{}
										}
									}
									if _, err = r.session.In("id", ids).Update(&models.OrderVerifyCode{
										VerifyStatus: 2,
									}); err != nil {
										r.session.Rollback()
										glog.Error(params.RefundOrderSn, ", 退款更新核销码状态出错2, ", err.Error())
										out.Message = "更新核销码退款状态失败"
										out.Error = err.Error()
										return &out, nil
									}

									//当商品的数量小于等于 退款的数量时 更新虚拟子订单的子状态为取消状态
									if orderGoods[i].Number <= orderGoods[i].RefundNum {
										_, err = r.session.Exec("UPDATE order_main SET order_status_child = 30100 WHERE order_sn IN('" + strings.Join(childOrderSn, "','") + "')")
										if err != nil {
											r.session.Rollback()
											out.Message = "更新虚拟子订单为取消状态出错"
											out.Error = out.Message
											glog.Error("更新虚拟子订单为取消状态出错，", err, "，", params.RefundOrderSn, "，", kit.JsonEncode(orderGoods), childOrderSn)
											return &out, nil
										}
									}
								}

								// 更新主单组合商品下的子商品退款数量
								//todo 全退时需要更新
								_, err = r.session.Exec("UPDATE order_product SET refund_num = refund_num +(group_item_num * ?)  WHERE order_sn = ?  AND parent_sku_id=?", j.Quantity, orderGoods[i].OrderSn, k.SkuId)
								// 相应的子订单需要
								if err != nil {
									r.session.Rollback()
									out.Message = "更新组合商品子商品的退款数量出错"
									out.Error = out.Message
									glog.Error("更新组合商品子商品的退款数量出错，", err, "，", params.RefundOrderSn, "，", kit.JsonEncode(orderGoods))
									return &out, nil
								}
							}
							//更新订单的refund_num 退款的是主单就更新主单 退款的是子单就更新子单
							_, err = r.session.Exec("UPDATE order_product SET refund_num=? WHERE id=?", orderGoods[i].RefundNum, k.Id)
							if err != nil {
								r.session.Rollback()
								out.Message = "更新购买产品售后数量错误"
								out.Error = out.Message
								glog.Error("更新购买产品售后数量错误，", err, "，", params.RefundOrderSn, "，", kit.JsonEncode(orderGoods))
								return &out, nil
							}
							//跟新子单品

						} else {
							glog.Info(r.orderMain.OrderSn, "应答售后单:", refundOrder.ChannelId, isChange)
						}
					}
				}

				//是否全部退完了
				isAllRefund := true
				for _, product := range orderGoods {
					if product.Number > product.RefundNum {
						isAllRefund = false
						break
					}
				}

				orderDetail := GetOrderDetailByOrderSn(r.orderMain.OrderSn, "push_third_order,locked_stock")

				//子订单部分退款，如果已经全部退完，则把订单修改为已取消
				// todo 确认全部退款的情况 第三方与阿闻子订单父订单订单状态的更新情况 -1026
				if isAllRefund {
					if _, err = r.session.ID(r.orderMain.Id).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
						OrderStatus:      0,
						OrderStatusChild: 20107,
						CancelReason:     "商家退款自动取消",
						CancelTime:       time.Now(),
					}); err != nil {
						r.session.Rollback()
						out.Message = "更新订单信息失败！"
						out.Error = err.Error()
						glog.Error("取消订单,更新订单信息失败！", params.RefundOrderSn, err.Error())
						return &out, nil
					}
					//第三方订单修改所有的子订单状态
					if r.orderMain.ChannelId == ChannelMtId || r.orderMain.ChannelId == ChannelJddjId || r.orderMain.ChannelId == ChannelElmId {
						if _, err = r.session.Where("parent_order_sn = ?", r.orderMain.OrderSn).
							Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
							OrderStatus:      0,
							OrderStatusChild: 20107,
							CancelReason:     "商家退款自动取消",
							CancelTime:       time.Now(),
						}); err != nil {
							r.session.Rollback()
							out.Message = "更新子订单信息失败！"
							out.Error = err.Error()
							glog.Error("取消订单,更新子订单信息失败！", params.RefundOrderSn, err.Error())
							return &out, nil
						}
						//虚拟子订单状态更新
						if _, err = r.session.Where("parent_order_sn = ? AND  is_virtual =1 ", r.orderMain.OrderSn).
							Cols("order_status_child").Update(&models.OrderMain{
							OrderStatusChild: 30100,
						}); err != nil {
							r.session.Rollback()
							out.Message = "更新子订单信息失败！"
							out.Error = err.Error()
							glog.Error("取消订单,更新子订单信息失败！", params.RefundOrderSn, err.Error())
							return &out, nil
						}

						//更新组合商品子商品的退款数量
						_, err = r.session.Exec("UPDATE order_product SET refund_num = number WHERE order_sn = ? AND parent_sku_id !=''", r.orderMain.OrderSn)
						if err != nil {
							r.session.Rollback()
							out.Message = "更新组合商品子商品数量"
							out.Error = out.Message
							glog.Error("更新组合商品子商品数量出错，", err, "，", r.orderMain.OrderSn)
							return &out, nil
						}
					}

					// 正向单没有推送到第三方的直接释放库存
					if orderDetail.PushThirdOrder == 0 && orderDetail.LockedStock > 0 && r.orderMain.AppChannel != SaasAppChannel {
						if err = FreedStockByParentOrderSn(r.orderMain.GetParentOrderSn()); err != nil {
							glog.Error("订单取消释放库存失败", r.orderMain.GetParentOrderSn(), " ", err.Error())
						}
						// 清除在途库存
						DeleteTransportationInventory(r.orderMain.GetParentOrderSn(), r.orderMain.ChannelId)
					}
				}

				//第三方渠道退款推送子龙或全渠道
				if r.orderMain.ChannelId != ChannelAwenId && r.orderMain.ChannelId != ChannelMallId && r.orderMain.ChannelId != ChannelDigitalHealth && r.orderMain.AppChannel != SaasAppChannel {
					// 通知第三方退款,阿闻退款后再推全渠道退货
					if orderDetail.PushThirdOrder > 0 && r.orderMain.IsVirtual == 0 {
						has := true
						//1整单退款 2部分退款
						if refundOrder.FullRefund == 2 {
							has = false
						}
						// 通知退货
						err = r.AllChannelRefundNotice(refundOrder.RefundSn, has, params.ResultType, false, r.session)
						if err != nil {
							glog.Error(r.orderMain.OldOrderSn, "-", refundOrder.RefundSn, "-退款,通知第三方系统失败！", "，", err)
						}
					}
				}
				//saas宠物退款成功，退返库存，只有上面的流程退款订单更新数据，在此处调用，否则会查询不到数据
				if refundOrder.AppChannel == SaasAppChannel {
					//更新父订单状态
					if _, err = r.session.Where("order_sn=?", r.orderMain.ParentOrderSn).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
						OrderStatus:      0,
						OrderStatusChild: 20107,
						CancelReason:     "商家退款自动取消",
						CancelTime:       time.Now(),
					}); err != nil {
						r.session.Rollback()
						out.Message = "更新订单信息失败！"
						out.Error = err.Error()
						glog.Error("取消订单,更新订单信息失败！", params.RefundOrderSn, err.Error())
						return &out, nil
					}
					go func() {
						stockRes := RefundStock(refundOrder.RefundSn, refundOrder.OrderSn, refundOrder.ChannelId)
						if stockRes.Code != 200 {
							glog.Errorf("退款成功，退返库存失败，refundSn:%s, orderSn:%s, message:%s", refundOrder.RefundSn, refundOrder.OrderSn, stockRes.Msg)
						}
					}()
				}
			} else {
				refundStatus = 6
				notifyType = "first-agree"
			}
		}
		//拒绝退款
	} else if params.ResultType == 2 {
		notifyType = "reject"
		refundStatus = 2
		//退款退货 且在退款中  通知类型首次拒绝
		if refundOrder.RefundType == 2 && refundOrder.RefundState == 1 {
			notifyType = "first-reject"
		}

		//第三方更新退款商品的实际退款数量字段为0
		if refundOrder.ChannelId != ChannelAwenId && refundOrder.ChannelId != ChannelMallId && refundOrder.ChannelId != ChannelDigitalHealth {
			_, err = r.session.Exec("update refund_order_product set tkcount=0 where refund_sn=? ", refundOrder.RefundSn)
			if err != nil {
				r.session.Rollback()
				out.Error = params.OrderId + "，修改退款数量tkcount出错，" + err.Error()
				glog.Error(out.Error)
				return &out, nil
			}
			_, err = r.session.Exec("update refund_order_third_product set tkcount=0 where refund_sn=? ", refundOrder.RefundSn)
			if err != nil {
				r.session.Rollback()
				out.Error = params.OrderId + "，修改退款数量tkcount出错1，" + err.Error()
				glog.Error(out.Error)
				return &out, nil
			}
		}
	} else { //只插入日志不做处理
		refundStatus = 5
		if find := strings.Contains(params.OperationType, "拒绝"); find {
			notifyType = "reject"
		}
	}

	//更新退款单的状态,退款完成更新退款完成时间
	if refundStatus == 3 {
		_, err = r.session.Exec("update refund_order set refund_state=?,refunded_time=? where refund_sn=?", refundStatus, time.Now().Format(kit.DATETIME_LAYOUT), refundOrder.RefundSn)
	} else {
		_, err = r.session.Exec("update refund_order set refund_state=? where refund_sn=?", refundStatus, refundOrder.RefundSn)
	}
	if err != nil {
		r.session.Rollback()
		glog.Error(params.RefundOrderSn, "，更新售后单状态失败，", err)
		out.Error = "更新售后单状态失败"
		return &out, nil
	}

	refundOrderLog := SetRefundOrderLog(refundOrder.OldOrderSn, params.Reason, "", "2", params.OperationType, notifyType,
		params.ResultTypeNote, refundOrder.RefundSn, params.OperationUser)

	_, err = r.session.Insert(refundOrderLog)
	if err != nil {
		r.session.Rollback()
		glog.Error(params.RefundOrderSn, "，售后单应答，回调插入记录出错，", err)
		out.Message = "售后单应答，回调插入记录出错"
		out.Error = out.Message
		return &out, nil
	}

	//阿闻订单 且状态为退款完成或者终审完成时：退款
	if (refundOrder.ChannelId == ChannelAwenId || refundOrder.ChannelId == ChannelDigitalHealth) && (refundStatus == 3 || refundStatus == 7) {
		glog.Info("售后单调用退款支付接口：", refundStatus, "，", kit.JsonEncode(refundOrder))
		if r.orderMain.AppChannel == SaasAppChannel && r.orderMain.PayMode == 8 {
			err = r.handleStoreCardRefund(r.session, r.orderMain)
			if err != nil {
				r.session.Rollback()
				glog.Error(r.orderMain.OldOrderSn, "，", params.RefundOrderSn, "，处理储值卡退款失败，", err)
				out.Message = "售后单应答，处理储值卡退款失败"
				out.Error = err.Error()
				return &out, nil
			}
		} else {
			res, err := r.RefundOrderPay(ctx, &oc.RefundOrderPayRequest{
				RefundOrderSn: params.RefundOrderSn,
				ResType:       "商家终审完成自动发起退款申请",
				OperationType: "商家终审完成自动发起退款申请",
			})

			if err != nil {
				r.session.Rollback()
				glog.Error(r.orderMain.OldOrderSn, "，", params.RefundOrderSn, "，调用退款支付接口失败，", err)
				out.Message = "售后单应答，退款支付失败"
				out.Error = err.Error()
				return &out, nil
			} else if res.Code != 200 {
				r.session.Rollback()
				glog.Error(r.orderMain.OldOrderSn, "，", params.RefundOrderSn, "，调用退款支付接口失败，", res.Message)
				out.Message = "售后单应答，退款支付失败"
				if res.Message == "支付中心退款处理中" {
					out.Message = res.Message
				}
				out.Error = res.Message
				return &out, nil
			}
		}
	}

	//商城推送oms
	if r.orderMain.ChannelId == ChannelMallId || r.orderMain.Source == 5 {
		//push oms
		orderDetail := GetOrderDetailByOrderSn(r.orderMain.OrderSn, "push_third_order")
		//通知第三方退款,阿闻退款后再推全渠道退货
		if orderDetail.PushThirdOrder > 0 && r.orderMain.IsVirtual == 0 {
			has := true
			//1整单退款 2部分退款
			if refundOrder.FullRefund == 2 {
				has = false
			}
			//通知退货
			err = r.AllChannelRefundNotice(refundOrder.RefundSn, has, params.ResultType, false, r.session)
			if err != nil {
				glog.Error("美团退款,通知第三方系统失败！", r.orderMain.OldOrderSn, "，", err)
			}
		}
	}

	r.session.Commit()

	go func() {
		//记录退款订单流转日志
		SaveOrderLog([]*models.OrderLog{{
			OrderSn: params.OrderId,
			LogType: models.StorePassFinalExamination,
		}})

		//数据中心，后台通知状态修改
		MessageUpdate(params.RefundOrderSn)

		//refundStatus ： 3 终审同意  6 初审同意  2 拒绝 5拒绝
		if r.orderMain.AppChannel != SaasAppChannel {
			subscribeOrder := []string{r.orderMain.OrderSn}
			refundOrderId := "" //跳转电商的订单详情
			upetDb := GetUPetDBConn()
			if r.orderMain.IsVirtual == 1 {
				parentOrder := GetOrderMainByOrderSn(r.orderMain.ParentOrderSn)
				if subscribeOrder[0] != parentOrder.OldOrderSn {
					subscribeOrder = append(subscribeOrder, parentOrder.OldOrderSn)
				}
				// 虚拟退款单需要退款单号
				refundOrderId = refundOrder.OldRefundSn
			} else {
				refundIdSlice, err := upetDb.Query("select refund_id from upet_refund_return where refund_sn=?;", refundOrder.OldRefundSn)
				if err != nil {
					glog.Error("查询电商的退款订单号返回结果报错：", err, r.orderMain.OrderSn, refundOrder.OldRefundSn)
				}
				glog.Info("查询电商的退款订单号返回结果：", kit.JsonEncode(refundIdSlice), r.orderMain.OrderSn, refundOrder.OldRefundSn)
				if len(refundIdSlice) > 0 {
					refundOrderId = string(refundIdSlice[0]["refund_id"])
				}
			}

			if r.orderMain.ChannelId == ChannelAwenId || r.orderMain.ChannelId == ChannelDigitalHealth {
				refundOrderId = strconv.FormatInt(refundOrder.Id, 10)
			}

			glog.Info("订单退款状态相关消息通知的参数信息：refundOrderId=", refundOrderId, "，OrderSn=", r.orderMain.OrderSn, "，refundStatus=", refundStatus)
			for _, s := range subscribeOrder {
				if refundStatus == 3 {
					go pushRefundSuccessTemplate(dto.PushSubscribeMessage{
						OrderSn: s,
						OrgId:   r.orderMain.OrgId,
					}, refundOrderId, cast.ToString(refundOrder.RefundType), refundOrder.RefundAmount, kit.GetTimeNow())
				} else if refundStatus == 6 {
					go pushRefundStatusTemplate(
						dto.PushSubscribeMessage{
							OrderSn: s,
							OrgId:   r.orderMain.OrgId,
						},
						refundOrderId,
						"商家同意退款",
						cast.ToString(refundOrder.RefundType),
						"您的售后订单已初审",
						refundOrder.RefundAmount)
				} else if refundStatus == 5 || refundStatus == 2 {
					refundSn := refundOrder.RefundSn
					if len(refundOrder.OldRefundSn) > 0 {
						refundSn = refundOrder.OldRefundSn
					}
					status := "审核不通过"
					// 用户取消也是调拒绝方法，不发通知
					if params.Reason != "用户取消" {
						go pushRefundFailTemplate(dto.PushSubscribeMessage{
							OrderSn: s,
							OrgId:   r.orderMain.OrgId,
						}, refundSn, refundOrderId, cast.ToString(refundOrder.RefundType), status, params.Reason)
					}
				}
			}
		}
	}()

	glog.Info("应答售后单结束：", kit.JsonEncode(params))
	//腾讯有数退款上报
	if r.orderMain.IsPushTencent == 1 {
		go AddReturnOrderToTencent(refundOrder.OrderSn)
	}

	// 发票-红冲操作
	if (refundStatus == 3 || refundStatus == 7) && refundOrder.AppChannel != SaasAppChannel { // 退款成功
		var iv InvoiceService
		go iv.RefundInvoice(ctx, &oc.RefundInvoiceRequest{
			RefundSn: refundOrder.RefundSn,
		})
		// 卡类型订单额外处理
		if r.orderMain.OrderType == 17 || r.orderMain.OrderType == 18 {
			card := CardService{}
			go card.Refund(context.Background(), &oc.CardRefundReq{RefundSn: refundOrder.RefundSn})
		}
		// 退款成功更新健康值
		if r.orderMain.AppChannel != SaasAppChannel {
			go refundUpdateHealthVal(r.orderMain.MemberId, &refundOrder, r.orderMain.ShopName)
		}
	}

	out.Code = 200
	return &out, nil

}

// 退款成功更新健康值
func refundUpdateHealthVal(scrmUserId string, refundOrder *models.RefundOrder, shopName string) {
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	model := &cc.AddUserHealthValReq{
		UserId:       scrmUserId,
		HealthType:   5, // 退款
		Type:         2, // 支出
		Title:        "退款成功扣减健康值",
		Content:      "退款成功扣减健康值",
		OrderSn:      refundOrder.OrderSn,
		RefundAmount: refundOrder.RefundAmount,
		ShopId:       refundOrder.ShopId,
		ShopName:     shopName,
	}
	re, err := client.RPC.AddUserHealthVal(client.Ctx, model)
	glog.Info("用户线上消费退款扣减健康值(refundUpdateHealthVal)请求参数：", kit.JsonEncode(model))
	if err != nil {
		glog.Error("退款成功更新健康值失败, error: ", err)
	}
	if re.Code != 200 {
		glog.Error("退款成功更新健康值失败, message: ", re.Message)
	}
}

// 推送退款订单到腾讯有数
func AddReturnOrderToTencent(orderSn string) {
	glog.Info("腾讯有数退款推送：orderSn=", orderSn)
	//判断是否已经上报过
	redisClient := GetRedisConn()
	lockKey := "order-center:AddReturnOrderToTencent:" + orderSn
	if !redisClient.SetNX(lockKey, time.Now().Unix(), 180*time.Minute).Val() {
		glog.Info("腾讯有数退款 ：orderSn=", orderSn)
		return
	}
	glog.Info("腾讯有数退款推送2：orderSn=", orderSn)
	//查询退款订单商品信息
	orderProduct := GetRefundOrderProductByOrderSn(orderSn, "refund_order_product.*, refund_order.freight")
	if len(orderProduct) == 0 {
		glog.Error(orderSn, ": 退款商品不存在，")
		return
	}

	var products []*oc.RefundOrderProducts
	for _, v := range orderProduct {
		product := oc.RefundOrderProducts{
			Id:           v.Id,
			RefundSn:     v.RefundSn,
			SkuId:        v.SkuId,
			Quantity:     int64(v.Quantity),
			Tkcount:      int64(v.Tkcount),
			RefundAmount: v.RefundAmount,
			Freight:      v.Freight,
			ProductName:  v.ProductName,
		}

		products = append(products, &product)
	}

	//调腾讯有数接口上报订单
	var refundSn string
	var goods []*ext.ReturnGoodsInfo
	var returnFreightAmount float64 //退款运费金额
	refundTotalAmount := 0          //退款金额

	for _, v := range products {
		refundSn = v.RefundSn
		refundAmount, _ := strconv.ParseFloat(v.RefundAmount, 64)
		returnFreightAmount, _ = strconv.ParseFloat(v.Freight, 64)
		refundTotalAmount += kit.YuanToFen(refundAmount)
		goodsInfo := &ext.ReturnGoodsInfo{
			ExternalSkuId:     v.SkuId,
			SkuNameChinese:    v.ProductName,
			IsGift:            0,
			ExternalSpuId:     v.SkuId,
			SpuNameChinese:    v.ProductName,
			ReturnGoodsNum:    v.Tkcount,
			ReturnGoodsAmount: float32(refundAmount),
		}
		goods = append(goods, goodsInfo)
	}

	returnOrders := &ext.TencentReturnOrders{
		ExternalReturnOrderId: refundSn,
		ExternalOrderId:       orderSn,
		ReturnCreateTime:      strconv.FormatInt(time.Now().UnixNano()/1e6, 10),
		ReturnNum:             int64(len(goods)),
		ReturnAmount:          float32(kit.FenToYuan(refundTotalAmount)),
		ReturnFreightAmount:   float32(returnFreightAmount),
		ReturnOrderAmount:     float32(kit.FenToYuan(refundTotalAmount + kit.YuanToFen(returnFreightAmount))),
		ReturnOrderStatus:     "1290",
		StatusChangeTime:      strconv.FormatInt(time.Now().UnixNano()/1e6, 10),
		ReturnGoodsInfo:       goods,
	}

	var orders []*ext.TencentReturnOrders
	orders = append(orders, returnOrders)

	request := ext.AddReturnOrderToTencentReq{
		Orders: orders,
	}

	extClient := ext.GetExternalTencentClient()
	defer extClient.Close()
	result, _ := extClient.RPC.AddReturnOrderToTencent(extClient.Ctx, &request)
	if result != nil {
		if result.Code != 0 {
			glog.Error("腾讯有数退款订单推送失败，" + result.Error + "，" + kit.JsonEncode(request))
		}
	} else {
		glog.Error("腾讯有数退款订单推送失败，返回空")
	}

}

// 撤销售后单
func (r RefundOrderService) RefundOrderCancel(ctx context.Context, params *oc.RefundOrderCancelRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}

	r.session = GetDBConn().NewSession()
	defer r.session.Close()

	refundOrder := models.RefundOrder{}
	_, err := r.session.Where("refund_sn = ? or old_refund_sn = ? ", params.RefundOrderSn, params.RefundOrderSn).Get(&refundOrder)
	if err != nil {
		out.Message = "撤销售后单,查询退款单出错"
		out.Error = out.Message
		glog.Error("撤销售后单,查询退款单出错：" + params.RefundOrderSn + err.Error())
		return &out, nil
	}
	if len(refundOrder.OrderSn) <= 0 {
		out.Message = "售后单还不存在就接到了其他的回调处理"
		out.Error = out.Message
		glog.Error("撤销售后单,找不到退款单：" + params.RefundOrderSn)
		return &out, nil
	}
	if refundOrder.RefundState == 3 || refundOrder.RefundState == 7 || refundOrder.RefundState == 8 {
		out.Message = "售后单已处理成功，请不要重复处理"
		out.Error = out.Message
		glog.Error("撤销售后单,售后单不正确：" + params.RefundOrderSn)
		return &out, nil
	}

	r.session.Begin()

	_, err = r.session.Exec("update refund_order_product set tkcount = 0 where refund_sn = ?", refundOrder.RefundSn)
	if err != nil {
		r.session.Rollback()
		out.Message = "修改商品退款数量出错"
		out.Error = "修改商品退款数量出错" + err.Error()
		return &out, nil
	}
	_, err = r.session.Exec("update refund_order set refund_state=9 where refund_sn=?", refundOrder.RefundSn)
	if err != nil {
		r.session.Rollback()
		out.Code = 400
		out.Error = err.Error()
		return &out, nil
	}

	refundOrderLog := SetRefundOrderLog(refundOrder.OldOrderSn, params.Reason, "", "1", params.OperationType,
		"cancelRefund", params.ResType, refundOrder.RefundSn, params.OperationUser)
	//写入售后单记录表 refund_order_log
	isOk, err := r.session.Insert(refundOrderLog)
	if err != nil {
		r.session.Rollback()
		glog.Error("退款回调插入记录出错:" + refundOrder.RefundSn + err.Error())
		out.Message = "退款回调插入记录出错"
		out.Error = "退款回调插入记录出错" + refundOrder.RefundSn + err.Error()
		return &out, nil
	}
	if isOk == 0 {
		r.session.Rollback()
		out.Message = "退款回调log插入0条记录"
		out.Error = "退款回调log插入0条记录" + refundOrder.RefundSn
		return &out, nil
	}

	r.orderMain = new(models.OrderMain)
	_, err = r.session.Where("order_sn = ?", refundOrder.OrderSn).Get(r.orderMain)
	if err != nil {
		r.session.Rollback()
		out.Message = "退款查询订单详情失败"
		out.Error = "退款查询订单详情失败; err: " + err.Error()
		return &out, nil
	}

	r.session.Commit()

	out.Code = 200
	return &out, nil
}

// 调用退款支付接口
func (r RefundOrderService) RefundOrderPay(ctx context.Context, params *oc.RefundOrderPayRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("售后单调用退款支付接口入参：", kit.JsonEncode(params))

	if r.session == nil {
		r.session = GetDBConn().NewSession()
		defer r.session.Close()
	}

	refundOrder := models.RefundOrder{}
	//查询日志
	glog.Info("售后单调用退款支付查询退款单：", params.RefundOrderSn)
	isOk, err := r.session.SQL("select /*FORCE_MASTER*/ * from `refund_order` where refund_sn=?  and refund_state in(3,7,8) and `channel_id` in (1,9)", params.RefundOrderSn).Get(&refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error("售后单调用退款支付接口查询不到状态订单:" + params.RefundOrderSn + err.Error())
		} else {
			glog.Error("售后单调用退款支付接口查询不到状态订单:" + params.RefundOrderSn)
		}
		out.Message = "售后单调用退款支付接口查询不到状态订单"
		out.Error = out.Message
		return &out, nil
	}

	glog.Info("售后单调用退款支付查询订单：", params.RefundOrderSn, refundOrder.OrderSn)
	r.orderMain = new(models.OrderMain)
	isOk, err = r.session.Select("pay_sn").Where("order_sn=? and pay_sn != ''", refundOrder.OrderSn).Get(r.orderMain)
	if err != nil {
		glog.Error("售后单查询原订单失败，", err, ", ", params.RefundOrderSn)
		out.Message = "售后单查询原订单失败"
		out.Error = out.Message
		return &out, nil
	} else if !isOk {
		out.Message = "售后单未查询原订单支付单号"
		out.Error = out.Message
		return &out, nil
	}

	//如果存在退款记录，先查一下退款状态
	if len(refundOrder.PayRefundId) > 0 {
		refundAmount0, err := strconv.ParseFloat(refundOrder.RefundAmount, 64)
		refundQueryUrl := utils.PayCenterUrl + "/pay/refundquery"
		payRefundQueryRequest := dto.PayRefundQueryRequest{
			// saas-v1.0 todo 退款用的商户号 是否需要区分
			MerchantId:    utils.MerchantId,
			TransactionNo: refundOrder.PayRefundTranNo,
			ClientIP:      utils.GetClientIp(),
			RefundId:      refundOrder.PayRefundId,
			RefundAmt:     cast.ToInt32(kit.YuanToFen(refundAmount0)),
		}
		jsonData0 := kit.JsonEncode(payRefundQueryRequest)
		_, fromData0 := utils.PayCenterSign(jsonData0)
		result0, err := utils.HttpPost(refundQueryUrl, []byte(fromData0), utils.ContentTypeToForm)
		glog.Info("退款查询-支付中心返回结果：", refundOrder.OrderSn, string(result0))
		if err != nil {
			glog.Error("调用支付中心退款查询接口错误:"+jsonData0, err.Error())
		}
		var response0 dto.PayRefundQueryResponse
		err = json.Unmarshal(result0, &response0)
		if err != nil {
			glog.Error("调用支付中心退款查询接口错误:"+jsonData0+":"+string(result0), err)
			out.Message = "调用支付中心退款查询接口错误"
			out.Error = response0.Message
			return &out, nil
		}
		if response0.Code != 200 {
			glog.Error("调用支付中心退款查询接口错误:" + jsonData0 + ":" + response0.Message)
			out.Message = "调用支付中心退款查询接口错误"
			out.Error = response0.Message
			return &out, nil
		}
		if response0.Data.RspCode != "3" {
			out.Message = "支付中心退款处理中"
			out.Error = response0.Message
			return &out, nil
		}
	}

	glog.Info("售后单调用退款支付转换金额：", params.RefundOrderSn, refundOrder.OrderSn)
	refundAmount, err := strconv.ParseFloat(refundOrder.RefundAmount, 64)
	// saas-v1.0 todo 退款用的商户号 是否需要区分
	url := utils.PayCenterUrl + "/pay/refund"
	glog.Info("售后单调用退款支付处理参数：", params.RefundOrderSn, refundOrder.OrderSn)
	refundOrderPayDto := dto.RefundOrderPayDto{
		MerchantId:  utils.MerchantId,
		TradeNo:     r.orderMain.PaySn, //原来的销售单号
		RefundAmt:   kit.YuanToFen(refundAmount),
		CallbackUrl: utils.RefundPayCallbackUrl,
		BackParam:   params.RefundOrderSn,
		ExtendInfo:  params.RefundOrderSn,
		ClientIP:    utils.GetClientIp(),
		RefundId:    params.RefundOrderSn,
	}
	jsonData := kit.JsonEncode(refundOrderPayDto)
	glog.Info("退款参数：order_sn:"+refundOrder.OrderSn+",parent_order_sn:"+r.orderMain.ParentOrderSn, jsonData)
	//签名并获取 组装From 参数
	_, fromData := utils.PayCenterSign(jsonData)

	//获取结果
	result, err := utils.HttpPost(url, []byte(fromData), utils.ContentTypeToForm)
	//{"code":200,"message":"退款成功","data":{"backParam":"50000782604","callbackUrl":"http://awen.rvet.cn/external/refund-order/pay","clientIP":"***********","extendInfo":"50000782604","refundAmt":"13800","refundId":"20241222210218542070246098827287","rspCode":"1","rspMessage":"退款成功","transactionNo":"202412220386184207","merchantId":"AAECggEAcboPccMCaLS","sign":"B9822B602754C95C863629D5F1227425"},"baiduData":null}
	glog.Info("退款-支付中心返回结果：", refundOrder.OrderSn, string(result))
	if err != nil {
		glog.Error("调用支付中心退款支付接口错误:"+jsonData, err.Error())
	}
	var response dto.RefundOrderPayResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		glog.Error("调用支付中心退款支付接口错误:"+jsonData+":"+string(result), err)
		out.Message = "调用支付中心退款支付接口错误"
		out.Error = response.Message
		return &out, nil
	}

	if response.Code != 200 {
		glog.Error("支付中心退款支付接口返回不成功, ", jsonData, ", ", string(result), response)
		_, err = r.session.Exec("update refund_order set refund_state=8 where refund_sn=?", refundOrder.RefundSn)
		if err != nil {
			glog.Error(params.RefundOrderSn, ", 更新退款失败状态出错, ", err)
		}

		out.Message = "退款失败，" + response.Message
		return &out, nil
	}

	//记录支付中心退款单号
	_, err = r.session.Exec("update refund_order set pay_refund_id=?,pay_refund_tran_no=? where refund_sn=?",
		response.Data.RefundId,
		response.Data.TransactionNo,
		refundOrder.RefundSn)
	if err != nil {
		glog.Error(params.RefundOrderSn, ", 记录支付中心退款单号错误, ", err)
	}
	//腾讯有数退款上报
	if r.orderMain.IsPushTencent == 1 {
		glog.Info("腾讯有数售后退款上报：orderSn=", refundOrder.OrderSn)
		go AddReturnOrderToTencent(refundOrder.OrderSn)
	}

	go func() {
		// 失败自动退款的不减扣金额
		if params.ResType != "拼团失败自动退款" {
			refundAmount := cast.ToInt32(cast.ToFloat32(refundOrder.RefundAmount) * 100)
			UpdateCommunityGroupRefundPayAmount(refundOrder.OrderSn, refundAmount)
		}
	}()

	out.Code = 200
	out.Message = "调用退款支付成功"
	return &out, nil

}

// 回调退款支付接口
func (r RefundOrderService) RefundOrderPayCallBack(ctx context.Context, params *oc.RefundOrderPayCallBackRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("售后单回调退款支付接口入参：", kit.JsonEncode(params))
	defer glog.Info("售后单回调退款支付接口退出", kit.JsonEncode(params))

	//连接池勿关闭
	redisConn := GetRedisConn()

	lockCard := "lock:refund_pay_callBack:" + params.RefundOrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Second).Val()
	if !lockRes {
		out.Code = 400
		out.Message = "没有获取到锁"
		return &out, nil
	}
	//defer redisConn.Del(lockCard)

	r.session = GetDBConn().NewSession()
	defer r.session.Close()

	refundOrder := models.RefundOrder{}
	//查询日志
	isOk, err := r.session.SQL("select  * from `refund_order` where refund_sn=?  and refund_state in(3,7,8) and `channel_id` in (1,9)", params.RefundOrderSn).Get(&refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error(params.RefundOrderSn, " 售后单调用退款支付接口查询不到状态订单:"+err.Error())
		}
		if !isOk {
			glog.Error(params.RefundOrderSn, " 查不到 3 7 8 状态的退款订单:"+err.Error())
		} else {
			glog.Error("售后单调用退款支付接口查询不到状态订单:" + params.RefundOrderSn)
		}
		out.Message = "售后单调用退款支付接口查询不到状态订单"
		out.Error = out.Message
		return &out, nil
	}

	refundStatus := 3
	if params.RspCode == "3" {
		refundStatus = 8
	}
	if refundStatus == 3 {
		_, err = r.session.Exec("update refund_order set refund_state=?,refunded_time=? where refund_sn=?", refundStatus, time.Now().Format(kit.DATETIME_LAYOUT), refundOrder.RefundSn)
	} else {
		_, err = r.session.Exec("update refund_order set refund_state=? where refund_sn=?", refundStatus, refundOrder.RefundSn)
	}
	if err != nil {
		r.session.Rollback()
		glog.Error("调用支付中心退款，更新refund_order记录出错:" + params.RefundOrderSn + err.Error())
		out.Code = 400
		out.Error = err.Error()
		return &out, nil
	}

	if params.RspCode == "1" {
		refundOrderLog := SetRefundOrderLog(refundOrder.OldOrderSn, params.RspMessage, "", "2", "商家终审完成自动发起退款申请", "apply-refund-pay",
			"商家终审完成自动发起退款申请", params.RefundOrderSn, "")

		_, err = r.session.Insert(refundOrderLog)
		if err != nil {
			r.session.Rollback()
			glog.Error("调用支付中心退款，回调插入记录出错:" + params.RefundOrderSn + err.Error())
			out.Message = "调用支付中心退款，回调插入记录出错"
			out.Error = out.Message
			return &out, nil
		}
	}
	err = r.session.Commit()
	if err != nil {
		glog.Error(params.RefundOrderSn, " commit失败:"+err.Error())
		out.Message = "commit失败"
		out.Error = out.Message
		return &out, err
	}

	go func() {
		//记录订单流转日志
		SaveOrderLog([]*models.OrderLog{{
			OrderSn: refundOrder.OrderSn,
			LogType: models.RefundPaymentSuccess,
		}})

		r.RefundPushThird(refundOrder)

		if refundStatus == 3 && r.orderMain.AppChannel != SaasAppChannel {
			// 阿闻到家订单退款减积分
			IntegralOperation(refundOrder.RefundSn, false)
		}

	}()

	out.Code = 200
	out.Message = "调用退款支付成功"
	return &out, nil
}

// 特殊-京东到家退款需要商家审核流程，为了统一后台处理，当成售后单处理
func (r RefundOrderService) CancelOrderByJd(ctx context.Context, params *oc.CancelOrderByJdRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("京东到家退款需要商家审核流程(CancelOrderByJd)接口入参：", kit.JsonEncode(params))

	r.session = GetDBConn().NewSession()
	defer r.session.Close()

	orderMain := GetOrderMainByOldOrderSn(params.ExternalOrderSn)
	if orderMain.Id == 0 {
		out.Message = "订单未找到"
		out.Error = out.Message + params.ExternalOrderSn
		return &out, nil
	}

	if orderMain.OrderStatus == 20 && orderMain.OrderStatusChild == 20101 {
		orderService := OrderService{}
		cancelOrderRequest := oc.CancelOrderRequest{}
		cancelOrderRequest.CancelReason = "商家全额退款自动取消"
		cancelOrderRequest.OrderSn = orderMain.OldOrderSn
		cancelOrderRequest.IsRefund = 1

		res, err := orderService.CancelOrder(context.Background(), &cancelOrderRequest)
		if err != nil {
			out.Message = "全额退款取消订单失败"
			out.Error = out.Message + orderMain.OrderSn + err.Error()
			glog.Error(orderMain.OldOrderSn, ", (CancelOrderByJd)全额退款取消订单失败", err)
			return &out, nil
		}
		if res.Code != 200 {
			glog.Warning(orderMain.OldOrderSn, ", (CancelOrderByJd)全额退款取消订单失败"+kit.JsonEncode(res))
			out.Message = "全额退款取消订单失败"
			out.Error = out.Message + res.Error + res.Message
			return &out, nil
		}

		out.Code = 200
		out.Message = "取消订单成功"
		return &out, nil
	}

	apply := &oc.RefundOrderApplyRequest{
		RefundOrderSn:   GetSn("refund")[0],
		OrderId:         orderMain.OrderSn,
		ExternalOrderId: orderMain.OldOrderSn,
		Reason:          "用户申请取消订单",
		RefundType:      1,
		ShopId:          orderMain.ShopId,
		FullRefund:      1,
		RefundAmount:    float32(kit.YuanToFen(orderMain.Total)),
		OperationUser:   "京东用户",
		ResType:         "等待处理中",
		ApplyOpUserType: "1",
		OrderFrom:       5,
		OperationType:   "用户申请取消订单",
		ChannelId:       ChannelJddjId,
		IsCancalOrder:   1,
	}
	apply.RefundRemark = apply.Reason

	res, err := r.RefundOrderApply(nil, apply)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		out.Error = out.Message
		glog.Error(orderMain.OldOrderSn, ", CancelOrderByJd 售后单申请, ", err)
		return &out, nil
	}
	out.Code = res.Code
	out.Message = res.Message

	return &out, nil
}

// 特殊-京东到家售后单可以修改但是单号是同一个，不符合现有逻辑 需先删除，后重新插入
func (r RefundOrderService) DeleteRefundOrder(ctx context.Context, params *oc.DeleteRefundOrderRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("京东到家修改售后单删除原有数据(DeleteRefundOrder)接口入参：", kit.JsonEncode(params))

	db := GetDBConn()
	//订单信息
	refundOrder := models.RefundOrder{}
	//查询日志
	isOk, err := db.SQL("select  * from `refund_order` where refund_sn=? ", params.RefundSn).Get(&refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error("京东到家修改售后单删除原有数据(DeleteRefundOrder),查询不到对应的订单:" + params.RefundSn + err.Error())
		}
		out.Message = "京东到家修改售后单删除原有数据(DeleteRefundOrder)查询不到对应的订单"
		out.Error = "京东到家修改售后单删除原有数据(DeleteRefundOrder)查询不到对应的订单！" + params.RefundSn
		return &out, nil
	}
	if refundOrder.RefundState == 1 {
		glog.Error("(DeleteRefundOrder)删除refund_order时候"+params.RefundSn+",状态:", refundOrder.RefundState)
	}

	if refundOrder.RefundState == 2 || refundOrder.RefundState == 3 {
		out.Message = "改订单已经关闭。"
		glog.Error("(DeleteRefundOrder)删除refund_order时候：改订单已经关闭"+params.RefundSn+",状态:", refundOrder.RefundState)
		return &out, nil
	}

	session := db.NewSession()
	defer session.Close()
	_, err = session.Exec("DELETE  FROM  refund_order  WHERE refund_sn=? ", params.RefundSn)
	if err != nil {
		session.Rollback()
		out.Error = "删除售后单订单失败" + params.RefundSn + err.Error()
		out.Message = "删除售后单订单失败"
		glog.Error("(DeleteRefundOrder)删除refund_order失败" + params.RefundSn + err.Error())
		return &out, nil
	}
	_, err = session.Exec("DELETE  FROM  refund_order_product  WHERE refund_sn=? ", params.RefundSn)
	if err != nil {
		session.Rollback()
		out.Error = "删除售后单订单失败" + params.RefundSn + err.Error()
		out.Message = "删除售后单订单失败"
		glog.Error("(DeleteRefundOrder)删除refund_order_product失败" + params.RefundSn + err.Error())
		return &out, nil
	}
	_, err = session.Exec("DELETE  FROM  refund_order_log  WHERE refundid=? ", params.RefundSn)
	if err != nil {
		session.Rollback()
		out.Error = "删除售后单订单失败" + params.RefundSn + err.Error()
		out.Message = "删除售后单订单失败"
		glog.Error("(DeleteRefundOrder)删除refund_order_log失败" + params.RefundSn + err.Error())
		return &out, nil
	}
	session.Commit()
	out.Code = 200
	out.Message = "取消订单成功"
	return &out, nil
}

// 异步接收 售后单 请求参数
func (r RefundOrderService) SaveMtRefundOrderData(ctx context.Context, params *oc.OrderRetrunRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("售后单(SaveMtRefundOrderData)：", paramsJson)

	//连接池勿关闭
	db := GetDBConn()

	out := oc.BaseResponse{Code: 200, Message: "ok"}
	//  判定如果不是申请退款就需要先查询是否有申请退款落地
	//todo 确认为什么代运营的门店申请退款NotifyType字段为空
	//上面一条，是因为美团可退款退货门店，返回的是空，美团官方给的文档表示参考status字段，目前不处理

	//连接池勿关闭
	redisConn := GetRedisConn()

	lockCard := "lock:order_refund_data_i:" + params.RefundId
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		out.Code = 400
		out.Message = "ok"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	//如果状态是已经申述，那么退款单状态改成6
	if strings.Contains(params.ResType, "已申诉") {
		glog.Info("收到申述:", params.ResType)
		refund_state := 1
		if strings.Contains(params.ResType, "终审") {
			refund_state = 6
		}
		//查询退款单号
		refundOrder := models.RefundOrder{}
		isOk, err := db.SQL("select  * from `refund_order` where old_refund_sn=?  ",
			params.RefundId).Get(&refundOrder)
		if err != nil || !isOk {
			out.Message = "申述未找到订单"
			out.Error = out.Message
			return &out, nil
		}

		_, err = db.Exec("update refund_order set refund_state=?,refunded_time=? where old_refund_sn=?", refund_state, time.Now().Format(kit.DATETIME_LAYOUT), params.RefundId)
		if err != nil {
			glog.Error("售后单(SaveRefundOrderData)INSERT-ERROR:"+params.RefundId, err.Error())
			out.Message = "接收美团数据 插入记录出错"
			return &out, nil
		}

		// 记录退款单推送脚印
		refundLog := &models.RefundOrderLog{
			RefundSn:      refundOrder.RefundSn,
			OldOrderSn:    params.OrderId,
			Ctime:         time.Now(),
			Operationer:   params.Operationer,
			NotifyType:    params.NotifyType,
			OperationType: params.ResType,
			ResType:       params.ResType,
		}

		db.Insert(refundLog)

		return &out, nil
	}

	if params.ResType == "终审已同意" {

		//查询退款单号
		refundOrder := models.RefundOrder{}
		isOk, err := db.SQL("select  * from `refund_order` where old_refund_sn=?  and refund_state in(1,5)",
			params.RefundId).Get(&refundOrder)
		if err != nil {
			//没有找到1或者5的退款单不处理
			glog.Error("查询退款单号出错:", err.Error(), params.RefundId)
		}
		if isOk {
			//如果美团告诉我们终审通过的时候，我们还在初审状态，那么直接改转态到再处理
			_, err = db.Exec("update refund_order set refund_state=?,refunded_time=? where old_refund_sn=?", 6, time.Now().Format(kit.DATETIME_LAYOUT), params.RefundId)
			if err != nil {
				glog.Error("售后单(SaveRefundOrderData)INSERT-ERROR:"+params.RefundId, err.Error())
				out.Message = "接收美团数据 插入记录出错"
				return &out, nil
			}
		}

	}

	if params.NotifyType != "part" && params.NotifyType != "apply" && params.NotifyType != "" {
		strSql := "SELECT  *  FROM `refund_order_mt_data` a  WHERE a.`refund_sn`=?  AND a.`notify_type` IN ('apply') "
		//订单信息
		orderInfo := models.RefundOrderMtData{}
		//查询日志
		orderIsOk, err := db.SQL(strSql, params.RefundId).Get(&orderInfo)
		if err != nil || !orderIsOk {
			out.Code = 400
			if err != nil {
				glog.Error("售后单(SaveRefundOrderData),查询不到对应的订单:" + params.RefundId + err.Error())
			}
			out.Message = "查询不到对应的售后单"
			out.Error = "查询不到对应的售后单！" + params.RefundId
			return &out, nil
		}
	}

	model := models.RefundOrderMtData{
		RefundSn:      params.RefundId,
		OrderSn:       params.OrderId,
		ProcessStatus: 1,
		Data:          paramsJson,
		NotifyType:    params.NotifyType,
	}
	if params.NotifyType == "part" || params.NotifyType == "apply" {
		model.NotifyType = "apply"
	}
	isOk, err := db.Insert(model)
	if err != nil {
		glog.Error("售后单(SaveRefundOrderData)INSERT-ERROR:"+params.RefundId, err.Error())
		out.Message = "接收美团数据 插入记录出错"
		return &out, nil
	}
	if isOk == 0 {
		out.Message = "接收美团数据插入0条记录"
		out.Error = "接收美团数据插入0条记录" + params.RefundId
		return &out, nil
	}

	go TimingProcessingMtRefundOrderData(true)
	return &out, nil
}

// 获取商品对应子订单信息
func (r RefundOrderService) SplitOrderRefundGoods(ctx context.Context, request *oc.RefundOrderApplyRequest) (*oc.SplitOrderRefundGoodsResponse, error) {
	out := new(oc.SplitOrderRefundGoodsResponse)
	out.Code = 200

	//连接池勿关闭
	db := GetDBConn()

	var orderProducts []models.OrderProduct

	//查询子单商品
	err := db.Table("order_product").
		Join("left", "order_main", "order_main.order_sn=order_product.order_sn").
		Where("order_main.parent_order_sn = ? ", request.OrderId).
		Select("order_product.*").
		Find(&orderProducts)

	if err != nil {
		out.Code = 400
		out.Message = "查询子订单报错"
		out.Error = err.Error()
		glog.Error("查询子订单报错：", err)
		return out, err
	}

	if len(orderProducts) <= 0 {
		out.Code = 400
		out.Message = "没有查询到订单"
		out.Error = err.Error()
		return out, nil
	} else {
		//子订单下的退款商品 子订单号=>商品信息
		orderRefundProductMap := map[string][]*oc.RefundOrderGoodsData{}
		orderProductMap := map[string][]models.OrderProduct{}
		//子订单号与商品的关系
		for _, product := range orderProducts {
			orderProductMap[product.OrderSn] = append(orderProductMap[product.OrderSn], product)
		}
		//从请求的商品中获取
		//如果存在多个skuId会不会有问题呢？
		var reqRefundNum int32
		for _, good := range request.RefundOrderGoodsData {
			strParentSku := cast.ToString(good.ParentSkuId)
			goodSkuStr := good.SkuId + ":" + strParentSku
			for _, product := range orderProducts {
				productSkuStr := product.SkuId + ":" + product.ParentSkuId
				//判断条件要改 改成skuId+组合商品id进行判断 v6.0添加 因为V6.0按照组合商品进行了拆单
				//当不同组合中有同一个商品 那么只使用skuId或者spuId进行判断 会导致其他组合的中的该商品会 对应到错误的订单号
				if goodSkuStr == productSkuStr {
					orderRefundProductMap[product.OrderSn] = append(orderRefundProductMap[product.OrderSn], good)
				}
			}
			reqRefundNum += good.Quantity
		}

		for orderSn, goodData := range orderRefundProductMap {
			var orderGood oc.SplitOrderRefundGoods
			orderGood.OrderSn = orderSn
			orderGood.RefundOrderGoodsData = goodData
			//计算总的退款金额 TotalRefundAmount
			for _, good := range goodData {
				orderGood.TotalRefundAmount = orderGood.TotalRefundAmount + cast.ToFloat32(good.RefundAmount)
				//循环子订单下所有的商品 （阿闻退款时更新的是子订单商品的refundNum）
				//只要退款商品 销售数量 > 本次退款+已经退款的数量 那么该订单的是否全部退款字段就为0
				for _, product := range orderProductMap[orderGood.OrderSn] {
					//此处的判断改为skuId+组合商品id进行判断
					if (product.SkuId == good.SkuId) && (product.Number > (good.Quantity + product.RefundNum)) {
						orderGood.IsAllRefund = 0
					}
				}
			}

			out.OrderGoods = append(out.OrderGoods, &orderGood)
		}

		//判断子订单下退款的商品是否全部退
		var refundNum int32
		if _, err = db.Table("order_product").Select("sum(number-refund_num) as refund_num").
			Join("left", "order_main", "order_main.order_sn=order_product.order_sn").
			Where("order_main.parent_order_sn = ? ", request.OrderId).Get(&refundNum); err != nil {
			glog.Errorf("查询子订单下退款的商品是否全部退出错：订单号：%s,err:%s", request.OrderId, err.Error())
		}

		if reqRefundNum == cast.ToInt32(refundNum) {
			out.OrderGoods[0].IsAllRefund = 1
		}

	}
	return out, nil
}

// 健康管理计划-退卡
func (r RefundOrderService) HealthPlanRefundOrder(ctx context.Context, params *oc.HealthPlanRefundOrderRequest) (*oc.BaseResponse, error) {

	glog.Info("健康管理计划-退卡接口入参：HealthPlanRefundOrder：", kit.JsonEncode(params))

	out := new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	//更新状态、记录日志
	db := GetDBConn()

	orderModel := models.Order{}
	ok, err := db.SQL("select order_sn from `order_meal` where ensure_code=? and batch_code=? and pet_id=? and user_id=?",
		params.EnsureCode, params.BatchCode, params.PetId, params.UserId).Get(&orderModel)
	if err != nil {
		out.Code = 400
		out.Message = "fail"
		return out, nil
	}

	if !ok {
		out.Message = "该订单不存在"
		out.Code = 400
		return out, nil
	}

	//grpcContext信息
	grpcContext := r.LoadGrpcContext(ctx)
	glog.Info("Context信息：", kit.JsonEncode(grpcContext))
	if grpcContext.Channel.ChannelId == 0 || grpcContext.Channel.UserAgent == 0 {
		out.Code = 400
		out.Message = "缺少订单来源或UserAgent"
		glog.Error("缺少订单来源或UserAgent:" + orderModel.OrderSn)
		return out, nil
	}

	//判断是否已经退过卡
	refundOrder := models.RefundOrder{}
	ok, err = db.SQL("select order_sn from `refund_order` where order_sn=?", orderModel.OrderSn).Get(&refundOrder)
	if err != nil {
		out.Code = 400
		out.Message = "退款订单查询错误"
		return out, nil
	}

	if ok {
		out.Message = "已经退过卡"
		out.Code = 400
		return out, nil
	}

	//判断订单支付时间是否大于2分钟
	orderInfo := new(models.OrderMain)
	//查询日志
	orderIsOk, err := db.SQL("select  * from `order_main` where order_sn=?", orderModel.OrderSn).Get(orderInfo)
	if err != nil || !orderIsOk {
		if err != nil {
			glog.Error("售后单申请,查询不到对应的订单:" + orderModel.OrderSn + err.Error())
		}
		out.Message = "查询不到对应的订单,或者是支付时间没到2分钟！"
		return out, nil
	}

	now := time.Now()
	d, _ := time.ParseDuration("-2m")
	d1 := now.Add(d)
	if orderInfo.PayTime.After(d1) {
		out.Code = 400
		out.Message = "申请售后单需要支付时间大于2分钟才能申请，请稍候"
		return out, nil
	}

	//保存数据
	refundsn := GetSn("refund")[0]

	session := db.NewSession()
	defer session.Close()
	session.Begin()
	//修改订单状态
	_, err = session.Exec("update `order_main` set order_status = 0, order_status_child=20107 where order_sn = ?", orderModel.OrderSn)
	if err != nil {
		session.Rollback()
		out.Message = "修改订单状态出错"
		return out, nil
	}

	//插入refund_order表
	time := time.Now()
	refundOrderModel := models.RefundOrder{}
	refundOrderModel.OrderSn = orderModel.OrderSn
	refundOrderModel.RefundSn = refundsn
	refundOrderModel.CreateTime = time
	refundOrderModel.RefundTypeSn = "仅退款"
	refundOrderModel.RefundType = 1
	refundOrderModel.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(params.RefundAmount)) //strconv.FormatFloat(float64(params.RefundAmount), 'f', 2, 64)
	refundOrderModel.RefundState = 3
	refundOrderModel.ChannelId = int32(grpcContext.Channel.ChannelId)
	refundOrderModel.FullRefund = 1
	refundOrderModel.RefundedTime = time

	//插入refund_order_log表
	refundOrderLogModel := models.RefundOrderLog{
		RefundSn:        refundsn,
		OldOrderSn:      orderModel.OrderSn,
		Ctime:           time,
		Money:           int(params.RefundAmount),
		ApplyOpUserType: "用户",
		ResType:         "0",
	}

	//插入refund_order_product表
	refundOrderProduct := models.RefundOrderProduct{
		RefundSn:    refundsn,
		ProductType: 1,
		//RefundAmount: strconv.FormatFloat(float64(params.RefundAmount), 'f', 2, 32),
		RefundAmount: fmt.Sprintf("%.2f", kit.FenToYuan(params.RefundAmount)),
	}
	_, err = session.Insert(&refundOrderModel, &refundOrderLogModel, &refundOrderProduct)
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = "保存数据失败"
		return out, nil
	}

	session.Commit()

	if params.RefundAmount > 0 {
		refundAmount, err := strconv.ParseFloat(refundOrderModel.RefundAmount, 64)
		url := utils.PayCenterUrl + "/pay/refund"
		refundOrderPayDto := dto.RefundOrderPayDto{
			MerchantId: utils.MerchantId,
			//原来的销售单号
			TradeNo:     orderInfo.PaySn,
			RefundAmt:   kit.YuanToFen(refundAmount),
			CallbackUrl: utils.RefundPayCallbackUrl,
			BackParam:   refundsn,
			ExtendInfo:  refundsn,
			ClientIP:    utils.GetClientIp(),
			RefundId:    refundsn,
		}
		jsonData := kit.JsonEncode(refundOrderPayDto)
		glog.Info("退款参数："+orderInfo.OrderSn, jsonData)
		//签名并获取 组装From 参数
		_, fromData := utils.PayCenterSign(jsonData)

		//获取结果
		result, err := utils.HttpPost(url, []byte(fromData), utils.ContentTypeToForm)
		if err != nil {
			glog.Error("调用支付中心退款支付接口错误:"+jsonData, err.Error())
		}
		var response dto.RefundOrderPayResponse
		err = json.Unmarshal(result, &response)
		if err != nil {
			glog.Error("调用支付中心退款支付接口错误:"+jsonData+":"+string(result), err)
			out.Message = "调用支付中心退款支付接口错误"
			return out, nil
		}
	}
	return out, nil
}

func (r RefundOrderService) UpdateOrderStatus(ctx context.Context, request *oc.UpdateOrderStatusRequest) (*oc.BaseResponse, error) {
	out := new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	//更新状态、记录日志
	db := GetDBConn()
	yesterTime := time.Now().AddDate(0, 0, -1).Format("2006-01-02 15:04:05")
	var orderModel []models.OrderMain
	err := db.SQL("select id from `order_main` a where a.order_type=6 and a.order_status=10 and a.create_time<?", yesterTime).Find(&orderModel)
	if err != nil {
		out.Error = err.Error()
		out.Code = 400
		out.Message = "订单查询错误"
		return out, nil
	}

	if len(orderModel) > 0 {
		for _, v := range orderModel {
			_, err = db.Exec("update `order_main` set order_status = 0, order_status_child=20107 where id = ?", v.Id)
			if err != nil {
				glog.Error("更新order订单出错:", err)
				out.Error = err.Error()
				out.Code = 400
				out.Message = "更新订单出错"
				return out, nil
			}
		}
	}

	return out, nil
}

// 获取退款订单商品信息请求
func (o *OrderService) GetRefundOrderProducts(ctx context.Context, in *oc.GetOneOrderRequest) (*oc.GetRefundOrderProductResponse, error) {
	out := oc.GetRefundOrderProductResponse{Code: 200}

	orderProduct := GetRefundOrderProductByOrderSn(in.OrderSn, "refund_order_product.*, refund_order.freight")
	if len(orderProduct) == 0 {
		out.Message = "商品不存在"
		return &out, nil
	}

	var products []*oc.RefundOrderProducts

	for _, v := range orderProduct {
		product := oc.RefundOrderProducts{
			Id:           v.Id,
			RefundSn:     v.RefundSn,
			SkuId:        v.SkuId,
			Quantity:     int64(v.Quantity),
			Tkcount:      int64(v.Tkcount),
			RefundAmount: v.RefundAmount,
			Freight:      v.Freight,
			ProductName:  v.ProductName,
		}

		products = append(products, &product)
	}

	out.RefundOrderProducts = products
	return &out, nil
}

func (r RefundOrderService) RefundOrderSnList(ctx context.Context, params *oc.RefundOrderSnListVo) (*oc.RefundOrderSnListResp, error) {
	resp := &oc.RefundOrderSnListResp{Code: 400}
	glog.Info("resp : ", kit.JsonEncode(params))
	db := GetDBConn()

	where := db.Table("order_main").
		Join("inner", "refund_order", "order_main.order_sn = refund_order.order_sn").
		Where("order_main.parent_order_sn = ? ", params.OrderSn)

	if params.RefundState > 0 {
		where.And("refund_state = ? ", params.RefundState)
	}

	err := where.Select("*").Find(&resp.Data)
	if err != nil {
		resp.Msg = "查询退款单列表失败"
		glog.Info("RefundOrderSnList： ", err.Error())
		return resp, nil
	}
	resp.Code = 200
	return resp, nil
}

// 手动全退的接口
func (r RefundOrderService) RepeatRefundOrderPay(ctx context.Context, params *oc.RefundOrderPayVo) (*oc.BaseResponse, error) {
	logPrefix := "手动全退的接口,"
	out := oc.BaseResponse{Code: 400}
	glog.Info(logPrefix+"RepeatRefundOrderPay售后单调用退款支付接口入参：", kit.JsonEncode(params))

	if r.session == nil {
		r.session = GetDBConn().NewSession()
		defer r.session.Close()
	}
	userNo := params.UserNo
	userName := params.UserName

	record := models.AbnormalOrderRecord{
		OperateType:   2,
		OperateReason: params.Reason,
		OrderSn:       params.OrderSn,
		UserNo:        userNo,
		UserName:      userName,
		Status:        1,
		IpAddr:        params.IpAddr,
	}
	//// 查询主单号是否为取消状态，是否为阿闻订单
	main := models.OrderMain{}
	ok, err := r.session.SQL("select * from dc_order.order_main om where order_sn =? and parent_order_sn=''", params.OrderSn).Get(&main)
	glog.Info(logPrefix+"main: ", main.OrderSn, " ", kit.JsonEncode(main))
	if err != nil {
		out.Message = "查询sql异常"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "未查询到主单信息，请检查是否是阿闻渠道订单"
		glog.Error(out.Message)
		return &out, nil
	}
	if main.ChannelId != ChannelAwenId && main.ChannelId != ChannelDigitalHealth {
		out.Message = "只处理阿闻渠道订单全退"
		glog.Error(out.Message)
		return &out, nil
	}
	if main.OrderStatus != 0 {
		out.Message = "主单不是取消状态,无法退款"
		glog.Error(out.Message)
		return &out, nil
	}

	pcc := pay.GetPayCenterClient()
	payInfo, err := pcc.RPC.PayQueryByOrderId(pcc.Ctx, &pay.PayQueryByOrderIdRequest{OrderId: params.OrderSn})
	if err != nil {
		out.Message = "查询支付信息出错 " + err.Error()
		return &out, nil
	}

	if payInfo.Status == 0 || payInfo.Status == 5 {
		out.Message = "用户未支付或者支付失败,不能进行退款操作"
		return &out, nil
	}
	if len(payInfo.OutTradeNo) <= 0 {
		out.Message = "没有查询到支付流水号outTradeNo"
		return &out, nil
	}
	if len(payInfo.TradeNo) <= 0 {
		out.Message = "没有查询到支付单号PaySn"
		return &out, nil
	}

	if payInfo.Status == 4 {
		out.Message = "正在退款中,不能再次进行退款"
		return &out, nil
	}
	var refundData []models.RefundOrder
	r.session.SQL("select * from dc_order.refund_order ro where  order_sn in( select order_sn from dc_order.order_main om"+
		" where  parent_order_sn =? ) and refund_state =3 ", params.OrderSn).Find(&refundData)

	if len(refundData) > 0 {
		out.Message = "退款失败，该功能只针对全额退款"
		return &out, nil
	}

	//补全第三方的订单信息
	if len(main.PaySn) <= 0 {
		main.PaySn = payInfo.TradeNo
		main.PayTime = time.Unix(int64(payInfo.PayTime), 0)
		main.PayAmount = int32(payInfo.PayPrice)
		main.PayMode = int32(payInfo.PayType)
		r.session.Exec("update dc_order.order_main  ro set pay_time = ?,pay_amount =?, pay_mode =?, pay_sn =?, is_pay= 1 where order_sn =?",
			main.PayTime, main.PayAmount, main.PayMode, main.PaySn, params.OrderSn)

		r.session.Exec("update dc_order.order_pay_notify  ro set pay_time = ?,pay_amount =?, pay_mode =?, pay_sn =? where order_sn =?",
			main.PayTime, main.PayAmount, main.PayMode, main.PaySn, params.OrderSn)
	}

	// 先调用电银的接口查询订单的状态 订单状态 0：未支付 1：已支付 2：部分退款 3：全部退款
	// 1：如果是未支付和全部退款的话直接返回不做处理
	// 2：1和2直接用总金额减去退款金额
	url := utils.PayCenterUrl + "/pay/query"
	refundOrderPayDto := dto.RefundOrderQueryDto{
		MerchantId: payInfo.MerchantId,
		TradeNo:    payInfo.OutTradeNo, //原来的销售单号
	}
	if main.AppChannel == cast.ToInt32(config.GetString("eshop_store_app_channel")) {
		refundOrderPayDto.AppId = 9
	}
	jsonData := kit.JsonEncode(refundOrderPayDto)
	glog.Info(logPrefix+"查询订单详情： ", main.OrderSn, " jsonData ", jsonData)
	//签名并获取 组装From 参数
	_, fromData := utils.PayCenterSign(jsonData)
	//fmt.Println("fromData", fromData)
	//获取结果
	result, err := utils.HttpPost(url, []byte(fromData), utils.ContentTypeToForm)

	glog.Info(logPrefix+"result : ", main.OrderSn, string(result))

	var res dto.QueryResult
	err = json.Unmarshal(result, &res)
	if err != nil {
		out.Message = "json格式化异常"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}
	glog.Info(logPrefix+"res : ", main.OrderSn, " ", kit.JsonEncode(res))

	if res.Code != 200 {
		out.Message = "查询退款单详情失败"
		glog.Error(out.Message, res.Message)
		return &out, nil
	}

	var pay_price = 0
	var refund_price = 0
	if len(res.ResData) > 0 {
		if res.ResData[0].PayStatus == 0 || res.ResData[0].PayStatus == 3 {
			out.Message = "订单查询为未支付或者已全部退款，无需操作"
			glog.Error(out.Message, res.Message)
			return &out, nil
		} else {
			pay_price = res.ResData[0].PayPrice
			refund_price = res.ResData[0].Refund

		}
	}
	glog.Info(logPrefix+"查询订单需要退款的金额： ", main.OrderSn, "  pay_price ", pay_price, " refund_price: ", refund_price)
	//有些储值卡的接口退款成功了但是退款单的装填没有改
	var refund_all_money = 0 // 已经全部退款的数据
	for i := range res.ResData {
		for i2 := range res.ResData[i].RefundDetail {
			refundDetail := res.ResData[i].RefundDetail[i2]
			if cast.ToInt(refundDetail.RefundStatus) == 1 { // 退款成功
				refund_all_money += refundDetail.RefundAmount
				//glog.Info("refund_all_money + = ", refund_all_money)
			}
			if cast.ToInt(refundDetail.RefundStatus) == 2 {
				out.Message = "该笔订单正在退款中，不能再次进行退款"
				return &out, nil
			}
		}
	}
	glog.Info(logPrefix+"refund_all_money : ", main.OrderSn, " price ", refund_all_money)
	if pay_price == refund_all_money {
		// 手动退款成功修改所有退款状态为已完成
		var orderSnList []string
		r.session.SQL(" select order_sn from dc_order.order_main om where  parent_order_sn = ? ", params.OrderSn).Find(&orderSnList)
		if len(orderSnList) > 0 {
			r.session.Where("refund_state != 3 ").In("order_sn", orderSnList).Update(&models.RefundOrder{RefundState: 3, RefundedTime: time.Now().Local()})
		}
		if err != nil {
			glog.Error("手动退款成功修改所有退款状态为已完成:", err.Error())
		}
		out.Message = "该笔订单已经退款，不能再次进行退款"
		glog.Info(logPrefix + out.Message)
		return &out, nil
	}
	if refund_all_money > 0 { // 如果上面不是针对的全额退款，这里直接拦截退出，有退过部分不让退了
		out.Message = "退款失败，该功能只针对全额退款"
		return &out, nil
	}
	// 阿闻渠道发起过配送的话不退运费
	var push_delivery = 0
	refund_amt := 0
	b, err := r.session.SQL("select push_delivery from dc_order.order_detail od where order_sn =?;", params.OrderSn).Get(&push_delivery)
	if err != nil || !b {
		out.Message = "未查询到order_detail的是否发过配送信息"
		glog.Error(out.Message)
		return &out, nil
	}
	if push_delivery == 1 {
		refund_amt = pay_price - refund_price - int(main.Freight)
	} else {
		refund_amt = pay_price - refund_price
	}
	glog.Info(logPrefix+"计算运费后需要退款的金额： ", main.OrderSn, " refund_amt ", refund_amt, " push_delivery: ", push_delivery)

	// 发起部分退款的操作
	glog.Info(logPrefix+"售后单调用退款支付转换金额：", params.OrderSn)
	urlRefund := utils.PayCenterUrl + "/pay/refund"
	glog.Info(logPrefix+"售后单调用退款支付处理参数：", params.OrderSn)
	refundOrderPay := dto.RefundOrderPayDto{
		MerchantId:  utils.MerchantId,
		TradeNo:     main.PaySn, //原来的销售单号
		RefundAmt:   refund_amt,
		CallbackUrl: utils.RefundPayCallbackUrl,
		BackParam:   "",
		ExtendInfo:  "",
		ClientIP:    utils.GetClientIp(),
	}
	encode := kit.JsonEncode(refundOrderPay)
	glog.Info(logPrefix+"退款参数：order_sn:"+main.OrderSn, encode)
	//签名并获取 组装From 参数
	_, fromData = utils.PayCenterSign(encode)

	//获取结果
	result, err = utils.HttpPost(urlRefund, []byte(fromData), utils.ContentTypeToForm)
	glog.Info(logPrefix+"退款-支付中心返回结果：", params.OrderSn, string(result))

	record.RefundAmount = cast.ToString(float64(refund_amt) / 100)
	record.RecordData = encode
	record.OrgId = int64(main.OrgId)
	r.UpdateOrInsertRefundRecord(record)

	if err != nil {
		out.Message = "发起post请求电银失败 "
		glog.Error(out.Message+jsonData, err.Error())
		return &out, nil
	}
	var json_iterator = jsoniter.ConfigCompatibleWithStandardLibrary
	valid := json_iterator.Valid(result)

	if valid {
		codeRes := json_iterator.Get(result, "code").ToInt32() // 用这个主要是它的错误返回居然和正常返回不一样，无法序列化，只能通过code先来判断一下
		if codeRes != 200 {
			msg := json_iterator.Get(result, "message").ToString()
			out.Message = "调用支付中心退款支付接口 " + msg
			glog.Error(out.Message, string(result))
			record.Detail = out.Message
			record.Status = 2
			r.UpdateOrInsertRefundRecord(record)
			return &out, nil
		}
	}

	var response dto.RefundOrderPayResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		glog.Error("json格式化异常:"+jsonData+":"+string(result), err)
		out.Message = "json序列化异常，调用支付中心退款支付接口错误"
		out.Error = response.Message
		return &out, nil
	}

	if response.Code != 200 { //
		glog.Error("支付中心退款支付接口返回不成功, ", jsonData, ", ", string(result), response)
		out.Message = "退款失败，" + response.Message
		return &out, nil
	}
	// 插入退款订单记录
	refundSn := response.Data.BackParam
	if refundSn == "" {
		refundSn = GetSn("refund")[0] // 重新生成
	}
	refundOrder := models.RefundOrder{
		OrderSn:         main.OrderSn,
		RefundSn:        refundSn,
		CreateTime:      time.Now(),
		RefundType:      1, // 仅退款
		RefundAmount:    cast.ToString(float64(refund_amt) / 100),
		RefundState:     3, // 退款成功
		ChannelId:       main.ChannelId,
		FullRefund:      1,
		RefundedTime:    time.Now(),
		PayRefundId:     response.Data.RefundId,
		PayRefundTranNo: response.Data.TransactionNo,
		PushThird:       1,
	}
	_, err = r.session.Insert(&refundOrder)
	if err != nil {
		glog.Error("插入退款订单记录失败:", err)
		// 这里不需要返回错误,因为退款已经成功了
	}

	//写入售后单记录表 refund_order_log
	refundOrderLog := models.RefundOrderLog{
		OldOrderSn:      main.OrderSn,
		RefundSn:        refundSn,
		Ctime:           time.Now(),
		CreateTime:      time.Now(),
		OperationType:   "手动退款",
		Money:           refund_amt,
		NotifyType:      "agree",
		ResType:         "2",
		ApplyOpUserType: "2",
		Reason:          params.Reason,
		Operationer:     userName,
	}
	_, err = r.session.Insert(&refundOrderLog)
	if err != nil {
		glog.Error(logPrefix+"插入售后单记录表失败:", err)
		// 这里不需要返回错误,因为退款已经成功了
	}
	// 手动退款成功修改所有退款状态为已完成
	var orderSnList []string
	r.session.SQL(" select order_sn from dc_order.order_main om where  parent_order_sn = ?", params.OrderSn).Find(&orderSnList)
	if len(orderSnList) > 0 {
		r.session.Where("refund_state != 3 ").In("order_sn", orderSnList).Update(&models.RefundOrder{RefundState: 3, RefundedTime: time.Now().Local()})
	}

	//腾讯有数退款上报
	if main.IsPushTencent == 1 {
		glog.Info(logPrefix+"腾讯有数售后退款上报：orderSn=", params.OrderSn)
		go AddReturnOrderToTencent(params.OrderSn)
	}

	out.Code = 200
	out.Message = "调用退款支付成功"
	record.Detail = out.Message
	record.Status = 3
	r.UpdateOrInsertRefundRecord(record)
	r.session.Exec("update dc_order.order_pay_notify  ro set deal_status =1 where order_sn =?", params.OrderSn)
	return &out, nil

}

func (s *RefundOrderService) UpdateOrInsertRefundRecord(record models.AbnormalOrderRecord) {
	glog.Info("record : ", kit.JsonEncode(record))
	// 退款写入记录表
	record.CreateTime = time.Now()

	var id = 0
	s.session.SQL("select id  from dc_order.abnormal_order_record aor where order_sn = ?  and operate_type = ? ;", record.OrderSn, record.OperateType).Get(&id)
	if id <= 0 {
		i, err := s.session.Insert(&record)
		glog.Info(" insert : ", i, err)
	} else {
		i, err := s.session.ID(id).Update(&record)
		glog.Info(" Update : ", i, err)
	}

}

// 查询操作记录
func (r RefundOrderService) RefundRecordList(ctx context.Context, params *oc.ExpressCompanyListRequest) (*oc.RefundRecordListResponse, error) {
	out := oc.RefundRecordListResponse{
		Code: 400,
	}

	conn := GetDBConn()
	session := conn.Table("abnormal_order_record").Alias("a").Where("a.status = 3").Where("a.org_id=?", params.OrgId)
	sessionCount := *session
	total, err := sessionCount.Count()

	if err != nil {
		out.Message = "查询操作记录异常"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}
	session.Select("a.*").OrderBy("a.create_time desc ").
		Limit(int(params.PageSize), int((params.Page-1)*params.PageSize)).Find(&out.Data)
	//fmt.Println(total, err)

	out.Code = 200
	out.Total = int32(total)
	return &out, nil
}

// 手动完成订单
func (r RefundOrderService) RefundOrderFinish(ctx context.Context, params *oc.RefundOrderPayVo) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{
		Code: 400,
	}
	logPrefix := fmt.Sprintf("RefundOrderFinish手动完成订单,%s", params.OrderSn)
	glog.Info(logPrefix, "入参为", kit.JsonEncode(params))

	if r.session == nil {
		r.session = GetDBConn().NewSession()
		defer r.session.Close()
	}

	//// 查询主单号是否为取消状态，是否为阿闻订单
	/**
	1: 先查询 pay-center上的PayStatus状态 0未支付 1：支付成功  2：部分退款 3：全部退款 4支付处理中  5：支付失败

	*/
	main := models.OrderMain{}
	ok, err := r.session.SQL("select * from dc_order.order_main om where order_sn =? and parent_order_sn=''", params.OrderSn).Get(&main)
	glog.Info(logPrefix, "main: ", kit.JsonEncode(main))
	if err != nil {
		out.Message = "查询sql异常"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "未查询到主单信息，请检查是否是阿闻渠道订单"
		glog.Error(logPrefix, out.Message)
		return &out, nil
	}
	if main.ChannelId != ChannelAwenId && main.ChannelId != ChannelDigitalHealth {
		if main.OrgId == 6 {
			out.Message = "只处理小程序订单完成"
		} else {
			out.Message = "只处理阿闻渠道订单完成"
		}

		glog.Error(logPrefix, out.Message)
		return &out, nil
	}

	var refundData []models.RefundOrder
	r.session.SQL("select * from dc_order.refund_order ro where  order_sn in( select order_sn from dc_order.order_main om"+
		" where  parent_order_sn =? ) and refund_state =3 ", params.OrderSn).Find(&refundData)

	if len(refundData) > 0 {
		out.Message = "用户已经部分或者全部退款，不能将订单状态变成已完成"
		return &out, nil
	}

	pcc := pay.GetPayCenterClient()
	payInfo, err := pcc.RPC.PayQueryByOrderId(pcc.Ctx, &pay.PayQueryByOrderIdRequest{OrderId: params.OrderSn, AllStatus: true})
	if err != nil {
		out.Message = "查询支付信息出错 " + err.Error()
		return &out, nil
	}
	if len(payInfo.TradeNo) <= 0 {
		out.Message = "用户未支付,不能将订单状态变成已完成"
		return &out, nil
	}
	if payInfo.Status == 0 || payInfo.Status == 5 {
		out.Message = "用户未支付或者支付失败,不能将订单状态变成已完成"
		return &out, nil
	}
	if payInfo.Status == 4 {
		out.Message = "该笔订单正在退款中，不能将订单状态变成已完成"
		return &out, nil
	}
	if payInfo.Status == 3 || payInfo.Status == 2 {
		out.Message = "该笔订单已经退款/或者部分退款，不能将订单状态变成已完成"
		return &out, nil
	}

	//补全第三方的订单信息
	if len(main.PaySn) <= 0 {
		main.PaySn = payInfo.TradeNo
		main.PayTime = time.Unix(int64(payInfo.PayTime), 0)
		main.PayAmount = int32(payInfo.PayPrice)
		main.PayMode = int32(payInfo.PayType)
		r.session.Exec("update dc_order.order_main  ro set pay_time = ?,pay_amount =?, pay_mode =?, pay_sn =?,is_pay= 1  where order_sn =?",
			main.PayTime, main.PayAmount, main.PayMode, main.PaySn, params.OrderSn)

		r.session.Exec("update dc_order.order_pay_notify  ro set pay_time = ?,pay_amount =?, pay_mode =?, pay_sn =? where order_sn =?",
			main.PayTime, main.PayAmount, main.PayMode, main.PaySn, params.OrderSn)
	}

	/**
		2：上面的这个状态去查询的时候有时不对，只能去查询电银的订单状态
		  主单的状态： PayStatus  	订单状态 0：未支付 1：已支付 2：部分退款 3：全部退款
	      退款单状态： refund_status 	退款状态 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	*/
	url := utils.PayCenterUrl + "/pay/query"
	refundOrderPayDto := dto.RefundOrderQueryDto{
		MerchantId: payInfo.MerchantId,
		TradeNo:    payInfo.OutTradeNo, //原来的销售单号

	}
	if main.AppChannel == cast.ToInt32(config.GetString("eshop_store_app_channel")) {
		refundOrderPayDto.AppId = 9
	}

	jsonData := kit.JsonEncode(refundOrderPayDto)
	glog.Info(logPrefix, "查询订单详情： ", main.OrderSn, " jsonData ", jsonData)
	//签名并获取 组装From 参数
	_, fromData := utils.PayCenterSign(jsonData)
	//fmt.Println("fromData", fromData)
	//获取结果
	result, err := utils.HttpPost(url, []byte(fromData), utils.ContentTypeToForm)

	glog.Info(logPrefix, "result : ", main.OrderSn, string(result))

	var res dto.QueryResult
	err = json.Unmarshal(result, &res)
	if err != nil {
		out.Message = "json格式化异常"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}
	glog.Info(logPrefix, "res : ", main.OrderSn, " ", kit.JsonEncode(res))

	if res.Code != 200 {
		out.Message = "查询退款单详情失败"
		glog.Error(out.Message, res.Message)
		return &out, nil
	}

	var pay_price = 0
	var refund_price = 0
	if len(res.ResData) > 0 { // 先通过返回的订单状态判断
		if res.ResData[0].PayStatus == 0 || res.ResData[0].PayStatus == 3 || res.ResData[0].PayStatus == 2 {
			out.Message = "订单查询为未支付或者部分退款或者全部退款，不能将订单状态变成已完成"
			glog.Error(out.Message, res.Message)
			return &out, nil
		} else {
			pay_price = res.ResData[0].PayPrice
			refund_price = res.ResData[0].Refund
		}
	}
	// 通过订单的支付金额和退款金额判断
	glog.Info(logPrefix, "pay_price : ", main.OrderSn, " price ", pay_price, " refund_price ", refund_price)
	if pay_price == refund_price {
		out.Message = "该笔订单已经退款，不能将订单状态变成已完成"
		glog.Info(out.Message)
		return &out, nil
	}

	var refund_all_money = 0 // 已经全部退款的数据
	for i := range res.ResData {
		for i2 := range res.ResData[i].RefundDetail {
			refundDetail := res.ResData[i].RefundDetail[i2]
			if cast.ToInt(refundDetail.RefundStatus) == 1 { // 退款成功
				refund_all_money += refundDetail.RefundAmount
				//glog.Info("refund_all_money + = ", refund_all_money)
			}
			if cast.ToInt(refundDetail.RefundStatus) == 2 {
				out.Message = "该笔订单有正在退款中的，不能将订单状态变成已完成"
				return &out, nil
			}
		}
	}
	// 通过退款单的明细判断
	glog.Info(logPrefix, "refund_all_money : ", main.OrderSn, " price ", refund_all_money)
	if pay_price == refund_all_money {
		out.Message = "该笔订单已经退款，不能将订单状态变成已完成"
		glog.Info(logPrefix, out.Message)
		return &out, nil
	}

	var data_list []models.OrderMain
	r.session.SQL("select * from dc_order.order_main om where order_sn = ? or parent_order_sn = ? ;", params.OrderSn, params.OrderSn).Find(&data_list)
	for i := range data_list {
		main := data_list[i]
		if main.OrderStatus != 0 {
			out.Message = "查询主单或者子单有状态是未取消的, 无法操作"
			return &out, nil
		}
	}

	//没有推过第三方的先推送第三方
	service := CommonService{
		session: r.session,
	}
	realOrder := new(models.Order)

	has, err := r.session.SQL("select a.*,b.push_third_order,b.expected_time,b.push_delivery,b.split_order_result from order_main a "+
		"inner join order_detail b on a.order_sn=b.order_sn where a.order_sn=? and a.is_virtual=0", params.OrderSn).Get(realOrder)
	if err != nil {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单出错, ", err)
		out.Message = "推送第三方失败：" + err.Error()
		return &out, nil
	}
	if !has {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单失败,未找到订单信息")
		out.Message = "推送第三方失败：未找到订单信息"
		return &out, nil
	}

	service.orderMain = realOrder.OrderMain
	//没有拆单的先拆单
	//查询出错可能导致两次拆单的问题
	if realOrder.Id > 0 && realOrder.SplitOrderResult != 1 {
		//	//1:拆分订单入库
		err := saveSplitOrder(service.orderMain)
		if err != nil {
			glog.Error(logPrefix, params.OrderSn, ", 拆单失败！", err)
			//拆分失败更新主订单状态为异常单
			_, err = r.session.Exec("update order_detail inner join order_main on order_detail.order_sn=order_main.order_sn set split_order_result = ?,split_order_fail_reason = ? where order_main.old_order_sn = ?", 2, err.Error(), params.OrderSn)
			if err != nil {
				out.Message = "拆单失败状态更新失败!" + err.Error()
				glog.Error(logPrefix, "拆单失败状态更新失败！：", params.OrderSn, " ", err.Error())
				return &out, nil
			}
		}
	}

	if realOrder.PushThirdOrder == 0 { // 没有推送第三方的发起第三方的推送
		glog.Info(logPrefix, "订单没有推送过第三方的推送第三方进行： ", kit.JsonEncode(service))
		//实物子订单推送第三方
		realOrderSon := &CommonService{
			orderMain: new(models.OrderMain),
			session:   r.session,
		}
		//需要发配送的实物子订单（查询强制走主库）
		ok, err := realOrderSon.session.Select("/*FORCE_MASTER*/ *").Where("parent_order_sn = ? and is_virtual = 0", params.OrderSn).Get(realOrderSon.orderMain)
		if err != nil {
			out.Message = "实物子订单查询错误" + err.Error()
			glog.Error("实物子订单查询错误：", params.OrderSn, " ", err.Error())
			return &out, nil
		}
		if !ok {
			out.Message = "没有查询到实物子订单"
			glog.Error("没有查询到实物子订单：", params.OrderSn)
			return &out, nil
		}
		err = realOrderSon.PushThirdOrder(true)
		if err != nil {
			out.Message = "推送第三方结束" + err.Error()

			_, err = realOrderSon.session.In("order_sn", realOrderSon.orderMain.ParentOrderSn, realOrderSon.orderMain.OrderSn).
				Cols("push_third_order_reason").Update(&models.OrderDetail{PushThirdOrderReason: err.Error()})
			if err != nil {
				glog.Error(realOrderSon.orderMain.OrderSn, ", ", "RefundOrderFinish手动完成订单那更新推送第三方结果！", err.Error())
			}

			return &out, nil
		}
	}

	// 更新主状态和子状态和更新时间
	_, err = r.session.Exec("update dc_order.order_main set order_status = 30, order_status_child =20106, confirm_time=now()   where parent_order_sn = ? or order_sn  = ?", params.OrderSn, params.OrderSn)
	if err != nil {
		out.Message = "更新主状态和子状态和更新时间失败"
		glog.Error(out.Message, err.Error())
		return &out, nil
	}

	userNo := params.UserNo
	//userName := info.UserInfo.UserName
	userName := params.UserName

	record := models.AbnormalOrderRecord{
		OperateType:   3,
		OperateReason: params.Reason,
		OrderSn:       params.OrderSn,
		UserNo:        userNo,
		UserName:      userName,
		Status:        3,
		IpAddr:        params.IpAddr,
		Detail:        "successful",
		OrgId:         int64(main.OrgId),
	}

	r.UpdateOrInsertRefundRecord(record)
	//插入log日志
	log := models.OrderLog{
		OrderSn:     params.OrderSn,
		LogType:     10,
		OperateUser: "",
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}
	r.session.Insert(&log)

	out.Code = 200
	return &out, nil
}

// 批量退配送费
func (r RefundOrderService) BatchRefundDeliveryFee(ctx context.Context, params *oc.BatchRefundDeliveryFeeRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{
		Code: 400,
	}
	// 下载excel
	req, err := http.NewRequest("POST", params.FileUrl, nil)
	if err != nil {
		out.Message = err.Error()
		return &out, nil
	}
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := utils.Client60Second.Do(req)
	if err != nil {
		out.Message = err.Error()
		return &out, nil
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return &out, nil
	}

	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		out.Message = err.Error()
		return &out, nil
	}

	var refundList []models.RefundOrderDelivery
	for i, row := range rows {
		if i == 0 {
			continue
		}
		orderMain, err := GetChildRealOrderByOrderSn(row[0])
		if err != nil {
			continue
		}
		refundReason := ""
		if len(row) >= 2 {
			refundReason = row[1]
		}
		if orderMain != nil && orderMain.Id > 0 {

			orderDelivery := models.RefundOrderDelivery{
				OrderSn:           orderMain.OrderSn,
				ParentOrderSn:     orderMain.ParentOrderSn,
				PaySn:             orderMain.PaySn,
				DeliveryFee:       int(orderMain.Freight - orderMain.FreightPrivilege),
				RefundDeliveryFee: 0,
				RefundReason:      refundReason,
				FailReason:        "",
				Status:            0,
				CreateNo:          params.UserNo,
				CreateName:        params.UserName,
				CreateIp:          params.UserIp,
				CreateTime:        time.Now(),
				PlaceTime:         orderMain.CreateTime,
			}

			if orderMain.ChannelId != 1 {
				orderDelivery.FailReason = "只有阿闻渠道的订单才能使用此功能退配送费"
				orderDelivery.Status = 2
			}
			//if orderMain.OrderStatus != 0 {
			//	orderDelivery.FailReason = "只有“已取消”的订单才能使用此功能退配送费"
			//	orderDelivery.Status = 2
			//}

			refundList = append(refundList, orderDelivery)
		}
	}

	conn := GetDBConn()
	_, err = conn.Insert(refundList)
	if err != nil {
		out.Message = "处理数据异常"
		return &out, nil
	}
	out.Code = 200
	return &out, nil
}

// GetRefundDeliveryFee 获取退配送费记录
func (r RefundOrderService) GetRefundDeliveryFee(ctx context.Context, params *oc.GerRefundDeliveryFeeRequest) (*oc.GerRefundDeliveryFeeResponse, error) {
	out := new(oc.GerRefundDeliveryFeeResponse)
	conn := GetDBConn().Table("refund_order_delivery").Alias("a").Join("left", "dc_order.order_main b", "a.order_sn=b.order_sn").Where("b.org_id=?", params.OrgId)
	if len(params.Keyword) > 0 {
		conn.Where("a.order_sn = ? or a.parent_order_sn = ?", params.Keyword, params.Keyword)
	}
	countSession := conn.Clone()
	defer countSession.Close()
	totalCount, err := countSession.Count()
	out.TotalCount = cast.ToInt32(totalCount)
	err = conn.Select("a.id,a.order_sn,a.parent_order_sn,a.pay_sn,delivery_fee,a.refund_delivery_fee,a.refund_reason,fail_reason,a.status,create_no,a.create_name,a.create_ip,a.place_time,a.create_time").OrderBy("a.create_time desc").Limit(int(params.PageSize), int((params.PageIndex-1)*params.PageSize)).Find(&out.Data)
	if err != nil {
		return nil, errors.New("查询数据出错")
	}
	for i, _ := range out.Data {
		out.Data[i].DeliveryFeeStr = cast.ToString(kit.FenToYuan(out.Data[i].DeliveryFee))
		out.Data[i].RefundDeliveryFeeStr = cast.ToString(kit.FenToYuan(out.Data[i].RefundDeliveryFee))
	}

	return out, nil
}

// ExportRefundDeliveryFee 导出退配送费记录
func (r RefundOrderService) ExportRefundDeliveryFee(ctx context.Context, params *oc.GerRefundDeliveryFeeRequest) (*oc.ExportRefundDeliveryFeeResponse, error) {
	//获取配送费记录数据
	var refundRecord []models.RefundOrderDelivery
	conn := GetDBConn("root:d&!89iCEGKOuVHkT@(*************:23306)/dc_order?charset=utf8mb4").Table("refund_order_delivery").Alias("a").Join("inner", "dc_order.order_main b", "a.order_sn=b.order_sn").Where("b.org_id=?", params.OrgId)
	if len(params.Keyword) > 0 {
		conn.Where("a.order_sn = ? or a.parent_order_sn = ?", params.Keyword, params.Keyword)
	}
	err := conn.Select("a.id,a.order_sn,a.parent_order_sn,a.pay_sn,a.delivery_fee,a.refund_delivery_fee,a.refund_reason,a.fail_reason,a.status,a.create_no,a.create_name,a.create_ip,a.place_time,a.create_time").OrderBy("a.create_time desc").Find(&refundRecord)
	if err != nil {
		return nil, errors.New("查询数据出错")
	}

	//组装excel数据
	nameList := []string{
		"导入订单时间", "父订单编码", "实物子订单编号", "下单时间", "订单配送费金额", "配送费退款金额", "退款状态", "退款理由", "退款失败原因",
		"操作人/ip地址",
	}
	sheetName := "Sheet1"
	f := excelize.NewFile()
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			f.SetCellValue(sheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			f.SetCellValue(sheetName, string(rune(65+i))+"1", nameList[i])
		}
	}

	n := "0"
	for k := range refundRecord {
		n = strconv.Itoa(k + 2)
		// 导入订单时间
		f.SetCellValue(sheetName, "A"+n, kit.GetTimeNow(refundRecord[k].CreateTime))
		// 父订单编号
		f.SetCellValue(sheetName, "B"+n, refundRecord[k].ParentOrderSn)
		// 实物子订单编号
		f.SetCellValue(sheetName, "C"+n, refundRecord[k].OrderSn)
		// 下单时间
		f.SetCellValue(sheetName, "D"+n, kit.GetTimeNow(refundRecord[k].PlaceTime))
		// 订单配送费金额
		f.SetCellValue(sheetName, "E"+n, cast.ToString(kit.FenToYuan(refundRecord[k].DeliveryFee)))
		// 配送费退款金额
		f.SetCellValue(sheetName, "F"+n, cast.ToString(kit.FenToYuan(refundRecord[k].RefundDeliveryFee)))
		statusName := "处理中"
		if refundRecord[k].Status == 1 {
			statusName = "退款成功"
		} else if refundRecord[k].Status == 2 {
			statusName = "退款失败"
		}
		// 退款状态
		f.SetCellValue(sheetName, "G"+n, statusName)
		// 退款理由
		f.SetCellValue(sheetName, "H"+n, refundRecord[k].RefundReason)
		// 退款失败原因
		f.SetCellValue(sheetName, "I"+n, refundRecord[k].FailReason)
		// 操作人\IP地址
		f.SetCellValue(sheetName, "J"+n, refundRecord[k].CreateName+"-"+refundRecord[k].CreateIp)
	}

	f.Save()

	//上传文件
	//将数据存入buff中
	var buff bytes.Buffer
	if err = f.Write(&buff); err != nil {
		return nil, errors.New("导出失败")
	}
	var name = "退配送费-导出数据(" + time.Now().Format("20060102150405") + ")" + ".xlsx"

	//生成文件
	fd, err := os.Create(name)
	if err != nil {
		return nil, errors.New("导出失败")
	}

	defer os.Remove(name)
	defer fd.Close()

	if err = f.Write(fd); err != nil {
		return nil, errors.New("导出失败")
	}

	//同步文件到七牛云
	url, err := utils.UploadExcelToQiNiu(name)
	if err != nil {
		return nil, errors.New("文件上传失败")
	}

	return &oc.ExportRefundDeliveryFeeResponse{
		FileUrl: url,
	}, nil
}

// handleStoreCardRefund 处理储值卡退款
func (s *RefundOrderService) handleStoreCardRefund(session *xorm.Session, order *models.OrderMain) error {
	// 1. 查询储值卡使用记录
	storeCardRecord := new(models.MStoreCardUseRecord)
	exists, err := session.Where("order_no = ? AND operate_type = ?", order.OrderSn, models.OperateTypeConsumption).Get(storeCardRecord)
	if err != nil {
		return fmt.Errorf("查询储值卡使用记录失败: %v", err)
	}

	// 如果没有储值卡使用记录，直接返回
	if !exists {
		return nil
	}

	operateInfo := models.NewOperateInfo(
		cast.ToInt(order.Id),
		order.OrderSn,
		cast.ToInt64(order.MemberId),
		order.MemberName,
	)
	// 1. 恢复储值卡余额
	if err = models.RestoreStoreCardAmount(session, storeCardRecord, operateInfo.OperatorId); err != nil {
		return fmt.Errorf("恢复储值卡余额失败: %v", err)
	}
	// 2. 添加储值卡退款记录
	if err = models.RefundStoreCard(session, storeCardRecord, operateInfo); err != nil {
		return fmt.Errorf("添加储值卡退款记录失败: %v", err)
	}

	return nil
}

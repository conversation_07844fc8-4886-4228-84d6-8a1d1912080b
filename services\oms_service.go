package services

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"strings"

	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"

	//"order-center/proto/pc"
	"order-center/utils"
	"strconv"
	"time"

	//"encoding/json"
	//"order-center/dto"
	//"order-center/models"
	pt "order-center/proto/oc"
	//"strconv"
	//"strings"
	//"time"
	//
	"github.com/maybgit/glog"
	//"github.com/spf13/cast"
)

type OmsService struct {
	CommonService
}

// 订单发货(提供给巨益调用)
// 思路：
// 1:循环巨益oms的发货回调单
// 2:判断是否拆单，比对每单发货商品数量和库里面的订单商品总数是否一致
// 3:调用李强写的通用方法更新订单发货状态
func (omss OmsService) OmsOrderDelivery(ctx context.Context, params *pt.OmsOrderDeliveryRequest) (*pt.OmsOrderDeliveryResponse, error) {
	//initqqd()
	var out pt.OmsOrderDeliveryResponse
	var err error
	logPrefix := fmt.Sprintf("OmsOrderDelivery-order-center====入参：%s", kit.JsonEncode(params))
	glog.Info(logPrefix)
	if len(params.Omsorders) > 0 {
		Engine := GetDBConn()
		for _, omsorder := range params.Omsorders {
			//如果订单是快递配送，并且后台已发货，这里不处理
			if has, _ := Engine.Table("order_express").Where("order_sn = ? AND delivery_from = 2", omsorder.Orderid).Exist(); has {
				out.Code = 400
				out.Message = "商家自配"
				continue
			}
			var issplit = 0 //是否拆单0未拆单，1拆单，默认0未拆单

			//重新定义通用发货mq数据
			var dispatch = dto.DispatchResponse{
				OrderId:       omsorder.Orderid,          //订单id
				LogisticsCode: omsorder.LogisticsCompany, //物流公司简写
				LogisticsNum:  omsorder.LogisticsCode,    //物流单号
				SendDate:      time.Now().Format("2006/01/02 15:04:05"),
				Freight:       "0",
				PackageInfo: dto.PackageInfo{
					PackageCode:   "",
					PackageName:   "",
					PackageDetail: []dto.PackageDetail{},
				},
			}

			//调用订单发货函数结构体
			var delp = dto.DeliverParam{
				Source:        1,
				OrderSn:       omsorder.Orderid,
				IsEntire:      issplit,
				DeliverDetail: []dto.DeliverDetail{},
			}

			var num int32 //传过来的订单商品数量
			for _, goods := range omsorder.Goodslist {
				num += goods.Stock
			}

			var products []models.OrderProduct
			if err = Engine.Where("order_sn=?", omsorder.Orderid).Find(&products); err != nil {
				glog.Error(logPrefix, "查询订单商品失败：", err.Error())
			}
			var allnum int32
			for _, item := range products {
				allnum += item.Number
			}
			//判断是否拆单,比对传过来的订单商品数量和库里面订单商品数量
			if num < allnum {
				issplit = 1
			}

			for _, nowpar := range omsorder.Goodslist {
				//inParmar := pc.NewSkuThirdResponse{SkuId: nowpar.Sku, ErpId: 2}
				//skuThird, err := pcClient.RPC.QuerySkuThirdQQd(ctx, &inParmar)
				//if err != nil {
				//	glog.Error("请求商品中心SKUID报错：", err.Error())
				//	return nil, err
				//}
				var packageDetail = dto.PackageDetail{
					GoodsID: nowpar.Sku,
					Num:     strconv.Itoa(int(nowpar.Stock)),
				}
				var DeliverDetail = dto.DeliverDetail{
					GoodsSku: nowpar.Sku,
					Num:      nowpar.Stock,
				}
				dispatch.PackageInfo.PackageDetail = append(dispatch.PackageInfo.PackageDetail, packageDetail)
				delp.DeliverDetail = append(delp.DeliverDetail, DeliverDetail)
			}

			isok, _ := OrderStatus(&delp)
			if !isok {
				glog.Error(logPrefix, "调用 发货函数失败："+kit.JsonEncode(delp))

			}

			express := &models.UpetExpress{}
			_, err := GetUPetDBConn().Where("e_code_kdniao=?", omsorder.LogisticsCompany).Get(express)

			oldOrderExpress := &models.OrderExpress{}
			Engine.Table("order_express").Where("order_sn=? and express_no=?", omsorder.Orderid, omsorder.LogisticsCode).Cols("id").Get(oldOrderExpress)
			orderExpress := &models.OrderExpress{
				OrderSn:      omsorder.Orderid,
				ExpressNo:    omsorder.LogisticsCode,
				ExpressCode:  omsorder.LogisticsCompany,
				ExpressName:  express.EName,
				DeliveryFrom: 1,
				Num:          num,
				DeliveryTime: time.Now(),
				CreateTime:   time.Now(),
			}
			if oldOrderExpress.Id > 0 {
				_, err = Engine.Where("id=?", oldOrderExpress.Id).Update(orderExpress)
				if err != nil {
					glog.Error(logPrefix, "更新OrderExpress订单发货记录失败："+kit.JsonEncode(orderExpress))
				}
			} else {
				_, err = Engine.Insert(orderExpress)
				if err != nil {
					glog.Error(logPrefix, "写入OrderExpress订单发货记录失败："+kit.JsonEncode(orderExpress))
				}
			}

			var c CommonService

			go c.PushOrderSubscribeMessage(omsorder.Orderid)

			mqInfo := new(models.MqInfo)
			mqInfo.Exchange = "ordercenter"
			mqInfo.Quene = "dc-sz-qqd-dispatch"
			mqInfo.Content = kit.JsonEncode(dispatch)

			mqInfo.Ispush = 0
			_, err = Engine.Insert(mqInfo)

			out.Code = 200
			out.Message = "OK"
			if err != nil {
				out.Code = 400
				out.Message = "写入数据库失败"
				glog.Error(logPrefix, "发货通知写入数据库失败：", err.Error()+"|"+mqInfo.Content)
			}
			glog.Info(logPrefix, "PushOrderStatusToTencent:1160 orderSn=", omsorder.Orderid)
			go PushOrderStatusToTencent(omsorder.Orderid, 0)
		}
	}

	return &out, err
}

// 售后订单同步（阿闻售后单推送到巨益）
// 思路：
// 1:循环所有退款单
// 2:组装成巨益oms退货请求实体
// 3:调用巨益的订单同步接口
func (omss OmsService) AfterOrderSynchronizeToOms(ctx context.Context, params *pt.AfterorderRequest) (*pt.AfterorderResponse, error) {
	var out pt.AfterorderResponse
	var err error

	glog.Info("退款订单同步oms进入请求参数：", kit.JsonEncode(params))
	myEngine := GetDBConn()

	inpar := dto.OmsAfterorderOrder{}

	var (
		priceTotal int32
		// ShopCode   = "JD01"
	)
	for _, x := range params.Orders {
		//统计退单商品数量,判断是否整单退
		isAllRefund := "N"
		var refundnum int32 //传过来的订单商品数量
		for _, goods := range x.Details {
			refundnum += cast.ToInt32(goods.Backqty)
		}
		//查询商品总数
		var products []models.OrderProduct
		err = myEngine.Select("id, number, sku_id, discount_price").
			Where("order_sn=?", x.Tid).Find(&products)
		if err != nil {
			return nil, err
		}
		var totalCount int32
		var productMap = make(map[string]models.OrderProduct)
		for _, p := range products {
			productMap[cast.ToString(p.Id)] = p
			totalCount = totalCount + p.Number
		}
		if refundnum == totalCount {
			isAllRefund = "Y"
		}

		//查询门店仓库编码
		parOrder := models.OrderMain{}
		_, err = myEngine.Where("order_sn=?", x.Tid).Get(&parOrder)
		if err != nil {
			glog.Error("订单添加，查询电商ID错误：", err.Error())
			return nil, err
		}
		var hasGoodReturn string
		if x.Aftsaletype == "JustRefund" {
			hasGoodReturn = "N"
		} else if x.Aftsaletype == "RefundAndGoods" {
			hasGoodReturn = "Y"
		}
		//判断是电商渠道且主体为3，则ShopCode读取配置文件
		// if parOrder.ChannelId == ChannelMallId && parOrder.ShopId == CSYMainId {
		// ShopCode = config.GetString("ShopCode")
		// }
		ShopCode := GetShopCodeByOrder(&parOrder)
		for _, product := range x.Details {
			num := cast.ToInt32(product.Backqty)
			priceTotal += productMap[product.Oid].DiscountPrice * num
			aitem := dto.RefundApplyOrders{
				ShopCode:         ShopCode,                                                   //店铺id
				MallRefundStatus: changeAfterOrderStatusToOms(x.Status),                      //退款申请状态
				AppliedTime:      x.Created,                                                  //申请时间
				HasGoodReturn:    hasGoodReturn,                                              //是否退货N/Y
				ModifyTime:       kit.GetTimeNow(),                                           //修改时间
				ExpressNo:        x.Logistbillcode,                                           //快递单号
				Quantity:         num,                                                        //申请数量
				Reason:           changeAfterOrderReasonToOms(x.Reasoncode, x.Aftsaleremark), //申请原因
				RefundId:         x.Rtid,                                                     //平台退款申请ID
				TradeId:          x.Tid,                                                      //平台交易号
				IsWhole:          isAllRefund,                                                //是否整单退款N/Y
				GoodsCode:        product.Outeriid,                                           //商品编码
				GoodsName:        product.Eshopskuname,                                       //商品名称
				SkuId:            product.Outeriid,                                           //规格编码
				SkuName:          product.Eshopskuname,                                       //规格名称
			}
			inpar.RefundApplyOrders = append(inpar.RefundApplyOrders, aitem)
		}
	}
	for i := 0; i < len(inpar.RefundApplyOrders); i++ {
		inpar.RefundApplyOrders[i].ApplyAmount = fmt.Sprintf("%.2f", float64(priceTotal)/100)
	}

	//订单同步
	glog.Info("退款订单同步oms请求参数：", kit.JsonEncode(inpar))
	respBytes, err := utils.OmsApi("greatonce.oms.refunds.synchronize", kit.JsonEncode(inpar), "json")

	if err != nil {

		if len(inpar.RefundApplyOrders) > 0 && inpar.RefundApplyOrders[0].MallRefundStatus != "WAIT_SELLER_AGREE" {
			check := CheckExist(inpar.RefundApplyOrders[0].RefundId, models.RedoTypeOMSOrderRefund)
			if check == false {
				redoTask := new(models.OrderRedoTask)
				redoTask.RedoType = models.RedoTypeOMSOrderRefund //重试类型 1：正向订单重推巨益OMS 2:退款订单重新推送巨益OMS
				redoTask.OrderSn = inpar.RefundApplyOrders[0].RefundId
				redoTask.Params = kit.JsonEncode(params)
				redoTask.UpdateTime = time.Now()
				redoTask.CreateTime = time.Now()
				redoTask.FailInfo = err.Error()
				//下次执行时间
				nextRedoDuration := utils.GetNextOrderRedoTimeDuration(0)
				redoTask.NextRedoTime = time.Now().Add(nextRedoDuration)
				errAdd := ReDoTaskAdd(redoTask)
				if errAdd != nil {
					glog.Error(inpar.RefundApplyOrders[0].RefundId, "-OMS退款订单写入重推任务失败", errAdd.Error())
				}
			}
		}

		out.Code = 400
		out.Message = err.Error()
		glog.Error("售后订单同步到oms请求错误：" + err.Error())
		return &out, nil
	}
	glog.Info("退款订单同步oms请求参数：", kit.JsonEncode(inpar), ";响应参数：", string(respBytes))
	retmes := new(*dto.OmsBaseResponse)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("退款订单同步oms返回结果转JSON错误：" + err.Error())
		return &out, nil
	}

	return &out, err
}

// OmsOrderSynchronizeByOrder 通过订单创建发货单
// 1. 查询订单信息与订单下的商品信息
// 2. 将订单数据按巨益接口要求重新整合
// 3. 调用巨益发货单创建接口
func (ac OmsService) OmsOrderSynchronizeByOrder(order *models.OrderMain) (*dto.DeliveryOrderCreateResponse, error) {
	var remark string
	_, err := ac.session.Table("dc_order.order_detail").Where("order_sn = ?", order.OrderSn).Select("buyer_memo").Get(&remark)
	if err != nil {
		return nil, err
	}
	// var ShopCode = "JD01"
	// //判断是电商渠道且主体为3，则ShopCode读取配置文件
	// if order.ChannelId == ChannelMallId && order.ShopId == CSYMainId {
	// 	ShopCode = config.GetString("ShopCode")
	// } else if order.ChannelId == ChannelMallId && order.ShopId == JCJMainId {
	// 	ShopCode = config.GetString("JCJShopCode")
	// }
	ShopCode := GetShopCodeByOrder(order)
	pushOmsOrder := &dto.DeliveryOrderCreateRequest{
		DeliveryOrderRequest: &dto.DeliveryOrderRequest{
			DeliveryOrderCode: order.OrderSn,
			OrderType:         dto.CommonlyDeliveryOrder,
			WarehouseCode:     order.WarehouseCode,
			CreateTime:        kit.GetTimeNow(),
			PlaceOrderTime:    kit.GetTimeNow(order.CreateTime),
			PayTime:           kit.GetTimeNow(order.PayTime),
			PayNo:             order.PaySn,
			OperateTime:       kit.GetTimeNow(),
			ShopCode:          ShopCode,
			LogisticsCode:     "OTHER",
			Remark:            remark,
			ReceiverInfo: dto.ReceiverInfo{
				Name:          order.ReceiverName,
				Mobile:        order.EnReceiverMobile,
				Tel:           order.EnReceiverPhone,
				Province:      order.ReceiverState,
				City:          order.ReceiverCity,
				Area:          order.ReceiverDistrict,
				DetailAddress: order.ReceiverAddress,
			},
		},
	}

	var orderProducts []*models.OrderProduct
	if err := ac.session.Where("order_sn =?", order.OrderSn).Find(&orderProducts); err != nil {
		return nil, err
	}

	for _, op := range orderProducts {
		var orderLine = &dto.OrderLineRequest{
			OwnerCode:   op.SkuId,
			ItemId:      op.SkuId,
			PlanQty:     op.Number,
			RetailPrice: fmt.Sprintf("%.2f", kit.FenToYuan(op.DiscountPrice)),
			ActualPrice: fmt.Sprintf("%.2f", kit.FenToYuan(op.DiscountPrice)),
			ItemName:    op.ProductName,
		}
		pushOmsOrder.OrderLines = append(pushOmsOrder.OrderLines, orderLine)
	}

	return ac.OmsOrderSynchronize(pushOmsOrder)
}

// 发货单创建
// 1. 查询订单信息与订单下的商品信息
// 2. 将订单数据按巨益接口要求重新整合
// 3. 调用巨益发货单创建接口
func (ac OmsService) OmsOrderSynchronize(params *dto.DeliveryOrderCreateRequest) (*dto.DeliveryOrderCreateResponse, error) {
	Engine := GetDBConn()
	mqInfoList := make([]*models.MqInfo, 0)
	params.DeliveryOrderRequest.BuyerNick = "阿闻用户"
	glog.Info("OmsOrderSynchronize, run ", kit.JsonEncode(params))
	//定义仓库修改数据结构体
	var OrderStockInfo = dto.OrderStore{}
	//仓库编码
	OrderStockInfo.Code = params.DeliveryOrderRequest.WarehouseCode
	//是否完成
	OrderStockInfo.Isfinish = "1"

	parOrder := models.OrderMain{}
	_, err := Engine.Where("order_sn=?", params.DeliveryOrderRequest.DeliveryOrderCode).Get(&parOrder)
	if err != nil {
		glog.Error("订单添加，查询电商ID错误：", err.Error())
		return nil, err
	}
	//锁定库存赋值电商订单号
	OrderStockInfo.Orderid = parOrder.OldOrderSn
	OrderStockInfo.Source = parOrder.Source

	//把商品信息库存写入消息队列
	mqInfo := new(models.MqInfo)
	mqInfo.Exchange = "ordercenter"
	mqInfo.Quene = "dc_sz_stock_update"
	mqInfo.Content = kit.JsonEncode(OrderStockInfo)
	mqInfo.Ispush = 0
	mqInfoList = append(mqInfoList, mqInfo)
	// oms不认WarehouseCode，则这些无用途
	if params.DeliveryOrderRequest.WarehouseCode == "JD01" {
		DSCode := config.GetString("OmDSCode")
		if DSCode == "" {
			params.DeliveryOrderRequest.WarehouseCode = "0579WZJDZC01"
		} else {
			params.DeliveryOrderRequest.WarehouseCode = DSCode
		}

	}
	//params.DeliveryOrderRequest.ShopCode = "JD03"

	bytesData, err := xml.MarshalIndent(params, "", " ")
	if err != nil {
		glog.Error(err)
	}

	//glog.Info("oms订单添加内容:" + kit.JsonEncode(params))

	//获取验签字符串
	respBytes, err := utils.OmsApi("deliveryorder.create", string(bytesData), "xml")

	glog.Info("oms订单请求", utils.OmsUrl, " 返回数据:"+kit.JsonEncode(string(respBytes)), " 入参："+kit.JsonEncode(params))
	if err != nil {
		check := CheckExist(params.DeliveryOrderRequest.DeliveryOrderCode, models.RedoTypeOMSOrder)
		if !check {
			redoTask := new(models.OrderRedoTask)
			redoTask.RedoType = models.RedoTypeOMSOrder //重试类型 1：正向订单重推巨益OMS
			redoTask.OrderSn = params.DeliveryOrderRequest.DeliveryOrderCode
			redoTask.Params = kit.JsonEncode(params)
			redoTask.FailInfo = err.Error()
			//下次执行时间
			nextRedoDuration := utils.GetNextOrderRedoTimeDuration(0)
			redoTask.NextRedoTime = time.Now().Add(nextRedoDuration)
			errAdd := ReDoTaskAdd(redoTask)
			if errAdd != nil {
				glog.Error(params.DeliveryOrderRequest.DeliveryOrderCode, "-OMS订单写入重推任务失败", errAdd.Error())
			}
		}
		glog.Error(parOrder.OldOrderSn, ", 订单添加请求错误：", err.Error())
		return nil, err
	}
	retmes := new(dto.DeliveryOrderCreateResponse)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		//panic(err)
		glog.Error(parOrder.OldOrderSn, ", 订单添加转JSON错误：", err.Error())
		return nil, err
	}

	//T推送订单成功 todo 先推送OMS，再插入我们本地，可能会OMS的库存先回来，然后我们本地再释放库存，减去库存，就会减去2次
	if cast.ToInt(retmes.Code) == 0 {
		_, err = Engine.Insert(mqInfoList)
		if err != nil {
			glog.Error(parOrder.OldOrderSn, ", 发货商品库存通知写入数据库失败：", err.Error())
		}
	}
	return retmes, err
}

// 瑞鹏OMS正向订单处理成功之后回调
// 一般用于 阿闻推送瑞鹏oms时 oms落单成功 但是推送供应链或者冻结库存失败是 oms重新处理成功之后回调阿闻，
// 让阿闻修改订单推送第三方的状态
func (omss OmsService) RpOmsOrderDelivered(ctx context.Context, params *pt.RpOmsOrderDeliveredRequest) (*pt.BaseResponse, error) {
	out := new(pt.BaseResponse)
	var err error
	out.Code = 200
	if params.OrderSn == "" {
		return out, nil
	}
	orderMain := GetOrderMainByOrderSn(params.OrderSn)
	if orderMain == nil || orderMain.Id == 0 {
		out.Code = 400
		out.Message = "订单不存在"
		return out, nil
	}
	orderDetail := GetOrderDetailByOrderSn(params.OrderSn, "push_delivery,push_third_order")
	//推送oms成功的就不用做处理了
	if orderDetail.PushThirdOrder == 1 {
		return out, nil
	}
	updateOrderSn := []string{params.OrderSn}
	if orderMain.OrderSn != "" {
		updateOrderSn = append(updateOrderSn, orderMain.OrderSn)
	}
	db := GetDBConn()
	_, err = db.Exec("UPDATE order_detail SET push_third_order = 1,push_third_order_reason='' WHERE order_sn IN ('" + strings.Join(updateOrderSn, "','") + "')")
	//订单同步
	if err != nil {
		out.Code = 400
		out.Message = "更新失败" + err.Error()
		glog.Error("RpOmsOrderSuccessCallback更新失败"+err.Error(), params)
		return out, nil
	}
	//删除在途库存
	go DeleteTransportationInventory(orderMain.ParentOrderSn, orderMain.ChannelId, true)
	//这里是否需要发配送 因为阿闻这边对于推送第三方失败后 是否有推配送 的实现规则不一致（美团等第三方订单 即使推送第三方失败了也发了配送）
	//导致此时并不知道这个订单是否推了配送  暂时通过order_detail中得
	//没有推配送
	if orderDetail.PushDelivery == 0 {
		if omss.orderMain.DeliveryType != 3 && //不是自提单
			omss.orderMain.OrderType != 2 && //不是预订单
			(omss.orderMain.ChannelId == ChannelAwenId || omss.orderMain.ChannelId == ChannelDigitalHealth || omss.orderMain.ChannelId == ChannelMtId ||
				(omss.orderMain.ChannelId == ChannelElmId && omss.orderMain.LogisticsCode == "6") ||
				(omss.orderMain.ChannelId == ChannelJddjId && omss.orderMain.LogisticsCode == "2938")) {
			//不等于美团专送的才
			if !strings.Contains("2002,1001,1004,2010,3001,1007", omss.orderMain.LogisticsCode) {
				//美团配送
				_ = omss.PushMpOrder()
			}
		}
	}
	out.Message = "处理成功"
	return out, err
}

func (omss OmsService) OmsRepush(ctx context.Context, params *pt.OmsRepushRequest) (*pt.OmsRepushResponse, error) {
	resp := new(pt.OmsRepushResponse)

	resp.Code = 200

	var repush models.OmsRepush
	_, err := omss.session.Table("oms_repush").Where("refund_sn = ? AND is_push = 0", params.RefundSn).Get(&repush)
	if err != nil {
		resp.Code = 400
		resp.Error = err.Error()
		return resp, err
	}

	var request dto.OrderRefundHttpRequest
	err = json.Unmarshal([]byte(repush.Request), &request)
	if err != nil {
		resp.Code = 400
		resp.Error = err.Error()
		return resp, err
	}
	retCode, msg := new(RpomsService).OrderRefundAdd(&request)
	if retCode == 200 {
		obj := models.OmsRepush{IsPush: 1}
		_, err = omss.session.Table("oms_repush").
			Where("refund_sn = ? ", params.RefundSn).Update(&obj)
		if err != nil {
			glog.Errorf("更新重推记录状态失败: %v", err)
		}
	} else {
		resp.Code = 400
		resp.Error = msg
		return resp, err
	}
	resp.Message = "success"
	return resp, nil
}

// GetShopCodeByOrder 根据订单信息获取对应的ShopCode
func GetShopCodeByOrder(order *models.OrderMain) string {
	var ShopCode = "JD01"

	//判断是电商渠道且主体为3，则ShopCode读取配置文件
	if order.ChannelId == ChannelMallId && order.ShopId == CSYMainId {
		ShopCode = config.GetString("ShopCode")
	} else if order.ChannelId == ChannelMallId && order.ShopId == JCJMainId {
		ShopCode = config.GetString("JCJShopCode")
	}

	return ShopCode
}

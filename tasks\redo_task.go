package tasks

import (
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/tricobbler/rp-kit/cast"
	"order-center/dto"
	"order-center/models"
	"order-center/services"
	"order-center/utils"
	"strconv"
	"sync"
	"time"
)

//重试任务
type redoTask struct {
}

type redoInterface interface {
	redoTask(task *models.OrderRedoTask, redo *redoTask) error
}

const (
	//巨益OMS正向订单重推
	KeyOrderRePushToOMSLock = "oms-order:order-repush-to-jy"
	////巨益OMS正向订单重推
	KeyRefundOrderRePushToOMSLock = "refund-order-repush-to-jy"
	////重新退VIP打折卡
	KeyRefundOrderRePushDiscountCard = "refund-order-repush-discount-card"
	////重新退门店券
	KeyRefundOrderRePushWasteCoupon = "refund-order-repush-waste-coupon"
)

//正向订单重新推送巨益OMS
func (e *redoTask) PushOrderToJyOMSTask() {
	myTask := cron.New(cron.WithSeconds())
	myTask.AddFunc("0 */1 * * * *", func() {
		taskInstance := &redoOMSOrder{}
		param := &dto.RedoTask{
			Name:     "重推订单到巨益OMS",
			LockCard: KeyOrderRePushToOMSLock,
			//上锁时间 理论时间 ：(为重推单据量/每次并发量) * 3s 比如1万张单子 50并发请求 则上锁时间大约可设置为 200 * 3s = 600s
			//3s因为对方地址无效 导致DNS与请求的时间花费 如果正常 一般一个请求200毫秒 那么正常情况下50个并发 一万张单子会导致 一次任务会执行40s左右
			LockDuration: 5 * 60 * time.Second,
			RedoLimit:    7,
			TaskType:     models.RedoTypeOMSOrder,
		}
		_ = e.StartTask(param, taskInstance)
	})

	myTask.Start()
}

//正向订单重新推送巨益OMS
func (e *redoTask) PushRefundOrderToJyOMSTask() {
	myTask := cron.New(cron.WithSeconds())
	myTask.AddFunc("0 */1 * * * *", func() {
		taskInstance := &redoOMSRefundOrder{}
		param := &dto.RedoTask{
			Name:     "重推退款订单到巨益OMS",
			LockCard: KeyRefundOrderRePushToOMSLock,
			//上锁时间 理论时间 ：(为重推单据量/每次并发量) * 3s 比如1万张单子 50并发请求 则上锁时间大约可设置为 200 * 3s = 600s
			//3s因为对方地址无效 导致DNS与请求的时间花费 如果正常 一般一个请求200毫秒 那么正常情况下50个并发 一万张单子会导致 一次任务会执行40s左右
			LockDuration: 5 * 60 * time.Second,
			RedoLimit:    7,
			TaskType:     models.RedoTypeOMSOrderRefund,
		}
		_ = e.StartTask(param, taskInstance)
	})

	myTask.Start()
}

//VIP退款的时候退打折卡的重推任务
func (e *redoTask) PushDiscountCardRefundTask() {
	myTask := cron.New(cron.WithSeconds())
	myTask.AddFunc("0 */1 * * * *", func() {
		taskInstance := &redoZiLongDiscountCardRefund{}
		param := &dto.RedoTask{
			Name:     "重新退VIP打折卡",
			LockCard: KeyRefundOrderRePushDiscountCard,
			//上锁时间 理论时间 ：(为重推单据量/每次并发量) * 3s 比如1万张单子 50并发请求 则上锁时间大约可设置为 200 * 3s = 600s
			//3s因为对方地址无效 导致DNS与请求的时间花费 如果正常 一般一个请求200毫秒 那么正常情况下50个并发 一万张单子会导致 一次任务会执行40s左右
			LockDuration: 5 * 60 * time.Second,
			RedoLimit:    5,
			TaskType:     models.RedoTypeDiscountCardRefund,
		}
		_ = e.StartTask(param, taskInstance)
	})

	myTask.Start()
}

//VIP退款的时候退门店券的重推任务
func (e *redoTask) PushWasteCouponTask() {
	myTask := cron.New(cron.WithSeconds())
	myTask.AddFunc("0 */1 * * * *", func() {
		taskInstance := &redoZiLongWasteCoupon{}
		param := &dto.RedoTask{
			Name:     "重推作废子龙门店券",
			LockCard: KeyRefundOrderRePushWasteCoupon,
			//上锁时间 理论时间 ：(为重推单据量/每次并发量) * 3s 比如1万张单子 50并发请求 则上锁时间大约可设置为 200 * 3s = 600s
			//3s因为对方地址无效 导致DNS与请求的时间花费 如果正常 一般一个请求200毫秒 那么正常情况下50个并发 一万张单子会导致 一次任务会执行40s左右
			LockDuration: 5 * 60 * time.Second,
			RedoLimit:    5,
			TaskType:     models.RedoTypeWasteCoupon,
		}
		_ = e.StartTask(param, taskInstance)
	})

	myTask.Start()
}

//获取导出任务
//获取任务时上锁 获取到任务后更新任务状态并解锁
func (e *redoTask) GetRedoData(taskParam *dto.RedoTask) ([]*models.OrderRedoTask, error) {

	task, err := services.GetWaitRedoTask(taskParam.RedoLimit, taskParam.TaskType)

	if err != nil {
		glog.Error(taskParam.Name, "查询重试任务出错,", err.Error())
		return nil, err
	}

	return task, err
}

//开始任务
// step 1 获取需要执行的任务
// step 2 执行任务
// step 3 更新任务数据 失败更新失败原因  成功 写入导出的数据
func (e *redoTask) StartTask(taskParam *dto.RedoTask, taskInstance redoInterface) (err error) {

	defer func() {
		if err1 := recover(); err1 != nil {
			glog.Error("zx找错误:", taskParam, err1)
		}
	}()

	//上锁 执行期间不允许其他服务器 获取任务
	getLock, expireTime := e.LockTask(taskParam.LockCard, taskParam.LockDuration)
	if getLock == false {
		//log.Error(taskParam.Name, "查询重试任务，获取查询锁失败")
		return nil
	}
	defer e.UnlockTask(taskParam.LockCard, expireTime)

	tasks, err := e.GetRedoData(taskParam)

	if len(tasks) == 0 {
		return
	}

	//执行任务 50个并发执行
	channelG := make(chan bool, 50)
	wg := new(sync.WaitGroup)
	for _, task := range tasks {
		channelG <- true
		wg.Add(1)
		go func(task *models.OrderRedoTask, taskInstance redoInterface, taskParam *dto.RedoTask) {
			defer func() {
				<-channelG
				wg.Done()
			}()
			updateTask := new(models.OrderRedoTask)
			updateTask.Id = task.Id
			repo := services.GetDBConn()
			updateTask.RedoCount = task.RedoCount + 1
			nextRedoDuration := utils.GetNextOrderRedoTimeDuration(updateTask.RedoCount)
			updateTask.NextRedoTime = task.NextRedoTime.Add(nextRedoDuration)
			updateTask.UpdateTime = time.Now()

			glog.Info(task.OrderSn, "-", task.RedoType, "-开始执行"+taskParam.Name+"任务：任务id:", task.Id, "-第"+cast.ToString(updateTask.RedoCount)+"次")
			err1 := taskInstance.redoTask(task, e)
			//如果失败更新状态
			//失败之后 如果是最后一次 更新任务状态为失败
			if err1 != nil {
				glog.Info(task.OrderSn, "-", task.RedoType, "-"+taskParam.Name+"失败：任务id:", task.Id, "-第"+cast.ToString(updateTask.RedoCount)+"次")
				//如果是最后一次 更新状态为失败 否则更新为执行中
				if updateTask.RedoCount == taskParam.RedoLimit {
					updateTask.TaskStatus = models.TaskStatusRedoFail
				} else {
					updateTask.TaskStatus = models.TaskStatusRedoing
				}
				updateTask.FailInfo = err1.Error()

				//判断是否需要发送机器人
				services.SendWebhookNotify(task, taskParam.RedoLimit)

				_, err1 = repo.Where("id=?", updateTask.Id).Update(updateTask)
				if err1 != nil {
					glog.Error(task.OrderSn, "-", task.RedoType, "-"+taskParam.Name+"执行失败，更新任务状态失败：任务id:", task.Id, "-第"+cast.ToString(updateTask.RedoCount)+"次")
				}
				return
			}

			//更新为重试成功
			updateTask.TaskStatus = models.TaskStatusRedid
			_, err1 = repo.Where("id=?", updateTask.Id).Update(updateTask)

			if err1 != nil {
				glog.Error(task.OrderSn, "-", task.RedoType, "-"+taskParam.Name+"任务执行成功，但更新任务状态失败：任务id:", task.Id, "-第"+cast.ToString(updateTask.RedoCount)+"次")
			}
			glog.Info(task.OrderSn, "-", task.RedoType, "-"+taskParam.Name+"-任务执行成功：任务id:", task.Id, "-第"+cast.ToString(task.RedoCount)+"次")
		}(task, taskInstance, taskParam)
	}

	wg.Wait()
	close(channelG)
	return nil
}

//锁在分布式下的异常情况
//1 : 程序panic 导致锁的释放代码未执行 解决：将删除锁的操作放在defer
//2 : 机器宕机，程序执行直接中断 导致锁一直无法执行释放， 解决：给锁一个超时时间expiration，超时后可以自动删除
//3 : 删除锁时 因为锁已过期导致将非自己的锁删除了  解决：锁的值设置为过期时间，当删除时与当前的时间进行对比，如果未到过期时间则不再进行删除
//4 : 多个进行（实例同时获得锁），因为是分布式系统 一个锁过期之后可能存在其他的多个实例同时获得锁的情况，
// 解决办法： 在获取不到锁时 检测是否存在过期未删除的锁，存在则删除，并重新设置，但是此时可能存在多个进程同时获取到锁的情况
//5 : 程序未执行完成 锁时间到了，这会导致逻辑未执行完成 而其他进程获得了锁，造成：1：数据错误 2：sql 执行update delete时死锁
//解决办法：如果遇到需要很长时间执行完成的任务 应该给到分布式任务进行执行
//6 : 如果宕机且锁的时间很长，则如何解决其他进程获取锁等待过长的问题？
// 解决方法：使用分布式任务进行执行
//duration 是以秒为单位的 不允许设置没有过期时间的锁
//返回过期时间
func (s *redoTask) LockTask(key string, duration time.Duration) (bool, int64) {
	now := time.Now()
	//将锁的值 设置为过期的时间点
	timeExpireUnix := now.Add(duration).Unix()
	//获取锁之前先删除可能过期未删除的锁
	s.DelExpiredLock(key)
	redisConn := services.GetRedisConn()
	lockRes := redisConn.SetNX(key, timeExpireUnix, duration).Val()
	if !lockRes {
		//log.Info("执行" + key + "任务-获取锁失败")
		return false, timeExpireUnix
	}
	defer func() {
		//如果发生panic删除锁 避免时间浪费
		if err := recover(); err != nil {
			s.UnlockTask(key, timeExpireUnix)
		}
	}()
	return true, timeExpireUnix
}

//解锁
//3 : 删除锁时 因为锁已过期导致将非自己的锁删除了  解决：锁的值设置为过期时间，当删除时与当前的时间进行对比，如果已经过期了则不再进行删除
func (s *redoTask) UnlockTask(key string, timeExpireUnix int64) {
	//执行删除操作前 可能锁已经被其他进程获得，比如该进程因为锁过期了但是任务还没有执行完，那么等待执行完删除锁时该
	//锁很可能已被其他进程获得，那么此时的删除可能会删除其他正在进行的任务的锁，需要我们判断锁是否过期
	//过期的时间点的时间时间戳 与当前时间进行比较 未过期才删除 如果过期了就不再进行删除 以免删除了其他进程加的锁
	if timeExpireUnix > time.Now().Unix() {
		redisConn := services.GetRedisConn()
		redisConn.Del(key)
	}
}

//判断某个锁是否过期  过期了就删除
//因为锁的值保存的都是应该过期的时间
//获取改锁的值 与当前时间进行比较  如果值小于当前时间 说明过期了
//如果锁的值 >当前时间  则不应该删除 因为还没到过期时间
//todo 后面可以加一个默认可删除的时间 比如超过5分钟就删除
func (s *redoTask) DelExpiredLock(key string) {
	redisConn := services.GetRedisConn()
	if redisConn.Exists(key).Val() > 0 {
		//获取这个锁的过期时间
		timeUnix, _ := strconv.Atoi(redisConn.Get(key).Val())
		//与当前时间比较 如果没有设置时间 则默认为5分钟
		now := time.Now().Unix()
		//如果这个锁到期时间 在当前时间之前  则进行删除
		if now > int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(key)
		}
	}
}

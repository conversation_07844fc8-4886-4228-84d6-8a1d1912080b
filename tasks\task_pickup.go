package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/google/uuid"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/mc"
	"order-center/proto/oc"
	"order-center/services"
	"time"
)

// 社区团购相关定时任务
func init() {
	if !kit.EnvCanCron() {
		return
	}
	c := cron.New()
	// 每分钟执行社区团购订单状态
	if _, err := c.AddFunc("@every 1m", pickupAutoUpdateStatus); err != nil {
		glog.Info("社区团购 pickupAutoUpdateStatus 创建任务出错：", err.Error())
	}
	c.Start()
}

const (
	pickupTypeCurrent  = 1 // 当前团
	pickupTypePrevious = 2 // 上一个团
)

// 更新处理器
type pickupUpdateStatusHandler struct {
	Db            *xorm.Engine
	UpetDb        *xorm.Engine
	PC            *models.PickupConfig
	OrderService  *services.OrderService
	MessageClient *mc.Client
	PickupOrders  []*pickupOrder
	StationTotals map[string]*stationTotal // 门店站点 分组金额总计
	PickupConfig  *models.PickupConfig     // 团购全局配置
}

type stationTotal struct {
	Current  int32 // 当前团
	Previous int32 // 上一个团
}

// 社区团购订单信息
type pickupOrder struct {
	Id               int32
	OrderSn          string
	ParentOrderSn    string
	OrderStatus      int32
	OrderStatusChild int32
	ShopStationKey   string
	ShopName         string
	MemberId         string
	PayAmount        int32 // 单位分
	StationDailyMin  float64
	PickupId         int32
	PickupStationId  int32
	TodayEndTime     time.Time // 当日的截止时间
	PayTime          time.Time // 支付时间
	Source           int32
	PickupType       int32 // 0无效，1截止时间前，2截止时间后
}

// 并发锁key
var pickupLockKey = "order-center:pickupAutoUpdateStatus:lock"

// 锁所有者
var pickupLockOwner = uuid.New().String()

// 自动更新状态，包括自动接单、自动取消
func pickupAutoUpdateStatus() {
	var err error
	defer func() {
		if err != nil {
			glog.Info("社区团购订单自动接单处理出错 ", err.Error())
		}
	}()

	if locked, err := pickupCheckLock(); err != nil {
		return
	} else if !locked {
		return
	}
	// 因为锁有自延期，删除锁时不需要判断所有者
	defer func() {
		pickupLockOwner = uuid.New().String()
		_, err = services.GetRedisConn().Del(pickupLockKey).Result()
	}()

	h, err := initPickupUpdateStatusHandler()
	if err != nil {
		return
	}

	for _, po := range h.PickupOrders {
		if po.PickupType == 0 || po.OrderStatusChild != 20101 {
			continue
		}
		// 站点或者活动无效，取消订单
		if po.PickupId == 0 || po.PickupStationId == 0 {
			_ = h.autoCancel(po)
			continue
		}

		// 满团了，自动接单
		if h.isSuccess(po) {
			_ = h.autoAccept(po)
			continue
		}
		// 如果是上一个团
		if po.PickupType == pickupTypePrevious {
			_ = h.autoCancel(po)
		}
	}

	// 避免短时间执行任务时多台服务器先后执行
	time.Sleep(10 * time.Second)
}

// 获取锁，同时**每分钟**自动延期
func pickupCheckLock() (bool, error) {
	lua := redis.NewScript(`
local v = redis.call("get",KEYS[1])
-- lua只有false和nil为假，其它的都是true，存在锁 -- 
if v then
	-- 当前进程获取的锁，且未释放，自动延期 --
	if v == ARGV[1] then
		redis.call("set",KEYS[1],ARGV[1],"ex",80)
	end
	-- 这次任务周期不需要执行 --
	return 0
else
	-- lua脚本的原子性，同时一个key操作只会在一台服务器执行，这里不需要set nx --
	redis.call("set",KEYS[1],ARGV[1],"ex",80)
	-- 新获取到锁，执行任务 --
	return 1
end
`)
	if r, err := lua.Run(services.GetRedisConn(),
		[]string{pickupLockKey},
		[]string{pickupLockOwner},
	).Result(); err != nil {
		return false, err
	} else {
		return cast.ToInt32(r) > 0, nil
	}
}

// 初始化处理器
func initPickupUpdateStatusHandler() (h *pickupUpdateStatusHandler, err error) {
	h = &pickupUpdateStatusHandler{
		Db:            services.GetDBConn(),
		UpetDb:        services.GetUPetDBConn(),
		OrderService:  new(services.OrderService),
		MessageClient: mc.GetMessageCenterClient(),
		StationTotals: make(map[string]*stationTotal, 0),
	}

	if h.PC, err = models.GetPickupConfig(h.Db); err != nil {
		return
	}
	if h.PickupOrders, err = queryPickupOrders(h.Db); err != nil {
		return
	}

	now := time.Now()

	// 按门店站点分组汇总金额
	for _, order := range h.PickupOrders {
		if h.StationTotals[order.ShopStationKey] == nil {
			h.StationTotals[order.ShopStationKey] = new(stationTotal)
		}

		// 当前团的起始时间
		startTime := order.TodayEndTime.AddDate(0, 0, -1)
		if now.After(order.TodayEndTime) {
			startTime = order.TodayEndTime
		}

		if order.PayTime.After(startTime) {
			order.PickupType = pickupTypeCurrent
			h.StationTotals[order.ShopStationKey].Current += order.PayAmount
		} else if order.PayTime.After(startTime.AddDate(0, 0, -1)) {
			order.PickupType = pickupTypePrevious
			h.StationTotals[order.ShopStationKey].Previous += order.PayAmount
		}
	}

	return
}

// 获取支付订单按门店站点分组
func queryPickupOrders(db *xorm.Engine) (pos []*pickupOrder, err error) {
	pos = make([]*pickupOrder, 0)

	err = db.Table("dc_order.order_main").Alias("om").
		Join("inner", "dc_order.order_detail od", "om.order_sn = od.order_sn").
		Join("left", "datacenter.pickup p", "om.shop_id = p.store_finance_code").
		Join("left", "datacenter.pickup_station ps", "ps.id = od.pickup_station_id and ps.status = 1").
		Select(`om.id,om.order_sn,om.order_status,om.order_status_child,om.shop_id,om.shop_name,om.member_id,om.parent_order_sn,
om.total as pay_amount,p.station_daily_min,p.id as pickup_id,ps.id as pickup_station_id,
concat(od.pickup_station_id,'-',om.shop_id) as shop_station_key,concat(CURDATE(),' ',p.end_time,':00') as today_end_time,om.pay_time,om.source`).
		Where("od.pickup_station_id > 0 and channel_id = 1"). // 社区团购
		Where("om.parent_order_sn != '' and order_status >= 20").
		Where("om.create_time >= ?", time.Now().Add(-73*time.Hour).Format(kit.DATETIME_LAYOUT)). // 充分利用索引
		Where("om.pay_time >= ?", time.Now().Add(-72*time.Hour).Format(kit.DATETIME_LAYOUT)).
		Find(&pos)

	return
}

// 判断当前订单团是否成功
func (h *pickupUpdateStatusHandler) isSuccess(po *pickupOrder) bool {
	var total int32
	if po.PickupType == pickupTypeCurrent {
		total = h.StationTotals[po.ShopStationKey].Current
	} else {
		total = h.StationTotals[po.ShopStationKey].Previous
	}

	return (float64(total) - po.StationDailyMin*100) > -0.001
}

// 自动接单处理
func (h *pickupUpdateStatusHandler) autoAccept(po *pickupOrder) (err error) {
	// 非待接单的略过
	if po.OrderStatusChild != 20101 {
		return
	}
	defer func() {
		if err != nil {
			glog.Info("社区团购订单 " + po.OrderSn + " 自动接单处理失败：" + err.Error())
		} else {
			glog.Info("社区团购订单 " + po.OrderSn + " 自动接单处理成功")
		}
	}()

	if res, err := h.OrderService.AcceptOrder(context.Background(), &oc.AcceptOrderRequest{
		OrderSn: po.ParentOrderSn,
	}); err != nil {
		return err
	} else if res.Code != 200 {
		return errors.New(res.Message + res.Error)
	}

	go func() {
		_ = h.sendTemplateMessage(po, "成功")
	}()

	return
}

// 成团失败自动取消
func (h *pickupUpdateStatusHandler) autoCancel(po *pickupOrder) (err error) {
	// 非待接单的略过
	if po.OrderStatusChild != 20101 {
		return
	}
	defer func() {
		if err != nil {
			glog.Info("社区团购订单 " + po.OrderSn + "拼团失败取消订单处理失败：" + err.Error())
		} else {
			glog.Info("社区团购订单 " + po.OrderSn + "拼团失败取消订单成功")
		}
	}()

	if out, err := h.OrderService.CancelOrder(context.Background(), &oc.CancelOrderRequest{
		OrderSn:          po.OrderSn,
		CancelReason:     "社区团购拼团失败系统自动取消订单",
		IsRefund:         0,
		OrderStatus:      po.OrderStatus,
		OrderStatusChild: po.OrderStatusChild,
	}); err != nil {
		return err
	} else if out.Code != 200 {
		return errors.New(out.Message + out.Error)
	}

	var refundSn string
	if has, err := h.Db.Table("refund_order").Select("refund_sn").Where("order_sn=?", po.OrderSn).Get(&refundSn); err != nil {
		return err
	} else if has {
		// 订单取消成功后，调用退款服务
		ro := services.RefundOrderService{}
		if out, err := ro.RefundOrderPay(context.Background(), &oc.RefundOrderPayRequest{
			RefundOrderSn: refundSn,
			ResType:       "商家发起退款",
			OperationType: "商家发起退款",
		}); err != nil {
			return err
		} else if out.Code != 200 {
			return errors.New(out.Message + out.Error)
		}
	}

	go func() {
		_ = h.sendTemplateMessage(po, "失败")
	}()

	return
}

// 发送模板消息
func (h *pickupUpdateStatusHandler) sendTemplateMessage(po *pickupOrder, msg string) (err error) {
	defer func() {
		if err != nil {
			glog.Info("社区团购 订单 " + po.OrderSn + " 发送模板消息出错：" + err.Error())
		}
	}()

	var openId string
	if has, err := h.UpetDb.Table("upet_member").Where("scrm_user_id = ?", po.MemberId).Select("weixin_mini_openid").Get(&openId); err != nil {
		return errors.New("查询会员信息 " + err.Error())
	} else if !has {
		return nil
	}

	miniprogramState := "trial"
	if !kit.IsDebug {
		miniprogramState = "formal"
	}

	data, _ := json.Marshal(map[string]interface{}{
		"phrase5": map[string]interface{}{
			"value": msg,
		}, "character_string1": map[string]interface{}{
			"value": po.OrderSn,
		}, "thing2": map[string]interface{}{
			"value": po.ShopName,
		}, "thing3": map[string]interface{}{
			"value": "商品套餐",
		},
	})

	res, err := h.MessageClient.RPC.SubscribeMessage(h.MessageClient.Ctx, &mc.SubscribeMessageRequest{
		Touser:           openId,
		TemplateId:       h.PC.StatusTemplate,
		Data:             string(data),
		Page:             fmt.Sprintf("app/local/order/orderDetail?productId=%d&order_sn=%s", po.Id, po.OrderSn),
		MiniprogramState: miniprogramState,
	})

	if err != nil {
		return
	} else if res.Code != 200 {
		return errors.New(res.Message + res.Error)
	}

	return
}

package tasks

import (
	"encoding/json"
	"order-center/dto"
	"order-center/services"
	"order-center/utils"

	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
)

// RePushToZiLongMqTask 订单推送子龙失败重推送
// 解决完成订单或者申请售后单时 推送子龙 提示订单不存在
// 解决办法为：退款或者完成订单时如果子龙提示订单不存在，则推送主单->完成单->售后单
// v5.6.8 添加
func RePushToZiLongMqTask() {
	utils.Consume(services.QueueOrderRePushToZiLongQueue, services.QueueOrderRePushToZiLongRoute, services.QueueOrderRePushToZiLongExchange, RePushToZiLong)
}

// 订单推送子龙失败重推送
func RePushToZiLong(d amqp.Delivery) (response string, err error) {
	defer kit.CatchPanic()
	logHead := "RePushOrderToZiLong:"
	mqMessage := string(d.Body)

	glog.Info(logHead, " Consume RePushToZiLong MQ:", mqMessage)
	model := new(dto.MqRePushOrderToZiLong)
	err = json.Unmarshal(d.Body, model)

	if err != nil {
		glog.Error(logHead, "json.Unmarshal:", err, ",", mqMessage)
		return
	}
	_ = d.Ack(false)
	err = services.RePushToZiLongProcess(model)

	if err != nil {
		glog.Error(logHead, "services.RePushToZiLong.error:", err, kit.JsonEncode(model))
	}
	return
}

package models

import (
	"time"
)

type MemberPropertyGuaranteeQuotaDetail struct {
	Id                    int32     `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	MemberId              string    `xorm:"default 'NULL' comment('用户的scrm_id') VARCHAR(100)"`
	InsurancePolicyNumber string    `xorm:"default 'NULL' comment('保单号') VARCHAR(100)"`
	QuotaType             int32     `xorm:"default NULL comment('额度类型(1、医疗账户 2、服务账户)') INT(11)"`
	GenerateTime          time.Time `xorm:"default 'NULL' comment('生成时间') DATETIME"`
	GenerateAmount        int32     `xorm:"default NULL comment('发生金额；单位：分。正负数存数据库') INT(11)"`
	Remark                string    `xorm:"default 'NULL' comment('备注') VARCHAR(100)"`
	LinkOrderNumber       string    `xorm:"default 'NULL' comment('关联单据') VARCHAR(100)"`
	AllPrice              int32     `xorm:"default NULL comment('商品总金额；单位：分。') INT(11)"`
	ExpensePrice          int32     `xorm:"default NULL comment('报销金额；单位：分。') INT(11)"`
	RealPrice             int32     `xorm:"default NULL comment('实付金额；单位：分。') INT(11)"`
	CreateTime            time.Time `xorm:"default 'NULL' comment('创建时间') VARCHAR(100)"`
	VirtualCardId         int64     `xorm:"not null default 0 comment('卡号') BIGINT(20)"`
}

func (v MemberPropertyGuaranteeQuotaDetail) TableName() string {
	return "datacenter.member_property_guarantee_quota_detail"
}

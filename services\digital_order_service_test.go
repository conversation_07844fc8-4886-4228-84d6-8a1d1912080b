package services

import (
	"context"
	"order-center/proto/oc"
	"testing"
)

func TestPayDigitalOrder(t *testing.T) {
	ds := &DigitalOrderService{}
	var ctx context.Context
	req := &oc.PayDigitalOrderRequest{
		Openid:  "o_RXS5o3V0CyVM3iiaNOnIjT6pcU",
		OrderSn: "24af32fb31d5431c8f36df3c8892bcf2",
		UserId:  "4952badf1d4665f2863f6dfd91222c4f7348ba717e4d40224a2b505fcb2217e3",
	}
	ds.PayDigitalOrder(ctx, req)
}

//测试生产订单号
func TestCreateSn(t *testing.T) {
	ds := &DigitalOrderService{}
	ds.CreateSn()
}

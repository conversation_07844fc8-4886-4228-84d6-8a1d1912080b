package models

import (
	"time"
)

type OrderRisk struct {
	Id             int32     `xorm:"not null pk autoincr INT(10)"`
	OldOrderSn     string    `xorm:"default 'NULL' comment('订单号') index VARCHAR(50)"`
	UserMobile     string    `xorm:"not null comment('下单手机号') index VARCHAR(20)"`
	ReceiverMobile string    `xorm:"default '''' comment('收货手机号') index VARCHAR(20)"`
	ChannelId      int32     `xorm:"default NULL comment('渠道id') TINYINT(4)"`
	State          int32     `xorm:"default 10 comment('10异常，20标记为非异常') TINYINT(4)"`
	CreateTime     time.Time `xorm:"default 'current_timestamp()' DATETIME created"`
}

type RiskManagement struct {
	Id              int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	OrderDayMax     int       `xorm:"default 0 comment('下单手机号每日限制单数') TINYINT(1)"`
	ReceviveDayMax  int       `xorm:"default 0 comment('接收手机号每日限制单数') TINYINT(2)"`
	CumulativeTimes int       `xorm:"default 0 comment('累计次数') TINYINT(2)"`
	CreateTime      time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime      time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

func (t RiskManagement) TableName() string {
	return "dc_customer.risk_management"
}

type RiskUser struct {
	Id                     int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	Mobile                 string    `xorm:"default '''' comment('手机号') index CHAR(11)"`
	Status                 int       `xorm:"default 0 comment('状态 0解封,1管控中') TINYINT(1)"`
	Type                   int       `xorm:"default 0 comment('类型 位运算 第一位下单手机号是否被封、第二位收货手机号是否被封') TINYINT(1)"`
	LockTime               time.Time `xorm:"default 'NULL' comment('风控时间') DATETIME"`
	UserCumulativeTimes    int       `xorm:"default 0 comment('累计管控次数') TINYINT(1)"`
	LastUnlockTime         time.Time `xorm:"default 'NULL' comment('上一次下单手机号解封时间') DATETIME"`
	LastReceiverUnlockTime time.Time `xorm:"default 'NULL' comment('上一次收货手机号解封时间') DATETIME"`
	UnlockTime             time.Time `xorm:"default 'NULL' comment('自动解封时间') DATETIME"`
	CreateTime             time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime             time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

func (t RiskUser) TableName() string {
	return "dc_customer.risk_user"
}

type RiskUserLog struct {
	Id         int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	Type       int       `xorm:"default 0 comment('日志类型：0=风控，1=解封') TINYINT(1)"`
	Ruid       int       `xorm:"default 0 comment('同步risk_user的id') index INT(11)"`
	OpterId    string    `xorm:"default '''' comment('操作者员工编码') VARCHAR(20)"`
	Opter      string    `xorm:"default '''' comment('操作者') VARCHAR(20)"`
	Reason     string    `xorm:"default '''' comment('风控原因') VARCHAR(225)"`
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

func (t RiskUserLog) TableName() string {
	return "dc_customer.risk_user_log"
}

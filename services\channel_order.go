package services

import (
	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"
)

type channelOrder interface {
	PickOrder() error                                                 //拣货
	SyncDeliveryNode(*oc.DeliveryNodeRequest) error                   //同步配送节点
	CancelOrder(*oc.CancelAcceptOrderRequest) error                   //取消订单
	ApplyPartRefund(*oc.OrderApplyPartRefundRequest) error            //发起部分退款
	AgreeRefund(*oc.MtOrderRefundRequest, *models.RefundOrder) error  //同意退款
	RejectRefund(*oc.MtOrderRefundRequest, *models.RefundOrder) error //拒绝退款
}

// saas-v1.0
func NewChannelOrder(order *models.Order) (co channelOrder) {
	//由于第三方渠道拆单了，需要查询父订单的oldOrderSn来推送第三方渠道
	oldOrderSn := order.OldOrderSn
	if len(order.ParentOrderSn) > 0 {
		parentOrderMain := GetOrderMainByOrderSn(order.ParentOrderSn)
		oldOrderSn = parentOrderMain.OldOrderSn
	}

	switch order.ChannelId {
	case ChannelMtId:
		co = channelMtOrder{
			order:      order,
			oldOrderSn: oldOrderSn,
			etClient:   et.GetExternalClient(),
		}
	case ChannelElmId:
		co = channelElmOrder{
			order:      order,
			oldOrderSn: oldOrderSn,
			etClient:   et.GetExternalClient(),
		}
	case ChannelJddjId, 420:
		co = channelJddjOrder{
			order:      order,
			oldOrderSn: oldOrderSn,
			etClient:   et.GetExternalClient(),
		}
	default:
		panic("渠道错误")
	}

	return
}

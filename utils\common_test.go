package utils

import (
	"reflect"
	"testing"
	"time"
)

func TestGetTimeStr(t *testing.T) {
	type args struct {
		dateLayout string
		timeStr    string
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				dateLayout: DATETIME_LAYOUT,
				timeStr:    "2022-04-03 15:00:02",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTimeStr(tt.args.dateLayout, tt.args.timeStr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTimeStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

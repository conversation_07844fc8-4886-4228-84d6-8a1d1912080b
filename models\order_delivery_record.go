package models

import (
	"time"
)

type OrderDeliveryRecord struct {
	Id                  int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	DeliveryId          int64     `xorm:"default NULL comment('配送活动标识') BIGINT(32)"`
	MtPeisongId         string    `xorm:"default 'NULL' comment('美团配送内部订单id，最长不超过32个字符') VARCHAR(100)"`
	OrderSn             string    `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	CreateTime          time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime          time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	DeliveryServiceCode int32     `xorm:"not null default '0' comment('配送服务代码：飞速达:4002，快速达:4011，及时达:4012，集中送:4013，自由达:4014，闪送：5001') INT(11)"`
	TotalAmount         string    `xorm:"comment('未优惠需要支付的费用') VARCHAR(50)"`
	TotalFeeAfter       string    `xorm:"comment('优惠的额度') VARCHAR(50)"`
	CouponFee           string    `xorm:"comment('实际支付的费用') VARCHAR(50)"`
	DeliveryType        int       `json:"delivery_type" xorm:"default 'null' comment('//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风') INT(11) 'delivery_type'"`
}

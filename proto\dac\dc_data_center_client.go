package dac

import (
	"context"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type DcDataCenterClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  DatacenterServiceClient
}

func GetDcDataCenterClient() *DcDataCenterClient {
	var client DcDataCenterClient
	var err error
	url := config.GetString("grpc.data-center")

	//url = "172.30.128.66:10032"  测试环境
	//url = ""
	if url == "" {
		url = "127.0.0.1:10032"
	}

	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Errorf("GetDcDataCenterClient 获取连grpc接异常,err:%+v", err)
		return nil
	}
	client.RPC = NewDatacenterServiceClient(client.Conn)
	client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
	return &client
}

//关闭链接
func (s *DcDataCenterClient) Close() {
	if s == nil {
		return
	}
	if s.Conn != nil {
		s.Conn.Close()
	}
	if s.Cf != nil {
		s.Cf()
	}
}

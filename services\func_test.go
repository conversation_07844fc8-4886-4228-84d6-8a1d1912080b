package services

import (
	"encoding/json"
	"fmt"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/mc"
	"order-center/proto/oc"
	"reflect"
	"testing"
	"time"

	"github.com/go-xorm/xorm"
	kit "github.com/tricobbler/rp-kit"
)

func init() {
	kit.IsDebug = true
}

func Test_AwenOrderExport(t *testing.T) {

	GetWaitRedoTask(5, 1)

	type args struct {
		params *oc.AwenMaterOrderListRequest
	}
	tests := []struct {
		name        string
		args        args
		wantDetails []*oc.AwenMaterOrderListRequest
		wantErr     bool
	}{
		{
			name: "阿闻管家订单导出",
			args: args{
				params: &oc.AwenMaterOrderListRequest{
					//Endtime:   "2020-12-07 23:59:59",
					//Starttime: "2020-11-11 00:00:00",
					////Shopids:    []string{"AA0051"},
					//Pagesize:   65535,
					//Keyword:    "",
					//Searchtype: 0,
					//UserNo:     "U_J4BKN542",
					SearchType: 1,
					Keyword:    "4100011899779820",
				},
			},
			wantErr: false,
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, err := AwenOrderExport(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenOrderExportV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(gotDetails, tt.wantDetails) {
			//	t.Errorf("AwenOrderExportV2() gotDetails = %v, want %v", gotDetails, tt.wantDetails)
			//}
			t.Log(len(gotDetails))
			if len(gotDetails) > 0 {
				t.Log(gotDetails[0])
			}
		})
	}
}

func Test_MessageCreate(t *testing.T) {
	type args struct {
		dacClient *dac.Client
		message   *models.Message
	}

	dacClient := dac.GetDataCenterClient()

	tests := []struct {
		name string
		args args
	}{
		{
			name: "新订单通知数据中心",
			args: args{
				dacClient: dacClient,
				message: &models.Message{
					OrderId:     "",
					MessageType: 0,
					Msg:         "",
					FinanceCode: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			MessageCreate(tt.args.message)
		})
	}
}

func Test_IntegralOperation(t *testing.T) {
	type args struct {
		orderId      string
		integralType bool
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "积分操作",
			args: args{
				orderId:      "50000000348",
				integralType: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			IntegralOperation(tt.args.orderId, tt.args.integralType)
		})
	}
}

func Test_FreedDailyStock(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "释放当日库存限制",
			args: args{orderSn: "4000000001109548"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FreedDailyStock(tt.args.orderSn)
		})
	}
}

func Test_DealOrderPayNotify(t *testing.T) {
	type args struct {
		in *models.OrderPayNotify
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "DealOrderPayNotify",
			args: args{in: &models.OrderPayNotify{
				OrderSn:   "9964128298319764",
				PaySn:     "20250217180354891647156953811928",
				PayMode:   8,
				PayTime:   time.Now(),
				PayAmount: 2100,
			}},
		},
	}
	for _, tt := range tests {
		//测试推送电商
		//syncMallOrderResults := make([]dto.SyncMallOrderResult, 1)
		//syncMallOrderResult := dto.SyncMallOrderResult{
		//	OrderInfo: dto.SyncMallOrderInfo{
		//		OldOrderSn:     "323",
		//		OrderSn:        "323",
		//		OrderAmount:    100,
		//		ShippingFee:    100,
		//		GoodsAmount:    100,
		//		IsVirtual:      1,
		//		PromotionTotal: 0,
		//		PowerId:        12,
		//	},
		//	OrderGoods:    nil,
		//	OrderCodeinfo: nil,
		//}
		//syncMallOrderResults = append(syncMallOrderResults, syncMallOrderResult)
		//
		//signByte := kit.JsonEncodeByte(syncMallOrderResults)
		//data := string(signByte)
		//
		//stringA := kit.GetMd5("data=" + data)
		//sign := strings.ToUpper(kit.GetMd5(stringA + "52132e92d85005fd13e2a2bd89b7bed4"))
		////response := utils.HttpGet(pushMallAPI + "?act=openapi&op=getExpressInfo&sign=" + sign + "&e_code=HTKY&shipping_code=" + params.ExpressNo)
		//println(sign)

		t.Run(tt.name, func(t *testing.T) {
			DealOrderPayNotify(tt.args.in)
		})
	}
}

func Test_OrderIsSplit(t *testing.T) {
	type args struct {
		order_sn []string
	}
	tests := []struct {
		name string
		args args
		want map[string]int32
	}{
		{
			name: "test",
			args: args{[]string{"1591786668107114", "1591789077899605", "1591838911371460"}},
			want: map[string]int32{
				"1591786668107114": 1,
				"1591789077899605": 1,
				"1591838911371460": 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := OrderIsSplit(tt.args.order_sn); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderIsSplit() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_PushSplitResultToMall(t *testing.T) {
	type args struct {
		parentOrderMain *models.OrderMain
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "推送拆单给电商",
			args: args{
				parentOrderMain: GetOrderMainByOldOrderSn("970671275985721567"),
			},
		},
		{
			name: "推送拆单给电商",
			args: args{
				parentOrderMain: GetOrderMainByOldOrderSn("510671275904800604"),
			},
		},
		{
			name: "推送拆单给电商",
			args: args{
				parentOrderMain: GetOrderMainByOldOrderSn("840671275697868081"),
			},
		},
		{
			name: "推送拆单给电商",
			args: args{
				parentOrderMain: GetOrderMainByOldOrderSn("680671276401668329"),
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushSplitResultToMall(tt.args.parentOrderMain)
		})
	}
}

func Test_DeleteTransportationInventory(t *testing.T) {
	type args struct {
		orderSn   string
		channelId int32
		upStock   []bool
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "清除在途库存",
			args: args{
				orderSn:   "4000000001106962",
				channelId: 1,
				upStock:   []bool{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			DeleteTransportationInventory(tt.args.orderSn, tt.args.channelId, tt.args.upStock...)
		})
	}
}

func Test_saveSplitOrder(t *testing.T) {
	type args struct {
		mainOrder *models.OrderMain
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "拆分后子订单入库",
			args: args{
				mainOrder: GetOrderMainByOrderSn("4100000015531064"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if err := saveVipSplitOrder(tt.args.mainOrder); err != nil {
				t.Errorf("saveSplitOrder() error = %v", err)
			}
		})
	}
}

func Test_GetSn(t *testing.T) {
	type args struct {
		kind   string
		number []int
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "生成订单号",
			args: args{
				kind: "order",
			},
		},
		{
			name: "生成售后单号",
			args: args{
				kind: "refund",
			},
		},
		{
			name: "生成核销码",
			args: args{
				kind: "verify",
			},
		},
		{
			name: "生成配送标识",
			args: args{
				kind: "delivery",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetSn(tt.args.kind, tt.args.number...)
			if len(tt.args.number) == 0 {
				tt.args.number = []int{1}
			}
			if len(got) != len(tt.args.number) {
				t.Errorf("GetSn() = %v, want %v", len(got), len(tt.args.number))
			}
			t.Log(got)
		})
	}
}

func Test_SaveOrderLog(t *testing.T) {
	type args struct {
		orderLog []*models.OrderLog
	}
	tests := []struct {
		name string
		args args
	}{
		{
			"SaveOrderLog",
			args{orderLog: []*models.OrderLog{
				{
					OrderSn: "4000000001301838",
					LogType: 1,
				},
				{
					OrderSn: "4000000001301946",
					LogType: 2,
				},
				{
					OrderSn: "4000000001301838",
					LogType: 3,
				},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SaveOrderLog(tt.args.orderLog)
		})
	}
}

func TestRefundOrderExport(t *testing.T) {
	type args struct {
		in *oc.RefundOrderInfoRequest
	}
	jsonStr := `{"search_type":0,"keyword":"","start_time":"2022-02-15 00:00:00","end_time":"2022-02-15 23:59:59","user_type":0,"refund_state":0,"order_type":2,"channel_id":0,"shopids":["YC0001","YC0004","YC0003","YC0002","YC0012","YC0009","YC0005","YC0007","YC0006","YC0008","YC0011","YC0013","YC0014","YC0019","YC0018","YC0016","YC0036","YC0021","YC0024","YC0027","YC0025","YC0026","YC0031","YC0029","YC0028","YC0030","YC0032","YC0033","YC0034","YC0035","YC0037","YC0023","YC0020","YC0010","YC0022","YC0039","YC0042","YC0041","YC0044","YC0045","YC0043","YC0040","CX0001","CX0002","AA0018","AA0009","AA0006","AA0011","AA0022","AA0004","AA0007","AA0010","AA0003","AA0013","AA0008","AA0031","AA0014","AA0021","AA0001","AA0012","AA0017","AA0032","AA0027","AA0028","AA0025","AA0026","AA0024","AA0029","AA0036","AA0037","AA0033","AA0059","AA0035","AA0034","AA0015","AA0030","AA0016","AA0040","AA0058","AA0023","AA0057","AA0060","AA0053","AA0019","AA0020","CX0003","CX0004","CX0010","CX0013","CX0005","CX0007","CX0006","CX0008","AA0039","CX0009","CX0011","KD0001","YC0046","YC0055","CX0012","AA0041","AA0068","CX0014","CX0015","CX0016","CX0017","CX0018","CX0019","CX0020","CX0021","CX0022","CX0023","CX0024","YC0047","YC0048","YC0050","YC0049","YC0054","YC0051","CX0025","CX0026","CX0027","CX0028","CX0029","CX0030","AA0055","CX0032","AA0038","AA0070","AA0075","AA0067","AA0066","AA0065","AA0061","AA0062","AA0063","AA0077","AA0079","AA0073","AA0072","AA0071","AA0087","AA0083","AA0002","AA0056","AA0042","AA0043","AA0044","AA0045","AA0046","AA0047","AA0048","AA0069","AA0074","AA0080","AA0081","AA0052","AA0050","AA0051","YC0060","YC0062","YC0053","YC0056","YC0061","YC0052","YC0058","YC0059","AA0088","AA0089","AA0090","AA0091","AA0092","AA0049","AA0064","AA0076","AA0082","AA0085","AA0086","AA0094","AA0095","AA0097","AA0098","AA0099","AA0100","AA0101","AA0102","AA0103","AA0104","AA0105","CX0033","CX0034","CX0035","CX0036","CX0037","AA0107","AA0112","YC0064","YC0065","YC0066","YC0067","AA0108","CX0052","CX0054","CX0056","CX0055","CX0044","CX0043","CX0040","CX0041","CX0051","CX0047","CX0049","CX0057","CX0046","CX0059","CX0060","CX0061","CX0062","CX0063","CX0064","CX0065","CX0066","CX0067","CX0068","CX0069","CX0070","AA0109","AA0111","AA0113","AA0114","AA0115","AA0116","AA0118","AA0119","AA0120","AA0121","AA0123","AA0124","AA0125","AA0126","AA0127","AA0128","CX0038","CX0039","CX0042","YC0068","YC0070","YC0071","YC0072","CX0074","AA0132","CX0078","CX0079","CX0080","YC0073","CX0085","CX0050","CX0082","CX0083","CX0084","CX0071","CX0072","CX0077","CX0075","CX0076","CX0087","CX0086","CX0088","YC0057","CX0089","AA0129","AA0130","AA0131","AA0133","AA0134","AA0135","AA0137","AA0138","AA0139","AA0140","AA0141","AA0142","AA0143","AA0144","AA0145","AA0146","YC0074","YC0075","YC0077","AN0001","AN0002","AN0003","NJ0001","NJ0002","NJ0004","NJ0005","NJ0006","NJ0007","NJ0008","NJ0009","NJ0010","YC0063","CX0045","CX0093","CX0094","CX0096","CX0097","CX0098","AA0148","AA0149","AA0150","AA0151","AA0152","AA0153","AA0154","AA0155","AA0156","AA0157","AA0158","AA0159","AA0160","AA0161","AA0162","AA0163","AA0164","AA0165","AA0166","AA0167","AA0168","AA0169","AA0170","AA0136","AA0093","CX0106","CX0107","CX0105","CX0103","CX0102","CX0108","CX0090","CX0073","CX0104","AA0180","CX0092","CX0099","CX0100","CX0101","CX0109","CX0111","CX0112","CX0113","CX0114","CX0115","CX0116","CX0117","CX0118","CX0119","CX0120","CX0121","CX0124","CX0125","CX0126","CX0129","CX0130","CX0131","CX0132","CX0133","CX0134","CX0135","CX0136","CX0137","CX0138","CX0139","CX0140","CX0141","CX0142","CX0143","CX0144","CX0145","CX0146","CX0147","CX0148","CX0149","CX0150","CX0152","YC0078","YC0086","YC0079","AA0171","AA0173","AA0176","AA0177","AA0178","AA0179","AA0181","AA0182","AA0183","AA0184","AA0185","AA0186","AA0187","AA0188","AA0189","AA0190","AA0191","AA0192","AA0193","AA0195","AA0196","AA0197","AA0198","AA0199","AA0200","YC0080","YC0082","YC0083","YC0084","YC0085","YC0087","YC0089","YC0090","YC0091","YC0092","YC0093","YC0094","YC0095","YC0097","YC0098","YC0099","YC0101","AN0004","AN0006","AN0005","AN0008","AN0009","AN0010","AN0011","AN0012","AN0013","CX0123","AN0014","CX0153","CX0154","CX0155","CX0156","CX0157","CX0158","CX0159","CX0161","CX0162","CX0163","CX0164","CX0165","CX0166","CX0167","CX0169","CX0170","CX0172","CX0174","CX0175","CX0176","CX0177","CX0178","CX0179","CX0180","CX0181","CX0182","CX0183","CX0184","CX0185","AA0202","AA0203","AA0204","AA0205","AA0206","AA0208","AA0209","AA0210","AA0211","AA0212","AA0213","AA0214","AA0215","AA0216","AA0217","AA0218","AA0219","AN0015","AN0016","AN0017","AN0018","AN0019","CX0186","CX0187","CX0189","CX0191","CX0192","CX0193","CX0194","CX0195","CX0196","CX0197","CX0198","YC0105","YC0106","CX0203","AA0220","AA0221","AA0222","AA0223","AA0224","AA0225","AA0226","AA0227","AA0229","AA0230","CX0220","CX0221","CX0222","CX0223","CX0224","YC0114","YC0111","YC0113","YC0112","YC0115","AA0243","CX0205","CX0207","CX0206","CX0208","CX0209","CX0210","CX0235","AA0231","AA0234","AA0233","AA0235","AA0236","AA0237","AA0238","AA0239","AA0240","AA0241","AA0242","KD0002","CX0236","CX0240","CX0241","CX0242","CX0243","CX0244","CX0245","CX0246","CX0227","CX0249","CX0250","CX0251","CX0252","AN0020","CX0211","CX0212","CX0228","CX0229","CX0253","CX0254","CX0256","CX0257","CX0258","AN0022","YC0117","YC0119","YC0118","AN0023","AN0024","CX0231","CX0232","CX0110","CX0200","AA0244","CF0001","CF0002","CF0003","CF0004","CF0005","CF0006","CF0007","CF0008","CF0010","CF0011","CF0012","CF0013","CF0014","CF0015","CF0016","CF0017","CF0018","CF0019","CF0020","CX0259","CX0234","CX0261","CX0262","CX0263","CX0264","CX0237","CX0238","CX0239","AB0001","AB0002","AB0003","AB0004","AB0005","AB0006","AB0007","AB0008","AB0009","AB0010","AB0011","AB0012","AB0013","AB0014","AB0015","AB0016","AB0017","AB0018","AB0019","AB0020","AB0021","AB0022","AB0023","AB0024","AB0025","AB0026","AB0027","AB0028","AB0029","AB0030","AB0031","AB0032","AB0033","AB0034","AB0035","AB0036","AB0062","AB0063","AB3002","AB0037","AB0039","AB0038","AB0047","AB0046","AB0049","AB0045","AB0044","AB0061","AB0041","AB0040","AB0042","AB0043","AB0048","AB0056","AB0057","AB0059","AB0060","AB0050","CX0266","CF0021","CF0022","NWT001","NWT002","CX0267","CX0265","YC0109","NWT003","NWT004","CX0260","CX0269","CX0270","CX0271","CX0273","AA0245","AB0068","AB0051","AB0052","AB0069","AB0099","AB0077","AB0054","AB0058","AB0053","YC0096","NJ0003","NJ0011","YC0120","NJ0012","CX0274","CX0276","AA0247","AA0248","YC0121","YC0122","CF0023","CX0279","CX0280","CX0281","CX0282","CX0283","CX0284","CX0285","CX0213","CX0286","CX0277","CX0278","CX0287","CX0289","CX0173","CX0290","YC0123","YC0124","AN0031","AN0027","AN0028","AN0030","AN0032","CF0032","CF0024","CF0028","CF0029","CF0030","CF0031","AN0029","AN0033","AA0250","YC0125","YC0126","YC0127","YC0128","AN0026","CX0291","YC0133","CX0292","CX0293","CX0294","AB0055","AB0065","AB0066","AB0067","AB0070","AB0071","AB0072","AB0073","AB0074","AB0075","AB0076","AB0078","AB0079","AB0080","AN0025","NJ0013","CX0295","CX0296","CX0297","CF0035","CF0036","CF0037","CX0298","CX0299","CX0300","CF0041","CF0043","CF0044","CF0045","CF0046","CF0048","CF0049","CF0050","CF0051","CF0052","CF0053","CF0054","CF0056","CF0057","CX0302","CX0303","YC0134","CX0304","CX0305","YC0135","YC0140","YC0141","YC0142","AA0251","CX0306","AB0064","AB0081","AB0082","AB0083","AB0084","CX0307","CX0308","CX0309","CX0310","AA0252","AA0253","YC0143","YC0144","CX0315","CX0311","CX0312","CX0313","CX0314","YC0146","YC0147","YC0148","AN0034","AA0254","YC0149","YC0150","YC0151","YC0152","YC0153","NWT005","MC0002","YC0154","AA0255","CF0066","YC0155","YC0156","YC0157","AW0001","AW0002","AW0003","AW0004","AW0005","CX0318","CX0322","ML0002","ML0009","ML0010","ML0011","ML0012","ML0013","ML0014","ML0015","ML0016","ML0017","ML0018","ML0019","ML0020","ML0021","ML0022","ML0023","ML0024","ML0025","ML0026","ML0027","ML0028","ML0029","ML0030","ML0031","ML0032","ML0033","ML0034","ML0035","ML0036","ML0037","ML0038","ML0039","ML0040","ML0041","ML0042","ML0043","ML0044","ML0045","ML0046","ML0047","ML0048","ML0049","ML0050","ML0051","ML0052","ML0053","ML0054","ML0055","ML0056","ML0057","ML0058","ML0059","ML0060","ML0061","ML0062","ML0063","ML0064","ML0065","ML0066","ML0067","ML0068","ML0069","ML0070","ML0071","ML0072","ML0073","ML0074","ML0075","ML0076","ML0077","ML0078","MLM051","RP0001","RP0002","RP0003","RP0004","RP0005","RP0006","RP0007","RP0008","RP0009","RP0010","RP0011","RP0012","RP0013","RP0014","RP0015","RP0017","RP0018","RP0019","RP0020","RP0021","RP0022","RP0023","RP0025","RP0026","RP0027","RP0028","RP0029","RP0030","RP0031","RP0032","RP0033","RP0034","RP0035","RP0036","RP0037","RP0038","RP0039","RP0040","RP0041","RP0042","RP0043","RP0044","RP0045","RP0046","RP0047","RP0048","RP0049","RP0050","RP0052","RP0053","RP0054","RP0055","RP0057","RP0058","RP0059","RP0060","RP0061","RP0062","RP0063","RP0064","RP0065","RP0066","RP0067","RP0068","RP0069","RP0070","RP0071","RP0072","RP0073","RP0074","RP0075","RP0076","RP0077","RP0078","RP0079","RP0080","RP0081","RP0082","RP0083","RP0084","RP0085","RP0086","RP0087","RP0088","RP0089","RP0090","RP0091","RP0092","RP0093","RP0094","RP0095","RP0096","RP0097","RP0098","RP0099","RP0100","RP0101","RP0102","RP0103","RP0104","RP0105","RP0106","RP0107","RP0108","RP0109","RP0110","RP0111","RP0112","RP0113","RP0114","RP0115","RP0116","RP0117","RP0118","RP0119","RP0120","RP0121","RP0122","RP0123","RP0124","RP0125","RP0126","RP0127","RP0128","RP0129","RP0130","RP0131","RP0132","RP0133","RP0135","RP0136","RP0137","RP0138","RP0139","RP0140","RP0141","RP0142","RP0143","RP0144","RP0145","RP0146","RP0147","RP0148","RP0149","RP0150","RP0151","RP0152","RP0153","RP0154","RP0155","RP0156","RP0157","RP0158","RP0159","RP0160","RP0161","RP0162","RP0163","RP0164","RP0165","RP0166","RP0167","RP0168","RP0169","RP0170","RP0171","RP0172","RP0173","RP0174","RP0175","RP0176","RP0177","RP0178","RP0179","RP0180","RP0181","RP0182","RP0183","RP0184","RP0185","RP0186","RP0187","RP0188","RP0189","RP0190","RP0191","RP0192","RP0193","RP0194","RP0195","RP0196","RP0197","RP0198","RP0199","RP0200","RP0201","RP0202","RP0203","RP0204","RP0205","RP0206","RP0207","RP0208","RP0209","RP0210","RP0211","RP0212","RP0213","RP0214","RP0215","RP0216","RP0217","RP0218","RP0219","RP0220","RP0221","RP0222","RP0223","RP0224","RP0225","RP0226","RP0227","RP0228","RP0229","RP0230","RP0231","RP0232","RP0233","RP0234","RP0235","RP0236","RP0237","RP0238","RP0239","RP0240","RP0241","RP0243","RP0244","RP0245","RP0246","RP0247","RP0248","RP0249","RP0250","RP0251","RP0252","RP0253","RP0254","RP0255","RP0256","RP0257","RP0258","RP0259","RP0260","RP0261","RP0262","RP0263","RP0264","RP0265","RP0266","RP0267","RP0268","RP0269","RP0270","RP0271","RP0272","RP0273","RP0274","RP0276","RP0277","RP0278","RP0279","RP0280","RP0281","RP0282","RP0283","RP0284","RP0285","RP0286","RP0287","RP0292","RP0294","RP0296","RP0298","RP0299","RP0300","RP0304","RP0306","RP0312","RP0313","YC0160","RP0340","RP0371","RP0400","RP0310","RP0329","ML0080","SW0013","AA0259","AA0256","CX0324","AA0264","AA0262","RP0402","RP0341","YC0163","RP0275","ZLH0009","RP0336","ML0090","RP0355","RP0297","SW0002","AA0257","ML0099","RP0352","RP0350","SW0004","RP0305","RP0358","RP0338","RP0324","RP0368","ML0083","AA0261","RP0401","RP0373","SW0007","RP0356","CX0320","CX0342","RP0376","RP0333","ML0087","ML0093","RP0348","ML0082","RP0362","ML0089","RP0357","RP0363","SW0005","RP0303","RP0302","RP0295","RP0293","RP0335","RP0318","RP0321","RP0319","RP0314","RP0320","RP0342","RP0326","RP0343","RP0377","RP0380","RP0364","RP0389","RP0388","RP0387","RP0386","RP0385","RP0390","RP0384","RP0383","RP0382"],"page_index":1,"page_size":65535,"user_no":"U_VXCF5C0","app_channel":0}`
	param := new(oc.RefundOrderInfoRequest)
	_ = json.Unmarshal([]byte(jsonStr), param)
	tests := []struct {
		name string
		args args
	}{
		{
			//{"search_type":0,"keyword":"","start_time":"2021-11-26 00:00:00","end_time":"2021-11-26 23:59:59","user_type":0,"refund_state":0,"order_type":0,
			//"channel_id":0,"shopids":["CX0004","CX0013","CX0011","CX0010"],"page_index":1,"page_size":65535,"user_no":"U_VZ5S65H","app_channel":0}
			name: "导出退款订单列表",
			args: args{
				in: param,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := RefundOrderExport(tt.args.in)
			if err != nil {
				t.Errorf("RefundOrderExport() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(gotOut))
		})
	}
}

func TestCardEffectiveUponPurchase(t *testing.T) {
	type args struct {
		session      *xorm.Session
		orderMain    *models.OrderMain
		productSkuId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "1",
			args: args{
				session:      nil,
				orderMain:    nil,
				productSkuId: "108389",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CardEffectiveUponPurchase(tt.args.session, tt.args.orderMain, tt.args.productSkuId); (err != nil) != tt.wantErr {
				t.Errorf("CardEffectiveUponPurchase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushOrderStatusToTencent(t *testing.T) {
	type args struct {
		orderSn    string
		cancelType int
	}
	tests := []struct {
		name string
		args args
	}{
		{name: ""}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushOrderStatusToTencent("1615133020047809", 0)
		})

	}
}

func TestAwenParentOrderExport(t *testing.T) {
	type args struct {
		params *oc.AwenParentOrderListRequest
	}

	var param = new(oc.AwenParentOrderListRequest)
	paramJson := []byte(`{"search_type":0,"keyword":"","time_type":0,"start_time":"2021-11-13 00:00:00","end_time":"2021-11-15 23:59:59","product_name":"","channel_id":0,"order_status":0,"order_type":"","delivery_type":0,"pay_mode":0,"page_index":1,"page_size":10,"shopids":["CX0004"],"user_no":"","app_channel":0,"combine_type":1,"ip":"1","ip_location":"1"}`)
	_ = json.Unmarshal(paramJson, param)

	tests := []struct {
		name        string
		args        args
		wantDetails []*oc.AwenParentOrderExport
		wantErr     bool
	}{
		// TODO: Add test cases.
		{
			name: "阿闻管家父订单导出",
			args: args{
				params: param,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, err := AwenParentOrderExport(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenParentOrderExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(kit.JsonEncodeBeuty(gotDetails))
		})
	}
}

func TestMtOrderConfirm(t *testing.T) {
	type args struct {
		OrderSn   string
		channelId int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "", args: args{
			OrderSn:   "27010241600277148",
			channelId: 2,
		}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if _, err := MtOrderConfirm(tt.args.OrderSn, tt.args.channelId, 2); (err != nil) != tt.wantErr {
				t.Errorf("MtOrderConfirm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushSubscribeMessage(t *testing.T) {
	type args struct {
		param mc.SubscribeMessageRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "体验版发送通知",
			args: args{param: mc.SubscribeMessageRequest{
				Touser:           "oNtqZ67tMuVOAwkiMYeZckk86MlA",
				TemplateId:       "vEfrhipfKQY9nfEhjCHxBjEyo6xX_umFfoHXr9uYGAE",
				Page:             "",
				MiniprogramState: "",
				Lang:             "",
				OrgId:            3,
				Data:             "{\"thing3\": { \"value\": \"xxxxx\" },\"thing10\": {\"value\": \"xxxx\" }}",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := PushSubscribeMessage(tt.args.param); (err != nil) != tt.wantErr {
				t.Errorf("PushSubscribeMessage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkMallStock(t *testing.T) {
	for i := 0; i < 500; i++ {
		go CheckMallStock("1020754001", 1)
	}
}

func BenchmarkCheckSecKillStock(b *testing.B) {
	for i := 0; i < b.N; i++ {
		FreedSecKillStock("1020754001", 1, 100)
	}
}

func Test_freedSecKillStock(t *testing.T) {
	type args struct {
		sku       string
		secKillId int32
		number    int32
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		want1 error
		want2 string
	}{
		// TODO: Add test cases.
		{
			name: "测试释放库存",
			args: args{
				sku:       "1020754001",
				secKillId: 1,
				number:    100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FreedSecKillStock(tt.args.sku, tt.args.secKillId, tt.args.number)
		})
	}
}

func TestFreedSecKillStockByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "测试释放库存",
			args: args{
				orderSn: "4100000008205132",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FreedSecKillStockByOrderSn(tt.args.orderSn)
		})
	}
}

func Test_pushAdvertisementMp(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "1231321"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PushAdvertisementMp("4100000010721299")
		})
	}
}

func TestGetVerifyCodesByGroupSkuId(t *testing.T) {
	type args struct {
		orderSn    string
		groupSkuId string
		status     []int32
	}
	tests := []struct {
		name                 string
		args                 args
		wantOrderVerifyCodes []models.OrderVerifyCode
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn:    "4100000012369482",
				groupSkuId: "1031456099",
				status:     []int32{1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotOrderVerifyCodes := GetVerifyCodesByGroupSkuId(tt.args.orderSn, tt.args.groupSkuId, tt.args.status...); !reflect.DeepEqual(gotOrderVerifyCodes, tt.wantOrderVerifyCodes) {
				t.Errorf("GetVerifyCodesByGroupSkuId() = %v, want %v", gotOrderVerifyCodes, tt.wantOrderVerifyCodes)
			}
		})
	}
}

func TestQueryChildProducts(t *testing.T) {
	type args struct {
		parent        *oc.OrderProductModel
		channelId     int
		WarehouseType int32
		financeCode   string
	}
	parentJson := []byte(`{
            "id":"",
            "order_id":"",
            "order_sn":"",
            "sku":"1031397099",
            "parent_sku_id":"",
            "product_id":"1031397",
            "product_name":"临期虚实组合测试商品11101",
            "product_type":3,
            "combine_type":0,
            "bar_code":"",
            "price":0,
            "number":0,
            "specs":"",
            "payment_total":0,
            "privilege":0,
            "freight":0,
            "marking_price":0,
            "image":"",
            "deliver_num":0,
            "refund_num":0,
            "privilege_pt":0,
            "privilege_total":0,
            "pay_price":0,
            "sub_biz_order_id":"2055971202",
            "promotion_id":0,
            "article_number":"",
            "promotion_type":1,
            "sku_pay_total":0,
            "used_num":0,
            "term_type":0,
            "term_value":0,
            "mall_order_product_id":0,
            "virtual_invalid_refund":0,
            "is_third_product":0,
            "warehouse_type":0,
            "group_item_num":0
        }`)
	var parent oc.OrderProductModel
	_ = json.Unmarshal(parentJson, &parent)
	tests := []struct {
		name         string
		args         args
		wantChildren []*oc.OrderProductModel
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				parent:        &parent,
				channelId:     2,
				WarehouseType: 3,
				financeCode:   "CX0011",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotChildren := QueryChildProducts(tt.args.parent, tt.args.channelId, tt.args.WarehouseType, tt.args.financeCode); !reflect.DeepEqual(gotChildren, tt.wantChildren) {
				t.Errorf("QueryChildProducts() = %v, want %v", gotChildren, tt.wantChildren)
			}
		})
	}
}

func TestGetVerifyCodesStatus(t *testing.T) {
	type args struct {
		orderSn []string
	}
	tests := []struct {
		name string
		args args
		want map[string]int32
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: []string{"4100000012697943", "4000000001343348"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetVerifyCodesStatus(tt.args.orderSn); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetVerifyCodesStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAwenVirtualOrderProductExport(t *testing.T) {
	type args struct {
		params *oc.AwenVirtualOrderListRequest
	}

	jsonstr := `{"search_type":2,"keyword":"4100000015445102","time_type":0,"start_time":"2022-05-25 00:00:00","end_time":"2022-05-27 23:59:59","product_name":"","channel_id":0,"order_status":0,"order_type":"","page_index":1,"page_size":65535,"shopids":null,"user_no":"U_VNGERW0","pay_mode":0,"ip":"************","ip_location":""}`
	in := new(oc.AwenVirtualOrderListRequest)
	_ = json.Unmarshal([]byte(jsonstr), in)
	tests := []struct {
		name                string
		args                args
		wantDetails         []*oc.AwenVirtualOrderProductExport
		wantCombinedProduct map[string]string
		wantErr             bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				params: in,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, gotCombinedProduct, err := AwenVirtualOrderProductExport(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenVirtualOrderProductExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.wantDetails) {
				t.Errorf("AwenVirtualOrderProductExport() gotDetails = %v, want %v", gotDetails, tt.wantDetails)
			}
			if !reflect.DeepEqual(gotCombinedProduct, tt.wantCombinedProduct) {
				t.Errorf("AwenVirtualOrderProductExport() gotCombinedProduct = %v, want %v", gotCombinedProduct, tt.wantCombinedProduct)
			}
		})
	}
}

/*func TestRealPaymentVirtualGoods(t *testing.T) {
	type args struct {
		OrderSn   string
		channelId int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "", args: args{
			OrderSn:   "27010241600277148",
			channelId: 2,
		}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			price := RealPaymentVirtualGoods(868136)
			fmt.Println(" price:", price)
		})
	}
}*/

func TestPushDigitalHealthOrder(t *testing.T) {
	type args struct {
		orderSn        string
		consultOrderSn string
		orderStatus    int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "测试推送互联网医疗订单",
			args: args{
				orderSn:        "fsdee",
				consultOrderSn: "1020220510000028",
				orderStatus:    1,
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := PushDigitalHealthOrder(tt.args.orderSn, tt.args.consultOrderSn, tt.args.orderStatus, 0); (err != nil) != tt.wantErr {
				t.Errorf("PushDigitalHealthOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpdateCommunityGroupPayAmount(t *testing.T) {
	type args struct {
		orderSn   string
		payAmount int32
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "UpdateCommunityGroupPayAmount",
			args: args{
				orderSn:   "4100000014584164",
				payAmount: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			UpdateCommunityGroupPayAmount(tt.args.orderSn, tt.args.payAmount, true)
		})
	}
}

func TestAwenOrderExportProduct(t *testing.T) {
	type args struct {
		params *oc.AwenMaterOrderListRequest
	}
	jsonstr := `{"search_type":1,"keyword":"4100000015438702","time_type":0,"start_time":"2022-05-25 00:00:00","end_time":"2022-05-27 23:59:59","product_name":"","channel_id":0,"order_status":0,"order_type":"","delivery_type":0,"pay_mode":0,"page_index":1,"page_size":10,"shopids":null,"user_no":"U_VNGERW0","app_channel":0,"ip":"","ip_location":"","pickup_station_id":0}`
	in := new(oc.AwenMaterOrderListRequest)
	_ = json.Unmarshal([]byte(jsonstr), in)
	tests := []struct {
		name                string
		args                args
		wantDetails         []*oc.AwenOrderProductExport
		wantCombinedProduct map[string]string
		wantErr             bool
	}{
		{name: "导出实物订单商品明细"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, gotCombinedProduct, err := AwenOrderExportProduct(in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenOrderExportProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.wantDetails) {
				t.Errorf("AwenOrderExportProduct() gotDetails = %v, want %v", gotDetails, tt.wantDetails)
			}
			if !reflect.DeepEqual(gotCombinedProduct, tt.wantCombinedProduct) {
				t.Errorf("AwenOrderExportProduct() gotCombinedProduct = %v, want %v", gotCombinedProduct, tt.wantCombinedProduct)
			}
		})
	}
}

func TestAwenParentOrderProductExport(t *testing.T) {
	type args struct {
		params *oc.AwenParentOrderListRequest
	}
	jsonstr := `{"search_type":0,"keyword":"","time_type":0,"start_time":"2022-05-24 00:00:00","end_time":"2022-05-26 23:59:59","product_name":"","channel_id":0,"order_status":0,"sale_channel":0,"order_type":"","delivery_type":0,"pay_mode":0,"pay_sn":"","page_index":1,"page_size":65535,"shopids":null,"user_no":"U_VNGERW0","app_channel":0,"combine_type":0,"ip":"************","ip_location":"","order_filter":0,"pickup_station_id":0,"query_special":0,"order_group_activity_id":0}`
	in := new(oc.AwenParentOrderListRequest)
	_ = json.Unmarshal([]byte(jsonstr), in)
	tests := []struct {
		name                string
		args                args
		wantDetails         []*oc.AwenParentOrderProductExport
		wantCombinedProduct map[string]string
		wantErr             bool
	}{
		{name: "导出父订单商品明细"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, gotCombinedProduct, err := AwenParentOrderProductExport(in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenParentOrderProductExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.wantDetails) {
				t.Errorf("AwenParentOrderProductExport() gotDetails = %v, want %v", gotDetails, tt.wantDetails)
			}
			if !reflect.DeepEqual(gotCombinedProduct, tt.wantCombinedProduct) {
				t.Errorf("AwenParentOrderProductExport() gotCombinedProduct = %v, want %v", gotCombinedProduct, tt.wantCombinedProduct)
			}
		})
	}
}

func TestAwenOrderExport(t *testing.T) {
	type args struct {
		params *oc.AwenMaterOrderListRequest
	}
	jsonstr := `{"search_type":1,"keyword":"4100000015452501","time_type":0,"start_time":"2022-05-25 00:00:00","end_time":"2022-05-27 23:59:59","product_name":"","channel_id":0,"order_status":0,"order_type":"","delivery_type":0,"pay_mode":0,"page_index":1,"page_size":65535,"shopids":null,"user_no":"U_VNGERW0","app_channel":0,"ip":"************","ip_location":"","pickup_station_id":0}`
	in := new(oc.AwenMaterOrderListRequest)
	_ = json.Unmarshal([]byte(jsonstr), in)
	tests := []struct {
		name        string
		args        args
		wantDetails []*oc.AwenOrdeExport
		wantErr     bool
	}{
		{name: "导出实物订单"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDetails, err := AwenOrderExport(in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AwenOrderExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.wantDetails) {
				t.Errorf("AwenOrderExport() gotDetails = %v, want %v", gotDetails, tt.wantDetails)
			}
		})
	}
}

func TestUpdateUserHealth(t *testing.T) {
	type args struct {
		orderSn string
		params  models.HealthDetail
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "TestUpdateUserHealth",
			args: args{
				orderSn: "4100000014606855",
				params: models.HealthDetail{
					Type:       2,
					Title:      "线上消费",
					Content:    "小程序支付获得",
					HealthType: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			UpdateUserHealth(tt.args.orderSn, tt.args.params)
		})
	}
}

func TestGetVerifyCode(t *testing.T) {
	type args struct {
		number []int
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "test",
			args: args{number: []int{10}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetNewVerifyCode(tt.args.number...); !reflect.DeepEqual(got, tt.want) {
				fmt.Println(got)
			}
		})
	}
}

func Test_distribute(t *testing.T) {
	member := new(models.UpetMember)
	GetUPetDBConn().Where("member_mobile = '17704021685'").Get(member)

	type args struct {
		member  *models.UpetMember
		disId   int32
		disType int32
	}
	tests := []struct {
		name    string
		args    args
		want    *DistributeInfo
		wantErr bool
	}{
		{
			args: args{
				member:  member,
				disId:   828,
				disType: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := distribute(tt.args.member, tt.args.disId, tt.args.disType)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

package models

type OrderPromotionRecord struct {
	Id         string `xorm:"not null pk default ''uuid()'' VARCHAR(50)"`
	OrderSn    string `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	OrderDcId  string `xorm:"not null default '''' comment('订单商品表id') VARCHAR(50)"`
	Sku        string `xorm:"not null default '''' comment('sku') VARCHAR(50)"`
	Promotion  int    `xorm:"not null default 0 comment('优惠金额') INT(11)"`
	Num        int    `xorm:"not null default 0 comment('数量') INT(11)"`
	IsIncrease int    `xorm:"not null default 0 comment('是否是金额更多的商品0否 1是') TINYINT(1)"`
}

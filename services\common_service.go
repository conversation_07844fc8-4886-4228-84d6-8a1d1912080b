package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"order-center/pkg/code"
	"order-center/proto/cc"
	"order-center/proto/dac"
	"order-center/proto/dc"
	"order-center/proto/igc"
	"order-center/proto/sv"
	"strconv"
	"strings"
	"time"

	"order-center/dto"
	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/ic"
	"order-center/proto/mk"
	"order-center/proto/oc"
	"order-center/proto/pc"
	"order-center/utils"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

type CommonService struct {
	BaseService
	session       *xorm.Session
	orderMain     *models.OrderMain
	orderDetail   *models.OrderDetail
	diagnoseInfo  *models.DiagnoseInfo
	orderProducts []*models.OrderProduct
	DeliverInfo   *dto.DeliverPriceRes
}

// 推送到全渠道
func (c *CommonService) PushAllChannel() (err error) {
	glog.Info("推送全渠道接口参数：", kit.JsonEncode(c.orderMain))

	orderMain := *c.orderMain

	orderProductModel := c.GetAllOrderProduct()
	if len(orderProductModel) == 0 {
		return errors.New("推送全渠道查询订单商品信息为空")
	}

	var orderParmas []dto.OrderDetailsParam
	deliverLog := dto.DeliverLog{}
	var PaymentTotal int32
	for _, goods := range orderProductModel {
		Payment := goods.Number * goods.PayPrice
		PaymentTotal += Payment
		OrderDetailsParam := dto.OrderDetailsParam{
			Oid:          cast.ToString(goods.Id),
			Barcode:      goods.SkuId, //todo 不用转全渠道id，全渠道商品与商品中心以skuid做关联
			Eshopgoodsid: goods.SkuId, //todo 不用转全渠道id，全渠道商品与商品中心以skuid做关联
			Num:          fmt.Sprintf("%d", goods.Number),
			Payment:      fmt.Sprintf("%.2f", float64(Payment)/100),
			Weight:       "0",
			Size:         "0",
		}

		orderParmas = append(orderParmas, OrderDetailsParam)
		deliverLog.Goodslist = append(deliverLog.Goodslist, struct{ GoodsId string }{GoodsId: goods.SkuId})
	}

	//FreightPrivilege := c.FreightCal()
	Freight := orderMain.Freight - c.FreightCal()

	//均摊总额不等于总额需要调整商品总额，均摊总额等于商品总额
	PrivilegeGoodsTotal := orderMain.Total - Freight - orderMain.PackingCost //优惠后商品价格
	if PaymentTotal != PrivilegeGoodsTotal {
		difference := PrivilegeGoodsTotal - PaymentTotal //差额
		for i, i2 := range orderParmas {
			if difference != 0 {
				PaymentInt := int32(kit.YuanToFen(cast.ToFloat64(i2.Payment)))
				if PaymentInt == 0 {
					continue
				}

				if difference > 0 {
					orderParmas[i].Payment = fmt.Sprintf("%.2f", kit.FenToYuan(int64(PaymentInt+difference)))
					difference -= difference
				} else {
					if -difference > PaymentInt {
						orderParmas[i].Payment = fmt.Sprintf("%.2f", kit.FenToYuan(int64(PaymentInt-(PaymentInt-1))))
						difference += PaymentInt - 1
					} else {
						orderParmas[i].Payment = fmt.Sprintf("%.2f", kit.FenToYuan(int64(PaymentInt+difference)))
						difference -= difference
					}
				}
			}
		}
	}
	orderMain.Privilege = 0

	//实付价格加优惠金额
	//2020-10-15 去掉总优惠，单价传实付价（均摊价）
	TotalMoneystr := fmt.Sprintf("%.2f", kit.FenToYuan(orderMain.Total))
	OrderParam := dto.OrderParam{
		Tid:              orderMain.OrderSn,
		Weight:           "0",
		Size:             "0",
		Buyernick:        "",
		Buyermessage:     orderMain.OldOrderSn, //方便对账去掉买家留言，传美团单号
		Sellermemo:       orderMain.WarehouseCode,
		Total:            TotalMoneystr, //实付价格加优惠金额
		Privilege:        "0",
		Postfee:          fmt.Sprintf("%.2f", kit.FenToYuan(Freight+orderMain.PackingCost)), //加上包装费，不然全渠道总价对不上
		Receivername:     orderMain.ReceiverName,
		Receiverstate:    orderMain.ReceiverState,
		Receivercity:     orderMain.ReceiverCity,
		Receiverdistrict: orderMain.ReceiverDistrict,
		Receiveraddress:  orderMain.ReceiverAddress,
		Receiverphone:    utils.MobileDecrypt(orderMain.EnReceiverPhone),
		Receivermobile:   utils.MobileDecrypt(orderMain.EnReceiverMobile),
		Created:          kit.GetTimeNow(),
		Status:           "Payed",
		Type:             "NoCod",
		Paytime:          kit.GetTimeNow(),
		Btypecode:        HashGet("store:relation:dctoqqd", orderMain.ShopId), //根据门店财务编码获取redis数据
		Details:          orderParmas,
	}

	gjpOrder := dto.Orders{}
	gjpOrder.Orders = append(gjpOrder.Orders, OrderParam)
	glog.Info("推送全渠道参数：order_sn：", orderMain.OldOrderSn, kit.JsonEncode(gjpOrder))
	res, err := c.OrderSynchronizeNew(&gjpOrder)
	glog.Info("全渠道推送返回参数: ", orderMain.OldOrderSn, kit.JsonEncode(res), err)

	if err != nil {
		glog.Error("推送全渠道返回错误：", err.Error()+" order_sn:", orderMain.OldOrderSn, " "+kit.JsonEncode(gjpOrder))
		return errors.New("网络错误！")
	}
	if res.Code < 0 {
		glog.Error(res.Message, orderMain.OldOrderSn, "推送全渠道返回错误2：", res.Message)
		//if res.Code == -4 {
		//	return errors.New(res.Message)
		//}
		return errors.New(res.Message)
	}
	//扣库存mq
	c.InventoryStock(deliverLog)

	return nil
}

// todo 推送全渠道 v6.5.0
func (c *CommonService) PushRPOmsOrder() (httpCode int, ret *dto.RpOmsBaseResponse, err error) {
	glog.Info("推送OMS订单参数：", kit.JsonEncode(c.orderMain))
	request := new(dto.AddOmsOrderHttpRequest)

	request.ChannelOrderSn = c.orderMain.OrderSn
	request.OrderSource = c.orderMain.ChannelId
	request.ShopId = c.orderMain.ShopId
	request.ShopName = c.orderMain.ShopName

	request.MemberId = c.orderMain.MemberId
	if c.orderMain.MemberName != "" {
		request.MemberName = c.orderMain.MemberName
	} else {
		request.MemberName = c.orderMain.ReceiverName
	}
	IsThirdDeliver := c.IsThirdDeliver()

	request.PackingFee = c.orderMain.PackingCost
	request.ServiceFee = c.orderMain.ServiceCharge

	//商品优惠 + 运费优惠 因为拆单时未将运费优惠算到订单子单整单优惠上
	request.Privilege = c.orderMain.Privilege

	request.ContractFee = c.orderMain.ContractFee
	request.OrderStatus = models.OmsOrderStatusDelivered
	request.PayType = models.OmsPayTypePayFirst
	request.CreateType = models.OmsCreateTypePush
	request.OrderType = models.OmsOrderTypeB2C
	request.SaleChannelId = models.ParseOmsSaleChannel(c.orderMain.UserAgent)
	request.ReceiverName = c.orderMain.ReceiverName
	request.ReceiverProvince = c.orderMain.ReceiverState
	request.ReceiverCity = c.orderMain.ReceiverCity
	request.ReceiverDistrict = c.orderMain.ReceiverDistrict
	request.ReceiverAddress = c.orderMain.ReceiverAddress
	request.ReceiverPhone = c.orderMain.EnReceiverPhone

	request.DeliveryWarehouseCode = c.orderMain.WarehouseCode
	request.DeliverWarehouseName = c.orderMain.WarehouseName

	request.IsVirtual = c.orderMain.IsVirtual

	request.DeliveryType = models.ParseOmsOrderDeliverType(c.orderMain.DeliveryType, IsThirdDeliver)

	//第三方订单传第三方的订单号
	if c.IsThirdOrder() {
		parentOrder := GetOrderMainByOrderSn(c.orderMain.ParentOrderSn, "old_order_sn")
		request.TradeOrderSn = parentOrder.OldOrderSn
	} else {
		request.TradeOrderSn = c.orderMain.OrderSn
	}

	orderDetail := GetOrderDetailByOrderSn(c.orderMain.OrderSn)

	if !orderDetail.AcceptTime.IsZero() {
		request.OrderTime = orderDetail.AcceptTime.Format(kit.DATETIME_LAYOUT)
	} else {
		request.OrderTime = time.Now().Format(kit.DATETIME_LAYOUT)
	}
	//用户下单时间为阿闻订单的创建时间
	request.UserSubmitTime = c.orderMain.CreateTime.Format(kit.DATETIME_LAYOUT)

	request.Latitude = orderDetail.Latitude
	request.Longitude = orderDetail.Longitude
	request.BuyerMemo = orderDetail.BuyerMemo
	request.SellerMemo = orderDetail.SellerMemo
	request.Note = ""
	// 税率 特指物流税率
	request.TaxRate = 6
	if !orderDetail.ExpectedTime.IsZero() {
		request.ArriveTime = orderDetail.ExpectedTime.Format(kit.DATETIME_LAYOUT) // 预计到达时间
	}
	//订单商品
	orderProductModel := c.GetAllOrderProduct()
	if len(orderProductModel) == 0 {
		return 0, new(dto.RpOmsBaseResponse), errors.New("推送OMS查询订单商品信息为空")
	}

	//平台优惠
	var products []*dto.OmsOrderProduct
	for _, v := range orderProductModel {
		item := new(dto.OmsOrderProduct)
		item.ItemNum = v.ThirdSkuId
		item.ChannelProductId = cast.ToString(v.Id)
		item.PayPrice = v.PayPrice
		item.PaymentTotal = v.PaymentTotal
		item.PrivilegeTotal = v.PrivilegeTotal
		item.Number = v.Number
		item.IsFree = 0
		request.GoodsTotal += v.PaymentTotal
		//如果MarkingPrice == 0 则根据优惠金额与支付单价逆向算出一个markingPrice
		if v.MarkingPrice == 0 {
			v.MarkingPrice = cast.ToInt32(cast.ToFloat64(item.PaymentTotal+item.PrivilegeTotal) / cast.ToFloat64(item.Number))
		}
		item.MarkingPrice = v.MarkingPrice
		item.Total = v.MarkingPrice * v.Number
		products = append(products, item)
	}
	request.OrderProduct = products

	//平台优惠 & 将阿闻的优惠信息转换成oms的优惠信息推送给oms
	var promotions []*dto.OmsOrderPromotion
	orderPromotion := c.GetOrderPromotion()
	for _, item := range orderPromotion {
		request.PlatformPrivilege += item.PtCharge

		promotionItem := new(dto.OmsOrderPromotion)
		promotionItem.PromotionId = int(item.PromotionId)
		promotionItem.PromotionType = int(item.PromotionType)
		promotionItem.PromotionTitle = item.PromotionTitle
		promotionItem.PoiCharge = int(item.PoiCharge)
		promotionItem.PtCharge = int(item.PtCharge)
		promotionItem.PromotionFee = int(item.PromotionFee)
		promotions = append(promotions, promotionItem)
	}
	request.OrderPromotion = promotions

	//推送到oms 需要加上平台补贴
	request.PayTotal = c.orderMain.Total //实际支付金额+平台优惠

	//第三方配送  收入=商品优惠后金额（含税）+包装费+平台补贴
	//因为在阿闻 运费优惠在阿闻并没有摊在商品实际支付金额上 所以需要减掉运费优惠  在oms端会将运费优惠摊在运费优惠上
	if IsThirdDeliver {
		request.Freight = c.orderMain.Freight
		request.FreightPrivilege = c.orderMain.FreightPrivilege
		request.Income = request.GoodsTotal + request.PackingFee + request.PlatformPrivilege - request.FreightPrivilege
	} else {
		request.Freight = c.orderMain.Freight - c.orderMain.FreightPrivilege
		//自配送运费优惠为0
		request.FreightPrivilege = 0
		//自配送 = 商品优惠后金额（含税）+包装费+平台补贴+运费-运费优惠
		request.Income = request.GoodsTotal + request.PackingFee + request.PlatformPrivilege + request.Freight
		//自配送运费 = 运费-运费优惠
	}

	//处理支付信息 并推送给oms
	request.OrderPay = new(dto.OmsOrderPay)
	request.OrderPay.PayType = 1
	request.OrderPay.PaySn = c.orderMain.PaySn
	request.OrderPay.PayMode = c.orderMain.PayMode
	request.OrderPay.PayStatus = 1
	if c.orderMain.IsPay == 1 {
		request.OrderPay.PayStatus = 2
	}
	request.OrderPay.PayTime = c.orderMain.PayTime.Format(kit.DATETIME_LAYOUT)
	request.OrderPay.PayAmount = c.orderMain.Total

	//请求oms
	httpCode, ret, err = new(RpomsService).OrderAdd(request)
	if err != nil {
		return httpCode, ret, err
	}

	if httpCode == 200 {
		//扣库存mq，同步最新库存到各个渠道
		deliverLog := dto.DeliverLog{IsRPOms: true}
		for _, product := range orderProductModel {
			deliverLog.Goodslist = append(deliverLog.Goodslist, struct{ GoodsId string }{GoodsId: product.SkuId})
		}
		c.InventoryStock(deliverLog)
	}

	return httpCode, ret, nil
}

// 订单同步 emall.order.synchronize
// 注：除电商渠道的其他渠道(美团，京东，饿了么，阿闻到家)的订单同步
func (c *CommonService) OrderSynchronizeNew(params *dto.Orders) (*dto.ReOrders, error) {
	initqqd()

	signByte := kit.JsonEncodeByte(params)
	signStr := string(signByte)
	glog.Info("全渠道订单发货内容:" + signStr)

	//获取验签字符串
	parStr := utils.Sign(signStr, "emall.order.synchronize", tokenNew, appkeyConNew, appsecretConNew)
	glog.Info("zx测试全渠道地址:" + apiUrl + parStr)
	respBytes, err := utils.HttpPost(apiUrl+parStr, signByte, "application/json;charset=UTF-8")
	if err != nil {
		glog.Error("订单添加请求错误：", err.Error())
		return nil, err
	}
	retmes := new(dto.ReOrders)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		glog.Error("订单添加转JSON错误：", err.Error())
		return nil, err
	}
	vos := make([]*sv.SalesRecordVo, 0)
	for _, x := range params.Orders {
		for _, x1 := range x.Details {
			vo := sv.SalesRecordVo{}
			vo.OrderSn = x.Tid
			vo.SkuId = x1.Eshopgoodsid
			vo.Status = 1
			vo.Num = cast.ToInt32(x1.Num)
			vos = append(vos, &vo)
		}
	}
	recordVos := sv.SalesRecordVos{
		Data: vos,
	}
	client := sv.GetStockVisualClient()
	defer client.Close()

	glog.Info("下单可视化参数：", kit.JsonEncode(recordVos))
	_, errStock := client.RPC.InsertIntoSalesRecord(client.Ctx, &recordVos) // 可视化退款插入
	if errStock != nil {
		glog.Error("client.RPC.InsertIntoSalesRecord(client.Ctx, &recordVos)", errStock.Error())
	}

	return retmes, err
}

// 是否为自配送
func (c *CommonService) IsSelfDistribution() bool {
	if (c.orderMain.ChannelId == ChannelMtId && !strings.Contains("2002,1001,1004,2010,3001,1007", c.orderMain.LogisticsCode)) || //不等于美团专送的，其余的就是自配送
		(c.orderMain.ChannelId == ChannelElmId && c.orderMain.LogisticsCode == "6") ||
		(c.orderMain.ChannelId == ChannelJddjId && c.orderMain.LogisticsCode == "2938") {
		return true
	}

	return false
}

// 计算差额(商家应收金额-商品实付金额-运费-包装费)
func (c *CommonService) GetDifference(actulyPayed int, freight int, packingCost int, goodsPayTotal int) int {

	ret := actulyPayed - (goodsPayTotal + freight + packingCost)
	return ret
}

// 推送到子龙
func (c *CommonService) PushZilong(retry bool) (retCode int, err error) {
	glog.Info(c.orderMain.OrderSn, "推送子龙开始", c.orderMain)
	//推送子龙增加一个短时间的锁  避免短时间内连续推送 子龙那边做的幂等在很短的时间内是不可靠的

	//订单机构ID
	zlShopIdInt := c.ShopIdToZilongId(c.orderMain.ShopId)
	if zlShopIdInt == 0 {
		return code.ZiLongShopNotExist, errors.New("店铺未找到对应子龙ID！")
	}

	//出库机构ID
	zlWarehouse := c.ShopIdToZilongId(c.orderMain.WarehouseCode)
	if zlWarehouse == 0 {
		return code.ZiLongShopNotExist, errors.New("出货仓库未找到对应子龙ID！")
	}

	////计算运费优惠add by csf@20201102
	//freightPrivilege := c.FreightCal()

	//处理优惠金额，读取美团优惠信息中的商家承担金额 -- 尚笑要求的。2020.06.03确认的邮件
	var (
		PresentPayedAmount  int32 //商家承担的优惠金额
		PlatformPayedAmount int32 //美团平台承担的优惠金额
		//CombinePrivilegeAmount int32 //组合优惠
	)
	orderPromotion := c.GetOrderPromotion()
	if len(orderPromotion) > 0 {
		for _, v := range orderPromotion {
			PresentPayedAmount += v.PoiCharge
			//第三方订单的平台优惠 需要从商品均摊里取
			PlatformPayedAmount += v.PtCharge
		}
	}

	var DeliverLog dto.DeliverLog

	var OrdersDetails []dto.OrdersDetails
	//所有商品实付+平台补贴的，等于是算商家收入的
	goodsPayTotalInt := int32(0)
	if err = func() error {

		orderProductModel := c.GetAllOrderProduct()
		if len(orderProductModel) == 0 {
			return errors.New("推送子龙查询订单商品信息为空")
		}

		//var skuIds []int32
		skuMap := make(map[string]int32, 0)
		var thirdPtCharge int32
		var thirdPoiCharge int32

		for _, i2 := range orderProductModel {
			if _, ok := skuMap[i2.SkuId]; !ok {
				skuMap[i2.SkuId] = i2.SkuPayTotal
				//不存在的sku价格合才加上去，因为只要是相同的SKUID都是所有行里面的sku总和，不是单行数据的
				goodsPayTotalInt += i2.SkuPayTotal
			}
			thirdPtCharge += i2.PrivilegePt
			thirdPoiCharge += i2.Privilege
		}
		//第三方订单 因为平台
		if c.IsThirdOrder() {
			PlatformPayedAmount = thirdPtCharge
			PresentPayedAmount = thirdPoiCharge
		}

		orderDetailMap := map[string]*dto.OrdersDetails{}

		for _, v := range orderProductModel {
			itemCode := v.ThirdSkuId //ItemCodeMap[cast.ToInt32(v.SkuId)]
			if len(itemCode) == 0 {
				return errors.New("未找到对应子龙sku: " + v.SkuId)
			}

			skuPayTotalInt := int32(0)
			if val, ok := skuMap[v.SkuId]; ok {
				skuPayTotalInt += val
			}

			skuPayTotal := kit.FenToYuan(skuPayTotalInt)

			if _, ok := orderDetailMap[itemCode]; ok {
				orderDetailMap[itemCode].Count += v.Number
			} else {
				cleanItemCode := utils.StringCleaning(itemCode)
				orderDetailMap[itemCode] = &dto.OrdersDetails{
					ItemCode:    cleanItemCode,
					UnitPrice:   kit.FenToYuan(v.MarkingPrice),
					Count:       v.Number,
					DetailId:    c.orderMain.OrderSn + cleanItemCode,
					ActulyPayed: skuPayTotal,
				}
			}

			DeliverLog.Goodslist = append(DeliverLog.Goodslist, dto.GoodsId{GoodsId: v.SkuId})
		}

		for _, v := range orderDetailMap {
			OrdersDetails = append(OrdersDetails, *v)
		}

		return nil
	}(); err != nil {
		return code.ZiLongErrorCommon, err
	}

	orderDetail := new(models.OrderDetail)
	c.session.ID(c.orderMain.OrderSn).Get(orderDetail)
	if len(orderDetail.Extras) > 0 {
		orderDiscountExtraList := make([]dto.OrderDiscountExtra, 0)
		json.Unmarshal([]byte(orderDetail.Extras), &orderDiscountExtraList)
	}

	//是否为商家自配
	selfDistribution := c.IsSelfDistribution()

	total := c.orderMain.Total
	freight := c.orderMain.Freight

	//不推打包费 商品实际支付价格没有均摊掉包装费
	//total -= c.orderMain.PackingCost
	packingCost := c.orderMain.PackingCost

	//ChannelId 1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
	//子龙OrderSource 8阿闻电商 9美团 10饿了么 11阿闻到家 12京东到家 13门店 15互联网医疗
	orderFrom := 0
	switch c.orderMain.ChannelId {
	case 1:
		orderFrom = 11
	case 2:
		orderFrom = 9
	case 3:
		orderFrom = 10
	case 4:
		orderFrom = 12
	case 5:
		orderFrom = 8
	case 6:
		orderFrom = 13
	case 9:
		orderFrom = 15
	}

	var DispatchFee int

	//子龙计算口径差额
	var Difference int

	//自配送正向单不用改              配送费是优惠后的，                                              差额=0
	//逆向单：部分：应退     商品退款金额+ 包装0+配送费0   +平台补贴                     差额  =平台补贴
	//逆向单：全部：            商品金额  +包装费+配送费（优惠后）+ 剩下的平台补贴           差额 =剩下的平台补贴
	//
	//
	//非自配送：
	//正向：        商品退款金额+ 包装  +平台补贴+配送费0-配送费优惠                 差额=商家配送费优惠
	//逆向部分： 商品退款金额+配送费优惠0+包装0+平台补贴                 差额=平台补贴
	//逆向全部：商品退款金额+配送费优惠+包装+剩下平台补贴               差额=商家配送费优惠+剩下平台补贴

	//配送费：
	//自配送：运费-运费优惠
	//非自配：0

	//运费等于实际支付配送费
	DispatchFee = int(freight - c.orderMain.FreightPrivilege - c.orderMain.PtFreightPrivilege)

	//商家应收金额=商品实付金额（包含了商品的平台补贴）+包装费+平台补贴运费
	actulyPayed := goodsPayTotalInt + packingCost + c.orderMain.PtFreightPrivilege

	//第三方非自提且非自配 推送的总价需要减去运费
	if c.orderMain.DeliveryType != 3 && c.orderMain.ChannelId != ChannelAwenId && c.orderMain.ChannelId != ChannelDigitalHealth && !selfDistribution {
		total -= c.orderMain.Freight
		//非自配，运费=0
		DispatchFee = 0
		//饿了么实际收入不要减去商家运费优惠，饿了么那边会自己结算
		if c.orderMain.ChannelId == ChannelElmId {
			//非自配商家应收金额=商家应收金额-（平台配送费优惠）
			actulyPayed = actulyPayed - c.orderMain.PtFreightPrivilege
		} else {
			//非自配商家应收金额=商家应收金额-（商家配送费优惠+平台配送费优惠）
			actulyPayed = actulyPayed - c.orderMain.FreightPrivilege - c.orderMain.PtFreightPrivilege
		}
	} else {
		//自配商家应收=商家应收金额+配送费实际支付
		actulyPayed = actulyPayed + int32(DispatchFee)
	}
	//计算差额
	Difference = c.GetDifference(int(actulyPayed), DispatchFee, int(packingCost), int(goodsPayTotalInt))
	glog.Info(c.orderMain.OrderSn+" ", actulyPayed, DispatchFee, packingCost, Difference, int(goodsPayTotalInt))
	ZiLongPushOrder := dto.ZiLongPushOrderRequest{
		OrdersMaster: dto.OrdersMaster{
			OrgID:                  zlShopIdInt,
			PayedDate:              kit.GetTimeNow(c.orderMain.PayTime),
			StoreOrgId:             zlWarehouse,
			ActulyPayed:            kit.FenToYuan(actulyPayed),                                                                                                         //实际支付
			PresentPayedAmount:     kit.FenToYuan(PresentPayedAmount + c.orderMain.CombinePrivilege - (c.orderMain.FreightPrivilege + c.orderMain.PtFreightPrivilege)), //优惠金额包含组合商品里的优惠，不包含配送费优惠金额
			OrderCenterOrderNumber: c.orderMain.OrderSn,
			OrderSource:            orderFrom,
			ThirdOrderNumber:       c.orderMain.OldOrderSn,
			DispatchinFee:          kit.FenToYuan(DispatchFee), //北京要求传配送费优惠后金额
			PackingFee:             kit.FenToYuan(packingCost), //包装费不需要传
			PrescriptionId:         orderDetail.ConsultOrderSn,
			OrdersDetails:          OrdersDetails,
			Difference:             kit.FenToYuan(Difference), //子龙计算口径差额
		},
	}
	//
	////配送费：
	////自配送：运费-商家运费优惠
	////非自配：0-商家运费优惠
	//DispatchFee = kit.FenToYuan(freight - freightPrivilege)
	////推送时需要满足：DispatchFee + ∑orderDetails中ActulyPayed = actulyPayed
	//actulyPayed := kit.FenToYuan(total + PlatformPayedAmount) //尚笑需求：实付金额=实付金额+平台承担服务费(包装费不需要传)
	//
	//ZiLongPushOrder := dto.ZiLongPushOrderRequest{
	//	OrdersMaster: dto.OrdersMaster{
	//		OrgID:     zlShopIdInt,
	//		PayedDate: kit.GetTimeNow(c.orderMain.PayTime),
	//
	//		ActulyPayed:            actulyPayed,                                                                         //实际支付
	//		PresentPayedAmount:     kit.FenToYuan(PresentPayedAmount + c.orderMain.CombinePrivilege - freightPrivilege), //优惠金额包含组合商品里的优惠，不包含配送费优惠金额
	//		OrderCenterOrderNumber: c.orderMain.OrderSn,
	//		OrderSource:            orderFrom,
	//		ThirdOrderNumber:       c.orderMain.OldOrderSn,
	//		DispatchinFee:          DispatchFee,                //北京要求传配送费优惠后金额
	//		PackingFee:             kit.FenToYuan(packingCost), //包装费不需要传
	//		PrescriptionId:         orderDetail.ConsultOrderSn,
	//		OrdersDetails:          OrdersDetails,
	//		Difference:             Difference, //子龙计算口径差额
	//	},
	//}

	// 执行推送子龙订单
	dataJson := kit.JsonEncodeByte(ZiLongPushOrder)
	url := config.GetString("bj-scrm-url") + "ordercenter/ordersMaster/pushorder"
	var baseRes = &dto.ZiLongPushOrderResponse{}

	glog.Info(c.orderMain.OldOrderSn, "-订单推送子龙请求参数：", string(dataJson), ",", url)
	redisConn := GetRedisConn()

	//上个锁  避免因某种原因短时间内多次推送 在短时间内 目前子龙的幂等并不可靠
	lockCard := "lock:order_push_zilong:" + c.orderMain.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 25*time.Second).Val()
	if !lockRes {
		return code.ZiLongErrorCommon, errors.New("不要频繁推送子龙")
	}

	defer redisConn.Del(lockCard)
	retCode, resData, err := utils.HttpPostZl(url, dataJson, "")
	glog.Info(c.orderMain.OldOrderSn, "-订单推送子龙返回结果", string(resData), err, ",", url)

	//请求失败 且不是重试的时候才扔mq 也就是重试的时候 如果失败 不再扔mq,否则会可能造成无限循环
	if retCode == code.HttpRequestError && retry == false {
		c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
			Count:   0,
			Type:    dto.RePushZiLongOrder,
			OrderSn: c.orderMain.OrderSn,
		})
	}

	if err != nil {
		return code.ZiLongErrorCommon, err
	}

	if err = json.Unmarshal(resData, baseRes); err != nil {
		//加一行日志检测线上是否存在解码错误 但是推单成功的情况
		glog.Error(c.orderMain.OldOrderSn, "-订单推送子龙接结果解码失败", err, resData)
		return code.ZiLongErrorCommon, err
	}

	//正常返回业务错误 不进行重试
	//v5.6.8 修改
	reduceStock := true
	if !baseRes.Success {
		errMessage := baseRes.Message
		//如果错误码为 10010 且消息为 订单已存在 则继续往下走 否则根据错误码与错误信息返回错误
		//如果创建订单时如果出现错误 但是子龙那边已经成功的情况下（例如超时），会报错，流程不能继续往下走，boss订单异常，然后手动重新推单，
		// 重新推的结果就是订单已存在，此时不返回错误，不阻止流程，让其往下走 锁库存
		//TODO  后期需要与子龙进行优化，不能靠返回的消息进行判断 最好让子龙提供单独的状态码 目前存在其他错误也返回10010的情况
		if !(baseRes.StatusCode == code.ZiLongOrderExisted && strings.Contains(errMessage, "已存在")) { //非订单已存在的错误
			//优化文案
			switch baseRes.StatusCode {
			case 10002: //库存不足
				var stockLess []string
				for _, v := range baseRes.Result.StockErrorDetails {
					stockLess = append(stockLess, v.ProductName)
				}
				errMessage = strings.Join(stockLess, "、") + errMessage
			case 10011: //金额不一致
				errMessage = "订单数据金额不一致，推送子龙系统失败"
			case 10012: //产品不存在
				errMessage = errMessage + "，推送子龙系统失败"
				//商品不存在也需要重推
				if retry == false {
					c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
						Count:   0,
						Type:    dto.RePushZiLongOrder,
						OrderSn: c.orderMain.OrderSn,
					})
				}
			case 10004: //产品不存在
				errMessage = errMessage + "，推送子龙系统失败"
				//商品不存在也需要重推
				if retry == false {
					c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
						Count:   0,
						Type:    dto.RePushZiLongOrder,
						OrderSn: c.orderMain.OrderSn,
					})
				}
			default:
				errMessage += "，推送子龙系统失败"
			}
			return code.ZiLongErrorCommon, errors.New(errMessage)
		} else {
			//如果子龙是订单已存在 对我们来说是算推送成功的 但是不能再扣库存
			reduceStock = false
		}
	}

	if len(baseRes.Result.StockErrorDetails) > 0 {
		var sku []string
		for _, i2 := range baseRes.Result.StockErrorDetails {
			sku = append(sku, i2.ItemCode)
		}
		return code.ZiLongErrorCommon, errors.New("商品库存锁定失败,sku：" + strings.Join(sku, ","))
	}

	//扣库存mq
	if reduceStock {
		c.InventoryStock(DeliverLog)
	}
	c.RePushRefundOrderToZiLongAfterPushOrderSuccess()
	return code.ZiLongSuccess, nil
}

// 暂时不需要 后续可能需要 先不删除
// 正向单推送成功之后 推送那些推送失败的退款单
// 根据push_third 字段判断是否推送失败 那么有可能存在 已经推送失败但是 该字段还未更新的情况 这种退款单无法推送
// 退款单推送失败的 只有正向单已经推送成功的或者网络问题才会重推
// 所以因为正项单没有推送过去的情况 逆向单可能因为网络原因存在重新推单的任务 不可能存在因为正向单不存在而产生的重推任务
// 因此此处重推退款单 如果因为网络问题可能 产生多个因为网络原因而失败的重试任务 那在重试的过程中 如果其中一个任务推送成功了 RefundRePushThird方法会进行拦截
// 需要保证 push_third 字段得到更新 否则可能出现多次推送 目前push_third的更新机制不仅仅是因为推送第三方失败会更新 在逻辑执行过程中其他的错误也会更新为推送失败
func (c *CommonService) RePushRefundOrderToZiLongAfterPushOrderSuccess() {
	orderSn := c.orderMain.OrderSn
	if c.IsThirdOrder() {
		orderSn = c.orderMain.ParentOrderSn
	}
	var waitSend []*models.RefundOrder
	err := c.session.Table("refund_order").Select("refund_sn").
		Where("order_sn = ? AND refund_state=3 AND push_third = 0 AND push_third_fail_reason != ''", orderSn).
		Find(&waitSend)
	if err != nil {
		glog.Error(orderSn, "-", "正向单推送成功后查询是否有推送失败的退款单失败", err)
		return
	}
	if len(waitSend) > 0 {
		go func(list []*models.RefundOrder) {
			afterSaleService := new(AfterSaleService)
			ctx := context.Background()
			for _, v := range list {
				res, err := afterSaleService.RefundRePushThird(ctx, &oc.RefundRePushThirdRequest{RefundSn: v.RefundSn})
				if res.Code != 200 || err != nil {
					glog.Error(v.RefundSn, "-", "正向单推送成功后推送退款单失败:", res.Message)
				}
			}
		}(waitSend)
	}
	return
}

// shopid转子龙机构Id,需要用仓库ID转对应的子龙ID
func (c *CommonService) ShopIdToZilongId(warehouseCode string) int {
	zlShopId := HashGet("store:relation:dctozl", warehouseCode)
	if zlShopId == "" {
		glog.Error("子龙店铺未找到！", warehouseCode, ", ", kit.RunFuncName(2))
		return 0
	}

	return cast.ToInt(zlShopId)
}

// 扣库存mq
func (c *CommonService) InventoryStock(DeliverLog dto.DeliverLog) {
	//成功后扣库存
	DeliverLog.Orderid = c.orderMain.OrderSn //添加数据，便于释放库存
	DeliverLog.Code = c.orderMain.WarehouseCode
	DeliverLog.Source = c.orderMain.Source
	DeliverLog.Isfinish = "1"

	_, err := c.session.Insert(models.MqInfo{
		Exchange: "ordercenter",
		Quene:    "dc_sz_stock_update",
		Content:  kit.JsonEncode(DeliverLog),
		Ispush:   0,
		Lastdate: time.Time{},
	})
	if err != nil {
		glog.Error("扣库存mq保存失败 ", c.orderMain.OrderSn, err.Error())
	}
}

// 推送美配订单
func (c CommonService) PushMpOrder() error {
	glog.Info(c.orderMain.OrderSn, "开始发起配送")

	if c.orderDetail == nil {
		c.orderDetail = new(models.OrderDetail)
		//这里必须用结构体内的session查询，因为提交订单时会在未提交的事务中查询，如果新开一个session会查不到
		c.orderDetail = GetOrderDetailByOrderSn(c.orderMain.OrderSn)
	}

	//获取实物子订单
	realOrder := &CommonService{
		orderMain:   new(models.OrderMain),
		session:     c.session,
		DeliverInfo: c.DeliverInfo,
	}

	if c.IsThirdOrder() && c.orderMain.ParentOrderSn == "" {
		mainOrderSn := c.orderMain.OrderSn
		has, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, "第三方订单发配送查询实物子单出错，", err)
			return errors.New("第三方订单发配送查询实物子单出错")
		}
		if has == false {
			glog.Error(mainOrderSn, "第三方订单发配送未查询到实物子订单，", err)
			return errors.New("第三方订单发配送未到查询实物子单")
		}
		realOrder.orderDetail = GetOrderDetailByOrderSn(realOrder.orderMain.OrderSn)
	} else {
		//快递配送、商家自配，不推送美配1
		if c.orderMain.DeliveryType == 1 || c.orderMain.DeliveryType == 5 {
			return nil
		}
		realOrder.orderMain = c.orderMain
		realOrder.orderDetail = c.orderDetail
	}
	//SAAS平台不发配送
	//if c.orderMain.AppChannel == 12 {
	//	return nil
	//}

	realOrder.orderProducts = GetOrderProductByOrderSn(realOrder.orderMain.OrderSn, "")
	//判断是否有退款中的退款单，是的话，不发配送，直接进入异常
	ishave := 0
	_, err := realOrder.session.SQL("SELECT 1 FROM refund_order WHERE refund_state in (1,5) and order_sn in(?,?)", realOrder.orderMain.OrderSn, realOrder.orderMain.ParentOrderSn).Get(&ishave)
	if err != nil {
		glog.Error(realOrder.orderMain.OrderSn, "发配送查询是否有未处理的退款单报错，", err)
		return errors.New("发配送查询是否有未处理的退款单报错" + err.Error())
	}
	if ishave == 1 {
		err = errors.New("发配送的时候有未处理的退款单，停止发配送")
	} else {
		//glog.Info(c.orderMain.OrderSn, "发起美团配送")
		glog.Info("发送美团配送：数据：", kit.JsonEncode(realOrder))
		err = realOrder.MpOrderCreate()
	}

	//无异常直接返回
	if err == nil {
		return nil
	}

	// 配送异常返回信息优化
	errInfo := err.Error()
	err = func() (err error) {
		if strings.Contains(errInfo, "goods_weight") {
			errInfo = "订单内商品重量不符合规范美团无法配送，请呼叫跑腿配送"
		}
		return errors.New(errInfo)
	}()
	deliveryId := GetSn("delivery")[0]
	if strings.Contains(errInfo, "自行配送") {
		deliveryId = strings.Split(errInfo, "|")[1]
	}

	//记录配送异常
	oe := OrderExceptionService{}

	ExceptionRequest := oc.OrderExceptionRequest{
		DeliveryId:     deliveryId,
		OrderId:        realOrder.orderMain.OrderSn,
		ExceptionDescr: err.Error(),
		ExceptionTime:  kit.GetTimeNow(),
		Source:         1,
		OrderStatus:    2,
	}
	res, err := oe.OrderExceptionAdd(nil, &ExceptionRequest)
	if err != nil {
		glog.Error("推送配送单失败，异常单保存失败！", realOrder.orderMain.OldOrderSn, err.Error())
	}
	if res.Code != 200 {
		glog.Error("推送配送单失败，异常单保存失败！", realOrder.orderMain.OldOrderSn, res.Message)
	}

	//通知数据中心
	go MessageCreate(&models.Message{
		OrderId:     realOrder.orderMain.OrderSn,
		MessageType: 5,
		FinanceCode: realOrder.orderMain.ShopId,
		Msg:         fmt.Sprintf("【配送异常】订单：%s，请重点关注！", realOrder.orderMain.OrderSn),
	})

	glog.Info("推送配送单流程完成 ", realOrder.orderMain.OldOrderSn)
	return nil
}

// 自动切换推送配送
func (c CommonService) AotuPushDelivery(orderSn string, deliverInfo dto.DeliverPriceRes) error {
	c.session = GetDBConn().NewSession()
	defer c.session.Close()

	//if c.orderMain == nil {
	c.orderMain = new(models.OrderMain)
	c.orderMain = GetOrderMainByOrderSn(orderSn)
	glog.Info("重新发送别的配色", "重新赋值了："+orderSn)
	//}

	c.orderDetail = new(models.OrderDetail)
	c.orderDetail = GetOrderDetailByOrderSn(c.orderMain.OrderSn)

	c.DeliverInfo = &deliverInfo
	glog.Info("超时未接单自动取消，重新发送别的配色", "订单号："+orderSn, c.orderMain)
	return c.PushMpOrder()
}

// 推送闪送订单
func (c *CommonService) OnlyPushShanSong(orderSn string) error {
	c.session = GetDBConn().NewSession()
	defer c.session.Close()

	if c.orderMain == nil {
		c.orderMain = new(models.OrderMain)
		c.orderMain = GetOrderMainByOrderSn(orderSn)
	}

	if c.orderDetail == nil {
		c.orderDetail = new(models.OrderDetail)
		c.orderDetail = GetOrderDetailByOrderSn(c.orderMain.OrderSn)
	}

	err := c.PushShanSong()
	if err == nil {
		return nil
	}

	glog.Info("推送闪送失败，添加异常配送单", "订单号："+orderSn)

	//记录配送异常
	oe := OrderExceptionService{}
	deliveryId := GetSn("delivery")[0]
	ExceptionRequest := oc.OrderExceptionRequest{
		DeliveryId:     deliveryId,
		OrderId:        c.orderMain.OrderSn,
		ExceptionDescr: err.Error(),
		ExceptionTime:  kit.GetTimeNow(),
		Source:         1,
		OrderStatus:    2,
	}
	res, err := oe.OrderExceptionAdd(nil, &ExceptionRequest)
	if err != nil {
		glog.Error("推送配送单失败，异常单保存失败！", c.orderMain.OldOrderSn, err.Error())
	}
	if res.Code != 200 {
		glog.Error("推送配送单失败，异常单保存失败！", c.orderMain.OldOrderSn, res.Message)
	}

	//通知数据中心
	go MessageCreate(&models.Message{
		OrderId:     c.orderMain.OrderSn,
		MessageType: 5,
		FinanceCode: c.orderMain.ShopId,
		Msg:         fmt.Sprintf("【配送异常】订单：%s，请重点关注！", c.orderMain.OrderSn),
	})

	glog.Info("推送配送单完成 ", c.orderMain.OldOrderSn)
	return nil
}

// 查询第三方平台的报价
func (c *CommonService) SearchDeliveryPrice() (priceMap map[int]dto.DeliverPriceRes, err error) {
	glog.Info(c.orderMain.OrderSn, "配送单查询价格")

	DeliverPriceResmap := map[int]dto.DeliverPriceRes{}
	var warehouseCode string
	if c.orderMain.ChannelId == ChannelMtId && c.orderMain.Source == 1 {
		var warehouseCategory int32
		if has, err := c.session.Table("dc_dispatch.warehouse").ID(c.orderMain.WarehouseId).
			Select("category").Get(&warehouseCategory); err != nil {
			glog.Error(c.orderMain.OrderSn, ", 查询仓库信息失败, ", err.Error(), kit.RunFuncName(2))
			return DeliverPriceResmap, errors.New("查询仓库信息失败")
		} else if !has {
			glog.Error(c.orderMain.OrderSn, ", 查询仓库信息失败, 仓库未找到", kit.RunFuncName(2))
			return DeliverPriceResmap, errors.New("仓库未找到")
		}

		if warehouseCategory == 5 {
			warehouseCode = "QZC" + c.orderMain.WarehouseCode
		} else {
			warehouseCode = c.orderMain.WarehouseCode
		}
	} else {
		warehouseCode = c.orderMain.WarehouseCode
	}
	deliveryServiceCode := int32(100029)
	deliveryConfig := new(models.DeliveryConfig)
	//是否第三方配送
	isThree := false
	// 判断 如果是成宠物saas
	if cast.ToString(c.orderMain.AppChannel) == config.GetString("eshop_store_app_channel") {

		_, err := c.session.Where("finance_code = ?", c.orderMain.ShopId).
			Where("channel_id = ?", c.orderMain.ChannelId).
			Where("org_id = ?", c.orderMain.OrgId).
			Get(deliveryConfig)

		if err != nil {
			glog.Error("查询配送配置出错", c.orderMain.OrderSn, err.Error())
			return DeliverPriceResmap, errors.New("查询配送配置出错")
		}

		if deliveryConfig.ID == 0 {
			return DeliverPriceResmap, errors.New("未查询到当前渠道的配送方式")
		}

		//如果是自选配送，直接报错，进入异常
		if deliveryConfig.DeliveryMethod == 4 {
			return DeliverPriceResmap, errors.New("配送方式为自行配送")
		}
		//如果是平台配送或者是第三方配送，我们不需要发配送直接返回成功
		if deliveryConfig.ThirdType == 1 && deliveryConfig.DeliveryMethod == 2 {
			if c.orderMain.ChannelId == 1 {
				isThree = true
			} else {
				return DeliverPriceResmap, nil
			}
		}
		deliveryServiceCode = int32(deliveryConfig.ServiceCode)
		//var shopNo string
		//if has, err := c.session.Table("dc_dispatch.warehouse_delivery_relation").Where("warehouse_id=?", c.orderMain.WarehouseId).
		//	Select("shop_no").Get(&shopNo); err != nil {
		//	glog.Error(c.orderMain.OrderSn, ", 查询仓库对应的配送门店编码信息失败, ", err.Error(), kit.RunFuncName(2), "数据：", kit.JsonEncode(c.orderMain))
		//	return errors.New("查询仓库对应的配送门店编码信息失败")
		//} else if !has {
		//	glog.Error(c.orderMain.OrderSn, ", 未找到仓库对应的配送门店编码信息失败", kit.RunFuncName(2), "数据：", kit.JsonEncode(c.orderMain))
		//	return errors.New("未找到仓库对应的配送门店编码信息失败")
		//}
		warehouseCode = deliveryConfig.StoreID
	}
	//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
	deliveryTypeList := make([]int, 0)
	//需要比价的和发送的配送
	//加入美配
	if !isThree {
		deliveryTypeList = append(deliveryTypeList, 0)
	} else {
		deliveryTypeList = append(deliveryTypeList, 5)
	}

	//不发其他配送
	////加入达达
	//deliveryTypeList = append(deliveryTypeList, 3)
	if cast.ToString(c.orderMain.AppChannel) != config.GetString("eshop_store_app_channel") {
		//加入蜂鸟
		deliveryTypeList = append(deliveryTypeList, 4)
	}

	//后续有别的配送继续往里面加
	if kit.IsDebug && cast.ToString(c.orderMain.AppChannel) != config.GetString("eshop_store_app_channel") {
		warehouseCode = "test_0001" //测试配送的门店信息

	}

	//第三方订单号
	var OuterOrderSourceNo string
	_, err = c.session.SQL(`SELECT old_order_sn FROM order_main WHERE order_sn=?`, c.orderMain.ParentOrderSn).Get(&OuterOrderSourceNo)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ",MpOrderCreate查询第三方订单号出错", err)
	}

	if c.orderDetail.Latitude == 0 || c.orderDetail.Longitude == 0 {
		var orderDetail models.OrderDetail
		_, err = c.session.SQL(`select latitude,longitude from  order_detail where order_sn=?`,
			c.orderMain.ParentOrderSn).Get(&orderDetail)
		if err != nil {
			glog.Error("推送美团配送单,经纬度获取失败", c.orderMain.OrderSn, err.Error())
			return DeliverPriceResmap, errors.New("推送美团配送出错,经纬度获取失败！")
		}
		c.orderDetail.Latitude = orderDetail.Latitude
		c.orderDetail.Longitude = orderDetail.Longitude
	}

	ReceiverLng := Wrap(c.orderDetail.Longitude, 6)
	ReceiverLat := Wrap(c.orderDetail.Latitude, 6)

	var coordinateType int32
	FnCoordinateType := int32(3)
	//用于别的需要用转行的坐标系，默认火星
	LogisticsLat := cast.ToFloat64(ReceiverLat) / 1000000
	LogisticsLng := cast.ToFloat64(ReceiverLng) / 1000000
	if c.orderMain.ChannelId == ChannelElmId {
		//坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
		coordinateType = 1
		FnCoordinateType = 2
		hxLng, hxLat := utils.BD09ToGCJ02(cast.ToFloat64(ReceiverLng), cast.ToFloat64(ReceiverLat))
		//百度坐标系转火星
		LogisticsLat = cast.ToFloat64(cast.ToInt64(hxLat)) / 1000000
		LogisticsLng = cast.ToFloat64(cast.ToInt64(hxLng)) / 1000000
	}

	// 美配收货地址不能包含“ + &”
	c.orderMain.ReceiverAddress = strings.Replace(c.orderMain.ReceiverAddress, "+", " ", -1)
	c.orderMain.ReceiverAddress = strings.Replace(c.orderMain.ReceiverAddress, "&", " ", -1)
	////先判断快速达配送能力
	//MpOrderCheckRequest := et.MpOrderCheckRequest{
	//	ShopId:              warehouseCode,
	//	DeliveryServiceCode: "100029",
	//	ReceiverAddress:     c.orderMain.ReceiverAddress,
	//	ReceiverLng:         ReceiverLng,
	//	ReceiverLat:         ReceiverLat,
	//	CoordinateType:      coordinateType,
	//	CheckType:           1,
	//	MockOrderTime:       time.Now().Unix(),
	//}
	//glog.Info("推送判断快速达配送能力请求参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(MpOrderCheckRequest))
	//CheckRes, err := etClient.MPServer.MpOrderCheck(etClient.Ctx, &MpOrderCheckRequest)
	//if err != nil {
	//	glog.Error("推送判断快速达配送能力接口错误！ ", c.orderMain.OldOrderSn, " err:", err.Error())
	//	return errors.New("推送判断快速达配送能力错误！")
	//}
	//glog.Info("推送判断快速达配送能力返回参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(CheckRes))
	//
	////如果无法配送快速达将调用自由达
	//if CheckRes.Code != 200 || CheckRes.ExternalCode == "11" {
	//	return errors.New(CheckRes.Message)
	//}

	//var resByte []byte
	etClient := et.GetExternalClient()

	//DeliverPriceList := make([]dto.DeliverPrice, 0)
	deliveryId := cast.ToInt64(GetSn("delivery")[0])

	receiverPhone := c.orderMain.ReceiverPhone
	if len(c.orderMain.EnReceiverPhone) > 0 {
		receiverPhone = utils.MobileDecrypt(c.orderMain.EnReceiverPhone)
	}
	//开始通过各自的第三方查询价格
	for _, k := range deliveryTypeList {

		DeliverPriceResItem := dto.DeliverPriceRes{}
		DeliverPriceResItem.DeliveryType = k
		DeliverPriceResItem.IsOk = false
		DeliverPriceResItem.OrderId = c.orderMain.OrderSn
		DeliverPriceResItem.ParentOrderSn = c.orderMain.ParentOrderSn

		switch k {
		//美配
		case 0:

			MpOrder := et.MpOrderCreateRequest{
				DeliveryId:          deliveryId,
				OrderId:             c.orderMain.OrderSn,
				ShopId:              warehouseCode,       // 测试环境：test_0001，正式环境使用财务编码   先定死黄兴店财务编码
				DeliveryServiceCode: deliveryServiceCode, //合同上签的是快速达4011
				OuterOrderSourceNo:  OuterOrderSourceNo,
				ReceiverName:        c.orderMain.ReceiverName,
				ReceiverAddress:     c.orderMain.ReceiverAddress,
				ReceiverPhone:       receiverPhone,
				ReceiverLng:         ReceiverLng,
				ReceiverLat:         ReceiverLat,
				CoordinateType:      coordinateType,
				GoodsValue:          float32(kit.FenToYuan(c.orderMain.Total)),
				GoodsWeight:         float32(c.orderMain.TotalWeight) / 1000,
				PoiSeq:              c.orderDetail.PickupCode,
				AppChannel:          cast.ToString(c.orderMain.AppChannel),
			}

			glog.Info("美配查询配送费 ", c.orderMain.OldOrderSn, kit.JsonEncode(MpOrder))
			res, err := etClient.MPServer.MpOrderPreCreate(etClient.Ctx, &MpOrder)
			glog.Error("美配查询配送费返回", c.orderMain.OldOrderSn, kit.JsonEncode(res))
			if err != nil {
				glog.Error("美配查询配送费接口错误！ ", c.orderMain.OldOrderSn, err.Error())
				DeliverPriceResItem.Err = "美团：" + err.Error()
				DeliverPriceResmap[k] = DeliverPriceResItem
				//return DeliverPriceResmap, err
				break
			}
			if res.Code != 200 {
				glog.Error("美配查询配送费接口错误！ ", c.orderMain.OldOrderSn, kit.JsonEncode(res))
				DeliverPriceResItem.Err = "美团：" + res.Message
				DeliverPriceResmap[k] = DeliverPriceResItem

				//return DeliverPriceResmap, errors.New(res.Message)
				break
			}
			DeliverPriceResItem1 := dto.DeliverPriceRes{}
			err = json.Unmarshal([]byte(res.Data), &DeliverPriceResItem1)
			if err != nil {
				glog.Error("美配查询配送费解析返回失败", c.orderMain.OldOrderSn, kit.JsonEncode(res), err.Error())
				DeliverPriceResmap[k] = DeliverPriceResItem
				break
			}
			DeliverPriceResItem.IsOk = true
			DeliverPriceResItem.MtPeisongId = DeliverPriceResItem1.MtPeisongId
			DeliverPriceResItem.DeliveryId = DeliverPriceResItem1.DeliveryId
			DeliverPriceResItem.DeliveryFee = DeliverPriceResItem1.DeliveryFee
			DeliverPriceResItem.DeliveryDistance = DeliverPriceResItem1.DeliveryDistance

			DeliverPriceResmap[k] = DeliverPriceResItem
			glog.Info("美配查询配送费返回参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(res))
			break
		//达达
		case 3:
			DaDaOrder := et.DaDaDeliverFeeRequst{}
			DaDaOrder.CargoPrice = kit.FenToYuan(c.orderMain.Total)
			DaDaOrder.CargoWeight = float64(c.orderMain.TotalWeight) / 1000
			DaDaOrder.IsPrepay = 0
			DaDaOrder.ReceiverName = c.orderMain.ReceiverName
			DaDaOrder.ReceiverAddress = c.orderMain.ReceiverAddress
			DaDaOrder.ReceiverLat = LogisticsLat
			DaDaOrder.ReceiverLng = LogisticsLng
			DaDaOrder.ReceiverPhone = receiverPhone
			DaDaOrder.OriginId = c.orderMain.OrderSn
			DaDaOrder.FetchCode = c.orderDetail.PickupCode
			glog.Info("达达查询配送费 ", c.orderMain.OldOrderSn, kit.JsonEncode(DaDaOrder))
			res, err := etClient.DaDa.QueryDeliverFee(etClient.Ctx, &DaDaOrder)
			glog.Info("达达查询配送费返回", c.orderMain.OldOrderSn, kit.JsonEncode(res))
			if err != nil {
				glog.Error("达达查询配送费接口错误！ ", c.orderMain.OldOrderSn, err.Error())
				DeliverPriceResmap[k] = DeliverPriceResItem
				//DeliverPriceList = append(DeliverPriceList, itemPrice)
				break
			}
			if res.Code != 0 {
				glog.Error("达达查询配送费接口错误！ ", c.orderMain.OldOrderSn, res.Error)
				DeliverPriceResmap[k] = DeliverPriceResItem
				//DeliverPriceList = append(DeliverPriceList, itemPrice)
				break
			}
			DeliverPriceResItem.IsOk = true
			value, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", res.Data.Fee), 64)
			DeliverPriceResItem.DeliveryFee = value
			DeliverPriceResItem.MtPeisongId = res.Data.DeliveryNo
			DeliverPriceResItem.DeliveryDistance = cast.ToInt64(res.Data.Distance)
			DeliverPriceResItem.DeliveryId = deliveryId

			DeliverPriceResmap[k] = DeliverPriceResItem
			break
		case 4: //蜂鸟

			params := et.FnDeliverFeeRequst{}
			params.PartnerOrderCode = c.orderMain.OrderSn
			params.ReceiverLatitude = cast.ToFloat64(ReceiverLat) / 1000000
			params.ReceiverLongitude = cast.ToFloat64(ReceiverLng) / 1000000
			params.GoodsTotalAmountCent = cast.ToInt64(c.orderMain.PayTotal)
			params.OrderType = 1
			params.GoodsWeight = float32(c.orderMain.TotalWeight) / 1000
			params.GoodsActualAmountCent = cast.ToInt64(c.orderMain.GoodsPayTotal)
			params.ReceiverAddress = c.orderMain.ReceiverAddress
			params.PositionSource = FnCoordinateType

			for _, productItem := range c.orderProducts {
				if productItem.ProductType == 1 {

					paritem := et.OrderItemOpenapiDto{
						ItemActualAmountCent: cast.ToInt64(productItem.SkuPayTotal),
						ItemAmountCent:       cast.ToInt64(productItem.MarkingPrice * productItem.Number),
					}
					params.GoodsItemList = append(params.GoodsItemList, &paritem)
				}
			}
			params.GoodsCount = cast.ToInt32(len(params.GoodsItemList))

			glog.Info("蜂鸟查询配送费 ", c.orderMain.OldOrderSn, kit.JsonEncode(params))
			res, err := etClient.Fn.QueryDeliverFee(etClient.Ctx, &params)
			glog.Info("蜂鸟查询配送费返回", c.orderMain.OldOrderSn, kit.JsonEncode(res))
			if err != nil {
				glog.Error("蜂鸟查询配送费接口错误！ ", c.orderMain.OldOrderSn, err.Error())
				DeliverPriceResItem.Err = "蜂鸟：" + err.Error()
				DeliverPriceResmap[k] = DeliverPriceResItem
				//DeliverPriceList = append(DeliverPriceList, itemPrice)
				break
			}
			if res.Code != "200" {
				glog.Error("蜂鸟查询配送费接口错误！ ", c.orderMain.OldOrderSn, res.Msg)
				DeliverPriceResItem.Err = "蜂鸟：" + res.Msg
				DeliverPriceResmap[k] = DeliverPriceResItem
				//DeliverPriceList = append(DeliverPriceList, itemPrice)
				break
			}

			QueryFee := dto.FnQueryFee{}
			json.Unmarshal([]byte(res.BusinessData), &QueryFee)

			//是否获取到价格
			isGet := false
			for _, x := range QueryFee.GoodsInfos {
				if x.IsValid == 1 {
					isGet = true
					//params.ActualDeliveryAmountCent = x.ActualDeliveryAmountCent
					//value, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", x.ActualDeliveryAmountCent), 64)
					DeliverPriceResItem.DeliveryFee = kit.FenToYuan(x.ActualDeliveryAmountCent)
					DeliverPriceResItem.MtPeisongId = x.TIndexID
					//正式的只有一个服务包，取到有可用价格就返回
					break
				}
			}
			//如果没有获取到任何可以用价格，就返回
			if !isGet {
				DeliverPriceResmap[k] = DeliverPriceResItem
				break
			}

			DeliverPriceResItem.IsOk = true
			DeliverPriceResItem.DeliveryDistance = int64(QueryFee.Distance)
			DeliverPriceResItem.DeliveryId = deliveryId
			DeliverPriceResmap[k] = DeliverPriceResItem

			break
		case 5: //麦芽田配送
			DeliverPriceResItem.IsOk = true
			DeliverPriceResItem.MtPeisongId = c.orderMain.OrderSn
			DeliverPriceResItem.DeliveryId = cast.ToInt64(c.orderMain.OrderSn)
			DeliverPriceResItem.DeliveryFee = kit.FenToYuan(c.orderMain.Freight)
			DeliverPriceResItem.DeliveryDistance = 1000
			DeliverPriceResmap[k] = DeliverPriceResItem
		}

	}
	return DeliverPriceResmap, nil

}

// 处理配送报表
func (c *CommonService) InsertDeliveryReport(priceMap map[int]dto.DeliverPriceRes, minDeliverType int) (err error) {

	//处理发配送的报表数据
	//发配送查询的价格记录，用于做发配送报表
	OrderDeliveryReportPriceList := make([]models.OrderDeliveryReportPrice, 0)
	//虚幻记录价格
	for _, k := range priceMap {

		OrderDeliveryReportPrice := models.OrderDeliveryReportPrice{}
		OrderDeliveryReportPrice.OrderSn = c.orderMain.OrderSn
		OrderDeliveryReportPrice.DeliveryType = k.DeliveryType

		//查询成功的配送渠道
		if k.IsOk {
			OrderDeliveryReportPrice.DeliveryPrice = cast.ToString(k.DeliveryFee)
		} else {
			OrderDeliveryReportPrice.DeliveryPrice = "获取失败"
		}
		OrderDeliveryReportPriceList = append(OrderDeliveryReportPriceList, OrderDeliveryReportPrice)
	}
	//先根据子单号查询是否存在
	id := 0
	//
	db := GetDBConn()
	_, err = db.SQL("select id from dc_order.order_delivery_report where order_sn=?", c.orderMain.OrderSn).Get(&id)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, "，查询是否存在配送报表数据出错，", err)
	} else {
		dbSession := db.NewSession()

		OrderDeliveryReport := models.OrderDeliveryReport{}
		OrderDeliveryReport.OrderSn = c.orderMain.OrderSn
		OrderDeliveryReport.Distance = cast.ToInt(priceMap[minDeliverType].DeliveryDistance)
		OrderDeliveryReport.ParentOrderSn = c.orderMain.ParentOrderSn
		OrderDeliveryReport.LastDeliveryType = minDeliverType
		OrderDeliveryReport.LastDelivery = DeliveryTypeKeyMap[minDeliverType]

		//说明没有报表数据，需要插入
		if id == 0 {
			//查询父单号中的第三方订单号
			old_order_sn := ""
			_, err = c.session.SQL("select old_order_sn from dc_order.order_delivery_report where order_sn=?", c.orderMain.ParentOrderSn).Get(&old_order_sn)
			if err != nil {
				glog.Error(c.orderMain.OrderSn, "，配送报表查询父单号报错，", err)
			}
			OrderDeliveryReport.OldOrderSn = old_order_sn
			dbSession.Begin()
			_, err = dbSession.Insert(OrderDeliveryReport)
			if err == nil {
				_, err = dbSession.Insert(OrderDeliveryReportPriceList)
				if err == nil {
					dbSession.Commit()
				}
			}
			if err != nil {
				dbSession.Rollback()
				glog.Error(c.orderMain.OrderSn, "，插入配送报表出错，", err)
			}
		} else { //执行修改
			_, err = dbSession.Exec("delete from order_delivery_report_price where order_sn=?", c.orderMain.OrderSn)
			if err != nil {
				dbSession.Rollback()
				glog.Error(c.orderMain.OrderSn, "，删除历史配送数据出错，", err)
			} else {
				_, err = dbSession.Insert(OrderDeliveryReportPriceList)
				if err != nil {
					dbSession.Rollback()
					glog.Error(c.orderMain.OrderSn, "，插入配送价格出错，", err)
				} else {
					_, err = dbSession.ID(id).Cols("last_delivery", "last_delivery_type").Update(&OrderDeliveryReport)
					if err != nil {
						dbSession.Rollback()
						glog.Error(c.orderMain.OrderSn, "，插入配送报表出错，", err)
					} else {
						dbSession.Commit()
					}

				}
			}

		}
	}
	return nil
}

// 新增配送单(自配送)
// oldOrderSn 第三方渠道订单号
func (c *CommonService) MpOrderCreate() (err error) {
	glog.Info(c.orderMain.OrderSn, "创建配送单 ： 入参：", kit.JsonEncode(c.orderMain))
	var warehouseCode string
	if c.session == nil {
		db := GetDBConn()
		c.session = db.NewSession()
	}
	if c.orderMain.ChannelId == ChannelMtId && c.orderMain.Source == 1 {
		var warehouseCategory int32
		if has, err := c.session.Table("dc_dispatch.warehouse").ID(c.orderMain.WarehouseId).
			Select("category").Get(&warehouseCategory); err != nil {
			glog.Error(c.orderMain.OrderSn, ", 查询仓库信息失败, ", err.Error(), kit.RunFuncName(2))
			return errors.New("查询仓库信息失败")
		} else if !has {
			glog.Error(c.orderMain.OrderSn, ", 查询仓库信息失败, 仓库未找到", kit.RunFuncName(2))
			return errors.New("仓库未找到")
		}

		if warehouseCategory == 5 {
			warehouseCode = "QZC" + c.orderMain.WarehouseCode
		} else {
			warehouseCode = c.orderMain.WarehouseCode
		}
	} else {
		warehouseCode = c.orderMain.WarehouseCode
	}
	deliveryServiceCode := int32(100029)
	deliveryConfig := new(models.DeliveryConfig)
	// 如果是成宠物saas, 则取warehouse_delivery_relation.shop_no是美配的第三方门店
	if cast.ToString(c.orderMain.AppChannel) == config.GetString("eshop_store_app_channel") {

		_, err := c.session.Where("finance_code = ?", c.orderMain.ShopId).
			Where("channel_id = ?", c.orderMain.ChannelId).
			Where("org_id = ?", c.orderMain.OrgId).
			Get(deliveryConfig)

		if err != nil {
			return errors.New("查询配送配置出错")
		}

		if deliveryConfig.ID == 0 {
			return errors.New("未查询到当前渠道的配送方式")
		}

		//如果是自选配送，直接报错，进入异常
		if deliveryConfig.DeliveryMethod == 4 {

			DeliveryRecord := models.OrderDeliveryRecord{}
			IsHave, err := c.session.Where("order_sn=?", c.orderMain.OrderSn).Get(&DeliveryRecord)
			if err != nil {
				return errors.New("查询配送OrderDeliveryRecord 出错" + err.Error())
			}
			deliveryId := GetSn("delivery")[0]
			//不存在配送记录插入一条
			if !IsHave {
				DeliveryRecord = models.OrderDeliveryRecord{
					DeliveryId:          cast.ToInt64(deliveryId),
					MtPeisongId:         c.orderMain.OrderSn,
					OrderSn:             c.orderMain.OrderSn,
					DeliveryServiceCode: deliveryServiceCode,
					DeliveryType:        2,
				}
				_, err = c.session.Insert(&DeliveryRecord)
				if err != nil {
					glog.Error("推送配送单,添加返回单号失败 ", c.orderMain.OldOrderSn, err.Error())
					return err
				}
			}

			return errors.New("配送方式为自行配送|" + deliveryId)
		}
		//如果是平台配送或者是第三方配送，我们不需要发配送直接返回成功
		//if deliveryConfig.DeliveryMethod == 1 || deliveryConfig.DeliveryMethod == 2 {
		//	return nil
		//}
		deliveryServiceCode = int32(deliveryConfig.ServiceCode)
		//var shopNo string
		//if has, err := c.session.Table("dc_dispatch.warehouse_delivery_relation").Where("warehouse_id=?", c.orderMain.WarehouseId).
		//	Select("shop_no").Get(&shopNo); err != nil {
		//	glog.Error(c.orderMain.OrderSn, ", 查询仓库对应的配送门店编码信息失败, ", err.Error(), kit.RunFuncName(2), "数据：", kit.JsonEncode(c.orderMain))
		//	return errors.New("查询仓库对应的配送门店编码信息失败")
		//} else if !has {
		//	glog.Error(c.orderMain.OrderSn, ", 未找到仓库对应的配送门店编码信息失败", kit.RunFuncName(2), "数据：", kit.JsonEncode(c.orderMain))
		//	return errors.New("未找到仓库对应的配送门店编码信息失败")
		//}
		warehouseCode = deliveryConfig.StoreID
	}

	if kit.IsDebug && cast.ToString(c.orderMain.AppChannel) != config.GetString("eshop_store_app_channel") {
		warehouseCode = "test_0001" //测试配送的门店信息
	}

	if len(c.orderDetail.OrderSn) == 0 {
		glog.Error(c.orderMain.OrderSn, ", 查询订单配送信息失败, ", kit.RunFuncName(2))
		return errors.New("查询订单配送信息失败")
	}

	if c.orderDetail.Latitude == 0 || c.orderDetail.Longitude == 0 {
		var orderDetail models.OrderDetail
		_, err = c.session.SQL(`select latitude,longitude from  order_detail where order_sn=?`,
			c.orderMain.ParentOrderSn).Get(&orderDetail)
		if err != nil {
			glog.Error("推送美团配送单,经纬度获取失败", c.orderMain.OrderSn, err.Error())
			return errors.New("推送美团配送出错,经纬度获取失败！")
		}
		c.orderDetail.Latitude = orderDetail.Latitude
		c.orderDetail.Longitude = orderDetail.Longitude
	}

	ReceiverLng := Wrap(c.orderDetail.Longitude, 6)
	ReceiverLat := Wrap(c.orderDetail.Latitude, 6)

	etClient := et.GetExternalClient()
	//zxadd 判断如果是美团配送的，直接重新发起美团配送
	//if c.orderMain.ChannelId == ChannelMtId {
	//	//如果在这些里面，说明他属于美团配送，否则就还是走原来的自配
	//	if strings.Contains("2002,1001,1004,2010,3001,1007", c.orderMain.LogisticsCode) {
	//		mtOrderReques := et.MtOrderStatusRequest{
	//			OrderId:       c.orderMain.OldOrderSn,
	//			StoreMasterId: c.orderMain.AppChannel,
	//		}
	//
	//		res, err := etClient.MtOrder.MtLogisticsPush(etClient.Ctx, &mtOrderReques)
	//		if err != nil {
	//			glog.Error("推送美团配送出错 ", c.orderMain.OldOrderSn, " err:", err.Error())
	//			return errors.New("推送美团配送出错！")
	//		}
	//		if res.Code != 200 {
	//			glog.Error("推送美团配送出错 ", c.orderMain.OldOrderSn, " err:", res.Message)
	//			return errors.New("推送美团配送出错！")
	//		}
	//		return nil
	//	}
	//}
	////配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
	//deliveryTypeList := make([]int, 0)
	////需要比价的和发送的配送
	////加入美配
	//deliveryTypeList = append(deliveryTypeList, 0)
	////加入达达
	//deliveryTypeList = append(deliveryTypeList, 0)
	////后续有别的配送继续往里面加

	//第三方订单号
	var OuterOrderSourceNo string
	_, err = c.session.SQL(`SELECT old_order_sn FROM order_main WHERE order_sn=?`, c.orderMain.ParentOrderSn).Get(&OuterOrderSourceNo)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ",MpOrderCreate查询第三方订单号出错", err)
	}

	var coordinateType int32
	FnCoordinateType := int32(3)
	if c.orderMain.ChannelId == ChannelElmId {
		//坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
		coordinateType = 1
		FnCoordinateType = 2
	}

	// 美配收货地址不能包含“ + &”
	c.orderMain.ReceiverAddress = strings.Replace(c.orderMain.ReceiverAddress, "+", " ", -1)
	c.orderMain.ReceiverAddress = strings.Replace(c.orderMain.ReceiverAddress, "&", " ", -1)
	////先判断快速达配送能力
	//MpOrderCheckRequest := et.MpOrderCheckRequest{
	//	ShopId:              warehouseCode,
	//	DeliveryServiceCode: "100029",
	//	ReceiverAddress:     c.orderMain.ReceiverAddress,
	//	ReceiverLng:         ReceiverLng,
	//	ReceiverLat:         ReceiverLat,
	//	CoordinateType:      coordinateType,
	//	CheckType:           1,
	//	MockOrderTime:       time.Now().Unix(),
	//}
	//glog.Info("推送判断快速达配送能力请求参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(MpOrderCheckRequest))
	//CheckRes, err := etClient.MPServer.MpOrderCheck(etClient.Ctx, &MpOrderCheckRequest)
	//if err != nil {
	//	glog.Error("推送判断快速达配送能力接口错误！ ", c.orderMain.OldOrderSn, " err:", err.Error())
	//	return errors.New("推送判断快速达配送能力错误！")
	//}
	//glog.Info("推送判断快速达配送能力返回参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(CheckRes))
	//
	////如果无法配送快速达将调用自由达
	//if CheckRes.Code != 200 || CheckRes.ExternalCode == "11" {
	//	return errors.New(CheckRes.Message)
	//}

	deliveryId := cast.ToInt64(GetSn("delivery")[0])

	receiverPhone := c.orderMain.ReceiverPhone
	if len(c.orderMain.EnReceiverPhone) > 0 {
		receiverPhone = utils.MobileDecrypt(c.orderMain.EnReceiverPhone)
	}
	DeliverPriceResmap, err := c.SearchDeliveryPrice()
	glog.Info("查询所有配送返回价格：", kit.JsonEncode(DeliverPriceResmap))
	if err != nil {
		return err
	}
	minPrice := float64(999999)
	minDeliverType := 999999

	//指定的要发配送的渠道
	SelDeliverType := -1
	if c.DeliverInfo != nil && c.DeliverInfo.SelDeliverType != "" {
		SelDeliverType = cast.ToInt(c.DeliverInfo.SelDeliverType)
	}
	//指定的要发配送的渠道
	PcDeliverType := -1
	if c.DeliverInfo != nil && c.DeliverInfo.PcDeliverType != "" {
		PcDeliverType = cast.ToInt(c.DeliverInfo.PcDeliverType)
	}
	//发配送查询的价格记录，用于做发配送报表
	OrderDeliveryReportPriceList := make([]models.OrderDeliveryReportPrice, 0)

	errMes := ""
	//有成功的，找出价格最低的
	for _, k := range DeliverPriceResmap {

		OrderDeliveryReportPrice := models.OrderDeliveryReportPrice{}
		OrderDeliveryReportPrice.OrderSn = c.orderMain.OrderSn
		OrderDeliveryReportPrice.DeliveryType = k.DeliveryType
		OrderDeliveryReportPrice.DeliveryPrice = "获取失败"
		//查询成功的配送渠道
		if k.IsOk {
			OrderDeliveryReportPrice.DeliveryPrice = cast.ToString(k.DeliveryFee)
			OrderDeliveryReportPriceList = append(OrderDeliveryReportPriceList, OrderDeliveryReportPrice)
			if k.DeliveryFee < minPrice {
				//选择了渠道只查询当前渠道的
				if SelDeliverType != -1 && SelDeliverType != k.DeliveryType {
					continue
				}
				//如果有要排除的发配送
				if PcDeliverType != -1 && PcDeliverType == k.DeliveryType {
					continue
				}
				minPrice = k.DeliveryFee
				minDeliverType = k.DeliveryType
			}
		} else {
			errMes += k.Err
			OrderDeliveryReportPriceList = append(OrderDeliveryReportPriceList, OrderDeliveryReportPrice)
		}

	}
	if minDeliverType == 999999 {
		return errors.New(errMes)
	}
	//根据不同的渠道发配送
	switch minDeliverType {
	case 0: //美配
		MpOrder := et.MpOrderCreateRequest{
			DeliveryId:          deliveryId,
			OrderId:             c.orderMain.OrderSn,
			ShopId:              warehouseCode,       // 测试环境：test_0001，正式环境使用财务编码   先定死黄兴店财务编码
			DeliveryServiceCode: deliveryServiceCode, //合同上签的是快速达4011
			OuterOrderSourceNo:  OuterOrderSourceNo,
			ReceiverName:        c.orderMain.ReceiverName,
			ReceiverAddress:     c.orderMain.ReceiverAddress,
			ReceiverPhone:       receiverPhone,
			ReceiverLng:         ReceiverLng,
			ReceiverLat:         ReceiverLat,
			CoordinateType:      coordinateType,
			GoodsValue:          float32(kit.FenToYuan(c.orderMain.Total)),
			GoodsWeight:         float32(c.orderMain.TotalWeight) / 1000,
			PoiSeq:              c.orderDetail.PickupCode,
			AppChannel:          cast.ToString(c.orderMain.AppChannel),
		}

		glog.Info("美配发起配送 ", c.orderMain.OldOrderSn, kit.JsonEncode(MpOrder))
		res, err := etClient.MPServer.MpOrderCreate(etClient.Ctx, &MpOrder)
		glog.Info("美配发起配送返回参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(res))
		if err != nil {
			glog.Error("美配发起配送接口错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("美配发起配送接口错误")
		}
		if res.Code != 200 {
			glog.Error("美配发起配送接口错误！ ", c.orderMain.OldOrderSn, res.Error)
			return errors.New("美配发起配送接口错误" + res.Error)
		}
		DeliverPriceResItem := dto.DeliverPriceRes{}
		err = json.Unmarshal([]byte(res.Data), &DeliverPriceResItem)
		if err != nil {
			glog.Error("美配发配送费解析返回失败", c.orderMain.OldOrderSn, kit.JsonEncode(res), err.Error())
			return errors.New("美配发配送费解析错误")
		}
		DeliverPriceResItem.DeliveryType = minDeliverType
		DeliverPriceResItem.IsOk = true
		DeliverPriceResmap[minDeliverType] = DeliverPriceResItem
		glog.Info("美配发起配送检查错误 ", kit.JsonEncode(res), DeliverPriceResmap[minDeliverType])
		break

	case 3: //达达
		par1 := et.DaDaAddAfterQueryRequst{}
		par1.DeliveryNo = DeliverPriceResmap[minDeliverType].MtPeisongId
		par1.OriginId = DeliverPriceResmap[minDeliverType].OrderId
		glog.Info("达达发配送请求参数：", par1)
		res, err := etClient.DaDa.AddAfterQuery(etClient.Ctx, &par1)

		if err != nil {
			glog.Error("达达发配送接口错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("达达发配送接口错误")

		}
		if res.Code != 0 {
			glog.Error("达达发配送接口错误！ ", c.orderMain.OldOrderSn, res.Error)
			return errors.New("达达发配送接口错误")
		}
		break
	case 4: //蜂鸟
		params := et.FnDeliverFeeRequst{}
		params.PartnerOrderCode = c.orderMain.OrderSn
		params.ReceiverLatitude = cast.ToFloat64(ReceiverLat) / 1000000
		params.ReceiverLongitude = cast.ToFloat64(ReceiverLng) / 1000000
		params.GoodsTotalAmountCent = cast.ToInt64(c.orderMain.PayTotal)
		params.OrderType = 1
		params.GoodsWeight = float32(c.orderMain.TotalWeight) / 1000
		params.GoodsActualAmountCent = cast.ToInt64(c.orderMain.GoodsPayTotal)
		params.ReceiverAddress = c.orderMain.ReceiverAddress
		params.PositionSource = FnCoordinateType
		params.ReceiverName = c.orderMain.ReceiverName
		params.ReceiverPrimaryPhone = strings.Replace(receiverPhone, "_", ",", -1)

		for _, productItem := range c.orderProducts {
			if productItem.ProductType == 1 {

				paritem := et.OrderItemOpenapiDto{
					ItemActualAmountCent: cast.ToInt64(productItem.SkuPayTotal),
					ItemAmountCent:       cast.ToInt64(productItem.MarkingPrice * productItem.Number),
				}
				params.GoodsItemList = append(params.GoodsItemList, &paritem)
			}
		}
		params.GoodsCount = cast.ToInt32(len(params.GoodsItemList))
		params.ActualDeliveryAmountCent = int64(kit.YuanToFen(DeliverPriceResmap[minDeliverType].DeliveryFee))
		params.PreCreateOrderTIndexId = DeliverPriceResmap[minDeliverType].MtPeisongId
		glog.Info("蜂鸟发配送请求参数：", params)
		res, err := etClient.Fn.AddAfterQuery(etClient.Ctx, &params)

		if err != nil {
			glog.Error("蜂鸟发配送接口错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("蜂鸟发配送接口错误")

		}
		if res.Code != "200" {
			glog.Error("蜂鸟发配送接口错误！ ", c.orderMain.OldOrderSn, res.Msg)
			return errors.New("蜂鸟发配送接口错误")
		}
		//物流单号匹配
		AddDelivery := dto.AddDeliveryResponse{}
		err = json.Unmarshal([]byte(res.BusinessData), &AddDelivery)
		if err != nil {
			glog.Error("解析蜂鸟配送返回出错！ ", c.orderMain.OldOrderSn, err.Error())
		}
		nowmap := DeliverPriceResmap[minDeliverType]
		nowmap.MtPeisongId = cast.ToString(AddDelivery.OrderId)
		DeliverPriceResmap[minDeliverType] = nowmap
		break
	case 5: //麦芽田发配送
		params, err := c.GetMytOrderInfo(deliveryConfig.StoreID, c.orderMain.OrderSn)
		if err != nil {
			glog.Error("查询麦芽田配送数据失败！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("查询麦芽田配送数据失败")
		}
		res, err := etClient.Myt.PushNewOrder(etClient.Ctx, params)
		if err != nil {
			glog.Error("麦芽田发配送接口错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("麦芽田发配送接口错误")

		}
		if res.Code != 200 {
			glog.Error("麦芽田发配送接口错误！ ", c.orderMain.OldOrderSn, res.Message)
			return errors.New("麦芽田发配送接口错误")
		}
		par := et.MytOrderConfirmRequest{}
		par.OrderId = params.Order.OrderId
		par.ShopId = params.Order.ShopId
		par.UpdateTime = time.Now().Unix()
		//下单成功后调用确认订单
		res, err = etClient.Myt.ConfirmOrder(etClient.Ctx, &par)
		if err != nil {
			glog.Error("麦芽田确认订单错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("麦芽田确认订单接口错误")

		}
		if res.Code != 200 {
			glog.Error("麦芽田确认订单接口错误！ ", c.orderMain.OldOrderSn, res.Message)
			return errors.New("麦芽田确认订单接口错误")
		}

	default:
		return errors.New("没有可用的配送")
	}

	//DeliverPriceResmap[minDeliverType].DeliveryCount = 1
	//
	////加入重试的次数
	//if c.DeliverInfo != nil {
	//	DeliverPriceResmap[minDeliverType].DeliveryCount = c.DeliverInfo.DeliveryCount + 1
	//}

	if cast.ToString(c.orderMain.AppChannel) != config.GetString("eshop_store_app_channel") {
		c.InsertDeliveryReport(DeliverPriceResmap, minDeliverType)
	}

	//一直只有一个配送 ，暂时不加入自动取消了
	//glog.Info(c.orderMain.OrderSn, "，配送订单加入5分钟没接单取消切换队列")
	//DeliveryTime, _ := config.Get("orderCenter.DeliveryTime")
	//if DeliveryTime == "" {
	//	DeliveryTime = "15"
	//}
	//if err := utils.PublishRabbitMQV2(DeliveryOrderNoPickRoute, MQExchange, kit.JsonEncode(DeliverPriceResmap[minDeliverType]), cast.ToInt64(DeliveryTime)*int64(time.Minute/time.Millisecond)); err != nil {
	//	glog.Error(c.orderMain.OrderSn, "，配送订单加入5分钟没接单取消切换队列失败，", err)
	//}

	//if CheckRes.Code == 200 {
	//
	//	receiverPhone := c.orderMain.ReceiverPhone
	//	if len(c.orderMain.EnReceiverPhone) > 0 {
	//		receiverPhone = utils.MobileDecrypt(c.orderMain.EnReceiverPhone)
	//	}
	//	MpOrder := et.MpOrderCreateRequest{
	//		DeliveryId:          deliveryId,
	//		OrderId:             c.orderMain.OrderSn,
	//		ShopId:              warehouseCode,       // 测试环境：test_0001，正式环境使用财务编码   先定死黄兴店财务编码
	//		DeliveryServiceCode: deliveryServiceCode, //合同上签的是快速达4011
	//		OuterOrderSourceNo:  OuterOrderSourceNo,
	//		ReceiverName:        c.orderMain.ReceiverName,
	//		ReceiverAddress:     c.orderMain.ReceiverAddress,
	//		ReceiverPhone:       receiverPhone,
	//		ReceiverLng:         ReceiverLng,
	//		ReceiverLat:         ReceiverLat,
	//		CoordinateType:      coordinateType,
	//		GoodsValue:          float32(kit.FenToYuan(c.orderMain.Total)),
	//		GoodsWeight:         float32(c.orderMain.TotalWeight) / 1000,
	//		PoiSeq:              c.orderDetail.PickupCode,
	//	}
	//
	//	glog.Info("推送美团快速达配送单请求参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(MpOrder))
	//	res, err := etClient.MPServer.MpOrderCreate(etClient.Ctx, &MpOrder)
	//	if err != nil {
	//		glog.Error("推送美团快速达配送单接口错误！ ", c.orderMain.OldOrderSn, err.Error())
	//
	//		isOk := c.CheckShanSong()
	//		if isOk {
	//			err := c.PushShanSong()
	//			if err != nil {
	//				glog.Error("发起闪送失败MpOrderCreate：", err.Error())
	//			}
	//			return err
	//		}
	//
	//		return errors.New("推送美团快速达配送单接口错误！")
	//	}
	//	glog.Info("推送美团快速达配送单返回参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(res))
	//
	//	if res.Code != 200 {
	//		isOk := c.CheckShanSong()
	//		if isOk {
	//			err := c.PushShanSong()
	//			if err != nil {
	//				glog.Error("发起闪送失败MpOrderCreate：", err.Error())
	//			}
	//			return err
	//		}
	//		return errors.New("快速达:" + res.Message)
	//	}
	//
	//	resByte = []byte(res.Data)
	//} else {
	//
	//	glog.Info("没有配送能力的订单：", c.orderMain.OldOrderSn)
	//	// 没有配送能力的发起闪送
	//	isOk := c.CheckShanSong()
	//	if isOk {
	//		err := c.PushShanSong()
	//		if err != nil {
	//			glog.Error("发起闪送失败：", err.Error())
	//		}
	//		return err
	//	}
	//
	//}

	_, err = c.session.In("order_sn", []string{c.orderMain.ParentOrderSn, c.orderMain.OrderSn}).Update(&models.OrderDetail{
		PushDelivery: 1,
	})
	if err != nil {
		glog.Error("推送配送单,更新order状态失败 ", c.orderMain.OldOrderSn, err.Error())
	}

	//data, err := gjson.DecodeToJson(resByte)
	//if err != nil {
	//	return err
	//}

	//DeliveryIdInt, err := strconv.ParseInt(data.GetString("delivery_id"), 10, 64)
	//if err != nil {
	//	glog.Error("推送配送单,DeliveryId转int失败 ", c.orderMain.OldOrderSn, err.Error())
	//	return err
	//}

	DeliveryRecord := models.OrderDeliveryRecord{
		DeliveryId:          DeliverPriceResmap[minDeliverType].DeliveryId,
		MtPeisongId:         DeliverPriceResmap[minDeliverType].MtPeisongId,
		OrderSn:             DeliverPriceResmap[minDeliverType].OrderId,
		DeliveryServiceCode: deliveryServiceCode,
		DeliveryType:        minDeliverType,
	}
	glog.Info("推送配送单,添加返回单号参数 ", c.orderMain.OldOrderSn, kit.JsonEncode(DeliveryRecord))
	_, err = c.session.Insert(&DeliveryRecord)
	if err != nil {
		glog.Error("推送配送单,添加返回单号失败 ", c.orderMain.OldOrderSn, err.Error())
		return err
	}
	glog.Info("推送配送单成功 ", c.orderMain.OldOrderSn)
	return nil
}

// 检查闪送配置
func (c *CommonService) CheckShanSong() bool {
	// 查询启用禁用
	type StoreDelivey struct {
		FinanceCode string `json:"finance_code"`
		Status      int    `json:"status"`
	}

	data := StoreDelivey{}
	flag := false
	isOk, err := c.session.SQL("select finance_code , status from datacenter.store_delivery;").Get(&data)
	if err != nil {
		glog.Error("查询是开启闪送异常：", err.Error())
	}

	glog.Info("检查配置闪送信息：", kit.JsonEncode(data))
	if isOk {
		if data.Status == 1 {
			flag = true
		}
	}
	return flag

}

// 推送闪送，闪送需要先提交计费，最后提交订单
func (c *CommonService) PushShanSong() error {
	//return errors.New("闪送配送暂未启用")

	//闪送超过10公斤不发起配送
	if c.orderMain.TotalWeight > 10000 {
		return errors.New("配送超过10公斤")
	}

	//根据门店财务编码获取对应的数据中心仓库类型(根据类型推送子龙还是全渠道,grpc接口张震提供)
	glog.Info("推送闪送，获取对应的数据中心仓库类型(GetWarehouseInfoByFanceCode) 入参 ", c.orderMain.OrderSn, " FinanceCode : "+c.orderMain.ShopId)
	dcClient := dc.GetDcDispatchClient()
	defer dcClient.Close()

	warehouse, err := dcClient.RPC.GetWarehouseInfoByFanceCode(dcClient.Ctx, &dc.GetWarehouseInfoByFanceCodeRequest{FinanceCode: c.orderMain.ShopId})
	if err != nil {
		return err
	}

	dacClient := dac.GetDataCenterClient()
	defer dacClient.Close()
	glog.Info("推送闪送，获取店铺配送配置," + c.orderMain.OrderSn + ",FinanceCode: " + c.orderMain.ShopId + ",仓库编号:" + warehouse.Warehouseinfo.Code)
	store, err := dacClient.RPC.GetStoreDeliveryList(dacClient.Ctx, &dac.GetStoreDeliveryListRequest{
		FinanceCode: warehouse.Warehouseinfo.Code,
		Status:      2,
	})
	if err != nil {
		return err
	}

	var storeDelivery *dac.StoreDelivery
	if len(store.Data) > 0 {
		storeDelivery = store.Data[0]
	} else {
		glog.Info("推送闪送，该店铺没有配置配送," + c.orderMain.OrderSn + ",FinanceCode: " + c.orderMain.ShopId + ",仓库编号:" + warehouse.Warehouseinfo.Code)
		return errors.New("该店铺没有配置配送")
	}

	//lng,lat:= utils.GCJ02ToBD09(cast.ToFloat64(warehouseLng) ,cast.ToFloat64(warehouseLat))
	receiverLng, receiverLat := utils.GCJ02ToBD09(c.orderDetail.Longitude, c.orderDetail.Latitude)

	iss := et.IssOrderCalculateRequest{
		CityName:     storeDelivery.City,
		AppointType:  0,
		StoreId:      storeDelivery.ThirdStoreId,
		DeliveryType: 1,
		Sender: &et.IssOrderCalculateSender{
			FromAddress:    storeDelivery.StoreAddress,
			FromSenderName: storeDelivery.StoreName,
			FromMobile:     storeDelivery.ContactInformation,
			FromLongitude:  storeDelivery.Lng,
			FromLatitude:   storeDelivery.Lat,
		},
	}

	receiverPhone := c.orderMain.ReceiverPhone
	if len(c.orderMain.EnReceiverPhone) > 0 {
		receiverPhone = utils.MobileDecrypt(c.orderMain.EnReceiverPhone)
	}
	if len(receiverPhone) > 0 {
		receiverPhone = strings.Replace(receiverPhone, ",", "#", -1)
		receiverPhone = strings.Replace(receiverPhone, "_", "#", -1)
	}
	receiverList := et.IssOrderCalculateReceiver{
		OrderNo:        c.orderMain.OrderSn,
		ToAddress:      c.orderMain.ReceiverAddress,
		ToReceiverName: c.orderMain.ReceiverName,
		ToMobile:       receiverPhone,
		GoodType:       storeDelivery.GoodType,
		Weight:         cast.ToInt32(math.Ceil(float64(c.orderMain.TotalWeight) / 1000)),
		ToLongitude:    cast.ToString(receiverLng),
		ToLatitude:     cast.ToString(receiverLat),
	}
	iss.ReceiverList = append(iss.ReceiverList, &receiverList)
	//美配的快递标识
	deliveryServiceCode := int32(5001)

	etClient := et.GetExternalClient()
	defer etClient.Close()

	glog.Info("推送闪送，配送单计费接口参数", c.orderMain.OrderSn, kit.JsonEncode(iss))

	res, err := etClient.ShanSong.OrderCalculate(etClient.Ctx, &iss)
	if err != nil {
		glog.Error("推送闪送配送单计费接口错误！ ", c.orderMain.OrderSn, err.Error())
		return err
	}
	glog.Info("推送闪送，配送单计费接口返回", c.orderMain.OrderSn, kit.JsonEncode(res))
	if res.Code == 200 {
		issOrderNo := et.IssOrderNoRequest{
			IssOrderNo: res.Data.OrderNumber,
		}
		glog.Warning("推送闪送，提交配送单接口参数", c.orderMain.OrderSn, kit.JsonEncode(issOrderNo))
		placeRes, err := etClient.ShanSong.OrderPlace(etClient.Ctx, &issOrderNo)
		if err != nil {
			glog.Error("推送闪送，提交配送单接口异常！ ", c.orderMain.OrderSn, err.Error())
			return errors.New("推送闪送，提交配送单接口错误！")
		}
		glog.Info("推送闪送，提交配送单计费接口返回", c.orderMain.OrderSn, kit.JsonEncode(placeRes))
		if placeRes.Code == 200 {
			deliveryId := cast.ToInt64(GetSn("delivery")[0])
			DeliveryRecord := models.OrderDeliveryRecord{
				DeliveryId:          deliveryId,
				MtPeisongId:         placeRes.Data.OrderNumber,
				OrderSn:             c.orderMain.OrderSn,
				DeliveryServiceCode: deliveryServiceCode,
				TotalAmount:         cast.ToString(placeRes.Data.TotalAmount),
				TotalFeeAfter:       cast.ToString(placeRes.Data.TotalFeeAfterSave),
				CouponFee:           cast.ToString(placeRes.Data.CouponSaveFee),
			}
			glog.Info("提交闪送配送单,添加返回单号参数 ", c.orderMain.OrderSn, DeliveryRecord)
			_, err = c.session.Insert(&DeliveryRecord)
			if err != nil {
				glog.Error("提交闪送配送单,添加返回单号失败 ", c.orderMain.OrderSn, err.Error())
				return err
			}
			_, err = c.session.Where("order_sn = ?", c.orderMain.OrderSn).Update(&models.OrderDetail{
				PushDelivery: 1,
			})
			if err != nil {
				glog.Error("推送美团配送单,更新order状态失败 ", c.orderMain.OldOrderSn, err.Error())
			}
			glog.Info("推送闪送成功", c.orderMain.OrderSn)
		} else {
			return errors.New(placeRes.Error)
		}
	} else {
		return errors.New(res.Error)
	}

	return err
}

func (c *CommonService) RefundPushThird(refundOrder models.RefundOrder) {
	glog.Info(refundOrder.RefundSn, "退款单推送第三方", kit.JsonEncode(refundOrder))
	c.orderMain = GetOrderMainByOrderSn(refundOrder.OrderSn)
	if c.orderMain.Id == 0 {
		glog.Error("退款回调,查询不到对应的订单:" + refundOrder.OrderSn)
		return
	}

	//虚拟商品不推第三方
	if c.orderMain.IsVirtual == 1 {
		return
	}
	if refundOrder.AppChannel == SaasAppChannel {
		return
	}
	// orderDetail := GetOrderDetailByOrderSn(c.orderMain.OrderSn, "push_third_order")
	has := true
	//1整单退款 2部分退款
	if refundOrder.FullRefund == 2 {
		has = false
	}
	//通知退货
	err := c.AllChannelRefundNotice(refundOrder.RefundSn, has, 0, false, c.session)
	if err != nil {
		glog.Error("美团退款,通知第三方系统失败！", c.orderMain.OldOrderSn, err.Error())
	}
}

// 全渠道退货通知
func (c *CommonService) AllChannelRefundNotice(refundSn string, has bool, resultType int32, retry bool, resession *xorm.Session) (err error) {
	glog.Info(c.orderMain.OldOrderSn, "-", refundSn, "部分退款进入推送第三方: ")
	var (
		OrderProduct []dto.OrderProductAndRet
	)

	pushThird := new(dto.OrderPushThird)
	// 检测是否已经推送过
	if ok, err := c.session.Table("refund_order").Alias("r").Join("inner", "order_detail d", "d.order_sn = r.order_sn").
		Where("refund_sn = ?", refundSn).Select("r.push_third,d.push_third_order,channel_id,order_source").Get(pushThird); err != nil {
		return err
	} else if !ok {
		return errors.New("未找到退款单")
	} else if pushThird.PushThird > 0 {
		if !(pushThird.ChannelId == 5 || (pushThird.OrderSource == 5 && pushThird.ChannelId == 1)) {
			return nil // 已经推送不处理
		}
	}

	defer func() {
		if c.orderMain.AppChannel != SaasAppChannel {
			// 记录退款单推送脚印
			refundLog := &models.RefundOrderLog{
				RefundSn:    refundSn,
				OldOrderSn:  c.orderMain.OldOrderSn,
				Ctime:       time.Now(),
				Operationer: "阿闻系统",
				NotifyType:  "pushThird",
			}
			refundUpdate := make(map[string]interface{})

			if err != nil {
				refundLog.OperationType = fmt.Sprintf("退款单推送%s失败", models.GetOrderSourceChannel(c.orderMain.Source))
				refundLog.Reason = err.Error()
				refundUpdate["push_third"] = 0
				refundUpdate["push_third_fail_reason"] = err.Error()
			} else {
				refundLog.OperationType = fmt.Sprintf("退款单推送%s成功", models.GetOrderSourceChannel(c.orderMain.Source))
				refundUpdate["push_third"] = 1
			}

			if _, ie := c.session.Insert(refundLog); ie != nil {
				glog.Info(refundSn, "-AllChannelRefundNotice 插入脚印出错 ", ie.Error())
			}
			// 已成功的不需要再更新
			if _, ue := c.session.Table("refund_order").Where("refund_sn = ? and push_third = 0", refundSn).Update(refundUpdate); ue != nil {
				glog.Info(refundSn, "-AllChannelRefundNotice 更新推送状态出错 ", ue.Error())
			}
		}
	}()

	if pushThird.PushThirdOrder < 1 && c.orderMain.AppChannel != SaasAppChannel {
		return errors.New(fmt.Sprintf("因为正向单没有推至%s，所以逆向单推单失败", models.GetOrderSourceChannel(c.orderMain.Source)))
	}

	if c.IsThirdOrder() {
		//非阿闻
		err = c.session.SQL(`select product_type,refund_price AS pay_price, order_product_id,sku_id,tkcount,quantity,barcode,refund_amount,parent_sku_id,privilege_total,order_product_id as id 
FROM refund_order_third_product WHERE refund_sn=?`, refundSn).Find(&OrderProduct)
	} else {
		err = c.session.SQL(`select 
							a.*,b.tkcount,
							b.quantity,
							a.id as order_product_id,
							b.refund_amount 
							FROM refund_order_product b 
							INNER JOIN order_product a ON a.sku_id=b.sku_id AND a.pay_price=b.refund_price 
							WHERE b.refund_sn=? AND a.order_sn=? GROUP BY b.id`,
			refundSn, c.orderMain.OrderSn).Find(&OrderProduct)
	}

	if err != nil {
		return errors.New("部分退款查询错误")
	}

	var (
		sumRefundAmount   float64
		sumDiscountAmount float64
		sumtkCount        int
		RefundGoodsOrders []*oc.RefundGoodsOrder
	)

	//OrderProduct 为退款商品商品的子商品集合
	//找出其中的实物商品
	//如果下面的针对OrderProduct的处理需要增加字段 则第三方的退款数据需要在退款单拆单的时候写入
	for _, i2 := range OrderProduct {
		//实物商品才会推送
		if i2.ProductType == 1 {
			sumDiscountAmount += kit.FenToYuan(i2.PrivilegeTotal)
			if i2.Tkcount == 0 {
				continue
			}
			fl := cast.ToFloat64(i2.RefundAmount)
			sumRefundAmount += fl

			sumtkCount += i2.Tkcount
			goods := &oc.RefundGoodsOrder{
				GoodsId:        i2.SkuId,
				Quantity:       int32(i2.Tkcount),
				RefundAmount:   fmt.Sprintf("%.2f", fl),
				OcId:           i2.Id, //子单商品id
				Barcode:        i2.BarCode,
				Id:             i2.Id, //子单商品id
				OrderProductId: i2.OrderProductId,
				RefundPrice:    kit.FenToYuan(i2.PayPrice),
			}
			RefundGoodsOrders = append(RefundGoodsOrders, goods)
		}
	}
	//没有可退数量，直接不用推送全渠道了
	if sumtkCount == 0 {
		return errors.New("没有可退数量")
	}

	//第三方调用过来时是主单号 所以推单需要推子单号 但是其他流程可能还是依然走主单号
	//所以需要新建变量realOrder来走 以免影响c的值
	//v6.0添加
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
		session:   c.session,
	}
	if c.IsThirdOrder() && c.orderMain.ParentOrderSn == "" {
		mainOrderSn := c.orderMain.OrderSn
		have, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, "-全渠道退货通知第三方订单查询实物子单出错，", err)
			return errors.New("全渠道退货通知查询实物子单出错")
		}
		if !have {
			glog.Error(mainOrderSn, "-全渠道退货通知未查询到实物子订单，", err)
			return errors.New("全渠道退货通知未到查询实物子单")
		}
	} else {
		realOrder.orderMain = c.orderMain
	}

	// 商城或者电商仓
	if (c.orderMain.ChannelId == ChannelMallId || realOrder.orderMain.Source == 5) && realOrder.orderMain.OrderType != 21 {
		var params = &oc.AfterApplyOrderRequest{
			RefundSn:          refundSn,
			OrderSn:           realOrder.orderMain.OrderSn,
			CreateTime:        kit.GetTimeNow(),
			RefundTypeSn:      "waitagree",
			ReasonCode:        "01",
			RefundType:        2,
			RefundGoodsOrders: RefundGoodsOrders,
			DiscountAmount:    fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Privilege)),
			PostFee:           fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Freight)),
			RefundReason:      "退款回调",
			RefundAmount:      fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Total)),
			OrderSource:       1,
		}
		if resultType == 1 {
			params.Status = "finished"
		} else if resultType == 2 {
			params.Status = "refuse"
		}

		glog.Info(refundSn, "-第三方退货订单请求管易方法：", kit.GetTimeNow())
		model := SetAfterOrderRequest(params)
		omsService := OmsService{}
		out, err1 := omsService.AfterOrderSynchronizeToOms(context.Background(), model)
		glog.Info(refundSn, "-第三方退货订单请求管易方法结束时间：", kit.GetTimeNow())

		if err1 != nil {
			return err1
		}
		if out.Code != 0 {
			return errors.New(out.Message)
		}
	} else {
		if realOrder.orderMain.Source == 1 {
			//推送全渠道
			var Refund = oc.AfterApplyOrderRequest{
				RefundSn:          refundSn,
				OrderSn:           realOrder.orderMain.OrderSn,
				CreateTime:        kit.GetTimeNow(),
				Status:            "waitAgree",
				RefundTypeSn:      "RefundAndGoods",
				ReasonCode:        "01",
				RefundType:        2,
				RefundGoodsOrders: RefundGoodsOrders,
				DiscountAmount:    fmt.Sprintf("%.2f", sumDiscountAmount),
				PostFee:           "0",
				RefundReason:      "退款回调",
				RefundAmount:      fmt.Sprintf("%.2f", sumRefundAmount),
				OrderSource:       1,
			}

			model := SetAfterOrderRequest(&Refund)
			glog.Info(refundSn, "-全渠道部分退款进入zhou 2 ")

			if has {
				//连接池勿关闭
				redisConn := GetRedisConn()
				lockCard := "qqd:lock:" + realOrder.orderMain.OrderSn
				lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 60*time.Minute).Val()
				if !lockRes {
					return nil
				}
			}
			glog.Info(realOrder.orderMain.OldOrderSn, ",", refundSn, "-调用全渠道退款")

			allChannelService := AllChannelService{}
			grpcRes, err := allChannelService.AfterOrderSynchronizeNew(context.Background(), model)
			if err != nil {
				return err
			}
			if grpcRes.Code != 200 {
				return errors.New(grpcRes.Message)
			}

		} else if realOrder.orderMain.Source == 3 && realOrder.orderMain.AppChannel != SaasAppChannel {
			//推送子龙退款单
			allRefund := 1
			if !has {
				allRefund = 0
			}

			//先冻结
			glog.Info(realOrder.orderMain.OldOrderSn, "-", refundSn, "-推送子龙通知子龙冻结订单开始！")
			refundOrderRePush := dto.RefundOrderParameter{
				RefundGoodsOrders: RefundGoodsOrders,
				RefundSn:          refundSn,
				IsCancel:          0,
				AllRefund:         allRefund,
			}
			//没有退款单时调用取消,allRefund为是否已经全部退完
			return realOrder.PushZiLongRefundOrder(&refundOrderRePush, retry, resession)
		} else if realOrder.orderMain.Source == 4 { //rp oms
			lockCard := refundSn
			redis := GetRedisConn()
			lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Second).Val()
			defer redis.Del(lockCard)
			if lockRes {
				err = c.PushRPOmsRefund(realOrder.orderMain, refundSn, RefundGoodsOrders, "")
				if err != nil {
					glog.Errorf(refundSn, "-全渠道推送RPOMS失败：", err.Error())
					return err
				}
				glog.Info(refundSn, "-全渠道推送RPOMS成功", kit.JsonEncode(realOrder.orderMain))
			} else {
				return errors.New("获取锁失败")
			}

		}
	}
	return nil
}

// 推送子龙退款单
// retry参数控制当重试逻辑跑该方法时不会产生重推的死循环
func (c *CommonService) PushZiLongRefundOrder(refundParam *dto.RefundOrderParameter, retry bool, resession *xorm.Session) error {
	var err error
	//订单机构ID
	zlShopIdInt := c.ShopIdToZilongId(c.orderMain.ShopId)
	if zlShopIdInt == 0 {
		errors.New("店铺未找到对应子龙ID！")
	}

	//出库机构ID
	zlWarehouse := c.ShopIdToZilongId(c.orderMain.WarehouseCode)
	if zlWarehouse == 0 {
		errors.New("出货仓库未找到对应子龙ID！")
	}

	glog.Info(c.orderMain.OrderSn, "-", refundParam.RefundSn, "-推送子龙退款单通知子龙订单开始！", kit.JsonEncode(refundParam))
	//在前面的调用链中，都赋值了c.orderMain
	orderMain := *c.orderMain
	//在前面的调用链中，有的赋值了c.orderDetail 有的没有 所以此处如果没有赋值 则进行赋值
	if c.orderDetail == nil || c.orderDetail.OrderSn == "" {
		c.orderDetail = GetOrderDetailByOrderSn(orderMain.OrderSn)
	}

	//退款推子龙:非自配不推配送费,自配推优惠后的配送费
	noSelfDistribution := 0

	if orderMain.DeliveryType != 3 {
		if orderMain.ChannelId == ChannelElmId {
			if orderMain.LogisticsCode != "6" {
				noSelfDistribution = 1
			}
		} else if orderMain.ChannelId == ChannelJddjId {
			if orderMain.LogisticsCode != "2938" {
				noSelfDistribution = 1
			}
		} else if orderMain.ChannelId == ChannelMtId {
			//美团专配
			if strings.Contains("2002,1001,1004,2010,3001,1007", orderMain.LogisticsCode) {
				noSelfDistribution = 1
			}
		}
	}
	//配送费，包装费，退款金额，退款商品金额，差额, 配送费优惠
	var Freight, PackingCost, RefundAmount, GoodsPayTotal, Difference, sumFreightPrivilege int
	var RmaDetails []dto.RmaDetails
	//是否有虚实组合商品
	isHaveVir := 0
	goodsPrivilegePt := int64(0)
	if err := func() error {
		var skuThirds []models.OrderProductThirdSku
		if err := GetDBConn().Table("order_product").Where("order_sn = ?", orderMain.OrderSn).Select("id,sku_id,third_sku_id,combine_type,privilege_pt").
			Find(&skuThirds); err != nil {
			return errors.New("查询货号出错 " + err.Error())
		}

		itemCodeMap := map[string]string{}
		//虚实组合的商品
		itemcombine := map[string]string{}
		for _, v := range skuThirds {
			itemCodeMap[v.SkuId] = v.ThirdSkuId
			if v.CombineType == 3 {
				itemcombine[cast.ToString(v.Id)] = "1"
			}
			goodsPrivilegePt += v.PrivilegePt
		}

		refundGoodsMap := map[string]*dto.RmaDetails{}
		for _, v := range refundParam.RefundGoodsOrders {
			itemCode := itemCodeMap[v.GoodsId]
			if len(itemCode) == 0 {
				return errors.New(orderMain.OldOrderSn + "未找到对应子龙sku: " + v.GoodsId)
			}
			cleanItemCode := utils.StringCleaning(itemCode)
			//如果是有虚实组合的
			if _, ok := itemcombine[v.OcId]; ok {
				isHaveVir = 1
			}
			rmaRefund := cast.ToFloat64(v.RefundAmount)
			if _, ok := refundGoodsMap[itemCode]; ok {
				refundGoodsMap[itemCode].RmaCount += int(v.Quantity)

				decimalValue := decimal.NewFromFloat(rmaRefund)
				itemCodeDecimalValue := decimal.NewFromFloat(refundGoodsMap[itemCode].RmaRefund)
				finaldecimalValue := decimalValue.Add(itemCodeDecimalValue)
				rmaRefundValue, _ := finaldecimalValue.Float64()
				refundGoodsMap[itemCode].RmaRefund = rmaRefundValue
			} else {
				refundGoodsMap[itemCode] = &dto.RmaDetails{
					ItemCode:  cleanItemCode,
					RmaCount:  int(v.Quantity),
					DetailId:  orderMain.OrderSn + cleanItemCode,
					RmaRefund: rmaRefund,
				}
			}
		}

		for _, v := range refundGoodsMap {
			RmaDetails = append(RmaDetails, *v)
			//计算退款商品实际支付金额
			GoodsPayTotal += kit.YuanToFen(v.RmaRefund)

		}

		return nil
	}(); err != nil {
		return err
	}

	freightPrivilege := c.FreightCal()

	//计算运费优惠add by csf@20201102

	//查询退款单的平台补贴金额
	var RefundOrder models.RefundOrder
	activityPtAmount := "0"
	deliveryPrice := "0"
	_, err = resession.SQL("select activity_pt_amount,delivery_price from refund_order where refund_sn=?", refundParam.RefundSn).Get(&RefundOrder)
	if err != nil {
		glog.Error(orderMain.OldOrderSn+"-退款单推送获取平台补贴错误", err)
		return errors.New("退款单推送获取平台补贴错误！")
	}
	activityPtAmount = RefundOrder.ActivityPtAmount
	deliveryPrice = RefundOrder.DeliveryPrice
	//判断是否拦截不推送子龙
	isIntercept := false

	//有些地方先推送子龙再插入的数据库，只能再计算了
	if refundParam.AllRefund == 1 {
		var RefundOrder models.RefundOrder
		noworder := ""
		if c.orderMain.ParentOrderSn == "" {
			noworder = c.orderMain.OrderSn
		} else {
			noworder = c.orderMain.ParentOrderSn
		}
		totals, _ := c.session.Where("order_sn=? and refund_state=3 and refund_sn!=?", noworder, refundParam.RefundSn).Sums(RefundOrder, "activity_pt_amount", "delivery_price")
		//平台优惠金额
		activityPtAmount = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.PtChargeTotal-int32(kit.YuanToFen(totals[0]))))

		if c.IsThirdOrder() {
			//如果是虚实组合，
			if isHaveVir == 1 {
				isIntercept = true
				//并且以前没有退成功过，这一次是全部退款需要推送子龙
				if cast.ToInt32(totals[0]) == 0 {
					//平台补贴等于实物商品均摊的平台补贴之和加上平台运费补贴
					activityPtAmount = fmt.Sprintf("%.2f", kit.FenToYuan(int32(goodsPrivilegePt)+c.orderMain.PtFreightPrivilege))
					isIntercept = false
				}

			}
		}

	} else {
		if isHaveVir == 1 {
			isIntercept = true
		}
	}

	//全部退款
	if refundParam.AllRefund == 1 {
		Freight = int(c.orderMain.Freight - freightPrivilege - orderMain.PtFreightPrivilege)
		PackingCost = int(orderMain.PackingCost)
		//运费优惠
		sumFreightPrivilege = int(orderMain.PtFreightPrivilege)

		//如果不是饿了么的，那么商家运费优惠也要算进去
		if c.orderMain.ChannelId != ChannelElmId {
			sumFreightPrivilege = sumFreightPrivilege + int(freightPrivilege)
		}
	} else {
		Freight = 0
		PackingCost = 0
		sumFreightPrivilege = 0
	}
	//非自配
	if noSelfDistribution > 0 {
		Freight = 0
		RefundAmount = GoodsPayTotal + PackingCost + kit.YuanToFen(cast.ToFloat64(activityPtAmount)) - sumFreightPrivilege
	} else {
		RefundAmount = GoodsPayTotal + Freight + PackingCost + kit.YuanToFen(cast.ToFloat64(activityPtAmount))
	}

	//计算差额
	Difference = c.GetDifference(RefundAmount, Freight, PackingCost, GoodsPayTotal)
	glog.Info(orderMain.OrderSn + " " + refundParam.RefundSn + " RefundAmount:" + cast.ToString(RefundAmount) + " Freight:" + cast.ToString(Freight) + " PackingCost:" + cast.ToString(PackingCost) + " GoodsPayTotal:" + cast.ToString(GoodsPayTotal) + " activityPtAmount:" + activityPtAmount + " deliveryPrice" + deliveryPrice)

	RmaMasterRequest := dto.RmaMasterRequest{
		RmaMaster: dto.RmaMaster{
			OrgID:                  zlShopIdInt,
			StoreOrgId:             zlWarehouse,
			OrderCenterOrderNumber: orderMain.OrderSn,
			OrderCenterRmaNumber:   refundParam.RefundSn,
			RmaDate:                kit.GetTimeNow(),
			DispatchinFee:          kit.FenToYuan(Freight),
			PackingFee:             kit.FenToYuan(PackingCost),
			RmaDetails:             RmaDetails,
			RefundAmount:           kit.FenToYuan(RefundAmount),
			Difference:             kit.FenToYuan(Difference),
		},
	}

	url := config.GetString("bj-scrm-url") + "ordercenter/rmaMaster/createsalereturn"

	dataJson := kit.JsonEncodeByte(RmaMasterRequest)
	glog.Info(c.orderMain.OrderSn, "-", refundParam.RefundSn+"-退款单推送子龙请求参数： ", string(dataJson), ",", url)
	if isIntercept {
		return errors.New("暂不支持虚实组合退款推送子龙，请联系IT处理！")
	}
	retCode, resData, err := utils.HttpPostZl(url, dataJson, "")
	glog.Info(c.orderMain.OrderSn, "-", refundParam.RefundSn+"-退款单推送子龙返回结果： ", string(resData), ",", url)

	//网络错误 且不是重试的请求 进行重试
	if retCode == code.HttpRequestError && !retry {
		//扔一个重试mq
		c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
			Count:    0,
			Type:     dto.RePushZiLongRefundOrder,
			OrderSn:  c.orderMain.OrderSn,
			RefundSn: refundParam.RefundSn,
		})
	}
	if err != nil {
		return err
	}

	var baseRes = &dto.ZiLongPushOrderResponse{}
	err = json.Unmarshal(resData, &baseRes)
	if err != nil {
		glog.Error(orderMain.OldOrderSn+"-退款单推送结果解码失败", err)
		return errors.New("退款单！")
	}

	if !baseRes.Success {
		//以下注释的为需要重试的逻辑  可以暂时不删除 怕后续又需要重试
		//如果正向单不存在 则重推正向单 正向单推送成功的时候会检测未推送或者推送失败的退款单并重新进行推送
		//正向单不存在 可能有以下原因
		//1:逆向单来得太快 正向单还在途中，以至于子龙收到退款单时正向单还未落库 这种情况等待正向单推送结束自动推退款单就好
		//2:正向单明确推送失败 在正向单推送失败的情况下 如果是网络错误就会重推 否则不会重推，所以如果正向单推送失败 逆向单不需要进行重推
		//因为正向单如果不重推的情况下推送逆向单没用 我们需要让正向单推送成功 并正向单推送的时候检测是否没有推送成功的退款单，并进行重新推送
		//3:如果正向单根本就没有推 则重新推送一下正向单 正向单推送成功之后会将没有没有推送成功的逆向单再推送
		//4:如果我们检测到正向单明确推送成功了  则只需要重推一下逆向单就好
		if baseRes.StatusCode == code.ZiLongOrderNotExist && strings.Contains(baseRes.Message, "不存在订单") {
			//没有推送的 才再次进行推送  当PushThirdOrder = 0 但是PushThirdOrderReason 不为空时表示的时 推送过了但是推送失败了
			if c.orderDetail.PushThirdOrder == 0 && c.orderDetail.PushThirdOrderReason == "" {
				_, err = new(OrderService).MtRePushThird(context.Background(), &oc.MtRePushThirdRequest{OrderSn: orderMain.OrderSn})
			}
		}
		businessErr, _ := baseRes.BusinessError.(string)
		sysErr, _ := baseRes.SystemError.(string)
		return errors.New(businessErr + "-" + sysErr)
	}
	return nil
}

// 查询运费优惠
func (c *CommonService) FreightCal() int32 {
	return c.CalTotalChargeFreight(nil, 1)
}

// 查询所有运费优惠，包括平台的和商家的
func (c *CommonService) FreightAll() int32 {
	return c.CalTotalChargeFreight(nil, 3)
}

// 锁库存
func (c *CommonService) OrderLockInventory() error {
	freezeParams := &ic.FreezeRequest{
		OrderId: c.orderMain.OrderSn,
		Source:  c.orderMain.ChannelId,
	}

	var warehouseId int32
	// 商城的不需要查仓库id
	if c.orderMain.ChannelId != ChannelMallId {
		warehouseId = c.orderMain.WarehouseId
	}

	switch c.orderMain.ChannelId {
	case ChannelAwenId, ChannelMtId, ChannelElmId, ChannelJddjId:
		freezeParams.Source = 2
	case ChannelMallId:
		freezeParams.Source = 1
	case ChannelDigitalHealth:
		freezeParams.Source = 3
	}

	//得查询出所有商品，不然组合商品里面的数据查询不出来
	orderProductModel := c.GetAllOrderProduct()
	var freezeGoods []models.OrderFreezeStock //订单在途库存数据
	var ids []int64
	var lockStock bool //是否需要锁库存，有实物商品才需要锁库存
	goodsIdMap := map[string]struct{}{}
	for _, i2 := range orderProductModel {
		//实物商品才锁库存，虚拟和组合商品没有库存概念
		if i2.ProductType != 1 {
			continue
		}

		lockStock = true
		ids = append(ids, cast.ToInt64(i2.ProductId))

		if _, ok := goodsIdMap[i2.SkuId]; !ok {
			freezeParams.GoodsList = append(freezeParams.GoodsList, &ic.OrderGoodsInfo{
				GoodsId:       i2.SkuId,
				Number:        i2.Number,
				WarehouseId:   warehouseId,
				WarehouseType: i2.WarehouseType,
			})
			goodsIdMap[i2.SkuId] = struct{}{}

			freezeGood := models.OrderFreezeStock{
				OrderSn:     c.orderMain.OrderSn,
				SkuId:       i2.SkuId,
				Stock:       i2.Number,
				WarehouseId: warehouseId,
			}
			freezeGoods = append(freezeGoods, freezeGood)
		} else {
			for _, i5 := range freezeParams.GoodsList {
				if i5.GoodsId == i2.SkuId {
					i5.Number += i2.Number
				}
			}
			for index, i6 := range freezeGoods {
				if i6.SkuId == i2.SkuId {
					freezeGoods[index].Stock = freezeGoods[index].Stock + i2.Number
				}
			}
		}
	}
	//不需要锁库存
	if !lockStock {
		glog.Info(c.orderMain.OldOrderSn, ", 虚拟订单不需要锁库存")
		return nil
	}

	glog.Info("订单锁库存请求oldordersn: ", c.orderMain.OldOrderSn, ", ", kit.JsonEncode(freezeParams), ", ", kit.RunFuncName(2))

	if c.orderMain.AppChannel == 12 {
		res := FreezeCommitOrder(c.orderMain.OrderSn, c.orderMain.ShopId, c.session)
		glog.Info("订单锁库存请求oldordersn: ", c.orderMain.OldOrderSn, ", ", kit.JsonEncode(freezeParams), ", ", kit.RunFuncName(2), kit.JsonEncode(res))

		if res.Code != 200 {
			glog.Error(c.orderMain.OldOrderSn, ", 锁定库存失败, ", kit.JsonEncode(res))
			return errors.New(res.Msg)
		}
	} else {
		//锁定库存
		client := ic.GetInventoryServiceClient()
		res, err := client.RPC.FreezeStock(client.Ctx, freezeParams)
		glog.Info("订单锁库存请求oldordersn: ", c.orderMain.OldOrderSn, ", ", kit.JsonEncode(freezeParams), ", ", kit.RunFuncName(2), kit.JsonEncode(res))
		if err != nil {
			glog.Error(c.orderMain.OldOrderSn, ", 锁定库存失败, ", err)
			return err
		}
		if res.Code != 200 {
			glog.Error(c.orderMain.OldOrderSn, ", 锁定库存失败, ", kit.JsonEncode(res))
			return errors.New(res.Message)
		}

		//落地在途库存
		if len(freezeGoods) > 0 {
			glog.Info("订单锁在途库存请求oldordersn: ", c.orderMain.OldOrderSn, ", ", kit.JsonEncode(freezeGoods), ", ", kit.RunFuncName(2))
			if _, err = c.session.Insert(&freezeGoods); err != nil {
				glog.Error(c.orderMain.OldOrderSn, ", 锁库存落地在途库存失败, ", err)
				return err
			}
		}
	}

	return nil
}

// 第三方渠道锁库存前先查库存（不查库存的话redis内没有相应键值）
func (c *CommonService) OrderQueryInventory(args ...int32) {

	var warehouseId int32
	if len(args) > 0 {
		warehouseId = args[0]
	}

	//1电商，2本地生活，3互联网医疗
	var stockSource int32
	if c.orderMain.ChannelId == ChannelDigitalHealth {
		stockSource = 3
	} else if c.orderMain.ChannelId == ChannelMallId {
		stockSource = 1
	} else {
		stockSource = 2
	}

	orderProducts := c.GetOrderProduct()
	productsInfo := make([]*pc.ProductsInfo, len(orderProducts))
	var lockStock bool //是否需要锁库存，有实物商品才需要锁库存
	for k := range orderProducts {
		if orderProducts[k].ProductType == 1 {
			lockStock = true
		}
		productsInfo[k] = &pc.ProductsInfo{
			SkuId:       cast.ToInt32(orderProducts[k].SkuId),
			FinanceCode: []string{c.orderMain.ShopId},
			WarehouseId: warehouseId,
		}
	}

	//不需要锁库存
	if !lockStock {
		return
	}
	//todo 商品无需修改
	pcClient := pc.GetDcProductClient()
	defer pcClient.Close()
	if _, err := pcClient.RPC.GetStockInfoBySkuCode(context.Background(), &pc.GetStockInfoRequest{
		ProductsInfo: productsInfo,
		Source:       stockSource,
		ChannelId:    c.orderMain.ChannelId,
	}); err != nil {
		glog.Error(c.orderMain.OldOrderSn, ", 查询库存失败, ", err)
	}
}

// 推送第三方
// retry 是否来自重试请求 重试控制参数
func (c *CommonService) PushThirdOrder(retry bool) (err error) {
	glog.Info(c.orderMain.OrderSn, "进入推送订单到第三方")
	//如果是逍宠订单，则不推送第三方
	if c.orderMain.OrgId == 6 {
		return nil
	}

	orderLogs := []*models.OrderLog{
		{
			OrderSn: c.orderMain.ParentOrderSn,
			LogType: models.OrderLogPushedOrder,
		},
		{
			OrderSn: c.orderMain.OrderSn,
			LogType: models.OrderLogPushedOrder,
		},
	}

	switch c.orderMain.Source {
	case 3: //推送子龙
		_, err = c.PushZilong(retry)
	case 1: //推送全渠道
		oms := &OmsService{
			CommonService: *c,
		}
		_, err = oms.OmsOrderSynchronizeByOrder(c.orderMain)
	case 4: //推送瑞鹏oms
		_, _, err = c.PushRPOmsOrder()
	case 5: // 推送oms
		oms := &OmsService{
			CommonService: *c,
		}
		_, err = oms.OmsOrderSynchronizeByOrder(c.orderMain)
	default:
		glog.Error(c.orderMain.OldOrderSn, ", 订单来源不正确, ", kit.RunFuncName(2))
	}

	//记录推送第三方操作记录
	if err != nil {
		orderLogs = []*models.OrderLog{
			{
				OrderSn: c.orderMain.ParentOrderSn,
				LogType: models.OrderLogPushOrderFailed,
				Reason:  err.Error(),
			},
			{
				OrderSn: c.orderMain.OrderSn,
				LogType: models.OrderLogPushOrderFailed,
				Reason:  err.Error(),
			},
		}
	}

	go SaveOrderLog(orderLogs)

	return err
}

// 查询订单优惠信息
func (c *CommonService) GetOrderPromotion(fields ...string) (orderPromotions []models.OrderPromotion) {
	//如果是第三方的单子 查询订单活动 查询主单的活动信息
	//第三方子单判断 如果是子单 则需要查询主单的活动信息
	orderSn := c.orderMain.OrderSn
	if c.IsThirdOrder() && c.orderMain.ParentOrderSn != "" && c.orderMain.ParentOrderSn != c.orderMain.OrderSn {
		orderSn = c.orderMain.ParentOrderSn
	}

	ss := c.session.Where("order_sn = ?", orderSn)
	if len(fields) > 0 {
		ss.Select(fields[0])
	}

	err := ss.Find(&orderPromotions)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ", 查询订单优惠信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}

	//glog.Info("查询优惠信息:", c.orderMain.OrderSn, orderPromotions)
	return
}

// 查询主订单商品信息
// 对于第三方 只查询拆单前商品（不要查询多出来的组合商品）
func (c *CommonService) GetOrderProduct(fields ...string) (orderProducts []models.OrderProduct) {
	ss := c.session.Where("order_sn = ?", c.orderMain.OrderSn)
	//v6.0之前 第三方的订单 是不会写入组合商品子商品的，所以不会存在parent_sku_id的
	//v6.0之后进行了拆单，大部分第三方流程中也不需要查询子商品，如果需要查询子商品的信息接口请调用v6.0新增方法GetAllOrderProduct
	if c.orderMain.ChannelId != ChannelAwenId && c.orderMain.ChannelId != ChannelMallId && c.orderMain.ChannelId != ChannelDigitalHealth {
		ss.Where("parent_sku_id = ''")
	}
	if len(fields) > 0 {
		ss.Select(fields[0])
	}

	err := ss.Find(&orderProducts)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ", 查询订单商品信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}
	//glog.Info(c.orderMain.OldOrderSn, ", 查询商品信息, ", kit.JsonEncode(orderProducts))
	return
}

// v6.0 新增
// 查询订单商品信息
func (c *CommonService) GetAllOrderProduct(fields ...string) (orderProducts []models.OrderProduct) {
	ss := c.session.Where("order_sn = ?", c.orderMain.OrderSn)
	if len(fields) > 0 {
		ss.Select(fields[0])
	}
	err := ss.Find(&orderProducts)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ", 查询订单商品信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}
	//glog.Info(c.orderMain.OldOrderSn, ", 查询商品信息, ", kit.JsonEncode(orderProducts))
	return
}

// 查询退款申请时查询订单商品信息
// 阿闻渠道传过来的是主单
// 第三方传过来的是主单 但是v6.0之后因为需要对组合商品拆单所以 不能查组合商品的子商品
func (c *CommonService) GetRefundApplyOrderProduct(fields ...string) (orderProducts []models.OrderProduct) {
	ss := c.session.Where("order_sn = ?", c.orderMain.OrderSn)
	//非阿闻渠道的第三方顶大那商品
	if c.orderMain.ChannelId != ChannelAwenId && c.orderMain.ChannelId != ChannelMallId && c.orderMain.ChannelId != ChannelDigitalHealth {
		ss.Where("parent_sku_id = ''")
	}
	if len(fields) > 0 {
		ss.Select(fields[0])
	}

	err := ss.Find(&orderProducts)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ", 查询订单商品信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}
	return
}

// 查询实物子订单订单商品信息
func (c *CommonService) GetRealOrderProduct(orderSn string, fields ...string) (orderProducts []models.OrderProduct) {
	ss := c.session.Where("order_sn = ?", orderSn)
	if len(fields) > 0 {
		ss.Select(fields[0])
	}

	err := ss.Find(&orderProducts)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ", 查询订单商品信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}

	//glog.Info(c.orderMain.OldOrderSn, ", 查询实物子订单商品信息, ", kit.JsonEncode(orderProducts))
	return
}

// 获取子订单购买商品集合
func (c *CommonService) GetChildOrderProducts() (orderGoods []models.OrderProduct) {
	//查询退款数量小于购买数量的
	orderGoods = make([]models.OrderProduct, 0)
	err := c.session.SQL("SELECT a.* FROM order_product a INNER JOIN `order_main` b ON a.order_sn=b.order_sn WHERE b.parent_order_sn=?", c.orderMain.OrderSn).Find(&orderGoods)
	if err != nil {
		glog.Error(c.orderMain.ParentOrderSn, "，售后单获取购买商品出错，", err.Error())
	}

	return orderGoods
}

// 查询售后单商品信息
func (c *CommonService) GetRefundOrderProduct(refundOrderSn string) (refundOrderGoods []models.RefundOrderProduct) {
	//下单时候的商品集合
	refundOrderGoods = make([]models.RefundOrderProduct, 0)
	err := c.session.SQL("select * from refund_order_product where refund_sn=? ", refundOrderSn).Find(&refundOrderGoods)
	if err != nil {
		glog.Error(refundOrderSn, ", 获取售后商品信息失败: ", err)
	}

	return
}

// FreedStockByParentOrderSn 通过订单号整单释放库存
func FreedStockByParentOrderSn(orderSn string) error {
	db := GetDBConn()
	c := &CommonService{
		session:   db.NewSession(),
		orderMain: GetOrderMainByOrderSn(orderSn),
	}

	defer c.session.Close()

	return c.FreedStock(nil)
}

// 释放库存 goodsInfo为空时为整单释放
func (c *CommonService) FreedStock(goodsInfo []*ic.OrderGoodsInfo) error {
	var params ic.FreedStockRequest
	params.OrderId = c.orderMain.OrderSn

	var warehouseId int32
	// 商城的不需要查仓库id
	if c.orderMain.ChannelId != ChannelMallId {
		warehouseId = c.orderMain.WarehouseId
	}

	switch c.orderMain.ChannelId {
	case ChannelAwenId, ChannelMtId, ChannelElmId, ChannelJddjId:
		params.Source = 2
	case ChannelMallId:
		params.Source = 1
	case ChannelDigitalHealth:
		params.Source = 3
	}

	if len(goodsInfo) == 0 {
		products := c.GetAllOrderProduct("product_type,sku_id,number")
		goodsIdMap := map[string]int8{}
		for _, i2 := range products {
			if i2.ProductType != 1 {
				continue
			}
			if _, ok := goodsIdMap[i2.SkuId]; !ok {
				params.GoodsList = append(params.GoodsList, &ic.OrderGoodsInfo{
					GoodsId:     i2.SkuId,
					Number:      i2.Number,
					WarehouseId: warehouseId,
				})
				goodsIdMap[i2.SkuId] = 1
			} else {
				for _, i5 := range params.GoodsList {
					if i5.GoodsId == i2.SkuId {
						i5.Number += i2.Number
					}
				}
			}
		}
	} else {
		params.GoodsList = goodsInfo
	}

	orderDetail := GetOrderByOrderSn(c.orderMain.OrderSn, "push_third_order")
	if orderDetail.PushThirdOrder == 1 {
		params.Status = 1
	}

	glog.Info(params.OrderId, ", 订单释放库存参数, ", kit.JsonEncode(params), ", ", kit.RunFuncName(2))

	//如果是saas宠物连锁,走新的库存释放接口
	if c.orderMain.AppChannel == SaasAppChannel {
		res := UnFreeze(c.orderMain.OrderSn, c.orderMain.ShopId)
		if res.Code != 200 {
			return errors.New(fmt.Sprintf("FreedStock 释放库存失败,msg:%s", res.Msg))
		}
	} else {
		icClient := ic.GetInventoryServiceClient()
		r, err := icClient.RPC.FreedStock(icClient.Ctx, &params)
		if err != nil {
			glog.Error(params.OrderId, ", FreedStock 释放库存调用失败：", err)
			return err
		} else if r.Code != 200 {
			glog.Error(params.OrderId, ", FreedStock 释放库存失败, ", kit.JsonEncode(r))
			return errors.New(r.Message)
		} else {
			glog.Info(params.OrderId, ", FreedStock 释放库存调用成功, ", kit.JsonEncode(r))
		}
	}
	return nil

}

// 释放当日库存限制
func (c *CommonService) FreedDailyStock() {
	//释放单日库存
	orderMain := GetOrderMainByOrderSn(c.orderMain.OrderSn, "order_sn,shop_id")
	if len(orderMain.ShopId) == 0 {
		return
	}

	//查询订单商品信息
	orderProducts := c.GetOrderProduct()

	//查询参与限时折扣的商品信息
	mkClient := mk.GetTimeDiscountServiceClient()
	defer mkClient.Close()

	var Promotionids []string
	var skuids []string
	for _, item := range orderProducts {
		skuids = append(skuids, item.SkuId)
		if item.PromotionId > 0 {
			PromotionIdStr := strconv.Itoa(int(item.PromotionId))
			Promotionids = append(Promotionids, PromotionIdStr)
		}
	}
	limitRes, err := mkClient.TimeDiscount.QueryLimitCountBySkus(mkClient.Ctx, &mk.QueryLimitCountRequest{Shopid: orderMain.ShopId, Promotionids: strings.Join(Promotionids, ","), Skuids: strings.Join(skuids, ",")})
	if err != nil {
		return
		//"查询是否参与限时折扣异常:" + err.Error()
	}

	client := GetRedisConn()
	for _, product := range orderProducts {
		if product.PromotionId <= 0 {
			continue
		}

		//有商品参与折扣活动
		for _, prom := range limitRes.PromotionLimits {
			if prom.ProductSkuId != product.SkuId {
				continue
			}
			date := time.Now().Format("20060102") //年月日格式
			//本单已使用数量
			orderLockKey := fmt.Sprintf("order:limitstockcount:%s:%d:%s:%s:%s", orderMain.ShopId, product.PromotionId, date, prom.ProductSkuId, orderMain.OrderSn)
			ordercount, _ := strconv.Atoi(client.Get(orderLockKey).Val())

			if ordercount > 0 {
				lockKey := fmt.Sprintf("order:limitstockcount:%s:%d:%s:%s", orderMain.ShopId, product.PromotionId, date, prom.ProductSkuId)
				usedcount, _ := strconv.Atoi(client.Get(lockKey).Val())

				client.Set(lockKey, usedcount-ordercount, time.Hour*24)
				client.Del(orderLockKey) //释放掉订单占用掉的库存
			}
		}
	}
}

// 处理电商未支付订单取消
func (c *CommonService) ShopNoPayCancel() error {
	err := c.FreedStock(nil)
	if err != nil {
		glog.Error("取消未支付订单错误:调用释放库存失败", c.orderMain.OrderSn, " ", err)
		return err
	}

	_, err = c.session.ID(c.orderMain.Id).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
		OrderStatus:      0,
		OrderStatusChild: 20205,
		CancelReason:     "未支付自动取消",
		CancelTime:       time.Now(),
	})
	if err != nil {
		glog.Error("取消订单,更新订单状态失败！", c.orderMain.OrderSn, " ", err.Error())
		return err
	}
	//v2.9.10新增 如果是秒杀订单 需要增加活动虚拟库存
	if c.orderMain.OrderType == 12 {
		go FreedSecKillStockByOrderSn(c.orderMain.OrderSn)
	}
	//清理在途库存
	go DeleteTransportationInventory(c.orderMain.OrderSn, c.orderMain.ChannelId)

	return nil
}

// 查询门店设置
func (c *CommonService) GetShopSet() (*dac.ShopSetGetResponse, error) {
	if len(c.orderMain.ShopId) == 0 {
		return nil, errors.New("订单：" + c.orderMain.OrderSn + "，财务编码不能为空")
	}

	//根据门店id取是否自动接单
	glog.Info("根据门店id取门店设置(GetShopSet)入参 ", c.orderMain.OrderSn, " ShopId : "+c.orderMain.ShopId, ", ", kit.RunFuncName(2))
	dataCenterConn := dac.GetDataCenterClient()
	ShopSet, err := dataCenterConn.RPC.ShopSetGet(dataCenterConn.Ctx, &dac.ShopSetGetRequest{ShopId: c.orderMain.ShopId})
	if err != nil {
		glog.Error("调用根据门店id取门店设置(GetShopSet) 调用错误 ", c.orderMain.OrderSn, " ", err.Error())
		return ShopSet, err
	}
	glog.Info("调用根据门店id取门店设置(GetShopSet) 返回 ", c.orderMain.OrderSn, kit.JsonEncode(ShopSet))
	if ShopSet.Code != 200 {
		return ShopSet, errors.New(ShopSet.Message)
	}

	return ShopSet, err
}

// 查询店铺在某个渠道的设置
func (c *CommonService) GetShopBusinessSetupInfo(shopId string) *dac.ShopBusinessSetupInfo {
	// 数据中心grpc
	dacClient := dac.GetDataCenterClient()

	var request = new(dac.ShopBusinessSetupGetRequest)
	request.Finance_Code = shopId
	request.Channel_Id = 1
	grpcRes, grpcErr := dacClient.RPC.ShopBusinessSetupGet(dacClient.Ctx, request)
	if grpcErr == nil && grpcRes.Code == 200 && grpcRes.Data != nil {
		return grpcRes.Data
	}
	if grpcErr != nil {
		glog.Error(grpcErr)
	}
	return nil
}

// 查询店铺配送设置
func (c *CommonService) GetShopDeliveryService(shopId string) *dac.ShopDeliveryServiceDetail {
	// 数据中心grpc
	dacClient := dac.GetDataCenterClient()

	//构造请求
	var request = new(dac.ShopDeliveryServiceDetailRequest)
	request.Channel_Id = 1
	request.Finance_Code = shopId
	// 查询数据
	grpcRes, grpcErr := dacClient.RPC.ShopDeliveryServiceDetail(dacClient.Ctx, request)
	if grpcErr == nil && grpcRes.Code == 200 && grpcRes.Data != nil {
		return grpcRes.Data
	}
	if grpcErr != nil {
		glog.Error(grpcErr)
	}
	return nil
}

// 查询店铺业务设置
func (c *CommonService) GetShopBusinessSetup(shopId string) *dac.ShopBusinessSetupInfo {
	// 数据中心grpc
	dacClient := dac.GetDataCenterClient()

	//构造请求
	var request = new(dac.ShopBusinessSetupGetRequest)
	request.Channel_Id = 1
	request.Finance_Code = shopId
	// 查询数据
	grpcRes, grpcErr := dacClient.RPC.ShopBusinessSetupGet(dacClient.Ctx, request)
	if grpcErr == nil && grpcRes.Code == 200 && grpcRes.Data != nil {
		return grpcRes.Data
	}
	if grpcErr != nil {
		glog.Error(grpcErr)
	}
	return nil
}

// 查询店铺信息
func (c *CommonService) GetShopInfo(shopId string) *dac.ShopStoreInfo {
	// 数据中心grpc
	dacClient := dac.GetDataCenterClient()

	// 构造请求
	var request = new(dac.ShopStoreGetRequest)
	request.Finance_Code = shopId
	request.Channel_Id = 1
	// 查询数据
	grpcRes, grpcErr := dacClient.RPC.ShopStoreGet(dacClient.Ctx, request)
	if grpcErr == nil && grpcRes.Code == 200 && grpcRes.Data != nil {
		return grpcRes.Data
	}
	if grpcErr != nil {
		glog.Error(grpcErr)
	}
	return nil
}

// 取消订单 退货通知
// 第三方退货通知cancelType 0是美团调用取消需要取退款单退货数量  1是后台取消取订单表退货数量
// cancelType 1：管家后台取消  0： 客户取消
// hasRefundSn 是否有退款码 ：0否 1是
func (c *CommonService) RefundNotice(CancelReason string, RefundSn string, cancelType int, hasRefundSn int) (err error) {
	//第三方调用过来时是主单号 所以推单需要推子单号 但是其他流程可能还是依然走主单号
	//所以需要新建变量realOrder来走 以免影响c的值
	//v6.0添加
	glog.Info(RefundSn, "退款通知第三方打印", hasRefundSn)
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
		session:   c.session,
	}

	//连接池勿关闭
	redisConn := GetRedisConn()
	//防止重复推
	lockCard := "qqd:lock:" + c.orderMain.OldOrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		glog.Info("第三方退货获取锁失败：" + c.orderMain.OldOrderSn)
		return nil
	} else {
		defer redisConn.Del(lockCard)
	}

	if c.IsThirdOrder() && c.orderMain.ParentOrderSn == "" {
		mainOrderSn := c.orderMain.OrderSn
		has, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, "第三方订单退货通知查询实物子单出错，", err)
			return errors.New("第三方订单退货通知查询实物子单出错")
		}
		if !has {
			glog.Error(mainOrderSn, "第三方订单退货通知未查询到实物子订单，", err)
			return errors.New("第三方订单退货通知未到查询实物子单")
		}
	} else {
		realOrder.orderMain = c.orderMain
	}

	detail := GetOrderDetailByOrderSn(realOrder.orderMain.OrderSn, "push_third_order")
	// 针对取消订单，如果正向单没推送，逆向单也不推送了
	if detail.PushThirdOrder == 0 {
		return nil
	}

	pushThird := new(dto.OrderPushThird)
	// 检测是否已经推送过
	_, err = c.session.Table("refund_order").Where("refund_sn = ?", RefundSn).Select("push_third").Get(pushThird)
	if err != nil {
		return err
	} else if pushThird.PushThird > 0 {
		return nil // 已经推送不处理
	}

	defer func() {
		if realOrder.orderMain.AppChannel != SaasAppChannel {
			// 记录退款单推送脚印
			refundLog := &models.RefundOrderLog{
				RefundSn:    RefundSn,
				OldOrderSn:  c.orderMain.OldOrderSn,
				Ctime:       time.Now(),
				Operationer: "阿闻系统",
				NotifyType:  "pushThird",
			}
			refundUpdate := make(map[string]interface{})

			if err != nil {
				refundLog.OperationType = fmt.Sprintf("退款单推送%s失败", models.GetOrderSourceChannel(c.orderMain.Source))
				refundLog.Reason = err.Error()
				refundUpdate["push_third"] = 0
				refundUpdate["push_third_fail_reason"] = err.Error()
			} else {
				refundLog.OperationType = fmt.Sprintf("退款单推送%s成功", models.GetOrderSourceChannel(c.orderMain.Source))
				refundUpdate["push_third"] = 1
			}

			if _, ie := c.session.Insert(refundLog); ie != nil {
				glog.Info("RefundNotice 插入脚印出错 ", ie.Error())
			}
			// 已成功的不需要再更新
			if _, ue := c.session.Table("refund_order").Where("refund_sn = ? and push_third = 0", RefundSn).Update(refundUpdate); ue != nil {
				glog.Info("RefundNotice 更新推送状态出错 ", ue.Error())
			}
		}
	}()

	RefundGoodsOrders := realOrder.GetRefundGoods(RefundSn, cancelType, hasRefundSn)
	glog.Info(RefundSn, "退款通知第三方打印获取商品数据结果", kit.JsonEncode(realOrder.orderMain), kit.JsonEncode(RefundGoodsOrders))
	if len(RefundGoodsOrders) == 0 {
		return nil
	}

	if (realOrder.orderMain.ChannelId == ChannelMallId || realOrder.orderMain.Source == 5) && realOrder.orderMain.OrderType != 21 {
		var params = &oc.AfterApplyOrderRequest{
			RefundSn:          RefundSn,
			OrderSn:           realOrder.orderMain.OrderSn,
			CreateTime:        kit.GetTimeNow(),
			Status:            "agree",
			RefundTypeSn:      "RefundAndGoods",
			ReasonCode:        "01",
			RefundType:        2,
			RefundGoodsOrders: RefundGoodsOrders,
			DiscountAmount:    fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Privilege)),
			PostFee:           fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Freight)),
			RefundReason:      CancelReason,
			RefundAmount:      fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Total)),
			OrderSource:       1,
		}
		glog.Info("第三方退货订单请求管易方法：", kit.GetTimeNow())
		model := SetAfterOrderRequest(params)
		omsService := OmsService{}
		_, err := omsService.AfterOrderSynchronizeToOms(context.Background(), model)
		glog.Info("取消未支付订单请求管易方法结束时间：", kit.GetTimeNow())
		if err != nil {
			return err
		}
	} else {
		if realOrder.orderMain.Source == 1 {
			//推送到全渠道
			var Refund = oc.AfterApplyOrderRequest{
				RefundSn:          RefundSn,
				OrderSn:           realOrder.orderMain.OrderSn,
				CreateTime:        kit.GetTimeNow(),
				Status:            "waitAgree",
				RefundTypeSn:      "RefundAndGoods",
				ReasonCode:        "01",
				RefundType:        2,
				RefundGoodsOrders: RefundGoodsOrders,
				DiscountAmount:    fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Privilege)),
				PostFee:           fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Freight)),
				RefundReason:      CancelReason,
				RefundAmount:      fmt.Sprintf("%.2f", kit.FenToYuan(realOrder.orderMain.Total)),
				OrderSource:       1,
			}

			model := SetAfterOrderRequest(&Refund)
			allChannelService := AllChannelService{}
			grpcRes, err := allChannelService.AfterOrderSynchronizeNew(context.Background(), model)
			if err != nil {
				return err
			}
			if grpcRes.Code != 200 {
				return errors.New(grpcRes.Message)
			}

		} else if realOrder.orderMain.Source == 4 { //瑞鹏oms推送
			lockCard := RefundSn
			redis := GetRedisConn()
			lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Second).Val()
			defer redis.Del(lockCard)
			if lockRes {
				err := c.PushRPOmsRefund(realOrder.orderMain, RefundSn, RefundGoodsOrders, CancelReason)
				if err != nil {
					glog.Errorf(RefundSn, "推送RPOMS失败：", err.Error())
					return err
				}
				glog.Info(RefundSn, "全渠道推送RPOMS成功", kit.JsonEncode(realOrder.orderMain))
			}
		} else if realOrder.orderMain.AppChannel != SaasAppChannel {
			//推送到子龙  没有退款单时调用取消,allRefund为是否已经全部退完
			//查退款订单 第三依然要用主单号  不能使用realOrder
			var RefundOrder []models.RefundOrder
			realOrder.session.Where("old_order_sn = ?", c.orderMain.OldOrderSn).Find(&RefundOrder)
			isCancel := 0
			if len(RefundOrder) == 0 {
				isCancel = 1
			}

			//先冻结
			glog.Info("推送子龙通知子龙冻结订单开始！", realOrder.orderMain.OldOrderSn)
			refundOrderRePush := dto.RefundOrderParameter{
				RefundGoodsOrders: RefundGoodsOrders,
				RefundSn:          RefundSn,
				IsCancel:          isCancel,
				AllRefund:         1,
			}

			glog.Info("推送子龙开始！", c.orderMain.OldOrderSn)
			//没有退款单时调用取消,allRefund为是否已经全部退完
			return realOrder.PushZiLongRefundOrder(&refundOrderRePush, false, c.session)
		}
	}
	return nil
}

// 查询退款商品
// v6.0
// cancelType 1：管家后台取消  0：客户取消
// hasRefundSn 是否有退款码 ：0否 1是
// 50000005454,4100000015513745,4100000015513745,0,0
func (c *CommonService) GetRefundGoods(RefundSn string, cancelType int, hasRefundSn int) (RefundGoodsOrders []*oc.RefundGoodsOrder) {
	glog.Info("取消订单全渠道退货通知:", RefundSn, c.orderMain.OrderSn, c.orderMain.OldOrderSn, cancelType, hasRefundSn)
	//4100000013448885,4100000013448885,0,1
	var RefundOrder []models.RefundOrder
	if c.IsThirdOrder() {
		//第三方订单退款单查询父单号
		c.session.Where("order_sn = ? ", c.orderMain.ParentOrderSn).Find(&RefundOrder)
	} else {
		c.session.Where("order_sn = ? ", c.orderMain.OrderSn).Find(&RefundOrder)
	}

	//满足条件：客户取消 有退款单以及退款单号
	//一般是客户在非boss后台整单退、取消订单、部分退款直到退完的最后一笔

	if cancelType == 0 && len(RefundOrder) > 0 && hasRefundSn == 1 {
		var OrderProduct []dto.OrderProductAndRet
		if c.IsThirdOrder() {
			newOrderProduct := make([]dto.OrderProductAndRet, 0)
			//组合商品
			var thirdRefundList []models.RefundOrderThirdProduct
			err := c.session.Table("refund_order_third_product").Where("refund_sn=?", RefundSn).Find(&thirdRefundList)
			if err != nil {
				glog.Error(c.orderMain.OldOrderSn, "，取消订单查询退款商品退款明细查询失败3，", err, RefundSn)
				return RefundGoodsOrders
			}
			//如果没有 说明数据出了问题
			if len(thirdRefundList) == 0 {
				glog.Error(c.orderMain.OldOrderSn, "，取消订单查询退款商品退款明细查询失败4，未查询到退款商品明细信息", RefundSn)
				return RefundGoodsOrders
			} else {
				//如果有 直接取里面的数据
				for _, refundGood := range thirdRefundList {
					var copyGoods dto.OrderProductAndRet
					copyGoods.Tkcount = int(refundGood.Quantity)
					//已经退款的数量 等于父商品的退款数量 * 子商品的数量
					copyGoods.RefundAmount = refundGood.RefundAmount
					copyGoods.ProductType = refundGood.ProductType
					copyGoods.SkuId = refundGood.SkuId
					copyGoods.Id = cast.ToString(refundGood.OrderProductId)
					copyGoods.BarCode = refundGood.Barcode
					copyGoods.PrivilegeTotal = int(refundGood.PrivilegeTotal)
					copyGoods.Number = int(refundGood.Number)
					copyGoods.PayPrice = int(refundGood.ProductPrice)
					copyGoods.PaymentTotal = int(refundGood.PaymentTotal)
					copyGoods.OrderProductId = refundGood.OrderProductId
					newOrderProduct = append(newOrderProduct, copyGoods)
				}
			}
			//重置
			OrderProduct = newOrderProduct
		} else {
			//阿闻
			err := c.session.Table("order_product").
				Alias("a").
				Select("a.*,a.id AS order_product_id,IFNULL(b.tkcount, a.number) AS tkcount,IFNULL(b.refund_amount, '')").
				Join("inner", "`order_main` c", "a.order_sn=c.order_sn").
				Join("left", "refund_order_product b", "a.sku_id=b.sku_id AND a.pay_price=b.refund_price AND b.refund_sn=?", RefundSn).
				Where("a.order_sn=? AND a.number>a.refund_num", c.orderMain.OrderSn).
				GroupBy("a.id").Find(&OrderProduct)
			if err != nil {
				glog.Error(c.orderMain.OldOrderSn, "，数据库查询失败，", err)
				return RefundGoodsOrders
			}
			if len(OrderProduct) == 0 {
				glog.Error(c.orderMain.OldOrderSn, "，取消订单查询退款商品为查询到退款商品信息，", err)
				return RefundGoodsOrders
			}
		}

		glog.Info(c.orderMain.OldOrderSn, "，GetRefundGoods："+kit.JsonEncode(OrderProduct))

		//第三方这里查询的只是主单商品  全额退款的时候 需要查询子订单
		for _, i2 := range OrderProduct {
			if i2.ProductType == 1 {
				//每一个的退款数金额 直接用了总的减去-已经退了的
				//实物商品用实际支付金额 -已经退款的
				var RefundAmount float64

				//第三方渠道退款数据已经在上面算出来了
				//阿闻渠道用数量乘以单价计算
				//todo v6.3.3 此处如果RefundNum 不准 当拒绝了退款时  RefundNum数字是错误的 需要解决拒绝退款时的数据更新
				if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelMallId || c.orderMain.ChannelId == ChannelDigitalHealth {
					hasRefundAmount := i2.PayPrice * i2.RefundNum                          //已经退了=已退数量 * 单价 阿闻使用
					RefundAmount = kit.FenToYuan(int64(i2.PaymentTotal - hasRefundAmount)) //总的可退金额-已退款金额=可退款金额
				} else {
					RefundAmount = cast.ToFloat64(i2.RefundAmount)
				}

				goods := oc.RefundGoodsOrder{
					GoodsId:        i2.SkuId,
					Quantity:       int32(i2.Tkcount),
					RefundAmount:   fmt.Sprintf("%.2f", RefundAmount),
					OcId:           i2.Id,
					Barcode:        i2.BarCode,
					Id:             i2.Id,
					RefundPrice:    kit.FenToYuan(i2.PayPrice),
					OrderProductId: i2.OrderProductId,
				}
				if goods.Quantity > 0 {
					RefundGoodsOrders = append(RefundGoodsOrders, &goods)
				}
			}
		}

		//已经退款的总金额  2020/7/23累加计算会有bug,用统计方式
		//阿闻渠道使用最后一个倒减的方式计算最后一个退款金额 第三方依然使用上面算出来的实际退款金额为准
		if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelMallId || c.orderMain.ChannelId == ChannelDigitalHealth {
			var RefundOrders models.RefundOrder
			refundAmount, _ := c.session.Where("order_sn=? AND refund_state=3", c.orderMain.OrderSn).Sum(RefundOrders, "refund_amount")
			sumRefundAmount := int32(kit.YuanToFen(refundAmount))

			var itemRefundAmount int32
			lastIndex := len(RefundGoodsOrders) - 1
			for ind, val := range RefundGoodsOrders {
				if ind == lastIndex { //最后一个
					//阿闻渠道和其他渠道自配订单保持原来的逻辑，其他渠道退款传多少就是多少，（自配送实际运费算成本已经包含在实付金额里面了）
					isSelfDelivery := c.IsSelfDistribution() //是否自配单
					if c.orderMain.DeliveryType != 3 {       //不是自提单，自提单没有运费
						if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelDigitalHealth || isSelfDelivery {
							var Freight int32
							//去掉优惠的邮费
							FreightFloat := c.FreightCal()
							Freight = c.orderMain.Freight - FreightFloat
							RefundGoodsOrders[ind].RefundAmount = fmt.Sprintf("%.2f", float64(c.orderMain.Total-itemRefundAmount-sumRefundAmount-Freight-c.orderMain.PackingCost)/100)
						}
					}
				} else { //非最后一个
					itemRefundAmount += int32(kit.YuanToFen(cast.ToFloat64(val.RefundAmount)))
				}
			}
		}
	} else {
		//没有退款单 前面的流程也没有传退款单号 或者是后台取消
		//场景为没有退款单 一般是后台取消 如果非后台取消都会有退款单

		//没有退款单的取消直接查询所有的子单商品
		orderProduct := c.GetAllOrderProduct()
		if len(orderProduct) == 0 {
			glog.Error(c.orderMain.OldOrderSn, ", 查询订单商品信息异常")
			return
		}

		//第三方子单商品的退款数量需要根据主单商品的退款数量来决定
		//因为在退款时 第三方的退款并没有更新子弹商品的退款数量

		//第三方记录每个sku过往的退款总额
		thirdSkuRefundAmount := make(map[string]int32)
		if c.IsThirdOrder() {
			//查询主商品信息
			parentProduct := GetThirdOrderProduct(c.orderMain.ParentOrderSn)
			parentProductMap := make(map[string]models.OrderProduct)
			for _, v := range parentProduct {
				parentProductMap[v.SkuId] = v
			}

			for i, v := range orderProduct {
				//非组合商品子商品 diffSku 重新计算了已经退款的数量
				if v.ParentSkuId == "" {
					if parent, ok := parentProductMap[v.SkuId]; ok {
						orderProduct[i].RefundNum = parent.RefundNum
					}
				} else {
					//组合商品子商品信息
					if parent, ok := parentProductMap[v.ParentSkuId]; ok {
						orderProduct[i].RefundNum = parent.RefundNum * v.GroupItemNum
					}
				}
			}
			//查询过往的退款总额
			var refundDetails []struct {
				SkuId         string
				ParentSkuId   string
				TotalRefunded float32
			}
			err := c.session.SQL(
				`SELECT sku_id,parent_sku_id,SUM(b.refund_amount) as total_refunded FROM refund_order a JOIN refund_order_third_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 GROUP BY sku_id,parent_sku_id`, c.orderMain.ParentOrderSn).Find(&refundDetails)
			if err != nil {
				glog.Error("查询第三方sku已退款总额明细出错", c.orderMain.ParentOrderSn, err)
				return []*oc.RefundGoodsOrder{}
			}
			if len(refundDetails) > 0 {
				for _, k := range refundDetails {
					thirdSkuRefundAmount[k.SkuId+":"+k.ParentSkuId] = int32(kit.YuanToFen(k.TotalRefunded))
				}
			}
		}

		//此处第三方与阿闻没有做区分
		//阿闻使用支付金额 - 已经退掉的金额时可能存在问题  使用i2.PayPrice*i2.RefundNum得出的已经退掉的金额在阿闻有效
		//但是第三方可能无效 因为第三方以前退的金额并不一定等于单价乘以数量 而是以每次实际的退款金额为主 对于第三方的单子：
		//1：多次部分退款后 通过第三方美团最后全部退款 是不会走到这里来的
		//2: 第三方直接全部退款也是不会走到这里来的
		//3：仅有后台直接取消时会走到这里来 后台直接取消时此时第三方订单可能已经有了一些部分退款
		//结论不管怎么样 第三方的单子本次全部退款时的退款金额 只能以该sku支付总额 - 过往退款的总和 不能使用单价加总计算过往的退款额
		//因为组合商品的退款可能存在再次均摊的可能 要保证 ：sku支付总额 - 过往退款的总和 不应该出现负数
		//但实际上无法完全保证 会存在这种可能 因为当保证一个sku过往退款不能超过实际支付总额时
		//那么退款均摊时为了保证均摊后综合与退款额相等 就得导致其他sku出现同样的问题
		glog.Info(c.orderMain.OldOrderSn, "，GetRefundGoods1："+kit.JsonEncode(orderProduct))

		glog.Info(c.orderMain.OldOrderSn, "，全额退款时统计过往退款总额1："+kit.JsonEncode(thirdSkuRefundAmount))

		for _, i2 := range orderProduct {
			if i2.ProductType == 1 {
				//实物商品用实际支付金额 -已经退款的
				hasRefundAmount := i2.PayPrice * i2.RefundNum //已经退了=已退数量 * 单价 阿闻使用
				//第三方已经退了的只能通过退款记录关联查询
				if c.IsThirdOrder() {
					//上面是
					matchKey := i2.SkuId + ":" + i2.ParentSkuId
					hasRefundAmount = thirdSkuRefundAmount[matchKey] //第三方的已退款总额 = 过往退款综合
				}
				RefundAmount := kit.FenToYuan(i2.PaymentTotal - hasRefundAmount) //总的可退金额-已退款金额=可退款金额
				goods := oc.RefundGoodsOrder{
					GoodsId:        i2.SkuId,
					Quantity:       i2.Number - i2.RefundNum,
					RefundAmount:   fmt.Sprintf("%.2f", RefundAmount),
					OcId:           cast.ToString(i2.Id),
					Barcode:        i2.BarCode,
					Id:             cast.ToString(i2.Id),
					RefundPrice:    kit.FenToYuan(i2.PayPrice),
					OrderProductId: i2.Id,
				}
				if goods.Quantity > 0 {
					RefundGoodsOrders = append(RefundGoodsOrders, &goods)
				}
			}
		}

		//已退款金额总和
		var sumRefundAmount int32
		var refundOrder models.RefundOrder
		//第三方只能使用主订单去查询退款总额
		if c.IsThirdOrder() {
			var floatRefundAmount float32
			//此处只能查询实物的退款单总额
			_, err := c.session.SQL(
				`SELECT IFNULL(SUM(b.refund_amount),0) b FROM refund_order a JOIN refund_order_third_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 AND b.product_type=1`, c.orderMain.ParentOrderSn).Get(&floatRefundAmount)
			sumRefundAmount = int32(kit.YuanToFen(floatRefundAmount))
			//如果查询出错 则可能导致金额出错 所以返回空数据
			if err != nil {
				glog.Error(c.orderMain.OldOrderSn, "查询第三方sku已退款总额出错", c.orderMain.ParentOrderSn, err)
				return []*oc.RefundGoodsOrder{}
			}
		} else {
			refundAmount, _ := c.session.Where("order_sn=? and refund_state=3", c.orderMain.OrderSn).
				Sum(refundOrder, "refund_amount")
			sumRefundAmount = int32(kit.YuanToFen(refundAmount))
		}

		//没有退款单的时候 不管是阿闻还是第三方都通过倒减的方式计算最后一个商品的退款金额
		var itemRefundAmount int32
		for ind, val := range RefundGoodsOrders {
			if ind != len(RefundGoodsOrders)-1 {
				//非最后一个就是 单价于剩余退款数量相乘得到的结果
				itemRefundAmount += int32(kit.YuanToFen(cast.ToFloat64(val.RefundAmount)))
			} else {
				//阿闻渠道和其他渠道自配订单保持原来的逻辑，其他渠道退款传多少就是多少，（自配送实际运费算成本已经包含在实付金额里面了）
				isSelfDelivery := c.IsSelfDistribution() //是否自配单
				if c.orderMain.DeliveryType != 3 {       //不是自提单，自提单没有运费
					if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelDigitalHealth || isSelfDelivery {
						//去掉优惠的运费：实付支付运费
						Freight := c.orderMain.Freight - c.CalTotalChargeFreight(nil, 1)
						//最后一个 = 总金额 - 已经退款的金额 - 最后一个退款单中前面商品的退款总额 - 下单时total上加的运费 包装费
						RefundGoodsOrders[ind].RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.Total-itemRefundAmount-sumRefundAmount-Freight-c.orderMain.PackingCost))
					}
				}
			}
		}
	}

	return RefundGoodsOrders
}

// 保存取消订单退款单
func (c *CommonService) SaveCancelRefundOrder(RefundSn string, CancelReason string, UserName string, isBusiness int,
	oldRefundSn string, pushThird int32, pushThirdFailReason string) error {
	glog.Info("保存取消订单退款单，", c.orderMain.OrderSn, "-", RefundSn, "-", CancelReason, "-", UserName, "-", isBusiness, "-", oldRefundSn)
	var err error
	inpar := models.RefundOrder{
		RefundSn:            RefundSn,
		CreateTime:          time.Now(),
		RefundReason:        CancelReason,
		RefundAmount:        "0",
		RefundState:         3,
		ApplyOpUserType:     "2",
		OrderSn:             c.orderMain.OrderSn,
		ShopId:              c.orderMain.ShopId,
		RefundType:          1,
		OldOrderSn:          c.orderMain.OldOrderSn,
		FullRefund:          1,
		ChannelId:           c.orderMain.ChannelId,
		OldRefundSn:         oldRefundSn,
		OrderSource:         c.orderMain.Source,
		PushThird:           pushThird,
		PushThirdFailReason: pushThirdFailReason,
		RefundedTime:        time.Now(),
	}

	//todo 旧的在线问诊已经不需要，代码测试过后删除
	//if c.orderMain.OrderType == 13 {
	//	inpar.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.Total))
	//
	//	_, err = c.session.Insert(&inpar)
	//	if err != nil {
	//		glog.Error("退款回调插入出错:" + err.Error())
	//		return err
	//	}
	//} else {
	if c.orderMain.ChannelId == ChannelMtId {
		inpar.AppChannel = c.orderMain.AppChannel
	}

	//下单时候的商品集合 重新计算一次
	//todo 以下处理与getRefundGoods中的计算逻辑一致 后期优化
	orderProducts := c.GetOrderProduct()

	if len(orderProducts) == 0 {
		glog.Error(c.orderMain.OrderSn, ", ", c.orderMain.OldOrderSn, ", 保存取消订单退款单时未查询到原订单商品")
		return errors.New("未查询到原订单商品")
	}

	//第三方子单商品的退款数量需要根据主单商品的退款数量来决定
	//因为在退款时 第三方的退款并没有更新子弹商品的退款数量

	//第三方记录每个sku过往的退款总额
	thirdSkuRefundAmount := make(map[string]int32)
	if c.IsThirdOrder() {
		//查询过往的退款总额
		var refundDetails []struct {
			SkuId         string
			TotalRefunded float32
		}
		err = c.session.SQL(
			`SELECT sku_id,SUM(b.refund_amount) as total_refunded FROM refund_order a JOIN refund_order_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3 GROUP BY sku_id`, c.orderMain.OrderSn).Find(&refundDetails)
		if err != nil {
			glog.Error("保存售后单,查询第三方sku已退款总额明细出错", c.orderMain.ParentOrderSn, err)
			return errors.New("保存售后单,查询第三方sku已退款总额明细出错")
		}
		if len(refundDetails) > 0 {
			for _, k := range refundDetails {
				thirdSkuRefundAmount[k.SkuId] = int32(kit.YuanToFen(k.TotalRefunded))
			}
		}
	}

	//退款商品
	retGoods := make([]*models.RefundOrderProduct, 0)

	var proRefundAmount int32

	//第三方记录每个sku过往的退款总额
	thirdSkuPaymentAmount := make(map[string]int32)
	if c.IsThirdOrder() {
		for _, t := range orderProducts {
			thirdSkuPaymentAmount[t.SkuId] += t.PaymentTotal
		}
	}

	//全额退款得自己去计算这次退的商品
	for _, x := range orderProducts {
		//可退数量小于购买数量的才进行退款计算
		if x.Number <= x.RefundNum {
			continue
		}
		//实物商品用实际支付金额 -已经退款的
		var RefundAmount float64
		hasRefundAmount := x.PayPrice * x.RefundNum //已经退了=已退数量 * 单价 阿闻使用
		if c.IsThirdOrder() {
			hasRefundAmount = thirdSkuRefundAmount[x.SkuId] //第三方的已退款总额 = 过往退款综合
		}
		//第三方已经退了的只能通过退款记录关联查询
		RefundAmount = kit.FenToYuan(x.PaymentTotal - hasRefundAmount)
		if c.IsThirdOrder() {
			//前面已经有过退款时 第三方需要处理一个商品多个价格 退款按照均价退的问题
			if paymentTotal, ok := thirdSkuPaymentAmount[x.SkuId]; ok && x.RefundNum > 0 {
				RefundAmount = kit.FenToYuan(paymentTotal - hasRefundAmount)
			}
		}

		refundProduct := &models.RefundOrderProduct{
			RefundSn:       RefundSn, //退款单号
			SkuId:          x.SkuId,
			ParentSkuId:    x.ParentSkuId,
			ProductName:    x.ProductName,
			ProductType:    x.ProductType,
			ProductPrice:   x.DiscountPrice, //单个商品优惠后金额
			Spec:           x.Specs,
			Quantity:       x.Number - x.RefundNum, //退款数量
			Tkcount:        x.Number - x.RefundNum,
			RefundAmount:   fmt.Sprintf("%.2f", RefundAmount), //当前商品退款总金额
			RefundPrice:    x.PayPrice,                        //退款金额
			SubBizOrderId:  x.SubBizOrderId,
			MarkingPrice:   x.MarkingPrice,
			OrderProductId: x.Id,
		}
		//计算退款单这次退款的总价，用订单实际价格减去已经退款的价格
		retGoods = append(retGoods, refundProduct)
		proRefundAmount += x.PayPrice * (x.Number - x.RefundNum)
	}

	//已经退款的总金额  2020/7/23累加计算会有bug,用统计方式
	var RefundOrder models.RefundOrder
	var sumRefundAmount int32
	noworder := ""
	if c.orderMain.ParentOrderSn == "" {
		noworder = c.orderMain.OrderSn
	} else {
		noworder = c.orderMain.ParentOrderSn
	}
	if c.IsThirdOrder() {
		var floatRefundAmount float32
		//此处查出实物与虚拟总和
		_, err := c.session.SQL(
			`SELECT IFNULL(SUM(b.refund_amount),0) FROM refund_order a JOIN refund_order_third_product b ON a.refund_sn = b.refund_sn
						WHERE a.order_sn = ? AND a.refund_state = 3`, c.orderMain.OrderSn).Get(&floatRefundAmount)
		sumRefundAmount = int32(kit.YuanToFen(floatRefundAmount))
		glog.Info("zx测试新688 " + kit.JsonEncode(c.orderMain))

		activityPtAmount, _ := c.session.Where("order_sn=? and refund_state=3", noworder).Sum(RefundOrder, "activity_pt_amount")
		//平台优惠金额
		inpar.ActivityPtAmount = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.PtChargeTotal-int32(kit.YuanToFen(activityPtAmount))))

		//如果查询出错 则可能导致金额出错 所以返回空数据
		if err != nil {
			glog.Error(c.orderMain.OldOrderSn, "保存售后单，查询第三方sku已退款总额出错", c.orderMain.ParentOrderSn, err)
			return errors.New("保存售后单,查询第三方sku已退款总额出错")
		}
	} else {
		refundAmount, _ := c.session.Where("order_sn=? and refund_state=3", c.orderMain.OrderSn).Sum(RefundOrder, "refund_amount")
		sumRefundAmount = int32(kit.YuanToFen(refundAmount))
	}
	deliveryPrice, _ := c.session.Where("order_sn=? and refund_state=3", noworder).Sum(RefundOrder, "delivery_price")
	//本次退款的运费
	inpar.DeliveryPrice = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.Freight-int32(kit.YuanToFen(deliveryPrice))))

	//仅阿闻渠道进行倒减
	if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelMallId || c.orderMain.ChannelId == ChannelDigitalHealth {
		var itemRefundAmount int32
		for ind, val := range retGoods {
			if ind != len(retGoods)-1 {
				itemRefundAmount += val.Quantity * val.RefundPrice
			} else {
				var freight int32
				//去掉优惠的邮费
				FreightFloat := c.FreightCal()
				freight = c.orderMain.Freight - FreightFloat
				retGoods[ind].RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(c.orderMain.Total-sumRefundAmount-itemRefundAmount-freight-c.orderMain.PackingCost))
			}
		}
	}
	//todo 贺林
	inpar.RefundAmount = fmt.Sprintf("%.2f", kit.FenToYuan(int64(c.orderMain.Total-sumRefundAmount)))
	//插入了为0 的数据
	_, err = c.session.Insert(&inpar, &retGoods)

	if err != nil {
		glog.Error("退款回调插入出错:" + err.Error())
		return err
	}

	//第三方售后单拆单入库
	if c.IsThirdOrder() {
		//对退款商品进行处理
		var splitRefundGood []*models.RefundOrderThirdProduct
		glog.Info("保存售后单，拆单前的数据", kit.JsonEncode(retGoods))
		splitRefundGood, err = c.ThirdRefundGoodSplit(retGoods)
		glog.Info("保存售后单，拆单后的数据", kit.JsonEncode(retGoods))
		if err != nil {
			glog.Error(c.orderMain.OrderSn, "保存售后单，第三方售后单拆单出错", err)
			return errors.New("保存售后单，第三方售后单拆单出错")
		}
		_, err = c.session.Insert(splitRefundGood)
		if err != nil {
			glog.Error(c.orderMain.OrderSn, "保存售后单,售后单拆单结果落库出错", err)
			return errors.New("保存售后单，售后单拆单结果落库出错")
		}
	}

	//以下为其他信息更新
	retOrderGoods := make([]models.RefundOrderProduct, 0)
	//查询退款表 数据更新商品的退款数量
	err = c.session.SQL("select * from refund_order_product where refund_sn=?", RefundSn).Find(&retOrderGoods)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, "退款金额数据错误:"+c.orderMain.OldOrderSn+err.Error())
		return err
	}

	//修改订单商品表的退款数量
	for _, x := range retOrderGoods {
		if x.ProductType == 2 || x.ProductType == 3 {
			var verifyCodes []models.OrderVerifyCode
			if x.ProductType == 2 {
				verifyCodes = GetValidOrderVerifyCodes(c.orderMain.OrderSn, 1)
				//第三方组合商品
			} else if x.ProductType == 3 && (c.orderMain.ChannelId == ChannelElmId || c.orderMain.ChannelId == ChannelMtId || c.orderMain.ChannelId == ChannelJddjId) {
				verifyCodes = GetVerifyCodesByGroupSkuId(c.orderMain.OrderSn, x.SkuId, 1)
			}
			if len(verifyCodes) == 0 {
				continue
			}
			var codesId []int64
			//被核销掉的某sku的数量
			SkuNumMap := make(map[string]int)
			for _, verifyCode := range verifyCodes {
				//虚拟商品 退掉数量内的
				//阿闻退款走子订单 所以Quantity直接去子订单的退款数量
				quantity := int(x.Quantity)
				//第三方订单 且是组合的情况下 组合下的每个虚拟订单 都需要退掉组合商品的数量* 该sku在该组合中的数量（x.Quantity * group_item_num）
				if x.ProductType == 3 && c.IsThirdOrder() {
					quantity = int(x.Quantity * verifyCode.GroupItemNum)
				}
				if _, has := SkuNumMap[verifyCode.SkuId]; has {
					SkuNumMap[verifyCode.SkuId] += 1
				} else {
					SkuNumMap[verifyCode.SkuId] = 1
				}
				if SkuNumMap[verifyCode.SkuId] > quantity {
					continue
				}
				codesId = append(codesId, verifyCode.Id)
			}
			_, err = c.session.In("id", codesId).Cols("verify_status").Update(&models.OrderVerifyCode{
				VerifyStatus: 2,
			})
			if err != nil {
				glog.Error("退款更新核销码状态出错:" + c.orderMain.OldOrderSn + err.Error())
				return err
			}
		}
		//等于3是饿了么
		//都是取消订单才来这里的啊
		if c.IsThirdOrder() {
			//更新主单商品
			_, err = c.session.Exec("UPDATE order_product SET refund_num=number WHERE order_sn=?", c.orderMain.OrderSn)
		}
		if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelMallId || c.orderMain.ChannelId == ChannelDigitalHealth {
			_, err = c.session.Exec("UPDATE order_product SET refund_num=refund_num+? WHERE order_sn=? AND sku_id=? AND discount_price=?", x.Quantity, c.orderMain.OrderSn, x.SkuId, x.ProductPrice)
		}

		if err != nil {
			glog.Error(c.orderMain.OldOrderSn, ", 退款修改退款数量出错, ", err)
			return err
		}
	}
	//}

	//退款日志
	refundOrderLog := &models.RefundOrderLog{
		Money:       kit.YuanToFen(cast.ToFloat64(inpar.RefundAmount)),
		RefundSn:    RefundSn,
		Ctime:       time.Now(),
		Reason:      CancelReason,
		NotifyType:  "agree",
		Operationer: UserName,
		OldOrderSn:  c.orderMain.OldOrderSn,
	}

	if isBusiness == 1 {
		refundOrderLog.OperationType = "商家后台取消"
	} else {
		refundOrderLog.OperationType = "取消订单"
	}

	if CancelReason == "没有备注" {
		refundOrderLog.OperationType = "用户取消订单"
	}

	if c.orderMain.ChannelId == ChannelElmId {
		refundOrderLog.OperationType = "取消订单"
	}

	_, err = c.session.Insert(refundOrderLog)
	if err != nil {
		glog.Error("退款回调插入记录出错, ", err, ", ", c.orderMain.OldOrderSn)
		return err
	}

	return nil
}

// 保存前端传过来的 售后订单
func (c *CommonService) SaveRefundOrder(params *oc.AfterApplyOrderRequest) (*oc.BaseResponse, string) {
	createTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.CreateTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	//定义返回信息
	var out = new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"
	var err error

	refundOrder := models.RefundOrder{
		RefundSn:   params.RefundSn,
		OrderSn:    params.OrderSn,
		CreateTime: createTime,
		//Status:         params.Status,
		RefundTypeSn:   params.RefundTypeSn,
		ReasonCode:     params.ReasonCode,
		RefundRemark:   params.RefundRemark,
		RefundReason:   params.RefundReason,
		RefundType:     params.RefundType,
		DiscountAmount: params.DiscountAmount,
		Freight:        params.PostFee,
		RefundAmount:   params.RefundAmount,
		TradeCode:      c.orderMain.OldOrderSn,
		OrderSource:    params.OrderSource,
		ExpressName:    params.ExpressName,
		ExpressNum:     params.ExpressNum,
		ChannelId:      c.orderMain.ChannelId,
	}
	_, err = c.session.Insert(&refundOrder)

	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = err.Error()
		c.session.Rollback()
		return out, ""
	}
	// 保存售后单商品信息
	if len(params.RefundGoodsOrders) > 0 {
		for _, item := range params.RefundGoodsOrders {
			//管易 需要分割 goodsId  例如  A0987654321555@A0987654321
			itemCode := "0"
			skuCode := "0"
			if params.OrderSource == 2 {
				arr := strings.Split(item.GoodsId, "@")
				for i := 0; i < len(arr); i++ {
					if i == 0 {
						itemCode = arr[i]
					}
					if i == 1 {
						skuCode = arr[i]
					}
				}
			}
			orderGoods := models.RefundOrderProduct{
				RefundSn:     refundOrder.RefundSn,
				SkuId:        item.GoodsId,
				Quantity:     item.Quantity,
				RefundAmount: item.RefundAmount,
				Itemcode:     itemCode,
				Skucode:      skuCode,
				OcId:         item.OcId,
				Barcode:      item.Barcode,
			}
			_, err = c.session.Insert(&orderGoods)
			if err != nil {
				out.Code = 400
				out.Error = err.Error()
				out.Message = err.Error()
				c.session.Rollback()
				return out, ""
			}

		}
	}

	// 保存售后单支付信息
	if len(params.RefundPayOrder) > 0 {
		for _, item := range params.RefundPayOrder {
			payTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, item.PayTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
			orderPay := models.RefundOrderPay{
				RefundSn:    refundOrder.RefundSn,
				Paytypecode: item.PayTypeCode,
				Payment:     item.Payment,
				Paytime:     payTime,
			}
			_, err = c.session.Insert(&orderPay)
			if err != nil {
				out.Code = 400
				out.Error = err.Error()
				out.Message = err.Error()
				c.session.Rollback()
				return out, ""
			}
		}
	}

	return out, refundOrder.RefundSn
}

// TODO: 完成子订单同时完成主订单
// 订单完成逻辑
func (c *CommonService) FinalizeOrder(params *oc.AccomplishOrderRequest) error {

	glog.Info("订单完成逻辑：", c.orderMain.OrderSn, kit.JsonEncode(params))
	//第三方调用过来时是主单号 此时c.orderMain.ParentOrderSn是空的
	//所以此处需要判断是否时第三发订单 如果是的话 需要重置c.orderMain
	//v6.0第三方订单拆单之后添加
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
		session:   c.session,
	}
	if c.IsThirdOrder() && c.orderMain.ParentOrderSn == "" {
		mainOrderSn := c.orderMain.OrderSn
		has, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, "完成第三方订单查询实物子单出错，", err)
			return errors.New("更新订单完成查询实物子单出错")
		}
		if !has {
			glog.Error(mainOrderSn, "完成第三方订单未查询到实物子订单，", err)
			return errors.New("更新订单完成未到查询实物子单")
		}
	} else {
		realOrder.orderMain = c.orderMain
	}

	completeOrderSn := []string{realOrder.orderMain.OrderSn}

	// 查询主订单是否还有其他未完成的实物子订单
	existed, err := c.session.SQL(`
		SELECT 1 FROM order_main WHERE parent_order_sn = ? AND is_virtual = 0 AND order_sn != ? 
		AND order_status != 30`,
		realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn).Exist()
	if err != nil {
		glog.Error(params.OrderSn, ",查询主订单是否还有其他未完成的实物子订单,", err)
	}
	if !existed {
		completeOrderSn = append(completeOrderSn, realOrder.orderMain.ParentOrderSn)
	}
	isThree := false
	ConfirmTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.ConfirmTime, time.Local)
	deliveryConfig := new(models.DeliveryConfig)

	//如果是SAAS并且是小程序订单的话，就要判断是不是麦芽田订单，是的话就要调用完成接口
	if realOrder.orderMain.ChannelId == 1 && realOrder.orderMain.OrgId == 6 {
		_, err = c.session.Where("finance_code = ?", c.orderMain.ShopId).
			Where("channel_id = ?", c.orderMain.ChannelId).
			Where("org_id = ?", c.orderMain.OrgId).
			Get(deliveryConfig)

		if err != nil {
			glog.Error("查询配送配置出错", c.orderMain.OrderSn, err.Error())
		}

		if deliveryConfig.ID == 0 {
		}

		//如果是平台配送或者是第三方配送，我们不需要发配送直接返回成功
		if deliveryConfig.ThirdType == 1 && deliveryConfig.DeliveryMethod == 2 {
			if c.orderMain.ChannelId == 1 {
				isThree = true
			}
		}
	}
	if isThree {
		realOrder.session.Begin()
	}

	affected, err := realOrder.session.Cols("order_status,order_status_child,confirm_time").
		Where("order_status!=0").In("order_sn", completeOrderSn).Update(&models.OrderMain{
		OrderStatus:      30,
		OrderStatusChild: 20106,
		ConfirmTime:      ConfirmTime,
	})
	if err != nil {
		if isThree {
			realOrder.session.Rollback()
		}
		glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, ", 更新订单完成出错，", err)
		return errors.New("更新订单完成出错")
	}
	if affected == 0 {
		glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, completeOrderSn, ", 更新订单完成失败", ConfirmTime)
		//return errors.New("更新订单完成失败")
	}
	if isThree {
		etClient := et.GetExternalClient()
		//调用麦芽田订单完成
		par := et.MytOrderConfirmRequest{}
		par.OrderId = realOrder.orderMain.OrderSn
		par.ShopId = deliveryConfig.StoreID
		par.UpdateTime = time.Now().Unix()
		//下单成功后调用确认订单
		res, err := etClient.Myt.ConfirmOrder(etClient.Ctx, &par)
		if err != nil {
			realOrder.session.Rollback()
			glog.Error("麦芽田完成订单错误！ ", c.orderMain.OldOrderSn, err.Error())
			return errors.New("麦芽田确认订单接口错误")
		}
		if res.Code != 200 {
			realOrder.session.Rollback()
			glog.Error("麦芽田完成订单接口错误！ ", c.orderMain.OldOrderSn, res.Message)
			return errors.New("麦芽田确认订单接口错误")
		}
		realOrder.session.Commit()
	}
	// ZlAccomplishOrder realOrder.RePushToZiLongTrigger(dto.RePush_OrderFinished_NetWorkError, 0, nil)
	//异常配送单自动已送达回调处理 第三的订单依然走主单 不能使用realOrder
	_ = c.OrderExceptionAotuArrive()

	go func() {
		MessageUpdate(c.orderMain.ParentOrderSn)

		orderLogs := make([]*models.OrderLog, len(completeOrderSn))
		for k := range completeOrderSn {
			orderLogs[k] = &models.OrderLog{
				OrderSn: completeOrderSn[k],
				LogType: models.OrderLogCompleted,
			}
		}
		SaveOrderLog(orderLogs)
		if (c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelDigitalHealth) && c.orderMain.OrgId != 6 {
			//一小时达冻结1天，电商仓则冻结15天
			effectTime := 1
			if c.orderMain.Source == 5 {
				effectTime = 15
			}
			if len(realOrder.orderMain.ParentOrderSn) > 0 {
				AddHealthVal(effectTime, realOrder.orderMain)
			} else {
				newReal, _ := GetRealOrderByParentOrderSn(realOrder.orderMain.OrderSn)
				AddHealthVal(effectTime, newReal.OrderMain)
			}

		}
	}()

	return nil
}

// 接单后推送第三方
// 发配送
// 更改接单状态
// 阿闻 京东的后台接单（acceptOrder）直接调用
// 美团饿了么接单后 回调（mtAcceptOrder）时如果没有推送第三方则调用
func (c *CommonService) AcceptPushThird(acceptUsername string) {
	glog.Info(c.orderMain.OldOrderSn, ",AcceptPushThird推送第三方")
	orderMainUp := &models.OrderMain{
		OrderStatusChild: 20102,
	}

	// 快递配送订单流转 20101未接单=>20201待发货=>20202全部发货（待收货）=>20106已完成
	if c.orderMain.DeliveryType == 1 {
		orderMainUp.OrderStatusChild = 20201
	}

	orderDetailUp := &models.OrderDetail{
		AcceptTime:     time.Now(),
		AcceptUsername: acceptUsername,
	}

	err := c.PushThirdOrder(false)
	if err != nil {
		glog.Error(c.orderMain.OldOrderSn, ", 推送第三方失败, ", err)
		orderDetailUp.PushThirdOrderReason = err.Error()
	} else {
		if c.orderMain.OrgId != 6 {
			go DeleteTransportationInventory(c.orderMain.ParentOrderSn, c.orderMain.ChannelId, true)
		} else {
			res := Freeze(c.orderMain.ParentOrderSn, c.orderMain.ShopId, 2)
			if res.Code != 200 {
				glog.Info(c.orderMain.OldOrderSn, ", SAAS扣减库存失败：", res.Msg)
			} else {
				glog.Info(c.orderMain.ParentOrderSn, ", SAAS扣减库存成功：")
			}
		}

		orderDetailUp.PushThirdOrder = 1

		//自提单不推配送
		if c.orderMain.DeliveryType != 3 && //不是自提单
			c.orderMain.OrderType != 2 && //不是预订单
			(c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelDigitalHealth || c.orderMain.ChannelId == ChannelMtId ||
				(c.orderMain.ChannelId == ChannelElmId && c.orderMain.LogisticsCode == "6") ||
				(c.orderMain.ChannelId == ChannelJddjId && c.orderMain.LogisticsCode == "2938")) {
			//不等于美团专送的才
			if !strings.Contains("2002,1001,1004,2010,3001,1007", c.orderMain.LogisticsCode) {
				//美团配送
				_ = c.PushMpOrder()
			}
		}
	}

	c.session.Begin()

	_, err = c.session.In("order_sn", []string{c.orderMain.ParentOrderSn, c.orderMain.OrderSn}).Update(orderMainUp)
	if err != nil {
		c.session.Rollback()
		glog.Error(c.orderMain.ParentOrderSn, c.orderMain.OrderSn, ", 更新订单主表失败，", err)
		return
	}
	_, err = c.session.In("order_sn", []string{c.orderMain.ParentOrderSn, c.orderMain.OrderSn}).
		Cols("accept_time,accept_username,push_third_order,push_third_order_reason").Update(orderDetailUp)
	if err != nil {
		c.session.Rollback()
		glog.Error(c.orderMain.ParentOrderSn, c.orderMain.OrderSn, ", 更新订单详情表失败，", err)
		return
	} else {
		glog.Info(c.orderMain.ParentOrderSn, c.orderMain.OrderSn, ", 更新订单详情表成功，", orderDetailUp)
	}

	c.session.Commit()
}

// 保存本地生活订单
func (c *CommonService) SaveMtOrder(params *oc.MtAddOrderRequest, WarehouseInfo *models.Warehouse, GrpcContext *models.GrpcContext, appChannel int32) (err error) {

	if params.GroupId > 0 { // 社团团购新加的订单类型
		params.OrderType = 15
	}
	LogisticsName := ""
	deliveryConfig := new(models.DeliveryConfig)
	glog.Info("保存订单信息1", kit.JsonEncode(params))
	if params.OrgId == 6 && params.OrderType != 3 {

		_, err := c.session.Where("finance_code = ?", params.ShopId).
			Where("channel_id = ?", int32(GrpcContext.Channel.ChannelId)).
			Where("org_id = ?", params.OrgId).
			Get(deliveryConfig)

		if err != nil {
			glog.Error("查询配送配置出错", params.OrderSn, err.Error())
		}
		glog.Info("保存订单信息1", kit.JsonEncode(deliveryConfig))
		if deliveryConfig.ID > 0 {
			switch deliveryConfig.DeliveryMethod {
			case 1:
				LogisticsName = "外卖平台配送"
			case 2:
				LogisticsName = "三方配送"
			case 3:
				LogisticsName = "聚合配送"
			case 4:
				LogisticsName = "自行配送"
			}

		}
	}
	c.orderMain = &models.OrderMain{
		OrderStatus:      params.OrderStatus,
		OrderStatusChild: params.OrderStatusChild,
		ShopId:           params.ShopId,
		ShopName:         params.ShopName,
		MemberId:         params.MemberId,
		MemberName:       params.MemberName,
		MemberTel:        params.MemberTel,
		ReceiverName:     params.ReceiverName,
		ReceiverState:    params.ReceiverState,
		ReceiverCity:     params.ReceiverCity,
		ReceiverDistrict: params.ReceiverDistrict,
		ReceiverAddress:  params.ReceiverAddress,
		ReceiverPhone:    params.ReceiverPhone,
		CombinePrivilege: params.CombinePrivilege,
		Privilege:        params.Privilege,
		ReceiverMobile:   params.ReceiverMobile,
		Total:            params.Total,
		PayTotal:         params.Total,
		GoodsTotal:       params.GoodsTotal,
		IsPay:            params.IsPay,
		OrderType:        params.OrderType,
		Freight:          params.Freight,
		DeliveryType:     params.DeliveryType,
		PackingCost:      params.PackingCost,
		TotalWeight:      params.TotalWeight,
		LogisticsCode:    params.LogisticsCode,
		ServiceCharge:    params.ServiceCharge,
		ContractFee:      params.ContractFee, //履约费 当前只有美团平台订单有 v6.5.0添加
		ChannelId:        int32(GrpcContext.Channel.ChannelId),
		UserAgent:        int32(GrpcContext.Channel.UserAgent),
		IsPushTencent:    params.IsPushTencent,
		OrderPayType:     params.OrderPayType,
		Lng:              params.Lng,
		Lat:              params.Lat,
		OrgId:            params.OrgId,
		LogisticsName:    LogisticsName,
	}
	c.orderMain.AppChannel = appChannel
	if len(params.PayInfo) > 0 {
		c.orderMain.PaySn = params.PayInfo[0].PaySn
		c.orderMain.PayMode = params.PayInfo[0].PayMode
		c.orderMain.PayAmount = params.PayInfo[0].PayAmount
		payTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.PayInfo[0].PayTime, time.Local)
		c.orderMain.PayTime = payTime
	}

	//生成订单号
	orderSns := GetSn("order")
	c.orderMain.OrderSn = orderSns[0]

	glog.Info(c.orderMain.OrderSn, "，SaveMtOrder-1:", kit.JsonEncode(params))

	//自提单取货地址填门店地址
	if c.orderMain.DeliveryType == 3 {
		client := dac.GetDataCenterClient()
		result, err := client.RPC.QueryStoreInfo(context.Background(), &dac.StoreInfoRequest{
			FinanceCode: []string{c.orderMain.ShopId},
		})
		if err != nil {
			glog.Error(c.orderMain.OldOrderSn, ", 获取店铺信息失败, ", err)
		} else if result.Code != 200 {
			glog.Warning(c.orderMain.OldOrderSn, ", 获取店铺信息失败, ", result.Message)
		} else if len(result.Details) > 0 {
			shopInfo := result.Details[0]
			c.orderMain.ReceiverName = shopInfo.ShopName
			c.orderMain.ReceiverState = shopInfo.Province
			c.orderMain.ReceiverCity = shopInfo.City
			c.orderMain.ReceiverDistrict = ""
			c.orderMain.ReceiverAddress = shopInfo.Address
		}
	}

	//由于自提订单没有收货地址，则默认使用深圳总部地址。后续根据实际情况修改
	if len(c.orderMain.ReceiverName) == 0 {
		c.orderMain.ReceiverName = "巨星"
	}
	if len(c.orderMain.ReceiverState) == 0 {
		c.orderMain.ReceiverState = "广东省"
	}
	if len(c.orderMain.ReceiverCity) == 0 {
		c.orderMain.ReceiverCity = "深圳市"
	}
	if len(c.orderMain.ReceiverDistrict) == 0 {
		c.orderMain.ReceiverDistrict = "福田区"
	}
	if len(c.orderMain.ReceiverAddress) == 0 {
		c.orderMain.ReceiverAddress = "广东省深圳市福田区京基时代广场A栋56楼"
	}
	c.orderMain.OldOrderSn = c.orderMain.OrderSn

	c.orderMain.WarehouseCode = WarehouseInfo.Code
	c.orderMain.WarehouseName = WarehouseInfo.Name
	c.orderMain.WarehouseId = WarehouseInfo.Id

	switch WarehouseInfo.Category {
	case 4, 5: //前置仓
		c.orderMain.Source = 4 //rp oms
	case 3: //门店仓
		c.orderMain.Source = 3 //子龙
	case 1: //中心仓
		c.orderMain.Source = 5 // 中心仓
	}

	c.orderDetail = &models.OrderDetail{
		OrderSn:        c.orderMain.OrderSn,
		PayType:        params.PayType,
		Invoice:        params.Invoice,
		BuyerMemo:      params.BuyerMemo,
		SellerMemo:     params.SellerMemo,
		DeliveryRemark: params.DeliveryRemark,
		Extras:         params.Extras,
		Latitude:       params.Latitude,
		Longitude:      params.Longitude,
		PickupCode:     params.PickupCode,
		IsAdjust:       params.IsAdjust,
		ConsultOrderSn: params.ConsultOrderSn,
	}

	billCompletedTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.BillCompletedTime, time.Local)
	c.orderDetail.BillCompletedTime = billCompletedTime
	billCanceledTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.BillCanceledTime, time.Local)
	c.orderDetail.BillCanceledTime = billCanceledTime
	tradeCreatedTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.TradeCreatedTime, time.Local)
	c.orderDetail.TradeCreatedTime = tradeCreatedTime
	tradePaymentTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.TradePaymentTime, time.Local)
	c.orderDetail.TradePaymentTime = tradePaymentTime
	tradeTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, params.TradeTime, time.Local)
	c.orderDetail.TradeTime = tradeTime

	if params.DeliveryType != 3 {
		c.orderDetail.PickupStationId = params.PickupStationId
	}

	/*
		佣金和业绩归属说明：
		1，用户A从团长B分享的小程序卡片或团海报参团，或直接进店参加团长B的团，订单佣金归团长B，业绩归团长B和B的门店。
		2，分销员A生成店铺海报A，推给用户B，B扫码进店参团，订单业绩和佣金归团长。
		3，分销员A生成店铺海报A，推给分销员B，B扫码进店开团，该团所有订单业绩都归A，佣金归B。

		需求总结：分销员才会有业绩，业绩默认归团长，如果开的团是通过分销员海报进来的，则归海报分销员
		注：记录业绩归属，海报推广分销员id查询，因为小程序二维码场景值最多32位，故params.PickupDisMemberId是电商upet_member的member_id，需要转换为scrm_user_id
	*/
	if params.GroupId > 0 {
		// 查询团的信息
		var groupActivity models.OrderGroupActivity
		if _, err := c.session.Select("id,member_id,shop_dis_member_id,shop_dis_chain_id,is_dis").ID(params.GroupId).Get(&groupActivity); err != nil {
			glog.Error("SaveMtOrder下单接口保存订单查询开团表信息错误：", err, params)
		} else if groupActivity.Id > 0 {
			// 业绩归属说明：如果团绑定了海报分销员(shop_dis_member_id),业绩归海报分销员，否则，归"分销团长"
			if groupActivity.ShopDisMemberId != "" {
				c.orderDetail.ShopDisMemberId = groupActivity.ShopDisMemberId
				c.orderDetail.ShopDisChainId = groupActivity.ShopDisChainId
			} else if groupActivity.IsDis == 1 {
				c.orderDetail.ShopDisMemberId = groupActivity.MemberId
				upetDb := GetUPetDBConn()
				var disMember models.UpetMember
				if _, err := upetDb.Select("scrm_user_id,distri_chainid").Where("scrm_user_id = ?", groupActivity.MemberId).Get(&disMember); err != nil {
					glog.Error("SaveMtOrder下单接口保存订单查询团长分销员信息错误：", err, params)
				} else {
					c.orderDetail.ShopDisChainId = disMember.DistriChainid
				}
			}
		}
	} else {
		//业绩归属，目前仅只记录电商内部分销员信息
		shopDisMemberMobile := ""  //分销员的手机号
		shopDisScrmUserId := ""    //分销员的member_id
		shopDisChainId := int32(0) //内部分销员绑定的门店id
		castDisId := false         //是否要处理电商分销

		if params.ShopDisMemberFrom == 1 {
			if params.ShopDisMemberId != "" { //有分销员时 是upet_member的member_id
				if disMemberInfo, err := GetUPetMemberInfo(cast.ToInt32(params.ShopDisMemberId), "member_id,scrm_user_id,member_mobile,distri_chainid,distri_state"); err != nil {
					glog.Errorf("SaveMtOrder下单%s查询电商会员%s分销信息错误：%s", c.orderMain.OrderSn, params.ShopDisMemberId, err.Error())
				} else if disMemberInfo.MemberId > 0 && disMemberInfo.DistriState == 2 {
					shopDisMemberMobile = disMemberInfo.MemberMobile
					shopDisScrmUserId = disMemberInfo.ScrmUserId
					shopDisChainId = disMemberInfo.DistriChainid
					//如果下单的人不是分销员，那么建立粉丝关系
					go UpdateUpetDisMemberRelation(params.MemberId, disMemberInfo.MemberId)
				}
			} else if params.MemberId != "" { //没有分销员时，找当前会员关联的上个会员绑定的分销员信息，第三方下单渠道可能没有这个值
				if memberInfo, err := GetUPetMemberInfoByScrmUserId(params.MemberId, "member_id"); err != nil {
					glog.Errorf("SaveMtOrder下单%s查询电商会员%s上级分销信息错误：%s", c.orderMain.OrderSn, params.MemberId, err.Error())
				} else if memberInfo.MemberId > 0 {
					prevDisMemberInfo, _ := GetDisMemberInfo(memberInfo.MemberId)
					if prevDisMemberInfo != nil && prevDisMemberInfo.DisType > 0 {
						shopDisMemberMobile = prevDisMemberInfo.MemberMobile
						shopDisScrmUserId = prevDisMemberInfo.ScrmUserId
						shopDisChainId = prevDisMemberInfo.ChainId
					}
				}
			} else if params.DisId > 0 {
				castDisId = true
			}
		} else if params.DisId > 0 {
			castDisId = true
		}

		if castDisId { //电商分销
			if disMemberInfo, err := GetUPetMemberInfoByDisId(params.DisId, "member_id,scrm_user_id,member_mobile,distri_chainid,distri_state"); err != nil {
				glog.Errorf("SaveMtOrder下单%s查询电商记录%d分销信息错误：%s", c.orderMain.OrderSn, params.DisId, err.Error())
			} else if disMemberInfo.MemberId > 0 && disMemberInfo.DistriState == 2 {
				shopDisMemberMobile = disMemberInfo.MemberMobile
				shopDisScrmUserId = disMemberInfo.ScrmUserId
				shopDisChainId = disMemberInfo.DistriChainid
				//如果下单的人不是分销员，那么建立粉丝关系
				go UpdateUpetDisMemberRelation(params.MemberId, disMemberInfo.MemberId)
			}
		}

		if shopDisScrmUserId != "" && shopDisMemberMobile != "" { //如果有分销信息，找分销员的的员工信息
			zlSession := GetZlHospitalConn().NewSession()
			defer zlSession.Close()
			staffInfo := &models.ZlOrganUserInfo{}
			if err := staffInfo.GetByMobile(zlSession, shopDisMemberMobile); err != nil {
				glog.Errorf("SaveMtOrder下单%s查询分销员工%s信息错误：%s", c.orderMain.OrderSn, shopDisMemberMobile, err.Error())
			} else if staffInfo.UserId > 0 { //记录业绩
				c.orderDetail.PerformanceStaffName = staffInfo.UserRealName
				c.orderDetail.PerformanceOperatorName = "系统分配"
				c.orderDetail.PerformanceOperatorTime = time.Now()
				orderPerformance := &models.OrderPerformance{
					OrderSn:             c.orderMain.OrderSn,
					StaffId:             cast.ToString(staffInfo.UserId),
					StaffName:           c.orderDetail.PerformanceStaffName,
					PerformanceStatus:   0, //需要支付成功后才算
					CreateTime:          c.orderDetail.PerformanceOperatorTime,
					UpdateTime:          c.orderDetail.PerformanceOperatorTime,
					OperatorName:        c.orderDetail.PerformanceOperatorName,
					PerformanceMemberId: shopDisScrmUserId,
					PerformanceChainId:  shopDisChainId,
				}
				if _, err := c.session.Insert(orderPerformance); err != nil { //如果出错了，不记录业绩信息
					c.orderDetail.PerformanceStaffName = ""
					c.orderDetail.PerformanceOperatorName = ""
					var clearTime time.Time
					c.orderDetail.PerformanceOperatorTime = clearTime

					glog.Errorf("SaveMtOrder下单记录业绩信息错误:%s %s", err.Error(), kit.JsonEncode(orderPerformance))
				}
			}
		}
	}

	//第三方渠道有各自的订单编号

	c.orderMain.OldOrderSn = c.orderMain.OrderSn
	//第三方渠道有各自的订单编号
	switch GrpcContext.Channel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		c.orderMain.OldOrderSn = params.OrderSn //第三方订单
		//c.orderMain.ParentOrderSn = c.orderMain.OrderSn //父订单 与 平台订单号默认相同 V6.0去掉了
		c.orderDetail.SplitOrderResult = 0
		//v6.0之前默认时1 也就是第三方订单没有拆单 6.0之后第三方订单需要进行拆单 改成0 拆单成功之后改为1
		//因为第三方订单中的组合商品是没有子商品明细的 所以需要获取处理组合商品中子商品明细 方便后续退款等功能的查询
		var newOrderProductModel []*oc.OrderProductModel

		for _, v := range params.OrderProductModel {
			newOrderProductModel = append(newOrderProductModel, v)
			if v.ProductType == 3 {
				children := QueryChildProducts(v, GrpcContext.Channel.ChannelId, WarehouseInfo.Category, params.ShopId)
				//将子商品放进去
				if len(children) > 0 {
					newOrderProductModel = append(newOrderProductModel, children...)
				} else {
					glog.Info(c.orderMain.OldOrderSn, v.Sku, "未查询到子商品信息")
				}
			}
		}
		//TODO 是否要重置商品的skuPayTotal 需要看退款 推送等流程是否要检测该字段
		//重置商品参数
		params.OrderProductModel = newOrderProductModel
	case ChannelAwenId, ChannelDigitalHealth:
		c.orderDetail.PickupCode = getPickCode(c.orderMain) //获取门店自提码
	}

	if params.OrderChannelSrcType != nil {
		c.orderDetail.ChildChannelId = params.OrderChannelSrcType.SrcType
		c.orderDetail.ChildChannelName = params.OrderChannelSrcType.SrcTypeName
	}

	if params.ExpectedTime != "" {
		c.orderDetail.ExpectedTime, _ = time.ParseInLocation(kit.DATETIME_LAYOUT, params.ExpectedTime, time.Local)
	}

	orderProductModel := make([]*models.OrderProduct, len(params.OrderProductModel))

	//非组合商品子商品sku与切片索引的关系
	skuIndexMap := make(map[string]int)
	//组合父商品sku与子商品集合关系
	skuChildMap := make(map[string][]*models.OrderProduct)

	// 记录每个组合商品种的最大金额子商品
	maxSubProduct := make(map[string]*models.OrderProduct)

	//拆单前的商品
	for k, i2 := range params.OrderProductModel {
		Product := &models.OrderProduct{
			OrderSn:              c.orderMain.OrderSn, //主单订单号
			SkuId:                i2.Sku,
			ProductId:            i2.ProductId,
			ProductType:          i2.ProductType,
			CombineType:          i2.CombineType,
			ChannelCategoryName:  i2.ChannelCategoryName,
			ParentSkuId:          i2.ParentSkuId,
			ProductName:          i2.ProductName,
			BarCode:              i2.BarCode,
			DiscountPrice:        i2.Price,    //该字段 非组合商品实际取值为原价 即市场价 组合商品时是算了优惠后的价格
			PayPrice:             i2.PayPrice, //支付单价
			Number:               i2.Number,
			Specs:                i2.Specs,
			PaymentTotal:         i2.PaymentTotal, //实际支付的金额
			SkuPayTotal:          i2.SkuPayTotal,  //该sku支付的总额
			Privilege:            i2.Privilege,
			PrivilegePt:          i2.PrivilegePt,
			PrivilegeTotal:       i2.PrivilegeTotal,
			MarkingPrice:         i2.MarkingPrice, //市场价
			VipPrice:             i2.VipPrice,
			DeliverNum:           i2.DeliverNum,
			RefundNum:            i2.RefundNum,
			GroupItemNum:         i2.GroupItemNum,
			SubBizOrderId:        i2.SubBizOrderId,
			PromotionId:          int64(i2.PromotionId),
			PromotionType:        i2.PromotionType,
			ThirdSkuId:           i2.ArticleNumber,
			MallOrderProductId:   i2.MallOrderProductId,
			TermType:             i2.TermType,
			TermValue:            i2.TermValue,
			VirtualInvalidRefund: i2.VirtualInvalidRefund,
			IsPrescribedDrug:     i2.IsPrescribedDrug,
			CreateTime:           time.Now(),
			UpdateTime:           time.Now(),
			LocationCode:         i2.LocationCode,
		}

		imgs := strings.Split(i2.Image, ",")
		if len(imgs) > 0 {
			Product.Image = imgs[0]
		}

		//diffSku(标志请勿删除)
		//非子商品
		if i2.ParentSkuId == "" {
			skuIndexMap[i2.Sku] = k
		}
		//存在父sku 说明是子商品
		//只说明此处没有查出父商品
		if i2.ParentSkuId != "" {
			skuChildMap[i2.ParentSkuId] = append(skuChildMap[i2.ParentSkuId], Product)
		}
		orderProductModel[k] = Product

		// 商品实付金额
		if Product.ProductType != 3 {
			c.orderMain.GoodsPayTotal += Product.PaymentTotal
			// 记录每个组合商品种的最大金额子商品
			if msp, has := maxSubProduct[Product.ParentSkuId]; has {
				if Product.PaymentTotal > msp.PaymentTotal {
					maxSubProduct[Product.ParentSkuId] = Product
				}
			} else {
				maxSubProduct[Product.ParentSkuId] = Product
			}
		}
	}

	// 组合商品价格0值处理
	for _, product := range orderProductModel {
		// 非组合子商品或者单价不等于0
		if product.ProductType == 3 || len(product.ParentSkuId) == 0 || product.PayPrice != 0 {
			continue
		}
		maxProduct := maxSubProduct[product.ParentSkuId]

		// 强制变为1分钱
		product.PayPrice = 1
		product.DiscountPrice = 1
		add := product.Number*product.PayPrice - product.PaymentTotal

		product.PaymentTotal = product.Number * product.PayPrice
		// 新的商品优惠要减掉增加的金额
		product.Privilege -= add

		maxProduct.PaymentTotal -= add
		maxProduct.PayPrice = cast.ToInt32(cast.ToFloat64(maxProduct.PaymentTotal) / cast.ToFloat64(maxProduct.Number))
		maxProduct.DiscountPrice = maxProduct.PayPrice
		maxProduct.Privilege += add

		// 考虑多个sku相同情况，计算sku总价
		for _, op := range orderProductModel {
			if op.SkuId == product.SkuId {
				op.SkuPayTotal += add
			} else if op.SkuId == maxProduct.SkuId {
				maxProduct.SkuPayTotal -= add
			}
		}
	}

	//TODO: 将组合商品的子商品用json存储，后面版本评估后将json外的子商品清除
	//存在组合商品
	if len(skuChildMap) > 0 {
		for parentSku, childProducts := range skuChildMap {
			if parentIndex, ok := skuIndexMap[parentSku]; ok {
				orderProductModel[parentIndex].ChildrenSku = kit.JsonEncode(childProducts)
			}
		}
	}
	//优惠信息
	orderPromotions := make([]models.OrderPromotion, len(params.OrderPromotion))
	for k, i2 := range params.OrderPromotion {
		orderPromotions[k] = models.OrderPromotion{
			OrderSn:        c.orderMain.OrderSn,
			PromotionId:    int64(i2.PromotionId),
			PromotionType:  i2.PromotionType,
			PromotionTitle: i2.PromotionTitle,
			PoiCharge:      i2.PoiCharge,
			PtCharge:       i2.PtCharge,
			PromotionFee:   i2.PromotionFee,
		}
		// 平台补贴
		c.orderMain.PtChargeTotal += i2.PtCharge
		// 商家补贴
		c.orderMain.PoiChargeTotal += i2.PoiCharge
	}

	//统计运费优惠
	c.orderMain.FreightPrivilege = c.CalTotalChargeFreight(orderPromotions, 1)
	// 平台配送费优惠
	c.orderMain.PtFreightPrivilege = c.CalTotalChargeFreight(orderPromotions, 2)
	// 商家预计收入
	c.orderMain.ActualReceiveTotal = c.orderMain.GetActualReceiveTotal()

	if c.orderMain.OrgId == 0 {
		c.orderMain.OrgId = 1
	}

	switch GrpcContext.Channel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		//第三方订单循环插入商品以得到商品的记录id用于后面的拆单
		//插入数据库
		for _, product := range orderProductModel {
			_, err = c.session.Insert(product)
			if err != nil {
				glog.Error("订单插入商品数据库失败, ", err, ", ", kit.JsonEncode(c.orderMain))
			} else {
				//赋值商品信息
				c.orderProducts = append(c.orderProducts, product)
			}
		}
		_, err = c.session.Insert(c.orderMain, c.orderDetail, &orderPromotions)
		if err != nil {
			glog.Error("订单插入数据库失败, ", err, ", ", kit.JsonEncode(c.orderMain))
		}
	case ChannelAwenId, ChannelDigitalHealth:
		c.orderMain.EnMemberTel = utils.MobileEncrypt(c.orderMain.MemberTel)
		c.orderMain.MemberTel = MobileReplaceWithStar(c.orderMain.MemberTel)

		if len(c.orderMain.ReceiverMobile) > 0 {
			c.orderMain.EnReceiverMobile = utils.MobileEncrypt(c.orderMain.ReceiverMobile)
			c.orderMain.ReceiverMobile = MobileReplaceWithStar(c.orderMain.ReceiverMobile)
		}

		if len(c.orderMain.ReceiverPhone) > 0 {
			c.orderMain.EnReceiverPhone = utils.MobileEncrypt(c.orderMain.ReceiverPhone)
			c.orderMain.ReceiverPhone = MobileReplaceWithStar(c.orderMain.ReceiverPhone)
		}

		_, err = c.session.Insert(c.orderMain, c.orderDetail, &orderProductModel, &orderPromotions)
		if err != nil {
			glog.Error("订单插入数据库失败, ", err, ", ", kit.JsonEncode(c.orderMain))
		}
	}

	// 社区团购订单扩展表入库
	if params.GroupId > 0 {
		params.OrderSn = c.orderMain.OrderSn
		if err = c.SaveOrderMainGroup(params); err != nil {
			glog.Error(params.OrderSn, ", 保存社区团购订单失败, ", err.Error())
			return errors.New("保存社区团购订单失败，" + err.Error())
		}
	}

	return nil
}

// 保存团订单表order_main_group
func (c *CommonService) SaveOrderMainGroup(params *oc.MtAddOrderRequest) (err error) {
	var member models.UpetMember
	db := GetUPetDBConn()
	if _, err = db.Select("member_avatar").Where("scrm_user_id = ?", params.MemberId).Get(&member); err != nil {
		glog.Error("保存团订单SaveOrderMainGroup查询会员信息失败：", params, err.Error())
		return err
	}

	mainGroup := models.OrderMainGroup{
		OrderSn:              params.OrderSn, // 拆单后会更新为子单号
		ParentOrderSn:        params.OrderSn,
		MemberId:             params.MemberId,
		OrderGroupActivityId: cast.ToInt32(params.GroupId),
		MemberName:           params.NickName,
		MemberProfile:        GetUserAvatar(params.AvatarUrl),
		ReceiverName:         params.GroupName,
		ReceiverMobile:       MobileReplaceWithStar(params.GroupMobile),
		EnReceiverMobile:     utils.MobileEncrypt(params.GroupMobile),
		ReceiverAddress:      params.GroupAddress,
		CreatedAt:            time.Now(),
		UpdateAt:             time.Now(),
	}
	if _, err = c.session.Insert(mainGroup); err != nil {
		glog.Error("保存团订单SaveOrderMainGroup入库失败：", params, err.Error())
	}
	return
}

// 保存电商订单
func (c *CommonService) SaveMallOrder(params *oc.MtAddOrderRequest, warehouseId int32, GrpcContext *models.GrpcContext) (err error) {
	c.orderMain = &models.OrderMain{
		OrderStatus:      params.OrderStatus,
		OrderStatusChild: params.OrderStatusChild,
		ShopId:           params.ShopId,
		ShopName:         params.ShopName,
		MemberId:         params.MemberId,
		MemberName:       params.MemberName,
		EnMemberTel:      utils.MobileEncrypt(params.MemberTel),
		MemberTel:        MobileReplaceWithStar(params.MemberTel),
		ReceiverName:     params.ReceiverName,
		ReceiverState:    params.ReceiverState,
		ReceiverCity:     params.ReceiverCity,
		ReceiverDistrict: params.ReceiverDistrict,
		ReceiverAddress:  params.ReceiverAddress,
		EnReceiverPhone:  utils.MobileEncrypt(params.ReceiverPhone),
		ReceiverPhone:    MobileReplaceWithStar(params.ReceiverPhone),
		Privilege:        params.Privilege,
		EnReceiverMobile: utils.MobileEncrypt(params.ReceiverMobile),
		ReceiverMobile:   MobileReplaceWithStar(params.ReceiverMobile),
		Total:            params.Total,
		PayTotal:         params.Total,
		GoodsTotal:       params.GoodsTotal,
		IsPay:            params.IsPay,
		OrderType:        params.OrderType,
		Freight:          params.Freight,
		DeliveryType:     params.DeliveryType,
		PackingCost:      params.PackingCost,
		TotalWeight:      params.TotalWeight,
		LogisticsCode:    params.LogisticsCode,
		ServiceCharge:    params.ServiceCharge,
		ChannelId:        int32(GrpcContext.Channel.ChannelId),
		UserAgent:        int32(GrpcContext.Channel.UserAgent),
		Source:           1, //oms
		IsVirtual:        params.IsVirtual,
		// WarehouseCode:    "JD01", //todo 电商仓的默认仓库
		IsPushTencent: params.IsPushTencent,
		OrderPayType:  params.OrderPayType,
		AppChannel:    1, //商城的appChannel写死为1  代运营只加入本地生活流程，商场不会有代运营的商品
		OrgId:         params.OrgId,
	}
	//如果没有传参数的话，默认就是1 瑞鹏
	if c.orderMain.OrgId == 0 {
		c.orderMain.OrgId = 1
	}
	//判断是电商渠道且主体为3，则ShopCode读取配置文件
	// if c.orderMain.ChannelId == ChannelMallId && c.orderMain.ShopId == CSYMainId {
	// 	c.orderMain.WarehouseCode = config.GetString("ShopCode")
	// }
	c.orderMain.WarehouseCode = GetShopCodeByOrder(c.orderMain)
	//拼团订单用已有订单号
	if c.orderMain.OrderType == 4 {
		c.orderMain.OrderSn = params.OrderSn
	} else {
		//已存在订单号
		orderSns := GetSn("order")
		c.orderMain.OrderSn = orderSns[0]
	}

	c.orderMain.OldOrderSn = params.OrderSn

	c.orderDetail = &models.OrderDetail{
		OrderSn:        c.orderMain.OrderSn,
		PayType:        params.PayType,
		Invoice:        params.Invoice,
		BuyerMemo:      params.BuyerMemo,
		SellerMemo:     params.SellerMemo,
		DeliveryRemark: params.DeliveryRemark,
		Extras:         params.Extras,
		Latitude:       params.Latitude,
		Longitude:      params.Longitude,
		PickupCode:     params.PickupCode,
		IsAdjust:       params.IsAdjust,
		PowerId:        params.PowerId,
	}
	//c.GetOrderProduct()
	skuThirdMap := map[string]string{}
	skuMap := map[string]string{}

	//通过skuid查询商品货号
	if err = func() error {
		var skuIds []int32
		for _, v := range params.OrderProductModel {
			//虚拟商品不需要货号
			if v.ProductType != 2 {
				skuIds = append(skuIds, cast.ToInt32(v.Sku))
			}
		}

		if len(skuIds) == 0 {
			return nil
		}

		//电商积分兑换第三方商品不处理查询货号等逻辑
		if params.OrderProductModel[0].IsThirdProduct == 1 {
			return nil
		}
		//todo 商品无需修改
		pcClient := pc.GetDcProductClient()
		defer pcClient.Close()

		res, err := pcClient.RPC.QuerySkuThird(pcClient.Ctx, &pc.OneofIdRequest{
			Id: &pc.OneofIdRequest_SkuId{
				SkuId: &pc.ArrayIntValue{
					Value: skuIds,
				},
			},
		})
		if err != nil {
			return errors.New("查询商品信息失败, " + err.Error() + ", " + kit.JsonEncode(skuIds))
		} else if len(res.Details) == 0 {
			return errors.New("未查到商品信息, " + kit.JsonEncode(skuIds))
		}
		for _, v := range res.Details {
			//电商取a8货号
			if v.ErpId == 2 {
				skuThirdMap[cast.ToString(v.SkuId)] = cast.ToString(v.ThirdSkuId)
				skuMap[cast.ToString(v.SkuId)] = cast.ToString(v.ProductId)
			}
		}
		for _, skuId := range skuIds {
			skuIdStr := cast.ToString(skuId)
			if _, ok := skuMap[skuIdStr]; !ok {
				return errors.New("商品" + "[" + skuIdStr + "]" + "不存在")
			}
		}

		return nil
	}(); err != nil {
		glog.Error(c.orderMain.OldOrderSn, ",", err)
		return
	}

	orderProductModel := make([]*models.OrderProduct, len(params.OrderProductModel))
	var parentSkuIdIndex int
	var childrenSku []*models.OrderProduct
	for k, i2 := range params.OrderProductModel {
		Product := &models.OrderProduct{
			OrderSn:              c.orderMain.OrderSn,
			SkuId:                i2.Sku,
			ProductId:            skuMap[i2.Sku],
			ProductType:          i2.ProductType,
			ParentSkuId:          i2.ParentSkuId,
			ProductName:          i2.ProductName,
			BarCode:              i2.BarCode,
			DiscountPrice:        i2.Price, //折扣价
			PayPrice:             i2.PayPrice,
			Number:               i2.Number,
			Specs:                i2.Specs,
			PaymentTotal:         i2.PaymentTotal,
			SkuPayTotal:          i2.SkuPayTotal,
			Privilege:            i2.Privilege,
			PrivilegePt:          i2.PrivilegePt,
			PrivilegeTotal:       i2.PrivilegeTotal,
			MarkingPrice:         i2.MarkingPrice,
			DeliverNum:           i2.DeliverNum,
			RefundNum:            i2.RefundNum,
			Image:                i2.Image,
			SubBizOrderId:        i2.SubBizOrderId,
			PromotionId:          int64(i2.PromotionId),
			PromotionType:        i2.PromotionType,
			MallOrderProductId:   i2.MallOrderProductId,
			TermType:             i2.TermType,
			TermValue:            i2.TermValue,
			VirtualInvalidRefund: i2.VirtualInvalidRefund,
			// //添加药品仓标识
			WarehouseType: i2.WarehouseType,
		}

		//虚拟商品不需要货号
		if i2.ProductType != 2 {
			Product.ThirdSkuId = skuThirdMap[i2.Sku]
		}

		// 商品实付金额
		if Product.ProductType != 3 {
			c.orderMain.GoodsPayTotal += Product.PaymentTotal
		}

		//TODO: 将组合商品的子商品用json存储，后面版本评估后将json外的子商品清除
		if len(i2.ParentSkuId) == 0 || k == len(params.OrderProductModel)-1 {
			if len(i2.ParentSkuId) != 0 {
				childrenSku = append(childrenSku, Product)
			}
			if len(childrenSku) > 0 {
				orderProductModel[parentSkuIdIndex].ChildrenSku = kit.JsonEncode(childrenSku)
				childrenSku = []*models.OrderProduct{}
			}
			parentSkuIdIndex = k
		} else {
			childrenSku = append(childrenSku, Product)
		}

		orderProductModel[k] = Product
	}

	//优惠信息
	orderPromotions := make([]models.OrderPromotion, len(params.OrderPromotion))
	for k, i2 := range params.OrderPromotion {
		orderPromotions[k] = models.OrderPromotion{
			OrderSn:        c.orderMain.OrderSn,
			PromotionId:    int64(i2.PromotionId),
			PromotionType:  i2.PromotionType,
			PromotionTitle: i2.PromotionTitle,
			PoiCharge:      i2.PoiCharge,
			PtCharge:       i2.PtCharge,
			PromotionFee:   i2.PromotionFee,
		}
	}

	//统计运费优惠
	c.orderMain.FreightPrivilege = c.CalTotalChargeFreight(orderPromotions, 1)
	// 商家预计收入
	c.orderMain.ActualReceiveTotal = c.orderMain.GetActualReceiveTotal()

	_ = c.session.Begin()

	glog.Info("SaveMallOrder", kit.JsonEncode(c.orderMain))
	//插入数据库
	_, err = c.session.Insert(c.orderMain, c.orderDetail, &orderProductModel, &orderPromotions)
	//插入失败 不回滚也不返回错误
	if err != nil {
		_ = c.session.Rollback()
		glog.Error("电商订单插入数据库失败, ", err, ", ", kit.JsonEncode(c.orderMain))
		return err
	}

	// 需要锁库存，并且不是第三方商品的才调用锁库存,Vip卡实物订单也不锁库存
	lockInventory := params.OrderProductModel[0].IsThirdProduct == 0 && c.orderMain.OrderType != 4 && c.orderMain.OrderType != 20
	if lockInventory && c.orderMain.OrderType == 11 {
		// 预售如果开启虚拟库存，则不锁库
		if len(params.OrderProductModel) > 0 && params.OrderProductModel[0].UseVirtualStock > 0 {
			lockInventory = false
		}
	}

	// 需要锁库存，并且不是第三方商品的才调用锁库存
	if lockInventory {
		//锁之前查一下库存
		c.OrderQueryInventory(warehouseId)
		//锁库存
		err = c.OrderLockInventory()
		if err != nil {
			_ = c.session.Rollback()
			return errors.New("订单冻结库存失败，" + err.Error())
		}
	}
	_ = c.session.Commit()

	go checkOrderRisk(c.orderMain, orderProductModel)

	return nil
}

// 计算优惠运费
// chargeType优惠类型, 默认0总优惠, 1商家优惠, 2平台优惠
func (c *CommonService) CalTotalChargeFreight(orderPromotions []models.OrderPromotion, chargeType ...int8) int32 {
	if len(orderPromotions) == 0 {
		orderPromotions = c.GetOrderPromotion()
	}

	var _type int8
	if len(chargeType) > 0 {
		_type = chargeType[0]
	}

	var freightFloat int32
	for _, i2 := range orderPromotions {
		if _, ok := FreightType[i2.PromotionType]; ok ||
			strings.Contains(i2.PromotionTitle, "减运") ||
			strings.Contains(i2.PromotionTitle, "运费红包") || strings.Contains(i2.PromotionTitle, "减配送费") {
			switch _type {
			case 1: //商家优惠
				freightFloat += i2.PoiCharge
			case 2: //平台优惠
				freightFloat += i2.PtCharge
			default: //总优惠
				freightFloat += i2.PromotionFee
			}
		}
	}

	return freightFloat
}

// 异常配送单自动已送达回调处理
func (c *CommonService) OrderExceptionAotuArrive() (err error) {
	glog.Info(c.orderMain.ParentOrderSn, ", ", c.orderMain.OrderSn, ", 自动已送达进入")

	//根据订单编号查询异常
	_, err = c.session.Exec(`update order_exception set order_status=4 ,is_show=0 where order_sn = ? and order_status=3`, c.orderMain.OrderSn)
	if err != nil {
		glog.Error(c.orderMain.ParentOrderSn, ", ", c.orderMain.OrderSn, ", 自动已送达出错, ", err.Error())
	}
	return
}

// 注：一个用户只能同时下一个订单。
func (c *CommonService) FreezeIntegral(memberId, orderId string, score int64, orgId int32) (bool, error) {
	var freezeKey = fmt.Sprintf("Integral.Lock.%s", memberId)
	//1、查询总积分，判断积分是否充足
	//2、冻结积分
	redisConn := GetRedisConn()

	freezeLockBool := redisConn.SetNX(freezeKey, memberId, time.Hour*72).Val()
	if freezeLockBool {
		lockIntegralSql := "SELECT memberid,integral FROM datacenter.member_integral_info WHERE memberid = ? and org_id=?;"
		memberIntegralInfo := dto.MemberIntegralInfo{}
		GetDBConn().SQL(lockIntegralSql, memberId, orgId).Get(&memberIntegralInfo)
		if memberIntegralInfo.Integral == 0 || memberIntegralInfo.Integral < score {
			return false, errors.New(fmt.Sprintf("该会员(%s)积分不足", memberId))
		}
		//清理过期的，然后再冻结
		integralKey := fmt.Sprintf("Integral.Lock.memberId:%s.%s", memberId, cast.ToString(orgId))
		c.CleanRedisHash(integralKey)
		freezeBool := redisConn.HSet(integralKey, orderId, fmt.Sprintf("%s-%d-%d", memberId, score, time.Now().Add(time.Hour*72).Unix())).Val()
		if freezeBool {
			return true, nil
		} else {
			return false, errors.New("冻结积分明细失败")
		}
	} else {
		//冻结积分失败
		return false, errors.New("积分冻结失败，加用户积分锁失败")
	}

}

// 抵扣积分
// 1. redis 查询积分
// 2. mysql 查询积分
// 3. 修改mysql积分
func (c *CommonService) DeductIntegral(memberId, orderId string, orgId int32) (bool, error) {
	glog.Info("记录扣减积分参数：", memberId, orderId)
	integralKey := fmt.Sprintf("Integral.Lock.memberId:%s.%s", memberId, cast.ToString(orgId))
	redisConn := GetRedisConn()
	session := GetDBConn().NewSession()
	defer session.Close()

	//获取冻结的积分总额
	freezeVal := redisConn.HGet(integralKey, orderId).Val()
	integral := cast.ToInt64(strings.Split(freezeVal, "-")[1])
	//重新确认数据库中的积分是否充足
	lockIntegralSql := "SELECT memberid,integral FROM datacenter.member_integral_info WHERE memberid = ? and org_id=?;"
	memberIntegralInfo := dto.MemberIntegralInfo{}
	session.SQL(lockIntegralSql, memberId, orgId).Get(&memberIntegralInfo)
	memberScore := memberIntegralInfo.Integral - integral
	if memberScore < 0 {
		return false, errors.New(fmt.Sprintf("该会员(%s)积分不足", memberId))
	}

	session.Begin()

	// 扣减用户积分 -- 规则：优先扣除老积分
	upSql := "UPDATE datacenter.member_integral_info SET integral = ? WHERE memberid = ? and org_id=?;"
	session.Exec(upSql, memberScore, memberId, orgId)
	integralId := kit.GetGuid32()

	// 查询用户的会员等级
	member := &models.UpetMember{}
	_, err := GetUPetDBConn().Table("upet_member").Where("scrm_user_id=?", memberId).Get(member)

	// 添加积分消费记录
	memberIntegralRecord := models.MemberIntegralRecord{
		Integralid:     integralId,
		Orderid:        orderId,
		Memberid:       memberId,
		Payamount:      0,
		Integralcount:  integral,
		Integraltype:   52,
		Integralreason: fmt.Sprintf("积分兑换:%s", orderId),
		Ischeck:        1,
		Lasttime:       time.Now(),
		Createdate:     time.Now(),
		OrgId:          cast.ToInt(orgId),
		UserLevelId:    cast.ToInt32(member.UserLevelId),
	}
	_, err = session.Table("datacenter.member_integral_record").Insert(&memberIntegralRecord)
	if err != nil {
		_ = session.Rollback()
		return false, err
	}
	//添加抵扣记录并修改记录列表的结余积分
	integralRecordSql := "SELECT integralid,memberid,integralcount,surplusintegralcount FROM datacenter.member_integral_record WHERE ischeck=1 and surplusintegralcount>0 and integraltype in(11,21,31,41,61,71) and memberid = ? and org_id=? order by createdate asc"
	var integralRecordList []models.MemberIntegralRecord
	session.SQL(integralRecordSql, memberId, orgId).Find(&integralRecordList)
	leaveIntegral := integral //扣减之后剩余积分
	var integralDeductionList []models.MemberIntegralDeduction
	isOk := true
	for _, v := range integralRecordList {
		//剩余积分全部抵扣完，退出循环
		if leaveIntegral <= 0 {
			break
		}
		var thisDeductIntegral int64 //本次抵扣积分数
		if leaveIntegral >= v.Surplusintegralcount {
			thisDeductIntegral = v.Surplusintegralcount
			leaveIntegral = leaveIntegral - thisDeductIntegral
		} else {
			thisDeductIntegral = leaveIntegral
			leaveIntegral = leaveIntegral - thisDeductIntegral
		}

		//插入抵扣记录
		integralDeduction := models.MemberIntegralDeduction{
			Memberid:            memberId,
			Deductintegralid:    integralId,
			Integralid:          v.Integralid,
			Deductintegralcount: thisDeductIntegral,
		}
		integralDeductionList = append(integralDeductionList, integralDeduction)
		//更新积分记录的剩余积分数
		consumeScoreSql := "UPDATE datacenter.member_integral_record SET surplusintegralcount=? WHERE integralid=?;"
		_, err = session.Exec(consumeScoreSql, v.Surplusintegralcount-thisDeductIntegral, v.Integralid)
		if err != nil {
			session.Rollback()
			isOk = false
			break
		}
	}
	if !isOk {
		return false, nil
	}
	_, err = session.Table("datacenter.member_integral_deduction").Insert(&integralDeductionList)
	if err != nil {
		session.Rollback()
		return false, err
	}
	err = session.Commit()
	if err != nil {
		session.Rollback()
		return false, err
	}

	go func() {
		igcClient := igc.GetIntegralServiceClient()
		_, err := igcClient.IO.IntegralChangeMessage(context.Background(), &igc.IntegralChangeMessageRequest{
			MemberId:       memberId,
			IntegralType:   -2,
			InsertIntegral: cast.ToInt32(integral),
			OrgId:          orgId,
		})
		if err != nil {
			glog.Error("积分兑换通知异常：", err.Error())
		}
	}()
	//扣减成功后，删除冻结字段方法
	redisConn.HDel(integralKey, orderId)
	_freezeKey := fmt.Sprintf("Integral.Lock.%s", memberId)
	redisConn.Del(_freezeKey)
	return true, nil
}

// 清除过期的积分冻结数据
func (c *CommonService) CleanRedisHash(integralKey string) {
	redisConn := GetRedisConn()
	//看看这个锁定key有没有存在
	if redisConn.Exists(integralKey).Val() > 0 {
		records := redisConn.HGetAll(integralKey).Val()
		for k, v := range records {
			values := strings.Split(v, "-")
			freezeTime, _ := strconv.Atoi(values[2])
			timeoutTime := time.Unix(int64(freezeTime), 0)
			if timeoutTime.Before(time.Now()) {
				redisConn.HDel(integralKey, k)
			}
		}
	}
}

// 流程日志落地
func (c *CommonService) SaveOrderLog(orderLog []*models.OrderLog) {
	if len(orderLog) == 0 {
		return
	}

	orderLogMap := map[string]*models.OrderLog{}
	//去重
	for _, v := range orderLog {
		if len(v.OrderSn) == 0 {
			glog.Warning("流程日志记录有空订单号, ", kit.JsonEncode(v), ", ", kit.RunFuncName(2))
			continue
		}
		mapKey := v.OrderSn + cast.ToString(v.LogType)
		if _, ok := orderLogMap[mapKey]; !ok {
			orderLogMap[mapKey] = v
		}
	}

	orderLog = []*models.OrderLog{}
	for _, v1 := range orderLogMap {
		orderLog = append(orderLog, v1)
	}

	if _, err := c.session.Insert(orderLog); err != nil {
		glog.Error("订单流程落地失败, ", err, ", ", kit.JsonEncode(orderLog), ", ", kit.RunFuncName(2))
	}
}

// 推送父订单订阅消息
func (c *CommonService) PushOrderSubscribeMessage(orderSn string) {
	c.orderMain = new(models.OrderMain)
	c.orderMain = GetOrderMainByOrderSn(orderSn)
	if c.orderMain.Id > 0 {
		subscribeOrder := []string{c.orderMain.OrderSn, c.orderMain.ParentOrderSn}

		dsOrderId := "" //跳转电商的订单详情
		upetDb := GetUPetDBConn()
		if c.orderMain.IsVirtual == 1 {
			parentOrder := GetOrderMainByOrderSn(c.orderMain.ParentOrderSn)
			subscribeOrder = append(subscribeOrder, parentOrder.OldOrderSn)
			orderIdSlice, err := upetDb.Query("SELECT order_id FROM upet_vr_order WHERE erp_order_sn=?;", c.orderMain.OrderSn)
			if err != nil {
				glog.Error("查询电商的退款订单号返回结果报错：", err, c.orderMain.OrderSn)
			}
			glog.Info("查询电商的退款订单号返回结果：", kit.JsonEncode(orderIdSlice), c.orderMain.OrderSn)
			if len(orderIdSlice) > 0 {
				dsOrderId = string(orderIdSlice[0]["order_id"])
			}
		} else {
			orderIdSlice, err := upetDb.Query("SELECT order_id FROM upet_orders WHERE order_sn=?;", c.orderMain.OrderSn)
			if err != nil {
				glog.Error("查询电商的退款订单号返回结果报错：", err, c.orderMain.OrderSn)
			}
			glog.Info("查询电商的退款订单号返回结果：", kit.JsonEncode(orderIdSlice), c.orderMain.OrderSn)
			if len(orderIdSlice) > 0 {
				dsOrderId = string(orderIdSlice[0]["order_id"])
			}
		}
		if c.orderMain.ChannelId == ChannelAwenId || c.orderMain.ChannelId == ChannelDigitalHealth {
			dsOrderId = c.orderMain.OrderSn
		}

		glog.Info("订单状态变更通知的参数信息：", dsOrderId, c.orderMain.OrderSn)
		for _, s := range subscribeOrder {
			param := dto.PushSubscribeMessage{
				OrderSn: s,
				OrgId:   c.orderMain.OrgId,
			}
			status := "已发货"
			remarks := "您的订单已发货"
			pushOrderStatusTemplate(param, orderSn, status, remarks, dsOrderId, cast.ToString(c.orderMain.IsVirtual))
		}
	}
}

// RePushToZiLongProcess
// 重新推送订单到子龙
// v5.6.8
func RePushToZiLongProcess(mqRefundRePush *dto.MqRePushOrderToZiLong) error {
	// 重推次数+1
	count := mqRefundRePush.Count + 1
	c := &CommonService{
		BaseService: BaseService{},
		session:     GetDBConn().NewSession(),
		orderMain:   GetOrderMainByOrderSn(mqRefundRePush.OrderSn),
		orderDetail: GetOrderDetailByOrderSn(mqRefundRePush.OrderSn),
	}
	defer c.session.Close()

	logHead := "RePushToZiLongProcess:" + c.orderMain.OrderSn
	maxCountString, _ := config.Get("repush_zilong_max_count")
	fmt.Println("maxCountString：" + maxCountString)
	maxCount, _ := strconv.Atoi(maxCountString)
	if maxCount == 0 {
		maxCount = 5
	}

	// 如果配置中心有配置，且配置值大于5,重推次数达到maxCount,丢弃这条消息
	glog.Info(logHead, "RePushToZiLongProcess:", kit.JsonEncode(mqRefundRePush))
	defer c.session.Close()

	switch mqRefundRePush.Type {
	// 重推订单
	case dto.RePushZiLongOrder:
		retCode, err := new(OrderService).MtRePushThird(context.Background(), &oc.MtRePushThirdRequest{
			OrderSn: c.orderMain.OrderSn,
		})
		if retCode.Code != 200 || err != nil {
			if count < maxCount {
				c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
					Count:   count,
					Type:    dto.RePushZiLongOrder,
					OrderSn: c.orderMain.OrderSn,
				})
			}
			return err
		}
		// 重推退单
	case dto.RePushZiLongRefundOrder:
		retCode, err := new(AfterSaleService).RefundRePushThird(context.Background(), &oc.RefundRePushThirdRequest{
			RefundSn: mqRefundRePush.RefundSn,
		})
		if retCode.Code != 200 || err != nil {
			if count < maxCount {
				c.RePushToZiLongTrigger(&dto.MqRePushOrderToZiLong{
					Count:    count,
					Type:     dto.RePushZiLongRefundOrder,
					OrderSn:  c.orderMain.OrderSn,
					RefundSn: mqRefundRePush.RefundSn,
				})
			}
			return err
		}
	default:
	}
	// 重推子龙流程处理成功
	glog.Info(logHead, "-repush to zilong success:", kit.JsonEncode(mqRefundRePush))
	return nil
}

// 触发-订单推送子龙重推
// rePushCount 重推次数 从0开始
// v5.6.8
func (c *CommonService) RePushToZiLongTrigger(mqContent *dto.MqRePushOrderToZiLong) bool {
	//logHead := c.orderMain.OrderSn + "-RePushOrderToZiLongTrigger"
	logHead := "------------"
	mqContentJson := kit.JsonEncode(mqContent)

	//放入延迟队列 延迟市场为 2的rePushCount次方 分钟
	delayTimeExp := utils.Exponent(2, mqContent.Count)
	delayTime := int64(time.Duration(delayTimeExp) * 5 * time.Second / time.Millisecond)
	glog.Info(logHead, "-PublishRabbitMQ:", delayTime, " -", mqContentJson)

	err := utils.PublishRabbitMQDelayed(QueueOrderRePushToZiLongQueue, QueueOrderRePushToZiLongRoute, QueueOrderRePushToZiLongExchange, mqContentJson, delayTime)
	if err != nil {
		glog.Error(c.orderMain.OrderSn, ",repush task PublishRabbitMQ.failed:，", err)
	}
	return true
}

// v6.0添加 第三方订单拆单
// @return realOrders 子实物订单号
func (s *CommonService) ThirdSplitOrder() (realOrders []string, err error) {
	//是否是组合商品 如果是组合商品 需要查询子商品
	glog.Info(s.orderMain.OrderSn, "进入第三方订单拆单！v6.0")
	realOrders, err = s.SaveThirdSplitOrder()
	if err != nil {
		glog.Error(s.orderMain.OrderSn, "第三方订单拆单失败！", err)
		//拆分失败更新主订单状态为异常单
		_, err = s.session.Exec(
			"UPDATE order_detail INNER JOIN order_main ON order_detail.order_sn=order_main.order_sn "+
				"SET split_order_result = ?,split_order_fail_reason = ? WHERE order_main.order_sn = ?",
			2, err.Error(), s.orderMain.OrderSn)
		if err != nil {
			glog.Error("第三方订单 拆单失败状态更新失败！：", s.orderMain.OrderSn, " ", err.Error())
			return realOrders, err
		}
	}
	return realOrders, nil
}

// 第三发订单的拆单入库
// v6.0临时添加
// todo 后续需要跟非第三方的进行整合优化
// 返回实物
func (s *CommonService) SaveThirdSplitOrder() (realOrderSn []string, err error) {
	//发生的概率很小每天几次 对业务使用睡眠的方式对业务影响暂时不大
	if len(s.orderProducts) == 0 {
		glog.Error("第三方订单拆单时查询主单商品无数据：", s.orderMain.OrderSn)
	}

	//主订单商品
	orderProductsMap := make(map[int64]*models.OrderProduct, 0)
	//主订单商品sku-数量（电商拆单之后没有价格，所以通过数量分摊到各个订单）
	orderProductNumberMap := make(map[string]int32, 0)

	for _, p := range s.orderProducts {
		orderProductsMap[p.Id] = p
		orderProductNumberMap[p.SkuId] += p.Number
	}

	var req []dto.SplitThirdOrderReq // 原来的订单不动
	for _, item := range s.orderProducts {
		//调拆单接口时要过滤掉组合商品 组合商品的组合商品已经放出来 此时的组合商品只是一个虚的标志 没有实际意义 不需要拆单
		if item.ProductType == 3 {
			continue
		}
		var preq dto.SplitThirdOrderReq
		preq.OrderProductId = item.Id
		preq.Skuid = cast.ToInt32(item.SkuId)
		preq.Groupskuid = cast.ToInt32(item.ParentSkuId)
		preq.Stock = item.Number   //所需库存
		preq.Price = item.PayPrice //均摊后支付单价 有的支付单价一直
		//虚拟商品
		if item.ProductType == 2 {
			preq.Isvirtual = 1
		}
		if len(item.ParentSkuId) > 0 {
			preq.Isgroup = 1
		}
		req = append(req, preq) // 加入原来的订单集合不变
	}

	comeFrom := 1
	//调用拆单接口
	splitOrders := []dto.SplitThirdOrderResp{}
	if len(req) > 0 {
		glog.Info(fmt.Sprintf("第三方订单拆单OrderSn:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;参数:%s", s.orderMain.OrderSn, s.orderMain.WarehouseCode, s.orderMain.ReceiverState, comeFrom, kit.JsonEncode(req)))
		splitOrders, err = SplitThirdOrder(req, s.orderMain.OrderSn, s.orderMain.WarehouseCode)
		if err != nil {
			return realOrderSn, err
		}
		glog.Info(fmt.Sprintf("第三方订单拆单OrderSn:%s,WarehouseCode:%s,ReceiverState:%s,comeFrom:%d;拆单结果%s", s.orderMain.OrderSn, s.orderMain.WarehouseCode, s.orderMain.ReceiverState, comeFrom, kit.JsonEncode(splitOrders)))
	}
	//合并两个订单的接口
	if len(splitOrders) <= 0 {
		return realOrderSn, errors.New("调用拆单接口返回子订单数量为0")
	}

	//拆分后的订单入库保存,如果是虚拟子订单还需为虚拟商品生成核销码，
	var (
		childOrders        []*models.OrderMain       //子单
		childOrderDetails  []*models.OrderDetail     //子单详情
		childOrderProducts []*models.OrderProduct    //子单商品
		orderVerifyCodes   []*models.OrderVerifyCode //子单虚拟商品核销码
		virtualTotal       int32                     // 虚拟订单合计金额
	)

	//获取指定数量订单号
	orderSns := GetSn("order", len(splitOrders))

	ordersLen := int32(len(splitOrders))
	for index, childOrder := range splitOrders {
		//子订单
		child := *s.orderMain      //从主单拷贝一份到子单
		child.Id = 0               //清空主键
		child.Freight = 0          //子订单运费先置0
		child.FreightPrivilege = 0 //子订单运费优惠先置0

		child.OrderSn = orderSns[index]
		child.OldOrderSn = child.OrderSn //子订单的渠道订单号与订单号是一样的 也就是子订单的渠道订单号没有意义
		child.ParentOrderSn = s.orderMain.OrderSn
		child.WarehouseCode = childOrder.Warehousecode
		childDetail := *s.orderDetail //订单详情 从主单拷贝一份到子单
		childDetail.OrderSn = child.OrderSn
		childDetail.SplitOrderResult = 1 //拆单成功
		//newSplitOrdersLen := len(newSplitOrders) // 子单的数量用于运费的计算均摊

		var (
			itemPaymentSum        int32 //子订单实付总金额
			itemPrivilegeSum      int32 //商家优惠金额
			itemPrivilegeTotalSum int32 //总优惠金额
			itemGoodsTotalSum     int32 //子订单商品金额
			product               *models.OrderProduct
			ok                    bool
		)

		skuPayTotalSum := map[string]int32{}
		skuPayTotalRecord := map[string]int32{}
		//处理子订单的商品 计算子订单中的每个sku的实付金额
		for _, goods := range childOrder.SplitOrderGoods {
			product, ok = orderProductsMap[goods.OrderProductId]
			//商品数据
			if ok {
				//不存在的时候才放入
				//product.SkuId+product.ParentSkuId 第三方非组合商品的SkuPayTotal 已经在美团均摊计算好
				//拆单时只需要将组合商品子商品的skuPaytotal加上即可 如果一个组合里存在一个sku多个价格的问题此处就可能出问题
				//当前只考虑一个组合商品里一个sku只存在一个价格的情况
				if _, skuPayTotalOk := skuPayTotalRecord[product.SkuId+product.ParentSkuId]; !skuPayTotalOk {
					skuPayTotalRecord[product.SkuId+product.ParentSkuId] = product.SkuPayTotal
					skuPayTotalSum[product.SkuId] += product.SkuPayTotal
				}
			}
		}

		//子商品
		for _, goods := range childOrder.SplitOrderGoods {
			//子订单商品
			skuId := cast.ToString(goods.Skuid)
			parentSkuId := ""
			if goods.Groupskuid > 0 {
				parentSkuId = cast.ToString(goods.Groupskuid)
			}

			product, ok = orderProductsMap[goods.OrderProductId]
			if ok {

				childProduct := *product //从主单商品复制一份 原商品

				childProduct.Id = 0 //清空主键
				childProduct.OrderSn = child.OrderSn
				childProduct.Number = product.Number                          //数量
				childProduct.SkuPayTotal = skuPayTotalSum[childProduct.SkuId] //记录的是sku的支付总金额  不一定是这一个订单的
				childOrderProducts = append(childOrderProducts, &childProduct)

				//虚拟商品生成核销码
				if product.ProductType == 2 {
					//有虚拟商品是虚拟订单
					child.IsVirtual = 1
					checkoffCodes := GetNewVerifyCode(int(childProduct.Number)) //生成购买数量的核销码
					glog.Info(s.orderMain.OrderSn, " 生成核销码", kit.JsonEncode(checkoffCodes))
					for _, code := range checkoffCodes {
						verifyCodeExpiryDate := GetCheckoffExpireDate(product.TermType, product.TermValue)

						var checkOff = &models.OrderVerifyCode{
							OrderSn:              child.OrderSn,
							VerifyCode:           code,
							SkuId:                skuId,
							ParentSkuId:          parentSkuId,
							GroupItemNum:         childProduct.GroupItemNum,
							VerifyCodeExpiryDate: verifyCodeExpiryDate,
							VerifyStatus:         0, //默认未核销
						}
						orderVerifyCodes = append(orderVerifyCodes, checkOff)
					}
				}

				//统计订单实付金额，商品原总金额，优惠金额
				itemPaymentSum += product.PaymentTotal                     //总支付金额 不包含运费
				itemGoodsTotalSum += product.Number * product.MarkingPrice //原价总额
				itemPrivilegeSum += product.Privilege
				itemPrivilegeTotalSum += product.PrivilegeTotal
			}
		}

		//----所有的子订单商品循环处理结束-----
		if child.IsVirtual == 1 { //虚拟订单付款后主状态变成完成，子状态为待核销
			child.OrderStatus = 30
			child.OrderStatusChild = 30101 //待核销
			ordersLen = ordersLen - 1

		} else { //含实物商品的订单，就是实物单，将运费,打包费，服务费放到实物单
			realOrderSn = append(realOrderSn, child.OrderSn)
			child.Freight = s.orderMain.Freight
			child.PackingCost = s.orderMain.PackingCost
			child.ServiceCharge = s.orderMain.ServiceCharge
			child.FreightPrivilege = s.orderMain.FreightPrivilege
		}

		//订单的总优惠价格  平台优惠+商家优惠 订单没有区分这两个优惠的字段 只有总优惠
		child.Privilege = itemPrivilegeTotalSum
		child.PayAmount = itemPaymentSum //均摊后实际支付的价

		child.GoodsPayTotal = itemPaymentSum // 商品实付金额
		//实物总支付金额 = 实付商品支付金额+ 实际运费 + 包装费 其他优惠已经均摊进PayAmount
		//虚拟总支付金额 = 实际商品支付金额
		if child.IsVirtual == 1 {
			child.Total = child.PayAmount
			child.PayTotal = child.Total
			child.ActualReceiveTotal = child.Total

			virtualTotal += child.Total // 实物订单总是在最后
		} else {
			//只有实物商品才加包装费与运费
			child.Total = child.PayAmount + child.Freight - child.FreightPrivilege + child.PackingCost
			child.PayTotal = s.orderMain.Total - virtualTotal
			child.ActualReceiveTotal = s.orderMain.ActualReceiveTotal - virtualTotal
		}

		child.GoodsTotal = child.PayAmount + child.Privilege //原来商品金额
		childOrders = append(childOrders, &child)
		childOrderDetails = append(childOrderDetails, &childDetail)
	}

	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()
	//todo 如果实物会被拆成多个单 则需要进行运费的分摊
	_ = session.Begin()

	//子订单
	if len(childOrders) > 0 {
		if _, err = session.Insert(&childOrders); err != nil {
			glog.Error(s.orderMain.OrderSn, ", 插入子订单失败, ", err, ", ", kit.JsonEncode(childOrders))
			_ = session.Rollback()
			return realOrderSn, err
		}
	}
	//子订单详情
	if len(childOrderDetails) > 0 {
		if _, err = session.Insert(&childOrderDetails); err != nil {
			glog.Error(s.orderMain.OrderSn, ", 插入子订单详情失败, ", err, ", ", kit.JsonEncode(childOrderDetails))
			_ = session.Rollback()
			return realOrderSn, err
		}
	}
	//商品
	if len(childOrderProducts) > 0 {
		if _, err = session.Insert(&childOrderProducts); err != nil {
			glog.Error(s.orderMain.OrderSn, ", 插入子订单商品失败, ", err, ", ", kit.JsonEncode(childOrderProducts))
			_ = session.Rollback()
			return realOrderSn, err
		}
	}
	//核销码
	if len(orderVerifyCodes) > 0 {
		if _, err = session.Insert(&orderVerifyCodes); err != nil {
			glog.Error(s.orderMain.OrderSn, ", 插入订单核销码失败, ", err, ", ", kit.JsonEncode(orderVerifyCodes))
			_ = session.Rollback()
			return realOrderSn, err
		}
	}

	//更新拆单状态
	if _, err = session.Exec("update order_detail set split_order_result=1 where order_sn=?", s.orderMain.OrderSn); err != nil {
		glog.Error(s.orderMain.OrderSn, ", 更新订单拆单状态失败, ", err)
		_ = session.Rollback()
		return realOrderSn, err
	}

	//提交事务失败回滚
	err = session.Commit()
	if err != nil {
		_ = session.Rollback()
		return realOrderSn, err
	}
	return realOrderSn, nil
}

// v6.0添加
// 第三方订单推单、删除在途库存、发起配送
// todo 阿闻支付拆单后的推单与第三方接单后的推单进行代码封装
func (s *CommonService) ThirdPushOrder() error {
	//是否是组合商品 如果是组合商品 需要查询子商品
	//实物子订单推送第三方
	//线锁住订单 因为美团以及饿了么的订单可能存在接单后回调并（MtAcceptOrder方法）推送第三方的情况
	//回调的地方（MtAcceptOrder方法）加了锁 ，此处也要将订单锁住
	redisConn := GetRedisConn()
	lockCard := "lock:order_" + s.orderMain.OldOrderSn

	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		glog.Error(s.orderMain.OrderSn, ",推单失败-订单不可操作")
		return errors.New("订单不可操作")
	}
	defer redisConn.Del(lockCard)

	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
	}
	realOrder.session = GetDBConn().NewSession()
	defer realOrder.session.Close()

	// 由于强制走主库还会出现找不出子订单情况，先加个延时试试
	// 需要发配送的实物子订单（查询强制走主库）
	// 小程序订单，如果存在需要发配送的实物单(目前只存在一个子实物订单)，发配送；电商我们这边不发配送，电商自己发
	//todo 使用上下文的数据  不要实时查询数据
	findChildOrder := func() bool {
		success, err := realOrder.session.Select("/*FORCE_MASTER*/ *").
			Where("parent_order_sn = ? and is_virtual = 0", s.orderMain.OrderSn).
			Get(realOrder.orderMain)
		if err != nil {
			glog.Error("实物子订单查询错误：", s.orderMain.OrderSn, " ", err.Error())
		}
		return success
	}
	ok := false
	for retryCount := 0; !ok && retryCount < 2; retryCount++ {
		ok = findChildOrder()
		if !ok {
			time.Sleep(100 * time.Millisecond)
		}
	}
	if !ok {
		//查询失败 会导致没有推送第三方 删库存与 发配送
		glog.Error(s.orderMain.OrderSn, "第三方订单查询拆单后子订单失败！")
		return errors.New("第三方订单查询拆单后子订单失败")
	}

	glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "拆单后推送实物子单到第三方")

	_ = realOrder.session.Begin()

	upOrderDetail := new(models.OrderDetail)

	//推送第三方（子龙 or 全渠道）
	realOrderDetail := GetOrderDetailByOrderSn(realOrder.orderMain.OrderSn, "push_third_order")

	if realOrderDetail.PushThirdOrder == 0 {
		glog.Info(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单支付通知,处理子订单2（推送第三方）！")
		//记录接单节点
		realOrder.SaveOrderLog([]*models.OrderLog{
			{
				OrderSn: realOrder.orderMain.ParentOrderSn,
				LogType: models.OrderLogAcceptedOrder,
			},
			{
				OrderSn: realOrder.orderMain.OrderSn,
				LogType: models.OrderLogAcceptedOrder,
			},
		})

		//推送第三方（子龙 or 全渠道）
		err := realOrder.PushThirdOrder(false)
		glog.Info(realOrder.orderMain.OldOrderSn, ", 推送第三方成功状态：", err)
		if err != nil {
			upOrderDetail.PushThirdOrder = 0
			upOrderDetail.PushThirdOrderReason = err.Error()
		} else {
			//删除在途库存
			if realOrder.orderMain.AppChannel == 12 {
				res := Freeze(realOrder.orderMain.ParentOrderSn, realOrder.orderMain.ShopId, 2)
				if res.Code != 200 {
					glog.Info(realOrder.orderMain.OldOrderSn, ", SAAS扣减库存失败：", res.Msg)
				}
			} else {
				go DeleteTransportationInventory(realOrder.orderMain.ParentOrderSn, realOrder.orderMain.ChannelId, true)
			}
			upOrderDetail.PushThirdOrder = 1
		}

		//更新状态
		_, err = realOrder.session.In("order_sn", realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn).
			Cols("push_third_order,push_third_order_reason").Update(upOrderDetail)
		if err != nil {
			glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "推送第三方后更新订单推送状态失败！", err.Error())
			_ = realOrder.session.Rollback()
			return err
		}
		_ = realOrder.session.Commit()

		//自提单不推配送 或者是美团专送不再推送 第三方推送失败也发配送
		if realOrder.orderMain.DeliveryType != 3 && realOrder.orderMain.OrderType != 2 {
			if realOrder.orderMain.ChannelId == ChannelMtId ||
				(realOrder.orderMain.ChannelId == ChannelElmId && realOrder.orderMain.LogisticsCode == "6") ||
				(realOrder.orderMain.ChannelId == ChannelJddjId && realOrder.orderMain.LogisticsCode == "2938") {
				//不等于美团专送的才
				if !strings.Contains("2002,1001,1004,2010,3001,1007", realOrder.orderMain.LogisticsCode) {
					//美团配送
					_ = s.PushMpOrder()
				}
			}
		}
	}
	return nil
}

// 判断是否是第三方订单
func (s *CommonService) IsThirdOrder() bool {
	if s.orderMain.ChannelId == ChannelMtId || s.orderMain.ChannelId == ChannelJddjId || s.orderMain.ChannelId == ChannelElmId {
		return true
	} else {
		return false
	}
}

// 判断是第三方进行配送
func (s *CommonService) IsThirdDeliver() bool {
	if s.orderMain.ChannelId == ChannelElmId && s.orderMain.LogisticsCode != "6" { //6自配
		return true
	}
	if s.orderMain.ChannelId == ChannelJddjId && s.orderMain.LogisticsCode != "2938" { //2938自配
		return true
	}
	if s.orderMain.ChannelId == ChannelMtId {
		if strings.Contains("2002,1001,1004,2010,3001,1007", s.orderMain.LogisticsCode) { //自配
			return true
		}
	}
	return false
}

// 第三方退款时更新子单商品的退款数量
func (s *CommonService) ThirdChildOrderGoodRefundNumUpdate(product models.OrderProduct) error {
	//查询子单商品
	return nil
}

// 推送新的oms
func (c *CommonService) PushRPOmsRefund(orderMain *models.OrderMain, refundSn string, refundGoods []*oc.RefundGoodsOrder, RefundReason string) (err error) {
	//c.orderMain = orderMain
	if orderMain.AppChannel == SaasAppChannel {
		return nil
	}
	glog.Info(refundSn, ",", orderMain.OrderSn, ",推送RPOMS退款单 OrderMain：", kit.JsonEncode(orderMain), "退款商品", kit.JsonEncode(refundGoods))

	//待接单状态的订单不推送 直接在阿闻接住
	/*
		if orderMain.OrderStatusChild == 20101 {
			return
		}*/

	var orderRefund models.RefundOrder

	hasOrder, err := c.session.Table("refund_order").
		Where("refund_sn = ?", refundSn).
		Get(&orderRefund)
	if err != nil {
		glog.Errorf("获取退款订单信息失败 : <%v>", err)
		return errors.New(fmt.Sprintf("获取退款订单信息失败 : <%v>", err))
	} else if orderRefund.PushThird > 0 {
		return nil
	}

	glog.Info(refundSn, ",", orderMain.OrderSn, ",推送RPOMS退款单 是否有退款单：", kit.JsonEncode(hasOrder))

	request := &dto.OrderRefundHttpRequest{
		CreateType:          1,
		ChannelRefundSn:     refundSn,
		ChannelOrderSn:      orderMain.OrderSn,
		RefundWarehouseCode: orderMain.WarehouseCode,
		Note:                "阿闻系统推送",
		Entire:              false,
	}
	if hasOrder {
		request.RefundType = orderRefund.RefundType
		request.RefundReason = orderRefund.RefundReason
	} else {
		request.RefundType = 1
		request.RefundReason = RefundReason
	}

	//退款单商品
	var refundProducts []*dto.OrderRefundProduct
	var orderProducts []*models.OrderProduct

	err = c.session.Table("order_product").
		Where("order_sn = ?", orderMain.OrderSn).Find(&orderProducts)
	if err != nil || len(orderProducts) == 0 {
		return errors.New(fmt.Sprintf("PushRPOmsRefund 获取订单商品失败 : <%v>", err))
	}

	var realSkuId []string
	thirdSkuMap := make(map[string]string)
	//平台优惠 通过订单商品进行加总
	for _, x := range orderProducts {
		thirdSkuMap[x.SkuId] = x.ThirdSkuId
		realSkuId = append(realSkuId, x.SkuId)
	}

	//退款商品参数
	//订单商品退多少数量
	refundProductIdMap := make(map[string]int32)
	for _, v := range refundGoods {
		RefundAmount := kit.YuanToFen(cast.ToFloat64(v.RefundAmount))
		//改sku在本次退了多少个
		if _, ok := refundProductIdMap[v.GoodsId]; ok {
			refundProductIdMap[v.GoodsId] += v.Quantity
		} else {
			refundProductIdMap[v.GoodsId] = v.Quantity
		}

		item := &dto.OrderRefundProduct{
			ItemNum:      thirdSkuMap[v.GoodsId],
			RefundNumber: v.Quantity,
			RefundAmount: int32(RefundAmount),
			IsFree:       0,
			Id:           int(v.OrderProductId),
		}
		//根据数量与金额 计算退款金额 向下取整
		item.RefundPrice = cast.ToInt32(math.Floor(cast.ToFloat64(RefundAmount) / cast.ToFloat64(item.RefundNumber)))
		refundProducts = append(refundProducts, item)
	}
	request.OrderRefundProduct = refundProducts

	//判断是否退完
	FullRefund := true

	if !hasOrder {
		//没有退款单用商品去判断
		//因为第三方订单更新商品退了多少的之后只更新了主订单的商品的退款数量
		//所以在下面判断改订单是否已经退完的时候 第三方订单需要通过主订单的商品进行判断
		//判断主订单实物商品是否都退完了
		//用于计算是否全退的商品
		var calFullRefundProducts []*models.OrderProduct
		if c.IsThirdOrder() {
			err = c.session.Table("order_product").Select("sku_id,number,refund_num").
				Where("order_sn = ?", orderMain.ParentOrderSn).In("sku_id", realSkuId).Find(&calFullRefundProducts)
			if err != nil || len(calFullRefundProducts) == 0 {
				return errors.New(fmt.Sprintf("PushRPOmsRefund 获取主订单商品失败 : <%v>", err))
			}
		} else {
			calFullRefundProducts = orderProducts
		}

		type refundNumRecord struct {
			number    int32
			refundNum int32
		}
		//得出所有的sku 的下单数量以及已经退款数量
		hasRefundCal := make(map[string]*refundNumRecord)
		for _, x := range calFullRefundProducts {
			if _, ok := hasRefundCal[x.SkuId]; ok {
				hasRefundCal[x.SkuId].number += x.Number
				hasRefundCal[x.SkuId].refundNum += x.RefundNum
			} else {
				hasRefundCal[x.SkuId] = &refundNumRecord{
					number:    x.Number,
					refundNum: x.RefundNum,
				}
			}
		}

		for sku, record := range hasRefundCal {
			var thisTimeRefundCount int32
			//改sku在这次退款中退了多少个
			if _, ok := refundProductIdMap[sku]; ok {
				thisTimeRefundCount = refundProductIdMap[sku]
			}
			//看是否每个sku的数量都被退完
			//有退款单 直接对比Number RefundNum
			//没有退款单 对比Number RefundNum与本次退款数量之和
			if record.number > record.refundNum+thisTimeRefundCount {
				FullRefund = false
			}
		}
	} else { //有退款单直接用退款单状态进行判断
		if orderRefund.FullRefund != 1 {
			FullRefund = false
		}
	}
	//计算退款金额
	//如果是第三方，那么退款单记录的总金额不是实际退款数
	for _, p := range request.OrderRefundProduct {
		request.RefundAmount += p.RefundAmount
	}

	glog.Info(refundSn, ",", orderMain.OrderSn, ",推送RPOMS退款单 orderProduct：", kit.JsonEncode(orderProducts), "是否全退", FullRefund)

	platformService := PlatformService(orderMain.OrderSn, refundSn)
	if orderMain.ChannelId == ChannelMtId {
		//平台服务费
		request.RefundServiceFee = platformService.ServiceCharge
		//平台补贴
		request.PlatformPrivilege = platformService.Allowance
	}

	//如果渠道是第三方，ordermain就是主单，refundsn查到的退款单数据也是不对的
	//如果是最后一次，赋值下面字段 是否退运费
	//全退的情况
	if FullRefund {
		request.Entire = true
		if orderMain.ChannelId != ChannelMtId {
			orderPromotion := c.GetOrderPromotion("pt_charge")
			for _, item := range orderPromotion {
				request.PlatformPrivilege += item.PtCharge
			}
			request.RefundServiceFee = orderMain.ServiceCharge
		}
		request.RefundContractFee = orderMain.ContractFee
		request.RefundPackingFee = orderMain.PackingCost
		//阿文全是自配 阿文或者非第三方自配  退的运费 = 运费-运费优惠
		request.RefundAmount += request.PlatformPrivilege

		//非自配
		if c.IsThirdDeliver() {
			request.RefundFreight = orderMain.Freight
			request.FreightPrivilege = orderMain.FreightPrivilege
			request.RefundAmount = request.RefundAmount + orderMain.PackingCost
		} else { //自配
			//阿闻 且已经发单 不退运费 否则都会退运费
			if !((orderMain.ChannelId == ChannelAwenId || orderMain.ChannelId == ChannelDigitalHealth) && orderMain.OrderStatusChild != 20102) {
				request.RefundFreight = orderMain.Freight - orderMain.FreightPrivilege
				request.RefundAmount = request.RefundAmount + orderMain.Freight - orderMain.FreightPrivilege + orderMain.PackingCost
			}
		}
	}

	retCode, msg := new(RpomsService).OrderRefundAdd(request)

	switch retCode {
	case 200:
		// 退款单推送成功再加库存
		c.doRefundStock(refundSn, orderMain, request.OrderRefundProduct)
		return nil
	case 400:
		//记录下来重推
		go func() {
			data := string(kit.JsonEncodeByte(request))
			omsRepush := models.OmsRepush{
				RefundSn:   request.ChannelRefundSn,
				Request:    data,
				CreateTime: time.Now(),
			}
			_, err := c.session.Insert(&omsRepush)
			if err != nil {
				glog.Errorf("oms推送失败请求记录失败: %v", err)
			}
		}()
		return errors.New(msg)
	case 401:
		return errors.New(msg)
	}
	return nil
}

// 不管oms有没有推送成功都需要把库存加回去
func (c *CommonService) doRefundStock(refundSn string, orderMain *models.OrderMain, list []*dto.OrderRefundProduct) {
	detail := GetOrderDetailByOrderSn(orderMain.OrderSn, "order_sn,push_third_order")
	if detail == nil || detail.OrderSn == "" {
		glog.Errorf("oms退款单处理 查询订单详情不存在:%s %s %s", refundSn, orderMain.OrderSn, kit.JsonEncode(list))
		return
	}
	// 如果正向单没有推送到oms则不需要往下执行
	if detail.PushThirdOrder != 1 {
		glog.Infof("oms退款单处理 正向单未推送到oms直接跳过:%s %s", refundSn, orderMain.OrderSn)
		return
	}

	redisConn := GetRedisConn()
	lockKey := fmt.Sprintf("lock:awen:refund:%s", refundSn)
	now := time.Now()
	duration := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Add(time.Hour * 24).Sub(now)
	lockRes := redisConn.SetNX(lockKey, time.Now().Unix(), duration).Val()
	if !lockRes {
		glog.Infof("oms退款单处理 库存已退还直接跳过:%s %s", refundSn, orderMain.OrderSn)
		return
	}

	//把库存加回去
	inventoryConn := ic.GetInventoryServiceClient()
	inventoryRequest := new(ic.SyncOmsDiffStockRequest)
	inventoryRequest.WarehouseCode = orderMain.WarehouseCode
	for _, v := range list {
		inventoryRequest.List = append(inventoryRequest.List, &ic.SkuStock{
			ThirdSkuId: v.ItemNum,
			Stock:      v.RefundNumber,
		})
	}
	glog.Infof(refundSn, ",oms退款单处理 退还阿闻库存请求参数:%s", kit.JsonEncode(inventoryRequest))
	res, err := inventoryConn.RPC.SyncOmsDiffStock(context.Background(), inventoryRequest)
	glog.Infof(refundSn, ",oms退款单处理 退还阿闻库存返回:%s", kit.JsonEncode(res))
	if err != nil {
		glog.Errorf("oms退款单处理 退还库存异常:%+v %s %s", err, refundSn, kit.JsonEncode(inventoryRequest))
	}

}

func AddHealthVal(effectDay int, orderMain *models.OrderMain) {

	payAmount := kit.FenToYuan(orderMain.Total)

	//查询退款订单，减去退款成功的值
	refunds := GetRefundOrderByOrderSn(orderMain.OrderSn, "refund_sn,refund_amount,refund_state")
	if len(refunds) > 0 {
		for _, i := range refunds {
			if i.RefundState == 3 {
				refund := cast.ToFloat64(i.RefundAmount)
				payAmount = payAmount - refund
			}
		}
	}
	if payAmount <= 0 {
		return
	}

	model := &cc.AddUserHealthValReq{
		UserId:     orderMain.MemberId,
		HealthType: 1, // 线上消费
		Type:       3, // 冻结
		Title:      "线上消费",
		Content:    "线上消费:" + orderMain.OrderSn,
		OrderSn:    orderMain.OrderSn,
		PayAmount:  fmt.Sprintf("%.2f", payAmount),
		ShopId:     orderMain.ShopId,
		ShopName:   orderMain.ShopName,
		EffectTime: kit.GetTimeNow(time.Now().AddDate(0, 0, effectDay)),
		PayTime:    kit.GetTimeNow(orderMain.PayTime),
	}
	glog.Info("用户线上消费增加冻结健康值(AddHealthVal)请求参数：", kit.JsonEncode(model))
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	re, err := client.RPC.AddUserHealthVal(client.Ctx, model)

	if err != nil {
		glog.Error("消费更新健康值失败, error: ", err)
	}
	if re.Code != 200 {
		glog.Error("消费更新健康值失败, message: ", re.Message)
	}
}

// ShopId 是麦芽田那边的门店ID，不是财务编码
func (c *CommonService) GetMytOrderInfo(ShopId string, OrderId string) (*et.MytOrderRequest, error) {
	db := GetDBConn()
	glog.Info("获取麦芽田订单数据：", ShopId, OrderId)
	deliveryConfig := new(models.DeliveryConfig)
	_, err := db.Where("store_id = ?", ShopId).
		Where("channel_id = ?", 1).
		Where("org_id = ?", 6).
		Get(deliveryConfig)

	if err != nil {
		return nil, errors.New("查询到当前渠道的配送方式错误")
	}
	if deliveryConfig.ID == 0 {
		return nil, errors.New("未查询到当前渠道的配送方式")
	}
	if deliveryConfig.ThirdType != 1 && deliveryConfig.DeliveryMethod != 2 {
		return nil, errors.New("当前配置的不是第三方配送麦芽田")
	}
	m := models.Order{}
	have, err := db.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where(" order_main.org_id=6 and order_main.channel_id=1 and order_main.delivery_type=2 and parent_order_sn!=''").
		Where("order_main.shop_id=? and order_main.order_sn=?", deliveryConfig.FinanceCode, OrderId).
		Get(&m)
	if err != nil {
		glog.Error(OrderId, ", 查询订单总信息失败, ", err)
		return nil, errors.New("查询订单总信息失败")
	}
	if !have {
		return nil, errors.New("订单不存在：" + OrderId)
	}
	now := time.Now()

	orderSns := m.OrderSn
	var orderProducts []models.OrderProduct
	orderProductMap := map[string][]models.OrderProduct{}

	//主单商品
	err = db.Table("order_product").
		Join("inner", "order_detail", "order_product.order_sn=order_detail.order_sn").
		Where("order_product.order_sn=?", orderSns).Find(&orderProducts)
	if err != nil {
		glog.Error(OrderId, ", 查询订单商品信息失败, ", err)
		return nil, errors.New("查询订单商品信息失败")
	}

	//子订单号与商品的关系
	for _, product := range orderProducts {
		orderProductMap[product.OrderSn] = append(orderProductMap[product.OrderSn], product)
	}

	item := et.MytOrderRequest{}
	item.UpdateTime = now.Unix()
	total := cast.ToInt32(m.PayTotal)
	//订单信息
	mytOrder := et.OrderDetails{}
	mytOrder.OrderId = m.OrderSn
	mytOrder.OrderSn = cast.ToInt32(m.Id)
	mytOrder.PickupCode = m.PickupCode
	mytOrder.ShopId = ShopId
	mytOrder.Category = "chaoshi"
	mytOrder.IsPreOrder = false
	mytOrder.PickTime = m.PickingTime.Unix()
	mytOrder.UserRemark = m.BuyerMemo
	mytOrder.OriginTag = "宠财神"
	mytOrder.TotalPrice = total
	mytOrder.BalancePrice = total
	mytOrder.ShopName = m.ShopName

	//订单费用信息
	MytOrderFee := et.OrderFee{}

	MytOrderFee.TotalFee = total
	MytOrderFee.SendFee = cast.ToInt32(m.Freight)
	MytOrderFee.PackageFee = cast.ToInt32(m.PackingCost)
	MytOrderFee.ShopFee = total
	MytOrderFee.UserFee = total
	MytOrderFee.PayType = 2
	MytOrderFee.NeedInvoice = false
	//MytOrderFee.Invoice
	//MytOrderFee.Commission=0
	//MytOrderFee.Activity
	MytOrderFee.IsFirst = false
	MytOrderFee.IsFavorite = false
	mytOrder.OrderFee = &MytOrderFee

	//订单商品信息
	if Products, exists := orderProductMap[m.OrderSn]; exists {
		for _, Product := range Products {
			orderGoods := et.OrderGoods{
				GoodsId:              Product.ProductId,
				GoodsName:            Product.ProductName,
				Thumb:                Product.Image,
				SkuId:                Product.SkuId,
				Unit:                 Product.Specs,
				Weight:               int32(kit.YuanToFen(Product.WeightForUnit)), // 以克为单位
				Upc:                  Product.BarCode,
				ShelfNo:              Product.LocationCode,
				Number:               cast.ToInt32(Product.Number),
				GoodsPrice:           cast.ToInt32(Product.PayPrice),     // 以分为单位
				GoodsTotalFee:        cast.ToInt32(Product.PaymentTotal), // 总费用
				PackageNumber:        0,
				PackagePrice:         0, // 包装费用
				PackageTotalFee:      0, // 包装总费用
				ReduceFee:            0,
				DiscountFee:          0,
				DiscountPlatformFee:  0,
				DiscountMerchantFee:  0,
				DiscountAgentFee:     0,
				DiscountLogisticsFee: 0,
				TotalFee:             cast.ToInt32(Product.PaymentTotal), // 最终总费用
			}
			item.OrderGoods = append(item.OrderGoods, &orderGoods)

		}
	}

	//客户信息
	customer := et.OrderCustomer{}
	customer.OrderPhone = utils.MobileDecrypt(m.EnMemberTel)
	customer.RealName = m.ReceiverName
	customer.Phone = utils.MobileDecrypt(m.EnReceiverPhone)
	customer.ReservePhone = utils.MobileDecrypt(m.EnReceiverPhone)
	customer.SecretPhone = m.ReceiverPhone
	customer.Address = m.ReceiverAddress
	customer.Longitude = m.Lng
	customer.Latitude = m.Lat
	item.OrderCustomer = &customer

	item.Order = &mytOrder
	//jsonData, err := json.Marshal(item)
	//if err != nil {
	//	glog.Error(OrderId, ", 序列化失败, ", err)
	//	resp.Message = "序列化失败" + err.Error()
	//	return nil, err
	//}
	//resp.Data = string(jsonData)
	//resp.Message = "ok"
	//resp.Code = 200
	return &item, nil

}

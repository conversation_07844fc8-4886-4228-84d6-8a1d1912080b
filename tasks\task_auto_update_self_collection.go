package tasks

import (
	"order-center/models"
	"order-center/services"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

// 阿闻自提订单备货时间超过店铺设置时间, 自动更改备货状态
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("*/10 * * * * *", autoUpdateSelfCollection); err != nil {
		//time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func autoUpdateSelfCollection() {
	redis := services.GetRedisConn()

	lockCard := "task:lock:self_collection"
	lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redis.Del(lockCard)
	result := redis.ZRange(services.SelfCollectionPicking, 0, 30)

	db := services.GetDBConn()

	var orderList []*models.Order

	err := db.Select("order_main.*,is_picking,picking_time,accept_time,push_third_order").Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("is_virtual=0").Where("order_main.org_id!=6").
		In("order_main.order_sn", result.Val()).
		Find(&orderList)
	if err != nil {
		return
	}
	if len(orderList) == 0 {
		return
	}

	orderMap := make(map[string]*models.Order)
	for _, v := range orderList {
		orderMap[v.OrderSn] = v
	}

	var orderLogs []*models.OrderLog
	for _, v := range result.Val() {
		stockTime := int64(redis.ZScore(services.SelfCollectionPicking, v).Val())
		//增加一个逻辑判断，如果查询到的数据在map中，就继续执行
		if _, ok := orderMap[v]; !ok {
			continue
		}
		order := orderMap[v]
		if order.IsPicking == 1 {
			redis.ZRem(services.SelfCollectionPicking, v)
			continue
		}

		if order.PushThirdOrder == 0 && order.OrderStatus == 20 {
			redis.ZRem(services.SelfCollectionPicking, v)
			continue
		}
		if order.AcceptTime.Year() >= 2020 && time.Now().Unix()-order.AcceptTime.Unix() > stockTime*60 {
			_, err = db.In("order_sn", []string{order.ParentOrderSn, order.OrderSn}).Update(&models.OrderDetail{
				IsPicking:   1,
				PickingTime: time.Now(),
			})
			if err != nil {
				continue
			}
			redis.ZRem(services.SelfCollectionPicking, v)

			orderLogs = append(orderLogs, []*models.OrderLog{
				{
					OrderSn: order.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: order.ParentOrderSn,
					LogType: models.OrderLogPickedOrder,
				},
			}...)
		}
	}

	if len(orderLogs) > 0 {
		services.SaveOrderLog(orderLogs)
	}
}

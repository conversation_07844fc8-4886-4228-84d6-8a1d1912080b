package tasks

import (
	"time"

	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

//处理美团订单定时任务
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("*/10 * * * * *", func() {
		redisConn := services.GetRedisConn()

		lockCard := "task:lock:mt_submit_order_data"
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
		if !lockRes {
			return
		}
		defer redisConn.Del(lockCard)

		services.TimingProcessingMtSubmitOrder(false)
	}); err != nil {
		time.Sleep(time.Second * 30)
	} else {
		task.Start()
	}
}

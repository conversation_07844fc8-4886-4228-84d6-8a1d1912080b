package services

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"reflect"
	"testing"
)

func Test_GetOrderMainByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据订单号查询订单主信息",
			args: args{
				orderSn: "asdf",
				fields:  "*",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderMainByOrderSn(tt.args.orderSn, tt.args.fields)
			if got.Id == 0 {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderMainByOldOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据渠道订单号查询订单主信息",
			args: args{
				orderSn: "1613977023240233",
				fields:  "*",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderMainByOldOrderSn(tt.args.orderSn, tt.args.fields)
			if got.Id == 0 {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderDetailByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据订单号查询订单详情",
			args: args{
				orderSn: "4000000000670494",
				fields:  "order_sn,latitude,longitude,pickup_code",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderDetailByOrderSn(tt.args.orderSn, tt.args.fields)
			if got.OrderSn == "" {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据订单号查询订单总信息",
			args: args{
				orderSn: "1613977023240233",
				fields:  "*",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderByOrderSn(tt.args.orderSn, tt.args.fields)
			if got.Id == 0 {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderByOldOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据渠道订单号查询订单总信息",
			args: args{
				orderSn: "190671375630519165",
				fields:  "*",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderByOldOrderSn(tt.args.orderSn, tt.args.fields)
			if got.Id == 0 {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderById(t *testing.T) {
	type args struct {
		id     int64
		fields []string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据订单id查询订单总信息",
			args: args{
				id:     1,
				fields: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderById(tt.args.id, tt.args.fields...)
			if got.Id == 0 {
				t.Fatal("数据不正确")
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func Test_GetOrderByParentOrderSn(t *testing.T) {
	type args struct {
		parentOrderSn string
		fields        []string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "根据父订单号查询子订单总信息",
			args: args{
				parentOrderSn: "4000000000346656",
				fields:        []string{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetOrderByParentOrderSn(tt.args.parentOrderSn, tt.args.fields...)
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestGetChildOrderProductByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name              string
		args              args
		wantOrderProducts []models.OrderProduct
		wantErr           bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4000000001349856",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOrderProducts, err := GetChildOrderProductByOrderSn(tt.args.orderSn)
			fmt.Println(gotOrderProducts, err)
		})
	}
}

func TestGetVerifiedCountByOrderSn(t *testing.T) {
	type args struct {
		orderSn []string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: []string{
					"4000000001343348",
					"4000000001350780",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetVerifiedCountByOrderSn(tt.args.orderSn)
			fmt.Println(got, err)
		})
	}
}

func TestGetChildOrderProductByParentSku(t *testing.T) {
	type args struct {
		orderSn     string
		parentSkuId string
	}
	tests := []struct {
		name              string
		args              args
		wantOrderProducts []*models.OrderProduct
		wantErr           bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn:     "4000000001349856",
				parentSkuId: "1023048099",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOrderProducts, err := GetChildOrderProductByParentSku(tt.args.orderSn, tt.args.parentSkuId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChildOrderProductByParentSku() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOrderProducts, tt.wantOrderProducts) {
				t.Errorf("GetChildOrderProductByParentSku() gotOrderProducts = %v, want %v", gotOrderProducts, tt.wantOrderProducts)
			}
		})
	}
}

func TestGetRefundOrderByParentOrderSn(t *testing.T) {
	type args struct {
		OrderSn   string
		channelId int32
	}
	tests := []struct {
		name string
		args args
		want []*models.Order
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				OrderSn:   "4100000012555595",
				channelId: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetRefundOrderByParentOrderSn(tt.args.OrderSn, tt.args.channelId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRefundOrderByParentOrderSn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetThirdOrderProduct(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name              string
		args              args
		wantOrderProducts []models.OrderProduct
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4100000013523347",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotOrderProducts := GetThirdOrderProduct(tt.args.orderSn); !reflect.DeepEqual(gotOrderProducts, tt.wantOrderProducts) {
				t.Errorf("GetThirdOrderProduct() = %v, want %v", gotOrderProducts, tt.wantOrderProducts)
			}
		})
	}
}

func TestGetRefundOrderProductByRefundSn(t *testing.T) {
	type args struct {
		refundSn string
		fields   string
	}
	tests := []struct {
		name string
		args args
		want []*models.RefundOrderProduct
	}{
		// TODO: Add test cases.
		{
			name: "查询退款商品",
			args: args{
				refundSn: "50000002233",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetRefundOrderProductByRefundSn(tt.args.refundSn, tt.args.fields); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRefundOrderProductByRefundSn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetChildRealOrderByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  []string
	}
	tests := []struct {
		name          string
		args          args
		wantRealOrder *models.OrderMain
		wantErr       bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4100000014271308",
				//fields: []string{"order_sn"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRealOrder, err := GetChildRealOrderByOrderSn(tt.args.orderSn, tt.args.fields...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChildRealOrderByOrderSn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotRealOrder, tt.wantRealOrder) {
				t.Errorf("GetChildRealOrderByOrderSn() gotRealOrder = %v, want %v", gotRealOrder, tt.wantRealOrder)
			}
		})
	}
}

func TestGetDeliveryByOrderSn(t *testing.T) {
	type args struct {
		orderSn string
		fields  []string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.OrderDeliveryRecord
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4100000014290139",
				fields:  []string{"id,delivery_id,mt_peisong_id,order_sn,delivery_service_code"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetDeliveryByOrderSn(tt.args.orderSn, tt.args.fields...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeliveryByOrderSn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDeliveryByOrderSn() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetRealOrderByParentOrderSn(t *testing.T) {
	type args struct {
		parentOrderSn string
		fields        []string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.Order
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				parentOrderSn: "4100000014321402",
				fields:        []string{"order_sn"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetRealOrderByParentOrderSn(tt.args.parentOrderSn, tt.args.fields...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRealOrderByParentOrderSn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRealOrderByParentOrderSn() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetLastDeliveryNode(t *testing.T) {
	type args struct {
		orderSn string
		fields  []string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.OrderDeliveryRecord
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "自配送获取配送节点",
			args: args{
				orderSn: "4100000010294824",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetLastDeliveryNodeByOrderSn(tt.args.orderSn, tt.args.fields...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLastDeliveryNode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLastDeliveryNode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetLastDeliveryNodeById1(t *testing.T) {
	type args struct {
		deliveryId int64
		fields     []string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.OrderDeliveryNode
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "自配送获取配送节点1",
			args: args{
				deliveryId: 1973133814,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetLastDeliveryNodeById(tt.args.deliveryId, tt.args.fields...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLastDeliveryNodeById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLastDeliveryNodeById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCheckDeliveryOrderIsAcceptedById1(t *testing.T) {
	type args struct {
		deliveryId int64
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				deliveryId: 1503540040,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CheckDeliveryOrderIsAcceptedById(tt.args.deliveryId)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckDeliveryOrderIsAcceptedById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckDeliveryOrderIsAcceptedById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetVirtualOrderSumTotal(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4100000014526596",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetVirtualOrderSumTotal(tt.args.orderSn)
			if !tt.wantErr(t, err, fmt.Sprintf("GetVirtualOrderSumTotal(%v)", tt.args.orderSn)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetVirtualOrderSumTotal(%v)", tt.args.orderSn)
		})
	}
}

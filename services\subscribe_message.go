package services

import (
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/proto/mc"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/maybgit/glog"
)

type KeyWordData struct {
	Value string `json:"value"`
}

//app/spellgroup/spellDetails?pin_order_sn=4100000003071303
//拼团成功通知、拼团失败通知 ——点击到拼团详情

//app/mall/page/orderDetail?order_id=123
//订单状态变更通知（发货提醒，虚拟商品订单不弹）——点击到订单详情

//app/mall/refund/return-detail?type=1&returnid=1171  （退款）
//app/mall/refund/refund-detail?type=2&refundid=1154（退货退款）

func splitString(str string) string {
	var size, n int
	isLong := false
	for i := 0; i < 21 && n < len(str); i++ {
		_, size = utf8.DecodeRuneInString(str[n:])
		if n < 17 {
			n += size
		}
		if i >= 20 {
			isLong = true
		}
	}
	if isLong {
		return str[:n] + "..."
	}
	return str
}

func pushOrderStatusTemplate(param dto.PushSubscribeMessage, orderSn, status, remarks, orderId, orderType string) error {
	glog.Info("发送订单状态通知：", param.OrderSn, kit.JsonEncode(param))

	//if len(param.TemplateId) <= 0 {
	//	param.TemplateId = config.GetString("order-status-template")
	//}
	template, err := GetWechatSubscribeMsgTemplate("order-status-template", param.OrgId)
	if err != nil {
		glog.Error("获取微信消息模版异常，e=" + err.Error())
		return err
	}
	order := GetOrderMainByOrderSn(param.OrderSn)
	subscribes := GetOrderSubMessages(order.OldOrderSn, 3)
	for _, subscribe := range subscribes {
		products := GetOrderProductByOrderSn(param.OrderSn, "*")
		var productStr strings.Builder
		for i, t1 := range products {
			productStr.WriteString(t1.ProductName)
			if i != len(products)-1 {
				productStr.WriteString(",")
			}
		}
		//templateData := config.GetString("order-status-template-data")
		dataStr := fmt.Sprintf(template.Content, splitString(productStr.String()), status, orderSn, remarks)
		//是否是虚拟订单，0否1是
		Page := "app/mall/page/orderDetail?order_id=" + orderId
		if orderType == "1" {
			Page = "app/mall/page/orderDetailVr?order_id=" + orderId
		}
		err := PushSubscribeMessage(mc.SubscribeMessageRequest{
			Touser:     subscribe.OpenId,
			TemplateId: subscribe.TemplateId,
			Data:       dataStr,
			Page:       Page,
			OrgId:      param.OrgId,
		})
		if err != nil {
			glog.Error("推送订单状态订阅消息失败！ ", kit.JsonEncode(param), err.Error())
		}
	}
	return nil
}

func pushRefundSuccessTemplate(param dto.PushSubscribeMessage, refundId, refundType, refundAmount, refundTime string) error {
	glog.Info("发送退款成功通知：", param.OrderSn, kit.JsonEncode(param))
	//if len(param.TemplateId) <= 0 {
	//	param.TemplateId = config.GetString("refund-success-template")
	//}
	template, err := GetWechatSubscribeMsgTemplate("refund-success-template", param.OrgId)
	if err != nil {
		glog.Error("获取微信消息模版异常，e=" + err.Error())
		return err
	}
	subscribes := GetOrderSubMessages(param.OrderSn, 5)
	for _, subscribe := range subscribes {
		//templateData := config.GetString("refund-success-template-data")
		dataStr := fmt.Sprintf(template.Content, param.OrderSn, refundAmount, refundTime)

		req := mc.SubscribeMessageRequest{
			Touser:     subscribe.OpenId,
			TemplateId: subscribe.TemplateId,
			Data:       dataStr,
			Page:       "app/mall/refund/return-detail?returnid=" + refundId,
			OrgId:      param.OrgId,
		}
		if kit.IsDebug {
			req.MiniprogramState = "trial"
		}
		// 虚拟退款单传退款单号
		if len(refundId) >= 16 {
			req.Page = "app/mall/refund/vr/detail?refund_sn=" + refundId
			// 未发布前调整体验版
			publishedAt, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, "2022-09-20 09:00:00", time.Local)
			if time.Now().Before(publishedAt) {
				req.MiniprogramState = "trial"
			}
		}

		err := PushSubscribeMessage(req)
		if err != nil {
			glog.Error("推送订单状态订阅消息失败！ ", kit.JsonEncode(param), err.Error())
		}
	}
	return nil
}
func pushRefundFailTemplate(param dto.PushSubscribeMessage, refundSn, refundId, refundType, status, remarks string) error {
	glog.Info("发送退款失败通知：", param.OrderSn, kit.JsonEncode(param))
	//if len(param.TemplateId) <= 0 {
	//	param.TemplateId = config.GetString("refund-fail-template")
	//}

	template, err := GetWechatSubscribeMsgTemplate("refund-fail-template", param.OrgId)
	if err != nil {
		glog.Error("获取微信消息模版异常，e=" + err.Error())
		return err
	}
	subscribes := GetOrderSubMessages(param.OrderSn, 6)
	for _, subscribe := range subscribes {
		//templateData := config.GetString("refund-fail-template-data")
		dataStr := fmt.Sprintf(template.Content, status, refundSn, remarks)

		req := mc.SubscribeMessageRequest{
			Touser:     subscribe.OpenId,
			TemplateId: subscribe.TemplateId,
			Data:       dataStr,
			Page:       "app/mall/refund/return-detail?returnid=" + refundId,
			OrgId:      param.OrgId,
		}
		if kit.IsDebug {
			req.MiniprogramState = "trial"
		}
		// 虚拟退款单传退款单号
		if len(refundId) >= 16 {
			req.Page = "app/mall/refund/vr/detail?refund_sn=" + refundId
			// 未发布前调整体验版
			publishedAt, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, "2022-09-20 09:00:00", time.Local)
			if time.Now().Before(publishedAt) {
				req.MiniprogramState = "trial"
			}
		}

		err := PushSubscribeMessage(req)
		if err != nil {
			glog.Error("推送订单状态订阅消息失败！ ", kit.JsonEncode(param), err.Error())
		}
	}
	return nil
}
func pushRefundStatusTemplate(param dto.PushSubscribeMessage, refundId, status, refundType, remarks, refundAmount string) error {
	glog.Info("发送退款状态通知：", param.OrderSn, kit.JsonEncode(param))
	//if len(param.TemplateId) <= 0 {
	//	param.TemplateId = config.GetString("refund-status-template")
	//}
	template, err := GetWechatSubscribeMsgTemplate("refund-status-template", param.OrgId)
	if err != nil {
		glog.Error("获取微信消息模版异常，e=" + err.Error())
		return err
	}
	subscribes := GetOrderSubMessages(param.OrderSn, 4)
	for _, subscribe := range subscribes {
		//templateData := config.GetString("refund-status-template-data")
		dataStr := ""
		if param.OrgId == 2 { //极宠家订阅模板参数3个
			dataStr = fmt.Sprintf(template.Content, status, refundAmount, remarks)
		} else {
			dataStr = fmt.Sprintf(template.Content, status, remarks)
		}

		req := mc.SubscribeMessageRequest{
			Touser:     subscribe.OpenId,
			TemplateId: subscribe.TemplateId,
			Data:       dataStr,
			Page:       "app/mall/refund/return-detail?returnid=" + refundId,
			OrgId:      param.OrgId,
		}
		if kit.IsDebug {
			req.MiniprogramState = "trial"
		}
		// 虚拟退款单传退款单号
		if len(refundId) >= 16 {
			req.Page = "app/mall/refund/vr/detail?refund_sn=" + refundId
			// 未发布前调整体验版
			publishedAt, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, "2022-09-20 09:00:00", time.Local)
			if time.Now().Before(publishedAt) {
				req.MiniprogramState = "trial"
			}
		}

		err := PushSubscribeMessage(req)
		if err != nil {
			glog.Error("推送订单状态订阅消息失败！ ", kit.JsonEncode(param), err.Error())
		}
	}
	return nil
}

func PreSalePayTemplate(param dto.PushSubscribeMessage, startTime, endTime, remarks string, isVirtual int32) error {
	glog.Info("推送尾款支付提醒通知：", param.OrderSn, kit.JsonEncode(param))
	if len(param.TemplateId) <= 0 {
		param.TemplateId = config.GetString("pre-sale-pay-template")
	}

	subscribes := GetOrderSubscribeMessageList(param.OrderSn, param.TemplateId)
	for _, subscribe := range subscribes {
		templateData := config.GetString("pre-sale-pay-template-data")

		timeStr := startTime + "~" + endTime

		dataStr := fmt.Sprintf(templateData, timeStr, remarks)

		//是否是虚拟订单，0否1是
		Page := "app/mall/page/orderDetail?order_id=" + param.OrderSn
		if isVirtual == 1 {
			Page = "app/mall/page/orderDetailVr?order_id=" + param.OrderSn
		}
		err := PushSubscribeMessage(mc.SubscribeMessageRequest{
			Touser:     subscribe.OpenId,
			TemplateId: subscribe.TemplateId,
			Data:       dataStr,
			Page:       Page,
			OrgId:      param.OrgId,
		})
		if err != nil {
			glog.Error("推送尾款支付订阅消息失败！ ", kit.JsonEncode(param), err.Error())
		}
	}
	return nil
}

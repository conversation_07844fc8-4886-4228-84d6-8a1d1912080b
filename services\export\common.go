package export

import (
	"bytes"
	"errors"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/utils"
	"os"
)

func createStoreInfoToMap(shopIds []string) (storeMap map[string]*dac.StoreInfo, err error) {
	dcClient := dac.GetDataCenterClient()
	defer dcClient.Close()

	var res *dac.StoreInfoResponse
	storeMap = make(map[string]*dac.StoreInfo)
	if res, err = dcClient.RPC.QueryStoreInfo(dcClient.Ctx, &dac.StoreInfoRequest{
		FinanceCode: shopIds,
	}); err != nil {
		err = errors.New("调用QueryStoreInfo失败, " + err.Error())
		return
	} else if res.Code != 200 {
		err = errors.New("调用QueryStoreInfo失败, " + res.Message)
		return
	} else {
		for _, v := range res.Details {
			storeMap[v.FinanceCode] = v
		}
	}

	return
}

// 上传至oss生成下载链接公共方法
func generateDownUrl(f *excelize.File, fileName string) (url string, err error) {
	//将数据存入buff中
	var buff bytes.Buffer
	if err = f.Write(&buff); err != nil {
		return "", errors.New("excel写入buffer失败, " + err.Error())
	}
	var name = "" //kit.GetGuid36() + ".xlsx"
	if fileName != "" {
		name = fileName + ".xlsx"
	} else {
		name = kit.GetGuid36() + ".xlsx"
	}

	//生成文件
	fd, err := os.Create(name)
	if err != nil {
		return "", errors.New("文件创建失败, " + err.Error())
	}

	defer os.Remove(name)
	defer fd.Close()

	if err = f.Write(fd); err != nil {
		return "", errors.New("excel写入文件失败, " + err.Error())
	}

	//同步文件到七牛云
	url, err = utils.UploadExcelToQiNiu(name)
	if err != nil {
		return "", errors.New("文件上传失败, " + err.Error())
	}

	return url, nil
}

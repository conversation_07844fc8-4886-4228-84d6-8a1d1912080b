package tasks

import (
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/services"
	"time"

	"github.com/maybgit/glog"
)

type TaskReleaseStock struct {
	services.BaseService
}

//定时任务发配送
func (s TaskReleaseStock) TaskReleaseStock() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:release_stock"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	db := services.GetDBConn()

	now := time.Now()
	m, _ := time.ParseDuration("12h")
	date := now.Add(m)
	expected_time := kit.GetTimeNow(date)

	var orderList []*models.OrderMain
	db.SQL(`
		select order_main.* from order_main
		inner join order_detail on order_main.order_sn=order_detail.order_sn
		left join order_exception on order_exception.order_sn = order_main.order_sn
		channel_id != 5 AND order_type = 2 AND order_status = '20' AND push_delivery = '0' AND delivery_type = 2 AND push_third_order = 1 AND ISNULL(order_exception.order_sn) AND expected_time <= ? AND expected_time > ?
		`, expected_time, now).
		Find(&orderList)

	for _, orderMain := range orderList {
		func() {
			session := db.NewSession()
			defer session.Close()
			glog.Info("自动释放未支付订单的库存！", orderMain.OldOrderSn)
			if orderMain.ChannelId == services.ChannelElmId {
				if orderMain.LogisticsCode == "6" {
					services.PushMpOrder(session, orderMain)
				}
			} else {
				services.PushMpOrder(session, orderMain)
			}
		}()
	}
}

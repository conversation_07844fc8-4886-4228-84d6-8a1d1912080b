package models

import "time"

//拼团处理异步回调通知
type PinOrderNotify struct {
	Id               int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	NotifyType    int32     `xorm:"not null default 0 comment('推送类型1:支付回调 2:退款回调 3:推送订单中心 4：推送电商') INT(1)"`
	PinOrderSn      string    `xorm:"default '' comment('拼团订单编号') VARCHAR(36)"`
	DataJson         string    `xorm:"not null default '''' comment('内容数据') TEXT"`
	DealStatus int32     `xorm:"not null default 0 comment('处理状态') TINYINT(4)"`
	DealNum int32     `xorm:"not null default 0 comment('处理状态') INT(11)"`
	Reason    string    `xorm:"default '' comment('原因') VARCHAR(255)"`
	CreateTime time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME created"`
	UpdateTime time.Time `xorm:"default 'NULL' comment('更新时间') DATETIME updated"`
}


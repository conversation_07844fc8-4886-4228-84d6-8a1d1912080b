package services

import (
	"encoding/json"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/ic"
	"order-center/proto/oc"
	"reflect"
	"testing"
	"time"

	"github.com/spf13/cast"

	"github.com/ppkg/kit"
	"github.com/stretchr/testify/assert"
	"xorm.io/xorm"
)

func TestTool(t *testing.T) {
	//conn, err := grpc.Dial("api.rp-pet.com:11005", grpc.WithInsecure())
	//if err != nil {
	//	t.Log(err)
	//	return
	//}
	//defer conn.Close()
	//client := oc.NewOrderServiceClient(conn)
	//request := &oc.AccomplishOrderRequest{
	//	OrderSn:     "1599787608804467",
	//	ConfirmTime: time.Now().Format(kit.DATETIME_LAYOUT),
	//}
	//grpcRes, err := client.AccomplishOrder(context.Background(), request)
	//if err != nil {
	//	t.Log(err)
	//	return
	//}
	//
	//t.Log(grpcRes)
	//配送费，包装费，退款金额，退款商品金额，差额, 配送费优惠
	var PackingCost, RefundAmount, GoodsPayTotal, sumFreightPrivilege, Difference, Freight float64
	PackingCost = 0.1
	GoodsPayTotal = 4.1
	sumFreightPrivilege = 2
	activityPtAmount := "1.11"
	Freight = 0
	RefundAmount = kit.FenToYuan(kit.YuanToFen(GoodsPayTotal + PackingCost + cast.ToFloat64(activityPtAmount) - sumFreightPrivilege))
	fmt.Println(RefundAmount)

	//计算差额
	Difference = GetDifference(RefundAmount, Freight, PackingCost, GoodsPayTotal)
	fmt.Println(Difference)

}

//计算差额(商家应收金额-商品实付金额-运费-包装费)
func GetDifference(actulyPayed float64, freight float64, packingCost float64, goodsPayTotal float64) float64 {

	ret := kit.YuanToFen(actulyPayed) - (kit.YuanToFen(goodsPayTotal) + kit.YuanToFen(freight) + kit.YuanToFen(packingCost))
	return kit.FenToYuan(ret)
}

/*func TestCommonService_PushZiLongRefundOrderUf(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		RefundGoodsOrders []*oc.RefundGoodsOrder
		orderModel        *models.OrderMain
		RefundSn          string
		isCancel          int
		allRefund         int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "订单完成前推送子龙退款",
			args: args{
				RefundGoodsOrders: []*oc.RefundGoodsOrder{
					{
						GoodsId:      "1010308001",
						Quantity:     1,
						Id:           "22ed86b3fc07471ca228ccaa36e354d9",
						RefundAmount: "1",
					},
				},
				orderModel: &models.OrderMain{
					ShopId: "RP0318",
					//GyOrderSn: "27010243966418726",
					OrderSn: "1606358143707992",
				},
				RefundSn:  "40180434509",
				allRefund: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommonService{
				BaseService: tt.fields.BaseService,
			}
			if err := c.PushZiLongRefundOrderUf(tt.args.RefundGoodsOrders, tt.args.RefundSn, tt.args.allRefund); (err != nil) != tt.wantErr {
				t.Errorf("PushZiLongRefundOrderUf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}*/

func TestCommonService_OrderSynchronizeNew(t *testing.T) {
	type args struct {
		params *dto.Orders
	}
	tests := []struct {
		name    string
		args    args
		want    *dto.ReOrders
		wantErr bool
	}{
		{
			name: "电商订单添加",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var stockParam = dto.OrderParam{}
			var stockParam333 = dto.OrderParam{}
			var stockdetliParam = dto.OrderDetailsParam{}
			var stockdetliParam333 = dto.OrderDetailsParam{}
			var orders = dto.Orders{}

			stockdetliParam.Oid = "10002982121" //必填
			// stockdetliParam.Barcode = "0101011" //商品条码
			stockdetliParam.Eshopgoodsid = "AODY005" //必填  商家ID，对应全聚到本地商品后，可以自动选择商品
			//stockdetliParam.Outeriid = "11001" // 网店商家编码se
			//stockdetliParam.Eshopgoodsname = "周翔测试商品"
			//stockdetliParam.Eshopskuid = "11001"   //有SKU才传，不然就无法自动选中了
			//stockdetliParam.Numiid = "009"
			//stockdetliParam.Skuid = "009"
			stockdetliParam.Num = "1"
			stockdetliParam.Payment = "0"
			//stockdetliParam.Unitid = "1"
			//stockdetliParam.Picpath = "1212"
			//stockdetliParam.Weight = "4.3"
			//stockdetliParam.Size = "6.5"
			//stockdetliParam.Unitqty = ""

			stockdetliParam333.Oid = "A012345111"
			stockdetliParam333.Barcode = "1000004"      //商品条码
			stockdetliParam333.Eshopgoodsid = "AODY005" //商家ID，对应全聚到本地商品后，可以自动选择商品
			stockdetliParam333.Outeriid = "AODY005"     // 网店商家编码
			stockdetliParam333.Eshopgoodsname = "测试商品1"
			//stockdetliParam1.Eshopskuid = "ADDLS01"   //有SKU才传，不然就无法自动选中了
			//stockdetliParam1.Numiid = "1000004"
			//stockdetliParam1.Skuid = "1000004"
			stockdetliParam333.Num = "50"
			stockdetliParam333.Payment = "200.5"
			//stockdetliParam1.Unitid = "1"
			//stockdetliParam1.Picpath = "1212"
			//stockdetliParam1.Weight = "4.3"
			//stockdetliParam1.Size = "6.5"
			//stockdetliParam1.Unitqty = ""

			stockParam.Tid = "1586662732223124"
			//stockParam.Weight = "5.63"
			//stockParam.Size = "8"
			//stockParam.Buyernick = "zhouxiang"
			//stockParam.Buyermessage = "卖家留言"
			stockParam.Sellermemo = "AWSCSZSGC001" //必填，需要匹配仓库  在全聚到里面的 系统--仓库规则设置，设置好仓库
			stockParam.Total = "0"                 //计算
			stockParam.Privilege = "0"
			stockParam.Postfee = "0"
			stockParam.Receivername = "测试"
			stockParam.Receiverstate = "北京"
			stockParam.Receivercity = "北京市"
			stockParam.Receiverdistrict = "东城区"
			stockParam.Receiveraddress = "收货地址"
			//stockParam.Receiverphone = "13826142230"
			stockParam.Receivermobile = "1336895959"
			stockParam.Created = time.Now().Format("2006/01/02 15:04:05")
			stockParam.Status = "Payed" //已付款订单
			stockParam.Type = "NoCod"   //订单类型（Cod=货到付款, NoCod=非货到付款）
			//stockParam.Logistbtypecode="ZTO"
			//stockParam.Invoicename = "新锐派"
			//stockParam.Sellerflag = "0"
			//stockParam.Paytime = "2019-08-05 11:08:24"

			stockParam333.Tid = "15866627322231923"
			//stockParam.Weight = "5.63"
			//stockParam.Size = "8"
			//stockParam.Buyernick = "zhouxiang"
			//stockParam.Buyermessage = "卖家留言"
			stockParam333.Sellermemo = "AWSCSZSGC001" //必填，需要匹配仓库  在全聚到里面的 系统--仓库规则设置，设置好仓库
			stockParam333.Total = "200.5"             //计算
			stockParam333.Privilege = "0"
			stockParam333.Postfee = "0"
			stockParam333.Receivername = "测试"
			stockParam333.Receiverstate = "北京"
			stockParam333.Receivercity = "北京市"
			stockParam333.Receiverdistrict = "东城区"
			stockParam333.Receiveraddress = "收货地址"
			//stockParam.Receiverphone = "13826142230"
			stockParam333.Receivermobile = "1336895959"
			stockParam333.Created = time.Now().Format("2006/01/02 15:04:05")
			stockParam333.Status = "Payed" //已付款订单
			stockParam333.Type = "NoCod"   //订单类型（Cod=货到付款, NoCod=非货到付款）
			//stockParam.Logistbtypecode="ZTO"
			//stockParam.Invoicename = "新锐派"
			//stockParam.Sellerflag = "0"
			//stockParam.Paytime = "2019-08-05 11:08:24"

			//stockParam.Details = append(stockParam.Details, stockdetliParam)
			stockParam333.Details = append(stockParam333.Details, stockdetliParam333)
			//orders.Orders = append(orders.Orders, stockParam)
			orders.Orders = append(orders.Orders, stockParam333)

			c := CommonService{}
			got, err := c.OrderSynchronizeNew(&orders)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderSynchronizeNew() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderSynchronizeNew() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCommonService_GetOrderPromotion(t *testing.T) {
	type fields struct {
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
		BaseService BaseService
	}
	tests := []struct {
		name                string
		fields              fields
		wantOrderPromotions []models.OrderPromotion
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommonService{
				//session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
				BaseService: tt.fields.BaseService,
			}
			if gotOrderPromotions := c.GetOrderPromotion(); !reflect.DeepEqual(gotOrderPromotions, tt.wantOrderPromotions) {
				t.Errorf("GetOrderPromotion() = %v, want %v", gotOrderPromotions, tt.wantOrderPromotions)
			}
		})
	}
}

func TestCommonService_PushThirdOrder(t *testing.T) {
	type fields struct {
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
		BaseService BaseService
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "PushThirdOrder",
			fields: fields{
				//session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000014290139"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommonService{
				//session:   tt.fields.session,
				orderMain: tt.fields.orderMain,
			}
			defer c.session.Close()
			if err := c.PushThirdOrder(false); (err != nil) != tt.wantErr {
				t.Errorf("PushThirdOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCommonService_AllChannelRefundNotice(t *testing.T) {
	type fields struct {
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
		BaseService BaseService
	}
	type args struct {
		retorder_id string
		has         bool
		resultType  int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				//session:     GetDBConn().NewSession(),
				orderMain:   GetOrderMainByOrderSn("4100000015529131"),
				orderDetail: GetOrderDetailByOrderSn("4100000015529131"),
			},
			args: args{
				retorder_id: "50000005465",
				has:         false,
				resultType:  1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//c := CommonService{
			//	session:     tt.fields.session,
			//	orderMain:   tt.fields.orderMain,
			//	orderDetail: tt.fields.orderDetail,
			//	BaseService: tt.fields.BaseService,
			//}
			//if err := c.AllChannelRefundNotice(tt.args.retorder_id, tt.args.has, tt.args.resultType, false); (err != nil) != tt.wantErr {
			//	t.Errorf("AllChannelRefundNotice() error = %v, wantErr %v", err, tt.wantErr)
			//}
		})
	}
}

func TestCommonService_GetOrderProduct(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	type args struct {
		fields []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "查询订单商品信息",
			fields: fields{
				//session: GetDBConn().NewSession(),
				orderMain: &models.OrderMain{
					OrderSn: "1602781951086758",
				},
			},
			args: args{fields: []string{""}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			gotOrderProducts := c.GetOrderProduct(tt.args.fields...)
			if len(gotOrderProducts) == 0 {
				t.Fatal("数据不正确")
			}

		})
	}
}

func TestCommonService_OrderLockInventory(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "OrderLockInventory",
			fields: fields{
				//session: GetDBConn().NewSession(),
				orderMain: &models.OrderMain{
					WarehouseCode: "",
					OrderSn:       "4000000000086183",
					ChannelId:     1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if err := c.OrderLockInventory(); (err != nil) != tt.wantErr {
				t.Errorf("OrderLockInventory() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCommonService_OrderExceptionAotuArrive(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "",
			fields: fields{
				//session: GetDBConn().NewSession(),
				orderMain: &models.OrderMain{
					OrderSn:       "",
					ParentOrderSn: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if err := c.OrderExceptionAotuArrive(); err != nil {
				t.Errorf("OrderExceptionAotuArrive() error = %v", err)
			}
		})
	}
}

/*func TestCommonService_SaveCancelRefundOrder(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	type args struct {
		RefundSn     string
		CancelReason string
		UserName     string
		isBusiness   int
		oldRefundSn  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			//glog.Info("保存取消订单退款单", c.orderMain.OrderSn, ",",RefundSn, ",",CancelReason, ",",UserName,",", isBusiness,"," ,oldRefundSn)
			//4100000013930125商品已售完yangy1
			//770671233880805289,***********,没有备注,,0,
			name: "保存取消订单退款单",
			args: args{
				RefundSn:     "***********",
				CancelReason: "没有备注",
				UserName:     "没有备注",
				isBusiness:   0,
				oldRefundSn:  "",
			},
			fields: fields{
				session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000012514086"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if err := c.SaveCancelRefundOrder(tt.args.RefundSn, tt.args.CancelReason, tt.args.UserName, tt.args.isBusiness, tt.args.oldRefundSn, 0, ""); (err != nil) != tt.wantErr {
				t.Errorf("SaveCancelRefundOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}*/
func TestFreezeStock(t *testing.T) {
	//freezeParams := &ic.FreezeRequest{
	//	OrderId: "4100000966161629",
	//	Source:  2,
	//}
	//freezeParams.GoodsList = append(freezeParams.GoodsList, &ic.OrderGoodsInfo{
	//	GoodsId:       "1018127001",
	//	Number:        2,
	//	WarehouseId:   1286,
	//	WarehouseType: 0,
	//})
	//freezeParams.GoodsList = append(freezeParams.GoodsList, &ic.OrderGoodsInfo{
	//	GoodsId:       "1000271001",
	//	Number:        1,
	//	WarehouseId:   1286,
	//	WarehouseType: 0,
	//})

	//freezeParams := &ic.FreezeRequest{
	//	OrderId: "4100000966212676",
	//	Source:  2,
	//}
	//freezeParams.GoodsList = append(freezeParams.GoodsList, &ic.OrderGoodsInfo{
	//	GoodsId:       "100166",
	//	Number:        1,
	//	WarehouseId:   70,
	//	WarehouseType: 0,
	//})

	//client :=
	//_, err := client.RPC.FreezeStock(client.Ctx, freezeParams)

	var params ic.FreedStockRequest
	params.OrderId = "4100000966161629"
	params.Source = 2
	params.GoodsList = append(params.GoodsList, &ic.OrderGoodsInfo{
		GoodsId:       "1018127001",
		Number:        2,
		WarehouseId:   1286,
		WarehouseType: 0,
	})
	params.GoodsList = append(params.GoodsList, &ic.OrderGoodsInfo{
		GoodsId:       "1000271001",
		Number:        1,
		WarehouseId:   1286,
		WarehouseType: 0,
	})

	//icClient := micro.NewGrpcClient(ic.InventoryCenter, ic.NewInventoryServiceClient)
	//_, err := icClient.FreedStock(micro.Context(), &params)

	//if err != nil {
	//	fmt.Println(err)
	//	return
	//}
	fmt.Println("sussess")
}

/*func TestCommonService_PushZiLongRefundOrder(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	type args struct {
		RefundGoodsOrders []*oc.RefundGoodsOrder
		RefundSn          string
		isCancel          int
		allRefund         int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if err := c.PushZiLongRefundOrder(tt.args.RefundGoodsOrders, tt.args.RefundSn, tt.args.isCancel, tt.args.allRefund); (err != nil) != tt.wantErr {
				t.Errorf("CommonService.PushZiLongRefundOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}*/

/*func TestCommonService_RePushToZiLongTrigger(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	type args struct {
		rePushType        int
		refundOrderRepush *dto.RefundOrderParameter
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "",
			fields: fields{
				BaseService: BaseService{},
				session:     GetDBConn().NewSession(),
				orderMain: &models.OrderMain{
					// ShopId:     "CX0004",
					// OldOrderSn: "4100000010879099",
					OrderSn: "4100000010897283",
				},
				orderDetail: &models.OrderDetail{},
			},
			args: args{
				rePushType:        dto.RePush_OrderFinished_OrderNotExist,
				refundOrderRepush: &dto.RefundOrderParameter{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if got := c.RePushToZiLongTrigger(tt.args.rePushType, 0, tt.args.refundOrderRepush); got != tt.want {
				t.Errorf("CommonService.RePushToZiLongTrigger() = %v, want %v", got, tt.want)
			}
		})
	}
}*/

func TestCommonService_FinalizeOrder(t *testing.T) {
	type fields struct {
		BaseService BaseService
		session     *xorm.Session
		orderMain   *models.OrderMain
		orderDetail *models.OrderDetail
	}
	type args struct {
		params *oc.AccomplishOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				params: &oc.AccomplishOrderRequest{},
			},
			fields: fields{
				orderMain: GetOrderMainByOrderSn("4100000013545974"),
				//session:   GetDBConn().NewSession(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:     tt.fields.session,
				orderMain:   tt.fields.orderMain,
				orderDetail: tt.fields.orderDetail,
			}
			if err := c.FinalizeOrder(tt.args.params); (err != nil) != tt.wantErr {
				t.Errorf("FinalizeOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCommonService_MpOrderCreate(t *testing.T) {
	type fields struct {
		BaseService  BaseService
		session      *xorm.Session
		orderMain    *models.OrderMain
		orderDetail  *models.OrderDetail
		diagnoseInfo *models.DiagnoseInfo
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			//50000130455,990671233880337866,990671233880337866,0,1
			name: "",
			fields: fields{
				BaseService: BaseService{},
				//session:     GetDBConn().NewSession(),
				orderMain:   GetOrderMainByOrderSn("9964128298534530"),
				orderDetail: GetOrderDetailByOrderSn("9964128298534530"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:      tt.fields.session,
				orderMain:    tt.fields.orderMain,
				orderDetail:  tt.fields.orderDetail,
				diagnoseInfo: tt.fields.diagnoseInfo,
			}
			if err := c.MpOrderCreate(); (err != nil) != tt.wantErr {
				t.Errorf("CommonService.MpOrderCreate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCommonService_GetRefundGoods(t *testing.T) {
	type fields struct {
		BaseService  BaseService
		session      *xorm.Session
		orderMain    *models.OrderMain
		orderDetail  *models.OrderDetail
		diagnoseInfo *models.DiagnoseInfo
	}
	type args struct {
		RefundSn    string
		cancelType  int
		hasRefundSn int
	}
	tests := []struct {
		name                  string
		fields                fields
		args                  args
		wantRefundGoodsOrders []*oc.RefundGoodsOrder
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				RefundSn:    "50000005416",
				cancelType:  1,
				hasRefundSn: 1,
			},
			fields: fields{
				//session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000015468282"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:      tt.fields.session,
				orderMain:    tt.fields.orderMain,
				orderDetail:  tt.fields.orderDetail,
				diagnoseInfo: tt.fields.diagnoseInfo,
			}
			if gotRefundGoodsOrders := c.GetRefundGoods(tt.args.RefundSn, tt.args.cancelType, tt.args.hasRefundSn); !reflect.DeepEqual(gotRefundGoodsOrders, tt.wantRefundGoodsOrders) {
				t.Errorf("GetRefundGoods() = %v, want %v", gotRefundGoodsOrders, tt.wantRefundGoodsOrders)
			}
		})
	}
}

func TestCommonService_PushRPOms(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				//session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000016726707"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			c.PushRPOmsOrder()
			//tt.wantErr(t, c.PushRPOms(), fmt.Sprintf("PushRPOms()"))
		})
	}
}

func TestCommonService_PushRPOmsRefund(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	str := `[{"id":"1803246","goods_id":"1033230001","quantity":5,"refund_amount":"3.00","oc_id":"1803246","barcode":"A5899633688","refundorderid":"","food_name":"","spec":"","refund_price":0,"box_price":0,"box_num":0,"tkcount":0,"food_price":0,"selnumber":0,"promotion_id":0,"sku_id":"","order_product_id":1803246}]`
	var product []*oc.RefundGoodsOrder
	_ = json.Unmarshal([]byte(str), &product)
	type args struct {
		orderMain    *models.OrderMain
		refundSn     string
		refundGoods  []*oc.RefundGoodsOrder
		RefundReason string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderMain:    GetOrderMainByOrderSn("4100000015513745"),
				refundSn:     "50000005454",
				refundGoods:  product,
				RefundReason: "asdf",
			},
			fields: fields{
				//session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000015513745"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			tt.wantErr(t, c.PushRPOmsRefund(tt.args.orderMain, tt.args.refundSn, tt.args.refundGoods, tt.args.RefundReason), fmt.Sprintf("PushRPOmsRefund(%v, %v, %v, %v)", tt.args.orderMain, tt.args.refundSn, tt.args.refundGoods, tt.args.RefundReason))
		})
	}
}

func TestCommonService_RepushRefundOrderToZiLongAfterPushOrderSuccess(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	type args struct {
		orderSn string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn: "4100000014603082",
			},
			fields: fields{
				//session: GetDBConn().NewSession(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			c.RePushRefundOrderToZiLongAfterPushOrderSuccess()
		})
	}
}

func TestCommonService_PushZilong(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	type args struct {
		retry bool
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantRetCode int
		wantErr     assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				retry: false,
			},
			fields: fields{
				//session:   GetDBConn().NewSession(),
				orderMain: GetOrderMainByOrderSn("4100000014298219"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			gotRetCode, err := c.PushZilong(tt.args.retry)
			if !tt.wantErr(t, err, fmt.Sprintf("PushZilong(%v)", tt.args.retry)) {
				return
			}
			assert.Equalf(t, tt.wantRetCode, gotRetCode, "PushZilong(%v)", tt.args.retry)
		})
	}
}

func TestCommonService_RePushToZiLongTrigger(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	type args struct {
		mqContent *dto.MqRePushOrderToZiLong
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				mqContent: &dto.MqRePushOrderToZiLong{
					Count:    0,
					Type:     1,
					OrderSn:  "234234",
					RefundSn: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			assert.Equalf(t, tt.want, c.RePushToZiLongTrigger(tt.args.mqContent), "RePushToZiLongTrigger(%v)", tt.args.mqContent)
		})
	}
}

func TestCommonService_PushZiLongRefundOrder(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
	}
	type args struct {
		refundParam *dto.RefundOrderParameter
		retry       bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				orderMain: GetOrderMainByOrderSn("4100000014603082"),
				//session:   GetDBConn().NewSession(),
			},
			args: args{
				refundParam: &dto.RefundOrderParameter{
					RefundGoodsOrders: []*oc.RefundGoodsOrder{
						&oc.RefundGoodsOrder{
							GoodsId: "1111",
						},
					},
				},
				retry: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService: tt.fields.BaseService,
				//session:       tt.fields.session,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
			}
			tt.wantErr(t, c.PushZiLongRefundOrder(tt.args.refundParam, tt.args.retry, GetDBConn().NewSession()), fmt.Sprintf("PushZiLongRefundOrder(%v, %v)", tt.args.refundParam, tt.args.retry))
		})
	}
}

func TestCommonService_RePushToZiLongTrigger1(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
		DeliverInfo   *dto.DeliverPriceRes
	}
	type args struct {
		mqContent *dto.MqRePushOrderToZiLong
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
		{name: "11"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonService{
				BaseService:   tt.fields.BaseService,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
				DeliverInfo:   tt.fields.DeliverInfo,
			}
			sss := dto.MqRePushOrderToZiLong{}
			sss.OrderSn = "111"
			sss.Type = 1
			sss.Count = 0
			assert.Equalf(t, tt.want, c.RePushToZiLongTrigger(&sss), "RePushToZiLongTrigger(%v)", tt.args.mqContent)
		})
	}
}

func TestCommonService_AotuPushDelivery(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		session       *xorm.Session
		orderMain     *models.OrderMain
		orderDetail   *models.OrderDetail
		diagnoseInfo  *models.DiagnoseInfo
		orderProducts []*models.OrderProduct
		DeliverInfo   *dto.DeliverPriceRes
	}
	type args struct {
		orderSn     string
		deliverInfo dto.DeliverPriceRes
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "测试发配送"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommonService{
				BaseService:   tt.fields.BaseService,
				session:       nil,
				orderMain:     tt.fields.orderMain,
				orderDetail:   tt.fields.orderDetail,
				diagnoseInfo:  tt.fields.diagnoseInfo,
				orderProducts: tt.fields.orderProducts,
				DeliverInfo:   tt.fields.DeliverInfo,
			}
			tt.wantErr(t, c.AotuPushDelivery("9964128298400329", tt.args.deliverInfo), fmt.Sprintf("AotuPushDelivery(%v, %v)", tt.args.orderSn, tt.args.deliverInfo))
		})
	}
}

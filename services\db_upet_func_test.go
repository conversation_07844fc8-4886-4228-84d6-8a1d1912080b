package services

import (
	"fmt"
	"order-center/models"
	"reflect"
	"testing"
)

func TestGetUPetConfig(t *testing.T) {
	type args struct {
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				key: "distribute_isuse",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetConfig(tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.<PERSON>rf("GetUPetConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUPetGoodsCommonInfo(t *testing.T) {
	type args struct {
		commonId int
		fields   string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.UpetGoodsCommon
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				commonId: 1000041,
				fields:   "is_dis,dis_commis_rate,goods_name",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetGoodsCommonInfo(tt.args.commonId, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetGoodsCommonInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPetGoodsCommonInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUPetGoodsInfo(t *testing.T) {
	type args struct {
		goodId int64
		fields string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.UpetGoods
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				goodId: 107463,
				fields: "goods_id,is_virtual,goods_salenum,goods_storage",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetGoodsInfo(tt.args.goodId, tt.args.fields, 1)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetGoodsInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPetGoodsInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUPetMemberInfo(t *testing.T) {
	type args struct {
		memberId int32
		fields   string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.UpetMember
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				memberId: 128064,
				fields:   "member_id,member_name",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetMemberInfo(tt.args.memberId, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetMemberInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPetMemberInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDisMemberInfo(t *testing.T) {
	type args struct {
		memberId int32
	}
	tests := []struct {
		name    string
		args    args
		want    *models.DisMemberInfo
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				memberId: 103819,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetDisMemberInfo(tt.args.memberId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDisMemberInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDisMemberInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAreaIdByName(t *testing.T) {
	type args struct {
		name string
	}
	tests := []struct {
		name       string
		args       args
		wantAreaId int
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				name: "江苏省",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotAreaId := GetAreaIdByName(tt.args.name); gotAreaId != tt.wantAreaId {
				t.Errorf("GetAreaIdByName() = %v, want %v", gotAreaId, tt.wantAreaId)
			}
		})
	}
}

func TestGetOutMemberInfo(t *testing.T) {
	type args struct {
		memberId int32
	}
	tests := []struct {
		name string
		args args
		want int32
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				memberId: 103819,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetOutMemberInfo(tt.args.memberId); got != tt.want {
				t.Errorf("GetOutMemberInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUPetMemberIdByScrmUserId(t *testing.T) {
	type args struct {
		scrmUserId string
		fields     string
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				scrmUserId: "1f3f117c2abc432dbde5edf17c76e0c2",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetMemberInfoByScrmUserId(tt.args.scrmUserId, tt.args.fields)
			fmt.Println(got, err)
			/*if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetMemberIdByScrmUserId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUPetMemberIdByScrmUserId() got = %v, want %v", got, tt.want)
			}*/
		})
	}
}

func TestGetUPetVirtualGoodsInfo(t *testing.T) {
	type args struct {
		goodId int64
		fields string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.UpetGoods
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				goodId: 1023432003,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetVirtualGoodsInfo(tt.args.goodId, tt.args.fields, 1)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetVirtualGoodsInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPetVirtualGoodsInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetUPetGoodsSpec(t *testing.T) {
	type args struct {
		specName  string
		goodsSpec string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				specName:  "a:1:{i:1;s:6:\"种类\";}",
				goodsSpec: "a:1:{i:3;s:17:\"小型成犬1.5KG\";}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			str, got := GetUPetGoodsSpec(tt.args.specName, tt.args.goodsSpec)
			fmt.Println(str, got)
		})
	}
}

func TestGetUPetAddress(t *testing.T) {
	type args struct {
		id     int32
		fields string
	}
	tests := []struct {
		name    string
		args    args
		want    *models.UpetAddress
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				id: 3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUPetAddress(tt.args.id, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPetAddress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPetAddress() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUnSerializeInfo(t *testing.T) {
	type args struct {
		value string
	}
	tests := []struct {
		name    string
		args    args
		wantStr []string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				value: "a:3:{i:6;a:2:{s:4:\"name\";s:12:\"适合犬型\";i:0;s:6:\"不限\";}i:7;a:2:{s:4:\"name\";s:12:\"适合犬种\";i:0;s:6:\"不限\";}i:8;a:2:{s:4:\"name\";s:12:\"适合犬龄\";i:0;s:6:\"不限\";}}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotStr, err := UnSerializeInfo(tt.args.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnSerializeInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotStr, tt.wantStr) {
				t.Errorf("UnSerializeInfo() gotStr = %v, want %v", gotStr, tt.wantStr)
			}
		})
	}
}

func TestUnSerializeAttrInfo(t *testing.T) {
	type args struct {
		value string
	}
	tests := []struct {
		name       string
		args       args
		wantResMap map[string]string
		wantErr    bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				value: "a:3:{i:6;a:2:{s:4:\"name\";s:12:\"适合犬型\";i:0;s:6:\"不限\";}i:7;a:2:{s:4:\"name\";s:12:\"适合犬种\";i:0;s:6:\"不限\";}i:8;a:2:{s:4:\"name\";s:12:\"适合犬龄\";i:0;s:6:\"不限\";}}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotResMap, err := UnSerializeAttrInfo(tt.args.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnSerializeAttrInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResMap, tt.wantResMap) {
				t.Errorf("UnSerializeAttrInfo() gotResMap = %v, want %v", gotResMap, tt.wantResMap)
			}
		})
	}
}

func TestGetStorePlateInfoByID(t *testing.T) {
	type args struct {
		plateId int
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				plateId: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetStorePlateInfoByID(tt.args.plateId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStorePlateInfoByID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetStorePlateInfoByID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

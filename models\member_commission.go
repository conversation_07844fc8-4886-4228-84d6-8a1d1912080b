package models

import (
	"time"
)

type MemberCommission struct {
	MemberId          string    `xorm:"not null pk comment('用户唯一标识') CHAR(16)"`
	CommissionCash    int       `xorm:"default 0 comment('佣金总额(分)') INT(11)"`
	CommissionFrozen  int       `xorm:"default 0 comment('佣金冻结(分)') INT(11)"`
	CommissionCashout int       `xorm:"default 0 comment('佣金已提现总额(分)') INT(11)"`
	UpdateTime        time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

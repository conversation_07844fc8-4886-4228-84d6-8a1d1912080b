// Code generated by protoc-gen-go. DO NOT EDIT.
// source: base/base.proto

package base

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//基础公用的数据结构
//app客户端信息
type ClientInfo struct {
	//设备序列号
	DeviceSeries string `protobuf:"bytes,1,opt,name=deviceSeries,proto3" json:"deviceSeries"`
	//设备品牌
	DeviceBrand string `protobuf:"bytes,2,opt,name=deviceBrand,proto3" json:"deviceBrand"`
	//设备操作系统
	Os string `protobuf:"bytes,3,opt,name=os,proto3" json:"os"`
	//设备操作系统版本号
	OsVersion string `protobuf:"bytes,4,opt,name=osVersion,proto3" json:"osVersion"`
	//网络
	Network string `protobuf:"bytes,5,opt,name=network,proto3" json:"network"`
	//网络运营商
	NetworkOperator string `protobuf:"bytes,6,opt,name=networkOperator,proto3" json:"networkOperator"`
	//分辨率
	Resolution string `protobuf:"bytes,7,opt,name=resolution,proto3" json:"resolution"`
	//产品版本号
	Version string `protobuf:"bytes,8,opt,name=version,proto3" json:"version"`
	//基础库版本
	SdkVersion string `protobuf:"bytes,9,opt,name=sdkVersion,proto3" json:"sdkVersion"`
	//基础库版本
	AppletVersion string `protobuf:"bytes,10,opt,name=appletVersion,proto3" json:"appletVersion"`
	//基础库版本
	OpenId               string   `protobuf:"bytes,11,opt,name=openId,proto3" json:"openId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientInfo) Reset()         { *m = ClientInfo{} }
func (m *ClientInfo) String() string { return proto.CompactTextString(m) }
func (*ClientInfo) ProtoMessage()    {}
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_d66ec2e140567106, []int{0}
}

func (m *ClientInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientInfo.Unmarshal(m, b)
}
func (m *ClientInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientInfo.Marshal(b, m, deterministic)
}
func (m *ClientInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientInfo.Merge(m, src)
}
func (m *ClientInfo) XXX_Size() int {
	return xxx_messageInfo_ClientInfo.Size(m)
}
func (m *ClientInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClientInfo proto.InternalMessageInfo

func (m *ClientInfo) GetDeviceSeries() string {
	if m != nil {
		return m.DeviceSeries
	}
	return ""
}

func (m *ClientInfo) GetDeviceBrand() string {
	if m != nil {
		return m.DeviceBrand
	}
	return ""
}

func (m *ClientInfo) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *ClientInfo) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *ClientInfo) GetNetwork() string {
	if m != nil {
		return m.Network
	}
	return ""
}

func (m *ClientInfo) GetNetworkOperator() string {
	if m != nil {
		return m.NetworkOperator
	}
	return ""
}

func (m *ClientInfo) GetResolution() string {
	if m != nil {
		return m.Resolution
	}
	return ""
}

func (m *ClientInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *ClientInfo) GetSdkVersion() string {
	if m != nil {
		return m.SdkVersion
	}
	return ""
}

func (m *ClientInfo) GetAppletVersion() string {
	if m != nil {
		return m.AppletVersion
	}
	return ""
}

func (m *ClientInfo) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d66ec2e140567106, []int{1}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*ClientInfo)(nil), "base.ClientInfo")
	proto.RegisterType((*BaseResponse)(nil), "base.BaseResponse")
}

func init() { proto.RegisterFile("base/base.proto", fileDescriptor_d66ec2e140567106) }

var fileDescriptor_d66ec2e140567106 = []byte{
	// 276 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x91, 0xc1, 0x4a, 0xc3, 0x40,
	0x10, 0x86, 0x69, 0x4c, 0x52, 0x33, 0xad, 0x16, 0x06, 0x91, 0x3d, 0x88, 0x94, 0xe0, 0xa1, 0x27,
	0x3d, 0xf8, 0x06, 0xf5, 0xd4, 0x93, 0x10, 0xc1, 0xfb, 0xb6, 0x19, 0x25, 0x34, 0xee, 0x2c, 0x3b,
	0x6b, 0x7d, 0x59, 0x1f, 0x46, 0x76, 0xb3, 0xa1, 0xad, 0x97, 0x65, 0xfe, 0x6f, 0xfe, 0xe1, 0x1f,
	0x66, 0x61, 0xb1, 0xd5, 0x42, 0x4f, 0xe1, 0x79, 0xb4, 0x8e, 0x3d, 0x63, 0x1e, 0xea, 0xfa, 0x37,
	0x03, 0x78, 0xe9, 0x3b, 0x32, 0x7e, 0x63, 0x3e, 0x18, 0x6b, 0x98, 0xb7, 0x74, 0xe8, 0x76, 0xf4,
	0x46, 0xae, 0x23, 0x51, 0x93, 0xe5, 0x64, 0x55, 0x35, 0x67, 0x0c, 0x97, 0x30, 0x1b, 0xf4, 0xda,
	0x69, 0xd3, 0xaa, 0x2c, 0x5a, 0x4e, 0x11, 0x5e, 0x43, 0xc6, 0xa2, 0x2e, 0x62, 0x23, 0x63, 0xc1,
	0x3b, 0xa8, 0x58, 0xde, 0xc9, 0x49, 0xc7, 0x46, 0xe5, 0x11, 0x1f, 0x01, 0x2a, 0x98, 0x1a, 0xf2,
	0x3f, 0xec, 0xf6, 0xaa, 0x88, 0xbd, 0x51, 0xe2, 0x0a, 0x16, 0xa9, 0x7c, 0xb5, 0xe4, 0xb4, 0x67,
	0xa7, 0xca, 0xe8, 0xf8, 0x8f, 0xf1, 0x1e, 0xc0, 0x91, 0x70, 0xff, 0xed, 0x43, 0xc4, 0x34, 0x9a,
	0x4e, 0x48, 0xc8, 0x38, 0xa4, 0xfc, 0xcb, 0x21, 0x23, 0xc9, 0x30, 0x29, 0xed, 0x7e, 0x5c, 0xae,
	0x1a, 0x26, 0x8f, 0x04, 0x1f, 0xe0, 0x4a, 0x5b, 0xdb, 0x93, 0x1f, 0x2d, 0x10, 0x2d, 0xe7, 0x10,
	0x6f, 0xa1, 0x64, 0x4b, 0x66, 0xd3, 0xaa, 0x59, 0x6c, 0x27, 0x55, 0x37, 0x30, 0x5f, 0x6b, 0xa1,
	0x86, 0xc4, 0xb2, 0x11, 0x42, 0x84, 0x7c, 0xc7, 0x2d, 0xc5, 0xbb, 0x16, 0x4d, 0xac, 0xc3, 0x6e,
	0x5f, 0x24, 0xa2, 0x3f, 0x29, 0xdd, 0x72, 0x94, 0x78, 0x03, 0x05, 0x39, 0xc7, 0x2e, 0x9d, 0x72,
	0x10, 0xdb, 0x32, 0xfe, 0xdf, 0xf3, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x2e, 0x61, 0x3d, 0x04,
	0xd2, 0x01, 0x00, 0x00,
}

package ac

import (
	"context"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type DcActivityCenterClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  ActivityServiceClient
	GB   GroupBuyServiceClient //拼团
	CB   CycleBuyServiceClient //周期购
	SK   SeckillServiceClient  //秒杀
	BB   BookBuyServiceClient  //预售
	NB   NewBuyServiceClient   //新人专享
}

func GetDcActivityCenterClient() *DcActivityCenterClient {
	var client DcActivityCenterClient
	var err error
	url := config.GetString("grpc.activity-center")
	if url == "" {
		url = "127.0.0.1:7074"
	}

	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Errorf("GetDcActivityCenterClient 获取连grpc接异常,err:%+v", err)
		return nil
	}
	client.RPC = NewActivityServiceClient(client.Conn)
	client.GB = NewGroupBuyServiceClient(client.Conn)
	client.CB = NewCycleBuyServiceClient(client.Conn)
	client.BB = NewBookBuyServiceClient(client.Conn)
	client.SK = NewSeckillServiceClient(client.Conn)
	client.NB = NewNewBuyServiceClient(client.Conn)
	client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
	return &client
}

//关闭链接
func (s *DcActivityCenterClient) Close() {
	if s == nil {
		return
	}
	if s.Conn != nil {
		s.Conn.Close()
	}
	if s.Cf != nil {
		s.Cf()
	}
}

syntax = "proto3";

package ac;

service SeckillService {
  // 创建秒杀活动
  rpc CreateSeckill(SeckillRequest) returns (SeckillResponse);

  // 获取秒杀活动分页列表（boss后台管理用）
  rpc GetSeckillPagingList(SeckillListRequest) returns (PromotionListResponse);
  // 秒杀商品导出
  rpc SeckillProductExport(SeckillProductExportReq) returns (SeckillProductExportRes);
  // 编辑秒杀活动
  rpc UpdateSeckill(SeckillRequest) returns (SeckillResponse);
  // 停止秒杀活动
  rpc StopSeckill(StopSeckillRequest) returns (SeckillResponse);
  // 停止秒杀活动
  rpc ShowSeckill(ShowSeckillRequest) returns (SeckillResponse);
  // 获取秒杀活动详情
  rpc GetSeckillDetail(GetSeckillDetailRequest) returns (Promotion);

  // 获取秒杀商品列表
  rpc GetSeckillProductList(GetSeckillProductListRequest) returns(GetSeckillProductListResponse);
  // 秒杀活动商品详细信息
  rpc GetSeckillProductDetail(GetSeckillProductDetailRequest) returns(GetSeckillProductDetailResponse);
  // 创建或者编辑秒杀活动商品
  rpc CreateOrUpdateSeckillProduct(CreateOrUpdateSeckillProductRequest) returns (SeckillResponse);

  // 删除秒杀活动商品信息
  rpc DeleteSeckillProduct(DeleteSeckillProductRequest) returns (SeckillResponse);
  // 获取可以参加秒杀活动的阿闻商城的商品
  rpc GetSeckillUPetProductSelectList(GetUPetProductSelectListRequest) returns (GetUPetProductSelectListResponse);

  // 用户端-获取秒杀活动列表
  rpc SeckillListForCustomer(SeckillListCRequest) returns (SeckillListCResponse);
  // 用户端-获取秒杀商品列表
  rpc SeckillProductListForCustomer(SeckillProductListCRequest) returns(SeckillProductListCResponse);
  // 用户端-获取sku同级（相同spu）的商品列表
  rpc SeckillProductSameLevelSkuList(SeckillProductSkuListRequest) returns(SeckillProductSkuListResponse);
  // 获取秒杀活动实时库存
  rpc GetSeckillProductStock(GetSeckillProductStockRequest) returns (GetSeckillProductStockResponse);
  // 改变小程序用户的秒杀订阅状态
  rpc SeckillProductNotice(SeckillProductNoticeRequest) returns (SeckillProductNoticeResponse);

  // 获取电商商品信息
  rpc GetSeckillUpetGoods(GetSeckillUpetGoodsRequest) returns (GetSeckillUpetGoodsResponse);
  // 最近的秒杀活动
  rpc RecentSeckillList(RecentSeckillListCRequest) returns (RecentSeckillListCResponse);
}

// 获取秒杀活动实时库存 请求
message GetSeckillProductStockRequest{
  // 秒杀活动id
  int32 promotion_id = 1;
  // 商品sku id
  int32 sku_id = 2;
}
// 获取秒杀活动实时库存 响应
message GetSeckillProductStockResponse{
  CommonResponse common = 1;
  int32 stock_surplus = 2;
}

// 改变小程序用户的秒杀订阅状态 请求
message SeckillProductNoticeRequest{
  // 秒杀活动id
  int32 promotion_id = 1;
  // 商品sku id
  int32 sku_id = 2;
  // 小程序user_id
  string user_id = 3;
  // 小程序open_id
  string open_id = 4;
  // 操作类型 1:订阅 2:取消
  int32 type = 5;
}
// 改变小程序用户的秒杀订阅状态 响应
message SeckillProductNoticeResponse{
  CommonResponse common = 1;
}


message SeckillResponse{
  int64 affect_rows = 1;
}
// 获取可以参加秒杀活动的阿闻商城的商品
message GetUPetProductSelectListRequest{
  // 秒杀活动id
  int32 promotion_id = 1;
  // 商品名称
  string product_name = 2;
  // 商品的产品id
  int32 spu_id = 3;
  // 商品sku id
  int32 sku_id = 4;
  // 当前多少页 从1开始
  int32 page_index = 5;
  // 每页多少条数据
  int32 page_size = 6;
  // 主体：1-默认，2-极宠家
  int32 org_id = 7;
}
// 获取可以参加秒杀活动的阿闻商城的商品
message GetUPetProductSelectListResponse{
  repeated SelectUPetProductData data = 1;
  int32 total = 2;
}
// 阿闻电商参加周期购活动的商品信息
message SelectUPetProductData{
  // 产品 spu id
  int32 spu_id = 1;
  // 商品 sku id
  int32 sku_id = 2;
  // 产品名称
  string product_name = 3;
  // 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
  int32 time_conflict = 4;
  // 商品图片
  string  pic = 5;
  // 库存
  int32 stock = 6;
  // 价格 单位分
  int32 market_price = 7;
  // 是否时虚拟产品 1是 0否
  int32 is_virtual = 8;
  // 是否与其他活动有时间上的冲突，有冲突的活动名称
  string time_conflict_desc = 9;
  //商品类型
  int32 goods_type = 10;
  //组合商品的子商品skuI
  repeated SeckillChildRen childSkuIds = 24;
}
//组合商品子商品讯息
message SeckillChildRen {
  //商品ID
  int32 sku_id = 1;
  //规则
  int32 rule_num = 2;
  //是否为虚拟 0:不是 1：是虚拟
  int32 is_virtual = 3;
  //0不是药品仓 1药品仓
  int32 stock_warehouse = 7;
}

// 删除秒杀活动商品信息
message DeleteSeckillProductRequest{
  int32 id = 1;
}

// 创建或编辑秒杀活动商品
message CreateOrUpdateSeckillProductRequest{
  // 产品spu id
  int32 spu_id = 1;
  // 商品 sku id
  int32 sku_id = 2;
  // 秒杀价（单位分）
  int32 price = 3;
  // 活动库存
  int32 stock = 4;
  // 活动id
  int32 promotion_id = 5;
  // 渠道id
  int32 channel_id = 6;
  // 产品名称
  string product_name = 7;
  // 商品原价
  int32 market_price = 8;
  //org_id  1-默认 2-极宠家
  int32 org_id = 9;
}

message PromotionProduct{
  // 活动产品id
  int32 id = 1 ;
  // 产品spuid
  int32 spu_id = 2;
  // 商品skuId
  int32 sku_id = 3;
  // 活动id
  int32 promotion_id = 4;
  // 活动类型  11-秒杀
  int32 types = 5;
  // 渠道
  int32 channel_id = 6;
  // 商品名称
  string product_name = 7;
  // 上架下架:0-下架 1-上架
  int32 up_down_state = 8;
  // 秒杀价
  int32 seckill_price = 9;
  // 原价
  int32 market_price = 10;
  // 采购价
  int32 purchase_price = 21;
  // 秒杀实时剩余库存
  int32 stock_surplus = 11;
  // 商品图片
  string product_img = 12;
  // 订阅状态，如果入参有user_id 返回该字段
  int32 sub_state = 13;

  // 秒杀总库存
  int32 seckill_stock = 16;
  // 创建日期
  string create_time = 17;
  // 最后更新时间
  string update_time = 18;
  // 折扣率
  string discount_rate = 22;
  // 是否异常 1:异常 0：正常
  int32 is_exception = 19;
  // 标记状态 0不显示按钮、1显示标记非异常、2显示取消标记
  int32 mark_state = 20;
}

// 获取秒杀活动列表
message SeckillListRequest{
  // 活动id列表，用逗号隔开
  string cids = 1;
  // 活动状态：-3待提交 -2待审核 1未开始 2进行中 3已结束 4已终止
  int32 status = 2;
  // 活动名称
  string title = 3;
  // 当前多少页,从1开始
  int32 page_index = 4;
  // 每页多少条数据
  int32 page_size = 5;
  // 排序顺序：1-创建时间倒序，2-创建时间升序,3-活动开始时间倒序，4-活动开始时间升序
  int32 order_by = 6;
  //活动id
  int32 Id = 7;
}
// 获取秒杀活动列表
message PromotionListResponse {
  // 总的条数
  int32 total = 1;
  // 活动列表
  repeated Promotion data = 2;
}

message Promotion {
  // 活动id
  int32 id = 1;
  // 渠道id 多个用逗号隔开
  string channel_id = 2;
  // 活动标题
  string title = 3;
  // 活动类型 11-秒杀
  int32 types = 4;
  // 活动状态 -3:待提交 -2:待审核 1-未开始 2-进行中 3-已结束
  int32 status = 5;

  // 状态文本：待提交、待审核、未开始、进行中、已结束、已终止（status=3 && is_system_end=1）
  string status_text = 6;
  // 是否系统结束 0否 1-是
  int32 is_system_end = 7;
  // 活动开始时间
  string begin_time = 8;
  // 活动截止时间
  string end_time = 9;
  //单笔购买上限
  int32 seckill_order_limit = 10;
  // 最后更新人Id
  string update_user_id = 11;
  // 最后更新人姓名
  string update_user_name = 12;
  // 最后更新时间
  string update_time = 13;
  // 创建人
  string create_user_id = 14;
  // 创建日期
  string create_time = 15;
  // 创建人名称
  string create_user_name = 16;
  //是否显示 1 显示 0 不显示
  int32 is_show = 17;
  // 是否包邮
  int32 is_shipping_free = 18;
  // 异常商品计数
  int32 exception_count = 19;
  // 总商品数量
  int32 total_count = 20;
  // 审核理由
  string check_reason = 21;
  //单用户该活动下单上限
  int32 seckill_order_cnt = 22;
}

// 创建/编辑秒杀活动
message SeckillRequest{
  // 活动id
  int32 id = 1;
  // 活动名称
  string title = 2;
  // 活动开始时间
  string begin_time = 3;
  // 活动结束时间
  string end_time = 4;
  // 购买上限
  int32 seckill_order_limit = 5;
  // 是否免邮费 0否1是
  int32 is_shipping_free = 6;
  //用户Id，即userno
  string user_id = 7;
  // 用户名称
  string user_name = 8;
  // 是否显示 1是 0 否
  int32 is_show = 9;
  //单用户该活动下单上限
  int32 seckill_order_cnt = 10;
}
// 停止秒杀活动
message StopSeckillRequest{
  // 秒杀活动ID
  int32 id = 1;
  //用户Id，即userno
  string user_id = 2;
  // userName,即登录人姓名
  string user_name = 3;
}

// 展示或者隐藏秒杀活动
message ShowSeckillRequest{
  // 秒杀活动ID
  int32 id = 1;
  // 是否显示 1 显示 0 不显示
  int32 is_show = 2;
  //用户Id，即userno
  string user_id = 3;
  // userName,即登录人姓名
  string user_name = 4;
}

// 获取秒杀活动详情
message GetSeckillDetailRequest{
  int32 id = 1;
}

// 获取秒杀商品列表
message GetSeckillProductListRequest{
  //  活动id
  int32 promotion_id = 1;
  // 商品名称
  string product_name = 2;
  // 商品spu id
  int32 spu_id = 3;
  // 商品sku id
  int32 sku_id = 4;
  // 当前多少页,从1开始
  int32 page_index = 5;
  // 每页多少条数据
  int32 page_size = 6;
  // 排序顺序：1-创建时间倒叙，2-创建时间升序
  int32 order_by = 7;
  // 小程序用户ID，后台管理获取秒杀商品列表不需要该字段
  string user_id = 8;
  // 1异常商品
  int32 type = 10;
}
// 获取秒杀商品列表
message GetSeckillProductListResponse{
  // 总的条数
  int32 total = 1;
  repeated PromotionProduct data = 2;
}
// 秒杀活动商品详细信息
message GetSeckillProductDetailRequest{
  int32 id = 1;
  //活动id 用于不知道id的情况 通过活动id与sku进行查询
  int32 promotion_id = 2;
  // 活动商品skuId 用于不知道id的情况 通过活动id与sku进行查询
  int64 sku_id = 3;
}
// 秒杀活动商品详细信息
message GetSeckillProductDetailResponse{
  PromotionProduct data = 1;
}



// 用户端-获取秒杀活动列表 Request
message SeckillListCRequest{
  // 活动id列表，用逗号隔开
  string cids = 1;
}

// 获取最近秒杀活动
message RecentSeckillListCRequest{
}

// 用户端-获取秒杀活动列表 Response
message SeckillListCResponse {
  CommonResponse common = 1;
  // 总的条数
  int32 total = 2;
  // 活动列表
  repeated Seckill data = 3;
}

// 用户端-获取最近秒杀活动 Response
message RecentSeckillListCResponse {
  CommonResponse common = 1;
  // 活动id列表 都好分割
  repeated int32 data = 2;
}

message Seckill {
  // 活动id
  int32 cid = 1;
  // 活动标题
  string title = 2;
  // 活动状态 1-未开始 2-进行中 3-已结束
  int32 status = 3;
  // 活动开始时间
  string begin_time = 4;
  // 活动截止时间
  string end_time = 5;
}



// 用户端-获取sku同级（相同spu）的商品列表 Request
message SeckillProductSkuListRequest{
  //  活动id
  int32 cid = 1;
  int32 sku_id = 2;
  int32 spu_id = 3;
}
// 用户端-获取sku同级（相同spu）的商品列表 Response
message SeckillProductSkuListResponse{
  CommonResponse common = 1;
  repeated SecKillProduct data = 2;
}


// 用户端-获取秒杀商品列表 Request
message SeckillProductListCRequest{
  //  活动id,多个用逗号隔开
  string cids = 1;
  // 小程序用户ID
  string user_id = 2;
  // 小程序OpenID
  string open_id = 3;
  // // 商品spu id
  // int32 spu_id = 4;
  // // 商品sku id
  // int32 sku_id = 5;
  // 当前多少页,从1开始
  int32 page_index = 4;
  // 每页多少条数据
  int32 page_size = 5;
}
// 用户端-获取秒杀商品列表 Response
message SeckillProductListCResponse{
  CommonResponse common = 1;
  // 总的条数
  int32 total = 2;
  repeated SecKillProduct data = 3;
}
message SecKillProduct{
  // 活动id
  int32 cid = 1 ;
  // 产品spuid
  int32 spu_id = 2;
  // 商品skuId
  int32 sku_id = 3;
  // 商品名称
  string product_name = 4;
  // 秒杀价
  int32 seckill_price = 5;
  // 原价
  int32 original_price = 6;
  // 秒杀实时剩余库存
  int32 stock_surplus = 7;
  // 秒杀总库存
  int32 stock_total = 8;
  // 商品图片
  string product_img = 9;
  // 订阅状态，如果入参有user_id 返回该字段
  int32 sub_state = 10;

  // 活动名称
  string title = 11;
  // 单笔购买上限
  int32 seckill_order_limit = 12;
  // 当前时间
  string now_date = 13;
  // 活动开始时间
  string begin_date = 14;
  // 活动结束时间
  string end_date = 15;

}

message CommonResponse {
  RetCode code = 1;
  string msg = 2;
}

enum RetCode {
  UNKNOWN = 0; // The first enum value must be zero in proto3
  SUCCESS = 200;
  ERROR = 400;
  InvalidParams = 1001;
}

message GetSeckillUpetGoodsRequest {
  // 商品skuId
  int32 sku_id = 3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}

message GetSeckillUpetGoodsResponse {
  SelectUPetProductData data = 1;
}

message SeckillProductExportReq {
  // 秒杀活动id
  int32 id = 1;
}

message SeckillProductExportRes {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 文件字节流
  bytes file = 3;
}


syntax = "proto3";

package ac;

import "ac/lucky_draw.proto";
import "ac/activity_model.proto";

service LuckyDrawService {
    //抽奖组件-活动列表
    rpc LuckyDrawActivityList(LuckyDrawActivityRequest) returns (LuckyDrawActivityListRes);
    //抽奖组件-活动详情
    rpc LuckyDrawActivityInfo(LuckyDrawActivityInfoReq) returns (LuckyDrawActivityInfoRes);
    //抽奖组件-编辑或新增活动
    rpc EditLuckyDrawActivity(LuckyDrawActivity) returns (BaseResponseNew);

    //抽奖组件-小程序获取抽奖活动内容
    rpc MiniLuckDrawActivityInfo(MiniLuckyDrawRequest) returns (MiniLuckyDrawActivityResponse);
    //抽奖组件-小程序获取用户抽奖情况
    rpc MiniLuckDrawInfo(MiniLuckyDrawRequest) returns (MiniLuckDrawInfoResponse);
    //抽奖组件-抽奖
    rpc LuckDrawStart(MiniLuckyDrawRequest) returns (MiniLuckDrawStartResponse);
    //抽奖组件-分享
    rpc LuckDrawShare(MiniLuckyDrawRequest) returns (BaseResponseNew);
    //抽奖组件-抽奖记录
    rpc WinningRecordList(WinningRecordReq) returns (WinningRecordRes);
    //抽奖组件-收货地址
    rpc LuckyDrawAddress(LuckyDrawAddressReq) returns (BaseResponseNew);

    //抽奖组件-推荐或者取消到积分商城
    rpc LuckyDrawActivityRecommend(LuckyDrawActivityRecommendReq) returns (LuckyDrawActivityRecommendResponse);

}
package tasks

import (
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/services"
	"time"
)

//释放在库库存
//仅删除数据库中在途库存的数据  并不释放redis库存
//非未支付的订单都进行释放
//需求背景：目前发现线上还有很多几个月之前的在途库存没有释放 不确定是什么地方出现了释放失败导致 故用任务进行释放
// 每天执行一次
//@version v5.6.8
func init() {
	if !kit.EnvCanCron() {
		return
	}
	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 0 04 */1 * *", func() {
		redisConn := services.GetRedisConn()
		lockCard := "task:lock:freed_locked_stock"
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Hour).Val()
		if !lockRes {
			return
		}
		defer redisConn.Del(lockCard)
		freedStock()
	}); err != nil {
		time.Sleep(time.Second * 30)
	} else {
		task.Start()
	}
}

//查询48 小时之前的在途库存 如果满足删除的条件  则进行删除
func freedStock() {
	glog.Info("执行清理在途库存任务")
	endTime := time.Now().AddDate(0, 0, -2).Format(kit.DATETIME_LAYOUT)
	db := services.GetDBConn()
	session := db.NewSession()
	defer session.Close()
	//从订单数据里获取 order_sn 与 渠道
	//查询订单
	var freezeStocks []*models.OrderFreezeStock
	//查询强制走主库 查询
	err := session.Select("order_sn,MIN(create_time) create_time").Where("create_time <= ?", endTime).GroupBy("order_sn").Find(&freezeStocks)
	if err != nil {
		glog.Error(", 查询订单在途库存失败, ", err, ", ", kit.RunFuncName(2))
		return
	}

	if len(freezeStocks) == 0 {
		return
	}
	type check struct {
		PushThirdOrder int32
		OrderStatus    int32
	}

	var intDays = -60
	confDays, _ := config.Get("delete_freed_locked_days")
	intConfDays := cast.ToInt(confDays)
	//判断是否小于-2 暂时不允许删除48小时之后的数据 避免配置错误导致数据被误删
	if intConfDays <= -2 {
		intDays = intConfDays
	}
	//已支付 但推送第三方失败情况下 可删除的订单时间 往前推intDays天
	compareTime := time.Now().AddDate(0, 0, intDays)

	for _, v := range freezeStocks {
		checkRes := new(check)
		//查询是否满足删除条件
		//删除条件1： 订单状态为已取消 已完成 订单状态为已支付 且推送第三方成功
		has, err := session.Table("order_main").
			Select("order_main.order_status,order_detail.push_third_order").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			Where("order_main.order_sn = ?", v.OrderSn).Get(checkRes)
		if err != nil {
			glog.Error(v.OrderSn, "清理在途库存任务-查询订单信息出错", err)
			continue
		}
		if !has {
			//订单不存在 则进行删除
			glog.Info(v.OrderSn, "清理在途库存任务-查询订单信息失败")
			_, err = session.Delete(&models.OrderFreezeStock{
				OrderSn: v.OrderSn,
			})
			if err != nil {
				glog.Error(v.OrderSn, "清理在途库存任务-订单不存在的在途库存删除出错", err)
			}
			continue
		}
		//未支付不删除
		if checkRes.OrderStatus == 10 {
			continue
		}

		//是否可被删除
		//已取消 已完成可被删除，已支付 且推送第三方成功可被删除  已支付推送第三方失败
		//在一定时间（delete_freed_locked_days）之前可删除 之后不删除
		//也即只要不是：已支付 且 推送第三方未成功 且 锁库存的时间在compareTime之后  都可以删除
		if !(checkRes.OrderStatus == 20 && checkRes.PushThirdOrder == 0 && v.CreateTime.After(compareTime)) {
			glog.Info(v.OrderSn, "清理在途库存任务-查询订单信息", kit.JsonEncode(v), kit.JsonEncode(checkRes), compareTime)
			_, err = session.Delete(&models.OrderFreezeStock{
				OrderSn: v.OrderSn,
			})
			if err != nil {
				glog.Error(v.OrderSn, "清理在途库存任务-删除在途库存失败", err)
				continue
			}
		}
	}
}

package tasks

import (
	"order-center/models"
	"order-center/proto/et"
	"order-center/services"
	"testing"

	"github.com/go-xorm/xorm"
)

func TestTaskLogisticsReSync_LogisticsReSync(t *testing.T) {
	type fields struct {
		SyncData    *models.OrderLogisticsSync
		Session     *xorm.Session
		EtClient    *et.Client
		BaseService services.BaseService
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name:   "美团信息回传任务",
			fields: fields{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := TaskLogisticsReSync{
				SyncData:    tt.fields.SyncData,
				EtClient:    tt.fields.EtClient,
				Session:     tt.fields.Session,
				BaseService: tt.fields.BaseService,
			}
			s.LogisticsReSync()
		})
	}
}

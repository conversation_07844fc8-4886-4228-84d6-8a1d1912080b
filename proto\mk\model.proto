syntax = "proto3";

package mk;

// 星期定义
enum weekDay {
    Sunday = 0;
    Monday = 1;
    Tuesday = 2;
    Wednesday = 3;
    Thursday = 4;
    Friday = 5;
    Saturday = 6;
}

// 查询状态
enum promotionState {
    zero = 0; // 无
    progress = 1; // 进行中
    wait = 2; // 待生效
    finished = 3; // 已结束
}
// 渠道类型
enum channelType {
    //不限制渠道
    NotLimit = 0;
    //阿闻管家
    Upet = 1;
    // 美团
    Meituan = 2;
    // 饿了么
    Ele = 3;
    // 京东到家
    JdDaojia = 4;
}

// 响应代码 0 代表成功，其他代表错误
enum code {
    default = 0;
    // 成功
    success = 200;
    // 服务端异常
    serverException = 400;
    // 当前用户没有权限
    userNotAuthority = 201;
    // grpc链接异常
    grpcConnectionError = 404;
    // 参数错误
    parameterError = 403;
    // 业务错误
    businessError = 300;
    // 保存数据库错误
    saveDbException = 401;
    //查询数据库错误
    queryDbException = 402;
}

// 促销活动类型
enum promotionTypes {
    // 默认
    defaulttypes = 0;
    // 满减活动
    reachReduce = 1;
    // 限时折扣
    timeDiscount = 2;
    // 满减运费
    reachReduceDelivery = 3;
    // 社区团购
    pickup = 10;
    // 拼团活动
    group = 11;
    // 赠险
    insurance = 12;
}

// 新增活动实体dto
message promotionDto {
    // 活动名称
    string title = 1;
    // 类型 1 满减活动 2限时折扣 3 满减运费
    promotionTypes types = 7;
    // 是否启用
    bool isEnable = 2;
    //是否应用全部商品
    bool isAllProduct = 3;
    // 开始日期
    string beginDate = 4;
    // 结束日期
    string endDate = 5;
    // 活动状态 0 未知 1 进行中 2 待生效 3 已结束 4 冻结中 -- 查询的时候使用
    promotionState state = 6;
    // 循环周期 0周日 1-6分别表示周一到周六
    repeated int32 weekDays = 8;
}

// 列表查询dto
message promotionListDto {
    // id 更新时使用
    int32 id = 1;
    // 活动名称
    string title = 2;
    // 类型 1 满减活动
    promotionTypes types = 5;
    // 开始日期
    string beginDate = 3;
    // 截止日期
    string endDate = 4;
    // 概述
    string summary = 7;
    // 活动状态 0 未知 1 进行中 2 待生效 3 已结束 4 冻结中
    promotionState state = 6;
    // 循环周期 0周日 1-6分别表示周一到周六
    repeated int32 weekDays = 8;
}

// 活动使用的渠道dto
message promotionChannelDto {
    // id 更新时使用
    int32 id = 1;
    // 促销活动Id
    int32 promotionId = 2;
    // 渠道类型 0 不限制 1 阿闻管家 2 美团 3 饿了么 3 京东到家 
    int32 channelId = 3;
}

// 活动涉及的商品dto
message promotionProductDto {
    // id 更新时使用
    int32 id = 1;
    // 商品spuId
    int32 spuId=5;
    // 商品货号
    int32 productSkuId = 2;
    // 商品名称
    string productName = 3;
    // 是否参与活动 true 参与 false 不参与
    bool isSelected = 4;
    // 商品价格
    int32 price = 6;
}

// 活动享受的满减优惠dto
message promotionReduceDto {
    // 促销活动Id
    int32 promotionId = 2;
    // 最小金额
    double reachMoney = 16;
    // 减免金额
    double reduceMoney = 17;
}

// 参与活动的店铺dto
message promotionShopDto {
    // 活动id
    int32 id = 1;
    //店铺代码
    string shopId = 10;
    //店铺名称
    string shopName = 11;
}

// 参与周期dto
message promotionWeekDayDto {
    // 开始时间
    string beginTime = 10;
    // 结束时间
    string endTime = 11;
}

// 限时折扣配置
message promotionTimeDiscountDto {
    // 促销活动Id
    int32 promotionId = 7;
    // 用户类型 0 全部 1 新客户
    int32 UserType = 2;
    // 折扣类型  0 按折扣 固定活动价格
    int32 DisountType = 3;
    // 为 0 时代表折扣 为 1 代表固定价格 (统一传浮点数)
    double DiscountValue = 4;
    // 单限购 0 不限制, 非0  限制多少数量
    int32 LimitCountByOrder = 5;
    // 当日限购 0 不限制,非0 限时库存数量
    int32 LimitCountByStock = 6;
    // 配置的活动购买数量
    int32 ConfigBuyCount = 8;
    // 会员额外折扣
    double VipDiscount = 9;
}

// 满减运费
message promotionReduceDeliveryDto {
    // 促销活动Id
    int32 promotionId = 5;
    // 0 普通阶梯递减 1 最高阶梯免配送费
    int32 ReduceDeliveryType = 2;
    // 满足减免最小金额
    double ReachMoney = 3;
    // 减免金额
    double ReduceMoney = 4;
}

// 促销活动配置dto
message promotionShopConfigDto {
    // 单店每单可购买限时折扣最大sku数量
    int32 timeDiscount_MaxSkuCountByOrder = 1;
}

// 优惠计算信息dto
message promotionCalcDto {
    // 类型 1满减、2限时折扣、4会员价
    int32 promotionType = 1;
    // 促销活动
    int32 promotionId = 2;
    // 名称
    string promotionTitle = 3;
    // 金额
    int32 promotionFee = 4;
}

message promotionVipDiscountDto {
    // 折扣或价格
    int32 discountValue = 2;
}

//促销活动与SKU关联关系
message promotionSkuDto {
    // 类型 1 满减 2 限时折扣 3 满减运费 4会员折扣
    int32 types = 1;
    // 促销活动名称
    string promotionName = 2;
    // sku信息
    string skuId = 3;
    // 促销活动明细信息
    repeated promotionReduceDto reduceList = 4;
    // 限时折扣
    promotionSkuTimeDiscountDto timeDiscount = 6;
    // 促销活动Id
    int32 promotionId = 7;
    // 会员折扣
    promotionVipDiscountDto vipDiscount = 9;
    //商品快照信息
    string ProductSnapshotJson = 8;
}

    //限时折扣
    message promotionSkuTimeDiscountDto {
        // 促销活动Id
        int32 promotionId = 1;
        // 截止日期
        string endDate = 2;
        // 折扣类型  0 按折扣 固定活动价格
        int32 disountType = 3;
        // 为 0 时代表折扣 为 1 代表固定价格
        int32 discountValue = 4;
        // 单限购 0 不限制, 非0  限制多少数量
        int32 limitCountByOrder = 5;
        // 当日限购 0 不限制,非0 限时库存数量
        int32 limitCountByStock = 6;
        // 付费vip额外折扣
        int32 vipDiscount = 7;
    }

// 根据店铺查询取消活动Dto
message promotionQueryByShopIdDto {
    string shopId = 1;
    // 类型 1 满减 2 限时折扣 3 满减运费
    int32 types = 2;
    // 促销活动明细信息
    repeated promotionReduceDto reduceList = 4;
    // 满减运费
    repeated promotionReduceDeliveryDto reduceDeliveryList = 5;
    // 限时折扣
    repeated promotionTimeDiscountDto timeDiscount = 6;
}

// 计算减免金额请求的相关商品
message promotionCalcProductDto {
    // skuId
    string skuId = 1;
    // 总金额
    int64 sumMoney = 2;
    // 单价
    int64 price = 3;
    // 数量
    int64 count = 4;
    // 参与折扣的数量
    int64 discountCount = 5;
    // 促销活动Id
    int32 promotionId = 6;
    // 折扣后商品的价格
    int32 discountPrice = 7;
    // 仅vip优惠价格，用于超出限购恢复原价，同时用于标识会员价
    int32 onlyVipDiscountPrice = 9;
    // 促销活动Id
    int32 promotionType = 8;
}

// 任务列表
message promotionTaskDto {
    int32 id =1 ;
    //当前步骤 0 待开始  1 进行中 2 已完成
    int32 step=2;
    // 成功数量
    int32 pass=3;
    // 失败数量
    int32 fail=4;
    // 类型 1 满减 2 限时折扣
    int32 types=8;
    // 标题
    string title=9;
    // 结果详情excel链接
    string resultUrl = 10;
    // 结果
    string result = 11;
    // 创建用户Id
    string createUserId=5;
    // 创建用户姓名
    string createUserName=6;
    // 最后更新时间
    string updateDate=7;
}

// 任务明细Dto
message promotionTaskDetailDto {
    // 任务Id
    int32 taskId=1;
    // 活动Id
    int32 promotionId=2;
    // 店铺Id
    string shopId=3;
    // 店铺名称
    string shopName=4;
    // skuId
    string productSkuId=5;
    // 商品名称
    string productName=6;
    // 代码 0 失败 1 成功
    int32 code=7;
    // 执行结果信息
    string message=8;
    // 执行完成时间
    string updateDate=9;
}


message PromotionTime {
    // 开始时间
    string beginTime = 1;
    // 结束时间
    string endTime = 2;
}

message PromotionShop {
    // 财务编码
    string id = 1;
    // 门店名称
    string name = 2;
}
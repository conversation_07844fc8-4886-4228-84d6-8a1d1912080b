syntax = "proto3";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
package pm;

service PetMillionsAppletsService {
    //创建窝信息信息
    rpc CreateNest (CreateNestReq) returns (BaseResponse);
    //修改窝的名称
    rpc EditNestName (EditNestNameReq) returns (BaseResponse);
    //根据窝Id获取窝信息
    rpc GetNest (GetNestReq) returns (BaseResponse);
    //获取窝的列表 名称支持模糊搜索
    rpc GetNestList (GetNestListReq) returns (BaseResponse);
    //新增免疫计划
    rpc CreatePlan (CreatePlanReq) returns (BaseResponse);
    //获取所有免疫计划（不需要分页一个用户每个宠物种类最多5个，查询宠物种类必传）
    rpc GetPlanList (GetPlanListReq) returns (BaseResponse);
    //新增免疫计划记录
    rpc GetPlan (GetPlanReq) returns (BaseResponse);
    //编辑免疫计划
    rpc EditPlan (EditPlanReq) returns (BaseResponse);
    //新增免疫计划记录
    rpc CreatePlanRecord (CreatePlanRecordReq) returns (BaseResponse);
    //获取单个免疫计划内容
    rpc GetPlanRecord (GetPlanRecordReq) returns (BaseResponse);
    //获取所有免疫计划记录 （废弃）
    rpc GetPlanRecordList (GetPlanRecordListReq) returns (BaseResponse);
    //编辑免疫计划记录
    rpc EditPlanRecord (EditPlanRecordReq) returns (BaseResponse);

    //获取宠物列表.
    rpc GetPetList (GetPetListReq) returns (BaseResponse);
    //获取宠物详情
    rpc GetPet (GetPetReq) returns (BaseResponse);
    //修改宠物
    rpc EditPet (EditPetReq) returns (BaseResponse);
    //宠物的就诊记录
    rpc GetPetReferralRecords (GetPetReferralRecordsReq) returns (GetPetReferralRecordsRes);

    //批量更新宠物疫苗驱虫信息
    rpc BatchEditPet (BatchEditPetReq) returns (BaseResponse);
    //录入鼻纹信息
    rpc CreatePetImg (CreatePetImgReq) returns (BaseResponse);
    //新增交易信息
    rpc CreateDeliveryRecord (CreateDeliveryRecordReq) returns (BaseResponse);
    // 通过手机号码查询其绑定的商户列表
    rpc GetMarchentByMobile (MarchentGetByMobileRequest) returns (MarchentGetByMobileResponse);
    // 获取商户详细信息
    rpc GetMarchentInfo (MarchentGetInfoRequest) returns (MarchentGetInfoResponse);
    // 通过商户信息查看客户信息
    rpc GetMarchentDeliveryRecord (MarchentDeliveryRecordGetRequest) returns (MarchentDeliveryRecordGetResponse);
    // 调用小程序api
    rpc PostMiniAppApi (MiniAppPostApiRequest) returns (MiniAppPostApiResponse);
    // 查询未读消息列表
    rpc GetMessageList (MessageGetListRequest) returns (MessageGetListResponse);
    // 查询未读消息明细
    rpc GetMessageDetail (MessageGetDetailRequest) returns (MessageGetDetailResponse);
    // 批量更新消息为已读
    rpc ReadAllMessage (AllMessageReadRequest) returns (BaseResponse);
    // 推送微信疫苗订阅消息
    rpc SendVaccineMessage (SubscribeMessageSendRequest) returns (SubscribeMessageSendResponse);
    // 删除过时的微信订阅消息
    rpc DeleteObsoleteMessage (ObsoleteMessageDeleteRequest) returns (ObsoleteMessageDeleteResponse);
    // 获取疫苗库品牌 集合
    rpc GetVaccineBrand (GetVaccineBrandReq) returns (BaseResponse);
    //获取疫苗名称 集合
    rpc GetVaccineName (GetVaccineNameReq) returns (BaseResponse);
    //获取宠物品种 例如（阿富汗猎犬）
    rpc GetPetBreed (GetPetBreedReq) returns (BaseResponse);
    //获取宠物花色 例如（黑色）
    rpc GetPetNCount (GetPetNCountReq) returns (BaseResponse);
    //获取宠物公共花色
    rpc GetCommonPetColor (google.protobuf.Empty) returns (GetCommonPetColorRes);

    //客户宠物列表
    rpc GetPayCustomerList (PayCustomerListReq) returns (BaseResponse);
    //获取客户关联宠物累计数据
    rpc GetCustomerData (CustomerDataReq) returns (BaseResponse);
    //转诊记录
    rpc GetReferralRecords (ReferralRecordsReq) returns (BaseResponse);

    // 获取子龙宠物列表
    rpc GetScrmPets (GetScrmPetsReq) returns (BaseResponse);

    //客户建档
    rpc AddCustomerAndPets (AddCustomerAndPetsReq) returns (BaseResponse);

    //获取客户建档详情
    rpc GetCustomerDetails (GetCustomerDetailsReq) returns (BaseResponse);

    //交付客户确认
    rpc CustomerConfirm (CustomerConfirmReq) returns (BaseResponse);

    //新增客户档案（不包含宠物信息）
    rpc AddCustomer (AddCustomerReq) returns (BaseResponse);

    //是否存在客户档案
    rpc ExistCustomer (ExistCustomerReq) returns (BaseResponse);

}
//nest
message CreateNestReq {
    //窝名称
    string nest_name = 2;
    //窝code
    string nest_code = 3;
    //宠物出生日期
    string pet_birthday = 4;
    //宠物种类
    string pet_species = 5;
    //宠物品种
    string pet_breed = 6;
    //数量(公)
    int32 male_number = 7;
    //数量(母)
    int32 female_number = 8;
    //关联商户Id
    int32 marchent_id = 9;
    //免疫计划模板id
    int32 plan_id = 13;
    //第一次驱虫时间
    string first_date = 10;
    //第二次驱虫时间
    string two_date = 11;
    //第三次驱虫时间
    string three_date = 12;
    //宠物品种Id
    int32 pet_variety = 15;
    //宠物种类Id-1未知 1000猫 1001狗 1002其他
    int32 pet_kindof = 14;

}
message EditNestNameReq {
    //窝id
    int32 id = 1;
    //窝名称
    string nest_name = 2;
}
message GetNestReq {
    //窝id
    int32 id = 1;
}
message GetNestListReq {
    //窝名称
    string nest_name = 1;
    //分页、页码
    int32 page_index = 2;
    //分页、每页个数
    int32 page_size = 3;
    //关联商户Id
    int32 marchent_id = 4;
}
message CreatePlanReq {
    //宠物种类,例如(猫)
    string pet_species = 1;
    //关联商户Id
    int32 marchent_id = 2;
    //计划模板名称
    string plan_name = 3;
    //首次疫苗品牌
    string first_vaccine_brand = 4;
    //首次疫苗名称
    string first_vaccine_name = 5;
    //首次疫苗间隔天数
    int32 first_interval_days = 6;
    //第二次疫苗品牌
    string two_vaccine_brand = 7;
    //第二次疫苗名称
    string two_vaccine_name = 8;
    //第二次疫苗间隔天数
    int32 two_interval_days = 9;
    //第三次疫苗品牌
    string three_vaccine_brand = 10;
    //第三次疫苗名称
    string three_vaccine_name = 11;
    //第三次疫苗间隔天数
    int32 three_vaccine_days = 12;
    //宠物种类Id-1未知 1000猫 1001狗 1002其他
    int32 pet_kindof = 13;

}

message EditPlanReq {
    //宠物种类,例如(猫)
    string pet_species = 1;
    //关联商户Id
    int32 marchent_id = 2;
    //计划模板名称
    string plan_name = 3;
    //首次疫苗品牌
    string first_vaccine_brand = 4;
    //首次疫苗名称
    string first_vaccine_name = 5;
    //首次疫苗间隔天数
    int32 first_interval_days = 6;
    //第二次疫苗品牌
    string two_vaccine_brand = 7;
    //第二次疫苗名称
    string two_vaccine_name = 8;
    //第二次疫苗间隔天数
    int32 two_interval_days = 9;
    //第三次疫苗品牌
    string three_vaccine_brand = 10;
    //第三次疫苗名称
    string three_vaccine_name = 11;
    //第三次疫苗间隔天数
    int32 three_vaccine_days = 12;
    //免疫计划Id
    int32 id = 13;
}

message CreatePlanRecordReq {
    //宠物种类
    string pet_species = 1;
    //首次免疫时间
    string first_vaccine_date = 2;
    //首次免疫使用的品牌
    string first_vaccine_brand = 3;
    //首次免疫使用的名称
    string first_vaccine_name = 4;
    //首次关联疫苗库Id
    //int32 first_vaccine_id = 5;
    //第二免疫时间
    string two_vaccine_date = 6;
    //第二免疫使用的品牌
    string two_vaccine_brand = 7;
    //第二次免疫使用的名称
    string two_vaccine_name = 8;
    //第二次关联疫苗库Id
    //int32 two_vaccine_id = 9;
    //第三免疫时间
    string three_vaccine_date = 10;
    //第三免疫使用的品牌
    string three_vaccine_brand = 11;
    //第三免疫使用的名称
    string three_vaccine_name = 12;
    //关联商户Id
    int32 marchent_id = 14;
    //宠物种类Id-1未知 1000猫 1001狗 1002其他
    int32 pet_kindof = 15;
}
message EditPlanRecordReq {
    //宠物种类
    string pet_species = 1;
    //首次免疫时间
    string first_vaccine_date = 2;
    //首次免疫使用的品牌
    string first_vaccine_brand = 3;
    //首次免疫使用的名称
    string first_vaccine_name = 4;
    //首次关联疫苗库Id
    //int32 first_vaccine_id = 5;
    //第二免疫时间
    string two_vaccine_date = 6;
    //第二免疫使用的品牌
    string two_vaccine_brand = 7;
    //第二次免疫使用的名称
    string two_vaccine_name = 8;
    //第二次关联疫苗库Id
    //int32 two_vaccine_id = 9;
    //第三免疫时间
    string three_vaccine_date = 10;
    //第三免疫使用的品牌
    string three_vaccine_brand = 11;
    //第三免疫使用的名称
    string three_vaccine_name = 12;
    //第三次关联疫苗库Id
    //int32 three_vaccine_id = 13;
    //关联商户Id
    int32 marchent_id = 14;
    //id
    int32 id = 15;
}
message GetPlanReq {
    //免疫计划Id
    int32 plan_id = 1;
}
message GetPlanRecordReq {
    //免疫计划记录Id
    int32 plan_records_id = 1;
}

message GetPlanListReq {
    //关联商户Id
    int32 marchent_id = 1;
    //宠物种类
    string pet_species = 2;
    //宠物种类Id
    int32 pet_kindof = 3;
}
message GetPlanRecordListReq {
    //关联商户Id
    int32 marchent_id = 1;
    //宠物种类
    string pet_species = 2;
    //宠物种类Id
    int32 pet_kindof = 3;
}

//pet
message GetPetListReq {
    //窝Id
    int32 nest_id = 1;
}
message GetPetReq {
    //宠物Id
    int32 pet_id = 1;
}
message EditPetReq {
    //宠物Id
    int32 pet_id = 1;
    //宠物花色
    string n_count = 2;
    //宠物芯片号(需要唯一)
    string pet_code = 3;
    //首次驱虫时间
    string first_date = 4;
    //第二次驱虫时间
    string two_date = 5;
    //第二次驱虫时间
    string three_date = 6;
    //第三次驱虫时间
    string remarks = 7;
    //repeated  list 在前面加

    //免疫计划记录详情
    PlanRecords plan_records = 8;
    // repeated google.protobuf.Any  plan_records=8;

    //性别(0:其他,1:公,2母)
    int32 sex = 9;
}

message BatchEditPetReq {
    //窝id
    int32 nest_id = 1;
    //首次驱虫时间
    string first_date = 2;
    //第二次驱虫时间
    string two_date = 3;
    //第三次驱虫时间
    string three_date = 5;
    //repeated  list 在前面加
    //窝的免疫计划记录
    int32 plan_records_id = 6;
    //免疫计划记录详情
    PlanRecords plan_records = 8;
}
message CreatePetImgReq {
    //宠物Id
    int32 pet_id = 1;
    //默认头像（第一张鼻纹图片地址）
    string default_img = 2;
    //图片集合
    repeated string img_data = 3;
}

//plan
message PlanRecords {
    //宠物Id
    int32 pet_id = 1;
    //首次免疫时间
    int32 marchent_id = 2;
    //宠物芯片号(需要唯一)
    string first_vaccine_date = 3;
    //首次免疫使用的品牌
    string first_vaccine_brand = 4;
    //首次免疫使用的名称
    string first_vaccine_name = 5;
    //第二免疫时间
    string two_vaccine_date = 6;
    //第二免疫使用的品牌
    string two_vaccine_brand = 7;
    //第二次免疫使用的名称
    string two_vaccine_name = 8;
    //第三免疫时间
    string three_vaccine_date = 9;
    //第三免疫使用的品牌
    string three_vaccine_brand = 10;
    //第三免疫使用的名称
    string three_vaccine_name = 11;
    //免疫计划记录Id
    int32 plan_records_id = 12;
}
//deliveryRecordReq
message CreateDeliveryRecordReq {
    //宠物id
    int32 pet_id = 2;
    //商户id
    int32 marchent_id = 3;
    //客户姓名
    string pay_name = 4;
    //交易金额(单位元，保留整数)
    int32 pay_amount = 5;
    //客户手机号码
    string mobile = 6;
    //验证码
    string VerificationCode = 7;
}

message BaseResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    //数据条数
    int64 data_count = 3;
    //结果
    string data = 4;
}

// 请求
message MarchentGetByMobileRequest {
    // 手机号码
    string mobile = 1;
    // 微信用户的openId
    string openId = 2;
}

// 商户数据模型
message MarchentGetByMobileDto {
    // 商户id
    int32 id = 1;
    // 商户名称
    string title = 2;
    // 合作状态(0-暂停合作，1-正常合作，2-待审核)
    int32 state = 3;
    // 已经创建的窝数量
    int32 nestCount = 4;

}

// 响应
message MarchentGetByMobileResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 查询到的商户列表
    repeated MarchentGetByMobileDto data = 3;
}

// 获取商户信息
message MarchentGetInfoRequest {
    // 商户id
    int32 marchentId = 1;
}

// 商户详细信息
message MarchentGetInfoDto {
    // 名称
    string title = 1;
    // 店铺logo
    string logo = 2;
    // 店铺地址
    string address = 3;
    // 联系方式
    string telephone = 4;
    // 店铺介绍
    string remarks = 5;
}

// 获取商户信息返回
message MarchentGetInfoResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 数据
    MarchentGetInfoDto data = 3;
}


message MarchentDeliveryRecordGetRequest {
    // 商户id
    int32 marchentId = 1;
    // 分页索引
    int32 pageIndex = 2;
    // 分页大小
    int32 pageSize = 3;
}

// 商户客户信息数据模型
message MarchentDeliveryRecordDto {

    // 宠物出生日期
    string petBirthday = 1;
    // 宠物种类
    string petSpecies = 10;
    // 宠物品种
    string petBreed = 2;
    // 宠物头像
    string petPortrait = 3;
    // 宠物性别 0:公,1母
    string petSex = 4;
    // 宠物备注
    string petRemarks = 5;
    // 客户姓名
    string customerName = 6;
    // 客户手机号码
    string customerMobile = 7;
    // 支付金额
    double payMoney = 8;
    // 支付日期
    string payDate = 9;
}

message MarchentDeliveryRecordGetResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 数据
    repeated MarchentDeliveryRecordDto data = 3;
    // 总记录条数
    int64 total = 4;
}

// 调用小程序的api
message MiniAppPostApiRequest {
    // 小程序的api名称,参照 https://developers.weixin.qq.com/miniprogram/dev/api-backend/ 例如 auth.code2Session 代表 登录凭证校验
    string apiName = 1;
    // 调用api需要的参数
    google.protobuf.Struct params = 2;
}

message MiniAppPostApiResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 微信接口返回的数据接口,参照 https://developers.weixin.qq.com/miniprogram/dev/api-backend/ 对应接口返回数据模型
    google.protobuf.Struct data = 3;
}

// 查询未读消息请求
message MessageGetListRequest {
    // 商户Id
    int32 marchentId = 1;
}

// 未读消息数据模型
message MessageGetListDataDto {
    // 窝代码
    string nestCode = 1;
    // 标题
    string title = 2;
    // 窝Id
    int64 nestId = 3;
    // 疫苗时间
    string createDate = 4;
    // 未读数量
    int32 unReadCount = 5;

}
// 查询未读消息响应
message MessageGetListResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 分页数据
    repeated MessageGetListDataDto data = 4;
}

// 根据消息Id查询消息明细
message MessageGetDetailRequest {
    // 窝Id
    int32 nestId = 1;
}

// 消息明细dto
message MessageGetDetailDto {
    // 宠物Id
    int32 petId = 1;
    // 宠物头像
    string petPortrait = 2;
    // 宠物性别 0:公,1母
    int32 petSex = 3;
    // 宠物备注
    string petRemarks = 4;
    // 疫苗时间
    string createDate = 5;
    // 是否已读 0 未读 1 已读
    int32 isRead = 6;
}

// 根据消息Id查询消息明细
message MessageGetDetailResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 数据模型
    repeated MessageGetDetailDto data = 3;
}

// 更新消息状态
message AllMessageReadRequest {
    // 商户Id
    int32 marchentId = 2;
}

// 推送微信订阅消息数据请求
message SubscribeMessageSendRequest {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 窝id，不传递则发送所有窝
    int32 nestId = 2;
    // 宠物id，不传递则发送所有宠物
    int32 petId = 3;
}
// 推送微信订阅消息数据响应
message SubscribeMessageSendResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 总消息数量
    int64 total = 3;
    // 发送成功数量
    int64 success = 4;
}

// 推送微信订阅消息数据请求
message ObsoleteMessageDeleteRequest {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 窝id，不传递则发送所有窝
    int32 nestId = 2;
    // 宠物id，不传递则发送所有宠物
    int32 petId = 3;
}
// 推送微信订阅消息数据响应
message ObsoleteMessageDeleteResponse {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    // 总消息数量
    int64 total = 3;
    // 发送成功数量
    int64 success = 4;
}

message GetVaccineBrandReq {
    //宠物种类 例如(猫,犬)
    string pet_species = 1;
}

message GetVaccineNameReq {
    //宠物种类 例如(猫,犬)
    string pet_species = 1;
    //疫苗品牌
    string vaccine_brand = 2;
}

message GetPetBreedReq {
    repeated string pet_dict_parent_ids = 1;
}

message GetPetNCountReq {
    //宠物种类 例如(猫,犬)
    string pet_species = 1;

    //宠物品种 例如(阿富汗猎犬)
    string pet_breed = 2;
}

message PayCustomerListReq {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 交付人手机号码 or 客户姓名
    string searchKey = 2;
    //交付时间 例如传值（2020-02-01）
    string pay_time = 3;
    //分页、每页个数
    int32 page_size = 4;
    //分页、页码
    int32 page_index = 5;
}

message ReferralRecordsReq {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 交付人手机号码
    string mobile = 2;
    //宠物Id
    int32 petId = 3;
}

message CustomerDataReq {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
}

message GetScrmPetsReq {
    // 客户手机号码
    string mobile = 1;
}

message GetCommonPetColorRes {
    repeated string data = 1;
}

message GetPetReferralRecordsReq {
    //商户id
    int32 marchent_id = 1;
    //宠物id
    string scrm_pet_id = 2;
    //手机号
    string mobile = 3;
    //页码
    int32 page_no = 4;
    //页量
    int32 page_size = 5;
}

message GetPetReferralRecordsRes {
    message unit {
        //外部转诊编码
        string apply_code = 1;
        //病例结束日期
        string apply_end_time = 2;
        //接收分院编码
        string brand_code = 3;
        //接收分院名称
        string clinic_name = 4;
        //接收医生姓名
        string apply_physician_name = 5;
        //转诊机构ID
        string target_id = 6;
        //外部转诊机构名称
        string target_orgname = 7;
        //客户姓名
        string cus_name = 8;
        //手机号码
        string cellphone = 9;
        //宠物ID
        int32 pet_logicid = 10;
        //宠物姓名
        string pet_name = 11;
        //宠物种类
        string pet_kindof = 12;
        //宠物品种
        string pet_variety = 13;
        //宠物出生日期
        string pet_birthday = 14;
        //宠物年龄
        string pet_age = 15;
        //病情诊断
        string main_symptom = 16;
        //结算金额
        double actual_amount = 17;
    }
    repeated unit data = 1;
}

message AddCustomerAndPetsReq {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 客户手机号码
    string mobile = 2;
    //交付人姓名
    string payName = 3;
    //用户scrm userId
    string userId = 4;
    //Scrm petId
    string scrmPetId = 5;
    //宠物名称
    string petName = 6;
    //宠物生日
    string petBirthday = 7;
    //宠物性别
    int32 petSex = 8;
    //宠物种类Id-1未知 1000猫 1001狗 1002其他
    int32 petKindof = 9;
    //宠物品种Id
    int32 petVariety = 10;
    //头像
    string petAvatar = 11;
    //新宠petId
    int32 petId = 12;
    //商户名称
    string shopName = 13;
    //芯片号
    string chipCode = 14;
    //花色
    string petFlower = 15;
    //支付金额
    int32 payAmount = 16;
    //交付日期
    string deliveryDate = 17;
    //宠物品种
    string petBreed = 18;
    //宠物种类
    string petSpecies = 19;
}

message GetCustomerDetailsReq {
    //交付表Id
    int32 id = 1;
}

message CustomerConfirmReq {
    //交付表Id
    int32 id = 1;
    // 客户手机号码
    string mobile = 2;
    //验证码
    string code = 3;
    //宠物名称
    string petName = 4;
}

message AddCustomerReq {
    // 商户Id，不传递则发送所有的商户信息
    int32 marchentId = 1;
    // 客户手机号码
    string mobile = 2;
}

message ExistCustomerReq {
    // 客户手机号码
    string mobile = 1;
    //客户姓名
    string payName = 2;
}

package models

import (
	"fmt"
	"order-center/proto/oc"
	"time"

	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 开团表
type OrderGroupActivity struct {
	Id                       int64     `json:"id"`
	PromotionGroupActivityId int64     `json:"promotion_group_activity_id"` // datacenter.promotion_group_activity.id
	StoreFinanceCode         string    `json:"store_finance_code"`          // 店铺财务编码
	StoreName                string    `json:"store_name"`                  // 店铺名称
	IsDis                    int32     `json:"is_dis"`                      // 是否分销员(开团时的状态) 0否1是
	Status                   int32     `json:"status"`                      // 团状态 0开团 1拼团成功 2拼团失败
	IsBreak                  int32     `json:"is_break"`                    // 终止 0否 1是
	FinalTakeType            int32     `json:"final_take_type"`             // 团长代收状态 0不代收 1代收
	MemberId                 string    `json:"member_id"`                   // 开团会员id
	MemberName               string    `json:"member_name"`                 // 开团会员名称
	MemberProfile            string    `json:"member_profile"`              // 开团会员头像
	MinAmount                int32     `json:"min_amount"`                  // 成团最小金额
	PayAmount                int32     `json:"pay_amount"`                  // 已支付金额
	WaitAmount               int32     `json:"wait_amount"`                 //待结算金额 分
	PaidAmount               int32     `json:"paid_amount"`                 //已结算金额 分
	ReceiverName             string    `json:"receiver_name"`               // 收件人
	ReceiverState            string    `json:"receiver_state"`              // 收件省
	ReceiverCity             string    `json:"receiver_city"`               // 收件市
	ReceiverDistrict         string    `json:"receiver_district"`           // 收件区
	ReceiverAddress          string    `json:"receiver_address"`            // 收件地址
	ReceiverMobile           string    `json:"receiver_mobile"`             // 收件手机
	EnReceiverMobile         string    `json:"en_receiver_mobile"`          // 加密手机号
	StartTime                time.Time `json:"start_time"`                  // 团开始时间
	EndTime                  time.Time `json:"end_time"`                    // 团结束时间
	DeliverDays              int32     `json:"deliver_days"`                // 最晚N日内送达
	IsSubscribe              int32     `json:"is_subscribe"`                // 是否订阅消息 0否 1是
	Lng                      string    `json:"lng"`                         // 经度
	Lat                      string    `json:"lat"`                         // 纬度
	CreatedAt                time.Time `json:"created_at"`                  // 创建时间
	UpdatedAt                time.Time `json:"updated_at"`                  // 更新时间
	GroupAt                  time.Time `json:"group_at"`                    //成团或失败的时间
	IsReceive                int32     `json:"is_receive"`                  //是否确认收货 1是  0否
	CommissionRate           int32     `json:"commission_rate"`             //佣金比例，单位%
	DisChainFinanceCode      string    `json:"dis_chain_finance_code"`      //分销员单位财务编码
	DisChainName             string    `json:"dis_chain_name"`              //分销员单位名称
	ChannelId                int32     `json:"channel_id"`                  //开团渠道id
	UserAgent                int32     `json:"user_agent"`                  //开团渠道来源
	ShopDisMemberId          string    `json:"shop_dis_member_id"`          //通过海报开团的海报分销员id
	ShopDisChainId           int32     `json:"shop_dis_chain_id"`           //海报分销员门店id
}

func (v OrderGroupActivity) ToOcCommunityGroupOrderListResponseDetails(requestFrom int32) *oc.CommunityGroupOrderListResponse_Details {
	info := &oc.CommunityGroupOrderListResponse_Details{
		Id:                  v.Id,
		FinanceCode:         v.StoreFinanceCode,
		StoreName:           v.StoreName,
		IsDis:               v.IsDis,
		Status:              v.Status,
		TakeType:            v.FinalTakeType,
		MinAmount:           v.MinAmount,
		PayAmount:           v.PayAmount,
		WaitAmount:          v.WaitAmount,
		PaidAmount:          v.PaidAmount,
		StartTime:           v.StartTime.Format(kit.DATETIME_LAYOUT),
		EndTime:             v.EndTime.Format(kit.DATETIME_LAYOUT),
		CreatedAt:           v.CreatedAt.Format(kit.DATETIME_LAYOUT),
		MemberId:            v.MemberId,
		MemberName:          v.MemberName,
		MemberProfile:       v.MemberProfile,
		ReceiverName:        v.ReceiverName,
		ReceiverMobile:      v.ReceiverMobile,
		EnReceiverMobile:    v.EnReceiverMobile,
		ReceiverAddress:     v.ReceiverAddress,
		DisChainFinanceCode: v.DisChainFinanceCode,
		DisChainName:        v.DisChainName,
		ChannelId:           v.ChannelId,
		UserAgent:           v.UserAgent,
	}
	if requestFrom == 2 {
		info.DeliveryType = 0
	}
	if !v.EndTime.IsZero() {
		info.GroupAt = v.EndTime.Format(kit.DATETIME_LAYOUT)
	}
	if v.Status == 1 {
		expectedTime := v.EndTime
		info.ExpectedTimeText = "预计" + expectedTime.AddDate(0, 0, cast.ToInt(v.DeliverDays)).Format(kit.DATE_LAYOUT) + "之前发货"
	} else {
		switch v.DeliverDays {
		case 0:
			info.ExpectedTimeText = "成团当日发货"
		case 1:
			info.ExpectedTimeText = "成团次日发货"
		default:
			info.ExpectedTimeText = fmt.Sprintf("成团后%d日内发货", v.DeliverDays)
		}
	}
	return info
}

type OrderGroupActivityExt struct {
	OrderGroupActivity `xorm:"extends"`
	Name               string `json:"name"`          //门店名称
	Image              string `json:"image"`         //门店头像
	GroupName          string `json:"group_name"`    //团长代收时团员的联系姓名
	GroupMobile        string `json:"group_mobile"`  //团长代收时团员的联系手机
	GroupAddress       string `json:"group_address"` //团长代收时团员的地址
}

type OrderGroupActivityAndMainGroup struct {
	OrderGroupActivity `xorm:"extends"`
	ParentOrderSn      string `json:"parent_order_sn"` //父单号
}

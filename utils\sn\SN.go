package sn

import (
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
)

type SN interface {
	Generate() string
}

func NewSN(kind string, db *xorm.Engine, redisDb *redis.Client) (sn SN) {
	switch kind {
	case "order": //订单号
		sn = &orderSN{
			redis: redisDb,
			db:    db,
		}
	case "refund": //退款单号
		sn = &refundSN{
			redis: redisDb,
			db:    db,
		}
	case "verify": //核销码
		sn = &verifySN{}
	case "delivery": //配送标识
		sn = &deliverySN{
			redis: redisDb,
			db:    db,
		}
	case "digital":
		sn = &digitalSN{}
	default:
		panic("not support kind")
	}

	return
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ext/external-tencent.proto

package ext

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PushOrderStatusReq struct {
	//数据源id（create方法返回的data.dataSource.id）
	DataSourceId         string             `protobuf:"bytes,1,opt,name=dataSourceId,proto3" json:"dataSourceId"`
	Orders               []*OrderStatusInfo `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PushOrderStatusReq) Reset()         { *m = PushOrderStatusReq{} }
func (m *PushOrderStatusReq) String() string { return proto.CompactTextString(m) }
func (*PushOrderStatusReq) ProtoMessage()    {}
func (*PushOrderStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{0}
}

func (m *PushOrderStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushOrderStatusReq.Unmarshal(m, b)
}
func (m *PushOrderStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushOrderStatusReq.Marshal(b, m, deterministic)
}
func (m *PushOrderStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushOrderStatusReq.Merge(m, src)
}
func (m *PushOrderStatusReq) XXX_Size() int {
	return xxx_messageInfo_PushOrderStatusReq.Size(m)
}
func (m *PushOrderStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushOrderStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushOrderStatusReq proto.InternalMessageInfo

func (m *PushOrderStatusReq) GetDataSourceId() string {
	if m != nil {
		return m.DataSourceId
	}
	return ""
}

func (m *PushOrderStatusReq) GetOrders() []*OrderStatusInfo {
	if m != nil {
		return m.Orders
	}
	return nil
}

type OrderStatusInfo struct {
	//商家订单号
	ExternalOrderId string `protobuf:"bytes,1,opt,name=external_order_id,json=externalOrderId,proto3" json:"external_order_id"`
	//主订单状态，1150已支付待发货，1160已发货，1180销售完成/已收货，1280退款中，1290退货完成
	OrderStatus string `protobuf:"bytes,2,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//状态变更时间，unix毫秒级时间，如 order_status状态为 1150 ，则传 1150（已支付待发货）状态变更的时间
	StatusChangeTime     string   `protobuf:"bytes,3,opt,name=status_change_time,json=statusChangeTime,proto3" json:"status_change_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderStatusInfo) Reset()         { *m = OrderStatusInfo{} }
func (m *OrderStatusInfo) String() string { return proto.CompactTextString(m) }
func (*OrderStatusInfo) ProtoMessage()    {}
func (*OrderStatusInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{1}
}

func (m *OrderStatusInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderStatusInfo.Unmarshal(m, b)
}
func (m *OrderStatusInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderStatusInfo.Marshal(b, m, deterministic)
}
func (m *OrderStatusInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderStatusInfo.Merge(m, src)
}
func (m *OrderStatusInfo) XXX_Size() int {
	return xxx_messageInfo_OrderStatusInfo.Size(m)
}
func (m *OrderStatusInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderStatusInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderStatusInfo proto.InternalMessageInfo

func (m *OrderStatusInfo) GetExternalOrderId() string {
	if m != nil {
		return m.ExternalOrderId
	}
	return ""
}

func (m *OrderStatusInfo) GetOrderStatus() string {
	if m != nil {
		return m.OrderStatus
	}
	return ""
}

func (m *OrderStatusInfo) GetStatusChangeTime() string {
	if m != nil {
		return m.StatusChangeTime
	}
	return ""
}

type PushOrderSumReq struct {
	//数据源id（create方法返回的data.dataSource.id）
	DataSourceId         string       `protobuf:"bytes,1,opt,name=dataSourceId,proto3" json:"dataSourceId"`
	Orders               []*OrdersSum `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushOrderSumReq) Reset()         { *m = PushOrderSumReq{} }
func (m *PushOrderSumReq) String() string { return proto.CompactTextString(m) }
func (*PushOrderSumReq) ProtoMessage()    {}
func (*PushOrderSumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{2}
}

func (m *PushOrderSumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushOrderSumReq.Unmarshal(m, b)
}
func (m *PushOrderSumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushOrderSumReq.Marshal(b, m, deterministic)
}
func (m *PushOrderSumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushOrderSumReq.Merge(m, src)
}
func (m *PushOrderSumReq) XXX_Size() int {
	return xxx_messageInfo_PushOrderSumReq.Size(m)
}
func (m *PushOrderSumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushOrderSumReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushOrderSumReq proto.InternalMessageInfo

func (m *PushOrderSumReq) GetDataSourceId() string {
	if m != nil {
		return m.DataSourceId
	}
	return ""
}

func (m *PushOrderSumReq) GetOrders() []*OrdersSum {
	if m != nil {
		return m.Orders
	}
	return nil
}

type OrdersSum struct {
	//日期，unix时间戳，字段长度为13字节
	RefDate string `protobuf:"bytes,1,opt,name=ref_date,json=refDate,proto3" json:"ref_date"`
	//该日期的下单金额之和
	GiveOrderAmountSum float32 `protobuf:"fixed32,2,opt,name=give_order_amount_sum,json=giveOrderAmountSum,proto3" json:"give_order_amount_sum"`
	//该日期的下单数量之和
	GiveOrderNumSum int32 `protobuf:"varint,3,opt,name=give_order_num_sum,json=giveOrderNumSum,proto3" json:"give_order_num_sum"`
	//该日期的支付金额之和
	PaymentAmountSum float32 `protobuf:"fixed32,4,opt,name=payment_amount_sum,json=paymentAmountSum,proto3" json:"payment_amount_sum"`
	//该日期的支付数量之和
	PayedNumSum          int32    `protobuf:"varint,5,opt,name=payed_num_sum,json=payedNumSum,proto3" json:"payed_num_sum"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrdersSum) Reset()         { *m = OrdersSum{} }
func (m *OrdersSum) String() string { return proto.CompactTextString(m) }
func (*OrdersSum) ProtoMessage()    {}
func (*OrdersSum) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{3}
}

func (m *OrdersSum) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrdersSum.Unmarshal(m, b)
}
func (m *OrdersSum) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrdersSum.Marshal(b, m, deterministic)
}
func (m *OrdersSum) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrdersSum.Merge(m, src)
}
func (m *OrdersSum) XXX_Size() int {
	return xxx_messageInfo_OrdersSum.Size(m)
}
func (m *OrdersSum) XXX_DiscardUnknown() {
	xxx_messageInfo_OrdersSum.DiscardUnknown(m)
}

var xxx_messageInfo_OrdersSum proto.InternalMessageInfo

func (m *OrdersSum) GetRefDate() string {
	if m != nil {
		return m.RefDate
	}
	return ""
}

func (m *OrdersSum) GetGiveOrderAmountSum() float32 {
	if m != nil {
		return m.GiveOrderAmountSum
	}
	return 0
}

func (m *OrdersSum) GetGiveOrderNumSum() int32 {
	if m != nil {
		return m.GiveOrderNumSum
	}
	return 0
}

func (m *OrdersSum) GetPaymentAmountSum() float32 {
	if m != nil {
		return m.PaymentAmountSum
	}
	return 0
}

func (m *OrdersSum) GetPayedNumSum() int32 {
	if m != nil {
		return m.PayedNumSum
	}
	return 0
}

type PushVisitPageReq struct {
	//开始日期。格式为 yyyymmdd
	BeginDate string `protobuf:"bytes,1,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	//结束日期，限定查询1天数据，允许设置的最大值为昨日。格式为 yyyymmdd
	EndDate              string   `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushVisitPageReq) Reset()         { *m = PushVisitPageReq{} }
func (m *PushVisitPageReq) String() string { return proto.CompactTextString(m) }
func (*PushVisitPageReq) ProtoMessage()    {}
func (*PushVisitPageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{4}
}

func (m *PushVisitPageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushVisitPageReq.Unmarshal(m, b)
}
func (m *PushVisitPageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushVisitPageReq.Marshal(b, m, deterministic)
}
func (m *PushVisitPageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushVisitPageReq.Merge(m, src)
}
func (m *PushVisitPageReq) XXX_Size() int {
	return xxx_messageInfo_PushVisitPageReq.Size(m)
}
func (m *PushVisitPageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushVisitPageReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushVisitPageReq proto.InternalMessageInfo

func (m *PushVisitPageReq) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *PushVisitPageReq) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

type PushVisitPageRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushVisitPageRes) Reset()         { *m = PushVisitPageRes{} }
func (m *PushVisitPageRes) String() string { return proto.CompactTextString(m) }
func (*PushVisitPageRes) ProtoMessage()    {}
func (*PushVisitPageRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{5}
}

func (m *PushVisitPageRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushVisitPageRes.Unmarshal(m, b)
}
func (m *PushVisitPageRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushVisitPageRes.Marshal(b, m, deterministic)
}
func (m *PushVisitPageRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushVisitPageRes.Merge(m, src)
}
func (m *PushVisitPageRes) XXX_Size() int {
	return xxx_messageInfo_PushVisitPageRes.Size(m)
}
func (m *PushVisitPageRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PushVisitPageRes.DiscardUnknown(m)
}

var xxx_messageInfo_PushVisitPageRes proto.InternalMessageInfo

func (m *PushVisitPageRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PushVisitPageRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PushVisitPageRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type AddOrdersToTencentReq struct {
	//数据源id（create方法返回的data.dataSource.id）
	DataSourceId         string           `protobuf:"bytes,1,opt,name=dataSourceId,proto3" json:"dataSourceId"`
	Orders               []*TencentOrders `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddOrdersToTencentReq) Reset()         { *m = AddOrdersToTencentReq{} }
func (m *AddOrdersToTencentReq) String() string { return proto.CompactTextString(m) }
func (*AddOrdersToTencentReq) ProtoMessage()    {}
func (*AddOrdersToTencentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{6}
}

func (m *AddOrdersToTencentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrdersToTencentReq.Unmarshal(m, b)
}
func (m *AddOrdersToTencentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrdersToTencentReq.Marshal(b, m, deterministic)
}
func (m *AddOrdersToTencentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrdersToTencentReq.Merge(m, src)
}
func (m *AddOrdersToTencentReq) XXX_Size() int {
	return xxx_messageInfo_AddOrdersToTencentReq.Size(m)
}
func (m *AddOrdersToTencentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrdersToTencentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrdersToTencentReq proto.InternalMessageInfo

func (m *AddOrdersToTencentReq) GetDataSourceId() string {
	if m != nil {
		return m.DataSourceId
	}
	return ""
}

func (m *AddOrdersToTencentReq) GetOrders() []*TencentOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

//通用返回
type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{7}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type TencentOrders struct {
	//商家订单号
	ExternalOrderId string `protobuf:"bytes,1,opt,name=external_order_id,json=externalOrderId,proto3" json:"external_order_id"`
	//订单创建时间，unix时间戳 字段长度为 13 字节
	CreateTime string `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//订单来源,枚举值:商家小程序：wxapp；商家app：app；商家H5：mobileweb；商家pcweb：pcweb；线下人工pos：offstore_pos_manual；线下自助收银：offstore_pos_self_help；其他：other
	OrderSource string `protobuf:"bytes,3,opt,name=order_source,json=orderSource,proto3" json:"order_source"`
	//订单类型；1：普通订单；2：充值订单；3：消费订单；普通购买商品订单传 1 即可
	OrderType int32 `protobuf:"varint,4,opt,name=order_type,json=orderType,proto3" json:"order_type"`
	//订单品牌 id
	BrandId string `protobuf:"bytes,5,opt,name=brand_id,json=brandId,proto3" json:"brand_id"`
	//订单品牌名称
	BrandName string `protobuf:"bytes,6,opt,name=brand_name,json=brandName,proto3" json:"brand_name"`
	//订单商品总数量
	GoodsNumTotal int64 `protobuf:"varint,7,opt,name=goods_num_total,json=goodsNumTotal,proto3" json:"goods_num_total"`
	//订单商品总重量，默认单位为克
	GoodsWeight float32 `protobuf:"fixed32,8,opt,name=goods_weight,json=goodsWeight,proto3" json:"goods_weight"`
	//商品总金额，单位默认为元 注：已含单品级别优惠的商品金额，如单品直降
	GoodsAmountTotal float32 `protobuf:"fixed32,9,opt,name=goods_amount_total,json=goodsAmountTotal,proto3" json:"goods_amount_total"`
	//订单运费，单位默认为元 注：运费为0时，传0.00
	FreightAmount float32 `protobuf:"fixed32,10,opt,name=freight_amount,json=freightAmount,proto3" json:"freight_amount"`
	//订单金额，单位默认为元 注：商品总金额+运费金额=订单金额
	OrderAmount float32 `protobuf:"fixed32,11,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount"`
	//订单应付金额，单位默认为元 注：订单金额-订单级别的优惠金额（如：订单满减）=订单应付金额
	PayableAmount float32 `protobuf:"fixed32,12,opt,name=payable_amount,json=payableAmount,proto3" json:"payable_amount"`
	//实付金额，单位默认为元 注：订单应付金额-支付优惠金额（如：微信支付优惠、招商银行优惠等）=订单实付金额
	PaymentAmount float32 `protobuf:"fixed32,13,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount"`
	//主订单状态，1110待支付，1150已支付待发货，1160已发货，1180销售完成/已收货，1280退款中，1290退货完成
	OrderStatus string `protobuf:"bytes,14,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//状态变更时间，unix毫秒级时间，如 order_status状态为 1150 ，则传 1150（已支付待发货）状态变更的时间
	StatusChangeTime string `protobuf:"bytes,15,opt,name=status_change_time,json=statusChangeTime,proto3" json:"status_change_time"`
	//用户信息，json格式
	UserInfo *TenUserInfo `protobuf:"bytes,16,opt,name=user_info,json=userInfo,proto3" json:"user_info"`
	//主订单商品信息，数组类型，每个sku存一个数组单位
	GoodsInfo []*TenGoodsInfo `protobuf:"bytes,17,rep,name=goods_info,json=goodsInfo,proto3" json:"goods_info"`
	//主订单用到的券信息，数组类型
	CouponInfo []*TenCouponInfo `protobuf:"bytes,18,rep,name=coupon_info,json=couponInfo,proto3" json:"coupon_info"`
	//主订单每种支付方式的支付信息,order_status = 1110时 payment_info非必填，其他状态码必填
	PaymentInfo []*TenPaymentInfo `protobuf:"bytes,19,rep,name=payment_info,json=paymentInfo,proto3" json:"payment_info"`
	//快递信息
	ExpressInfo *TenExpressInfo `protobuf:"bytes,20,opt,name=express_info,json=expressInfo,proto3" json:"express_info"`
	//发票信息，类型为数组
	InvoiceInfo []*TenInvoiceInfo `protobuf:"bytes,21,rep,name=invoice_info,json=invoiceInfo,proto3" json:"invoice_info"`
	//订单赠送总积分
	PointsTotal float32 `protobuf:"fixed32,22,opt,name=points_total,json=pointsTotal,proto3" json:"points_total"`
	//订单详情页信息，CPS业务跳转使用
	TargetUrl *TenTargetUrl `protobuf:"bytes,23,opt,name=target_url,json=targetUrl,proto3" json:"target_url"`
	//商家标记订单已删除，0-未删除，1-已删除
	IsDeleted            int32    `protobuf:"varint,24,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TencentOrders) Reset()         { *m = TencentOrders{} }
func (m *TencentOrders) String() string { return proto.CompactTextString(m) }
func (*TencentOrders) ProtoMessage()    {}
func (*TencentOrders) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{8}
}

func (m *TencentOrders) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TencentOrders.Unmarshal(m, b)
}
func (m *TencentOrders) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TencentOrders.Marshal(b, m, deterministic)
}
func (m *TencentOrders) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TencentOrders.Merge(m, src)
}
func (m *TencentOrders) XXX_Size() int {
	return xxx_messageInfo_TencentOrders.Size(m)
}
func (m *TencentOrders) XXX_DiscardUnknown() {
	xxx_messageInfo_TencentOrders.DiscardUnknown(m)
}

var xxx_messageInfo_TencentOrders proto.InternalMessageInfo

func (m *TencentOrders) GetExternalOrderId() string {
	if m != nil {
		return m.ExternalOrderId
	}
	return ""
}

func (m *TencentOrders) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *TencentOrders) GetOrderSource() string {
	if m != nil {
		return m.OrderSource
	}
	return ""
}

func (m *TencentOrders) GetOrderType() int32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

func (m *TencentOrders) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *TencentOrders) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *TencentOrders) GetGoodsNumTotal() int64 {
	if m != nil {
		return m.GoodsNumTotal
	}
	return 0
}

func (m *TencentOrders) GetGoodsWeight() float32 {
	if m != nil {
		return m.GoodsWeight
	}
	return 0
}

func (m *TencentOrders) GetGoodsAmountTotal() float32 {
	if m != nil {
		return m.GoodsAmountTotal
	}
	return 0
}

func (m *TencentOrders) GetFreightAmount() float32 {
	if m != nil {
		return m.FreightAmount
	}
	return 0
}

func (m *TencentOrders) GetOrderAmount() float32 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *TencentOrders) GetPayableAmount() float32 {
	if m != nil {
		return m.PayableAmount
	}
	return 0
}

func (m *TencentOrders) GetPaymentAmount() float32 {
	if m != nil {
		return m.PaymentAmount
	}
	return 0
}

func (m *TencentOrders) GetOrderStatus() string {
	if m != nil {
		return m.OrderStatus
	}
	return ""
}

func (m *TencentOrders) GetStatusChangeTime() string {
	if m != nil {
		return m.StatusChangeTime
	}
	return ""
}

func (m *TencentOrders) GetUserInfo() *TenUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *TencentOrders) GetGoodsInfo() []*TenGoodsInfo {
	if m != nil {
		return m.GoodsInfo
	}
	return nil
}

func (m *TencentOrders) GetCouponInfo() []*TenCouponInfo {
	if m != nil {
		return m.CouponInfo
	}
	return nil
}

func (m *TencentOrders) GetPaymentInfo() []*TenPaymentInfo {
	if m != nil {
		return m.PaymentInfo
	}
	return nil
}

func (m *TencentOrders) GetExpressInfo() *TenExpressInfo {
	if m != nil {
		return m.ExpressInfo
	}
	return nil
}

func (m *TencentOrders) GetInvoiceInfo() []*TenInvoiceInfo {
	if m != nil {
		return m.InvoiceInfo
	}
	return nil
}

func (m *TencentOrders) GetPointsTotal() float32 {
	if m != nil {
		return m.PointsTotal
	}
	return 0
}

func (m *TencentOrders) GetTargetUrl() *TenTargetUrl {
	if m != nil {
		return m.TargetUrl
	}
	return nil
}

func (m *TencentOrders) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

type TenTargetUrl struct {
	//微信小程序落地页url，当落地页为微信小程序时必填
	UrlMiniprogram string `protobuf:"bytes,1,opt,name=url_miniprogram,json=urlMiniprogram,proto3" json:"url_miniprogram"`
	//微信小程序appid，当落地页为微信小程序时必填
	MiniprogramAppid string `protobuf:"bytes,2,opt,name=miniprogram_appid,json=miniprogramAppid,proto3" json:"miniprogram_appid"`
	//小程序原始ID，登录小程序管理后台-设置-基本设置-帐号信息中，gh_xx，当落地页为微信小程序时必填
	MiniprogramUsername string `protobuf:"bytes,3,opt,name=miniprogram_username,json=miniprogramUsername,proto3" json:"miniprogram_username"`
	//qq小程序落地页url，当落地页为QQ小程序时必填
	UrlMiniprogramQq string `protobuf:"bytes,4,opt,name=url_miniprogram_qq,json=urlMiniprogramQq,proto3" json:"url_miniprogram_qq"`
	//qq小程序appid，当落地页为QQ小程序时必填
	MiniprogramAppidQq string `protobuf:"bytes,5,opt,name=miniprogram_appid_qq,json=miniprogramAppidQq,proto3" json:"miniprogram_appid_qq"`
	//h5落地页url
	UrlH5                string   `protobuf:"bytes,6,opt,name=url_h5,json=urlH5,proto3" json:"url_h5"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenTargetUrl) Reset()         { *m = TenTargetUrl{} }
func (m *TenTargetUrl) String() string { return proto.CompactTextString(m) }
func (*TenTargetUrl) ProtoMessage()    {}
func (*TenTargetUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{9}
}

func (m *TenTargetUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenTargetUrl.Unmarshal(m, b)
}
func (m *TenTargetUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenTargetUrl.Marshal(b, m, deterministic)
}
func (m *TenTargetUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenTargetUrl.Merge(m, src)
}
func (m *TenTargetUrl) XXX_Size() int {
	return xxx_messageInfo_TenTargetUrl.Size(m)
}
func (m *TenTargetUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_TenTargetUrl.DiscardUnknown(m)
}

var xxx_messageInfo_TenTargetUrl proto.InternalMessageInfo

func (m *TenTargetUrl) GetUrlMiniprogram() string {
	if m != nil {
		return m.UrlMiniprogram
	}
	return ""
}

func (m *TenTargetUrl) GetMiniprogramAppid() string {
	if m != nil {
		return m.MiniprogramAppid
	}
	return ""
}

func (m *TenTargetUrl) GetMiniprogramUsername() string {
	if m != nil {
		return m.MiniprogramUsername
	}
	return ""
}

func (m *TenTargetUrl) GetUrlMiniprogramQq() string {
	if m != nil {
		return m.UrlMiniprogramQq
	}
	return ""
}

func (m *TenTargetUrl) GetMiniprogramAppidQq() string {
	if m != nil {
		return m.MiniprogramAppidQq
	}
	return ""
}

func (m *TenTargetUrl) GetUrlH5() string {
	if m != nil {
		return m.UrlH5
	}
	return ""
}

type TenInvoiceInfo struct {
	//是否需要发票，true代表需要，false代表不需要
	IfNeedInvoice bool `protobuf:"varint,1,opt,name=if_need_invoice,json=ifNeedInvoice,proto3" json:"if_need_invoice"`
	//发票类型，枚举值，取值如下： 1000(增值税专用发票) 1001(普通发票) 1002(机动车专用发票) 1003(机打发票) 1004(定额发票 ) 1005(剪开式发票) 1006（其他）
	InvoiceType string `protobuf:"bytes,2,opt,name=invoice_type,json=invoiceType,proto3" json:"invoice_type"`
	//发票抬头
	InvoiceTitle string `protobuf:"bytes,3,opt,name=invoice_title,json=invoiceTitle,proto3" json:"invoice_title"`
	//发票内容
	InvoiceContent string `protobuf:"bytes,4,opt,name=invoice_content,json=invoiceContent,proto3" json:"invoice_content"`
	//发票附加信息
	InvoiceAdditionInfo string `protobuf:"bytes,5,opt,name=invoice_addition_info,json=invoiceAdditionInfo,proto3" json:"invoice_addition_info"`
	//公司名称
	InvoiceCompany string `protobuf:"bytes,6,opt,name=invoice_company,json=invoiceCompany,proto3" json:"invoice_company"`
	//纳税人识别号
	InvoiceTaxpayer string `protobuf:"bytes,7,opt,name=invoice_taxpayer,json=invoiceTaxpayer,proto3" json:"invoice_taxpayer"`
	//注册地址
	RegistryAddress string `protobuf:"bytes,8,opt,name=registry_address,json=registryAddress,proto3" json:"registry_address"`
	//注册电话
	RegistryPhone string `protobuf:"bytes,9,opt,name=registry_phone,json=registryPhone,proto3" json:"registry_phone"`
	//开户银行
	RegistryBankName string `protobuf:"bytes,10,opt,name=registry_bank_name,json=registryBankName,proto3" json:"registry_bank_name"`
	//开户账号
	RegistryBankAccount string `protobuf:"bytes,11,opt,name=registry_bank_account,json=registryBankAccount,proto3" json:"registry_bank_account"`
	//发票收件地址
	InvoiceDeliveryAddress string `protobuf:"bytes,12,opt,name=invoice_delivery_address,json=invoiceDeliveryAddress,proto3" json:"invoice_delivery_address"`
	//发票收件人姓名
	InvoiceDeliveryName string `protobuf:"bytes,13,opt,name=invoice_delivery_name,json=invoiceDeliveryName,proto3" json:"invoice_delivery_name"`
	//发票收件人电话
	InvoiceDeliveryPhone string `protobuf:"bytes,14,opt,name=invoice_delivery_phone,json=invoiceDeliveryPhone,proto3" json:"invoice_delivery_phone"`
	//发票号码
	InvoiceNum           string   `protobuf:"bytes,15,opt,name=invoice_num,json=invoiceNum,proto3" json:"invoice_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenInvoiceInfo) Reset()         { *m = TenInvoiceInfo{} }
func (m *TenInvoiceInfo) String() string { return proto.CompactTextString(m) }
func (*TenInvoiceInfo) ProtoMessage()    {}
func (*TenInvoiceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{10}
}

func (m *TenInvoiceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenInvoiceInfo.Unmarshal(m, b)
}
func (m *TenInvoiceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenInvoiceInfo.Marshal(b, m, deterministic)
}
func (m *TenInvoiceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenInvoiceInfo.Merge(m, src)
}
func (m *TenInvoiceInfo) XXX_Size() int {
	return xxx_messageInfo_TenInvoiceInfo.Size(m)
}
func (m *TenInvoiceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenInvoiceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenInvoiceInfo proto.InternalMessageInfo

func (m *TenInvoiceInfo) GetIfNeedInvoice() bool {
	if m != nil {
		return m.IfNeedInvoice
	}
	return false
}

func (m *TenInvoiceInfo) GetInvoiceType() string {
	if m != nil {
		return m.InvoiceType
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceTitle() string {
	if m != nil {
		return m.InvoiceTitle
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceContent() string {
	if m != nil {
		return m.InvoiceContent
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceAdditionInfo() string {
	if m != nil {
		return m.InvoiceAdditionInfo
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceCompany() string {
	if m != nil {
		return m.InvoiceCompany
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceTaxpayer() string {
	if m != nil {
		return m.InvoiceTaxpayer
	}
	return ""
}

func (m *TenInvoiceInfo) GetRegistryAddress() string {
	if m != nil {
		return m.RegistryAddress
	}
	return ""
}

func (m *TenInvoiceInfo) GetRegistryPhone() string {
	if m != nil {
		return m.RegistryPhone
	}
	return ""
}

func (m *TenInvoiceInfo) GetRegistryBankName() string {
	if m != nil {
		return m.RegistryBankName
	}
	return ""
}

func (m *TenInvoiceInfo) GetRegistryBankAccount() string {
	if m != nil {
		return m.RegistryBankAccount
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceDeliveryAddress() string {
	if m != nil {
		return m.InvoiceDeliveryAddress
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceDeliveryName() string {
	if m != nil {
		return m.InvoiceDeliveryName
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceDeliveryPhone() string {
	if m != nil {
		return m.InvoiceDeliveryPhone
	}
	return ""
}

func (m *TenInvoiceInfo) GetInvoiceNum() string {
	if m != nil {
		return m.InvoiceNum
	}
	return ""
}

type TenExpressInfo struct {
	//订单物流状态
	LogisticsStatus string `protobuf:"bytes,1,opt,name=logistics_status,json=logisticsStatus,proto3" json:"logistics_status"`
	//商品总重量，单位默认为克
	GoodsTotalWeight float32 `protobuf:"fixed32,2,opt,name=goods_total_weight,json=goodsTotalWeight,proto3" json:"goods_total_weight"`
	//收件人姓名
	ReceiverName string `protobuf:"bytes,3,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	//收件人联系电话
	ReceiverPhone string `protobuf:"bytes,4,opt,name=receiver_phone,json=receiverPhone,proto3" json:"receiver_phone"`
	//收件人地址
	ReceiverAddress string `protobuf:"bytes,5,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	//国家编码
	ReceiverCountryCode string `protobuf:"bytes,6,opt,name=receiver_country_code,json=receiverCountryCode,proto3" json:"receiver_country_code"`
	//省份编码
	ReceiverProvinceCode string `protobuf:"bytes,7,opt,name=receiver_province_code,json=receiverProvinceCode,proto3" json:"receiver_province_code"`
	//城市编码
	ReceiverCityCode string `protobuf:"bytes,8,opt,name=receiver_city_code,json=receiverCityCode,proto3" json:"receiver_city_code"`
	//区编码
	ReceiverDistrictCode string `protobuf:"bytes,9,opt,name=receiver_district_code,json=receiverDistrictCode,proto3" json:"receiver_district_code"`
	//期望送货时间段，格式为“起始时间-结束时间”，如"9:00-12:00"
	ExpectedDeliveryTime string `protobuf:"bytes,10,opt,name=expected_delivery_time,json=expectedDeliveryTime,proto3" json:"expected_delivery_time"`
	//期望送货日期，格式“YYYYMMDD”
	ExpectedDeliveryDate string `protobuf:"bytes,11,opt,name=expected_delivery_date,json=expectedDeliveryDate,proto3" json:"expected_delivery_date"`
	//包裹信息，object类型
	ExpressPackageInfo   []*TenExpressPackageInfo `protobuf:"bytes,12,rep,name=express_package_info,json=expressPackageInfo,proto3" json:"express_package_info"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *TenExpressInfo) Reset()         { *m = TenExpressInfo{} }
func (m *TenExpressInfo) String() string { return proto.CompactTextString(m) }
func (*TenExpressInfo) ProtoMessage()    {}
func (*TenExpressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{11}
}

func (m *TenExpressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenExpressInfo.Unmarshal(m, b)
}
func (m *TenExpressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenExpressInfo.Marshal(b, m, deterministic)
}
func (m *TenExpressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenExpressInfo.Merge(m, src)
}
func (m *TenExpressInfo) XXX_Size() int {
	return xxx_messageInfo_TenExpressInfo.Size(m)
}
func (m *TenExpressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenExpressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenExpressInfo proto.InternalMessageInfo

func (m *TenExpressInfo) GetLogisticsStatus() string {
	if m != nil {
		return m.LogisticsStatus
	}
	return ""
}

func (m *TenExpressInfo) GetGoodsTotalWeight() float32 {
	if m != nil {
		return m.GoodsTotalWeight
	}
	return 0
}

func (m *TenExpressInfo) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverPhone() string {
	if m != nil {
		return m.ReceiverPhone
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverCountryCode() string {
	if m != nil {
		return m.ReceiverCountryCode
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverProvinceCode() string {
	if m != nil {
		return m.ReceiverProvinceCode
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverCityCode() string {
	if m != nil {
		return m.ReceiverCityCode
	}
	return ""
}

func (m *TenExpressInfo) GetReceiverDistrictCode() string {
	if m != nil {
		return m.ReceiverDistrictCode
	}
	return ""
}

func (m *TenExpressInfo) GetExpectedDeliveryTime() string {
	if m != nil {
		return m.ExpectedDeliveryTime
	}
	return ""
}

func (m *TenExpressInfo) GetExpectedDeliveryDate() string {
	if m != nil {
		return m.ExpectedDeliveryDate
	}
	return ""
}

func (m *TenExpressInfo) GetExpressPackageInfo() []*TenExpressPackageInfo {
	if m != nil {
		return m.ExpressPackageInfo
	}
	return nil
}

type TenExpressPackageInfo struct {
	//物流公司编码，枚举类型，枚举值请参见文章后面的“物流商 code”
	ExpressCompanyCode string `protobuf:"bytes,1,opt,name=express_company_code,json=expressCompanyCode,proto3" json:"express_company_code"`
	//物流公司名称
	ExpressCompanyName string `protobuf:"bytes,2,opt,name=express_company_name,json=expressCompanyName,proto3" json:"express_company_name"`
	//运单号
	ExpressCode string `protobuf:"bytes,3,opt,name=express_code,json=expressCode,proto3" json:"express_code"`
	//发货时间，格式为时间戳 字段长度为 13 字节
	ShipTime string `protobuf:"bytes,4,opt,name=ship_time,json=shipTime,proto3" json:"ship_time"`
	//运费跳转页面，json字符串
	ExpressPage *TenExpressPage `protobuf:"bytes,5,opt,name=express_page,json=expressPage,proto3" json:"express_page"`
	//物流包裹信息
	ExpressPackageInfo   []*TenExpressPackInfo `protobuf:"bytes,6,rep,name=express_package_info,json=expressPackageInfo,proto3" json:"express_package_info"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TenExpressPackageInfo) Reset()         { *m = TenExpressPackageInfo{} }
func (m *TenExpressPackageInfo) String() string { return proto.CompactTextString(m) }
func (*TenExpressPackageInfo) ProtoMessage()    {}
func (*TenExpressPackageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{12}
}

func (m *TenExpressPackageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenExpressPackageInfo.Unmarshal(m, b)
}
func (m *TenExpressPackageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenExpressPackageInfo.Marshal(b, m, deterministic)
}
func (m *TenExpressPackageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenExpressPackageInfo.Merge(m, src)
}
func (m *TenExpressPackageInfo) XXX_Size() int {
	return xxx_messageInfo_TenExpressPackageInfo.Size(m)
}
func (m *TenExpressPackageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenExpressPackageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenExpressPackageInfo proto.InternalMessageInfo

func (m *TenExpressPackageInfo) GetExpressCompanyCode() string {
	if m != nil {
		return m.ExpressCompanyCode
	}
	return ""
}

func (m *TenExpressPackageInfo) GetExpressCompanyName() string {
	if m != nil {
		return m.ExpressCompanyName
	}
	return ""
}

func (m *TenExpressPackageInfo) GetExpressCode() string {
	if m != nil {
		return m.ExpressCode
	}
	return ""
}

func (m *TenExpressPackageInfo) GetShipTime() string {
	if m != nil {
		return m.ShipTime
	}
	return ""
}

func (m *TenExpressPackageInfo) GetExpressPage() *TenExpressPage {
	if m != nil {
		return m.ExpressPage
	}
	return nil
}

func (m *TenExpressPackageInfo) GetExpressPackageInfo() []*TenExpressPackInfo {
	if m != nil {
		return m.ExpressPackageInfo
	}
	return nil
}

type TenExpressPackInfo struct {
	//商品sku id
	ExternalSkuId string `protobuf:"bytes,1,opt,name=external_sku_id,json=externalSkuId,proto3" json:"external_sku_id"`
	//商品数量
	Number               int64    `protobuf:"varint,2,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenExpressPackInfo) Reset()         { *m = TenExpressPackInfo{} }
func (m *TenExpressPackInfo) String() string { return proto.CompactTextString(m) }
func (*TenExpressPackInfo) ProtoMessage()    {}
func (*TenExpressPackInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{13}
}

func (m *TenExpressPackInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenExpressPackInfo.Unmarshal(m, b)
}
func (m *TenExpressPackInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenExpressPackInfo.Marshal(b, m, deterministic)
}
func (m *TenExpressPackInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenExpressPackInfo.Merge(m, src)
}
func (m *TenExpressPackInfo) XXX_Size() int {
	return xxx_messageInfo_TenExpressPackInfo.Size(m)
}
func (m *TenExpressPackInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenExpressPackInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenExpressPackInfo proto.InternalMessageInfo

func (m *TenExpressPackInfo) GetExternalSkuId() string {
	if m != nil {
		return m.ExternalSkuId
	}
	return ""
}

func (m *TenExpressPackInfo) GetNumber() int64 {
	if m != nil {
		return m.Number
	}
	return 0
}

type TenExpressPage struct {
	//快递详情页跳转链接（小程序页面，小程序填此字段）
	MiniprogramPath string `protobuf:"bytes,1,opt,name=miniprogram_path,json=miniprogramPath,proto3" json:"miniprogram_path"`
	//小程序APPID，填写了miniprogram_path需填此字段
	MiniprogramAppid string `protobuf:"bytes,2,opt,name=miniprogram_appid,json=miniprogramAppid,proto3" json:"miniprogram_appid"`
	//快递详情页跳转链接（h5页面，公众号填此字段）
	MiniprogramH5        string   `protobuf:"bytes,3,opt,name=miniprogram_h5,json=miniprogramH5,proto3" json:"miniprogram_h5"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenExpressPage) Reset()         { *m = TenExpressPage{} }
func (m *TenExpressPage) String() string { return proto.CompactTextString(m) }
func (*TenExpressPage) ProtoMessage()    {}
func (*TenExpressPage) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{14}
}

func (m *TenExpressPage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenExpressPage.Unmarshal(m, b)
}
func (m *TenExpressPage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenExpressPage.Marshal(b, m, deterministic)
}
func (m *TenExpressPage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenExpressPage.Merge(m, src)
}
func (m *TenExpressPage) XXX_Size() int {
	return xxx_messageInfo_TenExpressPage.Size(m)
}
func (m *TenExpressPage) XXX_DiscardUnknown() {
	xxx_messageInfo_TenExpressPage.DiscardUnknown(m)
}

var xxx_messageInfo_TenExpressPage proto.InternalMessageInfo

func (m *TenExpressPage) GetMiniprogramPath() string {
	if m != nil {
		return m.MiniprogramPath
	}
	return ""
}

func (m *TenExpressPage) GetMiniprogramAppid() string {
	if m != nil {
		return m.MiniprogramAppid
	}
	return ""
}

func (m *TenExpressPage) GetMiniprogramH5() string {
	if m != nil {
		return m.MiniprogramH5
	}
	return ""
}

type TenPaymentInfo struct {
	//支付方式，见<枚举列表>页
	PaymentType string `protobuf:"bytes,1,opt,name=payment_type,json=paymentType,proto3" json:"payment_type"`
	//微信支付订单ID/流水号
	TransId string `protobuf:"bytes,2,opt,name=trans_id,json=transId,proto3" json:"trans_id"`
	//金额，单位默认为元
	TransAmount          float32  `protobuf:"fixed32,3,opt,name=trans_amount,json=transAmount,proto3" json:"trans_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenPaymentInfo) Reset()         { *m = TenPaymentInfo{} }
func (m *TenPaymentInfo) String() string { return proto.CompactTextString(m) }
func (*TenPaymentInfo) ProtoMessage()    {}
func (*TenPaymentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{15}
}

func (m *TenPaymentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenPaymentInfo.Unmarshal(m, b)
}
func (m *TenPaymentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenPaymentInfo.Marshal(b, m, deterministic)
}
func (m *TenPaymentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenPaymentInfo.Merge(m, src)
}
func (m *TenPaymentInfo) XXX_Size() int {
	return xxx_messageInfo_TenPaymentInfo.Size(m)
}
func (m *TenPaymentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenPaymentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenPaymentInfo proto.InternalMessageInfo

func (m *TenPaymentInfo) GetPaymentType() string {
	if m != nil {
		return m.PaymentType
	}
	return ""
}

func (m *TenPaymentInfo) GetTransId() string {
	if m != nil {
		return m.TransId
	}
	return ""
}

func (m *TenPaymentInfo) GetTransAmount() float32 {
	if m != nil {
		return m.TransAmount
	}
	return 0
}

type TenCouponInfo struct {
	//卡券类型；1：商家券；2：微信支付券
	CouponType int32 `protobuf:"varint,1,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	//该类券优惠金额总额，单位默认为元
	CouponAmountTotal float32 `protobuf:"fixed32,2,opt,name=coupon_amount_total,json=couponAmountTotal,proto3" json:"coupon_amount_total"`
	//该类券的细节券信息
	CouponDetail         []*TenCouponDetail `protobuf:"bytes,3,rep,name=coupon_detail,json=couponDetail,proto3" json:"coupon_detail"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *TenCouponInfo) Reset()         { *m = TenCouponInfo{} }
func (m *TenCouponInfo) String() string { return proto.CompactTextString(m) }
func (*TenCouponInfo) ProtoMessage()    {}
func (*TenCouponInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{16}
}

func (m *TenCouponInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenCouponInfo.Unmarshal(m, b)
}
func (m *TenCouponInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenCouponInfo.Marshal(b, m, deterministic)
}
func (m *TenCouponInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenCouponInfo.Merge(m, src)
}
func (m *TenCouponInfo) XXX_Size() int {
	return xxx_messageInfo_TenCouponInfo.Size(m)
}
func (m *TenCouponInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenCouponInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenCouponInfo proto.InternalMessageInfo

func (m *TenCouponInfo) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *TenCouponInfo) GetCouponAmountTotal() float32 {
	if m != nil {
		return m.CouponAmountTotal
	}
	return 0
}

func (m *TenCouponInfo) GetCouponDetail() []*TenCouponDetail {
	if m != nil {
		return m.CouponDetail
	}
	return nil
}

type TenCouponDetail struct {
	//券 id
	ExternalCouponId string `protobuf:"bytes,1,opt,name=external_coupon_id,json=externalCouponId,proto3" json:"external_coupon_id"`
	//券批次 id（该字段需要在优惠券接口中添加卡券批次）
	CouponBatchId string `protobuf:"bytes,2,opt,name=coupon_batch_id,json=couponBatchId,proto3" json:"coupon_batch_id"`
	//券名称
	CouponName string `protobuf:"bytes,3,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	//该张券优惠金额，单位默认为元
	CouponAmount         float32  `protobuf:"fixed32,4,opt,name=coupon_amount,json=couponAmount,proto3" json:"coupon_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenCouponDetail) Reset()         { *m = TenCouponDetail{} }
func (m *TenCouponDetail) String() string { return proto.CompactTextString(m) }
func (*TenCouponDetail) ProtoMessage()    {}
func (*TenCouponDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{17}
}

func (m *TenCouponDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenCouponDetail.Unmarshal(m, b)
}
func (m *TenCouponDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenCouponDetail.Marshal(b, m, deterministic)
}
func (m *TenCouponDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenCouponDetail.Merge(m, src)
}
func (m *TenCouponDetail) XXX_Size() int {
	return xxx_messageInfo_TenCouponDetail.Size(m)
}
func (m *TenCouponDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_TenCouponDetail.DiscardUnknown(m)
}

var xxx_messageInfo_TenCouponDetail proto.InternalMessageInfo

func (m *TenCouponDetail) GetExternalCouponId() string {
	if m != nil {
		return m.ExternalCouponId
	}
	return ""
}

func (m *TenCouponDetail) GetCouponBatchId() string {
	if m != nil {
		return m.CouponBatchId
	}
	return ""
}

func (m *TenCouponDetail) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *TenCouponDetail) GetCouponAmount() float32 {
	if m != nil {
		return m.CouponAmount
	}
	return 0
}

type TenGoodsInfo struct {
	//sku编号
	ExternalSkuId string `protobuf:"bytes,1,opt,name=external_sku_id,json=externalSkuId,proto3" json:"external_sku_id"`
	//商品主图
	PrimaryImageUrl string `protobuf:"bytes,2,opt,name=primary_image_url,json=primaryImageUrl,proto3" json:"primary_image_url"`
	//sku 名称
	SkuNameChinese string `protobuf:"bytes,3,opt,name=sku_name_chinese,json=skuNameChinese,proto3" json:"sku_name_chinese"`
	//单件商品原价，单位默认为元
	GoodsAmount float32 `protobuf:"fixed32,4,opt,name=goods_amount,json=goodsAmount,proto3" json:"goods_amount"`
	//多件商品实付金额（分摊了优惠的金额）,单位默认为元
	PaymentAmount float32 `protobuf:"fixed32,5,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount"`
	//是否赠品，0代表非赠品，1代表赠品
	IsGift int32 `protobuf:"varint,6,opt,name=is_gift,json=isGift,proto3" json:"is_gift"`
	//sku 所属 spu 编号，若无 spu，传输内容请与 external_sku_id 保持一致
	ExternalSpuId string `protobuf:"bytes,7,opt,name=external_spu_id,json=externalSpuId,proto3" json:"external_spu_id"`
	//spu 名称，若无 spu，传输内容请与 sku_name_chinese 保持一致
	SpuNameChinese string `protobuf:"bytes,8,opt,name=spu_name_chinese,json=spuNameChinese,proto3" json:"spu_name_chinese"`
	//商品售卖单位
	SaleUnit string `protobuf:"bytes,9,opt,name=sale_unit,json=saleUnit,proto3" json:"sale_unit"`
	//末级类目 id
	CategoryId string `protobuf:"bytes,10,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	//末级类目名称
	CategoryName string `protobuf:"bytes,11,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	//商品数量
	GoodsNum int64 `protobuf:"varint,12,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	//商品重量，单位默认为克
	GoodsWeight float32 `protobuf:"fixed32,13,opt,name=goods_weight,json=goodsWeight,proto3" json:"goods_weight"`
	//主订单销售门店信息
	StoreInfo *TenStoreInfo `protobuf:"bytes,14,opt,name=store_info,json=storeInfo,proto3" json:"store_info"`
	//主订单来源渠道，数组类型
	ChanInfo []*TenChanInfo `protobuf:"bytes,15,rep,name=chan_info,json=chanInfo,proto3" json:"chan_info"`
	//佣金，json字符串
	CommissionInfo []*TenCommissionInfo `protobuf:"bytes,16,rep,name=commission_info,json=commissionInfo,proto3" json:"commission_info"`
	//第三方推广信息，数组类型，CPS业务必传
	ThirdPromotionInfo   []*TenThirdPromotionInfo `protobuf:"bytes,17,rep,name=third_promotion_info,json=thirdPromotionInfo,proto3" json:"third_promotion_info"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *TenGoodsInfo) Reset()         { *m = TenGoodsInfo{} }
func (m *TenGoodsInfo) String() string { return proto.CompactTextString(m) }
func (*TenGoodsInfo) ProtoMessage()    {}
func (*TenGoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{18}
}

func (m *TenGoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenGoodsInfo.Unmarshal(m, b)
}
func (m *TenGoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenGoodsInfo.Marshal(b, m, deterministic)
}
func (m *TenGoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenGoodsInfo.Merge(m, src)
}
func (m *TenGoodsInfo) XXX_Size() int {
	return xxx_messageInfo_TenGoodsInfo.Size(m)
}
func (m *TenGoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenGoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenGoodsInfo proto.InternalMessageInfo

func (m *TenGoodsInfo) GetExternalSkuId() string {
	if m != nil {
		return m.ExternalSkuId
	}
	return ""
}

func (m *TenGoodsInfo) GetPrimaryImageUrl() string {
	if m != nil {
		return m.PrimaryImageUrl
	}
	return ""
}

func (m *TenGoodsInfo) GetSkuNameChinese() string {
	if m != nil {
		return m.SkuNameChinese
	}
	return ""
}

func (m *TenGoodsInfo) GetGoodsAmount() float32 {
	if m != nil {
		return m.GoodsAmount
	}
	return 0
}

func (m *TenGoodsInfo) GetPaymentAmount() float32 {
	if m != nil {
		return m.PaymentAmount
	}
	return 0
}

func (m *TenGoodsInfo) GetIsGift() int32 {
	if m != nil {
		return m.IsGift
	}
	return 0
}

func (m *TenGoodsInfo) GetExternalSpuId() string {
	if m != nil {
		return m.ExternalSpuId
	}
	return ""
}

func (m *TenGoodsInfo) GetSpuNameChinese() string {
	if m != nil {
		return m.SpuNameChinese
	}
	return ""
}

func (m *TenGoodsInfo) GetSaleUnit() string {
	if m != nil {
		return m.SaleUnit
	}
	return ""
}

func (m *TenGoodsInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *TenGoodsInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *TenGoodsInfo) GetGoodsNum() int64 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

func (m *TenGoodsInfo) GetGoodsWeight() float32 {
	if m != nil {
		return m.GoodsWeight
	}
	return 0
}

func (m *TenGoodsInfo) GetStoreInfo() *TenStoreInfo {
	if m != nil {
		return m.StoreInfo
	}
	return nil
}

func (m *TenGoodsInfo) GetChanInfo() []*TenChanInfo {
	if m != nil {
		return m.ChanInfo
	}
	return nil
}

func (m *TenGoodsInfo) GetCommissionInfo() []*TenCommissionInfo {
	if m != nil {
		return m.CommissionInfo
	}
	return nil
}

func (m *TenGoodsInfo) GetThirdPromotionInfo() []*TenThirdPromotionInfo {
	if m != nil {
		return m.ThirdPromotionInfo
	}
	return nil
}

type TenThirdPromotionInfo struct {
	//是否计佣；1：是；0：否
	IsCalculated int32 `protobuf:"varint,1,opt,name=is_calculated,json=isCalculated,proto3" json:"is_calculated"`
	//不计佣原因
	NoCalculatedReason string `protobuf:"bytes,2,opt,name=no_calculated_reason,json=noCalculatedReason,proto3" json:"no_calculated_reason"`
	//佣金比例，2位小数，不带百分号，如 10% 填10.00，CPS业务必传
	CommissionRate float32 `protobuf:"fixed32,3,opt,name=commission_rate,json=commissionRate,proto3" json:"commission_rate"`
	//佣金金额（实际计佣金额*佣金比例/100），单位元，2位小数，CPS业务必传
	CommissionFee float32 `protobuf:"fixed32,4,opt,name=commission_fee,json=commissionFee,proto3" json:"commission_fee"`
	//实际计算佣金的商品金额，不包括运费、优惠券等的金额，单位元，2位小数，CPS业务必传
	ActualCommissionAmount float32 `protobuf:"fixed32,5,opt,name=actual_commission_amount,json=actualCommissionAmount,proto3" json:"actual_commission_amount"`
	//商品是否已结算；1：是；0：否
	IsSettle int32 `protobuf:"varint,6,opt,name=is_settle,json=isSettle,proto3" json:"is_settle"`
	//结算时间，unix时间戳 字段长度为 13 字节
	SettleTime           string   `protobuf:"bytes,7,opt,name=settle_time,json=settleTime,proto3" json:"settle_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenThirdPromotionInfo) Reset()         { *m = TenThirdPromotionInfo{} }
func (m *TenThirdPromotionInfo) String() string { return proto.CompactTextString(m) }
func (*TenThirdPromotionInfo) ProtoMessage()    {}
func (*TenThirdPromotionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{19}
}

func (m *TenThirdPromotionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenThirdPromotionInfo.Unmarshal(m, b)
}
func (m *TenThirdPromotionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenThirdPromotionInfo.Marshal(b, m, deterministic)
}
func (m *TenThirdPromotionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenThirdPromotionInfo.Merge(m, src)
}
func (m *TenThirdPromotionInfo) XXX_Size() int {
	return xxx_messageInfo_TenThirdPromotionInfo.Size(m)
}
func (m *TenThirdPromotionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenThirdPromotionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenThirdPromotionInfo proto.InternalMessageInfo

func (m *TenThirdPromotionInfo) GetIsCalculated() int32 {
	if m != nil {
		return m.IsCalculated
	}
	return 0
}

func (m *TenThirdPromotionInfo) GetNoCalculatedReason() string {
	if m != nil {
		return m.NoCalculatedReason
	}
	return ""
}

func (m *TenThirdPromotionInfo) GetCommissionRate() float32 {
	if m != nil {
		return m.CommissionRate
	}
	return 0
}

func (m *TenThirdPromotionInfo) GetCommissionFee() float32 {
	if m != nil {
		return m.CommissionFee
	}
	return 0
}

func (m *TenThirdPromotionInfo) GetActualCommissionAmount() float32 {
	if m != nil {
		return m.ActualCommissionAmount
	}
	return 0
}

func (m *TenThirdPromotionInfo) GetIsSettle() int32 {
	if m != nil {
		return m.IsSettle
	}
	return 0
}

func (m *TenThirdPromotionInfo) GetSettleTime() string {
	if m != nil {
		return m.SettleTime
	}
	return ""
}

type TenCommissionInfo struct {
	//佣金类型，枚举值如下：1：按比例提成；2：按金额提成
	CommissionType int32 `protobuf:"varint,1,opt,name=commission_type,json=commissionType,proto3" json:"commission_type"`
	//佣金金额，单位元
	CommissionFee        float32  `protobuf:"fixed32,2,opt,name=commission_fee,json=commissionFee,proto3" json:"commission_fee"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenCommissionInfo) Reset()         { *m = TenCommissionInfo{} }
func (m *TenCommissionInfo) String() string { return proto.CompactTextString(m) }
func (*TenCommissionInfo) ProtoMessage()    {}
func (*TenCommissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{20}
}

func (m *TenCommissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenCommissionInfo.Unmarshal(m, b)
}
func (m *TenCommissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenCommissionInfo.Marshal(b, m, deterministic)
}
func (m *TenCommissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenCommissionInfo.Merge(m, src)
}
func (m *TenCommissionInfo) XXX_Size() int {
	return xxx_messageInfo_TenCommissionInfo.Size(m)
}
func (m *TenCommissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenCommissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenCommissionInfo proto.InternalMessageInfo

func (m *TenCommissionInfo) GetCommissionType() int32 {
	if m != nil {
		return m.CommissionType
	}
	return 0
}

func (m *TenCommissionInfo) GetCommissionFee() float32 {
	if m != nil {
		return m.CommissionFee
	}
	return 0
}

type TenChanInfo struct {
	//小程序渠道
	ChanWxapp *TenChanWxapp `protobuf:"bytes,1,opt,name=chan_wxapp,json=chanWxapp,proto3" json:"chan_wxapp"`
	//自定义渠道
	ChanCustom *TenChanCustom `protobuf:"bytes,2,opt,name=chan_custom,json=chanCustom,proto3" json:"chan_custom"`
	//智慧零售入口小程序必传，来源小程序或公众号appid
	ChanReferAppId string `protobuf:"bytes,3,opt,name=chan_refer_app_id,json=chanReferAppId,proto3" json:"chan_refer_app_id"`
	//智慧零售入口小程序必传，引流渠道编码
	ChanId string `protobuf:"bytes,4,opt,name=chan_id,json=chanId,proto3" json:"chan_id"`
	//腾讯CPS追踪参数，CPS业务必填
	TxCpsId              string   `protobuf:"bytes,5,opt,name=tx_cps_id,json=txCpsId,proto3" json:"tx_cps_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenChanInfo) Reset()         { *m = TenChanInfo{} }
func (m *TenChanInfo) String() string { return proto.CompactTextString(m) }
func (*TenChanInfo) ProtoMessage()    {}
func (*TenChanInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{21}
}

func (m *TenChanInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenChanInfo.Unmarshal(m, b)
}
func (m *TenChanInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenChanInfo.Marshal(b, m, deterministic)
}
func (m *TenChanInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenChanInfo.Merge(m, src)
}
func (m *TenChanInfo) XXX_Size() int {
	return xxx_messageInfo_TenChanInfo.Size(m)
}
func (m *TenChanInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenChanInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenChanInfo proto.InternalMessageInfo

func (m *TenChanInfo) GetChanWxapp() *TenChanWxapp {
	if m != nil {
		return m.ChanWxapp
	}
	return nil
}

func (m *TenChanInfo) GetChanCustom() *TenChanCustom {
	if m != nil {
		return m.ChanCustom
	}
	return nil
}

func (m *TenChanInfo) GetChanReferAppId() string {
	if m != nil {
		return m.ChanReferAppId
	}
	return ""
}

func (m *TenChanInfo) GetChanId() string {
	if m != nil {
		return m.ChanId
	}
	return ""
}

func (m *TenChanInfo) GetTxCpsId() string {
	if m != nil {
		return m.TxCpsId
	}
	return ""
}

type TenChanCustom struct {
	//自定义渠道的标识符，是自定义渠道的最小粒度
	ChanCustomId string `protobuf:"bytes,1,opt,name=chan_custom_id,json=chanCustomId,proto3" json:"chan_custom_id"`
	//自定义渠道的描述
	ChanCustomIdDesc string `protobuf:"bytes,2,opt,name=chan_custom_id_desc,json=chanCustomIdDesc,proto3" json:"chan_custom_id_desc"`
	//3级自定义渠道的标识符，3级是针对4级的分类，要求4级数据必须存在
	ChanCustomCat_3 string `protobuf:"bytes,3,opt,name=chan_custom_cat_3,json=chanCustomCat3,proto3" json:"chan_custom_cat_3"`
	//3级自定义渠道的描述，若chan_custom_cat_3存在则必须存在
	ChanCustomCat_3Desc string `protobuf:"bytes,4,opt,name=chan_custom_cat_3_desc,json=chanCustomCat3Desc,proto3" json:"chan_custom_cat_3_desc"`
	//2级自定义渠道的标识符，2级是针对3级的分类，要求3、4级数据必须存在
	ChanCustomCat_2 string `protobuf:"bytes,5,opt,name=chan_custom_cat_2,json=chanCustomCat2,proto3" json:"chan_custom_cat_2"`
	//2级自定义渠道的描述，若chan_custom_cat_2存在则必须存在
	ChanCustomCat_2Desc string `protobuf:"bytes,6,opt,name=chan_custom_cat_2_desc,json=chanCustomCat2Desc,proto3" json:"chan_custom_cat_2_desc"`
	//1级自定义渠道的标识符，1级是针对2级的分类，要求2、3、4级数据必须存在
	ChanCustomCat_1 string `protobuf:"bytes,7,opt,name=chan_custom_cat_1,json=chanCustomCat1,proto3" json:"chan_custom_cat_1"`
	//1级自定义渠道的描述，若chan_custom_cat_1存在则必须存在
	ChanCustomCat_1Desc  string   `protobuf:"bytes,8,opt,name=chan_custom_cat_1_desc,json=chanCustomCat1Desc,proto3" json:"chan_custom_cat_1_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenChanCustom) Reset()         { *m = TenChanCustom{} }
func (m *TenChanCustom) String() string { return proto.CompactTextString(m) }
func (*TenChanCustom) ProtoMessage()    {}
func (*TenChanCustom) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{22}
}

func (m *TenChanCustom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenChanCustom.Unmarshal(m, b)
}
func (m *TenChanCustom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenChanCustom.Marshal(b, m, deterministic)
}
func (m *TenChanCustom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenChanCustom.Merge(m, src)
}
func (m *TenChanCustom) XXX_Size() int {
	return xxx_messageInfo_TenChanCustom.Size(m)
}
func (m *TenChanCustom) XXX_DiscardUnknown() {
	xxx_messageInfo_TenChanCustom.DiscardUnknown(m)
}

var xxx_messageInfo_TenChanCustom proto.InternalMessageInfo

func (m *TenChanCustom) GetChanCustomId() string {
	if m != nil {
		return m.ChanCustomId
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomIdDesc() string {
	if m != nil {
		return m.ChanCustomIdDesc
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_3() string {
	if m != nil {
		return m.ChanCustomCat_3
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_3Desc() string {
	if m != nil {
		return m.ChanCustomCat_3Desc
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_2() string {
	if m != nil {
		return m.ChanCustomCat_2
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_2Desc() string {
	if m != nil {
		return m.ChanCustomCat_2Desc
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_1() string {
	if m != nil {
		return m.ChanCustomCat_1
	}
	return ""
}

func (m *TenChanCustom) GetChanCustomCat_1Desc() string {
	if m != nil {
		return m.ChanCustomCat_1Desc
	}
	return ""
}

type TenChanWxapp struct {
	//小程序场景值
	ChanScene            string   `protobuf:"bytes,1,opt,name=chan_scene,json=chanScene,proto3" json:"chan_scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenChanWxapp) Reset()         { *m = TenChanWxapp{} }
func (m *TenChanWxapp) String() string { return proto.CompactTextString(m) }
func (*TenChanWxapp) ProtoMessage()    {}
func (*TenChanWxapp) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{23}
}

func (m *TenChanWxapp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenChanWxapp.Unmarshal(m, b)
}
func (m *TenChanWxapp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenChanWxapp.Marshal(b, m, deterministic)
}
func (m *TenChanWxapp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenChanWxapp.Merge(m, src)
}
func (m *TenChanWxapp) XXX_Size() int {
	return xxx_messageInfo_TenChanWxapp.Size(m)
}
func (m *TenChanWxapp) XXX_DiscardUnknown() {
	xxx_messageInfo_TenChanWxapp.DiscardUnknown(m)
}

var xxx_messageInfo_TenChanWxapp proto.InternalMessageInfo

func (m *TenChanWxapp) GetChanScene() string {
	if m != nil {
		return m.ChanScene
	}
	return ""
}

type TenStoreInfo struct {
	//主订单销售门店id
	ExternalStoreId string `protobuf:"bytes,1,opt,name=external_store_id,json=externalStoreId,proto3" json:"external_store_id"`
	//主订单销售门店名称
	StoreName string `protobuf:"bytes,2,opt,name=store_name,json=storeName,proto3" json:"store_name"`
	//主订单销售门店所属城市
	StoreCity            string   `protobuf:"bytes,3,opt,name=store_city,json=storeCity,proto3" json:"store_city"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenStoreInfo) Reset()         { *m = TenStoreInfo{} }
func (m *TenStoreInfo) String() string { return proto.CompactTextString(m) }
func (*TenStoreInfo) ProtoMessage()    {}
func (*TenStoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{24}
}

func (m *TenStoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenStoreInfo.Unmarshal(m, b)
}
func (m *TenStoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenStoreInfo.Marshal(b, m, deterministic)
}
func (m *TenStoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenStoreInfo.Merge(m, src)
}
func (m *TenStoreInfo) XXX_Size() int {
	return xxx_messageInfo_TenStoreInfo.Size(m)
}
func (m *TenStoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenStoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenStoreInfo proto.InternalMessageInfo

func (m *TenStoreInfo) GetExternalStoreId() string {
	if m != nil {
		return m.ExternalStoreId
	}
	return ""
}

func (m *TenStoreInfo) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *TenStoreInfo) GetStoreCity() string {
	if m != nil {
		return m.StoreCity
	}
	return ""
}

type TenUserInfo struct {
	//下单人 open_id，order_source = wxapp时，必填
	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//小程序或公众号的appid
	AppId string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id"`
	//下单人 union_id
	UnionId string `protobuf:"bytes,3,opt,name=union_id,json=unionId,proto3" json:"union_id"`
	//下单人手机号
	UserPhone string `protobuf:"bytes,4,opt,name=user_phone,json=userPhone,proto3" json:"user_phone"`
	//下单人用户 id
	UserId string `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//下单人会员号
	MemberId string `protobuf:"bytes,6,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	//下单人在KA注册后首次下单时间，格式为UNIX时间戳 字段长度为 13 字节
	UserFirstOrderTime   string   `protobuf:"bytes,7,opt,name=user_first_order_time,json=userFirstOrderTime,proto3" json:"user_first_order_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TenUserInfo) Reset()         { *m = TenUserInfo{} }
func (m *TenUserInfo) String() string { return proto.CompactTextString(m) }
func (*TenUserInfo) ProtoMessage()    {}
func (*TenUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{25}
}

func (m *TenUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TenUserInfo.Unmarshal(m, b)
}
func (m *TenUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TenUserInfo.Marshal(b, m, deterministic)
}
func (m *TenUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TenUserInfo.Merge(m, src)
}
func (m *TenUserInfo) XXX_Size() int {
	return xxx_messageInfo_TenUserInfo.Size(m)
}
func (m *TenUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TenUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TenUserInfo proto.InternalMessageInfo

func (m *TenUserInfo) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *TenUserInfo) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TenUserInfo) GetUnionId() string {
	if m != nil {
		return m.UnionId
	}
	return ""
}

func (m *TenUserInfo) GetUserPhone() string {
	if m != nil {
		return m.UserPhone
	}
	return ""
}

func (m *TenUserInfo) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TenUserInfo) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *TenUserInfo) GetUserFirstOrderTime() string {
	if m != nil {
		return m.UserFirstOrderTime
	}
	return ""
}

type AddReturnOrderToTencentReq struct {
	//数据源id（create方法返回的data.dataSource.id）
	DataSourceId         string                 `protobuf:"bytes,1,opt,name=dataSourceId,proto3" json:"dataSourceId"`
	Orders               []*TencentReturnOrders `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddReturnOrderToTencentReq) Reset()         { *m = AddReturnOrderToTencentReq{} }
func (m *AddReturnOrderToTencentReq) String() string { return proto.CompactTextString(m) }
func (*AddReturnOrderToTencentReq) ProtoMessage()    {}
func (*AddReturnOrderToTencentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{26}
}

func (m *AddReturnOrderToTencentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddReturnOrderToTencentReq.Unmarshal(m, b)
}
func (m *AddReturnOrderToTencentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddReturnOrderToTencentReq.Marshal(b, m, deterministic)
}
func (m *AddReturnOrderToTencentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddReturnOrderToTencentReq.Merge(m, src)
}
func (m *AddReturnOrderToTencentReq) XXX_Size() int {
	return xxx_messageInfo_AddReturnOrderToTencentReq.Size(m)
}
func (m *AddReturnOrderToTencentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddReturnOrderToTencentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddReturnOrderToTencentReq proto.InternalMessageInfo

func (m *AddReturnOrderToTencentReq) GetDataSourceId() string {
	if m != nil {
		return m.DataSourceId
	}
	return ""
}

func (m *AddReturnOrderToTencentReq) GetOrders() []*TencentReturnOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

type TencentReturnOrders struct {
	//商家退货退款单号
	ExternalReturnOrderId string `protobuf:"bytes,1,opt,name=external_return_order_id,json=externalReturnOrderId,proto3" json:"external_return_order_id"`
	//关联的订单号
	ExternalOrderId string `protobuf:"bytes,2,opt,name=external_order_id,json=externalOrderId,proto3" json:"external_order_id"`
	//退货退款单创建时间，unix时间戳 字段长度为 13 字节
	ReturnCreateTime string `protobuf:"bytes,3,opt,name=return_create_time,json=returnCreateTime,proto3" json:"return_create_time"`
	//该笔退货退款单的商品数量
	ReturnNum int64 `protobuf:"varint,4,opt,name=return_num,json=returnNum,proto3" json:"return_num"`
	//该笔退货退款单的商品退款金额，单位元，2位小数
	ReturnAmount float32 `protobuf:"fixed32,5,opt,name=return_amount,json=returnAmount,proto3" json:"return_amount"`
	//运费退款金额，单位元，2位小数注：运费为0时，传0
	ReturnFreightAmount float32 `protobuf:"fixed32,6,opt,name=return_freight_amount,json=returnFreightAmount,proto3" json:"return_freight_amount"`
	//该笔退货退款单的退款金额，单位元，2位小数注：商品退款金额+运费退款金额=订单金额
	ReturnOrderAmount float32 `protobuf:"fixed32,7,opt,name=return_order_amount,json=returnOrderAmount,proto3" json:"return_order_amount"`
	//退货退款单状态，传1290（退货退款完成）
	ReturnOrderStatus string `protobuf:"bytes,8,opt,name=return_order_status,json=returnOrderStatus,proto3" json:"return_order_status"`
	//状态变更时间，unix毫秒级时间
	StatusChangeTime string `protobuf:"bytes,9,opt,name=status_change_time,json=statusChangeTime,proto3" json:"status_change_time"`
	//主订单商品信息，数组类型，每个sku存一个数组单位
	ReturnGoodsInfo      []*ReturnGoodsInfo `protobuf:"bytes,10,rep,name=return_goods_info,json=returnGoodsInfo,proto3" json:"return_goods_info"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *TencentReturnOrders) Reset()         { *m = TencentReturnOrders{} }
func (m *TencentReturnOrders) String() string { return proto.CompactTextString(m) }
func (*TencentReturnOrders) ProtoMessage()    {}
func (*TencentReturnOrders) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{27}
}

func (m *TencentReturnOrders) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TencentReturnOrders.Unmarshal(m, b)
}
func (m *TencentReturnOrders) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TencentReturnOrders.Marshal(b, m, deterministic)
}
func (m *TencentReturnOrders) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TencentReturnOrders.Merge(m, src)
}
func (m *TencentReturnOrders) XXX_Size() int {
	return xxx_messageInfo_TencentReturnOrders.Size(m)
}
func (m *TencentReturnOrders) XXX_DiscardUnknown() {
	xxx_messageInfo_TencentReturnOrders.DiscardUnknown(m)
}

var xxx_messageInfo_TencentReturnOrders proto.InternalMessageInfo

func (m *TencentReturnOrders) GetExternalReturnOrderId() string {
	if m != nil {
		return m.ExternalReturnOrderId
	}
	return ""
}

func (m *TencentReturnOrders) GetExternalOrderId() string {
	if m != nil {
		return m.ExternalOrderId
	}
	return ""
}

func (m *TencentReturnOrders) GetReturnCreateTime() string {
	if m != nil {
		return m.ReturnCreateTime
	}
	return ""
}

func (m *TencentReturnOrders) GetReturnNum() int64 {
	if m != nil {
		return m.ReturnNum
	}
	return 0
}

func (m *TencentReturnOrders) GetReturnAmount() float32 {
	if m != nil {
		return m.ReturnAmount
	}
	return 0
}

func (m *TencentReturnOrders) GetReturnFreightAmount() float32 {
	if m != nil {
		return m.ReturnFreightAmount
	}
	return 0
}

func (m *TencentReturnOrders) GetReturnOrderAmount() float32 {
	if m != nil {
		return m.ReturnOrderAmount
	}
	return 0
}

func (m *TencentReturnOrders) GetReturnOrderStatus() string {
	if m != nil {
		return m.ReturnOrderStatus
	}
	return ""
}

func (m *TencentReturnOrders) GetStatusChangeTime() string {
	if m != nil {
		return m.StatusChangeTime
	}
	return ""
}

func (m *TencentReturnOrders) GetReturnGoodsInfo() []*ReturnGoodsInfo {
	if m != nil {
		return m.ReturnGoodsInfo
	}
	return nil
}

type ReturnGoodsInfo struct {
	//sku 编号
	ExternalSkuId string `protobuf:"bytes,1,opt,name=external_sku_id,json=externalSkuId,proto3" json:"external_sku_id"`
	//sku 名称
	SkuNameChinese string `protobuf:"bytes,2,opt,name=sku_name_chinese,json=skuNameChinese,proto3" json:"sku_name_chinese"`
	//是否赠品，0代表非赠品，1代表赠品
	IsGift int64 `protobuf:"varint,3,opt,name=is_gift,json=isGift,proto3" json:"is_gift"`
	//sku 所属 spu 编号，若无 spu，传输内容请与 external_sku_id 保持一致
	ExternalSpuId string `protobuf:"bytes,4,opt,name=external_spu_id,json=externalSpuId,proto3" json:"external_spu_id"`
	//spu 名称，若无 spu，传输内容请与 sku_name_chinese 保持一致
	SpuNameChinese string `protobuf:"bytes,5,opt,name=spu_name_chinese,json=spuNameChinese,proto3" json:"spu_name_chinese"`
	//退货商品数量
	ReturnGoodsNum int64 `protobuf:"varint,6,opt,name=return_goods_num,json=returnGoodsNum,proto3" json:"return_goods_num"`
	//退货商品金额，单位元，两位小数
	ReturnGoodsAmount    float32  `protobuf:"fixed32,7,opt,name=return_goods_amount,json=returnGoodsAmount,proto3" json:"return_goods_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReturnGoodsInfo) Reset()         { *m = ReturnGoodsInfo{} }
func (m *ReturnGoodsInfo) String() string { return proto.CompactTextString(m) }
func (*ReturnGoodsInfo) ProtoMessage()    {}
func (*ReturnGoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_36ed65dc2bd1538c, []int{28}
}

func (m *ReturnGoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnGoodsInfo.Unmarshal(m, b)
}
func (m *ReturnGoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnGoodsInfo.Marshal(b, m, deterministic)
}
func (m *ReturnGoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnGoodsInfo.Merge(m, src)
}
func (m *ReturnGoodsInfo) XXX_Size() int {
	return xxx_messageInfo_ReturnGoodsInfo.Size(m)
}
func (m *ReturnGoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnGoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnGoodsInfo proto.InternalMessageInfo

func (m *ReturnGoodsInfo) GetExternalSkuId() string {
	if m != nil {
		return m.ExternalSkuId
	}
	return ""
}

func (m *ReturnGoodsInfo) GetSkuNameChinese() string {
	if m != nil {
		return m.SkuNameChinese
	}
	return ""
}

func (m *ReturnGoodsInfo) GetIsGift() int64 {
	if m != nil {
		return m.IsGift
	}
	return 0
}

func (m *ReturnGoodsInfo) GetExternalSpuId() string {
	if m != nil {
		return m.ExternalSpuId
	}
	return ""
}

func (m *ReturnGoodsInfo) GetSpuNameChinese() string {
	if m != nil {
		return m.SpuNameChinese
	}
	return ""
}

func (m *ReturnGoodsInfo) GetReturnGoodsNum() int64 {
	if m != nil {
		return m.ReturnGoodsNum
	}
	return 0
}

func (m *ReturnGoodsInfo) GetReturnGoodsAmount() float32 {
	if m != nil {
		return m.ReturnGoodsAmount
	}
	return 0
}

func init() {
	proto.RegisterType((*PushOrderStatusReq)(nil), "ext.PushOrderStatusReq")
	proto.RegisterType((*OrderStatusInfo)(nil), "ext.OrderStatusInfo")
	proto.RegisterType((*PushOrderSumReq)(nil), "ext.PushOrderSumReq")
	proto.RegisterType((*OrdersSum)(nil), "ext.OrdersSum")
	proto.RegisterType((*PushVisitPageReq)(nil), "ext.PushVisitPageReq")
	proto.RegisterType((*PushVisitPageRes)(nil), "ext.PushVisitPageRes")
	proto.RegisterType((*AddOrdersToTencentReq)(nil), "ext.AddOrdersToTencentReq")
	proto.RegisterType((*BaseResponse)(nil), "ext.BaseResponse")
	proto.RegisterType((*TencentOrders)(nil), "ext.TencentOrders")
	proto.RegisterType((*TenTargetUrl)(nil), "ext.TenTargetUrl")
	proto.RegisterType((*TenInvoiceInfo)(nil), "ext.TenInvoiceInfo")
	proto.RegisterType((*TenExpressInfo)(nil), "ext.TenExpressInfo")
	proto.RegisterType((*TenExpressPackageInfo)(nil), "ext.TenExpressPackageInfo")
	proto.RegisterType((*TenExpressPackInfo)(nil), "ext.TenExpressPackInfo")
	proto.RegisterType((*TenExpressPage)(nil), "ext.TenExpressPage")
	proto.RegisterType((*TenPaymentInfo)(nil), "ext.TenPaymentInfo")
	proto.RegisterType((*TenCouponInfo)(nil), "ext.TenCouponInfo")
	proto.RegisterType((*TenCouponDetail)(nil), "ext.TenCouponDetail")
	proto.RegisterType((*TenGoodsInfo)(nil), "ext.TenGoodsInfo")
	proto.RegisterType((*TenThirdPromotionInfo)(nil), "ext.TenThirdPromotionInfo")
	proto.RegisterType((*TenCommissionInfo)(nil), "ext.TenCommissionInfo")
	proto.RegisterType((*TenChanInfo)(nil), "ext.TenChanInfo")
	proto.RegisterType((*TenChanCustom)(nil), "ext.TenChanCustom")
	proto.RegisterType((*TenChanWxapp)(nil), "ext.TenChanWxapp")
	proto.RegisterType((*TenStoreInfo)(nil), "ext.TenStoreInfo")
	proto.RegisterType((*TenUserInfo)(nil), "ext.TenUserInfo")
	proto.RegisterType((*AddReturnOrderToTencentReq)(nil), "ext.AddReturnOrderToTencentReq")
	proto.RegisterType((*TencentReturnOrders)(nil), "ext.TencentReturnOrders")
	proto.RegisterType((*ReturnGoodsInfo)(nil), "ext.ReturnGoodsInfo")
}

func init() { proto.RegisterFile("ext/external-tencent.proto", fileDescriptor_36ed65dc2bd1538c) }

var fileDescriptor_36ed65dc2bd1538c = []byte{
	// 2792 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xcd, 0x72, 0x1b, 0xc7,
	0xf1, 0x2f, 0x12, 0x02, 0x08, 0x34, 0x00, 0x82, 0x1c, 0x7e, 0x08, 0x96, 0xcb, 0x65, 0x79, 0xfd,
	0xb7, 0xff, 0xf2, 0x87, 0x14, 0x91, 0xb2, 0xe2, 0xe4, 0x90, 0x4a, 0x68, 0x2a, 0xb6, 0x51, 0x65,
	0x2b, 0xf4, 0x92, 0xb2, 0x4f, 0xa9, 0xad, 0xe5, 0xee, 0x00, 0xd8, 0x12, 0xb0, 0xbb, 0xda, 0x9d,
	0x95, 0xc9, 0x63, 0x5e, 0xc0, 0x79, 0x80, 0xbc, 0x40, 0x2a, 0x55, 0xb9, 0xe6, 0x98, 0xa7, 0xf0,
	0x21, 0x55, 0xb9, 0xe4, 0x98, 0x43, 0x0e, 0x79, 0x84, 0x54, 0x77, 0xcf, 0xec, 0xce, 0x02, 0xcb,
	0x94, 0x54, 0x95, 0x1b, 0xb6, 0xfb, 0xd7, 0x3d, 0xdd, 0xd3, 0x3d, 0xdd, 0x3d, 0x03, 0xb8, 0x23,
	0xaf, 0xd4, 0x4f, 0xe4, 0x95, 0x92, 0x59, 0xec, 0x2f, 0xee, 0x2b, 0x19, 0x07, 0x32, 0x56, 0x0f,
	0xd2, 0x2c, 0x51, 0x89, 0x68, 0xc9, 0x2b, 0xe5, 0x4c, 0x41, 0x9c, 0x15, 0xf9, 0xfc, 0x37, 0x59,
	0x28, 0xb3, 0x73, 0xe5, 0xab, 0x22, 0x77, 0xe5, 0x0b, 0xe1, 0xc0, 0x20, 0xf4, 0x95, 0x7f, 0x9e,
	0x14, 0x59, 0x20, 0x27, 0xe1, 0x78, 0xe3, 0xee, 0xc6, 0xbd, 0x9e, 0x5b, 0xa3, 0x89, 0x8f, 0xa1,
	0x93, 0xa0, 0x54, 0x3e, 0xde, 0xbc, 0xdb, 0xba, 0xd7, 0x3f, 0xde, 0x7f, 0x20, 0xaf, 0xd4, 0x03,
	0x4b, 0xd1, 0x24, 0x9e, 0x26, 0xae, 0xc6, 0x38, 0x3f, 0x6c, 0xc0, 0x68, 0x85, 0x27, 0x3e, 0x84,
	0x5d, 0x63, 0x9a, 0x47, 0x30, 0x2f, 0x32, 0x4b, 0x8d, 0x0c, 0x83, 0x64, 0x26, 0xa1, 0x78, 0x07,
	0x06, 0x0c, 0xc9, 0x49, 0x7e, 0xbc, 0x49, 0xb0, 0x7e, 0x52, 0xa9, 0x14, 0x1f, 0x83, 0x60, 0xa6,
	0x17, 0xcc, 0xfd, 0x78, 0x26, 0x3d, 0x15, 0x2d, 0xe5, 0xb8, 0x45, 0xc0, 0x1d, 0xe6, 0x9c, 0x12,
	0xe3, 0x22, 0x5a, 0x4a, 0xe7, 0xb7, 0x30, 0xaa, 0x1c, 0x2f, 0x96, 0xaf, 0xea, 0xf5, 0xfb, 0x2b,
	0x5e, 0x6f, 0x57, 0x5e, 0xe7, 0xa8, 0xc6, 0xf8, 0xfb, 0xb7, 0x0d, 0xe8, 0x95, 0x54, 0xf1, 0x06,
	0x74, 0x33, 0x39, 0xf5, 0x42, 0x5f, 0x49, 0xad, 0x75, 0x2b, 0x93, 0xd3, 0x27, 0xbe, 0x92, 0xe2,
	0x08, 0x0e, 0x66, 0xd1, 0x4b, 0xa9, 0x37, 0xc0, 0x5f, 0x26, 0x45, 0xac, 0xbc, 0xbc, 0x58, 0x92,
	0x87, 0x9b, 0xae, 0x40, 0x26, 0x29, 0x3a, 0x21, 0x16, 0x6a, 0xfb, 0x08, 0x84, 0x25, 0x12, 0x17,
	0x4b, 0xc2, 0xa3, 0xa3, 0x6d, 0x77, 0x54, 0xe2, 0x9f, 0x16, 0x4b, 0x04, 0x7f, 0x0c, 0x22, 0xf5,
	0xaf, 0x97, 0x32, 0x56, 0xb6, 0xf2, 0x5b, 0xa4, 0x7c, 0x47, 0x73, 0x2a, 0xd5, 0x0e, 0x0c, 0x53,
	0xff, 0x5a, 0x86, 0xa5, 0xd6, 0x36, 0x69, 0xed, 0x13, 0x91, 0x35, 0x3a, 0x5f, 0xc1, 0x0e, 0xee,
	0xdc, 0xb7, 0x51, 0x1e, 0xa9, 0x33, 0x7f, 0x26, 0x71, 0xeb, 0xde, 0x02, 0xb8, 0x94, 0xb3, 0x28,
	0xb6, 0x5d, 0xec, 0x11, 0x85, 0x9c, 0x7c, 0x03, 0xba, 0x32, 0x0e, 0x99, 0xc9, 0x91, 0xdb, 0x92,
	0x71, 0x88, 0x2c, 0xe7, 0xdb, 0x35, 0x6d, 0xb9, 0x10, 0x70, 0x2b, 0x48, 0x42, 0xd6, 0xd3, 0x76,
	0xe9, 0xb7, 0x18, 0xc3, 0xd6, 0x52, 0xe6, 0xb9, 0x3f, 0x2b, 0x35, 0xe8, 0x4f, 0xb1, 0x0f, 0x6d,
	0x99, 0x65, 0x49, 0xa6, 0x43, 0xcd, 0x1f, 0xce, 0x0c, 0x0e, 0x4e, 0xc2, 0x90, 0x43, 0x70, 0x91,
	0x5c, 0x70, 0xe6, 0xbf, 0x6a, 0x94, 0x3f, 0x5c, 0x89, 0xb2, 0xa0, 0x28, 0x6b, 0x25, 0xac, 0xb3,
	0x8c, 0xb4, 0x0b, 0x83, 0xcf, 0xfc, 0x1c, 0xed, 0x4e, 0x93, 0x38, 0x97, 0xff, 0x13, 0xe3, 0xff,
	0xb5, 0x05, 0xc3, 0xda, 0x6a, 0xaf, 0x75, 0x56, 0xde, 0x86, 0x7e, 0x90, 0x49, 0x5f, 0xe9, 0x13,
	0xc0, 0x2b, 0x02, 0x93, 0x30, 0xf7, 0xad, 0xc3, 0x44, 0x0e, 0xeb, 0xb5, 0xf5, 0x61, 0x22, 0x12,
	0x06, 0x94, 0x21, 0xea, 0x3a, 0x95, 0x94, 0x2e, 0x6d, 0xb7, 0x47, 0x94, 0x8b, 0xeb, 0x94, 0x02,
	0x7a, 0x99, 0xf9, 0x71, 0x88, 0x56, 0xb4, 0xd9, 0x23, 0xfa, 0x9e, 0x84, 0x94, 0x0a, 0xc4, 0x8a,
	0xfd, 0xa5, 0x1c, 0x77, 0x74, 0x2a, 0x20, 0xe5, 0xa9, 0xbf, 0x94, 0xe2, 0x7d, 0x18, 0xcd, 0x92,
	0x24, 0xcc, 0x29, 0xc3, 0x54, 0xa2, 0xfc, 0xc5, 0x78, 0xeb, 0xee, 0xc6, 0xbd, 0x96, 0x3b, 0x24,
	0xf2, 0xd3, 0x62, 0x79, 0x81, 0x44, 0xb4, 0x91, 0x71, 0xdf, 0xcb, 0x68, 0x36, 0x57, 0xe3, 0x2e,
	0x65, 0x6c, 0x9f, 0x68, 0xdf, 0x11, 0x09, 0x53, 0x9b, 0x21, 0x3a, 0xb1, 0x59, 0x5b, 0x8f, 0x53,
	0x9b, 0x38, 0x9c, 0xd8, 0xac, 0xf0, 0x3d, 0xd8, 0x9e, 0x66, 0x24, 0xa8, 0xf1, 0x63, 0x20, 0xe4,
	0x50, 0x53, 0x19, 0x5b, 0xed, 0x8d, 0x06, 0xf5, 0x79, 0xdd, 0xa4, 0x3a, 0x82, 0xa8, 0x29, 0xf5,
	0xaf, 0xfd, 0xcb, 0x85, 0x34, 0xa0, 0x01, 0x6b, 0xd2, 0xd4, 0x1a, 0xcc, 0x3a, 0x79, 0xe3, 0x61,
	0x09, 0xab, 0x4e, 0xdd, 0x5a, 0x65, 0xdb, 0x7e, 0xd5, 0xca, 0x36, 0x6a, 0xae, 0x6c, 0xe2, 0x3e,
	0xf4, 0x8a, 0x1c, 0x13, 0x24, 0x9e, 0x26, 0xe3, 0x9d, 0xbb, 0x1b, 0xf7, 0xfa, 0xc7, 0x3b, 0x26,
	0x7f, 0x9f, 0xe5, 0x32, 0xa3, 0xba, 0xdc, 0x2d, 0xf4, 0x2f, 0xf1, 0x10, 0x80, 0x77, 0x91, 0xf0,
	0xbb, 0x94, 0xef, 0xbb, 0x06, 0xff, 0x05, 0x72, 0x48, 0xa0, 0x37, 0x33, 0x3f, 0xc5, 0x23, 0xe8,
	0x07, 0x49, 0x91, 0x26, 0x31, 0x8b, 0x88, 0xfa, 0x11, 0x39, 0x25, 0x16, 0xc9, 0x40, 0x50, 0xfe,
	0x16, 0x3f, 0x85, 0x81, 0xd9, 0x0d, 0x92, 0xda, 0x23, 0xa9, 0x3d, 0x23, 0x75, 0xc6, 0x3c, 0x12,
	0xeb, 0xa7, 0xd5, 0x07, 0xca, 0xc9, 0xab, 0x34, 0x93, 0xb9, 0x36, 0x70, 0x9f, 0x1c, 0x2a, 0xe5,
	0x7e, 0xcd, 0x3c, 0x96, 0x93, 0xd5, 0x07, 0xca, 0x45, 0xf1, 0xcb, 0x24, 0x0a, 0x24, 0xcb, 0x1d,
	0xd4, 0xd7, 0x9b, 0x30, 0x8f, 0xe5, 0xa2, 0xea, 0x03, 0xc3, 0x91, 0x26, 0x51, 0xac, 0x72, 0x9d,
	0x4e, 0x87, 0x1c, 0x7f, 0xa6, 0x71, 0x26, 0x3d, 0x04, 0x50, 0x7e, 0x36, 0x93, 0xca, 0x2b, 0xb2,
	0xc5, 0xf8, 0x36, 0x19, 0x54, 0xee, 0xd8, 0x05, 0x71, 0x9e, 0x65, 0x0b, 0xb7, 0xa7, 0xcc, 0x4f,
	0x3c, 0x13, 0x51, 0xee, 0x85, 0x72, 0x21, 0x95, 0x0c, 0xc7, 0x63, 0x3e, 0x4d, 0x51, 0xfe, 0x84,
	0x09, 0xce, 0x0f, 0x9b, 0x30, 0xb0, 0x45, 0xc5, 0xff, 0xc3, 0xa8, 0xc8, 0x16, 0xde, 0x32, 0x8a,
	0xa3, 0x34, 0x4b, 0x66, 0x99, 0xbf, 0xd4, 0x67, 0x7d, 0xbb, 0xc8, 0x16, 0x5f, 0x57, 0x54, 0xf1,
	0x11, 0xec, 0x5a, 0x20, 0xcf, 0x4f, 0xd3, 0x28, 0xd4, 0x07, 0x7e, 0xc7, 0x62, 0x9c, 0x20, 0x5d,
	0x1c, 0xc1, 0xbe, 0x0d, 0xc6, 0x0c, 0xa0, 0x33, 0xca, 0xc7, 0x7f, 0xcf, 0xe2, 0x3d, 0xd3, 0x2c,
	0xcc, 0xbc, 0x15, 0x43, 0xbc, 0x17, 0x2f, 0xa8, 0x1c, 0xf4, 0xdc, 0x9d, 0xba, 0x2d, 0xdf, 0xbc,
	0x10, 0x0f, 0xeb, 0x0b, 0x90, 0x35, 0x88, 0xe7, 0x0a, 0x21, 0x56, 0x0d, 0xfa, 0xe6, 0x85, 0x38,
	0x80, 0x0e, 0xea, 0x9f, 0x3f, 0xd6, 0x85, 0xa2, 0x5d, 0x64, 0x8b, 0x2f, 0x1f, 0x3b, 0x7f, 0x6a,
	0xc3, 0x76, 0x3d, 0x48, 0x58, 0x37, 0xa2, 0xa9, 0x17, 0x4b, 0x19, 0x7a, 0x3a, 0x5c, 0xb4, 0x25,
	0x5d, 0x77, 0x18, 0x4d, 0x9f, 0x4a, 0x19, 0x6a, 0x2c, 0xc6, 0xcf, 0xc4, 0x9d, 0x4a, 0x97, 0x1e,
	0x14, 0x34, 0x8d, 0x8a, 0xd7, 0xbb, 0x30, 0x2c, 0x21, 0x91, 0x5a, 0x98, 0x0d, 0x30, 0x72, 0x17,
	0x48, 0xc3, 0x10, 0x18, 0x50, 0x90, 0xc4, 0x4a, 0xc6, 0x4a, 0xbb, 0xbd, 0xad, 0xc9, 0xa7, 0x4c,
	0x15, 0xc7, 0x70, 0x60, 0x80, 0x7e, 0x18, 0x46, 0x2a, 0x32, 0xe7, 0x82, 0xbd, 0xde, 0xd3, 0xcc,
	0x13, 0xcd, 0x23, 0x67, 0x6a, 0xca, 0x97, 0xa9, 0x1f, 0x5f, 0x6b, 0xff, 0x2b, 0xe5, 0x44, 0x15,
	0x1f, 0xc0, 0x4e, 0x69, 0xaa, 0x7f, 0x85, 0x5d, 0x38, 0xa3, 0x72, 0xd9, 0x73, 0x8d, 0x82, 0x0b,
	0x4d, 0x46, 0x68, 0x26, 0x67, 0x51, 0xae, 0xb2, 0x6b, 0x34, 0x04, 0x0f, 0x02, 0x15, 0xcd, 0x9e,
	0x3b, 0x32, 0xf4, 0x13, 0x26, 0x63, 0x65, 0x2a, 0xa1, 0xe9, 0x3c, 0x89, 0x25, 0x15, 0xcd, 0x9e,
	0x3b, 0x34, 0xd4, 0x33, 0x24, 0x62, 0xf0, 0x4b, 0xd8, 0xa5, 0x1f, 0x3f, 0xe7, 0x8a, 0x0e, 0x1c,
	0x7c, 0xc3, 0xf9, 0xcc, 0x8f, 0x9f, 0x53, 0x61, 0x3f, 0x86, 0x83, 0x3a, 0xda, 0x0f, 0x82, 0xb2,
	0x82, 0xf6, 0xdc, 0x3d, 0x5b, 0xe0, 0x84, 0x59, 0xe2, 0x67, 0x30, 0x36, 0xee, 0x85, 0x72, 0x11,
	0xbd, 0x94, 0x96, 0xed, 0x03, 0x12, 0x3b, 0xd4, 0xfc, 0x27, 0x9a, 0x6d, 0x5c, 0xb0, 0x76, 0xbd,
	0x94, 0x24, 0xf3, 0x86, 0xb5, 0x5d, 0x37, 0x62, 0x64, 0xe1, 0x27, 0x70, 0xb8, 0x26, 0xc3, 0xee,
	0x73, 0xcd, 0xdd, 0x5f, 0x11, 0xe2, 0x5d, 0x78, 0x1b, 0x4c, 0xf2, 0x60, 0xcb, 0xd2, 0x55, 0x17,
	0x34, 0xe9, 0x69, 0xb1, 0x74, 0xfe, 0x79, 0x8b, 0x92, 0xd5, 0xaa, 0x44, 0x18, 0x8b, 0x45, 0x82,
	0xee, 0x46, 0x41, 0x6e, 0xea, 0xba, 0x6e, 0xd6, 0x25, 0xbd, 0xaa, 0xed, 0x5c, 0x7e, 0xa9, 0xdc,
	0x98, 0x6e, 0xb7, 0x69, 0x35, 0x31, 0x2a, 0x3a, 0xba, 0xe5, 0xbd, 0x0b, 0xc3, 0x4c, 0x06, 0x12,
	0xcd, 0xf3, 0xac, 0xb3, 0x3b, 0x30, 0x44, 0xf2, 0x93, 0xc2, 0xab, 0x41, 0xec, 0xdf, 0x2d, 0x13,
	0x5e, 0xa6, 0xb2, 0x63, 0x94, 0x30, 0x1a, 0x66, 0x36, 0xbd, 0x6d, 0x12, 0x86, 0xe9, 0xd6, 0x6e,
	0x97, 0x50, 0x8a, 0x5c, 0x76, 0xed, 0xd1, 0x90, 0xd3, 0x31, 0xb1, 0x65, 0xe6, 0x29, 0xf3, 0x4e,
	0x71, 0xe6, 0xf9, 0x04, 0x0e, 0x2b, 0x2b, 0xb2, 0xe4, 0x65, 0x14, 0x53, 0xb6, 0x87, 0x52, 0x27,
	0xf0, 0x7e, 0x69, 0x8d, 0x66, 0x92, 0x14, 0xe5, 0x9c, 0x59, 0x29, 0x52, 0x7a, 0x99, 0xae, 0xc9,
	0x39, 0xbd, 0x4c, 0xa4, 0xd6, 0xd7, 0x08, 0x31, 0xbb, 0xa2, 0x40, 0xb1, 0x44, 0xaf, 0xbe, 0xc6,
	0x13, 0xcd, 0x34, 0x52, 0xf2, 0x2a, 0x95, 0x81, 0x92, 0x61, 0x95, 0x08, 0xd4, 0x52, 0x39, 0xb7,
	0xf7, 0x0d, 0xd7, 0x24, 0x02, 0xb5, 0xd5, 0x46, 0x29, 0x9a, 0x68, 0xfb, 0xcd, 0x52, 0x34, 0xf9,
	0x7e, 0x05, 0xfb, 0xa6, 0x7d, 0xa5, 0x7e, 0xf0, 0xdc, 0x9f, 0xe9, 0x76, 0x34, 0xa0, 0x76, 0x74,
	0x67, 0xa5, 0x8d, 0x9d, 0x31, 0x84, 0xba, 0x92, 0x90, 0x6b, 0x34, 0xe7, 0xaf, 0x9b, 0x70, 0xd0,
	0x88, 0xc6, 0xd2, 0x6b, 0xd6, 0xd1, 0x15, 0xc5, 0x2b, 0xa7, 0xd0, 0x5e, 0xa9, 0x4b, 0x97, 0x15,
	0xda, 0x85, 0x06, 0x09, 0xca, 0xa8, 0xcd, 0x26, 0x09, 0xca, 0xab, 0x77, 0xaa, 0x56, 0x4c, 0xba,
	0xf5, 0xd8, 0x58, 0x22, 0x43, 0x29, 0xde, 0x84, 0x5e, 0x3e, 0x8f, 0x52, 0xde, 0x4d, 0xce, 0xba,
	0x2e, 0x12, 0x68, 0x07, 0xad, 0x56, 0x9e, 0xe2, 0x28, 0xdc, 0x6e, 0x6c, 0xe5, 0x74, 0x09, 0xe8,
	0xcb, 0xea, 0x43, 0x4c, 0x6e, 0xd8, 0xc3, 0x0e, 0xed, 0xe1, 0xed, 0x86, 0x3d, 0xbc, 0x71, 0x03,
	0x2f, 0x40, 0xac, 0x23, 0xb1, 0xb7, 0x94, 0xc3, 0x75, 0xfe, 0xbc, 0xa8, 0x46, 0xeb, 0xa1, 0x21,
	0x9f, 0x3f, 0x2f, 0x26, 0xa1, 0x38, 0x84, 0x4e, 0x5c, 0x2c, 0x2f, 0x65, 0x46, 0x9b, 0xd4, 0x72,
	0xf5, 0x97, 0xf3, 0xfb, 0x0d, 0xbb, 0x02, 0x90, 0xcd, 0x1f, 0x80, 0xdd, 0x7f, 0xbd, 0xd4, 0x57,
	0x73, 0x53, 0x01, 0x2c, 0xfa, 0x99, 0xaf, 0xe6, 0xaf, 0xd7, 0xc3, 0xdf, 0x83, 0x6d, 0x1b, 0x3c,
	0x7f, 0xac, 0xa3, 0x30, 0xb4, 0xa8, 0x5f, 0x3e, 0x76, 0x5e, 0x90, 0x41, 0xd6, 0x50, 0x45, 0x73,
	0x8d, 0x9e, 0xbf, 0xa8, 0x2f, 0xb2, 0x31, 0x66, 0xd4, 0x32, 0x43, 0xbd, 0xca, 0xfc, 0x38, 0xf7,
	0xca, 0xf5, 0xb7, 0xe8, 0x9b, 0xaf, 0xdf, 0xcc, 0xd2, 0x93, 0x6c, 0x8b, 0xa7, 0x22, 0xa2, 0xf1,
	0x1c, 0xeb, 0xfc, 0x61, 0x83, 0xee, 0x2c, 0xd5, 0xf8, 0x47, 0xf7, 0x10, 0x9e, 0x13, 0xcb, 0x15,
	0xdb, 0x66, 0x26, 0xa4, 0x05, 0x1f, 0xc0, 0x9e, 0x06, 0xd4, 0x26, 0x78, 0x2e, 0x7e, 0xbb, 0xcc,
	0xb2, 0x47, 0xf8, 0x9f, 0xc3, 0x50, 0xe3, 0x43, 0xa9, 0xfc, 0x68, 0x31, 0x6e, 0x59, 0x2f, 0x0f,
	0xe5, 0xda, 0x4f, 0x88, 0xe7, 0x0e, 0x02, 0xeb, 0xcb, 0xf9, 0xf3, 0x06, 0x8c, 0x56, 0x10, 0x58,
	0x6b, 0xca, 0xb0, 0x9b, 0x81, 0xd6, 0x44, 0x7e, 0xc7, 0x70, 0xb4, 0x3f, 0x78, 0xf3, 0x1f, 0x69,
	0xd0, 0xa5, 0xaf, 0x82, 0x79, 0xb5, 0x49, 0xda, 0xa6, 0xcf, 0x90, 0xaa, 0x6f, 0x5f, 0x8c, 0xb3,
	0x0a, 0xb4, 0xf6, 0x9a, 0x8e, 0xd1, 0xbb, 0xa5, 0x17, 0x7a, 0x33, 0xf9, 0x32, 0x3e, 0xb0, 0xfd,
	0x75, 0x7e, 0x6c, 0xd3, 0x48, 0x58, 0xce, 0xdf, 0xaf, 0x9c, 0xa3, 0x1f, 0xc2, 0x6e, 0x9a, 0x45,
	0x4b, 0x3f, 0xbb, 0xf6, 0xa2, 0x25, 0x1e, 0x15, 0x9c, 0x51, 0xd9, 0xd0, 0x91, 0x66, 0x4c, 0x90,
	0x8e, 0x63, 0xe6, 0x3d, 0xd8, 0x41, 0x55, 0x68, 0xa7, 0x17, 0xcc, 0xa3, 0x58, 0xe6, 0xc6, 0xde,
	0xed, 0xfc, 0x79, 0x81, 0xc6, 0x9e, 0x32, 0xb5, 0xba, 0x8d, 0xd5, 0x4c, 0xee, 0x5b, 0x97, 0xac,
	0x86, 0xeb, 0x4e, 0xbb, 0xe9, 0xba, 0x73, 0x1b, 0xb6, 0xa2, 0xdc, 0x9b, 0x45, 0x53, 0x45, 0xcd,
	0xa3, 0xed, 0x76, 0xa2, 0xfc, 0x8b, 0x68, 0xaa, 0xea, 0x0e, 0xa6, 0xe4, 0xe0, 0xd6, 0x8a, 0x83,
	0x29, 0x3a, 0x88, 0x46, 0xa7, 0x2b, 0x46, 0x77, 0xb5, 0xd1, 0x69, 0xcd, 0x68, 0x2c, 0x46, 0xfe,
	0x42, 0x7a, 0x45, 0x1c, 0x29, 0xdd, 0x10, 0xba, 0x48, 0x78, 0x16, 0x47, 0x8a, 0xc2, 0xe4, 0x2b,
	0x39, 0x4b, 0x70, 0xa3, 0x42, 0x5d, 0xf9, 0xc1, 0x90, 0x26, 0x21, 0x85, 0xc9, 0x00, 0x28, 0x92,
	0x5c, 0xe6, 0x07, 0x86, 0x48, 0xb1, 0x7c, 0x13, 0x7a, 0xe5, 0x6d, 0x96, 0x26, 0x96, 0x96, 0xdb,
	0x35, 0xf7, 0xd8, 0xb5, 0x2b, 0xec, 0x70, 0xfd, 0x0a, 0xfb, 0x10, 0x20, 0x57, 0x49, 0xa6, 0x0b,
	0xda, 0x76, 0xfd, 0x2a, 0x71, 0x8e, 0x1c, 0xbe, 0x7c, 0xe5, 0xe6, 0x27, 0xde, 0xee, 0xf0, 0x12,
	0xc8, 0x02, 0x23, 0xca, 0xff, 0xf2, 0x76, 0x87, 0x97, 0x40, 0xbe, 0xdd, 0x05, 0xfa, 0x97, 0xf8,
	0x25, 0x66, 0xed, 0x72, 0x19, 0xe5, 0x79, 0x39, 0x97, 0xee, 0x90, 0xd0, 0x61, 0x75, 0x68, 0x0c,
	0x9b, 0x44, 0xb7, 0x83, 0xda, 0x37, 0x36, 0x30, 0x35, 0x8f, 0xb2, 0x10, 0x7b, 0xf8, 0x32, 0xa9,
	0xa6, 0xdb, 0xdd, 0x7a, 0x03, 0xbb, 0x40, 0xcc, 0x99, 0x81, 0x70, 0xfd, 0x55, 0x6b, 0x34, 0xe7,
	0x2f, 0xdc, 0xc0, 0xd6, 0xd1, 0x34, 0x94, 0xe7, 0x5e, 0xe0, 0x2f, 0x82, 0x62, 0xe1, 0xe3, 0x2d,
	0x89, 0xcb, 0xc5, 0x20, 0xca, 0x4f, 0x4b, 0x1a, 0xf6, 0xac, 0x38, 0xb1, 0x40, 0x5e, 0x26, 0xfd,
	0x3c, 0x89, 0x4d, 0xcf, 0x8a, 0x93, 0x0a, 0xeb, 0x12, 0x07, 0x27, 0x6d, 0xcb, 0xff, 0x0c, 0xdb,
	0x35, 0xd7, 0x2e, 0xcb, 0x4f, 0x17, 0x1b, 0xf5, 0x7b, 0x60, 0x51, 0xbc, 0xa9, 0x94, 0x3a, 0xc7,
	0x87, 0x15, 0xf5, 0x73, 0x29, 0x71, 0x62, 0xf5, 0x03, 0x55, 0x50, 0xc5, 0x28, 0xd1, 0xb5, 0x7c,
	0x3f, 0x64, 0x7e, 0xb5, 0xad, 0x3a, 0xf1, 0xdf, 0x84, 0x5e, 0x94, 0x7b, 0xb9, 0x54, 0x78, 0xe3,
	0xe0, 0xd4, 0xef, 0x46, 0xf9, 0x39, 0x7d, 0x63, 0x36, 0x32, 0x87, 0x3b, 0x27, 0x27, 0x3e, 0x30,
	0x89, 0x9e, 0x2b, 0x03, 0xd8, 0x5d, 0x8b, 0xd5, 0x8a, 0x73, 0x56, 0x91, 0xb5, 0x5c, 0xa1, 0x42,
	0xbb, 0xee, 0xdc, 0x66, 0x83, 0x73, 0xce, 0x8f, 0x1b, 0xd0, 0xb7, 0xd2, 0x08, 0xb3, 0x93, 0x72,
	0xed, 0xfb, 0x2b, 0x3f, 0x4d, 0x49, 0xb5, 0x95, 0x9d, 0x88, 0xfa, 0x0e, 0x19, 0x2e, 0x25, 0x24,
	0xfd, 0xa4, 0xa7, 0x01, 0x94, 0x08, 0x8a, 0x5c, 0x25, 0xfc, 0x86, 0x69, 0x3f, 0x0d, 0xcc, 0xfd,
	0xf8, 0x94, 0x38, 0x2e, 0x29, 0xe6, 0xdf, 0xe2, 0x03, 0xd8, 0x25, 0xa1, 0x4c, 0x4e, 0x71, 0x14,
	0x4d, 0x53, 0x3c, 0x90, 0xba, 0x0e, 0x21, 0xc3, 0x45, 0xfa, 0x49, 0x9a, 0x4e, 0x42, 0xac, 0x1e,
	0x9c, 0xfd, 0xa1, 0x9e, 0x2e, 0x3a, 0x94, 0xe9, 0xa1, 0xb8, 0x03, 0x3d, 0x75, 0xe5, 0x05, 0x69,
	0x6e, 0xbd, 0x48, 0xa9, 0xab, 0xd3, 0x34, 0x9f, 0x84, 0xce, 0xef, 0x5a, 0xdc, 0x99, 0xaa, 0x15,
	0xff, 0x0f, 0xb6, 0x2d, 0x33, 0xab, 0x5a, 0x3a, 0xa8, 0xac, 0x9a, 0x84, 0xe2, 0x3e, 0xec, 0xd5,
	0x51, 0x5e, 0x28, 0xf3, 0xc0, 0xb4, 0x66, 0x1b, 0xfa, 0x44, 0xe6, 0x41, 0xe9, 0x86, 0x86, 0x07,
	0xbe, 0xf2, 0x1e, 0xd9, 0x6e, 0x30, 0xf8, 0xd4, 0x57, 0x8f, 0xc4, 0x31, 0x1c, 0xae, 0x41, 0x59,
	0x39, 0x7b, 0x25, 0xea, 0xf8, 0x9b, 0xd4, 0x1f, 0x6b, 0x4f, 0xeb, 0xea, 0x8f, 0x9b, 0xd4, 0x1f,
	0xb3, 0xfa, 0x4e, 0x83, 0xfa, 0xe3, 0x9b, 0xd4, 0x1f, 0xe9, 0x3c, 0xac, 0xab, 0x3f, 0x6a, 0x52,
	0x7f, 0xc4, 0xea, 0xbb, 0x0d, 0xea, 0x8f, 0x50, 0xbd, 0x73, 0x9f, 0xda, 0x59, 0x99, 0x33, 0xe2,
	0x2d, 0x9d, 0x5a, 0x79, 0x20, 0xe3, 0xf2, 0xc1, 0x18, 0x29, 0xe7, 0x48, 0x70, 0xae, 0x08, 0x5e,
	0x16, 0xc0, 0xda, 0xf3, 0xa7, 0x2e, 0x98, 0x6b, 0xcf, 0x9f, 0x8c, 0xa6, 0x07, 0x48, 0x86, 0x58,
	0xe3, 0x2c, 0x17, 0x50, 0x2a, 0xd9, 0x25, 0x1b, 0xaf, 0x17, 0x3a, 0x3e, 0xcc, 0xc6, 0x6b, 0x85,
	0xf3, 0x0f, 0x3e, 0x03, 0xe6, 0xa1, 0x0c, 0x33, 0x2e, 0x49, 0xa5, 0x35, 0x19, 0x74, 0xf0, 0x73,
	0x12, 0x8a, 0x03, 0xe8, 0xe8, 0x54, 0xe5, 0x25, 0xda, 0x3e, 0x65, 0xe8, 0x1b, 0xd0, 0x2d, 0xe2,
	0x88, 0x47, 0x09, 0x56, 0xbe, 0x45, 0xdf, 0x6c, 0x18, 0x3d, 0xcc, 0xd9, 0x77, 0x32, 0x7a, 0xaa,
	0xe3, 0xfb, 0xd8, 0x6d, 0xd8, 0xe2, 0x77, 0x3b, 0x93, 0xc0, 0x1d, 0x7a, 0xa3, 0x0b, 0xb1, 0x72,
	0x2c, 0x25, 0x0e, 0x9a, 0xc8, 0xe2, 0x08, 0x76, 0x99, 0x30, 0x09, 0xc5, 0x11, 0x1c, 0x90, 0xd4,
	0x34, 0xca, 0x72, 0xa5, 0x9f, 0x86, 0xad, 0x1a, 0x22, 0x90, 0xf9, 0x39, 0xf2, 0xe8, 0x75, 0x98,
	0x6a, 0x49, 0x06, 0x77, 0x4e, 0xc2, 0xd0, 0x95, 0xaa, 0xc8, 0x62, 0xa6, 0xbe, 0xee, 0xfb, 0xf8,
	0xc3, 0x95, 0xf7, 0xf1, 0xb1, 0xfd, 0x3e, 0x6e, 0x29, 0xae, 0x5e, 0xc9, 0xff, 0xdd, 0x82, 0xbd,
	0x06, 0xbe, 0xf8, 0x14, 0xc6, 0x65, 0x60, 0x33, 0x62, 0xac, 0x3e, 0x6f, 0x1f, 0x18, 0xbe, 0x25,
	0xc7, 0x73, 0xce, 0xfa, 0x83, 0xf8, 0x66, 0xf3, 0x83, 0x38, 0x5d, 0x2a, 0x49, 0xb7, 0xfd, 0x2e,
	0xde, 0x32, 0x97, 0x4a, 0xe4, 0x9c, 0x56, 0xaf, 0xe3, 0x6f, 0x01, 0x68, 0x74, 0xac, 0xff, 0x29,
	0x69, 0xb9, 0x3d, 0xa6, 0x60, 0x57, 0xa7, 0x2b, 0x38, 0xb1, 0x6b, 0x65, 0x7f, 0xc0, 0x44, 0x5d,
	0xec, 0xe9, 0xc2, 0x4c, 0xa0, 0x95, 0x37, 0xe7, 0x0e, 0x81, 0xf7, 0x98, 0xf9, 0x79, 0xed, 0xe5,
	0xf9, 0x01, 0xec, 0xd5, 0x76, 0x40, 0x4b, 0x6c, 0xf1, 0x34, 0x9c, 0x55, 0xde, 0xdf, 0x80, 0xd7,
	0xef, 0x0c, 0x7c, 0x06, 0x6d, 0xfc, 0x7f, 0x7d, 0x45, 0xee, 0xdd, 0xf0, 0x8a, 0xfc, 0x2b, 0xd0,
	0x2a, 0x3c, 0xeb, 0x75, 0x18, 0xac, 0x79, 0x9b, 0xc3, 0x51, 0x3d, 0x10, 0x8f, 0xb2, 0x3a, 0xc1,
	0xf9, 0xe3, 0x26, 0x8c, 0x56, 0x40, 0xaf, 0x3c, 0xc5, 0x36, 0x4d, 0xa6, 0x9b, 0x8d, 0x93, 0xa9,
	0x35, 0x4f, 0xb6, 0xf8, 0x52, 0x76, 0xf3, 0x3c, 0x79, 0xeb, 0x55, 0xe7, 0xc9, 0x76, 0xe3, 0x3c,
	0x79, 0x0f, 0x76, 0x6a, 0x5b, 0x82, 0xe9, 0xd1, 0xa1, 0x35, 0xb7, 0x2d, 0xdf, 0x31, 0x47, 0xaa,
	0xd0, 0xd4, 0xa6, 0xe6, 0x5a, 0x28, 0xbf, 0xa8, 0x66, 0xe7, 0xe3, 0xbf, 0x6f, 0xd2, 0x7d, 0x0d,
	0x4f, 0xc7, 0xb9, 0xcc, 0x5e, 0x46, 0x81, 0x14, 0xa7, 0x20, 0xd6, 0xff, 0xbf, 0x12, 0x3c, 0x6f,
	0x35, 0xfe, 0xb1, 0x75, 0x87, 0x3b, 0x73, 0xed, 0xbf, 0xa8, 0x5f, 0xc0, 0xb0, 0xf6, 0xe7, 0x9a,
	0x38, 0x20, 0xcc, 0xea, 0xdf, 0x77, 0x77, 0x1a, 0xc9, 0xb9, 0xf8, 0x1a, 0x6e, 0xdf, 0x50, 0x28,
	0xc4, 0xdb, 0xc6, 0x90, 0x1b, 0xca, 0x48, 0x93, 0x35, 0x9f, 0xc2, 0xc0, 0xfe, 0xcb, 0x55, 0xec,
	0x97, 0xab, 0x5a, 0xff, 0xc2, 0x36, 0xbb, 0x31, 0x5a, 0xf9, 0x93, 0x5a, 0xdc, 0x5e, 0x91, 0x35,
	0x7f, 0x5d, 0x37, 0x88, 0x5f, 0x76, 0xe8, 0xff, 0xee, 0x47, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff,
	0xc8, 0xfa, 0xea, 0x25, 0x0d, 0x1f, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TencentServiceClient is the client API for TencentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TencentServiceClient interface {
	//推送订单到腾讯有数[开发之前请添加数据仓库，dataSourceType = 0]
	AddOrdersToTencent(ctx context.Context, in *AddOrdersToTencentReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//上报微信小程序页面访问数据
	PushVisitPage(ctx context.Context, in *PushVisitPageReq, opts ...grpc.CallOption) (*PushVisitPageRes, error)
	//推送退款订单到腾讯有数
	AddReturnOrderToTencent(ctx context.Context, in *AddReturnOrderToTencentReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//报时效要求：
	//每天 6:00 前完成前一天的数据上报。
	//推送#汇总订单接口
	PushOrderSum(ctx context.Context, in *PushOrderSumReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//推送订单状态变更
	//订单同步的时效性要求是5分钟
	PushOrderStatus(ctx context.Context, in *PushOrderStatusReq, opts ...grpc.CallOption) (*BaseResponse, error)
}

type tencentServiceClient struct {
	cc *grpc.ClientConn
}

func NewTencentServiceClient(cc *grpc.ClientConn) TencentServiceClient {
	return &tencentServiceClient{cc}
}

func (c *tencentServiceClient) AddOrdersToTencent(ctx context.Context, in *AddOrdersToTencentReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ext.TencentService/AddOrdersToTencent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tencentServiceClient) PushVisitPage(ctx context.Context, in *PushVisitPageReq, opts ...grpc.CallOption) (*PushVisitPageRes, error) {
	out := new(PushVisitPageRes)
	err := c.cc.Invoke(ctx, "/ext.TencentService/PushVisitPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tencentServiceClient) AddReturnOrderToTencent(ctx context.Context, in *AddReturnOrderToTencentReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ext.TencentService/AddReturnOrderToTencent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tencentServiceClient) PushOrderSum(ctx context.Context, in *PushOrderSumReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ext.TencentService/PushOrderSum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tencentServiceClient) PushOrderStatus(ctx context.Context, in *PushOrderStatusReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ext.TencentService/PushOrderStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TencentServiceServer is the server API for TencentService service.
type TencentServiceServer interface {
	//推送订单到腾讯有数[开发之前请添加数据仓库，dataSourceType = 0]
	AddOrdersToTencent(context.Context, *AddOrdersToTencentReq) (*BaseResponse, error)
	//上报微信小程序页面访问数据
	PushVisitPage(context.Context, *PushVisitPageReq) (*PushVisitPageRes, error)
	//推送退款订单到腾讯有数
	AddReturnOrderToTencent(context.Context, *AddReturnOrderToTencentReq) (*BaseResponse, error)
	//报时效要求：
	//每天 6:00 前完成前一天的数据上报。
	//推送#汇总订单接口
	PushOrderSum(context.Context, *PushOrderSumReq) (*BaseResponse, error)
	//推送订单状态变更
	//订单同步的时效性要求是5分钟
	PushOrderStatus(context.Context, *PushOrderStatusReq) (*BaseResponse, error)
}

// UnimplementedTencentServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTencentServiceServer struct {
}

func (*UnimplementedTencentServiceServer) AddOrdersToTencent(ctx context.Context, req *AddOrdersToTencentReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOrdersToTencent not implemented")
}
func (*UnimplementedTencentServiceServer) PushVisitPage(ctx context.Context, req *PushVisitPageReq) (*PushVisitPageRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushVisitPage not implemented")
}
func (*UnimplementedTencentServiceServer) AddReturnOrderToTencent(ctx context.Context, req *AddReturnOrderToTencentReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddReturnOrderToTencent not implemented")
}
func (*UnimplementedTencentServiceServer) PushOrderSum(ctx context.Context, req *PushOrderSumReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushOrderSum not implemented")
}
func (*UnimplementedTencentServiceServer) PushOrderStatus(ctx context.Context, req *PushOrderStatusReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushOrderStatus not implemented")
}

func RegisterTencentServiceServer(s *grpc.Server, srv TencentServiceServer) {
	s.RegisterService(&_TencentService_serviceDesc, srv)
}

func _TencentService_AddOrdersToTencent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrdersToTencentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TencentServiceServer).AddOrdersToTencent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.TencentService/AddOrdersToTencent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TencentServiceServer).AddOrdersToTencent(ctx, req.(*AddOrdersToTencentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TencentService_PushVisitPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushVisitPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TencentServiceServer).PushVisitPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.TencentService/PushVisitPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TencentServiceServer).PushVisitPage(ctx, req.(*PushVisitPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TencentService_AddReturnOrderToTencent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddReturnOrderToTencentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TencentServiceServer).AddReturnOrderToTencent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.TencentService/AddReturnOrderToTencent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TencentServiceServer).AddReturnOrderToTencent(ctx, req.(*AddReturnOrderToTencentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TencentService_PushOrderSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushOrderSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TencentServiceServer).PushOrderSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.TencentService/PushOrderSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TencentServiceServer).PushOrderSum(ctx, req.(*PushOrderSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TencentService_PushOrderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushOrderStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TencentServiceServer).PushOrderStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.TencentService/PushOrderStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TencentServiceServer).PushOrderStatus(ctx, req.(*PushOrderStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TencentService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ext.TencentService",
	HandlerType: (*TencentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddOrdersToTencent",
			Handler:    _TencentService_AddOrdersToTencent_Handler,
		},
		{
			MethodName: "PushVisitPage",
			Handler:    _TencentService_PushVisitPage_Handler,
		},
		{
			MethodName: "AddReturnOrderToTencent",
			Handler:    _TencentService_AddReturnOrderToTencent_Handler,
		},
		{
			MethodName: "PushOrderSum",
			Handler:    _TencentService_PushOrderSum_Handler,
		},
		{
			MethodName: "PushOrderStatus",
			Handler:    _TencentService_PushOrderStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ext/external-tencent.proto",
}

package utils

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"io/ioutil"
	"net/http"
	"order-center/proto/common"
	"strings"
	"time"
)

const (
	tokenKey    = "offline:token"
	GetTokenUrl = "/api/auth/anyone/getNewToken?userId=10001"
)

// 对接肖宠的
func XiaoCHttpPost(url string, dataJson []byte, Headers string) ([]byte, error) {
	urlPat, _ := config.Get("offline_url")
	//if urlPat == "" {
	//	urlPat = "https://pets-saas-pre.rvet.cn"
	//}
	//urlPat := "http://127.0.0.1:8154"
	req, err := http.NewRequest("POST", urlPat+url, bytes.NewBuffer(dataJson))
	client := http.Client{
		Timeout: time.Second * 60,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}}
	req.Header.Set("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		glog.Error(err)
		return []byte(""), err
	}

	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	glog.Info("请求逍宠库存 请求头：", req.Header, "url:", url, "请求参数：", string(dataJson), "返回结果:", string(body))
	return body, err
}

func GetToken() (string, error) {
	redis := common.GetRedisConn()
	token := redis.Get(tokenKey).Val()
	if token != "" {
		return token, nil
	}
	//增加请求次数，超过三次就不请求了
	count := redis.Incr("offline_token_request_count").Val()
	if count > 3 {
		return "", errors.New("请求次数过多，请稍后再试")
	}
	// Redis 中不存在 token，或者获取失败，则重新获取
	token, err := GetOfflineToken()
	if err != nil {
		return "", err
	}
	// 将新的 token 存储到 Redis 中，设置过期时间为 29 天
	redis.Set(tokenKey, token, 29*24*time.Hour)
	return token, nil
}

func GetOfflineToken() (string, error) {
	respBody, err := HttpApi("GET", GetTokenUrl, "", nil)
	glog.Infof(fmt.Sprintf("请求新线下门店的获取token接口， 数据返回：%s", string(respBody)))
	if err != nil {
		glog.Error("请求新线下门店的获取token接口，get请求失败，地址："+GetTokenUrl+",err=", err.Error())
		return "", err
	} else {
		var result map[string]interface{}
		err = json.Unmarshal(respBody, &result)
		if err != nil {
			glog.Error("请求新线下门店的获取token接口,解析token失败，err=", err.Error())
			return "", err
		}
		if result["code"].(float64) == 0 {
			token := result["data"].(map[string]interface{})["token"].(string)
			return token, nil
		} else {
			glog.Error("请求新线下门店的获取token接口，返回错误，地址："+GetTokenUrl+",err=", result["errorMsg"].(string))
			return "", errors.New(result["errorMsg"].(string))
		}
	}
}

func HttpApi(method, url, Headers string, body interface{}) ([]byte, error) {
	glog.Infof("HttpApi: method=%s, url=%s, Headers=%s, body=%v", method, url, Headers, body)
	var reqBody []byte
	var err error

	if body != nil {
		switch t := body.(type) {
		case string:
			reqBody = []byte(t)
		case []byte:
			reqBody = t
		case map[string]interface{}:
			reqBody, err = json.Marshal(body)
			if err != nil {
				return nil, err
			}
		default:
			return nil, errors.New("unsupported body type")
		}
	}
	apiUrl := config.GetString("offline_url")
	if apiUrl == "" {
		apiUrl = "https://pets-saas-pre.rvet.cn"
	}
	newUrl := fmt.Sprintf("%s%s", apiUrl, url)
	req, err := http.NewRequest(method, newUrl, bytes.NewReader(reqBody))
	if err != nil {
		glog.Error(err)
		return nil, err
	}

	// Set headers
	req.Header.Set("ApplicationId", "1")
	req.Header.Set("Authorization", "bGFtcF93ZWI6bGFtcF93ZWJfc2VjcmV0")
	if len(Headers) > 0 {
		strList := strings.Split(Headers, "&")
		for i := 0; i < len(strList); i++ {
			v := strList[i]
			valueList := strings.Split(v, "|")
			req.Header.Set(valueList[0], valueList[1])
		}
	}
	if url != GetTokenUrl {
		token, err := GetToken()
		if err != nil {
			return nil, err
		}
		req.Header.Set("token", token)
	}

	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{Timeout: time.Second * 15, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	res, err := client.Do(req)
	if err != nil {
		glog.Info(err)
		return nil, err
	}
	defer res.Body.Close()

	return ioutil.ReadAll(res.Body)
}

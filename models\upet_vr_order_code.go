package models

type UpetVrOrderCode struct {
	RecId                  int64  `xorm:"not null pk autoincr comment('兑换码表索引id') INT(11)"`
	OrderId                int64  `xorm:"not null comment('虚拟订单id') index INT(11)"`
	ErpOrderId             string `xorm:"default '0' comment('ERP订单号') index VARCHAR(100)"`
	ErpRefundId            string `xorm:"default '0' comment('ERP退款流水') VARCHAR(50)"`
	StoreId                int    `xorm:"not null default 0 comment('店铺ID') INT(10)"`
	BuyerId                int    `xorm:"not null default 0 comment('买家ID') INT(10)"`
	VrCode                 string `xorm:"not null comment('兑换码') index VARCHAR(18)"`
	VrState                int    `xorm:"not null default 0 comment('使用状态 0:(默认)未使用1:已使用2:已过期') TINYINT(4)"`
	VrUsetime              int    `xorm:"comment('使用时间') index(idx_vr_usetime_chain_id) INT(11)"`
	PayPrice               string `xorm:"not null default 0.00 comment('实际支付金额(结算)') DECIMAL(10,2)"`
	VrIndate               int64  `xorm:"comment('过期时间') INT(11)"`
	CommisRate             int    `xorm:"not null default 0 comment('佣金比例') SMALLINT(6)"`
	RefundLock             int    `xorm:"default 0 comment('退款锁定状态:0为正常,1为锁定,2为同意,默认为0') TINYINT(3)"`
	VrInvalidRefund        int    `xorm:"not null default 1 comment('允许过期退款1是0否') TINYINT(4)"`
	QrPic                  string `xorm:"default '' comment('二维码') VARCHAR(80)"`
	VrCodeRemark           string `xorm:"comment('医院备注') VARCHAR(200)"`
	ErpTime                int    `xorm:"comment('ERP同步时间') INT(11)"`
	ErpStatus              int    `xorm:"comment('是否同步') TINYINT(4)"`
	ErpOrderStatus         int    `xorm:"comment('ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)') TINYINT(4)"`
	ErpMobile              string `xorm:"comment('ERP手机号码') VARCHAR(11)"`
	ErpChargeoff           int    `xorm:"default 0 comment('核销状态：0为默认，1为ERP核销') TINYINT(4)"`
	ChainId                int    `xorm:"comment('核销门店ID') index index(idx_vr_usetime_chain_id) INT(11)"`
	ChainName              string `xorm:"comment('核销门店名称') VARCHAR(50)"`
	ErpChargeoffhospitalid string `xorm:"default '0' comment('ERP核销门店ID') VARCHAR(100)"`
	IsDis                  int    `xorm:"not null default 0 comment('是否分销订单') TINYINT(3)"`
	DisCommisRate          string `xorm:"not null default 0.00 comment('分销佣金比例') DECIMAL(4,2)"`
	DisMemberId            int    `xorm:"default 0 comment('分销会员ID') INT(10)"`
	OutMemberId            int    `xorm:"default 0 comment('推荐人ID') INT(11)"`
	IsSettlement           int    `xorm:"default 0 comment('是否进入结算0否，1是') TINYINT(1)"`
	DisType                int    `xorm:"default 0 comment('0自主购买，1分享连接，2扫码,3自己扫自己') TINYINT(4)"`
	GiftPrice              string `xorm:"default 0.00 comment('赠品金额') DECIMAL(10,2)"`
}

func (m *UpetVrOrderCode) TableName() string {
	return "upet_vr_order_code"
}

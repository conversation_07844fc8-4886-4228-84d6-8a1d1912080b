package models

import "time"

type TTenantPercentage struct {
	Id          int64     `json:"id" xorm:"pk autoincr not null comment('提成id') BIGINT 'id'"`
	TenantId    int64     `json:"tenant_id" xorm:"not null comment('店铺id') BIGINT 'tenant_id'"`
	Title       string    `json:"title" xorm:"not null comment('提成标题') VARCHAR(50) 'title'"`
	Status      string    `json:"status" xorm:"not null default 'ENABLED' comment('状态：ENABLED启用，DISABLED停用，DELETED删除') VARCHAR(255) 'status'"`
	EmployeeCnt int       `json:"employee_cnt" xorm:"default 0 comment('员工数量') INT 'employee_cnt'"`
	CreatedBy   int64     `json:"created_by" xorm:"not null comment('创建人') BIGINT 'created_by'"`
	CreatedTime time.Time `json:"created_time" xorm:"not null comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy   int64     `json:"updated_by" xorm:"default 'null' comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime time.Time `json:"updated_time" xorm:"default 'null' comment('更新时间') DATETIME 'updated_time'"`
}

package tasks

import (
	"context"
	"time"

	"order-center/models"
	os "order-center/proto/oc"
	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

//阿闻小程序订单5分钟未接单自动取消
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New()
	if _, err := task.AddFunc("@every 10s", cancelNoAcceptOrder); err != nil {
		//time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func cancelNoAcceptOrder() {
	//连接池勿关闭
	redis := services.GetRedisConn()

	lockCard := "task:lock:cancel_no_pay_order_5Min"
	lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redis.Del(lockCard)

	//连接池勿关闭
	db := services.GetDBConn()

	var orderList []models.Order
	err := db.Table("order_main").Join("inner", "order_detail", "order_main.order_sn = order_detail.order_sn").
		Join("left", "order_main c", "order_main.parent_order_sn = c.order_sn").
		Where("( (order_main.order_status_child = 20101 AND order_main.order_status = 20) or (order_main.is_virtual = 1 and order_main.order_status_child = 30101 AND c.order_status_child = 20101 ) ) "+
			"AND order_main.channel_id in (1,9) "+
			"AND (order_detail.split_order_result =2 or (ISNULL(`order_main`.parent_order_sn)=0 and LENGTH(trim(`order_main`.parent_order_sn))>0)) "+
			"AND order_detail.pickup_station_id=0 "+ //排除社区团购自提的订单
			"AND order_main.order_type <> 15 "+ //排除团长制拼团的订单
			"and DATE_ADD(order_main.pay_time, INTERVAL 5 MINUTE) < now()").
		Limit(30, 0).Find(&orderList)
	if err != nil {
		return
	}

	for _, v := range orderList {
		//超过300秒，5分钟，商家未接单取消订单
		//调用取消订单服务

		var orderStatus int32
		var orderStatusChild int32
		if v.IsVirtual == 1 {
			orderStatus = 30
			orderStatusChild = 30101 // 待核销
		} else {
			orderStatus = 20         // 已付款
			orderStatusChild = 20101 // 未接单
		}

		o := services.OrderService{}
		if out, _ := o.CancelOrder(context.Background(), &os.CancelOrderRequest{
			OrderSn:          v.OrderSn,
			CancelReason:     "付款5分钟后商家未接单，系统自动取消订单。",
			IsRefund:         0,
			OrderStatus:      orderStatus,
			OrderStatusChild: orderStatusChild,
		}); out.Code == 200 {
			glog.Info("cancelNoAcceptOrder.success,", kit.JsonEncode(v), ",", kit.JsonEncode(out))
			//查退款单号，调用退款支付服务，给用户付款
			var refundOrderNo []string
			if err := db.Table("refund_order").Select("refund_sn").Where("order_sn=?", v.OrderSn).Find(&refundOrderNo); err != nil {
				return
			} else {
				//订单取消成功后，调用退款服务
				ro := services.RefundOrderService{}
				if roOut, _ := ro.RefundOrderPay(context.Background(), &os.RefundOrderPayRequest{
					RefundOrderSn: refundOrderNo[0],
					ResType:       "商家发起退款",
					OperationType: "商家发起退款",
				}); roOut.Code == 200 {
				} else {
					return
				}
			}
		}
	}
}

package tasks

import (
	"fmt"
	"order-center/services"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

// 会员卡过期
func init() {
	if !kit.EnvCanCron() {
		return
	}
	c := cron.New()
	if _, err := c.AddFunc("0 0 * * ?", func() {
		vipExpire()
		vipOrderComplete()
	}); err != nil {
		glog.Info("vipExpire、vipOrderComplete 创建任务出错：", err.Error())
	}
	c.Start()
}

// 付费会员过期处理
func vipExpire() {
	r := services.GetRedisConn()
	key := "order-center:task:vipExpire"
	if !r.SetNX(key, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer r.Del(key)

	db := services.GetUPetDBConn()

	now := time.Now()
	start := now.AddDate(0, 0, -4).Format(kit.DATE_LAYOUT)
	end := fmt.Sprintf("%s 23:59:59", now.AddDate(0, 0, -1).Format(kit.DATE_LAYOUT))

	// 近期3天过期的都会执行一次
	// 所以要考虑之前有过期订单后又开卡的场景
	if rs, err := db.Exec(`update datacenter.vip_card_order o
    inner join upet_member m on m.scrm_user_id = o.user_id
         set m.vip_card_state = 0
where m.vip_card_state = 1 and o.expiry_date between ? and ? and o.state = 10
and not exists(select 1 from datacenter.vip_card_order e where e.user_id = o.user_id and e.state = 10 and e.expiry_date >= ?);`, start, end, now.Format(kit.DATE_LAYOUT)); err != nil {
		glog.Error("vipExpire " + err.Error())
	} else {
		rows, _ := rs.RowsAffected()
		glog.Info("vipExpire 处理成功，影响", rows)
	}
}

// 会员卡7天后订单自动完成
func vipOrderComplete() {
	r := services.GetRedisConn()
	key := "order-center:task:vipOrderComplete"
	if !r.SetNX(key, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer r.Del(key)

	db := services.GetUPetDBConn()
	now := time.Now()

	// 集团卡，有分销得 ，核销挂分销门店；无分销得，核销不挂门店
	// 区域卡，不管是否分销，核销都不挂门店
	if rs, err := db.Exec(`update upet_vr_order o
left join upet_vr_order_code oc on oc.order_id = o.order_id
left join dc_order.order_main om on om.parent_order_sn = concat(o.order_sn,'')
left join datacenter.vip_card_template t on t.id = o.goods_id
left join upet_chain c on o.chain_id = c.chain_id and t.or_id = -1
set oc.vr_state = 1, oc.erp_chargeoff = 1, oc.is_settlement = 1,oc.vr_usetime = unix_timestamp(),o.use_state = 1, o.order_state = 40, o.finnshed_time = unix_timestamp(),
oc.chain_id = ifnull(c.chain_id,0),oc.chain_name = c.chain_name,oc.erp_chargeoffhospitalid = ifnull(c.chain_erp_id,0),
om.order_status = 30,om.order_status_child = 30103
where o.order_type = 17 and o.order_state = 20 and oc.vr_state=0 and o.add_time between ? and ?;`, now.AddDate(0, 0, -10).Unix(), now.AddDate(0, 0, -7).Unix()); err != nil {
		glog.Error("vipOrderComplete " + err.Error())
	} else {
		rows, _ := rs.RowsAffected()
		glog.Info("vipOrderComplete 处理成功，影响", rows)
	}
}

package models

type ExportTask struct {
	Id               int32  `xorm:"not null pk autoincr comment('任务id') INT(11)"`
	TaskContent      int32  `xorm:"not null default 0 comment('任务内容:1.导出订单数据;2.导出(含商品明细)数据') INT(1)"`
	TaskStatus       int32  `xorm:"not null default 1 comment('任务状态:1:生成中;2:已完成;3.失败') INT(1)"`
	ResulteFileUrl   string `xorm:"not null default '''' comment('结果文件路径') VARCHAR(255)"`
	OperationFileUrl string `xorm:"not null default '''' comment('操作文件路径') VARCHAR(255)"`
	ModifyTime       string `xorm:"default 'NULL' comment('修改时间') DATETIME"`
	CreateId         string `xorm:"not null default ''0'' comment('用户编码') VARCHAR(100)"`
	CreateTime       string `xorm:"default 'NULL' comment('创建时间') DATETIME"`
	TaskName         string `xorm:"not null default '''' comment('任务名称') VARCHAR(50)"`
	CreateName       string `xorm:"not null default '''' comment('用户名称') VARCHAR(50)"`
	Ip               string `xorm:"not null default '''' comment('IP') VARCHAR(50)"`
	IpLocation       string `xorm:"not null default '''' comment('IP归属地') VARCHAR(50)"`
	TaskNum          int32  `xorm:"not null default 1 comment('导出数量) INT(11)"`
	FinanceCode      string `xorm:"not null default '' comment('财务编码') VARCHAR(50) 'finance_code'"`
	OrgId            int    `xorm:"not null default 1 comment('主体id') INT 'org_id'"`
}

package recommend

import (
	"_/proto/common"
	"_/proto/pc"
	"context"
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	Conn  *grpc.ClientConn
	Ctx   context.Context
	Cf    context.CancelFunc
	Tips  RecommendTipsClient
	Prods RecommendProdsClient
}

func GetRecommendClient() *Client {
	var client Client
	var err error
	url := config.GetString("recommend-grpc-url")

	// url = "************:50051" // 测试的推荐服务地址
	glog.Info(url)

	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure(), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(50000000))); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.Tips = NewRecommendTipsClient(client.Conn)
		client.Prods = NewRecommendProdsClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Minute*30)
		return &client
	}
}

//关闭链接
func (d *Client) Close() {
	d.Conn.Close()
	d.Cf()
}

type RecommendResult struct {
	UserId string
	Bbc    map[string][]int32
	Store  map[string][]int32
	Tips   map[string][]int32
}

//缓存推荐结果 dataType(bbc=电商,store=本地生活)
func GetCacheRecommendProductResult(dataType, petID, userID, financeCode string) []int32 {
	//北京的接口都 返回空数据数据了。直接注释
	// 北京推荐接口没有用户宠物的时候，居然返回的数据宠物id是0，宠物id是字符串类型，又不是int类型，太另类，做个特写
	if petID == "" {
		petID = "0"
	}

	var result *RecommendResult
	redis := common.GetRedisConn()
	defer redis.Close()
	key := "personalized-api:recommend-product:" + userID

	setCache := func(rr *RecommendResult) {
		b, _ := json.Marshal(rr)
		redis.Set(key, string(b), time.Minute*60)
	}

	cmd := redis.Get(key)

	if cmd.Val() != "" {
		json.Unmarshal([]byte(cmd.Val()), &result)
	} else {
		result = ParseRecommendProductResult(GetRecommendPorduct(userID, financeCode, 200))
	}

	if dataType == "store" && result != nil && result.Store != nil {
		if v, has := result.Store[petID]; has && len(v) > 0 {
			//判断是否有没有进行SKUID的有效过滤
			if v[len(v)-1] == 0 {
				result.Store[petID] = getHasStockSkuIDs(financeCode, v)
				glog.Info("userID：", userID, "petID：", petID, "推荐数据：", len(v), "有效数据：", len(result.Store[petID]))
				setCache(result)
			}
			return result.Store[petID]
		}
	} else if dataType == "bbc" && result != nil && result.Bbc != nil {
		if v, has := result.Bbc[petID]; has && len(v) > 0 {
			return v
		}
	}
	return []int32{}
}

//调用商品中心服务，过滤无效没库存的skuid
func getHasStockSkuIDs(financeCode string, skuIDs []int32) []int32 {
	client := pc.GetDcProductClient()
	defer client.Close()
	if out, err := client.RPC.GetHasStockSkuIDs(client.Ctx, &pc.HasStockSkuIDsRequest{
		FinanceCode: financeCode,
		SkuId:       skuIDs,
	}); err != nil {
		glog.Error(err)
	} else if out != nil && len(out.SkuId) > 0 {
		return out.SkuId
	}
	return []int32{}
}

//缓存贴士推荐结果
func GetCacheRecommendTipsResult(petID, userID string) []int32 {
	if petID == "" {
		petID = "0"
	}
	var result *RecommendResult
	redis := common.GetRedisConn()
	defer redis.Close()
	key := "personalized-api:recommend-tips:" + userID
	cmd := redis.Get(key)

	if cmd.Val() != "" {
		json.Unmarshal([]byte(cmd.Val()), &result)
	} else {
		if result = ParseRecommendTipsResult(GetRecommendTips(userID, 100)); result != nil {
			b, _ := json.Marshal(result)
			redis.Set(key, string(b), time.Minute*60)
		}
	}
	if result != nil && result.Tips != nil {
		if v, has := result.Tips[petID]; has {
			return v
		}
	}
	return []int32{}
}

//转换北京推荐部门@王丽写的奇葩grpc服务的结果
func ParseRecommendTipsResult(str string) *RecommendResult {
	if str == "" {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			glog.Error("ParseRecommendTipsResult", err, str)
		}
	}()

	var result RecommendResult
	result.Tips = make(map[string][]int32)
	strs := strings.Split(str, "=")
	for i, v := range strs {
		if i == len(strs)-1 {
			break
		}

		petid := "0"
		if i == 0 {
			if len(v) > 80 {
				result.UserId = v[7:39]
				petid = v[len(v)-32:]
			} else if len(v) > 40 {
				result.UserId = v[7:39]

			}
		} else {
			if !strings.HasSuffix(v, "]}") {
				petid = v[len(v)-32:]
			}
		}

		for _, v2 := range strings.Split(strings.Split(strs[i+1], "]")[0][1:], ",") {
			id, _ := strconv.Atoi(v2)
			result.Tips[petid] = append(result.Tips[petid], int32(id))
		}
	}
	return &result
}

//转换北京推荐部门@王丽写的奇葩grpc服务的结果
func ParseRecommendProductResult(str string) *RecommendResult {
	if str == "" {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			glog.Error("ParseRecommendProductResult", err, str)
		}
	}()

	strs := strings.Split(str, "=")
	var result RecommendResult
	result.Bbc = make(map[string][]int32)
	result.Store = make(map[string][]int32)

	regSKU, _ := regexp.Compile(`\d{5,}`)

	for i, v := range strs {
		if i == 0 && len(v) > 40 {
			result.UserId = v[7:39]
		}
		if v == "{bbc" {
			var petid string
			preStr := strs[i-1]
			if strings.HasSuffix(preStr, "petprods:{0") {
				petid = "0"
			} else {
				petid = preStr[len(preStr)-32:]
			}
			bbc := regSKU.FindAllString(strs[i+1], -1)
			for _, b := range bbc {
				sku, _ := strconv.Atoi(b)
				result.Bbc[petid] = append(result.Bbc[petid], int32(sku))
			}

			store := regSKU.FindAllString(strs[i+2], -1)
			for _, s := range store {
				sku, _ := strconv.Atoi(s)
				result.Store[petid] = append(result.Store[petid], int32(sku))
			}
			//末尾加个0，用于判断有没有进行sku有无库存的SkuIDs数据过滤
			if len(result.Store[petid]) > 0 {
				result.Store[petid] = append(result.Store[petid], 0)
			}
		}
	}
	// fmt.Println(result)
	return &result
}

//推荐商品（bbc,本地生活）
func GetRecommendPorduct(userID, financeCode string, size int32) string {
	client := GetRecommendClient()
	defer client.Close()

	out, err := client.Prods.GetRcmdProds(client.Ctx, &ProdRequest{
		Userid:  userID,
		Chainid: financeCode,
		Size:    size,
	})

	var b []byte
	var message string
	if out != nil {
		b, _ = json.Marshal(out)
		message = strings.ReplaceAll(out.Message, " ", "")
	}

	if err != nil {
		glog.Error(err, userID, financeCode, string(b))
	} else {
		glog.Info(userID, financeCode, string(b))
	}
	return message
}

//推荐的贴士
func GetRecommendTips(userID string, size int32) string {
	client := GetRecommendClient()
	defer client.Close()

	out, err := client.Tips.GetRmdtips(client.Ctx, &TipRequest{
		Userid: userID,
		Size:   size,
	})

	var b []byte
	var message string
	if out != nil {
		b, _ = json.Marshal(out)
		message = strings.ReplaceAll(out.Message, " ", "")
	}

	if err != nil {
		glog.Error(err, userID, string(b))
	} else {
		glog.Info(userID, string(b))
	}
	return message
}

//int32切片分页
func PageInt32Array(arr []int32, pageIndex, pageSize int) []int32 {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()
	l := len(arr)
	if l == 0 {
		return arr
	}

	s := pageIndex*pageSize - pageSize
	if s > l {
		return []int32{}
	}

	i := pageIndex * pageSize
	if i > l {
		i = l
	}

	return arr[s:i]
}

//int32切片分页 倒序取数据
func PageInt32ArrayReverse(arr []int32, pageIndex, pageSize int) []int32 {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()
	l := len(arr)

	if l == 0 {
		return arr
	}

	i := l - pageIndex*pageSize
	if i < 0 {
		i = 0
	}
	if l-(pageIndex*pageSize-pageSize) < 0 {
		return []int32{}
	}

	return arr[i : l-(pageIndex*pageSize-pageSize)]
}

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/proto/pc"
	"order-center/utils"

	"github.com/go-redis/redis"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type CartService struct {
	CommonService
}

func (s CartService) SubmitOrder(ctx context.Context, request *oc.NewAddOrderRequest) (*oc.NewAddOrderResponse, error) {
	panic("implement me")
}

// MtUpdateOrder
// 美团自配的订单，美团下单后可以修改订单信息，我们要跟着修改
func (s CartService) MtUpdateOrder(ctx context.Context, params *oc.MtAddOrderRequest) (*oc.MtAddOrderResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("收到修改订单（MtUpdateOrder）：", paramsJson)
	out := oc.MtAddOrderResponse{Code: 200, Message: "ok"}
	//先根据外部订单号查询出主订单和子订单，
	db := GetDBConn()
	var orderMain models.OrderMain
	isOk, err := db.Table("order_main").
		Where("`order_main`.old_order_sn = ? ", params.OrderSn).Get(&orderMain)
	if err != nil || !isOk {
		out.Code = 400
		out.Message = "根据订单号未查询到订单" + " " + params.OrderSn
		return &out, nil
	}
	//根据主单号查询子单
	var orderMainList []*models.OrderMain
	err = db.Table("order_main").Where("parent_order_sn=?", orderMain.OrderSn).Find(&orderMainList)
	if err != nil {
		out.Message = "查询第三方订单数据出错" + err.Error()
		glog.Error("查询第三方订单数据出错")
		return &out, nil
	}
	var OrderSns []string
	//先加入父单号
	OrderSns = append(OrderSns, orderMain.OrderSn)

	//再加入子单号，一起修改
	for _, k := range orderMainList {
		OrderSns = append(OrderSns, k.OrderSn)
	}

	orderMainUp := &models.OrderMain{
		OrderStatus:      params.OrderStatus,
		OrderStatusChild: params.OrderStatusChild,
		ShopId:           params.ShopId,
		ShopName:         params.ShopName,
		MemberId:         params.MemberId,
		MemberName:       params.MemberName,
		MemberTel:        params.MemberTel,
		ReceiverName:     params.ReceiverName,
		ReceiverState:    params.ReceiverState,
		ReceiverCity:     params.ReceiverCity,
		ReceiverDistrict: params.ReceiverDistrict,
		ReceiverAddress:  params.ReceiverAddress,
		ReceiverPhone:    params.ReceiverPhone,
		CombinePrivilege: params.CombinePrivilege,
		Privilege:        params.Privilege,
		ReceiverMobile:   params.ReceiverMobile,
		Total:            params.Total,
		PayTotal:         params.Total,
		GoodsTotal:       params.GoodsTotal,
		IsPay:            params.IsPay,
		OrderType:        params.OrderType,
		Freight:          params.Freight,
		DeliveryType:     params.DeliveryType,
		PackingCost:      params.PackingCost,
		TotalWeight:      params.TotalWeight,
		LogisticsCode:    params.LogisticsCode,
		ServiceCharge:    params.ServiceCharge,
		ContractFee:      params.ContractFee, //履约费 当前只有美团平台订单有 v6.5.0添加
		IsPushTencent:    params.IsPushTencent,
		OrderPayType:     params.OrderPayType,
		Lng:              params.Lng,
		Lat:              params.Lat,
	}

	//自提单取货地址填门店地址
	if params.DeliveryType == 3 {
		client := dac.GetDataCenterClient()
		result, err := client.RPC.QueryStoreInfo(context.Background(), &dac.StoreInfoRequest{
			FinanceCode: []string{orderMain.ShopId},
		})
		if err != nil {
			glog.Error(orderMain.OldOrderSn, ", 获取店铺信息失败, ", err)
		} else if result.Code != 200 {
			glog.Warning(orderMain.OldOrderSn, ", 获取店铺信息失败, ", result.Message)
		} else if len(result.Details) > 0 {
			shopInfo := result.Details[0]
			orderMainUp.ReceiverName = shopInfo.ShopName
			orderMainUp.ReceiverState = shopInfo.Province
			orderMainUp.ReceiverCity = shopInfo.City
			orderMainUp.ReceiverDistrict = ""
			orderMainUp.ReceiverAddress = shopInfo.Address
		}
	}

	//由于自提订单没有收货地址，则默认使用深圳总部地址。后续根据实际情况修改
	if len(orderMainUp.ReceiverName) == 0 {
		orderMainUp.ReceiverName = "巨星"
	}
	if len(orderMainUp.ReceiverState) == 0 {
		orderMainUp.ReceiverState = "广东省"
	}
	if len(orderMainUp.ReceiverCity) == 0 {
		orderMainUp.ReceiverCity = "深圳市"
	}
	if len(orderMainUp.ReceiverDistrict) == 0 {
		orderMainUp.ReceiverDistrict = "福田区"
	}
	if len(orderMainUp.ReceiverAddress) == 0 {
		orderMainUp.ReceiverAddress = "广东省深圳市福田区京基时代广场A栋56楼"
	}

	orderDetailup := &models.OrderDetail{
		PayType:        params.PayType,
		Invoice:        params.Invoice,
		BuyerMemo:      params.BuyerMemo,
		SellerMemo:     params.SellerMemo,
		DeliveryRemark: params.DeliveryRemark,
		Extras:         params.Extras,
		Latitude:       params.Latitude,
		Longitude:      params.Longitude,
		PickupCode:     params.PickupCode,
		IsAdjust:       params.IsAdjust,
		ConsultOrderSn: params.ConsultOrderSn,
	}
	if params.ExpectedTime != "" {
		orderDetailup.ExpectedTime, _ = time.ParseInLocation(kit.DATETIME_LAYOUT, params.ExpectedTime, time.Local)
	}

	db.ShowSQL()
	session := db.NewSession()
	defer session.Close()
	session.Begin()

	_, err = session.Cols("receiver_name,receiver_state,receiver_city,receiver_district,receiver_address,receiver_phone,receiver_mobile,delivery_type").
		In("order_sn", OrderSns).Update(orderMainUp)
	if err != nil {
		out.Code = 400
		session.Rollback()
		glog.Error("修改修改数据出错" + params.OrderSn)
		out.Message = "修改修改数据出错" + " " + params.OrderSn
		return &out, nil
	}
	_, err = session.Cols("buyer_memo,expected_time,latitude,longitude").
		In("order_sn", OrderSns).Update(orderDetailup)
	if err != nil {
		session.Rollback()
		out.Code = 400
		glog.Error("修改修改数据出错" + params.OrderSn)
		out.Message = "修改修改数据出错" + " " + params.OrderSn
		return &out, nil
	}
	session.Commit()
	return &out, nil
}

// MtSubmitOrderData
// 美团订单数据 落地 将美团订单插入数据库 之后使用异步任务调用MtSubmitOrder方法完成订单数据的真正写入
func (s CartService) MtSubmitOrderData(ctx context.Context, params *oc.MtAddOrderRequest) (*oc.MtAddOrderResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("收到订单（MtSubmitOrderData）k：", paramsJson)
	out := oc.MtAddOrderResponse{Code: 200, Message: "ok"}

	//连接池勿关闭
	db := GetDBConn()

	model := models.OrderMtData{}
	model.OrderSn = params.OrderSn
	model.ProcessStatus = 1
	model.Data = paramsJson
	model.CreateTime = time.Now().In(time.Local)

	isOk, err := db.Insert(model)
	if err != nil {
		glog.Error("接收美团数据插入记录出错:"+params.OrderSn, err.Error())
		out.Message = "接收美团数据 插入记录出错"
		return &out, nil
	}
	if isOk == 0 {
		out.Message = "接收美团数据插入0条记录"
		out.Error = "接收美团数据插入0条记录" + params.OrderSn
		return &out, nil
	}
	glog.Info("收到订单（MtSubmitOrderData）s：", paramsJson)

	go TimingProcessingMtSubmitOrder(true)
	return &out, nil
}

// MtSubmitOrder 阿闻，美团，饿了么，京东到家，电商订单入口
func (s CartService) MtSubmitOrder(ctx context.Context, params *oc.MtAddOrderRequest) (*oc.MtAddOrderResponse, error) {

	glog.Info(params.ReceiverPhone, "，收到订单（MtSubmitOrder）v6.0：", kit.JsonEncode(params))
	out := oc.MtAddOrderResponse{Code: 200, Message: "ok"}
	redisClient := GetRedisConn()
	//用户id+订单号作为订单唯一提交的锁，防止重复提交。因为美团、饿了么、京东到家没有用户id
	lockKey := "order-center:submitorder:" + params.MemberId + params.OrderSn
	if !redisClient.SetNX(lockKey, time.Now().Unix(), 3*time.Minute).Val() {
		out.Code = 400
		out.Message = "任务正在执行中"
		return &out, nil
	}
	defer redisClient.Del(lockKey)

	//grpcContext信息
	grpcContext := s.LoadGrpcContext(ctx)
	glog.Info(params.ReceiverPhone, params.OrderSn, "下单context", kit.JsonEncode(grpcContext))
	if grpcContext.Channel.ChannelId == 0 || grpcContext.Channel.UserAgent == 0 {
		out.Code = 400
		out.Message = "缺少订单来源或UserAgent"
		glog.Error(params.ReceiverPhone, "缺少订单来源或UserAgent:"+params.OrderSn)
		return &out, nil
	}
	//v2.9.10 修改 提前到recover之前 如果session没有new  那么recover 里rollback会panic
	s.session = GetDBConn().NewSession()
	defer s.session.Close()

	storeId := params.ShopId
	//根据订单中的门店id获取对应的数据中心的门店财务编码
	switch grpcContext.Channel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		storeId = getFinanceCodeByChannel(grpcContext.Channel.ChannelId, params.ShopId)
		//根据财务编码找org_id
		shop := new(models.Store)
		if _, err := s.session.Table("datacenter.store").Where("finance_code = ?", storeId).Get(shop); err != nil {
			out.Code = 400
			out.Message = "获取门店信息出错"
			glog.Error(params.ReceiverPhone, "获取门店信息出错:"+params.OrderSn)
			return &out, nil
		}
		params.OrgId = shop.OrgId
		if params.OrgId == 0 {
			params.OrgId = 1
		}
	}

	UPetServ := new(UPetService)

	//秒杀订单
	if params.OrderType == 12 {
		UPetServ.session = GetUPetDBConn().NewSession()
		defer UPetServ.session.Close()
	}

	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Error(params.ReceiverPhone, "新增订单panic错误, ", params.ReceiverPhone, " ", fmt.Sprintf("%s", r), string(stack[:length]))
			_ = s.session.Rollback()

			//秒杀订单
			if params.OrderType == 12 {
				_ = UPetServ.session.Rollback()
			}
			out.Code = 400
			out.Error = fmt.Sprintf("%s", r)
			out.Message = fmt.Sprintf("%s", r)
			go delIntegralLock(params.MemberId, params.OrderSn, redisClient, params.OrgId)
		}
	}()

	isAotuOrder := int32(0)

	if params.OrderSn == "" && (grpcContext.Channel.ChannelId == ChannelMtId ||
		grpcContext.Channel.ChannelId == ChannelElmId ||
		grpcContext.Channel.ChannelId == ChannelJddjId) {
		out.Code = 400
		out.Message = "订单号不能为空"
		return &out, nil
	}

	//商品信息不能为空
	if len(params.OrderProductModel) == 0 {
		out.Code = 400
		out.Message = "订单商品信息不能为空"
		return &out, nil
	}

	//电商订单直接保存,没有其他渠道后续的逻辑
	if grpcContext.Channel.ChannelId == ChannelMallId {
		// 商城的实物订单先判断用户风险
		if err := submitCheckBlackList(params); err != nil {
			out.Code = 400
			out.Message = err.Error()
			return &out, nil
		}

		//判断是否是积分订单
		if params.OrderType == 8 {
			//冻结积分
			integral := CommonService{}
			freezeBool, err := integral.FreezeIntegral(params.MemberId, params.OrderSn, int64(params.Total), params.OrgId)
			if !freezeBool {
				glog.Error(params.OrderSn, ", 积分订单冻结积分失败, ", err)
				out.Code = 400
				out.Message = "积分订单冻结积分失败：" + err.Error()
				out.Error = err.Error()
				go delIntegralLock(params.MemberId, params.OrderSn, redisClient, params.OrgId)
				return nil, err
			}
		}

		//秒杀订单写入商城 后续如果有商城的其他商城订单类型写入 也走这里
		//秒杀需要将写商城的订单相关数据 v2.9.10
		//秒杀性能问题解决方案 ：
		//1 添加限流 让秒杀订单匀速处理（能快速实现） 2：异步保存订单数据到电商以及平台 需要异步流程（无法快速解决）
		if params.OrderType == 12 {
			_ = UPetServ.session.Begin()

			err, msg := UPetServ.CreateUPetMallOrder(params, grpcContext)
			if err != nil {
				_ = UPetServ.session.Rollback() //发生错误后进行回滚

				glog.Error(params.OrderSn, params.ReceiverPhone, ", 写入商城订单数据出错, ", msg, err, kit.JsonEncode(params))
				out.Code = 400
				out.Message = msg
				out.Error = err.Error()
				return &out, err
			}
			//赋值支付号与订单号  后续前端请求商城的支付等流程需要
			out.PaySn = params.PaySn
			out.OrderId = params.OrderId
		}

		// 保存电商的订单
		err := s.SaveMallOrder(params, params.WarehouseId, grpcContext)
		if err != nil {
			glog.Error(params.OrderSn, params.ReceiverPhone, ", 保存电商订单到平台失败, ", err, kit.JsonEncode(params))
			out.Code = 400
			out.Message = "保存电商订单失败！" + err.Error()
			out.Error = err.Error()
			go delIntegralLock(params.MemberId, params.OrderSn, redisClient, params.OrgId)
			//秒杀等需要吸入商城数据的订单落地平台时 如果出错  需要进行回滚
			if params.OrderType == 12 {
				_ = UPetServ.session.Rollback() //发生错误后进行回滚
			}
			return &out, nil
		}

		if params.OrderType == 12 {
			//落地平台成功之后在进行提交
			_ = UPetServ.session.Commit() //发生错误后进行回滚
			//秒杀订单写入商城之后的处理 后续如果有商城的其他商城订单类型写入 也走这里
			go UPetServ.AfterCreateMallOrder()
		}

		out.OrderSn = s.orderMain.OrderSn //返回订单号
		if out.PaySn == "" {              //商城的订单通过平台写入的情况下会赋值支付号  平台的订单号即支付号
			out.PaySn = s.orderMain.OrderSn
		}

		return &out, nil
	}

	//如果是京东到家订单调整则会有两个订单
	if params.OrderSn != "" {
		order := models.OrderMain{}
		ok, err := s.session.Where("old_order_sn = ?", params.OrderSn).OrderBy("id desc").Get(&order)
		if err != nil {
			out.Code = 400
			out.Message = err.Error()
			glog.Error(params.OrderSn + err.Error())
			return &out, nil
		}
		if params.IsAdjust == 1 {
			if order.OrderStatus != 0 {
				glog.Error("订单调整订单已存在:" + params.OrderSn)
				out.Code = 200
				return &out, nil
			}
		} else {
			if ok {
				out.Code = 400
				out.Message = "订单已存在"
				out.Error = "订单已存在"
				glog.Error("订单已存在:" + params.OrderSn)
				return &out, nil
			}
		}
	}

	//根据订单中的门店id获取对应的数据中心的门店财务编码
	switch grpcContext.Channel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		params.ShopId = getFinanceCodeByChannel(grpcContext.Channel.ChannelId, params.ShopId)
	}

	if params.ShopId == "" {
		out.Code = 400
		out.Message = "财务编码不存在！"
		out.Error = out.Message
		return &out, nil
	}

	// 查询门店信息
	dataCenterConn := dac.GetDataCenterClient()
	var appChannel, deliveryMethod int32
	if response, err := dataCenterConn.RPC.ShopStoreGet(dataCenterConn.Ctx, &dac.ShopStoreGetRequest{
		Finance_Code: params.ShopId,
	}); err != nil {
		out.Code = 400
		out.Message = "获取门店信息失败"
		out.Error = out.Message
		glog.Error(params.ReceiverPhone, "调用ShopStoreGet失败，", err, "，FinanceCode：", params.ShopId)
	} else if response != nil && response.Code == 200 {
		params.ShopName = response.Data.ShopName
		appChannel = response.Data.AppChannel
		deliveryMethod = response.Data.DeliveryMethod
	}
	if appChannel == 0 {
		appChannel = 1
	}
	//根据门店财务编码获取对应的数据中心仓库类型(根据类型推送子龙还是全渠道,grpc接口张震提供)
	glog.Info(params.ReceiverPhone, params.OrderSn, ", 获取对应的数据中心仓库类型(GetWarehouseInfoByFanceCode)入参 FinanceCode : "+params.ShopId)

	warehouseChannel := int32(1)
	switch grpcContext.Channel.ChannelId {
	case ChannelAwenId:
		if params.OrderType == 3 {
			warehouseChannel = 10
		}
		break
	case ChannelMtId, ChannelElmId, ChannelJddjId, ChannelDigitalHealth:
		warehouseChannel = cast.ToInt32(grpcContext.Channel.ChannelId)
		break
	}

	warehouseInfo := new(models.Warehouse)

	if has, err := s.session.Table("dc_dispatch.warehouse_relation_shop").Alias("r").
		Join("inner", "dc_dispatch.warehouse w", "r.warehouse_id = w.id").Select("w.*").
		Where("r.shop_id = ? and r.channel_id = ?", params.ShopId, warehouseChannel).
		Get(warehouseInfo); err != nil {
		glog.Error(params.OrderSn, ", 获取仓库信息失败, ", err, " FinanceCode : "+params.ShopId)
		out.Code = 400
		out.Message = "查询仓库出错"
		out.Error = err.Error()
		return &out, nil
	} else if !has {
		out.Code = 400
		out.Message = "仓库信息不存在，财务编码：" + params.ShopId
		return &out, nil
	}

	params.WarehouseId = warehouseInfo.Id

	//按店铺设置的配送方式(到店自提的除外), 仅针对阿闻、互联网医院渠道
	if params.OrderType != 3 && (grpcContext.Channel.ChannelId == ChannelAwenId || grpcContext.Channel.ChannelId == ChannelDigitalHealth) {
		switch deliveryMethod {
		case 1: //外卖配送
			params.DeliveryType = 2
		case 2: //物流配送
			params.DeliveryType = 1
			params.OrderType = 1
			params.ExpectedTime = "" //快递配送默认处理为没有期望送达时间
		case 3: //商家自配
			params.DeliveryType = 5
		}
	}
	if params.DeliveryType == 1 && params.PickupStationId > 0 {
		out.Code = 400
		out.Message = "店铺物流配送，不支持站点自提下单"
		return &out, nil
	}

	clientPc := pc.GetDcProductClient()
	defer clientPc.Close()

	// 查询下单商品快照信息v6.0
	productIds := make([]int32, 0, len(params.OrderProductModel))
	skuIds := make([]int32, 0)
	for _, item := range params.OrderProductModel {
		productIds = append(productIds, cast.ToInt32(item.ProductId))
		skuIds = append(skuIds, cast.ToInt32(item.Sku))
	}

	//重新写了一个方法，SAAS平台查询新的商品库，返回一样的结构体
	productSnapshotMap := make(map[int32]*pc.ChannelProductRequest)
	//如果是SAAS平台的话，就走新的库查询数据
	if params.OrgId == 6 {
		productSnapshotMap1, err := querySaaSChannelProductSnapshot(clientPc, int32(grpcContext.Channel.ChannelId), params.ShopId, productIds)
		if err != nil {
			out.Code = http.StatusBadRequest
			out.Message = "查询商品信息异常，下单失败"
			out.Error = err.Error()
			glog.Error(params.OrderSn, "查询商品信息异常，下单失败, ", err.Error(), ", ", kit.JsonEncode(productIds))
			return &out, nil
		}
		productSnapshotMap = productSnapshotMap1
	} else {
		productSnapshotMap1, err := queryChannelProductSnapshot(clientPc, int32(grpcContext.Channel.ChannelId), params.ShopId, skuIds)
		if err != nil {
			out.Code = http.StatusBadRequest
			out.Message = "查询商品信息异常，下单失败"
			out.Error = err.Error()
			glog.Error(params.OrderSn, "查询商品信息异常，下单失败, ", err.Error(), ", ", kit.JsonEncode(productIds))
			return &out, nil
		}
		productSnapshotMap = productSnapshotMap1

	}

	//阿闻到家
	if grpcContext.Channel.ChannelId == ChannelAwenId || grpcContext.Channel.ChannelId == ChannelDigitalHealth {
		//从sku信息中获取第三方货号信息及价格信息
		SkuMap := make(map[string]*pc.SkuInfo)
		for _, v := range productSnapshotMap {
			for _, sku := range v.SkuInfo {
				SkuMap[cast.ToString(sku.SkuId)] = sku
			}
		}

		//组合商品子商品的价格要取父商品的快照信息
		if len(productSnapshotMap) > 0 {
			for _, item := range params.OrderProductModel {
				skuInfo, ok := SkuMap[item.Sku]
				if !ok {
					continue
				}
				//如果是组合商品 则需要修改product_type
				if len(skuInfo.SkuGroup) > 0 {
					item.ProductType = 3
					//是什么组合 combine_type
					var realFlag, virtualFlag int32
					for _, child := range skuInfo.SkuGroup {
						if child.ProductType == 1 && realFlag == 0 {
							realFlag = 1
						}
						if child.ProductType == 2 && virtualFlag == 0 {
							virtualFlag = 2
						}
					}
					item.CombineType = realFlag + virtualFlag
				}
				item.BarCode = skuInfo.BarCode
				//v6.6.7.1 添加 将商品分类写入到订单商品表
				intProductId := cast.ToInt32(item.ProductId)
				productSnapshot, _ := productSnapshotMap[intProductId]
				item.ChannelCategoryName = productSnapshot.Product.ChannelCategoryName
				//第三方货号处理
				for _, t := range skuInfo.SkuThird {
					if warehouseInfo.Category == 3 { //门店仓
						if t.ErpId == 4 { //子龙
							item.ArticleNumber = t.ThirdSkuId
						}
						params.OrderPayType = "02"
					} else if warehouseInfo.Category == 4 || warehouseInfo.Category == 5 || warehouseInfo.Category == 1 { //前置仓 or 电商仓
						if t.ErpId == 2 { //a8
							item.ArticleNumber = t.ThirdSkuId
						}
						params.OrderPayType = "01"
					}
				}

				//实物和虚拟商品取规格
				if item.ProductType != 3 && len(skuInfo.Skuv) > 0 {
					item.Specs = skuInfo.Skuv[0].SpecName + "：" + skuInfo.Skuv[0].SpecValueValue
				}
			}
		}
		if grpcContext.Channel.ChannelId == ChannelAwenId && params.OrgId != 6 {
			if err := pickupValidateStation(grpcContext, params); err != nil {
				glog.Info("社区团购 提交订单验证自提点出错：" + err.Error())
				out.Code = http.StatusBadRequest
				out.Message = err.Error()
				out.Error = err.Error()
				return &out, nil
			}

			// 存在处方单号，标记处方药
			if params.ConsultOrderSn != "" {
				if has, err := s.session.Table("order_detail").Where("consult_order_sn = ?", params.ConsultOrderSn).Exist(); err != nil {
					out.Code = http.StatusBadRequest
					out.Message = err.Error()
					return &out, nil
				} else if has {
					out.Code = http.StatusBadRequest
					out.Message = "当前没有可用处方签"
					return &out, nil
				}
				// 处方药标识查询
				var prescribeIds []string
				prescribeMap := make(map[string]struct{})

				if err := s.session.Table("dc_product.product").Where("is_prescribed_drug = 1").
					In("id", productIds).Select("id").Find(&prescribeIds); err != nil {
					out.Code = http.StatusBadRequest
					out.Message = "查询商品信息出错"
					out.Error = err.Error()
					return &out, nil
				}
				for _, id := range prescribeIds {
					prescribeMap[id] = struct{}{}
				}
				for _, op := range params.OrderProductModel {
					if _, ok := prescribeMap[op.ProductId]; ok {
						op.IsPrescribedDrug = 1
					}
				}
			} else if grpcContext.Channel.UserAgent == 1 || grpcContext.Channel.UserAgent == 2 { // 针对阿闻渠道app来源，校验处方药
				if has, err := s.session.Table("dc_product.product").In("id", productIds).Where("is_prescribed_drug = 1").Exist(); err != nil {
					out.Code = http.StatusBadRequest
					out.Message = "查询商品信息出错"
					out.Error = err.Error()
					return &out, nil
				} else if has {
					out.Code = http.StatusBadRequest
					out.Message = "app暂不支持购买处方药，请到阿闻宠物小程序下单"
					return &out, nil
				}
			}
		}
	} else {
		//查询SKU信息
		skuMap := map[string]*pc.Sku{}

		//如果是新零售的数据
		if params.OrgId == 6 {

			for _, v := range productSnapshotMap {
				for _, vv := range v.SkuInfo {
					item := pc.Sku{}
					item.ProductId = v.Product.Id
					item.BarCode = vv.BarCode
					if len(vv.SkuThird) > 0 {
						item.SkuThird = vv.SkuThird
					}
					item.LocationCode = vv.LocationCode
					item.SkuValue = []*pc.SkuValue{&pc.SkuValue{SpecName: "规格", SpecValueValue: vv.Skuv[0].SpecValueValue}}
					skuMap[cast.ToString(vv.SkuId)] = &item
				}

			}

		} else {
			//todo v6.0 查询sku 修改skuMap 的逻辑 使用skuId进行查询
			res, err := clientPc.RPC.QueryChannelSku(ctx, &pc.OneofIdRequest{
				Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{
					Value: productIds,
				}},
				ChannelId: int32(grpcContext.Channel.ChannelId),
			})
			//查询第三方sku结果
			if err != nil {
				glog.Error(params.OrderSn, ", 查询订单sku信息失败, ", err)
			} else if len(res.Details) == 0 {
				glog.Error(params.OrderSn, ", 查询订单sku信息为空")
			} else {

				for _, v := range res.Details {
					skuMap[cast.ToString(v.ProductId)] = v
				}
			}
		}

		for _, product := range params.OrderProductModel {
			var sku *pc.Sku
			var ok bool
			if params.OrgId == 6 {
				sku, ok = skuMap[product.Sku]
			} else {
				sku, ok = skuMap[product.ProductId]
			}

			if !ok {
				continue
			}

			product.BarCode = sku.BarCode
			product.LocationCode = sku.LocationCode
			if params.OrgId == 6 {
				product.Specs = sku.SkuValue[0].SpecValueValue
			}

			//v6.6.7.1 添加 将商品分类写入到订单商品表
			intProductId := cast.ToInt32(product.ProductId)
			productSnapshot, _ := productSnapshotMap[intProductId]
			if productSnapshot == nil || productSnapshot.Product == nil {
				continue
			}
			product.ChannelCategoryName = productSnapshot.Product.ChannelCategoryName

			//如果是组合商品 则需要修改product_type
			if len(sku.SkuGroup) > 0 {
				product.ProductType = 3
				//是什么组合 combine_type
				var realFlag, virtualFlag int32
				for _, child := range sku.SkuGroup {
					if child.ProductType == 1 && realFlag == 0 {
						realFlag = 1
					}
					if child.ProductType == 2 && virtualFlag == 0 {
						virtualFlag = 2
					}
				}
				product.CombineType = realFlag + virtualFlag
			}
			switch warehouseInfo.Category {
			case 3:
				for _, third := range sku.SkuThird {
					if third.ErpId == 4 {
						product.ArticleNumber = third.ThirdSkuId
						break
					}
				}
			case 4, 5, 1:
				for _, third := range sku.SkuThird {
					if third.ErpId == 2 {
						product.ArticleNumber = third.ThirdSkuId
						break
					}
				}
			}
		}

	}

	// 设置订单商品主图
	for _, v := range params.OrderProductModel {
		snapshot, ok := productSnapshotMap[cast.ToInt32(v.ProductId)]
		if !ok {
			continue
		}
		//商品图片取第一张
		picArr := strings.Split(snapshot.Product.Pic, ",")
		if len(picArr) > 0 {
			v.Image = picArr[0]
		}
	}

	s.session.Begin()

	if grpcContext.Channel.ChannelId == ChannelElmId {
		params.PickupCode = cast.ToString(params.OrderIndex)
	}
	//保存订单
	err := s.SaveMtOrder(params, warehouseInfo, grpcContext, appChannel)
	//复制一份主单
	if err != nil {
		out.Code = 400
		out.Message = "保存订单失败！"
		out.Error = err.Error()
		s.session.Rollback()
		glog.Error(params.OrderSn, ", 保存订单失败, ", err.Error())
		return &out, nil
	}
	shopSet, err := s.GetShopSet()
	if err != nil {
		s.session.Rollback()
		out.Code = 400
		out.Message = "查询门店接单设置失败"
		out.Error = err.Error()
		return &out, err
	}
	isAotuOrder = shopSet.RetInfo.IsAotuOrder
	//如果是第三方的订单先查询一下库存再锁库存
	//switch s.orderMain.ChannelId {
	//case ChannelMtId, ChannelElmId, ChannelJddjId:
	//	s.OrderQueryInventory()
	//}
	//todo 所有平台全部查询一次库存
	if params.OrgId == 1 {
		s.OrderQueryInventory(params.WarehouseId)
	}
	s.orderDetail.LockedStock = 1
	//锁库存
	if (s.orderMain.ChannelId == ChannelMallId && s.orderMain.OrderType == 8 && params.OrderProductModel[0].IsThirdProduct == 1) || s.orderMain.OrderType == 4 {
		//积分订单中-电商的第三方商品不做库存扣减
		//拼团订单不锁库存
	} else {
		//订单需要扣库存
		err = s.OrderLockInventory()
		if err != nil {
			if s.orderMain.ChannelId == ChannelAwenId || s.orderMain.ChannelId == ChannelDigitalHealth {
				s.session.Rollback()
				out.Code = 400
				out.Message = err.Error()
				out.Error = err.Error()
				return &out, nil
			}

			s.orderDetail.LockedStock = 0
			//锁库存不成功不接单，可重新推送第三方再次锁库存
			isAotuOrder = 0
		}
	}
	//美团、饿了么，京东到家 接单
	if s.orderDetail.IsAdjust == 1 {
		s.orderMain.OrderStatusChild = 20102
		s.orderDetail.AcceptTime = time.Now()
		s.orderDetail.AcceptUsername = "订单调整自动接单"
	}

	_, err = s.session.ID(s.orderMain.Id).Update(s.orderMain)
	if err != nil {
		s.session.Rollback()
		out.Code = 400
		out.Message = "订单保存失败"
		out.Error = "订单保存失败"
		glog.Error("订单保存失败:" + s.orderMain.OrderSn + " " + err.Error())
		return &out, nil
	}

	_, err = s.session.ID(s.orderMain.OrderSn).Update(s.orderDetail)
	if err != nil {
		s.session.Rollback()
		out.Code = 400
		out.Message = "订单保存失败"
		out.Error = "订单保存失败"
		glog.Error("订单保存失败:" + s.orderMain.OrderSn + " " + err.Error())
		return &out, nil
	}

	glog.Info("添加订单，commit！", s.orderMain.OrderSn)

	err = s.session.Commit()

	if err != nil {
		out.Code = 400
		out.Message = "订单保存失败"
		out.Error = "订单保存失败"
		glog.Error("订单保存失败:" + s.orderMain.OrderSn + " " + err.Error())
		s.session.Rollback()
		return &out, nil
	}
	//第三方渠订单进行拆单
	//组合商品在第三方只是一个商品 下单时没有组合商品明细信息
	//v6.0之前的版本第三方的订单没有进行拆单，6.0版本因为有虚实组合 需要将虚拟商品与实物商品进行拆分 所以对第三方的订单进行统一拆单
	if s.orderMain.ChannelId == ChannelMtId || s.orderMain.ChannelId == ChannelElmId || s.orderMain.ChannelId == ChannelJddjId {
		//第三订单拆单 拆单失败不用处理 走异常
		realOrders, _ := s.ThirdSplitOrder()
		if isAotuOrder == 1 || s.orderDetail.IsAdjust == 1 {
			//非前置仓 或者 非自提 或者是京东 都需要接单确认到第三方平台
			//如果推送第三方接单失败（体现为未接单状态） 则可能导致第三方取消订单 钱已经退给了用户 但是我们已经将订单发起了配送与推送了第三方
			if warehouseInfo.Category != 4 || params.DeliveryType != 3 || s.orderMain.ChannelId == ChannelJddjId {
				glog.Error("MtOrderConfirm:入参", kit.JsonEncode(s.orderMain))
				_, err = MtOrderConfirm(params.OrderSn, s.orderMain.ChannelId, s.orderMain.AppChannel)
				if err != nil {
					glog.Error("推送接单失败！", params.OrderSn, err)
				}
				//调整单 一定接单成功
				if err == nil || s.orderDetail.IsAdjust == 1 {
					//获取实物子订单 更新主单与子单的接单状态 与接单时间
					orderMainUp := &models.OrderMain{
						OrderStatusChild: 20102,
					}
					orderDetailUp := &models.OrderDetail{
						AcceptTime:     time.Now(),
						AcceptUsername: "平台自动接单",
					}
					orders := []string{s.orderMain.OrderSn}
					orders = append(orders, realOrders...)
					glog.Info(s.orderMain.OrderSn, "更新接单状态订单号：", orders)
					_, err = s.session.In("order_sn", orders).Update(orderDetailUp)
					if err != nil {
						glog.Error("更新订单详情自动接单状态出错:"+s.orderMain.OrderSn+" "+err.Error(), orders)
					}

					//实物子弹也要更新状态
					_, err = s.session.In("order_sn", orders).Update(orderMainUp)
					if err != nil {
						glog.Error("更新自动接单状态出错1:"+s.orderMain.OrderSn+" "+err.Error(), orders)
					}
					//冻结积分
					server := OrderService{}
					server.session = s.session
					//接单后调用打印打印小票
					go server.PrintOrderDetail(params.OrderSn)

				}
			}

			//必须推一次第三方 因为阿闻与京东没有接单回调（我们的接单回调中可以发配送 删除库 推单 更改接单状态） 不会发配送 删除库 推单
			//如果已经在此处主动推送了第三方，则美团与饿了么回调后不在进行处理相关逻辑
			glog.Info("订单接单后，推送第三方进入", s.orderMain.OrderSn)
			_ = s.ThirdPushOrder()
		}
	}
	//}

	if s.orderMain.ChannelId == ChannelAwenId || s.orderMain.ChannelId == ChannelDigitalHealth {
		go func() {
			defer kit.CatchPanic()

			//记录订单流转日志
			SaveOrderLog([]*models.OrderLog{{
				OrderSn: s.orderMain.OrderSn,
				LogType: models.OrderLogSubmitOrder,
			}})

			//todo 支付订单统一15分钟过期
			//if s.orderMain.DeliveryType == 3 { // 阿闻自提订单超5分钟未支付自动取消
			//	glog.Info(s.orderMain.OrderSn, "，阿闻自提单加入未支付取消队列")
			//	if err := utils.PublishRabbitMQV2(AwenNoPayCancelRoute, MQExchange, s.orderMain.OrderSn, int64(5*time.Minute/time.Millisecond)); err != nil {
			//		glog.Error(s.orderMain.OrderSn, "，阿闻自提单加入未支付取消队列失败，", err)
			//	}
			//} else if s.orderMain.UserAgent == 3 || s.orderMain.UserAgent == 7 || s.orderMain.UserAgent == 1 || s.orderMain.UserAgent == 2 { //阿闻到家小程序和竖屏订单超15分钟未支付自动取消
			glog.Info(s.orderMain.OrderSn, "，阿闻到家小程序和竖屏订单加入未支付取消队列")
			if err := utils.PublishRabbitMQV2(AwenNoPayCancelRoute, MQExchange, s.orderMain.OrderSn, int64(15*time.Minute/time.Millisecond)); err != nil {
				glog.Error(s.orderMain.OrderSn, "，阿闻到家小程序和竖屏订单加入未支付取消队列失败，", err)
			}
			if s.orderDetail.ConsultOrderSn != "" {
				_ = PushDigitalHealthOrder(s.orderMain.OrderSn, s.orderDetail.ConsultOrderSn, 0, 0)
			}
		}()
	} else {
		//通知数据中心
		message := &models.Message{
			OrderId:     s.orderMain.OrderSn,
			MessageType: 1,
			FinanceCode: s.orderMain.ShopId,
			Msg:         fmt.Sprintf("【客户下单】您有一个新的订单：%s，请及时处理！", s.orderMain.OrderSn),
		}
		MessageCreate(message)
	}

	out.OrderSn = s.orderMain.OrderSn
	out.PaySn = s.orderMain.OrderSn
	if params.OrderType == 13 && params.DiagnoseData != nil {
		out.DoctorCode = params.DiagnoseData.DoctorCode
	}
	return &out, nil
}

// 查询渠道商品快照信息
func queryChannelProductSnapshot(client *pc.Client, channelId int32, financeCode string, skuIds []int32) (map[int32]*pc.ChannelProductRequest, error) {
	resp, err := client.RPC.QueryChannelProductSnapshot(context.Background(), &pc.ChannelProductSnapshotRequest{
		ChannelId:   channelId,
		FinanceCode: financeCode,
		SkuId:       skuIds,
	})
	if err != nil {
		glog.Errorf("rpc调用QueryChannelProductSnapshot异常,channelId:%d,financeCode:%s,skuIds:%v,err:%+v", channelId, financeCode, skuIds, err)
		return nil, err
	}

	if resp.Code != http.StatusOK {
		glog.Errorf("rpc调用QueryChannelProductSnapshot业务处理失败(%d),channelId:%d,financeCode:%s,skuIds:%v,msg:%s,err:%s", resp.Code, channelId, financeCode, skuIds, resp.Message, resp.Error)
		return nil, errors.New(resp.Message)
	}

	result := make(map[int32]*pc.ChannelProductRequest, len(resp.Details))
	for _, v := range resp.Details {
		productSnapshot := pc.ChannelProductRequest{}
		err = json.Unmarshal([]byte(v.JsonData), &productSnapshot)
		if err != nil {
			glog.Errorf("反序列化商品快照信息异常,channelId:%d,financeCode:%s,skuIds:%v,err:%+v", channelId, financeCode, skuIds, err)
			return nil, err
		}
		result[productSnapshot.Product.Id] = &productSnapshot
	}
	return result, nil

}

// 转写上面的方法
func querySaaSChannelProductSnapshot(client *pc.Client, channelId int32, financeCode string, productIds []int32) (map[int32]*pc.ChannelProductRequest, error) {
	resp, err := client.RPC.GetEshopProductSnapshotBySpuOrSku(context.Background(), &pc.GetChannelProductSnapshotBySpuOrSkuRequest{
		ChannelId:   channelId,
		FinanceCode: financeCode,
		ProductId:   productIds,
	})
	glog.Info("获取宠物saas的商品快照信息", kit.JsonEncode(resp))
	if err != nil {
		glog.Errorf("rpc调用QueryChannelProductSnapshot异常,channelId:%d,financeCode:%s,productIds:%v,err:%+v", channelId, financeCode, productIds, err)
		return nil, err
	}

	if resp.Code != http.StatusOK {
		glog.Errorf("rpc调用QueryChannelProductSnapshot业务处理失败(%d),channelId:%d,financeCode:%s,productIds:%v,msg:%s,err:%s", resp.Code, channelId, financeCode, productIds, resp.Message, resp.Error)
		return nil, errors.New(resp.Message)
	}

	result := make(map[int32]*pc.ChannelProductRequest, len(resp.Details))
	for _, v := range resp.Details {
		productSnapshot := pc.ChannelProductRequest{}
		productSnapshot.Product = v.Product
		productSnapshot.ProductAttr = v.ProductAttr
		productSnapshot.SkuInfo = v.SkuInfo
		productSnapshot.FinanceCode = financeCode
		//err = json.Unmarshal([]byte(v.JsonData), &productSnapshot)
		//if err != nil {
		//	glog.Errorf("反序列化商品快照信息异常,channelId:%d,financeCode:%s,productIds:%v,err:%+v", channelId, financeCode, productIds, err)
		//	return nil, err
		//}
		result[productSnapshot.Product.Id] = &productSnapshot
	}
	return result, nil

}

//删除积分订单锁

func checkDoctorIsOpen(doctorCode string, diagnoseForm, doctorType int32) bool {
	db := GetDBConn()
	if doctorType == 2 {
		var doctorInternetDoctor models.DiagnoseInternetDoctor
		ok, err := db.Where("doctor_code = ? and on_line = 1 and is_forbidden = 0", doctorCode).Get(&doctorInternetDoctor)
		if err != nil {
			return false
		}
		if !ok {
			return false
		}
	}
	return true
}

// 获取已经存在的问诊订单，并返回订单号和医生编号
func checkHaveOrder(doctorCode, userId string, diagnoseProject int32) (string, string, error) {
	db := GetDBConn()

	sql := db.Select("d.*").Table("diagnose_info").Alias("d").
		Join("inner", "order_main o", "o.order_sn=d.order_sn").
		Where("o.order_status in(10,20) and d.state in(1,2,3)")

	if len(doctorCode) > 0 && diagnoseProject != 1 {
		sql.And("d.doctor_code = ? ", doctorCode)
	}

	if len(userId) > 0 {
		sql.And("d.scrm_user_id = ? ", userId)
	}

	if diagnoseProject == 1 {
		sql.And("d.diagnose_project = ? ", diagnoseProject)
	} else {
		sql.And("d.diagnose_project != ? ", 1)
	}

	var diagnoseInfo models.DiagnoseInfo

	if ok, err := sql.Get(&diagnoseInfo); err != nil {
		return "", "", err
	} else if !ok {
		return "", "", nil
	}

	if diagnoseInfo.Id <= 0 {
		return "", "", nil
	}
	return diagnoseInfo.OrderSn, diagnoseInfo.DoctorCode, nil
}

func delIntegralLock(memberId, orderSn string, redisClient *redis.Client, orgId int32) {
	//订单异常删除key
	integralKey := fmt.Sprintf("Integral.Lock.memberId:%s.%s", memberId, cast.ToString(orgId))
	//扣减成功后，删除冻结字段方法
	redisClient.HDel(integralKey, orderSn)
	_freezeKey := fmt.Sprintf("Integral.Lock.%s", memberId)
	redisClient.Del(_freezeKey)
}

// HealthSubOrder 生产健康管理订阅订单
func (s CartService) HealthSubOrder(ctx context.Context, request *oc.HealthSubOrderRequest) (*oc.HealthSubOrderResponse, error) {
	glog.Info("收到订单（HealthSubOrder）：", kit.JsonEncode(request))
	out := oc.HealthSubOrderResponse{Code: 200, Message: "ok"}

	isReNew := 0

	if len(request.EnsureCode) > 0 {
		isReNew = 1
	}

	orderInfo := models.Order{
		OrderMain: &models.OrderMain{
			OrderSn:          GetSn("order")[0],
			OldOrderSn:       "",
			ParentOrderSn:    "",
			OrderStatus:      0,
			PaySn:            "",
			PayMode:          0,
			PayAmount:        0,
			ConfirmTime:      time.Now().Local(),
			DeliverTime:      time.Now().Local(),
			CancelReason:     "",
			IsVirtual:        0,
			OrderStatusChild: 10201,
			MemberId:         request.ScrmUserId,
			MemberName:       request.MemberName,
			MemberTel:        request.MemberTel,
			Privilege:        request.PayMoney,
			Total:            request.PayMoney,
			GoodsTotal:       1,
			IsPay:            0,
			CreateTime:       time.Now().Local(),
			OrderType:        6,
			Freight:          0,
			Source:           3,
			DeliveryType:     0,
			PackingCost:      0,
			ServiceCharge:    0,
			TotalWeight:      0,
			ChannelId:        ChannelAwenId,
			UserAgent:        3,
			FreightPrivilege: 0,
		},
		GyDeliverStatus: 0,
		PushDelivery:    0,
		PushThirdOrder:  0,
		Latitude:        0.000000,
		Longitude:       0.000000,
		LockedStock:     0,
		IsAdjust:        0,
	}
	//生成订单号

	orderInfo.OldOrderSn = orderInfo.OrderSn
	orderProduct := models.OrderProduct{
		OrderSn:        orderInfo.OrderSn,
		SkuId:          request.CategoryCode,
		ProductId:      request.CategoryCode,
		ProductName:    request.CategoryName,
		BarCode:        "",
		DiscountPrice:  request.PayMoney,
		PayPrice:       request.PayMoney,
		SkuPayTotal:    request.PayMoney,
		Number:         1,
		Specs:          "",
		PaymentTotal:   request.PayMoney,
		Privilege:      0,
		PrivilegePt:    0,
		PrivilegeTotal: 0,
		Freight:        0,
		MarkingPrice:   request.PayMoney,
		DeliverStatus:  0,
		DeliverNum:     1,
		RefundNum:      0,
		Image:          "",
		PromotionId:    0,
		PromotionType:  1,
	}

	orderMeal := models.OrderMeal{
		PetId:        request.ScrmPetId,
		UserId:       request.ScrmUserId,
		OrderSn:      orderInfo.OrderSn,
		CategoryCode: request.CategoryCode,
		EnsureCode:   request.EnsureCode,
		IsRenew:      isReNew,
	}

	//连接池勿关闭
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()
	session.Begin()

	//插入数据库
	_, err := session.Insert(&orderInfo, &orderProduct, &orderMeal)
	if err != nil {
		out.Code = 400
		out.Message = "订单保存失败"
		out.Error = "订单保存失败"
		session.Rollback()
		return &out, nil
	}

	err = session.Commit()
	if err != nil {
		out.Code = 400
		out.Message = "订单保存失败"
		out.Error = "订单保存失败"
		session.Rollback()
		return &out, nil
	}
	out.OrderSn = orderInfo.OrderSn
	return &out, nil
}

func (s CartService) UpdateOrderMeal(ctx context.Context, request *oc.UpdateOrderMealRequest) (*oc.BaseResponse, error) {
	glog.Info("收到参数（CreateOrderMeal）：", kit.JsonEncode(request))
	out := oc.BaseResponse{Code: 200, Message: "ok"}
	//连接池勿关闭
	db := GetDBConn()
	model := models.OrderMeal{
		OrderSn:    request.OrderSn,
		EnsureCode: request.EnsureCode,
		BatchCode:  request.BatchCode,
	}
	_, err := db.Exec("UPDATE order_meal SET ensure_code=? ,batch_code=? WHERE order_sn=? ", model.EnsureCode, model.BatchCode, model.OrderSn)
	if err != nil {
		glog.Error("更新OrderMeal,UpdateOrderMeal:", err, kit.JsonEncode(request))
		out.Code = 400
		out.Message = "更新订单与健康管理订阅套餐失败"
		return &out, nil
	}

	//推送订单状态到 腾讯有数
	go PushOrderStatusToTencent(model.OrderSn, 0)
	return &out, nil
}

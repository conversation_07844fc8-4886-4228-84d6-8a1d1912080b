package services

import (
	"encoding/json"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"strconv"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"
	"github.com/tricobbler/rp-kit/cast"
)

func SetRefundOrder(params *oc.RefundOrderApplyRequest, orderSn string) (model models.RefundOrder) {
	time := time.Now()
	model = models.RefundOrder{}
	//第三方订单退的的是主订单号
	model.OrderSn = orderSn
	model.RefundSn = params.RefundOrderSn
	model.CreateTime = time
	model.ServiceType = params.ServiceType
	if params.RefundType == 1 {
		model.RefundTypeSn = "仅退款"
	} else {
		model.RefundTypeSn = "退款退货"
	}
	model.RefundType = int32(params.RefundType)
	model.RefundRemark = params.RefundRemark
	model.RefundReason = params.Reason
	model.RefundAmount = cast.ToString(params.RefundAmount) //retOrderModel.Money
	model.RefundState = 1
	model.IsCancelOrder = params.IsCancalOrder
	model.ChannelId = params.ChannelId
	model.ApplyOpUserType = params.ApplyOpUserType
	model.FullRefund = params.FullRefund
	model.OldOrderSn = orderSn
	model.OldRefundSn = params.OldRefundSn
	model.ActivityPtAmount = cast.ToString(params.ActivityPtAmount)
	model.DeliveryPrice = cast.ToString(params.DeliveryPrice)
	if len(params.ExternalOrderId) > 0 {
		model.OldOrderSn = params.ExternalOrderId
	}
	return model
}

func SetRefundOrderLog(externalOrderId string, reason string, pictures string,
	applyOpUserType string, operationType string, notifyType string, resType string,
	refundSn string, operationUser string) (model models.RefundOrderLog) {
	model = models.RefundOrderLog{
		RefundSn:        refundSn,
		OldOrderSn:      externalOrderId,
		Ctime:           time.Now(),
		Reason:          reason,
		Pictures:        pictures,
		Money:           0, //params.RefundAmount
		ApplyOpUserType: applyOpUserType,
		OperationType:   operationType,
		Operationer:     operationUser,
		NotifyType:      notifyType, // "apply"
		ResType:         resType,    //params.ResType
		CreateTime:      time.Now(),
	}

	////操作人的KEY
	//key := "mt:return:"
	////操作备注的KEY
	//keyreason := "elm:returnReason:"
	//if notifyType == "apply" {
	//	key += notifyType + ":"
	//	keyreason += notifyType + ":"
	//}
	//if notifyType == "agree" || notifyType == "reject" || notifyType == "first-agree" {
	//	key += "res:"
	//	keyreason += "res:"
	//}
	//
	////连接池勿关闭
	//redisClient := GetRedisConn()
	//
	//if redisClient.Exists(keyreason+externalOrderId).Val() > 0 {
	//	codeStr := redisClient.Get(keyreason + externalOrderId).Val() // 获取Redis中的Code
	//	model.Reason = codeStr
	//}
	//
	//if redisClient.Exists(key+externalOrderId).Val() > 0 {
	//	codeStr := redisClient.Get(key + externalOrderId).Val() // 获取Redis中的Code
	//	glog.Infof("MtOrderRefundAgree:s:%s", codeStr)
	//	model.Operationer = codeStr
	//	redisClient.Del(key + externalOrderId)
	//}

	return model
}

//func DelRedisValue(notifyType string, externalOrderId string) {
//	//连接池勿关闭
//	redisClient := GetRedisConn()
//
//	//如果是申请或者应答，清除对应的redis
//	if notifyType == "agree" || notifyType == "reject" {
//		//redisClient.Del("mt:return:res:" + externalOrderId)
//		//redisClient.Del("elm:returnReason:res:" + externalOrderId)
//	}
//	if notifyType == "part" || notifyType == "apply" {
//		redisClient.Del("mt:return:apply:" + externalOrderId)
//		redisClient.Del("elm:returnReason:apply:" + externalOrderId)
//	}
//}

// 处理售后单
func TimingProcessingMtRefundOrderData(isRealTime bool) bool {
	db := GetDBConn()

	strSql := "select * from `refund_order_mt_data` a where a.`process_status`=1 and create_time<=DATE_SUB(SYSDATE(3),INTERVAL 10 SECOND) order by `id` asc"
	if isRealTime {
		strSql = "select * from `refund_order_mt_data` a where a.`process_status`=1 order by `id` asc"
	}
	orderMtRefundOrder := make([]models.RefundOrderMtData, 0)
	err := db.SQL(strSql).Find(&orderMtRefundOrder)
	if err != nil {
		glog.Error("定时处理获取美团订单数据错误:F:(TimingProcessingMtRefundOrderData)", err.Error())
	}

	if len(orderMtRefundOrder) <= 0 {
		return false
	}

	//连接池勿关闭
	redisConn := GetRedisConn()
	for k, item := range orderMtRefundOrder {
		lockCard := "task:lock:order_refund_data_r:" + item.RefundSn
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
		if !lockRes {
			continue
		}

		params := new(oc.OrderRetrunRequest)
		err = json.Unmarshal([]byte(item.Data), params)
		if err != nil {
			glog.Error("定时处理获取美团订单数据错误:J:(TimingProcessingMtRefundOrderData)"+item.RefundSn, err)
			orderMtRefundOrder[k].ProcessStatus = 2
			orderMtRefundOrder[k].Reason = "定时处理获取美团售后单数据转换JSON错误"
		} else {
			out := new(oc.BaseResponse)
			//发起售后单
			if params.NotifyType == "part" || params.NotifyType == "apply" {
				glog.Info("MtRefundOrderApply,", isRealTime, kit.JsonEncode(params))
				if params.Status == "20" {
					out = MtExpressInfoUpdate(params)
				} else {
					out = MtRefundOrderApply(params)
				}
			} else if params.NotifyType == "agree" || params.NotifyType == "reject" {
				//应答售后单
				out = MtRefundOrderAnswer(params)
			} else if params.NotifyType == "cancelRefund" || params.NotifyType == "cancelRefundComplaint" {
				// 用户撤销售后单
				out = MtRefundOrderCancel(params)
			}
			if out.Code != 200 {
				orderMtRefundOrder[k].ProcessStatus = 2
				orderMtRefundOrder[k].Reason = out.Message
			} else {
				orderMtRefundOrder[k].ProcessStatus = 3
			}
		}
		strSql = "update refund_order_mt_data set `process_status`=?,reason=?  where refund_sn=? and `notify_type`=?  "
		_, err = db.Exec(strSql, orderMtRefundOrder[k].ProcessStatus, orderMtRefundOrder[k].Reason, orderMtRefundOrder[k].RefundSn, orderMtRefundOrder[k].NotifyType)
		if err != nil {
			glog.Error("定时处理获取美团订单数据错误:U2:(TimingProcessingMtRefundOrderData)"+item.NotifyType, err.Error())
		}
		redisConn.Del(lockCard)
	}
	return true
}

// mt售后单申请
func MtRefundOrderApply(params *oc.OrderRetrunRequest) *oc.BaseResponse {
	out := new(oc.BaseResponse)

	refundAmount, err := strconv.ParseFloat(params.Money, 32)
	if err != nil {
		refundAmount = 0
	}
	apply := new(oc.RefundOrderApplyRequest)
	apply.ExternalOrderId = params.OrderId
	apply.Reason = params.Reason
	//退款类型:1为退款,2为退货退款
	apply.ServiceType = params.ServiceType
	if params.ServiceType == "2" {
		apply.RefundType = 2
	} else {
		apply.RefundType = 1
	}
	apply.ApplyOpUserType = params.ApplyOpUserType
	apply.FullRefund = params.RefunType
	apply.RefundAmount = float32(refundAmount)
	// params.Pictures url解码
	if params.Pictures != "" {
		apply.Pictures = kit.UrlDecode(params.Pictures)
	}

	apply.ResType = params.ResType
	if params.Operationer == "" {
		apply.OperationUser = "美团客人"
	} else {
		apply.OperationUser = params.Operationer
	}
	apply.OperationType = params.ApplyType
	apply.OrderFrom = 2
	apply.OldRefundSn = params.RefundId
	apply.ChannelId = 2
	apply.ActivityPtAmount = float32(params.ActivityPtAmount)
	//部分退还是整单退，1整单 2部分
	if params.RefunType == 2 {
		//读取该订单的退款单 以及正向订单
		var (
			orderProducts       []*models.OrderProduct       //记录正向单的sku 以及金额
			orderRefundProducts []*models.RefundOrderProduct //记录逆向单的sku 以及金额
		)
		orderProductMap := make(map[string]int32)             //正向单sku=>金额
		orderRefundProductMap := make(map[string]int32)       //逆向单sku=>金额
		orderProductNumberMap := make(map[string]int32)       //正向单sku=>数量
		orderRefundProductNumberMap := make(map[string]int32) //逆向单sku=>数量
		var refundGoodsId []string
		for _, item := range params.RefundGoodsOrders {
			refundGoodsId = append(refundGoodsId, item.GoodsId)
		}
		strRefundGoodsId := strings.Join(refundGoodsId, "','")
		session := GetDBConn()
		err = session.
			SQL("SELECT a.sku_id,sum(a.payment_total) payment_total,sum(a.number) number "+
				"FROM order_product a "+
				"JOIN order_main b on a.order_sn = b.order_sn "+
				"WHERE b.old_order_sn = ? AND a.sku_id IN( '"+strRefundGoodsId+"' )"+
				"GROUP by a.sku_id ", params.OrderId).
			Find(&orderProducts)
		if err != nil {
			glog.Error("MtRefundOrderApply 查询原单商品失败：", err)
		}

		if len(orderProducts) > 0 {
			for _, v := range orderProducts {
				orderProductMap[v.SkuId] = v.PaymentTotal
				orderProductNumberMap[v.SkuId] = v.Number
			}
		}
		//查询商品已退款单的退款商品以及金额
		err = session.
			SQL("SELECT a.sku_id,a.refund_amount,a.quantity "+
				"FROM refund_order_product a "+
				"JOIN refund_order b ON a.refund_sn = b.refund_sn "+
				"WHERE b.old_order_sn = ? AND b.refund_state NOT IN(2,8,9) "+
				"AND a.sku_id IN('"+strRefundGoodsId+"' )", params.OrderId).
			Find(&orderRefundProducts)
		if err != nil {
			glog.Error("MtRefundOrderApply 查询已退款商品失败：", err)
		}

		if len(orderRefundProducts) > 0 {
			for _, v := range orderRefundProducts {
				refundAmountRecord := int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount)))
				if _, ok := orderRefundProductMap[v.SkuId]; ok {
					orderRefundProductMap[v.SkuId] += refundAmountRecord
					orderRefundProductNumberMap[v.SkuId] += v.Quantity
				} else {
					orderRefundProductMap[v.SkuId] = refundAmountRecord
					orderRefundProductNumberMap[v.SkuId] = v.Quantity
				}
			}
		}

		//记录同一个sku 一次退款存在多条记录的星狂 因为正向订单合并了这种单个商品多个价格的情况
		//但是退款单美团因为是不同的价格 那么这个商品还是有两条记录
		sameSkuNumberMap := make(map[string]int32, 0)
		sameSkuAmountMap := make(map[string]int32, 0)
		for _, item := range params.RefundGoodsOrders {
			var refundOrderGood oc.RefundOrderGoodsData
			refundOrderGood.SkuId = item.GoodsId
			refundOrderGood.Quantity = item.Quantity
			floatAmount := kit.FenToYuan(kit.YuanToFen(item.RefundPrice) * int(item.Quantity))

			//如果一个商品退完时 因为美团没有说商品的退款金额 只给了退款单价，所以因为价格的不精确导致此处需要解决两个问题
			//1：单个商品退款总金额超了 2：总金额不足  这两个问题都会导致后续推送与计算的问题

			//该sku是否退款金额 大于 正向单的
			intRefundPrice := cast.ToInt32(kit.YuanToFen(floatAmount))
			intRefundNumber := item.Quantity
			hasRefundAmount, ok := orderRefundProductMap[item.GoodsId]
			if ok {
				intRefundPrice += hasRefundAmount
				intRefundNumber += orderRefundProductNumberMap[item.GoodsId]
			}

			//一个订单 多个相同sku
			if _, okq := sameSkuNumberMap[item.GoodsId]; okq {
				//已退款的数量需要加上本订单该sku的另外退款记录的数量
				intRefundNumber += sameSkuNumberMap[item.GoodsId]
				intRefundPrice += sameSkuAmountMap[item.GoodsId]
				hasRefundAmount += sameSkuAmountMap[item.GoodsId]
			}

			if totalAmount, cok := orderProductMap[item.GoodsId]; cok {
				//单一sku如果本次退完的情况 超过了原单的金额或者少于原单金额 进行正向单金额-过往退款金额进行修正。在没退完的情况下使用美团给出的单价*数量算金额
				//否则下游推送会被阻止  他们验证了单一sku退款的总金额是否超过了正向单的金额
				lastDiffAmount := totalAmount - hasRefundAmount
				if lastDiffAmount < 0 {
					lastDiffAmount = 0
				}

				if intRefundPrice > totalAmount { //超了
					floatAmount = kit.FenToYuan(lastDiffAmount)
				}
				/*else if intRefundPrice < totalAmount { //少了
					totalNumber, _ := orderProductNumberMap[item.GoodsId]
					//通过数量判断该商品是否本次退完了 且总金额比正向单的少
					if totalNumber > 0 && intRefundNumber >= totalNumber {
						floatAmount = kit.FenToYuan(lastDiffAmount)
					}
				}*/
			}

			intAmount := int32(kit.YuanToFen(floatAmount))
			if _, okq := sameSkuNumberMap[item.GoodsId]; okq {
				//已退款的数量需要加上本订单该sku的另外退款记录的数量
				sameSkuNumberMap[item.GoodsId] += item.Quantity
				sameSkuAmountMap[item.GoodsId] += intAmount
			} else {
				sameSkuNumberMap[item.GoodsId] = item.Quantity
				sameSkuAmountMap[item.GoodsId] = intAmount
			}

			refundPrice := cast.ToString(floatAmount)
			refundOrderGood.RefundAmount = refundPrice
			refundOrderGood.GoodsName = item.FoodName
			refundOrderGood.Spec = item.Spec
			refundOrderGood.RefundPrice = item.FoodPrice
			refundOrderGood.RefundRealityPrice = float32(item.RefundPrice)
			apply.RefundOrderGoodsData = append(apply.RefundOrderGoodsData, &refundOrderGood)
		}
	}

	r := RefundOrderService{}
	res, err := r.RefundOrderApply(nil, apply)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = res.Message
		glog.Error("external_ui售后单申请:", err)
		return out
	}

	out.Code = res.Code
	out.Message = res.Message
	out.Error = res.Error
	return out
}

// mt售后单应答
func MtRefundOrderAnswer(params *oc.OrderRetrunRequest) *oc.BaseResponse {
	out := new(oc.BaseResponse)

	answer := &oc.RefundOrderAnswerRequest{
		ExternalOrderId:  params.OrderId,
		RefundOrderSn:    params.RefundId,
		Reason:           params.Reason,
		ResultType:       1,
		ResultTypeNote:   params.ResType,
		OperationType:    params.ResType,
		OperationUser:    params.Operationer,
		ActivityPtAmount: params.ActivityPtAmount,
	}
	if params.NotifyType == "reject" {
		answer.ResultType = 2
	}

	r := RefundOrderService{}
	res, err := r.RefundOrderAnswer(nil, answer)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = res.Message
		glog.Error("external_ui售后单应答:", err)
		return out
	}

	glog.Info(params.OrderId, ", ", params.RefundId, ", 售后单应答结果：", kit.JsonEncode(res))
	out.Code = res.Code
	out.Message = res.Message
	return out
}

// mt售后单撤销
func MtRefundOrderCancel(params *oc.OrderRetrunRequest) *oc.BaseResponse {
	out := new(oc.BaseResponse)
	cancel := new(oc.RefundOrderCancelRequest)
	cancel.RefundOrderSn = params.RefundId
	cancel.ResType = params.ResType
	cancel.OperationType = params.ResType
	cancel.OperationUser = params.Operationer
	cancel.Reason = params.Reason

	r := RefundOrderService{}
	res, err := r.RefundOrderCancel(nil, cancel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = res.Message
		glog.Error("MtRefundOrderCancel:"+params.RefundId, err)
		return out
	}
	out.Code = res.Code
	out.Message = res.Message
	out.Error = res.Error
	return out
}

// mt售后单更新物流信息
func MtExpressInfoUpdate(params *oc.OrderRetrunRequest) *oc.BaseResponse {
	out := &oc.BaseResponse{Code: 400}
	prefix := "美团退货物流信息更新，"
	db := GetDBConn()

	reOrder := models.RefundOrder{}
	hasReOrder, _ := db.Where("order_sn=? and refund_sn=?", params.OrderId, params.RefundId).Get(&reOrder)
	if !hasReOrder {
		out.Code = 400
		out.Error = "售后单不存在"
		return out
	}

	//解析出params.LogisticsInfo中的expressCompany、expressNumber、reason的值
	var logisticsInfo dto.LogisticsInfo
	err := json.Unmarshal([]byte(params.LogisticsInfo), &logisticsInfo)
	if err != nil {
		out.Error = err.Error()
		return out
	}

	model := new(models.RefundOrderLogistics)
	has, err := db.Where("refund_sn = ?", params.RefundId).Get(model)
	if err != nil {
		glog.Error(prefix, "订单id:"+params.OrderId+",查询错误"+err.Error())
		out.Error = err.Error()
		return out
	}
	if has {
		model.ExpressNo = logisticsInfo.ExpressNumber
		model.UpdateTime = time.Now()
		_, err = db.Where("refund_sn = ?", params.RefundId).Update(model)
		if err != nil {
			glog.Error(prefix, params.RefundId+"更新错误:"+err.Error())
			out.Error = err.Error()
			return out
		}
	} else {
		model.ExpressNo = logisticsInfo.ExpressNumber
		model.RefundSn = params.RefundId
		model.CreateTime = time.Now()

		num, err := db.Insert(model)
		if err != nil {
			glog.Error(params.OrderId + "退货物流信息新增错误:" + err.Error())
			out.Error = err.Error()
			return out
		}

		if num == 0 {
			glog.Error(params.RefundId + "退货物流信息新增0条:")
			out.Error = "退货物流信息新增0条"
			return out
		}

		if hasReOrder {
			reOrderLog := models.RefundOrderLog{
				RefundSn:      reOrder.RefundSn,
				OldOrderSn:    reOrder.OldOrderSn,
				Ctime:         time.Now(),
				Money:         cast.ToInt(reOrder.RefundAmount),
				OperationType: "用户填写退货物流单号信息",
				NotifyType:    "returnExpress",
				ResType:       "用户填写退货物流单号信息",
				Operationer:   logisticsInfo.ExpressNumber,
			}
			_, err = db.Insert(reOrderLog)
			if err != nil {
				glog.Error(params.OrderId + "退货物流信息插入退货日志异常:" + err.Error())
				out.Error = "退货物流信息插入退货日志异常" + err.Error()
				return out
			}
		}
	}

	if hasReOrder {
		reOrder.ExpressName = logisticsInfo.ExpressCompany
		reOrder.ExpressNum = logisticsInfo.ExpressNumber
		db.Where("id=?", reOrder.Id).Update(reOrder)
	}
	out.Code = 200
	out.Message = "更新成功"
	return out
}

syntax = "proto3";

package mk;

import "mk/model.proto";

message promotionTaskQueryRequest {
    // 类型 0 全部 1 满减 2 限时折扣
    int32 types=1;
    // 创建用户Id
    string createUserId=2;

    // 页索引
    int32 pageIndex=3;
    // 页大小
    int32 pageSize=4;
    //主体Id
    int64  orgId=5;
}

message promotionTaskDetailQueryRequest {
    // 任务Id
    int32 taskId=1;
     // 页索引
     int32 pageIndex=3;
     // 页大小
     int32 pageSize=4;
}

message promotionTaskQueryResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message = 2;
    // 数据
    repeated promotionTaskDto data=3;
    // 总条数
    int64 total=4;
}

// 根据Id查询任务信息
message promotionTaskByIdQueryResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message = 2;
    // 数据
    promotionTaskDto data=3;
}

// 查询明细
message promotionTaskDetailQueryResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message = 2;
    // 总记录条数
    int32 total=4;
    // 任务列表
    repeated promotionTaskDetailDto data=3;

}
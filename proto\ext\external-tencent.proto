syntax = "proto3";
package ext;

//美团门店类API
service TencentService {
  //推送订单到腾讯有数[开发之前请添加数据仓库，dataSourceType = 0]
  rpc AddOrdersToTencent (AddOrdersToTencentReq) returns (BaseResponse);
  //上报微信小程序页面访问数据
  rpc PushVisitPage(PushVisitPageReq) returns (PushVisitPageRes);
  //推送退款订单到腾讯有数
  rpc AddReturnOrderToTencent (AddReturnOrderToTencentReq) returns (BaseResponse);
  //报时效要求：
  //每天 6:00 前完成前一天的数据上报。
  //推送#汇总订单接口
  rpc PushOrderSum(PushOrderSumReq) returns (BaseResponse);
  //推送订单状态变更
  //订单同步的时效性要求是5分钟
  rpc PushOrderStatus(PushOrderStatusReq) returns (BaseResponse);
}

message PushOrderStatusReq{
  //数据源id（create方法返回的data.dataSource.id）
  string    dataSourceId = 1;
  repeated  OrderStatusInfo orders = 2;
}
message OrderStatusInfo {
  //商家订单号
  string  external_order_id = 1;
  //主订单状态，1150已支付待发货，1160已发货，1180销售完成/已收货，1280退款中，1290退货完成
  string  order_status = 2;
  //状态变更时间，unix毫秒级时间，如 order_status状态为 1150 ，则传 1150（已支付待发货）状态变更的时间
  string  status_change_time = 3;
}
message PushOrderSumReq{
  //数据源id（create方法返回的data.dataSource.id）
  string    dataSourceId = 1;
  repeated  OrdersSum orders = 2;
}

message OrdersSum{
  //日期，unix时间戳，字段长度为13字节
  string  ref_date = 1;
  //该日期的下单金额之和
  float  give_order_amount_sum = 2;
  //该日期的下单数量之和
  int32  give_order_num_sum = 3;
  //该日期的支付金额之和
  float  payment_amount_sum = 4;
  //该日期的支付数量之和
  int32  payed_num_sum = 5;
}

message PushVisitPageReq{
  //开始日期。格式为 yyyymmdd
  string    begin_date = 1;
  //结束日期，限定查询1天数据，允许设置的最大值为昨日。格式为 yyyymmdd
  string    end_date = 2;
}

message PushVisitPageRes{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message AddOrdersToTencentReq {
  //数据源id（create方法返回的data.dataSource.id）
  string    dataSourceId = 1;
  repeated  TencentOrders orders = 2;
}

//通用返回
message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message TencentOrders {
  //商家订单号
  string  external_order_id = 1;
  //订单创建时间，unix时间戳 字段长度为 13 字节
  string  create_time = 2;
  //订单来源,枚举值:商家小程序：wxapp；商家app：app；商家H5：mobileweb；商家pcweb：pcweb；线下人工pos：offstore_pos_manual；线下自助收银：offstore_pos_self_help；其他：other
  string  order_source = 3;
  //订单类型；1：普通订单；2：充值订单；3：消费订单；普通购买商品订单传 1 即可
  int32   order_type = 4;
  //订单品牌 id
  string  brand_id = 5;
  //订单品牌名称
  string  brand_name = 6;
  //订单商品总数量
  int64   goods_num_total = 7;
  //订单商品总重量，默认单位为克
  float   goods_weight = 8;
  //商品总金额，单位默认为元 注：已含单品级别优惠的商品金额，如单品直降
  float   goods_amount_total = 9;
  //订单运费，单位默认为元 注：运费为0时，传0.00
  float   freight_amount = 10;
  //订单金额，单位默认为元 注：商品总金额+运费金额=订单金额
  float   order_amount = 11;
  //订单应付金额，单位默认为元 注：订单金额-订单级别的优惠金额（如：订单满减）=订单应付金额
  float   payable_amount = 12;
  //实付金额，单位默认为元 注：订单应付金额-支付优惠金额（如：微信支付优惠、招商银行优惠等）=订单实付金额
  float   payment_amount = 13;
  //主订单状态，1110待支付，1150已支付待发货，1160已发货，1180销售完成/已收货，1280退款中，1290退货完成
  string  order_status = 14;
  //状态变更时间，unix毫秒级时间，如 order_status状态为 1150 ，则传 1150（已支付待发货）状态变更的时间
  string  status_change_time = 15;
  //用户信息，json格式
  TenUserInfo user_info = 16;
  //主订单商品信息，数组类型，每个sku存一个数组单位
  repeated TenGoodsInfo goods_info = 17;
  //主订单用到的券信息，数组类型
  repeated TenCouponInfo coupon_info = 18;
  //主订单每种支付方式的支付信息,order_status = 1110时 payment_info非必填，其他状态码必填
  repeated TenPaymentInfo payment_info = 19;
  //快递信息
  TenExpressInfo express_info = 20;
  //发票信息，类型为数组
  repeated TenInvoiceInfo invoice_info = 21;
  //订单赠送总积分
  float points_total = 22;
  //订单详情页信息，CPS业务跳转使用
  TenTargetUrl target_url = 23;
  //商家标记订单已删除，0-未删除，1-已删除
  int32 is_deleted = 24;
}

message TenTargetUrl {
  //微信小程序落地页url，当落地页为微信小程序时必填
  string  url_miniprogram = 1;
  //微信小程序appid，当落地页为微信小程序时必填
  string  miniprogram_appid = 2;
  //小程序原始ID，登录小程序管理后台-设置-基本设置-帐号信息中，gh_xx，当落地页为微信小程序时必填
  string  miniprogram_username = 3;
  //qq小程序落地页url，当落地页为QQ小程序时必填
  string  url_miniprogram_qq = 4;
  //qq小程序appid，当落地页为QQ小程序时必填
  string  miniprogram_appid_qq = 5;
  //h5落地页url
  string  url_h5 = 6;
}

message TenInvoiceInfo {
  //是否需要发票，true代表需要，false代表不需要
  bool  if_need_invoice = 1;
  //发票类型，枚举值，取值如下： 1000(增值税专用发票) 1001(普通发票) 1002(机动车专用发票) 1003(机打发票) 1004(定额发票 ) 1005(剪开式发票) 1006（其他）
  string  invoice_type = 2;
  //发票抬头
  string  invoice_title = 3;
  //发票内容
  string  invoice_content = 4;
  //发票附加信息
  string  invoice_addition_info = 5;
  //公司名称
  string  invoice_company = 6;
  //纳税人识别号
  string  invoice_taxpayer = 7;
  //注册地址
  string  registry_address = 8;
  //注册电话
  string  registry_phone = 9;
  //开户银行
  string  registry_bank_name = 10;
  //开户账号
  string  registry_bank_account = 11;
  //发票收件地址
  string  invoice_delivery_address = 12;
  //发票收件人姓名
  string  invoice_delivery_name = 13;
  //发票收件人电话
  string  invoice_delivery_phone = 14;
  //发票号码
  string  invoice_num = 15;
}

message TenExpressInfo {
  //订单物流状态
  string  logistics_status = 1;
  //商品总重量，单位默认为克
  float   goods_total_weight = 2;
  //收件人姓名
  string  receiver_name = 3;
  //收件人联系电话
  string  receiver_phone = 4;
  //收件人地址
  string  receiver_address = 5;
  //国家编码
  string  receiver_country_code = 6;
  //省份编码
  string  receiver_province_code = 7;
  //城市编码
  string  receiver_city_code = 8;
  //区编码
  string  receiver_district_code = 9;
  //期望送货时间段，格式为“起始时间-结束时间”，如"9:00-12:00"
  string  expected_delivery_time = 10;
  //期望送货日期，格式“YYYYMMDD”
  string  expected_delivery_date = 11;
  //包裹信息，object类型
  repeated TenExpressPackageInfo express_package_info = 12;
}

message TenExpressPackageInfo {
  //物流公司编码，枚举类型，枚举值请参见文章后面的“物流商 code”
  string  express_company_code = 1;
  //物流公司名称
  string  express_company_name = 2;
  //运单号
  string  express_code = 3;
  //发货时间，格式为时间戳 字段长度为 13 字节
  string  ship_time = 4;
  //运费跳转页面，json字符串
  TenExpressPage express_page = 5;
  //物流包裹信息
  repeated TenExpressPackInfo express_package_info = 6;
}

message TenExpressPackInfo {
  //商品sku id
  string  external_sku_id = 1;
  //商品数量
  int64   number = 2;
}

message TenExpressPage {
  //快递详情页跳转链接（小程序页面，小程序填此字段）
  string  miniprogram_path = 1;
  //小程序APPID，填写了miniprogram_path需填此字段
  string  miniprogram_appid = 2;
  //快递详情页跳转链接（h5页面，公众号填此字段）
  string  miniprogram_h5 = 3;
}

message TenPaymentInfo {
  //支付方式，见<枚举列表>页
  string  payment_type = 1;
  //微信支付订单ID/流水号
  string  trans_id = 2;
  //金额，单位默认为元
  float   trans_amount = 3;
}

message TenCouponInfo {
  //卡券类型；1：商家券；2：微信支付券
  int32   coupon_type = 1;
  //该类券优惠金额总额，单位默认为元
  float   coupon_amount_total = 2;
  //该类券的细节券信息
  repeated TenCouponDetail coupon_detail = 3;
}

message TenCouponDetail {
  //券 id
  string  external_coupon_id = 1;
  //券批次 id（该字段需要在优惠券接口中添加卡券批次）
  string  coupon_batch_id = 2;
  //券名称
  string  coupon_name = 3;
  //该张券优惠金额，单位默认为元
  float coupon_amount = 4;
}

message TenGoodsInfo {
  //sku编号
  string  external_sku_id = 1;
  //商品主图
  string  primary_image_url = 2;
  //sku 名称
  string  sku_name_chinese = 3;
  //单件商品原价，单位默认为元
  float   goods_amount = 4;
  //多件商品实付金额（分摊了优惠的金额）,单位默认为元
  float   payment_amount = 5;
  //是否赠品，0代表非赠品，1代表赠品
  int32   is_gift = 6;
  //sku 所属 spu 编号，若无 spu，传输内容请与 external_sku_id 保持一致
  string  external_spu_id = 7;
  //spu 名称，若无 spu，传输内容请与 sku_name_chinese 保持一致
  string  spu_name_chinese = 8;
  //商品售卖单位
  string  sale_unit = 9;
  //末级类目 id
  string  category_id = 10;
  //末级类目名称
  string  category_name = 11;
  //商品数量
  int64   goods_num = 12;
  //商品重量，单位默认为克
  float   goods_weight = 13;
  //主订单销售门店信息
  TenStoreInfo  store_info = 14;
  //主订单来源渠道，数组类型
  repeated TenChanInfo chan_info = 15;
  //佣金，json字符串
  repeated TenCommissionInfo commission_info = 16;
  //第三方推广信息，数组类型，CPS业务必传
  repeated TenThirdPromotionInfo third_promotion_info = 17;
}

message TenThirdPromotionInfo {
  //是否计佣；1：是；0：否
  int32   is_calculated = 1;
  //不计佣原因
  string  no_calculated_reason = 2;
  //佣金比例，2位小数，不带百分号，如 10% 填10.00，CPS业务必传
  float   commission_rate = 3;
  //佣金金额（实际计佣金额*佣金比例/100），单位元，2位小数，CPS业务必传
  float   commission_fee = 4;
  //实际计算佣金的商品金额，不包括运费、优惠券等的金额，单位元，2位小数，CPS业务必传
  float   actual_commission_amount = 5;
  //商品是否已结算；1：是；0：否
  int32   is_settle = 6;
  //结算时间，unix时间戳 字段长度为 13 字节
  string  settle_time = 7;
}

message TenCommissionInfo {
  //佣金类型，枚举值如下：1：按比例提成；2：按金额提成
  int32   commission_type = 1;
  //佣金金额，单位元
  float   commission_fee = 2;
}

message TenChanInfo {
  //小程序渠道
  TenChanWxapp chan_wxapp = 1;
  //自定义渠道
  TenChanCustom chan_custom = 2;
  //智慧零售入口小程序必传，来源小程序或公众号appid
  string  chan_refer_app_id = 3;
  //智慧零售入口小程序必传，引流渠道编码
  string  chan_id = 4;
  //腾讯CPS追踪参数，CPS业务必填
  string  tx_cps_id = 5;
}

message TenChanCustom {
  //自定义渠道的标识符，是自定义渠道的最小粒度
  string  chan_custom_id = 1;
  //自定义渠道的描述
  string  chan_custom_id_desc = 2;
  //3级自定义渠道的标识符，3级是针对4级的分类，要求4级数据必须存在
  string  chan_custom_cat_3 = 3;
  //3级自定义渠道的描述，若chan_custom_cat_3存在则必须存在
  string  chan_custom_cat_3_desc = 4;
  //2级自定义渠道的标识符，2级是针对3级的分类，要求3、4级数据必须存在
  string  chan_custom_cat_2 = 5;
  //2级自定义渠道的描述，若chan_custom_cat_2存在则必须存在
  string  chan_custom_cat_2_desc = 6;
  //1级自定义渠道的标识符，1级是针对2级的分类，要求2、3、4级数据必须存在
  string  chan_custom_cat_1 = 7;
  //1级自定义渠道的描述，若chan_custom_cat_1存在则必须存在
  string  chan_custom_cat_1_desc = 8;
}

message TenChanWxapp {
  //小程序场景值
  string  chan_scene = 1;
}

message TenStoreInfo {
  //主订单销售门店id
  string  external_store_id = 1;
  //主订单销售门店名称
  string  store_name = 2;
  //主订单销售门店所属城市
  string  store_city = 3;
}

message TenUserInfo {
  //下单人 open_id，order_source = wxapp时，必填
  string  open_id = 1;
  //小程序或公众号的appid
  string  app_id = 2;
  //下单人 union_id
  string  union_id = 3;
  //下单人手机号
  string  user_phone = 4;
  //下单人用户 id
  string  user_id = 5;
  //下单人会员号
  string  member_id = 6;
  //下单人在KA注册后首次下单时间，格式为UNIX时间戳 字段长度为 13 字节
  string  user_first_order_time = 7;
}

message AddReturnOrderToTencentReq {
  //数据源id（create方法返回的data.dataSource.id）
  string    dataSourceId = 1;
  repeated  TencentReturnOrders orders = 2;
}

message TencentReturnOrders {
  //商家退货退款单号
  string  external_return_order_id = 1;
  //关联的订单号
  string external_order_id = 2;
  //退货退款单创建时间，unix时间戳 字段长度为 13 字节
  string  return_create_time = 3;
  //该笔退货退款单的商品数量
  int64   return_num = 4;
  //该笔退货退款单的商品退款金额，单位元，2位小数
  float   return_amount = 5;
  //运费退款金额，单位元，2位小数注：运费为0时，传0
  float   return_freight_amount = 6;
  //该笔退货退款单的退款金额，单位元，2位小数注：商品退款金额+运费退款金额=订单金额
  float   return_order_amount = 7;
  //退货退款单状态，传1290（退货退款完成）
  string  return_order_status = 8;
  //状态变更时间，unix毫秒级时间
  string  status_change_time = 9;
  //主订单商品信息，数组类型，每个sku存一个数组单位
  repeated ReturnGoodsInfo return_goods_info = 10;
}

message ReturnGoodsInfo {
  //sku 编号
  string  external_sku_id = 1;
  //sku 名称
  string  sku_name_chinese = 2;
  //是否赠品，0代表非赠品，1代表赠品
  int64   is_gift = 3;
  //sku 所属 spu 编号，若无 spu，传输内容请与 external_sku_id 保持一致
  string  external_spu_id = 4;
  //spu 名称，若无 spu，传输内容请与 sku_name_chinese 保持一致
  string  spu_name_chinese = 5;
  //退货商品数量
  int64   return_goods_num = 6;
  //退货商品金额，单位元，两位小数
  float   return_goods_amount = 7;
}
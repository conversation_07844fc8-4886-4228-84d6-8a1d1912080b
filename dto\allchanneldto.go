package dto

//订单同步 emall.order.synchronize
type Orders struct {
	Orders []OrderParam `json:"orders"`
}

type ReOrders struct {
	Orders  []ReOrdersDet `json:"orders"`
	Code    int32         `json:"code"`
	Message string        `json:"message"`
}

type ReOrdersDet struct {
	Tid      string `json:"tid"`
	Billcode string `json:"billcode"`
	Message  string `json:"message"`
	Status   string `json:"status"`
}

type OrderParam struct {
	//网店订单编号
	Tid string `json:"tid,omitempty"`
	//重量
	Weight string `json:"weight,omitempty"`
	//尺寸
	Size string `json:"size,omitempty"`
	//买家账号
	Buyernick string `json:"buyernick,omitempty"`
	//卖家留言
	Buyermessage string `json:"buyermessage,omitempty"`
	//卖家备注 可以用于设置对应仓库关系
	Sellermemo string `json:"sellermemo,omitempty"`
	//订单总金额
	Total string `json:"total,omitempty"`
	//订单享受优惠的金额
	Privilege string `json:"privilege,omitempty"`
	//运费
	Postfee string `json:"postfee,omitempty"`
	//收货人名称
	Receivername string `json:"receivername,omitempty"`
	//收货省
	Receiverstate string `json:"receiverstate,omitempty"`
	//收货市
	Receivercity string `json:"receivercity,omitempty"`
	//收货区
	Receiverdistrict string `json:"receiverdistrict,omitempty"`
	//收货地址
	Receiveraddress string `json:"receiveraddress,omitempty"`
	//收货电话
	Receiverphone string `json:"receiverphone,omitempty"`
	//收货人手机号
	Receivermobile string `json:"receivermobile,omitempty"`
	//
	Receiverzip string `json:"receiverzip,omitempty"`
	//订单创建时间
	Created string `json:"created,omitempty"`
	//订单状态 NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
	Status string `json:"status,omitempty"`
	//订单类型（Cod=货到付款, NoCod=非货到付款）
	Type string `json:"type,omitempty"`
	//发票抬头
	Invoicename string `json:"invoicename,omitempty"`
	//卖家旗帜（数值型）
	Sellerflag string `json:"sellerflag,omitempty"`
	//付款时间（时间格式：yyyy-MM-dd HH:mm:ss）
	Paytime string `json:"paytime,omitempty"`
	//物流公司编码
	Logistbtypecode []string `json:"logistbtypecode,omitempty"`
	//往来单位编码
	Btypecode string `json:"btypecode,omitempty"`
	//订单商品信息
	Details []OrderDetailsParam `json:"details,omitempty"`
}

//订单详情信息
type OrderDetailsParam struct {
	//网店订单明细编号
	Oid string `json:"oid,omitempty"`
	//商品条码
	Barcode string `json:"barcode,omitempty"`
	//网店商品ID
	Eshopgoodsid string `json:"eshopgoodsid,omitempty"`
	//网店商家编码
	Outeriid string `json:"outeriid,omitempty"`
	//网店商品名称
	Eshopgoodsname string `json:"eshopgoodsname,omitempty"`
	//网店商品SKU ID
	Eshopskuid string `json:"eshopskuid,omitempty"`
	//网店商品SKU名称
	Eshopskuname string `json:"eshopskuname,omitempty"`
	//商品ID
	Numiid string `json:"numiid,omitempty"`
	//规格ID
	Skuid string `json:"skuid,omitempty"`
	//基本单位数量（不能为0）
	Num string `json:"num,omitempty"`
	//商品总额
	Payment string `json:"payment,omitempty"`
	//商品图片路径
	Picpath string `json:"picpath,omitempty"`
	//重量
	Weight string `json:"weight,omitempty"`
	//尺寸，体积
	Size string `json:"size,omitempty"`
	//销售单位ID
	Unitid string `json:"unitid,omitempty"`
	//销售单位数量
	Unitqty string `json:"unitqty,omitempty"`
}

//emall.afterorder.synchronize  售后订单同步
type AfterorderOrder struct {
	Orders []AfterorderOrderInfo `json:"orders"`
}

type AfterorderOrderInfo struct {
	Rtid           string                       `json:"rtid,omitempty"`
	Tid            string                       `json:"tid,omitempty"`
	Total          string                       `json:"total,omitempty"`
	Privilege      string                       `json:"privilege,omitempty"`
	Postfee        string                       `json:"postfee,omitempty"`
	Created        string                       `json:"created,omitempty"`
	Status         string                       `json:"status,omitempty"`
	Aftsaletype    string                       `json:"aftsaletype,omitempty"`
	Reasoncode     string                       `json:"reasoncode,omitempty"`
	Logistbillcode string                       `json:"logistbillcode,omitempty"`
	Aftsaleremark  string                       `json:"aftsaleremark,omitempty"`
	Details        []AfterorderOrderInfoDetails `json:"details,omitempty"`
}

type AfterorderOrderInfoDetails struct {
	Oid            string `json:"oid,omitempty"`
	Eshopgoodsname string `json:"eshopgoodsname,omitempty"`
	Eshopskuname   string `json:"eshopskuname,omitempty"`
	Backqty        string `json:"backqty,omitempty"`
	Backtotal      string `json:"backtotal,omitempty"`
	Outeriid       string `json:"outeriid,omitempty"`
}

//emall.orderstatus.synchronize  订单状态同步
type OrderStatusSynchronize struct {
	Orders []OrderStatusInfo `json:"orders"`
}

type OrderStatusInfo struct {
	Tid          string `json:"tid,omitempty"`
	Status       string `json:"status,omitempty"`
	Refundstatus string `json:"refundstatus,omitempty"`
}

//售后订单同步  响应参数
type RetAfterorder struct {
	Orders  []RetAfterorderInfo `json:"orders,omitempty"`
	Code    int                 `json:"code,omitempty"`
	Message string              `json:"message,omitempty"`
}

type RetAfterorderInfo struct {
	Iserror  bool   `json:"iserror,omitempty"`
	Tid      string `json:"tid,omitempty"`
	Billcode string `json:"billcode,omitempty"`
	Message  string `json:"message,omitempty"`
}

type AfterorderStatus struct {
	Tid    string `json:"tid,omitempty"`
	Status string `json:"status,omitempty"`
}

syntax = "proto3";

option java_multiple_files = true;
option java_package = "io.grpc.recommend";
option java_outer_classname = "RecommendProto";
option objc_class_prefix = "RCMD";

package recommend;

// The greeting service definition.
service RecommendTips {
  // Sends a greeting
  rpc getRmdtips (TipRequest) returns (RcmdTipsReply) {}
}
// The request message containing the user's name.
message TipRequest {
  string userid=1;
  int32 size=2;
}
// The response message containing the greetings
message RcmdTipsReply {
  string message = 1;
  string host=2;
  int32 returncode=3;
  string tips=4;
}

// The greeting service definition.
service RecommendProds {
  // Sends a greeting
  rpc getRcmdProds (ProdRequest) returns (RcmdProdsReply) {}
}

message ProdRequest {
  string userid=1;
  string chainid=2;
  int32 size=3;
  
}

// The response message containing the greetings
message RcmdProdsReply {
  string message = 1;
  string host=2;
  int32 returncode=3;
  string prods=4;
}


package ctc

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	// 通用基础服务
	RPC       ContentCenterServiceClient
	CensusRPC CensusServiceClient
	BiliAdRPC BiliAdServiceClient
	DigitRPC  DigitalServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

func GetContentCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.content-center")
	}

	if url == "" {
		url = "127.0.0.1:7085"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("content-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewContentCenterServiceClient(grpcClient.Conn)
		grpcClient.CensusRPC = NewCensusServiceClient(grpcClient.Conn)
		grpcClient.BiliAdRPC = NewBiliAdServiceClient(grpcClient.Conn)
		grpcClient.DigitRPC = NewDigitalServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}

syntax = "proto3";

package sh;

service VoucherService{
  // 收回用户优惠券
  rpc RecoverUserVoucher(RecoverUserVoucherRequest) returns (Response);
  // 根据优惠券模板id查找已核销的用户手机号
  rpc FindUsedVoucherUserMobile(FindUsedVoucherUserMobileRequest) returns (FindUsedVoucherUserMobileResponse);
  //发商城优惠券
  rpc IssueAwardByMallCoupon(IssueAwardByMallCouponRequest) returns (IssueAwardByMallCouponResponse);
}
message IssueAwardByMallCouponRequest {
  //用户id
  string user_id = 1;
  //商城券id ,号分割的字符串
  string coupon_id = 2;
}
message  IssueAwardByMallCouponResponse{
  int64 code = 1;
  string message = 2;
  repeated IssueAwardCoupon data = 3;
}

message IssueAwardCoupon{
  string coupon_code = 2;
  int64 coupon_end_time = 3;
}


message Response {
  int64 code = 1;
  string message = 2;
}

message RecoverUserVoucher {
  string scrm_user_id = 1;
  repeated int64 voucher_t_ids = 2;
}

message RecoverUserVoucherRequest {
  int64 timestemp = 1;
  string sign = 2;
  repeated RecoverUserVoucher user_voucher = 3;
}

message FindUsedVoucherUserMobileRequest {
  repeated int64 voucher_t_ids = 1;
}

message UsedVoucherUserMobile {
  int64 voucher_t_id = 1;
  repeated string mobiles = 2;
}

message FindUsedVoucherUserMobileResponse {
  int64 code = 1;
  string message = 2;
  repeated UsedVoucherUserMobile list = 3;
}
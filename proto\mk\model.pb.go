// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/model.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 星期定义
type WeekDay int32

const (
	WeekDay_Sunday    WeekDay = 0
	WeekDay_Monday    WeekDay = 1
	WeekDay_Tuesday   WeekDay = 2
	WeekDay_Wednesday WeekDay = 3
	WeekDay_Thursday  WeekDay = 4
	WeekDay_Friday    WeekDay = 5
	WeekDay_Saturday  WeekDay = 6
)

var WeekDay_name = map[int32]string{
	0: "Sunday",
	1: "Monday",
	2: "Tuesday",
	3: "Wednesday",
	4: "Thursday",
	5: "Friday",
	6: "Saturday",
}

var WeekDay_value = map[string]int32{
	"Sunday":    0,
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
}

func (x WeekDay) String() string {
	return proto.EnumName(WeekDay_name, int32(x))
}

func (WeekDay) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{0}
}

// 查询状态
type PromotionState int32

const (
	PromotionState_zero     PromotionState = 0
	PromotionState_progress PromotionState = 1
	PromotionState_wait     PromotionState = 2
	PromotionState_finished PromotionState = 3
)

var PromotionState_name = map[int32]string{
	0: "zero",
	1: "progress",
	2: "wait",
	3: "finished",
}

var PromotionState_value = map[string]int32{
	"zero":     0,
	"progress": 1,
	"wait":     2,
	"finished": 3,
}

func (x PromotionState) String() string {
	return proto.EnumName(PromotionState_name, int32(x))
}

func (PromotionState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{1}
}

// 渠道类型
type ChannelType int32

const (
	//不限制渠道
	ChannelType_NotLimit ChannelType = 0
	//阿闻管家
	ChannelType_Upet ChannelType = 1
	// 美团
	ChannelType_Meituan ChannelType = 2
	// 饿了么
	ChannelType_Ele ChannelType = 3
	// 京东到家
	ChannelType_JdDaojia ChannelType = 4
)

var ChannelType_name = map[int32]string{
	0: "NotLimit",
	1: "Upet",
	2: "Meituan",
	3: "Ele",
	4: "JdDaojia",
}

var ChannelType_value = map[string]int32{
	"NotLimit": 0,
	"Upet":     1,
	"Meituan":  2,
	"Ele":      3,
	"JdDaojia": 4,
}

func (x ChannelType) String() string {
	return proto.EnumName(ChannelType_name, int32(x))
}

func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{2}
}

// 响应代码 0 代表成功，其他代表错误
type Code int32

const (
	Code_default Code = 0
	// 成功
	Code_success Code = 200
	// 服务端异常
	Code_serverException Code = 400
	// 当前用户没有权限
	Code_userNotAuthority Code = 201
	// grpc链接异常
	Code_grpcConnectionError Code = 404
	// 参数错误
	Code_parameterError Code = 403
	// 业务错误
	Code_businessError Code = 300
	// 保存数据库错误
	Code_saveDbException Code = 401
	//查询数据库错误
	Code_queryDbException Code = 402
)

var Code_name = map[int32]string{
	0:   "default",
	200: "success",
	400: "serverException",
	201: "userNotAuthority",
	404: "grpcConnectionError",
	403: "parameterError",
	300: "businessError",
	401: "saveDbException",
	402: "queryDbException",
}

var Code_value = map[string]int32{
	"default":             0,
	"success":             200,
	"serverException":     400,
	"userNotAuthority":    201,
	"grpcConnectionError": 404,
	"parameterError":      403,
	"businessError":       300,
	"saveDbException":     401,
	"queryDbException":    402,
}

func (x Code) String() string {
	return proto.EnumName(Code_name, int32(x))
}

func (Code) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{3}
}

// 促销活动类型
type PromotionTypes int32

const (
	// 默认
	PromotionTypes_defaulttypes PromotionTypes = 0
	// 满减活动
	PromotionTypes_reachReduce PromotionTypes = 1
	// 限时折扣
	PromotionTypes_timeDiscount PromotionTypes = 2
	// 满减运费
	PromotionTypes_reachReduceDelivery PromotionTypes = 3
	// 社区团购
	PromotionTypes_pickup PromotionTypes = 10
	// 拼团活动
	PromotionTypes_group PromotionTypes = 11
	// 赠险
	PromotionTypes_insurance PromotionTypes = 12
)

var PromotionTypes_name = map[int32]string{
	0:  "defaulttypes",
	1:  "reachReduce",
	2:  "timeDiscount",
	3:  "reachReduceDelivery",
	10: "pickup",
	11: "group",
	12: "insurance",
}

var PromotionTypes_value = map[string]int32{
	"defaulttypes":        0,
	"reachReduce":         1,
	"timeDiscount":        2,
	"reachReduceDelivery": 3,
	"pickup":              10,
	"group":               11,
	"insurance":           12,
}

func (x PromotionTypes) String() string {
	return proto.EnumName(PromotionTypes_name, int32(x))
}

func (PromotionTypes) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{4}
}

// 新增活动实体dto
type PromotionDto struct {
	// 活动名称
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	// 类型 1 满减活动 2限时折扣 3 满减运费
	Types PromotionTypes `protobuf:"varint,7,opt,name=types,proto3,enum=mk.PromotionTypes" json:"types"`
	// 是否启用
	IsEnable bool `protobuf:"varint,2,opt,name=isEnable,proto3" json:"isEnable"`
	//是否应用全部商品
	IsAllProduct bool `protobuf:"varint,3,opt,name=isAllProduct,proto3" json:"isAllProduct"`
	// 开始日期
	BeginDate string `protobuf:"bytes,4,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束日期
	EndDate string `protobuf:"bytes,5,opt,name=endDate,proto3" json:"endDate"`
	// 活动状态 0 未知 1 进行中 2 待生效 3 已结束 4 冻结中 -- 查询的时候使用
	State PromotionState `protobuf:"varint,6,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 循环周期 0周日 1-6分别表示周一到周六
	WeekDays             []int32  `protobuf:"varint,8,rep,packed,name=weekDays,proto3" json:"weekDays"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionDto) Reset()         { *m = PromotionDto{} }
func (m *PromotionDto) String() string { return proto.CompactTextString(m) }
func (*PromotionDto) ProtoMessage()    {}
func (*PromotionDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{0}
}

func (m *PromotionDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionDto.Unmarshal(m, b)
}
func (m *PromotionDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionDto.Marshal(b, m, deterministic)
}
func (m *PromotionDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDto.Merge(m, src)
}
func (m *PromotionDto) XXX_Size() int {
	return xxx_messageInfo_PromotionDto.Size(m)
}
func (m *PromotionDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDto proto.InternalMessageInfo

func (m *PromotionDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PromotionDto) GetTypes() PromotionTypes {
	if m != nil {
		return m.Types
	}
	return PromotionTypes_defaulttypes
}

func (m *PromotionDto) GetIsEnable() bool {
	if m != nil {
		return m.IsEnable
	}
	return false
}

func (m *PromotionDto) GetIsAllProduct() bool {
	if m != nil {
		return m.IsAllProduct
	}
	return false
}

func (m *PromotionDto) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *PromotionDto) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *PromotionDto) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *PromotionDto) GetWeekDays() []int32 {
	if m != nil {
		return m.WeekDays
	}
	return nil
}

// 列表查询dto
type PromotionListDto struct {
	// id 更新时使用
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 类型 1 满减活动
	Types PromotionTypes `protobuf:"varint,5,opt,name=types,proto3,enum=mk.PromotionTypes" json:"types"`
	// 开始日期
	BeginDate string `protobuf:"bytes,3,opt,name=beginDate,proto3" json:"beginDate"`
	// 截止日期
	EndDate string `protobuf:"bytes,4,opt,name=endDate,proto3" json:"endDate"`
	// 概述
	Summary string `protobuf:"bytes,7,opt,name=summary,proto3" json:"summary"`
	// 活动状态 0 未知 1 进行中 2 待生效 3 已结束 4 冻结中
	State PromotionState `protobuf:"varint,6,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 循环周期 0周日 1-6分别表示周一到周六
	WeekDays             []int32  `protobuf:"varint,8,rep,packed,name=weekDays,proto3" json:"weekDays"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionListDto) Reset()         { *m = PromotionListDto{} }
func (m *PromotionListDto) String() string { return proto.CompactTextString(m) }
func (*PromotionListDto) ProtoMessage()    {}
func (*PromotionListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{1}
}

func (m *PromotionListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionListDto.Unmarshal(m, b)
}
func (m *PromotionListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionListDto.Marshal(b, m, deterministic)
}
func (m *PromotionListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionListDto.Merge(m, src)
}
func (m *PromotionListDto) XXX_Size() int {
	return xxx_messageInfo_PromotionListDto.Size(m)
}
func (m *PromotionListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionListDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionListDto proto.InternalMessageInfo

func (m *PromotionListDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionListDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PromotionListDto) GetTypes() PromotionTypes {
	if m != nil {
		return m.Types
	}
	return PromotionTypes_defaulttypes
}

func (m *PromotionListDto) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *PromotionListDto) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *PromotionListDto) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *PromotionListDto) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *PromotionListDto) GetWeekDays() []int32 {
	if m != nil {
		return m.WeekDays
	}
	return nil
}

// 活动使用的渠道dto
type PromotionChannelDto struct {
	// id 更新时使用
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 渠道类型 0 不限制 1 阿闻管家 2 美团 3 饿了么 3 京东到家
	ChannelId            int32    `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionChannelDto) Reset()         { *m = PromotionChannelDto{} }
func (m *PromotionChannelDto) String() string { return proto.CompactTextString(m) }
func (*PromotionChannelDto) ProtoMessage()    {}
func (*PromotionChannelDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{2}
}

func (m *PromotionChannelDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionChannelDto.Unmarshal(m, b)
}
func (m *PromotionChannelDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionChannelDto.Marshal(b, m, deterministic)
}
func (m *PromotionChannelDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionChannelDto.Merge(m, src)
}
func (m *PromotionChannelDto) XXX_Size() int {
	return xxx_messageInfo_PromotionChannelDto.Size(m)
}
func (m *PromotionChannelDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionChannelDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionChannelDto proto.InternalMessageInfo

func (m *PromotionChannelDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionChannelDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionChannelDto) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 活动涉及的商品dto
type PromotionProductDto struct {
	// id 更新时使用
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 商品spuId
	SpuId int32 `protobuf:"varint,5,opt,name=spuId,proto3" json:"spuId"`
	// 商品货号
	ProductSkuId int32 `protobuf:"varint,2,opt,name=productSkuId,proto3" json:"productSkuId"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	// 是否参与活动 true 参与 false 不参与
	IsSelected bool `protobuf:"varint,4,opt,name=isSelected,proto3" json:"isSelected"`
	// 商品价格
	Price                int32    `protobuf:"varint,6,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionProductDto) Reset()         { *m = PromotionProductDto{} }
func (m *PromotionProductDto) String() string { return proto.CompactTextString(m) }
func (*PromotionProductDto) ProtoMessage()    {}
func (*PromotionProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{3}
}

func (m *PromotionProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionProductDto.Unmarshal(m, b)
}
func (m *PromotionProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionProductDto.Marshal(b, m, deterministic)
}
func (m *PromotionProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionProductDto.Merge(m, src)
}
func (m *PromotionProductDto) XXX_Size() int {
	return xxx_messageInfo_PromotionProductDto.Size(m)
}
func (m *PromotionProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionProductDto proto.InternalMessageInfo

func (m *PromotionProductDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionProductDto) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *PromotionProductDto) GetProductSkuId() int32 {
	if m != nil {
		return m.ProductSkuId
	}
	return 0
}

func (m *PromotionProductDto) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PromotionProductDto) GetIsSelected() bool {
	if m != nil {
		return m.IsSelected
	}
	return false
}

func (m *PromotionProductDto) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 活动享受的满减优惠dto
type PromotionReduceDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 最小金额
	ReachMoney float64 `protobuf:"fixed64,16,opt,name=reachMoney,proto3" json:"reachMoney"`
	// 减免金额
	ReduceMoney          float64  `protobuf:"fixed64,17,opt,name=reduceMoney,proto3" json:"reduceMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionReduceDto) Reset()         { *m = PromotionReduceDto{} }
func (m *PromotionReduceDto) String() string { return proto.CompactTextString(m) }
func (*PromotionReduceDto) ProtoMessage()    {}
func (*PromotionReduceDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{4}
}

func (m *PromotionReduceDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionReduceDto.Unmarshal(m, b)
}
func (m *PromotionReduceDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionReduceDto.Marshal(b, m, deterministic)
}
func (m *PromotionReduceDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionReduceDto.Merge(m, src)
}
func (m *PromotionReduceDto) XXX_Size() int {
	return xxx_messageInfo_PromotionReduceDto.Size(m)
}
func (m *PromotionReduceDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionReduceDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionReduceDto proto.InternalMessageInfo

func (m *PromotionReduceDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionReduceDto) GetReachMoney() float64 {
	if m != nil {
		return m.ReachMoney
	}
	return 0
}

func (m *PromotionReduceDto) GetReduceMoney() float64 {
	if m != nil {
		return m.ReduceMoney
	}
	return 0
}

// 参与活动的店铺dto
type PromotionShopDto struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//店铺代码
	ShopId string `protobuf:"bytes,10,opt,name=shopId,proto3" json:"shopId"`
	//店铺名称
	ShopName             string   `protobuf:"bytes,11,opt,name=shopName,proto3" json:"shopName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionShopDto) Reset()         { *m = PromotionShopDto{} }
func (m *PromotionShopDto) String() string { return proto.CompactTextString(m) }
func (*PromotionShopDto) ProtoMessage()    {}
func (*PromotionShopDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{5}
}

func (m *PromotionShopDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionShopDto.Unmarshal(m, b)
}
func (m *PromotionShopDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionShopDto.Marshal(b, m, deterministic)
}
func (m *PromotionShopDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionShopDto.Merge(m, src)
}
func (m *PromotionShopDto) XXX_Size() int {
	return xxx_messageInfo_PromotionShopDto.Size(m)
}
func (m *PromotionShopDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionShopDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionShopDto proto.InternalMessageInfo

func (m *PromotionShopDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionShopDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionShopDto) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

// 参与周期dto
type PromotionWeekDayDto struct {
	// 开始时间
	BeginTime string `protobuf:"bytes,10,opt,name=beginTime,proto3" json:"beginTime"`
	// 结束时间
	EndTime              string   `protobuf:"bytes,11,opt,name=endTime,proto3" json:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionWeekDayDto) Reset()         { *m = PromotionWeekDayDto{} }
func (m *PromotionWeekDayDto) String() string { return proto.CompactTextString(m) }
func (*PromotionWeekDayDto) ProtoMessage()    {}
func (*PromotionWeekDayDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{6}
}

func (m *PromotionWeekDayDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionWeekDayDto.Unmarshal(m, b)
}
func (m *PromotionWeekDayDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionWeekDayDto.Marshal(b, m, deterministic)
}
func (m *PromotionWeekDayDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionWeekDayDto.Merge(m, src)
}
func (m *PromotionWeekDayDto) XXX_Size() int {
	return xxx_messageInfo_PromotionWeekDayDto.Size(m)
}
func (m *PromotionWeekDayDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionWeekDayDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionWeekDayDto proto.InternalMessageInfo

func (m *PromotionWeekDayDto) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PromotionWeekDayDto) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

// 限时折扣配置
type PromotionTimeDiscountDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,7,opt,name=promotionId,proto3" json:"promotionId"`
	// 用户类型 0 全部 1 新客户
	UserType int32 `protobuf:"varint,2,opt,name=UserType,proto3" json:"UserType"`
	// 折扣类型  0 按折扣 固定活动价格
	DisountType int32 `protobuf:"varint,3,opt,name=DisountType,proto3" json:"DisountType"`
	// 为 0 时代表折扣 为 1 代表固定价格 (统一传浮点数)
	DiscountValue float64 `protobuf:"fixed64,4,opt,name=DiscountValue,proto3" json:"DiscountValue"`
	// 单限购 0 不限制, 非0  限制多少数量
	LimitCountByOrder int32 `protobuf:"varint,5,opt,name=LimitCountByOrder,proto3" json:"LimitCountByOrder"`
	// 当日限购 0 不限制,非0 限时库存数量
	LimitCountByStock int32 `protobuf:"varint,6,opt,name=LimitCountByStock,proto3" json:"LimitCountByStock"`
	// 配置的活动购买数量
	ConfigBuyCount int32 `protobuf:"varint,8,opt,name=ConfigBuyCount,proto3" json:"ConfigBuyCount"`
	// 会员额外折扣
	VipDiscount          float64  `protobuf:"fixed64,9,opt,name=VipDiscount,proto3" json:"VipDiscount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTimeDiscountDto) Reset()         { *m = PromotionTimeDiscountDto{} }
func (m *PromotionTimeDiscountDto) String() string { return proto.CompactTextString(m) }
func (*PromotionTimeDiscountDto) ProtoMessage()    {}
func (*PromotionTimeDiscountDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{7}
}

func (m *PromotionTimeDiscountDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTimeDiscountDto.Unmarshal(m, b)
}
func (m *PromotionTimeDiscountDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTimeDiscountDto.Marshal(b, m, deterministic)
}
func (m *PromotionTimeDiscountDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTimeDiscountDto.Merge(m, src)
}
func (m *PromotionTimeDiscountDto) XXX_Size() int {
	return xxx_messageInfo_PromotionTimeDiscountDto.Size(m)
}
func (m *PromotionTimeDiscountDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTimeDiscountDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTimeDiscountDto proto.InternalMessageInfo

func (m *PromotionTimeDiscountDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetUserType() int32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetDisountType() int32 {
	if m != nil {
		return m.DisountType
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetDiscountValue() float64 {
	if m != nil {
		return m.DiscountValue
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetLimitCountByOrder() int32 {
	if m != nil {
		return m.LimitCountByOrder
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetLimitCountByStock() int32 {
	if m != nil {
		return m.LimitCountByStock
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetConfigBuyCount() int32 {
	if m != nil {
		return m.ConfigBuyCount
	}
	return 0
}

func (m *PromotionTimeDiscountDto) GetVipDiscount() float64 {
	if m != nil {
		return m.VipDiscount
	}
	return 0
}

// 满减运费
type PromotionReduceDeliveryDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,5,opt,name=promotionId,proto3" json:"promotionId"`
	// 0 普通阶梯递减 1 最高阶梯免配送费
	ReduceDeliveryType int32 `protobuf:"varint,2,opt,name=ReduceDeliveryType,proto3" json:"ReduceDeliveryType"`
	// 满足减免最小金额
	ReachMoney float64 `protobuf:"fixed64,3,opt,name=ReachMoney,proto3" json:"ReachMoney"`
	// 减免金额
	ReduceMoney          float64  `protobuf:"fixed64,4,opt,name=ReduceMoney,proto3" json:"ReduceMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionReduceDeliveryDto) Reset()         { *m = PromotionReduceDeliveryDto{} }
func (m *PromotionReduceDeliveryDto) String() string { return proto.CompactTextString(m) }
func (*PromotionReduceDeliveryDto) ProtoMessage()    {}
func (*PromotionReduceDeliveryDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{8}
}

func (m *PromotionReduceDeliveryDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionReduceDeliveryDto.Unmarshal(m, b)
}
func (m *PromotionReduceDeliveryDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionReduceDeliveryDto.Marshal(b, m, deterministic)
}
func (m *PromotionReduceDeliveryDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionReduceDeliveryDto.Merge(m, src)
}
func (m *PromotionReduceDeliveryDto) XXX_Size() int {
	return xxx_messageInfo_PromotionReduceDeliveryDto.Size(m)
}
func (m *PromotionReduceDeliveryDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionReduceDeliveryDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionReduceDeliveryDto proto.InternalMessageInfo

func (m *PromotionReduceDeliveryDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionReduceDeliveryDto) GetReduceDeliveryType() int32 {
	if m != nil {
		return m.ReduceDeliveryType
	}
	return 0
}

func (m *PromotionReduceDeliveryDto) GetReachMoney() float64 {
	if m != nil {
		return m.ReachMoney
	}
	return 0
}

func (m *PromotionReduceDeliveryDto) GetReduceMoney() float64 {
	if m != nil {
		return m.ReduceMoney
	}
	return 0
}

// 促销活动配置dto
type PromotionShopConfigDto struct {
	// 单店每单可购买限时折扣最大sku数量
	TimeDiscount_MaxSkuCountByOrder int32    `protobuf:"varint,1,opt,name=timeDiscount_MaxSkuCountByOrder,json=timeDiscountMaxSkuCountByOrder,proto3" json:"timeDiscount_MaxSkuCountByOrder"`
	XXX_NoUnkeyedLiteral            struct{} `json:"-"`
	XXX_unrecognized                []byte   `json:"-"`
	XXX_sizecache                   int32    `json:"-"`
}

func (m *PromotionShopConfigDto) Reset()         { *m = PromotionShopConfigDto{} }
func (m *PromotionShopConfigDto) String() string { return proto.CompactTextString(m) }
func (*PromotionShopConfigDto) ProtoMessage()    {}
func (*PromotionShopConfigDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{9}
}

func (m *PromotionShopConfigDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionShopConfigDto.Unmarshal(m, b)
}
func (m *PromotionShopConfigDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionShopConfigDto.Marshal(b, m, deterministic)
}
func (m *PromotionShopConfigDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionShopConfigDto.Merge(m, src)
}
func (m *PromotionShopConfigDto) XXX_Size() int {
	return xxx_messageInfo_PromotionShopConfigDto.Size(m)
}
func (m *PromotionShopConfigDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionShopConfigDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionShopConfigDto proto.InternalMessageInfo

func (m *PromotionShopConfigDto) GetTimeDiscount_MaxSkuCountByOrder() int32 {
	if m != nil {
		return m.TimeDiscount_MaxSkuCountByOrder
	}
	return 0
}

// 优惠计算信息dto
type PromotionCalcDto struct {
	// 类型 1满减、2限时折扣、4会员价
	PromotionType int32 `protobuf:"varint,1,opt,name=promotionType,proto3" json:"promotionType"`
	// 促销活动
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 名称
	PromotionTitle string `protobuf:"bytes,3,opt,name=promotionTitle,proto3" json:"promotionTitle"`
	// 金额
	PromotionFee         int32    `protobuf:"varint,4,opt,name=promotionFee,proto3" json:"promotionFee"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionCalcDto) Reset()         { *m = PromotionCalcDto{} }
func (m *PromotionCalcDto) String() string { return proto.CompactTextString(m) }
func (*PromotionCalcDto) ProtoMessage()    {}
func (*PromotionCalcDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{10}
}

func (m *PromotionCalcDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionCalcDto.Unmarshal(m, b)
}
func (m *PromotionCalcDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionCalcDto.Marshal(b, m, deterministic)
}
func (m *PromotionCalcDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionCalcDto.Merge(m, src)
}
func (m *PromotionCalcDto) XXX_Size() int {
	return xxx_messageInfo_PromotionCalcDto.Size(m)
}
func (m *PromotionCalcDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionCalcDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionCalcDto proto.InternalMessageInfo

func (m *PromotionCalcDto) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *PromotionCalcDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionCalcDto) GetPromotionTitle() string {
	if m != nil {
		return m.PromotionTitle
	}
	return ""
}

func (m *PromotionCalcDto) GetPromotionFee() int32 {
	if m != nil {
		return m.PromotionFee
	}
	return 0
}

type PromotionVipDiscountDto struct {
	// 折扣或价格
	DiscountValue        int32    `protobuf:"varint,2,opt,name=discountValue,proto3" json:"discountValue"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionVipDiscountDto) Reset()         { *m = PromotionVipDiscountDto{} }
func (m *PromotionVipDiscountDto) String() string { return proto.CompactTextString(m) }
func (*PromotionVipDiscountDto) ProtoMessage()    {}
func (*PromotionVipDiscountDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{11}
}

func (m *PromotionVipDiscountDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionVipDiscountDto.Unmarshal(m, b)
}
func (m *PromotionVipDiscountDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionVipDiscountDto.Marshal(b, m, deterministic)
}
func (m *PromotionVipDiscountDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionVipDiscountDto.Merge(m, src)
}
func (m *PromotionVipDiscountDto) XXX_Size() int {
	return xxx_messageInfo_PromotionVipDiscountDto.Size(m)
}
func (m *PromotionVipDiscountDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionVipDiscountDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionVipDiscountDto proto.InternalMessageInfo

func (m *PromotionVipDiscountDto) GetDiscountValue() int32 {
	if m != nil {
		return m.DiscountValue
	}
	return 0
}

//促销活动与SKU关联关系
type PromotionSkuDto struct {
	// 类型 1 满减 2 限时折扣 3 满减运费 4会员折扣
	Types int32 `protobuf:"varint,1,opt,name=types,proto3" json:"types"`
	// 促销活动名称
	PromotionName string `protobuf:"bytes,2,opt,name=promotionName,proto3" json:"promotionName"`
	// sku信息
	SkuId string `protobuf:"bytes,3,opt,name=skuId,proto3" json:"skuId"`
	// 促销活动明细信息
	ReduceList []*PromotionReduceDto `protobuf:"bytes,4,rep,name=reduceList,proto3" json:"reduceList"`
	// 限时折扣
	TimeDiscount *PromotionSkuTimeDiscountDto `protobuf:"bytes,6,opt,name=timeDiscount,proto3" json:"timeDiscount"`
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,7,opt,name=promotionId,proto3" json:"promotionId"`
	// 会员折扣
	VipDiscount *PromotionVipDiscountDto `protobuf:"bytes,9,opt,name=vipDiscount,proto3" json:"vipDiscount"`
	//商品快照信息
	ProductSnapshotJson  string   `protobuf:"bytes,8,opt,name=ProductSnapshotJson,proto3" json:"ProductSnapshotJson"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSkuDto) Reset()         { *m = PromotionSkuDto{} }
func (m *PromotionSkuDto) String() string { return proto.CompactTextString(m) }
func (*PromotionSkuDto) ProtoMessage()    {}
func (*PromotionSkuDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{12}
}

func (m *PromotionSkuDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSkuDto.Unmarshal(m, b)
}
func (m *PromotionSkuDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSkuDto.Marshal(b, m, deterministic)
}
func (m *PromotionSkuDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSkuDto.Merge(m, src)
}
func (m *PromotionSkuDto) XXX_Size() int {
	return xxx_messageInfo_PromotionSkuDto.Size(m)
}
func (m *PromotionSkuDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSkuDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSkuDto proto.InternalMessageInfo

func (m *PromotionSkuDto) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionSkuDto) GetPromotionName() string {
	if m != nil {
		return m.PromotionName
	}
	return ""
}

func (m *PromotionSkuDto) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *PromotionSkuDto) GetReduceList() []*PromotionReduceDto {
	if m != nil {
		return m.ReduceList
	}
	return nil
}

func (m *PromotionSkuDto) GetTimeDiscount() *PromotionSkuTimeDiscountDto {
	if m != nil {
		return m.TimeDiscount
	}
	return nil
}

func (m *PromotionSkuDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionSkuDto) GetVipDiscount() *PromotionVipDiscountDto {
	if m != nil {
		return m.VipDiscount
	}
	return nil
}

func (m *PromotionSkuDto) GetProductSnapshotJson() string {
	if m != nil {
		return m.ProductSnapshotJson
	}
	return ""
}

//限时折扣
type PromotionSkuTimeDiscountDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// 截止日期
	EndDate string `protobuf:"bytes,2,opt,name=endDate,proto3" json:"endDate"`
	// 折扣类型  0 按折扣 固定活动价格
	DisountType int32 `protobuf:"varint,3,opt,name=disountType,proto3" json:"disountType"`
	// 为 0 时代表折扣 为 1 代表固定价格
	DiscountValue int32 `protobuf:"varint,4,opt,name=discountValue,proto3" json:"discountValue"`
	// 单限购 0 不限制, 非0  限制多少数量
	LimitCountByOrder int32 `protobuf:"varint,5,opt,name=limitCountByOrder,proto3" json:"limitCountByOrder"`
	// 当日限购 0 不限制,非0 限时库存数量
	LimitCountByStock int32 `protobuf:"varint,6,opt,name=limitCountByStock,proto3" json:"limitCountByStock"`
	// 付费vip额外折扣
	VipDiscount          int32    `protobuf:"varint,7,opt,name=vipDiscount,proto3" json:"vipDiscount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSkuTimeDiscountDto) Reset()         { *m = PromotionSkuTimeDiscountDto{} }
func (m *PromotionSkuTimeDiscountDto) String() string { return proto.CompactTextString(m) }
func (*PromotionSkuTimeDiscountDto) ProtoMessage()    {}
func (*PromotionSkuTimeDiscountDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{13}
}

func (m *PromotionSkuTimeDiscountDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSkuTimeDiscountDto.Unmarshal(m, b)
}
func (m *PromotionSkuTimeDiscountDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSkuTimeDiscountDto.Marshal(b, m, deterministic)
}
func (m *PromotionSkuTimeDiscountDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSkuTimeDiscountDto.Merge(m, src)
}
func (m *PromotionSkuTimeDiscountDto) XXX_Size() int {
	return xxx_messageInfo_PromotionSkuTimeDiscountDto.Size(m)
}
func (m *PromotionSkuTimeDiscountDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSkuTimeDiscountDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSkuTimeDiscountDto proto.InternalMessageInfo

func (m *PromotionSkuTimeDiscountDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionSkuTimeDiscountDto) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *PromotionSkuTimeDiscountDto) GetDisountType() int32 {
	if m != nil {
		return m.DisountType
	}
	return 0
}

func (m *PromotionSkuTimeDiscountDto) GetDiscountValue() int32 {
	if m != nil {
		return m.DiscountValue
	}
	return 0
}

func (m *PromotionSkuTimeDiscountDto) GetLimitCountByOrder() int32 {
	if m != nil {
		return m.LimitCountByOrder
	}
	return 0
}

func (m *PromotionSkuTimeDiscountDto) GetLimitCountByStock() int32 {
	if m != nil {
		return m.LimitCountByStock
	}
	return 0
}

func (m *PromotionSkuTimeDiscountDto) GetVipDiscount() int32 {
	if m != nil {
		return m.VipDiscount
	}
	return 0
}

// 根据店铺查询取消活动Dto
type PromotionQueryByShopIdDto struct {
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 类型 1 满减 2 限时折扣 3 满减运费
	Types int32 `protobuf:"varint,2,opt,name=types,proto3" json:"types"`
	// 促销活动明细信息
	ReduceList []*PromotionReduceDto `protobuf:"bytes,4,rep,name=reduceList,proto3" json:"reduceList"`
	// 满减运费
	ReduceDeliveryList []*PromotionReduceDeliveryDto `protobuf:"bytes,5,rep,name=reduceDeliveryList,proto3" json:"reduceDeliveryList"`
	// 限时折扣
	TimeDiscount         []*PromotionTimeDiscountDto `protobuf:"bytes,6,rep,name=timeDiscount,proto3" json:"timeDiscount"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PromotionQueryByShopIdDto) Reset()         { *m = PromotionQueryByShopIdDto{} }
func (m *PromotionQueryByShopIdDto) String() string { return proto.CompactTextString(m) }
func (*PromotionQueryByShopIdDto) ProtoMessage()    {}
func (*PromotionQueryByShopIdDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{14}
}

func (m *PromotionQueryByShopIdDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionQueryByShopIdDto.Unmarshal(m, b)
}
func (m *PromotionQueryByShopIdDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionQueryByShopIdDto.Marshal(b, m, deterministic)
}
func (m *PromotionQueryByShopIdDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQueryByShopIdDto.Merge(m, src)
}
func (m *PromotionQueryByShopIdDto) XXX_Size() int {
	return xxx_messageInfo_PromotionQueryByShopIdDto.Size(m)
}
func (m *PromotionQueryByShopIdDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQueryByShopIdDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQueryByShopIdDto proto.InternalMessageInfo

func (m *PromotionQueryByShopIdDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionQueryByShopIdDto) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionQueryByShopIdDto) GetReduceList() []*PromotionReduceDto {
	if m != nil {
		return m.ReduceList
	}
	return nil
}

func (m *PromotionQueryByShopIdDto) GetReduceDeliveryList() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.ReduceDeliveryList
	}
	return nil
}

func (m *PromotionQueryByShopIdDto) GetTimeDiscount() []*PromotionTimeDiscountDto {
	if m != nil {
		return m.TimeDiscount
	}
	return nil
}

// 计算减免金额请求的相关商品
type PromotionCalcProductDto struct {
	// skuId
	SkuId string `protobuf:"bytes,1,opt,name=skuId,proto3" json:"skuId"`
	// 总金额
	SumMoney int64 `protobuf:"varint,2,opt,name=sumMoney,proto3" json:"sumMoney"`
	// 单价
	Price int64 `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	// 数量
	Count int64 `protobuf:"varint,4,opt,name=count,proto3" json:"count"`
	// 参与折扣的数量
	DiscountCount int64 `protobuf:"varint,5,opt,name=discountCount,proto3" json:"discountCount"`
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,6,opt,name=promotionId,proto3" json:"promotionId"`
	// 折扣后商品的价格
	DiscountPrice int32 `protobuf:"varint,7,opt,name=discountPrice,proto3" json:"discountPrice"`
	// 仅vip优惠价格，用于超出限购恢复原价，同时用于标识会员价
	OnlyVipDiscountPrice int32 `protobuf:"varint,9,opt,name=onlyVipDiscountPrice,proto3" json:"onlyVipDiscountPrice"`
	// 促销活动Id
	PromotionType        int32    `protobuf:"varint,8,opt,name=promotionType,proto3" json:"promotionType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionCalcProductDto) Reset()         { *m = PromotionCalcProductDto{} }
func (m *PromotionCalcProductDto) String() string { return proto.CompactTextString(m) }
func (*PromotionCalcProductDto) ProtoMessage()    {}
func (*PromotionCalcProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{15}
}

func (m *PromotionCalcProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionCalcProductDto.Unmarshal(m, b)
}
func (m *PromotionCalcProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionCalcProductDto.Marshal(b, m, deterministic)
}
func (m *PromotionCalcProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionCalcProductDto.Merge(m, src)
}
func (m *PromotionCalcProductDto) XXX_Size() int {
	return xxx_messageInfo_PromotionCalcProductDto.Size(m)
}
func (m *PromotionCalcProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionCalcProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionCalcProductDto proto.InternalMessageInfo

func (m *PromotionCalcProductDto) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *PromotionCalcProductDto) GetSumMoney() int64 {
	if m != nil {
		return m.SumMoney
	}
	return 0
}

func (m *PromotionCalcProductDto) GetPrice() int64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PromotionCalcProductDto) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PromotionCalcProductDto) GetDiscountCount() int64 {
	if m != nil {
		return m.DiscountCount
	}
	return 0
}

func (m *PromotionCalcProductDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionCalcProductDto) GetDiscountPrice() int32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *PromotionCalcProductDto) GetOnlyVipDiscountPrice() int32 {
	if m != nil {
		return m.OnlyVipDiscountPrice
	}
	return 0
}

func (m *PromotionCalcProductDto) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

// 任务列表
type PromotionTaskDto struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//当前步骤 0 待开始  1 进行中 2 已完成
	Step int32 `protobuf:"varint,2,opt,name=step,proto3" json:"step"`
	// 成功数量
	Pass int32 `protobuf:"varint,3,opt,name=pass,proto3" json:"pass"`
	// 失败数量
	Fail int32 `protobuf:"varint,4,opt,name=fail,proto3" json:"fail"`
	// 类型 1 满减 2 限时折扣
	Types int32 `protobuf:"varint,8,opt,name=types,proto3" json:"types"`
	// 标题
	Title string `protobuf:"bytes,9,opt,name=title,proto3" json:"title"`
	// 结果详情excel链接
	ResultUrl string `protobuf:"bytes,10,opt,name=resultUrl,proto3" json:"resultUrl"`
	// 结果
	Result string `protobuf:"bytes,11,opt,name=result,proto3" json:"result"`
	// 创建用户Id
	CreateUserId string `protobuf:"bytes,5,opt,name=createUserId,proto3" json:"createUserId"`
	// 创建用户姓名
	CreateUserName string `protobuf:"bytes,6,opt,name=createUserName,proto3" json:"createUserName"`
	// 最后更新时间
	UpdateDate           string   `protobuf:"bytes,7,opt,name=updateDate,proto3" json:"updateDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTaskDto) Reset()         { *m = PromotionTaskDto{} }
func (m *PromotionTaskDto) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskDto) ProtoMessage()    {}
func (*PromotionTaskDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{16}
}

func (m *PromotionTaskDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskDto.Unmarshal(m, b)
}
func (m *PromotionTaskDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskDto.Marshal(b, m, deterministic)
}
func (m *PromotionTaskDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskDto.Merge(m, src)
}
func (m *PromotionTaskDto) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskDto.Size(m)
}
func (m *PromotionTaskDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskDto proto.InternalMessageInfo

func (m *PromotionTaskDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionTaskDto) GetStep() int32 {
	if m != nil {
		return m.Step
	}
	return 0
}

func (m *PromotionTaskDto) GetPass() int32 {
	if m != nil {
		return m.Pass
	}
	return 0
}

func (m *PromotionTaskDto) GetFail() int32 {
	if m != nil {
		return m.Fail
	}
	return 0
}

func (m *PromotionTaskDto) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionTaskDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PromotionTaskDto) GetResultUrl() string {
	if m != nil {
		return m.ResultUrl
	}
	return ""
}

func (m *PromotionTaskDto) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *PromotionTaskDto) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func (m *PromotionTaskDto) GetCreateUserName() string {
	if m != nil {
		return m.CreateUserName
	}
	return ""
}

func (m *PromotionTaskDto) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

// 任务明细Dto
type PromotionTaskDetailDto struct {
	// 任务Id
	TaskId int32 `protobuf:"varint,1,opt,name=taskId,proto3" json:"taskId"`
	// 活动Id
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 店铺Id
	ShopId string `protobuf:"bytes,3,opt,name=shopId,proto3" json:"shopId"`
	// 店铺名称
	ShopName string `protobuf:"bytes,4,opt,name=shopName,proto3" json:"shopName"`
	// skuId
	ProductSkuId string `protobuf:"bytes,5,opt,name=productSkuId,proto3" json:"productSkuId"`
	// 商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	// 代码 0 失败 1 成功
	Code int32 `protobuf:"varint,7,opt,name=code,proto3" json:"code"`
	// 执行结果信息
	Message string `protobuf:"bytes,8,opt,name=message,proto3" json:"message"`
	// 执行完成时间
	UpdateDate           string   `protobuf:"bytes,9,opt,name=updateDate,proto3" json:"updateDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTaskDetailDto) Reset()         { *m = PromotionTaskDetailDto{} }
func (m *PromotionTaskDetailDto) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskDetailDto) ProtoMessage()    {}
func (*PromotionTaskDetailDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{17}
}

func (m *PromotionTaskDetailDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskDetailDto.Unmarshal(m, b)
}
func (m *PromotionTaskDetailDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskDetailDto.Marshal(b, m, deterministic)
}
func (m *PromotionTaskDetailDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskDetailDto.Merge(m, src)
}
func (m *PromotionTaskDetailDto) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskDetailDto.Size(m)
}
func (m *PromotionTaskDetailDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskDetailDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskDetailDto proto.InternalMessageInfo

func (m *PromotionTaskDetailDto) GetTaskId() int32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *PromotionTaskDetailDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionTaskDetailDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionTaskDetailDto) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *PromotionTaskDetailDto) GetProductSkuId() string {
	if m != nil {
		return m.ProductSkuId
	}
	return ""
}

func (m *PromotionTaskDetailDto) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PromotionTaskDetailDto) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PromotionTaskDetailDto) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionTaskDetailDto) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

type PromotionTime struct {
	// 开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=beginTime,proto3" json:"beginTime"`
	// 结束时间
	EndTime              string   `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTime) Reset()         { *m = PromotionTime{} }
func (m *PromotionTime) String() string { return proto.CompactTextString(m) }
func (*PromotionTime) ProtoMessage()    {}
func (*PromotionTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{18}
}

func (m *PromotionTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTime.Unmarshal(m, b)
}
func (m *PromotionTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTime.Marshal(b, m, deterministic)
}
func (m *PromotionTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTime.Merge(m, src)
}
func (m *PromotionTime) XXX_Size() int {
	return xxx_messageInfo_PromotionTime.Size(m)
}
func (m *PromotionTime) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTime.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTime proto.InternalMessageInfo

func (m *PromotionTime) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PromotionTime) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type PromotionShop struct {
	// 财务编码
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 门店名称
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionShop) Reset()         { *m = PromotionShop{} }
func (m *PromotionShop) String() string { return proto.CompactTextString(m) }
func (*PromotionShop) ProtoMessage()    {}
func (*PromotionShop) Descriptor() ([]byte, []int) {
	return fileDescriptor_a39ad19e1b9234b3, []int{19}
}

func (m *PromotionShop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionShop.Unmarshal(m, b)
}
func (m *PromotionShop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionShop.Marshal(b, m, deterministic)
}
func (m *PromotionShop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionShop.Merge(m, src)
}
func (m *PromotionShop) XXX_Size() int {
	return xxx_messageInfo_PromotionShop.Size(m)
}
func (m *PromotionShop) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionShop.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionShop proto.InternalMessageInfo

func (m *PromotionShop) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PromotionShop) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func init() {
	proto.RegisterEnum("mk.WeekDay", WeekDay_name, WeekDay_value)
	proto.RegisterEnum("mk.PromotionState", PromotionState_name, PromotionState_value)
	proto.RegisterEnum("mk.ChannelType", ChannelType_name, ChannelType_value)
	proto.RegisterEnum("mk.Code", Code_name, Code_value)
	proto.RegisterEnum("mk.PromotionTypes", PromotionTypes_name, PromotionTypes_value)
	proto.RegisterType((*PromotionDto)(nil), "mk.promotionDto")
	proto.RegisterType((*PromotionListDto)(nil), "mk.promotionListDto")
	proto.RegisterType((*PromotionChannelDto)(nil), "mk.promotionChannelDto")
	proto.RegisterType((*PromotionProductDto)(nil), "mk.promotionProductDto")
	proto.RegisterType((*PromotionReduceDto)(nil), "mk.promotionReduceDto")
	proto.RegisterType((*PromotionShopDto)(nil), "mk.promotionShopDto")
	proto.RegisterType((*PromotionWeekDayDto)(nil), "mk.promotionWeekDayDto")
	proto.RegisterType((*PromotionTimeDiscountDto)(nil), "mk.promotionTimeDiscountDto")
	proto.RegisterType((*PromotionReduceDeliveryDto)(nil), "mk.promotionReduceDeliveryDto")
	proto.RegisterType((*PromotionShopConfigDto)(nil), "mk.promotionShopConfigDto")
	proto.RegisterType((*PromotionCalcDto)(nil), "mk.promotionCalcDto")
	proto.RegisterType((*PromotionVipDiscountDto)(nil), "mk.promotionVipDiscountDto")
	proto.RegisterType((*PromotionSkuDto)(nil), "mk.promotionSkuDto")
	proto.RegisterType((*PromotionSkuTimeDiscountDto)(nil), "mk.promotionSkuTimeDiscountDto")
	proto.RegisterType((*PromotionQueryByShopIdDto)(nil), "mk.promotionQueryByShopIdDto")
	proto.RegisterType((*PromotionCalcProductDto)(nil), "mk.promotionCalcProductDto")
	proto.RegisterType((*PromotionTaskDto)(nil), "mk.promotionTaskDto")
	proto.RegisterType((*PromotionTaskDetailDto)(nil), "mk.promotionTaskDetailDto")
	proto.RegisterType((*PromotionTime)(nil), "mk.PromotionTime")
	proto.RegisterType((*PromotionShop)(nil), "mk.PromotionShop")
}

func init() { proto.RegisterFile("mk/model.proto", fileDescriptor_a39ad19e1b9234b3) }

var fileDescriptor_a39ad19e1b9234b3 = []byte{
	// 1619 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xbf, 0x6f, 0xdc, 0xca,
	0x11, 0x16, 0xc9, 0xfb, 0x39, 0x27, 0xc9, 0xf4, 0xca, 0xb1, 0x2f, 0xb6, 0x21, 0x1f, 0x08, 0xc3,
	0x10, 0x84, 0x40, 0x09, 0x64, 0x20, 0x5d, 0x10, 0xdb, 0x92, 0x6c, 0xc8, 0xb0, 0x1c, 0x87, 0x27,
	0xdb, 0x65, 0xb0, 0x22, 0x57, 0x3a, 0xe6, 0x78, 0x24, 0xb3, 0xbb, 0x94, 0x7d, 0xa9, 0x53, 0xa6,
	0x48, 0xe2, 0x54, 0x69, 0xd2, 0xa5, 0x72, 0x91, 0x2e, 0x5d, 0xd2, 0x05, 0xc9, 0xdf, 0xf3, 0xba,
	0x57, 0x3d, 0xcc, 0x2e, 0x8f, 0xb7, 0x7b, 0x77, 0x92, 0x8d, 0x87, 0xd7, 0x71, 0xbe, 0x99, 0xdd,
	0x9d, 0x99, 0xfd, 0x76, 0x66, 0xee, 0x60, 0x73, 0x32, 0xfe, 0xe9, 0x24, 0x8f, 0x59, 0xba, 0x57,
	0xf0, 0x5c, 0xe6, 0xc4, 0x9d, 0x8c, 0x83, 0x3f, 0xba, 0xb0, 0x5e, 0xf0, 0x7c, 0x92, 0xcb, 0x24,
	0xcf, 0x0e, 0x65, 0x4e, 0x6e, 0x41, 0x53, 0x26, 0x32, 0x65, 0x7d, 0x67, 0xe0, 0xec, 0x74, 0x43,
	0x2d, 0x90, 0x1d, 0x68, 0xca, 0x69, 0xc1, 0x44, 0xbf, 0x3d, 0x70, 0x76, 0x36, 0xf7, 0xc9, 0xde,
	0x64, 0xbc, 0x57, 0x2f, 0x3b, 0x45, 0x4d, 0xa8, 0x0d, 0xc8, 0x5d, 0xe8, 0x24, 0xe2, 0x28, 0xa3,
	0x67, 0x29, 0xeb, 0xbb, 0x03, 0x67, 0xa7, 0x13, 0xd6, 0x32, 0x09, 0x60, 0x3d, 0x11, 0x4f, 0xd3,
	0xf4, 0x0d, 0xcf, 0xe3, 0x32, 0x92, 0x7d, 0x4f, 0xe9, 0x2d, 0x8c, 0xdc, 0x87, 0xee, 0x19, 0xbb,
	0x48, 0xb2, 0x43, 0x2a, 0x59, 0xbf, 0xa1, 0x7c, 0x98, 0x03, 0xa4, 0x0f, 0x6d, 0x96, 0xc5, 0x4a,
	0xd7, 0x54, 0xba, 0x99, 0x88, 0x1e, 0x0a, 0x89, 0x78, 0x6b, 0x85, 0x87, 0x43, 0xd4, 0x84, 0xda,
	0x00, 0x3d, 0xfc, 0xc0, 0xd8, 0xf8, 0x90, 0x4e, 0x45, 0xbf, 0x33, 0xf0, 0x76, 0x9a, 0x61, 0x2d,
	0x07, 0xdf, 0x3a, 0xe0, 0xd7, 0xab, 0x5e, 0x25, 0x42, 0x62, 0x4a, 0x36, 0xc1, 0x4d, 0x62, 0x95,
	0x8f, 0x66, 0xe8, 0x26, 0xf1, 0x3c, 0x45, 0xee, 0xca, 0x14, 0x35, 0xbf, 0x94, 0x22, 0x2b, 0x44,
	0xef, 0x9a, 0x10, 0x1b, 0x76, 0x88, 0x7d, 0x68, 0x8b, 0x72, 0x32, 0xa1, 0x7c, 0xaa, 0xae, 0xa1,
	0x1b, 0xce, 0xc4, 0x1f, 0x28, 0x78, 0x06, 0x5b, 0xf5, 0xa2, 0x83, 0x11, 0xcd, 0x32, 0x96, 0xae,
	0x0a, 0x7f, 0x00, 0xbd, 0xda, 0xec, 0x38, 0x56, 0x49, 0x68, 0x86, 0x26, 0x84, 0x01, 0x46, 0x7a,
	0xfd, 0x71, 0xac, 0x02, 0x6c, 0x86, 0x73, 0x20, 0xf8, 0x97, 0x63, 0x9c, 0x53, 0x5d, 0xfb, 0x15,
	0x69, 0x16, 0x45, 0x79, 0x1c, 0xab, 0x84, 0x36, 0x43, 0x2d, 0x20, 0x87, 0x0a, 0xbd, 0x66, 0x38,
	0x2e, 0xeb, 0xe3, 0x2d, 0xac, 0xf2, 0x10, 0xe5, 0xd7, 0x74, 0x32, 0x4b, 0xb1, 0x09, 0x91, 0x6d,
	0x80, 0x44, 0x0c, 0x59, 0xca, 0x22, 0xc9, 0x62, 0x95, 0xe7, 0x4e, 0x68, 0x20, 0x78, 0x76, 0xc1,
	0x93, 0x48, 0x27, 0xb4, 0x19, 0x6a, 0x21, 0xf8, 0x08, 0xa4, 0x76, 0x3c, 0x64, 0x71, 0x19, 0x31,
	0xf4, 0xfb, 0xcb, 0xf9, 0xd8, 0x06, 0xe0, 0x8c, 0x46, 0xa3, 0x93, 0x3c, 0x63, 0xd3, 0xbe, 0x3f,
	0x70, 0x76, 0x9c, 0xd0, 0x40, 0x70, 0x07, 0xae, 0xb6, 0xd3, 0x06, 0x37, 0x95, 0x81, 0x09, 0x05,
	0xef, 0x0c, 0x5a, 0x0e, 0x47, 0x79, 0xb1, 0x2a, 0x5f, 0xb7, 0xa1, 0x25, 0x46, 0x79, 0x71, 0x1c,
	0xf7, 0x41, 0x05, 0x5c, 0x49, 0x78, 0xe5, 0xf8, 0xa5, 0x52, 0xd1, 0x53, 0x9a, 0x5a, 0x0e, 0x4e,
	0x8c, 0xab, 0x78, 0xaf, 0x79, 0x80, 0x5b, 0xcf, 0x18, 0x7a, 0x9a, 0x4c, 0x58, 0xb5, 0xdb, 0x1c,
	0xa8, 0x18, 0xaa, 0x74, 0xbd, 0x9a, 0xa1, 0x28, 0x06, 0xff, 0x75, 0xa1, 0x3f, 0xe7, 0x7c, 0x32,
	0x61, 0x87, 0x89, 0x88, 0xf2, 0x32, 0x93, 0x2b, 0xf2, 0xd4, 0x5e, 0xce, 0xd3, 0x5d, 0xe8, 0xbc,
	0x15, 0x8c, 0xe3, 0x63, 0xa9, 0xd2, 0x58, 0xcb, 0xb8, 0xfa, 0x30, 0x11, 0xb8, 0x97, 0x52, 0x6b,
	0x56, 0x99, 0x10, 0x79, 0x08, 0x1b, 0xb3, 0xe3, 0xde, 0xd1, 0xb4, 0xd4, 0xcf, 0xc7, 0x09, 0x6d,
	0x90, 0xfc, 0x04, 0x6e, 0xbe, 0x4a, 0x26, 0x89, 0x3c, 0x40, 0xe8, 0xd9, 0xf4, 0x57, 0x3c, 0x66,
	0xbc, 0x62, 0xd8, 0xb2, 0x62, 0xd1, 0x7a, 0x28, 0xf3, 0x68, 0x5c, 0x71, 0x62, 0x59, 0x41, 0x1e,
	0xc1, 0xe6, 0x41, 0x9e, 0x9d, 0x27, 0x17, 0xcf, 0xca, 0xa9, 0x52, 0xf4, 0x3b, 0xca, 0x74, 0x01,
	0xc5, 0x58, 0xde, 0x25, 0xc5, 0xcc, 0xaf, 0x7e, 0x57, 0xdf, 0xb7, 0x01, 0x05, 0xff, 0x74, 0xe0,
	0xee, 0x22, 0xd5, 0x58, 0x9a, 0x5c, 0x32, 0x3e, 0x5d, 0x91, 0xca, 0xe6, 0x72, 0x2a, 0xf7, 0x80,
	0xd8, 0xcb, 0x8c, 0xa4, 0xae, 0xd0, 0x20, 0x45, 0xc3, 0x39, 0x45, 0x3d, 0x4d, 0xd1, 0xd0, 0xa2,
	0x68, 0x68, 0x50, 0x54, 0xa7, 0xd6, 0x84, 0x02, 0x0a, 0xb7, 0x2d, 0x8a, 0xea, 0x98, 0xd1, 0xdb,
	0x17, 0xf0, 0x40, 0x1a, 0x5c, 0xf8, 0xcd, 0x09, 0xfd, 0x38, 0x1c, 0x97, 0xd6, 0x05, 0x68, 0x16,
	0x6f, 0x9b, 0x66, 0xcb, 0x56, 0xc1, 0x3f, 0xcc, 0xea, 0x7c, 0x40, 0xd3, 0x08, 0x77, 0x7f, 0x08,
	0x1b, 0x56, 0x99, 0xad, 0xf6, 0xb2, 0xc1, 0xaf, 0x78, 0xa4, 0x8f, 0x60, 0xd3, 0xa0, 0x2e, 0x96,
	0x77, 0x5d, 0x37, 0x16, 0xd0, 0xaa, 0x00, 0x69, 0xe4, 0x39, 0xd3, 0x2c, 0xd3, 0x05, 0xa8, 0xc6,
	0x82, 0x5f, 0xc2, 0x9d, 0x5a, 0x36, 0xae, 0xb5, 0x72, 0x37, 0xb6, 0x58, 0xaa, 0x5d, 0xb1, 0xc1,
	0xe0, 0x1b, 0x17, 0x6e, 0xcc, 0xb3, 0x39, 0x2e, 0x67, 0x9d, 0x59, 0x35, 0x18, 0x1d, 0x60, 0xd5,
	0x4c, 0xcc, 0xf0, 0xd5, 0x13, 0xd7, 0x4d, 0xc9, 0x06, 0x55, 0x2d, 0x55, 0xe5, 0x52, 0xc7, 0xa4,
	0x05, 0xf2, 0x73, 0xac, 0x4b, 0x78, 0x83, 0xd8, 0xe9, 0xfa, 0x8d, 0x81, 0xb7, 0xd3, 0xdb, 0xbf,
	0x6d, 0xf5, 0x8e, 0xba, 0xca, 0x85, 0x86, 0x25, 0x39, 0x80, 0x75, 0xf3, 0xa6, 0xd4, 0x83, 0xe8,
	0xed, 0x3f, 0xb0, 0xbb, 0xce, 0xb8, 0x5c, 0x28, 0x00, 0xa1, 0xb5, 0xe8, 0x2b, 0xca, 0xc1, 0x2f,
	0xa0, 0x77, 0xb9, 0xf0, 0x4c, 0x7a, 0xfb, 0xf7, 0xac, 0x53, 0xec, 0xe4, 0x86, 0xa6, 0x3d, 0xf9,
	0x19, 0x6c, 0x55, 0xdd, 0x65, 0x98, 0xd1, 0x42, 0x8c, 0x72, 0xf9, 0x52, 0xe4, 0x99, 0x7a, 0x92,
	0xdd, 0x70, 0x95, 0x2a, 0xf8, 0xbb, 0x0b, 0xf7, 0xae, 0x09, 0x60, 0xd1, 0x65, 0x67, 0xd9, 0x65,
	0xa3, 0x79, 0xbb, 0x76, 0xf3, 0x1e, 0x40, 0x2f, 0x5e, 0xae, 0x5f, 0xb1, 0x5d, 0xbf, 0xe2, 0xa5,
	0xfa, 0xb5, 0xc8, 0x0c, 0xac, 0x48, 0xe9, 0x55, 0xf5, 0x2b, 0x5d, 0x55, 0xbf, 0xd2, 0xab, 0xea,
	0xd7, 0x92, 0x02, 0x7d, 0x34, 0x13, 0x5e, 0x5d, 0x89, 0x01, 0x05, 0x9f, 0x5c, 0xf8, 0x71, 0x1d,
	0xef, 0xaf, 0x4b, 0xc6, 0xa7, 0xcf, 0xa6, 0x43, 0xd5, 0x66, 0x30, 0x3f, 0xf3, 0x0e, 0xe4, 0x58,
	0x1d, 0xa8, 0x66, 0xae, 0x6b, 0x32, 0xf7, 0xfb, 0xb2, 0xef, 0x35, 0x10, 0x6e, 0x15, 0x30, 0xb5,
	0xbe, 0xa9, 0xd6, 0x6f, 0xaf, 0x5a, 0x3f, 0x2f, 0x9c, 0xe1, 0x8a, 0x95, 0xe4, 0xc9, 0x12, 0x9b,
	0x71, 0xa7, 0xfb, 0xf6, 0xfc, 0x76, 0x1d, 0x95, 0x83, 0xff, 0xb8, 0xc6, 0x7b, 0xc7, 0xba, 0x64,
	0x4c, 0x35, 0xf5, 0xcb, 0x73, 0xcc, 0x97, 0x87, 0x3d, 0xb9, 0x9c, 0xe8, 0x5a, 0x8a, 0x49, 0xf1,
	0xc2, 0x5a, 0x9e, 0xcf, 0x1e, 0x9e, 0x52, 0x68, 0x01, 0x51, 0xed, 0x5e, 0x43, 0xa3, 0x9a, 0xe3,
	0x06, 0x67, 0x74, 0xc3, 0x69, 0x2a, 0xad, 0x0d, 0x2e, 0xf2, 0xb6, 0xb5, 0xcc, 0x5b, 0x63, 0x9f,
	0x37, 0xea, 0xec, 0xb6, 0xcd, 0x3d, 0x05, 0x92, 0x7d, 0xb8, 0x95, 0x67, 0xe9, 0xd4, 0x78, 0x74,
	0xda, 0xb8, 0xab, 0x8c, 0x57, 0xea, 0x96, 0xcb, 0x73, 0x67, 0x45, 0x79, 0x0e, 0x3e, 0xbb, 0x46,
	0x65, 0x3f, 0xa5, 0x62, 0xbc, 0x6a, 0xc0, 0x21, 0xd0, 0x10, 0x92, 0x15, 0x15, 0x8b, 0xd4, 0x37,
	0x62, 0x05, 0x15, 0xa2, 0x7a, 0x4f, 0xea, 0x1b, 0xb1, 0x73, 0x9a, 0xa4, 0xd5, 0xfb, 0x51, 0xdf,
	0x73, 0x0a, 0x76, 0x4c, 0x0a, 0xd6, 0x93, 0x7c, 0xd7, 0x9c, 0xe4, 0xef, 0x43, 0x97, 0x33, 0x51,
	0xa6, 0xf2, 0x2d, 0x4f, 0x67, 0xd3, 0x4f, 0x0d, 0x20, 0xc9, 0xb5, 0x50, 0x0d, 0x3f, 0x95, 0x84,
	0x7d, 0x21, 0xe2, 0x8c, 0x4a, 0x86, 0x23, 0x4b, 0xd5, 0x94, 0xbb, 0xa1, 0x85, 0x61, 0x8f, 0x99,
	0xcb, 0xaa, 0x5a, 0xb7, 0x74, 0x8f, 0xb1, 0x51, 0xec, 0xc6, 0x65, 0x11, 0x53, 0xc9, 0x54, 0x25,
	0xd1, 0xc3, 0xbe, 0x81, 0x04, 0x7f, 0x73, 0x8d, 0x66, 0xab, 0xd2, 0xc5, 0x24, 0x4d, 0xd2, 0xea,
	0x0d, 0x4a, 0x2a, 0xc6, 0x75, 0x79, 0xaa, 0xa4, 0xaf, 0x68, 0x80, 0xf3, 0xd7, 0xeb, 0x5d, 0x39,
	0x3f, 0x36, 0xec, 0xf9, 0x71, 0x69, 0x1a, 0xaf, 0x82, 0xbe, 0x6e, 0x1a, 0x6f, 0x2d, 0x4f, 0xe3,
	0x04, 0x1a, 0x51, 0x1e, 0xcf, 0x48, 0xa7, 0xbe, 0xb1, 0x92, 0x4e, 0x98, 0x10, 0xf4, 0x82, 0x55,
	0x15, 0x7b, 0x26, 0x2e, 0x24, 0xa7, 0xbb, 0x94, 0x9c, 0x17, 0xb0, 0xf1, 0xc6, 0x7c, 0xb7, 0xf6,
	0x34, 0xeb, 0x5c, 0x33, 0xcd, 0xba, 0xf6, 0x34, 0xfb, 0xd8, 0xd8, 0x08, 0x8b, 0x9c, 0x41, 0xc8,
	0xee, 0x8c, 0x90, 0xd9, 0xbc, 0xe5, 0xaa, 0xef, 0x5d, 0x06, 0xed, 0xea, 0x07, 0x15, 0x01, 0x68,
	0x0d, 0xcb, 0x2c, 0xa6, 0x53, 0x7f, 0x0d, 0xbf, 0x4f, 0x72, 0xf5, 0xed, 0x90, 0x1e, 0xb4, 0x4f,
	0x4b, 0x26, 0x50, 0x70, 0xc9, 0x06, 0x74, 0xdf, 0xb3, 0x38, 0xd3, 0xa2, 0x47, 0xd6, 0xa1, 0x73,
	0x3a, 0x2a, 0xb9, 0x92, 0x1a, 0xb8, 0xea, 0x39, 0x4f, 0xf0, 0xbb, 0x89, 0x9a, 0x21, 0x95, 0x25,
	0x47, 0xa9, 0xb5, 0xfb, 0xc4, 0x98, 0x56, 0xd4, 0x0f, 0x3c, 0xd2, 0x81, 0xc6, 0xef, 0x19, 0xcf,
	0xfd, 0x35, 0xb4, 0x2c, 0x78, 0x7e, 0xc1, 0x99, 0x10, 0xbe, 0x83, 0xf8, 0x07, 0x9a, 0x48, 0xdf,
	0x45, 0xfc, 0x3c, 0xc9, 0x12, 0x31, 0x62, 0xb1, 0xef, 0xed, 0x1e, 0x43, 0xaf, 0xfa, 0x4d, 0xa6,
	0xba, 0xcf, 0x3a, 0x74, 0x5e, 0xe7, 0x52, 0xcd, 0xb4, 0xfe, 0x1a, 0x2e, 0x7a, 0x5b, 0x30, 0xa9,
	0x9d, 0x3d, 0x61, 0x89, 0x2c, 0x69, 0xe6, 0xbb, 0xa4, 0x0d, 0xde, 0x51, 0xca, 0xb4, 0x9b, 0x2f,
	0xe3, 0x43, 0x9a, 0xff, 0x36, 0xa1, 0x7e, 0x63, 0xf7, 0xdf, 0x8e, 0xbe, 0x40, 0x34, 0x8e, 0xd9,
	0x39, 0x2d, 0x53, 0xa9, 0xdc, 0x68, 0x8b, 0x32, 0x8a, 0xd0, 0x8b, 0xff, 0x39, 0xe4, 0x16, 0xdc,
	0x10, 0x8c, 0x5f, 0x32, 0x7e, 0xf4, 0x31, 0x62, 0x05, 0xba, 0xed, 0xff, 0xc9, 0x23, 0x3f, 0x02,
	0xbf, 0x44, 0xd2, 0xe7, 0xf2, 0x69, 0x29, 0x47, 0x39, 0x4f, 0xe4, 0xd4, 0xff, 0xbf, 0x43, 0xfa,
	0xb0, 0x75, 0xc1, 0x8b, 0xe8, 0x20, 0xcf, 0x32, 0x16, 0xa1, 0xed, 0x11, 0xe7, 0x39, 0xf7, 0xff,
	0xea, 0x91, 0x2d, 0xd8, 0x2c, 0x28, 0xa7, 0x13, 0x26, 0x19, 0xd7, 0xe0, 0x27, 0x8f, 0x10, 0xd8,
	0x38, 0x2b, 0x45, 0x92, 0x31, 0x21, 0x34, 0xf6, 0xd9, 0x55, 0xe7, 0xd1, 0x4b, 0x76, 0x78, 0x36,
	0x3f, 0xef, 0xcf, 0xea, 0xbc, 0xdf, 0x61, 0xd7, 0x32, 0xe1, 0xbf, 0x78, 0xbb, 0x7f, 0x70, 0xcc,
	0xe1, 0x4f, 0x95, 0x06, 0x1f, 0xd6, 0xab, 0x50, 0x54, 0xa9, 0xf0, 0xd7, 0xc8, 0x0d, 0xfc, 0x95,
	0x46, 0xa3, 0x91, 0xee, 0x2a, 0xbe, 0x83, 0x26, 0x66, 0x1b, 0xf0, 0x5d, 0x72, 0x07, 0xb6, 0x0c,
	0x93, 0x59, 0x97, 0xf1, 0x3d, 0xbc, 0xc8, 0x22, 0x89, 0xc6, 0x65, 0xe1, 0x03, 0xe9, 0x42, 0xf3,
	0x82, 0xe7, 0x65, 0xe1, 0xf7, 0xf0, 0xf2, 0x93, 0x4c, 0x94, 0x9c, 0x66, 0x11, 0xf3, 0xd7, 0xcf,
	0x5a, 0xea, 0x7f, 0x99, 0xc7, 0xdf, 0x05, 0x00, 0x00, 0xff, 0xff, 0xf6, 0x5b, 0xe5, 0x6d, 0xa9,
	0x11, 0x00, 0x00,
}

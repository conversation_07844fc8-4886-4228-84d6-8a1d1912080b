package es

import (
	"context"
	"encoding/json"

	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

type StoreIndex struct{}

type StoreDoc struct {
	Name          string `json:"name"`           //医院名称
	LocationPoint string `json:"location_point"` //医院坐标
	FinancialCode string `json:"financial_code"` //财务编码
	// LocationShape   Shape  `json:"location_shape"`
	Tags               string `json:"tags"`                 //标签
	HospitalAddress    string `json:"hospital_address"`     //医院地址
	HospitalAvatar     string `json:"hospital_avatar"`      //医院头像
	HospitalCity       string `json:"hospital_city"`        //医院城市
	HospitalType       int    `json:"hospital_type"`        //医院类型 1中心医院，2专科医院，3社区医院，4全科医院，5小型全科医院，6中型全科医院，7宠物店
	HospitalStatus     int    `json:"hospital_status"`      //医院状态 0正常 -1删除 1闭店 2暂停营业
	Mobile             string `json:"mobile"`               //医院电话
	Distance           string `json:"distance"`             //医院距离,单位：km
	HospitalCode       string `json:"hospital_code"`        //医院编码
	HospitalInternetId string `json:"hospital_internet_id"` //医院网络id
	CompanySource      int    `json:"companySource"`        //1=瑞鹏,2=其它宠物店
}

type Shape struct {
	Type        string        `json:"type"`
	Coordinates [][][]float64 `json:"coordinates"`
}

type StoreSearchWhere struct {
	Keywords string
	CityName string
	Lon      float64
	Lat      float64
	From     int
	Size     int
	//搜索类型：1.综合搜索;2.医院搜索
	Type int
}

//根据经纬度获取最近门店
func (c *StoreIndex) QueryGeoDistance(lat, lon float64, from, size int) ([]*StoreDoc, error) {
	geoDistanceSort := elastic.NewGeoDistanceSort("location_point").Point(lat, lon).Asc().Unit("km")
	query := elastic.NewMatchAllQuery()
	result, err := c.Search(from, size, query).SortBy(geoDistanceSort).Do(context.Background())
	if err != nil {
		glog.Error(err)
		return nil, err
	}

	var docs []*StoreDoc
	for _, v := range result.Hits.Hits {
		var doc StoreDoc
		if err = json.Unmarshal(v.Source, &doc); err != nil {
			glog.Error(err)
			return nil, err
		}
		docs = append(docs, &doc)
	}
	return docs, nil
}

func (c *StoreIndex) Search(from, size int, query ...elastic.Query) *elastic.SearchService {
	client := NewEsClient()
	search := client.Search().Index(IndexStore)
	for _, v := range query {
		search.Query(v)
	}
	return search.From(from).Size(size)
}

func ConvertToStoreDoc(hit *elastic.SearchHits) []*StoreDoc {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	var docs []*StoreDoc
	for _, v := range hit.Hits {
		var cpr StoreDoc
		if err := json.Unmarshal(v.Source, &cpr); err != nil {
			glog.Error(err)
			return nil
		}

		if len(v.Sort) == 2 {
			cpr.Distance = cast.ToString(v.Sort[1])
		}

		docs = append(docs, &cpr)
	}
	return docs
}

func (c *StoreIndex) QueryStore(w *StoreSearchWhere) []*StoreDoc {
	client := NewEsClient()
	searchService := new(elastic.SearchService)

	q := elastic.NewBoolQuery()

	if w.CityName != "" {
		q.Filter(elastic.NewTermQuery("hospital_city.keyword", w.CityName))
	}

	if w.Keywords != "" {
		//综合搜索
		if w.Type == 1 {
			q.Must(elastic.NewBoolQuery().Must(elastic.NewMatchQuery("name", w.Keywords)))
		}
		//医院搜索
		if w.Type == 2 {
			q.Must(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("name", w.Keywords), elastic.NewMatchQuery("tags", w.Keywords)))
		}
	}

	//src, _ := q.Source()
	//b, _ := json.Marshal(src)
	//println(string(b))

	if w.Size == 0 {
		w.Size = 999
	}

	searchService = client.Search(IndexStore).Query(q).From(w.From).Size(w.Size)
	if w.Lat != 0 && w.Lon != 0 {
		geoDistanceSort := elastic.NewGeoDistanceSort("location_point").Point(w.Lat, w.Lon).Asc().Unit("km")
		// searchService = searchService.SortBy(elastic.NewFieldSort("_score").Desc(),geoDistanceSort)
		searchService = searchService.SortBy(elastic.NewScoreSort(), geoDistanceSort)
	}

	if result, err := searchService.Do(context.Background()); err != nil {
		glog.Error(err)
		return nil
	} else {
		return ConvertToStoreDoc(result.Hits)
	}
}

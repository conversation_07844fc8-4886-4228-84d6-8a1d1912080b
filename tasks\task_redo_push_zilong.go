package tasks

import (
	"encoding/json"
	"errors"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/et"
	"order-center/services"
	"strings"
	"time"
)

//重推退打折卡
type redoZiLongDiscountCardRefund struct {
}

//重推退打折卡到子龙
func (e *redoZiLongDiscountCardRefund) redoTask(taskData *models.OrderRedoTask, redo *redoTask) (err error) {

	client := et.GetExternalClient()

	order := new(models.OrderMain)

	err = json.Unmarshal([]byte(taskData.Params), order)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskData.Params)
	}
	no := strings.Split(taskData.OrderSn, "|")[1]
	endTime := time.Now().Format(kit.DATETIME_LAYOUT)

	db := services.GetDcDBConn()
	//是否退打折卡
	//获取最后一个有效的打折卡时间，如果没有，那么当前时间为过期时间传给子龙退卡
	IsRetrunDiscount := ""
	if _, err1 := db.SQL("SELECT expiry_date FROM  datacenter.vip_card_order a "+
		" INNER JOIN datacenter.`vip_card_template` b ON a.card_id=b.id "+
		" INNER JOIN  datacenter.`vip_card_equity_config` c ON c.card_tid=b.id "+
		" INNER JOIN datacenter.`vip_card_equity` d ON d.id=c.equity_id "+
		" where user_id=? and order_sn!=? and expiry_date>=NOW() and state=10 AND d.equity_type=8 order by a.expiry_date desc", order.MemberId, order.OrderSn).Get(&IsRetrunDiscount); err != nil {
		return err1
	}

	par := &et.DiscountCardRefundReq{
		EnsureCode:   no,
		CustomerId:   order.MemberId,
		CreateSource: 5,
		EndTime:      endTime,
	}
	if IsRetrunDiscount != "" {
		endTime = IsRetrunDiscount
	}
	rs, errDiscount := client.ZiLong.DiscountCardRefund(client.Ctx, par)
	if errDiscount != nil {
		return err

	} else if rs.Code != 200 {
		errDiscount = errors.New(rs.Message)
		return errors.New(rs.Message)
	}

	return nil
}

//重新退子龙门店券
type redoZiLongWasteCoupon struct {
}

//重新推送到巨益OMS
func (e *redoZiLongWasteCoupon) redoTask(taskData *models.OrderRedoTask, redo *redoTask) (err error) {

	s := services.CardService{}
	//taskData.OrderSn 存的是退款单号，用于区分唯一数据
	return s.RetrunZiLongCouponCode(taskData.Params, taskData.OrderSn)

}

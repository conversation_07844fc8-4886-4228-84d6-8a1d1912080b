// Code generated by protoc-gen-go. DO NOT EDIT.
// source: global.proto

//import "google/api/annotations.proto";

package base

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//空请求
type NilRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NilRequest) Reset()         { *m = NilRequest{} }
func (m *NilRequest) String() string { return proto.CompactTextString(m) }
func (*NilRequest) ProtoMessage()    {}
func (*NilRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4baa8fc7dedf329e, []int{0}
}

func (m *NilRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NilRequest.Unmarshal(m, b)
}
func (m *NilRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NilRequest.Marshal(b, m, deterministic)
}
func (m *NilRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NilRequest.Merge(m, src)
}
func (m *NilRequest) XXX_Size() int {
	return xxx_messageInfo_NilRequest.Size(m)
}
func (m *NilRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NilRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NilRequest proto.InternalMessageInfo

//基本响应，用于写入操作无返回时使用
type BasicResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details              []string `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BasicResponse) Reset()         { *m = BasicResponse{} }
func (m *BasicResponse) String() string { return proto.CompactTextString(m) }
func (*BasicResponse) ProtoMessage()    {}
func (*BasicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4baa8fc7dedf329e, []int{1}
}

func (m *BasicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BasicResponse.Unmarshal(m, b)
}
func (m *BasicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BasicResponse.Marshal(b, m, deterministic)
}
func (m *BasicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BasicResponse.Merge(m, src)
}
func (m *BasicResponse) XXX_Size() int {
	return xxx_messageInfo_BasicResponse.Size(m)
}
func (m *BasicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BasicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BasicResponse proto.InternalMessageInfo

func (m *BasicResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BasicResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BasicResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BasicResponse) GetDetails() []string {
	if m != nil {
		return m.Details
	}
	return nil
}

func init() {
	proto.RegisterType((*NilRequest)(nil), "proto.NilRequest")
	proto.RegisterType((*BasicResponse)(nil), "proto.BasicResponse")
}

func init() { proto.RegisterFile("global.proto", fileDescriptor_4baa8fc7dedf329e) }

var fileDescriptor_4baa8fc7dedf329e = []byte{
	// 150 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x49, 0xcf, 0xc9, 0x4f,
	0x4a, 0xcc, 0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x05, 0x53, 0x4a, 0x3c, 0x5c, 0x5c,
	0x7e, 0x99, 0x39, 0x41, 0xa9, 0x85, 0xa5, 0xa9, 0xc5, 0x25, 0x4a, 0xb9, 0x5c, 0xbc, 0x4e, 0x89,
	0xc5, 0x99, 0xc9, 0x41, 0xa9, 0xc5, 0x05, 0xf9, 0x79, 0xc5, 0xa9, 0x42, 0x42, 0x5c, 0x2c, 0xc9,
	0xf9, 0x29, 0xa9, 0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0xac, 0x41, 0x60, 0xb6, 0x90, 0x04, 0x17, 0x7b,
	0x6e, 0x6a, 0x71, 0x71, 0x62, 0x7a, 0xaa, 0x04, 0x93, 0x02, 0xa3, 0x06, 0x67, 0x10, 0x8c, 0x2b,
	0x24, 0xc2, 0xc5, 0x9a, 0x5a, 0x54, 0x94, 0x5f, 0x24, 0xc1, 0x0c, 0x16, 0x87, 0x70, 0x40, 0xea,
	0x53, 0x52, 0x4b, 0x12, 0x33, 0x73, 0x8a, 0x25, 0x58, 0x14, 0x98, 0x41, 0xea, 0xa1, 0x5c, 0x27,
	0xf6, 0x28, 0x88, 0x2b, 0x92, 0xd8, 0xc0, 0x94, 0x31, 0x20, 0x00, 0x00, 0xff, 0xff, 0x83, 0xed,
	0x99, 0xbc, 0xa3, 0x00, 0x00, 0x00,
}

package models

import "time"

type TTenantPercentageValues struct {
	Id              int64     `json:"id" xorm:"pk autoincr not null comment('流水id') BIGINT 'id'"`
	TenantId        int64     `json:"tenant_id" xorm:"not null comment('店铺id') BIGINT 'tenant_id'"`
	PercentageId    int64     `json:"percentage_id" xorm:"not null comment('提成ID') BIGINT 'percentage_id'"`
	ProductType     string    `json:"product_type" xorm:"not null default '' comment('提成类型（产品类型）') VARCHAR(255) 'product_type'"`
	PerformanceWay  string    `json:"performance_way" xorm:"not null default 'SELL_PRICE' comment('提成方式') VARCHAR(255) 'performance_way'"`
	PercentageValue int       `json:"percentage_value" xorm:"not null default 0 TINYINT 'percentage_value'"`
	FixAllProduct   int       `json:"fix_all_product" xorm:"default 1 comment('适用全部商品:1适用,0不适用') BIT(1) 'fix_all_product'"`
	CreatedBy       int64     `json:"created_by" xorm:"not null comment('创建人') BIGINT 'created_by'"`
	CreatedTime     time.Time `json:"created_time" xorm:"not null comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy       int64     `json:"updated_by" xorm:"default 'null' comment('最后修改人') BIGINT 'updated_by'"`
	UpdatedTime     time.Time `json:"updated_time" xorm:"default 'null' comment('最后修改时间') DATETIME 'updated_time'"`
}

const (
	//提成方式
	PRODUCTTYPESELLPRICE = "SELL_PRICE"
	PRODUCTTYPEPAYPRICE  = "PAY_PRICE"
)

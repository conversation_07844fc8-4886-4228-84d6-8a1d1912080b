package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"github.com/xuri/excelize/v2"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"strconv"
	"strings"
	"time"
)

// 父订单-导出(含商品明细)数据
type ParentOrderProductExport struct {
	F           *excelize.File
	SheetName   string
	storeMap    map[string]*dac.StoreInfo
	taskParams  *oc.AwenParentOrderListRequest
	OrgId       int32
	FinanceCode string
}

// 逻辑
func (e *ParentOrderProductExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenParentOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []oc.AwenParentOrderProductExport
	var combinedProduct map[string]string

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	e.taskParams.Orgid = cast.ToInt64(e.OrgId)
	//e.taskParams.FinancialCode = e.FinanceCode
	for {
		details, combinedProduct, err = awenParentOrderProductExport(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		orderList = append(orderList, details...)
		glog.Info("details length - pagesize length", len(details), e.taskParams.PageSize)
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头

	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)

	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}
	var n string
	for k := range orderList {
		n = strconv.Itoa(k + 2)
		//宠物saas走这里
		if orderList[k].AppChannel == 12 {
			// 订单类型
			switch orderList[k].OrderType {
			case 22:
				e.F.SetCellValue(e.SheetName, "A"+n, "次卡")
			case 23:
				e.F.SetCellValue(e.SheetName, "A"+n, "储值卡")
			default:
				e.F.SetCellValue(e.SheetName, "A"+n, "普通订单")
			}
			// 订单号
			e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].OrderSn)
			// 外部订单号
			e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
			//收货人
			e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].ReceiverName)
			// 收货人联系方式
			e.F.SetCellValue(e.SheetName, "E"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip))
			// 收货人地址
			e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].ReceiverAddress)
			// 备注
			e.F.SetCellValue(e.SheetName, "G"+n, orderList[k].BuyerMemo)

			// SkuId
			e.F.SetCellValue(e.SheetName, "H"+n, orderList[k].SkuId)
			// 商品名称
			e.F.SetCellValue(e.SheetName, "I"+n, orderList[k].ProductName)
			//  店内分类
			e.F.SetCellValue(e.SheetName, "J"+n, orderList[k].ChannelCategoryName)
			// 商品类型
			switch orderList[k].ProductType {
			case 1:
				e.F.SetCellValue(e.SheetName, "K"+n, "实物商品")
			case 2:
				e.F.SetCellValue(e.SheetName, "K"+n, "虚拟商品")
			case 3:
				e.F.SetCellValue(e.SheetName, "K"+n, "组合商品")
			case 4:
				e.F.SetCellValue(e.SheetName, "K"+n, "服务")
			case 5:
				e.F.SetCellValue(e.SheetName, "K"+n, "活体")
			default:
				if orderList[k].OrderType == 22 {
					e.F.SetCellValue(e.SheetName, "K"+n, "次卡")
				} else if orderList[k].OrderType == 23 {
					e.F.SetCellValue(e.SheetName, "K"+n, "储值卡")
				}
			}

			// 订单状态
			e.F.SetCellValue(e.SheetName, "L"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
			// 发货数量
			e.F.SetCellValue(e.SheetName, "M"+n, orderList[k].Number)
			// 原价
			e.F.SetCellValue(e.SheetName, "N"+n, kit.FenToYuan(orderList[k].MarkingPrice))
			// 活动价
			e.F.SetCellValue(e.SheetName, "O"+n, kit.FenToYuan(orderList[k].PayPrice))
			// 订单金额（支付金额）
			e.F.SetCellValue(e.SheetName, "P"+n, kit.FenToYuan(orderList[k].PaymentTotal))
			// 平台补贴分摊
			e.F.SetCellValue(e.SheetName, "Q"+n, kit.FenToYuan(orderList[k].PrivilegePt))
			// 退款数量
			e.F.SetCellValue(e.SheetName, "R"+n, orderList[k].RefundNumber)
			// 退款金额
			e.F.SetCellValue(e.SheetName, "S"+n, kit.FenToYuan(orderList[k].RefundTotal))
			// 下单时间
			e.F.SetCellValue(e.SheetName, "T"+n, orderList[k].CreateTime)
			// 支付时间
			e.F.SetCellValue(e.SheetName, "U"+n, orderList[k].PayTime)
			// 所属连锁名称
			e.F.SetCellValue(e.SheetName, "V"+n, orderList[k].ChainName)
			// 门店名称
			e.F.SetCellValue(e.SheetName, "W"+n, orderList[k].ShopName)
			// 店铺ID(财务编码)
			e.F.SetCellValue(e.SheetName, "X"+n, orderList[k].ShopId)

			// 店铺类型
			e.F.SetCellValue(e.SheetName, "Y"+n, orderList[k].TenantType)
			// 新零售运营
			e.F.SetCellValue(e.SheetName, "Z"+n, orderList[k].TenantRetailOperationType)
			// 用户ID
			e.F.SetCellValue(e.SheetName, "AA"+n, orderList[k].MemberId)
			// 支付方式
			e.F.SetCellValue(e.SheetName, "AB"+n, services.PayMode[orderList[k].PayMode])

			// 送达时间
			e.F.SetCellValue(e.SheetName, "AC"+n, orderList[k].ExpectedTime)

		} else {
			// 阿闻走这里
			// 订单号
			e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
			// 外部订单号
			e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].OldOrderSn)
			//收货人
			e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].ReceiverName)
			// 收货人联系方式
			// 收货人地址
			if _, ok := phoneExportMap[orderList[k].ShopId]; ok {
				createTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, orderList[k].CreateTime)
				compareStrTime := "2022-04-01  00:00:00"
				compareTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, compareStrTime)
				if createTime.After(compareTime) {
					e.F.SetCellValue(e.SheetName, "D"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip))
					e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].ReceiverAddress)
				} else {
					e.F.SetCellValue(e.SheetName, "D"+n, "")
					e.F.SetCellValue(e.SheetName, "E"+n, "")
				}
			} else {
				e.F.SetCellValue(e.SheetName, "D"+n, "")
				e.F.SetCellValue(e.SheetName, "E"+n, "")
			}
			// 备注
			e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].BuyerMemo)
			// 商品筛选
			switch orderList[k].GroupType {
			case 0:
				e.F.SetCellValue(e.SheetName, "G"+n, "无组合商品")
			case 1:
				e.F.SetCellValue(e.SheetName, "G"+n, "有实实组合")
			case 2:
				e.F.SetCellValue(e.SheetName, "G"+n, "有虚虚组合")
			case 3:
				e.F.SetCellValue(e.SheetName, "G"+n, "有虚实组合")
			}
			// SkuId
			e.F.SetCellValue(e.SheetName, "H"+n, orderList[k].SkuId)
			// 商品名称
			e.F.SetCellValue(e.SheetName, "I"+n, orderList[k].ProductName)
			// 店内分类
			e.F.SetCellValue(e.SheetName, "J"+n, orderList[k].ChannelCategoryName)
			// 商品筛选
			switch orderList[k].ProductType {
			case 1:
				e.F.SetCellValue(e.SheetName, "K"+n, "实物商品")
			case 2:
				e.F.SetCellValue(e.SheetName, "K"+n, "虚拟商品")
			case 3:
				e.F.SetCellValue(e.SheetName, "K"+n, "组合商品")
			default:
				e.F.SetCellValue(e.SheetName, "K"+n, "")
			}
			// 组合商品SkuId
			e.F.SetCellValue(e.SheetName, "L"+n, orderList[k].ParentSkuId)
			// 组合商品名称
			if len(orderList[k].ParentSkuId) > 0 {
				if _, ok := combinedProduct[orderList[k].ParentSkuId]; ok {
					e.F.SetCellValue(e.SheetName, "M"+n, combinedProduct[orderList[k].ParentSkuId])
				}
			}
			// 订单状态
			e.F.SetCellValue(e.SheetName, "N"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
			// 发货数量
			e.F.SetCellValue(e.SheetName, "O"+n, orderList[k].Number)
			// 订单金额（支付金额）
			e.F.SetCellValue(e.SheetName, "P"+n, kit.FenToYuan(orderList[k].PaymentTotal))
			// 退款数量
			e.F.SetCellValue(e.SheetName, "Q"+n, orderList[k].RefundNumber)
			// 退款金额
			e.F.SetCellValue(e.SheetName, "R"+n, kit.FenToYuan(orderList[k].RefundTotal))
			/*if orderList[k].OrderStatus != 10 && orderList[k].RefundState == 3 {
				amount := services.OrderRetrunGetList(orderList[k].OrderSn)
				key := orderList[k].ProductName + orderList[k].SkuId + cast.ToString(orderList[k].PaymentTotal)
				if _, ok := amount[key]; ok {
					e.f.SetCellValue(e.sheetName, "M"+n, amount[key])
				}
			}*/
			// 支付时间
			e.F.SetCellValue(e.SheetName, "S"+n, orderList[k].PayTime)
			// 店铺ID(财务编码)
			e.F.SetCellValue(e.SheetName, "T"+n, orderList[k].ShopId)
			// 门店名称
			e.F.SetCellValue(e.SheetName, "U"+n, orderList[k].ShopName)
			// 业绩所属员工姓名
			e.F.SetCellValue(e.SheetName, "V"+n, orderList[k].PerformanceStaffName)
			//店铺类型
			ShopType := "新瑞鹏"
			if orderList[k].AppChannel != 1 {
				ShopType = "TP代运营"
			}
			e.F.SetCellValue(e.SheetName, "W"+n, ShopType)
			// 用户ID
			e.F.SetCellValue(e.SheetName, "X"+n, orderList[k].MemberId)
			// 支付方式
			e.F.SetCellValue(e.SheetName, "Y"+n, services.PayMode[orderList[k].PayMode])
			// 提货点名称
			e.F.SetCellValue(e.SheetName, "Z"+n, orderList[k].PickupStationName)
			// 提货点地址
			e.F.SetCellValue(e.SheetName, "AA"+n, orderList[k].PickupStationAddress)
			// 送达时间
			e.F.SetCellValue(e.SheetName, "AB"+n, orderList[k].ExpectedTime)
			// 平台补贴分摊
			e.F.SetCellValue(e.SheetName, "AC"+n, kit.FenToYuan(orderList[k].PrivilegePt))
		}

	}
	e.F.Save()

	return
}

// 设置表头
func (e *ParentOrderProductExport) SetSheetName() {
	var nameList []string
	if e.OrgId == 6 {
		nameList = []string{
			"订单类型", "订单号", "渠道订单号", "收货人", "收货人联系方式", "收货人地址", "备注", "SKUID", "商品名称", "店内分类",
			"商品类型", "订单状态", "销售数量", "原价", "活动价", "支付金额", "平台补贴分摊", "退款数量", "退款金额", "下单时间",
			"支付时间", "所属连锁", "店铺名称", "店铺ID", "店铺类型", "新零售运营", "用户ID", "支付方式", "送达时间", "", "",
		}

	} else {
		nameList = []string{
			"订单号", "外部订单号", "收货人", "收货人联系方式", "收货人地址", "配送备注", "商品筛选", "SKUID", "商品名称", "店内分类", "商品类型", "组合商品SKUID",
			"组合商品名称", "订单状态", "销售数量", "支付金额", "退款数量", "退款金额", "支付时间", "财务编码", "门店名称", "业绩归属人", "店铺类型", "用户ID", "支付方式",
			"提货点名称", "提货点地址", "送达时间", "平台补贴分摊", "", "",
		}
	}

	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *ParentOrderProductExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("订单列表-导出商品明细(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

// 阿闻管家父订单-导出(含商品明细)数据
func awenParentOrderProductExport(params *oc.AwenParentOrderListRequest) (details []oc.AwenParentOrderProductExport, combinedProduct map[string]string, err error) {
	glog.Info("阿闻管家父订单商品列表导出数据参数：", kit.JsonEncode(params))
	defer glog.Info("QUIT", "阿闻管家父订单商品列表导出数据参数：", kit.JsonEncode(params))

	conn := services.NewSlaveDbConn()
	defer conn.Close()

	var baseOrder []*oc.AwenParentOrderExport
	//先把符合要求的订单查出来
	//订单表，商品表，品牌表, 业绩表关联查询
	session := conn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Join("left", "order_main zd", "zd.parent_order_sn = order_main.order_sn").
		Join("left", "order_exception oe", "zd.order_sn = oe.order_sn AND zd.order_status=20 and oe.is_show=1").
		Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ?", "")

	session.OrderBy("`order_main`.`create_time` DESC")
	if params.CombineType == 1 || params.CombineType == 2 || params.CombineType == 3 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("`order_product`.combine_type = ? and `order_product`.product_type = ?  ", params.CombineType, 3)
	} else if params.CombineType == 4 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("NOT EXISTS(SELECT op.order_sn FROM order_product op WHERE op.order_sn=`order_main`.order_sn and op.product_type = 3) ")
	} else {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
	}

	//订单搜索
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case 1: //订单号
			session.And("`order_main`.order_sn like ?", params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
		case 3: //收货人姓名
			session.And("`order_main`.receiver_name like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_mobile = ?", utils.MobileEncrypt(params.Keyword))
		case 5: //买家手机号
			session.And("`order_main`.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
		case 6: //店铺名称
			session.And("`order_main`.shop_name like ?", "%"+params.Keyword+"%")
		case 7: //子订单号
			session.And("order_main.order_sn in (select parent_order_sn from order_main where order_sn = ?)", params.Keyword)
		default: //default case
		}
	}

	session.And("order_main.org_id = ?", params.Orgid)
	if params.Orgid == 6 {
		session.In("order_main.shop_id", params.Shopids)
	} else {
		//筛选用户权限门店
		if len(params.UserNo) > 0 {
			session.Join("inner", "datacenter.`store_user_authority` sua", "`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
		}

		//登录用户有权限的所有门店id(财务编码)
		if len(params.Shopids) > 0 {
			session.In("`order_main`.shop_id", params.Shopids)
		}
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}
	//销售渠道
	if params.SaleChannel > 0 {
		session.And("`order_main`.user_agent = ?", params.SaleChannel)
	}
	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == services.OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		if params.Orgid == 6 {
			session.Join("left", "order_payment", "order_main.order_sn=order_payment.order_sn").Where("order_payment.pay_type=?", params.PayMode)
		} else {
			session.And("order_main.pay_mode = ?", params.PayMode)
		}

	}

	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	if params.OrderDeliveryFilter != 0 {
		if params.OrderDeliveryFilter == 1 {
			session.And("oe.delivery_id is null")
		} else if params.OrderDeliveryFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("oe.delivery_id is not null")
		}
	}

	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}
	//异常订单搜索
	if params.OrderFilter > 0 {
		if params.OrderFilter == 1 {
			session.And("(order_detail.push_delivery_reason='' or order_detail.push_delivery=1) and (order_detail.push_third_order_reason='' or order_detail.push_third_order=1) and (order_detail.split_order_fail_reason='' or order_detail.split_order_result in(0,1))")
		} else if params.OrderFilter == 2 {
			session.And("(order_detail.push_delivery_reason<>'' and order_detail.push_delivery=0) or (order_detail.push_third_order_reason<>'' and order_detail.push_third_order=0) or (order_detail.split_order_fail_reason<>'' and order_detail.split_order_result=2)")
		}
	}
	if err = session.Select(`order_main.order_sn,order_main.channel_id,MAX(order_product.combine_type) group_type,order_main.shop_id`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1)").
		Find(&baseOrder); err != nil {
		glog.Info("zx订单导出报错20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(baseOrder) == 0 {
		return
	}

	var (
		orderSns   = make([]string, len(baseOrder))
		orderGroup = make(map[string]int32)
		shopIds    = make([]string, 0)
	)

	for k := range baseOrder {
		orderSns[k] = baseOrder[k].OrderSn
		orderGroup[baseOrder[k].OrderSn] = baseOrder[k].GroupType
		shopIds = append(shopIds, baseOrder[k].ShopId)
	}

	chainIds := make([]int64, 0)
	chainMap := make(map[int64]models.TChain)
	chainList := make([]models.TChain, 0)
	tenantList := make([]models.TTenant, 0)
	tenantMap := make(map[int64]models.TTenant)
	tenantRetailCfgList := make([]models.TTenantRetailCfg, 0)
	tenantRetailCfgMap := make(map[int64]models.TTenantRetailCfg)
	if params.Orgid == 6 {
		if e := conn.Table("eshop_saas.t_tenant").In("id", shopIds).Find(&tenantList); e != nil {
			err = errors.New("查询店铺信息失败" + e.Error())
			return
		}
		for _, tenant := range tenantList {
			tenantMap[tenant.Id] = tenant
			chainIds = append(chainIds, tenant.ChainId)
		}

		if e := conn.Table("eshop_saas.t_tenant_retail_cfg").In("tenant_id", shopIds).Find(&tenantRetailCfgList); e != nil {
			err = errors.New("店铺新零售运营配置失败" + e.Error())
			return
		}
		for _, retailcfg := range tenantRetailCfgList {
			tenantRetailCfgMap[retailcfg.TenantId] = retailcfg

		}

		if e := conn.Table("eshop_saas.t_chain").In("id", chainIds).Find(&chainList); e != nil {
			err = errors.New("查询连锁信息失败" + e.Error())
			return
		}
		for _, chain := range chainList {
			chainMap[chain.Id] = chain
		}
	}

	session = conn.Select(`	order_main.order_sn,
		order_main.old_order_sn,
		order_main.parent_order_sn as gy_order_sn,
		order_main.app_channel,
		order_main.channel_id,
		order_main.shop_id,
		order_main.member_id,
		order_main.shop_name,
		order_main.order_status_child,
		order_main.total,
		order_main.pay_time,
		order_main.create_time,
		order_main.pay_mode,
		order_main.receiver_name,
		order_main.receiver_address,
		order_main.receiver_mobile,
		order_main.create_time,
		order_main.order_type,
		refund_order.refund_amount,
		refund_order.refund_state,
		order_product.sku_id,
		order_product.parent_sku_id,
		order_product.product_id,
		order_product.product_name,
		order_product.number,
		order_product.marking_price,
		order_product.pay_price,
		order_product.payment_total,
		order_product.sku_pay_total,
order_product.privilege_pt,
		order_product.product_type,
		order_detail.performance_staff_name,
		order_detail.buyer_memo,
        order_product.combine_type group_type,
		order_product.channel_category_name channel_category_name,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		order_detail.expected_time`).
		Table("order_product").
		Join("left", "`order_main`", "order_main.order_sn = order_product.order_sn").
		Join("left", "refund_order", "order_main.order_sn=refund_order.order_sn").
		Join("left", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		OrderBy("`order_main`.`create_time` DESC").In("order_product.order_sn", orderSns).GroupBy("order_product.id")

	err = session.Find(&details)
	if err != nil {
		glog.Info("zx订单导出报错20221124")
		err = errors.New("订单导出查询订单列表失败" + err.Error())
		return
	}

	// 查询商品退货信息
	refundGoodsMap := map[string][]*dto.RefundOrderGoods{}
	if err = func() error {
		var refundGoods []*dto.RefundOrderGoods
		if err = conn.Select("o.parent_order_sn order_sn, b.sku_id,b.tkcount,b.parent_sku_id, b.refund_amount, b.product_price,b.refund_price").Table("refund_order").Alias("a").
			Join("inner", "order_main o", "o.order_sn = a.order_sn").
			Join("left", "refund_order_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("o.parent_order_sn", orderSns).
			In("a.channel_id", services.ChannelAwenId, services.ChannelIdOfflineShop, services.ChannelDigitalHealth).
			Find(&refundGoods); err != nil {
			glog.Info("zx订单导出报错20221124")
			return err
		}

		//第三方订单退款数据跟阿闻不一致，需要另外取
		var thirdRefundGoos []*dto.RefundOrderGoods
		if err = conn.Select("a.order_sn, b.sku_id,b.parent_sku_id,b.tkcount, b.refund_amount, b.product_price,b.refund_price").Table("refund_order").Alias("a").
			Join("inner", "order_main o", "a.order_sn = o.order_sn").
			Join("left", "refund_order_third_product b", "a.refund_sn = b.refund_sn").
			Where("a.refund_state = 3").In("o.order_sn", orderSns).
			In("o.channel_id", services.ChannelMtId, services.ChannelElmId, services.ChannelJddjId).
			Find(&thirdRefundGoos); err != nil {
			glog.Info("zx订单导出报错20221124")
			return err
		}
		for k := range refundGoods {
			refundGoodsMap[refundGoods[k].OrderSn] = append(refundGoodsMap[refundGoods[k].OrderSn], refundGoods[k])
		}
		for k := range thirdRefundGoos {
			refundGoodsMap[thirdRefundGoos[k].OrderSn] = append(refundGoodsMap[thirdRefundGoos[k].OrderSn], thirdRefundGoos[k])
		}

		return nil
	}(); err != nil {
		return
	}

	for i := range details {

		if params.CombineType == 1 || params.CombineType == 2 {
			details[i].GroupType = params.CombineType
		} else if params.CombineType == 4 {
			details[i].GroupType = 0
		}

		if _, ok := orderGroup[details[i].OrderSn]; ok {
			details[i].GroupType = orderGroup[details[i].OrderSn]
		}

		if len(refundGoodsMap) > 0 {
			for j := range refundGoodsMap[details[i].OrderSn] {
				if details[i].OrderSn == refundGoodsMap[details[i].OrderSn][j].OrderSn &&
					details[i].SkuId == refundGoodsMap[details[i].OrderSn][j].SkuId &&
					details[i].PayPrice == refundGoodsMap[details[i].OrderSn][j].RefundPrice &&
					details[i].ParentSkuId == refundGoodsMap[details[i].OrderSn][j].ParentSkuId {
					details[i].RefundNumber += refundGoodsMap[details[i].OrderSn][j].Tkcount
					details[i].RefundTotal += cast.ToInt32(kit.YuanToFen(cast.ToFloat64(refundGoodsMap[details[i].OrderSn][j].RefundAmount)))
				}
			}
		}

		if tenant, ok := tenantMap[cast.ToInt64(details[i].ShopId)]; ok {
			//店铺类型：1-直营店、2-加盟店、3-其他
			if tenant.Type == 1 {
				details[i].TenantType = "直营店"
			} else if tenant.Type == 2 {
				details[i].TenantType = "加盟店"
			}

			if chain, ok := chainMap[tenant.ChainId]; ok {
				details[i].ChainName = chain.Name
			}
		}

		if cfg, ok := tenantRetailCfgMap[cast.ToInt64(details[i].ShopId)]; ok {
			if cfg.OperationType == 1 {
				details[i].TenantRetailOperationType = "独立运营"
			} else if cfg.OperationType == 2 {
				details[i].TenantRetailOperationType = "代运营"
			}
		}
	}

	return
}

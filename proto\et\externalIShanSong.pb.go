// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/externalIShanSong.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//只需要传闪送的订单号的请求参数
type IssOrderNoRequest struct {
	//required 闪送订单号 闪送订单号在订单计费接口生成并返回
	IssOrderNo           string   `protobuf:"bytes,1,opt,name=issOrderNo,proto3" json:"issOrderNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssOrderNoRequest) Reset()         { *m = IssOrderNoRequest{} }
func (m *IssOrderNoRequest) String() string { return proto.CompactTextString(m) }
func (*IssOrderNoRequest) ProtoMessage()    {}
func (*IssOrderNoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{0}
}

func (m *IssOrderNoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssOrderNoRequest.Unmarshal(m, b)
}
func (m *IssOrderNoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssOrderNoRequest.Marshal(b, m, deterministic)
}
func (m *IssOrderNoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssOrderNoRequest.Merge(m, src)
}
func (m *IssOrderNoRequest) XXX_Size() int {
	return xxx_messageInfo_IssOrderNoRequest.Size(m)
}
func (m *IssOrderNoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IssOrderNoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IssOrderNoRequest proto.InternalMessageInfo

func (m *IssOrderNoRequest) GetIssOrderNo() string {
	if m != nil {
		return m.IssOrderNo
	}
	return ""
}

//订单计费请求参数
type IssOrderCalculateRequest struct {
	// required 城市名称 取闪送开通城市列表即可，格式：xx市
	CityName string `protobuf:"bytes,1,opt,name=cityName,proto3" json:"cityName"`
	//required 预约类型 0立即单，1预约单
	AppointType int32 `protobuf:"varint,2,opt,name=appointType,proto3" json:"appointType"`
	//预约时间 指的是预约取件时间 如：2020-02-02 22:00,
	AppointmentDate string `protobuf:"bytes,3,opt,name=appointmentDate,proto3" json:"appointmentDate"`
	//店铺ID 对应闪送门店ID
	StoreId string `protobuf:"bytes,4,opt,name=storeId,proto3" json:"storeId"`
	//可指定的交通工具方式 0未指定; 2摩托车; 8汽车；默认为0；指定交通方式会产生额外费用
	TravelWay int32 `protobuf:"varint,5,opt,name=travelWay,proto3" json:"travelWay"`
	//帮我取 帮我送 1.帮我送 2.帮我取 ；默认为1
	DeliveryType int32 `protobuf:"varint,6,opt,name=deliveryType,proto3" json:"deliveryType"`
	//required 订单发送信息
	Sender *IssOrderCalculateSender `protobuf:"bytes,7,opt,name=sender,proto3" json:"sender"`
	//订单收单信息
	ReceiverList         []*IssOrderCalculateReceiver `protobuf:"bytes,8,rep,name=receiverList,proto3" json:"receiverList"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *IssOrderCalculateRequest) Reset()         { *m = IssOrderCalculateRequest{} }
func (m *IssOrderCalculateRequest) String() string { return proto.CompactTextString(m) }
func (*IssOrderCalculateRequest) ProtoMessage()    {}
func (*IssOrderCalculateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{1}
}

func (m *IssOrderCalculateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssOrderCalculateRequest.Unmarshal(m, b)
}
func (m *IssOrderCalculateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssOrderCalculateRequest.Marshal(b, m, deterministic)
}
func (m *IssOrderCalculateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssOrderCalculateRequest.Merge(m, src)
}
func (m *IssOrderCalculateRequest) XXX_Size() int {
	return xxx_messageInfo_IssOrderCalculateRequest.Size(m)
}
func (m *IssOrderCalculateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IssOrderCalculateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IssOrderCalculateRequest proto.InternalMessageInfo

func (m *IssOrderCalculateRequest) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *IssOrderCalculateRequest) GetAppointType() int32 {
	if m != nil {
		return m.AppointType
	}
	return 0
}

func (m *IssOrderCalculateRequest) GetAppointmentDate() string {
	if m != nil {
		return m.AppointmentDate
	}
	return ""
}

func (m *IssOrderCalculateRequest) GetStoreId() string {
	if m != nil {
		return m.StoreId
	}
	return ""
}

func (m *IssOrderCalculateRequest) GetTravelWay() int32 {
	if m != nil {
		return m.TravelWay
	}
	return 0
}

func (m *IssOrderCalculateRequest) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *IssOrderCalculateRequest) GetSender() *IssOrderCalculateSender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *IssOrderCalculateRequest) GetReceiverList() []*IssOrderCalculateReceiver {
	if m != nil {
		return m.ReceiverList
	}
	return nil
}

//订单发送信息
type IssOrderCalculateSender struct {
	//required 寄件地址
	FromAddress string `protobuf:"bytes,1,opt,name=fromAddress,proto3" json:"fromAddress"`
	//required 寄件人姓名
	FromSenderName string `protobuf:"bytes,2,opt,name=fromSenderName,proto3" json:"fromSenderName"`
	//required 寄件联系人
	FromMobile string `protobuf:"bytes,3,opt,name=fromMobile,proto3" json:"fromMobile"`
	//required 寄件纬度 只支持百度坐标系
	FromLatitude string `protobuf:"bytes,4,opt,name=fromLatitude,proto3" json:"fromLatitude"`
	//required 寄件经度  只支持百度坐标系
	FromLongitude string `protobuf:"bytes,5,opt,name=fromLongitude,proto3" json:"fromLongitude"`
	//寄件详细地址
	FromAddressDetail    string   `protobuf:"bytes,6,opt,name=fromAddressDetail,proto3" json:"fromAddressDetail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssOrderCalculateSender) Reset()         { *m = IssOrderCalculateSender{} }
func (m *IssOrderCalculateSender) String() string { return proto.CompactTextString(m) }
func (*IssOrderCalculateSender) ProtoMessage()    {}
func (*IssOrderCalculateSender) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{2}
}

func (m *IssOrderCalculateSender) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssOrderCalculateSender.Unmarshal(m, b)
}
func (m *IssOrderCalculateSender) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssOrderCalculateSender.Marshal(b, m, deterministic)
}
func (m *IssOrderCalculateSender) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssOrderCalculateSender.Merge(m, src)
}
func (m *IssOrderCalculateSender) XXX_Size() int {
	return xxx_messageInfo_IssOrderCalculateSender.Size(m)
}
func (m *IssOrderCalculateSender) XXX_DiscardUnknown() {
	xxx_messageInfo_IssOrderCalculateSender.DiscardUnknown(m)
}

var xxx_messageInfo_IssOrderCalculateSender proto.InternalMessageInfo

func (m *IssOrderCalculateSender) GetFromAddress() string {
	if m != nil {
		return m.FromAddress
	}
	return ""
}

func (m *IssOrderCalculateSender) GetFromSenderName() string {
	if m != nil {
		return m.FromSenderName
	}
	return ""
}

func (m *IssOrderCalculateSender) GetFromMobile() string {
	if m != nil {
		return m.FromMobile
	}
	return ""
}

func (m *IssOrderCalculateSender) GetFromLatitude() string {
	if m != nil {
		return m.FromLatitude
	}
	return ""
}

func (m *IssOrderCalculateSender) GetFromLongitude() string {
	if m != nil {
		return m.FromLongitude
	}
	return ""
}

func (m *IssOrderCalculateSender) GetFromAddressDetail() string {
	if m != nil {
		return m.FromAddressDetail
	}
	return ""
}

//订单收单信息
type IssOrderCalculateReceiver struct {
	//required 第三方平台流水号 也即我们平台的订单号
	OrderNo string `protobuf:"bytes,1,opt,name=orderNo,proto3" json:"orderNo"`
	//required 收件地址
	ToAddress string `protobuf:"bytes,2,opt,name=toAddress,proto3" json:"toAddress"`
	//收件详细地址
	ToAddressDetail string `protobuf:"bytes,3,opt,name=toAddressDetail,proto3" json:"toAddressDetail"`
	//required 收件纬度 只支持百度坐标系
	ToLatitude string `protobuf:"bytes,4,opt,name=toLatitude,proto3" json:"toLatitude"`
	// required 收件经度 只支持百度坐标系
	ToLongitude string `protobuf:"bytes,5,opt,name=toLongitude,proto3" json:"toLongitude"`
	// required 收件人姓名
	ToReceiverName string `protobuf:"bytes,6,opt,name=toReceiverName,proto3" json:"toReceiverName"`
	//required 收件联系人
	ToMobile string `protobuf:"bytes,7,opt,name=toMobile,proto3" json:"toMobile"`
	// required 物品类型 枚举值
	//1:文件广告 3:电子产品 5:蛋糕 6:快餐水果 7:鲜花绿植 8:海鲜水产 9:汽车配件 10:其他 11:宠物 12:母婴 13:医药健康 14:教育
	GoodType int32 `protobuf:"varint,8,opt,name=goodType,proto3" json:"goodType"`
	//required 物品重量 重量为0会报错 必须大于0； 如果是浮点则上取整整数，例如：6.7kg传7；单位为kg；重量在5kg以内默认按照5kg收费，最大重量不超过50kg
	Weight int32 `protobuf:"varint,9,opt,name=weight,proto3" json:"weight"`
	//备注
	Remarks string `protobuf:"bytes,10,opt,name=remarks,proto3" json:"remarks"`
	//小费 单位为分，能被100整除
	AdditionFee int32 `protobuf:"varint,11,opt,name=additionFee,proto3" json:"additionFee"`
	//保险费用 对应保单产品接口的amount
	Insurance int32 `protobuf:"varint,12,opt,name=insurance,proto3" json:"insurance"`
	//保险产品ID 对应保单产品的productId
	InsuranceProId string `protobuf:"bytes,13,opt,name=insuranceProId,proto3" json:"insuranceProId"`
	//物品来源 对应商家版取号来源,支持美团,饿了么；不传时默认为“闪送”，传参数时必须和orderingSourceNo成对出现 如有值，则值不能为0
	OrderingSourceType int32 `protobuf:"varint,14,opt,name=orderingSourceType,proto3" json:"orderingSourceType"`
	//物品来源流水号 对应orderingSourceType流水号，传参数时必须和orderingSourceType成对出现 如有值，则值不能为""
	OrderingSourceNo     string   `protobuf:"bytes,15,opt,name=orderingSourceNo,proto3" json:"orderingSourceNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssOrderCalculateReceiver) Reset()         { *m = IssOrderCalculateReceiver{} }
func (m *IssOrderCalculateReceiver) String() string { return proto.CompactTextString(m) }
func (*IssOrderCalculateReceiver) ProtoMessage()    {}
func (*IssOrderCalculateReceiver) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{3}
}

func (m *IssOrderCalculateReceiver) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssOrderCalculateReceiver.Unmarshal(m, b)
}
func (m *IssOrderCalculateReceiver) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssOrderCalculateReceiver.Marshal(b, m, deterministic)
}
func (m *IssOrderCalculateReceiver) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssOrderCalculateReceiver.Merge(m, src)
}
func (m *IssOrderCalculateReceiver) XXX_Size() int {
	return xxx_messageInfo_IssOrderCalculateReceiver.Size(m)
}
func (m *IssOrderCalculateReceiver) XXX_DiscardUnknown() {
	xxx_messageInfo_IssOrderCalculateReceiver.DiscardUnknown(m)
}

var xxx_messageInfo_IssOrderCalculateReceiver proto.InternalMessageInfo

func (m *IssOrderCalculateReceiver) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToAddress() string {
	if m != nil {
		return m.ToAddress
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToAddressDetail() string {
	if m != nil {
		return m.ToAddressDetail
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToLatitude() string {
	if m != nil {
		return m.ToLatitude
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToLongitude() string {
	if m != nil {
		return m.ToLongitude
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToReceiverName() string {
	if m != nil {
		return m.ToReceiverName
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetToMobile() string {
	if m != nil {
		return m.ToMobile
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

func (m *IssOrderCalculateReceiver) GetWeight() int32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *IssOrderCalculateReceiver) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetAdditionFee() int32 {
	if m != nil {
		return m.AdditionFee
	}
	return 0
}

func (m *IssOrderCalculateReceiver) GetInsurance() int32 {
	if m != nil {
		return m.Insurance
	}
	return 0
}

func (m *IssOrderCalculateReceiver) GetInsuranceProId() string {
	if m != nil {
		return m.InsuranceProId
	}
	return ""
}

func (m *IssOrderCalculateReceiver) GetOrderingSourceType() int32 {
	if m != nil {
		return m.OrderingSourceType
	}
	return 0
}

func (m *IssOrderCalculateReceiver) GetOrderingSourceNo() string {
	if m != nil {
		return m.OrderingSourceNo
	}
	return ""
}

//失新增\修改门店 请求参数
type IssStoreOperationRequest struct {
	// required 店铺名称
	StoreName string `protobuf:"bytes,1,opt,name=storeName,proto3" json:"storeName"`
	// required 店铺所在城市名称 取闪送开通城市列表即可，格式：xx市
	CityName string `protobuf:"bytes,2,opt,name=cityName,proto3" json:"cityName"`
	// required 店铺地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	// required 店铺详细地址
	AddressDetail string `protobuf:"bytes,4,opt,name=addressDetail,proto3" json:"addressDetail"`
	// required 店铺纬度 只支持百度坐标系，更新后实时显示
	Latitude string `protobuf:"bytes,5,opt,name=latitude,proto3" json:"latitude"`
	// required 店铺经度 只支持百度坐标系，更新后实时显示
	Longitude string `protobuf:"bytes,6,opt,name=longitude,proto3" json:"longitude"`
	// required 店铺联系人手机号/座机 支持手机号，支持座机号：格式 区号-座机号-分机号 如：010-1234567-123，分机号可省略
	Phone string `protobuf:"bytes,7,opt,name=phone,proto3" json:"phone"`
	// required 店铺业务类型
	//1:文件广告 3:电子产品 5:蛋糕 6:快餐水果 7:鲜花绿植 8:海鲜水产 9:汽车配件 10:其他 11:宠物 12:母婴 13:医药健康 14:教育
	GoodType int32 `protobuf:"varint,8,opt,name=goodType,proto3" json:"goodType"`
	//操作类型:新增或更新 1.保存 2.更新，默认为1
	OperationType int32 `protobuf:"varint,9,opt,name=operationType,proto3" json:"operationType"`
	// 闪送店铺id 当operationType =2 即更新时 storeId不能为空
	StoreId              string   `protobuf:"bytes,10,opt,name=storeId,proto3" json:"storeId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssStoreOperationRequest) Reset()         { *m = IssStoreOperationRequest{} }
func (m *IssStoreOperationRequest) String() string { return proto.CompactTextString(m) }
func (*IssStoreOperationRequest) ProtoMessage()    {}
func (*IssStoreOperationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{4}
}

func (m *IssStoreOperationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssStoreOperationRequest.Unmarshal(m, b)
}
func (m *IssStoreOperationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssStoreOperationRequest.Marshal(b, m, deterministic)
}
func (m *IssStoreOperationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssStoreOperationRequest.Merge(m, src)
}
func (m *IssStoreOperationRequest) XXX_Size() int {
	return xxx_messageInfo_IssStoreOperationRequest.Size(m)
}
func (m *IssStoreOperationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IssStoreOperationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IssStoreOperationRequest proto.InternalMessageInfo

func (m *IssStoreOperationRequest) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *IssStoreOperationRequest) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *IssStoreOperationRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *IssStoreOperationRequest) GetAddressDetail() string {
	if m != nil {
		return m.AddressDetail
	}
	return ""
}

func (m *IssStoreOperationRequest) GetLatitude() string {
	if m != nil {
		return m.Latitude
	}
	return ""
}

func (m *IssStoreOperationRequest) GetLongitude() string {
	if m != nil {
		return m.Longitude
	}
	return ""
}

func (m *IssStoreOperationRequest) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *IssStoreOperationRequest) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

func (m *IssStoreOperationRequest) GetOperationType() int32 {
	if m != nil {
		return m.OperationType
	}
	return 0
}

func (m *IssStoreOperationRequest) GetStoreId() string {
	if m != nil {
		return m.StoreId
	}
	return ""
}

// 订单计费 与提交订单 返回
type IssBaseResponse struct {
	//状态码 200 成功 400 失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssBaseResponse) Reset()         { *m = IssBaseResponse{} }
func (m *IssBaseResponse) String() string { return proto.CompactTextString(m) }
func (*IssBaseResponse) ProtoMessage()    {}
func (*IssBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{5}
}

func (m *IssBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssBaseResponse.Unmarshal(m, b)
}
func (m *IssBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssBaseResponse.Marshal(b, m, deterministic)
}
func (m *IssBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssBaseResponse.Merge(m, src)
}
func (m *IssBaseResponse) XXX_Size() int {
	return xxx_messageInfo_IssBaseResponse.Size(m)
}
func (m *IssBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssBaseResponse proto.InternalMessageInfo

func (m *IssBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IssBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// 订单计费 与提交订单 返回
type IssAddOrderResponse struct {
	//状态码 200 成功 400 失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//错误信息
	Error                string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *IssAddOrderData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *IssAddOrderResponse) Reset()         { *m = IssAddOrderResponse{} }
func (m *IssAddOrderResponse) String() string { return proto.CompactTextString(m) }
func (*IssAddOrderResponse) ProtoMessage()    {}
func (*IssAddOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{6}
}

func (m *IssAddOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssAddOrderResponse.Unmarshal(m, b)
}
func (m *IssAddOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssAddOrderResponse.Marshal(b, m, deterministic)
}
func (m *IssAddOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssAddOrderResponse.Merge(m, src)
}
func (m *IssAddOrderResponse) XXX_Size() int {
	return xxx_messageInfo_IssAddOrderResponse.Size(m)
}
func (m *IssAddOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssAddOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssAddOrderResponse proto.InternalMessageInfo

func (m *IssAddOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssAddOrderResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IssAddOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *IssAddOrderResponse) GetData() *IssAddOrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

//计费与提交结果详情
type IssAddOrderData struct {
	//总距离，单位米
	TotalDistance int32 `protobuf:"varint,1,opt,name=totalDistance,proto3" json:"totalDistance"`
	//总续重，单位kg
	TotalWeight int32 `protobuf:"varint,2,opt,name=totalWeight,proto3" json:"totalWeight"`
	//未优惠需要支付的费用
	TotalAmount int32 `protobuf:"varint,3,opt,name=totalAmount,proto3" json:"totalAmount"`
	//优惠的额度
	CouponSaveFee int32 `protobuf:"varint,4,opt,name=couponSaveFee,proto3" json:"couponSaveFee"`
	//实际支付的费用
	TotalFeeAfterSave int32 `protobuf:"varint,5,opt,name=totalFeeAfterSave,proto3" json:"totalFeeAfterSave"`
	//闪送订单号
	OrderNumber string `protobuf:"bytes,6,opt,name=orderNumber,proto3" json:"orderNumber"`
	//费用明细
	FeeInfoList          []*IssFeeInfo `protobuf:"bytes,7,rep,name=feeInfoList,proto3" json:"feeInfoList"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *IssAddOrderData) Reset()         { *m = IssAddOrderData{} }
func (m *IssAddOrderData) String() string { return proto.CompactTextString(m) }
func (*IssAddOrderData) ProtoMessage()    {}
func (*IssAddOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{7}
}

func (m *IssAddOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssAddOrderData.Unmarshal(m, b)
}
func (m *IssAddOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssAddOrderData.Marshal(b, m, deterministic)
}
func (m *IssAddOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssAddOrderData.Merge(m, src)
}
func (m *IssAddOrderData) XXX_Size() int {
	return xxx_messageInfo_IssAddOrderData.Size(m)
}
func (m *IssAddOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_IssAddOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_IssAddOrderData proto.InternalMessageInfo

func (m *IssAddOrderData) GetTotalDistance() int32 {
	if m != nil {
		return m.TotalDistance
	}
	return 0
}

func (m *IssAddOrderData) GetTotalWeight() int32 {
	if m != nil {
		return m.TotalWeight
	}
	return 0
}

func (m *IssAddOrderData) GetTotalAmount() int32 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *IssAddOrderData) GetCouponSaveFee() int32 {
	if m != nil {
		return m.CouponSaveFee
	}
	return 0
}

func (m *IssAddOrderData) GetTotalFeeAfterSave() int32 {
	if m != nil {
		return m.TotalFeeAfterSave
	}
	return 0
}

func (m *IssAddOrderData) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *IssAddOrderData) GetFeeInfoList() []*IssFeeInfo {
	if m != nil {
		return m.FeeInfoList
	}
	return nil
}

//计费明细
type IssFeeInfo struct {
	Fee                  int32    `protobuf:"varint,1,opt,name=fee,proto3" json:"fee"`
	Des                  string   `protobuf:"bytes,2,opt,name=des,proto3" json:"des"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssFeeInfo) Reset()         { *m = IssFeeInfo{} }
func (m *IssFeeInfo) String() string { return proto.CompactTextString(m) }
func (*IssFeeInfo) ProtoMessage()    {}
func (*IssFeeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{8}
}

func (m *IssFeeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssFeeInfo.Unmarshal(m, b)
}
func (m *IssFeeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssFeeInfo.Marshal(b, m, deterministic)
}
func (m *IssFeeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssFeeInfo.Merge(m, src)
}
func (m *IssFeeInfo) XXX_Size() int {
	return xxx_messageInfo_IssFeeInfo.Size(m)
}
func (m *IssFeeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IssFeeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IssFeeInfo proto.InternalMessageInfo

func (m *IssFeeInfo) GetFee() int32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *IssFeeInfo) GetDes() string {
	if m != nil {
		return m.Des
	}
	return ""
}

func (m *IssFeeInfo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

//失新增\修改门店 返回数据
type IssStoreOperationResponse struct {
	//状态码 200 成功 400 失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//门店id
	StoreId              int32    `protobuf:"varint,4,opt,name=storeId,proto3" json:"storeId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssStoreOperationResponse) Reset()         { *m = IssStoreOperationResponse{} }
func (m *IssStoreOperationResponse) String() string { return proto.CompactTextString(m) }
func (*IssStoreOperationResponse) ProtoMessage()    {}
func (*IssStoreOperationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{9}
}

func (m *IssStoreOperationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssStoreOperationResponse.Unmarshal(m, b)
}
func (m *IssStoreOperationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssStoreOperationResponse.Marshal(b, m, deterministic)
}
func (m *IssStoreOperationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssStoreOperationResponse.Merge(m, src)
}
func (m *IssStoreOperationResponse) XXX_Size() int {
	return xxx_messageInfo_IssStoreOperationResponse.Size(m)
}
func (m *IssStoreOperationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssStoreOperationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssStoreOperationResponse proto.InternalMessageInfo

func (m *IssStoreOperationResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssStoreOperationResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IssStoreOperationResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *IssStoreOperationResponse) GetStoreId() int32 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

//取消订单返回
type IssAbortOrderResponse struct {
	//状态码 200 成功 400 失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//错误信息
	Error                string             `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *IssAbortOrderData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *IssAbortOrderResponse) Reset()         { *m = IssAbortOrderResponse{} }
func (m *IssAbortOrderResponse) String() string { return proto.CompactTextString(m) }
func (*IssAbortOrderResponse) ProtoMessage()    {}
func (*IssAbortOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{10}
}

func (m *IssAbortOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssAbortOrderResponse.Unmarshal(m, b)
}
func (m *IssAbortOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssAbortOrderResponse.Marshal(b, m, deterministic)
}
func (m *IssAbortOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssAbortOrderResponse.Merge(m, src)
}
func (m *IssAbortOrderResponse) XXX_Size() int {
	return xxx_messageInfo_IssAbortOrderResponse.Size(m)
}
func (m *IssAbortOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssAbortOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssAbortOrderResponse proto.InternalMessageInfo

func (m *IssAbortOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssAbortOrderResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IssAbortOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *IssAbortOrderResponse) GetData() *IssAbortOrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

//去掉订单的数据详细信息
type IssAbortOrderData struct {
	//扣款金额
	DeductAmount int32 `protobuf:"varint,1,opt,name=deductAmount,proto3" json:"deductAmount"`
	//取消类型 1：因客户取消 实际情况 商品取消也会返回1 ；3：因闪送员取消 10：闪送系统自动取消
	AbortType int32 `protobuf:"varint,2,opt,name=abortType,proto3" json:"abortType"`
	//取消原因
	AbortReason          string   `protobuf:"bytes,3,opt,name=abortReason,proto3" json:"abortReason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssAbortOrderData) Reset()         { *m = IssAbortOrderData{} }
func (m *IssAbortOrderData) String() string { return proto.CompactTextString(m) }
func (*IssAbortOrderData) ProtoMessage()    {}
func (*IssAbortOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{11}
}

func (m *IssAbortOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssAbortOrderData.Unmarshal(m, b)
}
func (m *IssAbortOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssAbortOrderData.Marshal(b, m, deterministic)
}
func (m *IssAbortOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssAbortOrderData.Merge(m, src)
}
func (m *IssAbortOrderData) XXX_Size() int {
	return xxx_messageInfo_IssAbortOrderData.Size(m)
}
func (m *IssAbortOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_IssAbortOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_IssAbortOrderData proto.InternalMessageInfo

func (m *IssAbortOrderData) GetDeductAmount() int32 {
	if m != nil {
		return m.DeductAmount
	}
	return 0
}

func (m *IssAbortOrderData) GetAbortType() int32 {
	if m != nil {
		return m.AbortType
	}
	return 0
}

func (m *IssAbortOrderData) GetAbortReason() string {
	if m != nil {
		return m.AbortReason
	}
	return ""
}

//查询闪送员位置的返回数据结构
type IssCourierInfoResponse struct {
	//状态码 200 成功 400 失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//详细信息
	Data                 *IssCourierInfoData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *IssCourierInfoResponse) Reset()         { *m = IssCourierInfoResponse{} }
func (m *IssCourierInfoResponse) String() string { return proto.CompactTextString(m) }
func (*IssCourierInfoResponse) ProtoMessage()    {}
func (*IssCourierInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{12}
}

func (m *IssCourierInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssCourierInfoResponse.Unmarshal(m, b)
}
func (m *IssCourierInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssCourierInfoResponse.Marshal(b, m, deterministic)
}
func (m *IssCourierInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssCourierInfoResponse.Merge(m, src)
}
func (m *IssCourierInfoResponse) XXX_Size() int {
	return xxx_messageInfo_IssCourierInfoResponse.Size(m)
}
func (m *IssCourierInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssCourierInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssCourierInfoResponse proto.InternalMessageInfo

func (m *IssCourierInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssCourierInfoResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IssCourierInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *IssCourierInfoResponse) GetData() *IssCourierInfoData {
	if m != nil {
		return m.Data
	}
	return nil
}

//闪送员位置数据
type IssCourierInfoData struct {
	//闪送员位置纬度
	Latitude string `protobuf:"bytes,1,opt,name=latitude,proto3" json:"latitude"`
	//闪送员位置经度
	Longitude string `protobuf:"bytes,2,opt,name=longitude,proto3" json:"longitude"`
	//闪送员姓名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	//闪送员手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	//闪送员位置所处的当前时间
	Time string `protobuf:"bytes,5,opt,name=time,proto3" json:"time"`
	//骑手类型 固定值1，代表众包。可忽略该值
	Type int32 `protobuf:"varint,6,opt,name=type,proto3" json:"type"`
	//闪送员服务总次数
	OrderCount int32 `protobuf:"varint,7,opt,name=orderCount,proto3" json:"orderCount"`
	//闪送员头像
	HeadIcon string `protobuf:"bytes,8,opt,name=headIcon,proto3" json:"headIcon"`
	//骑手id
	Id string `protobuf:"bytes,9,opt,name=id,proto3" json:"id"`
	//固定值0，代表未拉黑。可忽略该值
	Blacklisted int32 `protobuf:"varint,10,opt,name=blacklisted,proto3" json:"blacklisted"`
	//预计送达时间文案
	EstimateDeliveryTimeTip string `protobuf:"bytes,11,opt,name=estimateDeliveryTimeTip,proto3" json:"estimateDeliveryTimeTip"`
	//  配送过程轨迹列表
	DeliveryProcessTrail []*IssDeliveryProcessTrail `protobuf:"bytes,12,rep,name=deliveryProcessTrail,proto3" json:"deliveryProcessTrail"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *IssCourierInfoData) Reset()         { *m = IssCourierInfoData{} }
func (m *IssCourierInfoData) String() string { return proto.CompactTextString(m) }
func (*IssCourierInfoData) ProtoMessage()    {}
func (*IssCourierInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{13}
}

func (m *IssCourierInfoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssCourierInfoData.Unmarshal(m, b)
}
func (m *IssCourierInfoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssCourierInfoData.Marshal(b, m, deterministic)
}
func (m *IssCourierInfoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssCourierInfoData.Merge(m, src)
}
func (m *IssCourierInfoData) XXX_Size() int {
	return xxx_messageInfo_IssCourierInfoData.Size(m)
}
func (m *IssCourierInfoData) XXX_DiscardUnknown() {
	xxx_messageInfo_IssCourierInfoData.DiscardUnknown(m)
}

var xxx_messageInfo_IssCourierInfoData proto.InternalMessageInfo

func (m *IssCourierInfoData) GetLatitude() string {
	if m != nil {
		return m.Latitude
	}
	return ""
}

func (m *IssCourierInfoData) GetLongitude() string {
	if m != nil {
		return m.Longitude
	}
	return ""
}

func (m *IssCourierInfoData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IssCourierInfoData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *IssCourierInfoData) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *IssCourierInfoData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *IssCourierInfoData) GetOrderCount() int32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *IssCourierInfoData) GetHeadIcon() string {
	if m != nil {
		return m.HeadIcon
	}
	return ""
}

func (m *IssCourierInfoData) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *IssCourierInfoData) GetBlacklisted() int32 {
	if m != nil {
		return m.Blacklisted
	}
	return 0
}

func (m *IssCourierInfoData) GetEstimateDeliveryTimeTip() string {
	if m != nil {
		return m.EstimateDeliveryTimeTip
	}
	return ""
}

func (m *IssCourierInfoData) GetDeliveryProcessTrail() []*IssDeliveryProcessTrail {
	if m != nil {
		return m.DeliveryProcessTrail
	}
	return nil
}

//送货员轨迹数据
type IssDeliveryProcessTrail struct {
	//闪送员位置经度
	Latitude float32 `protobuf:"fixed32,1,opt,name=latitude,proto3" json:"latitude"`
	//闪送员位置纬度
	Longitude float32 `protobuf:"fixed32,2,opt,name=longitude,proto3" json:"longitude"`
	//闪送员位置所处的当前时间
	Datetime             string   `protobuf:"bytes,3,opt,name=datetime,proto3" json:"datetime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssDeliveryProcessTrail) Reset()         { *m = IssDeliveryProcessTrail{} }
func (m *IssDeliveryProcessTrail) String() string { return proto.CompactTextString(m) }
func (*IssDeliveryProcessTrail) ProtoMessage()    {}
func (*IssDeliveryProcessTrail) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b9d8ef2fd28ee53, []int{14}
}

func (m *IssDeliveryProcessTrail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssDeliveryProcessTrail.Unmarshal(m, b)
}
func (m *IssDeliveryProcessTrail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssDeliveryProcessTrail.Marshal(b, m, deterministic)
}
func (m *IssDeliveryProcessTrail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssDeliveryProcessTrail.Merge(m, src)
}
func (m *IssDeliveryProcessTrail) XXX_Size() int {
	return xxx_messageInfo_IssDeliveryProcessTrail.Size(m)
}
func (m *IssDeliveryProcessTrail) XXX_DiscardUnknown() {
	xxx_messageInfo_IssDeliveryProcessTrail.DiscardUnknown(m)
}

var xxx_messageInfo_IssDeliveryProcessTrail proto.InternalMessageInfo

func (m *IssDeliveryProcessTrail) GetLatitude() float32 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *IssDeliveryProcessTrail) GetLongitude() float32 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *IssDeliveryProcessTrail) GetDatetime() string {
	if m != nil {
		return m.Datetime
	}
	return ""
}

func init() {
	proto.RegisterType((*IssOrderNoRequest)(nil), "et.IssOrderNoRequest")
	proto.RegisterType((*IssOrderCalculateRequest)(nil), "et.IssOrderCalculateRequest")
	proto.RegisterType((*IssOrderCalculateSender)(nil), "et.IssOrderCalculateSender")
	proto.RegisterType((*IssOrderCalculateReceiver)(nil), "et.IssOrderCalculateReceiver")
	proto.RegisterType((*IssStoreOperationRequest)(nil), "et.IssStoreOperationRequest")
	proto.RegisterType((*IssBaseResponse)(nil), "et.IssBaseResponse")
	proto.RegisterType((*IssAddOrderResponse)(nil), "et.IssAddOrderResponse")
	proto.RegisterType((*IssAddOrderData)(nil), "et.IssAddOrderData")
	proto.RegisterType((*IssFeeInfo)(nil), "et.IssFeeInfo")
	proto.RegisterType((*IssStoreOperationResponse)(nil), "et.IssStoreOperationResponse")
	proto.RegisterType((*IssAbortOrderResponse)(nil), "et.IssAbortOrderResponse")
	proto.RegisterType((*IssAbortOrderData)(nil), "et.IssAbortOrderData")
	proto.RegisterType((*IssCourierInfoResponse)(nil), "et.IssCourierInfoResponse")
	proto.RegisterType((*IssCourierInfoData)(nil), "et.IssCourierInfoData")
	proto.RegisterType((*IssDeliveryProcessTrail)(nil), "et.IssDeliveryProcessTrail")
}

func init() { proto.RegisterFile("et/externalIShanSong.proto", fileDescriptor_9b9d8ef2fd28ee53) }

var fileDescriptor_9b9d8ef2fd28ee53 = []byte{
	// 1248 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0xcd, 0x6e, 0x1b, 0xb7,
	0x13, 0x87, 0x64, 0xcb, 0xb6, 0x46, 0x8e, 0x93, 0x30, 0x5f, 0x1b, 0xff, 0x93, 0x3f, 0x8c, 0x45,
	0xd0, 0xba, 0x41, 0xe1, 0x16, 0xce, 0xa5, 0x87, 0xa2, 0xa8, 0x6b, 0x21, 0x81, 0x80, 0x7c, 0x61,
	0x15, 0x20, 0x67, 0x7a, 0x77, 0x2c, 0x13, 0xd9, 0x5d, 0x2a, 0x24, 0xe5, 0xd4, 0x3d, 0x14, 0x7d,
	0x89, 0x9e, 0x7a, 0xec, 0xb5, 0x0f, 0xd1, 0xa7, 0xe8, 0xcb, 0xf4, 0x52, 0x70, 0x48, 0xae, 0xb8,
	0xfa, 0xc8, 0xc9, 0xb7, 0x9d, 0x1f, 0x87, 0xe4, 0x7c, 0xfc, 0x66, 0x86, 0x0b, 0xfb, 0x68, 0xbe,
	0xc1, 0x9f, 0x0d, 0xaa, 0x9a, 0x97, 0xa3, 0xf1, 0x05, 0xaf, 0xc7, 0xb2, 0x9e, 0x1c, 0x4d, 0x95,
	0x34, 0x92, 0x75, 0xd1, 0xa4, 0xcf, 0xe0, 0xf6, 0x48, 0xeb, 0x37, 0xaa, 0x40, 0xf5, 0x5a, 0x66,
	0xf8, 0x71, 0x86, 0xda, 0xb0, 0xff, 0x03, 0x88, 0x06, 0x4c, 0x3a, 0x07, 0x9d, 0xc3, 0x7e, 0x16,
	0x21, 0xe9, 0x3f, 0x5d, 0x48, 0xc2, 0xae, 0x53, 0x5e, 0xe6, 0xb3, 0x92, 0x1b, 0x0c, 0x9b, 0xf7,
	0x61, 0x27, 0x17, 0xe6, 0xea, 0x35, 0xaf, 0xd0, 0x6f, 0x6d, 0x64, 0x76, 0x00, 0x03, 0x3e, 0x9d,
	0x4a, 0x51, 0x9b, 0x77, 0x57, 0x53, 0x4c, 0xba, 0x07, 0x9d, 0xc3, 0x5e, 0x16, 0x43, 0xec, 0x10,
	0x6e, 0x7a, 0xb1, 0xc2, 0xda, 0x0c, 0xb9, 0xc1, 0x64, 0x83, 0x0e, 0x59, 0x84, 0x59, 0x02, 0xdb,
	0xda, 0x48, 0x85, 0xa3, 0x22, 0xd9, 0x24, 0x8d, 0x20, 0xb2, 0x47, 0xd0, 0x37, 0x8a, 0x5f, 0x62,
	0xf9, 0x9e, 0x5f, 0x25, 0x3d, 0xba, 0x63, 0x0e, 0xb0, 0x14, 0x76, 0x0b, 0x2c, 0xc5, 0x25, 0xaa,
	0x2b, 0x32, 0x62, 0x8b, 0x14, 0x5a, 0x18, 0x7b, 0x06, 0x5b, 0x1a, 0xeb, 0x02, 0x55, 0xb2, 0x7d,
	0xd0, 0x39, 0x1c, 0x1c, 0xff, 0xef, 0x08, 0xcd, 0xd1, 0x92, 0xc7, 0x63, 0x52, 0xc9, 0xbc, 0x2a,
	0x3b, 0x81, 0x5d, 0x85, 0x39, 0xda, 0x53, 0x5e, 0x0a, 0x6d, 0x92, 0x9d, 0x83, 0x8d, 0xc3, 0xc1,
	0xf1, 0xe3, 0x95, 0x5b, 0x33, 0xaf, 0x98, 0xb5, 0xb6, 0xa4, 0xff, 0x76, 0xe0, 0xc1, 0x9a, 0x6b,
	0x6c, 0xec, 0xce, 0x95, 0xac, 0x4e, 0x8a, 0x42, 0xa1, 0xd6, 0x3e, 0xb4, 0x31, 0xc4, 0xbe, 0x80,
	0x3d, 0x2b, 0x3a, 0x7d, 0x8a, 0x7f, 0x97, 0x94, 0x16, 0x50, 0x9b, 0x5e, 0x8b, 0xbc, 0x92, 0x67,
	0xa2, 0x0c, 0xe1, 0x8d, 0x10, 0x1b, 0x21, 0x2b, 0xbd, 0xe4, 0x46, 0x98, 0x59, 0x81, 0x3e, 0xbc,
	0x2d, 0x8c, 0x3d, 0x81, 0x1b, 0x24, 0xcb, 0x7a, 0xe2, 0x94, 0x7a, 0xa4, 0xd4, 0x06, 0xd9, 0xd7,
	0x70, 0x3b, 0x32, 0x70, 0x88, 0x86, 0x8b, 0x92, 0x02, 0xde, 0xcf, 0x96, 0x17, 0xd2, 0xdf, 0x37,
	0xe1, 0xe1, 0xda, 0x48, 0xd9, 0x7c, 0xcb, 0x16, 0x23, 0x83, 0x48, 0xf9, 0x96, 0x21, 0x2e, 0xce,
	0xe5, 0x39, 0x60, 0x19, 0xd5, 0x08, 0xde, 0x02, 0xcf, 0xa8, 0x05, 0xd8, 0xc6, 0xc5, 0xc8, 0x05,
	0xaf, 0x23, 0xc4, 0x66, 0xc0, 0xc8, 0x45, 0x8f, 0x63, 0xc8, 0x66, 0xc0, 0xc8, 0x60, 0x31, 0x65,
	0xc0, 0x39, 0xbb, 0x80, 0xda, 0x1a, 0x31, 0xd2, 0xc7, 0x7f, 0xdb, 0xd5, 0x48, 0x90, 0xed, 0xda,
	0x44, 0xca, 0x82, 0xb8, 0xb9, 0x43, 0xdc, 0x6c, 0x64, 0x76, 0x1f, 0xb6, 0x3e, 0xa1, 0x98, 0x5c,
	0x98, 0xa4, 0x4f, 0x2b, 0x5e, 0xb2, 0xb1, 0x51, 0x58, 0x71, 0xf5, 0x41, 0x27, 0xe0, 0x62, 0xe3,
	0x45, 0xaa, 0xb8, 0xa2, 0x10, 0x46, 0xc8, 0xfa, 0x39, 0x62, 0x32, 0xf0, 0x15, 0x37, 0x87, 0x6c,
	0xf4, 0x44, 0xad, 0x67, 0x8a, 0xd7, 0x39, 0x26, 0xbb, 0xae, 0x5a, 0x1a, 0xc0, 0x7a, 0xd4, 0x08,
	0x6f, 0x95, 0x1c, 0x15, 0xc9, 0x0d, 0xe7, 0x51, 0x1b, 0x65, 0x47, 0xc0, 0x28, 0x1d, 0xa2, 0x9e,
	0x8c, 0xe5, 0x4c, 0xe5, 0x48, 0xf6, 0xef, 0xd1, 0x71, 0x2b, 0x56, 0xd8, 0x53, 0xb8, 0xd5, 0x46,
	0x5f, 0xcb, 0xe4, 0x26, 0x9d, 0xbc, 0x84, 0xa7, 0x7f, 0xbb, 0x76, 0x33, 0xb6, 0xe5, 0xfd, 0x66,
	0x8a, 0x8a, 0x5b, 0xd3, 0x43, 0xbb, 0x79, 0x04, 0x7d, 0xaa, 0xfb, 0xa8, 0xdf, 0xcc, 0x81, 0x56,
	0x33, 0xea, 0x2e, 0x34, 0xa3, 0x04, 0xb6, 0xb9, 0x27, 0x8d, 0x23, 0x44, 0x10, 0x2d, 0xb9, 0x79,
	0x8b, 0x30, 0x8e, 0x0b, 0x6d, 0xd0, 0x9e, 0x5d, 0x06, 0xb2, 0x38, 0x2e, 0x34, 0xb2, 0xb5, 0xaa,
	0x6c, 0x88, 0xe2, 0x38, 0x30, 0x07, 0xd8, 0x5d, 0xe8, 0x4d, 0x2f, 0x64, 0x1d, 0x72, 0xef, 0x84,
	0xcf, 0x26, 0xfe, 0x09, 0xdc, 0x90, 0xc1, 0x73, 0x52, 0x70, 0xf9, 0x6f, 0x83, 0x71, 0x4b, 0x84,
	0x56, 0x4b, 0x4c, 0x5f, 0xc1, 0xcd, 0x91, 0xd6, 0x3f, 0x71, 0x8d, 0x19, 0xea, 0xa9, 0xac, 0x35,
	0x32, 0x06, 0x9b, 0xb9, 0x2c, 0x5c, 0xcc, 0x7a, 0x19, 0x7d, 0xb3, 0x5b, 0xb0, 0x51, 0xe9, 0x89,
	0x8f, 0x94, 0xfd, 0xb4, 0xa6, 0xa2, 0x52, 0x52, 0xf9, 0x10, 0x39, 0x21, 0xfd, 0x05, 0xee, 0x8c,
	0xb4, 0x3e, 0x29, 0x0a, 0xaa, 0xd5, 0xeb, 0x38, 0x92, 0x7d, 0x09, 0x9b, 0x05, 0x37, 0x9c, 0x42,
	0x3d, 0x38, 0xbe, 0xe3, 0xbb, 0x66, 0xb8, 0x62, 0xc8, 0x0d, 0xcf, 0x48, 0x21, 0xfd, 0xb3, 0x4b,
	0xbe, 0xc4, 0x2b, 0x36, 0x3c, 0x46, 0x1a, 0x5e, 0x0e, 0x85, 0x36, 0xc4, 0x63, 0x67, 0x41, 0x1b,
	0x74, 0xf5, 0x6b, 0x78, 0xf9, 0xde, 0x95, 0x90, 0x9f, 0x3e, 0x11, 0xd4, 0x68, 0x9c, 0x54, 0x72,
	0x56, 0x1b, 0x32, 0x30, 0x68, 0x38, 0xc8, 0xde, 0x94, 0xcb, 0xd9, 0x54, 0xd6, 0x63, 0x7e, 0x89,
	0xb6, 0xa2, 0x36, 0xdd, 0x4d, 0x2d, 0xd0, 0xf6, 0x3d, 0xda, 0xf4, 0x1c, 0xf1, 0xe4, 0xdc, 0xa0,
	0xb2, 0xb8, 0x9f, 0x44, 0xcb, 0x0b, 0xf6, 0x56, 0xd7, 0xca, 0x66, 0xd5, 0x19, 0x2a, 0x4f, 0x97,
	0x18, 0x62, 0xdf, 0xc2, 0xe0, 0x1c, 0x71, 0x54, 0x9f, 0x4b, 0x9a, 0x2c, 0xdb, 0x34, 0x59, 0xf6,
	0x7c, 0x8c, 0x9e, 0xbb, 0x95, 0x2c, 0x56, 0x49, 0x87, 0x00, 0xf3, 0x25, 0x9b, 0x84, 0x73, 0x0c,
	0x51, 0xb1, 0x9f, 0x16, 0x29, 0x30, 0x74, 0x4b, 0xfb, 0x69, 0x93, 0x67, 0x2c, 0xb3, 0x9c, 0xd3,
	0xf4, 0x9d, 0x7e, 0xa4, 0x86, 0xbc, 0x58, 0x78, 0xd7, 0x90, 0xed, 0x85, 0xe1, 0xdd, 0x9b, 0x33,
	0xf5, 0x57, 0xb8, 0x67, 0xb3, 0x7b, 0x26, 0x95, 0xb9, 0x3e, 0x72, 0x7d, 0xd5, 0x22, 0xd7, 0xbd,
	0x40, 0xae, 0xe6, 0x92, 0x88, 0x5e, 0x9f, 0xe8, 0x41, 0xd4, 0x5e, 0x72, 0x6f, 0x86, 0x62, 0x96,
	0x1b, 0x4f, 0x8c, 0x4e, 0x78, 0x33, 0xcc, 0x31, 0x5b, 0xf2, 0xdc, 0xee, 0x8a, 0x5e, 0x36, 0x73,
	0x80, 0xfa, 0xb0, 0x15, 0x32, 0xe4, 0x5a, 0xd6, 0xde, 0xba, 0x18, 0x4a, 0x7f, 0xeb, 0xc0, 0xfd,
	0x91, 0xd6, 0xa7, 0x72, 0xa6, 0x04, 0x2a, 0xca, 0xe8, 0x75, 0xb8, 0xfe, 0xb4, 0xe5, 0xfa, 0x7d,
	0xef, 0x7a, 0x74, 0x4b, 0xe4, 0xfb, 0x1f, 0x1b, 0xc0, 0x96, 0x17, 0x5b, 0x8d, 0xae, 0xf3, 0xb9,
	0x46, 0xd7, 0x5d, 0x6c, 0x74, 0x0c, 0x36, 0x6b, 0xdb, 0x7a, 0x9d, 0x45, 0xf4, 0x6d, 0x67, 0x58,
	0xe5, 0x26, 0x9f, 0xeb, 0xaa, 0x5e, 0x22, 0xfe, 0x89, 0x2a, 0xb4, 0x52, 0xfa, 0x6e, 0x38, 0xb9,
	0x35, 0xe7, 0xa4, 0x9d, 0xd2, 0x54, 0x1a, 0xa7, 0x94, 0x89, 0x6d, 0x5a, 0x89, 0x10, 0x6b, 0xed,
	0x05, 0xf2, 0x62, 0x94, 0xcb, 0x9a, 0xda, 0x68, 0x3f, 0x6b, 0x64, 0xb6, 0x07, 0x5d, 0x51, 0x50,
	0xef, 0xec, 0x67, 0x5d, 0x51, 0xd8, 0xac, 0x9c, 0x95, 0x3c, 0xff, 0x50, 0x0a, 0x6d, 0xd0, 0x35,
	0xcd, 0x5e, 0x16, 0x43, 0xec, 0x3b, 0x78, 0x80, 0xda, 0x88, 0x8a, 0x1b, 0x1c, 0x86, 0x17, 0xa2,
	0xa8, 0xf0, 0x9d, 0x98, 0xd2, 0x2c, 0xed, 0x67, 0xeb, 0x96, 0xd9, 0x1b, 0xb8, 0x1b, 0xde, 0x94,
	0x6f, 0x95, 0xcc, 0x51, 0xeb, 0x77, 0xca, 0xce, 0x92, 0x5d, 0x2a, 0xde, 0xf0, 0xa2, 0x1c, 0xae,
	0x50, 0xc9, 0x56, 0x6e, 0x4c, 0x25, 0xbd, 0x0d, 0x57, 0x6d, 0x58, 0xca, 0x50, 0xf7, 0x73, 0x19,
	0xea, 0xc6, 0x19, 0xda, 0x87, 0x9d, 0x82, 0x1b, 0xa4, 0xc8, 0xbb, 0x2c, 0x35, 0xf2, 0xf1, 0x5f,
	0x1b, 0x70, 0xab, 0xf9, 0x67, 0x18, 0xa3, 0xba, 0x14, 0x39, 0xb2, 0x17, 0xb0, 0xd7, 0x7e, 0xa0,
	0xb1, 0x47, 0x6b, 0x5e, 0xb8, 0x34, 0x9f, 0xf7, 0x1f, 0x2c, 0x74, 0xf2, 0x86, 0xd4, 0xdf, 0x03,
	0x10, 0xf0, 0xb6, 0xe4, 0x39, 0xb2, 0x7b, 0xf1, 0x21, 0xcd, 0x9f, 0xc8, 0xfa, 0xdd, 0xaf, 0x60,
	0xaf, 0xdd, 0x96, 0x1a, 0x33, 0x56, 0x3e, 0x13, 0xf6, 0x1f, 0xaf, 0x59, 0xf5, 0xc7, 0xfd, 0x00,
	0x30, 0x2f, 0xf9, 0x75, 0xc6, 0x3c, 0x5c, 0xea, 0x1b, 0xcd, 0xfe, 0x1f, 0x61, 0x10, 0x55, 0xcd,
	0xba, 0x03, 0xf6, 0x97, 0xab, 0x2f, 0x3a, 0x81, 0x9d, 0xca, 0xfa, 0x5c, 0xa8, 0xea, 0x85, 0x94,
	0x85, 0xce, 0xd0, 0xcc, 0x54, 0xbd, 0xee, 0xa0, 0x30, 0x1e, 0xe3, 0x81, 0x7e, 0xb6, 0x45, 0x7f,
	0x75, 0xcf, 0xfe, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xf7, 0xef, 0xe2, 0x8a, 0xf3, 0x0d, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IShanSongServiceClient is the client API for IShanSongService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IShanSongServiceClient interface {
	// 订单计费
	//关于寄件人/收件人联系方式的说明：
	//fromMobile字段toMobile字段都支持座机号，正常手机号。toMbile字段支持隐私号，座机号，手机号
	//1：隐私号格式 18701012345#001
	//2：座机号格式 010-12345678 或者010-12345678-123
	//3：正常手机号格式 11位
	//关于物品来源的说明：
	//orderingSourceNo字段orderingSourceType字段是为了方便骑手店里取餐方便提供的字段。
	//1：两个字段非必传。
	//2：两个字段必须成对出现。
	//3：orderingSourceType的枚举参考开发指南"物品来源枚举"
	//4：如果填写该字段会在闪送客户端展示订单取号标记。比如美团#001号，饿了么#002号。如果不传递此参数，那么默认为闪送。
	OrderCalculate(ctx context.Context, in *IssOrderCalculateRequest, opts ...grpc.CallOption) (*IssAddOrderResponse, error)
	// 提交订单
	//issOrderNo是计费接口返回的闪送订单编号
	OrderPlace(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssAddOrderResponse, error)
	// 失新增\修改门店
	StoreOperation(ctx context.Context, in *IssStoreOperationRequest, opts ...grpc.CallOption) (*IssStoreOperationResponse, error)
	// 取消订单
	AbortOrder(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssAbortOrderResponse, error)
	// 查询闪送员位置
	CourierInfo(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssCourierInfoResponse, error)
	// 确认物品送回接口
	ConfirmGoodsReturn(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssBaseResponse, error)
}

type iShanSongServiceClient struct {
	cc *grpc.ClientConn
}

func NewIShanSongServiceClient(cc *grpc.ClientConn) IShanSongServiceClient {
	return &iShanSongServiceClient{cc}
}

func (c *iShanSongServiceClient) OrderCalculate(ctx context.Context, in *IssOrderCalculateRequest, opts ...grpc.CallOption) (*IssAddOrderResponse, error) {
	out := new(IssAddOrderResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/OrderCalculate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iShanSongServiceClient) OrderPlace(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssAddOrderResponse, error) {
	out := new(IssAddOrderResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/OrderPlace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iShanSongServiceClient) StoreOperation(ctx context.Context, in *IssStoreOperationRequest, opts ...grpc.CallOption) (*IssStoreOperationResponse, error) {
	out := new(IssStoreOperationResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/StoreOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iShanSongServiceClient) AbortOrder(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssAbortOrderResponse, error) {
	out := new(IssAbortOrderResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/AbortOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iShanSongServiceClient) CourierInfo(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssCourierInfoResponse, error) {
	out := new(IssCourierInfoResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/CourierInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iShanSongServiceClient) ConfirmGoodsReturn(ctx context.Context, in *IssOrderNoRequest, opts ...grpc.CallOption) (*IssBaseResponse, error) {
	out := new(IssBaseResponse)
	err := c.cc.Invoke(ctx, "/et.IShanSongService/ConfirmGoodsReturn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IShanSongServiceServer is the server API for IShanSongService service.
type IShanSongServiceServer interface {
	// 订单计费
	//关于寄件人/收件人联系方式的说明：
	//fromMobile字段toMobile字段都支持座机号，正常手机号。toMbile字段支持隐私号，座机号，手机号
	//1：隐私号格式 18701012345#001
	//2：座机号格式 010-12345678 或者010-12345678-123
	//3：正常手机号格式 11位
	//关于物品来源的说明：
	//orderingSourceNo字段orderingSourceType字段是为了方便骑手店里取餐方便提供的字段。
	//1：两个字段非必传。
	//2：两个字段必须成对出现。
	//3：orderingSourceType的枚举参考开发指南"物品来源枚举"
	//4：如果填写该字段会在闪送客户端展示订单取号标记。比如美团#001号，饿了么#002号。如果不传递此参数，那么默认为闪送。
	OrderCalculate(context.Context, *IssOrderCalculateRequest) (*IssAddOrderResponse, error)
	// 提交订单
	//issOrderNo是计费接口返回的闪送订单编号
	OrderPlace(context.Context, *IssOrderNoRequest) (*IssAddOrderResponse, error)
	// 失新增\修改门店
	StoreOperation(context.Context, *IssStoreOperationRequest) (*IssStoreOperationResponse, error)
	// 取消订单
	AbortOrder(context.Context, *IssOrderNoRequest) (*IssAbortOrderResponse, error)
	// 查询闪送员位置
	CourierInfo(context.Context, *IssOrderNoRequest) (*IssCourierInfoResponse, error)
	// 确认物品送回接口
	ConfirmGoodsReturn(context.Context, *IssOrderNoRequest) (*IssBaseResponse, error)
}

// UnimplementedIShanSongServiceServer can be embedded to have forward compatible implementations.
type UnimplementedIShanSongServiceServer struct {
}

func (*UnimplementedIShanSongServiceServer) OrderCalculate(ctx context.Context, req *IssOrderCalculateRequest) (*IssAddOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderCalculate not implemented")
}
func (*UnimplementedIShanSongServiceServer) OrderPlace(ctx context.Context, req *IssOrderNoRequest) (*IssAddOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderPlace not implemented")
}
func (*UnimplementedIShanSongServiceServer) StoreOperation(ctx context.Context, req *IssStoreOperationRequest) (*IssStoreOperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreOperation not implemented")
}
func (*UnimplementedIShanSongServiceServer) AbortOrder(ctx context.Context, req *IssOrderNoRequest) (*IssAbortOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AbortOrder not implemented")
}
func (*UnimplementedIShanSongServiceServer) CourierInfo(ctx context.Context, req *IssOrderNoRequest) (*IssCourierInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CourierInfo not implemented")
}
func (*UnimplementedIShanSongServiceServer) ConfirmGoodsReturn(ctx context.Context, req *IssOrderNoRequest) (*IssBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmGoodsReturn not implemented")
}

func RegisterIShanSongServiceServer(s *grpc.Server, srv IShanSongServiceServer) {
	s.RegisterService(&_IShanSongService_serviceDesc, srv)
}

func _IShanSongService_OrderCalculate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssOrderCalculateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).OrderCalculate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/OrderCalculate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).OrderCalculate(ctx, req.(*IssOrderCalculateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IShanSongService_OrderPlace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssOrderNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).OrderPlace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/OrderPlace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).OrderPlace(ctx, req.(*IssOrderNoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IShanSongService_StoreOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssStoreOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).StoreOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/StoreOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).StoreOperation(ctx, req.(*IssStoreOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IShanSongService_AbortOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssOrderNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).AbortOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/AbortOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).AbortOrder(ctx, req.(*IssOrderNoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IShanSongService_CourierInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssOrderNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).CourierInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/CourierInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).CourierInfo(ctx, req.(*IssOrderNoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IShanSongService_ConfirmGoodsReturn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssOrderNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IShanSongServiceServer).ConfirmGoodsReturn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.IShanSongService/ConfirmGoodsReturn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IShanSongServiceServer).ConfirmGoodsReturn(ctx, req.(*IssOrderNoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _IShanSongService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.IShanSongService",
	HandlerType: (*IShanSongServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OrderCalculate",
			Handler:    _IShanSongService_OrderCalculate_Handler,
		},
		{
			MethodName: "OrderPlace",
			Handler:    _IShanSongService_OrderPlace_Handler,
		},
		{
			MethodName: "StoreOperation",
			Handler:    _IShanSongService_StoreOperation_Handler,
		},
		{
			MethodName: "AbortOrder",
			Handler:    _IShanSongService_AbortOrder_Handler,
		},
		{
			MethodName: "CourierInfo",
			Handler:    _IShanSongService_CourierInfo_Handler,
		},
		{
			MethodName: "ConfirmGoodsReturn",
			Handler:    _IShanSongService_ConfirmGoodsReturn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalIShanSong.proto",
}

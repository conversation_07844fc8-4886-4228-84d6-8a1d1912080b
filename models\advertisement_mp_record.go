package models

import "time"

//小程序支付回调通知数据实体
type AdvertisementMpRecord struct {
	Id         int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn    string    `xorm:"not null pk comment('订单编号') VARCHAR(36)"`
	ChannelId  int32     `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') int(3)"`
	UserId     string    `xorm:"default '''' comment('用户id') VARCHAR(55)"`
	Url        string    `xorm:"default '''' comment('转化行为的URL，没有则为http://www.qq.com') VARCHAR(55)"`
	ClickId    string    `xorm:"default '''' comment('落地页的click_id') VARCHAR(55)"`
	ActionType int32     `xorm:"not null default 0 comment('转化行为类型 1 下单 2 付费销售线索 3 外卖页面访问 4 下单 5 付费') int(3)"`
	PushJson   string    `xorm:"not null comment('mt 提交订单json 数据') TEXT "`
	DealNum    int32     `xorm:"not null default 0 comment('处理次数') INT(11)"`
	DealStatus int32     `xorm:"not null default 0 comment('处理状态') TINYINT(4)"`
	DealMsg    string    `xorm:"default 'NULL' comment('处理提示') VARCHAR(255)"`
	CreateTime time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME created"`
	UpdateTime time.Time `xorm:"default 'NULL' comment('更新时间') DATETIME updated"`
}

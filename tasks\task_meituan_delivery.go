package tasks

import (
	"bytes"
	"context"
	"strings"
	"time"

	"order-center/models"
	"order-center/proto/oc"
	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

type TaskMeiTuanDelivery struct {
	services.BaseService
}

// 预订单推送美团配送定时任务
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */1 * * * *", func() {
		service := TaskMeiTuanDelivery{}
		service.TaskMeiTuanDelivery()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}

	if _, err := task.AddFunc("0 */5 * * * *", func() {
		service := TaskMeiTuanDelivery{}
		service.TaskMeiTuanDeliveryAgain()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

// 预订单，提前45分提交美团配送订单
func (s TaskMeiTuanDelivery) TaskMeiTuanDelivery() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:delivery_mt"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	engine := services.GetDBConn()

	now := time.Now()
	m, _ := time.ParseDuration("45m")
	expectedTime := kit.GetTimeNow(now.Add(m))

	var orderList []*models.OrderMain
	engine.SQL(`
		SELECT o.id,o.order_sn,o.warehouse_id,po.old_order_sn,o.parent_order_sn,o.order_status,o.order_status_child,o.shop_id,o.shop_name,o.warehouse_code,o.warehouse_name,o.member_id,o.member_name,o.member_tel,o.receiver_name,o.receiver_state,o.receiver_city,o.receiver_district,o.receiver_address,o.receiver_phone,o.en_receiver_phone,o.receiver_mobile,o.total,o.goods_total,o.privilege,o.combine_privilege,o.freight,o.freight_privilege,o.packing_cost,o.service_charge,o.refund_amount,o.total_weight,o.is_pay,o.pay_time,o.pay_sn,o.pay_mode,o.pay_amount,o.confirm_time,o.deliver_time,o.cancel_time,o.cancel_reason,o.order_type,o.source,o.delivery_type,o.logistics_code,o.channel_id,o.user_agent,o.is_virtual,o.create_time,o.update_time,o.is_push_tencent,o.app_channel,o.order_pay_type,o.lng,o.lat,o.org_id
		FROM order_main o
		INNER JOIN order_detail ON o.order_sn = order_detail.order_sn
		LEFT JOIN order_exception ON order_exception.order_sn = o.order_sn
		INNER JOIN order_main po on po.order_sn = o.parent_order_sn
		WHERE o.channel_id != 5 
		AND o.order_type = 2 
		AND o.order_status = 20 
		AND o.parent_order_sn>0 
		AND o.is_virtual=0 
		AND o.delivery_type = 2
		AND order_detail.push_delivery = 0 
		AND order_detail.push_third_order = 1 
		And order_detail.pickup_station_id = 0
		AND ISNULL(order_exception.order_sn)
		AND order_detail.expected_time between ? and ?;
	`, kit.GetTimeNow(now), expectedTime).Find(&orderList)
	if len(orderList) == 0 {
		return
	}

	var err error
	var orderLogs []*models.OrderLog
	for _, i2 := range orderList {
		func() {
			session := engine.NewSession()
			defer session.Close()

			session.Begin()
			glog.Info("预订单提前45分钟自动推送！", i2.OldOrderSn)
			if i2.ChannelId == services.ChannelElmId || i2.ChannelId == services.ChannelMtId {
				if i2.LogisticsCode == "6" && i2.ChannelId == services.ChannelElmId {
					//美团配送
					services.PushMpOrder(session, i2)
				}
				if i2.ChannelId == services.ChannelMtId && !strings.Contains("2002,1001,1004,2010,3001,1007", i2.LogisticsCode) {
					//美团配送
					services.PushMpOrder(session, i2)
				}

			} else if i2.ChannelId == services.ChannelJddjId {
				//自配
				if i2.LogisticsCode == "2938" {
					//发配送前调用拣货完成且商家自送接口
					services.PushMpOrder(session, i2)
				}
				_, err = session.Where("order_sn = ?", i2.OrderSn).Update(&models.OrderDetail{
					IsPicking:   1,
					PickingTime: time.Now(),
				})
				if err != nil {
					glog.Error("预订单提前45分钟更新京东到家拣货失败！", i2.OldOrderSn)
					session.Rollback()
					return
				}

				orderLogs = append(orderLogs, &models.OrderLog{
					OrderSn: i2.OrderSn,
					LogType: models.OrderLogPickedOrder,
				})
			} else {
				err := services.PushMpOrder(session, i2)
				if err != nil {
					glog.Error("预订单提前45分钟自动推送失败！", i2.OldOrderSn, err)
					session.Rollback()
					return
				}
			}
			session.Commit()

			if i2.ChannelId == services.ChannelJddjId {
				//自配
				if i2.LogisticsCode == "2938" {
					storeMasterId := i2.AppChannel
					//发配送前调用拣货完成且商家自送接口
					err = services.JddjOrderSerllerDelivery(i2.OldOrderSn, storeMasterId)
					if err != nil {
						glog.Error("预订单提前45分钟自动推送京东到家拣货完成且商家自送接口失败！", i2.OldOrderSn, err)
						return
					}
				}
				//京东众包
				if i2.LogisticsCode == "9966" {
					storeMasterId := i2.AppChannel
					err = services.JddjOrderJDZBDelivery(i2.OldOrderSn, storeMasterId)
					if err != nil {
						glog.Error("预订单提前45分钟自动京东到家拣货完成且众包配送接口失败！", i2.OldOrderSn, err)
						return
					}
				}
			}
		}()
	}
}

// 预订单配送错误是不在门店营业时间的要自动重新发起配送 5分钟一次
func (s TaskMeiTuanDelivery) TaskMeiTuanDeliveryAgain() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:delivery_again_mt"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	engine := services.GetDBConn()

	var item []models.OrderExceptionCombine
	var buffer bytes.Buffer
	buffer.WriteString(`
		select 
		oe.*,a.order_sn as oid,a.old_order_sn 
		from order_exception oe 
		inner join order_main a on a.order_sn = oe.order_sn
		where a.order_status=20
		and a.order_type = 2 
		and oe.is_show=1
		and (oe.exception_descr = '快速达:不在门店营业时间内' or oe.exception_descr = '自由达:不在门店营业时间内')
	`)
	engine.SQL(buffer.String()).Find(&item)
	if len(item) == 0 {
		return
	}

	for _, i2 := range item {
		services := services.OrderExceptionService{}
		glog.Info("预订单配送错误是不在门店营业时间的要自动重新发起配送 5分钟一次！", i2.OldOrderSn)
		res, err := services.DistributionAgain(context.Background(), &oc.ExceptionOrderStatusRequest{
			OrderId:    i2.OrderSn,
			DeliveryId: i2.DeliveryId,
		})

		if err != nil {
			glog.Error("预订单不在门店营业时间再次发起配送错误：", i2.OldOrderSn, err)
			continue
		}

		if res.Code == 200 {
			glog.Error("预订单不在门店营业时间再次发起配送成功：", i2.OldOrderSn)
			continue
		} else {
			glog.Error("预订单不在门店营业时间再次发起配送错误：", i2.OldOrderSn, res.Message)
			continue
		}
	}
}

package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"strconv"
	"time"
)

// 虚拟订单-导出(含商品明细)数据
type VirtualOrderProductExport struct {
	F          *excelize.File
	SheetName  string
	storeMap   map[string]*dac.StoreInfo
	taskParams *oc.AwenVirtualOrderListRequest
}

// 逻辑
func (e *VirtualOrderProductExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenVirtualOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []*oc.AwenVirtualOrderProductExport
	var combinedProduct map[string]string

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	for {
		details, combinedProduct, err = services.AwenVirtualOrderProductExport(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		orderList = append(orderList, details...)
		glog.Info("details length - pagesize length", len(details), e.taskParams.PageSize)
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)
	var n string
	var lastOrderSn string
	beginCell := ""
	for k := range orderList {
		n = strconv.Itoa(k + 2)

		// 订单号
		e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
		// 父订单号
		e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].ParentOrderSn)
		// 外部订单号
		e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
		// SkuId
		e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].SkuId)
		// 商品名称
		e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].ProductName)
		// 店内分类
		e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].ChannelCategoryName)
		// 组合商品SkuId
		e.F.SetCellValue(e.SheetName, "G"+n, orderList[k].ParentSkuId)
		// 组合商品名称
		if len(orderList[k].ParentSkuId) > 0 {
			if _, ok := combinedProduct[orderList[k].ParentSkuId]; ok {
				e.F.SetCellValue(e.SheetName, "H"+n, combinedProduct[orderList[k].ParentSkuId])
			}
		}
		// 订单状态
		e.F.SetCellValue(e.SheetName, "I"+n, services.OrderMainStatusMap[orderList[k].OrderStatus])
		/*if orderList[k].OrderStatus == 0 {
			e.f.SetCellValue(e.sheetName, "H"+n, services.OrderMainStatusMap[orderList[k].OrderStatus])
		} else {
			e.f.SetCellValue(e.sheetName, "H"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
		}*/
		// 发货数量
		e.F.SetCellValue(e.SheetName, "J"+n, 1) ///orderList[k].Number
		// 订单金额
		e.F.SetCellValue(e.SheetName, "K"+n, kit.FenToYuan(orderList[k].PayPrice)) ///kit.FenToYuan(orderList[k].PaymentTotal)
		refundNumber := 0
		refundTotal := kit.FenToYuan(cast.ToInt64(orderList[k].RefundAmountSku))
		if refundTotal > 0 {
			refundNumber = 1
		}
		// 退款数量
		e.F.SetCellValue(e.SheetName, "L"+n, refundNumber)
		// 退款金额
		e.F.SetCellValue(e.SheetName, "M"+n, refundTotal)
		/*	if orderList[k].OrderStatus != 10 && orderList[k].VerifyStatus == "已退款" {
			e.f.SetCellValue(e.sheetName, "K"+n, kit.FenToYuan(cast.ToInt64(orderList[k].RefundAmountSku)))
		}*/
		// 支付时间
		e.F.SetCellValue(e.SheetName, "N"+n, orderList[k].PayTime)
		// 店铺ID(财务编码)
		e.F.SetCellValue(e.SheetName, "O"+n, orderList[k].ShopId)
		// 门店名称
		e.F.SetCellValue(e.SheetName, "P"+n, orderList[k].ShopName)
		// 业绩所属员工姓名
		e.F.SetCellValue(e.SheetName, "Q"+n, orderList[k].PerformanceStaffName)
		// 支付方式
		e.F.SetCellValue(e.SheetName, "R"+n, services.PayMode[orderList[k].PayMode])
		// 核销码
		e.F.SetCellValue(e.SheetName, "S"+n, orderList[k].VerifyCode)
		// 核销状态
		e.F.SetCellValue(e.SheetName, "T"+n, orderList[k].VerifyStatus)
		// 核销时间
		e.F.SetCellValue(e.SheetName, "U"+n, orderList[k].VerifyTime)
		// 核销地点
		e.F.SetCellValue(e.SheetName, "V"+n, orderList[k].VerifyShop)
		// 核销
		e.F.SetCellValue(e.SheetName, "W"+n, orderList[k].FinanceCodes)
		// 平台补贴分摊
		e.F.SetCellValue(e.SheetName, "X"+n, kit.FenToYuan(orderList[k].PrivilegePt))

		//说明是新的行，记录需要合并的初始位置
		if lastOrderSn != orderList[k].OrderSn+orderList[k].SkuId {
			lastOrderSn = orderList[k].OrderSn + orderList[k].SkuId
			beginCell = "X" + n
		}
		if k+1 < len(orderList) {
			//判断当前行的订单号和下一行的订单号不一样了，那么需要合并了
			if orderList[k].OrderSn+orderList[k].SkuId != orderList[k+1].OrderSn+orderList[k+1].SkuId && beginCell != "X"+n {
				err := e.F.MergeCell(e.SheetName, beginCell, "X"+n)
				if err != nil {

				}
			}

		} else { //行数已经不足够了
			//不是当前记录的行，那么需要合并
			if beginCell != "X"+n && lastOrderSn != "" {
				err := e.F.MergeCell(e.SheetName, beginCell, "X"+n)
				if err != nil {

				}

			}

		}

	}
	e.F.Save()

	return
}

// 设置表头
func (e *VirtualOrderProductExport) SetSheetName() {
	/*	nameList := []string{
		"订单号", "父订单号", "SKUID", "商品名称", "店内分类", "组合商品SKUID",
		"组合商品名称", "订单状态", "销售数量", "支付金额", "退款金额", "支付时间", "财务编码", "门店名称", "业绩归属人", "支付方式",
		"核销码","核销状态","核销时间","核销分院", "财务编码",
	}*/
	nameList := []string{
		"订单号", "父订单号", "外部订单号", "SKUID", "商品名称", "店内分类", "组合商品SKUID",
		"组合商品名称", "订单状态", "销售数量", "支付金额", "退款数量", "退款金额", "支付时间", "财务编码", "门店名称", "业绩归属人", "支付方式",
		"核销码", "核销状态", "核销时间", "核销分院", "财务编码", "平台补贴分摊",
	}
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *VirtualOrderProductExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("虚拟订单-导出商品明细(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

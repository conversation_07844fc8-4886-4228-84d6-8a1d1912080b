package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/services"
	"order-center/services/export"
	"order-center/utils"
	"time"

	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

type MqOrderExportTask struct {
	TaskId      int32  `json:"task_id"`
	UserNo      string `json:"user_no"`
	TaskContent int    `json:"task_content"`
	TaskParams  string `json:"task_params"`
}

type orderExportInterface interface {
	DataExport(string) (int, error)
	SetSheetName()
	GenerateDownUrl() (string, error)
}

// 消费订单导出任务
func consumeOrderExportTask() {
	go func() {
		for {
			utils.Consume(services.OrderExportTaskQueue, "", "datacenter", orderExportTask)
			glog.Info(services.OrderExportTaskQueue, "掉线重连")
			time.Sleep(time.Second * 5)
		}
	}()
}

func orderExportTask(d amqp.Delivery) (response string, err error) {
	defer kit.CatchPanic()

	funcName := "OrderExportTask"
	request := string(d.Body)
	glog.Info(funcName, ", MQ消息, ", request)
	model := new(MqOrderExportTask)
	err = json.Unmarshal(d.Body, model)
	if err != nil {
		glog.Error(funcName, ", json解析错误, ", err, ", json：", request)
		d.Ack(false)
		return "", err
	}

	// 如果任务已经完成，则跳过
	db := services.GetDBConn()
	var exportTask models.ExportTask
	if _, err := db.Table("datacenter.export_task").Where("id = ?", model.TaskId).Get(&exportTask); err != nil {
		glog.Error(funcName, ", 查询datacenter.export_task失败, ", err, ", json：", request)
		d.Ack(false)
		return "", err
	}

	if exportTask.TaskStatus == 2 || exportTask.TaskStatus == 3 {
		d.Ack(false)
		return "", err
	}

	//文件下载链接
	var url string
	var taskNums int

	//更新任务状态
	defer func() {
		dcClient := dac.GetDataCenterClient()
		defer dcClient.Close()

		params := &dac.UpdateOrderExportTaskListRequest{
			Id:         model.TaskId,
			TaskStatus: 3, //任务失败
			TaskNum:    int32(taskNums),
		}

		//任务成功
		if len(url) > 0 {
			params.TaskStatus = 2
			params.ResulteFileUrl = url
		} else if err != nil {
			glog.Error(model.UserNo, ", 数据导出任务失败, ", err)
		} else {
			glog.Error(model.UserNo, ", panic异常")
		}

		glog.Info("UpdateOrderExportTask2:", kit.JsonEncode(params), "开始")
		if res, err1 := dcClient.RPC.UpdateOrderExportTask(context.Background(), params); err1 != nil {
			glog.Error(model.UserNo, "修改导出Excel任务状态失败：", err1, kit.JsonEncode(params))
		} else if res.Code != 200 {
			glog.Error(model.UserNo, "导出任务修改状态失败：", res.Message+res.Error, kit.JsonEncode(params))
		}
		glog.Info("UpdateOrderExportTask2 更新状态结果")
		d.Ack(false)
	}()

	//修改任务状态
	StartTask(model)

	//文件数据导出
	oet := NewOrderExportTask(model.TaskContent, cast.ToInt32(exportTask.OrgId), exportTask.FinanceCode)

	if taskNums, err = oet.DataExport(model.TaskParams); err != nil {
		glog.Error(model.UserNo, ", 文件数据导出失败, ", err)
		err = errors.New("dataExport失败, " + err.Error())
		return "", err
	}

	glog.Info(model.UserNo, ",导出的订单数量,", taskNums)

	glog.Info(model.UserNo, ", 文件上传开始")

	//上传至oss生成下载链接
	url, err = oet.GenerateDownUrl()
	if err != nil {
		glog.Error(model.UserNo, ", 生成下载链接失败, ", err)
		err = errors.New("生成下载链接失败, " + err.Error())
		return
	}

	glog.Info(model.UserNo, ", ", model.TaskId, ", 导出任务结束, ", url)

	return "success", nil
}

// 手动导出 用于本地测试
func manualExport(TaskContent int, TaskParams string, orgId int32, financeCode string) (string, error) {
	oet := NewOrderExportTask(TaskContent, orgId, financeCode)

	if _, err := oet.DataExport(TaskParams); err != nil {
		err = errors.New("dataExport失败, " + err.Error())
		return "", err
	}

	//上传至oss生成下载链接
	url, err := oet.GenerateDownUrl()
	if err != nil {
		err = errors.New("生成下载链接失败, " + err.Error())
		return "", err
	}
	fmt.Println("结束", url)
	return "", nil
}

func NewOrderExportTask(taskContent int, orgId int32, financeCode string) (oet orderExportInterface) {
	switch taskContent {
	case 1: //实物订单-导出订单数据
		oet = &export.OrderExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 2: //实物订单-导出(含商品明细)数据
		oet = &export.OrderProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 3: //退货退款管理-导出订单数据
		oet = &export.RefundOrderExport{
			SheetName:   "Sheet1",
			F:           excelize.NewFile(),
			FinanceCode: financeCode,
			OrgId:       orgId,
		}

	case 4: //父订单-导出订单数据
		oet = &export.ParentOrderExport{
			SheetName:   "Sheet1",
			F:           excelize.NewFile(),
			FinanceCode: financeCode,
			OrgId:       orgId,
		}
	case 5: //父订单-导出(含商品明细)数据
		oet = &export.ParentOrderProductExport{
			SheetName:   "Sheet1",
			F:           excelize.NewFile(),
			FinanceCode: financeCode,
			OrgId:       orgId,
		}
	case 6: //虚拟订单-导出订单数据
		oet = &export.VirtualOrderExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 7: //虚拟订单-导出(含商品明细)数据
		oet = &export.VirtualOrderProductExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 10: //团长拼团制-团员订单
		oet = &export.CommunityGroupMemberOrderExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 11, 12: //团长拼团制-团员实物或虚拟子订单
		oet = &export.CommunityGroupMemberChildOrderExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 14: //配送报表导出
		oet = &export.DeliveryReportExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	case 15: //VIP虚拟卡导出
		oet = &export.VirtualCardExport{
			SheetName: "Sheet1",
			F:         excelize.NewFile(),
		}
	}

	return
}

// 更新任务状态为 进行中

func StartTask(task *MqOrderExportTask) {
	glog.Info("导出任务的详情", task)
	dcClient := dac.GetDataCenterClient()
	defer dcClient.Close()

	params := &dac.UpdateOrderExportTaskListRequest{
		Id:         task.TaskId,
		TaskStatus: 1, //任务进行中
	}
	if res, err1 := dcClient.RPC.UpdateOrderExportTask(context.Background(), params); err1 != nil {
		glog.Error(task.UserNo, "修改导出Excel任务状态为进行中失败：", err1, kit.JsonEncode(params))
	} else if res.Code != 200 {
		glog.Error(task.UserNo, "导出任务修改状态为进行中失败：", res.Message+res.Error, kit.JsonEncode(params))
	}
}

// 获取列表参数
type VipCardVirtualListReq struct {
	BatchId         string  `json:"batch_id" query:"batch_id"`                   //批次号
	OrgId           int32   `json:"org_id" query:"org_id"`                       //组织ID
	TemplateId      int32   `json:"template_id" query:"template_id"`             //卡模板ID
	UserId          string  `json:"user_id" query:"user_id"`                     //兑换用户id
	UserMobile      string  `json:"user_mobile" query:"user_mobile"`             //兑换用户手机号
	UseTimeStart    string  `json:"use_time_start" query:"use_time_start"`       //兑换时间
	UseTimeEnd      string  `json:"use_time_end" query:"use_time_end"`           //兑换时间
	CreateTimeStart string  `json:"create_time_start" query:"create_time_start"` //创建时间
	CreateTimeEnd   string  `json:"create_time_end" query:"create_time_end"`     //创建时间
	CardIds         []int64 `json:"card_ids" query:"card_ids"`                   //卡号集合，选中导出的数据
	PageIndex       int32   `json:"page_index" query:"page_index"`               //分页-当前页
	PageSize        int32   `json:"page_size" query:"page_size"`                 //分页-页码
}

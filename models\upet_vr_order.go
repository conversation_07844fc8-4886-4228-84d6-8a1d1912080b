package models

type UpetVrOrder struct {
	OrderId             int     `xorm:"not null pk autoincr comment('虚拟订单索引id') INT(11)"`
	OcId                int64   `xorm:"default 0 comment('订单中心主键id') BIGINT(10)"`
	OrderSn             int64   `xorm:"not null comment('订单编号') index BIGINT(20)"`
	ErpOrderSn          int64   `xorm:"default 0 comment('erp订单号') BIGINT(20)"`
	StoreId             int32   `xorm:"not null comment('卖家店铺id') index(idx_chain_id_store_id) INT(11)"`
	StoreName           string  `xorm:"not null comment('卖家店铺名称') VARCHAR(50)"`
	BuyerId             int32   `xorm:"not null comment('买家id') index INT(11)"`
	BuyerName           string  `xorm:"not null comment('买家登录名') VARCHAR(50)"`
	BuyerPhone          string  `xorm:"not null comment('买家手机') index VARCHAR(11)"`
	EncryptMobile       string  `xorm:"not null comment('买家手机') index VARCHAR(50)"`
	AddTime             int64   `xorm:"not null comment('订单生成时间') index INT(10)"`
	PaymentCode         string  `xorm:"default '' comment('支付方式名称代码') CHAR(10)"`
	PaymentTime         int     `xorm:"default 0 comment('支付(付款)时间') index INT(10)"`
	TradeNo             string  `xorm:"comment('第三方平台交易号') VARCHAR(35)"`
	CloseTime           int     `xorm:"default 0 comment('关闭时间') INT(10)"`
	CloseReason         string  `xorm:"comment('关闭原因') VARCHAR(50)"`
	FinnshedTime        int     `xorm:"comment('完成时间') INT(11)"`
	OrderAmount         float64 `xorm:"not null default 0.00 comment('订单总价格(支付金额)') DECIMAL(10,2)"`
	RefundAmount        float64 `xorm:"default 0.00 comment('退款金额') DECIMAL(10,2)"`
	RcbAmount           float64 `xorm:"not null default 0.00 comment('充值卡支付金额') DECIMAL(10,2)"`
	PdAmount            float64 `xorm:"not null default 0.00 comment('预存款支付金额') DECIMAL(10,2)"`
	OrderState          int     `xorm:"not null default 0 comment('订单状态：0(已取消)10(默认):未付款;20:已付款;40:已完成;') TINYINT(4)"`
	RefundState         int     `xorm:"default 0 comment('退款状态:0是无退款,1是部分退款,2是全部退款') TINYINT(1)"`
	BuyerMsg            string  `xorm:"comment('买家留言') VARCHAR(150)"`
	DeleteState         int     `xorm:"not null default 0 comment('删除状态0未删除1放入回收站2彻底删除') TINYINT(4)"`
	GoodsId             int     `xorm:"not null comment('商品id') INT(11)"`
	GoodsName           string  `xorm:"not null comment('商品名称') VARCHAR(50)"`
	GoodsPrice          float64 `xorm:"not null comment('商品价格') DECIMAL(10,2)"`
	GoodsNum            int32   `xorm:"not null default 1 comment('商品数量') SMALLINT(5)"`
	GoodsImage          string  `xorm:"comment('商品图片') VARCHAR(100)"`
	CommisRate          int     `xorm:"not null default 0 comment('佣金比例') SMALLINT(5)"`
	GcId                int     `xorm:"default 0 comment('商品最底级分类ID') MEDIUMINT(9)"`
	VrIndate            int     `xorm:"comment('有效期') INT(11)"`
	VrSendTimes         int     `xorm:"not null default 0 comment('兑换码发送次数') TINYINT(4)"`
	VrInvalidRefund     int     `xorm:"not null default 1 comment('允许过期退款1是0否') TINYINT(4)"`
	OrderPromotionType  int     `xorm:"not null default 0 comment('订单参加的促销类型 0无促销1团购') TINYINT(3)"`
	PromotionsId        int     `xorm:"default 0 comment('促销ID，与order_promotion_type配合使用') MEDIUMINT(9)"`
	OrderFrom           int     `xorm:"not null default 1 comment('1WEB2mobile') TINYINT(4)"`
	EvaluationState     int     `xorm:"not null default 0 comment('评价状态0默认1已评价2禁止评价') TINYINT(4)"`
	EvaluationTime      int     `xorm:"not null default 0 comment('评价时间') INT(11)"`
	UseState            int     `xorm:"default 0 comment('使用状态0默认，未使用1已使用，有一个被使用即为1') TINYINT(4)"`
	ApiPayTime          int     `xorm:"default 0 comment('在线支付动作时间,只有站内+在线组合支付时记录') INT(10)"`
	GoodsContractid     string  `xorm:"comment('商品开启的消费者保障服务id') VARCHAR(100)"`
	GoodsSpec           string  `xorm:"comment('规格') VARCHAR(200)"`
	ErpTime             int     `xorm:"comment('ERP同步时间') INT(11)"`
	ErpStatus           int     `xorm:"default 0 comment('0默认，1已同步，2已同步需隐藏') TINYINT(4)"`
	ErpOrderStatus      int     `xorm:"default 0 comment('ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)') TINYINT(4)"`
	ErpOrderIds         string  `xorm:"comment('ERP订单号') TEXT"`
	ErpMobile           string  `xorm:"comment('ERP手机号码') VARCHAR(11)"`
	ChainId             int32   `xorm:"default 0 comment('门店id') index(idx_chain_id_store_id) INT(10)"`
	OrderCommon         string  `xorm:"default '' comment('优惠信息') VARCHAR(1000)"`
	VoucherPrice        int     `xorm:"default 0 comment('优惠卷金额') INT(11)"`
	VoucherCode         string  `xorm:"default '' comment('优惠卷码') VARCHAR(32)"`
	IsDis               int     `xorm:"not null default 0 comment('是否分销订单') TINYINT(1)"`
	DisCommisRate       float64 `xorm:"not null default 0 comment('分销佣金比例') TINYINT(1)"`
	DisMemberId         int32   `xorm:"not null default 0 comment('分销会员ID') index INT(10)"`
	OutMemberId         int32   `xorm:"default 0 comment('推荐人iD') index INT(10)"`
	CustomerServiceId   int32   `xorm:"not null default 0 comment('客服id') index INT(10)"`
	CustomerServiceRate float64 `xorm:"not null default 0.00 comment('客服分成比列') DECIMAL(4,2)"`
	PaymentFrom         int     `xorm:"not null default 0 comment('0默认1电银') TINYINT(1)"`
	VipIsexpire         int     `xorm:"default 0 comment('0默认1过期') TINYINT(1)"`
	DisType             int32   `xorm:"default 0 comment('0自主购买，1分享连接，2扫码，3自己扫自己') TINYINT(2)"`
	IsLive              int     `xorm:"default 0 comment('0默认1直播下单') TINYINT(1)"`
	IsRenewal           int     `xorm:"default 0 comment('是否是会员卡续费订单') TINYINT(1)"`
	FirstOrder          int     `xorm:"default 0 comment('0默认1首次下单') TINYINT(1)"`
	OrderSnold          int64   `xorm:"comment('旧订单号') BIGINT(20)"`
	OrderType           int32   `xorm:"default 0 comment('0正常订单，4,拼团订单,99助力订单') TINYINT(4)"`
}

package tasks

import (
	"context"
	"time"

	"order-center/dto"
	"order-center/proto/oc"
	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

type TaskOrderComplete struct {
	services.BaseService
}

//阿闻自提单超过4小时自动完成,每5分钟执行一次
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */5 * * * *", func() {
		service := TaskOrderComplete{}
		service.TaskAutoOrderComplete()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s TaskOrderComplete) TaskAutoOrderComplete() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:AutoOrderComplete"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	//连接池勿关闭
	db := services.GetDBConn()

	var orderlist []dto.OldOrderSnDto
	db.SQL(`
		SELECT t1.old_order_sn,t2.refund_state FROM order_main AS t1
		LEFT JOIN refund_order AS t2 ON  t1.order_sn=t2.order_sn
		inner join order_detail t3 on t1.order_sn=t3.order_sn
		WHERE t1.order_type=3
		AND t1.channel_id=1
		AND t1.order_status=20
		AND t1.order_status_child=20102
		AND t3.is_picking=1 AND t3.expected_time<=DATE_SUB(SYSDATE(3),INTERVAL 4 HOUR)
		AND (t2.refund_state IS NULL OR t2.refund_state NOT IN(1,5,6,7))
	`).Find(&orderlist)

	if len(orderlist) > 0 {
		ctx := context.Background()
		services := services.OrderService{}
		curTime := kit.GetTimeNow()
		for _, i := range orderlist {
			glog.Info("阿闻自提单超过4小时自动完成定时任务执行开始", i.OldOrderSn)
			res, err := services.AccomplishOrder(ctx, &oc.AccomplishOrderRequest{
				OrderSn:     i.OldOrderSn,
				ConfirmTime: curTime,
			})
			if err != nil {
				glog.Error("阿闻自提单超过4小时自动完成定时任务错误：", i.OldOrderSn, err.Error())
				continue
			}
			if res.Code != 200 {
				glog.Error("阿闻自提单超过4小时自动完成定时任务错误：", i.OldOrderSn, res.Message)
				continue
			}

			glog.Info("阿闻自提单超过4小时自动完成定时任务成功完成：", i.OldOrderSn)
		}
	}
}

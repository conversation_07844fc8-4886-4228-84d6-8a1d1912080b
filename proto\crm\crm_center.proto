syntax = "proto3";

package crm;

//微信企业标签相关服务
service FlagService {
    //查询标签组列表
    rpc GetFlagGroupList (FlagGroupListRequest) returns (FlagGroupListResponse);
    //修改标签组
    rpc UpdateFlagGroup (FlagGroupInfo) returns (BaseResponse);
    //删除标签组
    rpc DeleteFlagGroup (FlagGroupInfo) returns (BaseResponse);
    //查询标签列表
    rpc GetFlagList (FlagListRequest) returns (FlagListResponse);
    //创建标签组和标签，修改标签
    rpc CreateFlag (CreateFlagRequest) returns (BaseResponse);
    //删除标签
    rpc DeleteFlag (FlagInfo) returns (BaseResponse);
    //同步标签到企业微信
    rpc SynchronizeToWeChat (FlagInfo) returns (BaseResponse);
    //根据标签ID查询标签信息
    rpc GetFlagInfo (FlagInfo) returns (FlagResponse);

    //根据条件获取客户列表数据
    rpc GetCustomerList (CustomerRequest) returns (BaseResponse);
    //根据条件获取客户详情数据
    rpc GetCustomerDetail (CustomerRequest) returns (BaseResponse);
    //根据用户信息获取用户的标签信息
    rpc GetCustomerFlagList (CustomerRequest) returns (FlagGroupListResponse);
    //根据条件导出
    rpc ExportCustomerList (CustomerRequest) returns (BaseResponse);
     //批量导入手动用户标签
    rpc CreateBatchTask (CreateBatchTaskRequest) returns (CreateBatchTaskResponse);
    //导入用户标签
    rpc GetTaskList (GetTaskListRequest) returns (GetTaskListResponse);


    // 提供给北京的rpc接口 创建企微标签
    rpc AddQWFlags(AddQWTagRequest) returns (AddTagResponse);
    // 提供给北京的rpc接口 更新企微标签信息
    rpc UpdateQWFlags(UpdateQWTagRequest) returns (QWTagResponse);
    //  提供给北京的rpc接口 删除企微标签信息
    rpc DelQWFlags(DelQWFlagRequest) returns (QWTagResponse);
    // 提供给北京的rpc接口 编辑客户企业标签
    rpc EditQWFlags(EditQWRequest) returns (QWTagResponse);
    // 提供给北京的rpc接口 获取企业标签库
    rpc GetTagFlags(GetQWFlagRequest) returns (GetQWtagResponse);

}
//平台商品库--获取异步任务数据接口--请求参数
message GetTaskListRequest {
    //任务id
    int32 id = 1;
    //任务内容:1:批量新建;2:批量删除;3:批量更新
    int32 task_content = 2;
    //任务状态:1:调度中;2:进行中;3:已完成
    int32 task_status = 3;
    //状态:1:正常;2:冻结;
    int32 status = 4;
    //创建人id
    string create_id = 5;
    //排序类型：createTimeDesc:根据创建时间倒序
    string sort = 6;
    //创建时间
    string createtime = 7;
    //当前页
    int32 page = 8;
    //每页显示数据条数
    int32 pageSize = 9;
    //渠道id
    int32 channel_id = 10;
}

//平台商品库--获取异步任务数据接口--响应参数
message GetTaskListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    //当前页
    int32 page = 4;
    //每页显示数据条数
    int32 pageSize = 5;
    //总数
    int32 count = 6;
    //结果
    repeated TaskList task_list = 7;
}

//任务列表模型
message TaskList {
    //任务id
    int32 id = 1;
    //任务内容:1:批量新建;2:批量删除;3:渠道--批量新建 4：渠道--批量上架
    int32 task_content = 2;
    //任务状态:1:调度中;2:进行中;3:已完成
    int32 task_status = 3;
    //任务详情
    string task_detail = 4;
    //操作文件路径
    string operation_file_url = 5;
    //操作结果文件路径
    string resulte_file_url = 6;
    //状态:1:正常;2:冻结;
    int32 status = 7;
    //创建人id
    string modify_id = 8;
    //创建时间
    string modify_time = 9;
    //创建人id
    string create_id = 10;
    //渠道id
    int32 channel_id = 11;
    //创建时间
    string create_time = 12;
    //操作请求的token值，类似userinfo
    string request_header = 13;
}

//平台商品库--新增异步任务数据接口--请求参数
message CreateBatchTaskRequest {
    //任务内容:1:导入用户标签
    int32 task_content = 1;
    //操作文件路径
    string operation_file_url = 2;
    //创建人id
    string create_id = 3;
    //标签名称
    string flag_name = 4;
    //标签组名称
    string group_flag_name = 5;
}

//平台商品库--新增异步任务数据接口--响应参数
message CreateBatchTaskResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
}

//客户详情接口
message CustomerRequest {
    //创建人id
    string userid = 1;
    //外部用户id
    string external_userid = 2;
    //用户昵称
    string nick_name = 3;
    //用户类型    全部给-1
    int32 customer_type = 4;
    //用户性别     部给-1
    int32 customer_sex = 5;
    //备注电话
    string remark_mobile = 6;
    //分页页码
    int32 page_index = 7;
    //分页页数
    int32 page_size = 8;
    //是否导出标记
    int32 is_export = 9;
}

//标签查询返回标签信息
message FlagResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    //标签信息
     FlagInfo flag_info = 4;
}

//标签组list请求参数
message FlagGroupListRequest {
    //当前页 111
    int32 page = 1;
    //每页大小
    int32 page_size = 2;
    //标签组名称
    string group_name = 3;
}

//标签组list返回参数
message FlagGroupListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    int32 total = 4;
    //标签信息列表
    repeated FlagGroupInfo flag_group_list = 5;
}

//标签查询列表请求参数
message FlagListRequest {
    //当前页
    int32 page = 1;
    //每页大小
    int32 page_size = 2;
    //标签类型 1:手动标签 2:自动标签 0全部
    int32 flag_type = 3;
    //标签组名称
    string group_name = 4;
    //标签名称
    string flag_name = 5;
}

//标签查询列表返回
message FlagListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    int32 total = 4;
    //标签信息列表
    repeated FlagInfo flag_list = 5;
}

//标签信息
message FlagInfo {
    //标签类型 1:手动标签 2:自动标签
    int32 flag_type = 1;
    //标签组名称
    string group_name = 2;
    //标签名称
    string flag_name = 3;
    //用户个数
    int32 flag_sum = 4;
    //标签标签ID，删除的时候只需要传ID
    int32 flag_id = 5;
    //选取中的条件
    repeated ConditionInfo list = 6;
    //企业微信的标签ID
    string tag_id = 7;
    //标签组ID
    int32 group_id = 8;
    //判断类型，1：满足任意一个被选中的条件即可，2：必须满足所有被选中的条件
    int32 check_type = 9;
}

//标签组
message FlagGroupInfo {
    //标签组名称
    string group_name = 1;
    //标签组主键
    int32 id = 2;
    //标签组在企业微信的ID
    string group_id = 3;
    //标签名称列表
    repeated string flag_name_list = 4;
    //标签信息列表
    repeated FlagInfo flag_list = 5;
    //操作人
    string operator = 6;
}

//创建或者修改标签，如果group_id=0则创建标签组和标签,如果flag_id=0创建标签，否则是修改
message CreateFlagRequest {
    //标签组名称
    string group_name = 1;
    //标签组ID 如果是创建传0
    int32 group_id = 2;
    //标签名称
    string flag_name = 3;
    //标签ID如果是创建传0
    int32 flag_id = 4;
    //操作人
    string operator = 5;
    //判断类型，1：满足任意一个被选中的条件即可，2：必须满足所有被选中的条件
    int32 check_type = 6;
    //选取中的条件
    repeated ConditionInfo list = 7;
    //1:手动标签 2:自动标签
    int32 flag_type = 8;
    //标签组在企业微信的ID
    string group_wx_id = 9;
    //标签在企业微信的ID
    string tag_id = 10;
}

//返回结果
message BaseResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    int32 total = 4;
    string data = 5;
}

//条件信息
message ConditionInfo {
    //条件ID
    int32 condition_id = 1;
    //值1，如果是只有单个值的就会在这里
    string value1 = 2;
    string value2 = 3;
    //选中的条件
    string sel_type = 4;
}



// 添加企业客户标签
message AddQWTagRequest{
    // 标签组id
    string group_id = 1;
    // 标签组名称，最长为30个字符
    string group_name = 2;
    // 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 3;
//    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
//    string agentid = 4;
    // 多个标签信息
    repeated TagRequest tag = 4;
}

message AddQWTagCTRequest{
    // 标签组id
    string group_id = 1;
    // 标签组名称，最长为30个字符
    string group_name = 2;
    // 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 3;
//    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
//    string agentid = 4;
    // 创建时间
    int64 create_time = 4;
    // 多个标签信息
    repeated TagCTRequest tag = 5;
}

message TagCTRequest{
    // 添加的标签名称，最长为30个字符
    string name = 1;
    // 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 2;
    // 创建时间
    int64 create_time = 3;
    // 标签的id
    string id = 4;
}

message QWGroupsAllResponse{
    // 标签组id
    string group_id = 1;
    // 标签组名称，最长为30个字符
    string group_name = 2;
    // 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 3;
    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
    //string agentid = 4;
    // 创建时间
    int64 create_time = 4;
    // 多个标签信息
    repeated QWTagsAllResponse tag = 5;
    // 	标签组是否已经被删除，只在指定tag_id进行查询时返回
    bool deleted = 6;
}

message QWTagsAllResponse{
    // 添加的标签名称，最长为30个字符
    string name = 1;
    // 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 2;
    // 创建时间
    int64 create_time = 3;
    // 标签的id
    string id = 4;
    // 标签是否已经被删除，只在指定tag_id/group_id进行查询时返回
    bool deleted = 5;
}


// 添加标签
message TagRequest{
    // 添加的标签名称，最长为30个字符
    string name = 1;
    // 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 2;
    // 标签的id
    string id = 4;
}

// 编辑标签的返回信息
message AddTagResponse{
    // 错误的code
    uint32 errcode = 1;
    // 错误的消息
    string errmsg = 2;
    // 返回的信息data
    AddQWTagCTRequest tag_group = 3;
}

// 编辑企业客户标签req
message UpdateQWTagRequest{

    // 标签或标签组的id
    string id = 1;
    // 新的标签或标签组名称，最长为30个字符
    string name = 2;
    // 标签/标签组的次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
    uint32 order = 3;

}

// 编辑企业客户标签resp
message QWTagResponse{
    // 返回码
    uint32 errcode = 1;
    // 对返回码的文本描述内容
    string  errmsg = 2;
}

// 删除标签的req
message DelQWFlagRequest{
    // 标签的id列表
    repeated string tag_id= 1;
    // 标签组的id列表
    repeated string group_id = 2;
//    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
//    string agentid = 3;
}

// 编辑客户企业标签
message EditQWRequest{
    // 添加外部联系人的userid
    string userid = 1;
    // 外部联系人userid
    string external_userid = 2;
    // 要标记的标签列表
    repeated string add_tag = 3;
    // 要移除的标签列表
    repeated string remove_tag = 4;
}
message GetQWFlagRequest{
    // 标签的id列表
    repeated string tag_id= 1;
    // 标签组的id列表
    repeated string group_id = 2;
}

message GetQWtagResponse{
    uint32 errcode = 1;
    string errmsg = 2;
    repeated QWGroupsAllResponse tag_group = 3;
}
package tasks

import (
	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/services"
	"order-center/utils"
)

// 订单超时未付款取消任务
func consumeOrderNopayCancelTask() {
	utils.Consume(services.AwenNoPayCancelQueue, services.AwenNoPayCancelRoute, services.AwenNoPayCancelExchange, func(d amqp.Delivery) (response string, err error) {
		defer kit.CatchPanic()

		funcName := "OrderNopayCancelTask"
		orderSn := string(d.Body)
		glog.Info(funcName, "，MQ消息，", orderSn)

		orderMain := services.GetOrderMainByOrderSn(orderSn)

		if orderMain == nil {
			d.Ack(false)
			return
		}

		//不是未付款直接退出
		if !checkIsNoPayed(orderMain) {
			d.Ack(false)
			return
		}

		//取消订单
		if err = noPayedOrderCancel(orderMain); err != nil {
			response = "fail"
		}

		d.Ack(false)
		return
	})
}

func checkIsNoPayed(orderMain *models.OrderMain) bool {
	//先查redis内是否有支付记录
	if services.GetRedisConn().Exists(services.AwenPayedOrderKey+orderMain.OrderSn).Val() > 0 {
		return false
	}

	if orderMain.OrderStatus != 10 {
		return false
	}
	return true
}

func noPayedOrderCancel(orderMain *models.OrderMain) error {
	_, err := services.GetDBConn().Exec("UPDATE `order_main` SET order_status=0,order_status_child=20107,cancel_reason='超时未支付，订单取消',cancel_time=now() WHERE order_sn = '" + orderMain.OrderSn + "' AND order_status=10")
	if err != nil {
		glog.Error(orderMain.OrderSn, "，更新订单为取消状态失败，", err)
		return err
	}
	if orderMain.AppChannel == services.SaasAppChannel {
		if _, err = services.GetDBConn().Exec("UPDATE `order_main` SET order_status=0,order_status_child=20107,cancel_reason='超时未支付，订单取消',cancel_time=now() WHERE parent_order_sn = '" + orderMain.OrderSn + "' AND order_status=10"); err != nil {
			glog.Error(orderMain.OrderSn, "，更新saas子订单为取消状态失败，", err)
			return err
		}
	}

	if orderMain.ChannelId == services.ChannelDigitalHealth || orderMain.ChannelId == services.ChannelAwenId {
		orderDetail := services.GetOrderDetailByOrderSn(orderMain.OrderSn)
		if orderDetail.ConsultOrderSn != "" {
			err = services.PushDigitalHealthOrder(orderMain.OrderSn, orderDetail.ConsultOrderSn, 4, 0)
			if err != nil {
				return err
			}
		}
	}

	//解冻库存
	services.FreedStock(orderMain.OrderSn)

	//释放阿闻到家小程序折扣活动当日库存
	services.FreedDailyStock(orderMain.OrderSn)

	//清除在途库存
	services.DeleteTransportationInventory(orderMain.OrderSn, orderMain.ChannelId)

	go services.PushOrderStatusToTencent(orderMain.OrderSn, 0)
	return nil
}

func pushD() {

}

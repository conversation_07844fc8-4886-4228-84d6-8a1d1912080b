package vip_card

import (
	"order-center/models"
	"order-center/proto/oc"
	"reflect"
	"testing"

	"github.com/go-xorm/xorm"
)

func TestGetPhysicalVipCardOrderList(t *testing.T) {
	type args struct {
		session *xorm.Session
		in      *oc.GetPhysicalVipCardOrderListRequest
	}
	tests := []struct {
		name      string
		args      args
		wantOut   []*models.PhysicalVipCardOrders
		wantTotal int32
		wantErr   bool
	}{
		// TODO: Add test cases.

	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := GetPhysicalVipCardOrderList(tt.args.session, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPhysicalVipCardOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.<PERSON><PERSON><PERSON>("GetPhysicalVipCardOrderList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetPhysicalVipCardOrderList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

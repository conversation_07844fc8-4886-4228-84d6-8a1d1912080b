syntax = "proto3";

package mk;

import "mk/model.proto";

/////////////////////////////////// Request  //////////////////////////////////////////////////////////////////////

// 基础请求
message baseRequest {
  //用户Id，即userno
  string userId = 1;
  //用户所属店铺ID
  string shopId = 2;
}

// 根据ID查询请求
message queryByIdRequest {
  // 单个实体Id
  int32 id = 2;
}

// 删除的请求
message deleteRequest {
  // 多个实体Id
  string ids = 1;
  // 单个实体Id
  int32 id = 2;
  // 当前登录用户信息
  string userId = 4;
  string userName = 5;
}

// 更新是否启用的请求
message updateIsEnableRequest {
  // 多个实体Id
  string ids = 1;
  // 单个实体Id
  int32 id = 2;
  // 是否启用
  bool isEnable = 3;
  // 当前登录用户信息
  string userId = 4;
  string userName = 5;
}

message promotionSummaryReq {
  // 多个门店用逗号分隔
  string shopId = 2;
}

message promotionSummaryRes {
  // 响应代码 200 成功 非 200 失败查看 message
  code code = 1;
  // 不成功的错误信息
  string message = 2;
  // 活动明细
  repeated promotionSummaryDto summary = 3;
  // 活动门店数量，仅多门店返回
  promotionSummaryShopDto shop = 4;
}

// 查询当前用户的活动信息
message promotionSummaryShopDto {
  // 有活动门店数量
  int32 promotionShopCount = 1;
  // 无活动门店数量
  int32 notPromotionShopCount = 2;
  // 生效活动数量
  int32 promotioningShopCount = 3;
}

// 根据登录用户按类别查询促销活动Dto
message promotionSummaryDto {
  // 类别Id，1 满减活动 2 限时折扣 3 满减运费 10社区团购 11 拼团活动 12 赠险
  int32 typeId = 1;
  // 类别名称
  string typeName = 2;
  // 活动数量
  int32 typeCount = 3;
  // 进行中的数量
  int32 typeInProgress = 5;
  // 未开始的数量
  int32 typeNotStart = 6;
  // 概览
  string summary = 7;
}


//根据店铺Id查询满减活动
message promotionQueryByShopIdRequest {
  //店铺Id
  string shopId = 1;
  // 是否返回活动满减详细信息
  bool showDetail = 3;
  // 活动类型 1 满减 2 限时折扣
  int32 types = 6;
  //分页信息--当前页
  int32 pageIndex = 7;
  //分页信息--每页个数
  int32 pageSize = 8;
  //置顶的商品id
  int32 productId = 9;
  // 仓库id
  int32 warehouseId = 10;
}

// 根据店铺Id查询满减活动
message QueryPromotionByShopIdReq {
  // 店铺Id
  string shopId = 1;
  // 限时活动详情
  bool showDetail = 2;
}

// 计算减免金额请求
message promotionCalcRequest {
  //用户所属店铺ID
  string shopId = 1;
  // 渠道Id
  int32 channelId = 2;
  //目的地坐标X 不传递或传递0，不计算运费
  double destinationX = 3;
  //目的地坐标Y 不传递或传递0，不计算运费
  double destinationY = 4;
  // 是否是新用户
  bool isNewUser = 6;
  // 用户id
  string user_id = 7;
  // 相关商品
  repeated promotionCalcProductDto promotionProduct = 5;
}

message GetDeliveryMoneyRequest {
  //用户所属店铺ID
  string shopId = 1;
  //目的地坐标X 不传递或传递0，不计算运费
  double destinationX = 3;
  //目的地坐标Y 不传递或传递0，不计算运费
  double destinationY = 4;
  // 重量
  double totalWeight = 6;
}

// 通过sku查询商品是否参与满减活动
message promotionQueryBySkuIdsRequest {
  // 门店财务编码
  string shopId = 1;
  // 渠道Id
  int32 channelId = 3;
  // 商品Sku列表
  repeated string skuIds = 2;
  // 是否返回满减明细列表
  bool isReachReduceInfo = 4;
}

/////////////////////////////////// PlayBillRequest  //////////////////////////////////////////////////////////////////////
message CreatePlayBillRequest {
  repeated string finance_code = 1; //门店列表
  string playbillname = 2; //海报名称
  string playbillcontent = 3; //海报内容
  int32 template = 4; //海报模板
  string templateimg = 5; //海报模板图
  string page_path = 6; //主页路径
}
message GetPlayBillListRequest {
  string finance_code = 1; //财务编码
}
message GetSinglePlayBillListRequest {
  string finance_code = 1; //财务编码
  string playbillname = 2; //海报名称
  string stime = 3; //海报创建起始时间
  string etime = 4; //海报创建创建时间
}

message DeleteSinglePlayBillRequest {
  string playbillid = 1; //海报id
}

message DownloadPlayBillRequest {
  string playbillid = 1; //海报id
  string finance_code = 2; //财务编码
  string access_token = 3; //微信小程序accesstoken
}
/////////////////////////////////// PlayBillRequest  //////////////////////////////////////////////////////////////////////

/////////////////////////////////// Response  //////////////////////////////////////////////////////////////////////

// 响应数据
message baseResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  code code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
}

// 查询优惠信息
message promotionQueryByShopIdResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  code code = 1;
  // 不成功的错误信息
  string message = 2;
  // 满减活动列表
  repeated promotionQueryByShopIdDto data = 3;
}

//计算减免金额请求
message promotionReduceCalcResponse {
  // 代码 非 200 取 message 错误信息
  code code = 1;
  // 错误信息
  string message = 2;
  // 减免金额
  double reduceMoney = 3;
  // 总金额
  double totalMoney = 5;
  // 实付金额
  double actualMoney = 6;
  // 优惠信息
  repeated promotionReduceDto promotionReduceList = 4;
  // 参与减免金额的商品Sku列表
  repeated string skuIds = 7;

  //总金额 以分为单位
  int32 reduceMoneyByMinUnit = 10;
  // 总金额 以分为单位
  int32 totalMoneyByMinUnit = 11;
  // 实付金额 以分为单位
  int32 actualMoneyByMinUnit = 12;
}
//计算减免金额请求
message promotionCalcResponse {
  // 代码 非 200 取 message 错误信息
  code code = 1;
  // 错误信息
  string message = 2;
  //优惠金额（参与商品优惠的金额，不包含运费优惠） 以分为单位
  int32 reduceMoneyByMinUnit = 4;
  // 总金额(商品总金额,不包含运费) 以分为单位
  int32 totalMoneyByMinUnit = 5;
  // 实付金额（商品的实收总金额,不包含运费） 以分为单位
  int32 actualMoneyByMinUnit = 6;
  // 总运费金额,以分为单位
  int32 upetDjMoneyByMinUnit = 7;
  // 总运费金额实收,以分为单位
  int32 upetActualDjMoneyByMinUnit = 13;
  // 总重量
  int32 totalWeight = 3;
  // 相关商品
  repeated promotionCalcProductDto promotionProduct = 8;
  //符合条件的满减信息
  repeated promotionReduceDto promotionReduceList = 9;
  //符合条件的满减运费信息
  promotionReduceDeliveryDto reduceDelivery = 10;
  //符合条件的限时优惠信息
  repeated promotionTimeDiscountDto timeDiscount = 11;
  // 优惠信息定义明细
  repeated promotionCalcDto calcList = 12;
  //能参与限时折扣商品种类数量
  int32 timeDiscountProductCount = 14;
  //已经参与限时折扣的商品种类数量
  int32 timeDiscountCalcProductCount = 15;
}

//通过sku查询商品是否参与满减活动 数据响应
message promotionQueryBySkuIdsResponse {
  // 代码 非 200 取 message 错误信息
  code code = 1;
  // 错误信息
  string message = 2;
  // 关联关系
  repeated promotionSkuDto data = 3;
  // 关联关系
  int64 pageCount = 4;
}

// 活动详情
message promotionDetailResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  code code = 1;
  // 不成功的错误信息
  string message = 2;

  int32 promotionId = 3;

  // 活动基本信息
  promotionDto promotion = 4;

  // 时间区间
  repeated PromotionTime timeRanges = 11;

  // 活动优惠信息
  repeated promotionReduceDto promotionReduceList = 12;

  //满减运费
  repeated promotionReduceDeliveryDto promotionReduceDeliveryList = 13;

  //限时折扣
  repeated promotionTimeDiscountDto promotionTimeDiscountList = 14;

}

/////////////////////////////////// PlayBillResponse  //////////////////////////////////////////////////////////////////////
message CreatePlayBillResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message GetPlayBillListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated PlayBill playbills = 4;
}

message GetSinglePlayBillListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated PlayBill playbills = 4;
}
message PlayBill {
  string playbillid = 1; //海报id
  string playbillname = 2; //海报名称
  string playbillcontent = 3; //海报内容
  //是否为门店码 1是 0否
  int32 is_store = 4;
  string createdate = 5; //创建时间
  int32 state = 6; //海报状态
  int32 num = 7; //海报数量
}

message DownloadPlayBillResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string playbillqrcode = 4;
  int32 template = 5;
}
/////////////////////////////////// PlayBillResponse  //////////////////////////////////////////////////////////////////////

message GetPromotionOperateLogListRequest {
  //门店财务编码
  string finance_code = 1;
  //商品sku_id
  int32 sku_id = 2;
  //活动名称
  string promotion_name = 3;
  //操作类型 1撤销 2编辑活动 3 创建活动 4删除
  int32 operate_type = 4;
  //开始时间
  string start_time = 5;
  //结束时间
  string end_time = 6;
  //当前页 从1开始  如不传默认为1
  int32 page_index = 7;
  //每页多少条数据  如不传 则默认为15条
  int32 page_size = 8;
  //活动id
  int32 promotion_id = 9;
  //活动类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 10;
}
message GetPromotionOperateLogListResponse{
  int32 code = 1;
  string message = 2;
  repeated PromotionOperateLog data = 3;
  int32 total = 4;
}
message PromotionOperateLog{
  //操作类型 1撤销 2编辑活动 3 创建活动 4删除
  int32 operate_type = 1;
  //活动名称
  string promotion_name = 2;
  //活动id
  int32 promotion_id = 3;
  //活动类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 4;
  //活动优惠信息
  string promotion_info = 5;
  //门店名称
  string shop_name = 6;
  //门店财务编码
  string finance_code = 7;
  //商品sku_id
  int32 sku_id = 8;
  //创建人用户名
  string create_user_name = 9;
  //创建人用户id
  string create_user_id = 10;
  //操作人用户id
  string operate_user_id = 11;
  //操作人用户名
  string operate_user_name = 12;
  //操作时间
  string create_time = 13;
  //id记录号
  int32 id = 14;
}

message StorePromotionOperateLogRequest{
  repeated StorePromotionOperateLog log_list = 1;

}
message StorePromotionOperateLog {
  string operate_user_id = 1;
  string operate_user_name = 2;
  int32 operate_type = 3;
  string promotion_name = 4;
  int32 promotion_id = 5;
  int32 promotion_type = 6;
  string params = 7;
  string shop_name = 8;
  string finance_code = 9;
  int32 sku_id = 10;
  string create_user_name = 11;
  string create_user_id = 12;
}

package dto

type DigitalOrderPayForm struct {
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	TransType int32 `json:"transType,omitempty"`
	//商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
	OutTradeNo string `json:"outTradeNo,omitempty"`
	//商户订单号
	OrderId string `json:"orderId,omitempty"`
	//实付金额 单位分
	PayPrice float64 `json:"payPrice"`
	//订单金额 单位分
	TotalPrice float64 `json:"totalPrice"`
	//优惠金额 单位分
	Discount int32 `json:"discount"`
	//微信用户标识 JSAPI 支付时必传
	Openid string `json:"openid,omitempty"`
	//子商户公众账号ID 微信小程序必传
	SubAppId string `json:"subAppId,omitempty"`
	//商品编号
	ProductId string `json:"productId,omitempty"`
	//商品名称 最长 32 字节
	ProductName string `json:"productName,omitempty"`
	//商品描述
	ProductDesc string `json:"productDesc,omitempty"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl,omitempty"`
	//客户端 IP
	ClientIP string `json:"clientIP,omitempty"`
	//商户号
	MerchantId string `json:"merchantId,omitempty"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo,omitempty"`
	//订单有效时间（分钟）
	ValidTime int `json:"validTime,omitempty"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"orderPayType"`
}

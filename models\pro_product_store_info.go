package models

import "time"

type ProProductStoreInfo struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	// skuId
	SkuId int `json:"sku_id" xorm:"default 0 INT 'sku_id'"`

	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`

	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)
	ChannelId int `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`

	// 上下架状态（1-上架，0-下架）
	UpDownState int `json:"up_down_state" xorm:"default 0 comment('上下架状态（1-上架，0-下架）') INT 'up_down_state'"`

	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int `json:"status" xorm:"default 0 comment('1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ') INT 'status'"`

	// 建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"`
	//市场价
	MarketPrice int `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	// 0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	IsDistribution int `json:"is_distribution" xorm:"default 0 comment('0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功') INT 'is_distribution'"`

	// 铺品或者上架报错信息
	SyncError string `json:"sync_error" xorm:"not null comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`

	// 第三方回写的skuID
	SkuThirdId string `json:"sku_third_id" xorm:"default '' comment('第三方回写的skuID') VARCHAR(50) 'sku_third_id'"`

	// 商品添加日期
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`

	// 商品最后更新日期
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//商品名称
	ProductName string `json:"product_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'product_name'"`
	//商品描述
	ProductDesc string `json:"product_desc" xorm:"default '' comment('商品描述') VARCHAR(255) 'product_desc'"`
	ProductPic  string `json:"product_pic" xorm:"default '' comment('商品图片') VARCHAR(255) 'product_pic'"`

	//商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	ProductType int `json:"product_type" xorm:"default 1 comment('商品类型：1-商品，2-服务 3-活体') INT 'product_type'"`
	//服务时长
	ServiceDuration int `json:"service_duration" xorm:"default 0 comment('服务时长') INT 'service_duration'"`
	//出生日期
	BirthDate string `json:"birth_date" xorm:"default 'null' comment('出生日期') VARCHAR(255) 'birth_date'"`
	//宠物品种
	PetVariety string `json:"pet_variety" xorm:"default '' comment('宠物品种') VARCHAR(255) 'pet_variety'"`
	//宠物品种名称
	PetVarietyName string `json:"pet_variety_name" xorm:"default '' comment('宠物品种名称') VARCHAR(255) 'pet_variety_name'"`
	//条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('条码') VARCHAR(36) 'bar_code'"`
}

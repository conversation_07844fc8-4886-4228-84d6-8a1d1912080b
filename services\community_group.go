package services

import (
	"context"
	"errors"
	"fmt"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/utils"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

type CommunityGroupService struct {
}

func (c *CommunityGroupService) MerberOrder(ctx context.Context, in *oc.MerberOrderReq) (*oc.MerberOrderRes, error) {
	out := &oc.MerberOrderRes{}
	pageSize := cast.ToInt(in.PageSize)
	pageIndex := cast.ToInt(in.PageIndex)
	db := GetDBConn().Table("order_main_group").Alias("omg").
		Join("left", "order_main as om", "omg.parent_order_sn=om.order_sn").
		Join("left", "refund_order as r", "omg.parent_order_sn=r.order_sn").
		Where("omg.order_group_activity_id=? and om.order_status<>10 and om.order_status<>0", in.Id)

	if in.Key != "" {
		if len(in.Key) == 11 {
			db.Where("omg.receiver_mobile = ?", in.Key)
		} else {
			db.Where("omg.order_sn = ?", in.Key)
		}
	}

	cloneSession := db.Clone()
	defer cloneSession.Close()

	total, err := cloneSession.Count()
	if err != nil {
		return out, err
	}
	out.Total = cast.ToInt32(total)

	if out.Total > 0 {
		var group []*models.OrderMainGroupExt
		err = db.Select("omg.*, om.order_status, r.refund_state").Limit(pageSize, pageIndex*pageSize-pageSize).
			OrderBy("id desc").Find(&group)

		if err != nil {
			return out, errors.New("查询失败，请稍后重试")
		}

		for _, v := range group {
			//订单状态处理 订单状态  1已接单 2退款中 3已取消  4已完成
			status := 1
			if v.OrderStatus == 30 {
				status = 4
			} else if v.OrderStatus == 0 {
				status = 3
			} else if v.RefundState == 1 {
				status = 2
			}

			tmp := &oc.MemberOrderData{
				OrderSn:      v.OrderSn,
				OrderStatus:  cast.ToInt32(status),
				Mbmber:       v.ReceiverName + " " + v.ReceiverMobile,
				CreatedAt:    string([]byte(v.CreatedAt.String())[:19]),
				GroupAddress: v.ReceiverAddress,
			}

			//查询订单对应的商品
			var orderProduct []*models.OrderProduct
			db.Table("order_product").Where("order_sn=?", v.OrderSn).Find(&orderProduct)
			if len(orderProduct) > 0 {
				for _, v2 := range orderProduct {
					product := &oc.OrderProduct{
						GoodsImg:  v2.Image,
						GoodsName: v2.ProductName,
						GoodsNum:  v2.Number,
						Specs:     v2.Specs,
					}
					tmp.GoodsList = append(tmp.GoodsList, product)
				}
			}
			out.Data = append(out.Data, tmp)
		}
	}

	return out, nil
}

func (c *CommunityGroupService) GroupCompleted(ctx context.Context, in *oc.GroupCompletedReq) (*oc.BaseResponseNew, error) {
	out := &oc.BaseResponseNew{
		Msg: "提交成功",
	}
	db := GetDBConn().NewSession()
	defer db.Close()

	info := &models.OrderGroupActivity{}
	has, err := db.Where("id=?", in.Id).Get(info)
	if err != nil {
		glog.Error("完成团单更新团订单完成状态失败：", in, err)
		return out, err
	}
	if !has {
		out.Msg = "ID不存在"
		return out, errors.New("id不存在")
	}

	//只要发现一个团订单是电商仓快递配送并且没有发货，则不能确认收货
	start := info.CreatedAt.Add(-24 * time.Hour).Format(kit.DATETIME_LAYOUT)
	var orderMainList []*models.OrderMain
	err = db.SQL("SELECT id,order_status,order_status_child,delivery_type FROM order_main om WHERE order_sn in(SELECT parent_order_sn FROM order_main_group WHERE order_group_activity_id = ? ) and create_time > ?;", in.Id, start).
		Find(&orderMainList)
	if err != nil {
		glog.Error("完成团单更新团订单完成状态失败：", in, err)
		return out, err
	}
	for _, v := range orderMainList {
		if v.DeliveryType == 1 && v.OrderStatus == 20 && v.OrderStatusChild != 20202 {
			return out, errors.New("存在团员订单未发货")
		}
	}
	//更新团状态为确认收货
	_, err = db.Exec("update order_group_activity set is_receive=1 where id=?", in.Id)
	if err != nil {
		glog.Error("完成团单更新团订单完成状态失败：", in, err)
		return out, err
	}

	return out, err

	/*var list []*models.OrderMainGroup
	db.Table("order_main_group").Select("parent_order_sn").Where("order_group_activity_id=?", in.Id).Find(&list)

	if len(list) == 0 {
		return out, errors.New("ID不存在")
	}


	var orderSn []string
	for _, v := range list {
		orderSn = append(orderSn, v.OrderSn)
	}
	//更新订单状态为完成
	update := &models.OrderMain{
		OrderStatus:      30,
		OrderStatusChild: 20106,
		ConfirmTime:      time.Now(),
	}
	db.Begin()
	_, err := db.Table("order_main").Where("order_status=20").In("order_sn", orderSn).Update(update)
	_, err = db.Table("order_main").Where("order_status=20").In("parent_order_sn", orderSn).Update(update)
	if err != nil {
		db.Rollback()
		glog.Error("完成团单更新订单完成状态失败：", in, err)
		return out, err
	}

	//更新团状态为确认收货
	_, err = db.Exec("update order_group_activity set is_receive=1 where id=?", in.Id)
	if err != nil {
		db.Rollback()
		glog.Error("完成团单更新团订单完成状态失败：", in, err)
		return out, err
	}
	if err = db.Commit(); err != nil {
		glog.Error("团单完成订单提交失败，参数:"+kit.JsonEncode(in), err)
		return out, err
	}

	return out, err*/
}

func (c *CommunityGroupService) MemberCompleted(ctx context.Context, in *oc.MemberCompletedReq) (*oc.BaseResponseNew, error) {
	out := &oc.BaseResponseNew{
		Msg: "提交成功",
	}
	db := GetDBConn()
	//更新订单状态为完成
	update := &models.OrderMain{
		OrderStatus:      30,
		OrderStatusChild: 20106,
		ConfirmTime:      time.Now(),
	}

	orderMain := &models.OrderMain{}
	_, err := db.Where("order_sn=?", in.OrderSn).Cols("delivery_type", "order_status", "order_status_child").Get(orderMain)
	if err != nil {
		glog.Error("更新订单完成状态失败：", in, err)
		return out, err
	}
	// 电商发货时必须要发货才能操作确认收货
	if orderMain.DeliveryType == 1 && orderMain.OrderStatus == 20 && orderMain.OrderStatusChild != 20202 {
		out.Msg = "请检查订单已经是全部发货状态"
		return out, errors.New(out.Msg)
	}

	affected, err := db.Table("order_main").
		Where("(order_sn=? or parent_order_sn=?) and is_virtual=0 and order_status not in(0,10) and order_status_child<>20107", in.OrderSn, in.OrderSn).Update(update)
	if err != nil {
		glog.Error("更新订单完成状态失败：", in, err)
		return out, err
	}

	if affected == 0 {
		out.Msg = "更新了0条数据，请检查订单号是否正确或该订单已经是完成状态"
		return out, errors.New("更新了0条数据，请检查订单号是否正确或该订单已经是完成状态")
	}

	return out, nil
}

// 开团
func (c *CommunityGroupService) CommunityGroupOpen(ctx context.Context, in *oc.CommunityGroupOpenRequest) (out *oc.CommunityGroupOpenResponse, err error) {
	out = new(oc.CommunityGroupOpenResponse)
	gaRes := new(dac.GroupActivityGetResponse)

	// A、参数处理
	inStr := kit.JsonEncode(in)
	var groupActivity models.OrderGroupActivity
	if err = kit.JsonDecode([]byte(inStr), &groupActivity); err != nil {
		out.Msg = "开团失败，稍后再试！"
		out.Error = err.Error()
		glog.Error("请求开团CommunityGroupOpen参数解析失败：", in, err)
		return
	}

	// B-1、获取店铺团活动
	dacClient := dac.GetDataCenterClient()
	if gaRes, err = dacClient.CG.GroupActivityGet(dacClient.Ctx, &dac.GroupActivityGetRequest{
		StoreFinanceCode: in.StoreFinanceCode,
		Status:           1,
	}); err != nil {
		out.Msg = "开团失败，稍后再试！"
		out.Error = err.Error()
		glog.Error("开团CommunityGroupOpen请求GroupActivityGet失败：", in, err)
		return
	}
	endTimeUnit, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, gaRes.EndAt, time.Local)
	if gaRes.Status != 1 || endTimeUnit.Unix() <= time.Now().Unix() {
		glog.Info("团购活动已结束时间：", kit.JsonEncode(gaRes), ",时间戳", endTimeUnit.Unix(), "当前时间:", time.Now())
		out.Msg = "团购活动已结束！"
		return
	}
	// B-2、获取团长个人信息（头像，名称）
	var member models.UpetMember
	upetDb := GetUPetDBConn()
	_, err = upetDb.Select("member_id,member_name,member_avatar,member_avatar,distri_state").Where("scrm_user_id = ?", in.MemberId).Get(&member)
	if err != nil || member.MemberId < 1 {
		out.Msg = "获取团长信息失败！"
		out.Error = "未获取到商城用户信息，" + in.MemberId
		glog.Error("开团CommunityGroupOpen获取用户信息失败：", in, err)
		return
	}
	// 临时需求，开关控制，开关开启并且非分销员不能开团
	if member.DistriState != 2 && config.GetString("show_open_btn") == "1" {
		out.Msg = "非分销员不允许开团！"
		glog.Error("开团CommunityGroupOpen不符合条件：", in, member, config.GetString("show_open_btn"))
		return
	}

	//获取店铺名称
	storeInfo, err := dacClient.RPC.QueryStoreInfo(ctx, &dac.StoreInfoRequest{
		FinanceCode: []string{in.StoreFinanceCode},
	})
	if err != nil {
		out.Msg = "获取店铺信息失败！"
		out.Error = "未获取到店铺信息，" + in.MemberId
		glog.Error("开团CommunityGroupOpen获取用店铺息失败：", in, err)
		return
	}
	for _, v := range storeInfo.Details {
		if v.FinanceCode == in.StoreFinanceCode {
			groupActivity.StoreName = v.Name
		}
	}

	// c、入库操作
	db := GetDBConn()
	groupActivity.PromotionGroupActivityId = gaRes.Id
	groupActivity.MemberName = in.NickName
	groupActivity.MemberProfile = GetUserAvatar(in.AvatarUrl)
	groupActivity.MinAmount = cast.ToInt32(gaRes.MinAmount)
	groupActivity.CreatedAt = time.Now()
	groupActivity.StartTime = groupActivity.CreatedAt
	groupActivity.EndTime = time.Now().Add(time.Duration(gaRes.MustHours) * time.Hour)
	groupActivity.UpdatedAt = time.Now()
	groupActivity.Lng = in.Lng
	groupActivity.Lat = in.Lat
	groupActivity.DeliverDays = gaRes.DeliverDays
	if in.DisMemberId > 0 { // 海报分销员id，对应的是电商upet_member表的member_id
		var disMember models.UpetMember
		if _, err := upetDb.Select("scrm_user_id,distri_chainid").Where("member_id = ?", in.DisMemberId).Get(&disMember); err != nil {
			glog.Error("开团CommunityGroupOpen获取海报分销用户信息失败：", err, in)
		} else {
			groupActivity.ShopDisMemberId = disMember.ScrmUserId
			groupActivity.ShopDisChainId = disMember.DistriChainid
		}
	}
	if member.DistriState == 2 { // 分销员判断
		groupActivity.IsDis = 1
		groupActivity.CommissionRate = cast.ToInt32(config.GetString("community_group_commission_rate"))
		upetMemberChain := models.UpetMemberChain{}
		if _, err = upetMemberChain.GetByScrmUserId(upetDb, in.MemberId); err != nil {
			out.Msg = "获取团长信息失败！"
			out.Error = "未获取到商城用户信息，" + in.MemberId
			glog.Error("开团CommunityGroupOpen获取用户分销信息失败：", in, err)
			return
		}
		groupActivity.DisChainName = upetMemberChain.ChainName
		groupActivity.DisChainFinanceCode = upetMemberChain.ChainAccountId
	}

	groupActivity.EnReceiverMobile = utils.MobileEncrypt(groupActivity.ReceiverMobile)
	groupActivity.ReceiverMobile = MobileReplaceWithStar(groupActivity.ReceiverMobile)

	if _, err = db.Insert(&groupActivity); err != nil {
		out.Msg = "开团失败，稍后再试！"
		glog.Error("请求开团CommunityGroupOpen失败：", in, err)
		return
	}

	out.Data = &oc.CommunityGroupOpenData{
		GroupId:    groupActivity.Id,
		TemplateId: config.GetString("community_group_template_id"),
	}
	out.Msg = "ok"
	return
}

// ActivityList 获取可以参加的团列表
func (c CommunityGroupService) ActivityList(ctx context.Context, in *oc.CommunityGroupActivityListRequest) (out *oc.CommunityGroupActivityListResponse, e error) {
	out = &oc.CommunityGroupActivityListResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("团长拼团 CommunityGroupService_ActivityList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.FinanceCode == "" {
		out.Message = "参数错误"
		return
	}
	if in.PageSize < 1 {
		in.PageSize = 2
	}

	var list []*models.OrderGroupActivity
	query := GetDBConn().Table(&models.OrderGroupActivity{})
	query.Where("store_finance_code=? ", in.FinanceCode).
		Where("end_time>NOW() and `status` IN (0, 1)").
		Limit(cast.ToInt(in.PageSize))

	if in.SortType == 0 {
		query.OrderBy("status asc,IF(min_amount>pay_amount,min_amount-pay_amount,pay_amount) asc,created_at desc")
	}
	total, err := query.FindAndCount(&list)
	if err != nil { //没有的时候直接返回，当正常处理
		out.Code = 200
		out.Data = make([]*oc.CommunityGroupActivityListResponse_List, 0)
		glog.Error("参团列表ActivityList接口出错:", in, err)
		return
	}
	if len(list) == 0 {
		out.Code = 200
		out.Data = make([]*oc.CommunityGroupActivityListResponse_List, 0)
		return
	}
	out.Total = cast.ToInt32(total)
	now := time.Now()
	for i := 0; i < len(list); i++ {
		listData := &oc.CommunityGroupActivityListResponse_List{
			Id:       cast.ToInt32(list[i].Id),
			NickName: list[i].MemberName,
			Profile:  list[i].MemberProfile,
			Amount:   list[i].MinAmount - list[i].PayAmount,
			Time:     cast.ToInt32(list[i].EndTime.Sub(now).Seconds()),
			TakeType: list[i].FinalTakeType,
		}
		if listData.Amount < 0 {
			listData.Amount = 0
		}
		out.Data = append(out.Data, listData)
	}
	out.Code = 200
	return
}

// Detail 团详情
func (c CommunityGroupService) Detail(ctx context.Context, in *oc.CommunityGroupDetailRequest) (out *oc.CommunityGroupDetailResponse, e error) {
	out = &oc.CommunityGroupDetailResponse{Code: 400}

	defer func() {
		if out.Code != 200 {
			glog.Info("团长拼团 CommunityGroupService_Detail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	info := &models.OrderGroupActivity{}
	query := GetDBConn().Table(&models.OrderGroupActivity{})
	has, err := query.Where("id=?", in.Id).Get(info)
	if err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "团信息不存在"
		return
	}
	mobile := info.ReceiverMobile
	if in.ScrmId != "" {
		if has, err := GetDBConn().Table("order_main_group").
			Where("member_id=? and order_group_activity_id=?", in.ScrmId, in.Id).Exist(); err != nil {
		} else if has {
			mobile = utils.MobileDecrypt(info.EnReceiverMobile)
		}
	}

	out.Data = &oc.CommunityGroupDetailResponse_Data{
		Id:              cast.ToInt32(info.Id),
		MemberName:      info.MemberName,
		MemberProfile:   info.MemberProfile,
		CreatedAt:       info.CreatedAt.Format(kit.DATETIME_LAYOUT),
		Time:            cast.ToInt32(info.EndTime.Sub(time.Now()).Seconds()),
		EndTime:         info.EndTime.Format(kit.DATETIME_LAYOUT),
		MinAmount:       info.MinAmount,
		PayAmount:       info.PayAmount,
		FinalTakeType:   info.FinalTakeType,
		ReceiverName:    info.ReceiverName,
		ReceiverAddress: info.ReceiverAddress,
		ReceiverMobile:  mobile,
		Status:          info.Status,
		FinanceCode:     info.StoreFinanceCode,
		IsSubscribe:     info.IsSubscribe,
		Lng:             info.Lng,
		Lat:             info.Lat,
		//MemberId:        info.MemberId,
		DeliverDays: info.DeliverDays,
	}

	if out.Data.Time < 0 {
		out.Data.Time = 0
	}

	startTime := info.CreatedAt.Format(kit.DATETIME_LAYOUT)
	endTime := info.EndTime.Add(8 * time.Hour).Format(kit.DATETIME_LAYOUT)

	//团成员的订单列表
	var memberOrders []*models.OrderMainGroupOrders
	query.Table("order_main_group").
		Join("left", "order_main", "order_main.parent_order_sn=order_main_group.parent_order_sn").
		Where("order_main_group.order_group_activity_id = ? and order_main_group.member_id = ?", info.Id, in.ScrmId).
		Where("order_main.order_status >= 20 and order_main.create_time BETWEEN ? and ?", startTime, endTime).
		Where("order_main.parent_order_sn <> ''").
		Select("order_main.id as order_id,order_main.order_sn")
	_ = query.Limit(2).Find(&memberOrders)
	for k, _ := range memberOrders {
		out.Data.Orders = append(out.Data.Orders, &oc.CommunityGroupDetailResponse_Data_MemberOrders{
			OrderId: memberOrders[k].OrderId,
			OrderSn: memberOrders[k].OrderSn,
		})
	}

	//百分比显示
	if info.PayAmount >= info.MinAmount {
		out.Data.Progress = 100.00
	} else {
		out.Data.Progress = cast.ToFloat32(info.PayAmount) / cast.ToFloat32(info.MinAmount) * 100.00
	}
	switch info.DeliverDays {
	case 0:
		out.Data.ExpectedDesc = "成团当日"
	case 1:
		out.Data.ExpectedDesc = "成团次日"
	default:
		out.Data.ExpectedDesc = fmt.Sprintf("成团后%d日内", info.DeliverDays)
	}
	if in.ScrmId == info.MemberId {
		out.Data.IsGrouper = 1
	}
	if in.ReturnDetail > 0 {
		//查出所有的拼团信息
		var details []*models.OrderMainGroupDetailsList
		query.Table("order_main_group").
			Where("order_group_activity_id = ?", info.Id).
			Where("pay_amount>0").
			Select("member_name,member_profile,created_at,pay_amount").
			OrderBy("id desc")
		if in.ReturnDetail == 1 { //特定返回6条数据
			query.Limit(6)
		}
		err = query.Find(&details)
		if err != nil {
			out.Message = err.Error()
			return
		}
		for i := 0; i < len(details); i++ {
			out.Data.GroupDetails = append(out.Data.GroupDetails, &oc.CommunityGroupDetailResponse_Data_GroupDetails{
				Profile:   details[i].MemberProfile,
				NickName:  details[i].MemberName,
				PayAmount: details[i].PayAmount,
				CreatedAt: details[i].CreatedAt.Format(kit.DATETIME_LAYOUT),
			})
		}
	}
	out.Code = 200
	return
}

func (c CommunityGroupService) IsGroup(ctx context.Context, in *oc.IsGroupReq) (*oc.IsGroupRes, error) {
	out := &oc.IsGroupRes{Data: &oc.IsGroupRes_Data{IsShowOpenBtn: 1}}
	db := GetDBConn()

	upetDb := GetUPetDBConn()
	upetMemberModel := &models.UpetMember{}
	if has, err := upetDb.Table("upet_member").Select("distri_state,distri_chainid").Where("scrm_user_id = ?", in.MemberId).Get(upetMemberModel); err != nil {
		return out, errors.New("查询会员信息 " + err.Error())
	} else if !has {
		return out, nil
	} else if upetMemberModel.DistriState == 2 {
		out.Data.DisType = 2
		if upetMemberModel.DistriChainid > 0 {
			out.Data.DisType = 1
		}
	}
	// 临时需求，开关控制，开启并且非分销员不展示按钮
	if upetMemberModel.DistriState != 2 && config.GetString("show_open_btn") == "1" {
		out.Data.IsShowOpenBtn = 0
	}

	has, _ := db.Table("order_group_activity").Where("member_id=?", in.MemberId).Exist()
	if has {
		out.Data.IsGroup = 1
	}
	//判断是不是分销员
	has, _ = db.Table("order_group_activity").Where("member_id=? and is_dis=1", in.MemberId).Exist()
	if has {
		out.Data.HasCommission = 1
	}
	return out, nil
}

func (c CommunityGroupService) MyGroup(ctx context.Context, in *oc.MyGroupReq) (*oc.MyGroupRes, error) {
	out := &oc.MyGroupRes{}
	db := GetDBConn()

	day := utils.GetYearMonthToDay(cast.ToInt(in.Year), cast.ToInt(in.Month))
	startTime := in.Year + "-" + in.Month + "-" + "01"
	endTime := in.Year + "-" + in.Month + "-" + cast.ToString(day)

	var model models.OrderGroupActivity
	has, err := db.Table("order_group_activity").
		Select("sum(pay_amount) pay_amount, sum(wait_amount) wait_amount, sum(paid_amount) paid_amount, min_amount").
		Where("member_id=? and is_dis=1 and status=1", in.MemberId).Where("created_at>=? and created_at<=? ", startTime, endTime).Get(&model)

	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	if has {
		out.PayAmount = model.PayAmount
		out.WaitAmount = model.WaitAmount
		out.PaidAmount = model.PaidAmount
	}
	out.Commissions = config.GetString("community_group_commission_rate") + "%"

	return out, nil
}

func (c CommunityGroupService) MyGroupList(ctx context.Context, in *oc.MyGroupListReq) (*oc.MyGroupListRes, error) {
	out := &oc.MyGroupListRes{}
	pageSize := cast.ToInt(in.PageSize)
	pageIndex := cast.ToInt(in.PageIndex)
	day := utils.GetYearMonthToDay(cast.ToInt(in.Year), cast.ToInt(in.Month))
	if in.Year == "" || in.Month == "" {
		dateStr := time.Now().Format("2006-01")
		if in.Year == "" {
			in.Year = dateStr[0:4]
		}
		if in.Month == "" {
			in.Month = dateStr[5:]
		}
	}

	startTime := in.Year + "-" + in.Month + "-" + "01" + " 00:00:00"
	endTime := in.Year + "-" + in.Month + "-" + cast.ToString(day) + " 23:59:59"

	db := GetDBConn().Table("order_group_activity").Alias("o").
		Join("left", "datacenter.store_business_setup as s", "o.store_finance_code=s.finance_code").
		Where("member_id=? and is_dis=?", in.MemberId, in.IsDis)
	if in.IsDis == 1 { //分销才日期筛选
		db.Where("o.created_at>=? and o.created_at<=? ", startTime, endTime)
	}
	cloneSession := db.Clone()
	defer cloneSession.Close()

	total, err := cloneSession.Count()
	if err != nil {
		return out, err
	}
	out.Total = cast.ToInt32(total)

	if out.Total > 0 {
		var group []*models.OrderGroupActivityExt
		err = db.Select("o.*,s.image").Limit(pageSize, pageIndex*pageSize-pageSize).OrderBy("id desc").Find(&group)
		if err != nil {
			return out, errors.New("查询失败，请稍后重试")
		}

		for _, v := range group {
			tmp := &oc.GroupActivity{
				Id:              cast.ToInt32(v.Id),
				Status:          v.Status,
				StoreName:       v.StoreName,
				StoreImg:        v.Image,
				ReceiverAddress: v.ReceiverAddress + " " + v.ReceiverName + " " + v.ReceiverMobile,
				PayMonery:       v.PayAmount,
				Transportation:  0,
				SurplusTime:     cast.ToInt32(v.EndTime.Unix() - time.Now().Unix()),
				FinalTakeType:   v.FinalTakeType,
				MinAmount:       v.MinAmount,
				IsReceive:       v.IsReceive,
			}

			out.Data = append(out.Data, tmp)
		}
	}

	return out, nil
}

// ParticipantList 参团、开团热度列表 只显示30分钟以内的
func (c *CommunityGroupService) ParticipantList(ctx context.Context, in *oc.CommunityGroupParticipantListRequest) (out *oc.CommunityGroupParticipantListResponse, e error) {
	out = &oc.CommunityGroupParticipantListResponse{}
	var list []*models.OrderGroupActivity

	engine := GetDBConn()

	startTime := time.Now().Add(-30 * time.Minute).Format(kit.DATETIME_LAYOUT)
	session := engine.NewSession()
	defer session.Close()
	session.Table(&models.OrderGroupActivity{}).
		Where("created_at > ? and store_finance_code = ?", startTime, in.FinanceCode).
		Select("member_name,member_profile,created_at").
		Limit(15).OrderBy("id desc")
	err := session.Find(&list)
	if err != nil {
		out.Message = err.Error()
		return
	}
	listLen := len(list)
	var joinList []*models.OrderMainGroupDetailsList
	if listLen < 30 {
		session1 := engine.NewSession()
		defer session1.Close()
		session1.Table("order_main_group").
			Join("left", "order_main", "order_main_group.parent_order_sn=order_main.parent_order_sn").
			Select("order_main_group.member_profile,order_main_group.created_at,order_main.member_name").
			Where("order_main_group.created_at > ? and order_main.shop_id=? and order_main.order_type = 15", startTime, in.FinanceCode).
			Limit(20 - listLen).
			Select("order_main_group.member_name,order_main_group.member_profile,order_main_group.created_at,(order_main.total-order_main.refund_amount) as pay_amount").
			OrderBy("order_main_group.id desc")
		err = session1.Find(&joinList)
		if err != nil {
			out.Message = err.Error()
			return
		}
	}
	joinLen := len(joinList)
	maxLen := listLen
	if joinLen > maxLen {
		maxLen = joinLen
	}
	for i := 0; i < maxLen; i++ {
		if i < listLen {
			out.Data = append(out.Data, &oc.CommunityGroupParticipantListResponse_List{
				NickName: list[i].MemberName,
				Profile:  list[i].MemberProfile,
				Time:     list[i].CreatedAt.Format(kit.DATETIME_LAYOUT),
				Type:     0,
			})
		}
		if i < joinLen {
			out.Data = append(out.Data, &oc.CommunityGroupParticipantListResponse_List{
				NickName: joinList[i].MemberName,
				Profile:  joinList[i].MemberProfile,
				Time:     joinList[i].CreatedAt.Format(kit.DATETIME_LAYOUT),
				Type:     1,
			})
		}
	}
	out.Code = 200
	return
}

// MessageSubscribe 消息订阅
func (c *CommunityGroupService) MessageSubscribe(ctx context.Context, in *oc.CommunityGroupMessageSubscribeRequest) (out *oc.BaseResponseNew, err error) {

	out = &oc.BaseResponseNew{}

	if in.Number == "" {
		err = errors.New("参数错误")
		return
	}

	egnine := GetDBConn()
	// 团消息订阅
	if in.Type == 0 {
		model := &models.OrderGroupActivity{
			IsSubscribe: 1,
		}
		_, _ = egnine.Where("id=? and member_id=?", in.Number, in.ScrmId).Cols("is_subscribe").Update(model)
	}

	// 订单消息订阅
	if in.Type == 1 {
		model := &models.OrderMainGroup{
			IsSubscribe: 1,
		}
		var parentOrderSn string
		egnine.Where("order_sn=?", in.Number).Cols("parent_order_sn").Limit(1).Get(&parentOrderSn)
		// 根据order_sn 找出父单号
		_, _ = egnine.Where("parent_order_sn=? and member_id=?", parentOrderSn, in.ScrmId).Cols("is_subscribe").Update(model)
	}

	return
}

func (c *CommunityGroupService) UpdateOrderGroup(ctx context.Context, in *oc.UpdateOrderGroupRequest) (out *oc.BaseResponseNew, err error) {
	egnine := GetDBConn()

	out = &oc.BaseResponseNew{}
	if in.PromotionGroupActivityId <= 0 {
		return nil, errors.New("参数不正确！")
	}

	if _, err := egnine.Where("promotion_group_activity_id = ? AND `status` = 0", in.PromotionGroupActivityId).Cols("`is_break`").
		Update(&models.OrderGroupActivity{IsBreak: 1}); err != nil {
		return nil, errors.New("更新失败！")
	}

	return out, nil
}

// boss 团长订单
func (c *CommunityGroupService) OrderList(ctx context.Context, in *oc.CommunityGroupOrderListRequest) (out *oc.CommunityGroupOrderListResponse, e error) {
	out = &oc.CommunityGroupOrderListResponse{Code: 400}

	session := GetDBConn().NewSession()
	defer session.Close()

	if in.RequestFrom > 0 {
		session.Table("order_group_activity").Alias("a").
			Join("left", "order_main_group as b", "a.id = b.order_group_activity_id").
			Join("left", "order_main as c", "b.parent_order_sn = c.parent_order_sn")
		isVirtual := 0
		if in.RequestFrom == 2 {
			isVirtual = 1
		}
		session.Where("c.is_virtual = ?", isVirtual)
	} else {
		session.Table("order_group_activity").Alias("a")
	}

	if in.GroupStatus != "" {
		session.Where("a.status=?", in.GroupStatus)
	}
	in.Keyword = strings.Trim(in.Keyword, " ")
	if len(in.Keyword) > 0 {
		switch in.SearchType {
		case "1": //团长手机
			var scrmUserIds []string
			err := GetUPetDBConn().Table("upet_member").
				Select("scrm_user_id").
				Where("member_mobile like ?", "%"+in.Keyword+"%").
				Where("scrm_user_id != ?", "").
				Find(&scrmUserIds)
			if err != nil {
				out.Message = "查询团长订单出错, " + err.Error()
				return
			}
			if len(scrmUserIds) > 0 {
				session.In("a.member_id", scrmUserIds)
			} else {
				session.Where("1=0")
			}
		case "2": //团编码
			session.Where("a.id = ?", in.Keyword)
		case "3": //财务编码
			session.Where("a.dis_chain_finance_code like ?", "%"+in.Keyword+"%")
		case "4": //团长单位名
			session.Where("a.dis_chain_name like ?", "%"+in.Keyword+"%")
		}
	}

	if in.GroupTakeType != "" {
		session.Where("a.final_take_type = ?", in.GroupTakeType)
	}
	switch in.TimeType {
	case "1":
		session.Where("a.created_at BETWEEN ? AND ?", in.StartTime, in.EndTime)
	case "2":
		session.Where("a.status = 1 and a.end_time BETWEEN ? AND ?", in.StartTime, in.EndTime)
	case "3":
		session.Where("a.status = 2 and a.end_time BETWEEN ? AND ?", in.StartTime, in.EndTime)
	}
	if len(in.ShopIds) > 0 {
		session.In("a.store_finance_code", in.ShopIds)
	}

	var list []*models.OrderGroupActivity

	var total int64
	if in.RequestFrom > 0 {
		sessionCount := session.Clone()
		defer sessionCount.Close()
		total, e = sessionCount.Select("count(distinct(a.id))").Count(&models.OrderGroupActivity{})
		if e != nil {
			out.Message = "总条数数据库查询出错！"
			out.Error = e.Error()
			glog.Info("OrderList总条数数据库查询出错：", e)
			return
		}
		e = session.Select("distinct(a.id),a.*").GroupBy("a.id").OrderBy("a.id desc").Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).Find(&list)
	} else {
		total, e = session.OrderBy("a.id desc").Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).FindAndCount(&list)
	}
	if e != nil {
		out.Message = "数据库查询出错！"
		out.Error = e.Error()
		glog.Info("OrderList查询数据库错误：", e)
		return
	}
	for i := 0; i < len(list); i++ {
		out.Details = append(out.Details, list[i].ToOcCommunityGroupOrderListResponseDetails(in.RequestFrom))
	}
	out.TotalCount = cast.ToInt32(total)
	out.Code = 200
	return
}

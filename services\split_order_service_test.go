package services

import (
	"order-center/dto"
	"order-center/models"
	"testing"
)

func TestSplitOrder(t *testing.T) {
	var splitOrderReqs []dto.SplitOrderReq
	splitOrderReq := dto.SplitOrderReq{
		Skuid:      1519525409,
		Stock:      1,
		Isgroup:    0,
		Isvirtual:  0,
		Groupskuid: 0,
	}
	splitOrderReqs = append(splitOrderReqs, splitOrderReq)
	splitOrderReq = dto.SplitOrderReq{
		Skuid:      1519553409,
		Stock:      1,
		Isgroup:    0,
		Isvirtual:  0,
		Groupskuid: 0,
	}
	splitOrderReqs = append(splitOrderReqs, splitOrderReq)
	splitOrderResp, _ := SplitOrder(splitOrderReqs, &models.OrderMain{}, 0, false, 5)
	println(splitOrderResp)

}

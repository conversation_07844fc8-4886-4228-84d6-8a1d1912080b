syntax = "proto3";

// 在线问诊 系统配置
package dgc;

// @Desc    	在线问诊 系统配置模块
// <AUTHOR>
// @Date		2021-10-13
service SystemService {
  // @Desc    	系统配置
  // <AUTHOR>
  // @Date		2021-10-13
  rpc SystemConfig(SystemConfigRequest) returns (SystemConfigResponse) {}
}

message SystemConfigRequest{
}
// 系统配置
message SystemConfigResponse{
  //医生排班时间内不接单开关：0关，1开
  int32 work_on_off = 1;
  //免费图文问诊时长（单位分钟）
  int32 free_image_text_duration = 2;
  //快速图文问诊时长（单位分钟）
  int32 quick_image_text_duration = 3;
  //找医生图文问诊时长（单位分钟）
  int32 find_image_text_duration = 4;
  //找医生电话问诊时长（单位分钟）
  int32 find_phone_duration = 5;
  //找医生视频问诊时长（单位分钟）
  int32 find_video_duration = 6;
  	//快速图文问诊价格（单位分）
  int32 quick_image_text_price  = 7;

}


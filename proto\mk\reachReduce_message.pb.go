// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/reachReduce_message.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//////////////////////////////////////////////// Dto    /////////////////////////////////////////////////////////////////////////////////
// 根据条件查询满减活动与店铺关联关系Dto
type ReachReducePromotionShopDto struct {
	// 促销活动
	PromotionDto *PromotionListDto `protobuf:"bytes,1,opt,name=promotionDto,proto3" json:"promotionDto"`
	// 促销活动和店铺关联关系
	PromotionShopDto *PromotionShopDto `protobuf:"bytes,2,opt,name=promotionShopDto,proto3" json:"promotionShopDto"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,3,rep,name=timeRanges,proto3" json:"timeRanges"`
	//促销活动
	PromotionReduce []*PromotionReduceDto `protobuf:"bytes,4,rep,name=promotionReduce,proto3" json:"promotionReduce"`
	// 创建日期
	CreateTime           string   `protobuf:"bytes,5,opt,name=createTime,proto3" json:"createTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReachReducePromotionShopDto) Reset()         { *m = ReachReducePromotionShopDto{} }
func (m *ReachReducePromotionShopDto) String() string { return proto.CompactTextString(m) }
func (*ReachReducePromotionShopDto) ProtoMessage()    {}
func (*ReachReducePromotionShopDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{0}
}

func (m *ReachReducePromotionShopDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReducePromotionShopDto.Unmarshal(m, b)
}
func (m *ReachReducePromotionShopDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReducePromotionShopDto.Marshal(b, m, deterministic)
}
func (m *ReachReducePromotionShopDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReducePromotionShopDto.Merge(m, src)
}
func (m *ReachReducePromotionShopDto) XXX_Size() int {
	return xxx_messageInfo_ReachReducePromotionShopDto.Size(m)
}
func (m *ReachReducePromotionShopDto) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReducePromotionShopDto.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReducePromotionShopDto proto.InternalMessageInfo

func (m *ReachReducePromotionShopDto) GetPromotionDto() *PromotionListDto {
	if m != nil {
		return m.PromotionDto
	}
	return nil
}

func (m *ReachReducePromotionShopDto) GetPromotionShopDto() *PromotionShopDto {
	if m != nil {
		return m.PromotionShopDto
	}
	return nil
}

func (m *ReachReducePromotionShopDto) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReachReducePromotionShopDto) GetPromotionReduce() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduce
	}
	return nil
}

func (m *ReachReducePromotionShopDto) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

/////////////////////////////////////////////////  Request  ///////////////////////////////////////////////////////////////////////////////
// 新增
type ReachReduceAddRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,3,opt,name=promotion,proto3" json:"promotion"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,5,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	PromotionShop []*PromotionShopDto `protobuf:"bytes,6,rep,name=promotionShop,proto3" json:"promotionShop"`
	// 排除的商品skuid
	ExcludeSkuIds []int32 `protobuf:"varint,7,rep,packed,name=excludeSkuIds,proto3" json:"excludeSkuIds"`
	// 满减优惠
	PromotionReduce      []*PromotionReduceDto `protobuf:"bytes,8,rep,name=promotionReduce,proto3" json:"promotionReduce"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ReachReduceAddRequest) Reset()         { *m = ReachReduceAddRequest{} }
func (m *ReachReduceAddRequest) String() string { return proto.CompactTextString(m) }
func (*ReachReduceAddRequest) ProtoMessage()    {}
func (*ReachReduceAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{1}
}

func (m *ReachReduceAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceAddRequest.Unmarshal(m, b)
}
func (m *ReachReduceAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceAddRequest.Marshal(b, m, deterministic)
}
func (m *ReachReduceAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceAddRequest.Merge(m, src)
}
func (m *ReachReduceAddRequest) XXX_Size() int {
	return xxx_messageInfo_ReachReduceAddRequest.Size(m)
}
func (m *ReachReduceAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceAddRequest proto.InternalMessageInfo

func (m *ReachReduceAddRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReachReduceAddRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReachReduceAddRequest) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReachReduceAddRequest) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReachReduceAddRequest) GetPromotionShop() []*PromotionShopDto {
	if m != nil {
		return m.PromotionShop
	}
	return nil
}

func (m *ReachReduceAddRequest) GetExcludeSkuIds() []int32 {
	if m != nil {
		return m.ExcludeSkuIds
	}
	return nil
}

func (m *ReachReduceAddRequest) GetPromotionReduce() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduce
	}
	return nil
}

// 更新
type ReachReduceUpdateRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 需要更新活动ID
	PromotionId int32 `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,5,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 删除相关商品
	DeletePromotionProductId []int32 `protobuf:"varint,6,rep,packed,name=deletePromotionProductId,proto3" json:"deletePromotionProductId"`
	// 添加相关商品
	AddPromotionProduct []*PromotionProductDto `protobuf:"bytes,7,rep,name=addPromotionProduct,proto3" json:"addPromotionProduct"`
	// 满减优惠
	PromotionReduce      []*PromotionReduceDto `protobuf:"bytes,8,rep,name=promotionReduce,proto3" json:"promotionReduce"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ReachReduceUpdateRequest) Reset()         { *m = ReachReduceUpdateRequest{} }
func (m *ReachReduceUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*ReachReduceUpdateRequest) ProtoMessage()    {}
func (*ReachReduceUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{2}
}

func (m *ReachReduceUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceUpdateRequest.Unmarshal(m, b)
}
func (m *ReachReduceUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceUpdateRequest.Marshal(b, m, deterministic)
}
func (m *ReachReduceUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceUpdateRequest.Merge(m, src)
}
func (m *ReachReduceUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_ReachReduceUpdateRequest.Size(m)
}
func (m *ReachReduceUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceUpdateRequest proto.InternalMessageInfo

func (m *ReachReduceUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReachReduceUpdateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReachReduceUpdateRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *ReachReduceUpdateRequest) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReachReduceUpdateRequest) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReachReduceUpdateRequest) GetDeletePromotionProductId() []int32 {
	if m != nil {
		return m.DeletePromotionProductId
	}
	return nil
}

func (m *ReachReduceUpdateRequest) GetAddPromotionProduct() []*PromotionProductDto {
	if m != nil {
		return m.AddPromotionProduct
	}
	return nil
}

func (m *ReachReduceUpdateRequest) GetPromotionReduce() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduce
	}
	return nil
}

// 根据条件查询满减活动与店铺关联关系请求
type ReachReducePromotionShopQuery struct {
	// 店铺列表，逗号分割
	ShopIds string `protobuf:"bytes,1,opt,name=shopIds,proto3" json:"shopIds"`
	// 当前登录用户Id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	// 用户名称
	UserName string `protobuf:"bytes,11,opt,name=userName,proto3" json:"userName"`
	// 开始日期
	BeginDateStart string `protobuf:"bytes,3,opt,name=beginDateStart,proto3" json:"beginDateStart"`
	BeginDateEnd   string `protobuf:"bytes,4,opt,name=beginDateEnd,proto3" json:"beginDateEnd"`
	// 截止日期
	EndDateStart string `protobuf:"bytes,5,opt,name=endDateStart,proto3" json:"endDateStart"`
	EndDateEnd   string `protobuf:"bytes,6,opt,name=endDateEnd,proto3" json:"endDateEnd"`
	// 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
	State PromotionState `protobuf:"varint,7,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 指定活动id
	PromotionIds []int32 `protobuf:"varint,10,rep,packed,name=promotion_ids,json=promotionIds,proto3" json:"promotion_ids"`
	// 页索引
	PageIndex int32 `protobuf:"varint,8,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,9,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReachReducePromotionShopQuery) Reset()         { *m = ReachReducePromotionShopQuery{} }
func (m *ReachReducePromotionShopQuery) String() string { return proto.CompactTextString(m) }
func (*ReachReducePromotionShopQuery) ProtoMessage()    {}
func (*ReachReducePromotionShopQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{3}
}

func (m *ReachReducePromotionShopQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReducePromotionShopQuery.Unmarshal(m, b)
}
func (m *ReachReducePromotionShopQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReducePromotionShopQuery.Marshal(b, m, deterministic)
}
func (m *ReachReducePromotionShopQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReducePromotionShopQuery.Merge(m, src)
}
func (m *ReachReducePromotionShopQuery) XXX_Size() int {
	return xxx_messageInfo_ReachReducePromotionShopQuery.Size(m)
}
func (m *ReachReducePromotionShopQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReducePromotionShopQuery.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReducePromotionShopQuery proto.InternalMessageInfo

func (m *ReachReducePromotionShopQuery) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetBeginDateStart() string {
	if m != nil {
		return m.BeginDateStart
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetBeginDateEnd() string {
	if m != nil {
		return m.BeginDateEnd
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetEndDateStart() string {
	if m != nil {
		return m.EndDateStart
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetEndDateEnd() string {
	if m != nil {
		return m.EndDateEnd
	}
	return ""
}

func (m *ReachReducePromotionShopQuery) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *ReachReducePromotionShopQuery) GetPromotionIds() []int32 {
	if m != nil {
		return m.PromotionIds
	}
	return nil
}

func (m *ReachReducePromotionShopQuery) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ReachReducePromotionShopQuery) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 根据Id 删除活动与店铺关联关系
type DeletePromotonShopByIds struct {
	PromotionShopId []int32 `protobuf:"varint,1,rep,packed,name=promotionShopId,proto3" json:"promotionShopId"`
	//当前登录用户
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	// 当前登录用户名
	UserName             string   `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePromotonShopByIds) Reset()         { *m = DeletePromotonShopByIds{} }
func (m *DeletePromotonShopByIds) String() string { return proto.CompactTextString(m) }
func (*DeletePromotonShopByIds) ProtoMessage()    {}
func (*DeletePromotonShopByIds) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{4}
}

func (m *DeletePromotonShopByIds) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePromotonShopByIds.Unmarshal(m, b)
}
func (m *DeletePromotonShopByIds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePromotonShopByIds.Marshal(b, m, deterministic)
}
func (m *DeletePromotonShopByIds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePromotonShopByIds.Merge(m, src)
}
func (m *DeletePromotonShopByIds) XXX_Size() int {
	return xxx_messageInfo_DeletePromotonShopByIds.Size(m)
}
func (m *DeletePromotonShopByIds) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePromotonShopByIds.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePromotonShopByIds proto.InternalMessageInfo

func (m *DeletePromotonShopByIds) GetPromotionShopId() []int32 {
	if m != nil {
		return m.PromotionShopId
	}
	return nil
}

func (m *DeletePromotonShopByIds) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DeletePromotonShopByIds) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type ReachReduceProductQueryRequest struct {
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// spu
	ProductSpuId string `protobuf:"bytes,2,opt,name=productSpuId,proto3" json:"productSpuId"`
	// sku
	ProductSkuId string `protobuf:"bytes,3,opt,name=productSkuId,proto3" json:"productSkuId"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	// 页索引
	PageIndex int32 `protobuf:"varint,8,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,9,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReachReduceProductQueryRequest) Reset()         { *m = ReachReduceProductQueryRequest{} }
func (m *ReachReduceProductQueryRequest) String() string { return proto.CompactTextString(m) }
func (*ReachReduceProductQueryRequest) ProtoMessage()    {}
func (*ReachReduceProductQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{5}
}

func (m *ReachReduceProductQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceProductQueryRequest.Unmarshal(m, b)
}
func (m *ReachReduceProductQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceProductQueryRequest.Marshal(b, m, deterministic)
}
func (m *ReachReduceProductQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceProductQueryRequest.Merge(m, src)
}
func (m *ReachReduceProductQueryRequest) XXX_Size() int {
	return xxx_messageInfo_ReachReduceProductQueryRequest.Size(m)
}
func (m *ReachReduceProductQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceProductQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceProductQueryRequest proto.InternalMessageInfo

func (m *ReachReduceProductQueryRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *ReachReduceProductQueryRequest) GetProductSpuId() string {
	if m != nil {
		return m.ProductSpuId
	}
	return ""
}

func (m *ReachReduceProductQueryRequest) GetProductSkuId() string {
	if m != nil {
		return m.ProductSkuId
	}
	return ""
}

func (m *ReachReduceProductQueryRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *ReachReduceProductQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ReachReduceProductQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 根据条件查询满减活动与店铺关联关系响应
type ReachReducePromotionShopResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 当前页数据
	Data                 []*ReachReducePromotionShopDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ReachReducePromotionShopResponse) Reset()         { *m = ReachReducePromotionShopResponse{} }
func (m *ReachReducePromotionShopResponse) String() string { return proto.CompactTextString(m) }
func (*ReachReducePromotionShopResponse) ProtoMessage()    {}
func (*ReachReducePromotionShopResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{6}
}

func (m *ReachReducePromotionShopResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReducePromotionShopResponse.Unmarshal(m, b)
}
func (m *ReachReducePromotionShopResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReducePromotionShopResponse.Marshal(b, m, deterministic)
}
func (m *ReachReducePromotionShopResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReducePromotionShopResponse.Merge(m, src)
}
func (m *ReachReducePromotionShopResponse) XXX_Size() int {
	return xxx_messageInfo_ReachReducePromotionShopResponse.Size(m)
}
func (m *ReachReducePromotionShopResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReducePromotionShopResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReducePromotionShopResponse proto.InternalMessageInfo

func (m *ReachReducePromotionShopResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReachReducePromotionShopResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReachReducePromotionShopResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ReachReducePromotionShopResponse) GetData() []*ReachReducePromotionShopDto {
	if m != nil {
		return m.Data
	}
	return nil
}

//根据Id查询活动信息
type ReachReduceByIdResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	PromotionId int32  `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,6,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	PromotionShop []*PromotionShopDto `protobuf:"bytes,7,rep,name=promotionShop,proto3" json:"promotionShop"`
	// 相关商品
	PromotionProduct []*PromotionProductDto `protobuf:"bytes,8,rep,name=promotionProduct,proto3" json:"promotionProduct"`
	// 满减优惠
	PromotionReduce      []*PromotionReduceDto `protobuf:"bytes,9,rep,name=promotionReduce,proto3" json:"promotionReduce"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ReachReduceByIdResponse) Reset()         { *m = ReachReduceByIdResponse{} }
func (m *ReachReduceByIdResponse) String() string { return proto.CompactTextString(m) }
func (*ReachReduceByIdResponse) ProtoMessage()    {}
func (*ReachReduceByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{7}
}

func (m *ReachReduceByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceByIdResponse.Unmarshal(m, b)
}
func (m *ReachReduceByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceByIdResponse.Marshal(b, m, deterministic)
}
func (m *ReachReduceByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceByIdResponse.Merge(m, src)
}
func (m *ReachReduceByIdResponse) XXX_Size() int {
	return xxx_messageInfo_ReachReduceByIdResponse.Size(m)
}
func (m *ReachReduceByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceByIdResponse proto.InternalMessageInfo

func (m *ReachReduceByIdResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReachReduceByIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReachReduceByIdResponse) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *ReachReduceByIdResponse) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReachReduceByIdResponse) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReachReduceByIdResponse) GetPromotionShop() []*PromotionShopDto {
	if m != nil {
		return m.PromotionShop
	}
	return nil
}

func (m *ReachReduceByIdResponse) GetPromotionProduct() []*PromotionProductDto {
	if m != nil {
		return m.PromotionProduct
	}
	return nil
}

func (m *ReachReduceByIdResponse) GetPromotionReduce() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduce
	}
	return nil
}

// 根据门店Id查询满减信息
type ReachReduceQueryByShopIdResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message              string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PromotionReduceDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ReachReduceQueryByShopIdResponse) Reset()         { *m = ReachReduceQueryByShopIdResponse{} }
func (m *ReachReduceQueryByShopIdResponse) String() string { return proto.CompactTextString(m) }
func (*ReachReduceQueryByShopIdResponse) ProtoMessage()    {}
func (*ReachReduceQueryByShopIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{8}
}

func (m *ReachReduceQueryByShopIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceQueryByShopIdResponse.Unmarshal(m, b)
}
func (m *ReachReduceQueryByShopIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceQueryByShopIdResponse.Marshal(b, m, deterministic)
}
func (m *ReachReduceQueryByShopIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceQueryByShopIdResponse.Merge(m, src)
}
func (m *ReachReduceQueryByShopIdResponse) XXX_Size() int {
	return xxx_messageInfo_ReachReduceQueryByShopIdResponse.Size(m)
}
func (m *ReachReduceQueryByShopIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceQueryByShopIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceQueryByShopIdResponse proto.InternalMessageInfo

func (m *ReachReduceQueryByShopIdResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReachReduceQueryByShopIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReachReduceQueryByShopIdResponse) GetData() []*PromotionReduceDto {
	if m != nil {
		return m.Data
	}
	return nil
}

type ReachReduceProductQueryResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总记录条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 当前页数据
	Data                 []*PromotionProductDto `protobuf:"bytes,8,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ReachReduceProductQueryResponse) Reset()         { *m = ReachReduceProductQueryResponse{} }
func (m *ReachReduceProductQueryResponse) String() string { return proto.CompactTextString(m) }
func (*ReachReduceProductQueryResponse) ProtoMessage()    {}
func (*ReachReduceProductQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a023ba80bc779c85, []int{9}
}

func (m *ReachReduceProductQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceProductQueryResponse.Unmarshal(m, b)
}
func (m *ReachReduceProductQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceProductQueryResponse.Marshal(b, m, deterministic)
}
func (m *ReachReduceProductQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceProductQueryResponse.Merge(m, src)
}
func (m *ReachReduceProductQueryResponse) XXX_Size() int {
	return xxx_messageInfo_ReachReduceProductQueryResponse.Size(m)
}
func (m *ReachReduceProductQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceProductQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceProductQueryResponse proto.InternalMessageInfo

func (m *ReachReduceProductQueryResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReachReduceProductQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReachReduceProductQueryResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ReachReduceProductQueryResponse) GetData() []*PromotionProductDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*ReachReducePromotionShopDto)(nil), "mk.reachReducePromotionShopDto")
	proto.RegisterType((*ReachReduceAddRequest)(nil), "mk.reachReduceAddRequest")
	proto.RegisterType((*ReachReduceUpdateRequest)(nil), "mk.reachReduceUpdateRequest")
	proto.RegisterType((*ReachReducePromotionShopQuery)(nil), "mk.reachReducePromotionShopQuery")
	proto.RegisterType((*DeletePromotonShopByIds)(nil), "mk.deletePromotonShopByIds")
	proto.RegisterType((*ReachReduceProductQueryRequest)(nil), "mk.reachReduceProductQueryRequest")
	proto.RegisterType((*ReachReducePromotionShopResponse)(nil), "mk.reachReducePromotionShopResponse")
	proto.RegisterType((*ReachReduceByIdResponse)(nil), "mk.reachReduceByIdResponse")
	proto.RegisterType((*ReachReduceQueryByShopIdResponse)(nil), "mk.reachReduceQueryByShopIdResponse")
	proto.RegisterType((*ReachReduceProductQueryResponse)(nil), "mk.reachReduceProductQueryResponse")
}

func init() { proto.RegisterFile("mk/reachReduce_message.proto", fileDescriptor_a023ba80bc779c85) }

var fileDescriptor_a023ba80bc779c85 = []byte{
	// 783 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xdd, 0x6e, 0xd3, 0x4a,
	0x10, 0x96, 0xe3, 0xfc, 0x79, 0xd2, 0xe6, 0xf4, 0xec, 0xe9, 0x69, 0x57, 0x3d, 0x3d, 0x6d, 0x64,
	0x10, 0x8a, 0x40, 0x4a, 0x45, 0x7a, 0x83, 0xb8, 0x2a, 0x25, 0x5c, 0x44, 0x42, 0xa8, 0x6c, 0xe0,
	0xba, 0x72, 0xb3, 0xab, 0x34, 0x4a, 0xec, 0x35, 0xf6, 0x5a, 0xb4, 0x3c, 0x00, 0xef, 0x80, 0xd4,
	0x5b, 0x6e, 0x79, 0x18, 0x1e, 0x82, 0xe7, 0x40, 0xbb, 0x76, 0x9c, 0x5d, 0x37, 0x29, 0x2d, 0xa1,
	0x77, 0x99, 0x6f, 0x67, 0x66, 0xc7, 0xdf, 0x37, 0x33, 0x1b, 0xd8, 0xf5, 0x27, 0x07, 0x11, 0xf3,
	0x86, 0xe7, 0x84, 0xd1, 0x64, 0xc8, 0x4e, 0x7d, 0x16, 0xc7, 0xde, 0x88, 0x75, 0xc2, 0x88, 0x0b,
	0x8e, 0x4a, 0xfe, 0x64, 0xa7, 0xe9, 0x4f, 0x0e, 0x7c, 0x4e, 0xd9, 0x34, 0xc5, 0xdc, 0x6f, 0x25,
	0xf8, 0x4f, 0x8b, 0x38, 0x89, 0xb8, 0xcf, 0xc5, 0x98, 0x07, 0x83, 0x73, 0x1e, 0xf6, 0x04, 0x47,
	0xcf, 0x60, 0x2d, 0x9c, 0x61, 0x3d, 0xc1, 0xb1, 0xd5, 0xb2, 0xda, 0x8d, 0xee, 0x66, 0xc7, 0x9f,
	0x74, 0x72, 0xfc, 0xf5, 0x38, 0x16, 0x3d, 0xc1, 0x89, 0xe1, 0x89, 0x8e, 0x60, 0x23, 0x2c, 0x64,
	0xc3, 0xa5, 0x05, 0xd1, 0xd9, 0x19, 0xb9, 0xe6, 0x8d, 0x9e, 0x02, 0x88, 0xb1, 0xcf, 0x88, 0x17,
	0x8c, 0x58, 0x8c, 0xed, 0x96, 0xdd, 0x6e, 0x74, 0xff, 0x96, 0xb1, 0x79, 0x95, 0xef, 0xe4, 0xb1,
	0xe6, 0x84, 0x8e, 0xe0, 0xaf, 0x3c, 0x4d, 0xfa, 0x45, 0xb8, 0xac, 0xe2, 0xb6, 0x8c, 0x3b, 0xd3,
	0x23, 0x79, 0x6b, 0xd1, 0x1d, 0xed, 0x01, 0x0c, 0x23, 0xe6, 0x09, 0x26, 0x73, 0xe3, 0x4a, 0xcb,
	0x6a, 0x3b, 0x44, 0x43, 0xdc, 0xef, 0x25, 0xf8, 0x57, 0x23, 0xec, 0x05, 0xa5, 0x84, 0x7d, 0x48,
	0x58, 0x2c, 0xd0, 0x16, 0x54, 0x93, 0x98, 0x45, 0x7d, 0xaa, 0x48, 0x72, 0x48, 0x66, 0xa1, 0x1d,
	0xa8, 0xcb, 0x5f, 0x6f, 0x3c, 0x9f, 0x29, 0x02, 0x1c, 0x92, 0xdb, 0xa8, 0x03, 0x4e, 0x5e, 0x00,
	0xb6, 0x15, 0x3b, 0x1b, 0x46, 0xa5, 0xb2, 0xc6, 0xb9, 0x4b, 0x81, 0x92, 0xca, 0x6d, 0x28, 0x79,
	0x0e, 0xeb, 0x06, 0xb3, 0xb8, 0xaa, 0xa2, 0x16, 0x8b, 0x60, 0xba, 0xa2, 0x87, 0xb0, 0xce, 0x2e,
	0x86, 0xd3, 0x84, 0xb2, 0xc1, 0x24, 0xe9, 0xd3, 0x18, 0xd7, 0x5a, 0x76, 0xbb, 0x42, 0x4c, 0x70,
	0x11, 0xe9, 0xf5, 0x3b, 0x91, 0xee, 0x5e, 0xd9, 0x80, 0x35, 0x52, 0xdf, 0x87, 0xd4, 0x13, 0x6c,
	0x15, 0x5e, 0x5b, 0xd0, 0xc8, 0xef, 0xe8, 0x53, 0xc5, 0x6c, 0x85, 0xe8, 0x90, 0xc9, 0x7c, 0xf9,
	0x9e, 0x98, 0xc7, 0x94, 0x4d, 0x99, 0x98, 0x4f, 0xd5, 0x49, 0xc4, 0x69, 0x32, 0x14, 0x7d, 0xaa,
	0x44, 0xa8, 0x90, 0xa5, 0xe7, 0xa8, 0x0f, 0xff, 0x78, 0x94, 0x16, 0x0f, 0x14, 0xff, 0x8d, 0xee,
	0xb6, 0x51, 0x68, 0x76, 0x26, 0xeb, 0x5d, 0x14, 0xf3, 0x07, 0xe4, 0xf9, 0x6c, 0xc3, 0xff, 0xcb,
	0x96, 0xc4, 0xdb, 0x84, 0x45, 0x97, 0x08, 0x43, 0x2d, 0x3e, 0xe7, 0xa1, 0x6c, 0x91, 0x54, 0xa4,
	0x99, 0xa9, 0xa9, 0x57, 0x5a, 0xaa, 0x5e, 0xa3, 0xa0, 0xde, 0x23, 0x68, 0x9e, 0xb1, 0xd1, 0x38,
	0xe8, 0x79, 0x82, 0x0d, 0x84, 0x17, 0x09, 0x25, 0xa0, 0x43, 0x0a, 0x28, 0x72, 0x61, 0x2d, 0x47,
	0x5e, 0x05, 0x54, 0xc9, 0xe8, 0x10, 0x03, 0x93, 0x3e, 0x2c, 0xa0, 0xf3, 0x4c, 0xe9, 0x44, 0x1b,
	0x98, 0x9c, 0xf9, 0xcc, 0x96, 0x59, 0xaa, 0xe9, 0xcc, 0xcf, 0x11, 0xd4, 0x86, 0x4a, 0x2c, 0x3c,
	0xc1, 0x70, 0xad, 0x65, 0xb5, 0x9b, 0x5d, 0x64, 0x8e, 0x8e, 0x3c, 0x21, 0xa9, 0x03, 0x7a, 0xa0,
	0x0d, 0xdb, 0xe9, 0x98, 0xc6, 0x18, 0x94, 0xce, 0x6b, 0x5a, 0xe7, 0xc5, 0x68, 0x17, 0x9c, 0xd0,
	0x1b, 0xb1, 0x7e, 0x40, 0xd9, 0x05, 0xae, 0xab, 0xd6, 0x9c, 0x03, 0x92, 0x18, 0x69, 0x0c, 0xc6,
	0x9f, 0x18, 0x76, 0xd4, 0x61, 0x6e, 0xbb, 0x1f, 0x61, 0x5b, 0xef, 0x98, 0x54, 0x81, 0xe3, 0x4b,
	0x99, 0xb4, 0xad, 0xa9, 0x3c, 0x50, 0xdc, 0x63, 0x4b, 0xdd, 0x5d, 0x84, 0x6f, 0xa5, 0x88, 0x6d,
	0x2a, 0xe2, 0xfe, 0xb0, 0x60, 0xcf, 0xec, 0x00, 0xd9, 0x5a, 0x4a, 0xfb, 0xd9, 0x98, 0x16, 0x46,
	0xce, 0xba, 0x3e, 0x72, 0xae, 0x7a, 0x4b, 0x64, 0xe0, 0x20, 0x4c, 0xf2, 0xeb, 0x0d, 0x4c, 0xf7,
	0x91, 0xcb, 0x25, 0x2b, 0xc4, 0xc0, 0xb2, 0x9b, 0xa4, 0xad, 0x6a, 0x4d, 0x55, 0xd7, 0xa1, 0x15,
	0x18, 0xfe, 0x6a, 0x41, 0x6b, 0x59, 0xab, 0x13, 0x16, 0x87, 0x3c, 0x88, 0x65, 0xfa, 0xf2, 0x90,
	0x53, 0xa6, 0xbe, 0xb1, 0xd9, 0xad, 0xcb, 0x76, 0x90, 0x36, 0x51, 0xa8, 0x9c, 0x85, 0xec, 0xdd,
	0xcd, 0xbe, 0x70, 0x66, 0xa2, 0x4d, 0xa8, 0x08, 0x2e, 0xbc, 0x69, 0xb6, 0x8f, 0x52, 0x03, 0x1d,
	0x42, 0x99, 0x7a, 0xc2, 0xcb, 0x1e, 0xaa, 0x7d, 0x99, 0xed, 0x86, 0x17, 0x99, 0x28, 0x67, 0xf7,
	0x8b, 0x0d, 0xdb, 0x9a, 0x97, 0xec, 0x81, 0x95, 0xcb, 0xbb, 0xef, 0xa5, 0x59, 0xfd, 0xad, 0xe7,
	0xaa, 0x76, 0xfb, 0xe7, 0xea, 0xa5, 0xf6, 0x97, 0x63, 0xb6, 0x31, 0xeb, 0x37, 0x6f, 0xcc, 0x6b,
	0x01, 0x8b, 0xd6, 0xa5, 0x73, 0xc7, 0x75, 0x69, 0xf6, 0x90, 0x9a, 0x92, 0xe3, 0xcb, 0x74, 0xfa,
	0x56, 0x16, 0xe9, 0x71, 0xd6, 0x2d, 0xf6, 0x8d, 0x35, 0xa5, 0x4d, 0x72, 0x65, 0xc1, 0xfe, 0xd2,
	0xa9, 0xbd, 0x97, 0x5e, 0x7e, 0x92, 0x55, 0xf7, 0x0b, 0xd6, 0x95, 0xd3, 0x59, 0x55, 0xfd, 0x05,
	0x3d, 0xfc, 0x19, 0x00, 0x00, 0xff, 0xff, 0x91, 0x98, 0xee, 0xe2, 0xb6, 0x0a, 0x00, 0x00,
}

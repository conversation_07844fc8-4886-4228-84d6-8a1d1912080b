package tasks

import (
	"encoding/json"
	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/services"
	"order-center/utils"
)

// PushDigitalHealthOrderMqTask 订单推送互联网医疗
func PushDigitalHealthOrderMqTask() {
	utils.Consume(services.QueuePushDigitalHealthOrder, "", services.MQExchange, PushDigitalHealthOrder)
}

// PushDigitalHealthOrder 订单推送互联网医疗
func PushDigitalHealthOrder(d amqp.Delivery) (response string, err error) {
	defer kit.CatchPanic()
	_ = d.Ack(false)
	logHead := "PushDigitalHealthOrder:"
	mqMessage := string(d.Body)
	glog.Info(logHead, "MQ:", mqMessage)
	model := new(dto.PushDigitalHealthOrder)
	err = json.Unmarshal(d.Body, model)

	if err != nil {
		glog.Error(logHead, "json.Unmarshal:", err, ",", mqMessage)
		return
	}
	err = services.PushDigitalHealthOrder(model.AwOrderSn,model.ConsultOrderSn,model.OrderStatus,model.Count)
	if err != nil {
		glog.Error(logHead, "services.PushDigitalHealthOrder.error:", err, kit.JsonEncode(model))
	}
	return
}

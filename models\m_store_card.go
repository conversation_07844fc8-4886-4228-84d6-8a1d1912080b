package models

import (
	"time"
)

type MStoreCard struct {
	Id             int64     `json:"id" xorm:"pk not null comment('唯一数据id') BIGINT 'id'"`
	ChainId        int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId       int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId         int64     `json:"card_id" xorm:"not null default 0 comment('卡基础表id') BIGINT 'card_id'"`
	Denomination   float64   `json:"denomination" xorm:"not null default '0.0000' comment('面额') DECIMAL(18) 'denomination'"`
	GiftAmount     float64   `json:"gift_amount" xorm:"not null default '0.0000' comment('赠送金') DECIMAL(18) 'gift_amount'"`
	DiscountType   string    `json:"discount_type" xorm:"not null default 'UN_LIMIT' comment('使用限制:UN_LIMIT 无限制,MONEY_LIMIT每次使用可抵扣多少元,DISCOUNT_LIMIT每次使用打几折') VARCHAR(16) 'discount_type'"`
	Discount       float64   `json:"discount" xorm:"not null default '0.0000' comment('折扣') DECIMAL(18) 'discount'"`
	DiscountRange  string    `json:"discount_range" xorm:"not null default 'UN_LIMIT' comment('折扣范围(UN_LIMIT-无限制，PRODUCT-商品，PRODUCT_CATEGORY-商品分类)') VARCHAR(16) 'discount_range'"`
	FosterDiscount float64   `json:"foster_discount" xorm:"not null default '100.0000' comment('寄养折扣,为100则表示不打折') DECIMAL(18) 'foster_discount'"`
	Status         string    `json:"status" xorm:"not null default 'ENABLE' comment('数据状态: ENABLE启用, DISABLE停用') VARCHAR(16) 'status'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (om *MStoreCard) TableName() string {
	return "eshop_saas.m_store_card"
}

type OperateInfo struct {
	OrderId      int    `json:"order_id"`      // 订单ID
	OrderNo      string `json:"order_no"`      // 订单编号
	OperatorId   int64  `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人名称
}

func NewOperateInfo(orderId int, orderNo string, operatorId int64, operatorName string) *OperateInfo {
	return &OperateInfo{
		OrderId:      orderId,
		OrderNo:      orderNo,
		OperatorId:   operatorId,
		OperatorName: operatorName,
	}
}

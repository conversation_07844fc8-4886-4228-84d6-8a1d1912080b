package utils

import (
	"order-center/dto"
	"testing"
)

func TestOmsSign_GenerateRpomsSign(t *testing.T) {
	type args struct {
		signData *dto.GenerateSign
	}
	jsonBy := `{"trade_order_sn":"4100000014475466","order_status":3,"order_source":1,"shop_id":"cx0003","shop_name":"宠颐生北京爱之源","sale_org_code":"awen","sale_channel_id":1,"delivery_org_code":"awen","delivery_warehouse_code":"021SHHXD","sale_org_name":"组织名称","delivery_org_name":"发货组织","delivery_warehouse_name":"发货仓库","member_id":"12","member_name":"杨阳","total":12400,"pay_total":2300,"privilege":10100,"freight":200,"packing_fee":100,"service_fee":200,"order_type":1,"delivery_type":1,"pay_type":1,"is_virtual":0,"create_user":"yangyang","create_type":1,"order_time":"2022-03-15 03:29:00","receiver_name":"杨阳","receiver_state":"广东省","receiver_city":"深圳市","receiver_district":"福田区","receiver_address":"经济大厦","receiver_phone":"13332970407","latitude":0,"longitude":0,"buyer_memo":"hah","seller_memo":"xx","performance_staff_name":"","note":"askdf","logistics_name":"顺风物流","logistics_id":"3284953335244","channel_order_sn":"4100088444834","tax_rate":60,"arrive_time":"2022-03-15 03:29:11","car_number":"粤B 888888","order_product":[{"item_num":"2146081001","marking_price":2900,"pay_price":1100,"total":2900,"number":1,"payment_total":1100,"privilege_total":0,"sku_pay_total":1100}]}`
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				signData: &dto.GenerateSign{
					IsJson: true,
					AppId:  1,
					Secret: "hPHbBzrcQglely0+wwtB6DD7YFnPKd2UR7ZpmCmb/sI=",
					Param: map[string]string{
						"timestamp": "1649324367",
					},
					JsonBody: jsonBy,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &OmsSign{}
			got, err := s.GenerateRpomsSign(tt.args.signData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateRpomsSign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GenerateRpomsSign() got = %v, want %v", got, tt.want)
			}
		})
	}
}

package services

import (
	"reflect"
	"testing"
)

func TestRefundOrderSn_QueryRefundAmount(t *testing.T) {
	var param RefundOrderSn
	for i := 0; i < 10000; i++ {
		param = append(param, "4100000014159690")
	}

	tests := []struct {
		name string
		s    RefundOrderSn
		want []*OrderRefundAmountRows
	}{
		// TODO: Add test cases.
		{
			name: "",
			s:    param,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.s.QueryRefundAmount(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("QueryRefundAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRealOrderRefund_CalRefundAmount(t *testing.T) {
	type fields struct {
		AwenOrderSn  RefundOrderSn
		ThirdOrderSn ThirdRealChildOrderSn
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]float32
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				AwenOrderSn: RefundOrderSn{
					"4100000014285386",
					"4100000014159690",
					"4100000014289501",
				},
				ThirdOrderSn: ThirdRealChildOrderSn{
					ThirdOrderMap: map[string]string{"4100000014207270": "4100000014212077"},
					OrderSn:       []string{"4100000014212077"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &RealOrderRefund{
				AwenOrderSn:  tt.fields.AwenOrderSn,
				ThirdOrderSn: tt.fields.ThirdOrderSn,
			}
			if got := o.CalRefundAmount(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CalRefundAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMainOrderRefund_CalRefundAmount(t *testing.T) {
	type fields struct {
		AwenOrderSn  AwenMainOrderSn
		ThirdOrderSn RefundOrderSn
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]float32
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				AwenOrderSn: AwenMainOrderSn{
					"4100000014158511",
				},
				ThirdOrderSn: RefundOrderSn{
					"4100000014207270",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &MainOrderRefund{
				AwenOrderSn:  tt.fields.AwenOrderSn,
				ThirdOrderSn: tt.fields.ThirdOrderSn,
			}
			if got := o.CalRefundAmount(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CalRefundAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVirtualOrderRefund_CalRefundAmount(t *testing.T) {
	type fields struct {
		AwenOrderSn  RefundOrderSn
		ThirdOrderSn ThirdVirtualChildOrderSn
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]float32
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				AwenOrderSn: RefundOrderSn{
					"4100000014289501",
				},
				ThirdOrderSn: ThirdVirtualChildOrderSn{
					"4100000014164420",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &VirtualOrderRefund{
				AwenOrderSn:  tt.fields.AwenOrderSn,
				ThirdOrderSn: tt.fields.ThirdOrderSn,
			}
			if got := o.CalRefundAmount(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CalRefundAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

if (!$?) {
    return
}
function pull {
    cd .\proto
    git pull
    cd ..
    git pull
}

function push {
    cd .\proto
    git push
    cd ..
    git push
}

function commit {
    $paras = $args[0]
    switch ($paras.Length) {
        0 { $msg = "update" }
        1 { 
            if ($paras[0] -eq "commit") {
                $msg = "update"
            }else {
                $msg = $paras[0]
            }
         }
        Default { $msg = $paras[1]  }
    }
    
    cd .\proto
    git commit -m $msg
    
    cd ..
    git commit -m $msg
}

function add{
    cd .\proto
    git add .
    cd ..
    git add .
}

function checkout{
    $paras = $args[0]
    cd .\proto
    git checkout $paras[1]

    cd ..
    git checkout $paras[1]
}

switch ($args[0]) {
    "pull" {pull}
    "push" {push}
    "commit" {commit $args}
    "add" {add}
    "checkout" {checkout $args}
    Default {
       add
       commit $args
       pull
       add
       commit $args
       push
    }
}
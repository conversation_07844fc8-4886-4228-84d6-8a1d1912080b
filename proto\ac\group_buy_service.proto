syntax = "proto3";

package ac;

import "ac/activity_model.proto";

service GroupBuyService {
  //团购活动列表
  rpc GetGroupBuyList(GroupBuyListRequest) returns (GroupBuyListResponse);
  //创建拼团活动
  rpc CreateGroupBuy(GroupBuyCreateRequest) returns (baseResponse);
  //更新拼团活动
  rpc UpdateGroupBuy(GroupBuyUpdateRequest) returns (baseResponse);
  //拼团活动详情
  rpc GetGroupBuyDetail(GroupBuyIdRequest) returns (GroupBuyDetailResponse);
  //删除拼团活动
  rpc DeleteGroupBuy(GroupBuyIdRequest) returns (baseResponse);
  //终止拼团活动
  rpc StopGroupBuy(GroupBuyIdRequest) returns (baseResponse);

  // 获取拼团商品列表 boss
  rpc GetGroupBuyProductList (GetGroupBuyProductListRequest) returns (GetGroupBuyProductListResponse);
  // 获取拼团商品列表 客户端
  rpc GetGroupBuyProductListForCustom (GetGroupBuyProductListRequest) returns (GroupBuyProductCustomListResponse);
  // 创建拼团商品
  rpc CreateGroupBuyProduct (CreateGroupBuyProductRequest) returns (baseResponse);
  // 更新拼团商品
  rpc UpdateGroupBuyProduct (UpdateGroupBuyProductRequest) returns (baseResponse);
  // 获取拼团商品详情
  rpc GetGroupBuyProductDetail(GroupBuyProductDetailRequest) returns (GetGroupBuyProductDetailResponse);
  // 获取拼团商品详情 -用于客户端请求
  rpc GetGroupBuyProductDetailForCustom(GroupBuyProductDetailRequest) returns (GetProductDetailForCustomResponse);

  // 删除拼团商品
  rpc DeleteGroupBuyProduct(GroupBuyProductIdRequest) returns (baseResponse);

  // 获取可以参加拼团活动的阿闻电商渠道的商品
  rpc GetGroupBuyUPetProductSelectList(GetGroupBuyUPetProductSelectListRequest) returns (GetGroupBuyUPetProductSelectListResponse);
  // 拼团订单下单与成团等行为给活动中心订单统计的回调
  rpc GroupBuyOrderStaticCallBack(GroupBuyOrderStaticRequest) returns (baseResponse);
  // 删除拼团商品
  rpc GetGroupBuySkusListBySkuId(GetGroupBuySkusListBySkuIdRequest) returns (GetGroupBuySkusListBySkuIdResponse);
  //根据活动id活动的状态相关字段
  rpc GetGroupBuyStatus(GetGroupBuyStatusRequest) returns (GetGroupBuyStatusResponse);

}
//拼团活动列表的请求数据
message GroupBuyListRequest {
  //活动名称
  string title = 1;
  //活动状态 0所有 1未开始（包括未开始与预告中） 2预告中 3显示中（包括预告中与进行中） 4已开始 5已结束 6已终止
  int32 status = 2;
  //业务渠道 1阿文到家 5电商
  int32 channelId = 3;
  //拼团类型 1普通拼团
  int32 type = 4;
  //当前多少页 从1开始
  DuringTime beginTimeDuring = 5;
  //结束时间段
  DuringTime endTimeDuring = 6;
  //分页参数
  Pagination pagination = 7;

}
//时间区间
message DuringTime {
  //开始时间
  string beginTime = 1;
  //结束时间 -M-D H:i:s格式
  string endTime = 2;
}
//分页参数
message Pagination {
  //当前多少页 从1开始 必传且必须大于0
  int32 pageIndex = 1;
  //每页多少条数据 必传且必须大于0
  int32  pageSize = 2;
}

//团购活动列表
message GroupBuyListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  int32 total = 4;
  //拼团活动信息
  repeated GroupBuyData data = 5;

}
message GroupBuyData {
  //活动的id
  int32 id = 1;
  // 活动名称
  string title = 2;
  // 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string beginDate = 3;
  // 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string endDate = 4;
  //业务渠道 1阿文到家 5电商
  int32 channelId = 5;
  //拼团类型 1普通拼团
  int32 type = 6;
  //状态描述
  string typeDesc = 7;
  //终止状态 0未终止 1已终止
  int32 stop = 8;
  //拼团活动信息 活动状态 1:未开始 2:预告中 3:进行中 4:已结束
  int32  status = 9;
  //状态描述 该状态描述包括自然状态 未开始 进行中 已结束 ；人为状态：已终止
  string statusDesc = 10;
  //拼团的有效期 日
  int32 expirationDay = 11;
  //拼团的有效期 小时
  int32 expirationHour = 12;
  //拼团的有效期 分钟
  int32 expirationMinute = 13;
  //是否可叠加其他活动 0不可叠加 1可叠加
  int32 additionPromotionAble = 14;
  //叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
  string additionPromotionType = 15;
  //是否开启预告 0不开启 1开启
  int32 previewSwitch = 16;
  //预告提前时间，单位为小时
  int32 previewHour = 17;
  //预告开始时间，当开启预告时计算并写入，避免查询时在begin_date上使用时间函数计算
  string previewBeginTime = 18;
  //是否模拟成团 0否1是
  int32  mockSuccess = 19;
  //模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
  int32 mockSuccessTarget = 20;
  //模拟成团最低参团人数
  int32 mockSuccessMember = 21;
  //是否免邮费 0否1是
  int32  deliveryFree = 22;
  //'门店财务编码 仅在channel_id=1时（活即阿文到家的拼团动）该字段的值才有意义
  string financeCode = 23;
  //创建时间
  string createTime = 24;
  //更新时间
  string updateTime = 25;
  //成团用户数（去重）
  int32 successMemberCount = 26;
  //成团真人订单数
  int32  successOrderCount = 27;
  //参加活动的商品数量
  int32  ProductCount = 28;
  //异常数量统计
  int32  exception_count = 29;
  // 审核原因
  string check_reason = 30;
}

//添加活动
message GroupBuyCreateRequest {
  // 名称
  string title = 1;
  // 业务渠道 1阿文到家 5电商
  int32 channelId = 2;
  // 门店财务编码 仅在channel_id=1时（即阿文到家的团购活动）才有值
  string financeCode = 3;
  // 开始时间
  string beginDate = 4;
  // 结束时间
  string endDate = 5;
  // 拼团类型 1普通拼团，
  int32 type = 6;
  // 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟），用户开团后，需要在1日内成团，超时则拼团失败
  int32 expirationMinute = 7;
  // 是否可叠加其他活动，默认不可以 0不可叠加 1可叠加
  int32 additionPromotionAble = 8;
  // 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
  string additionPromotionType = 9;
  // 是否开启活动预告 0不开启 1开启
  int32 previewSwitch = 10;
  // 预告提前时间，单位为小时
  int32 previewHour = 11;
  // 是否模拟成团 0否1是
  int32 mockSuccess = 12;
  // 模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
  int32 mockSuccessTarget = 13;
  // 模拟成团最低参团人数，≥1的整数
  int32 mockSuccessMember = 14;
  // 是否免邮费 0否1是
  int32 deliveryFree = 15;
  //用户Id，即userno
  string userId = 16;
  //用户名称，即登录人姓名
  string userName = 17;
}
//更新活动
message GroupBuyUpdateRequest {
  //ID
  int32 id = 1;
  // 名称
  string title = 2;
  // 业务渠道 1阿文到家 5电商
  int32 channelId = 3;
  // 门店财务编码 仅在channel_id=1时（即阿文到家的团购活动）才有值
  string financeCode = 4;
  // 开始时间
  string beginDate = 5;
  // 结束时间
  string endDate = 6;
  // 拼团类型 1普通拼团，
  int32 type = 7;
  // 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟），用户开团后，需要在1日内成团，超时则拼团失败
  int32 expirationMinute = 8;
  // 是否可叠加其他活动，默认不可以 0不可叠加 1可叠加
  int32 additionPromotionAble = 9;
  // 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
  string additionPromotionType = 10;
  // 是否开启活动预告 0不开启 1开启
  int32 previewSwitch = 11;
  // 预告提前时间，单位为小时
  int32 previewHour = 12;
  // 是否模拟成团 0否1是
  int32 mockSuccess = 13;
  // 模拟成团适用对象 1表示适用于所有 2表示适用于参团人数大于某个数的团
  int32 mockSuccessTarget = 14;
  // 模拟成团最低参团人数，≥1的整数
  int32 mockSuccessMember = 15;
  // 是否免邮费 0否1是
  int32 deliveryFree = 16;
  //用户Id，即userno
  string userId = 17;
  //用户名称，即登录人姓名
  string userName = 18;
}

//拼团商品列表的请求数据
message GetGroupBuyProductListRequest {
  // 拼团活动id
  repeated int32 gid = 1;
  //产品名称
  string productName = 2;
  //  商品sku id
  int32 skuId = 3;
  //  商品的产品id
  int32 productId = 4;
  //  商品的产品id
  int32 ChannelId = 5;
  //活动状态 0所有  1未开始（包括未开始与预告中） 2预告中 3显示中（包括预告中与进行中） 4已开始 5已结束 6已终止
  //说明：2与3是特殊的状态 一般用于用户端（小程序 app）,后台只返回 1 4 5 6的状态，
  //如果前端需要区分2 3状态时，在请求方将状态通过字段判断改为2或者3
  int32 Status = 6;
  //排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
  int32 orderBy = 7;
  //分页参数
  Pagination pagination = 8;
  //是否导出 1导出
  int32 export =9;
  // 1异常商品
  int32 type = 10;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 11;
}


//拼团商品数据
message GroupBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  // 拼团活动信息 所属拼团id
  int32 gid = 2;
  // 成团人数
  int32 successNum = 3;
  // 拼团价 单位分
  int32 price = 4;
  // 排序
  int32 sort = 5;
  // 限购类型 1：每人限购n件 2：每人限购<=n件 n为buy_limit_num字段返回的数字
  int32 buyLimitType = 6;
  // 限购的数量，与buy_limit_type字段配合使用
  int32 buyLimitNum = 7;
  // 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次
  int32 openNum = 8;
  // 每人参团次数 0表示不限制，>=0表示每个用户最多可参团少次
  int32 partNum = 9;
  // 初始参团人数
  int32 initPartNum = 10;
  // 拼团成功送的优惠券ID，多个用，隔开
  string successCoupon = 11;
  // 拼团成功每人最多送多少个优惠券 -1表示不限制 默认为1
  int32 successCouponLimitNum = 12;
  // 拼团失败送的优惠券ID，多个用，隔开
  string failCoupon = 13;
  // 拼团失败每人最多送多少个优惠券  -1表示不限制 默认为1
  int32 failCouponLimitNum = 14;
  // 分享卡片图片地址
  string shareImgUrl = 15;
  //商品sku id
  int32 skuId = 16;
  //商品的产品id
  int32 productId = 17;
  // 渠道id
  int32 channelId = 18;
  // 市场价格 单位分
  int32 marketPrice = 19;
  // 商品图片
  string pic = 20;
  // 商品库存
  int32 stock = 21;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 22;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 23;
  //组合商品的子商品skuI
  repeated ChildRen childSkuIds = 24;
  //商品名称
  string productName = 25;
  // 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
  int32 canBeEdited = 26;
  // 是否可被删除 0 不可删除 1 可删除 用于boss后台
  int32 canBeDeleted = 27;
  //参团人数 包括拼主与参团人数
  int32 PartUserCount = 28;
  //订单数
  int32  totalOrderCount = 29;
  //活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
  int32  status = 30;
  //活动终止状态 0未终止 1已终止
  int32  stop = 31;
  //创建时间
  string createTime = 32;
  //更新时间
  string updateTime = 33;
  //商品折扣率
  double priceRatio = 34;
  // 是否异常 1:异常 0：正常
  int32 is_normal = 35;
  // 是否标记 1:是 0：否
  int32 is_mark = 36;
  //R1集采价 单位分
  int32 R1PurchasePrice = 37;
  // 标记为正常的异常折扣
  double MarkDiscount =38;
  // 标记为正常的采购价(分)
  int32 MarkPurchasePrice =39;
}

//团购商品列表
message GroupBuyProductCustomListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //拼团商品信息
  repeated GroupBuyProductCustomList data = 5;
}

//拼团商品列表客户端返回数据
message GroupBuyProductCustomList {
  //活动商品信息记录id
  int32 id = 1;
  // 拼团活动信息 所属拼团id
  int32 gid = 2;
  // 拼团价 单位分
  int32 price = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  // 渠道id
  int32 channelId = 6;
  // 市场价格 单位分
  int32 marketPrice = 7;
  // 商品图片
  string pic = 8;
  //商品名称
  string productName = 9;
  //参团人数 包括拼主与参团人数
  int32 partUserCount = 10;
  // 初始参团人数
  int32 initPartNum = 11;
}


//拼团商品详细数据包含部分拼团信息
message GroupBuyProductDetailData {
  //活动商品信息记录id
  int32 id = 1;
  // 拼团活动信息 所属拼团id
  int32 gid = 2;
  // 成团人数
  int32 successNum = 3;
  // 拼团价 单位分
  int32 price = 4;
  // 排序
  int32 sort = 5;
  // 限购类型 1：每人限购n件 2：每人限购<=n件 n为buy_limit_num字段返回的数字
  int32 buyLimitType = 6;
  // 限购的数量，与buy_limit_type字段配合使用
  int32 buyLimitNum = 7;
  // 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次
  int32 openNum = 8;
  // 每人参团次数 0表示不限制，>=0表示每个用户最多可参团少次
  int32 partNum = 9;
  // 初始参团人数
  int32 initPartNum = 10;
  // 拼团成功送的优惠券ID，多个用，隔开
  string successCoupon = 11;
  // 拼团成功每人最多送多少个优惠券 -1表示不限制 默认为1
  int32 successCouponLimitNum = 12;
  // 拼团失败送的优惠券ID，多个用，隔开
  string failCoupon = 13;
  // 拼团失败每人最多送多少个优惠券  -1表示不限制 默认为1
  int32 failCouponLimitNum = 14;
  // 分享卡片图片地址
  string shareImgUrl = 15;
  //商品sku id
  int32 skuId = 16;
  //商品的产品id
  int32 productId = 17;
  // 渠道id
  int32 channelId = 18;
  // 拼团活动信息 预告开关
  int32 previewSwitch = 19;
  //拼团活动信息 预告提前时间，单位为小时
  int32 previewHour = 20;
  // 拼团活动信息 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string beginDate = 21;
  // 拼团活动信息 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string endDate = 22;
  //拼团活动信息 终止状态 0未终止 1已终止
  int32 stop = 23;
  //拼团活动信息 拼团类型 1普通拼团，目前仅有普通拼团
  int32 type = 24;
  //拼团活动信息 是否可叠加其他活动， 0不可叠加 1可叠加
  int32 additionPromotionAble = 25;
  //拼团活动信息 叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费  多个用逗号分隔
  string additionPromotionType = 26;
  //拼团活动信息 活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
  int32 status = 27;
  // 拼团活动信息 预告开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string PreviewBeginTime = 28;
  // 拼团活动信息 拼团的有效时间 （单位为分钟） 比如设置1日（1440分钟）
  int32 ExpirationMinute = 29;
  // 市场价格 单位分
  int32 marketPrice = 30;
  // 商品图片
  string pic = 31;
  //商品名称
  string productName = 32;
  // 商品库存
  int32 stock = 33;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 34;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 35;
  //组合商品的子商品skuI
  repeated ChildRen childSkuIds = 36;
  //创建时间
  string createTime = 37;
  //更新时间
  string updateTime = 38;
}

//团购商品列表
message GetGroupBuyProductListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //拼团商品信息
  repeated GroupBuyProductData data = 5;
  //异常商品数
  int32 nonormal_num = 6;
  //正常商品数
  int32 normal_num = 7;
}

//新增拼团活动商品
message CreateGroupBuyProductRequest {
  //  商品sku id
  int32 skuId = 1;
  //  商品的产品id
  int32 productId = 2;
  //主体信息
  SaveGroupBuyProductData saveData = 4;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 5;
}
//更新拼团商品
message UpdateGroupBuyProductRequest {
  // 需要更新的记录id
  int32 id = 1 ;
  //  商品sku id
  int32 skuId = 2;
  //  商品的产品id
  int32 productId = 3;
  //主体信息
  SaveGroupBuyProductData saveData = 5;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 6;
}

//拼团商品只需要id的请求
message GroupBuyProductIdRequest {
  // 活动商品信息记录id
  int32 id = 1;
}

//获取拼团商品信息
message GetGroupBuyProductDetailResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //拼团商品信息
  GroupBuyProductDetailData data = 4;
}

//添加/编辑拼团商品
message SaveGroupBuyProductData {
  //所属拼团id
  int32 gid = 1;
  // required 成团人数 开团的人也算在内 必须大于等于2
  int32 successNum = 2;
  // required 拼团价 单位分 售价需要在0.01元到商品价格之间
  int32 price = 3;
  // 排序 默认为1
  int32 sort = 4;
  // 限购类型 1:每人限购n件 2:每人限购<=n件 n使用buy_limit_num字段保存 默认为1
  int32 buyLimitType = 5;
  // required 限购的数量，与buy_limit_type字段配合使用
  int32 buyLimitNum = 6;
  // 每人开团次数限制 0 不限制，>=0 表示每个用户最多可开团多少次，默认为0
  int32 openNum = 7;
  // 每人参团次数 默认为0，0表示不限制，>=0表示每个用户最多可参团少次
  int32 partNum = 8;
  // 初始参团人数 虚拟数据 只用于显示
  int32 initPartNum = 9;
  // 拼团成功送的优惠券ID，多个用，隔开
  string successCoupon = 10;
  // 拼团成功每人最多送多少个优惠券 传-1表示不限制 默认为1
  //注意：前端界面显示为0时表示不限制，此时传值传-1 因为这种情况下后端区分不了是前端传的0还是没有传而被go设置了默认值0
  int32 successCouponLimitNum = 11;
  // 拼团失败送的优惠券ID，多个用，隔开
  string failCoupon = 12;
  // 拼团失败每人最多送多少个优惠券  传-1表示不限制 默认为1
  //注意：前端界面显示为0时表示不限制，此时传值传-1 因为这种情况下后端区分不了是前端传的0还是没有传而被go设置了默认值0
  int32 failCouponLimitNum = 13;
  // 分享卡片图片地址
  string shareImgUrl = 14;
}

//拼团商品只需要id的请求
message GroupBuyProductDetailRequest {
  // 活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 gid = 2;
  // 商品skuId
  int32 skuId = 3;
  // 商品的产品id
  int32 productId = 4;
  // 渠道 1电商 5商城
  int32 channelId = 5;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 6;
}

//批量ids的请求
message GroupBuyProductIdsRequest {
  // 活动商品信息记录id
  repeated int32 ids = 1;
}

//拼团详情请求
message GroupBuyIdRequest {
  // 活动id
  int32 id = 1;
}
//拼团详情响应
message GroupBuyDetailResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 拼团详情
  GroupBuyData groupBuyData = 4;
}

//阿闻电商参加拼团活动的商品
message GetGroupBuyUPetProductSelectListRequest {
  //活动id
  int32 gid = 1;
  //商品sku_id 对应商城的goods_id
  int32 skuId = 2;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 3;
  // 商品名称
  string productName = 4;
  //分页参数
  Pagination pagination = 5;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 6;

}

//阿闻电商参加拼团活动的商品
message GetGroupBuyUPetProductSelectListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //拼团商品信息
  repeated GroupBuySelectUPetProductData data = 5;
}

message GroupBuySelectUPetProductData {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 1;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 2;
  // 商品名称
  string productName = 3;
  // 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
  int32 timeConflict = 4;
  //商品图片
  string pic = 5;
  //库存
  int32 stock = 6;
  //价格 单位分
  int32 marketPrice = 7;
  //是否时虚拟产品 1是 0 否
  int32 isVirtual = 8;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 9;
  //组合商品的子商品信息
  repeated ChildRen childSkuIds = 10;
}

//拼团订单回调
message GroupBuyOrderStaticRequest {
  // required 回调场景 1 成团 2：下单支付成功 3：创建订单
  int32 syncType = 1;
  //拼团活动的产品记录id 关联dc.activity中的group_buy_product表
  int32 productRecordId = 2;
  // required 拼团id
  int32 gid = 3;
  // required skuId
  int32 skuId = 4;
  //  商品id
  int32 productId = 5;
  //required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
  int32 channelId = 6;
  //成团后的真人订单数 type为1时 该参数为必填
  int32 successRealOrder = 7;
  //成团后的订单人数（去重） type为1时 该参数为必填
  int32 successDistinctUserCount = 8;
}

//组合商品子商品讯息
message ChildRen {
  int32 skuId = 1;
  //规则
  int32 ruleNum = 2;
  //是否为虚拟 0:不是 1：是虚拟
  int32 isVirtual = 3;
  //0不是药品仓 1药品仓
  int32 stockWarehouse = 7;
}

message GetProductDetailForCustomResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //拼团商品信息
  ProductDetailForCustom data = 5;
}
message ProductDetailForCustom{
  //商品参加的拼团活动ID
  int32 Gid = 1;
  // required skuId
  int32 skuId = 2;
  //  商品id
  int32 productId = 3;
  //required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
  int32 channelId = 4;
  //拼团价
  int32 Price = 5;
  //原市场价
  int32 MarketPrice = 6;
  // required 成团人数 开团的人也算在内 必须大于等于2
  int32 SuccessNum = 7;
  //是否开启预告 0不开启 1开启
  int32 PreviewSwitch = 8;
  // 拼团活动信息 预告开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string PreviewBeginTime = 9;
  //拼团活动信息 活动状态  1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
  int32 Status = 10;
  // 开始时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string BeginDate = 11;
  // 结束时间 格式YYYY-MM-DD HH:ii:ss 如2018-05-04 18：25：59
  string EndDate = 12;
  //终止状态 0未终止 1已终止
  int32 Stop = 13;
  //拼团类型 1普通拼团，目前仅有普通拼团
  int32 Type = 14;
  //是否可叠加其他活动， 0不可叠加 1可叠加
  int32 AdditionPromotionAble = 15;
  //叠加的活动类型 1 优惠券 2 现时折扣 3店铺满减 4满减运费
  string AdditionPromotionType = 16;
  //限购类型 1每人限购n件 2每人限购<=n件 n使用buy_limit_num字段保存
  int32 BuyLimitType = 17;
  //限购的数量，与buy_limit_type字段配合使用
  int32 BuyLimitNum = 18;
  //分享卡片图片地址
  string ShareImgUrl = 19;
  //参团人数 包括拼主与参团人数
  int32 PartUserCount = 20;
  //活动商品绑定记录id
  int32 GroupBuyProductId = 21;
}
//通过spuId获取拼团sku规格信息请求参数
message GetGroupBuySkusListBySkuIdRequest{
  //产品sku id
  int32 skuId = 1;
  //业务渠道 1阿文到家 5电商
  int32 channelId = 2;
  //团号：拼团主的订单号
  string pinOrderSn = 3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}
//通过spuId获取拼团sku规格信息返回参数
message GetGroupBuySkusListBySkuIdResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //拼团商品信息
  repeated GroupBuySkusList data = 4;
}
//拼团sku规格信息
message GroupBuySkusList{
  // 拼团活动信息 所属拼团id
  int32 gid = 1;
  // 拼团价 单位分
  int32 price = 2;
  //商品sku id
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  // 市场价格 单位分
  int32 marketPrice = 5;
  // 商品图片
  string pic = 6;
  //商品名称
  string productName = 7;
  // 规格名称
  string specName = 8;
  // 规格值
  string specValue = 9;
  //限购类型 1每人限购n件 2每人限购<=n件 n使用buy_limit_num字段保存
  int32 BuyLimitType = 10;
  //限购的数量，与buy_limit_type字段配合使用
  int32 BuyLimitNum = 11;
  //渠道
  int32 channelId = 12;
}


//根据拼团活动id获取活动的状态
message GetGroupBuyStatusRequest{
  // 拼团id
  repeated int32 gids = 1;
}
//通过spuId获取拼团sku规格信息返回参数
message GetGroupBuyStatusResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //拼团商品信息
  repeated GroupBuyStatus data = 4;
}

//拼团sku规格信息
message GroupBuyStatus{
  // 拼团活动信息 所属拼团id
  int32 gid = 1;
  // 活动状态 1:未开始 2:预告中 3:进行中 4:已结束（包括自然结束与人为终止）
  int32 status = 2;
  //是否终止 0未终止 1已终止
  int32 stop = 3;
  //开始时间
  string begin_date = 4;
  //结束时间
  string end_date = 5;
}


package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"strconv"
	"time"
)

// 虚拟订单-导出订单数据
type VirtualOrderExport struct {
	F          *excelize.File
	SheetName  string
	storeMap   map[string]*dac.StoreInfo
	taskParams *oc.AwenVirtualOrderListRequest
}

// 逻辑
func (e *VirtualOrderExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenVirtualOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []*oc.AwenVirtualOrderExport

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	for {
		details, err = services.AwenVirtualOrderExport(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		orderList = append(orderList, details...)
		glog.Info("details length - pagesize length", len(details), e.taskParams.PageSize)
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)
	var n string
	for k := range orderList {
		n = strconv.Itoa(k + 2)
		// 订单号
		e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
		// 父订单号
		e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].ParentOrderSn)
		//外部订单号
		e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
		// 下单时间
		e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].CreateTime)
		// 支付流水号
		e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].PaySn)
		// 支付时间
		e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].PayTime)
		// 实收金额
		e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].Total))
		// 订单原价
		e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(orderList[k].GoodsTotal+orderList[k].Freight))
		// 优惠金额
		e.F.SetCellValue(e.SheetName, "I"+n, kit.FenToYuan(orderList[k].Privilege))
		// 退款金额
		e.F.SetCellValue(e.SheetName, "J"+n, orderList[k].RefundAmount)
		// 支付方式 1支付宝 2微信 3美团支付
		e.F.SetCellValue(e.SheetName, "K"+n, services.PayMode[orderList[k].PayMode])
		// 大区
		if value, ok := e.storeMap[orderList[k].ShopId]; ok {
			e.F.SetCellValue(e.SheetName, "L"+n, value.Bigregion)
		}
		// 城市
		e.F.SetCellValue(e.SheetName, "M"+n, orderList[k].ReceiverCity)
		// 店铺名称
		e.F.SetCellValue(e.SheetName, "N"+n, orderList[k].ShopName)
		// 店铺ID(财务编码)
		e.F.SetCellValue(e.SheetName, "O"+n, orderList[k].ShopId)
		//订单来源
		e.F.SetCellValue(e.SheetName, "P"+n, services.OrderFrom[orderList[k].ChannelId])
		// 销售渠道
		if _, ok := services.UserAgent[orderList[k].UserAgent]; ok {
			e.F.SetCellValue(e.SheetName, "Q"+n, services.UserAgent[orderList[k].UserAgent])
		} else {
			e.F.SetCellValue(e.SheetName, "Q"+n, "其它")
		}
		// 收货人姓名
		e.F.SetCellValue(e.SheetName, "R"+n, orderList[k].ReceiverName)
		// 收货人联系方式
		e.F.SetCellValue(e.SheetName, "S"+n, "")
		// 订单状态
		e.F.SetCellValue(e.SheetName, "T"+n, services.OrderMainStatusMap[orderList[k].OrderStatus])
		/*if orderList[k].OrderStatus == 0 {
			e.f.SetCellValue(e.sheetName, "S"+n, services.OrderMainStatusMap[orderList[k].OrderStatus])
		} else {
			e.f.SetCellValue(e.sheetName, "S"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
		}*/
		// 活动类型
		e.F.SetCellValue(e.SheetName, "U"+n, orderList[k].ActivityType)
		// 业绩归属人
		e.F.SetCellValue(e.SheetName, "V"+n, orderList[k].PerformanceStaffName)
		// 业绩分配人
		e.F.SetCellValue(e.SheetName, "W"+n, orderList[k].PerformanceOperatorName)
		// 业绩归属人所属门店编码
		e.F.SetCellValue(e.SheetName, "X"+n, orderList[k].PerformanceFinanceCode)
		// 业绩归属人所属门店名称
		e.F.SetCellValue(e.SheetName, "Y"+n, orderList[k].PerformanceChainName)
		// 业绩分配时间
		e.F.SetCellValue(e.SheetName, "Z"+n, orderList[k].PerformanceOperatorTime)
		// 仓库类型(门店类型)
		e.F.SetCellValue(e.SheetName, "AA"+n, orderList[k].Category)
		// 仓库名称
		e.F.SetCellValue(e.SheetName, "AB"+n, orderList[k].WarehouseName)
		// 顾客类型，默认0,1-新顾客，2-老顾客
		if (orderList[k].ChannelId == services.ChannelAwenId || orderList[k].ChannelId == services.ChannelDigitalHealth) && utils.CampareTime(orderList[k].CreateTime, "2021-06-10 00:00:00") {
			if orderList[k].IsNewCustomer == 1 {
				e.F.SetCellValue(e.SheetName, "AC"+n, "新顾客")
			} else if orderList[k].IsNewCustomer == 2 {
				e.F.SetCellValue(e.SheetName, "AC"+n, "老顾客")
			} else {
				e.F.SetCellValue(e.SheetName, "AC"+n, "打标中")
			}
		}
		// 支付方式
		e.F.SetCellValue(e.SheetName, "AD"+n, services.PayMode[orderList[k].PayMode])
		// 平台实际分摊补贴
		e.F.SetCellValue(e.SheetName, "AE"+n, kit.FenToYuan(orderList[k].PrivilegePt))
	}
	e.F.Save()

	return
}

// 设置表头
func (e *VirtualOrderExport) SetSheetName() {
	nameList := []string{
		"订单号", "父订单号", "外部订单号", "下单时间", "支付流水号", "支付时间", "实收金额", "订单原价", "优惠金额", "退款金额", "支付方式", "大区",
		"城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "收货人", "收货人联系方式", "订单状态", "活动类型",
		"业绩归属人", "业绩分配人", "业绩归属人所属门店编码", "业绩归属人所属门店名称", "业绩分配时间",
		"仓库类型", "仓库名称", "顾客类型", "支付方式", "平台实际分摊补贴",
	}
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *VirtualOrderExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("虚拟订单-导出订单数据(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

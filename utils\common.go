package utils

import (
	"time"
)

const (
	DATETIME_LAYOUT      = "2006-01-02 15:04:05"
	DATE_LAYOUT          = "2006-01-02"
	DATE_LAYOUT_SHORT_CN = "01月02日"
	DATE_LAYOUT_SHORT_EN = "01-02"
	TIME_LAYOUT          = "15:04:05"
	TIME_LAYOUT_SHORT    = "15:04"
)

// 获取当前标准格式时间
func GetTimeStr(dateLayout string, timeStr string) time.Time {

	payTime, err := time.ParseInLocation(dateLayout, timeStr, time.Local)
	if err != nil {
		return time.Now()
	}
	return payTime
}

func UnixToTimeFormat(layout string, t int64) string {
	if t == 0 {
		return ""
	}
	return time.Unix(t, 0).Format(layout)
}

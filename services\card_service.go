package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/ac"
	"order-center/proto/et"
	"order-center/proto/oc"
	"order-center/proto/sh"
	"order-center/utils"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"

	"github.com/golang-module/carbon/v2"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type CardService struct {
	BaseService
}

// New 会员卡、服务包开卡
func (c CardService) New(ctx context.Context, in *oc.CardNewReq) (out *oc.CardNewRes, e error) {
	glog.Info("CardService New 入参：", kit.JsonEncode(in))
	out = &oc.CardNewRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Error("CardService New 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := GetUPetDBConn()

	// 拿到卡信息
	cardTemplate := new(models.VipCardTemplate)
	if has, err := db.Where("id = ?", in.CardId).Get(cardTemplate); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "未找到卡模板"
		return
	}

	if cardTemplate.Type == 2 {
		p := GetMedicalDBConn()
		if has, err := p.Table("pm_service_pack").Where("pack_sku_id = ? and pack_status = 1", cardTemplate.SkuId).Exist(); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到有效的家庭医生服务包"
			return
		}
	}

	member := new(models.UpetMember)
	if has, err := db.Where("scrm_user_id = ?", in.ScrmId).Get(member); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		scrmMember := new(models.UpetMember)
		if has, err = GetDcDBConn().Table("scrm_organization_db.t_scrm_user_info").
			Where("user_id =?", in.ScrmId).Select("user_id as scrm_user_id,user_mobile as member_mobile,user_name as member_truename").Get(scrmMember); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "用户未找到"
			return
		}
		// 在通过手机号查找用户
		if has, err = db.Where("member_mobile = ?", scrmMember.MemberMobile).Get(member); err != nil {
			out.Message = err.Error()
			return
		} else if has {
			member.ScrmUserId = scrmMember.ScrmUserId
			if _, err = db.Where("member_id = ?", member.MemberId).Cols("scrm_user_id").Update(member); err != nil {
				out.Message = "用户未找到"
				return
			}
		} else {
			member = scrmMember
			now := time.Now()
			member.MemberName = fmt.Sprintf("%s%08x%05x", "upet_", now.Unix(), now.UnixNano()%0x100000)
			member.MemberTime = cast.ToString(now.Unix())
			member.MemberLoginTime = cast.ToString(now.Unix())
			member.MemberOldLoginTime = cast.ToString(now.Unix())
			member.IsBuy = 1
			member.IsAllowtalk = 1
			member.InformAllow = 1
			member.MemberState = 1
			if _, err = db.Insert(member); err != nil {
				out.Message = err.Error()
				return
			}
		}
	} else if member.VipCardState > 0 && cardTemplate.Type == 1 {
		//out.Message = "存在生效中的会员卡，不允许购买"
		//return
		//允许买多次
	}

	orderType := int32(17)
	expireRefund := 0 // 是否可以过期退款
	var pic string

	disInfo := new(DistributeInfo)

	if cardTemplate.Type == 2 {
		orderType = 18
		expireRefund = 1
		pic = "https://file.vetscloud.com/11d1fe56021484e00bfd4c08fd8e5a0d.jpg"
	} else if cardTemplate.Type == 1 {
		if in.Source == 0 {
			if d, err := distribute(member, in.DisId, in.DisType); err == nil {
				if cardTemplate.DisRate > 0 {
					disInfo = d
				} else {
					disInfo.ChainId = d.ChainId
				}
			}
		}
		if cardTemplate.OrId == 13 {
			pic = "https://file.vetscloud.com/09ead05edb267a08621858c8dc9d3b14.png"
		} else {
			pic = "https://file.vetscloud.com/219b6000b8865fe95a471e02cc34e07c.jpg"
		}
	}

	// 查有没有待支付的卡订单
	om := new(models.OrderMain)
	if has, err := db.Table("dc_order.order_main").Alias("om").Join("inner", "dc_order.order_product op", "op.order_sn = om.order_sn").
		Where("om.order_status = 10 and om.member_id = ? and om.order_type = ? and op.sku_id = ?", in.ScrmId, orderType, cardTemplate.Id).
		Select("om.order_sn,om.create_time").Get(om); err != nil {
		out.Message = err.Error()
		return
	} else if has {
		cols := "is_dis,dis_member_id,dis_commis_rate,chain_id,out_member_id,customer_service_id,customer_service_rate,dis_type"
		if disInfo.DisMemberId == 0 {
			if _, err = db.Where("order_sn = ?", om.OrderSn).Cols(cols).Update(&models.UpetVrOrder{}); err != nil {
				out.Message = err.Error()
				return
			}
		} else {
			if _, err = db.Where("order_sn = ?", om.OrderSn).Cols(cols).Update(&models.UpetVrOrder{
				IsDis:               1,
				DisMemberId:         disInfo.DisMemberId,
				DisCommisRate:       float64(cardTemplate.DisRate),
				ChainId:             disInfo.ChainId,
				OutMemberId:         disInfo.OutMemberId,
				CustomerServiceId:   disInfo.CustomerServiceId,
				CustomerServiceRate: disInfo.CustomerServiceRate,
				DisType:             disInfo.DisType,
			}); err != nil {
				out.Message = err.Error()
				return
			}
		}
		out.Code = 200
		out.OrderSn = om.OrderSn
		out.Deadline = int32(om.CreateTime.Add(30 * time.Minute).Unix())
		return
	}

	// 插入订单
	orderSn := GetSn("order")[0]
	price := int32(decimal.NewFromFloat32(cardTemplate.MemberDiscPrice * 100).Round(0).IntPart())
	if in.Source == 2 {
		price = 0
	}

	memberMobile := MobileReplaceWithStar(member.MemberMobile)
	enMobile := utils.MobileEncrypt(member.MemberMobile)
	now := time.Now()
	expire := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).AddDate(0, 0, int(cardTemplate.DurationDate))

	vrOrder := &models.UpetVrOrder{
		OrderSn:         cast.ToInt64(orderSn),
		StoreId:         1,
		StoreName:       "优宠商城官方自营",
		BuyerId:         member.MemberId,
		BuyerName:       member.MemberName,
		BuyerPhone:      memberMobile,
		EncryptMobile:   enMobile,
		AddTime:         now.Unix(),
		OrderAmount:     kit.FenToYuan(price),
		OrderState:      10,
		GoodsId:         int(cardTemplate.SkuId),
		GoodsName:       cardTemplate.CardName,
		GoodsPrice:      kit.FenToYuan(price),
		GoodsNum:        1,
		VrIndate:        int(expire.Unix()),
		GoodsSpec:       fmt.Sprintf("天数：%d天", cardTemplate.DurationDate),
		OrderType:       orderType,
		VrInvalidRefund: expireRefund,
		GoodsImage:      pic,
	}

	if disInfo.DisMemberId != 0 {
		vrOrder.IsDis = 1
		vrOrder.DisMemberId = disInfo.DisMemberId
		vrOrder.DisCommisRate = float64(cardTemplate.DisRate)
		vrOrder.ChainId = disInfo.ChainId
		vrOrder.OutMemberId = disInfo.OutMemberId
		vrOrder.CustomerServiceId = disInfo.CustomerServiceId
		vrOrder.CustomerServiceRate = disInfo.CustomerServiceRate
		vrOrder.DisType = disInfo.DisType
	}

	orderMain := &models.OrderMain{
		OrderSn:            orderSn,
		OldOrderSn:         orderSn,
		OrderStatus:        10,
		OrderStatusChild:   20101,
		ShopId:             "1",
		ShopName:           "优宠商城官方自营",
		MemberId:           in.ScrmId,
		MemberName:         in.UserName,
		MemberTel:          memberMobile,
		EnMemberTel:        enMobile,
		ReceiverMobile:     memberMobile,
		EnReceiverMobile:   enMobile,
		Total:              price,
		PayTotal:           price,
		GoodsTotal:         price,
		GoodsPayTotal:      price,
		ActualReceiveTotal: price,
		OrderType:          orderType,
		Source:             1,
		DeliveryType:       3,
		LogisticsCode:      "0000",
		ChannelId:          5,
		UserAgent:          in.UserAgent,
		IsVirtual:          1,
		IsPushTencent:      1,
		AppChannel:         1,
		OrderPayType:       "05",
	}

	orderDetail := &models.OrderDetail{
		OrderSn: orderSn,
	}

	orderProduct := &models.OrderProduct{
		OrderSn:              orderSn,
		SkuId:                cast.ToString(cardTemplate.SkuId),
		ProductName:          cardTemplate.CardName,
		ProductType:          2,
		CombineType:          0,
		MarkingPrice:         price,
		DiscountPrice:        price,
		PayPrice:             price,
		Number:               1,
		PaymentTotal:         price,
		SkuPayTotal:          price,
		DeliverNum:           1,
		TermType:             1,
		TermValue:            int32(expire.Unix()),
		VirtualInvalidRefund: int32(expireRefund),
		Image:                pic,
	}

	if _, err := db.Insert(vrOrder, orderMain, orderDetail, orderProduct); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	out.OrderSn = orderSn
	out.Deadline = int32(now.Add(30 * time.Minute).Unix())

	return
}

// PayNotify 服务包订单支付通知
func (c CardService) PayNotify(ctx context.Context, in *oc.CardPayNotifyReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}
	logFix := fmt.Sprintf("PayNotify=====订单编号：%s", in.OrderSn)
	glog.Info(logFix, "入参：", utils.InterfaceToJSON(in))
	defer func() {
		glog.Error("CardService PayNotify 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	db := GetUPetDBConn()

	order := new(models.OrderMain)

	//如果VIP单已经存在了，说明已经处理过了，直接跳过
	// 分销数据填充
	vrOrderHave := new(models.VipCardOrder)
	if _, err := db.Where("order_sn = ?", in.OrderSn).Get(vrOrderHave); err != nil {
		out.Message = err.Error()
		return
	} else if vrOrderHave.Id > 0 {
		out.Code = 200
		return
	}

	// 子单号
	if has, err := db.Where("order_sn = ? and order_status >= 20 and order_type in (17,18)", in.OrderSn).
		Select("/*FORCE_MASTER*/ *").Get(order); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "订单未找到"
		return
	}

	op := new(models.OrderProduct)

	var packId int32
	if has, err := db.Where("order_sn = ?", in.OrderSn).Select("sku_id,term_value").Get(op); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "未找到订单关联商品"
		return
	}

	skuId := cast.ToInt32(op.SkuId)
	template := new(models.VipCardTemplate)

	session := db.NewSession()
	defer session.Close()

	_ = session.Begin()

	now := time.Now()
	isHave := 0

	if has, err := db.Where("sku_id = ?", skuId).Get(template); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = fmt.Sprintf("卡模板 %d 未找到", skuId)
		return
	}

	//判断是否有生效的卡，来判断是加购，还是首次购买
	if _, err := db.Table("datacenter.vip_card_order").Alias("o").
		Join("inner", "datacenter.vip_card_template t", "o.card_id=t.id").
		Where("o.user_id= ? and t.or_id=? and o.state=10 and o.expiry_date >= ? ", order.MemberId, template.OrId, now.Format(kit.DATE_LAYOUT)).
		Select("o.id").Get(&isHave); err != nil {
		out.Message = err.Error()
		return
	}
	//开卡类型
	carDType := 3
	if isHave == 0 {
		carDType = 1
	}

	// 会员卡订单
	if order.OrderType == 17 {
		var userLevelId int32
		if _, err := db.Table("upet_member").Where("scrm_user_id = ?", order.MemberId).Select("user_level_id").Get(&userLevelId); err != nil {
			out.Message = err.Error()
			return
		}

		if _, err := db.Table("datacenter.vip_card_equity_config").Alias("c").
			Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
			Where("c.card_tid = ? and e.equity_type = 6 and e.status = 1", template.Id).
			Select("c.privilege_ids").Get(&packId); err != nil {
			out.Message = err.Error()
			return
		} else if packId == 0 {
			glog.Info("CardService PayNotify ", in.OrderSn, " 未找到家庭医生服务包")
		}

		vco := models.VipCardOrder{
			CardId:         int(template.Id),
			OrderSn:        in.OrderSn,
			OrderAmount:    order.PayTotal,
			UserId:         order.MemberId,
			UserMobile:     order.MemberTel,
			EnUserMobile:   order.EnMemberTel,
			UserName:       order.MemberName,
			UserLevelId:    userLevelId,
			CardName:       template.CardName,
			CardType:       template.CardType,
			CardCycle:      template.CardCycle,
			PayTime:        order.PayTime,
			State:          10,
			ExpiryDate:     time.Unix(int64(op.TermValue), 0),
			CollectionType: int32(carDType),
			VirtualCardId:  0,
		}

		// 分销数据填充
		vrOrder := new(models.UpetVrOrder)
		if _, err := db.Where("erp_order_sn = ?", in.OrderSn).Select("/*FORCE_MASTER*/ *").Get(vrOrder); err != nil {
			out.Message = err.Error()
			return
		} else if vrOrder.IsDis > 0 {
			vco.DisMemberId = vrOrder.DisMemberId
			vco.DisCommission = int32(decimal.NewFromFloat(vrOrder.OrderAmount * vrOrder.DisCommisRate).Round(0).IntPart())
			vco.Source = 1
			in.Source = 1
			// 分销人名称
			if _, err = db.Table("upet_member").Where("member_id = ?", vrOrder.DisMemberId).Select("bill_user_name").Get(&vco.DisMemberName); err != nil {
				out.Message = err.Error()
				return
			}

			if vrOrder.ChainId > 1 {
				type Chain struct {
					AreaInfo string
					Region   string
				}
				chain := new(Chain)
				if has, err := db.Table("upet_chain").Alias("c").
					Join("left", "upet_chain_region r", "r.region_id = c.region_id").
					Select("c.area_info,r.region_name as region").
					Where("c.chain_id = ?", vrOrder.ChainId).Get(chain); err != nil {
					out.Message = err.Error()
					return
				} else if has {
					vco.Region = chain.Region
					area := strings.Split(chain.AreaInfo, " ")
					if len(area) > 0 {
						vco.Province = area[0]
					}
					if len(area) > 1 {
						vco.City = area[1]
					}
				}
			}
		}

		//不是激活码兑换的才需要生成一个码
		// 来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-充值赠送(门店开卡)
		if in.Source != 2 {
			//主动购买的卡也生成一个虚拟卡号
			strPass := "FY" + utils.Get16MD5Encode(uuid.NewString())
			itemMode := models.VipCardVirtual{}
			itemMode.OrgId = template.OrId
			itemMode.UserId = order.MemberId
			itemMode.UserMobile = order.MemberTel
			itemMode.EnUserMobile = order.EnMemberTel
			itemMode.OrgName = ""
			itemMode.BatchId = "-1"
			itemMode.TemplateId = int(template.Id)
			itemMode.SellType = 0
			itemMode.Status = 1
			itemMode.UseTime = time.Now()
			itemMode.ExpireTime = time.Unix(int64(op.TermValue), 0)
			//存数据库需要加密
			itemMode.CardPass = utils.MobileEncrypt(strPass)

			_, err := session.Insert(&itemMode)
			if err != nil {
				glog.Error("创建卡密插入数据库失败", err.Error())
				session.Rollback()
				out.Message = err.Error()
				return
			}
			//赋值虚拟卡号给订单
			vco.VirtualCardId = itemMode.CardId
		}

		// 插入会员卡订单记录
		if _, err := session.Insert(vco); err != nil {
			_ = session.Rollback()
			out.Message = err.Error()
			return
		}
		// 插入服务金账户和账户明细 vip-2.0.1
		//todo 判断该卡模板是否有配置 健康服务金权益
		var fwjEquityData models.VipCardEquity
		if _, err := db.Table("datacenter.vip_card_equity_config").Alias("c").
			Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
			Where("c.card_tid = ? and e.equity_type=10 and e.status = 1 AND find_in_set(?,e.collection_ids) ", template.Id, carDType).
			Select("e.id ,e.equity_short_name").Get(&fwjEquityData); err != nil {
			out.Message = err.Error()
			return
		}
		if fwjEquityData.Id > 0 {
			d := time.Unix(int64(op.TermValue), 0)
			MemberPropertyGuaranteeQuotaData := models.MemberPropertyGuaranteeQuota{
				MemberId:                 order.MemberId,
				InsurancePolicyNumber:    in.OrderSn,
				QuotaType:                2,
				ServiceUnusedQuota:       30000,
				ServiceUsedQuota:         0,
				ServiceFreezeQuota:       0,
				ServiceUnusedQuotaExpire: time.Date(d.Year(), d.Month(), d.Day(), 23, 59, 59, 0, time.Local),
				ServiceAccumulateQuota:   30000,
				VirtualCardId:            vco.VirtualCardId,
			}
			MemberPropertyGuaranteeQuotaDetailData := models.MemberPropertyGuaranteeQuotaDetail{
				InsurancePolicyNumber: in.OrderSn,
				QuotaType:             2,
				GenerateTime:          time.Now().Local(),
				GenerateAmount:        30000,
				Remark:                "每月新增额度",
				CreateTime:            time.Now().Local(),
				MemberId:              order.MemberId,
				VirtualCardId:         vco.VirtualCardId,
			}

			glog.Info("CardService PayNotify 入参：", kit.JsonEncode(in), "插入健康服务金账户信息:", utils.InterfaceToJSON(MemberPropertyGuaranteeQuotaData), "|插入健康服务金账户明细：", utils.InterfaceToJSON(MemberPropertyGuaranteeQuotaDetailData))
			if _, err := session.Insert(&MemberPropertyGuaranteeQuotaData); err != nil {
				_ = session.Rollback()
				glog.Error(logFix, "插入服务金账户失败：", err.Error())
				out.Message = err.Error()
				return
			}

			if _, err := session.Insert(&MemberPropertyGuaranteeQuotaDetailData); err != nil {
				glog.Error(logFix, "插入服务金账户明细失败：", err.Error())
				_ = session.Rollback()
				out.Message = err.Error()
				return
			}
		}

		// 变更用户会员卡状态
		if _, err := session.Where("scrm_user_id = ?", order.MemberId).Update(models.UpetMember{VipCardState: 1}); err != nil {
			_ = session.Rollback()
			out.Message = err.Error()
			return
		}
	} else if order.OrderType == 18 { // 服务包订单
		packId = skuId
	}

	if err := session.Commit(); err != nil {
		out.Message = err.Error()
		return
	}

	// 浙闽二区，子龙打折卡、门店券自动发放
	if template.OrId == 13 {
		//通过是被动领取来发放
		var ecs []*models.EquityConfigReceive
		if err := db.Table("datacenter.vip_card_equity_config").Alias("c").
			Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
			Where("c.card_tid = ? and e.equity_type in (2,8) and e.status = 1 and e.is_active=2 and e.issue_type=1 and find_in_set(?,e.collection_ids)", template.Id, carDType).
			Select("e.id as equity_id,e.equity_type,c.privilege_ids,c.receive_num,e.receive_type,e.equity_short_name").
			Find(&ecs); err != nil {
			out.Message = err.Error()
			return
		}

		for _, ec := range ecs {
			// 处理“医疗礼包”需要根据会员卡来源进行筛选，过滤掉不同销售方式的门店券
			if ec.EquityShortName == "医疗礼包" {
				privilegeIds, err := filterPrivilegeIds(ec, template, in.Source)
				if err != nil {
					glog.Error(fmt.Sprintf("医疗礼包门店券自动发放，过滤门店券操作异常，e=%v", err.Error()))
					continue
				}
				ec.PrivilegeIds = privilegeIds
			}

			for i, id := range strings.Split(strings.TrimSpace(ec.PrivilegeIds), ",") {
				// 达到了领取限制，不处理
				if (i+1) > ec.ReceiveNum && (ec.ReceiveType == 1 || ec.ReceiveType == 2) {
					break
				}
				c.EquityReceive(ctx, &oc.CardEquityReceiveReq{
					OrderSn:  in.OrderSn,
					EquityId: ec.EquityId,
					Id:       id,
					ScrmId:   order.MemberId,
				})
			}
		}
	}

	// 创建服务包
	if packId > 0 {
		go func() {
			_ = retryDo(func() error {
				packBuyWay := 1
				if order.OrderType == 17 {
					packBuyWay = 2
				}
				if res, err := c.ServicePackCreate(ctx, &oc.CardServicePackCreateReq{
					AwOrderSn:          order.OrderSn,
					MemberId:           order.MemberId,
					MemberMobile:       utils.MobileDecrypt(order.EnMemberTel),
					MemberName:         order.MemberName,
					OrderSource:        order.ChannelId,
					OrderSourceChannel: order.UserAgent,
					OriginalPrice:      order.PayTotal,
					PackBuyWay:         int32(packBuyWay),
					PayPrice:           order.PayTotal,
					SkuId:              packId,
				}); err != nil {
					return err
				} else {
					if res.Code != 200 {
						return errors.New(res.Message)
					}
					if _, err = db.Table("dc_order.order_detail").In("order_sn", []string{order.OrderSn, order.ParentOrderSn}).Update(models.OrderDetail{
						ConsultOrderSn: cast.ToString(res.Data.SignId),
					}); err != nil {
						glog.Error("CardService PayNotify " + err.Error())
					}
				}
				return nil
			}, []time.Duration{3 * time.Second, 1 * time.Minute, 3 * time.Minute})
		}()
	}

	out.Code = 200

	return
}

// SalesTypeMap 卡来源对应销售方式 【卡来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-门店开卡】 【销售方式：1-主动购买、分销购买、虚拟卡券兑换，2-充值赠送】
var SalesTypeMap = map[int32]int32{
	0: 1,
	1: 1,
	2: 1,
	3: 2,
}

func filterPrivilegeIds(ec *models.EquityConfigReceive, template *models.VipCardTemplate, source int32) (string, error) {
	glog.Info(fmt.Sprintf("METHOD filterPrivilegeIds, params: source=%d, ec = %v, template=%v", source, kit.JsonEncode(ec), kit.JsonEncode(template)))
	privilegeIds := ec.PrivilegeIds
	salesType := SalesTypeMap[source]

	session := GetDcDBConn().Table("vip_card_equity_value")
	var values []string

	glog.Info(fmt.Sprintf("METHOD filterPrivilegeIds, Sql args: card_tid=%v, or_id=%v, equity_id=%v, sales_type=%v", template.Id, template.OrId, ec.EquityId, salesType))
	if err := session.SQL("SELECT privilege_id FROM datacenter.vip_card_equity_value WHERE card_tid=? AND or_id=? AND equity_id=? AND sales_type=?",
		template.Id, template.OrId, ec.EquityId, salesType).Find(&values); err != nil {
		glog.Error(fmt.Sprintf("查询医疗礼包对应的赠送价值配置信息异常：card_tid=%v, or_id=%v, equity_id=%v, sales_type=%v", template.Id, template.OrId, ec.EquityId, salesType))
		return "", errors.New("查询医疗礼包对应的赠送价值配置信息异常")
	}

	if len(values) > 0 {
		privilegeIds = strings.Join(values, ",")
	}

	return privilegeIds, nil
}

// NewByCode 通过激活码开卡
func (c CardService) NewByCode(ctx context.Context, in *oc.CardNewByCodeReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}

	defer func() {
		glog.Info("CardService NewByCode 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	in.Code = strings.TrimSpace(in.Code)
	if in.Code == "" {
		out.Message = "兑换码不能为空"
		return
	}

	enCode := utils.MobileEncrypt(in.Code)

	r := GetRedisConn()
	key := "order-center:card:new-buy-code:" + in.ScrmId
	if rs, err := r.SetNX(key, 1, 3*time.Minute).Result(); err != nil {
		out.Message = err.Error()
		return
	} else {
		defer r.Del(key)
		if !rs {
			out.Message = "兑换处理中，请不要重复提交"
			return
		}
	}

	// 当日单个用户能兑换10次
	checkKey := "order-center:card:new-buy-code:" + time.Now().Format(kit.DATE_LAYOUT)
	if count, err := r.HIncrBy(checkKey, in.ScrmId, 1).Result(); err != nil {
		out.Message = err.Error()
		return
	} else {
		go r.Expire(checkKey, 24*time.Hour)
		if count > 10 {
			out.Message = "您兑换次数太过频繁，请明天再试"
			return
		}
	}

	db := GetUPetDBConn()

	vv := new(models.VipCardVirtual)

	if has, err := db.Where("card_pass = ?", enCode).Get(vv); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "兑换码无效"
		return
	} else if vv.Status == 1 {
		out.Message = "兑换码已激活"
		return
	} else if vv.Status == 2 {
		out.Message = "兑换码已注销"
		return
	} else if vv.Status != 0 || (vv.ExpireTime.Before(time.Now()) && !vv.ExpireTime.IsZero()) {
		out.Message = "兑换码已失效"
		return
	}

	//查询是否是实物订单卡过来的数据
	vrSwOrder := new(models.UpetOrders)
	vrSwOrderSn := ""
	if has, err := db.Where("virtual_card_id = ? and order_type=21", vv.CardId).Get(vrSwOrder); err != nil {
		out.Message = "查询是否实物购买卡报错" + err.Error()
		return
	} else {
		vrSwOrderSn = cast.ToString(vrSwOrder.OrderSn)
		//如果存在需要判断状态
		if has {
			if vrSwOrder.OrderState <= 10 {
				out.Message = "实物订单已退款或未付款"
				return
			}
			if vrSwOrder.LockState > 0 {
				out.Message = "实物订单目前锁定状态"
				return
			}
			if vrSwOrder.RefundState > 0 {
				out.Message = "实物订单已退款无法激活"
				return
			}

		} else {
			vrSwOrderSn = ""
		}
	}

	out1, err := c.New(ctx, &oc.CardNewReq{
		CardId:    int32(vv.TemplateId),
		ScrmId:    in.ScrmId,
		UserName:  in.UserName,
		UserAgent: in.UserAgent,
		Source:    1,
	})
	if err != nil {
		out.Message = err.Error()
		return
	} else if out1.Code != 200 {
		out.Message = out1.Message
		return
	}

	// 必要的延迟，避免线上的主从延迟导致的问题
	time.Sleep(100 * time.Microsecond)

	session := db.NewSession()
	defer session.Close()
	session.Begin()

	now := time.Now()
	// 更新电商订单状态
	if _, err = session.Where("order_sn = ?", out1.OrderSn).Update(&models.UpetVrOrder{
		OrderState:  20,
		PaymentTime: int(now.Unix()),
		TradeNo:     in.Code, // 支付单号记录为卡密
	}); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	//查询实物订单
	vrOrder := new(models.UpetVrOrder)
	if has, err := session.Where("order_sn = ? ", out1.OrderSn).Select("buyer_phone,encrypt_mobile,vr_indate,order_amount").Get(vrOrder); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "卡订单未找到"
		return
	}

	payNotify := &models.OrderPayNotify{
		OrderSn:    out1.OrderSn,
		PaySn:      in.Code,
		PayMode:    4,
		PayTime:    now,
		PayAmount:  int32(decimal.NewFromFloat(vrOrder.OrderAmount * 100).Round(0).IntPart()),
		CreateTime: time.Now(),
	}
	if _, err = session.Insert(payNotify); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}
	// 来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-充值赠送(门店开卡)
	payNotify.Source = 2

	vcv := &models.VipCardVirtual{
		Status:       1,
		UserId:       in.ScrmId,
		UseTime:      time.Now(),
		UserMobile:   vrOrder.BuyerPhone,
		EnUserMobile: vrOrder.EncryptMobile,
	}
	if _, err = session.Where("card_pass = ?", enCode).Update(vcv); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	if err = session.Commit(); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	DealOrderPayNotify(payNotify)

	//激活的是一对一的，先查询子单
	order := new(models.OrderMain)
	if _, err = db.Where("parent_order_sn=?", out1.OrderSn).Get(order); err != nil {
		glog.Error("根据主单查询子单报错：", err.Error())
	}

	// 更新卡订单卡密标识
	if _, err = db.Where("order_sn = ?", order.OrderSn).Update(&models.VipCardOrder{
		CardPass:      in.Code,
		VirtualCardId: vv.CardId,
		Source:        2,
		EntityOrderSn: vrSwOrderSn,
	}); err != nil {
		glog.Error("NewByCode 更新卡订单卡密标识出错：", order.OrderSn, " ", in.Code, " 卡号：", vv.CardId, err.Error())
	}
	if _, err = db.Exec("update datacenter.member_property_guarantee_quota set virtual_card_id=? where insurance_policy_number=?", vv.CardId, order.OrderSn); err != nil {
		glog.Error("NewByCode 更新服务金卡号出错：", order.OrderSn, " ", in.Code, " 卡号：", vv.CardId, err.Error())
	}
	if _, err = db.Exec("update datacenter.member_property_guarantee_quota_detail set virtual_card_id=? where insurance_policy_number=?", vv.CardId, order.OrderSn); err != nil {
		glog.Error("NewByCode 更新服务金详情卡号出错：", order.OrderSn, " ", in.Code, " 卡号：", vv.CardId, err.Error())
	}
	//说明是通过实物订单过来的卡，需要通知实物订单完成
	if vrSwOrderSn != "" {
		par := dto.RefundApplyReq{}
		par.OrderSn = vrSwOrderSn

		data := kit.JsonEncode(par)
		errStr := ReqstUpet(data, "ordercompleted")
		if errStr != "1" {
			glog.Error("NewByCode 实物订单通完成失败：", order.OrderSn, " ", in.Code, " 卡号：", vv.CardId, errStr)
		}

	}

	// 必要的延迟，避免线上的主从延迟导致的问题
	time.Sleep(50 * time.Microsecond)

	out.Code = 200
	return
}

// NewByStore 通过门店退卡
func (c CardService) CardReturnByStore(ctx context.Context, in *oc.CardPayNotifyReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}
	defer func() {
		glog.Info("CardService NewByStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		if out.Code != 200 {
			acCenter := ac.GetActivityCenterClient()
			data, _ := structpb.NewStruct(map[string]interface{}{
				"msgtype": "markdown",
				"markdown": map[string]interface{}{
					"content": fmt.Sprintf("## VIP退卡处理失败：\n"+
						"> 异常类型：**<font color=\"red\">%s</font>**\n"+
						"> 异常单号：**<font color=\"red\">%s</font>**",
						"储值卡退款", in.OrderSn),
				},
			})

			key, _ := config.Get("awen.exception.robot")
			notifyIn := &ac.SendWebhookNotifyReq{
				Key:  key,
				Data: data,
			}

			ret, err := acCenter.Base.SendWebhookNotify(context.Background(), notifyIn)
			if err != nil {
				glog.Error("发送机器人提报警出错：", in.OrderSn, err.Error())
			}
			if ret.Code != 200 {
				glog.Error("发送机器人提报警出错：", in.OrderSn, ret.Message)
			}

		}
	}()

	redisConn := GetRedisConn()
	lockCard := "lock:CardReturnByStore:" + in.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 3*time.Minute).Val()
	if !lockRes {
		out.Message = "退款单正在处理中请稍后"
		return
	}
	defer redisConn.Del(lockCard)

	orderSn := strings.TrimSpace(in.OrderSn)
	if len(orderSn) == 0 {
		out.Message = "订单号不能为空"
		return
	}
	//判断手机号和订单是否符对应，并且订单号的状态是否对
	db := GetDcDBConn()
	order := new(models.VipCardOrder)
	//兼容主订单和子订单，先查询订单号
	//激活的是一对一的，先查询子单
	orderMain := new(models.OrderMain)
	if _, err := db.Where("parent_order_sn=? or order_sn=?", in.OrderSn, in.OrderSn).Get(orderMain); err != nil {
		glog.Error("根据主单查询子单报错：", err.Error())
		out.Message = "查询订单错误"
		return
	}

	selectSql := "SELECT * FROM vip_card_order WHERE order_sn=?  and source=3"
	ok, err := db.SQL(selectSql, orderMain.OrderSn).Get(order)
	if err != nil || !ok {
		out.Message = "没有找到对应的会员卡订单记录！"
		return
	}
	//如果是已经退款的直接返回成功
	if order.State == 20 {
		out.Code = 200
		return
	}
	//申请退卡
	//开始调用电商
	par := dto.RefundApplyReq{}
	par.ApplyType = 1
	par.ApplyUser = "admin"
	par.BuyerMessage = "子龙储值卡退款"
	par.OrderSn = order.OrderSn

	data := kit.JsonEncode(par)
	errStr := ReqstUpet(data, "vrOrderAppay")
	if errStr != "1" {
		out.Message = errStr
		return
	}
	//然后自动审批
	// 必要的延迟，避免线上的主从延迟导致的问题
	time.Sleep(100 * time.Microsecond)

	//查询主库退款单号
	refundId := 0
	upDb := GetUPetDBConn()
	_, err = upDb.SQL("select /*FORCE_MASTER*/ refund_id from upet_vr_refund a "+
		"INNER JOIN `upet_vr_order` b ON a.order_sn=b.order_sn where b.erp_order_sn = ? and a.admin_state=1", in.OrderSn).
		Get(&refundId)
	if err != nil {
		glog.Error("查询VIP退款单出错：", in.OrderSn, " ", err.Error())
		out.Message = "查询VIP退款单出错"
		return
	}
	if refundId == 0 {
		glog.Error("查询VIP退款单未查询到：", in.OrderSn)
		out.Message = "查询VIP退款单出错"
		return
	}

	//调用自动审核

	spPar := oc.RefundExamineReq{}
	spPar.RefundId = int32(refundId)
	spPar.RefundAmount = 0
	spPar.AdminType = 1
	spPar.State = 2
	spPar.UserName = "admin"
	spPar.UserNo = "admin"
	ret, err := c.RefundExamine(context.Background(), &spPar)
	if err != nil {
		glog.Error("调用自动审核报错：", in.OrderSn, " ", err.Error())
		out.Message = "调用自动审核报错"
		return
	}
	if ret.Code != 200 {
		out.Message = ret.Message
		glog.Error("调用自动审核报错：", in.OrderSn, " ", ret.Message)
		return
	}
	out.Code = 200
	return
}

// NewByStore 通过门店开卡
func (c CardService) NewByStore(ctx context.Context, in *oc.CardNewByStoreReq) (out *oc.CardNewByStoreRes, e error) {
	out = &oc.CardNewByStoreRes{Code: 400}

	defer func() {
		glog.Info("CardService NewByStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	if in.CardId == 0 {
		out.Message = "卡模板Id不能为空"
		return
	} else if in.ScrmId == "" {
		out.Message = "用户Id不能为空"
		return
	} else if in.StoreId == 0 {
		out.Message = "门店Id不能为空"
		return
	}

	r := GetRedisConn()
	key := "order-center:card:new-buy-store:" + in.ScrmId
	if rs, err := r.SetNX(key, 1, 3*time.Minute).Result(); err != nil {
		out.Message = err.Error()
		return
	} else {
		defer r.Del(key)
		if !rs {
			out.Message = "开卡处理中，请不要重复提交"
			return
		}
	}

	db := GetUPetDBConn()

	out1, err := c.New(ctx, &oc.CardNewReq{
		CardId: in.CardId,
		ScrmId: in.ScrmId,
		Source: 2,
	})
	if err != nil {
		out.Message = err.Error()
		return
	} else if out1.Code != 200 {
		if strings.Contains(out1.Message, "存在生效中的会员卡") {
			out.Code = 200
			out.Data = &oc.CardNewByStoreRes_Data{}
			return
		}
		out.Message = out1.Message
		return
	}

	// 必要的延迟，避免线上的主从延迟导致的问题
	time.Sleep(100 * time.Microsecond)

	session := db.NewSession()
	defer session.Close()
	session.Begin()

	now := time.Now()
	// 更新电商订单状态
	if _, err = session.Where("order_sn = ?", out1.OrderSn).Update(&models.UpetVrOrder{
		OrderState:  20,
		PaymentTime: int(now.Unix()),
		TradeNo:     cast.ToString(in.StoreId), // 支付单号记录为卡密
	}); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	vrOrder := new(models.UpetVrOrder)
	if has, err := session.Where("order_sn = ?", out1.OrderSn).Select("buyer_phone,encrypt_mobile,vr_indate,order_amount").Get(vrOrder); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "卡订单未找到"
		return
	}

	payNotify := &models.OrderPayNotify{
		OrderSn:    out1.OrderSn,
		PaySn:      cast.ToString(in.StoreId),
		PayMode:    4,
		PayTime:    now,
		PayAmount:  int32(decimal.NewFromFloat(vrOrder.OrderAmount * 100).Round(0).IntPart()),
		CreateTime: time.Now(),
	}
	if _, err = session.Insert(payNotify); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}
	// 来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-充值赠送(门店开卡)
	payNotify.Source = 3

	if err = session.Commit(); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	DealOrderPayNotify(payNotify)

	//激活的是一对一的，先查询子单
	order := new(models.OrderMain)
	if _, err = db.Where("parent_order_sn=?", out1.OrderSn).Get(order); err != nil {
		glog.Error("根据主单查询子单报错：", err.Error())
	}

	if _, err = db.Where("order_sn = ?", order.OrderSn).Update(&models.VipCardOrder{
		Source:  3,
		StoreId: in.StoreId,
	}); err != nil {
		glog.Error("NewByStore 更新卡订单标识出错：", err.Error())
	}

	// 必要的延迟，避免线上的主从延迟导致的问题
	time.Sleep(50 * time.Microsecond)

	out.Data = &oc.CardNewByStoreRes_Data{OrderSn: order.OrderSn}
	out.Code = 200
	return
}

// ServicePackCreate 服务包创建
func (c CardService) ServicePackCreate(ctx context.Context, in *oc.CardServicePackCreateReq) (out *oc.CardServicePackCreateRes, e error) {
	out = &oc.CardServicePackCreateRes{Code: 400}

	code, body := utils.HttpPostToDigitalHealth("/nuclei-api/callback/service-pack/create", kit.JsonEncodeByte(in), "json")

	defer func() {
		glog.Info("CardService ServicePackCreate 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out), code, body)
	}()

	if err := json.Unmarshal([]byte(body), out); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = int32(code)

	return
}

// ServicePackActivity 服务包激活
func (c CardService) ServicePackActivity(ctx context.Context, in *oc.CardServicePackActivityReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}

	defer func() {
		glog.Info("CardService ServicePackActivity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	if in.SignId == 0 {
		out.Message = "签约Id不能为空"
		return
	}

	db := GetUPetDBConn()

	session := db.NewSession()
	defer session.Close()

	session.Begin()

	order := new(models.OrderMain)
	if has, err := db.Table("dc_order.order_main").Alias("om").
		Join("inner", "dc_order.order_detail od", "od.order_sn = om.order_sn").
		Where("od.consult_order_sn = ? and om.member_id = ? and om.order_type in (17,18) and om.parent_order_sn != ''", cast.ToString(in.SignId), in.MemberId).
		OrderBy("om.id desc").
		Select("om.order_sn,om.parent_order_sn,om.order_status,om.order_status_child,om.order_type").Get(order); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "未找到订单"
		return
	} else {
		if order.OrderStatus <= 10 {
			out.Message = "订单状态 未支付或已退款 不允许激活"
			return
		}

		if count, err := db.Table("dc_order.refund_order").Where("refund_state in (1,5,6,7) and order_sn = ?", order.OrderSn).Count(); err != nil {
			out.Message = err.Error()
			return
		} else if count > 0 {
			out.Message = "订单退款中，不允许激活"
			return
		}

		if order.OrderType == 17 {
			// 处理服务包权益激活
			var cardId int32
			if _, err = db.Table("datacenter.vip_card_order").Where("order_sn = ?", order.OrderSn).Select("card_id").Get(&cardId); err != nil {
				out.Message = err.Error()
				return
			}

			equity := new(models.VipCardEquityConfig)
			if has, err = db.Table("datacenter.vip_card_equity_config").Alias("c").
				Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
				Where("c.card_tid = ? and e.equity_type = 6 and e.status = 1", cardId).
				Select("c.*").Get(equity); err != nil {
				out.Message = err.Error()
				return
			} else if !has {
				out.Message = "未找到家庭医生服务权益配置"
				return
			}

			if has, err = session.Where("order_sn = ? and equity_id =?", order.OrderSn, equity.EquityId).Exist(&models.VipUserEquityRecord{}); err != nil {
				out.Message = err.Error()
				return
			} else if has {
				out.Message = "服务包已激活，请不要重复操作。"
				return
			}

			if _, err = session.Insert(models.VipUserEquityRecord{
				OrderSn:     order.OrderSn,
				EquityId:    equity.EquityId,
				PrivilegeId: equity.PrivilegeIds,
			}); err != nil {
				session.Rollback()
				out.Message = err.Error()
				return
			}
		} else if order.OrderType == 18 { // 服务包订单完成
			if order.OrderStatusChild == 30103 {
				out.Message = "服务包已激活，请不要重复操作。"
				return
			}

			// 电商订单完成
			if _, err = session.Exec(`update upet_vr_order o
left join upet_vr_order_code c on c.order_id = o.order_id
set c.vr_state = 1, c.erp_chargeoff = 1, c.is_settlement = 1,c.vr_usetime = unix_timestamp(),o.use_state = 1, o.order_state = 40, o.finnshed_time = unix_timestamp()
where o.order_sn = ? and order_type = 18;`, order.OrderSn); err != nil {
				session.Rollback()
				out.Message = err.Error()
				return
			}
			// 订单中心订单完成
			if _, err = session.Exec(`update dc_order.order_main om
left join dc_order.order_verify_code c on c.order_sn = om.order_sn
set om.order_status = 30,om.order_status_child = 30103
where om.order_sn=?;`, order.OrderSn); err != nil {
				session.Rollback()
				out.Message = err.Error()
				return
			}
		}
	}

	code, body := utils.HttpPostToDigitalHealth("/nuclei-api/callback/service-pack/sign", kit.JsonEncodeByte(in), "json")

	glog.Info("HttpPostToDigitalHealth /nuclei-api/callback/service-pack/sign 入参：", kit.JsonEncode(in), "，返回：", code, body)

	if code != 200 {
		session.Rollback()
		if err := json.Unmarshal([]byte(body), out); err != nil {
			out.Message = err.Error()
			return
		}
		out.Code = 400
		return
	}

	if err := session.Commit(); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// EquityReceive 权益领取
func (c CardService) EquityReceive(ctx context.Context, in *oc.CardEquityReceiveReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}
	defer func() {
		glog.Info("CardService EquityReceive 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	r := GetRedisConn()
	key := fmt.Sprintf("order-center:card:equity-receive:%s:%d:%s:%s", in.ScrmId, in.EquityId, in.Id, in.OrderSn)
	if rs, err := r.SetNX(key, 1, 1*time.Minute).Result(); err != nil {
		out.Message = err.Error()
		return
	} else {
		defer r.Del(key)
		if !rs {
			out.Message = "领取处理中，请不要重复提交"
			return
		}
	}
	db := GetUPetDBConn()
	//前端过来的没有判断，需要判断下
	if in.IsMonth == 0 {
		issue_type := 0
		_, err := db.SQL("select issue_type from datacenter.vip_card_equity where id=?", in.EquityId).Get(&issue_type)
		if err != nil {
			out.Message = "查询权益类型报错!"
			glog.Error("查询权益类型报错", err.Error())
			return
		}
		if issue_type == 2 {
			in.IsMonth = 1
		}
	}

	order := new(models.VipCardOrder)
	equityReceive := new(models.EquityConfigReceive)

	var refundState int32

	q := db.Table("dc_order.order_main").Alias("om").
		Join("inner", "dc_order.order_detail od", "od.order_sn = om.order_sn").
		Join("left", "dc_order.refund_order r", "r.order_sn = om.order_sn and r.refund_state in (1,5,6,7)").
		Where("om.parent_order_sn != '' and om.order_status > 10 and om.member_id = ?", in.ScrmId)

	if in.OrderSn != "" {
		q.Where("om.order_sn = ?", in.OrderSn)
	} else if in.SignId > 0 {
		q.Where("od.consult_order_sn = ?", cast.ToString(in.SignId))
	} else {
		out.Message = "参数错误"
		return
	}

	if has, err := q.Select("/*FORCE_MASTER*/ r.refund_state").Get(&refundState); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "未找到有效订单"
		return
	} else if refundState > 0 {
		out.Message = "订单状态退款中，领取失败"
		return
	}
	////月度发放的逻辑限制
	////判断时间和数量
	//monthData := models.VipUserEquityMonth{}
	//统一判断时间和插入记录表的时间
	nowTime := time.Now()
	nowTimeStr := nowTime.Format(kit.DATETIME_LAYOUT)

	// 会员卡权益
	if in.OrderSn != "" {
		if has, err := db.Where("order_sn = ? and user_id = ? and state=10 and expiry_date >= now()", in.OrderSn, in.ScrmId).Select("/*FORCE_MASTER*/ *").Get(order); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到有效的会员卡订单"
			return
		}

		q2 := db.Table("datacenter.vip_card_equity_config").Alias("c").
			Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
			Where("c.card_tid = ? and c.status = 1 and find_in_set(?,c.privilege_ids)", order.CardId, in.Id)

		if in.EquityId > 0 {
			q2.Where("e.id = ?", in.EquityId)
		} else if in.Type > 0 {
			q2.Where("e.equity_type = ?", in.Type)
		} else {
			out.Message = "权益Id或Type不能为空"
			return
		}

		// 验证权益配置
		if has, err := q2.Select("e.id as equity_id,e.equity_name,e.equity_type,c.privilege_ids,c.receive_num,e.receive_type,e.equity_short_name").Get(equityReceive); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到匹配的权益"
			return
		}

		in.Type = equityReceive.EquityType

		//不是月度领取的权益判断
		if equityReceive.EquityShortName != "月度返券" {
			if has, err := db.Where("order_sn = ? and equity_id = ? and privilege_id = ?", in.OrderSn, equityReceive.EquityId, in.Id).Exist(&models.VipUserEquityRecord{}); err != nil {
				out.Message = err.Error()
				return
			} else if has {
				out.Message = equityReceive.EquityName + "已领取，请不要重复领取。"
				return
			}

			// 领取限制
			if equityReceive.ReceiveType > 0 && equityReceive.ReceiveNum > 0 {
				if count, err := db.Table("datacenter.vip_user_equity_record").Where("order_sn = ? and equity_id = ?", in.OrderSn, equityReceive.EquityId).
					In("privilege_id", strings.Split(equityReceive.PrivilegeIds, ",")).Count(); err != nil {
					out.Message = err.Error()
					return
				} else if int(count) >= equityReceive.ReceiveNum {
					out.Message = "超出领取限制"
					return
				}
			}
		} else {
			//判断是否在当前领取周期内的数据
			//订单创建时间的所在月
			//当前所在月
			c := carbon.SetLocation(time.Local)
			orderYear := c.Parse(order.CreateTime.Format(kit.DATETIME_LAYOUT)).Year()
			orderMonth := c.Parse(order.CreateTime.Format(kit.DATETIME_LAYOUT)).Month()

			now := c.Parse(nowTimeStr)
			nowMonth := now.Month()
			nowYear := now.Year()

			diffMonth := nowMonth - orderMonth + ((nowYear - orderYear) * 12)
			beginadd := 0
			endadd := 0
			addAfterTime := c.Parse(order.CreateTime.Format(kit.DATETIME_LAYOUT)).AddMonthsNoOverflow(diffMonth)
			if nowTime.Before(addAfterTime.ToStdTime()) {
				beginadd = -1
			} else {
				endadd = 1
			}

			beTime := c.Parse(order.CreateTime.Format(kit.DATETIME_LAYOUT)).AddMonthsNoOverflow(diffMonth + beginadd).ToDateTimeString()
			endTime := c.Parse(order.CreateTime.Format(kit.DATETIME_LAYOUT)).AddMonthsNoOverflow(diffMonth + endadd).ToDateTimeString()
			if has, err := db.Where("order_sn = ? and equity_id = ? and privilege_id = ? and create_time>=? and create_time<?", in.OrderSn, equityReceive.EquityId, in.Id, beTime, endTime).Exist(&models.VipUserEquityRecord{}); err != nil {
				out.Message = err.Error()
				return
			} else if has {
				out.Message = equityReceive.EquityName + "已领取，请不要重复领取。"
				return
			}

		}
	} else if in.SignId > 0 { // 家庭服务包权益
		medical := GetMedicalDBConn()
		var isReceive int
		// 验证一下领取状态
		if has, err := medical.SQL(`select pssec.is_receive from pet_medical.pm_service_sign pss
inner join pet_medical.pm_service_sign_equity_coupon pssec on pss.sign_id = pssec.sign_id 
where pss.member_id =? and pss.sign_id=? and pssec.coupon_id = ?`, in.ScrmId, in.SignId, in.Id).Get(&isReceive); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到匹配的权益"
			return
		} else if isReceive == 1 {
			out.Message = "优惠券已领取成功，请不要重复领取。"
			return
		}
	} else {
		out.Message = "参数错误"
		return
	}

	// 券领取结果
	var receiveResult string

	// 开始发券
	switch in.Type {
	case 1:
		uClient := sh.GetUpetCenterClient()
		if rs, err := uClient.VS.IssueAwardByMallCoupon(uClient.Ctx, &sh.IssueAwardByMallCouponRequest{
			UserId:   in.ScrmId,
			CouponId: in.Id,
		}); err != nil {
			out.Message = err.Error()
			return
		} else if rs.Code != 200 {
			out.Message = rs.Message
			return
		} else if len(rs.Data) > 0 {
			receiveResult = rs.Data[0].CouponCode
		}
	case 2:
		var mobile string
		if has, err := GetDcDBConn().Table("scrm_organization_db.t_scrm_user_info").Where("user_id =?", in.ScrmId).Select("user_mobile").Get(&mobile); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "用户未找到"
			return
		}

		client := et.GetExternalClient()
		if rs, err := client.ZiLong.SendCoupon(client.Ctx, &et.SendCouponReq{
			TemplateIdArr: []int32{cast.ToInt32(in.Id)},
			PhoneArr:      []string{mobile},
			Number:        1,
		}); err != nil {
			out.Message = err.Error()
			return
		} else if rs.Code != 200 {
			out.Message = rs.Message
			return
		} else if len(rs.Data) > 0 && len(rs.Data[0].CouponList) > 0 {
			receiveResult = rs.Data[0].CouponList[0].CouponCode
		}
	case 8: // 打折卡
		client := et.GetExternalClient()
		if rs, err := client.ZiLong.DiscountCardNew(client.Ctx, &et.DiscountCardNewReq{
			CustomerId:            in.ScrmId,
			ScrmCardsCategoryCode: in.Id,
			CreateSource:          5,
			StartTime:             order.CreateTime.Format(kit.DATETIME_LAYOUT),
			EndTime:               order.ExpiryDate.Format(kit.DATE_LAYOUT) + " 23:59:59",
		}); err != nil {
			out.Message = err.Error()
			return
		} else if rs.Code != 200 {
			out.Message = rs.Message
			return
		} else {
			receiveResult = rs.Data
		}
	default:
		out.Message = "Type参数错误"
		return
	}

	// 后置任务
	if in.OrderSn != "" {
		// 插入领取记录
		if _, err := db.Insert(models.VipUserEquityRecord{
			OrderSn:     order.OrderSn,
			EquityId:    int(equityReceive.EquityId),
			PrivilegeId: in.Id,
			GiftOrderSn: receiveResult,
			CreateTime:  nowTime,
		}); err != nil {
			out.Message = err.Error()
			return
		}
		////修改是否领取的状态
		//monthData.Status = 1
		//if _, err := db.ID(monthData.Id).Cols("status").Update(&monthData); err != nil {
		//	out.Message = err.Error()
		//	return
		//}

	} else if in.SignId > 0 {
		go retryDo(func() error {
			req := kit.JsonEncodeByte(map[string]interface{}{
				"coupon_id": cast.ToString(in.Id),
				"member_id": in.ScrmId,
				"sign_id":   in.SignId,
			})
			code, body := utils.HttpPostToDigitalHealth("/nuclei-api/callback/service-pack/coupon/receive", req, "json")
			glog.Info("HttpPostToDigitalHealth /nuclei-api/callback/service-pack/coupon/receive 入参：", string(req), "，返回：", body)
			if code != 200 {
				return errors.New(body)
			}
			return nil
		}, []time.Duration{3 * time.Second, 1 * time.Minute, 3 * time.Minute})
	}

	out.Code = 200
	return
}

// Refund 退卡处理
func (c CardService) Refund(ctx context.Context, in *oc.CardRefundReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}

	db := GetUPetDBConn()
	refund := new(models.RefundOrder)

	if has, err := db.Where("refund_sn = ?", in.RefundSn).Select("refund_amount,order_sn,create_time,refund_reason,refunded_time,refund_sn").Get(refund); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "退款单未找到"
		return
	}

	order := new(models.OrderMain)
	if has, err := db.Where("order_sn = ?", refund.OrderSn).Select("member_id,parent_order_sn,order_sn,order_type").Get(order); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "关联订单未找到"
		return
	} else if order.OrderType != 17 && order.OrderType != 18 {
		out.Message = "订单类型不需要处理"
		return
	}

	session := db.NewSession()
	defer session.Close()

	session.Begin()

	//// 兼容订单号
	orderSn := order.OrderSn
	//if order.ParentOrderSn != "" {
	//	orderSn = order.ParentOrderSn
	//}

	finishType := 4

	// 会员卡
	if order.OrderType == 17 {
		// 会员卡订单标记为退款
		if _, err := db.Where("order_sn = ?", orderSn).Update(models.VipCardOrder{
			State:        20,
			RefundAmount: int32(decimal.NewFromFloat(cast.ToFloat64(refund.RefundAmount) * 100).Round(0).IntPart()),
			RefundTime:   time.Now(),
		}); err != nil {
			session.Rollback()
			out.Message = err.Error()
			return
		}
		//查询这个用户除了这个卡之后还是不是会员
		IsVip := 0
		if _, err := db.SQL("SELECT 1 FROM  datacenter.vip_card_order where user_id=? and order_sn!=? and expiry_date>=NOW() and state=10", order.MemberId, order.OrderSn).Get(&IsVip); err != nil {
			session.Rollback()
			out.Message = err.Error()
			return
		}

		//如果没有有效会员了
		if IsVip == 0 {
			// 同一个用户同时只有一个卡，所以直接清卡
			if _, err := db.Where("scrm_user_id = ?", order.MemberId).Cols("vip_card_state").Update(models.UpetMember{VipCardState: 0}); err != nil {
				session.Rollback()
				out.Message = err.Error()
				return
			}
		}
		finishType = 2
	}
	//是否退打折卡
	//获取最后一个有效的打折卡时间，如果没有，那么当前时间为过期时间传给子龙退卡
	IsRetrunDiscount := ""
	if _, err := db.SQL("SELECT expiry_date FROM  datacenter.vip_card_order a "+
		" INNER JOIN datacenter.`vip_card_template` b ON a.card_id=b.id "+
		" INNER JOIN  datacenter.`vip_card_equity_config` c ON c.card_tid=b.id "+
		" INNER JOIN datacenter.`vip_card_equity` d ON d.id=c.equity_id "+
		" where user_id=? and order_sn!=? and expiry_date>=NOW() and state=10 AND d.equity_type=8 order by a.expiry_date desc", order.MemberId, order.OrderSn).Get(&IsRetrunDiscount); err != nil {
		session.Rollback()
		out.Message = err.Error()
		return
	}

	if err := session.Commit(); err != nil {
		out.Message = err.Error()
		return
	}

	// 同步互联网医疗
	go func() (err error) {
		defer func() {
			if err != nil {
				glog.Error("Card Refund 同步互联网医疗出错 ", err.Error())
			}
		}()

		var signId string
		// 查一下有签约id的还要通知互联网医疗
		if _, err = db.Table("dc_order.order_detail").Where("order_sn = ?", orderSn).Select("consult_order_sn").Get(&signId); err != nil {
			return err
		}

		if signId != "" {
			return retryDo(func() error {
				req := kit.JsonEncodeByte(map[string]interface{}{
					"aw_order_sn":       orderSn,
					"member_id":         order.MemberId,
					"refund_amount":     int32(decimal.NewFromFloat(cast.ToFloat64(refund.RefundAmount) * 100).Round(0).IntPart()),
					"refund_apply_date": refund.CreateTime.Format(kit.DATETIME_LAYOUT),
					"refund_end_date":   refund.RefundedTime.Format(kit.DATETIME_LAYOUT),
					"refund_order_sn":   refund.RefundSn,
					"refund_remark":     refund.RefundReason,
					"finish_type":       finishType,
					"sign_id":           cast.ToInt64(signId),
				})
				code, body := utils.HttpPostToDigitalHealth("/nuclei-api/callback/service-pack/refund", req, "json")
				glog.Info("HttpPostToDigitalHealth /nuclei-api/callback/service-pack/refund 入参：", string(req), "，返回：", body)
				if code != 200 {
					return errors.New(body)
				}
				return nil
			}, []time.Duration{3 * time.Second, 1 * time.Minute, 3 * time.Minute})
		}

		return nil
	}()

	// 打折卡退卡
	go func() (err error) {
		defer func() {
			if err != nil {
				glog.Error("Card Refund 打折卡退卡出错 ", err.Error())
			}
		}()
		var cardNos []string
		if err = db.Table("datacenter.vip_user_equity_record").Alias("r").
			Join("inner", "datacenter.vip_card_equity e", "e.id = r.equity_id").
			Where("r.order_sn = ? and e.equity_type = 8 and r.gift_order_sn !=''", orderSn).
			Select("r.gift_order_sn").
			Find(&cardNos); err != nil {
			return err
		} else if len(cardNos) == 0 {
			return nil
		}

		if IsRetrunDiscount == "" {
			IsRetrunDiscount = time.Now().Format(kit.DATETIME_LAYOUT)
		}

		client := et.GetExternalClient()
		for _, no := range cardNos {
			par := &et.DiscountCardRefundReq{
				EnsureCode:   no,
				CustomerId:   order.MemberId,
				CreateSource: 5,
				EndTime:      IsRetrunDiscount,
			}
			glog.Info(refund.RefundSn+" 退打折卡参数:", par)
			rs, errDiscount := client.ZiLong.DiscountCardRefund(client.Ctx, par)
			if errDiscount != nil {
				glog.Error(refund.RefundSn+" 退打折卡报错:", errDiscount.Error())

			} else if rs.Code != 200 {
				errDiscount = errors.New(rs.Message)
				glog.Error(refund.RefundSn+" 退打折卡报错:", rs.Message)
			}

			glog.Info(refund.RefundSn+" 退打折卡返回结果:", rs, errDiscount)
			if errDiscount != nil {
				//加入重试操作里面
				check := CheckExist(refund.RefundSn+"|"+no, models.RedoTypeDiscountCardRefund)
				if check == false {
					redoTask := new(models.OrderRedoTask)
					redoTask.RedoType = models.RedoTypeDiscountCardRefund //重试类型 1：正向订单重推巨益OMS
					redoTask.OrderSn = refund.RefundSn + "|" + no
					redoTask.Params = kit.JsonEncode(order)
					redoTask.FailInfo = errDiscount.Error()
					//下次执行时间
					nextRedoDuration := utils.GetNextOrderRedoTimeDuration(0)
					redoTask.NextRedoTime = time.Now().Add(nextRedoDuration)
					errAdd := ReDoTaskAdd(redoTask)
					if errAdd != nil {
						glog.Error(refund.RefundSn, par, "-退打折扣失败", errAdd.Error())
					}
				}

			}

		}

		return nil
	}()

	// 门店券退卡
	go func() (err error) {
		defer func() {
			if err != nil {
				glog.Error("Card Refund 门店券退卡出错 ", err.Error())
			}
		}()

		errWaste := c.RetrunZiLongCouponCode(orderSn, refund.RefundSn)
		if errWaste != nil {
			//加入重试操作里面
			check := CheckExist(refund.RefundSn, models.RedoTypeWasteCoupon)
			if check == false {
				redoTask := new(models.OrderRedoTask)
				redoTask.RedoType = models.RedoTypeWasteCoupon //重试类型 1：正向订单重推巨益OMS
				redoTask.OrderSn = refund.RefundSn
				redoTask.Params = orderSn
				redoTask.FailInfo = errWaste.Error()
				//下次执行时间
				nextRedoDuration := utils.GetNextOrderRedoTimeDuration(0)
				redoTask.NextRedoTime = time.Now().Add(nextRedoDuration)
				errAdd := ReDoTaskAdd(redoTask)
				if errAdd != nil {
					glog.Error(refund.RefundSn, orderSn, "-退门店券插入定时处理失败", errAdd.Error())
				}
			}

		}

		return nil
	}()

	out.Code = 200
	return
}

// QuerySignId 查询签约id
func (c CardService) QuerySignId(ctx context.Context, in *oc.QuerySignIdReq) (out *oc.QuerySignIdRes, e error) {
	out = &oc.QuerySignIdRes{Code: 400}

	db := GetUPetDBConn()

	var signId string
	if has, err := db.Table("dc_order.order_main").Alias("om").Join("inner", "dc_order.order_detail od", "od.order_sn = om.order_sn").
		Where("om.order_sn = ? and om.member_id = ?", in.OrderSn, in.ScrmId).Select("od.consult_order_sn").Get(&signId); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "订单号无效"
		return
	} else if signId != "" {
		out.SignId = cast.ToInt32(signId)
	}

	if out.SignId != 0 {
		out.Code = 200
		return
	}

	// 没有签约id，重新生成

	order := new(models.OrderMain)
	// 父订单号
	if has, err := db.Where("parent_order_sn = ? and order_status >= 20 and order_type in (17,18) and member_id = ?", in.OrderSn, in.ScrmId).Get(order); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "订单未找到"
		return
	}

	op := new(models.OrderProduct)

	var packId int32
	if has, err := db.Where("order_sn = ?", in.OrderSn).Select("sku_id,term_value").Get(op); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "未找到订单关联商品"
		return
	}

	skuId := cast.ToInt32(op.SkuId)

	// 会员卡订单
	if order.OrderType == 17 {
		template := new(models.VipCardTemplate)
		if has, err := db.Where("id = ?", skuId).Get(template); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = fmt.Sprintf("卡模板 %d 未找到", skuId)
			return
		}

		if _, err := db.Table("datacenter.vip_card_equity_config").Alias("c").
			Join("inner", "datacenter.vip_card_equity e", "e.id = c.equity_id").
			Where("c.card_tid = ? and e.equity_type = 6 and c.status = 1", skuId).
			Select("c.privilege_ids").Get(&packId); err != nil {
			out.Message = err.Error()
			return
		} else if packId == 0 {
			out.Message = "未找到家庭医生服务包权益配置"
			return
		}

	} else if order.OrderType == 18 { // 服务包订单
		packId = skuId
	}

	packBuyWay := 3
	if order.OrderType == 17 {
		packBuyWay = 2
	}
	if res, err := c.ServicePackCreate(ctx, &oc.CardServicePackCreateReq{
		AwOrderSn:          order.ParentOrderSn,
		MemberId:           order.MemberId,
		MemberMobile:       utils.MobileDecrypt(order.EnMemberTel),
		MemberName:         order.MemberName,
		OrderSource:        order.ChannelId,
		OrderSourceChannel: order.UserAgent,
		OriginalPrice:      order.PayTotal,
		PackBuyWay:         int32(packBuyWay),
		PayPrice:           order.PayTotal,
		SkuId:              packId,
	}); err != nil {
		out.Message = err.Error()
		return
	} else {
		if res.Code != 200 {
			out.Message = res.Message
			return
		}

		out.SignId = res.Data.SignId
		if _, err = db.Table("dc_order.order_detail").In("order_sn", []string{order.OrderSn, order.ParentOrderSn}).Update(models.OrderDetail{
			ConsultOrderSn: cast.ToString(res.Data.SignId),
		}); err != nil {
			out.Message = err.Error()
			return
		}
	}
	out.Code = 200
	return
}

// CheckCardId 检测卡id是否有效
func (c CardService) CheckCardId(ctx context.Context, in *oc.CheckCardIdReq) (out *oc.CheckCardIdRes, e error) {
	out = &oc.CheckCardIdRes{Code: 400}

	db := GetDcDBConn()

	var name string
	if has, err := db.Table("vip_card_template").Where("id = ?", in.CardId).Select("card_name").Get(&name); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Data = &oc.CheckCardIdRes_Data{
			Result: has,
			Name:   name,
		}
	}

	out.Code = 200
	return
}

// Refund 退卡审核
func (c CardService) RefundExamine(ctx context.Context, in *oc.RefundExamineReq) (out *oc.CardBaseResponse, e error) {
	out = &oc.CardBaseResponse{Code: 400}
	//先调用电商的接口，再处理自己的逻辑，电商成功我们才成功
	glog.Info("退卡审核参数:", in)
	par := dto.RefundExamineReq{}
	par.AdminState = int(in.State)
	par.RefundAmount = int(in.RefundAmount)
	par.RefundID = int(in.RefundId)
	par.AdminType = int(in.AdminType)
	par.AdminMessage = in.Reason
	par.AdminUser = in.UserName

	data := kit.JsonEncode(par)

	errStr := ReqstUpet(data, "vrOrderRefund")
	if errStr != "1" {
		out.Message = errStr
		return
	}

	out.Code = 200
	return
}

////判断自动审核 作废
//func (c CardService) RefundAuto(ctx context.Context, in *oc.RefundExamineReq) (out *oc.CardBaseResponse, e error) {
//	out = &oc.CardBaseResponse{Code: 400}
//	//先调用电商的接口，再处理自己的逻辑，电商成功我们才成功
//	glog.Info("自动审核参数:", in)
//	//调用逻辑判断是否有使用权益，并且是否7天以内。是的话就去调用退款审核通过接口
//
//	out.Code = 200
//	return
//}

// 重试执行
func retryDo(f func() error, ts []time.Duration) (err error) {
	if err = f(); err != nil {
		for _, t := range ts {
			time.Sleep(t)
			if err = f(); err != nil {
				continue
			} else {
				break
			}
		}
	}
	return
}

// 请求电商接口
func ReqstUpet(data string, op string) string {

	//data := kit.JsonEncode(in)
	stringA := kit.GetMd5("data=" + data)
	signsignValue := strings.ToUpper(kit.GetMd5(stringA + config.GetString("express_key")))

	dsparam := make(map[string]interface{})
	dsparam["data"] = data
	dsparam["sign"] = signsignValue
	dsurl := config.GetString("mall_api") + "/mobile/index.php?act=openapi&op=" + op
	code, body := utils.HttpPostFormToMall(dsurl, "", dsparam)
	glog.Info(fmt.Sprintf("调用电商接口(%s)返回结果：code:%s,返回内容:%s,接口参数:%s", dsurl, strconv.Itoa(code), body, data))

	if code != 200 {
		glog.Error("请求电商出错 ："+op, data)
		return "推送电商出错"
	}

	dsRes := new(dto.MallBaseResponse)
	err := json.Unmarshal([]byte(body), dsRes)
	if err != nil {
		glog.Error(op, data, ", 解析电商接口返回失败, ", err, ", ", body)
		return err.Error()
	} else if dsRes.Code != 200 {

		errorStr := new(dto.MallBaseResponse1)
		json.Unmarshal([]byte(body), errorStr)
		glog.Error(op, data, ", 订单同步电商出错, ", errorStr.Datas.Error)
		return errorStr.Datas.Error
	}

	return "1"
}

// 退子龙门店券
func (c CardService) RetrunZiLongCouponCode(orderSn string, RefundSn string) error {
	var cardNos []string
	db := GetUPetDBConn()
	if err := db.Table("datacenter.vip_user_equity_record").Alias("r").
		Join("inner", "datacenter.vip_card_equity e", "e.id = r.equity_id").
		Where("r.order_sn = ? and e.equity_type = 2 and r.gift_order_sn !=''", orderSn).
		Select("r.gift_order_sn").
		Find(&cardNos); err != nil {
		return err
	} else if len(cardNos) == 0 {
		return nil
	}

	client := et.GetExternalClient()
	par := et.WasteCouponReq{}
	par.OperatorId = "admin"
	par.Operator = "admin"
	var couponCodes = make([]string, 0)
	for _, no := range cardNos {
		couponCodes = append(couponCodes, no)
	}

	resp, err := client.ZiLong.GetVerifyDetail(client.Ctx, &et.GetVerifyDetailReq{
		CouponCodes: couponCodes,
	})
	if err != nil || len(resp.Data) == 0 {
		glog.Error(RefundSn+" 请求子龙接口，退卡获取门店券使用记录失败或没有记录", resp)
		return errors.New("请求子龙接口，退卡获取门店券使用记录失败或没有记录")
	} else {
		for _, data := range resp.Data {
			if data.Status == "1" {
				par.CouponCodes = append(par.CouponCodes, data.CouponCode)
			}
		}
		if len(par.CouponCodes) == 0 {
			return nil
		}

		glog.Info(RefundSn+" 退门店券参数:", par)
		rs, errWaste := client.ZiLong.WasteCoupon(context.Background(), &par)
		glog.Info(RefundSn+" 退门店券返回结果:", rs, errWaste)
		if errWaste != nil {
			glog.Error(RefundSn+" 退门店券报错:", errWaste.Error())
			return errWaste
		} else if rs.Code != 200 {
			glog.Error(RefundSn+" 退门店券报错:", rs.Message)
			errWaste = errors.New(rs.Message)
			return errWaste
		}

	}
	return nil
}

package models

import "time"

type TTenantPercentageProduct struct {
	Id           int64     `json:"id" xorm:"pk autoincr not null comment('流水id') BIGINT 'id'"`
	TenantId     int64     `json:"tenant_id" xorm:"not null comment('店铺id') BIGINT 'tenant_id'"`
	PercentageId int64     `json:"percentage_id" xorm:"not null comment('提成id') BIGINT 'percentage_id'"`
	ProductType  string    `json:"product_type" xorm:"default 'null' comment('提成类型（产品类型）') VARCHAR(255) 'product_type'"`
	ProductId    int64     `json:"product_id" xorm:"not null comment('员工编码') BIGINT 'product_id'"`
	CreatedBy    int64     `json:"created_by" xorm:"not null comment('创建人') BIGINT 'created_by'"`
	CreatedTime  time.Time `json:"created_time" xorm:"not null comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy    int64     `json:"updated_by" xorm:"default 'null' comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime  time.Time `json:"updated_time" xorm:"default 'null' comment('更新时间') DATETIME 'updated_time'"`
}

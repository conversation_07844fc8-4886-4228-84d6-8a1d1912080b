package models

import (
	"time"
)

type MemberPropertyGuaranteeQuota struct {
	Id                         int32     `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	MemberId                   string    `xorm:"default 'NULL' comment('用户的scrm_id') VARCHAR(100)"`
	InsurancePolicyNumber      string    `xorm:"default 'NULL' comment('保单号、会员卡号') VARCHAR(100)"`
	QuotaType                  int32     `xorm:"default NULL comment('账户类型(1-医疗账户,2-服务账户)') INT(11)"`
	InsuranceUnusedQuota       int32     `xorm:"default NULL comment('医保可用额度；单位：分') INT(11)"`
	InsuranceUsedQuota         int32     `xorm:"default NULL comment('医保已用额度；单位：分') INT(11)"`
	InsuranceUnusedQuotaExpire time.Time `xorm:"default 'NULL' comment('医保可用额度最大过期时间') DATETIME"`
	InsuranceAccumulateQuota   int32     `xorm:"default NULL comment('累计医保下发额度；单位：分') INT(11)"`
	ServiceUnusedQuota         int32     `xorm:"default NULL comment('服务可用额度，单位：分') INT(11)"`
	ServiceUsedQuota           int32     `xorm:"default NULL comment('服务已用额度；单位：分') INT(11)"`
	ServiceFreezeQuota         int32     `xorm:"default NULL comment('服务冻结额度；单位：分') INT(11)"`
	ServiceUnusedQuotaExpire   time.Time `xorm:"default 'NULL' comment('服务可用额度最大过期时间') DATETIME"`
	ServiceAccumulateQuota     int32     `xorm:"default NULL comment('累计服务下发额度；单位：分') INT(11)"`
	CreateTime                 time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME"`
	LastUpdateTime             time.Time `xorm:"default 'NULL' comment('最后更新时间') DATETIME"`
	VirtualCardId              int64     `xorm:"not null default 0 comment('卡号') BIGINT(20)"`
}

func (v MemberPropertyGuaranteeQuota) TableName() string {
	return "datacenter.member_property_guarantee_quota"
}

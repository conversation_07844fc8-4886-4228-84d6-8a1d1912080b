syntax = "proto3";
package et;
import "google/protobuf/empty.proto";

// 微盟商城相关接口
service WeimobCloudService {
  //推送库存
  rpc PushStock(PushStockRequest) returns (google.protobuf.Empty);
  //分页查询仓库列表
  rpc WarehouseListPage(WarehouseListPageRequest)
      returns (WarehouseListPageResponse);
  //获取订单详情
  rpc GetWeiMengOrderDetail (WeiMengOrderDetailRequest) returns (WeiMengOrderDetailResponse);
  //获取退款单详情
  rpc GetWeiMengRefundDetail (WeiMengRefundDetailRequest) returns (WeiMengRefundDetailResponse);
  //订单物流信息更新
  rpc  WeiMengOrderLogisticsUpdate(WeiMengOrderLogisticsUpdateRequest) returns (CommonResponse);


  // 查询商品列表接口
  rpc WeiMShopGoodsGetList(WeiMShopGoodsGetListDto) returns (WMCommonStrResponse);
  // 商品创建接口
  rpc WeiMShopGoodsCreate(WmShopGoodSCreateDto) returns (WmShopGoodSCreateResponse);

  // 查询微盟商品在售详情
  rpc WeiMShopGoodsGet(WMShopGoodsGetDto) returns (WMCommonStrResponse);

}

message WarehouseListPageResponse {
  repeated SimpleWarehouse List = 1;
  // 每页包含的数据条数
  int32 PageSize = 2;
  // 分页页码
  int32 PageNum = 3;
  // 查询返回的总数据条数
  int64 TotalCount = 4;
}

message WarehouseListPageRequest {
  // 每页包含的数据条数
  int32 PageSize = 1;
  // 分页页码
  int32 PageNum = 2;
  // 仓库类型,0:全部；1-商家仓；2-门店仓；3-外部仓
  int32 WarehouseType = 3;
  // 搜索类型,1-根据仓库名称搜索；2-根据仓库编号搜索
  int32 SearchType = 4;
  // 搜索关键字
  string Keyword = 5;
  // 微盟组织ID
  int64 OrgId = 6;
}

message SimpleWarehouse {
  // 配送方式，1-按订单销售门店的物流设置；2-仅商家配送
  int32 DeliveryType = 1;
  string WarehouseName = 2;
  string WarehouseCode = 3;
  int32 WarehouseId = 4;
  // 仓库子配送类型，1-商家配送；2-同城限时达；3-到店自提。当 deliveryType=2
  // 时，根据当前字段进行判断
  string SubDeliveryTypeList = 5;
  string Address = 6;
  string ManagerName = 7;
  string Phone = 8;
  string Remark = 9;
}

message PushStockRequest {
  // 微盟仓库编码
  string WarehouseCode = 1;
  // 微盟仓库ID
  int32 WarehouseId = 2;
  repeated SimpleProduct List = 3;
  // 库存变更类别，0-覆盖更新；1-增量更新
  int32 AlterType = 4;
  // 微盟组织ID
  int64 OrgId = 5;
}

message SimpleProduct {
  int64 ProductId = 1;
  repeated SimpleSku List = 2;
}

message SimpleSku {
  // 微盟skuId
  int64 SkuId = 1;
  // 库存数量
  int32 Stock = 2;
}


message CommonResponse {
  int32 code = 1;
  string error = 2;
  string message = 3;
}
message  WeiMengOrderLogisticsUpdateRequest {
  //订单编号。可以通过 weimob_shop/order/list/search 接口获取该 ID。
  int64 order_no = 1;
  //是否拆包发货。该字段为 true 时，fulfillItems 必填，此时会拆包发货。
  bool is_split_package = 2;
  //履约细分类型，订单配送类型为商家配送 1 对应：快递物流 1、无需物流 2。当履约细分类型为快递物流时，logistics 物流信息必填。
  int32 fulfill_method = 3;
  //发货单号
  string delivery_no = 4;
  //发货公司 code，详情可参见快递公司编码列表
  string delivery_company_code = 5;
  //发货公司名称
  string delivery_company_name = 6;
  //发货门店组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
  int64 vid = 7;
  //发货门店类型，同商家枚举定义。[1-集团，2-品牌，3-区域，5-商场，10-门店，100-自提点
  int32 vid_type = 8;

}

message WeiMengOrderDetailRequest {
  //订单查询范围。支持的范围类型包括：1-订单信息；2-履约信息；3-售后信息。不传则只展示订单信息，不展示订单项列表。
  repeated int32 order_domains = 1;
  //订单号
  int64 order_no = 2;
}

message WeiMengOrderDetailResponse {
  //返回码 200成功 400失败
  int32 code = 1;
  //订单详情数据
  bytes data = 2;
  string message = 3;
}

message WeiMengRefundDetailRequest {
  //退款单号
  int64 rightsId = 1;
}

message WeiMengRefundDetailResponse {
  //返回码 200成功 400失败
  int32 code = 1;
  //订单详情数据
  bytes data = 2;
  string message = 3;
}



// 微盟创建商品接口
message WmShopGoodSCreateDto{
  //    基础信息
  BasicInfo  basicInfo = 1;
  //    商品品牌 ID
  int64 brandId = 2;
  //    商品类目 ID
  int64 categoryId = 3;

  int64 deductStockType = 4;

  string defaultImageUrl = 5;
  //    商品分组 ID
  repeated int64 goodsClassifyIdList = 6;
  //    商品描述吧啦吧啦
  string goodsDesc = 7;
  //    商品图片
  repeated string goodsImageUrl = 8;

  // 商品模板
  int64 goodsTemplateId = 9;
  //    商品类型。类型包括：1-普通商品；2-虚拟商品。
  int64 goodsType = 10;
  //    商品视频图片链接
  string goodsVideoImageUrl = 11;

  //    商品视频链接
  string goodsVideoUrl  = 12 ;
  //    初始销量
  int64 initSales = 13;
  //    发票名称
  string invoiceTitle = 14;

  //    商品开票名称类型。类型包括：1-同商品名称； 2-自定义名称。
  int64 invoiceTitleType =15;
  //    商品是否可售。false-禁售；true-可售。
  bool isCanSell = 16;

  //    商品是否多规格。 false-单规格；true-多规格。
  bool isMultiSku = 17;
  bool isOnline = 18;
  //    限购开关。false-关闭； true-开启。
  bool limitSwitch = 19;
  // 商品编码
  string outerGoodsCode = 20;
  //    履约方式
  PerformanceWay performanceWay = 21;
  int64 sellUnitId = 22;
  //    商品规格 SKU 列表
  repeated SkuList skuList = 23;

  //    商品子类型。类型包括：101-普通商品；102-海淘商品；103-无需物流实物商品；201-普通虚拟商品；202-付费卷虚拟商品。
  int64 subGoodsType = 24;
  //    商品子标题
  string subTitle = 25;

  //    商品标签 ID，可以通过 weimob_shop/goods/tag/getList 接口获取该 ID。
  int64 tagId = 26;
  //    商品名称
  string title = 27;
  int64 wid = 28; // 商户 ID
  //    交付方式。0-单次交付；1-多次交付（只支持线上且现货）。周期购商品必填。
  int64 goodsDeliveryMode = 29;
  //    积分抵扣规则设置。
  PointDeductRule  pointDeductRule = 30;

}

message BasicInfo {
  int64 vid = 1;
}

//    履约方式
message PerformanceWay {
  repeated DeliveryList deliveryList = 1;

}
//    配送方式。配送方式与商品类型关联，普通实物商品：只能设置商家配送、同城限时达、到底自提。 虚拟商品：只能设置无需物流配送方式。
message  DeliveryList {
  //    配送方式 ID。可以通过 weimob_shop/fulfill/goods/fulfilltype/getList 接口获取该 ID。
  int64 deliveryId = 1;
  //    配送方式关系节点 ID。可以通过 weimob_shop/fulfill/goods/fulfilltype/getList 接口获取该 ID。
  int64 deliveryNodeShipId = 2;

  //    配送类型。类型包括：1-商家配送；2-同城限时达；3-到店自提；4-门店交易；5-无需物流；6-门店自助。
  int64 deliveryType = 3;
  //    配送模板 ID
  int64 templateId = 4;
}

message SkuList {
  //    图片 URL
  string imageUrl = 1;
  int64 costPrice = 2; // 成本价
  //    SKU 库存数量
  int64 skuStockNum = 3;
  //marketPrice 市场价
  int64 marketPrice = 4;
  //    销售价
  int64 salePrice = 5;
  //    体积
  int64 volume = 6;
  //    重量
  int64 weight = 7;
  //    商家编码
  string outerSkuCode = 8;

  repeated SkuSpecValueList skuSpecValueList = 9;


}
//    SKU 规格值列表
message SkuSpecValueList {
  int64 specId = 1; // 规格 ID
  int64 specValueId = 2; // 规格值 ID
}

//    积分抵扣规则设置。
message PointDeductRule {

  //    是否开启积分抵扣。0-不开启；1-开启。
  int64 openPointDeduct = 1;
  //    使用条件。0：不限；1：应收金额/销售价 >= useDiscount 折可用
  int64 useCondition = 2;
  //    使用折扣
  int64 useDiscount = 3;

  //    积分抵扣额度。0：不限；1：应收金额减抵扣额度/销售价 <= deductDiscount折；2：应收金额减抵扣额度/应收金额 <= deductDiscount折。
  int64 deductAmount = 4;
  //    抵扣折扣
  int64 deductDiscount = 5;

  //    积分抵扣显示类型：1-按比例显示；2-按金额显示。
  int64 deductionType = 6;

}


message WmShopGoodSCreateResponse {
  WMCode code = 1;
  WMData data =3;
}

message WMCode {
  string errcode = 1;
  string errmsg = 2;
}

// 业务返回数据
message WMData {
  repeated WMSkuList skuList = 1;
  int64 goodsId = 2; // 商品 ID
}
message  WMSkuList{
  string outerSkuCode = 1; // 规格编码
  int64 skuId = 2; // 商品 SKU ID
}

message WMQueryUploadImgDto {
  //    图片名称，长度不能大于50个字符
  string name = 1;

  //    类型 File 图片文件（支持的文件类型：gif,jpg,jpeg,png）
  string file = 2;
}

message WMQueryUploadImgResponse{
  WMUrlData data =1;
  WMCode code = 2;
}

message WMUrlData {
  int64 size = 1;
  WMUrlInfo  urlInfo = 2;

}

message WMUrlInfo {
  string name = 1;
  string url =2;
  int64 legalStatus = 3;

}


message WMQueryCategoryTreeResponse {
  WMCode code = 1;
  repeated WMCategoryList data = 2;
}

message  WMCategoryList {
  int64 categoryId = 1;
  string title = 2;
}

message WMQueryChildrenCategoryDto {
  int64 categoryId = 1;
}

message WMQueryChildrenCategoryResponse {
  WMCode code =1;
  repeated WMCategoryList categoryList = 2;
  int64 categoryId = 3;
  string title = 4;
}

message WMFindFreightTemplateListDto {
  //  非必填  商品id，如果传入，额外返回该商品使用的模板
  int64 goodsId = 1;
}


// 查询在售商品详情
message WMShopGoodsGetDto {
  int64   vid  = 1;
  int64 goodsId = 2;
}


//    查询商品列表
message WeiMShopGoodsGetListDto {
  //    分页页码
  int64 pageNum =1;

  //    每页包含的数据条数
  int64 pageSize =2;
  //    查询条件
  QueryParameter  queryParameter =3;
  //    基础信息
  BasicInfo basicInfo = 4;

}

//    查询条件
message QueryParameter {
  //    商品状态。状态包括：0-上架；1-下架；2-已售罄。
  int64 goodsStatus =1;
  //    商品分组 ID，传入二级分组 ID 可以通过 weimob_shop/goods/classify/getList 接口获取该 ID。
  int64 classifyId =2;

  //    查询商品的搜索内容，支持商品名称搜索。
  string search = 3;
  //    排序方式，取值范围[1,4]。1-商品销量销量 2-上下架时间 3-商品价格 4-商品排序值。
  int64 sort = 4;

}


// 微盟所有的字符串返回
message WMCommonStrResponse {
  //    http的code
  int64 httpCode = 1;
  // 返回的错误信息
  string error =2;

  // 请求的数据
  string data = 3;
}
package models

import "time"

type OrderMainGroup struct {
	Id                   int32     `xorm:"not null pk autoincr INT(11)"`
	OrderSn              string    `xorm:"not null default '' comment('子订单号') VARCHAR(50)"`
	ParentOrderSn        string    `xorm:"not null default '' comment('父订单号') unique VARCHAR(50)"`
	OrderGroupActivityId int32     `xorm:"not null default 0 comment('团id') index INT(11)"`
	PayAmount            int32     `xorm:"not null default 0 comment('已支付金额') INT(11)"`
	MemberId             string    `xorm:"not null default '' comment('团成员id') VARCHAR(50)"`
	MemberName           string    `xorm:"not null default '' comment('团员昵称') VARCHAR(50)"`
	MemberProfile        string    `xorm:"not null default '' comment('团成员头像') VARCHAR(120)"`
	ReceiverName         string    `xorm:"not null default '' comment('团长代收时团员的联系姓名') VARCHAR(15)"`
	ReceiverMobile       string    `xorm:"not null default '' comment('团长代收时团员的联系手机') VARCHAR(15)"`
	EnReceiverMobile     string    `xorm:"not null default '' comment('团长代收时团员的联系手机') VARCHAR(20)"`
	ReceiverAddress      string    `xorm:"not null default '' comment('团长代收时团员的地址') VARCHAR(15)"`
	IsSubscribe          int32     `xorm:"not null default 0 comment('是否订阅消息 0否1是') INT(11)"`
	CreatedAt            time.Time `xorm:"DATETIME"`
	UpdateAt             time.Time `xorm:"DATETIME"`
}

type OrderMainGroupOrders struct {
	OrderId int32  `json:"order_id"`
	OrderSn string `json:"order_sn"`
}

type OrderMainGroupDetailsList struct {
	MemberName    string    `json:"member_name"`
	MemberProfile string    `json:"member_profile"`
	PayAmount     int32     `json:"pay_amount"`
	CreatedAt     time.Time `json:"created_at"`
}

type OrderMainGroupExt struct {
	OrderMainGroup `xorm:"extends"`
	RefundState    int32 `json:"refund_state"`
	OrderStatus    int32 `json:"order_status"`
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/activity_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("ac/activity_service.proto", fileDescriptor_65b82874339967b5) }

var fileDescriptor_65b82874339967b5 = []byte{
	// 790 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x56, 0xdd, 0x4f, 0xdb, 0x3e,
	0x14, 0x95, 0x7e, 0x0f, 0x3f, 0x69, 0x1e, 0x8c, 0xd6, 0x2d, 0x5f, 0x65, 0x3c, 0x8c, 0xed, 0x19,
	0x24, 0xa6, 0x7d, 0xb0, 0x31, 0xa4, 0x52, 0x58, 0x41, 0xa2, 0x03, 0x91, 0x4e, 0x4c, 0xda, 0xc3,
	0xe4, 0x26, 0x77, 0x6b, 0xd4, 0xe6, 0x03, 0x5f, 0x07, 0xb6, 0x7f, 0x75, 0x7f, 0xcd, 0x64, 0x3b,
	0x71, 0xe2, 0xc4, 0xed, 0xa3, 0xcf, 0x3d, 0xe7, 0xdc, 0xe3, 0x6b, 0xc7, 0x2d, 0xd9, 0x66, 0xfe,
	0x01, 0xf3, 0x45, 0xf8, 0x10, 0x8a, 0x3f, 0x3f, 0x10, 0xf8, 0x43, 0xe8, 0xc3, 0x7e, 0xca, 0x13,
	0x91, 0xd0, 0xff, 0x98, 0xdf, 0xdb, 0xac, 0x96, 0xa3, 0x24, 0x80, 0xb9, 0x2e, 0x1e, 0xfe, 0xed,
	0x90, 0xb5, 0x7e, 0x5e, 0xf0, 0xb4, 0x8c, 0x1e, 0x91, 0x95, 0xeb, 0x14, 0xe2, 0x31, 0x07, 0x86,
	0x19, 0x07, 0xda, 0xd9, 0x67, 0xfe, 0x7e, 0x0a, 0x62, 0x0c, 0x0c, 0xe1, 0x16, 0xee, 0x33, 0x40,
	0xd1, 0xeb, 0x4a, 0xf0, 0xc6, 0x80, 0x98, 0x26, 0x31, 0x02, 0x3d, 0x26, 0x6b, 0x43, 0x10, 0x05,
	0x7c, 0x15, 0xa2, 0xa0, 0x6b, 0x92, 0x38, 0xa9, 0x28, 0xb7, 0xaa, 0x76, 0x92, 0x62, 0xd4, 0x87,
	0x84, 0x78, 0x10, 0x07, 0x83, 0x24, 0x4b, 0x93, 0x98, 0xb6, 0x25, 0xaf, 0x5c, 0xdf, 0xc2, 0x7d,
	0xaf, 0x01, 0x21, 0xfd, 0x40, 0x9e, 0x0d, 0x41, 0x9c, 0xff, 0x66, 0x51, 0x18, 0x2f, 0x68, 0xb8,
	0x29, 0x01, 0x28, 0x19, 0xa6, 0xdf, 0x01, 0x79, 0xe2, 0xb1, 0x07, 0xf0, 0xa6, 0x8c, 0x03, 0x6d,
	0x29, 0xef, 0x62, 0x29, 0xbb, 0xb5, 0x4a, 0xa3, 0x5c, 0x30, 0x24, 0x6d, 0xc9, 0xc8, 0xbb, 0xf5,
	0x63, 0x7c, 0x04, 0x4e, 0xb7, 0x0a, 0xa1, 0x05, 0x4b, 0x83, 0x45, 0x15, 0xa4, 0x27, 0x2a, 0xf5,
	0x08, 0xa2, 0x09, 0x70, 0xdd, 0x7e, 0x5d, 0x72, 0x6d, 0x4c, 0x5a, 0x38, 0x61, 0x94, 0x41, 0x86,
	0x20, 0xf4, 0x14, 0xc6, 0x10, 0xa5, 0x73, 0x26, 0x40, 0x07, 0x69, 0xc0, 0x26, 0x88, 0xa3, 0x82,
	0xf4, 0x13, 0x69, 0xc9, 0x80, 0xda, 0x3e, 0x1f, 0xfc, 0x66, 0x11, 0xbb, 0x8a, 0xba, 0x07, 0xd2,
	0x57, 0xe7, 0x6d, 0xa9, 0x37, 0xac, 0xc4, 0xa5, 0xd8, 0x8d, 0x23, 0x3d, 0x27, 0x5d, 0x35, 0xf5,
	0x6c, 0x82, 0x3e, 0x0f, 0x27, 0x30, 0x02, 0x44, 0xf6, 0x0b, 0xe8, 0x8e, 0x39, 0x8f, 0x5a, 0xc5,
	0x9d, 0xe4, 0x92, 0x6c, 0x0c, 0x58, 0xec, 0xc3, 0xbc, 0x61, 0xb4, 0x2b, 0xb9, 0xee, 0x9a, 0xdb,
	0xea, 0x88, 0xb4, 0xcc, 0xac, 0x0a, 0x93, 0xc6, 0xa5, 0x52, 0xf7, 0xdf, 0xe2, 0xe8, 0xdb, 0x28,
	0xe7, 0x31, 0x60, 0x62, 0x94, 0xc4, 0x62, 0x7a, 0x19, 0xff, 0x4c, 0x9a, 0xca, 0x62, 0x10, 0x55,
	0x56, 0xe5, 0x28, 0x2c, 0xb1, 0x39, 0x0a, 0x9b, 0xec, 0x4a, 0xfd, 0x91, 0xac, 0x4a, 0xe2, 0x1d,
	0x13, 0xc0, 0x23, 0xc6, 0x67, 0xb4, 0x5b, 0x68, 0x0d, 0x24, 0x85, 0x2e, 0x14, 0xa5, 0xd8, 0xac,
	0xd5, 0x47, 0xa4, 0x68, 0x16, 0x64, 0xc4, 0x35, 0x14, 0xe9, 0x7b, 0xb2, 0x32, 0x04, 0x51, 0x36,
	0xee, 0xe4, 0x1b, 0xb4, 0xfa, 0x3a, 0x40, 0xa9, 0xa4, 0x66, 0x3d, 0x4c, 0x92, 0x00, 0x55, 0x6f,
	0xb5, 0x37, 0xb3, 0x34, 0xbb, 0xad, 0x20, 0x48, 0x8f, 0x49, 0xd7, 0x56, 0x9e, 0xc1, 0x1c, 0x04,
	0x50, 0x6a, 0x98, 0x1a, 0x70, 0xcf, 0xea, 0x2d, 0x69, 0xdb, 0xea, 0x7e, 0x10, 0xe8, 0x83, 0x2a,
	0x56, 0x6e, 0xdd, 0x1b, 0xf2, 0xd4, 0x03, 0xc6, 0xfd, 0xa9, 0xa2, 0xe9, 0x66, 0x15, 0x40, 0x8a,
	0x9a, 0x98, 0xbc, 0x15, 0x1d, 0xbb, 0xdd, 0x0d, 0x97, 0xef, 0x6c, 0xdb, 0x34, 0x54, 0x6b, 0x77,
	0xcb, 0x77, 0x64, 0xd5, 0x13, 0x49, 0x5a, 0x3f, 0xd6, 0x2a, 0xe4, 0x16, 0x9e, 0xd4, 0x27, 0x74,
	0x19, 0xa5, 0x09, 0x17, 0x95, 0x09, 0x69, 0xc0, 0x84, 0xb6, 0x30, 0xa4, 0x9f, 0x49, 0x67, 0x30,
	0xe5, 0x21, 0x8a, 0x88, 0xc9, 0x81, 0xf4, 0x83, 0x80, 0x03, 0x22, 0xed, 0xa9, 0x7b, 0xdf, 0x2c,
	0x98, 0x33, 0x3e, 0xad, 0x84, 0xf8, 0x02, 0x8f, 0xf4, 0x94, 0xb4, 0x07, 0x53, 0x16, 0xc7, 0x30,
	0xf7, 0x04, 0x13, 0x21, 0x8a, 0xd0, 0x47, 0xfd, 0x54, 0x35, 0xe0, 0x85, 0x1e, 0x27, 0x64, 0xf5,
	0x82, 0xc5, 0xc1, 0x75, 0x96, 0x7f, 0x95, 0x5a, 0x6f, 0x41, 0xc5, 0xd7, 0xe5, 0xd4, 0x5f, 0x90,
	0xb6, 0x45, 0x3e, 0x63, 0x82, 0x2d, 0xf1, 0xd8, 0x76, 0x54, 0xf2, 0xa9, 0x7e, 0x27, 0x1b, 0xfa,
	0x07, 0x6e, 0xc4, 0xf8, 0x0c, 0xc4, 0x57, 0x34, 0xef, 0xde, 0x6e, 0x7e, 0xc1, 0x1d, 0x35, 0xb9,
	0xaf, 0xbd, 0x65, 0xe5, 0xdc, 0xfc, 0x4a, 0xbd, 0xea, 0x86, 0x31, 0x66, 0x38, 0x43, 0xf3, 0xaa,
	0xdb, 0xb0, 0xb4, 0xdc, 0x5d, 0x50, 0xc9, 0xdd, 0xce, 0x49, 0xb7, 0x1f, 0x04, 0x56, 0x3f, 0x49,
	0xd0, 0x0f, 0xab, 0xab, 0xe2, 0xbe, 0x47, 0xdf, 0xc8, 0x7a, 0x3d, 0xb6, 0x0e, 0xf6, 0xdc, 0xb5,
	0x23, 0x13, 0xee, 0xc5, 0x92, 0xaa, 0xf9, 0xf1, 0xa0, 0xd5, 0x18, 0xb7, 0xf0, 0xc8, 0x78, 0x40,
	0xb7, 0xeb, 0xf1, 0x34, 0xee, 0x0e, 0xe7, 0x38, 0x8e, 0xdc, 0xc6, 0x79, 0x1c, 0xa5, 0xd5, 0xde,
	0xb2, 0xb2, 0xd9, 0x79, 0x77, 0x90, 0x64, 0xb1, 0xa8, 0x27, 0xdc, 0xc9, 0x9f, 0xfe, 0x46, 0x45,
	0x1a, 0xbf, 0x5c, 0x52, 0x34, 0xce, 0x40, 0xb6, 0x14, 0x45, 0x36, 0xbd, 0xe6, 0x01, 0xf0, 0xbb,
	0x50, 0x4c, 0xbd, 0x59, 0x16, 0x06, 0x48, 0x4b, 0x03, 0x47, 0xb5, 0xb8, 0xa1, 0xaf, 0x96, 0x93,
	0x74, 0x9b, 0xc9, 0xff, 0xea, 0x3f, 0xde, 0xeb, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xbd, 0x93,
	0xdf, 0xfd, 0x1d, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ActivityServiceClient is the client API for ActivityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ActivityServiceClient interface {
	//打开宝箱
	OpenTreasure(ctx context.Context, in *PetTeaseRequest, opts ...grpc.CallOption) (*PetTeaseResponse, error)
	//获取奖励列表
	GetPetTeaseList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*PetTeaseListResponse, error)
	//调用电商发送优惠卷
	SendCoupon(ctx context.Context, in *SendCouponReq, opts ...grpc.CallOption) (*SendCouponRes, error)
	//520爱猫日-获取题目列表
	GetExamineList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*ExamineListResponse, error)
	//保存助力分享信息
	SaveShare(ctx context.Context, in *SaveShareReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//520爱猫日-提交答案
	SaveExamineAnswer(ctx context.Context, in *SaveExamineAnswerReq, opts ...grpc.CallOption) (*SaveExamineAnswerRes, error)
	//获取用户520爱猫日所有相关信息
	GetMemberShare(ctx context.Context, in *GetMemberShareReq, opts ...grpc.CallOption) (*GetMemberShareRes, error)
	//获取优惠卷模板列表
	GetCouponTemplate(ctx context.Context, in *GetCouponTemplateReq, opts ...grpc.CallOption) (*GetCouponTemplateRes, error)
	//保存门店优惠卷
	SaveMemberCoupon(ctx context.Context, in *SaveMemberCouponReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取门店优惠卷信息
	GetMemberCoupon(ctx context.Context, in *GetMemberCouponReq, opts ...grpc.CallOption) (*GetMemberCouponRes, error)
	//保存订阅消息
	SaveSubscribeMessage(ctx context.Context, in *SaveSubscribeMessageReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//取消订阅消息
	CancelSubscribeMessage(ctx context.Context, in *CancelSubscribeMessageReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取优惠券订阅消息
	GetCouponMessage(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*CouponMessageRes, error)
	//获取爱猫月活动信息
	GetCatMonthInfo(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*GetCatMonthInfoRes, error)
	//保存爱猫月活动信息
	SaveCatMonthInfo(ctx context.Context, in *SaveCatMonthInfoReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//添加或编辑水印
	SaveWatermark(ctx context.Context, in *SaveWatermarkReq, opts ...grpc.CallOption) (*SaveWatermarkRes, error)
	//获取水印列表
	WatermarkList(ctx context.Context, in *WatermarkListReq, opts ...grpc.CallOption) (*WatermarkListRes, error)
	//获取水印详情
	GetWatermark(ctx context.Context, in *GetWatermarkReq, opts ...grpc.CallOption) (*GetWatermarkRes, error)
	//获取水印关联的商品列表
	WatermarkGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListRes, error)
	//删除水印商品
	WatermarkGoodsDelete(ctx context.Context, in *GoodsDeleteReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//添加水印商品
	WatermarkGoodsAdd(ctx context.Context, in *GoodsAddReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//添加水印商品时弹窗中的商品列表
	SearchGoods(ctx context.Context, in *SearchGoodsReq, opts ...grpc.CallOption) (*SearchGoodsRes, error)
	//修改水印商品价格
	WatermarkGoodsPrice(ctx context.Context, in *GoodsPriceReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//终止水印
	StopWatermark(ctx context.Context, in *StopWatermarkReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//批量导入水印商品
	WatermarkGoodsImport(ctx context.Context, in *GoodsImportReq, opts ...grpc.CallOption) (*GoodsImportRes, error)
	//双旦活动添加修改收件地址
	ChristmasAddAddress(ctx context.Context, in *ChristmasAddAddressReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//双旦活动渠道统计
	ChannelStatistics(ctx context.Context, in *ChannelStatisticsReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	HandOutCoupon(ctx context.Context, in *HandOutCouponRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	HandOutCouponData(ctx context.Context, in *HandOutCouponRequest, opts ...grpc.CallOption) (*HandOutCouponResponse, error)
	// 宠物集市活动-获取用户领取的优惠券
	GetPetMarketUserCoupon(ctx context.Context, in *GetPetMarketUserCouponReq, opts ...grpc.CallOption) (*GetPetMarketUserCouponResponse, error)
	// 宠物集市活动-新增用户完成任务
	GetPetMarketTasks(ctx context.Context, in *GetPetMarketTasksReq, opts ...grpc.CallOption) (*GetPetMarketTasksResponse, error)
	// 宠物集市活动-新增用户完成任务
	AddPetMarketUserTask(ctx context.Context, in *AddPetMarketUserTaskReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 宠物集市活动-获取用户完成的任务
	GetPetMarketUserTasks(ctx context.Context, in *GetPetMarketUserTasksReq, opts ...grpc.CallOption) (*GetPetMarketUserTasksResponse, error)
	// 宠物集市活动-领取奖励
	AddPetMarketReward(ctx context.Context, in *AddPetMarketRewardReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 宠物集市活动-查询领取记录
	GetPetMarketUserReward(ctx context.Context, in *GetPetMarketUserRewardReq, opts ...grpc.CallOption) (*GetPetMarketUserRewardResponse, error)
	// 统计奖品领取数
	CountPetMarketReward(ctx context.Context, in *CountPetMarketRewardReq, opts ...grpc.CallOption) (*CountPetMarketRewardReqResponse, error)
	// 统计用户某时间段内是否购买过指定商品
	CountUserOrderWithSkuids(ctx context.Context, in *CountUserOrderWithSkuidsRequest, opts ...grpc.CallOption) (*CountUserOrderWithSkuidsResponse, error)
}

type activityServiceClient struct {
	cc *grpc.ClientConn
}

func NewActivityServiceClient(cc *grpc.ClientConn) ActivityServiceClient {
	return &activityServiceClient{cc}
}

func (c *activityServiceClient) OpenTreasure(ctx context.Context, in *PetTeaseRequest, opts ...grpc.CallOption) (*PetTeaseResponse, error) {
	out := new(PetTeaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/OpenTreasure", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetPetTeaseList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*PetTeaseListResponse, error) {
	out := new(PetTeaseListResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetPetTeaseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SendCoupon(ctx context.Context, in *SendCouponReq, opts ...grpc.CallOption) (*SendCouponRes, error) {
	out := new(SendCouponRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SendCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetExamineList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*ExamineListResponse, error) {
	out := new(ExamineListResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetExamineList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveShare(ctx context.Context, in *SaveShareReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveShare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveExamineAnswer(ctx context.Context, in *SaveExamineAnswerReq, opts ...grpc.CallOption) (*SaveExamineAnswerRes, error) {
	out := new(SaveExamineAnswerRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveExamineAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetMemberShare(ctx context.Context, in *GetMemberShareReq, opts ...grpc.CallOption) (*GetMemberShareRes, error) {
	out := new(GetMemberShareRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetMemberShare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetCouponTemplate(ctx context.Context, in *GetCouponTemplateReq, opts ...grpc.CallOption) (*GetCouponTemplateRes, error) {
	out := new(GetCouponTemplateRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetCouponTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveMemberCoupon(ctx context.Context, in *SaveMemberCouponReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveMemberCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetMemberCoupon(ctx context.Context, in *GetMemberCouponReq, opts ...grpc.CallOption) (*GetMemberCouponRes, error) {
	out := new(GetMemberCouponRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetMemberCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveSubscribeMessage(ctx context.Context, in *SaveSubscribeMessageReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveSubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CancelSubscribeMessage(ctx context.Context, in *CancelSubscribeMessageReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/CancelSubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetCouponMessage(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*CouponMessageRes, error) {
	out := new(CouponMessageRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetCouponMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetCatMonthInfo(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*GetCatMonthInfoRes, error) {
	out := new(GetCatMonthInfoRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetCatMonthInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveCatMonthInfo(ctx context.Context, in *SaveCatMonthInfoReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveCatMonthInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SaveWatermark(ctx context.Context, in *SaveWatermarkReq, opts ...grpc.CallOption) (*SaveWatermarkRes, error) {
	out := new(SaveWatermarkRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SaveWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkList(ctx context.Context, in *WatermarkListReq, opts ...grpc.CallOption) (*WatermarkListRes, error) {
	out := new(WatermarkListRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetWatermark(ctx context.Context, in *GetWatermarkReq, opts ...grpc.CallOption) (*GetWatermarkRes, error) {
	out := new(GetWatermarkRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListRes, error) {
	out := new(GoodsListRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkGoodsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkGoodsDelete(ctx context.Context, in *GoodsDeleteReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkGoodsDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkGoodsAdd(ctx context.Context, in *GoodsAddReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkGoodsAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) SearchGoods(ctx context.Context, in *SearchGoodsReq, opts ...grpc.CallOption) (*SearchGoodsRes, error) {
	out := new(SearchGoodsRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/SearchGoods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkGoodsPrice(ctx context.Context, in *GoodsPriceReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkGoodsPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) StopWatermark(ctx context.Context, in *StopWatermarkReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/StopWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) WatermarkGoodsImport(ctx context.Context, in *GoodsImportReq, opts ...grpc.CallOption) (*GoodsImportRes, error) {
	out := new(GoodsImportRes)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/WatermarkGoodsImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ChristmasAddAddress(ctx context.Context, in *ChristmasAddAddressReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/ChristmasAddAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ChannelStatistics(ctx context.Context, in *ChannelStatisticsReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/ChannelStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) HandOutCoupon(ctx context.Context, in *HandOutCouponRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/HandOutCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) HandOutCouponData(ctx context.Context, in *HandOutCouponRequest, opts ...grpc.CallOption) (*HandOutCouponResponse, error) {
	out := new(HandOutCouponResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/HandOutCouponData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetPetMarketUserCoupon(ctx context.Context, in *GetPetMarketUserCouponReq, opts ...grpc.CallOption) (*GetPetMarketUserCouponResponse, error) {
	out := new(GetPetMarketUserCouponResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetPetMarketUserCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetPetMarketTasks(ctx context.Context, in *GetPetMarketTasksReq, opts ...grpc.CallOption) (*GetPetMarketTasksResponse, error) {
	out := new(GetPetMarketTasksResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetPetMarketTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) AddPetMarketUserTask(ctx context.Context, in *AddPetMarketUserTaskReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/AddPetMarketUserTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetPetMarketUserTasks(ctx context.Context, in *GetPetMarketUserTasksReq, opts ...grpc.CallOption) (*GetPetMarketUserTasksResponse, error) {
	out := new(GetPetMarketUserTasksResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetPetMarketUserTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) AddPetMarketReward(ctx context.Context, in *AddPetMarketRewardReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/AddPetMarketReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) GetPetMarketUserReward(ctx context.Context, in *GetPetMarketUserRewardReq, opts ...grpc.CallOption) (*GetPetMarketUserRewardResponse, error) {
	out := new(GetPetMarketUserRewardResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/GetPetMarketUserReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CountPetMarketReward(ctx context.Context, in *CountPetMarketRewardReq, opts ...grpc.CallOption) (*CountPetMarketRewardReqResponse, error) {
	out := new(CountPetMarketRewardReqResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/CountPetMarketReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CountUserOrderWithSkuids(ctx context.Context, in *CountUserOrderWithSkuidsRequest, opts ...grpc.CallOption) (*CountUserOrderWithSkuidsResponse, error) {
	out := new(CountUserOrderWithSkuidsResponse)
	err := c.cc.Invoke(ctx, "/ac.ActivityService/CountUserOrderWithSkuids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActivityServiceServer is the server API for ActivityService service.
type ActivityServiceServer interface {
	//打开宝箱
	OpenTreasure(context.Context, *PetTeaseRequest) (*PetTeaseResponse, error)
	//获取奖励列表
	GetPetTeaseList(context.Context, *BaseRequest) (*PetTeaseListResponse, error)
	//调用电商发送优惠卷
	SendCoupon(context.Context, *SendCouponReq) (*SendCouponRes, error)
	//520爱猫日-获取题目列表
	GetExamineList(context.Context, *BaseRequest) (*ExamineListResponse, error)
	//保存助力分享信息
	SaveShare(context.Context, *SaveShareReq) (*BaseResponse, error)
	//520爱猫日-提交答案
	SaveExamineAnswer(context.Context, *SaveExamineAnswerReq) (*SaveExamineAnswerRes, error)
	//获取用户520爱猫日所有相关信息
	GetMemberShare(context.Context, *GetMemberShareReq) (*GetMemberShareRes, error)
	//获取优惠卷模板列表
	GetCouponTemplate(context.Context, *GetCouponTemplateReq) (*GetCouponTemplateRes, error)
	//保存门店优惠卷
	SaveMemberCoupon(context.Context, *SaveMemberCouponReq) (*BaseResponse, error)
	//获取门店优惠卷信息
	GetMemberCoupon(context.Context, *GetMemberCouponReq) (*GetMemberCouponRes, error)
	//保存订阅消息
	SaveSubscribeMessage(context.Context, *SaveSubscribeMessageReq) (*BaseResponse, error)
	//取消订阅消息
	CancelSubscribeMessage(context.Context, *CancelSubscribeMessageReq) (*BaseResponse, error)
	//获取优惠券订阅消息
	GetCouponMessage(context.Context, *BaseRequest) (*CouponMessageRes, error)
	//获取爱猫月活动信息
	GetCatMonthInfo(context.Context, *BaseRequest) (*GetCatMonthInfoRes, error)
	//保存爱猫月活动信息
	SaveCatMonthInfo(context.Context, *SaveCatMonthInfoReq) (*BaseResponse, error)
	//添加或编辑水印
	SaveWatermark(context.Context, *SaveWatermarkReq) (*SaveWatermarkRes, error)
	//获取水印列表
	WatermarkList(context.Context, *WatermarkListReq) (*WatermarkListRes, error)
	//获取水印详情
	GetWatermark(context.Context, *GetWatermarkReq) (*GetWatermarkRes, error)
	//获取水印关联的商品列表
	WatermarkGoodsList(context.Context, *GoodsListReq) (*GoodsListRes, error)
	//删除水印商品
	WatermarkGoodsDelete(context.Context, *GoodsDeleteReq) (*BaseResponse, error)
	//添加水印商品
	WatermarkGoodsAdd(context.Context, *GoodsAddReq) (*BaseResponse, error)
	//添加水印商品时弹窗中的商品列表
	SearchGoods(context.Context, *SearchGoodsReq) (*SearchGoodsRes, error)
	//修改水印商品价格
	WatermarkGoodsPrice(context.Context, *GoodsPriceReq) (*BaseResponse, error)
	//终止水印
	StopWatermark(context.Context, *StopWatermarkReq) (*BaseResponse, error)
	//批量导入水印商品
	WatermarkGoodsImport(context.Context, *GoodsImportReq) (*GoodsImportRes, error)
	//双旦活动添加修改收件地址
	ChristmasAddAddress(context.Context, *ChristmasAddAddressReq) (*BaseResponseNew, error)
	//双旦活动渠道统计
	ChannelStatistics(context.Context, *ChannelStatisticsReq) (*BaseResponseNew, error)
	HandOutCoupon(context.Context, *HandOutCouponRequest) (*BaseResponseNew, error)
	HandOutCouponData(context.Context, *HandOutCouponRequest) (*HandOutCouponResponse, error)
	// 宠物集市活动-获取用户领取的优惠券
	GetPetMarketUserCoupon(context.Context, *GetPetMarketUserCouponReq) (*GetPetMarketUserCouponResponse, error)
	// 宠物集市活动-新增用户完成任务
	GetPetMarketTasks(context.Context, *GetPetMarketTasksReq) (*GetPetMarketTasksResponse, error)
	// 宠物集市活动-新增用户完成任务
	AddPetMarketUserTask(context.Context, *AddPetMarketUserTaskReq) (*BaseResponse, error)
	// 宠物集市活动-获取用户完成的任务
	GetPetMarketUserTasks(context.Context, *GetPetMarketUserTasksReq) (*GetPetMarketUserTasksResponse, error)
	// 宠物集市活动-领取奖励
	AddPetMarketReward(context.Context, *AddPetMarketRewardReq) (*BaseResponse, error)
	// 宠物集市活动-查询领取记录
	GetPetMarketUserReward(context.Context, *GetPetMarketUserRewardReq) (*GetPetMarketUserRewardResponse, error)
	// 统计奖品领取数
	CountPetMarketReward(context.Context, *CountPetMarketRewardReq) (*CountPetMarketRewardReqResponse, error)
	// 统计用户某时间段内是否购买过指定商品
	CountUserOrderWithSkuids(context.Context, *CountUserOrderWithSkuidsRequest) (*CountUserOrderWithSkuidsResponse, error)
}

// UnimplementedActivityServiceServer can be embedded to have forward compatible implementations.
type UnimplementedActivityServiceServer struct {
}

func (*UnimplementedActivityServiceServer) OpenTreasure(ctx context.Context, req *PetTeaseRequest) (*PetTeaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenTreasure not implemented")
}
func (*UnimplementedActivityServiceServer) GetPetTeaseList(ctx context.Context, req *BaseRequest) (*PetTeaseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetTeaseList not implemented")
}
func (*UnimplementedActivityServiceServer) SendCoupon(ctx context.Context, req *SendCouponReq) (*SendCouponRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCoupon not implemented")
}
func (*UnimplementedActivityServiceServer) GetExamineList(ctx context.Context, req *BaseRequest) (*ExamineListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExamineList not implemented")
}
func (*UnimplementedActivityServiceServer) SaveShare(ctx context.Context, req *SaveShareReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveShare not implemented")
}
func (*UnimplementedActivityServiceServer) SaveExamineAnswer(ctx context.Context, req *SaveExamineAnswerReq) (*SaveExamineAnswerRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveExamineAnswer not implemented")
}
func (*UnimplementedActivityServiceServer) GetMemberShare(ctx context.Context, req *GetMemberShareReq) (*GetMemberShareRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberShare not implemented")
}
func (*UnimplementedActivityServiceServer) GetCouponTemplate(ctx context.Context, req *GetCouponTemplateReq) (*GetCouponTemplateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCouponTemplate not implemented")
}
func (*UnimplementedActivityServiceServer) SaveMemberCoupon(ctx context.Context, req *SaveMemberCouponReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveMemberCoupon not implemented")
}
func (*UnimplementedActivityServiceServer) GetMemberCoupon(ctx context.Context, req *GetMemberCouponReq) (*GetMemberCouponRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberCoupon not implemented")
}
func (*UnimplementedActivityServiceServer) SaveSubscribeMessage(ctx context.Context, req *SaveSubscribeMessageReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSubscribeMessage not implemented")
}
func (*UnimplementedActivityServiceServer) CancelSubscribeMessage(ctx context.Context, req *CancelSubscribeMessageReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSubscribeMessage not implemented")
}
func (*UnimplementedActivityServiceServer) GetCouponMessage(ctx context.Context, req *BaseRequest) (*CouponMessageRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCouponMessage not implemented")
}
func (*UnimplementedActivityServiceServer) GetCatMonthInfo(ctx context.Context, req *BaseRequest) (*GetCatMonthInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCatMonthInfo not implemented")
}
func (*UnimplementedActivityServiceServer) SaveCatMonthInfo(ctx context.Context, req *SaveCatMonthInfoReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCatMonthInfo not implemented")
}
func (*UnimplementedActivityServiceServer) SaveWatermark(ctx context.Context, req *SaveWatermarkReq) (*SaveWatermarkRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveWatermark not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkList(ctx context.Context, req *WatermarkListReq) (*WatermarkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkList not implemented")
}
func (*UnimplementedActivityServiceServer) GetWatermark(ctx context.Context, req *GetWatermarkReq) (*GetWatermarkRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWatermark not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkGoodsList(ctx context.Context, req *GoodsListReq) (*GoodsListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkGoodsList not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkGoodsDelete(ctx context.Context, req *GoodsDeleteReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkGoodsDelete not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkGoodsAdd(ctx context.Context, req *GoodsAddReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkGoodsAdd not implemented")
}
func (*UnimplementedActivityServiceServer) SearchGoods(ctx context.Context, req *SearchGoodsReq) (*SearchGoodsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchGoods not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkGoodsPrice(ctx context.Context, req *GoodsPriceReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkGoodsPrice not implemented")
}
func (*UnimplementedActivityServiceServer) StopWatermark(ctx context.Context, req *StopWatermarkReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopWatermark not implemented")
}
func (*UnimplementedActivityServiceServer) WatermarkGoodsImport(ctx context.Context, req *GoodsImportReq) (*GoodsImportRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatermarkGoodsImport not implemented")
}
func (*UnimplementedActivityServiceServer) ChristmasAddAddress(ctx context.Context, req *ChristmasAddAddressReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChristmasAddAddress not implemented")
}
func (*UnimplementedActivityServiceServer) ChannelStatistics(ctx context.Context, req *ChannelStatisticsReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelStatistics not implemented")
}
func (*UnimplementedActivityServiceServer) HandOutCoupon(ctx context.Context, req *HandOutCouponRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandOutCoupon not implemented")
}
func (*UnimplementedActivityServiceServer) HandOutCouponData(ctx context.Context, req *HandOutCouponRequest) (*HandOutCouponResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandOutCouponData not implemented")
}
func (*UnimplementedActivityServiceServer) GetPetMarketUserCoupon(ctx context.Context, req *GetPetMarketUserCouponReq) (*GetPetMarketUserCouponResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetMarketUserCoupon not implemented")
}
func (*UnimplementedActivityServiceServer) GetPetMarketTasks(ctx context.Context, req *GetPetMarketTasksReq) (*GetPetMarketTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetMarketTasks not implemented")
}
func (*UnimplementedActivityServiceServer) AddPetMarketUserTask(ctx context.Context, req *AddPetMarketUserTaskReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPetMarketUserTask not implemented")
}
func (*UnimplementedActivityServiceServer) GetPetMarketUserTasks(ctx context.Context, req *GetPetMarketUserTasksReq) (*GetPetMarketUserTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetMarketUserTasks not implemented")
}
func (*UnimplementedActivityServiceServer) AddPetMarketReward(ctx context.Context, req *AddPetMarketRewardReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPetMarketReward not implemented")
}
func (*UnimplementedActivityServiceServer) GetPetMarketUserReward(ctx context.Context, req *GetPetMarketUserRewardReq) (*GetPetMarketUserRewardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetMarketUserReward not implemented")
}
func (*UnimplementedActivityServiceServer) CountPetMarketReward(ctx context.Context, req *CountPetMarketRewardReq) (*CountPetMarketRewardReqResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountPetMarketReward not implemented")
}
func (*UnimplementedActivityServiceServer) CountUserOrderWithSkuids(ctx context.Context, req *CountUserOrderWithSkuidsRequest) (*CountUserOrderWithSkuidsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserOrderWithSkuids not implemented")
}

func RegisterActivityServiceServer(s *grpc.Server, srv ActivityServiceServer) {
	s.RegisterService(&_ActivityService_serviceDesc, srv)
}

func _ActivityService_OpenTreasure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTeaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).OpenTreasure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/OpenTreasure",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).OpenTreasure(ctx, req.(*PetTeaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetPetTeaseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetPetTeaseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetPetTeaseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetPetTeaseList(ctx, req.(*BaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SendCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SendCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SendCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SendCoupon(ctx, req.(*SendCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetExamineList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetExamineList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetExamineList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetExamineList(ctx, req.(*BaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveShare(ctx, req.(*SaveShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveExamineAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveExamineAnswerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveExamineAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveExamineAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveExamineAnswer(ctx, req.(*SaveExamineAnswerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetMemberShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetMemberShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetMemberShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetMemberShare(ctx, req.(*GetMemberShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetCouponTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetCouponTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetCouponTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetCouponTemplate(ctx, req.(*GetCouponTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveMemberCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveMemberCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveMemberCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveMemberCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveMemberCoupon(ctx, req.(*SaveMemberCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetMemberCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetMemberCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetMemberCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetMemberCoupon(ctx, req.(*GetMemberCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveSubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveSubscribeMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveSubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveSubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveSubscribeMessage(ctx, req.(*SaveSubscribeMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CancelSubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubscribeMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CancelSubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/CancelSubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CancelSubscribeMessage(ctx, req.(*CancelSubscribeMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetCouponMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetCouponMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetCouponMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetCouponMessage(ctx, req.(*BaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetCatMonthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetCatMonthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetCatMonthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetCatMonthInfo(ctx, req.(*BaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveCatMonthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCatMonthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveCatMonthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveCatMonthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveCatMonthInfo(ctx, req.(*SaveCatMonthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SaveWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SaveWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SaveWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SaveWatermark(ctx, req.(*SaveWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WatermarkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkList(ctx, req.(*WatermarkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetWatermark(ctx, req.(*GetWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkGoodsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkGoodsList(ctx, req.(*GoodsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkGoodsDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkGoodsDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkGoodsDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkGoodsDelete(ctx, req.(*GoodsDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkGoodsAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkGoodsAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkGoodsAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkGoodsAdd(ctx, req.(*GoodsAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_SearchGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGoodsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).SearchGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/SearchGoods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).SearchGoods(ctx, req.(*SearchGoodsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkGoodsPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsPriceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkGoodsPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkGoodsPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkGoodsPrice(ctx, req.(*GoodsPriceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_StopWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).StopWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/StopWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).StopWatermark(ctx, req.(*StopWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_WatermarkGoodsImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsImportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).WatermarkGoodsImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/WatermarkGoodsImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).WatermarkGoodsImport(ctx, req.(*GoodsImportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ChristmasAddAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChristmasAddAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ChristmasAddAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/ChristmasAddAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ChristmasAddAddress(ctx, req.(*ChristmasAddAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ChannelStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelStatisticsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ChannelStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/ChannelStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ChannelStatistics(ctx, req.(*ChannelStatisticsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_HandOutCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandOutCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).HandOutCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/HandOutCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).HandOutCoupon(ctx, req.(*HandOutCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_HandOutCouponData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandOutCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).HandOutCouponData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/HandOutCouponData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).HandOutCouponData(ctx, req.(*HandOutCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetPetMarketUserCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetMarketUserCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetPetMarketUserCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetPetMarketUserCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetPetMarketUserCoupon(ctx, req.(*GetPetMarketUserCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetPetMarketTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetMarketTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetPetMarketTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetPetMarketTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetPetMarketTasks(ctx, req.(*GetPetMarketTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_AddPetMarketUserTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPetMarketUserTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).AddPetMarketUserTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/AddPetMarketUserTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).AddPetMarketUserTask(ctx, req.(*AddPetMarketUserTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetPetMarketUserTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetMarketUserTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetPetMarketUserTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetPetMarketUserTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetPetMarketUserTasks(ctx, req.(*GetPetMarketUserTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_AddPetMarketReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPetMarketRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).AddPetMarketReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/AddPetMarketReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).AddPetMarketReward(ctx, req.(*AddPetMarketRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_GetPetMarketUserReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetMarketUserRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).GetPetMarketUserReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/GetPetMarketUserReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).GetPetMarketUserReward(ctx, req.(*GetPetMarketUserRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CountPetMarketReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountPetMarketRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CountPetMarketReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/CountPetMarketReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CountPetMarketReward(ctx, req.(*CountPetMarketRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CountUserOrderWithSkuids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserOrderWithSkuidsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CountUserOrderWithSkuids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ActivityService/CountUserOrderWithSkuids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CountUserOrderWithSkuids(ctx, req.(*CountUserOrderWithSkuidsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ActivityService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.ActivityService",
	HandlerType: (*ActivityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OpenTreasure",
			Handler:    _ActivityService_OpenTreasure_Handler,
		},
		{
			MethodName: "GetPetTeaseList",
			Handler:    _ActivityService_GetPetTeaseList_Handler,
		},
		{
			MethodName: "SendCoupon",
			Handler:    _ActivityService_SendCoupon_Handler,
		},
		{
			MethodName: "GetExamineList",
			Handler:    _ActivityService_GetExamineList_Handler,
		},
		{
			MethodName: "SaveShare",
			Handler:    _ActivityService_SaveShare_Handler,
		},
		{
			MethodName: "SaveExamineAnswer",
			Handler:    _ActivityService_SaveExamineAnswer_Handler,
		},
		{
			MethodName: "GetMemberShare",
			Handler:    _ActivityService_GetMemberShare_Handler,
		},
		{
			MethodName: "GetCouponTemplate",
			Handler:    _ActivityService_GetCouponTemplate_Handler,
		},
		{
			MethodName: "SaveMemberCoupon",
			Handler:    _ActivityService_SaveMemberCoupon_Handler,
		},
		{
			MethodName: "GetMemberCoupon",
			Handler:    _ActivityService_GetMemberCoupon_Handler,
		},
		{
			MethodName: "SaveSubscribeMessage",
			Handler:    _ActivityService_SaveSubscribeMessage_Handler,
		},
		{
			MethodName: "CancelSubscribeMessage",
			Handler:    _ActivityService_CancelSubscribeMessage_Handler,
		},
		{
			MethodName: "GetCouponMessage",
			Handler:    _ActivityService_GetCouponMessage_Handler,
		},
		{
			MethodName: "GetCatMonthInfo",
			Handler:    _ActivityService_GetCatMonthInfo_Handler,
		},
		{
			MethodName: "SaveCatMonthInfo",
			Handler:    _ActivityService_SaveCatMonthInfo_Handler,
		},
		{
			MethodName: "SaveWatermark",
			Handler:    _ActivityService_SaveWatermark_Handler,
		},
		{
			MethodName: "WatermarkList",
			Handler:    _ActivityService_WatermarkList_Handler,
		},
		{
			MethodName: "GetWatermark",
			Handler:    _ActivityService_GetWatermark_Handler,
		},
		{
			MethodName: "WatermarkGoodsList",
			Handler:    _ActivityService_WatermarkGoodsList_Handler,
		},
		{
			MethodName: "WatermarkGoodsDelete",
			Handler:    _ActivityService_WatermarkGoodsDelete_Handler,
		},
		{
			MethodName: "WatermarkGoodsAdd",
			Handler:    _ActivityService_WatermarkGoodsAdd_Handler,
		},
		{
			MethodName: "SearchGoods",
			Handler:    _ActivityService_SearchGoods_Handler,
		},
		{
			MethodName: "WatermarkGoodsPrice",
			Handler:    _ActivityService_WatermarkGoodsPrice_Handler,
		},
		{
			MethodName: "StopWatermark",
			Handler:    _ActivityService_StopWatermark_Handler,
		},
		{
			MethodName: "WatermarkGoodsImport",
			Handler:    _ActivityService_WatermarkGoodsImport_Handler,
		},
		{
			MethodName: "ChristmasAddAddress",
			Handler:    _ActivityService_ChristmasAddAddress_Handler,
		},
		{
			MethodName: "ChannelStatistics",
			Handler:    _ActivityService_ChannelStatistics_Handler,
		},
		{
			MethodName: "HandOutCoupon",
			Handler:    _ActivityService_HandOutCoupon_Handler,
		},
		{
			MethodName: "HandOutCouponData",
			Handler:    _ActivityService_HandOutCouponData_Handler,
		},
		{
			MethodName: "GetPetMarketUserCoupon",
			Handler:    _ActivityService_GetPetMarketUserCoupon_Handler,
		},
		{
			MethodName: "GetPetMarketTasks",
			Handler:    _ActivityService_GetPetMarketTasks_Handler,
		},
		{
			MethodName: "AddPetMarketUserTask",
			Handler:    _ActivityService_AddPetMarketUserTask_Handler,
		},
		{
			MethodName: "GetPetMarketUserTasks",
			Handler:    _ActivityService_GetPetMarketUserTasks_Handler,
		},
		{
			MethodName: "AddPetMarketReward",
			Handler:    _ActivityService_AddPetMarketReward_Handler,
		},
		{
			MethodName: "GetPetMarketUserReward",
			Handler:    _ActivityService_GetPetMarketUserReward_Handler,
		},
		{
			MethodName: "CountPetMarketReward",
			Handler:    _ActivityService_CountPetMarketReward_Handler,
		},
		{
			MethodName: "CountUserOrderWithSkuids",
			Handler:    _ActivityService_CountUserOrderWithSkuids_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/activity_service.proto",
}

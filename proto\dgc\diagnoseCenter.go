package dgc

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type Client struct {
	lock     sync.Mutex
	Conn     *grpc.ClientConn
	Ctx      context.Context
	DCT      DoctorServiceClient
	Im       ImServiceClient
	ODR      OrderServiceClient
	Hospital HospitalServiceClient
	PMT      PreemptServiceClient
	STM      SystemServiceClient
	MSG      MINIProgramMSGServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

type PlatformChannel struct {
	ChannelId  int
	UserAgent  int
	AppChannel int
}

type GrpcContext struct {
	Channel PlatformChannel
}

func GetDiagnoseServiceClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.diagnose-center")
	}

	if url == "" {
		url = "127.0.0.1:11012"
	}
	//url = "**********:11012"

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("diagnose-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.DCT = NewDoctorServiceClient(grpcClient.Conn)
		grpcClient.Im = NewImServiceClient(grpcClient.Conn)
		grpcClient.ODR = NewOrderServiceClient(grpcClient.Conn)
		grpcClient.Hospital = NewHospitalServiceClient(grpcClient.Conn)
		grpcClient.PMT = NewPreemptServiceClient(grpcClient.Conn)
		grpcClient.STM = NewSystemServiceClient(grpcClient.Conn)
		grpcClient.MSG = NewMINIProgramMSGServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}

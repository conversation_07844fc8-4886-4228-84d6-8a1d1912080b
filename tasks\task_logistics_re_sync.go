package tasks

import (
	"encoding/json"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
	"order-center/models"
	"order-center/proto/et"
	"time"

	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

//同步给第三方配送失败记录的重新同步
type TaskLogisticsReSync struct {
	SyncData *models.OrderLogisticsSync
	EtClient *et.Client
	Session  *xorm.Session
	services.BaseService
}

//阿闻自提单超过4小时自动完成,每5分钟执行一次
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("TaskLogisticsReSync task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */1 * * * *", func() {
		service := TaskLogisticsReSync{}
		service.LogisticsReSync()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s TaskLogisticsReSync) LogisticsReSync() {
	//连接池勿关闭
	db := services.GetDBConn()
	var taskList []*models.OrderLogisticsSync
	err := db.SQL(`SELECT * FROM order_logistics_sync WHERE status=2 AND re_sync_cnt <3 limit 10`).Find(&taskList)
	if err != nil {
		glog.Error("查询异常配送同步数据出错", err)
	}
	s.Session = db.NewSession()
	defer s.Session.Close()
	s.EtClient = et.GetExternalClient()
	if len(taskList) > 0 {
		for _, v := range taskList {
			s.SyncData = v
			s.DoSync()
		}
	}
}

func (s TaskLogisticsReSync) DoSync() {

	//连接池勿关闭
	redisConn := services.GetRedisConn()
	lockCard := "task:lock:LogisticsReSync:" + cast.ToString(s.SyncData.Id)
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 30*time.Second).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	LogisticsSync := new(et.MtOrderLogisticsSyncRequest)
	_ = json.Unmarshal([]byte(s.SyncData.Data), LogisticsSync)

	var res *et.ExternalResponse
	glog.Info(s.SyncData.OrderSn, ",reSyncDeliveryNode-更新配送信息至美团：", s.SyncData.Data)
	res, err := s.EtClient.MtOrder.MtOrderLogisticsSync(s.EtClient.Ctx, LogisticsSync)
	glog.Info(s.SyncData.OrderSn, ",reSyncDeliveryNode-更新配送信息至美团返回结果：", s.SyncData.Data, res, err)

	var status = 2
	if err != nil {
		glog.Error(s.SyncData.OrderSn, ", 自配送商家同步发货状态和配送信息错误, 自配订单配送中错误, ", err.Error())
	} else if res.Code != 200 {
		glog.Error(s.SyncData.OrderSn, ", 自配送商家同步发货状态和配送信息错误, 自配订单配送中错误, ", kit.JsonEncode(res))
	} else {
		status = 1
	}
	has, err := s.Session.Exec("UPDATE order_logistics_sync SET re_sync_cnt = re_sync_cnt+1,status=? WHERE id=?", status, s.SyncData.Id)
	if err != nil {
		glog.Error(s.SyncData.OrderSn, ",reSyncDeliveryNode-更新推送次数与状态出错：", s.SyncData.Data)
		return
	}
	fmt.Println(has)
}

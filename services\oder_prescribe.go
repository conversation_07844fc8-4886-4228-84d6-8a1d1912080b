package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"net/http"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type baseResponse struct {
	Code int `json:"code"`
	Data struct {
		OrderSn          int    `json:"order_sn"`
		PrescriptionCode string `json:"prescription_code"`
	} `json:"data"`
	Message   string `json:"message"`
	PageCount int    `json:"page_count"`
	PageTotal int    `json:"page_total"`
	RequestID string `json:"request_id"`
}

// Prescribe 开处方单
func (o *OrderService) Prescribe(ctx context.Context, req *oc.OrderPrescribeReq) (*oc.OrderPrescribeRes, error) {
	out := new(oc.OrderPrescribeRes)
	out.Code = 400

	var skuIds []int32
	skuMap := make(map[int32]interface{})
	for _, v := range req.Skus {
		skuIds = append(skuIds, v.SkuId)
		skuMap[v.SkuId] = v.Num
	}
	session := GetDBConn().NewSession()
	defer session.Close()
	var product []models.ProductSkuExtend

	err := session.Table("dc_product.product").
		Select("product.*,sku.id as sku_id").
		Join("inner", "dc_product.sku", "sku.product_id=product.id").
		In("sku.id", skuIds).Find(&product)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if len(product) == 0 {
		out.Message = "查询商品数据异常"
		return out, nil
	}
	// 校验药品是否为处方
	PrescriptionMedicines := make([]*oc.PrescriptionMedicine, len(req.Skus))

	for i, v := range product {
		var DosageVal float32
		var remark string
		//用量根据宠物品种判断 -1未知 1000猫 1001犬 1002其它
		if v.DrugDosage != nil && len(v.DrugDosage.RecommendDosage) > 0 {
			dosageVal := make(map[string]interface{})
			var firstDosageVal float64
			for i, dosage := range v.DrugDosage.RecommendDosage {
				if i == 0 { //匹配不到，则获取第一个用量
					firstDosageVal = dosage.Value
				}
				dosageVal[dosage.Code] = dosage.Value
				//备注
				remark += fmt.Sprintf("%s每次%s(%s)/公斤;", dosage.Name, decimal.NewFromFloat(dosage.Value).Round(2).String(), v.DrugDosage.DosingUnitName)
			}
			if dosageVal[req.PetInfo.PetVarietyCode] != nil {
				DosageVal = cast.ToFloat32(dosageVal[req.PetInfo.PetVariety])
			} else {
				DosageVal = float32(firstDosageVal)
			}
		}

		if v.IsPrescribedDrug == 0 {
			out.Message = "处方开具失败"
			return out, nil
		}

		if DosageVal < 0.001 {
			out.Message = "互联网医疗开处方单,用量不能为0"
			return out, nil
		}
		//请求参数组装
		medicine := &oc.PrescriptionMedicine{
			GroupName:      "1",
			Dosage:         DosageVal,                     //用量
			DosingDays:     int64(v.DosingDays),           //投药天数
			DosingFreq:     v.DrugDosage.UseFrequencyName, //投药频率
			DosingFreqCode: v.DrugDosage.UseFrequency,
			DosingMode:     v.DrugDosage.DosingWayName, //投药方式
			DosingModeCode: v.DrugDosage.DosingWay,
			DosingUnit:     v.DrugDosage.DosingUnitName, //投药单位
			DosingUnitCode: v.DrugDosage.DosingUnit,
			//DrugTotal:      0, //总用药量=单次用量*频次系数*天数(互联网处理)
			Remark:  remark,
			SkuId:   v.SkuId,
			SaleNum: cast.ToInt64(skuMap[v.SkuId]),
		}
		PrescriptionMedicines[i] = medicine
	}

	params := dto.CreatePrescription{
		FinanceCode:          req.FinanceCode,
		HospitalName:         req.HospitalName,
		PetWeight:            req.PetWeight,
		OperateType:          1,
		ConsultMemberPetInfo: req.PetInfo,
		PrescriptionDiagnose: req.Diagnose,
		PrescriptionMedicine: PrescriptionMedicines,
	}
	//获取结果
	code, body := utils.HttpPostToDigitalHealth("/nuclei-api/medical/prescription/create", kit.JsonEncodeByte(params), "json")
	glog.Info("互联网医疗开处方单，参数：", kit.JsonEncode(params), "返回结果:", body)

	var respData baseResponse
	err = json.Unmarshal([]byte(body), &respData)
	if err != nil {
		glog.Errorf("互联网医疗开处方单，反序列化响应数据异常,响应:%s,err:%+v", body, err)
		out.Message = err.Error()
		return out, err
	}
	if code != 200 {
		glog.Errorf("互联网医疗开处方单，接口请求响应失败:%s,err:%+v", body, respData.Message)
		out.Message = respData.Message
		return out, err
	}
	if respData.Code != http.StatusOK || respData.Data.OrderSn == 0 {
		glog.Errorf("互联网医疗开处方单, 请求业务处理失败,响应:%s", body)
		out.Message = respData.Message
		return out, nil
	}

	out.ConsultOrderSn = fmt.Sprintf("%d", respData.Data.OrderSn)

	// 缓存处方单号
	if len(out.ConsultOrderSn) > 0 {
		cacheKey := prescribeCacheKey(req.PetInfo.MemberId, req.FinanceCode, req.Skus)
		GetRedisConn().Set(cacheKey, out.ConsultOrderSn, time.Hour*24*3) // 处方单号有效期3天
	}

	out.Code = 200
	return out, nil
}

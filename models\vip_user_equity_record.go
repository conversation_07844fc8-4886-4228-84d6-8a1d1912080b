package models

import (
	"time"
)

type VipUserEquityRecord struct {
	Id          int       `xorm:"not null pk autoincr INT(11)"`
	OrderSn     string    `xorm:"not null index VARCHAR(50)"`
	EquityId    int       `xorm:"not null default 0 comment('权益id') TINYINT(1)"`
	PrivilegeId string    `xorm:"default NULL comment('权益值') VARCHAR(256)"`
	GiftOrderSn string    `xorm:"default NULL comment('礼包电商父订单号') VARCHAR(50)"`
	CreateTime  time.Time `xorm:"default 'current_timestamp()' DATETIME created"`
	UpdateTime  time.Time `xorm:"default 'current_timestamp()' DATETIME updated"`
}

func (r VipUserEquityRecord) TableName() string {
	return "datacenter.vip_user_equity_record"
}

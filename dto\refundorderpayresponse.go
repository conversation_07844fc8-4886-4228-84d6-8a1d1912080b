package dto

type RefundOrderPayResponse struct {
	//商户号
	Code  int `json:"code,omitempty"`
	//交易流水号
	Message  string `json:"message,omitempty"`
	//退款金额，以分为单位
	Data  RefundOrderPayData `json:"data,omitempty"`

}

type RefundOrderPayData struct {
	//商户私有域：交易返回时原样返回给商户网站，给商户备用
	BackParam  string `json:"backParam,omitempty"`
	//后台回调地址
	CallbackUrl  string `json:"callbackUrl,omitempty"`
	//客户端 IP ：如 127.0.0.1
	ClientIP  string `json:"clientIP,omitempty"`
	//扩展信息：预留字段，JSON 格式
	ExtendInfo  string `json:"extendInfo,omitempty"`
	//退款金额，以分为单位
	RefundAmt  string `json:"refundAmt,omitempty"`
	//退款订单号
	RefundId  string `json:"refundId,omitempty"`
	//返回状态码 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	RspCode  string `json:"rspCode,omitempty"`
	//返回信息
	RspMessage  string `json:"rspMessage,omitempty"`
	//交易流水号
	TransactionNo  string `json:"transactionNo,omitempty"`
	//签名
	Sign  string `json:"sign,omitempty"`
}

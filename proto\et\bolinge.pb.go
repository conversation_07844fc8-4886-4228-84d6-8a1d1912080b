// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/bolinge.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BlgTokenReq struct {
	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	// 密码
	Password             string   `protobuf:"bytes,2,opt,name=password,proto3" json:"password"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlgTokenReq) Reset()         { *m = BlgTokenReq{} }
func (m *BlgTokenReq) String() string { return proto.CompactTextString(m) }
func (*BlgTokenReq) ProtoMessage()    {}
func (*BlgTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_14fb5496621f4039, []int{0}
}

func (m *BlgTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlgTokenReq.Unmarshal(m, b)
}
func (m *BlgTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlgTokenReq.Marshal(b, m, deterministic)
}
func (m *BlgTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlgTokenReq.Merge(m, src)
}
func (m *BlgTokenReq) XXX_Size() int {
	return xxx_messageInfo_BlgTokenReq.Size(m)
}
func (m *BlgTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BlgTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_BlgTokenReq proto.InternalMessageInfo

func (m *BlgTokenReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *BlgTokenReq) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type BlgTokenResp struct {
	//是否成功
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	//信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//小程序登录code
	Code int32 `protobuf:"varint,3,opt,name=code,proto3" json:"code"`
	//返回对象
	Result *BlgTokenData `protobuf:"bytes,4,opt,name=result,proto3" json:"result"`
	//服务器时间戳
	Timestamp            int64    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlgTokenResp) Reset()         { *m = BlgTokenResp{} }
func (m *BlgTokenResp) String() string { return proto.CompactTextString(m) }
func (*BlgTokenResp) ProtoMessage()    {}
func (*BlgTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_14fb5496621f4039, []int{1}
}

func (m *BlgTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlgTokenResp.Unmarshal(m, b)
}
func (m *BlgTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlgTokenResp.Marshal(b, m, deterministic)
}
func (m *BlgTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlgTokenResp.Merge(m, src)
}
func (m *BlgTokenResp) XXX_Size() int {
	return xxx_messageInfo_BlgTokenResp.Size(m)
}
func (m *BlgTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BlgTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_BlgTokenResp proto.InternalMessageInfo

func (m *BlgTokenResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *BlgTokenResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BlgTokenResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BlgTokenResp) GetResult() *BlgTokenData {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *BlgTokenResp) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type BlgTokenData struct {
	// 过期时间，单位：秒
	Expired int32 `protobuf:"varint,1,opt,name=expired,proto3" json:"expired"`
	// token
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlgTokenData) Reset()         { *m = BlgTokenData{} }
func (m *BlgTokenData) String() string { return proto.CompactTextString(m) }
func (*BlgTokenData) ProtoMessage()    {}
func (*BlgTokenData) Descriptor() ([]byte, []int) {
	return fileDescriptor_14fb5496621f4039, []int{2}
}

func (m *BlgTokenData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlgTokenData.Unmarshal(m, b)
}
func (m *BlgTokenData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlgTokenData.Marshal(b, m, deterministic)
}
func (m *BlgTokenData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlgTokenData.Merge(m, src)
}
func (m *BlgTokenData) XXX_Size() int {
	return xxx_messageInfo_BlgTokenData.Size(m)
}
func (m *BlgTokenData) XXX_DiscardUnknown() {
	xxx_messageInfo_BlgTokenData.DiscardUnknown(m)
}

var xxx_messageInfo_BlgTokenData proto.InternalMessageInfo

func (m *BlgTokenData) GetExpired() int32 {
	if m != nil {
		return m.Expired
	}
	return 0
}

func (m *BlgTokenData) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type BlgVerifyReq struct {
	//订单号
	OrderNo string `protobuf:"bytes,1,opt,name=orderNo,proto3" json:"orderNo"`
	//门店名称
	Store string `protobuf:"bytes,2,opt,name=store,proto3" json:"store"`
	//核销时间
	VerificationTime string `protobuf:"bytes,3,opt,name=verificationTime,proto3" json:"verificationTime"`
	//核销skuid
	SkuNo string `protobuf:"bytes,4,opt,name=skuNo,proto3" json:"skuNo"`
	//产品名称
	SkuName string `protobuf:"bytes,5,opt,name=skuName,proto3" json:"skuName"`
	//用户id
	UserId               string   `protobuf:"bytes,6,opt,name=userId,proto3" json:"userId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlgVerifyReq) Reset()         { *m = BlgVerifyReq{} }
func (m *BlgVerifyReq) String() string { return proto.CompactTextString(m) }
func (*BlgVerifyReq) ProtoMessage()    {}
func (*BlgVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_14fb5496621f4039, []int{3}
}

func (m *BlgVerifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlgVerifyReq.Unmarshal(m, b)
}
func (m *BlgVerifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlgVerifyReq.Marshal(b, m, deterministic)
}
func (m *BlgVerifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlgVerifyReq.Merge(m, src)
}
func (m *BlgVerifyReq) XXX_Size() int {
	return xxx_messageInfo_BlgVerifyReq.Size(m)
}
func (m *BlgVerifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BlgVerifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_BlgVerifyReq proto.InternalMessageInfo

func (m *BlgVerifyReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *BlgVerifyReq) GetStore() string {
	if m != nil {
		return m.Store
	}
	return ""
}

func (m *BlgVerifyReq) GetVerificationTime() string {
	if m != nil {
		return m.VerificationTime
	}
	return ""
}

func (m *BlgVerifyReq) GetSkuNo() string {
	if m != nil {
		return m.SkuNo
	}
	return ""
}

func (m *BlgVerifyReq) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *BlgVerifyReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type BlgVerifyResp struct {
	//是否成功
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	//信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//小程序登录code
	Code int32 `protobuf:"varint,3,opt,name=code,proto3" json:"code"`
	//服务器时间戳
	Timestamp            int64    `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlgVerifyResp) Reset()         { *m = BlgVerifyResp{} }
func (m *BlgVerifyResp) String() string { return proto.CompactTextString(m) }
func (*BlgVerifyResp) ProtoMessage()    {}
func (*BlgVerifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_14fb5496621f4039, []int{4}
}

func (m *BlgVerifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlgVerifyResp.Unmarshal(m, b)
}
func (m *BlgVerifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlgVerifyResp.Marshal(b, m, deterministic)
}
func (m *BlgVerifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlgVerifyResp.Merge(m, src)
}
func (m *BlgVerifyResp) XXX_Size() int {
	return xxx_messageInfo_BlgVerifyResp.Size(m)
}
func (m *BlgVerifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BlgVerifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_BlgVerifyResp proto.InternalMessageInfo

func (m *BlgVerifyResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *BlgVerifyResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BlgVerifyResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BlgVerifyResp) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func init() {
	proto.RegisterType((*BlgTokenReq)(nil), "et.BlgTokenReq")
	proto.RegisterType((*BlgTokenResp)(nil), "et.BlgTokenResp")
	proto.RegisterType((*BlgTokenData)(nil), "et.BlgTokenData")
	proto.RegisterType((*BlgVerifyReq)(nil), "et.BlgVerifyReq")
	proto.RegisterType((*BlgVerifyResp)(nil), "et.BlgVerifyResp")
}

func init() { proto.RegisterFile("et/bolinge.proto", fileDescriptor_14fb5496621f4039) }

var fileDescriptor_14fb5496621f4039 = []byte{
	// 384 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0xcd, 0xae, 0xd3, 0x30,
	0x10, 0x85, 0x95, 0x7b, 0x9b, 0xdc, 0x76, 0xda, 0x8a, 0x60, 0x21, 0x64, 0x55, 0x2c, 0xa2, 0xac,
	0x22, 0x16, 0x45, 0x6a, 0xf7, 0x2c, 0x2a, 0x10, 0x62, 0xd3, 0x85, 0xa9, 0xba, 0x77, 0x93, 0x21,
	0xb2, 0x9a, 0xc4, 0xa9, 0xed, 0x14, 0x78, 0x19, 0x1e, 0x82, 0x27, 0x44, 0x76, 0xe2, 0xfe, 0xb1,
	0x65, 0x97, 0x6f, 0xc6, 0x3e, 0x3e, 0x73, 0x26, 0x10, 0xa3, 0xf9, 0x70, 0x90, 0x95, 0x68, 0x4a,
	0x5c, 0xb6, 0x4a, 0x1a, 0x49, 0x9e, 0xd0, 0xa4, 0x9f, 0x61, 0xba, 0xa9, 0xca, 0x9d, 0x3c, 0x62,
	0xc3, 0xf0, 0x44, 0x16, 0x30, 0xee, 0x34, 0xaa, 0x86, 0xd7, 0x48, 0x83, 0x24, 0xc8, 0x26, 0xec,
	0xc2, 0xb6, 0xd7, 0x72, 0xad, 0x7f, 0x48, 0x55, 0xd0, 0xa7, 0xbe, 0xe7, 0x39, 0xfd, 0x1d, 0xc0,
	0xec, 0xaa, 0xa3, 0x5b, 0x42, 0xe1, 0x45, 0x77, 0x79, 0x8e, 0x5a, 0x3b, 0x9d, 0x31, 0xf3, 0x68,
	0x3b, 0x35, 0x6a, 0xcd, 0x4b, 0x1c, 0x54, 0x3c, 0x12, 0x02, 0xa3, 0x5c, 0x16, 0x48, 0x9f, 0x93,
	0x20, 0x0b, 0x99, 0xfb, 0x26, 0x19, 0x44, 0x0a, 0x75, 0x57, 0x19, 0x3a, 0x4a, 0x82, 0x6c, 0xba,
	0x8a, 0x97, 0x68, 0x96, 0xfe, 0xa5, 0x4f, 0xdc, 0x70, 0x36, 0xf4, 0xc9, 0x3b, 0x98, 0x18, 0x51,
	0xa3, 0x36, 0xbc, 0x6e, 0x69, 0x98, 0x04, 0xd9, 0x33, 0xbb, 0x16, 0xd2, 0x8f, 0x57, 0x7f, 0xf6,
	0x96, 0x75, 0x81, 0x3f, 0x5b, 0xa1, 0xb0, 0x70, 0xfe, 0x42, 0xe6, 0x91, 0xbc, 0x81, 0xd0, 0xd8,
	0x63, 0x83, 0xbb, 0x1e, 0xd2, 0x3f, 0xfd, 0x80, 0x7b, 0x54, 0xe2, 0xfb, 0x2f, 0x9b, 0x14, 0x85,
	0x17, 0xa9, 0x0a, 0x54, 0x5b, 0x39, 0x04, 0xe5, 0xd1, 0x0a, 0x68, 0x23, 0x95, 0x1f, 0xaf, 0x07,
	0xf2, 0x1e, 0xe2, 0xb3, 0xbd, 0x2c, 0x72, 0x6e, 0x84, 0x6c, 0x76, 0xa2, 0xee, 0x07, 0x9d, 0xb0,
	0x7f, 0xea, 0x4e, 0xe1, 0xd8, 0x6d, 0xa5, 0x9b, 0xd9, 0x2a, 0x58, 0x70, 0x91, 0x1e, 0xbb, 0xad,
	0x5d, 0x4d, 0xd8, 0xbf, 0x38, 0x20, 0x79, 0x0b, 0x91, 0xdd, 0xd2, 0xd7, 0x82, 0x46, 0xae, 0x31,
	0x50, 0xda, 0xc1, 0xfc, 0xc6, 0xf3, 0x7f, 0xdc, 0xca, 0x5d, 0xd6, 0xa3, 0x87, 0xac, 0x57, 0x1d,
	0xc0, 0xa6, 0x2a, 0xbf, 0xa1, 0x3a, 0x8b, 0x1c, 0xc9, 0x0a, 0xe6, 0x5f, 0xb0, 0x41, 0xc5, 0x0d,
	0xba, 0xf8, 0xc9, 0xab, 0xdb, 0x15, 0x32, 0x3c, 0x2d, 0xe2, 0xfb, 0x82, 0x6e, 0xc9, 0x1a, 0x66,
	0xfb, 0x9b, 0x50, 0x88, 0x3f, 0x71, 0x89, 0x7f, 0xf1, 0xfa, 0xa1, 0xa2, 0xdb, 0x43, 0xe4, 0xfe,
	0xea, 0xf5, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x93, 0x7c, 0x2d, 0x3e, 0xe9, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BlgServiceClient is the client API for BlgService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BlgServiceClient interface {
	//获取token
	GenerateToken(ctx context.Context, in *BlgTokenReq, opts ...grpc.CallOption) (*BlgTokenResp, error)
	//核销
	Verification(ctx context.Context, in *BlgVerifyReq, opts ...grpc.CallOption) (*BlgVerifyResp, error)
}

type blgServiceClient struct {
	cc *grpc.ClientConn
}

func NewBlgServiceClient(cc *grpc.ClientConn) BlgServiceClient {
	return &blgServiceClient{cc}
}

func (c *blgServiceClient) GenerateToken(ctx context.Context, in *BlgTokenReq, opts ...grpc.CallOption) (*BlgTokenResp, error) {
	out := new(BlgTokenResp)
	err := c.cc.Invoke(ctx, "/et.BlgService/GenerateToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blgServiceClient) Verification(ctx context.Context, in *BlgVerifyReq, opts ...grpc.CallOption) (*BlgVerifyResp, error) {
	out := new(BlgVerifyResp)
	err := c.cc.Invoke(ctx, "/et.BlgService/Verification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlgServiceServer is the server API for BlgService service.
type BlgServiceServer interface {
	//获取token
	GenerateToken(context.Context, *BlgTokenReq) (*BlgTokenResp, error)
	//核销
	Verification(context.Context, *BlgVerifyReq) (*BlgVerifyResp, error)
}

// UnimplementedBlgServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBlgServiceServer struct {
}

func (*UnimplementedBlgServiceServer) GenerateToken(ctx context.Context, req *BlgTokenReq) (*BlgTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateToken not implemented")
}
func (*UnimplementedBlgServiceServer) Verification(ctx context.Context, req *BlgVerifyReq) (*BlgVerifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Verification not implemented")
}

func RegisterBlgServiceServer(s *grpc.Server, srv BlgServiceServer) {
	s.RegisterService(&_BlgService_serviceDesc, srv)
}

func _BlgService_GenerateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlgTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlgServiceServer).GenerateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.BlgService/GenerateToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlgServiceServer).GenerateToken(ctx, req.(*BlgTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlgService_Verification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlgVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlgServiceServer).Verification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.BlgService/Verification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlgServiceServer).Verification(ctx, req.(*BlgVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BlgService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.BlgService",
	HandlerType: (*BlgServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateToken",
			Handler:    _BlgService_GenerateToken_Handler,
		},
		{
			MethodName: "Verification",
			Handler:    _BlgService_Verification_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/bolinge.proto",
}

package dto

type MallBaseResponse struct {
	//响应码
	Code int32 `json:"code"`
	//响应结果success|failure
	Datas interface{} `json:"datas"`
}

type MallBaseResponse1 struct {
	//响应码
	Code int32 `json:"code"`
	//响应结果success|failure
	Datas MallBaseError `json:"datas"`
}
type MallBaseError struct {
	Error string `json:"error"`
}

//电商返回
type MallPushResponse struct {
	//响应码
	Code int32 `json:"code"`
}

type SyncMallOrderResult struct {
	OrderInfo     SyncMallOrderInfo       `json:"order_info"`
	OrderGoods    []SyncMallOrderGoods    `json:"order_goods"`
	OrderCodeinfo []SyncMallOrderCodeinfo `json:"order_codeinfo"`
}

type SyncMallOrderInfo struct {
	OldOrderSn     string `json:"old_order_sn"`    //电商原订单编号
	OrderSn        string `json:"order_sn"`        //订单编号
	OrderAmount    int32  `json:"order_amount"`    //订单总金额
	ShippingFee    int32  `json:"shipping_fee"`    //运费
	GoodsAmount    int32  `json:"goods_amount"`    //商品总金额
	IsVirtual      int32  `json:"is_virtual"`      //是否是虚拟订单，0否1是
	PromotionTotal int32  `json:"promotion_total"` //优惠金额
	PowerId        int32  `json:"power_id"`        //助力ID
	OrderType      int32  `json:"order_type"`      //助力ID
	WarehouseCode  string `json:"warehouse_code"`  //药品仓的id
}

type SyncMallOrderGoods struct {
	RecID         int64                           `json:"rec_id"`          //电商订单商品索引id
	GoodsID       string                          `json:"goods_id"`        //	电商商品skuid
	GoodsName     string                          `json:"goods_name"`      //	商品名称
	GoodsNum      int32                           `json:"goods_num"`       //商品数量
	GoodsPrice    int32                           `json:"goods_price"`     //商品原价
	GoodsPayPrice int32                           `json:"goods_pay_price"` //	商品实际成交金额
	OcId          int64                           `json:"oc_id"`           //订单中心订单商品索引id
	VoucherInfo   []SyncMallOrderGoodsVoucherInfo `json:"voucher_info"`    //商品分摊优惠信息
	DiscountPrice int32                           `json:"discount_price"`  //商品优惠单价
}

type SyncMallOrderGoodsVoucherInfo struct {
	VoucherPrice  int32 `json:"voucher_price"`  //单个商品优惠后价格
	VoucherAmount int32 `json:"voucher_amount"` //单个商品优惠金额
}

type SyncMallOrderCodeinfo struct {
	ErpOrderID string `json:"erp_order_id"` //ERP订单号
	VrCode     string `json:"vr_code"`      //核销码
}

type PinOrderResult struct {
	OrderType     int32  `json:"order_type"`      //订单类型 1 拼团
	BusinessJson  string `json:"business_json"`   //业务类型：提交的回传数据
	OrderSn       string `json:"order_sn"`        //订单编号
	TradeNo       string `json:"trade_no"`        //交易单号
	PaymentTime   string `json:"payment_time"`    //支付时间
	AddTime       string `json:"AddTime"`         //订单创建时间
	IsVirtual     int32  `json:"is_virtual"`      //是否是虚拟订单，0否1是
	OrderAmount   int32  `json:"order_amount"`    //订单总金额
	GoodsAmount   int32  `json:"goods_amount"`    //商品总金额
	GoodsId       int32  `json:"goods_id"`        //电商商品skuid
	GoodsNum      int32  `json:"goods_num"`       //商品数量
	GoodsPrice    int32  `json:"goods_price"`     //商品原价
	GoodsPayPrice int32  `json:"goods_pay_price"` //商品实际支付金额
}

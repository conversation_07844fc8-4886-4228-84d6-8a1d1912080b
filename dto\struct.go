package dto

import "sort"

type (
	DianShangOrder struct {
		WarehouseId   int32
		WarehouseCode string
		Orderid       string
		Num           int32
		Thirdid       string
		Goods         []DianShangGoods
	}

	DianShangGoods struct {
		Id    string
		Num   int32
		Allot int32
		RecId string
	}

	OldOrderDetail struct {
		Petid               string   `json:"petid"`               //宠物id
		Isneedpost          bool     `json:"isneedpost"`          //是否需要邮寄
		Recipient           string   `json:"recipient"`           //收件人
		Address             string   `json:"address"`             //收件地址
		Mobile              string   `json:"mobile"`              //收件人联系电话
		Expressno           string   `json:"expressno"`           //物流单号
		Expresscode         string   `json:"expresscode"`         //物流公司编码
		Expressstate        int32    `json:"expressstate"`        //物流状态
		Cancelltime         string   `json:"cancelltime"`         //取消时间
		PayTime             string   `json:"paytime"`             //支付时间
		RefundTime          string   `json:"refundtime"`          //退款时间
		Platform            string   `json:"platform"`            //平台名称
		Membermobile        string   `json:"membermobile"`        //用户手机号
		Payway              string   `json:"payway"`              //支付方式
		Expressmoney        int32    `json:"expressmoney"`        //油费
		Ordertypedetail     int32    `json:"ordertypedetail"`     //订单类型小类1-255
		Orderid             string   `json:"orderid"`             //订单号
		Memberid            string   `json:"memberid"`            //用户id
		Ordermoney          string   `json:"ordermoney"`          //订单金额
		Ordertype           int32    `json:"ordertype"`           //订单类型
		Ordertypename       string   `json:"ordertypename"`       //订单类型名称
		Useragent           int32    `json:"useragent"`           //用户useragent
		Orderstate          int32    `json:"orderstate"`          //订单状态
		Orderchildenstate   int32    `json:"orderchildenstate"`   //订单子状态
		Orderdetail         string   `json:"orderdetail"`         //订单详情
		Platformid          int32    `json:"platformid"`          //平台ID
		Belonghospitalid    int32    `json:"belonghospitalid"`    //订单归属门店信息
		Createtime          string   `json:"createtime"`          //订单创建时间
		Lasttime            string   `json:"lasttime"`            //最后更新时间
		Sumquantity         int32    `json:"sumquantity"`         //
		Memberintegrals     []string `json:"memberintegrals"`     //积分明细，暂时不要
		Isevaluate          int32    `json:"isevaluate"`          //是否有价值
		Ispostupet          int32    `json:"ispostupet"`          //是否推送优宠
		Isnotify            int32    `json:"isnotify"`            //是否通知标记
		Id                  string   `json:"id"`                  //子订单号
		Sku                 string   `json:"sku"`                 // skuid
		Goodsid             string   `json:"goodsid"`             //商品货号
		Barcode             string   `json:"barcode"`             //商品编码
		Goodsimage          string   `json:"goodsimage"`          //商品图片地址
		Siglegoodsimage     string   `json:"siglegoodsimage"`     //商品缩略图
		Name                string   `json:"name"`                //商品名称
		Univalence          int32    `json:"univalence"`          //商品原价
		Sellprice           int32    `json:"sellprice"`           //商品售价
		Number              int32    `json:"Number"`              //数量
		PaymentTotal        int32    `json:"paymenttotal"`        //实付金额
		MarkingPrice        int32    `json:"markingPrice"`        //商品原价
		Quantity            int32    `json:"quantity"`            //商品数量
		Unit                string   `json:"unit"`                //商品单位
		Applyhospitalid     string   `json:"applyhospitalid"`     //申请门店id
		Chargeoff           int32    `json:"chargeoff"`           //核销状态 -- 很重要 2-待核销，3-已核销，1-不需要核销，4-已过期
		Chargeoffcode       string   `json:"chargeoffcode"`       //核销码
		Expiredate          string   `json:"expiredate"`          //过期时间
		Chargeoffobject     string   `json:"chargeoffobject"`     //核销对象
		Chargeoffobjectname string   `json:"chargeoffobjectname"` //核销对象名称
		Chargeofftime       string   `json:"chargeofftime"`       //核销时间
		Chargeoffhospitalid string   `json:"chargeoffhospitalid"` //核销医院编号ID
		Chargeoffmemberid   string   `json:"chargeoffmemberid"`   //核销人信息
		Isneedpush          int32    `json:"isneedpush"`          //是否推送
		ChannelId           int32    `json:"channel_id"`          //渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店

		ParentOrderChildstate int32  `json:"parent_order_child_state"` //父订单的子状态
		OldOrderSn            string `json:"old_order_sn"`             //父订单外部订单号
	}
	SplitOrderReq struct {
		Skuid         int32  `json:"skuid"`         //商品SKUID
		Stock         int32  `json:"stock"`         //商品库存
		Isgroup       int32  `json:"isgroup"`       //是否是组合
		Isvirtual     int32  `json:"isvirtual"`     //是否是虚拟
		Groupskuid    int32  `json:"groupskuid"`    //组合的skuid值
		Warehousecode string `json:"warehousecode"` //仓库编号-可仓库id，也可是仓库编号，根据业务场景处理
		Price         int32  `json:"price"`         //商品支付金额
	}
	SplitOrderResp struct {
		Ordersn         string            `json:"ordersn"`           //订单号-加锁，同一订单只能执行一次拆单
		Warehousecode   string            `json:"warehousecode"`     //仓库编号-可仓库id，也可是仓库编号，根据业务场景处理
		SplitOrderGoods []SplitOrderGoods `json:"split_order_goods"` //订单下的商品明细信息
	}
	SplitOrderGoods struct {
		Skuid      int32 `json:"skuid"`      //商品SKUID
		Stock      int32 `json:"stock"`      //商品库存
		Isgroup    int32 `json:"isgroup"`    //是否是组合
		Isvirtual  int32 `json:"isvirtual"`  //是否是虚拟
		Groupskuid int32 `json:"groupskuid"` //组合的skuid值
		Price      int32 `json:"price"`      //商品支付金额
	}

	//第三方
	SplitThirdOrderReq struct {
		OrderProductId int64  `json:"orderProductId"` //商品SKUID
		Skuid          int32  `json:"skuid"`          //商品SKUID
		Stock          int32  `json:"stock"`          //商品库存
		Isgroup        int32  `json:"isgroup"`        //是否是组合
		Isvirtual      int32  `json:"isvirtual"`      //是否是虚拟
		Groupskuid     int32  `json:"groupskuid"`     //组合的skuid值
		Warehousecode  string `json:"warehousecode"`  //仓库编号-可仓库id，也可是仓库编号，根据业务场景处理
		Price          int32  `json:"price"`          //商品支付金额
	}
	SplitThirdOrderResp struct {
		Ordersn         string                 `json:"ordersn"`           //订单号-加锁，同一订单只能执行一次拆单
		Warehousecode   string                 `json:"warehousecode"`     //仓库编号-可仓库id，也可是仓库编号，根据业务场景处理
		SplitOrderGoods []SplitThirdOrderGoods `json:"split_order_goods"` //订单下的商品明细信息
	}
	SplitThirdOrderGoods struct {
		OrderProductId int64 `json:"orderProductId"` //商品SKUID
		Skuid          int32 `json:"skuid"`          //商品SKUID
		Stock          int32 `json:"stock"`          //商品库存
		Isgroup        int32 `json:"isgroup"`        //是否是组合
		Isvirtual      int32 `json:"isvirtual"`      //是否是虚拟
		Groupskuid     int32 `json:"groupskuid"`     //组合的skuid值
		Price          int32 `json:"price"`          //商品支付金额
	}

	DeliverLog struct {
		IsRPOms   bool // 是瑞鹏oms
		Code      string
		Source    int32
		Isfinish  string
		Orderid   string //释放库存
		Goodslist []GoodsId
	}

	GoodsId struct {
		GoodsId string
	}

	//拆单模块
	WarehouseModel struct {
		Id          int    `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
		Thirdid     string `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易id') VARCHAR(32)"`
		Code        string `xorm:"not null default '''' comment('仓库编号') VARCHAR(32)"`
		Name        string `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
		Comefrom    int    `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易)') INT(11)"`
		Level       int    `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
		Category    int    `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)') INT(11)"`
		Arealevel   int    `xorm:"not null default 1 comment('配送区域等级(1-一级，2-二级，3-3级)') INT(11)"`
		Areaid      int    `xorm:"not null default 0 comment('区域id') INT(11)"`
		Enough      bool
		Goodsnumber int
		GoodsType   int
	}

	Wrapper struct {
		warehouse []*WarehouseModel
		by        func(p, q *WarehouseModel) bool
	}

	SortBy func(p, q *WarehouseModel) bool

	OrderSkuWarehouseId struct {
		SkuId       string
		WarehouseId int32
		Number      int32
	}

	MemberIntegralInfo struct {
		Memberid string
		Integral int64
	}
)

func (pw Wrapper) Len() int { // 重写 Len() 方法
	return len(pw.warehouse)
}
func (pw Wrapper) Swap(i, j int) { // 重写 Swap() 方法
	pw.warehouse[i], pw.warehouse[j] = pw.warehouse[j], pw.warehouse[i]
}
func (pw Wrapper) Less(i, j int) bool { // 重写 Less() 方法
	return pw.by(pw.warehouse[i], pw.warehouse[j])
}

// 封装成 SortLog 方法
func SortLog(warehouse []*WarehouseModel, by SortBy) {
	sort.Sort(Wrapper{warehouse, by})
}

//退款审核请求电商参数
type RefundExamineReq struct {
	RefundID     int    `json:"refund_id"`
	AdminMessage string `json:"admin_message"`
	AdminState   int    `json:"admin_state"`
	AdminUser    string `json:"admin_user"`
	RefundAmount int    `json:"refund_amount"`
	AdminType    int    `json:"admin_type"`
}

//退款申请请求电商参数
type RefundApplyReq struct {
	OrderSn      string `json:"order_sn"`
	BuyerMessage string `json:"buyer_message"`
	ApplyType    int    `json:"apply_type"`
	ApplyUser    string `json:"apply_user"`
}

package services

import (
	"context"
	"fmt"
	"order-center/proto/oc"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
)

func TestVipService_GetOrderList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.GetOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.GetOrderListResponse
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "GetOrderList",
			args: args{
				in: &oc.GetOrderListRequest{
					Source:    -1,
					UserId:    "2208378681f945fe90e725ff943a7591",
					PageIndex: 1,
					PageSize:  10,
					Order:     "DESC",
					Expiry:    1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, _ := v.GetOrderList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
		})
	}
}

func TestVipCardOrderService_VipVrRefundDetail(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.VipVrRefundDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.VipVrRefundDetailResp
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "VipVrRefundDetail",
			args: args{
				in: &oc.VipVrRefundDetailReq{
					RefundId: 54282,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.VipVrRefundDetail(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("VipVrRefundDetail(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "VipVrRefundDetail(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_GetVipVrRefundList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.GetVrRefundListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.GetOrderListRequest
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "GetVipVrRefundList",
			args: args{
				in: &oc.GetVrRefundListRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetVipVrRefundList(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("GetVipVrRefundList(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "GetVipVrRefundList(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_VipVrRefundDetail1(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.VipVrRefundDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.VipVrRefundDetailResp
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "VipVrRefundDetail",
			args: args{
				in: &oc.VipVrRefundDetailReq{
					RefundId: 54282,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.VipVrRefundDetail(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("VipVrRefundDetail(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "VipVrRefundDetail(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_DelVipOrderCard(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.DelVipOrderCardRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.VcBaseResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "DelVipOrderCard",
			args: args{
				in: &oc.DelVipOrderCardRequest{
					Id: 14,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.DelVipOrderCard(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("DelVipOrderCard(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "DelVipOrderCard(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_GetOrderOperateLogList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.GetOrderOperateLogListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.GetOrderOperateLogListResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "GetOrderOperateLogList",
			args: args{
				in: &oc.GetOrderOperateLogListRequest{
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetOrderOperateLogList(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("GetOrderOperateLogList(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "GetOrderOperateLogList(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_CreateVipVrRefund(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CreateVrRefundRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CreateVrRefundResponse
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "申请退款"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardOrderService{
				BaseService: tt.fields.BaseService,
			}
			in := oc.CreateVrRefundRequest{}
			in.OrderSn = "****************"
			in.Mobile = "15118811943"
			in.ApplyType = 2
			in.ApplyUser = "zhou"
			in.BuyerMessage = "1111"
			gotOut, err := v.CreateVipVrRefund(context.Background(), &in)
			if !tt.wantErr(t, err, fmt.Sprintf("CreateVipVrRefund(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "CreateVipVrRefund(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestVipCardOrderService_GetPhysicalVipCardOrderList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.GetPhysicalVipCardOrderListRequest
	}
	tests := []struct {
		name    string
		v       *VipCardOrderService
		args    args
		wantOut *oc.GetPhysicalVipCardOrderListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			v:    &VipCardOrderService{},
			args: args{
				ctx: context.Background(),
				in: &oc.GetPhysicalVipCardOrderListRequest{
					//OrderSn: "****************",
					//OrderState:       0,
					PageIndex: 2,
					PageSize:  2,
					// PaymentTimeStart: "2010-01-01 01:00:00",
					// PaymentTimeEnd:   "2023-08-28 23:59:59",
					// MemberMobile:     "18870070534",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.v.GetPhysicalVipCardOrderList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("VipCardOrderService.GetPhysicalVipCardOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("VipCardOrderService.GetPhysicalVipCardOrderList() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardOrderService_PVCExpressImport(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.PVCExpressImportRequest
	}
	tests := []struct {
		name    string
		v       *VipCardOrderService
		args    args
		wantOut *oc.PVCExpressImportResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		// {
		// 	name: "导入运单号",
		// 	v:    &VipCardOrderService{},
		// 	args: args{
		// 		ctx: context.Background(),
		// 		in:  &oc.PVCExpressImportRequest{}
		// 	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.v.PVCExpressImport(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("VipCardOrderService.PVCExpressImport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("VipCardOrderService.PVCExpressImport() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardOrderService_PVCExpressImportTemplate(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.PVCExpressImportTemplateRequest
	}
	tests := []struct {
		name    string
		v       *VipCardOrderService
		args    args
		wantOut *oc.PVCExpressImportTemplateResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aa",
			v:    &VipCardOrderService{},
			args: args{
				ctx: context.Background(),
				in:  &oc.PVCExpressImportTemplateRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.v.PVCExpressImportTemplate(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("VipCardOrderService.PVCExpressImportTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("VipCardOrderService.PVCExpressImportTemplate() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardOrderService_PhysicalVipCardOrderExportList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.PhysicalVipCardOrderExportListRequest
	}
	tests := []struct {
		name    string
		v       *VipCardOrderService
		args    args
		wantOut *oc.PhysicalVipCardOrderExportListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "l",
			v:    &VipCardOrderService{},
			args: args{
				ctx: context.Background(),
				in: &oc.PhysicalVipCardOrderExportListRequest{
					PageIndex: 1,
					PageSize:  2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.v.PhysicalVipCardOrderExportList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("VipCardOrderService.PhysicalVipCardOrderExportList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("VipCardOrderService.PhysicalVipCardOrderExportList() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardOrderService_PVCEdit(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.PVCEditRequest
	}
	tests := []struct {
		name    string
		v       *VipCardOrderService
		args    args
		wantOut *oc.PVCEditResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			v:    new(VipCardOrderService),
			args: args{
				ctx: context.Background(),
				in: &oc.PVCEditRequest{
					OrderSn: "****************",
					//VirtualCard: "FY1002391487",
					VirtualCard: "FY1002391376",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.v.PVCEdit(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("VipCardOrderService.PVCEdit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("VipCardOrderService.PVCEdit() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

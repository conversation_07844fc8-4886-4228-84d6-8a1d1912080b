package services

//
////拆单第一步 保存原订单
//func splitStep1(session *xorm.Session, params *oc.NewAddOrderRequest) (errmsg string) {
//	OrderSn, err := strconv.ParseInt(params.Order.OrderSn, 10, 64)
//	if err != nil {
//		return err.Error()
//	}
//	BuyerId, err := strconv.Atoi(params.Order.BuyerId)
//	if err != nil {
//		return err.Error()
//	}
//	BuyerPhone, err := strconv.ParseInt(params.Order.BuyerPhone, 10, 64)
//	if err != nil {
//		return err.Error()
//	}
//
//	orderModel := models.UpetOrders{
//		OrderId:     int(params.Order.OrderId),
//		OrderSn:     OrderSn,
//		StoreId:     int(params.Order.StoreId),
//		StoreName:   params.Order.StoreName,
//		BuyerId:     BuyerId,
//		BuyerName:   params.Order.BuyerName,
//		BuyerEmail:  params.Order.BuyerEmail,
//		BuyerPhone:  BuyerPhone,
//		AddTime:     int(time.Now().Unix()),
//		PaymentCode: params.Order.PaymentCode,
//		GoodsAmount: int(params.Order.GoodsAmount),
//		OrderAmount: int(params.Order.OrderAmount),
//		ShippingFee: int(params.Order.ShippingFee),
//		OrderState:  int(params.Order.OrderState),
//		OrderFrom:   int(params.Order.OrderFrom),
//		OrderType:   int(params.Order.OrderType),
//		ChainId:     int(params.Order.ChainId),
//		RptAmount:   int(params.Order.RptAmount),
//		IsDis:       int(params.Order.IsDis),
//	}
//
//	CommonModel := models.UpetOrderCommon{
//		OrderId:        int(params.Common.OrderId),
//		StoreId:        int(params.Common.StoreId),
//		OrderMessage:   params.Common.OrderMessage,
//		ReciverName:    params.Common.ReciverName,
//		ReciverInfo:    params.Common.ReciverInfoSerialize,
//		ReciverCityId:  int(params.Common.ReciverCityId),
//		InvoiceInfo:    params.Common.InvoiceInfo,
//		PromotionInfo:  params.Common.PromotionInfo,
//		PromotionTotal: int(params.Common.PromotionTotal),
//		ReciverDateMsg: params.Common.ReciverDateMsg,
//	}
//
//	orderProduct := []*models.UpetOrderGoods{}
//	for _, i2 := range params.OrderGoods {
//
//		RecId, err := strconv.Atoi(i2.RecId)
//		if err != nil {
//			return err.Error()
//		}
//		GoodsId, err := strconv.Atoi(i2.GoodsId)
//		if err != nil {
//			return err.Error()
//		}
//		BuyerId, err := strconv.Atoi(i2.BuyerId)
//		if err != nil {
//			return err.Error()
//		}
//		GcId, err := strconv.Atoi(i2.GcId)
//		if err != nil {
//			return err.Error()
//		}
//
//		GoodsCommonid, err := strconv.Atoi(i2.GoodsCommonid)
//		if err != nil {
//			return err.Error()
//		}
//		orderProduct = append(orderProduct, &models.UpetOrderGoods{
//			RecId:           RecId,
//			OrderId:         int(i2.OrderId),
//			GoodsId:         GoodsId,
//			GoodsName:       i2.GoodsName,
//			GoodsPrice:      int(i2.GoodsPrice),
//			GoodsNum:        int(i2.GoodsNum),
//			GoodsImage:      i2.GoodsImage,
//			GoodsPayPrice:   int(i2.GoodsPayPrice),
//			StoreId:         int(i2.StoreId),
//			BuyerId:         BuyerId,
//			GoodsType:       int(i2.GoodsType),
//			PromotionsId:    int(i2.PromotionsId),
//			CommisRate:      int(i2.CommisRate),
//			GcId:            GcId,
//			GoodsSpec:       i2.GoodsSpec,
//			GoodsContractid: i2.GoodsContractid,
//			GoodsCommonid:   GoodsCommonid,
//			AddTime:         int(i2.AddTime),
//			IsDis:           int(i2.IsDis),
//			DisCommisRate:   int(i2.DisCommisRate),
//			DisMemberId:     int(i2.DisMemberId),
//			ChainId:         int(i2.ChainId),
//			Sku:             i2.Sku,
//		})
//	}
//	_, err = session.Insert(orderModel, CommonModel, orderProduct)
//	if err != nil {
//		glog.Error("原订单保存失败！", err.Error())
//		return ("原订单保存失败！")
//	}
//
//	return ""
//}
//
////查询仓库 根据仓库重组订单
//func splitStep2(params *oc.NewAddOrderRequest) ([]Order, string) {
//	client := dc.GetDcDispatchClient()
//
//	var goods []*dc.OrderGoodsDetail
//	currentTime := time.Now().Local()
//
//	newFormat := kit.GetTimeNow(currentTime)
//	for _, pro := range params.OrderGoods {
//		goods = append(goods, &dc.OrderGoodsDetail{
//			Id:         pro.RecId,
//			Orderid:    params.Order.OrderSn,
//			Goodsid:    pro.Sku,
//			Name:       pro.GoodsName,
//			Univalence: pro.GoodsPrice,
//			Sellprice:  pro.GoodsPrice,
//			Quantity:   pro.GoodsNum,
//			Lasttime:   newFormat,
//		})
//	}
//
//	area_arr := strings.Split(params.Common.ReciverInfo.Area, " ")
//	Receiverstate := area_arr[0]
//	if area_arr[0] == "" {
//		Receiverstate = params.Common.ReciverInfo.Address
//	}
//
//	DemoRequest := dc.DemolitionOrderRequest{
//		Id:                int64(params.Order.OrderId),
//		Orderid:           params.Order.OrderSn,
//		Memberid:          params.Order.BuyerId,
//		Ordermoney:        params.Order.OrderAmount,
//		Ordersource:       1,
//		OrderGoodsDetails: goods,
//		Province:          Receiverstate,
//		Createtime:        newFormat,
//		Lasttime:          newFormat,
//	}
//
//	glog.Info("拆分仓库请求参数：", params.Order.OrderSn, kit.JsonEncode(DemoRequest))
//	//拆分库存
//	grpcRes, err := client.Dosc.DemolitionOrder(client.Ctx, &DemoRequest)
//	if err != nil {
//		return nil, err.Error()
//	}
//	if grpcRes == nil {
//		return nil, "拆分库存返回错误！"
//	}
//
//	glog.Info("拆分库存返回参数：ordersn:", params.Order.OrderSn, kit.JsonEncode(grpcRes))
//	//找不到仓库
//	if grpcRes.Code == 400 {
//		return nil, grpcRes.Message
//	}
//
//	if len(grpcRes.WarehouseToGoodsList) == 0 {
//		return nil, "未找到仓库信息"
//	}
//
//	//根据返回仓库组合同仓库为一个订单，通过商品购买数量与赠品数量拆分普通商品和赠品
//	demoorder := splitGift(grpcRes, params)
//
//	return demoorder, ""
//}
//
////拆单第三步 计算新订单金额，邮费，优惠 记录明细
//func splitSep3(session *xorm.Session, demoorder []Order, params *oc.NewAddOrderRequest) ([]*oc.DemoOrder, string) {
//	var AddOrders []*oc.DemoOrder
//
//	//找出赠品recid
//	recId := []string{}
//	for _, i2 := range params.OrderGoods {
//		if i2.GoodsPrice == 0 {
//			recId = append(recId, i2.RecId)
//		}
//	}
//	//不重複仓库id，用于确定最后一个非赠品订单，便于分摊邮费和优惠
//	var ware []interface{}
//	for _, i2 := range demoorder {
//		for _, i3 := range i2.Goods {
//			if !utils.InSlice(i3.RecId, recId) && !utils.InSlice(int(i2.WarehouseId), ware) {
//				ware = append(ware, int(i2.WarehouseId))
//			}
//		}
//	}
//
//	var PromotionTotalT int32 //已拆分优惠金额
//	var FreightTotalT int32   //已拆分邮费
//	for _, i2 := range demoorder {
//		var newOrderProduct []models.OrderProduct
//		//计算订单总金额
//		var goodsTotal int32
//		var orderPayableAmount int32
//
//		OrderGoodss := []*oc.OrderGoods{}
//		//生成订单号
//		rand.Seed(int64(utils.HashInt(kit.GetGuid32())))
//		random := rand.Intn(899) + 100
//		OrderSn := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
//
//		for _, good := range i2.Goods {
//			v := findOrderProduct(good, params.OrderGoods)
//			if good.Num == 0 {
//				return nil, "拆单商品数量为0，没有找到对应商品！"
//			}
//
//			if v == nil {
//				return nil, "拆单商品未找到！"
//			}
//
//			//总金额
//			var totalPrice int32
//			totalPrice = v.GoodsPrice * good.Num
//			goodsTotal += totalPrice
//
//			//订单总金额
//			orderPayableAmount += totalPrice
//			var orderProductModel models.OrderProduct
//			orderProductModel = models.OrderProduct{
//				OrderSn:       OrderSn,
//				SkuId:         v.Sku,
//				ProductId:     v.GoodsCommonid,
//				ProductName:   v.GoodsName,
//				DiscountPrice: v.GoodsPrice,
//				Number:        good.Num,
//				Specs:         v.GoodsSpec,
//				MarkingPrice:  v.GoodsPrice,
//				Image:         v.GoodsImage,
//			}
//
//			//返回参数 修改数量，总价，邮费，优惠金额
//			OrderGoods := *v
//			//OrderGoods.OcId = Id
//			OrderGoods.GoodsNum = good.Num
//			OrderGoods.GoodsPrice = v.GoodsPrice
//			OrderGoodss = append(OrderGoodss, &OrderGoods)
//			newOrderProduct = append(newOrderProduct, orderProductModel)
//		}
//		//已拆订单
//		area_arr := strings.Split(params.Common.ReciverInfo.Area, " ")
//
//		//地址必须有省市区三级，要不然不能推全渠道，可能存在两级的情况，默认三级等于二级
//		if len(area_arr) < 2 {
//			return nil, "地址信息有误，请您联系客服！"
//		}
//		Receiverstate := area_arr[0]
//		if area_arr[0] == "" {
//			Receiverstate = params.Common.ReciverInfo.Address
//		}
//		Receivercity := area_arr[1]
//		if area_arr[1] == "" {
//			Receivercity = params.Common.ReciverInfo.Address
//		}
//
//		Receiverdistrict := ""
//		if len(area_arr) > 2 {
//			Receiverdistrict = area_arr[2]
//		} else {
//			Receiverdistrict = Receivercity
//		}
//
//		//分摊订单邮费、优惠
//		Amount := params.Order.OrderAmount + params.Common.PromotionTotal - params.Order.ShippingFee
//		var PromotionTotal int32
//		var FreightTotal int32
//		if len(ware) > 0 && i2.WarehouseId == ware[len(ware)-1] {
//			FreightTotal = params.Order.ShippingFee - FreightTotalT
//		} else {
//			var first float64
//			if Amount > 0 {
//				first = float64(goodsTotal) / float64(Amount)
//			} else {
//				first = 0
//			}
//			second := kit.FenToYuan(params.Order.ShippingFee)
//			FreightTotal = int32(utils.ConvertDoubleFloat(first * second * 100))
//			FreightTotalT += FreightTotal
//		}
//		if len(ware) > 0 && i2.WarehouseId == ware[len(ware)-1] {
//			PromotionTotal = params.Common.PromotionTotal - PromotionTotalT
//		} else {
//			var first float64
//			if Amount > 0 {
//				first = float64(goodsTotal) / float64(Amount)
//			} else {
//				first = 0
//			}
//			second := kit.FenToYuan(params.Common.PromotionTotal)
//			PromotionTotal = int32(utils.ConvertDoubleFloat(first * second * 100))
//			PromotionTotalT += PromotionTotal
//		}
//
//		//渠道表 UA（useragent） 1-Android 2-iOS 3-小程序 4-公众号 5-Web 6-其它
//		var userAgent int32
//		switch params.Order.OrderFrom {
//		case 1:
//			userAgent = 5
//		case 2:
//			userAgent = 4
//		case 5:
//			userAgent = 6
//		case 7:
//			userAgent = 3
//		case 8:
//			userAgent = 3
//		default:
//			userAgent = 6
//		}
//
//		//取来源
//		Source := OrderGoodss[0].Source
//		orderModel := &models.OrderMain{
//			OldOrderSn:       params.Order.OrderSn,
//			OrderSn:          OrderSn,
//			OrderStatus:      10,    //默认未付款
//			OrderStatusChild: 10201, //默认未付款
//			ShopName:         params.Order.StoreName,
//			MemberId:         params.Order.BuyerId,
//			MemberName:       params.Order.BuyerName,
//			MemberTel:        params.Order.BuyerPhone,
//			ReceiverName:     params.Common.ReciverName,
//			ReceiverState:    Receiverstate,
//			ReceiverCity:     Receivercity,
//			ReceiverDistrict: Receiverdistrict,
//			ReceiverAddress:  params.Common.ReciverInfo.Address,
//			ReceiverPhone:    params.Common.ReciverInfo.TelPhone,
//			Privilege:        PromotionTotal,
//			ReceiverMobile:   params.Common.ReciverInfo.MobPhone,
//			Total:            orderPayableAmount - PromotionTotal + FreightTotal,
//			GoodsTotal:       goodsTotal - PromotionTotal,
//			CreateTime:       time.Now(),
//			OrderType:        params.Order.OrderType,
//			Freight:          FreightTotal,
//			Source:           Source,
//			WarehouseCode:    i2.WarehouseCode,
//			DeliveryType:     1,
//			ChannelId:        5,
//			UserAgent:        userAgent,
//		}
//
//		orderDetailModel := &models.OrderDetail{
//			PayType: "NoCod",
//			//GjpStatus:        "NoPay",
//			Invoice: params.Common.InvoiceInfo,
//		}
//
//		Order := *params.Order
//		Order.ShippingFee = FreightTotal
//		Order.GoodsAmount = goodsTotal - PromotionTotal
//		Order.OrderAmount = orderPayableAmount - PromotionTotal + FreightTotal
//		Order.OrderSn = OrderSn
//		Common := *params.Common
//		Common.PromotionTotal = PromotionTotal
//		protoDemoOrder := oc.DemoOrder{
//			Order:      &Order,
//			Common:     &Common,
//			OrderGoods: OrderGoodss,
//		}
//
//		//分摊商品邮费、优惠
//		var ProFreightTotalT int32
//		var ProPromotionTotalT int32
//		//找出全部非赠品id
//		var DiffSkus []string
//		for _, i2 := range protoDemoOrder.OrderGoods {
//			if i2.GoodsPrice > 0 && !utils.InSlice(i2.RecId, DiffSkus) {
//				DiffSkus = append(DiffSkus, i2.RecId)
//			}
//		}
//		for i4, i5 := range protoDemoOrder.OrderGoods {
//			Amount = protoDemoOrder.Order.OrderAmount + protoDemoOrder.Common.PromotionTotal - protoDemoOrder.Order.ShippingFee
//			goodsTotal = i5.GoodsPrice * i5.GoodsNum
//			var ProPromotionTotal int32
//			var ProFreightTotal int32
//			if len(DiffSkus) > 0 && i5.RecId == DiffSkus[len(DiffSkus)-1] {
//				ProFreightTotal = protoDemoOrder.Order.ShippingFee - ProFreightTotalT
//			} else {
//				var first float64
//				if Amount > 0 {
//					first = float64(goodsTotal) / float64(Amount)
//				} else {
//					first = 0
//				}
//				second := kit.FenToYuan(protoDemoOrder.Order.ShippingFee)
//				ProFreightTotal = int32(utils.ConvertDoubleFloat(first * second * 100))
//				ProFreightTotalT += ProFreightTotal
//			}
//
//			if len(DiffSkus) > 0 && i5.RecId == DiffSkus[len(DiffSkus)-1] {
//				ProPromotionTotal = protoDemoOrder.Common.PromotionTotal - ProPromotionTotalT
//			} else {
//				var first float64
//				if Amount > 0 {
//					first = float64(goodsTotal) / float64(Amount)
//				} else {
//					first = 0
//				}
//				second := kit.FenToYuan(protoDemoOrder.Common.PromotionTotal)
//				ProPromotionTotal = int32(utils.ConvertDoubleFloat(first * second * 100))
//				ProPromotionTotalT += ProPromotionTotal
//			}
//			//修改商品返回参实际成交价、邮费及优惠
//			protoDemoOrder.OrderGoods[i4].GoodsPayPrice = goodsTotal - ProPromotionTotal
//			for i6, i7 := range newOrderProduct {
//				if cast.ToString(i7.Id) != i5.OcId {
//					continue
//				}
//				newOrderProduct[i6].Freight = ProFreightTotal
//				newOrderProduct[i6].Privilege = ProPromotionTotal      //商家优惠
//				newOrderProduct[i6].PrivilegeTotal = ProPromotionTotal //总优惠
//				newOrderProduct[i6].PaymentTotal = i7.DiscountPrice*i7.Number - ProPromotionTotal
//			}
//		}
//
//		AddOrders = append(AddOrders, &protoDemoOrder)
//		_, err := session.Insert(orderModel, orderDetailModel, &newOrderProduct)
//		if err != nil {
//			glog.Error("生成订单失败err : ", err.Error())
//			return nil, "生成订单失败"
//		}
//	}
//
//	return AddOrders, ""
//}
//
////管易 组合仓库数据
//func splitStep4(params *oc.NewAddOrderRequest) ([]Order, string) {
//	//组合拆库数据
//	grpcRes := &dc.DemolitionOrderResponse{}
//	grpcRes.Code = 200
//	for _, i2 := range params.OrderGoods {
//		grpcRes.WarehouseToGoodsList = append(grpcRes.WarehouseToGoodsList, &dc.WarehouseToGoods{
//			Warehouseid:   62, //默认管易仓库id
//			Orderid:       params.Order.OrderSn,
//			Quantity:      i2.GoodsNum,
//			Goodsid:       i2.Sku,
//			Thirdid:       "2",          //全渠道1   管易2
//			Warehousecode: "JCJCSCK001", //默认仓库管易代码
//		})
//	}
//
//	//根据返回仓库组合同仓库为一个订单，通过商品购买数量与赠品数量拆分普通商品和赠品
//	demoorder := splitGift(grpcRes, params)
//
//	return demoorder, ""
//}
//
////拆分全渠道、管易订单
//func distinguishOrder(params *oc.NewAddOrderRequest) (*oc.NewAddOrderRequest, *oc.NewAddOrderRequest) {
//	var OrderNum int32
//	var GyOrderNum int32
//	var GjpOrderNum int32
//	order := *params.Order
//	common := *params.Common
//
//	gpjorder := *params.Order
//	gpjcommon := *params.Common
//	GyOrder := &oc.NewAddOrderRequest{
//		Order:      &order,
//		Common:     &common,
//		OrderGoods: []*oc.OrderGoods{},
//	}
//
//	GjpOrder := &oc.NewAddOrderRequest{
//		Order:      &gpjorder,
//		Common:     &gpjcommon,
//		OrderGoods: []*oc.OrderGoods{},
//	}
//
//	GyOrder.Order.ShippingFee = 0
//	GyOrder.Common.PromotionTotal = 0
//	GjpOrder.Order.ShippingFee = 0
//	GjpOrder.Common.PromotionTotal = 0
//	GyProducts := []*oc.OrderGoods{}
//	GjpProducts := []*oc.OrderGoods{}
//	GyOrder.Order.OrderAmount = 0
//	GjpOrder.Order.OrderAmount = 0
//	for _, i2 := range params.OrderGoods {
//		OrderNum += i2.GoodsNum
//		if i2.Source == 1 {
//			GjpProducts = append(GjpProducts, i2)
//			GjpOrderNum += i2.GoodsNum
//			GjpOrder.Order.OrderAmount += i2.GoodsNum * i2.GoodsPrice
//		}
//		if i2.Source == 2 {
//			GyProducts = append(GyProducts, i2)
//			GyOrderNum += i2.GoodsNum
//			GyOrder.Order.OrderAmount += i2.GoodsNum * i2.GoodsPrice
//		}
//	}
//
//	//管易邮费均摊  管易订单总金额 / 订单总金额 * 邮费
//	Amount := params.Order.GoodsAmount + params.Common.PromotionTotal
//
//	GyOrder.Order.ShippingFee = int32(kit.YuanToFen(float64(GyOrder.Order.OrderAmount) / float64(Amount) * kit.FenToYuan(int64(params.Order.ShippingFee))))
//	GjpOrder.Order.ShippingFee = params.Order.ShippingFee - GyOrder.Order.ShippingFee
//
//	//全渠道邮费均摊  1 - 管易订单总金额 / 订单总金额 * 邮费
//
//	GyOrder.Common.PromotionTotal = int32(kit.YuanToFen(float64(GyOrder.Order.OrderAmount) / float64(Amount) * kit.FenToYuan(int64(params.Common.PromotionTotal))))
//	GjpOrder.Common.PromotionTotal = params.Common.PromotionTotal - GyOrder.Common.PromotionTotal
//
//	GyOrder.OrderGoods = GyProducts
//	GjpOrder.OrderGoods = GjpProducts
//
//	return GyOrder, GjpOrder
//}
//
//func splitGift(grpcRes *dc.DemolitionOrderResponse, params *oc.NewAddOrderRequest) []Order {
//	var demoorder []Order
//	//将拆分仓库后的相同仓的商品组合在一起
//	for _, i2 := range grpcRes.WarehouseToGoodsList {
//		if !demoIn(i2.Warehouseid, demoorder) {
//			id := Order{}
//			good := Goods{
//				Id:    i2.Goodsid,
//				Num:   i2.Quantity,
//				Allot: i2.Quantity,
//			}
//			id.Goods = append(id.Goods, good)
//			demoorder = append(demoorder, Order{
//				WarehouseId:   i2.Warehouseid,
//				WarehouseCode: i2.Warehousecode,
//				Orderid:       i2.Orderid,
//				Num:           i2.Quantity,
//				Thirdid:       i2.Thirdid,
//				Goods:         id.Goods,
//			})
//		} else {
//			for i1, i3 := range demoorder {
//				if i3.WarehouseId == i2.Warehouseid {
//					good := Goods{
//						Id:    i2.Goodsid,
//						Num:   i2.Quantity,
//						Allot: i2.Quantity,
//					}
//					demoorder[i1].Num = demoorder[i1].Num + i2.Quantity
//					demoorder[i1].Goods = append(demoorder[i1].Goods, good)
//				}
//			}
//		}
//	}
//
//	//保存主键id 对应数量
//	m02 := make(map[string]int32)
//	for _, i4 := range params.OrderGoods {
//		m02[i4.RecId] += i4.GoodsNum
//	}
//
//	//根据价格分配数量及赋上rec_id
//	for _, i2 := range params.OrderGoods {
//		for i1, demo := range demoorder {
//			for k2, i3 := range demo.Goods {
//				if i3.Id == i2.Sku {
//					if i3.Allot == 0 || m02[i2.RecId] == 0 { //此订单已分配完或全部商品数量分配完跳过
//						continue
//					} else if i3.Allot > m02[i2.RecId] && m02[i2.RecId] > 0 { //此订单分配数量大于全部商品数量时
//						if i3.RecId == "" { ////RecId为空时赋值，反之新增
//							demoorder[i1].Goods[k2].Num = m02[i2.RecId]
//							demoorder[i1].Goods[k2].RecId = i2.RecId
//						} else {
//							good := Goods{
//								Id:    i3.Id,
//								Num:   m02[i2.RecId],
//								RecId: i2.RecId,
//								Allot: 0,
//							}
//							demoorder[i1].Goods = append(demoorder[i1].Goods, good)
//						}
//
//						demoorder[i1].Goods[k2].Allot -= m02[i2.RecId] //分配完减去分配值
//						m02[i2.RecId] = 0                              //已分配完
//					} else if i3.Allot == m02[i2.RecId] { //当此订单分配数等于所有未分配数量时
//						if i3.RecId == "" { //RecId为空时赋值，反之新增
//							demoorder[i1].Goods[k2].Num = m02[i2.RecId]
//							demoorder[i1].Goods[k2].RecId = i2.RecId
//							demoorder[i1].Goods[k2].Allot -= m02[i2.RecId] //分配完减去分配值
//						} else {
//							demoorder[i1].Goods[k2].Allot -= m02[i2.RecId] //分配完减去分配值
//							good := Goods{
//								Id:    i3.Id,
//								Num:   m02[i2.RecId],
//								RecId: i2.RecId,
//								Allot: 0,
//							}
//							demoorder[i1].Goods = append(demoorder[i1].Goods, good)
//						}
//						m02[i2.RecId] = 0
//					} else {
//						if i3.Allot > m02[i2.RecId] {
//							good := Goods{
//								Id:    i3.Id,
//								Num:   m02[i2.RecId],
//								RecId: i2.RecId,
//							}
//							demoorder[i1].Goods = append(demoorder[i1].Goods, good)
//							m02[i2.RecId] = 0
//							demoorder[i1].Goods[k2].Allot -= i3.Allot
//						} else {
//							//RecId为空时赋值，反之新增
//							if i3.RecId != "" {
//								good := Goods{
//									Id:    i3.Id,
//									Num:   i3.Allot,
//									RecId: i2.RecId,
//								}
//								demoorder[i1].Goods = append(demoorder[i1].Goods, good)
//							} else {
//								demoorder[i1].Goods[k2].Num = i3.Allot
//								demoorder[i1].Goods[k2].RecId = i2.RecId
//							}
//
//							m02[i2.RecId] -= i3.Allot
//							demoorder[i1].Goods[k2].Allot = 0
//						}
//					}
//				}
//			}
//		}
//	}
//
//	return demoorder
//}
//
////根据返回仓库组合同仓库为一个订单，
////通过商品购买数量与赠品数量拆分普通商品和赠品
//func demoIn(id int32, demo []Order) bool {
//	for _, i2 := range demo {
//		if i2.WarehouseId == id {
//			return true
//		}
//	}
//	return false
//}

package models

import (
	oc "order-center/proto/oc"
)

type Basecity struct {
	Id       int    `xorm:"not null pk autoincr INT(11)"`
	Parentid int    `xorm:"not null INT(11)"`
	Cityname string `xorm:"default 'NULL' LONGTEXT"`
	Path     string `xorm:"default 'NULL' index VARCHAR(25)"`
}

func (city *Basecity) ToSectionDto() *oc.SectionDto {
	var dto = new(oc.SectionDto)
	dto.Id = int32(city.Id)
	dto.ParentId = int32(city.Parentid)
	dto.SectionName = city.Cityname
	return dto
}

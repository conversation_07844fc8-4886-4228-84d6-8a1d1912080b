package models

import "time"

type HealthDetail struct {
	Id           int32     `json:"id"`
	UserId       string    `json:"user_id"`       // 用户scrm_user_id
	Type         int8      `json:"type"`          // 默认0,1=收入，2=支出 3=冻结
	Title        string    `json:"title"`         // 标题
	Content      string    `json:"content"`       // 明细说明
	PayAmount    float64   `json:"pay_amount"`    // 支付金额
	RefundAmount float64   `json:"refund_amount"` // 退款金额
	OrderSn      string    `json:"order_sn"`
	ShopId       string    `json:"shop_id"`
	ShopName     string    `json:"shop_name"`
	HealthVal    int32     `json:"health_val"`  // 健康值
	HealthType   int32     `json:"health_type"` // 健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效 5退款
	EffectTime   time.Time `json:"effect_time"` // 生效时间
	CreateTime   time.Time `xorm:"<-" json:"create_time"`
	UpdateTime   time.Time `xorm:"<-" json:"update_time"`
}

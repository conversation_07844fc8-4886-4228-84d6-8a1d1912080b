package models

import (
	"_/proto/pc"
	"time"
)

type ChannelProductRequestEs struct {
	Product     *ChannelProductEs `json:"product"`      //商品主表
	SkuInfo     []*SkuInfoEs      `json:"sku_info"`     //SKU信息
	FinanceCode string            `json:"finance_code"` //财务编码
	WarehouseId int32             `json:"warehouse_id"` //仓库id
	Tags        string            `json:"tags"`         //商品标签
	Cities      []string          `json:"cities"`       //商品适用城市，仅虚拟商品有效
	RegionId    int32             `json:"region_id"`    //大区标识ID，电商商品用
	UpdateDate  string            `json:"update_date"`  //最后更新时间
	// HasStockNew       int32             `json:"has_stock_new"`      // 是否有库存
	WarehouseCategory int32 `json:"warehouse_category"` // 仓库类型

}

type ChannelProductEs struct {
	Id                   int32  `json:"id"`                     //商品id（spu）
	Name                 string `json:"name"`                   //商品名称
	ShortName            string `json:"short_name"`             //商品短标题
	Pic                  string `json:"pic"`                    //商品图片（多图）
	SellingPoint         string `json:"selling_point"`          //商品卖点
	ChannelId            string `json:"channel_id"`             //渠道id
	ChannelCategoryId    int32  `json:"channel_category_id"`    //渠道的分类id
	ChannelCategoryName  string `json:"channel_category_name"`  //渠道的分类名称
	IsVirtual            int32  `json:"is_virtual"`             //虚拟商品，1是，0否  //TODO IsVirtual不再使用，换成ProductType
	ProductType          int32  `json:"product_type"`           //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
	TermType             int32  `json:"term_type"`              //只有虚拟商品才有值(1.有效期至多少  2.有效期天数)
	TermValue            int32  `json:"term_value"`             //如果term_type=1 存：时间戳  如果term_type=2 存多少天
	VirtualInvalidRefund int32  `json:"virtual_invalid_refund"` // 是否支持过期退款 1：是 0：否
	IsVip                int32  `json:"is_vip"`                 //是否是VIP商品

}

type SkuInfoEs struct {
	SkuId          int32         `json:"sku_id"`          //SKU ID
	MarketPrice    int32         `json:"market_price"`    //市场价
	RetailPrice    int32         `json:"retail_price"`    //建议价格
	MemberPrice    int32         `json:"member_price"`    //会员价
	SalesVolume    int32         `json:"sales_volume"`    //销量
	PromotionType  int32         `json:"promotion_type"`  //活动促销类型
	PromotionPrice int32         `json:"promotion_price"` //促销价
	Skuv           []*SkuvEs     `json:"skuv"`            //规格
	WeightForUnit  float64       `json:"weight_for_unit"` //重量 KG
	SkuGroup       []*SkuGroupEs `json:"sku_group"`       //组合商品的明细sku
}

type SkuvEs struct {
	SpecName       string `json:"spec_name"`        //规格名
	SpecValueValue string `json:"spec_value_value"` //规格值

}

type ChannelProductEsBaseData struct {
	JsonData    string    `json:"json_data"`
	FinanceCode string    `json:"finance_code"`
	ChannelId   string    `json:"channel_id"`
	SkuId       int32     `json:"sku_id"`
	UpDownState int32     `json:"up_down_state"`
	SalesVolume int32     `json:"sales_volume"`
	Tags        string    `json:"tags"`
	UpdateDate  time.Time `json:"update_date"`
}

type SkuGroupEs struct {
	ProductId      int32                     `json:"product_id"`       //商品ID
	SkuId          int32                     `json:"sku_id"`           //SKU
	GroupProductId int32                     `json:"group_product_id"` //组合的商品ID
	GroupSkuId     int32                     `json:"group_sku_id"`     //组合的商品SKUID
	Count          int32                     `json:"count"`            //组合的商品数量
	DiscountType   int32                     `json:"discount_type"`    // 折扣类型(1-按折扣优惠，2-按固定价格优惠)
	DiscountValue  int32                     `json:"discount_price"`   // 折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）
	MarketPrice    int32                     `json:"market_price"`     // 市场价
	ProductType    int32                     `json:"product_type"`     // 商品类型（1-实物商品，2-虚拟商品，3-组合商品）
	ChannelId      int32                     `json:"channel_id"`       // 渠道id
	ProductName    string                    `json:"product_name"`     // 商品名称
	Snapshot       *pc.ChannelProductRequest `json:"snapshot"`         // 子商品快照
}

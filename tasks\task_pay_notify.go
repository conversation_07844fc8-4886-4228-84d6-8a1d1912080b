package tasks

import (
	"encoding/json"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	os "order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type PayNotify struct {
	services.AllChannelService
	services.OmsService
	services.BaseService
}

//订阅支付通知消息
func subPayMQ() {
	queue := "ds-sz-realorder-payinfo"
	exchange := "ordercenter"
	defer kit.CatchPanic()

	glog.Info(fmt.Sprintf("订阅MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := utils.NewMqConn()
	defer conn.Close()
	ch := utils.NewMqChannel(conn)
	defer ch.Close()

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
	}
	// name// durable// delete when unused // exclusive//no-wait //arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		glog.Error("RabbitMQ Consume fail, err : ", q.Name, err)
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error("收到支付通知错误:", fmt.Sprintf("%s", err), " ", string(d.Body))
						d.Reject(true)
					}
				}()
				//解析内容
				glog.Infof("收到支付通知：ordersn:%s ", string(d.Body))
				SaveLog("SubPayMQ", string(d.Body))
				var Payed Payed
				if err := json.Unmarshal(d.Body, &Payed); err != nil {
					glog.Error(err)
					SaveLog("SubPayMQ2", err.Error())
					panic(err)

				}

				s := PayNotify{}
				ok, err := s.pay(&Payed)

				if ok {
					d.Ack(false) //用完删除信息
					var errStr string
					if err != nil {
						errStr = err.Error()
					}
					SaveLog("SubPayMQ10", "推送结束mq已答应！"+Payed.OrderSn+errStr)
				} else {
					var errStr string
					if err != nil {
						errStr = err.Error()
					}
					SaveLog("SubPayMQ10", "推送结束mq未答应"+Payed.OrderSn+errStr)
					//d.Ack(false) //用完删除信息
					panic(err)
				}
			}()
		}
	}
}

type Payed struct {
	OrderSn     string `json:"order_sn"`
	PaymentCode string `json:"payment_code"`
	PaymentTime string `json:"payment_time"`
}

//支付通知后的逻辑
func (s *PayNotify) pay(Payed *Payed) (bool, error) {
	defer func() {
		if r := recover(); r != nil {
			SaveLog("SubPayMQ30", fmt.Sprintf("%s", r))
		}
	}()

	db := services.GetDBConn()

	var orderModel []models.OrderMain
	err := db.Where("old_order_sn = ? and order_status = 10", Payed.OrderSn).Find(&orderModel)
	if err != nil {
		SaveLog("SubPayMQ3", err.Error()+Payed.OrderSn)
		return true, err
	}
	if len(orderModel) == 0 {
		SaveLog("SubPayMQ4", "订单不存在或不是未付款状态！order_sn"+Payed.OrderSn)
		return true, err
	}

	session := db.NewSession()
	defer session.Close()
	session.Begin()

	// 全渠道
	//gjpOrder := dto.Orders{}
	// oms
	//shopCode, _ := config.Get("OmsShopCode")
	omsOrderList := []*dto.DeliveryOrderCreateRequest{}
	for _, order := range orderModel {
		//oms同步订单
		if order.Source != 1 {
			continue
		}
		//if order.GjpStatus == "Payed" {
		//	SaveLog("SubPayMQ4", "订单已推送！ "+Payed.OrderSn)
		//	return true, errors.New("订单已推送！ " + Payed.OrderSn)
		//}

		// oms
		var deliveryOrder dto.DeliveryOrderCreateRequest
		var orderLines []*dto.OrderLineRequest
		goodsModel := []models.OrderProduct{}
		session.Where("order_sn = ?", order.OrderSn).Find(&goodsModel)
		//ctx := context.Background()
		for _, goods := range goodsModel {
			//productList, err := client.RPC.QuerySkuIddBySkuThird(kit.SetTimeoutCtx(ctx, 3*time.Second), &pc.NewSkuThirdRequest{
			//	Sku_Third: goods.SkuId,
			//	ErpId:     2,
			//})
			//if err != nil {
			//	glog.Error("请求商品中心SKUID报错：", err.Error())
			//	return true, err
			//}

			// oms 订单列表
			var orderLine = &dto.OrderLineRequest{
				OwnerCode:   goods.SkuId,
				ItemId:      goods.SkuId,
				PlanQty:     goods.Number,
				RetailPrice: fmt.Sprintf("%.2f", kit.FenToYuan(goods.DiscountPrice)),
				ActualPrice: fmt.Sprintf("%.2f", kit.FenToYuan(goods.DiscountPrice)),
				ItemName:    goods.ProductName,
			}

			orderLines = append(orderLines, orderLine)
		}
		// oms 订单列表
		deliveryOrder.OrderLines = orderLines

		// oms 发货单信息
		var delivery = &dto.DeliveryOrderRequest{
			DeliveryOrderCode: order.OrderSn,
			OrderType:         dto.CommonlyDeliveryOrder,
			WarehouseCode:     order.WarehouseCode,
			CreateTime:        kit.GetTimeNow(),
			PlaceOrderTime:    kit.GetTimeNow(order.CreateTime),
			PayTime:           Payed.PaymentTime,
			PayNo:             order.PaySn,
			OperateTime:       kit.GetTimeNow(),
			ShopCode:          order.WarehouseCode,
			LogisticsCode:     "OTHER",
			ReceiverInfo: dto.ReceiverInfo{
				Name:          order.ReceiverName,
				Mobile:        utils.MobileDecrypt(order.EnReceiverMobile),
				Tel:           utils.MobileDecrypt(order.EnReceiverPhone),
				Province:      order.ReceiverState,
				City:          order.ReceiverCity,
				Area:          order.ReceiverDistrict,
				DetailAddress: order.ReceiverAddress,
			},
		}
		deliveryOrder.DeliveryOrderRequest = delivery

		omsOrderList = append(omsOrderList, &deliveryOrder)

		var PayModeInt int32
		switch Payed.PaymentCode {
		case "alipay":
			PayModeInt = 1
		case "wxpay":
			PayModeInt = 2
		case "wxpay_jsapi":
			PayModeInt = 2
		case "alipay_native":
			PayModeInt = 1
		default:
			PayModeInt = 4
		}
		payTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, Payed.PaymentTime, time.Local)
		if err != nil {
			SaveLog("SubPayMQ51", "时间格式错误 order_sn:"+Payed.OrderSn+err.Error())
			payTime = time.Now().Local()
		}

		_, err = session.ID(order.Id).Update(&models.OrderMain{
			PaySn:     order.PaySn,
			PayMode:   PayModeInt,
			PayAmount: order.Total,
			PayTime:   payTime,
		})
		if err != nil {
			SaveLog("SubPayMQ16", err.Error()+Payed.OrderSn)
			session.Rollback()
			return false, err
		}
	}

	if len(omsOrderList) != 0 {
		glog.Info("推送oms参数：order_sn: ", Payed.OrderSn, kit.JsonEncode(omsOrderList))
		for _, v := range omsOrderList {
			res, err := s.OmsOrderSynchronize(v)
			if err != nil {
				SaveLog("SubPayMQ20", "推送oms返回错误："+err.Error()+kit.JsonEncode(omsOrderList))
				return false, err
			}
			glog.Infof("oms推送状态:%s order_sn:%s", res.Message, Payed.OrderSn)
			if cast.ToInt(res.Code) != 0 {
				SaveLog("SubPayMQ45", res.Message+Payed.OrderSn+"推送oms返回错误2："+res.Message)
				return false, errors.New(res.Message)
			}
			glog.Infof("oms推送返回参数:order_sn:%s %s", Payed.OrderSn, kit.JsonEncode(res))
			SaveLog("SubPayMQ5", Payed.OrderSn+kit.JsonEncode(res))
		}
	}

	local, _ := time.LoadLocation("Local")
	payTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, Payed.PaymentTime, local)
	if err != nil {
		SaveLog("SubPayMQ51", "时间格式错误 order_sn:"+Payed.OrderSn+err.Error())
		payTime = time.Now().Local()
	}
	_, err = session.Where("old_order_sn = ?", Payed.OrderSn).Update(&models.OrderMain{
		OrderStatus:      20,
		OrderStatusChild: 20201,
		//GjpStatus:        "Payed",
		PayTime: payTime,
		IsPay:   1,
	})
	if err != nil {
		SaveLog("SubPayMQ6", err.Error()+Payed.OrderSn)
		session.Rollback()
		return false, err
	}

	session.Commit()

	// 发布消费增加积分mq
	if orderModel[0].ChannelId == services.ChannelMallId {
		var integral os.IntegralNotify
		integral.Oldordersn = Payed.OrderSn
		integral.Notifytime = kit.GetTimeNow()
		integral.Mobile = orderModel[0].MemberTel
		integral.Orderfrom = orderModel[0].ChannelId
		integral.Integraltype = 41
		var totalAmount int32
		for _, v := range orderModel {
			totalAmount += v.PayAmount //根据订单的实付金额增加积分
		}
		integral.TotalAmount = totalAmount
		integral.OrgId = orderModel[0].OrgId
		services.PublishIntegralChange(integral)
	}

	return true, err
}

//保存日志
func SaveLog(api string, content string) {
	services.GetDBConn().Exec("INSERT INTO api_log(`api`, `content`) VALUES (?, ?)", api, content)
}

func subConfirmDeliveryMq() {
	queue := "ds-sz-realorder-delivery-notice"
	exchange := "ordercenter"
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	glog.Info(fmt.Sprintf("订阅MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := utils.NewMqConn()
	defer conn.Close()
	ch := utils.NewMqChannel(conn)
	defer ch.Close()

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
	}
	// name// durable// delete when unused // exclusive// no-wait // arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		glog.Error("RabbitMQ Consume fail, err : ", q.Name, err)
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error(err)
					}
				}()
				//todo 处理业务逻辑
				//解析内容
				glog.Infof("收到确认收货通知：order_sn:%s ", string(d.Body))
				SaveLog("SubConfirmDeliveryMq", string(d.Body))
				var DeliveryParam DeliveryParam
				if err := json.Unmarshal(d.Body, &DeliveryParam); err != nil {
					glog.Error(err)
					d.Ack(false) //用完删除信息
					SaveLog("SubConfirmDeliveryMq", err.Error())
					panic(err)
				}

				ok, err := ConfirmDelivery(&DeliveryParam)

				if ok {
					d.Ack(false) //用完删除信息
					SaveLog("SubConfirmDeliveryMq2", "推送结束mq已应答！"+DeliveryParam.OrderSn)
				} else {
					SaveLog("SubConfirmDeliveryMq3", "推送结束mq未应答"+DeliveryParam.OrderSn+err.Error())
					glog.Errorf("收到确认收货通知：order_sn:%s 错误：", string(d.Body)+err.Error())
					d.Reject(true) //重试
				}
			}()
		}
	}
}

type DeliveryParam struct {
	OrderSn      string `json:"order_sn"`
	DeliveryTime string `json:"finnshed_time"`
}

//确认收货mq订阅
func ConfirmDelivery(DeliveryParam *DeliveryParam) (bool, error) {
	Db := services.GetDBConn()

	orderModel := models.OrderMain{}
	ok, err := Db.Where("order_sn = ?", DeliveryParam.OrderSn).Get(&orderModel)
	if err != nil {
		SaveLog("ConfirmDelivery4", err.Error()+DeliveryParam.OrderSn)
		return true, err
	}

	if !ok {
		SaveLog("ConfirmDelivery5", "订单不存在"+DeliveryParam.OrderSn)
		return true, errors.New("订单不存在！" + DeliveryParam.OrderSn)
	}
	DeliveryTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, DeliveryParam.DeliveryTime, time.Local)
	if err != nil {
		SaveLog("ConfirmDelivery6", "时间格式错误 order_sn:"+DeliveryParam.OrderSn+err.Error())
		return true, err
	}
	_, err = Db.Where("order_sn = ?", DeliveryParam.OrderSn).Update(&models.OrderMain{
		OrderStatus:      30,
		OrderStatusChild: 20203,
		ConfirmTime:      DeliveryTime,
	})

	if err != nil {
		SaveLog("ConfirmDelivery7", "更新错误 order_sn:"+DeliveryParam.OrderSn+err.Error())
		return false, err
	}

	SaveLog("PushOrderStatusToTencent:1180 orderSn=", DeliveryParam.OrderSn)
	go services.PushOrderStatusToTencent(DeliveryParam.OrderSn, 0)
	//增加健康值，商城渠道冻结15天
	go services.AddHealthVal(15, &orderModel)
	return true, nil
}

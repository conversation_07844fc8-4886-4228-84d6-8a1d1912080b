package models

import "time"

type OmsRepush struct {
	Id         int64     `xorm:"pk autoincr comment('主键') BIGINT(20)"`
	RefundSn   string    `xorm:"not null default '''' comment('退款单号') unique VARCHAR(50)"`
	Request    string    `xorm:"not null default '''' comment('请求体') TEXT"`
	IsPush     int		 `xorm:"not null default 0 comment('是否推送，0否1是') TINYINT(4)"`
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间')"`
}

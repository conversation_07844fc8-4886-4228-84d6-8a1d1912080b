package services

import (
	"context"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

func TestSubscribeMessageService_PushTemplate(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.PushTemplateRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.PushTemplateResponse
		wantErr bool
	}{
		{ name:"推送订阅消息",
			args: args{
			ctx: context.Background(),
				request: &oc.PushTemplateRequest{
				OpenId: "",
				OrderSn: "4000000019761101",
				TemplateId: "nLYNJmUexKZbpgsLhOGAt21K42Z72aLoxUWtGEDGNE0",
				PushType: 4,
				PreSalePay: &oc.PreSalePay{
					StartTime: "2021年08月13日",
					EndTime: "2021年08月18日",
					Remarks: "不付尾款，定金不退哦~!",
				},
			},
		},},
	}

		for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := SubscribeMessageService{}

			got, err := s.PushTemplate(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("PushTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PushTemplate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

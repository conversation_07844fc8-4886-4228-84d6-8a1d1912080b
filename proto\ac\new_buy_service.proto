syntax = "proto3";

package ac;

import "ac/activity_model.proto";

service NewBuyService {
  //获取新人专享商品列表 boss
  rpc GetNewBuyProductList (GetNewBuyProductListRequest) returns (GetNewBuyProductListResponse);
  //创建新人专享商品
  rpc CreateNewBuyProduct (CreateNewBuyProductRequest) returns (baseResponse);
  //更新新人专享商品
  rpc UpdateNewBuyProduct (UpdateNewBuyProductRequest) returns (baseResponse);
  //获取新人专享商品详情
  rpc GetNewBuyProductDetail(NewBuyProductDetailRequest) returns (GetNewBuyProductDetailResponse);
  //删除新人专享商品
  rpc DeleteNewBuyProduct(NewBuyProductIdRequest) returns (baseResponse);
  //获取可以参加新人专享活动的阿闻电商渠道的商品
  rpc GetNewBuyUPetProductSelectList(GetNewBuyUPetProductSelectListRequest) returns (GetNewBuyUPetProductSelectListResponse);

  //创建异步导出任务
  rpc CreateTask (CreateTaskRequest) returns (baseResponse);
  //查询异步任务列表
  rpc GetTaskList (TaskListRequest) returns (TaskListResponse);

  // 新人专享列表
  rpc GetNewBuyProductByMall(NewPeopleListRequest)returns(NewPeopleListResponse);
  // 新人专享券列表
  rpc GetNewBuyVoucherList(NewPeopleVoucherRequest)returns(NewPeopleVoucherResponse);
  // 新人专享券领取
  rpc NewPeopleVoucherGet(NewPeopleVoucherGetRequest)returns(baseResponse);
  // 新人专享券设置
  rpc NewPeopleVoucherAdd(NewPeopleVoucherAddRequest)returns(NewPeopleVoucherAddResponse);
  // 获取新人专享券列表
  rpc GetNewPeopleVoucherList(baseRequest)returns(NewBuyVoucherListResponse);
  // 获取参与新人专享活动商品（进行中）
  rpc GetNewPeopleProduct(GetNewPeopleProductRequest)returns(GetNewBuyProductListResponse);

  //新人专享活动信息
  rpc GetNewBuyInfo(NewBuyInfoRequest) returns (NewBuyInfoResponse);
  //新人专享活动信息
  rpc GetNewBuyDetail(NewBuyInfoRequest) returns (NewBuyInfoResponse);
  //创建新人专享活动
  rpc CreateNewBuy(NewBuyRequest) returns (baseResponse);
  //更新新人专享活动
  rpc UpdateNewBuy(NewBuyRequest) returns (UpdateNewBuyResponse);
  // 新人专享banner
  rpc NewPeopleBanner(NewPeopleBannerRequest) returns (NewPeopleBannerResponse);
}

//新人专享商品列表的请求数据
message GetNewBuyProductListRequest {
  //新人专享活动id
  int32 nid = 1;
  //产品名称
  string productName = 2;
  //商品sku id
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //商品的产品id
  int32 ChannelId = 5;
  //商品状态 -1删除 0默认
  int32 Status = 6;
  //排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
  int32 orderBy = 7;
  //分页参数
  Page pagination = 8;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 9;
}

//新人专享商品列表
message GetNewBuyProductListResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //新人专享商品信息
  repeated NewBuyProductData data = 5;
}

//新人专享商品数据
message NewBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 nid = 2;
  //渠道ID
  int32 channelId = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 price = 7;
  //新人价 单位分
  int32 newPrice = 8;
  //短标题
  string shortTitle = 9;
  //长标题
  string longTitle = 10;
  //排序
  int32 sort = 11;
  //状态 -1删除 0默认
  int32 status = 12;
  // 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
  int32 canBeEdited = 13;
  // 是否可被删除 0 不可删除 1 可删除 用于boss后台
  int32 canBeDeleted = 14;
  // 商品图片
  string pic = 15;
  // 商品库存
  int32 stock = 16;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 17;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 18;
  //创建时间
  string createTime = 19;
  //更新时间
  string updateTime = 20;
}

//新增新人专享活动商品
message CreateNewBuyProductRequest {
  //商品sku id
  int32 skuId = 1;
  //商品的产品id
  int32 productId = 2;
  //主体信息
  SaveNewBuyProductData saveData = 3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}

//添加/编辑新人专享商品
message SaveNewBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 nid = 2;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 newPrice = 7;
  //3期价 单位分
  int32 sort = 8;
  //短标题
  string shortTitle = 9;
  //长标题
  string longTitle = 10;
}

// 更新新人专享商品
message UpdateNewBuyProductRequest {
  //需要更新的记录id
  int32 id = 1 ;
  //商品sku id
  int32 skuId = 2;
  //商品的产品id
  int32 productId = 3;
  //主体信息
  SaveNewBuyProductData saveData = 4;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 5;
}

// 新人专享商品只需要id的请求
message NewBuyProductDetailRequest {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 nid = 2;
  //商品skuId
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //渠道 1电商 5商城
  int32 channelId = 5;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 6;
}

//获取新人专享商品信息
message GetNewBuyProductDetailResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //新人专享商品信息
  NewBuyProductDetailData data = 4;
}

//新人专享商品详细数据包含部分新人专享信息
message NewBuyProductDetailData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 nid = 2;
  //渠道ID
  int32 channelId = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 price = 7;
  //新人价 单位分
  int32 newPrice = 8;
  //短标题
  string shortTitle = 9;
  //长标题
  string longTitle = 10;
  //排序
  int32 sort = 11;
  //状态 -1删除 0默认
  int32 status = 12;
  //创建时间
  string createTime = 13;
  //更新时间
  string updateTime = 14;

  //新人专享信息
  //开始时间
  string beginDate = 15;
  //结束时间
  string endDate = 16;

  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 17;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 18;
}

//新人专享商品只需要id的请求
message NewBuyProductIdRequest {
  //活动商品信息记录id
  int32 id = 1;
}

//阿闻电商参加新人专享活动的商品
message GetNewBuyUPetProductSelectListRequest {
  //活动id
  int32 nid = 1;
  //商品sku_id 对应商城的goods_id
  int32 skuId = 2;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 3;
  // 商品名称
  string productName = 4;
  //分页参数
  Page pagination = 5;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 6;
}

//阿闻电商参加新人专享活动的商品
message GetNewBuyUPetProductSelectListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //新人专享商品信息
  repeated NewBuySelectUPetProductData data = 5;
}

message NewBuySelectUPetProductData {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 1;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 2;
  // 商品名称
  string productName = 3;
  // 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
  int32 timeConflict = 4;
  //商品图片
  string pic = 5;
  //库存
  int32 stock = 6;
  //价格 单位分
  int32 marketPrice = 7;
  //是否时虚拟产品 1是 0 否
  int32 isVirtual = 8;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 9;
  //组合商品的子商品信息
  repeated ChildInfo childSkuIds = 10;
}

//组合商品子商品讯息
message ChildInfo {
  int32 skuId = 1;
  //规则
  int32 ruleNum = 2;
  //是否为虚拟 0:不是 1：是虚拟
  int32 isVirtual = 3;
  //0不是药品仓 1药品仓
  int32 stockWarehouse = 7;
}

//分页参数
message Page {
  //当前多少页 从1开始 必传且必须大于0
  int32 pageIndex = 1;
  //每页多少条数据 必传且必须大于0
  int32 pageSize = 2;
}

message NewBuyIdRequest {
  //活动商品信息记录id
  int32 id = 1;
  //分页参数
  Page pagination = 5;
}
//活动商品导入数据响应
message NewBuyProductImportData {
  //导入时间
  string createTime = 1;
  //导入结果（0=>全部成功，1=>部分成功）
  int32 result = 2;
  //操作结果文件路径
  string resultFileUrl = 3;
}
//活动商品导入列表响应
message GetNewBuyProductImportListResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //新人专享商品信息
  repeated NewBuyProductImportData data = 5;
}
//活动商品导入请求
message ImportNewBuyProductRequest {

}

// 异步任务创建
message CreateTaskRequest {
  // 活动ID
  int32 nid = 1;
  // 任务类型 3 导出新人专享数据
  int32 taskContent = 2;
  // 操作文件路径或者参数
  string operationFileUrl = 3;
  // 操作请求的token值，类似userinfo
  string requestHeader = 4;
  // 创建人id
  string createId = 5;
  // 主体id
  int32 OrgId = 6; 
}

// 任务列表请求参数
message TaskListRequest {
  //活动ID
  int32 nid = 1;
  // 任务: 1 导出新人专享数据
  int32 taskContent = 2;
  // 查询人id
  string userNo = 3;
  //分页参数
  Page pagination = 4;
  // 任务状态
  int32 taskStatus = 5;
}

// 任务列表响应参数
message TaskListResponse {
  int32 code = 1;
  // 消息
  string msg = 2;
  // 总数
  int32 total = 3;
  // 数据
  repeated TaskData data = 4;
}

// 任务数据
message TaskData {
  int32 id = 1;
  //活动ID
  int32 nid = 2;
  // 任务类型 1 导入新人专享商品数据
  int32 taskContent = 3;
  // 任务状态 1 进行中 2 已完成 3 失败
  int32 taskStatus = 4;
  // 操作文件路径或者参数
  string operationFileUrl = 5;
  // 操作请求的token值，类似userinfo
  string requestHeader = 6;
  // 操作结果文件路径
  string resulteFileUrl = 7;
  // 创建时间
  string createTime = 8;
  // 更新时间
  string modifyTime = 9;
  // 创建人id
  string createId = 10;
  // 任务详情
  string taskDetail = 11;
}

// 新人专享列表
message NewPeopleListRequest {
  string from = 1;
  int32  page = 2;
  int32  page_size = 3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}
message NewPeopleListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 新人专享分页数据
  repeated NewPeopleData data = 4;
  int32 has_more = 5;
  // 当前页和条数
  int32 page = 6;
  int32  page_size = 7;
}
message NewPeopleData{
  //商品图片链接
  string goods_image_url = 1;
  //商品名称
  string goods_name = 2;
  //商品简称
  string goods_short_name = 7;
  //新人价
  float goods_price = 3;
  //市场价
  float goods_normal_price = 4;
  //skuid
  int32 goods_id = 5;
  //是否虚拟商品 1 是 0 否
  int32 is_virtual = 6;
  //是否购物车显示 1 是 0否
  int32 is_show_cart = 8;
}

// 新人专享券
message NewPeopleVoucherRequest{
  string uid = 1;
}
message NewPeopleVoucherResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 已领取总数
  int32 has_get_total = 4;

  repeated NewPeopleVoucherData data = 5;
}
message NewPeopleVoucherData {
  //代金券模版编号
  int32 voucher_t_id = 1;
  //代金券使用时的订单限额
  string limit_price = 2;
  //代金券模版面额
  string voucher_price = 3;
  //0-未领取，1-已领取，2-已抢光，3-已过期
  int32 has_get = 4;
}

// 新人券领取
message NewPeopleVoucherGetRequest {
  string uid = 1;
  string voucher_t_ids = 2;
}

// 新人专享券设置
message NewPeopleVoucherAddRequest {
  string voucher_t_ids = 1;
}
message NewPeopleVoucherAddResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 错误id对应的信息
  repeated NewPeopleVoucherAddErrData err_ids = 4;
}
message NewPeopleVoucherAddErrData {
  //代金券模版编号
  int32 voucher_t_id = 1;
  //错误信息
  string msg = 2;
}

//新人活动详情请求
message NewBuyInfoRequest {
  int32 id = 1;
}

//创建新人专享活动请求
message NewBuyRequest {
  // 活动id
  int32 id = 1;
  // 活动名称
  string title = 2;
  //渠道id
  int32 channel_id = 3;
  // 活动开始时间
  string begin_date = 4;
  // 活动结束时间
  string end_date = 5;
  //分享封面
  string cover = 6;
  //头图
  string head_img = 7;
  //活动状态
  int32 status = 8;
  // 参加活动的商品数量
  int32 product_count = 9;
  // 是否免邮费 0否1是
  int32 is_shipping_free = 10;
  //创建时间
  string create_time = 11;
  //更新时间
  string update_time = 12;
}

//新人专享活动详情响应
message NewBuyInfoResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //列表
  NewBuyRequest detail = 4;
}

// 小程序接口获取参加新人专享活动商品
message GetNewPeopleProductRequest {
  // sku_id集合，空表示所有
  string sku_ids = 1;
}

// 新人专享券列表
message NewBuyVoucherListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //列表
  repeated NewBuyVoucherData data = 4;
}
message NewBuyVoucherData {
  //商城优惠券id
  int32 voucher_id = 1;
}

// 新人专享banner
message NewPeopleBannerRequest{
}

message NewPeopleBannerResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //列表
  NewPeopleBannerData data = 4;
}
message NewPeopleBannerData{
  //分享标题
  string title = 1;
  //开始时间
  string begin_date = 2;
  //结束时间
  string end_date = 3;
  //头图
  string cover = 4;
  //分享封面
  string pic = 5;
  //当前时间
  string sys_time = 6;
}

//更新活动响应
message UpdateNewBuyResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //冲突商品信息
  repeated ConflictProductData data = 4;
}
message ConflictProductData {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 1;
  // 商品名称
  string productName = 3;
}

package tasks

import (
	"context"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/cc"
	"order-center/proto/oc"
	"order-center/services"
	"time"
)

//核销码到期自动退款
func handleExpireVerifyCode() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:HandleExpireVerifyCode"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	//连接池勿关闭
	db := services.GetDBConn()
	//todo 其他渠道的虚拟商品到期自动自动退后续需要处理
	var items []models.OrderVerifyCode
	err := db.SQL(`
		SELECT distinct(order_verify_code.order_sn) FROM order_verify_code 
		INNER JOIN order_product ON order_product.order_sn = order_verify_code.order_sn
		INNER JOIN order_main ON order_main.order_sn = order_verify_code.order_sn
		WHERE order_verify_code.verify_code_expiry_date < ?
		AND order_verify_code.verify_status = 0 
		AND order_product.virtual_invalid_refund = 1 
		AND order_main.channel_id =1
		AND order_main.order_status != 0
	`, kit.GetTimeNow()).Find(&items)
	if err != nil {
		glog.Error("查询过期核销码失败, ", err)
		return
	}

	if len(items) <= 0 {
		return
	}

	orderProductMap := map[string][]models.OrderVerifyCode{}

	for _, v := range items {
		orderProductMap[v.OrderSn] = append(orderProductMap[v.OrderSn], v)
	}
	refund := services.RefundOrderService{}

	for _, item := range items {
		amount, shopId := getRefundCodesRefundAmount(db, item.OrderSn)
		if amount <= 0 {
			continue
		}

		model := new(oc.RefundOrderApplyRequest)
		model.OrderId = item.OrderSn
		model.Reason = "虚拟核销码到期自动退款"
		model.RefundType = 1
		model.FullRefund = 1
		model.ShopId = shopId
		model.ApplyOpUserType = "2"
		model.ChannelId = services.ChannelAwenId
		model.RefundRemark = "虚拟核销码到期自动退款"
		model.OperationUser = "核销码自动退"
		model.ResType = "等待处理中"
		model.OperationType = "商家申请售后单"
		model.RefundAmount = float32(amount)
		model.ExternalOrderId = model.OrderId

		glog.Info("handleExpireVerifyCode1:", kit.JsonEncode(model))

		out, err := refund.RefundOrderApply(context.Background(), model)
		if err != nil {
			glog.Errorf(item.OrderSn, ", 虚拟核销码到期自动退款申请错误：%s", err.Error())
			continue
		}
		if out.Code != 200 {
			glog.Errorf(item.OrderSn, ", 虚拟核销码到期自动退款申请错误：%s", out.Message)
			continue
		}

		answerModel := new(oc.RefundOrderAnswerRequest)

		glog.Info("handleExpireVerifyCode2:", out)
		answerModel.OrderId = model.OrderId
		answerModel.RefundOrderSn = out.RefundOrderSn
		answerModel.Reason = ""
		answerModel.ResultType = 1
		answerModel.ResultTypeNote = "商家同意退款"
		answerModel.OperationType = "商家同意退款"
		answerModel.OperationUser = model.OperationUser
		answerModel.ExternalOrderId = answerModel.OrderId
		answerOut, err1 := refund.RefundOrderAnswer(context.Background(), answerModel)
		if err1 != nil {
			glog.Errorf("虚拟核销码到期自动退款应答错误：%s", err1.Error())
		}
		glog.Info("handleExpireVerifyCode3:", kit.JsonEncode(answerOut))
	}
}

func getRefundCodesRefundAmount(db *xorm.Engine, orderSn string) (amount float64, shopId string) {
	var orderMain models.OrderMain
	isOk, err := db.Table("order_main").Join("inner", "order_verify_code", "`order_main`.order_sn = `order_verify_code`.order_sn").
		Where("`order_main`.order_sn = ? and order_verify_code.verify_status = 0", orderSn).Get(&orderMain)
	if err != nil || !isOk {
		return amount, shopId
	}
	var orderProducts []models.OrderProduct
	err = db.Table("order_product").Where("order_sn = ?", orderMain.OrderSn).Find(&orderProducts)
	if err != nil {
		return amount, shopId
	}

	amount = 0
	for _, product := range orderProducts {
		count, _ := db.Select("count(id)").Where("order_sn = ? and verify_status = 0", orderSn).Count(new(models.OrderVerifyCode))
		amount += kit.FenToYuan(product.PayPrice) * float64(count)
	}
	return amount, orderMain.ShopId
}

type VerifyCodeExpire struct {
	Id          int32
	OldOrderSn  string
	ProductName string
	ExpireTime  time.Time
	DurationDay int32
	MemberId    string
}

// 核销码临期通知
func verifyCodeExpireNotice() {
	r := services.GetRedisConn()
	lockKey := "task:lock:verifyCodeExpireNotice"
	if !r.SetNX(lockKey, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer r.Del(lockKey)

	glog.Info("verifyCodeExpireNotice 开始执行")

	// 上一次执行的id
	var beforeId int32
	// 每一次的取数
	pageSize := 5000

	// 一个订单号发一次
	sends := make(map[string]struct{})
	now := time.Now()

	// 订阅后，该订单核销成功或没核销的兑换码临期7天时提醒用户。
	// 如果兑换码剩余期限不足7天，则在临期3天提醒。
	// 如果不足3天，则在临期1天提醒。如购买时就临期1天，则订阅也不提醒。
	// 电商的时间都是以23:59:59结尾，如果有其他情况应该考虑
	// **** 注意必须保证临期天数倒序 ****
	timeMap := []struct {
		Time string
		Day  int32
	}{
		// 临期7天
		{now.AddDate(0, 0, 6).Format(kit.DATE_LAYOUT) + " " + "23:59:59", 7},
		// 临期3天
		{now.AddDate(0, 0, 2).Format(kit.DATE_LAYOUT) + " " + "23:59:59", 3},
		// 临期1天
		{now.Format(kit.DATE_LAYOUT) + " " + "23:59:59", 1},
	}

	var times []string
	for _, v := range timeMap {
		times = append(times, v.Time)
	}

	client := cc.GetCustomerCenterLongClient()
	db := services.GetDBConn()

	var sendCount int32

	for {
		var expires []*VerifyCodeExpire

		if err := db.Table("order_verify_code").Alias("c").
			Join("inner", "order_product op", "op.order_sn = c.order_sn").
			Join("inner", "order_main om", "om.order_sn = c.order_sn").
			Join("inner", "order_main p", "p.order_sn = om.parent_order_sn").
			Where("om.channel_id = 5 and c.verify_status = 0 and c.id > ?", beforeId).
			In("c.verify_code_expiry_date", times).
			Select(`om.member_id,p.old_Order_sn,op.product_name,c.verify_code_expiry_date as expire_time,c.id,
DATEDIFF(c.verify_code_expiry_date,c.create_time) as duration_day`).
			OrderBy("c.id asc").Limit(pageSize).
			Find(&expires); err != nil {
			glog.Error("verifyCodeExpireNotice " + err.Error())
			break
		}

		if len(expires) == 0 {
			break
		}

		for _, expire := range expires {
			if _, has := sends[expire.OldOrderSn]; has {
				continue
			}

			expireTime := expire.ExpireTime.Format(kit.DATETIME_LAYOUT)
			// 临期天数
			var expireDay int32

			if pass, day := func() (bool, int32) {
				for _, v := range timeMap {
					if expire.DurationDay >= v.Day {
						if v.Time == expireTime {
							return true, v.Day
						}
						break
					}
				}
				return false, 0
			}(); !pass {
				continue
			} else {
				expireDay = day
			}

			sendCount++

			if len([]rune(expire.ProductName)) > 20 {
				expire.ProductName = string([]rune(expire.ProductName)[:20])
			}

			_, _ = client.RPC.SendSubscribeMessage(client.Ctx, &cc.SendSubscribeMessageReq{
				ScrmUserId:  expire.MemberId,
				TemplateKey: "vr-code-expire",
				Values: []*cc.MessageValue{
					{
						Type:  "string",
						Value: expire.ProductName,
					}, {
						Type:  "string",
						Value: expireTime,
					}, {
						Type:  "string",
						Value: fmt.Sprintf("%d天", expireDay),
					}, {
						Type:  "string",
						Value: "点击查看订单详情，记得尽快使用哦！",
					},
				},
				Page: "app/mall/page/orderDetailVr?order_id=" + expire.OldOrderSn,
			})

			sends[expire.OldOrderSn] = struct{}{}
		}

		if len(expires) < pageSize {
			break
		}

		beforeId = expires[len(expires)-1].Id
	}

	glog.Info("verifyCodeExpireNotice 执行结束，符合条件 ", sendCount)
}

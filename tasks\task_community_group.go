package tasks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"order-center/models"
	"order-center/proto/mc"
	"order-center/proto/oc"
	"order-center/services"
	"time"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 社区团购相关定时任务
func init() {
	if !kit.EnvCanCron() {
		return
	}
	c := cron.New()
	// 每分钟执行社区团购订单状态
	if _, err := c.AddFunc("@every 1m", communityGroupAutoUpdateStatus); err != nil {
		glog.Info("社区团购 communityGroupAutoUpdateStatus 创建任务出错：", err.Error())
	}
	c.Start()
}

type taskCommunityGroup struct {
	Db                    *xorm.Engine
	UpetDb                *xorm.Engine
	OrderService          *services.OrderService
	MessageClient         *mc.Client
	TemplateId            string //消息模板id
	LockKey               string
	Owner                 string
	Type                  int32          //类型
	ActivityStatusUpdated map[int32]bool // 活动状态是否已经更新
}

// 团订单
type communityGroupOrder struct {
	Id               int32
	OrderSn          string
	ParentOrderSn    string
	OrderStatus      int32
	OrderStatusChild int32
	ShopName         string
	StoreName        string // 店铺名称
	MemberId         string
	LeaderMemberId   string    // 团长会员id
	EndTime          time.Time // 活动结束时间
	PayAmount        int32     // 已支付金额
	MinAmount        int32     // 成团金额
	Status           int32     // 活动状态 团状态 0开团 1拼团成功 2拼团失败
	Source           int32
}

func newTaskCommunityGroup(T int32) *taskCommunityGroup {
	t := &taskCommunityGroup{
		LockKey:               "order-center:communityGroupAutoUpdateStatus:lock",
		Owner:                 uuid.New().String(),
		Type:                  T,
		Db:                    services.GetDBConn(),
		UpetDb:                services.GetUPetDBConn(),
		OrderService:          new(services.OrderService),
		MessageClient:         mc.GetMessageCenterClient(),
		ActivityStatusUpdated: make(map[int32]bool),
	}

	if t.Type == 1 {
		t.TemplateId, _ = config.Get("community_group_template_id")
	}

	return t
}

func communityGroupAutoUpdateStatus() {
	t := newTaskCommunityGroup(1)
	if ok, err := t.Lock(); err != nil {
		glog.Info("获取锁失败：" + err.Error())
		return
	} else if !ok {
		return
	}
	defer t.Unlock()

	if err := t.handle(); err != nil {
		glog.Info("社区团购 task_community_group 执行任务出错 " + err.Error())
	}

	// 避免短时间执行任务时多台服务器先后执行
	time.Sleep(15 * time.Second)
}

func (t *taskCommunityGroup) Lock() (bool, error) {
	lua := redis.NewScript(`
local v = redis.call("get",KEYS[1])
-- lua只有false和nil为假，其它的都是true，存在锁 -- 
if v then
	-- 当前进程获取的锁，且未释放，自动延期 --
	if v == ARGV[1] then
		redis.call("set",KEYS[1],ARGV[1],"ex",300)
	end
	-- 这次任务周期不需要执行 --
	return 0
else
	-- lua脚本的原子性，同时一个key操作只会在一台服务器执行，这里不需要set nx --
	redis.call("set",KEYS[1],ARGV[1],"ex",300)
	-- 新获取到锁，执行任务 --
	return 1
end
`)
	if r, err := lua.Run(services.GetRedisConn(),
		[]string{t.LockKey},
		[]string{t.Owner},
	).Result(); err != nil {
		return false, err
	} else {
		return cast.ToInt32(r) > 0, nil
	}
}

func (t *taskCommunityGroup) Unlock() {
	services.GetRedisConn().Del(t.LockKey)
}

// 处理拼团状态
func (t *taskCommunityGroup) handle() (err error) {
	var cgs []*communityGroupOrder
	// 统计起始时间，一周内
	startTime := time.Now().Add(-49 * time.Hour).Format(kit.DATETIME_LAYOUT)

	if err = t.Db.SQL("SELECT ac.id, m.order_sn, m.parent_order_sn, m.order_status, m.order_status_child, m.shop_name, m.member_id, ac.member_id as leader_member_id, ac.store_name, ac.end_time, ac.pay_amount, ac.min_amount, ac.status, m.source "+
		"FROM `order_group_activity` AS `ac` "+
		"INNER JOIN order_main_group g ON g.order_group_activity_id  = ac.id "+
		"INNER JOIN order_main  	 m ON m.create_time >= ? AND m.channel_id = 1 AND g.parent_order_sn = IF(m.is_pay > 0, m.parent_order_sn, m.order_sn) "+
		"WHERE (ac.created_at  >= ? ) AND ( (ac.status in (0, 2) and m.order_status >= 20) OR (ac.status = 1 AND  m.order_status  = 20 AND m.order_status_child = 20101) )", startTime, startTime).Find(&cgs); err != nil {
		return errors.New("查询活动订单 " + err.Error())
	}

	for _, cg := range cgs {
		switch cg.Status {
		case 1: // 成团后支付
			_ = t.autoAccept(cg)
		case 2: // 失败后取消订单
			_ = t.autoCancel(cg)
		case 0: // 进行中刷新状态
			_ = t.refreshActivityStatus(cg)
		}
	}

	// 没有参加的团到期也结束
	_, err = t.Db.Exec("update order_group_activity set status = 2, group_at = now() where status = 0 and "+
		"created_at >= ? and end_time <= ?", startTime, time.Now().Format(kit.DATETIME_LAYOUT))

	return
}

// 更新活动状态
func (t *taskCommunityGroup) refreshActivityStatus(cg *communityGroupOrder) (err error) {
	defer func() {
		if err != nil {
			glog.Info("社区团购 task_community_group " + cg.OrderSn + " 更新活动状态：" + err.Error())
		}
	}()

	if cg.Status != 0 {
		return
	}

	// 拼团成功
	if cg.PayAmount >= cg.MinAmount {
		if cg.OrderStatus == 20 && cg.OrderStatusChild == 20101 { // 支付订单接单
			// 更新状态标记
			if !t.ActivityStatusUpdated[cg.Id] {
				if _, err = t.Db.ID(cg.Id).Update(&models.OrderGroupActivity{Status: 1, GroupAt: time.Now()}); err != nil {
					return errors.New("更新拼团状态 " + err.Error())
				}

				go func() {
					_ = t.sendTemplateMessage(&communityGroupOrder{
						Id:       cast.ToInt32(cg.Id),
						OrderSn:  cast.ToString(cg.Id),
						ShopName: cg.ShopName,
						MemberId: cg.LeaderMemberId,
					}, "成功")
				}()

				t.ActivityStatusUpdated[cg.Id] = true
			}
			_ = t.autoAccept(cg)
		}
	} else if cg.EndTime.Before(time.Now()) { // 已经结束，订单取消
		// 更新状态标记
		if !t.ActivityStatusUpdated[cg.Id] {
			if _, err = t.Db.ID(cg.Id).Update(&models.OrderGroupActivity{Status: 2, GroupAt: time.Now()}); err != nil {
				return errors.New("更新拼团状态 " + err.Error())
			}

			t.ActivityStatusUpdated[cg.Id] = true

			go func() {
				_ = t.sendTemplateMessage(&communityGroupOrder{
					Id:       cast.ToInt32(cg.Id),
					OrderSn:  cast.ToString(cg.Id),
					ShopName: cg.StoreName,
					MemberId: cg.LeaderMemberId,
				}, "失败")
			}()
		}
		_ = t.autoCancel(cg)
	}

	return
}

//获取小程序页面地址
func (t *taskCommunityGroup) page(cg *communityGroupOrder) string {
	if t.Type == 0 { //订单页面
		return fmt.Sprintf("app/local/order/orderDetail?productId=%d&order_sn=%s", cg.Id, cg.OrderSn)
	} else if t.Type == 1 { //团页面
		return fmt.Sprintf("app/communityGroup/communityDetail?id=%d", cg.Id)
	}
	return ""
}

// 成团失败自动取消
func (t *taskCommunityGroup) autoCancel(cg *communityGroupOrder) (err error) {
	// 非待接单的略过
	if cg.OrderStatusChild != 20101 && cg.OrderStatusChild != 20102 {
		return
	}
	defer func() {
		if err != nil {
			glog.Info("社区团购订单 task_community_group " + cg.OrderSn + "拼团失败取消订单处理失败：" + err.Error())
		} else {
			glog.Info("社区团购订单 task_community_group " + cg.OrderSn + "拼团失败取消订单成功")
		}
	}()

	if cg.OrderStatus >= 20 {
		if out, err := t.OrderService.CancelOrder(context.Background(), &oc.CancelOrderRequest{
			OrderSn:          cg.OrderSn,
			CancelReason:     "社区团购拼团失败系统自动取消订单",
			IsRefund:         0,
			OrderStatus:      cg.OrderStatus,
			OrderStatusChild: cg.OrderStatusChild,
		}); err != nil {
			return err
		} else if out.Code != 200 {
			return errors.New(out.Message + out.Error)
		}

		var refundSn string
		if has, err := t.Db.Table("refund_order").Select("refund_sn").Where("order_sn=?", cg.OrderSn).Get(&refundSn); err != nil {
			return err
		} else if has {
			// 订单取消成功后，调用退款服务
			ro := services.RefundOrderService{}
			if out, err := ro.RefundOrderPay(context.Background(), &oc.RefundOrderPayRequest{
				RefundOrderSn: refundSn,
				ResType:       "拼团失败自动退款", // 这里变更要同步 失败自动退款的不减扣金额 变更
				OperationType: "商家发起退款",
			}); err != nil {
				return err
			} else if out.Code != 200 {
				return errors.New(out.Message + out.Error)
			}
		}
	} else {
		if _, err = t.Db.Exec("UPDATE order_main SET order_status=0,order_status_child=20107,cancel_reason='拼团失败，订单取消',cancel_time=now() "+
			"WHERE order_sn = ? AND order_status=10", cg.OrderSn); err != nil {
			return errors.New("取消订单 " + err.Error())
		}
		// 释放阿闻到家小程序折扣活动当日库存
		services.FreedDailyStock(cg.OrderSn)
	}

	_ = t.sendTemplateMessage(cg, "失败")

	return
}

// 发送模板消息
func (t *taskCommunityGroup) sendTemplateMessage(po *communityGroupOrder, msg string) (err error) {
	defer func() {
		if err != nil {
			glog.Info("社区团购 task_community_group_subscribeMessage 信息 " + kit.JsonEncode(po) + " 发送模板消息出错：" + err.Error())
		}
	}()

	var openId string
	if has, err := t.UpetDb.Table("upet_member").Where("scrm_user_id = ?", po.MemberId).Select("weixin_mini_openid").Get(&openId); err != nil {
		return errors.New("查询会员信息 " + err.Error())
	} else if !has {
		return nil
	}

	miniprogramState := "trial"
	if !kit.IsDebug {
		miniprogramState = "formal"
	}

	data, _ := json.Marshal(map[string]interface{}{
		"phrase5": map[string]interface{}{
			"value": msg,
		}, "character_string1": map[string]interface{}{
			"value": po.OrderSn,
		}, "thing2": map[string]interface{}{
			"value": po.ShopName,
		}, "thing3": map[string]interface{}{
			"value": "商品套餐",
		},
	})

	res, err := t.MessageClient.RPC.SubscribeMessage(t.MessageClient.Ctx, &mc.SubscribeMessageRequest{
		Touser:           openId,
		TemplateId:       t.TemplateId,
		Data:             string(data),
		MiniprogramState: miniprogramState,
		Page:             t.page(po),
	})
	glog.Info("社区团购 task_community_group_subscribeMessage 发送订阅消息结果 " + kit.JsonEncode(res))
	if err != nil {
		return
	} else if res.Code != 200 {
		err = errors.New(res.Message + res.Error)
	}
	return
}

// 自动接单处理
func (t *taskCommunityGroup) autoAccept(cg *communityGroupOrder) (err error) {
	// 非待接单的略过
	if cg.OrderStatusChild != 20101 {
		return
	}
	defer func() {
		if err != nil {
			glog.Info("社区团购订单 task_community_group " + cg.OrderSn + " 自动接单处理失败：" + err.Error())
		} else {
			glog.Info("社区团购订单 task_community_group " + cg.OrderSn + " 自动接单处理成功")
		}
	}()

	if res, err := t.OrderService.AcceptOrder(context.Background(), &oc.AcceptOrderRequest{
		OrderSn: cg.ParentOrderSn,
	}); err != nil {
		return err
	} else if res.Code != 200 {
		return errors.New(res.Message + res.Error)
	}

	_ = t.sendTemplateMessage(cg, "成功")
	return
}

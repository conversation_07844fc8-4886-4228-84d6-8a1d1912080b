package models

import "time"

type StoreBusinessSetup struct {
	Id                int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	FinanceCode       string    `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
	ChannelId         int       `json:"channel_id" xorm:"not null comment('渠道id') INT 'channel_id'"`
	BusinessStatus    int       `json:"business_status" xorm:"not null comment('营业状态1:营业 2:闭店') INT 'business_status'"`
	Businessdate      string    `json:"businessdate" xorm:"default 'null' comment('营业日期（星期一**星期日分别为1**7）') VARCHAR(50) 'businessdate'"`
	Advanceorder      int8      `json:"advanceorder" xorm:"default 'null' comment('预订单设置（true:休息时间支持预订，false:休息时间不支持预订）') TINYINT(1) 'advanceorder'"`
	Advancedates      string    `json:"advancedates" xorm:"default 'null' comment('接受预定日期（json数组，例[{0,0},{0,1}]）') VARCHAR(256) 'advancedates'"`
	Advanceremindtime int       `json:"advanceremindtime" xorm:"default 'null' comment('预订单提醒：送到时间前xx分钟提醒备货') INT 'advanceremindtime'"`
	Notice            string    `json:"notice" xorm:"default '' comment('店铺公告') VARCHAR(256) 'notice'"`
	Mobile            string    `json:"mobile" xorm:"default '' comment('店铺电话') VARCHAR(128) 'mobile'"`
	QrCode            string    `json:"qrCode" xorm:"default '' comment('店铺二维码') VARCHAR(128) 'qrCode'"`
	BusinessCategory1 string    `json:"business_category1" xorm:"default '' comment('经营种类1') VARCHAR(56) 'business_category1'"`
	BusinessCategory2 string    `json:"business_category2" xorm:"default 'null' comment('经营种类2') VARCHAR(56) 'business_category2'"`
	Image             string    `json:"image" xorm:"default '' comment('店铺头像') VARCHAR(128) 'image'"`
	Businesssystem    string    `json:"businesssystem" xorm:"default '' comment('业务系统') VARCHAR(56) 'businesssystem'"`
	BusinesssystemID  string    `json:"businesssystemID" xorm:"default '' comment('业务系统ID') VARCHAR(56) 'businesssystemID'"`
	Invoice           int8      `json:"invoice" xorm:"not null comment('是否支持开具发票(true:支持 false:不支持)') TINYINT(1) 'invoice'"`
	AutoPrint         int8      `json:"auto_print" xorm:"not null comment('是否自动打印(true:自动打印 false:不自动打印)') TINYINT(1) 'auto_print'"`
	ReturnAddress     string    `json:"return_address" xorm:"default '' comment('退货地址') VARCHAR(128) 'return_address'"`
	IsSelfLifting     int8      `json:"is_self_lifting" xorm:"default 0 comment('是否支持自提(true:支持 false:不支持)') TINYINT(1) 'is_self_lifting'"`
	IsSelfLiftingApp  int8      `json:"is_self_lifting_app" xorm:"default 0 comment('小程序是否支持自提，1开启，0关闭') TINYINT(1) 'is_self_lifting_app'"`
	StockUpTime       int       `json:"stock_up_time" xorm:"default 'null' comment('备货时长 单位分钟 不能小于0') INT 'stock_up_time'"`
	MiniAppCode       string    `json:"miniAppCode" xorm:"default '' comment('店铺小程序码') VARCHAR(255) 'miniAppCode'"`
	EnablePickup      int8      `json:"enable_pickup" xorm:"default 0 comment('是否开启社区团购自提') TINYINT(1) 'enable_pickup'"`
	Updatetime        time.Time `json:"updatetime" xorm:"default 'CURRENT_TIMESTAMP' comment('最后更新时间') DATETIME 'updatetime' updated"`
	MiniBanner        string    `json:"mini_banner" xorm:"comment('小程序banner') TEXT 'mini_banner'"`
	AppBanner         string    `json:"app_banner" xorm:"comment('app banner') TEXT 'app_banner'"`
	//打印方式  1:本地打印 2:飞鹅打印
	WhatPrint int `json:"what_print" xorm:"not null comment('1:本地打印 2:飞鹅打印') INT 'what_print'"`
	//储值卡校验 1:不需要 2:需要
	IsCheck int `json:"is_check" xorm:"not null comment('1:不需要 2:需要') INT 'is_check'"`
}

func (t StoreBusinessSetup) TableName() string {
	return "datacenter.store_business_setup"
}

package models

import (
	"time"
)

//在线问诊 问诊信息表
type DiagnoseInfo struct {
	Id int32 `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	//订单编号（order_main.order_sn）
	OrderSn string `xorm:"not null default '''' comment('主订单号') index VARCHAR(63)"`
	////订单状态：0已取消,10(默认)未付款,20已付款,30已完成'（order_main.order_status）
	//OrderStatus int32 `json:"order_status"`
	//宠物id
	PetId string `xorm:"not null default '''' comment('宠物id') index VARCHAR(63)"`
	//宠物头像
	PetAvatar string `xorm:"default '''' comment('宠物头像') index VARCHAR(63)"`
	//宠物名字
	PetName string `xorm:"default '''' comment('宠物名字') index VARCHAR(63)"`
	//宠物种类大分类
	PetKindof string `xorm:"default '''' comment('宠物种类大分类') index VARCHAR(63)"`
	//种类
	PetVariety string `xorm:"default '''' comment('种类') index VARCHAR(63)"`
	//生日例如：2021-10-01 00:00:00
	PetBirthday string `xorm:"comment('结束问诊时间') DATETIME"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `xorm:"default 0 comment('1：已绝育 0：未绝育') INT(1)"`
	//性别：1GG,2MM
	PetSex int32 `xorm:"default 0 comment('性别：1GG,2MM') INT(1)"`

	//用户id
	ScrmUserId string `xorm:"not null default '''' comment('用户id') index VARCHAR(63)"`
	//医生编号
	DoctorCode string `xorm:"default '''' comment('医生编号') index VARCHAR(63)"`
	//医生类别：1门店医生，2互联网医生
	DoctorType int32 `xorm:"default 0 comment('医生类别：1门店医生，2互联网医生') INT(1)"`
	//问诊单状态：1待接入，2待回复，3问诊中，4已完成，5已取消
	State int32 `xorm:"default 0 comment('问诊单状态：1待接入，2待回复，3问诊中，4已完成，5已取消') INT(1)"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `xorm:"default 0 comment('问诊项目：1-免费义诊，2-快速咨询，3-找医生') INT(1)"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `xorm:"default 0 comment('问诊形式：1-图文，2-电话，3-视频') INT(1)"`
	//问诊费用(单位分)
	Amount int32 `xorm:"default 0 comment('问诊费用(单位分)') INT(11)"`
	//问诊时长（单位分钟）
	Duration int32 `xorm:"default 0 comment('问诊时长（单位分钟）') INT(11)"`
	//医生接入时间
	DoctorJoin time.Time `xorm:"comment('医生接入时间') DATETIME"`
	//真实结束问诊时间
	DiagnoseFinishTime time.Time `xorm:"comment('结束问诊时间') DATETIME"`
	//正常结束问诊时间（医生接入时间+问诊时长）
	CountdownFinishTime time.Time `json:"countdown_finish_time"`
	//医生首次回复时间
	FirstReplyTime time.Time `xorm:"comment('医生首次回复时间') DATETIME"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `json:"immune_status"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `json:"symptom"`
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	SymptomDesc string `json:"symptom_desc"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `json:"symptom_recent"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `json:"image"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `json:"have_hospital"`
	//就诊过的医院名称
	HaveHospitalName string `json:"have_hospital_name"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `json:"have_hospital_result"`
	//历史就诊的检查照片/药品照片
	HaveHospitalImage string `json:"have_hospital_image"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `json:"medical_history"`
	//其他病史信息
	MedicalHistoryInfo string `json:"medical_history_info"`
	//是否评价：1未评价，2已评价
	IsEvaluate int32 `json:"is_evaluate"`
	//创建日期
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	//最后更新时间
	UpdateTime time.Time `json:"update_time"`
}

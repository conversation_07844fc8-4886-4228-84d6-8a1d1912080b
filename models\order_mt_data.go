package models

import "time"

type OrderMtData struct {
	OrderSn       string    `xorm:"not null comment('美团订单号') VARCHAR(50)"`
	Data          string    `xorm:"not null comment('mt 提交订单json 数据') TEXT "` //
	ProcessStatus int       `xorm:"not null comment('1:初始化,2:处理中,3:处理完成') int(11)"`
	Reason        string    `xorm:"default 'NULL' comment('申请退款的原因') VARCHAR(500)"`
	CreateTime    time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
}

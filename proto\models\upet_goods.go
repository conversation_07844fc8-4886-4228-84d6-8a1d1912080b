package models

type UpetGoods struct {
	GoodsId              int32    `json:"goods_id"`                             //商品id(SKU)
	GoodsCommonid        int32    `json:"goods_commonid"`                       //商品公共表id
	GoodsName            string   `json:"goods_name"`                           //商品名称（+规格名称）
	ShortName            string   `json:"short_name"`                           //商品短标题（+规格名称）
	GoodsJingle          string   `json:"goods_jingle"`                         //商品广告词
	StoreId              int32    `json:"store_id"`                             //店铺id
	StoreName            string   `json:"store_name"`                           //店铺名称
	GcId                 int32    `json:"gc_id"`                                //商品分类id
	GcId1                int32    `json:"gc_id_1" xorm:"gc_id_1"`               //一级分类id
	GcId2                int32    `json:"gc_id_2" xorm:"gc_id_2"`               //二级分类id
	GcId3                int32    `json:"gc_id_3" xorm:"gc_id_3"`               //三级分类id
	BrandId              int32    `json:"brand_id"`                             //品牌id
	GoodsPrice           float64  `json:"goods_price"`                          //商品价格
	GoodsPromotionPrice  float64  `json:"goods_promotion_price"`                //商品促销价格
	GoodsPromotionType   int32    `json:"goods_promotion_type"`                 //促销类型 0无促销，1团购，2限时折扣
	GoodsMarketprice     float64  `json:"goods_marketprice"`                    //市场价
	GoodsSerial          string   `json:"goods_serial"`                         //商品货号
	GoodsStorageAlarm    int32    `json:"goods_storage_alarm"`                  //库存报警值
	GoodsBarcode         string   `json:"goods_barcode"`                        //商品条形码
	GoodsClick           int32    `json:"goods_click"`                          //商品点击数量
	GoodsSalenum         int32    `json:"goods_salenum"`                        //销售数量
	GoodsCollect         int32    `json:"goods_collect"`                        //收藏数量
	SpecName             string   `json:"spec_name"`                            //规格名称
	GoodsSpec            string   `json:"goods_spec"`                           //商品规格序列化
	GoodsStorage         int32    `json:"goods_storage"`                        //商品库存
	GoodsImage           string   `json:"goods_image"`                          //商品主图
	GoodsBody            string   `json:"goods_body"`                           //商品描述
	MobileBody           string   `json:"mobile_body"`                          //手机端商品描述
	GoodsState           int32    `json:"goods_state"`                          //商品状态 0下架，1正常，10违规（禁售）
	GoodsVerify          int32    `json:"goods_verify"`                         //商品审核 1通过，0未通过，10审核中
	GoodsAddtime         int32    `json:"goods_addtime"`                        //商品添加时间
	GoodsEdittime        int32    `json:"goods_edittime"`                       //商品编辑时间
	Areaid1              int32    `json:"areaid_1" xorm:"areaid_1"`             //一级地区id
	Areaid2              int32    `json:"areaid_2" xorm:"areaid_2"`             //二级地区id
	ColorId              int32    `json:"color_id"`                             //颜色规格id
	TransportId          int32    `json:"transport_id"`                         //运费模板id
	GoodsFreight         float64  `json:"goods_freight"`                        //运费 0为免运费
	GoodsVat             int32    `json:"goods_vat"`                            //是否开具增值税发票 1是，0否
	GoodsCommend         int32    `json:"goods_commend"`                        //商品推荐 1是，0否 默认0
	GoodsStcids          string   `json:"goods_stcids"`                         //店铺分类id 首尾用,隔开
	EvaluationGoodStar   int32    `json:"evaluation_good_star"`                 //好评星级
	EvaluationCount      int32    `json:"evaluation_count"`                     //评价数
	IsVirtual            int32    `json:"is_virtual"`                           //是否为虚拟商品 1是，0否
	VirtualIndate        int32    `json:"virtual_indate"`                       //虚拟商品有效期
	VirtualLimit         int32    `json:"virtual_limit"`                        //虚拟商品购买上限
	VirtualInvalidRefund int32    `json:"virtual_invalid_refund"`               //是否允许过期退款， 1是，0否
	IsFcode              int32    `json:"is_fcode"`                             //是否为F码商品 1是，0否
	IsPresell            int32    `json:"is_presell"`                           //是否是预售商品 1是，0否
	PresellDeliverdate   int32    `json:"presell_deliverdate"`                  //预售商品发货时间
	IsBook               int32    `json:"is_book"`                              //是否为预定商品，1是，0否
	BookDownPayment      float64  `json:"book_down_payment"`                    //定金金额
	BookFinalPayment     float64  `json:"book_final_payment"`                   //尾款金额
	BookDownTime         int32    `json:"book_down_time"`                       //预定结束时间
	BookBuyers           int32    `json:"book_buyers"`                          //预定人数
	HaveGift             int32    `json:"have_gift"`                            //是否拥有赠品
	IsOwnShop            int32    `json:"is_own_shop"`                          //是否为平台自营
	Contract1            bool     `json:"contract_1" xorm:"contract_1"`         //消费者保障服务状态 0关闭 1开启
	Contract2            bool     `json:"contract_2" xorm:"contract_2"`         //消费者保障服务状态 0关闭 1开启
	Contract3            bool     `json:"contract_3" xorm:"contract_3"`         //消费者保障服务状态 0关闭 1开启
	Contract4            bool     `json:"contract_4" xorm:"contract_4"`         //消费者保障服务状态 0关闭 1开启
	Contract5            bool     `json:"contract_5" xorm:"contract_5"`         //消费者保障服务状态 0关闭 1开启
	Contract6            bool     `json:"contract_6" xorm:"contract_6"`         //消费者保障服务状态 0关闭 1开启
	Contract7            bool     `json:"contract_7" xorm:"contract_7"`         //消费者保障服务状态 0关闭 1开启
	Contract8            bool     `json:"contract_8" xorm:"contract_8"`         //消费者保障服务状态 0关闭 1开启
	Contract9            bool     `json:"contract_9" xorm:"contract_9"`         //消费者保障服务状态 0关闭 1开启
	Contract10           bool     `json:"contract_10" xorm:"contract_10"`       //消费者保障服务状态 0关闭 1开启
	IsChain              bool     `json:"is_chain"`                             //是否为门店商品 1是，0否
	GoodsTransV          float64  `json:"goods_trans_v"`                        //重量或体积
	IsDis                int32    `json:"is_dis"`                               //是否分销
	IsBatch              bool     `json:"is_batch"`                             //是否批发商品 0零售  1批发
	BatchPrice           string   `json:"batch_price"`                          //批发阶梯价
	GoodsInv             int32    `json:"goods_inv"`                            //是否开发票
	MemberPrice1         float64  `json:"member_price_1" xorm:"member_price_1"` //会员等级价v1
	MemberPrice2         float64  `json:"member_price_2" xorm:"member_price_2"` //会员等级价v2
	MemberPrice3         float64  `json:"member_price_3" xorm:"member_price_3"` //会员等级价v3
	GoodsLimit           int32    `json:"goods_limit"`                          //限购：0不限购
	GoodsRecommendNum    int32    `json:"goods_recommend_num"`                  //APP推荐次数
	GSearchStatus        bool     `json:"g_search_status"`                      //是否是允许搜索商品 1否，0是
	GCType               int32    `json:"g_c_type"`                             //1猫站，2狗站
	Freight              int32    `json:"freight"`                              //是否包邮0为不包邮，2为包邮
	ChainId              int32    `json:"chain_id"`                             //记录商品归属门店id
	RegionId             int32    `json:"region_id"`                            //大区标识
	GoodPercent          int32    `json:"good_percent"`                         //好评率
	GoodsSku             string   `json:"goods_sku"`                            //中心货号
	GoodsSkuType         int32    `json:"goods_sku_type"`                       //货号类型 1全渠道 2管易 3门店
	IsVip                bool     `json:"is_vip"`                               //是否限制会员卡用户购买，0否，1是
	IsBzk                bool     `json:"is_bzk"`                               //是否限制保障卡用户购买，0否，1是
	RelevanceId          int32    `json:"relevance_id"`                         //关联索引ID
	Tags                 string   `json:"tags"`                                 //电商商品tag
	Cities               []string `json:"cities"`                               //商品适用城市，仅虚拟商品有效
	ChannelCategoryName  string   `json:"channel_category_name"`                //分类
	GoodsType            int32    `json:"goods_type"`                           //0普通商品，1实实组合，2虚虚组合，3虚实组合
	HasStock             int32    `json:"has_stock" xorm:"default 0 comment('有无库存') TINYINT(1)"`
	IsOpenVirtualStock   int32    `json:"is_open_virtual_stock" xorm:"default 0 comment('是否开启虚拟库存，0默认关闭，1-开启') TINYINT(3)"`
	VipState             int32    `json:"vip_state"` //是否VIP
}

type UpetGoodsExt struct {
	UpetGoods `xorm:"extends"`
	ShopIds   string `json:"shop_id"`
}

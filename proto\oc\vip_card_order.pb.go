// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/vip_card_order.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/golang/protobuf/ptypes/any"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PVCEditRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//会员卡号
	VirtualCard          string   `protobuf:"bytes,2,opt,name=virtual_card,json=virtualCard,proto3" json:"virtual_card"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCEditRequest) Reset()         { *m = PVCEditRequest{} }
func (m *PVCEditRequest) String() string { return proto.CompactTextString(m) }
func (*PVCEditRequest) ProtoMessage()    {}
func (*PVCEditRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{0}
}

func (m *PVCEditRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCEditRequest.Unmarshal(m, b)
}
func (m *PVCEditRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCEditRequest.Marshal(b, m, deterministic)
}
func (m *PVCEditRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCEditRequest.Merge(m, src)
}
func (m *PVCEditRequest) XXX_Size() int {
	return xxx_messageInfo_PVCEditRequest.Size(m)
}
func (m *PVCEditRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCEditRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PVCEditRequest proto.InternalMessageInfo

func (m *PVCEditRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PVCEditRequest) GetVirtualCard() string {
	if m != nil {
		return m.VirtualCard
	}
	return ""
}

type PVCEditResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCEditResponse) Reset()         { *m = PVCEditResponse{} }
func (m *PVCEditResponse) String() string { return proto.CompactTextString(m) }
func (*PVCEditResponse) ProtoMessage()    {}
func (*PVCEditResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{1}
}

func (m *PVCEditResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCEditResponse.Unmarshal(m, b)
}
func (m *PVCEditResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCEditResponse.Marshal(b, m, deterministic)
}
func (m *PVCEditResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCEditResponse.Merge(m, src)
}
func (m *PVCEditResponse) XXX_Size() int {
	return xxx_messageInfo_PVCEditResponse.Size(m)
}
func (m *PVCEditResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCEditResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PVCEditResponse proto.InternalMessageInfo

func (m *PVCEditResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PVCEditResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type PVCExpressEditRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 物流单号
	ShippingCode string `protobuf:"bytes,2,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	// 物流公司编码
	ShippingEcode        string   `protobuf:"bytes,3,opt,name=shipping_ecode,json=shippingEcode,proto3" json:"shipping_ecode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressEditRequest) Reset()         { *m = PVCExpressEditRequest{} }
func (m *PVCExpressEditRequest) String() string { return proto.CompactTextString(m) }
func (*PVCExpressEditRequest) ProtoMessage()    {}
func (*PVCExpressEditRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{2}
}

func (m *PVCExpressEditRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressEditRequest.Unmarshal(m, b)
}
func (m *PVCExpressEditRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressEditRequest.Marshal(b, m, deterministic)
}
func (m *PVCExpressEditRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressEditRequest.Merge(m, src)
}
func (m *PVCExpressEditRequest) XXX_Size() int {
	return xxx_messageInfo_PVCExpressEditRequest.Size(m)
}
func (m *PVCExpressEditRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressEditRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressEditRequest proto.InternalMessageInfo

func (m *PVCExpressEditRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PVCExpressEditRequest) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *PVCExpressEditRequest) GetShippingEcode() string {
	if m != nil {
		return m.ShippingEcode
	}
	return ""
}

type PVCExpressEditResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressEditResponse) Reset()         { *m = PVCExpressEditResponse{} }
func (m *PVCExpressEditResponse) String() string { return proto.CompactTextString(m) }
func (*PVCExpressEditResponse) ProtoMessage()    {}
func (*PVCExpressEditResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{3}
}

func (m *PVCExpressEditResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressEditResponse.Unmarshal(m, b)
}
func (m *PVCExpressEditResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressEditResponse.Marshal(b, m, deterministic)
}
func (m *PVCExpressEditResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressEditResponse.Merge(m, src)
}
func (m *PVCExpressEditResponse) XXX_Size() int {
	return xxx_messageInfo_PVCExpressEditResponse.Size(m)
}
func (m *PVCExpressEditResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressEditResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressEditResponse proto.InternalMessageInfo

func (m *PVCExpressEditResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PVCExpressEditResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetPhysicalVipCardOrderDetailRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhysicalVipCardOrderDetailRequest) Reset()         { *m = GetPhysicalVipCardOrderDetailRequest{} }
func (m *GetPhysicalVipCardOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetPhysicalVipCardOrderDetailRequest) ProtoMessage()    {}
func (*GetPhysicalVipCardOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{4}
}

func (m *GetPhysicalVipCardOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest.Unmarshal(m, b)
}
func (m *GetPhysicalVipCardOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetPhysicalVipCardOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest.Merge(m, src)
}
func (m *GetPhysicalVipCardOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest.Size(m)
}
func (m *GetPhysicalVipCardOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhysicalVipCardOrderDetailRequest proto.InternalMessageInfo

func (m *GetPhysicalVipCardOrderDetailRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type GetPhysicalVipCardOrderDetailResponse struct {
	Code                 int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PhysicalVipCardOrderDetail `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetPhysicalVipCardOrderDetailResponse) Reset()         { *m = GetPhysicalVipCardOrderDetailResponse{} }
func (m *GetPhysicalVipCardOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetPhysicalVipCardOrderDetailResponse) ProtoMessage()    {}
func (*GetPhysicalVipCardOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{5}
}

func (m *GetPhysicalVipCardOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse.Unmarshal(m, b)
}
func (m *GetPhysicalVipCardOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetPhysicalVipCardOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse.Merge(m, src)
}
func (m *GetPhysicalVipCardOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse.Size(m)
}
func (m *GetPhysicalVipCardOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhysicalVipCardOrderDetailResponse proto.InternalMessageInfo

func (m *GetPhysicalVipCardOrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPhysicalVipCardOrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPhysicalVipCardOrderDetailResponse) GetData() *PhysicalVipCardOrderDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type PhysicalVipCardOrderDetail struct {
	//订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 支付时间
	PaymentTime string `protobuf:"bytes,2,opt,name=payment_time,json=paymentTime,proto3" json:"payment_time"`
	//订单来源：'1:PC端,2:移动端,3:APP端,4:ERP,5:智慧门店,6:有赞,7:阿闻宠物,8:阿闻商城
	OrderFrom int32 `protobuf:"varint,3,opt,name=order_from,json=orderFrom,proto3" json:"order_from"`
	//订单来源文本
	OrderFromText string `protobuf:"bytes,4,opt,name=order_from_text,json=orderFromText,proto3" json:"order_from_text"`
	//卡号
	VirtualCardId int64 `protobuf:"varint,5,opt,name=virtual_card_id,json=virtualCardId,proto3" json:"virtual_card_id"`
	//卡号
	VirtualCardIdText string `protobuf:"bytes,6,opt,name=virtual_card_id_text,json=virtualCardIdText,proto3" json:"virtual_card_id_text"`
	//订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货
	OrderState int32 `protobuf:"varint,7,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	//订单状态文本
	OrderStateText string `protobuf:"bytes,8,opt,name=order_state_text,json=orderStateText,proto3" json:"order_state_text"`
	//支付方式：
	PaymentCode     string `protobuf:"bytes,9,opt,name=payment_code,json=paymentCode,proto3" json:"payment_code"`
	PaymentCodeText string `protobuf:"bytes,10,opt,name=payment_code_text,json=paymentCodeText,proto3" json:"payment_code_text"`
	//订单备注
	OrderMessage string `protobuf:"bytes,11,opt,name=order_message,json=orderMessage,proto3" json:"order_message"`
	//会员编号
	ScrmUserId string `protobuf:"bytes,12,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//会员手机
	BuyerPhone string `protobuf:"bytes,13,opt,name=buyer_phone,json=buyerPhone,proto3" json:"buyer_phone"`
	//会员加密手机号
	EncryptMobile string `protobuf:"bytes,14,opt,name=encrypt_mobile,json=encryptMobile,proto3" json:"encrypt_mobile"`
	//会员账号
	BuyName              string          `protobuf:"bytes,15,opt,name=buy_name,json=buyName,proto3" json:"buy_name"`
	Address              *Address1       `protobuf:"bytes,16,opt,name=address,proto3" json:"address"`
	Goods                []*Goods        `protobuf:"bytes,17,rep,name=goods,proto3" json:"goods"`
	Delivery             *DeliveryInfo   `protobuf:"bytes,18,opt,name=delivery,proto3" json:"delivery"`
	Steps                []*UpetOrderLog `protobuf:"bytes,19,rep,name=steps,proto3" json:"steps"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PhysicalVipCardOrderDetail) Reset()         { *m = PhysicalVipCardOrderDetail{} }
func (m *PhysicalVipCardOrderDetail) String() string { return proto.CompactTextString(m) }
func (*PhysicalVipCardOrderDetail) ProtoMessage()    {}
func (*PhysicalVipCardOrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{6}
}

func (m *PhysicalVipCardOrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalVipCardOrderDetail.Unmarshal(m, b)
}
func (m *PhysicalVipCardOrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalVipCardOrderDetail.Marshal(b, m, deterministic)
}
func (m *PhysicalVipCardOrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalVipCardOrderDetail.Merge(m, src)
}
func (m *PhysicalVipCardOrderDetail) XXX_Size() int {
	return xxx_messageInfo_PhysicalVipCardOrderDetail.Size(m)
}
func (m *PhysicalVipCardOrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalVipCardOrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalVipCardOrderDetail proto.InternalMessageInfo

func (m *PhysicalVipCardOrderDetail) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetPaymentTime() string {
	if m != nil {
		return m.PaymentTime
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetOrderFrom() int32 {
	if m != nil {
		return m.OrderFrom
	}
	return 0
}

func (m *PhysicalVipCardOrderDetail) GetOrderFromText() string {
	if m != nil {
		return m.OrderFromText
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetVirtualCardId() int64 {
	if m != nil {
		return m.VirtualCardId
	}
	return 0
}

func (m *PhysicalVipCardOrderDetail) GetVirtualCardIdText() string {
	if m != nil {
		return m.VirtualCardIdText
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

func (m *PhysicalVipCardOrderDetail) GetOrderStateText() string {
	if m != nil {
		return m.OrderStateText
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetPaymentCode() string {
	if m != nil {
		return m.PaymentCode
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetPaymentCodeText() string {
	if m != nil {
		return m.PaymentCodeText
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetOrderMessage() string {
	if m != nil {
		return m.OrderMessage
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetBuyerPhone() string {
	if m != nil {
		return m.BuyerPhone
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetEncryptMobile() string {
	if m != nil {
		return m.EncryptMobile
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetBuyName() string {
	if m != nil {
		return m.BuyName
	}
	return ""
}

func (m *PhysicalVipCardOrderDetail) GetAddress() *Address1 {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *PhysicalVipCardOrderDetail) GetGoods() []*Goods {
	if m != nil {
		return m.Goods
	}
	return nil
}

func (m *PhysicalVipCardOrderDetail) GetDelivery() *DeliveryInfo {
	if m != nil {
		return m.Delivery
	}
	return nil
}

func (m *PhysicalVipCardOrderDetail) GetSteps() []*UpetOrderLog {
	if m != nil {
		return m.Steps
	}
	return nil
}

type DeliveryInfo struct {
	//物流单号
	ShippingCode string `protobuf:"bytes,1,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	//物流公司名称
	EName string `protobuf:"bytes,2,opt,name=e_name,json=eName,proto3" json:"e_name"`
	//配送时间
	ShippingTime         string   `protobuf:"bytes,3,opt,name=shipping_time,json=shippingTime,proto3" json:"shipping_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryInfo) Reset()         { *m = DeliveryInfo{} }
func (m *DeliveryInfo) String() string { return proto.CompactTextString(m) }
func (*DeliveryInfo) ProtoMessage()    {}
func (*DeliveryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{7}
}

func (m *DeliveryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInfo.Unmarshal(m, b)
}
func (m *DeliveryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInfo.Marshal(b, m, deterministic)
}
func (m *DeliveryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInfo.Merge(m, src)
}
func (m *DeliveryInfo) XXX_Size() int {
	return xxx_messageInfo_DeliveryInfo.Size(m)
}
func (m *DeliveryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInfo proto.InternalMessageInfo

func (m *DeliveryInfo) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *DeliveryInfo) GetEName() string {
	if m != nil {
		return m.EName
	}
	return ""
}

func (m *DeliveryInfo) GetShippingTime() string {
	if m != nil {
		return m.ShippingTime
	}
	return ""
}

type UpetOrderLog struct {
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//处理时间
	LogTime string `protobuf:"bytes,2,opt,name=log_time,json=logTime,proto3" json:"log_time"`
	//订单状态：0(已取消)10:未付款;20:已付款;30:已发货;40:已收货;
	LogOrderstateText    string   `protobuf:"bytes,3,opt,name=log_orderstate_text,json=logOrderstateText,proto3" json:"log_orderstate_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetOrderLog) Reset()         { *m = UpetOrderLog{} }
func (m *UpetOrderLog) String() string { return proto.CompactTextString(m) }
func (*UpetOrderLog) ProtoMessage()    {}
func (*UpetOrderLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{8}
}

func (m *UpetOrderLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetOrderLog.Unmarshal(m, b)
}
func (m *UpetOrderLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetOrderLog.Marshal(b, m, deterministic)
}
func (m *UpetOrderLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetOrderLog.Merge(m, src)
}
func (m *UpetOrderLog) XXX_Size() int {
	return xxx_messageInfo_UpetOrderLog.Size(m)
}
func (m *UpetOrderLog) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetOrderLog.DiscardUnknown(m)
}

var xxx_messageInfo_UpetOrderLog proto.InternalMessageInfo

func (m *UpetOrderLog) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *UpetOrderLog) GetLogTime() string {
	if m != nil {
		return m.LogTime
	}
	return ""
}

func (m *UpetOrderLog) GetLogOrderstateText() string {
	if m != nil {
		return m.LogOrderstateText
	}
	return ""
}

type PVCExpressImportListRequest struct {
	// 页码，不传默认为1
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportListRequest) Reset()         { *m = PVCExpressImportListRequest{} }
func (m *PVCExpressImportListRequest) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportListRequest) ProtoMessage()    {}
func (*PVCExpressImportListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{9}
}

func (m *PVCExpressImportListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportListRequest.Unmarshal(m, b)
}
func (m *PVCExpressImportListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportListRequest.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportListRequest.Merge(m, src)
}
func (m *PVCExpressImportListRequest) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportListRequest.Size(m)
}
func (m *PVCExpressImportListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportListRequest proto.InternalMessageInfo

func (m *PVCExpressImportListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PVCExpressImportListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type PVCExpressImportListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*PVCExpressImportListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int64    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportListResponse) Reset()         { *m = PVCExpressImportListResponse{} }
func (m *PVCExpressImportListResponse) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportListResponse) ProtoMessage()    {}
func (*PVCExpressImportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{10}
}

func (m *PVCExpressImportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportListResponse.Unmarshal(m, b)
}
func (m *PVCExpressImportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportListResponse.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportListResponse.Merge(m, src)
}
func (m *PVCExpressImportListResponse) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportListResponse.Size(m)
}
func (m *PVCExpressImportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportListResponse proto.InternalMessageInfo

func (m *PVCExpressImportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PVCExpressImportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PVCExpressImportListResponse) GetData() []*PVCExpressImportListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PVCExpressImportListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PVCExpressImportListResponse_List struct {
	// 操作时间
	CreatedAt string `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结果url
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	// 导入结果文本
	Result               string   `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportListResponse_List) Reset()         { *m = PVCExpressImportListResponse_List{} }
func (m *PVCExpressImportListResponse_List) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportListResponse_List) ProtoMessage()    {}
func (*PVCExpressImportListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{10, 0}
}

func (m *PVCExpressImportListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportListResponse_List.Unmarshal(m, b)
}
func (m *PVCExpressImportListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportListResponse_List.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportListResponse_List.Merge(m, src)
}
func (m *PVCExpressImportListResponse_List) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportListResponse_List.Size(m)
}
func (m *PVCExpressImportListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportListResponse_List proto.InternalMessageInfo

func (m *PVCExpressImportListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *PVCExpressImportListResponse_List) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PVCExpressImportListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

type PhysicalVipCardOrderExportListRequest struct {
	// 页码，不传默认为1
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhysicalVipCardOrderExportListRequest) Reset()         { *m = PhysicalVipCardOrderExportListRequest{} }
func (m *PhysicalVipCardOrderExportListRequest) String() string { return proto.CompactTextString(m) }
func (*PhysicalVipCardOrderExportListRequest) ProtoMessage()    {}
func (*PhysicalVipCardOrderExportListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{11}
}

func (m *PhysicalVipCardOrderExportListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalVipCardOrderExportListRequest.Unmarshal(m, b)
}
func (m *PhysicalVipCardOrderExportListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalVipCardOrderExportListRequest.Marshal(b, m, deterministic)
}
func (m *PhysicalVipCardOrderExportListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalVipCardOrderExportListRequest.Merge(m, src)
}
func (m *PhysicalVipCardOrderExportListRequest) XXX_Size() int {
	return xxx_messageInfo_PhysicalVipCardOrderExportListRequest.Size(m)
}
func (m *PhysicalVipCardOrderExportListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalVipCardOrderExportListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalVipCardOrderExportListRequest proto.InternalMessageInfo

func (m *PhysicalVipCardOrderExportListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PhysicalVipCardOrderExportListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type PhysicalVipCardOrderExportListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                                         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*PhysicalVipCardOrderExportListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int64    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhysicalVipCardOrderExportListResponse) Reset() {
	*m = PhysicalVipCardOrderExportListResponse{}
}
func (m *PhysicalVipCardOrderExportListResponse) String() string { return proto.CompactTextString(m) }
func (*PhysicalVipCardOrderExportListResponse) ProtoMessage()    {}
func (*PhysicalVipCardOrderExportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{12}
}

func (m *PhysicalVipCardOrderExportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse.Unmarshal(m, b)
}
func (m *PhysicalVipCardOrderExportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse.Marshal(b, m, deterministic)
}
func (m *PhysicalVipCardOrderExportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalVipCardOrderExportListResponse.Merge(m, src)
}
func (m *PhysicalVipCardOrderExportListResponse) XXX_Size() int {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse.Size(m)
}
func (m *PhysicalVipCardOrderExportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalVipCardOrderExportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalVipCardOrderExportListResponse proto.InternalMessageInfo

func (m *PhysicalVipCardOrderExportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PhysicalVipCardOrderExportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PhysicalVipCardOrderExportListResponse) GetData() []*PhysicalVipCardOrderExportListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PhysicalVipCardOrderExportListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PhysicalVipCardOrderExportListResponse_List struct {
	// 操作时间
	CreatedAt string `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 状态 0处理中、1成功、2失败
	State int32 `protobuf:"varint,2,opt,name=state,proto3" json:"state"`
	// 状态文本
	StateText string `protobuf:"bytes,3,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	// 当state = 1时返回链接
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	// 当state = 2返回失败原因
	Result               string   `protobuf:"bytes,5,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhysicalVipCardOrderExportListResponse_List) Reset() {
	*m = PhysicalVipCardOrderExportListResponse_List{}
}
func (m *PhysicalVipCardOrderExportListResponse_List) String() string {
	return proto.CompactTextString(m)
}
func (*PhysicalVipCardOrderExportListResponse_List) ProtoMessage() {}
func (*PhysicalVipCardOrderExportListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{12, 0}
}

func (m *PhysicalVipCardOrderExportListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List.Unmarshal(m, b)
}
func (m *PhysicalVipCardOrderExportListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List.Marshal(b, m, deterministic)
}
func (m *PhysicalVipCardOrderExportListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List.Merge(m, src)
}
func (m *PhysicalVipCardOrderExportListResponse_List) XXX_Size() int {
	return xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List.Size(m)
}
func (m *PhysicalVipCardOrderExportListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalVipCardOrderExportListResponse_List proto.InternalMessageInfo

func (m *PhysicalVipCardOrderExportListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *PhysicalVipCardOrderExportListResponse_List) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *PhysicalVipCardOrderExportListResponse_List) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *PhysicalVipCardOrderExportListResponse_List) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PhysicalVipCardOrderExportListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

type PVCExpressImportRequest struct {
	// 文件字节流
	File                 []byte   `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportRequest) Reset()         { *m = PVCExpressImportRequest{} }
func (m *PVCExpressImportRequest) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportRequest) ProtoMessage()    {}
func (*PVCExpressImportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{13}
}

func (m *PVCExpressImportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportRequest.Unmarshal(m, b)
}
func (m *PVCExpressImportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportRequest.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportRequest.Merge(m, src)
}
func (m *PVCExpressImportRequest) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportRequest.Size(m)
}
func (m *PVCExpressImportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportRequest proto.InternalMessageInfo

func (m *PVCExpressImportRequest) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

type PVCExpressImportResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportResponse) Reset()         { *m = PVCExpressImportResponse{} }
func (m *PVCExpressImportResponse) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportResponse) ProtoMessage()    {}
func (*PVCExpressImportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{14}
}

func (m *PVCExpressImportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportResponse.Unmarshal(m, b)
}
func (m *PVCExpressImportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportResponse.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportResponse.Merge(m, src)
}
func (m *PVCExpressImportResponse) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportResponse.Size(m)
}
func (m *PVCExpressImportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportResponse proto.InternalMessageInfo

func (m *PVCExpressImportResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PVCExpressImportResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type PVCExpressImportTemplateRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportTemplateRequest) Reset()         { *m = PVCExpressImportTemplateRequest{} }
func (m *PVCExpressImportTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportTemplateRequest) ProtoMessage()    {}
func (*PVCExpressImportTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{15}
}

func (m *PVCExpressImportTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportTemplateRequest.Unmarshal(m, b)
}
func (m *PVCExpressImportTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportTemplateRequest.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportTemplateRequest.Merge(m, src)
}
func (m *PVCExpressImportTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportTemplateRequest.Size(m)
}
func (m *PVCExpressImportTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportTemplateRequest proto.InternalMessageInfo

type PVCExpressImportTemplateResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	Template             []byte   `protobuf:"bytes,3,opt,name=template,proto3" json:"template"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PVCExpressImportTemplateResponse) Reset()         { *m = PVCExpressImportTemplateResponse{} }
func (m *PVCExpressImportTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*PVCExpressImportTemplateResponse) ProtoMessage()    {}
func (*PVCExpressImportTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{16}
}

func (m *PVCExpressImportTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PVCExpressImportTemplateResponse.Unmarshal(m, b)
}
func (m *PVCExpressImportTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PVCExpressImportTemplateResponse.Marshal(b, m, deterministic)
}
func (m *PVCExpressImportTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PVCExpressImportTemplateResponse.Merge(m, src)
}
func (m *PVCExpressImportTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_PVCExpressImportTemplateResponse.Size(m)
}
func (m *PVCExpressImportTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PVCExpressImportTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PVCExpressImportTemplateResponse proto.InternalMessageInfo

func (m *PVCExpressImportTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PVCExpressImportTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PVCExpressImportTemplateResponse) GetTemplate() []byte {
	if m != nil {
		return m.Template
	}
	return nil
}

type GetPhysicalVipCardOrderExportResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhysicalVipCardOrderExportResponse) Reset()         { *m = GetPhysicalVipCardOrderExportResponse{} }
func (m *GetPhysicalVipCardOrderExportResponse) String() string { return proto.CompactTextString(m) }
func (*GetPhysicalVipCardOrderExportResponse) ProtoMessage()    {}
func (*GetPhysicalVipCardOrderExportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{17}
}

func (m *GetPhysicalVipCardOrderExportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhysicalVipCardOrderExportResponse.Unmarshal(m, b)
}
func (m *GetPhysicalVipCardOrderExportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhysicalVipCardOrderExportResponse.Marshal(b, m, deterministic)
}
func (m *GetPhysicalVipCardOrderExportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhysicalVipCardOrderExportResponse.Merge(m, src)
}
func (m *GetPhysicalVipCardOrderExportResponse) XXX_Size() int {
	return xxx_messageInfo_GetPhysicalVipCardOrderExportResponse.Size(m)
}
func (m *GetPhysicalVipCardOrderExportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhysicalVipCardOrderExportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhysicalVipCardOrderExportResponse proto.InternalMessageInfo

func (m *GetPhysicalVipCardOrderExportResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPhysicalVipCardOrderExportResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetPhysicalVipCardOrderListRequest struct {
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//订单编号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//用户手机号
	MemberMobile string `protobuf:"bytes,4,opt,name=member_mobile,json=memberMobile,proto3" json:"member_mobile"`
	//购买时间
	PaymentTimeStart string `protobuf:"bytes,5,opt,name=payment_time_start,json=paymentTimeStart,proto3" json:"payment_time_start"`
	PaymentTimeEnd   string `protobuf:"bytes,6,opt,name=payment_time_end,json=paymentTimeEnd,proto3" json:"payment_time_end"`
	//订单状态：-1(全部);0(已取消);10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货,
	OrderState           int32    `protobuf:"varint,7,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhysicalVipCardOrderListRequest) Reset()         { *m = GetPhysicalVipCardOrderListRequest{} }
func (m *GetPhysicalVipCardOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPhysicalVipCardOrderListRequest) ProtoMessage()    {}
func (*GetPhysicalVipCardOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{18}
}

func (m *GetPhysicalVipCardOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhysicalVipCardOrderListRequest.Unmarshal(m, b)
}
func (m *GetPhysicalVipCardOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhysicalVipCardOrderListRequest.Marshal(b, m, deterministic)
}
func (m *GetPhysicalVipCardOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhysicalVipCardOrderListRequest.Merge(m, src)
}
func (m *GetPhysicalVipCardOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPhysicalVipCardOrderListRequest.Size(m)
}
func (m *GetPhysicalVipCardOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhysicalVipCardOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhysicalVipCardOrderListRequest proto.InternalMessageInfo

func (m *GetPhysicalVipCardOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetPhysicalVipCardOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPhysicalVipCardOrderListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetPhysicalVipCardOrderListRequest) GetMemberMobile() string {
	if m != nil {
		return m.MemberMobile
	}
	return ""
}

func (m *GetPhysicalVipCardOrderListRequest) GetPaymentTimeStart() string {
	if m != nil {
		return m.PaymentTimeStart
	}
	return ""
}

func (m *GetPhysicalVipCardOrderListRequest) GetPaymentTimeEnd() string {
	if m != nil {
		return m.PaymentTimeEnd
	}
	return ""
}

func (m *GetPhysicalVipCardOrderListRequest) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

type GetPhysicalVipCardOrderListResponse struct {
	Code                 int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32                `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*PhysicalOrderData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPhysicalVipCardOrderListResponse) Reset()         { *m = GetPhysicalVipCardOrderListResponse{} }
func (m *GetPhysicalVipCardOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPhysicalVipCardOrderListResponse) ProtoMessage()    {}
func (*GetPhysicalVipCardOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{19}
}

func (m *GetPhysicalVipCardOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhysicalVipCardOrderListResponse.Unmarshal(m, b)
}
func (m *GetPhysicalVipCardOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhysicalVipCardOrderListResponse.Marshal(b, m, deterministic)
}
func (m *GetPhysicalVipCardOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhysicalVipCardOrderListResponse.Merge(m, src)
}
func (m *GetPhysicalVipCardOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPhysicalVipCardOrderListResponse.Size(m)
}
func (m *GetPhysicalVipCardOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhysicalVipCardOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhysicalVipCardOrderListResponse proto.InternalMessageInfo

func (m *GetPhysicalVipCardOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPhysicalVipCardOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPhysicalVipCardOrderListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetPhysicalVipCardOrderListResponse) GetData() []*PhysicalOrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

type PhysicalOrderData struct {
	//订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 支付时间
	PaymentTime string `protobuf:"bytes,2,opt,name=payment_time,json=paymentTime,proto3" json:"payment_time"`
	//订单来源：'1:PC端,2:移动端,3:APP端,4:ERP,5:智慧门店,6:有赞,7:阿闻宠物,8:阿闻商城
	OrderFrom int32 `protobuf:"varint,3,opt,name=order_from,json=orderFrom,proto3" json:"order_from"`
	//订单来源文本
	OrderFromText string `protobuf:"bytes,4,opt,name=order_from_text,json=orderFromText,proto3" json:"order_from_text"`
	//卡号
	VirtualCardId int64 `protobuf:"varint,5,opt,name=virtual_card_id,json=virtualCardId,proto3" json:"virtual_card_id"`
	//卡号
	VirtualCardIdText string `protobuf:"bytes,6,opt,name=virtual_card_id_text,json=virtualCardIdText,proto3" json:"virtual_card_id_text"`
	//订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货
	OrderState int32 `protobuf:"varint,7,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	//订单状态文本
	OrderStateText string `protobuf:"bytes,8,opt,name=order_state_text,json=orderStateText,proto3" json:"order_state_text"`
	//快递单号
	ShippingCode      string    `protobuf:"bytes,9,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	ShippingExpressId int32     `protobuf:"varint,10,opt,name=shipping_express_id,json=shippingExpressId,proto3" json:"shipping_express_id"`
	ECodeKdniao       string    `protobuf:"bytes,11,opt,name=e_code_kdniao,json=eCodeKdniao,proto3" json:"e_code_kdniao"`
	Address           *Address1 `protobuf:"bytes,12,opt,name=address,proto3" json:"address"`
	Goods             []*Goods  `protobuf:"bytes,13,rep,name=goods,proto3" json:"goods"`
	//是否可以修改卡号
	IsCanUp              int32    `protobuf:"varint,14,opt,name=is_can_up,json=isCanUp,proto3" json:"is_can_up"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhysicalOrderData) Reset()         { *m = PhysicalOrderData{} }
func (m *PhysicalOrderData) String() string { return proto.CompactTextString(m) }
func (*PhysicalOrderData) ProtoMessage()    {}
func (*PhysicalOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{20}
}

func (m *PhysicalOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalOrderData.Unmarshal(m, b)
}
func (m *PhysicalOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalOrderData.Marshal(b, m, deterministic)
}
func (m *PhysicalOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalOrderData.Merge(m, src)
}
func (m *PhysicalOrderData) XXX_Size() int {
	return xxx_messageInfo_PhysicalOrderData.Size(m)
}
func (m *PhysicalOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalOrderData proto.InternalMessageInfo

func (m *PhysicalOrderData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PhysicalOrderData) GetPaymentTime() string {
	if m != nil {
		return m.PaymentTime
	}
	return ""
}

func (m *PhysicalOrderData) GetOrderFrom() int32 {
	if m != nil {
		return m.OrderFrom
	}
	return 0
}

func (m *PhysicalOrderData) GetOrderFromText() string {
	if m != nil {
		return m.OrderFromText
	}
	return ""
}

func (m *PhysicalOrderData) GetVirtualCardId() int64 {
	if m != nil {
		return m.VirtualCardId
	}
	return 0
}

func (m *PhysicalOrderData) GetVirtualCardIdText() string {
	if m != nil {
		return m.VirtualCardIdText
	}
	return ""
}

func (m *PhysicalOrderData) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

func (m *PhysicalOrderData) GetOrderStateText() string {
	if m != nil {
		return m.OrderStateText
	}
	return ""
}

func (m *PhysicalOrderData) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *PhysicalOrderData) GetShippingExpressId() int32 {
	if m != nil {
		return m.ShippingExpressId
	}
	return 0
}

func (m *PhysicalOrderData) GetECodeKdniao() string {
	if m != nil {
		return m.ECodeKdniao
	}
	return ""
}

func (m *PhysicalOrderData) GetAddress() *Address1 {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *PhysicalOrderData) GetGoods() []*Goods {
	if m != nil {
		return m.Goods
	}
	return nil
}

func (m *PhysicalOrderData) GetIsCanUp() int32 {
	if m != nil {
		return m.IsCanUp
	}
	return 0
}

type Address1 struct {
	//顾 客
	ReciverName string `protobuf:"bytes,1,opt,name=reciver_name,json=reciverName,proto3" json:"reciver_name"`
	//电 话
	MobPhone string `protobuf:"bytes,2,opt,name=mob_phone,json=mobPhone,proto3" json:"mob_phone"`
	//加密电话
	EncryptMobile string `protobuf:"bytes,3,opt,name=encrypt_mobile,json=encryptMobile,proto3" json:"encrypt_mobile"`
	//收货地址
	ReciverAddress       string   `protobuf:"bytes,4,opt,name=reciver_address,json=reciverAddress,proto3" json:"reciver_address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Address1) Reset()         { *m = Address1{} }
func (m *Address1) String() string { return proto.CompactTextString(m) }
func (*Address1) ProtoMessage()    {}
func (*Address1) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{21}
}

func (m *Address1) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Address1.Unmarshal(m, b)
}
func (m *Address1) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Address1.Marshal(b, m, deterministic)
}
func (m *Address1) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Address1.Merge(m, src)
}
func (m *Address1) XXX_Size() int {
	return xxx_messageInfo_Address1.Size(m)
}
func (m *Address1) XXX_DiscardUnknown() {
	xxx_messageInfo_Address1.DiscardUnknown(m)
}

var xxx_messageInfo_Address1 proto.InternalMessageInfo

func (m *Address1) GetReciverName() string {
	if m != nil {
		return m.ReciverName
	}
	return ""
}

func (m *Address1) GetMobPhone() string {
	if m != nil {
		return m.MobPhone
	}
	return ""
}

func (m *Address1) GetEncryptMobile() string {
	if m != nil {
		return m.EncryptMobile
	}
	return ""
}

func (m *Address1) GetReciverAddress() string {
	if m != nil {
		return m.ReciverAddress
	}
	return ""
}

type Goods struct {
	//商品id(SKU)
	GoodsId int64 `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//商品主图
	GoodsImage string `protobuf:"bytes,2,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	//商品名称（+规格名称）
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	// 商品价格
	GoodsPrice float32 `protobuf:"fixed32,4,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//商品数量
	GoodsNum             int32    `protobuf:"varint,5,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Goods) Reset()         { *m = Goods{} }
func (m *Goods) String() string { return proto.CompactTextString(m) }
func (*Goods) ProtoMessage()    {}
func (*Goods) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{22}
}

func (m *Goods) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Goods.Unmarshal(m, b)
}
func (m *Goods) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Goods.Marshal(b, m, deterministic)
}
func (m *Goods) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Goods.Merge(m, src)
}
func (m *Goods) XXX_Size() int {
	return xxx_messageInfo_Goods.Size(m)
}
func (m *Goods) XXX_DiscardUnknown() {
	xxx_messageInfo_Goods.DiscardUnknown(m)
}

var xxx_messageInfo_Goods proto.InternalMessageInfo

func (m *Goods) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *Goods) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *Goods) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *Goods) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *Goods) GetGoodsNum() int32 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

type OrderData struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//订单号
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//订单金额(分)
	OrderAmount int32 `protobuf:"varint,3,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount"`
	//用户id
	UserId string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户等级
	UserLevelId int32 `protobuf:"varint,5,opt,name=user_level_id,json=userLevelId,proto3" json:"user_level_id"`
	//卡名称
	CardName string `protobuf:"bytes,6,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//卡类型
	CardType int32 `protobuf:"varint,7,opt,name=card_type,json=cardType,proto3" json:"card_type"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,8,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//支付时间
	PayTime string `protobuf:"bytes,9,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//状态 10-已支付 20-已退款
	State int32 `protobuf:"varint,10,opt,name=state,proto3" json:"state"`
	//会员卡有效期
	ExpiryDate string `protobuf:"bytes,11,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date"`
	//退款金额(分)
	RefundAmount int32 `protobuf:"varint,12,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//退款时间
	RefundTime string `protobuf:"bytes,13,opt,name=refund_time,json=refundTime,proto3" json:"refund_time"`
	CreateTime string `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime string `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//周期名称
	CycleName string `protobuf:"bytes,16,opt,name=cycle_name,json=cycleName,proto3" json:"cycle_name"`
	//卡类型名称
	CardTypeName string `protobuf:"bytes,17,opt,name=card_type_name,json=cardTypeName,proto3" json:"card_type_name"`
	// 手机号
	UserMobile string `protobuf:"bytes,18,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	//来源 0主动购买 1分销购买，2虚拟卡券兑换
	Source int32 `protobuf:"varint,19,opt,name=source,proto3" json:"source"`
	//卡密
	CardPass string `protobuf:"bytes,20,opt,name=card_pass,json=cardPass,proto3" json:"card_pass"`
	//大区
	Region string `protobuf:"bytes,21,opt,name=region,proto3" json:"region"`
	//省
	Province string `protobuf:"bytes,22,opt,name=province,proto3" json:"province"`
	//市
	City string `protobuf:"bytes,23,opt,name=city,proto3" json:"city"`
	//分销人名称
	DisMemberName string `protobuf:"bytes,24,opt,name=dis_member_name,json=disMemberName,proto3" json:"dis_member_name"`
	//分销人id
	DisMemberId string `protobuf:"bytes,25,opt,name=dis_member_id,json=disMemberId,proto3" json:"dis_member_id"`
	//分销佣金，分
	DisCommission int32 `protobuf:"varint,26,opt,name=dis_commission,json=disCommission,proto3" json:"dis_commission"`
	//卡号
	CardId string `protobuf:"bytes,27,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	//虚拟卡号
	VirtualCardId int64 `protobuf:"varint,28,opt,name=virtual_card_id,json=virtualCardId,proto3" json:"virtual_card_id"`
	//加密手机号
	EnUserMobile string `protobuf:"bytes,29,opt,name=en_user_mobile,json=enUserMobile,proto3" json:"en_user_mobile"`
	// 子龙门店id
	StoreId int32 `protobuf:"varint,30,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	// 门店名称
	StoreName string `protobuf:"bytes,31,opt,name=store_name,json=storeName,proto3" json:"store_name"`
	// 大区组织id
	OrgId int32 `protobuf:"varint,32,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	// 支付方式
	PaymentCode string `protobuf:"bytes,33,opt,name=payment_code,json=paymentCode,proto3" json:"payment_code"`
	// 卡模板id
	TId string `protobuf:"bytes,34,opt,name=t_id,json=tId,proto3" json:"t_id"`
	// 虚拟订单状态：0-已取消；10-未付款；20-已付款；40-已完成
	OrderState int32 `protobuf:"varint,35,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	// 会员卡是否过期：0-否，1-是
	IsExpiry int32 `protobuf:"varint,36,opt,name=is_expiry,json=isExpiry,proto3" json:"is_expiry"`
	//实体卡订单号
	EntityOrderSn        string   `protobuf:"bytes,37,opt,name=entity_order_sn,json=entityOrderSn,proto3" json:"entity_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderData) Reset()         { *m = OrderData{} }
func (m *OrderData) String() string { return proto.CompactTextString(m) }
func (*OrderData) ProtoMessage()    {}
func (*OrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{23}
}

func (m *OrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderData.Unmarshal(m, b)
}
func (m *OrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderData.Marshal(b, m, deterministic)
}
func (m *OrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderData.Merge(m, src)
}
func (m *OrderData) XXX_Size() int {
	return xxx_messageInfo_OrderData.Size(m)
}
func (m *OrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderData proto.InternalMessageInfo

func (m *OrderData) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OrderData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderData) GetOrderAmount() int32 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *OrderData) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *OrderData) GetUserLevelId() int32 {
	if m != nil {
		return m.UserLevelId
	}
	return 0
}

func (m *OrderData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *OrderData) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *OrderData) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *OrderData) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *OrderData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *OrderData) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *OrderData) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *OrderData) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

func (m *OrderData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *OrderData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *OrderData) GetCycleName() string {
	if m != nil {
		return m.CycleName
	}
	return ""
}

func (m *OrderData) GetCardTypeName() string {
	if m != nil {
		return m.CardTypeName
	}
	return ""
}

func (m *OrderData) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *OrderData) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *OrderData) GetCardPass() string {
	if m != nil {
		return m.CardPass
	}
	return ""
}

func (m *OrderData) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *OrderData) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *OrderData) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *OrderData) GetDisMemberName() string {
	if m != nil {
		return m.DisMemberName
	}
	return ""
}

func (m *OrderData) GetDisMemberId() string {
	if m != nil {
		return m.DisMemberId
	}
	return ""
}

func (m *OrderData) GetDisCommission() int32 {
	if m != nil {
		return m.DisCommission
	}
	return 0
}

func (m *OrderData) GetCardId() string {
	if m != nil {
		return m.CardId
	}
	return ""
}

func (m *OrderData) GetVirtualCardId() int64 {
	if m != nil {
		return m.VirtualCardId
	}
	return 0
}

func (m *OrderData) GetEnUserMobile() string {
	if m != nil {
		return m.EnUserMobile
	}
	return ""
}

func (m *OrderData) GetStoreId() int32 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *OrderData) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *OrderData) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *OrderData) GetPaymentCode() string {
	if m != nil {
		return m.PaymentCode
	}
	return ""
}

func (m *OrderData) GetTId() string {
	if m != nil {
		return m.TId
	}
	return ""
}

func (m *OrderData) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

func (m *OrderData) GetIsExpiry() int32 {
	if m != nil {
		return m.IsExpiry
	}
	return 0
}

func (m *OrderData) GetEntityOrderSn() string {
	if m != nil {
		return m.EntityOrderSn
	}
	return ""
}

type GetOrderListResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32        `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*OrderData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOrderListResponse) Reset()         { *m = GetOrderListResponse{} }
func (m *GetOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderListResponse) ProtoMessage()    {}
func (*GetOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{24}
}

func (m *GetOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListResponse.Unmarshal(m, b)
}
func (m *GetOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListResponse.Marshal(b, m, deterministic)
}
func (m *GetOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListResponse.Merge(m, src)
}
func (m *GetOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderListResponse.Size(m)
}
func (m *GetOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListResponse proto.InternalMessageInfo

func (m *GetOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetOrderListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetOrderListResponse) GetData() []*OrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetOrderListRequest struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//10-购卡 20-退卡
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	//用户id
	UserId string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//订单号
	OrderSn string `protobuf:"bytes,5,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 是否导出 1-购卡导出 2-退卡导出
	Export int32 `protobuf:"varint,6,opt,name=export,proto3" json:"export"`
	//导出ids
	Ids string `protobuf:"bytes,7,opt,name=ids,proto3" json:"ids"`
	// 手机号
	UserMobile string `protobuf:"bytes,8,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	//卡状态
	State int32 `protobuf:"varint,9,opt,name=state,proto3" json:"state"`
	//卡名称
	CardName string `protobuf:"bytes,10,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//来源 0主动购买 1分销购买，2虚拟卡券兑换，3门店开卡
	Source int32 `protobuf:"varint,11,opt,name=source,proto3" json:"source"`
	//购买时间-开始时间
	PayTimeStart string `protobuf:"bytes,12,opt,name=pay_time_start,json=payTimeStart,proto3" json:"pay_time_start"`
	//购买时间-结束时间
	PayTimeEnd string `protobuf:"bytes,13,opt,name=pay_time_end,json=payTimeEnd,proto3" json:"pay_time_end"`
	//排序规则：asc-正序，desc-倒序
	Order string `protobuf:"bytes,14,opt,name=order,proto3" json:"order"`
	//是否过期：0-全部，1-未过期的，2-过期的
	Expiry int32 `protobuf:"varint,15,opt,name=expiry,proto3" json:"expiry"`
	//实体卡订单号
	EntityOrderSn string `protobuf:"bytes,16,opt,name=entity_order_sn,json=entityOrderSn,proto3" json:"entity_order_sn"`
	//卡号
	VirtualCardId        int64    `protobuf:"varint,17,opt,name=virtual_card_id,json=virtualCardId,proto3" json:"virtual_card_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListRequest) Reset()         { *m = GetOrderListRequest{} }
func (m *GetOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderListRequest) ProtoMessage()    {}
func (*GetOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{25}
}

func (m *GetOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListRequest.Unmarshal(m, b)
}
func (m *GetOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListRequest.Marshal(b, m, deterministic)
}
func (m *GetOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListRequest.Merge(m, src)
}
func (m *GetOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderListRequest.Size(m)
}
func (m *GetOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListRequest proto.InternalMessageInfo

func (m *GetOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetOrderListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetOrderListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetOrderListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetOrderListRequest) GetExport() int32 {
	if m != nil {
		return m.Export
	}
	return 0
}

func (m *GetOrderListRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *GetOrderListRequest) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *GetOrderListRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GetOrderListRequest) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *GetOrderListRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GetOrderListRequest) GetPayTimeStart() string {
	if m != nil {
		return m.PayTimeStart
	}
	return ""
}

func (m *GetOrderListRequest) GetPayTimeEnd() string {
	if m != nil {
		return m.PayTimeEnd
	}
	return ""
}

func (m *GetOrderListRequest) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *GetOrderListRequest) GetExpiry() int32 {
	if m != nil {
		return m.Expiry
	}
	return 0
}

func (m *GetOrderListRequest) GetEntityOrderSn() string {
	if m != nil {
		return m.EntityOrderSn
	}
	return ""
}

func (m *GetOrderListRequest) GetVirtualCardId() int64 {
	if m != nil {
		return m.VirtualCardId
	}
	return 0
}

type GetVipCardOrderEquityRequest struct {
	//订单编号
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVipCardOrderEquityRequest) Reset()         { *m = GetVipCardOrderEquityRequest{} }
func (m *GetVipCardOrderEquityRequest) String() string { return proto.CompactTextString(m) }
func (*GetVipCardOrderEquityRequest) ProtoMessage()    {}
func (*GetVipCardOrderEquityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{26}
}

func (m *GetVipCardOrderEquityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipCardOrderEquityRequest.Unmarshal(m, b)
}
func (m *GetVipCardOrderEquityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipCardOrderEquityRequest.Marshal(b, m, deterministic)
}
func (m *GetVipCardOrderEquityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipCardOrderEquityRequest.Merge(m, src)
}
func (m *GetVipCardOrderEquityRequest) XXX_Size() int {
	return xxx_messageInfo_GetVipCardOrderEquityRequest.Size(m)
}
func (m *GetVipCardOrderEquityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipCardOrderEquityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipCardOrderEquityRequest proto.InternalMessageInfo

func (m *GetVipCardOrderEquityRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type GetVipCardOrderEquityResponse struct {
	Code                 int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*EquityData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVipCardOrderEquityResponse) Reset()         { *m = GetVipCardOrderEquityResponse{} }
func (m *GetVipCardOrderEquityResponse) String() string { return proto.CompactTextString(m) }
func (*GetVipCardOrderEquityResponse) ProtoMessage()    {}
func (*GetVipCardOrderEquityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{27}
}

func (m *GetVipCardOrderEquityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipCardOrderEquityResponse.Unmarshal(m, b)
}
func (m *GetVipCardOrderEquityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipCardOrderEquityResponse.Marshal(b, m, deterministic)
}
func (m *GetVipCardOrderEquityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipCardOrderEquityResponse.Merge(m, src)
}
func (m *GetVipCardOrderEquityResponse) XXX_Size() int {
	return xxx_messageInfo_GetVipCardOrderEquityResponse.Size(m)
}
func (m *GetVipCardOrderEquityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipCardOrderEquityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipCardOrderEquityResponse proto.InternalMessageInfo

func (m *GetVipCardOrderEquityResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetVipCardOrderEquityResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetVipCardOrderEquityResponse) GetData() []*EquityData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetVrRefundListRequest struct {
	// 订单号ID
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// erp订单号
	ErpOrderSn string `protobuf:"bytes,2,opt,name=erp_order_sn,json=erpOrderSn,proto3" json:"erp_order_sn"`
	// 用户手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	// 申请时间-开始时间
	StartTime string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 申请时间-结束时间
	EndTime string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 状态：0-全部（默认）；1-待审核；2-审批拒绝；3-退款成功；4-退款失败；5-注销成功
	AdminState int32 `protobuf:"varint,6,opt,name=admin_state,json=adminState,proto3" json:"admin_state"`
	// 是否导出：0-列表查询（默认）；1-导出列表
	Export int32 `protobuf:"varint,7,opt,name=export,proto3" json:"export"`
	//当前多少页 从1开始 必传且必须大于0
	PageIndex int32 `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页多少条数据 必传且必须大于0
	PageSize int32 `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//卡名称
	CardName             string   `protobuf:"bytes,10,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVrRefundListRequest) Reset()         { *m = GetVrRefundListRequest{} }
func (m *GetVrRefundListRequest) String() string { return proto.CompactTextString(m) }
func (*GetVrRefundListRequest) ProtoMessage()    {}
func (*GetVrRefundListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{28}
}

func (m *GetVrRefundListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVrRefundListRequest.Unmarshal(m, b)
}
func (m *GetVrRefundListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVrRefundListRequest.Marshal(b, m, deterministic)
}
func (m *GetVrRefundListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVrRefundListRequest.Merge(m, src)
}
func (m *GetVrRefundListRequest) XXX_Size() int {
	return xxx_messageInfo_GetVrRefundListRequest.Size(m)
}
func (m *GetVrRefundListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVrRefundListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVrRefundListRequest proto.InternalMessageInfo

func (m *GetVrRefundListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetVrRefundListRequest) GetErpOrderSn() string {
	if m != nil {
		return m.ErpOrderSn
	}
	return ""
}

func (m *GetVrRefundListRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GetVrRefundListRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetVrRefundListRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetVrRefundListRequest) GetAdminState() int32 {
	if m != nil {
		return m.AdminState
	}
	return 0
}

func (m *GetVrRefundListRequest) GetExport() int32 {
	if m != nil {
		return m.Export
	}
	return 0
}

func (m *GetVrRefundListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetVrRefundListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetVrRefundListRequest) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

type GetVrRefundListResponse struct {
	Code                 int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*GetVrRefundListData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetVrRefundListResponse) Reset()         { *m = GetVrRefundListResponse{} }
func (m *GetVrRefundListResponse) String() string { return proto.CompactTextString(m) }
func (*GetVrRefundListResponse) ProtoMessage()    {}
func (*GetVrRefundListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{29}
}

func (m *GetVrRefundListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVrRefundListResponse.Unmarshal(m, b)
}
func (m *GetVrRefundListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVrRefundListResponse.Marshal(b, m, deterministic)
}
func (m *GetVrRefundListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVrRefundListResponse.Merge(m, src)
}
func (m *GetVrRefundListResponse) XXX_Size() int {
	return xxx_messageInfo_GetVrRefundListResponse.Size(m)
}
func (m *GetVrRefundListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVrRefundListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVrRefundListResponse proto.InternalMessageInfo

func (m *GetVrRefundListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetVrRefundListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetVrRefundListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetVrRefundListResponse) GetData() []*GetVrRefundListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetVrRefundListData struct {
	// 申请批次
	RefundId int32 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	// 用户id
	MemberId string `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 用户手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	// 订单号
	OrderSn string `protobuf:"bytes,4,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 申请类型：1-仅注销身份和权益；2-退款+注销身份和权益
	ApplyType int32 `protobuf:"varint,5,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	// 申请人
	ApplyUser string `protobuf:"bytes,6,opt,name=apply_user,json=applyUser,proto3" json:"apply_user"`
	// 申请原因
	BuyerMessage string `protobuf:"bytes,7,opt,name=buyer_message,json=buyerMessage,proto3" json:"buyer_message"`
	// 申请时间
	AddTime string `protobuf:"bytes,8,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	// 状态
	AdminState int32 `protobuf:"varint,9,opt,name=admin_state,json=adminState,proto3" json:"admin_state"`
	// 审核人
	AdminUser string `protobuf:"bytes,10,opt,name=admin_user,json=adminUser,proto3" json:"admin_user"`
	// 审核时间
	AdminTime string `protobuf:"bytes,11,opt,name=admin_time,json=adminTime,proto3" json:"admin_time"`
	// 审核原因
	AdminMessage string `protobuf:"bytes,12,opt,name=admin_message,json=adminMessage,proto3" json:"admin_message"`
	// 电银退款状态：0-默认；1-退款中；2-成功；3-失败；4-接口异常
	DyState int32 `protobuf:"varint,13,opt,name=dy_state,json=dyState,proto3" json:"dy_state"`
	// 加密手机号
	EncryptMobile string `protobuf:"bytes,14,opt,name=encrypt_mobile,json=encryptMobile,proto3" json:"encrypt_mobile"`
	// erp订单号
	ErpOrderSn string `protobuf:"bytes,15,opt,name=erp_order_sn,json=erpOrderSn,proto3" json:"erp_order_sn"`
	// 卡名称
	CardName             string   `protobuf:"bytes,16,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVrRefundListData) Reset()         { *m = GetVrRefundListData{} }
func (m *GetVrRefundListData) String() string { return proto.CompactTextString(m) }
func (*GetVrRefundListData) ProtoMessage()    {}
func (*GetVrRefundListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{30}
}

func (m *GetVrRefundListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVrRefundListData.Unmarshal(m, b)
}
func (m *GetVrRefundListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVrRefundListData.Marshal(b, m, deterministic)
}
func (m *GetVrRefundListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVrRefundListData.Merge(m, src)
}
func (m *GetVrRefundListData) XXX_Size() int {
	return xxx_messageInfo_GetVrRefundListData.Size(m)
}
func (m *GetVrRefundListData) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVrRefundListData.DiscardUnknown(m)
}

var xxx_messageInfo_GetVrRefundListData proto.InternalMessageInfo

func (m *GetVrRefundListData) GetRefundId() int32 {
	if m != nil {
		return m.RefundId
	}
	return 0
}

func (m *GetVrRefundListData) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *GetVrRefundListData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GetVrRefundListData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetVrRefundListData) GetApplyType() int32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *GetVrRefundListData) GetApplyUser() string {
	if m != nil {
		return m.ApplyUser
	}
	return ""
}

func (m *GetVrRefundListData) GetBuyerMessage() string {
	if m != nil {
		return m.BuyerMessage
	}
	return ""
}

func (m *GetVrRefundListData) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

func (m *GetVrRefundListData) GetAdminState() int32 {
	if m != nil {
		return m.AdminState
	}
	return 0
}

func (m *GetVrRefundListData) GetAdminUser() string {
	if m != nil {
		return m.AdminUser
	}
	return ""
}

func (m *GetVrRefundListData) GetAdminTime() string {
	if m != nil {
		return m.AdminTime
	}
	return ""
}

func (m *GetVrRefundListData) GetAdminMessage() string {
	if m != nil {
		return m.AdminMessage
	}
	return ""
}

func (m *GetVrRefundListData) GetDyState() int32 {
	if m != nil {
		return m.DyState
	}
	return 0
}

func (m *GetVrRefundListData) GetEncryptMobile() string {
	if m != nil {
		return m.EncryptMobile
	}
	return ""
}

func (m *GetVrRefundListData) GetErpOrderSn() string {
	if m != nil {
		return m.ErpOrderSn
	}
	return ""
}

func (m *GetVrRefundListData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

type CreateVrRefundRequest struct {
	// 用户手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 购买会员的订单号
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 申请原因
	BuyerMessage string `protobuf:"bytes,3,opt,name=buyer_message,json=buyerMessage,proto3" json:"buyer_message"`
	// 申请类型：1-仅注销身份和权益；2-退款+注销身份和权益
	ApplyType int32 `protobuf:"varint,4,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	// 申请人
	ApplyUser            string   `protobuf:"bytes,5,opt,name=apply_user,json=applyUser,proto3" json:"apply_user"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVrRefundRequest) Reset()         { *m = CreateVrRefundRequest{} }
func (m *CreateVrRefundRequest) String() string { return proto.CompactTextString(m) }
func (*CreateVrRefundRequest) ProtoMessage()    {}
func (*CreateVrRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{31}
}

func (m *CreateVrRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVrRefundRequest.Unmarshal(m, b)
}
func (m *CreateVrRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVrRefundRequest.Marshal(b, m, deterministic)
}
func (m *CreateVrRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVrRefundRequest.Merge(m, src)
}
func (m *CreateVrRefundRequest) XXX_Size() int {
	return xxx_messageInfo_CreateVrRefundRequest.Size(m)
}
func (m *CreateVrRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVrRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVrRefundRequest proto.InternalMessageInfo

func (m *CreateVrRefundRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CreateVrRefundRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CreateVrRefundRequest) GetBuyerMessage() string {
	if m != nil {
		return m.BuyerMessage
	}
	return ""
}

func (m *CreateVrRefundRequest) GetApplyType() int32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *CreateVrRefundRequest) GetApplyUser() string {
	if m != nil {
		return m.ApplyUser
	}
	return ""
}

type CreateVrRefundResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVrRefundResponse) Reset()         { *m = CreateVrRefundResponse{} }
func (m *CreateVrRefundResponse) String() string { return proto.CompactTextString(m) }
func (*CreateVrRefundResponse) ProtoMessage()    {}
func (*CreateVrRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{32}
}

func (m *CreateVrRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVrRefundResponse.Unmarshal(m, b)
}
func (m *CreateVrRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVrRefundResponse.Marshal(b, m, deterministic)
}
func (m *CreateVrRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVrRefundResponse.Merge(m, src)
}
func (m *CreateVrRefundResponse) XXX_Size() int {
	return xxx_messageInfo_CreateVrRefundResponse.Size(m)
}
func (m *CreateVrRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVrRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVrRefundResponse proto.InternalMessageInfo

func (m *CreateVrRefundResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreateVrRefundResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreateVrRefundResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type DelVipOrderCardRequest struct {
	// 会员卡订单id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 用户编号，前端不用传
	UserNo string `protobuf:"bytes,2,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 用户昵称，前端不用传
	UserName             string   `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelVipOrderCardRequest) Reset()         { *m = DelVipOrderCardRequest{} }
func (m *DelVipOrderCardRequest) String() string { return proto.CompactTextString(m) }
func (*DelVipOrderCardRequest) ProtoMessage()    {}
func (*DelVipOrderCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{33}
}

func (m *DelVipOrderCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVipOrderCardRequest.Unmarshal(m, b)
}
func (m *DelVipOrderCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVipOrderCardRequest.Marshal(b, m, deterministic)
}
func (m *DelVipOrderCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVipOrderCardRequest.Merge(m, src)
}
func (m *DelVipOrderCardRequest) XXX_Size() int {
	return xxx_messageInfo_DelVipOrderCardRequest.Size(m)
}
func (m *DelVipOrderCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVipOrderCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelVipOrderCardRequest proto.InternalMessageInfo

func (m *DelVipOrderCardRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DelVipOrderCardRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *DelVipOrderCardRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type VcBaseResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VcBaseResponse) Reset()         { *m = VcBaseResponse{} }
func (m *VcBaseResponse) String() string { return proto.CompactTextString(m) }
func (*VcBaseResponse) ProtoMessage()    {}
func (*VcBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{34}
}

func (m *VcBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VcBaseResponse.Unmarshal(m, b)
}
func (m *VcBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VcBaseResponse.Marshal(b, m, deterministic)
}
func (m *VcBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VcBaseResponse.Merge(m, src)
}
func (m *VcBaseResponse) XXX_Size() int {
	return xxx_messageInfo_VcBaseResponse.Size(m)
}
func (m *VcBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VcBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VcBaseResponse proto.InternalMessageInfo

func (m *VcBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VcBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetOrderOperateLogListRequest struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 开始时间
	StartTime string `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间
	EndTime              string   `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderOperateLogListRequest) Reset()         { *m = GetOrderOperateLogListRequest{} }
func (m *GetOrderOperateLogListRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderOperateLogListRequest) ProtoMessage()    {}
func (*GetOrderOperateLogListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{35}
}

func (m *GetOrderOperateLogListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderOperateLogListRequest.Unmarshal(m, b)
}
func (m *GetOrderOperateLogListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderOperateLogListRequest.Marshal(b, m, deterministic)
}
func (m *GetOrderOperateLogListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderOperateLogListRequest.Merge(m, src)
}
func (m *GetOrderOperateLogListRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderOperateLogListRequest.Size(m)
}
func (m *GetOrderOperateLogListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderOperateLogListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderOperateLogListRequest proto.InternalMessageInfo

func (m *GetOrderOperateLogListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetOrderOperateLogListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetOrderOperateLogListRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetOrderOperateLogListRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type GetOrderOperateLogListResponse struct {
	Code                 int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*OrderOperateLogData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetOrderOperateLogListResponse) Reset()         { *m = GetOrderOperateLogListResponse{} }
func (m *GetOrderOperateLogListResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderOperateLogListResponse) ProtoMessage()    {}
func (*GetOrderOperateLogListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{36}
}

func (m *GetOrderOperateLogListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderOperateLogListResponse.Unmarshal(m, b)
}
func (m *GetOrderOperateLogListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderOperateLogListResponse.Marshal(b, m, deterministic)
}
func (m *GetOrderOperateLogListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderOperateLogListResponse.Merge(m, src)
}
func (m *GetOrderOperateLogListResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderOperateLogListResponse.Size(m)
}
func (m *GetOrderOperateLogListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderOperateLogListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderOperateLogListResponse proto.InternalMessageInfo

func (m *GetOrderOperateLogListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetOrderOperateLogListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetOrderOperateLogListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetOrderOperateLogListResponse) GetData() []*OrderOperateLogData {
	if m != nil {
		return m.Data
	}
	return nil
}

type OrderOperateLogData struct {
	// 操作日志id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 操作人
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 操作时间
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 订单号
	OrderSn string `protobuf:"bytes,4,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 卡名称
	CardName string `protobuf:"bytes,5,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	// 购买时间
	PayTime              string   `protobuf:"bytes,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderOperateLogData) Reset()         { *m = OrderOperateLogData{} }
func (m *OrderOperateLogData) String() string { return proto.CompactTextString(m) }
func (*OrderOperateLogData) ProtoMessage()    {}
func (*OrderOperateLogData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{37}
}

func (m *OrderOperateLogData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderOperateLogData.Unmarshal(m, b)
}
func (m *OrderOperateLogData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderOperateLogData.Marshal(b, m, deterministic)
}
func (m *OrderOperateLogData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderOperateLogData.Merge(m, src)
}
func (m *OrderOperateLogData) XXX_Size() int {
	return xxx_messageInfo_OrderOperateLogData.Size(m)
}
func (m *OrderOperateLogData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderOperateLogData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderOperateLogData proto.InternalMessageInfo

func (m *OrderOperateLogData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OrderOperateLogData) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *OrderOperateLogData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *OrderOperateLogData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderOperateLogData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *OrderOperateLogData) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

type VipVrRefundDetailReq struct {
	//会员卡订单号
	RefundId int32 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	//用户id(前端不用传)
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipVrRefundDetailReq) Reset()         { *m = VipVrRefundDetailReq{} }
func (m *VipVrRefundDetailReq) String() string { return proto.CompactTextString(m) }
func (*VipVrRefundDetailReq) ProtoMessage()    {}
func (*VipVrRefundDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{38}
}

func (m *VipVrRefundDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipVrRefundDetailReq.Unmarshal(m, b)
}
func (m *VipVrRefundDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipVrRefundDetailReq.Marshal(b, m, deterministic)
}
func (m *VipVrRefundDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipVrRefundDetailReq.Merge(m, src)
}
func (m *VipVrRefundDetailReq) XXX_Size() int {
	return xxx_messageInfo_VipVrRefundDetailReq.Size(m)
}
func (m *VipVrRefundDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VipVrRefundDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_VipVrRefundDetailReq proto.InternalMessageInfo

func (m *VipVrRefundDetailReq) GetRefundId() int32 {
	if m != nil {
		return m.RefundId
	}
	return 0
}

func (m *VipVrRefundDetailReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type VipVrRefundDetailResp struct {
	Code                 int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *VipVrRefundDetailData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *VipVrRefundDetailResp) Reset()         { *m = VipVrRefundDetailResp{} }
func (m *VipVrRefundDetailResp) String() string { return proto.CompactTextString(m) }
func (*VipVrRefundDetailResp) ProtoMessage()    {}
func (*VipVrRefundDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{39}
}

func (m *VipVrRefundDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipVrRefundDetailResp.Unmarshal(m, b)
}
func (m *VipVrRefundDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipVrRefundDetailResp.Marshal(b, m, deterministic)
}
func (m *VipVrRefundDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipVrRefundDetailResp.Merge(m, src)
}
func (m *VipVrRefundDetailResp) XXX_Size() int {
	return xxx_messageInfo_VipVrRefundDetailResp.Size(m)
}
func (m *VipVrRefundDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_VipVrRefundDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_VipVrRefundDetailResp proto.InternalMessageInfo

func (m *VipVrRefundDetailResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VipVrRefundDetailResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VipVrRefundDetailResp) GetData() *VipVrRefundDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

type VipVrRefundDetailData struct {
	// 申请批次
	RefundId int32 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	// 用户手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	// 订单号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// erp订单号
	ErpOrderSn string `protobuf:"bytes,4,opt,name=erp_order_sn,json=erpOrderSn,proto3" json:"erp_order_sn"`
	// 申请类型：0-未知，1-仅注销身份和权益；2-退款+注销身份和权益
	ApplyType int32 `protobuf:"varint,5,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	// 会员购买记录
	Order []*VipCardOrderData `protobuf:"bytes,6,rep,name=order,proto3" json:"order"`
	// 会员优惠
	Equity []*EquityData `protobuf:"bytes,7,rep,name=equity,proto3" json:"equity"`
	// 系统推荐：0-可退款；1-拒绝
	Advise int32 `protobuf:"varint,8,opt,name=advise,proto3" json:"advise"`
	// 用户id
	ScrmUserId           string   `protobuf:"bytes,9,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipVrRefundDetailData) Reset()         { *m = VipVrRefundDetailData{} }
func (m *VipVrRefundDetailData) String() string { return proto.CompactTextString(m) }
func (*VipVrRefundDetailData) ProtoMessage()    {}
func (*VipVrRefundDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{40}
}

func (m *VipVrRefundDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipVrRefundDetailData.Unmarshal(m, b)
}
func (m *VipVrRefundDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipVrRefundDetailData.Marshal(b, m, deterministic)
}
func (m *VipVrRefundDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipVrRefundDetailData.Merge(m, src)
}
func (m *VipVrRefundDetailData) XXX_Size() int {
	return xxx_messageInfo_VipVrRefundDetailData.Size(m)
}
func (m *VipVrRefundDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_VipVrRefundDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_VipVrRefundDetailData proto.InternalMessageInfo

func (m *VipVrRefundDetailData) GetRefundId() int32 {
	if m != nil {
		return m.RefundId
	}
	return 0
}

func (m *VipVrRefundDetailData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *VipVrRefundDetailData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *VipVrRefundDetailData) GetErpOrderSn() string {
	if m != nil {
		return m.ErpOrderSn
	}
	return ""
}

func (m *VipVrRefundDetailData) GetApplyType() int32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *VipVrRefundDetailData) GetOrder() []*VipCardOrderData {
	if m != nil {
		return m.Order
	}
	return nil
}

func (m *VipVrRefundDetailData) GetEquity() []*EquityData {
	if m != nil {
		return m.Equity
	}
	return nil
}

func (m *VipVrRefundDetailData) GetAdvise() int32 {
	if m != nil {
		return m.Advise
	}
	return 0
}

func (m *VipVrRefundDetailData) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type VipCardOrderData struct {
	// 大区id:-1-全国、13-浙闵二区
	OrId int32 `protobuf:"varint,1,opt,name=or_id,json=orId,proto3" json:"or_id"`
	// 卡模板id
	CardId int32 `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	// 卡名称
	CardName string `protobuf:"bytes,3,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	// 购买金额
	OrderAmount int32 `protobuf:"varint,4,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount"`
	// 开卡时间
	PayTime string `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	// 过期时间
	ExpiryDate string `protobuf:"bytes,6,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date"`
	// 订单状态：0-已取消，10(默认)-未付款，20-已付款，40-已完成
	OrderState int32 `protobuf:"varint,7,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	// 用户id
	UserId string `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 会员id
	MemberId             int32    `protobuf:"varint,9,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipCardOrderData) Reset()         { *m = VipCardOrderData{} }
func (m *VipCardOrderData) String() string { return proto.CompactTextString(m) }
func (*VipCardOrderData) ProtoMessage()    {}
func (*VipCardOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{41}
}

func (m *VipCardOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardOrderData.Unmarshal(m, b)
}
func (m *VipCardOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardOrderData.Marshal(b, m, deterministic)
}
func (m *VipCardOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardOrderData.Merge(m, src)
}
func (m *VipCardOrderData) XXX_Size() int {
	return xxx_messageInfo_VipCardOrderData.Size(m)
}
func (m *VipCardOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardOrderData proto.InternalMessageInfo

func (m *VipCardOrderData) GetOrId() int32 {
	if m != nil {
		return m.OrId
	}
	return 0
}

func (m *VipCardOrderData) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *VipCardOrderData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *VipCardOrderData) GetOrderAmount() int32 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *VipCardOrderData) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *VipCardOrderData) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *VipCardOrderData) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

func (m *VipCardOrderData) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *VipCardOrderData) GetMemberId() int32 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

type EquityData struct {
	// 权益名称
	EquityName string `protobuf:"bytes,1,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	// 权益类型
	EquityType int32 `protobuf:"varint,2,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	// 是否已使用：0-否，1-是
	UsedState int32 `protobuf:"varint,3,opt,name=used_state,json=usedState,proto3" json:"used_state"`
	// 权益相关详细数据，json字符串
	EquityContent string `protobuf:"bytes,4,opt,name=equity_content,json=equityContent,proto3" json:"equity_content"`
	// 礼包电商父订单号
	GiftOrderSn          string   `protobuf:"bytes,5,opt,name=gift_order_sn,json=giftOrderSn,proto3" json:"gift_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityData) Reset()         { *m = EquityData{} }
func (m *EquityData) String() string { return proto.CompactTextString(m) }
func (*EquityData) ProtoMessage()    {}
func (*EquityData) Descriptor() ([]byte, []int) {
	return fileDescriptor_91b4d7d76fd779cc, []int{42}
}

func (m *EquityData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityData.Unmarshal(m, b)
}
func (m *EquityData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityData.Marshal(b, m, deterministic)
}
func (m *EquityData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityData.Merge(m, src)
}
func (m *EquityData) XXX_Size() int {
	return xxx_messageInfo_EquityData.Size(m)
}
func (m *EquityData) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityData.DiscardUnknown(m)
}

var xxx_messageInfo_EquityData proto.InternalMessageInfo

func (m *EquityData) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *EquityData) GetEquityType() int32 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *EquityData) GetUsedState() int32 {
	if m != nil {
		return m.UsedState
	}
	return 0
}

func (m *EquityData) GetEquityContent() string {
	if m != nil {
		return m.EquityContent
	}
	return ""
}

func (m *EquityData) GetGiftOrderSn() string {
	if m != nil {
		return m.GiftOrderSn
	}
	return ""
}

func init() {
	proto.RegisterType((*PVCEditRequest)(nil), "oc.PVCEditRequest")
	proto.RegisterType((*PVCEditResponse)(nil), "oc.PVCEditResponse")
	proto.RegisterType((*PVCExpressEditRequest)(nil), "oc.PVCExpressEditRequest")
	proto.RegisterType((*PVCExpressEditResponse)(nil), "oc.PVCExpressEditResponse")
	proto.RegisterType((*GetPhysicalVipCardOrderDetailRequest)(nil), "oc.GetPhysicalVipCardOrderDetailRequest")
	proto.RegisterType((*GetPhysicalVipCardOrderDetailResponse)(nil), "oc.GetPhysicalVipCardOrderDetailResponse")
	proto.RegisterType((*PhysicalVipCardOrderDetail)(nil), "oc.PhysicalVipCardOrderDetail")
	proto.RegisterType((*DeliveryInfo)(nil), "oc.DeliveryInfo")
	proto.RegisterType((*UpetOrderLog)(nil), "oc.UpetOrderLog")
	proto.RegisterType((*PVCExpressImportListRequest)(nil), "oc.PVCExpressImportListRequest")
	proto.RegisterType((*PVCExpressImportListResponse)(nil), "oc.PVCExpressImportListResponse")
	proto.RegisterType((*PVCExpressImportListResponse_List)(nil), "oc.PVCExpressImportListResponse.List")
	proto.RegisterType((*PhysicalVipCardOrderExportListRequest)(nil), "oc.PhysicalVipCardOrderExportListRequest")
	proto.RegisterType((*PhysicalVipCardOrderExportListResponse)(nil), "oc.PhysicalVipCardOrderExportListResponse")
	proto.RegisterType((*PhysicalVipCardOrderExportListResponse_List)(nil), "oc.PhysicalVipCardOrderExportListResponse.List")
	proto.RegisterType((*PVCExpressImportRequest)(nil), "oc.PVCExpressImportRequest")
	proto.RegisterType((*PVCExpressImportResponse)(nil), "oc.PVCExpressImportResponse")
	proto.RegisterType((*PVCExpressImportTemplateRequest)(nil), "oc.PVCExpressImportTemplateRequest")
	proto.RegisterType((*PVCExpressImportTemplateResponse)(nil), "oc.PVCExpressImportTemplateResponse")
	proto.RegisterType((*GetPhysicalVipCardOrderExportResponse)(nil), "oc.GetPhysicalVipCardOrderExportResponse")
	proto.RegisterType((*GetPhysicalVipCardOrderListRequest)(nil), "oc.GetPhysicalVipCardOrderListRequest")
	proto.RegisterType((*GetPhysicalVipCardOrderListResponse)(nil), "oc.GetPhysicalVipCardOrderListResponse")
	proto.RegisterType((*PhysicalOrderData)(nil), "oc.PhysicalOrderData")
	proto.RegisterType((*Address1)(nil), "oc.Address1")
	proto.RegisterType((*Goods)(nil), "oc.Goods")
	proto.RegisterType((*OrderData)(nil), "oc.OrderData")
	proto.RegisterType((*GetOrderListResponse)(nil), "oc.GetOrderListResponse")
	proto.RegisterType((*GetOrderListRequest)(nil), "oc.GetOrderListRequest")
	proto.RegisterType((*GetVipCardOrderEquityRequest)(nil), "oc.GetVipCardOrderEquityRequest")
	proto.RegisterType((*GetVipCardOrderEquityResponse)(nil), "oc.GetVipCardOrderEquityResponse")
	proto.RegisterType((*GetVrRefundListRequest)(nil), "oc.GetVrRefundListRequest")
	proto.RegisterType((*GetVrRefundListResponse)(nil), "oc.GetVrRefundListResponse")
	proto.RegisterType((*GetVrRefundListData)(nil), "oc.GetVrRefundListData")
	proto.RegisterType((*CreateVrRefundRequest)(nil), "oc.CreateVrRefundRequest")
	proto.RegisterType((*CreateVrRefundResponse)(nil), "oc.CreateVrRefundResponse")
	proto.RegisterType((*DelVipOrderCardRequest)(nil), "oc.DelVipOrderCardRequest")
	proto.RegisterType((*VcBaseResponse)(nil), "oc.VcBaseResponse")
	proto.RegisterType((*GetOrderOperateLogListRequest)(nil), "oc.GetOrderOperateLogListRequest")
	proto.RegisterType((*GetOrderOperateLogListResponse)(nil), "oc.GetOrderOperateLogListResponse")
	proto.RegisterType((*OrderOperateLogData)(nil), "oc.OrderOperateLogData")
	proto.RegisterType((*VipVrRefundDetailReq)(nil), "oc.VipVrRefundDetailReq")
	proto.RegisterType((*VipVrRefundDetailResp)(nil), "oc.VipVrRefundDetailResp")
	proto.RegisterType((*VipVrRefundDetailData)(nil), "oc.VipVrRefundDetailData")
	proto.RegisterType((*VipCardOrderData)(nil), "oc.VipCardOrderData")
	proto.RegisterType((*EquityData)(nil), "oc.EquityData")
}

func init() { proto.RegisterFile("oc/vip_card_order.proto", fileDescriptor_91b4d7d76fd779cc) }

var fileDescriptor_91b4d7d76fd779cc = []byte{
	// 2885 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x4b, 0x6f, 0x1b, 0xc9,
	0xf1, 0x07, 0x9f, 0x22, 0x8b, 0xd4, 0x6b, 0xf4, 0xf0, 0x88, 0xb2, 0x2d, 0x79, 0x64, 0x7b, 0xbd,
	0xfb, 0xdf, 0x95, 0xf1, 0x77, 0x4e, 0x7b, 0x49, 0xa0, 0xc8, 0x5e, 0xaf, 0x12, 0xdb, 0x32, 0xe8,
	0x07, 0xb0, 0xc0, 0x22, 0x83, 0x11, 0xa7, 0x45, 0x0f, 0x42, 0xce, 0x8c, 0x67, 0x86, 0x8a, 0xb8,
	0x40, 0x72, 0x4a, 0x0e, 0xc9, 0x2d, 0xd8, 0x5c, 0xb2, 0xa7, 0xfd, 0x02, 0xb9, 0x06, 0x9b, 0x0f,
	0x93, 0xaf, 0x90, 0xcb, 0x7e, 0x81, 0xa0, 0xaa, 0xba, 0xe7, 0x4d, 0x52, 0x11, 0x9c, 0x5b, 0x6e,
	0xec, 0xaa, 0xea, 0xea, 0xae, 0x67, 0xff, 0xba, 0x87, 0x70, 0xc3, 0x1b, 0x3c, 0xbc, 0x70, 0x7c,
	0x73, 0x60, 0x05, 0xb6, 0xe9, 0x05, 0xb6, 0x08, 0x0e, 0xfd, 0xc0, 0x8b, 0x3c, 0xad, 0xea, 0x0d,
	0x7a, 0x3b, 0x43, 0xcf, 0x1b, 0x8e, 0xc4, 0x43, 0xa2, 0x9c, 0x4d, 0xce, 0x1f, 0x5a, 0xee, 0x94,
	0xd9, 0xc6, 0x0b, 0x58, 0x79, 0xf9, 0xf6, 0xf8, 0x89, 0xed, 0x44, 0x7d, 0xf1, 0x7e, 0x22, 0xc2,
	0x48, 0xdb, 0x81, 0x16, 0xcd, 0x37, 0x43, 0x57, 0xaf, 0xec, 0x57, 0x1e, 0xb4, 0xfb, 0x4b, 0x34,
	0x7e, 0xe5, 0x6a, 0x77, 0xa0, 0x7b, 0xe1, 0x04, 0xd1, 0xc4, 0x1a, 0xd1, 0x3a, 0x7a, 0x95, 0xd8,
	0x1d, 0x49, 0x3b, 0xb6, 0x02, 0xdb, 0xf8, 0x19, 0xac, 0xc6, 0xfa, 0x42, 0xdf, 0x73, 0x43, 0xa1,
	0x69, 0x50, 0x1f, 0x78, 0xb6, 0x20, 0x65, 0x8d, 0x3e, 0xfd, 0xd6, 0x74, 0x58, 0x1a, 0x8b, 0x30,
	0xb4, 0x86, 0x42, 0x2a, 0x51, 0x43, 0xe3, 0x77, 0xb0, 0x85, 0x0a, 0x2e, 0xfd, 0x40, 0x84, 0xe1,
	0x15, 0xf7, 0x75, 0x00, 0xcb, 0xe1, 0x3b, 0xc7, 0xf7, 0x1d, 0x77, 0x68, 0xd2, 0x52, 0xac, 0xb3,
	0xab, 0x88, 0xc7, 0xb8, 0xe4, 0x3d, 0x58, 0x89, 0x85, 0x04, 0x49, 0xd5, 0x48, 0x2a, 0x9e, 0xfa,
	0x04, 0x89, 0xc6, 0x17, 0xb0, 0x9d, 0x5f, 0xff, 0x5a, 0x76, 0x1c, 0xc1, 0xdd, 0xa7, 0x22, 0x7a,
	0xf9, 0x6e, 0x1a, 0x3a, 0x03, 0x6b, 0xf4, 0xd6, 0xf1, 0xd1, 0x3d, 0xa7, 0xb8, 0xe1, 0xc7, 0x22,
	0xb2, 0x9c, 0xd1, 0x62, 0xb3, 0x8c, 0x3f, 0x56, 0xe0, 0xde, 0x02, 0x1d, 0xd7, 0xd9, 0x9a, 0xf6,
	0x08, 0xea, 0xb6, 0x15, 0x59, 0x64, 0x7f, 0xe7, 0xd1, 0xed, 0x43, 0x6f, 0x70, 0x38, 0x67, 0x0d,
	0x92, 0x35, 0xfe, 0xd9, 0x80, 0xde, 0x6c, 0xa1, 0x05, 0x49, 0xe3, 0x5b, 0xd3, 0xb1, 0x70, 0x23,
	0x33, 0x72, 0xc6, 0x6a, 0x33, 0x1d, 0x49, 0x7b, 0xed, 0x8c, 0x85, 0x76, 0x0b, 0x80, 0x67, 0x9f,
	0x07, 0xde, 0x98, 0xb6, 0xd5, 0xe8, 0xb7, 0x89, 0xf2, 0x45, 0xe0, 0x8d, 0xb5, 0xfb, 0xb0, 0x9a,
	0xb0, 0xcd, 0x48, 0x5c, 0x46, 0x7a, 0x9d, 0x43, 0x17, 0xcb, 0xbc, 0x16, 0x97, 0x11, 0xca, 0xa5,
	0xd3, 0xd3, 0x74, 0x6c, 0xbd, 0xb1, 0x5f, 0x79, 0x50, 0xeb, 0x2f, 0xa7, 0x32, 0xf4, 0xc4, 0xd6,
	0x1e, 0xc2, 0x66, 0x4e, 0x8e, 0x95, 0x36, 0x49, 0xe9, 0x7a, 0x46, 0x98, 0x14, 0xef, 0x41, 0x47,
	0x5a, 0x17, 0x59, 0x91, 0xd0, 0x97, 0x68, 0x83, 0xbc, 0xe5, 0x57, 0x48, 0xd1, 0x1e, 0xc0, 0x5a,
	0x4a, 0x80, 0xb5, 0xb5, 0x48, 0xdb, 0x4a, 0x22, 0x45, 0xaa, 0x52, 0xde, 0xa0, 0x88, 0xb5, 0x33,
	0xde, 0xa0, 0x44, 0xfd, 0x04, 0xd6, 0xd3, 0x22, 0xac, 0x0d, 0x48, 0x6e, 0x35, 0x25, 0x47, 0xea,
	0x0e, 0x80, 0x7d, 0x60, 0xaa, 0x50, 0x77, 0x38, 0xf3, 0x89, 0xf8, 0x5c, 0xc6, 0x7b, 0x1f, 0xba,
	0xe1, 0x20, 0x18, 0x9b, 0x93, 0x50, 0x04, 0xe8, 0x94, 0x2e, 0xc9, 0x00, 0xd2, 0xde, 0x84, 0x22,
	0x38, 0xb1, 0xd1, 0xc0, 0xb3, 0xc9, 0x54, 0x04, 0xa6, 0xff, 0xce, 0x73, 0x85, 0xbe, 0xcc, 0x02,
	0x44, 0x7a, 0x89, 0x14, 0x2c, 0x1e, 0xe1, 0x0e, 0x82, 0xa9, 0x1f, 0x99, 0x63, 0xef, 0xcc, 0x19,
	0x09, 0x7d, 0x85, 0x23, 0x20, 0xa9, 0xcf, 0x89, 0x88, 0x69, 0x70, 0x36, 0x99, 0x9a, 0xae, 0x35,
	0x16, 0xfa, 0x2a, 0xa7, 0xc1, 0xd9, 0x64, 0xfa, 0xc2, 0x1a, 0x0b, 0xed, 0x3e, 0x2c, 0x59, 0xb6,
	0x8d, 0x45, 0xa5, 0xaf, 0x51, 0xde, 0x75, 0x31, 0xef, 0x8e, 0x98, 0xf4, 0xff, 0x7d, 0xc5, 0xd4,
	0xf6, 0xa0, 0x31, 0xf4, 0x3c, 0x3b, 0xd4, 0xd7, 0xf7, 0x6b, 0x0f, 0x3a, 0x8f, 0xda, 0x28, 0xf5,
	0x14, 0x09, 0x7d, 0xa6, 0x6b, 0x9f, 0x42, 0xcb, 0x16, 0x23, 0xe7, 0x42, 0x04, 0x53, 0x5d, 0x23,
	0x4d, 0x6b, 0x28, 0xf3, 0x58, 0xd2, 0x4e, 0xdc, 0x73, 0xaf, 0x1f, 0x4b, 0x68, 0xf7, 0xa1, 0x11,
	0x46, 0xc2, 0x0f, 0xf5, 0x0d, 0x52, 0x47, 0xa2, 0x6f, 0x7c, 0x11, 0x51, 0xf2, 0x3e, 0xf3, 0x86,
	0x7d, 0x66, 0x1b, 0x1e, 0x74, 0xd3, 0x1a, 0x8a, 0x2d, 0xa5, 0x52, 0xd2, 0x52, 0xb6, 0xa0, 0x29,
	0xd8, 0x58, 0x4e, 0xea, 0x86, 0x20, 0x53, 0xd3, 0x73, 0x29, 0xe5, 0x6b, 0xd9, 0xb9, 0x98, 0xf3,
	0x46, 0x04, 0xdd, 0xf4, 0x3e, 0x92, 0x0a, 0x72, 0x6c, 0x5a, 0xab, 0x26, 0x2b, 0xe8, 0xc4, 0x46,
	0xd6, 0xc8, 0x1b, 0xa6, 0xab, 0x67, 0x69, 0xe4, 0x91, 0x16, 0xed, 0x10, 0x36, 0x90, 0x45, 0x92,
	0xa9, 0xdc, 0xe3, 0x05, 0xd7, 0x47, 0xde, 0xf0, 0x34, 0xe6, 0x60, 0xbe, 0x18, 0x5f, 0xc1, 0x6e,
	0xd2, 0xdd, 0x4e, 0xc6, 0xbe, 0x17, 0x44, 0xcf, 0x9c, 0x30, 0xee, 0xb1, 0xb7, 0x00, 0x7c, 0x6b,
	0x28, 0x4c, 0xc7, 0xb5, 0xc5, 0xa5, 0xec, 0x26, 0x6d, 0xa4, 0x9c, 0x20, 0x41, 0xdb, 0x05, 0x1a,
	0x98, 0xa1, 0xf3, 0x0d, 0xef, 0xa4, 0xd1, 0x6f, 0x21, 0xe1, 0x95, 0xf3, 0x8d, 0x30, 0xfe, 0x55,
	0x81, 0x9b, 0xe5, 0xba, 0xaf, 0xd5, 0xa4, 0x3e, 0x8f, 0x9b, 0x14, 0xc6, 0xed, 0x1e, 0x35, 0xa9,
	0x39, 0xda, 0x0f, 0x69, 0x40, 0x53, 0xb4, 0x4d, 0x68, 0x44, 0x5e, 0x64, 0x8d, 0xa8, 0x4b, 0xd4,
	0xfa, 0x3c, 0xe8, 0x9d, 0x42, 0x1d, 0x65, 0xd0, 0xc6, 0x41, 0x20, 0xac, 0x48, 0xd8, 0xa6, 0x15,
	0xc9, 0xb0, 0xb6, 0x25, 0xe5, 0x28, 0xd2, 0xd6, 0xa0, 0x36, 0x09, 0x46, 0x72, 0x37, 0xf8, 0x53,
	0xdb, 0x86, 0x66, 0x20, 0xc2, 0xc9, 0x48, 0xb9, 0x55, 0x8e, 0x8c, 0x01, 0xdc, 0x2b, 0xeb, 0x88,
	0x4f, 0x2e, 0x3f, 0xa4, 0x57, 0x7f, 0xa8, 0xc2, 0xfd, 0x45, 0xab, 0x5c, 0xcb, 0xbf, 0xc7, 0x19,
	0xff, 0x3e, 0x9c, 0x75, 0x08, 0x14, 0xd7, 0x59, 0xec, 0xe9, 0xdf, 0x57, 0xae, 0xe6, 0xea, 0x4d,
	0xac, 0x4d, 0x6c, 0xa8, 0x6c, 0x34, 0x0f, 0x70, 0x52, 0x21, 0x93, 0xdb, 0x71, 0x06, 0xab, 0xf8,
	0xd4, 0xcb, 0xe2, 0xd3, 0xc8, 0xc4, 0xe7, 0x33, 0xb8, 0x91, 0xcf, 0x18, 0x15, 0x11, 0x0d, 0xea,
	0xe7, 0xd8, 0xc4, 0x70, 0x4b, 0xdd, 0x3e, 0xfd, 0x36, 0xbe, 0x04, 0xbd, 0x28, 0x7e, 0xad, 0xa3,
	0xff, 0x0e, 0xec, 0xe5, 0x35, 0xbd, 0x16, 0x63, 0x7f, 0x64, 0x45, 0x42, 0x6e, 0xc0, 0x18, 0xc1,
	0xfe, 0x6c, 0x91, 0x6b, 0xc5, 0xb3, 0x07, 0xad, 0x48, 0x6a, 0x20, 0xa7, 0x75, 0xfb, 0xf1, 0xd8,
	0x78, 0x33, 0x13, 0x47, 0x70, 0x78, 0xaf, 0x69, 0xe7, 0x5f, 0xab, 0x60, 0xcc, 0xd0, 0xfb, 0x81,
	0xd2, 0x3f, 0x83, 0x2b, 0x6a, 0x05, 0xd0, 0x37, 0x16, 0xe3, 0x33, 0x3c, 0xfb, 0xf8, 0x44, 0xe2,
	0x94, 0xe8, 0x32, 0x51, 0x1e, 0x48, 0x9f, 0x82, 0x96, 0x06, 0x1f, 0x78, 0x3e, 0x07, 0x2a, 0x4f,
	0xd6, 0x52, 0x10, 0xe4, 0x15, 0xd2, 0xf1, 0x18, 0xcf, 0x48, 0x0b, 0xd7, 0x96, 0xa0, 0x60, 0x25,
	0x25, 0xfb, 0xc4, 0xb5, 0x17, 0x22, 0x02, 0xe3, 0x2f, 0x15, 0x38, 0x98, 0xeb, 0x9b, 0x6b, 0x05,
	0x39, 0xae, 0x37, 0xc6, 0x48, 0x3c, 0xd0, 0x3e, 0x96, 0xa5, 0x5c, 0xa7, 0x52, 0xde, 0x4a, 0x97,
	0x32, 0x63, 0x34, 0x2b, 0xb2, 0x24, 0x8c, 0xfb, 0xae, 0x0e, 0xeb, 0x05, 0xde, 0xff, 0xd0, 0xdb,
	0x55, 0xd1, 0x5b, 0x01, 0x15, 0xb4, 0x4b, 0x50, 0xc1, 0x21, 0x6c, 0x24, 0x17, 0x0d, 0xae, 0x70,
	0x34, 0x06, 0x68, 0xdd, 0xf5, 0xf8, 0xb6, 0x21, 0x6b, 0xdf, 0xd6, 0x0c, 0x58, 0x16, 0x8c, 0xf4,
	0x7e, 0x6d, 0xbb, 0x8e, 0xe5, 0x49, 0x0c, 0xd7, 0x11, 0xa8, 0xed, 0x97, 0x44, 0x4a, 0xa3, 0xa7,
	0xee, 0x95, 0xd0, 0xd3, 0xf2, 0x0c, 0xf4, 0xd4, 0x83, 0xb6, 0x13, 0x9a, 0x03, 0xcb, 0x35, 0x27,
	0x3e, 0x61, 0xb8, 0x46, 0x7f, 0xc9, 0x09, 0x8f, 0x2d, 0xf7, 0x8d, 0x6f, 0x7c, 0x57, 0x81, 0x96,
	0x52, 0x89, 0x81, 0x0f, 0xc4, 0x00, 0x01, 0x11, 0x23, 0x1c, 0xce, 0x8b, 0x8e, 0xa4, 0x11, 0xce,
	0xd9, 0x85, 0xf6, 0xd8, 0x3b, 0x93, 0x98, 0x91, 0x13, 0xa3, 0x35, 0xf6, 0xce, 0x66, 0x21, 0xc6,
	0x5a, 0x19, 0x62, 0xfc, 0x08, 0x56, 0xd5, 0x32, 0xca, 0x40, 0xce, 0x8e, 0x15, 0x49, 0x96, 0x1b,
	0x32, 0xbe, 0xaf, 0x40, 0x83, 0x2c, 0xc1, 0x6c, 0x25, 0x5b, 0x52, 0x48, 0x89, 0xc6, 0x8c, 0x63,
	0x25, 0x6b, 0x9c, 0x54, 0x0f, 0x30, 0x17, 0x29, 0x98, 0xab, 0x2c, 0x40, 0x36, 0xc9, 0xc3, 0x85,
	0x28, 0x64, 0x51, 0x3c, 0xdf, 0x0f, 0x9c, 0x01, 0x77, 0x94, 0xaa, 0x9c, 0xff, 0x12, 0x29, 0x68,
	0xb2, 0x9c, 0x3f, 0x19, 0x53, 0x7a, 0x36, 0xfa, 0xbc, 0x99, 0x17, 0x93, 0xb1, 0xf1, 0x63, 0x0b,
	0xda, 0x49, 0x51, 0xad, 0x40, 0x35, 0xde, 0x60, 0xd5, 0xb1, 0x33, 0x45, 0x56, 0x2d, 0x14, 0x19,
	0xb3, 0xac, 0xb1, 0x37, 0x71, 0x23, 0x59, 0x43, 0x9c, 0xb5, 0x47, 0x44, 0xd2, 0x6e, 0xc0, 0x92,
	0x82, 0xef, 0xec, 0x9f, 0xe6, 0x84, 0xa1, 0xbb, 0x01, 0xcb, 0xc4, 0x18, 0x89, 0x0b, 0x31, 0x52,
	0x45, 0xd3, 0xe8, 0x77, 0x90, 0xf8, 0x0c, 0x69, 0x27, 0x36, 0xee, 0x9a, 0x4a, 0x85, 0x8c, 0xe6,
	0x3a, 0x69, 0x21, 0x41, 0x45, 0x91, 0x98, 0xd1, 0xd4, 0x57, 0xc5, 0x41, 0xcc, 0xd7, 0x53, 0x9f,
	0xfc, 0x45, 0xcc, 0xc1, 0x74, 0x30, 0x12, 0x54, 0x14, 0x8d, 0x3e, 0x89, 0x1f, 0x23, 0x01, 0x6d,
	0xf2, 0xad, 0x29, 0x77, 0x06, 0x2e, 0x85, 0x25, 0xdf, 0x9a, 0x52, 0x57, 0x88, 0x0f, 0x77, 0x48,
	0x1f, 0xee, 0x7b, 0xd0, 0x11, 0x97, 0xbe, 0x13, 0x4c, 0x4d, 0x1b, 0x79, 0x9c, 0xe9, 0xc0, 0xa4,
	0xc7, 0x28, 0x70, 0x00, 0xcb, 0x81, 0x38, 0x9f, 0xb8, 0xb6, 0xf2, 0x45, 0x97, 0xa6, 0x77, 0x99,
	0x28, 0x9d, 0xb1, 0x07, 0x1d, 0x29, 0x44, 0x2b, 0xcb, 0xeb, 0x0a, 0x93, 0x68, 0xf1, 0x3d, 0xe8,
	0x30, 0xcc, 0x60, 0x01, 0xbe, 0xab, 0x48, 0x2c, 0xa2, 0x04, 0x26, 0xbe, 0x1d, 0x0b, 0xf0, 0x5d,
	0x05, 0x98, 0xa4, 0x9a, 0x1a, 0xd9, 0xcc, 0x3e, 0x5b, 0x93, 0xd0, 0x05, 0x29, 0xe4, 0xb4, 0xbb,
	0xb0, 0x12, 0x3b, 0x8d, 0x45, 0xd6, 0xb9, 0x13, 0x28, 0xcf, 0xa9, 0x74, 0xa2, 0xd8, 0xc8, 0x02,
	0xd0, 0xe4, 0x2a, 0x61, 0x7c, 0x3c, 0x6d, 0x43, 0x33, 0xf4, 0x26, 0xc1, 0x40, 0xe8, 0x1b, 0x64,
	0xa6, 0x1c, 0xc5, 0x31, 0xf1, 0xad, 0x30, 0xd4, 0x37, 0x93, 0x80, 0xbd, 0xb4, 0xc2, 0x90, 0xf1,
	0xce, 0xd0, 0xf1, 0x5c, 0x7d, 0x4b, 0xe1, 0x1d, 0x1c, 0x21, 0x02, 0xf0, 0x03, 0xef, 0xc2, 0x71,
	0x07, 0x42, 0xdf, 0xe6, 0x39, 0x6a, 0x4c, 0xc7, 0x8c, 0x13, 0x4d, 0xf5, 0x1b, 0x44, 0xa7, 0xdf,
	0xd8, 0x70, 0x6d, 0x27, 0x34, 0xe5, 0x21, 0x4a, 0x46, 0xe8, 0x5c, 0xa2, 0xb6, 0x13, 0x3e, 0x27,
	0x2a, 0x59, 0x61, 0xc0, 0x72, 0x4a, 0xce, 0xb1, 0xf5, 0x1d, 0x6e, 0x05, 0xb1, 0xd4, 0x89, 0x8d,
	0xd5, 0x8e, 0x32, 0x03, 0x6f, 0x3c, 0x76, 0xc2, 0x10, 0xf7, 0xd6, 0x23, 0x83, 0x70, 0xe6, 0x71,
	0x4c, 0xc4, 0x2c, 0x56, 0xbd, 0x7d, 0x97, 0xf7, 0x3e, 0xe0, 0xa6, 0x5e, 0xd2, 0xfc, 0x6f, 0x96,
	0x35, 0xff, 0xbb, 0xd8, 0x55, 0xcc, 0xb4, 0x53, 0x6f, 0xb1, 0xdf, 0x85, 0xfb, 0x26, 0x71, 0xeb,
	0x0e, 0xb4, 0xc2, 0xc8, 0x0b, 0x04, 0xaa, 0xb9, 0xcd, 0x3d, 0x8e, 0xc6, 0x27, 0x36, 0xa3, 0x4b,
	0x64, 0x91, 0xbd, 0x7b, 0x0a, 0x5d, 0x7a, 0x01, 0x47, 0x6c, 0x0b, 0x9a, 0x5e, 0x30, 0xc4, 0x79,
	0xfb, 0x9c, 0xb6, 0x5e, 0x30, 0x3c, 0xb1, 0x0b, 0xb7, 0xf6, 0x3b, 0xc5, 0x5b, 0xfb, 0x3a, 0xd4,
	0x23, 0x9c, 0x67, 0x30, 0x30, 0x8d, 0x4e, 0x0a, 0x20, 0xe1, 0xa0, 0x70, 0xf0, 0xec, 0x52, 0x33,
	0xe6, 0xec, 0xd7, 0xef, 0x72, 0xe9, 0x39, 0xe1, 0x13, 0x1a, 0xa3, 0x4b, 0x84, 0x1b, 0x39, 0xd1,
	0xd4, 0x8c, 0xdb, 0xc6, 0x3d, 0xd5, 0x41, 0x91, 0x7c, 0x2a, 0x5f, 0x89, 0x7e, 0x0b, 0x9b, 0x4f,
	0xd5, 0x3d, 0xf2, 0x43, 0x23, 0x8b, 0x3b, 0x19, 0x64, 0xb1, 0x8c, 0xa7, 0x49, 0x1e, 0x51, 0xfc,
	0x58, 0x83, 0x8d, 0xec, 0xfa, 0x8c, 0xfa, 0x6e, 0x42, 0x82, 0xf1, 0x8a, 0xa0, 0x0f, 0x73, 0x55,
	0x62, 0xbc, 0x02, 0xe6, 0xd3, 0xa0, 0x4e, 0xbd, 0x88, 0x77, 0x42, 0xbf, 0x67, 0xb7, 0xbf, 0x74,
	0x57, 0x6d, 0x64, 0xbb, 0xea, 0x36, 0x34, 0x05, 0xc1, 0x5b, 0x6a, 0x79, 0x8d, 0xbe, 0x1c, 0xe1,
	0x0d, 0xc2, 0xb1, 0x43, 0x6a, 0x75, 0xed, 0x3e, 0xfe, 0xcc, 0xd7, 0x69, 0xab, 0x50, 0xa7, 0x71,
	0x33, 0x6b, 0xa7, 0x9b, 0x59, 0xa6, 0xad, 0x42, 0xae, 0xad, 0x26, 0xa5, 0xdd, 0xc9, 0x94, 0xf6,
	0x5d, 0x58, 0x51, 0x2d, 0x53, 0xa2, 0x51, 0x7e, 0x8e, 0xe9, 0xca, 0xc6, 0xc9, 0x48, 0x74, 0x9f,
	0x12, 0x2e, 0x41, 0xa1, 0xb2, 0xc5, 0x49, 0x19, 0x44, 0xa0, 0x9b, 0xd0, 0x20, 0x43, 0x65, 0x73,
	0xe3, 0x81, 0xb4, 0x19, 0xd3, 0x69, 0x35, 0xb6, 0x79, 0x46, 0x32, 0xad, 0x95, 0x24, 0x53, 0x59,
	0x1d, 0xae, 0x97, 0xd4, 0xa1, 0xf1, 0x39, 0xdc, 0x7c, 0x2a, 0xa2, 0xcc, 0x4d, 0xe2, 0xfd, 0xc4,
	0x89, 0xa6, 0x57, 0x78, 0xd5, 0x7c, 0x0f, 0xb7, 0x66, 0x4c, 0xbd, 0x56, 0xe2, 0x1a, 0x99, 0x7b,
	0xec, 0x0a, 0xa6, 0x28, 0xeb, 0x4b, 0xe5, 0xe8, 0x3f, 0xaa, 0xb0, 0x8d, 0x6b, 0x06, 0x7d, 0x3a,
	0x22, 0xd2, 0x69, 0x3a, 0x07, 0xfa, 0xee, 0x43, 0x57, 0x04, 0xbe, 0x99, 0x3b, 0xb4, 0x41, 0x04,
	0xfe, 0x69, 0x92, 0x61, 0x19, 0x6c, 0x23, 0x47, 0xf2, 0x0a, 0x1b, 0x48, 0xc8, 0x5c, 0x8f, 0xaf,
	0xb0, 0x01, 0x03, 0xe6, 0x1d, 0x68, 0x09, 0x75, 0x76, 0xc9, 0x9c, 0x15, 0xc9, 0xc1, 0x65, 0xd9,
	0x63, 0xc7, 0x95, 0x2d, 0x83, 0x13, 0x17, 0x88, 0xc4, 0x2d, 0x23, 0x49, 0xea, 0xa5, 0x4c, 0x52,
	0x67, 0x2f, 0x59, 0xad, 0xb9, 0x97, 0xac, 0x76, 0xae, 0xe0, 0xe6, 0xe5, 0xb1, 0xf1, 0xa7, 0x0a,
	0xdc, 0x28, 0xf8, 0xee, 0x03, 0xb6, 0x98, 0xff, 0xcb, 0xb4, 0x98, 0x1b, 0x04, 0x58, 0xb3, 0xcb,
	0xa5, 0x02, 0xf9, 0x87, 0x3a, 0x35, 0x9b, 0x3c, 0x17, 0x2d, 0x90, 0x80, 0x40, 0x42, 0xae, 0x46,
	0xbf, 0xc5, 0x04, 0x46, 0x3f, 0xc9, 0xd9, 0xa5, 0x60, 0xaa, 0x3a, 0xb8, 0x66, 0x85, 0x30, 0x9d,
	0x17, 0xf5, 0x6c, 0x5e, 0xdc, 0x02, 0xb0, 0x7c, 0x7f, 0x34, 0x65, 0xc4, 0xc4, 0x70, 0xab, 0x4d,
	0x14, 0x05, 0x99, 0x98, 0x8d, 0xfd, 0x43, 0xa2, 0x2d, 0x66, 0xe3, 0x09, 0x85, 0x00, 0x87, 0x9f,
	0x5a, 0x95, 0x97, 0xb8, 0x0f, 0x75, 0x89, 0xa8, 0x5e, 0x6c, 0x77, 0xa0, 0x65, 0xd9, 0x32, 0x43,
	0xb8, 0x1b, 0x21, 0xc2, 0x2f, 0xcb, 0x90, 0x76, 0x21, 0x43, 0x70, 0x7d, 0x12, 0xa0, 0xf5, 0x41,
	0xae, 0x8f, 0x14, 0x5a, 0x3f, 0x66, 0x93, 0xf2, 0x4e, 0x8a, 0x4d, 0xea, 0x0f, 0x60, 0x99, 0xd9,
	0x6a, 0x7b, 0xb2, 0x3b, 0x11, 0x31, 0xb5, 0x3d, 0x7b, 0x2a, 0x37, 0xb0, 0xcc, 0xe7, 0xab, 0x3d,
	0xe5, 0xd5, 0xaf, 0xf8, 0x50, 0x9c, 0xaf, 0xad, 0xd5, 0x42, 0x6d, 0x65, 0x92, 0x72, 0x2d, 0x97,
	0x94, 0x7f, 0xab, 0xc0, 0xd6, 0x31, 0xa1, 0x39, 0x95, 0x0a, 0xaa, 0x9e, 0x93, 0x78, 0x56, 0x66,
	0xc6, 0xb3, 0x5a, 0x78, 0x48, 0xc8, 0x46, 0xa4, 0x56, 0x12, 0x91, 0x6c, 0xd0, 0xeb, 0xf3, 0x83,
	0xde, 0xc8, 0x05, 0xdd, 0xf8, 0x1a, 0xb6, 0xf3, 0xdb, 0xbd, 0x6e, 0x09, 0x89, 0x20, 0xf0, 0x02,
	0xb9, 0x45, 0x1e, 0x18, 0xbf, 0x82, 0xed, 0xc7, 0x62, 0xf4, 0xd6, 0x61, 0xdf, 0x61, 0x5b, 0x55,
	0xde, 0x48, 0xee, 0x20, 0x0d, 0xba, 0x83, 0xa8, 0x63, 0xd4, 0xf5, 0xa4, 0x66, 0x3a, 0x46, 0x5f,
	0x78, 0xe8, 0x6d, 0x66, 0x24, 0xd7, 0xa2, 0x16, 0xb1, 0xd0, 0xdb, 0x3f, 0x85, 0x95, 0xb7, 0x83,
	0x9f, 0x5b, 0xe1, 0x35, 0x9f, 0xa6, 0x8c, 0x6f, 0x2b, 0xd4, 0xf2, 0x69, 0x77, 0xa7, 0xbe, 0x08,
	0xac, 0x48, 0x3c, 0xf3, 0x86, 0x1f, 0x06, 0x2c, 0x64, 0x5b, 0x6d, 0x6d, 0x5e, 0xab, 0xad, 0x67,
	0x5a, 0xad, 0xf1, 0xe7, 0x0a, 0xdc, 0x9e, 0xb5, 0xab, 0xff, 0x6e, 0x7f, 0xcb, 0x2d, 0x99, 0xea,
	0x6f, 0x7f, 0xaf, 0xc0, 0x46, 0x09, 0xb7, 0x10, 0xc7, 0x4c, 0xb8, 0xaa, 0xd9, 0x70, 0xe5, 0x2f,
	0x3f, 0xb5, 0xc2, 0xe5, 0x67, 0x4e, 0x6f, 0xcb, 0x54, 0x5d, 0x23, 0x07, 0x69, 0xd2, 0xb7, 0xbd,
	0x66, 0xe6, 0xb6, 0x67, 0x3c, 0x83, 0xcd, 0xb7, 0x8e, 0xaf, 0xb2, 0x3b, 0xfe, 0xc2, 0x39, 0xbf,
	0x31, 0xa7, 0x40, 0x5d, 0x35, 0x0d, 0xea, 0x8c, 0x08, 0xb6, 0x4a, 0xb4, 0x85, 0xfe, 0x7f, 0x18,
	0x90, 0xcf, 0x32, 0xdf, 0x39, 0x77, 0xd0, 0xf5, 0x05, 0xb5, 0x69, 0xe7, 0x57, 0x4b, 0x96, 0x5d,
	0x7c, 0xbc, 0x24, 0x1d, 0xa7, 0x3a, 0xb3, 0xe3, 0xd4, 0xe6, 0x23, 0x8b, 0x7a, 0xa1, 0xfb, 0x2d,
	0x38, 0x63, 0x3e, 0x51, 0xe0, 0xaf, 0x49, 0x59, 0xb5, 0x29, 0x4d, 0x4b, 0xbe, 0xca, 0xa2, 0x55,
	0x12, 0x12, 0xde, 0x87, 0xa6, 0x20, 0x40, 0xa4, 0x2f, 0x95, 0x42, 0x24, 0xc9, 0x45, 0x3b, 0x2c,
	0xfb, 0xc2, 0x09, 0xd5, 0x35, 0x5f, 0x8e, 0x0a, 0x5f, 0x0f, 0xdb, 0xf9, 0xaf, 0x87, 0xc6, 0xb7,
	0x55, 0x58, 0xcb, 0xaf, 0xae, 0x6d, 0xe0, 0x16, 0x13, 0x7f, 0xd5, 0xbd, 0x80, 0x23, 0xae, 0x60,
	0x25, 0x17, 0xb2, 0xba, 0xff, 0x65, 0xf2, 0xae, 0x96, 0xcb, 0xbb, 0xfc, 0xf3, 0x48, 0xbd, 0xf8,
	0x3c, 0x92, 0x4e, 0xcd, 0x46, 0xf6, 0x21, 0x22, 0xf7, 0xe4, 0xd0, 0x2c, 0x3c, 0x39, 0x2c, 0x7c,
	0x1f, 0x4c, 0xe5, 0x69, 0x2b, 0x73, 0xf9, 0xc8, 0x20, 0x0b, 0x89, 0xaa, 0x14, 0xb2, 0x30, 0x7e,
	0xa8, 0x00, 0x24, 0x6e, 0xa6, 0x6d, 0xd0, 0x28, 0xfd, 0x9c, 0x06, 0x4c, 0x52, 0x65, 0x2b, 0x05,
	0x28, 0xe6, 0xec, 0x1f, 0x29, 0xa0, 0xce, 0x98, 0x49, 0x28, 0x6c, 0xb9, 0x4d, 0xf9, 0xce, 0x8a,
	0x94, 0xe4, 0xe4, 0xe5, 0xf9, 0x03, 0xcf, 0x8d, 0x84, 0x1b, 0x3f, 0xb3, 0x32, 0xf5, 0x98, 0x89,
	0x78, 0x9b, 0x1f, 0x3a, 0xe7, 0x91, 0x99, 0xbb, 0x35, 0x75, 0x90, 0x28, 0xb3, 0xef, 0xd1, 0xf7,
	0x1d, 0xd8, 0x48, 0x07, 0xf4, 0x95, 0x08, 0x2e, 0x9c, 0x81, 0xd0, 0x8e, 0xa0, 0x9b, 0xbe, 0xea,
	0x69, 0x0a, 0xad, 0xe5, 0x2f, 0x7f, 0x3d, 0xbd, 0xc8, 0x90, 0x2d, 0xf5, 0x6b, 0xd8, 0x2a, 0x45,
	0xff, 0xda, 0xbe, 0x42, 0x7e, 0xb3, 0xee, 0x14, 0xbd, 0x3b, 0x73, 0x24, 0xa4, 0xf6, 0xe7, 0xa0,
	0xb1, 0x40, 0x1a, 0x21, 0x6a, 0xbd, 0x12, 0x50, 0xa9, 0x94, 0xee, 0x96, 0xf2, 0xa4, 0xba, 0x2f,
	0x61, 0xbd, 0xd0, 0x10, 0x34, 0xbd, 0xb4, 0x8f, 0xf4, 0xc5, 0xfb, 0xde, 0xce, 0x0c, 0x4e, 0xe8,
	0x6b, 0xbf, 0x80, 0x75, 0x09, 0x00, 0x12, 0xb6, 0x46, 0xf2, 0xa5, 0x30, 0xa6, 0xd7, 0x2b, 0x63,
	0xc9, 0x5d, 0x1d, 0xc1, 0x6a, 0xee, 0xb8, 0x67, 0x0b, 0xcb, 0x31, 0x40, 0x4f, 0xa3, 0x5d, 0x65,
	0xcf, 0x6f, 0x93, 0xee, 0x43, 0x25, 0x47, 0x9f, 0x76, 0x27, 0x1d, 0xb9, 0xd2, 0xc3, 0xba, 0x67,
	0xcc, 0x13, 0x91, 0x0b, 0xb8, 0xb0, 0x3b, 0xe7, 0xeb, 0x87, 0x76, 0x5f, 0xaa, 0x58, 0xf0, 0xe9,
	0xa8, 0xf7, 0xd1, 0x42, 0x39, 0xb9, 0x5e, 0x44, 0x08, 0x63, 0xce, 0x1f, 0x54, 0x1e, 0xcc, 0xd1,
	0x94, 0xf9, 0x43, 0x4e, 0xef, 0xe3, 0x2b, 0x48, 0xca, 0x55, 0x83, 0x99, 0xab, 0xf2, 0x77, 0xb5,
	0x2b, 0xdb, 0x39, 0x6f, 0xcd, 0xdc, 0x27, 0xba, 0xdf, 0xc0, 0xed, 0xf9, 0xdf, 0x69, 0xb5, 0x8f,
	0xaf, 0xf2, 0x2d, 0x97, 0xd7, 0xfd, 0xe4, 0xea, 0x9f, 0x7d, 0x35, 0x51, 0xfc, 0x3e, 0xaa, 0x3e,
	0x59, 0x6a, 0x07, 0x65, 0x9f, 0xe7, 0x73, 0xdf, 0x3c, 0x7b, 0x77, 0xe7, 0x0b, 0xc9, 0x65, 0x9e,
	0xf2, 0x1f, 0xd2, 0x92, 0xff, 0x5f, 0x71, 0x99, 0x94, 0xfe, 0x27, 0x8c, 0xcb, 0x64, 0xc6, 0xdf,
	0xb5, 0x1e, 0xc1, 0x92, 0xfc, 0x27, 0x9a, 0xa6, 0x29, 0xb1, 0xd4, 0xd4, 0x8d, 0x0c, 0x2d, 0xee,
	0x1f, 0x6b, 0xf9, 0x0d, 0x6a, 0xbb, 0x65, 0xdb, 0x56, 0x5a, 0x6e, 0x96, 0x33, 0xa5, 0xba, 0xaf,
	0x60, 0xb3, 0xec, 0x3f, 0x0b, 0xda, 0xde, 0xec, 0x7f, 0x33, 0xb0, 0xda, 0xfd, 0x45, 0x7f, 0x77,
	0x38, 0x6b, 0xd2, 0xdf, 0xf7, 0x7e, 0xf2, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xdc, 0x88, 0x1b,
	0x56, 0xf8, 0x27, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VipCardOrderServiceClient is the client API for VipCardOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VipCardOrderServiceClient interface {
	//会员卡订单
	GetOrderList(ctx context.Context, in *GetOrderListRequest, opts ...grpc.CallOption) (*GetOrderListResponse, error)
	GetVipCardOrderEquity(ctx context.Context, in *GetVipCardOrderEquityRequest, opts ...grpc.CallOption) (*GetVipCardOrderEquityResponse, error)
	GetVipVrRefundList(ctx context.Context, in *GetVrRefundListRequest, opts ...grpc.CallOption) (*GetVrRefundListResponse, error)
	VipVrRefundDetail(ctx context.Context, in *VipVrRefundDetailReq, opts ...grpc.CallOption) (*VipVrRefundDetailResp, error)
	CreateVipVrRefund(ctx context.Context, in *CreateVrRefundRequest, opts ...grpc.CallOption) (*CreateVrRefundResponse, error)
	DelVipOrderCard(ctx context.Context, in *DelVipOrderCardRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	GetOrderOperateLogList(ctx context.Context, in *GetOrderOperateLogListRequest, opts ...grpc.CallOption) (*GetOrderOperateLogListResponse, error)
	//会员卡实体卡订单列表
	GetPhysicalVipCardOrderList(ctx context.Context, in *GetPhysicalVipCardOrderListRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderListResponse, error)
	//会员卡实体卡订单详情
	GetPhysicalVipCardOrderDetail(ctx context.Context, in *GetPhysicalVipCardOrderDetailRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderDetailResponse, error)
	//会员卡实体卡导出
	GetPhysicalVipCardOrderExport(ctx context.Context, in *GetPhysicalVipCardOrderListRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderExportResponse, error)
	//健康会员卡实体卡订单导出结果列表
	PhysicalVipCardOrderExportList(ctx context.Context, in *PhysicalVipCardOrderExportListRequest, opts ...grpc.CallOption) (*PhysicalVipCardOrderExportListResponse, error)
	// 会员卡实体卡 批量导入物流模板
	PVCExpressImportTemplate(ctx context.Context, in *PVCExpressImportTemplateRequest, opts ...grpc.CallOption) (*PVCExpressImportTemplateResponse, error)
	// 会员卡实体卡更新物流信息
	PVCExpressEdit(ctx context.Context, in *PVCExpressEditRequest, opts ...grpc.CallOption) (*PVCExpressEditResponse, error)
	// 会员卡实体卡会员卡号编辑
	PVCEdit(ctx context.Context, in *PVCEditRequest, opts ...grpc.CallOption) (*PVCEditResponse, error)
	// 会员卡实体卡 批量导入物流
	PVCExpressImport(ctx context.Context, in *PVCExpressImportRequest, opts ...grpc.CallOption) (*PVCExpressImportResponse, error)
	// 会员卡实体卡 批量导入物流历史
	PVCExpressImportList(ctx context.Context, in *PVCExpressImportListRequest, opts ...grpc.CallOption) (*PVCExpressImportListResponse, error)
}

type vipCardOrderServiceClient struct {
	cc *grpc.ClientConn
}

func NewVipCardOrderServiceClient(cc *grpc.ClientConn) VipCardOrderServiceClient {
	return &vipCardOrderServiceClient{cc}
}

func (c *vipCardOrderServiceClient) GetOrderList(ctx context.Context, in *GetOrderListRequest, opts ...grpc.CallOption) (*GetOrderListResponse, error) {
	out := new(GetOrderListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetVipCardOrderEquity(ctx context.Context, in *GetVipCardOrderEquityRequest, opts ...grpc.CallOption) (*GetVipCardOrderEquityResponse, error) {
	out := new(GetVipCardOrderEquityResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetVipCardOrderEquity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetVipVrRefundList(ctx context.Context, in *GetVrRefundListRequest, opts ...grpc.CallOption) (*GetVrRefundListResponse, error) {
	out := new(GetVrRefundListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetVipVrRefundList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) VipVrRefundDetail(ctx context.Context, in *VipVrRefundDetailReq, opts ...grpc.CallOption) (*VipVrRefundDetailResp, error) {
	out := new(VipVrRefundDetailResp)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/VipVrRefundDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) CreateVipVrRefund(ctx context.Context, in *CreateVrRefundRequest, opts ...grpc.CallOption) (*CreateVrRefundResponse, error) {
	out := new(CreateVrRefundResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/CreateVipVrRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) DelVipOrderCard(ctx context.Context, in *DelVipOrderCardRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/DelVipOrderCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetOrderOperateLogList(ctx context.Context, in *GetOrderOperateLogListRequest, opts ...grpc.CallOption) (*GetOrderOperateLogListResponse, error) {
	out := new(GetOrderOperateLogListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetOrderOperateLogList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetPhysicalVipCardOrderList(ctx context.Context, in *GetPhysicalVipCardOrderListRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderListResponse, error) {
	out := new(GetPhysicalVipCardOrderListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetPhysicalVipCardOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetPhysicalVipCardOrderDetail(ctx context.Context, in *GetPhysicalVipCardOrderDetailRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderDetailResponse, error) {
	out := new(GetPhysicalVipCardOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetPhysicalVipCardOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) GetPhysicalVipCardOrderExport(ctx context.Context, in *GetPhysicalVipCardOrderListRequest, opts ...grpc.CallOption) (*GetPhysicalVipCardOrderExportResponse, error) {
	out := new(GetPhysicalVipCardOrderExportResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/GetPhysicalVipCardOrderExport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PhysicalVipCardOrderExportList(ctx context.Context, in *PhysicalVipCardOrderExportListRequest, opts ...grpc.CallOption) (*PhysicalVipCardOrderExportListResponse, error) {
	out := new(PhysicalVipCardOrderExportListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PhysicalVipCardOrderExportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PVCExpressImportTemplate(ctx context.Context, in *PVCExpressImportTemplateRequest, opts ...grpc.CallOption) (*PVCExpressImportTemplateResponse, error) {
	out := new(PVCExpressImportTemplateResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PVCExpressImportTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PVCExpressEdit(ctx context.Context, in *PVCExpressEditRequest, opts ...grpc.CallOption) (*PVCExpressEditResponse, error) {
	out := new(PVCExpressEditResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PVCExpressEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PVCEdit(ctx context.Context, in *PVCEditRequest, opts ...grpc.CallOption) (*PVCEditResponse, error) {
	out := new(PVCEditResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PVCEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PVCExpressImport(ctx context.Context, in *PVCExpressImportRequest, opts ...grpc.CallOption) (*PVCExpressImportResponse, error) {
	out := new(PVCExpressImportResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PVCExpressImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardOrderServiceClient) PVCExpressImportList(ctx context.Context, in *PVCExpressImportListRequest, opts ...grpc.CallOption) (*PVCExpressImportListResponse, error) {
	out := new(PVCExpressImportListResponse)
	err := c.cc.Invoke(ctx, "/oc.VipCardOrderService/PVCExpressImportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VipCardOrderServiceServer is the server API for VipCardOrderService service.
type VipCardOrderServiceServer interface {
	//会员卡订单
	GetOrderList(context.Context, *GetOrderListRequest) (*GetOrderListResponse, error)
	GetVipCardOrderEquity(context.Context, *GetVipCardOrderEquityRequest) (*GetVipCardOrderEquityResponse, error)
	GetVipVrRefundList(context.Context, *GetVrRefundListRequest) (*GetVrRefundListResponse, error)
	VipVrRefundDetail(context.Context, *VipVrRefundDetailReq) (*VipVrRefundDetailResp, error)
	CreateVipVrRefund(context.Context, *CreateVrRefundRequest) (*CreateVrRefundResponse, error)
	DelVipOrderCard(context.Context, *DelVipOrderCardRequest) (*VcBaseResponse, error)
	GetOrderOperateLogList(context.Context, *GetOrderOperateLogListRequest) (*GetOrderOperateLogListResponse, error)
	//会员卡实体卡订单列表
	GetPhysicalVipCardOrderList(context.Context, *GetPhysicalVipCardOrderListRequest) (*GetPhysicalVipCardOrderListResponse, error)
	//会员卡实体卡订单详情
	GetPhysicalVipCardOrderDetail(context.Context, *GetPhysicalVipCardOrderDetailRequest) (*GetPhysicalVipCardOrderDetailResponse, error)
	//会员卡实体卡导出
	GetPhysicalVipCardOrderExport(context.Context, *GetPhysicalVipCardOrderListRequest) (*GetPhysicalVipCardOrderExportResponse, error)
	//健康会员卡实体卡订单导出结果列表
	PhysicalVipCardOrderExportList(context.Context, *PhysicalVipCardOrderExportListRequest) (*PhysicalVipCardOrderExportListResponse, error)
	// 会员卡实体卡 批量导入物流模板
	PVCExpressImportTemplate(context.Context, *PVCExpressImportTemplateRequest) (*PVCExpressImportTemplateResponse, error)
	// 会员卡实体卡更新物流信息
	PVCExpressEdit(context.Context, *PVCExpressEditRequest) (*PVCExpressEditResponse, error)
	// 会员卡实体卡会员卡号编辑
	PVCEdit(context.Context, *PVCEditRequest) (*PVCEditResponse, error)
	// 会员卡实体卡 批量导入物流
	PVCExpressImport(context.Context, *PVCExpressImportRequest) (*PVCExpressImportResponse, error)
	// 会员卡实体卡 批量导入物流历史
	PVCExpressImportList(context.Context, *PVCExpressImportListRequest) (*PVCExpressImportListResponse, error)
}

// UnimplementedVipCardOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVipCardOrderServiceServer struct {
}

func (*UnimplementedVipCardOrderServiceServer) GetOrderList(ctx context.Context, req *GetOrderListRequest) (*GetOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderList not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetVipCardOrderEquity(ctx context.Context, req *GetVipCardOrderEquityRequest) (*GetVipCardOrderEquityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipCardOrderEquity not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetVipVrRefundList(ctx context.Context, req *GetVrRefundListRequest) (*GetVrRefundListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipVrRefundList not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) VipVrRefundDetail(ctx context.Context, req *VipVrRefundDetailReq) (*VipVrRefundDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VipVrRefundDetail not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) CreateVipVrRefund(ctx context.Context, req *CreateVrRefundRequest) (*CreateVrRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVipVrRefund not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) DelVipOrderCard(ctx context.Context, req *DelVipOrderCardRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelVipOrderCard not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetOrderOperateLogList(ctx context.Context, req *GetOrderOperateLogListRequest) (*GetOrderOperateLogListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderOperateLogList not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetPhysicalVipCardOrderList(ctx context.Context, req *GetPhysicalVipCardOrderListRequest) (*GetPhysicalVipCardOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPhysicalVipCardOrderList not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetPhysicalVipCardOrderDetail(ctx context.Context, req *GetPhysicalVipCardOrderDetailRequest) (*GetPhysicalVipCardOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPhysicalVipCardOrderDetail not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) GetPhysicalVipCardOrderExport(ctx context.Context, req *GetPhysicalVipCardOrderListRequest) (*GetPhysicalVipCardOrderExportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPhysicalVipCardOrderExport not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PhysicalVipCardOrderExportList(ctx context.Context, req *PhysicalVipCardOrderExportListRequest) (*PhysicalVipCardOrderExportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PhysicalVipCardOrderExportList not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PVCExpressImportTemplate(ctx context.Context, req *PVCExpressImportTemplateRequest) (*PVCExpressImportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PVCExpressImportTemplate not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PVCExpressEdit(ctx context.Context, req *PVCExpressEditRequest) (*PVCExpressEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PVCExpressEdit not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PVCEdit(ctx context.Context, req *PVCEditRequest) (*PVCEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PVCEdit not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PVCExpressImport(ctx context.Context, req *PVCExpressImportRequest) (*PVCExpressImportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PVCExpressImport not implemented")
}
func (*UnimplementedVipCardOrderServiceServer) PVCExpressImportList(ctx context.Context, req *PVCExpressImportListRequest) (*PVCExpressImportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PVCExpressImportList not implemented")
}

func RegisterVipCardOrderServiceServer(s *grpc.Server, srv VipCardOrderServiceServer) {
	s.RegisterService(&_VipCardOrderService_serviceDesc, srv)
}

func _VipCardOrderService_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetOrderList(ctx, req.(*GetOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetVipCardOrderEquity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVipCardOrderEquityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetVipCardOrderEquity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetVipCardOrderEquity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetVipCardOrderEquity(ctx, req.(*GetVipCardOrderEquityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetVipVrRefundList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVrRefundListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetVipVrRefundList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetVipVrRefundList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetVipVrRefundList(ctx, req.(*GetVrRefundListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_VipVrRefundDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VipVrRefundDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).VipVrRefundDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/VipVrRefundDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).VipVrRefundDetail(ctx, req.(*VipVrRefundDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_CreateVipVrRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVrRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).CreateVipVrRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/CreateVipVrRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).CreateVipVrRefund(ctx, req.(*CreateVrRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_DelVipOrderCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVipOrderCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).DelVipOrderCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/DelVipOrderCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).DelVipOrderCard(ctx, req.(*DelVipOrderCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetOrderOperateLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderOperateLogListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetOrderOperateLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetOrderOperateLogList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetOrderOperateLogList(ctx, req.(*GetOrderOperateLogListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetPhysicalVipCardOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhysicalVipCardOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetPhysicalVipCardOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderList(ctx, req.(*GetPhysicalVipCardOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetPhysicalVipCardOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhysicalVipCardOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetPhysicalVipCardOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderDetail(ctx, req.(*GetPhysicalVipCardOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_GetPhysicalVipCardOrderExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhysicalVipCardOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/GetPhysicalVipCardOrderExport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).GetPhysicalVipCardOrderExport(ctx, req.(*GetPhysicalVipCardOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PhysicalVipCardOrderExportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PhysicalVipCardOrderExportListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PhysicalVipCardOrderExportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PhysicalVipCardOrderExportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PhysicalVipCardOrderExportList(ctx, req.(*PhysicalVipCardOrderExportListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PVCExpressImportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PVCExpressImportTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PVCExpressImportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PVCExpressImportTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PVCExpressImportTemplate(ctx, req.(*PVCExpressImportTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PVCExpressEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PVCExpressEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PVCExpressEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PVCExpressEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PVCExpressEdit(ctx, req.(*PVCExpressEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PVCEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PVCEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PVCEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PVCEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PVCEdit(ctx, req.(*PVCEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PVCExpressImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PVCExpressImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PVCExpressImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PVCExpressImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PVCExpressImport(ctx, req.(*PVCExpressImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardOrderService_PVCExpressImportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PVCExpressImportListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardOrderServiceServer).PVCExpressImportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.VipCardOrderService/PVCExpressImportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardOrderServiceServer).PVCExpressImportList(ctx, req.(*PVCExpressImportListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VipCardOrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.VipCardOrderService",
	HandlerType: (*VipCardOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOrderList",
			Handler:    _VipCardOrderService_GetOrderList_Handler,
		},
		{
			MethodName: "GetVipCardOrderEquity",
			Handler:    _VipCardOrderService_GetVipCardOrderEquity_Handler,
		},
		{
			MethodName: "GetVipVrRefundList",
			Handler:    _VipCardOrderService_GetVipVrRefundList_Handler,
		},
		{
			MethodName: "VipVrRefundDetail",
			Handler:    _VipCardOrderService_VipVrRefundDetail_Handler,
		},
		{
			MethodName: "CreateVipVrRefund",
			Handler:    _VipCardOrderService_CreateVipVrRefund_Handler,
		},
		{
			MethodName: "DelVipOrderCard",
			Handler:    _VipCardOrderService_DelVipOrderCard_Handler,
		},
		{
			MethodName: "GetOrderOperateLogList",
			Handler:    _VipCardOrderService_GetOrderOperateLogList_Handler,
		},
		{
			MethodName: "GetPhysicalVipCardOrderList",
			Handler:    _VipCardOrderService_GetPhysicalVipCardOrderList_Handler,
		},
		{
			MethodName: "GetPhysicalVipCardOrderDetail",
			Handler:    _VipCardOrderService_GetPhysicalVipCardOrderDetail_Handler,
		},
		{
			MethodName: "GetPhysicalVipCardOrderExport",
			Handler:    _VipCardOrderService_GetPhysicalVipCardOrderExport_Handler,
		},
		{
			MethodName: "PhysicalVipCardOrderExportList",
			Handler:    _VipCardOrderService_PhysicalVipCardOrderExportList_Handler,
		},
		{
			MethodName: "PVCExpressImportTemplate",
			Handler:    _VipCardOrderService_PVCExpressImportTemplate_Handler,
		},
		{
			MethodName: "PVCExpressEdit",
			Handler:    _VipCardOrderService_PVCExpressEdit_Handler,
		},
		{
			MethodName: "PVCEdit",
			Handler:    _VipCardOrderService_PVCEdit_Handler,
		},
		{
			MethodName: "PVCExpressImport",
			Handler:    _VipCardOrderService_PVCExpressImport_Handler,
		},
		{
			MethodName: "PVCExpressImportList",
			Handler:    _VipCardOrderService_PVCExpressImportList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/vip_card_order.proto",
}

package models

import (
	"time"
)

type OrderIntegral struct {
	Id            int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	OrderSn       string    `xorm:"default '''' comment('订单号') VARCHAR(50)"`
	OrderAmount   int       `xorm:"default 0 comment('订单金额') INT(11)"`
	OldOrderSn    string    `xorm:"default '''' comment('原父订单号') VARCHAR(50)"`
	UserName      string    `xorm:"default '''' comment('用户名') VARCHAR(50)"`
	UserTle       string    `xorm:"default '''' comment('用户电话') VARCHAR(50)"`
	EncryptMobile string    `xorm:"default '''' comment('用户电话加密') VARCHAR(50)"`
	ShopName      string    `xorm:"default '''' comment('门店名称') VARCHAR(50)"`
	ShopCity      string    `xorm:"default '''' comment('门店所在城市') VARCHAR(50)"`
	ShopCode      string    `xorm:"default '''' comment('门店编码') VARCHAR(50)"`
	Integral      int       `xorm:"default 0 comment('积分') INT(11)"`
	CurrentStatus int       `xorm:"default 0 comment('当前状态: 0未同步、1已同步') INT(11)"`
	IntegralType  int       `xorm:"default 0 comment('积分类型') INT(11)"`
	CreateTime    time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime    time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

package services

import (
	"context"
	kit "github.com/tricobbler/rp-kit"
	pb "order-center/proto/oc"
	"reflect"
	"testing"
)

func TestExpressInfo(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pb.ExpressInfoRequest
	}
	tests := []struct {
		name    string
		s       *OrderService
		args    args
		want    *pb.ExpressInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TestExpressInfo"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &OrderService{}
			app := pb.ExpressInfoRequest{}
			app.ExpressNo = "32423423432234234"
			app.OrderSn = "12211221"
			app.RefundSn = "40188872888"

			got, err := s.ExpressInfo(tt.args.ctx, &app)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("OrderCenter.ExpressInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>rf("OrderCenter.ExpressInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExpressCompanyList(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pb.ExpressCompanyListRequest
	}
	tests := []struct {
		name    string
		s       *OrderService
		args    args
		want    *pb.ExpressCompanyListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TestExpressCompanyList"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &OrderService{}
			app := pb.ExpressCompanyListRequest{}
			app.Keyword = ""
			app.Page = 1
			app.PageSize = 10

			got, err := s.ExpressCompanyList(tt.args.ctx, &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderCenter.ExpressCompanyList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderCenter.ExpressCompanyList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_ExpressInfoUpdate(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pb.ExpressInfoUpdateRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "退货物流信息更新",
			args: args{
				ctx: context.Background(),
				params: &pb.ExpressInfoUpdateRequest{
					OrderId:          "4000000000210818",
					ExpressCompanyId: 5,
					ExpressNo:        "75367683381190",
					RefundSn:         "50000000095",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &OrderService{}
			got, err := s.ExpressInfoUpdate(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("OrderCenter.ExpressInfoUpdate() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestAwenOrderPay(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pb.AwenOrderPayRequest
	}
	tests := []struct {
		name    string
		s       *OrderService
		args    args
		want    *pb.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TestAwenOrderPay"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &OrderService{}
			app := pb.AwenOrderPayRequest{}
			app.OutTradeNo = "9964128297368788"
			app.OrderId = "9964128297368788"
			app.PayPrice = 0
			app.TotalPrice = 0
			app.Discount = 0
			app.Openid = "oZpFj5BeZkId3f4G-ysAss4ENQV4"

			app.ProductId = "RP0000001"
			app.ProductName = "商品套餐-9964128297368788"
			app.ProductDesc = "商品描述"

			got, err := s.AwenOrderPay(tt.args.ctx, &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderCenter.ExpressInfoUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderCenter.ExpressInfoUpdate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAwenOrderCancel(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *pb.AwenOrderCancleRequest
	}
	tests := []struct {
		name string
		s    *OrderService
		args args
	}{
		{
			name: "TestAwenOrderCancel",
			s:    &OrderService{},
			args: args{
				params: &pb.AwenOrderCancleRequest{
					OrderId:      "4000000000084514",
					CancelReason: "优惠",
				},
				ctx: context.Background(),
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.s.AwenOrderCancle(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("OrderCenter.AwenOrderCancle() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestOrderService_ExpressInfo(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *pb.ExpressInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.ExpressInfoResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				params: &pb.ExpressInfoRequest{
					SearchType: "1",
					OrderSn:    "111111",
					ExpressNo:  "DPK366023635970",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.ExpressInfo(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpressInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ExpressInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

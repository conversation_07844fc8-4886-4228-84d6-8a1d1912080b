package models

import (
	"time"
)

type OrderFreezeStock struct {
	Id          int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn     string    `xorm:"not null default '''' comment('订单编号') index VARCHAR(50)"`
	SkuId       string    `xorm:"not null default '''' comment('商品skuid') index VARCHAR(50)"`
	Stock       int32     `xorm:"not null default 1 comment('冻结库存数量') INT(11)"`
	WarehouseId int32     `xorm:"not null default 0 comment('仓库id') INT(11)"`
	CreateTime  time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime  time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

package models

import "time"

//DigitalOrder 数字藏品订单
type DigitalOrder struct {
	Id         int64
	OrderSn    string    `json:"order_sn"`     // 订单号
	Status     int       `json:"status"`       // 订单状态：0表示待支付
	BuyUserId  string    `json:"buy_user_id"`  // 购买人唯一标识
	SellUserId string    `json:"sell_user_id"` // 售卖人唯一标识
	NftId      string    `json:"nft_id"`       // nft
	Price      float64   `json:"price"`        // 售价
	SellType   int       `json:"sell_type"`    // 销售类型：0表示免费领取,1表示支付购买
	PayTime    time.Time `json:"pay_time"`     // 支付时间
	PaySn      string    `json:"pay_sn"`       // 支付流水号
	CreatedAt  time.Time `json:"created_at"`   // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`   // 更新时间
}

package tasks

import (
	"context"
	"github.com/go-xorm/xorm"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/oc"
	"time"

	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
)

//修改错误的订单状态
//1：到家订单状态已付款48小时后 本应该自动完成 但是线上存在没有完成的单子
//2：到家订单已取消 但是子订单状态 不是20107
//需求背景：目前发现线上上述两种订单状态不对的情况 不确定是什么地方出了问题 故用任务进行释，后续还有其他不正确的情况可在此处进行增加逻辑进行处理
// 任务每天执行一次
//@version v5.6.8
func init() {
	if !kit.EnvCanCron() {
		return
	}

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 0 03 */1 * *", func() {
		TaskOrderStatusFix()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func TaskOrderStatusFix() {
	//连接池勿关闭
	glog.Info("执行修复订单状态任务")
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:task_order_status_fix"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 5*time.Hour).Val()
	if !lockRes {
		glog.Info("执行修复订单状态任务-获取锁失败")
		return
	}
	defer redisConn.Del(lockCard)

	db := services.GetDBConn()
	//处理订单状态已付款48小时后 本应该自动完成 但是线上存在没有完成的单子
	_ = fixDjOrderCompleteStatus(db)
	//处理订单已完成但是子状态不是20107的单子
	_ = fixCDjOrderChildCancelStatus(db)
}

//支付后48小时未完成的到家订单改成完成
func fixDjOrderCompleteStatus(db *xorm.Engine) error {
	glog.Info("执行修复订单状态任务-完成订单")
	session := db.NewSession()
	defer session.Close()
	//处理15天以内的记录
	startTime := time.Now().AddDate(0, 0, -15).Format(kit.DATETIME_LAYOUT)
	endTime := time.Now().AddDate(0, 0, -2).Format(kit.DATETIME_LAYOUT)
	type check struct {
		OldOrderSn       string
		ParentOrderSn    string
		OrderType        int32
		AcceptTime       time.Time
		Source           int32
		DeliverTime      time.Time
		OrderStatusChild int32
		DeliveryType     int32
	}
	var orderList []*check

	//已支付48小时的单子没有完成的子订单 主订单是跟随子订单一起完成的
	//预订单以及在线问诊订单不用处理
	err := session.Table("order_main").Alias("a").
		Join("inner", "order_detail b", "a.order_sn = b.order_sn").
		Select("a.old_order_sn, a.parent_order_sn, a.order_type,b.accept_time,a.source,a.deliver_time,a.order_status_child,a.delivery_type").
		Where("a.order_status = 20 AND a.create_time >=? AND "+
			"((a.order_type !=2 AND a.pay_time < ?) OR (a.order_type = 2 AND b.expected_time < ?)) "+
			"AND a.channel_id !=5 AND a.order_type !=13 AND parent_order_sn != ''", startTime, endTime, endTime).
		Find(&orderList)
	if err != nil {
		glog.Error("修复订单状态-完成订单-查询已支付未完成的订单出错", err)
	}

	//如果存在
	if len(orderList) > 0 {
		ctx := context.Background()
		serv := services.OrderService{}
		curTime := kit.GetTimeNow()
		for _, v := range orderList {
			// 团长拼团订单（order_type = 15），已接单15天后自动完成
			if v.DeliveryType != 1 && v.OrderType == 15 && v.AcceptTime.After(time.Now().Add(-15*24*time.Hour)) {
				continue
			}
			//物流配送接单后10天自动完成
			if v.DeliveryType == 1 && v.AcceptTime.After(time.Now().Add(-10*24*time.Hour)) {
				continue
			}
			//商家自配接单后10天自动完成
			if v.DeliveryType == 5 && v.AcceptTime.After(time.Now().Add(-10*24*time.Hour)) {
				continue
			}

			res, err := serv.AccomplishOrder(ctx, &oc.AccomplishOrderRequest{
				OrderSn:     v.OldOrderSn,
				ConfirmTime: curTime,
			})
			if err != nil {
				glog.Error(v.OldOrderSn, "修复订单状态-完成订单-完成订单出错：", err.Error())
				continue
			}
			if res.Code != 200 {
				glog.Error(v.OldOrderSn, "修复订单状态-完成订单-完成订单失败：", res.Message)
				continue
			}
			//查询退款记录中是否存在“退款中”的记录，有则更新为“退款成功”
			/*if v.ParentOrderSn != "" {
				res, err = serv.UpdateRefundState(ctx, &oc.AccomplishOrderRequest{
					OrderSn: v.ParentOrderSn,
				})
				if err != nil {
					glog.Error(v.OldOrderSn, "修复退款订单状态出错：", err.Error())
					continue
				}
				if res.Code != 200 {
					glog.Error(v.OldOrderSn, "修复退款订单状态出错失败：", res.Message)
					continue
				}
			}*/

			//被修复成功 写日志记录
			glog.Info(v.OldOrderSn, "修复订单状态-完成订单-完成订单成功：")
		}
	}
	return nil
}

//处理订单已取消 但是子状态不是20107的单子 查询订单完成时间 在2天之前的
func fixCDjOrderChildCancelStatus(db *xorm.Engine) error {
	glog.Info("执行修复订单状态任务-订单取消")
	session := db.NewSession()
	defer session.Close()

	endTime := time.Now().AddDate(0, 0, -2).Format(kit.DATETIME_LAYOUT)

	var orderSns []string
	//此处有可能查出来太多结果导致下面的in方法失败  但是实际应应该不会出现这种情况 所以 不使用limit限制条数 也不对结果进行分批执行
	startTime := time.Now().AddDate(0, 0, -3).Format(kit.DATETIME_LAYOUT)
	err := session.Table("order_main").Select("order_sn").
		Where("order_status = 0 AND channel_id !=5 AND order_status_child != 20107 AND create_time >= ? AND cancel_time < ? ", startTime, endTime).
		Find(&orderSns)
	if len(orderSns) == 0 {
		return nil
	}
	//到家的订单 已取消 子状态不为20107
	update := new(models.OrderMain)
	update.OrderStatusChild = 20107

	affected, err := session.Cols("order_status_child").In("order_sn", orderSns).Update(update)
	if err != nil {
		glog.Error(orderSns, "修复订单状态-订单取消-查询已支付未完成的订单出错", err)
	}
	if affected > 0 {
		//记录被修复的数据 记录
		glog.Info(orderSns, "修复订单状态-订单取消-修复完成，修复数据条数：", affected)
	}
	return nil
}

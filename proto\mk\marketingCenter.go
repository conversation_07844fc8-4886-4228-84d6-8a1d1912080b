package mk

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	Conn           *grpc.ClientConn
	Ctx            context.Context
	Cf             context.CancelFunc
	RPC            PromotionServiceClient      //通用的对外接口，app 第三方
	ReachReduce    ReachReduceServiceClient    //满减优惠活动操作接口
	TimeDiscount   TimeDiscountServiceClient   // 限时优惠活动操作接口
	ReduceDelivery ReduceDeliveryServiceClient //满减运费活动操作接口
	Task           PromotionTaskServiceClient  // 活动创建任务接口
	Market         MarketServiceClient         //运营中心活动操作接口
	Insurance      InsuranceServiceClient      // 赠险
}

// 获取grpc链接
func (client Client) getGrpcConn() *grpc.ClientConn {
	url := config.GetString("grpc.marketing-center")
	//url = "10.1.1.248:7042"
	if url == "" {
		url = "127.0.0.1:7042"
	}
	conn, err := grpc.Dial(url, grpc.WithInsecure())
	if err != nil {
		glog.Error(err)
		return nil
	}
	return conn
}

// 获取阿闻到家
func GetMarketingCenterClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.RPC = NewPromotionServiceClient(conn)
		client.Ctx = context.Background()
		client.Insurance = NewInsuranceServiceClient(conn)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*300)
		return client
	}
	return nil
}

// 满减优惠
func GetReachReduceServiceClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.ReachReduce = NewReachReduceServiceClient(conn)
		client.RPC = NewPromotionServiceClient(conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*300)
		return client
	}
	return nil
}

// 满减运费
func GetReduceDeliveryServiceClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.ReduceDelivery = NewReduceDeliveryServiceClient(conn)
		client.RPC = NewPromotionServiceClient(conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*300)
		return client
	}
	return nil
}

// 限时折扣
func GetTimeDiscountServiceClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.TimeDiscount = NewTimeDiscountServiceClient(conn)
		client.RPC = NewPromotionServiceClient(conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return client
	}
	return nil
}

// 活动创建任务
func GetTaskServiceClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.Task = NewPromotionTaskServiceClient(conn)
		client.RPC = NewPromotionServiceClient(conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return client
	}
	return nil
}

// 获取运营中心
func GetMarketCenterClient(c ...echo.Context) *Client {
	var client = new(Client)
	conn := client.getGrpcConn()
	if conn != nil {
		client.Conn = conn
		client.Market = NewMarketServiceClient(conn)
		client.RPC = NewPromotionServiceClient(conn)
		client.Insurance = NewInsuranceServiceClient(conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*300)
		return client
	}
	return nil
}

// 关闭链接
func (c *Client) Close() {
	c.Conn.Close()
	c.Cf()
}

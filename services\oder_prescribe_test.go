package services

import (
	"context"
	"fmt"
	"order-center/proto/oc"
	"testing"

	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
)

func TestOrderService_Prescribe(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.OrderPrescribeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.OrderPrescribeRes
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "TestOrderService_Prescribe",
			args: args{
				ctx: context.Background(),
				req: &oc.OrderPrescribeReq{
					FinanceCode:  "CX001",
					HospitalName: "TEST",
					PetWeight:    "5.0",
					PetInfo: &oc.ConsultMemberPetInfo{
						PetId:      "8acd3b66e3144f1c9b6ed308be6c2d28",
						PetKindof:  "1000",
						PetVariety: "1003",
					},
					Diagnose: []*oc.PrescriptionDiagnose{
						{
							DiseaseCode:     "1001",
							DiagnoseContent: "test",
							IsSure:          1,
							Position:        0,
						},
					},
					Skus: []*oc.OrderPrescribeSkuNum{
						{
							SkuId: 1012198001,
							Num:   1,
						},
						{
							SkuId: 1018879001,
							Num:   2,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.Prescribe(tt.args.ctx, tt.args.req)
			fmt.Println(kit.JsonEncode(got))
			if !tt.wantErr(t, err, fmt.Sprintf("Prescribe(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "Prescribe(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

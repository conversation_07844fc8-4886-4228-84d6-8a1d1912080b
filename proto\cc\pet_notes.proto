syntax = "proto3";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
package cc;

//阿闻小程序——宠物便签相关服务
service PetNotesService {
    rpc GetNotesByUserId (GetNotesByUserIdRequest) returns (GetNotesByUserIdResponse);
    //宠物便签——分页获取宠物便签列表
    rpc GetPetNotesList (GetPetNotesListRequest) returns (GetPetNotesListResponse);
    //宠物便签——获取宠物便签详情
    rpc GetPetNotesDetail (GetPetNotesDetailRequest) returns (GetPetNotesDetailResponse);
    //宠物便签——创建
    rpc CreatePetNotes (CreatePetNotesRequest) returns (CreatePetNotesResponse);
    //宠物便签——更新
    rpc UpdatePetNotes (UpdatePetNotesRequest) returns (UpdatePetNotesResponse);
    //宠物便签——删除（软删除）
    rpc DeletePetNotes (DeletePetNotesRequest) returns (DeletePetNotesResponse);
}

message GetNotesByUserIdRequest {
    string user_id = 1;
}

message GetNotesByUserIdResponse {
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    repeated PetNotes list = 4;
}

message PetNotes {
    //id
    int32 id = 1;
    //用户id
    string user_id = 2;
    //宠物id
    string pet_id = 3;
    string title = 4;
    //便签内容
    string content = 5;
    //是否设置提醒 1 未设置 2 设置
    int32 is_remind = 6;
    //是否被删除 1 未删除 2 已删除
    int32 is_deleted = 7;
    //提醒时间
    string remind_time = 8;
    //创建时间
    string create_time = 9;
    //创建人id
    string create_id = 10;
    //更新时间
    string update_time = 11;
    //更新人id
    string update_id = 12;
}

//宠物便签——分页获取宠物便签列表——Request
message GetPetNotesListRequest {
    //当前页
    int32 page_index = 1;
    //每页显示数据条数
    int32 page_size = 2;
    //用户id
    string user_id = 3;
    //宠物id
    string pet_id = 4;
    //便签内容
    string content = 5;
    //是否设置提醒 1 未设置 2 设置
    int32 is_remind = 6;
    //主键id
    int32 id = 7;
}

//宠物便签——分页获取宠物便签列表——Response
message GetPetNotesListResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    int32 total = 4;
    repeated PetNotesModel details = 5;
}

//宠物便签——分页获取宠物便签列表——Details Models
message PetNotesModel {
    //主键id
    int32 id = 1;
    //用户id
    string user_id = 2;
    //宠物id
    string pet_id = 3;
    //便签内容
    string title = 4;
    //便签内容
    string content = 5;
    //是否设置提醒 1 未设置 2 设置
    int32 is_remind = 6;
    //提醒时间
    string remind_time = 7;
    //创建人id
    string create_id = 8;
    //创建时间
    string create_time = 9;
    //更新人id
    string update_id = 10;
    //更新时间
    string update_time = 11;
}

//宠物便签——获取宠物便签详情——Request
message GetPetNotesDetailRequest {
    //主键id
    string ids = 1;
    //用户id
    string user_ids = 2;
    //宠物id
    string pet_ids = 3;
}

//宠物便签——获取宠物便签详情——Response
message GetPetNotesDetailResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated PetNotesModel details = 5;
}

//宠物便签——创建——Request
message CreatePetNotesRequest {
    //用户id
    string user_id = 1;
    //宠物id
    string pet_id = 2;
    //标题
    string tittle = 3;
    //便签内容
    string content = 4;
    //是否设置提醒 1 未设置 2 设置
    int32 is_remind = 5;
    //提醒时间
    string remind_time = 6;
    //创建人id
    string create_id = 7;
}

//宠物便签——创建——Response
message CreatePetNotesResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
}

//宠物便签——更新——Request
message UpdatePetNotesRequest {
    //主键id
    int32 id = 1;
    //用户id
    string user_id = 2;
    //宠物id
    string pet_id = 3;
    //标题
    string tittle = 4;
    //便签内容
    string content = 5;
    //是否设置提醒 0 设置 1 未设置
    int32 is_remind = 6;
    //提醒时间
    string remind_time = 7;
    //更新人id
    string update_id = 8;
}

//宠物便签——更新——Response
message UpdatePetNotesResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
}

//宠物便签——删除（软删除）——Request
message DeletePetNotesRequest {
    //主键id
    int32 id = 1;
    //更新人id
    string update_id = 2;
}

//宠物便签——删除（软删除）——Response
message DeletePetNotesResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
}
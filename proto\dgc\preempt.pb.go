// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/preempt.proto

// 医生可抢订单相关

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PreemptListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreemptListRequest) Reset()         { *m = PreemptListRequest{} }
func (m *PreemptListRequest) String() string { return proto.CompactTextString(m) }
func (*PreemptListRequest) ProtoMessage()    {}
func (*PreemptListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b20b6294526e5378, []int{0}
}

func (m *PreemptListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreemptListRequest.Unmarshal(m, b)
}
func (m *PreemptListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreemptListRequest.Marshal(b, m, deterministic)
}
func (m *PreemptListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreemptListRequest.Merge(m, src)
}
func (m *PreemptListRequest) XXX_Size() int {
	return xxx_messageInfo_PreemptListRequest.Size(m)
}
func (m *PreemptListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PreemptListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PreemptListRequest proto.InternalMessageInfo

//医生端-接诊操作
type PreemptOperateRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreemptOperateRequest) Reset()         { *m = PreemptOperateRequest{} }
func (m *PreemptOperateRequest) String() string { return proto.CompactTextString(m) }
func (*PreemptOperateRequest) ProtoMessage()    {}
func (*PreemptOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b20b6294526e5378, []int{1}
}

func (m *PreemptOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreemptOperateRequest.Unmarshal(m, b)
}
func (m *PreemptOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreemptOperateRequest.Marshal(b, m, deterministic)
}
func (m *PreemptOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreemptOperateRequest.Merge(m, src)
}
func (m *PreemptOperateRequest) XXX_Size() int {
	return xxx_messageInfo_PreemptOperateRequest.Size(m)
}
func (m *PreemptOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PreemptOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PreemptOperateRequest proto.InternalMessageInfo

func (m *PreemptOperateRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//医生端-接诊操作
type PreemptOperateResponse struct {
	AffectRows           int64    `protobuf:"varint,1,opt,name=affect_rows,json=affectRows,proto3" json:"affect_rows"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreemptOperateResponse) Reset()         { *m = PreemptOperateResponse{} }
func (m *PreemptOperateResponse) String() string { return proto.CompactTextString(m) }
func (*PreemptOperateResponse) ProtoMessage()    {}
func (*PreemptOperateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b20b6294526e5378, []int{2}
}

func (m *PreemptOperateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreemptOperateResponse.Unmarshal(m, b)
}
func (m *PreemptOperateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreemptOperateResponse.Marshal(b, m, deterministic)
}
func (m *PreemptOperateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreemptOperateResponse.Merge(m, src)
}
func (m *PreemptOperateResponse) XXX_Size() int {
	return xxx_messageInfo_PreemptOperateResponse.Size(m)
}
func (m *PreemptOperateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PreemptOperateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PreemptOperateResponse proto.InternalMessageInfo

func (m *PreemptOperateResponse) GetAffectRows() int64 {
	if m != nil {
		return m.AffectRows
	}
	return 0
}

func (m *PreemptOperateResponse) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

//抢单列表
type PreemptListResponse struct {
	//未被抢
	Normal []*Order `protobuf:"bytes,1,rep,name=normal,proto3" json:"normal"`
	//已被抢
	Preempted            []*Order `protobuf:"bytes,2,rep,name=preempted,proto3" json:"preempted"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreemptListResponse) Reset()         { *m = PreemptListResponse{} }
func (m *PreemptListResponse) String() string { return proto.CompactTextString(m) }
func (*PreemptListResponse) ProtoMessage()    {}
func (*PreemptListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b20b6294526e5378, []int{3}
}

func (m *PreemptListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreemptListResponse.Unmarshal(m, b)
}
func (m *PreemptListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreemptListResponse.Marshal(b, m, deterministic)
}
func (m *PreemptListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreemptListResponse.Merge(m, src)
}
func (m *PreemptListResponse) XXX_Size() int {
	return xxx_messageInfo_PreemptListResponse.Size(m)
}
func (m *PreemptListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PreemptListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PreemptListResponse proto.InternalMessageInfo

func (m *PreemptListResponse) GetNormal() []*Order {
	if m != nil {
		return m.Normal
	}
	return nil
}

func (m *PreemptListResponse) GetPreempted() []*Order {
	if m != nil {
		return m.Preempted
	}
	return nil
}

func init() {
	proto.RegisterType((*PreemptListRequest)(nil), "dgc.PreemptListRequest")
	proto.RegisterType((*PreemptOperateRequest)(nil), "dgc.PreemptOperateRequest")
	proto.RegisterType((*PreemptOperateResponse)(nil), "dgc.PreemptOperateResponse")
	proto.RegisterType((*PreemptListResponse)(nil), "dgc.PreemptListResponse")
}

func init() { proto.RegisterFile("dgc/preempt.proto", fileDescriptor_b20b6294526e5378) }

var fileDescriptor_b20b6294526e5378 = []byte{
	// 275 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x51, 0xcb, 0x4e, 0xb4, 0x30,
	0x14, 0xfe, 0xf9, 0x49, 0x46, 0xe7, 0x60, 0x34, 0xd6, 0x1b, 0xe2, 0x42, 0xd2, 0x15, 0x2b, 0x4c,
	0xf0, 0x0d, 0xdc, 0x19, 0x4d, 0xc6, 0x30, 0x71, 0xe5, 0x82, 0x60, 0x7b, 0x86, 0x90, 0x08, 0xad,
	0xa7, 0x1d, 0xe7, 0x79, 0x7c, 0x53, 0x43, 0xa9, 0x3a, 0x17, 0x97, 0xfd, 0x6e, 0xe7, 0xcb, 0x57,
	0x38, 0x96, 0x8d, 0xb8, 0xd1, 0x84, 0xd8, 0x69, 0x9b, 0x6b, 0x52, 0x56, 0xb1, 0x50, 0x36, 0x22,
	0x39, 0x1a, 0x70, 0x45, 0x12, 0x69, 0x44, 0xf9, 0x29, 0xb0, 0xa7, 0x51, 0xf6, 0xd8, 0x1a, 0x5b,
	0xe2, 0xfb, 0x12, 0x8d, 0xe5, 0x05, 0x9c, 0x79, 0x74, 0xa6, 0x91, 0x6a, 0x8b, 0x9e, 0x60, 0x97,
	0xb0, 0xef, 0xdc, 0x95, 0xe9, 0xe3, 0x20, 0x0d, 0xb2, 0x69, 0xb9, 0xe7, 0xde, 0xf3, 0x9e, 0xbf,
	0xc0, 0xf9, 0xb6, 0xc7, 0x68, 0xd5, 0x1b, 0x64, 0xd7, 0x10, 0xd5, 0x8b, 0x05, 0x0a, 0x5b, 0x91,
	0x5a, 0x19, 0xe7, 0x0b, 0x4b, 0x18, 0xa1, 0x52, 0xad, 0x0c, 0x4b, 0xe1, 0xc0, 0x08, 0xea, 0xaa,
	0xa5, 0x41, 0xaa, 0x5a, 0x19, 0xff, 0x77, 0xc9, 0x30, 0x60, 0xcf, 0x06, 0xe9, 0x5e, 0x72, 0x01,
	0x27, 0x1b, 0x35, 0x7d, 0x32, 0x87, 0x49, 0xaf, 0xa8, 0xab, 0xdf, 0xe2, 0x20, 0x0d, 0xb3, 0xa8,
	0x80, 0x5c, 0x36, 0x22, 0x9f, 0x0d, 0x8d, 0x4a, 0xcf, 0xb0, 0x0c, 0xa6, 0x7e, 0x08, 0x1c, 0x92,
	0xb7, 0x65, 0xbf, 0x64, 0xf1, 0x19, 0xc0, 0xa1, 0xbf, 0x32, 0x47, 0xfa, 0x68, 0x05, 0xb2, 0x3b,
	0x88, 0xd6, 0xee, 0xb2, 0x0b, 0x67, 0xdc, 0x1d, 0x2c, 0x89, 0x77, 0x89, 0xb1, 0x22, 0xff, 0xc7,
	0x1e, 0x7e, 0x52, 0xfd, 0x30, 0x2c, 0x59, 0x57, 0x6f, 0x2e, 0x9c, 0x5c, 0xfd, 0xc9, 0x7d, 0x87,
	0xbd, 0x4e, 0xdc, 0xb7, 0xdd, 0x7e, 0x05, 0x00, 0x00, 0xff, 0xff, 0xfa, 0xde, 0xfb, 0x4c, 0xe1,
	0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PreemptServiceClient is the client API for PreemptService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PreemptServiceClient interface {
	// @Desc    	医生端-抢单列表
	// <AUTHOR>
	// @Date		2021-10-09
	PreemptList(ctx context.Context, in *PreemptListRequest, opts ...grpc.CallOption) (*PreemptListResponse, error)
	// @Desc    	医生端-清单操作
	// <AUTHOR>
	// @Date		2021-10-12
	PreemptOperate(ctx context.Context, in *PreemptOperateRequest, opts ...grpc.CallOption) (*PreemptOperateResponse, error)
}

type preemptServiceClient struct {
	cc *grpc.ClientConn
}

func NewPreemptServiceClient(cc *grpc.ClientConn) PreemptServiceClient {
	return &preemptServiceClient{cc}
}

func (c *preemptServiceClient) PreemptList(ctx context.Context, in *PreemptListRequest, opts ...grpc.CallOption) (*PreemptListResponse, error) {
	out := new(PreemptListResponse)
	err := c.cc.Invoke(ctx, "/dgc.PreemptService/PreemptList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preemptServiceClient) PreemptOperate(ctx context.Context, in *PreemptOperateRequest, opts ...grpc.CallOption) (*PreemptOperateResponse, error) {
	out := new(PreemptOperateResponse)
	err := c.cc.Invoke(ctx, "/dgc.PreemptService/PreemptOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PreemptServiceServer is the server API for PreemptService service.
type PreemptServiceServer interface {
	// @Desc    	医生端-抢单列表
	// <AUTHOR>
	// @Date		2021-10-09
	PreemptList(context.Context, *PreemptListRequest) (*PreemptListResponse, error)
	// @Desc    	医生端-清单操作
	// <AUTHOR>
	// @Date		2021-10-12
	PreemptOperate(context.Context, *PreemptOperateRequest) (*PreemptOperateResponse, error)
}

// UnimplementedPreemptServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPreemptServiceServer struct {
}

func (*UnimplementedPreemptServiceServer) PreemptList(ctx context.Context, req *PreemptListRequest) (*PreemptListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreemptList not implemented")
}
func (*UnimplementedPreemptServiceServer) PreemptOperate(ctx context.Context, req *PreemptOperateRequest) (*PreemptOperateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreemptOperate not implemented")
}

func RegisterPreemptServiceServer(s *grpc.Server, srv PreemptServiceServer) {
	s.RegisterService(&_PreemptService_serviceDesc, srv)
}

func _PreemptService_PreemptList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreemptListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreemptServiceServer).PreemptList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.PreemptService/PreemptList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreemptServiceServer).PreemptList(ctx, req.(*PreemptListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreemptService_PreemptOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreemptOperateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreemptServiceServer).PreemptOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.PreemptService/PreemptOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreemptServiceServer).PreemptOperate(ctx, req.(*PreemptOperateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PreemptService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.PreemptService",
	HandlerType: (*PreemptServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreemptList",
			Handler:    _PreemptService_PreemptList_Handler,
		},
		{
			MethodName: "PreemptOperate",
			Handler:    _PreemptService_PreemptOperate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/preempt.proto",
}

package services

import (
	"encoding/json"
	"errors"
	"order-center/dto"
	"order-center/utils"
	"os"
	"strings"

	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

type channelMtOrder struct {
	order      *models.Order
	oldOrderSn string
	etClient   *et.Client
}

func (c channelMtOrder) PickOrder() (err error) {
	if c.order.LogisticsCode == "1001" || c.order.LogisticsCode == "2002" || c.order.LogisticsCode == "3001" {
		var res *et.ExternalResponse
		res, err = c.etClient.MtOrder.OrderPreparationMealComplete(c.etClient.Ctx, &et.PreparationMealComplete{
			OrderId:       cast.ToInt64(c.oldOrderSn),
			StoreMasterId: c.order.AppChannel,
		})
		if err != nil {
			glog.Error("外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, "，手动拣货通知美团错误！", err)
		}
		if res.Code != 200 {
			glog.Info("外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, "，手动拣货通知美团错误！", res.Error)
			err = errors.New(res.Error + "-" + res.Message)
		}
	}

	return err
}

// 配送状态同步给美团
func (c channelMtOrder) SyncDeliveryNode(params *oc.DeliveryNodeRequest) (err error) {
	//未接单之前直接返回
	if params.Status <= 0 {
		glog.Error(c.order.OrderSn, ",SyncDeliveryNode-配送状态异常：", params.Status)
		return
	}
	//2:如果订单已取消 则不在同步
	if c.order.OrderStatus == 0 {
		return
	}
	realOrderSn := c.order.OrderSn

	//父订单
	if c.order.ParentOrderSn == "" {
		rearOrder, err := GetRealOrderByParentOrderSn(c.order.OrderSn, "order_sn")
		if err != nil {
			//错误并不返回，只是同步给美团会缺少物流单号
			glog.Error(c.oldOrderSn, c.order.OrderSn, ", 自配送商家同步发货状态和配送信息查询子订单号出错", err.Error())
		}
		if rearOrder == nil {
			realOrderSn = ""
		} else {
			realOrderSn = rearOrder.OrderSn
		}
	}

	//查询物流信息
	delivery, err := GetDeliveryByOrderSn(realOrderSn)
	if err != nil {
		//错误并不返回，只是同步给美团会缺少物流单号
		glog.Error(c.order.OrderSn, ", 自配送商家同步发货状态和配送信息查询物流信息出错", err.Error())
	}

	//推送参数
	LogisticsSync := &et.MtOrderLogisticsSyncRequest{
		OrderId:       c.oldOrderSn,
		CourierName:   params.CourierName,
		CourierPhone:  params.CourierPhone,
		StoreMasterId: c.order.AppChannel,
		Latitude:      params.Latitude,
		Longitude:     params.Longitude,
	}
	//保存推送记录
	syncRecordData := models.OrderLogisticsSync{
		OrderSn:         realOrderSn,
		OldOrderSn:      c.order.OldOrderSn,
		ChannelId:       c.order.ChannelId,
		LogisticsStatus: params.Status,
		Status:          1,
		DataFull:        1,
	}
	//如果没有配送单且属于配送异常中的操作
	if (delivery == nil || delivery.Id == 0) && params.IsException == 1 {
		//自己创建一个死的第三方物流号 避免美团人为没有物流单号而影响回传率
		LogisticsSync.ThirdCarrierOrderId = "1111111111111"
		LogisticsSync.LogisticsProviderCode = "10017"
	}
	if delivery != nil && delivery.Id > 0 {
		syncRecordData.DeliveryId = delivery.Id
		LogisticsSync.ThirdCarrierOrderId = delivery.MtPeisongId
		//如果是取消配送
		//1:则查询最近的配送状态，如果配送订单没有接单 则不再同步
		if params.Status == 99 {
			has, err := CheckDeliveryOrderIsAcceptedById(delivery.DeliveryId)
			if err != nil {
				glog.Error(c.order.OrderSn, "取消配送，查询是否接单出错", err)
				return err
			}
			//未接过单 直接返回
			if !has {
				return nil
			}
		}
		//异常单发的配送 配送的服务商置为0
		if params.IsException == 1 {
			delivery.DeliveryServiceCode = 0
		}

		switch delivery.DeliveryType {
		case 0: //美配
			LogisticsSync.LogisticsProviderCode = "10017"
			//以下状态需要获取骑手的位置 美配的回调没有骑手位置信息 需要主动获取美团的骑手信息
			if params.Status == 20 || params.Status == 30 || params.Status == 50 || params.Status == 15 {
				etClient := et.GetExternalClient()
				locationRequest := &et.MpOrderRiderLocationRequest{
					MtPeisongId: delivery.MtPeisongId,
					DeliveryId:  delivery.DeliveryId,
					OrderSn:     realOrderSn,
				}
				glog.Error(c.order.OrderSn, ", 获取配送员位置参数", kit.JsonEncode(locationRequest))
				locationRes, err := etClient.MPServer.MpOrderRiderLocation(etClient.Ctx, locationRequest)
				glog.Error(c.order.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
				if err != nil {
					glog.Error(c.order.OrderSn, ", 获取配送员位置出错", err.Error())
					break
				}
				if locationRes == nil {
					glog.Error(c.order.OrderSn, ", 获取配送员位置失败")
					break
				}
				//位置信息
				if locationRes.Data != "" {
					courierLocation := new(dto.CourierLocation)
					_ = json.Unmarshal([]byte(locationRes.Data), courierLocation)
					LogisticsSync.Latitude = cast.ToString(float64(courierLocation.Lat) / 1000000)
					LogisticsSync.Longitude = cast.ToString(float64(courierLocation.Lng) / 1000000)
				}
			}
		case 1: //闪送
			LogisticsSync.LogisticsProviderCode = "10003"
			//坐标系的转换，将闪送的百度坐标系转换 美团的火星坐标系
			if params.Latitude != "" && params.Longitude != "" {
				hxLng, hxLat := utils.BD09ToGCJ02(cast.ToFloat64(params.Latitude), cast.ToFloat64(params.Longitude))
				LogisticsSync.Latitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLat*1000000)) / 1000000)
				LogisticsSync.Longitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLng*1000000)) / 1000000)
			}
		case 3: //达达
			LogisticsSync.LogisticsProviderCode = "10002"
			LogisticsSync.ThirdCarrierOrderId = params.MtPeisongId
			env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
			if env == "sit1" || env == "uat" || env == "dev" {
				//西藏自治区林芝市墨脱县金珠路
				LogisticsSync.Latitude = "29.33267"
				LogisticsSync.Longitude = "95.33735"
			} else {
				//达达的骑手经纬度需要自己查询
				etClient := et.GetExternalClient()
				GetOrderPar := et.DaDaformalCancelRequst{}
				GetOrderPar.OrderId = params.OrderSn
				locationRes, err := etClient.DaDa.Query(etClient.Ctx, &GetOrderPar)
				glog.Error(c.order.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
				if err != nil {
					glog.Error(c.order.OrderSn, ", 获取配送员位置出错", err.Error())
					break
				}
				if locationRes.Code != 0 {
					glog.Error(c.order.OrderSn, ", 获取配送员位置失败,", locationRes.Error)
					break
				}
				//位置信息
				if locationRes.Data.TransporterLat != "" {
					LogisticsSync.Latitude = locationRes.Data.TransporterLat
					LogisticsSync.Longitude = locationRes.Data.TransporterLng
				}
			}
			//坐标系的转换，将闪送的百度坐标系转换 美团的火星坐标系
			//if params.Latitude != "" && params.Longitude != "" {
			//	hxLng, hxLat := utils.BD09ToGCJ02(cast.ToFloat64(params.Latitude), cast.ToFloat64(params.Longitude))
			//	LogisticsSync.Latitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLat*1000000)) / 1000000)
			//	LogisticsSync.Longitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLng*1000000)) / 1000000)
			//}
		case 4: //蜂鸟
			//达达的骑手经纬度需要自己查询
			LogisticsSync.LogisticsProviderCode = "10004"
			LogisticsSync.ThirdCarrierOrderId = params.MtPeisongId

			etClient := et.GetExternalClient()
			GetOrderPar := et.FnCancelOrderRequst{}
			GetOrderPar.PartnerOrderCode = params.OrderSn
			locationRes, err := etClient.Fn.GetKnightInfo(etClient.Ctx, &GetOrderPar)
			glog.Error(c.order.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
			if err != nil {
				glog.Error(c.order.OrderSn, ", 获取配送员位置出错", err.Error())
				break
			}
			if locationRes.Code != "200" {
				glog.Error(c.order.OrderSn, ", 获取配送员位置失败,", locationRes.Msg)
				break
			}
			CarrierDriverModel := dto.CarrierDriverModel{}
			err = json.Unmarshal([]byte(locationRes.BusinessData), &CarrierDriverModel)
			if err != nil {
				glog.Error(c.order.OrderSn, ", 解析蜂鸟配送员位置出错", err.Error())
			}
			LogisticsSync.Latitude = CarrierDriverModel.CarrierDriverLatitude
			LogisticsSync.Longitude = CarrierDriverModel.CarrierDriverLongitude
			break
		default: //其他
			LogisticsSync.LogisticsProviderCode = "10017"
		}

		//switch delivery.DeliveryServiceCode {
		//case 100029: //美配
		//	LogisticsSync.LogisticsProviderCode = "10017"
		//	//以下状态需要获取骑手的位置 美配的回调没有骑手位置信息 需要主动获取美团的骑手信息
		//	if params.Status == 20 || params.Status == 30 || params.Status == 50 || params.Status == 15 {
		//		etClient := et.GetExternalClient()
		//		locationRequest := &et.MpOrderRiderLocationRequest{
		//			MtPeisongId: delivery.MtPeisongId,
		//			DeliveryId:  delivery.DeliveryId,
		//		}
		//		glog.Error(c.order.OrderSn, ", 获取配送员位置参数", kit.JsonEncode(locationRequest))
		//		locationRes, err := etClient.MPServer.MpOrderRiderLocation(etClient.Ctx, locationRequest)
		//		glog.Error(c.order.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
		//		if err != nil {
		//			glog.Error(c.order.OrderSn, ", 获取配送员位置出错", err.Error())
		//			break
		//		}
		//		if locationRes == nil {
		//			glog.Error(c.order.OrderSn, ", 获取配送员位置失败")
		//			break
		//		}
		//		//位置信息
		//		if locationRes.Data != "" {
		//			courierLocation := new(dto.CourierLocation)
		//			_ = json.Unmarshal([]byte(locationRes.Data), courierLocation)
		//			LogisticsSync.Latitude = cast.ToString(float64(courierLocation.Lat) / 1000000)
		//			LogisticsSync.Longitude = cast.ToString(float64(courierLocation.Lng) / 1000000)
		//		}
		//	}
		//case 5001: //闪送
		//	LogisticsSync.LogisticsProviderCode = "10003"
		//	//坐标系的转换，将闪送的百度坐标系转换 美团的火星坐标系
		//	if params.Latitude != "" && params.Longitude != "" {
		//		hxLng, hxLat := utils.BD09ToGCJ02(cast.ToFloat64(params.Latitude), cast.ToFloat64(params.Longitude))
		//		LogisticsSync.Latitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLat*1000000)) / 1000000)
		//		LogisticsSync.Longitude = cast.ToString(cast.ToFloat64(cast.ToInt64(hxLng*1000000)) / 1000000)
		//	}
		//default: //其他
		//	LogisticsSync.LogisticsProviderCode = "10017"
		//}
	}

	//如果是测试或者UAT环境，随便默认一个经纬度,因为达达的测试门店数据骑手经纬度无法获取到

	switch params.Status {
	case 20:
		LogisticsSync.LogisticsStatus = 10
	case 15: //美配回调没有15这个状态
		LogisticsSync.LogisticsStatus = 15
	case 30:
		LogisticsSync.LogisticsStatus = 20
	case 50:
		LogisticsSync.LogisticsStatus = 40
	case 99:
		LogisticsSync.LogisticsStatus = 100
	}
	syncJsonData := kit.JsonEncode(LogisticsSync)
	syncRecordData.Data = syncJsonData
	//同步必须项，以下字段都需要同步给美团
	//1）订单ID
	//2）物流单号
	//3）自配送状态 code
	//4）配送员名字
	//5）配送员电话
	//6）第三方配送方式
	//7）骑手当前的经纬
	if LogisticsSync.CourierName == "" || LogisticsSync.CourierPhone == "" || LogisticsSync.LogisticsProviderCode == "" ||
		LogisticsSync.ThirdCarrierOrderId == "" || LogisticsSync.OrderId == "" || LogisticsSync.Latitude == "" ||
		LogisticsSync.Longitude == "" || LogisticsSync.LogisticsStatus == 0 {
		syncRecordData.DataFull = 0 //该字段仅用于数据检测 看看有多少数据不齐全的情况
	}

	//如果同步失败
	var res *et.ExternalResponse
	glog.Info(c.order.OrderSn, ",SyncDeliveryNode-更新配送信息至美团：", syncJsonData)
	res, err = c.etClient.MtOrder.MtOrderLogisticsSync(c.etClient.Ctx, LogisticsSync)
	glog.Info(c.order.OrderSn, ",SyncDeliveryNode-更新配送信息至美团返回结果：", syncJsonData, res, err)

	if err != nil {
		glog.Error(c.order.OrderSn, ",自配送商家同步发货状态和配送信息错误, 自配订单配送中错误, ", err.Error())
		syncRecordData.Status = 2
		syncRecordData.ErrInfo = "同步出错" + err.Error()
	} else if res.Code != 200 {
		glog.Error(c.order.OrderSn, ", 自配送商家同步发货状态和配送信息失败", kit.JsonEncode(res))
		err = errors.New(res.Error + "-" + res.Message)
		syncRecordData.Status = 2
		syncRecordData.ErrInfo = "同步失败400:" + res.Message
	}
	//插入
	_, err = GetDBConn().Insert(syncRecordData)
	if err != nil {
		glog.Error(c.oldOrderSn, ",写入同步自配送状态记录出错", err.Error())
	}
	return
}

func (c channelMtOrder) CancelOrder(params *oc.CancelAcceptOrderRequest) (err error) {
	var res *et.ExternalResponse
	res, err = c.etClient.MtOrder.MtOrderCancel(c.etClient.Ctx, &et.MtOrderCancelRequest{
		OrderId:       c.oldOrderSn,
		Reason:        params.Reason,
		StoreMasterId: c.order.AppChannel,
	})
	if err != nil {
		glog.Error(c.oldOrderSn, ", 调用美团取消订单接口失败, ", err)
	}
	if res.Code != 200 {
		err = errors.New(res.Error + "-" + res.Message)
		glog.Error(c.oldOrderSn, ", 调用美团取消订单接口失败, ", kit.JsonEncode(res))
	}

	return
}

func (c channelMtOrder) ApplyPartRefund(params *oc.OrderApplyPartRefundRequest) (err error) {
	var FoodDatas []*et.ApplyPartRefundList
	for _, i2 := range params.FoodData {
		//美团的退款商品
		FoodData := et.ApplyPartRefundList{
			AppFoodCode: i2.AppFoodCode,
			SkuId:       i2.SkuId,
			Count:       i2.Count,
		}
		FoodDatas = append(FoodDatas, &FoodData)
	}

	OrderApplyPartRefund := &et.OrderApplyPartRefundRequset{
		OrderId:       cast.ToInt64(params.OrderId),
		Reason:        params.Reason,
		FoodData:      FoodDatas,
		StoreMasterId: c.order.AppChannel, //params.StoreMasterId,
	}
	glog.Info("调用美团发起部分退款请求：", kit.JsonEncode(OrderApplyPartRefund))
	var res *et.ExternalResponse
	res, err = c.etClient.MtReturn.OrderApplyPartRefund(c.etClient.Ctx, OrderApplyPartRefund)
	if err != nil {
		glog.Error(c.oldOrderSn, ", 调用美团发起部分退款请求失败, ", err)
	} else if res.Code != 200 {
		res.Message = strings.Replace(res.Message, "菜品", "商品", -1)
		err = errors.New(res.Error + "-" + res.Message)
		glog.Error(c.oldOrderSn, ", 调用美团发起部分退款请求失败, ", kit.JsonEncode(res))
	} else {
		glog.Info(c.oldOrderSn, ", 调用美团发起部分退款成功,", kit.JsonEncode(res))
	}

	return
}

func (c channelMtOrder) AgreeRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	var res *et.ExternalResponse
	if refundOrder.ServiceType != "" {
		inpar := et.MtReviewAfterSalesRequest{}
		inpar.WmOrderIdView = cast.ToInt64(params.OrderId)
		//退货退款初审通过，则为0-同意退货
		if refundOrder.ServiceType == "2" && refundOrder.RefundState == 1 {
			inpar.ReviewType = 0
		} else {
			inpar.ReviewType = 1

		}
		inpar.RejectReasonCode = params.RejectReasonCode
		inpar.RejectOtherReason = params.Reason
		res, err = c.etClient.MtOrder.MtReviewAfterSales(c.etClient.Ctx, &inpar)
		if err != nil {
			glog.Error(c.oldOrderSn, ", 调用美团同意退货退款，接口异常, ", err)
		} else if res.Code != 200 {
			err = errors.New(res.Error + "-" + res.Message)
			glog.Error(c.oldOrderSn, ", 调用美团同意退货退款，接口失败, ", kit.JsonEncode(res))
		} else {
			glog.Error(c.oldOrderSn, ", 调用美团同意退货退款，接口成功, ", kit.JsonEncode(res))
		}
	} else {
		res, err = c.etClient.MtOrder.MtOrderRefundAgree(c.etClient.Ctx, &et.MtOrderRefundRequest{
			Reason:        params.Reason,
			OrderId:       params.OrderId,
			StoreMasterId: refundOrder.AppChannel,
		})
		if err != nil {
			glog.Error(c.oldOrderSn, ", 调用美团同意退款，接口异常, ", err)
		} else if res.Code != 200 {
			err = errors.New(res.Error + "-" + res.Message)
			glog.Error(c.oldOrderSn, ", 调用美团同意退款，接口失败, ", kit.JsonEncode(res))
		} else {
			glog.Error(c.oldOrderSn, ", 调用美团同意退款，接口成功, ", kit.JsonEncode(res))
		}
	}
	return
}

func (c channelMtOrder) RejectRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	return
}

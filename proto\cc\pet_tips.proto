syntax = "proto3";

package cc;

////////////////////////////////////////////////////////// message //////////////////////////////////////////////////////////////////////////////////// 

// 数据传输对象
message petTipsDto {
    // Id
    int32 id=1;
    // 标题
    string title=2;
    // 主图
    string icon=3;
    // 阅读量
    int32 reading=10;
    // 详细内容
    string content=4;
    // 创建日期
    string createAt=5;
    // 创建人
    string createBy=6;
    // 更新日期
    string updateAt=7;
    // 更新人
    string updateBy=8;
    // 标签信息
    repeated petTipsTagDto tags=9;
    //展示阅读量，虚拟的随机生成
    int32 virtualReading = 11;
}

// 标签
message petTipsTagDto {
    // 名称
    string name=1;
    // 值
    string value=2;
}



// 新增或修改宠物贴士
message petTipsEditRequest {
    int32 id=1;
    //标题
    string title=2;
    // 主图
    string icon=3;
    //内容
    string content=4;
    //操作用户代码
    string userNo=5;
    // 标签信息
    repeated petTipsTagDto tags=6;
}

// 删除
message petTipsDeleteRequest{
    //单个id
    int32 id=1;
    // ids 删除多个，逗号分割id列表
    string ids=2;
}

// 新增或修改宠物贴士响应
message petTipsEditResponse {
    int32 code=1;
    string message=2;
}

// 查询
message petTipsQueryRequest {
    // 搜索
    string searchKey=1;
    // id
    int32 id=2;
    // 页索引
    int32 pageIndex=3;
    // 页大小
    int32 pageSize=4;
}
// 随机查询宠物贴士
message petTipsRandQueryRequest{
     // 随机查询数量
     int32 size=1;
}
// 查询响应
message petTipsQueryResponse{
    int32 code=1;
    string message=2;
    // 总记录条数
    int32 total=3;
    // 分页数据
    repeated petTipsDto data=4;
}

// 查询单个贴士信息
message petTipsGetRequest {
    // id
    int32 id=1;
}
// 查询单个贴士信息响应
message petTipsGetResponse {
    int32 code=1;
    string message=2;
    // 单个数据
    petTipsDto data=3;
}

// scrm 宠物品种查询
message scrmPetVarietyRequest {
    string id=1;
}
// scrm 宠物品种dto
message scrmPetVarietyDto {
    string id=1;
    string name=2;
    string extend=3;
}
// scrm 宠物品种响应
message scrmPetVarietyResponse {
    int32 code=1;
    string message=2;
    repeated scrmPetVarietyDto data=3;
}

/**
    判断宠物是否处于特殊阶段
**/
//Request
message tipsForCustomizedRequest{
    repeated  int32 tipIds = 1;
}

//Response
message tipsForCustomizedResponse {
    int32 code=1;
    string message=2;
    repeated tipsForCustomized data=3;
}

message tipsForCustomized{
    //主键id
    int32 id = 1;
    //标题
    string title = 2;
    //橱窗图片
    string icon = 3;
    //阅读量
    int32 reading = 4;
    //是否被删除 0 未 1 已删除
    int32 is_deleted = 5;
    //创建日期
    string create_at = 6;
    //创建人
    string create_by = 7;
    //更新日期
    string update_at = 8;
    //更新人id
    string update_by = 9;
    //贴士标签
    repeated tipsTag tag = 10;
}

message tipsTag{
    //标签名称
    string tag_name = 1;
    //标签值
    string tag_value = 2;
}

/**
    根据物种分页获取数据
**/
//Request
message QueryBySpeciesRequest{
    //物种
    string species = 1;
    // 页索引
    int32 pageIndex=2;
    // 页大小
    int32 pageSize=3;
}

/**
    判断宠物是否处于特殊阶段
**/
//Request
message QueryByConditionRequest{
    //物种
    string species = 1;
    //年龄
    string age = 2;
}

//Response
message QueryByConditionResponse {
    int32 code=1;
    string message=2;
    repeated QueryByCondition data=3;
}

message QueryByCondition{
    //主键id
    int32 id = 1;
    //标题
    string title = 2;
    //橱窗图片
    string icon = 3;
    //阅读量
    int32 reading = 4;
    //是否被删除 0 未 1 已删除
    int32 is_deleted = 5;
    //创建日期
    string create_at = 6;
    //创建人
    string create_by = 7;
    //更新日期
    string update_at = 8;
    //更新人id
    string update_by = 9;
    //贴士标签
    repeated tipsTag tag = 10;
}

/**
    未登录时分页获取贴士信息
**/
//Request
message GetTipsListRequest{
    //贴士id
    int32 tip_id = 1;
    // 页索引
    int32 pageIndex=2;
    // 页大小
    int32 pageSize=3;

}

//Response
message GetTipsListResponse {
    int32 code=1;
    string message=2;
    int32 total = 3;
    repeated GetTipsList data=4;
}

message GetTipsList{
    //主键id
    int32 id = 1;
    //标题
    string title = 2;
    //橱窗图片
    string icon = 3;
    //阅读量
    int32 reading = 4;
    //是否被删除 0 未 1 已删除
    int32 is_deleted = 5;
    //创建日期
    string create_at = 6;
    //创建人
    string create_by = 7;
    //更新日期
    string update_at = 8;
    //更新人id
    string update_by = 9;
    //贴士标签
    repeated tipsTag tag = 10;
}




/**
    旺财小贴士 - 宠物贴士推荐
**/
//Request
message recommendTipsRequest{
    //物种
    string species = 1;
}

message recommendTipsResponse {
    int32 code=1;
    string message=2;
    repeated recommendTips data=3;
}

message recommendTips{
    //主键id
    int32 id = 1;
    //标题
    string title = 2;
    //橱窗图片
    string icon = 3;
    //阅读量
    int32 reading = 4;
    //是否被删除 0 未 1 已删除
    int32 is_deleted = 5;
    //创建日期
    string create_at = 6;
    //创建人
    string create_by = 7;
    //更新日期
    string update_at = 8;
    //更新人id
    string update_by = 9;
    //贴士内容
    string content = 10;
    //贴士标签
    repeated tipsTag tag = 11;
}

/**
    通过贴士id([]int32)获取贴士信息
**/

message queryByIdRequest{
    //贴士id
    repeated int32 pet_tips_id = 1;
}

/**
    //分页获取贴士信息(带上标签数据)
**/

message queryTipWithTagsRequest{
    // 页索引
    int32 pageIndex = 1;
    // 页大小
    int32 pageSize = 2;
}


// 文章小贴士推荐
message ArticleTipsRequest {
}
message ArticleTipsResponse {
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    repeated ArticleTipsData data = 3;
}
message ArticleTipsData {
    // 文章id
    int64 article_id = 1;
    // 文章标题
    string title = 2;
}



////////////////////////////////////////////////////////// service ////////////////////////////////////////////////////////////////////////////////////

service petTipsService {
    //根据物种分页获取数据
    rpc QueryBySpecies(QueryBySpeciesRequest) returns(petTipsQueryResponse);
    //分页获取贴士信息(带上标签数据)
    rpc QueryTipWithTags(queryTipWithTagsRequest) returns(petTipsQueryResponse);
    //通过贴士id([]int32)获取贴士信息
    rpc QueryByIds(queryByIdRequest) returns(petTipsQueryResponse);
    // 新增
    rpc Add(petTipsEditRequest) returns(petTipsEditResponse);
    // 修改
    rpc Update(petTipsEditRequest) returns(petTipsEditResponse);
    // 更新阅读量
    rpc UpdateReading(petTipsEditRequest) returns(petTipsEditResponse);
    // 删除
    rpc Delete(petTipsDeleteRequest) returns(petTipsEditResponse);
    // 查询
    rpc Query(petTipsQueryRequest) returns(petTipsQueryResponse);
    // 随机查询
    rpc QueryRand(petTipsRandQueryRequest) returns(petTipsQueryResponse);
    // scrm 品种查询
    rpc QueryScrmPetVariety(scrmPetVarietyRequest) returns(scrmPetVarietyResponse);
    // 查询单个
    rpc Get(petTipsGetRequest) returns(petTipsGetResponse);
    //通过贴士ids获取信息
    rpc QueryByTipIds(tipsForCustomizedRequest) returns(tipsForCustomizedResponse);
    //根据特定条件获取数据
    rpc QueryByCondition(QueryByConditionRequest) returns(QueryByConditionResponse);
    //未登录时分页获取贴士信息
    rpc GetTipsList(GetTipsListRequest) returns(GetTipsListResponse);
    //旺财小贴士 - 宠物贴士推荐
    rpc RecommendTips(recommendTipsRequest) returns(recommendTipsResponse);
    // content-api内容中心推荐宠物贴士-小程序
    rpc ArticleTips(ArticleTipsRequest) returns(ArticleTipsResponse);
}
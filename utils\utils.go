package utils

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"io"
	"io/ioutil"
	"math"
	"math/big"
	"math/rand"
	"mime/multipart"
	"net/http"
	"net/url"
	"order-center/pkg/code"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"

	"github.com/spf13/cast"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

const (
	CODE_SUCCESS         = 200
	CODE_BUSINESSERROR   = 300
	CODE_SERVEREXCEPTION = 400
)

var HttpDefaultClient *http.Client
var HttpTransportClient *http.Client
var ClientNoTransport10S *http.Client
var Client60Second *http.Client
var Client30Second *http.Client

type RC4 struct {
	KEY string
	//Plaintexts 明文
	Plaintexts string
	//Ciphertexts 密文
	Ciphertexts string
}

func init() {
	HttpDefaultClient = http.DefaultClient

	HttpTransportClient = &http.Client{
		Timeout: time.Second * 60,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}}

	ClientNoTransport10S = &http.Client{
		Timeout: time.Second * 10,
	}
	Client30Second = &http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	Client60Second = &http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
}

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// 返回一个16位md5加密后的字符串
func Get16MD5Encode(data string) string {
	return strings.ToUpper(GetMD5Encode(data)[8:24])
}

// 返回一个32位md5加密后的字符串
func GetMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// UploadExcelToQiNiu1 上传excel文件到七牛云 流的方式
func UploadExcelToQiNiu1(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = kit.GetGuid36() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	host := config.GetString("file-upload-url")
	if len(host) == 0 {
		host = "https://api.rp-pet.com"
	}
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// 七牛上传结果
type QiNiuUploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// 将处理失败的商品信息导入excel上传至七牛云
func UploadExcelToQiNiu(fileName string) (url string, err error) {
	defer kit.CatchPanic()

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)

	fd, err := os.Open(fileName)
	if err != nil {
		return "", errors.New("打开文件失败，" + err.Error())
	}
	defer fd.Close()

	io.Copy(fileWriter, fd)
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	glog.Info("上传开始")
	// 上传文件

	path := config.GetString("file-upload-url") + "/fss/newup"
	resp, err := http.Post(path, contentType, bodyBuffer)
	if err != nil {
		return "", errors.New("上传接口接口调用失败，" + err.Error())
	}
	if resp != nil {
		defer resp.Body.Close()
	}

	glog.Info("上传完成")
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", errors.New("读取body失败，" + err.Error())
	}

	if len(respBody) == 0 {
		return "", errors.New("读取body失败，body为空")
	}

	result := new(QiNiuUploadResult)
	if err = json.Unmarshal(respBody, result); err != nil {
		return "", errors.New("解析json失败，" + err.Error() + "，json：" + string(respBody))
	}
	glog.Info("解析下载连接完成，", result.Url)

	if len(result.Url) == 0 {
		return "", errors.New("七牛云上传失败，" + result.Err)
	}

	return result.Url, nil
}

// 获取签名(正向接口)
// par: 应用参数的JSON ，methodName 调用API的名称,返回拼接好的
func Sign(par string, methodName string, tokenStr string, app_key string, appSecret string) string {

	format := "json"
	method := methodName
	sign_method := "md5"
	timestamp := kit.GetTimeNow()
	token := tokenStr
	v := "1.0"
	body := par

	var slice1 []string
	//slice1 := make([]string, ParLen)
	slice1 = append(slice1, "app_key"+app_key)
	slice1 = append(slice1, "format"+format)
	slice1 = append(slice1, "method"+method)
	slice1 = append(slice1, "sign_method"+sign_method)
	slice1 = append(slice1, "timestamp"+timestamp)
	slice1 = append(slice1, "v"+v)

	//如果是获取token的API就不需要TOKEN参数
	if methodName != "emall.token.get" {
		slice1 = append(slice1, "token"+token)
	}
	sort.Strings(slice1)
	content := appSecret
	for i := 0; i < len(slice1); i++ {
		content += slice1[i]
	}
	content += body
	content += appSecret
	sgin := strings.ToUpper(kit.GetMd5(content))
	retrunSgin := "method=" + method + "&timestamp=" + url.QueryEscape(timestamp) + "&format=" + format + "&app_key=" + app_key + "&v=" + v + "&sign_method=" + sign_method
	if methodName != "emall.token.get" {
		retrunSgin += "&token=" + token
	}
	retrunSgin += "&sign=" + sgin
	return retrunSgin
}

// 计算下次订单重试时间
// 下次重试时间 =创建时间+ 60秒 * 2 ^ task.RedoCount 也即任务创建后的 2 4 8 16 32 64 128 ...分钟时进行重试
func GetNextOrderRedoTimeDuration(count int) time.Duration {
	mulNum := Exponent(2, count)
	nextRedoDuration := time.Duration(mulNum*60) * time.Second
	return nextRedoDuration
}

// omsAPI请求通用方法
func HttpPostToOms(url string, bytesData []byte, format string) ([]byte, error) {
	//跳过证书验证
	//tr := &http.Transport{
	//	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	//	DialContext: (&net.Dialer{
	//		Timeout: 10 * time.Second,
	//	}).DialContext,
	//}
	logPrefix := fmt.Sprintf("HttpPostToOms-url:%s|byteData:%s|format:%s====", url, string(bytesData), format)
	glog.Info(logPrefix, "=====开始")
	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		glog.Error(err.Error())
		return nil, err
	}
	request.Header.Set("Content-Type", "application/"+format+";charset=UTF-8")

	//client := &http.Client{Transport: tr}
	resp, err := HttpTransportClient.Do(request)
	glog.Infof("%s请求参数为%s,结果为%s,错误为%v", logPrefix, kit.JsonEncode(request), kit.JsonEncode(resp), err)
	if err != nil {
		glog.Error(logPrefix, err.Error())
		return nil, err
	}
	if resp != nil {
		defer resp.Body.Close()
	}
	if resp.StatusCode != 200 {
		glog.Error(logPrefix, resp.Status)
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		glog.Error(logPrefix, err.Error())
		return nil, err
	}
	return respBytes, nil
}

func HttpPost(url string, bytesData []byte, contentType string) ([]byte, error) {
	//跳过证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		glog.Error(err.Error())
		return nil, err
	}
	if len(contentType) > 0 {
		request.Header.Set("Content-Type", contentType) //"application/json;charset=UTF-8"
	}
	client := &http.Client{Timeout: time.Second * 60, Transport: tr}
	resp, err := client.Do(request)
	if err != nil {
		glog.Error(err.Error())
		return nil, err
	}
	if resp != nil {
		defer resp.Body.Close()
	}
	if resp.StatusCode != 200 {
		glog.Error(resp.Status)
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		glog.Error(err.Error())
		return nil, err
	}
	return respBytes, nil
}

// OMS的http失败与否是按照http状态来给的  而不是code
func RpomsHttpPost(url string, bytesData []byte, contentType string) (respBytes []byte, httpCode int, err error) {
	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		glog.Error(err.Error())
		return nil, 0, err
	}
	if len(contentType) > 0 {
		request.Header.Set("Content-Type", contentType) //"application/json;charset=UTF-8"
	}
	resp, err := HttpTransportClient.Do(request)
	if err != nil {
		glog.Error("请求oms出错：" + url + ":" + err.Error())
		return nil, 0, errors.New("请求oms出错")
	}
	if resp != nil {
		defer resp.Body.Close()
	}

	respBytes, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		glog.Error("读取oms返回数据出错" + url + ":" + err.Error())
		return nil, 0, errors.New("读取oms返回数据出错")
	}
	return respBytes, resp.StatusCode, nil
}

// 子龙请求
// dataJson : 数据对象转化成json字符串
func HttpPostZl(url string, dataJson []byte, Headers string) (int, []byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := Client60Second.Do(req)
	if err != nil {
		glog.Error("调用子龙接口失败：", err, string(dataJson))
		return code.HttpRequestError, []byte(""), err
	}
	if res != nil {
		defer res.Body.Close()
	}

	body, err := ioutil.ReadAll(res.Body)
	if res.StatusCode != 200 {
		glog.Error("请求子龙出错："+url+"返回状态码："+cast.ToString(res.StatusCode), ",body:", string(dataJson), " 返回参数：", string(body))
		return code.HttpStatusCodeErr, nil, errors.New("状态码不对,返回http状态码：" + cast.ToString(res.StatusCode))
	}
	if err != nil {
		glog.Error("读取返回数据出错"+url+":"+err.Error(), ",body:", string(dataJson))
		return code.HttpResReadErr, nil, errors.New("读取返回数据出错:" + err.Error())
	}
	return code.HttpSuccess, body, nil
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.GetString("BJAuth.AppId"), config.GetString("BJAuth.Secret"), method, Timestamp, config.GetString("BJAuth.Version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.GetString("BJAuth.AppId")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.GetString("BJAuth.Version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign
	return arr
}

// 处理redis的setnx的返回结果。如果锁定时间已经超过默认时间5分钟，则自动删除。默认时间可更改
func DelRedisSetNx(redisConn *redis.Client, redisKey string, timeMinute int32) bool {
	if redisConn.Exists(redisKey).Val() > 0 {
		timeUnix, _ := strconv.Atoi(redisConn.Get(redisKey).Val())
		//与当前时间比较
		timeNowUnix := time.Now().Add(-1 * time.Minute * 5).Unix() // 5分钟
		if timeMinute > 0 {
			timeDuration := time.Duration(-1*timeMinute) * time.Minute
			timeNowUnix = time.Now().Add(timeDuration).Unix()
		}
		if timeNowUnix >= int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(redisKey)
			return true
		}
		return false
	}
	return true
}

// 计算地球上两点间距离
func EarthDistance(lat1, lng1, lat2, lng2 float64) float64 {
	radius := 6371.0 // 6378137
	rad := math.Pi / 180.0

	lat1 = lat1 * rad
	lng1 = lng1 * rad
	lat2 = lat2 * rad
	lng2 = lng2 * rad

	theta := lng2 - lng1
	dist := math.Acos(math.Sin(lat1)*math.Sin(lat2) + math.Cos(lat1)*math.Cos(lat2)*math.Cos(theta))

	return dist * radius
}

// 返回hash int
func HashInt(s string) int {
	v := int(crc32.ChecksumIEEE([]byte(s)))
	if v >= 0 {
		return v
	}
	if -v >= 0 {
		return -v
	}
	// v == MinInt
	return 0
}

// 饿了么API请求参数组装
func BuildCmd(arr map[string]string) string {
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		if v == "body" {
			str += v + "=" + url.QueryEscape(arr[v])
		} else {
			str += v + "=" + arr[v]
		}
	}
	return str
}

// OMS统一封装接口
// method:方法名称     jsonstr:业务数据的JOSN字符串
func OmsApi(method string, jsonstr string, format string) ([]byte, error) {

	arr := make(map[string]string)
	arr["app_key"] = url.QueryEscape(OmsappKey)
	arr["method"] = url.QueryEscape(method)
	arr["customerId"] = url.QueryEscape(Omscustomeid)
	arr["format"] = format
	timestamp := kit.GetTimeNow()
	arr["timestamp"] = timestamp
	arr["sign_method"] = "md5"
	//arr["body"] = string(jsonstr)
	//签名
	sign := OMSSign(arr, jsonstr)
	arr["sign"] = sign
	//参数组装
	arr["timestamp"] = url.QueryEscape(timestamp)
	param := BuildCmd(arr)
	escapeUrl := OmsUrl + param
	result, err := HttpPostToOms(escapeUrl, []byte(jsonstr), format)
	if err != nil {
		glog.Error("oms接口请求错误____", method, "请求参数____", jsonstr, "错误信息____", err)
		var retmes = []byte("本地错误")
		return retmes, err
	}
	return result, err
}

// 饿了么API签名
func OMSSign(arr map[string]string, body string) string {

	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		str += v + "" + arr[v]
	}
	sigstr := Omssecret + str + body + Omssecret
	return strings.ToUpper(kit.GetMd5(sigstr))
}

// 电商签名
func MallSign(arr map[string]string, body string) string {

	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//按字母进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		str += v + "" + arr[v]
	}

	return strings.ToUpper(kit.GetMd5(kit.GetMd5(str) + Mallsecret))
}

// 推送给电商
func HttpPostFormToMall(url, contentType string, param map[string]interface{}) (int, string) {
	if contentType == "" {
		contentType = "application/x-www-form-urlencoded"
	}
	return action(url, http.MethodPost, contentType, param)
}

func action(uri, httpMethod string, contentType string, param map[string]interface{}) (int, string) {
	defer kit.CatchPanic()

	var req *http.Request
	switch httpMethod {
	case http.MethodGet:
		if param != nil {
			uri += "?" + mapToValues(param).Encode()
		}
		req, _ = http.NewRequest(httpMethod, uri, nil)
	case http.MethodPost:
		httpMethod = http.MethodPost
		var reader io.Reader

		if contentType == "application/x-www-form-urlencoded" {
			reader = strings.NewReader(mapToValues(param).Encode())
		} else if contentType == "application/json;charset=UTF-8" {
			byteData, _ := json.Marshal(param)
			reader = bytes.NewReader(byteData)
		}
		req, _ = http.NewRequest(httpMethod, uri, reader)
		req.Header.Add("Content-Type", contentType)
		//电商要求，头部传Token add by csf
		//req.Header.Add("TOKEN", "fe657496b1c46be00e86c05d2b190c9a")
	default:
		return 0, "不支持的请求类型"
	}

	// for k, v := range httpHeader {
	// 	req.Header.Add(k, v)
	// }
	//ul := uuid.NewV4()
	//sn := strings.ReplaceAll(ul.String(), "-", "")
	//req.Header.Add("sn", sn)
	//req.Header.Add("source", source)
	//req.Header.Add("ua", ua)
	//req.Header.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))

	//client := http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

	res, err := Client30Second.Do(req)
	if err != nil {
		glog.Error(err)
		return 0, err.Error()
	}

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)
	return res.StatusCode, string(body)
}

func mapToValues(mp map[string]interface{}) url.Values {
	v := url.Values{}
	for key, val := range mp {
		switch val.(type) {
		case int:
			v.Add(key, strconv.Itoa(val.(int)))
		case int32:
			v.Add(key, strconv.Itoa(int(val.(int32))))
		case int64:
			v.Add(key, strconv.Itoa(int(val.(int64))))
		case float64:
			v.Add(key, strconv.FormatFloat(val.(float64), 'E', -1, 64))
		case float32:
			v.Add(key, strconv.FormatFloat(float64(val.(float32)), 'E', -1, 32))
		default:
			v.Add(key, val.(string))
		}
	}
	glog.Info(v.Encode())
	return v
}

// 判断开始时间和结束时间比较大小。如果开始时间大于结束时间则返回true
func CampareTime(sTimeStr, eTimeStr string) bool {
	format := "2006-01-02 15:04:05"
	a, _ := time.Parse(format, sTimeStr)
	b, _ := time.Parse(format, eTimeStr)
	return a.After(b)
}

var (
	//oms接口地址
	OmsUrl = config.GetString("OmsUrl")
	//appkey
	OmsappKey = config.GetString("OmsappKey")
	//secret
	Omssecret = config.GetString("Omssecret")
	//客户标识
	Omscustomeid = config.GetString("omscustomeid")

	//电商secret
	Mallsecret = config.GetString("Mallsecret")
)

const replacement = ""

var replacer = strings.NewReplacer(
	" ", replacement,
	"\r\n", replacement, // 换行
	"\r", replacement, // 换行
	"\n", replacement, // 换行
	"\v", replacement, // 垂直制
	"\f", replacement, // 换页
	"\t", replacement, // 水平制表
	"\u0085", replacement, // NEXT LINE
	"\u2028", replacement, // 行分隔符
	"\u2029", replacement, // 段落分隔符
)

// string去掉空格，换行符
func StringCleaning(s string) string {
	return replacer.Replace(s)
}

// 带逗号的字符串 转为 字符串切片
func StringsToSlice(commaStr string) ([]string, error) {
	out := make([]string, 0)
	commaStr = strings.Trim(commaStr, " ")
	if len(commaStr) > 0 {
		// 英文逗号
		out = append(out, strings.Split(commaStr, ",")...)
		if len(out) <= 0 {
			// 中文逗号
			out = append(out, strings.Split(commaStr, "，")...)
		}
	}
	return out, nil
}

// 随机生成指定长度字符串
func RandStr(n int) string {
	rand.Seed(time.Now().Unix())
	var letters = []byte("abcdefghjkmnpqrstuvwxyz123456789")
	if n <= 0 {
		return ""
	}
	b := make([]byte, n)
	arc := uint8(0)
	if _, err := rand.Read(b[:]); err != nil {
		return ""
	}
	var builder strings.Builder
	for _, x := range b {
		arc = x & 31
		builder.WriteString(string(letters[arc]))
	}
	return builder.String()
}

// A8对外接口验签规则
func A8Sign(params map[string]string, body string, secret string) string {
	//签名步骤一：按字典序排序参数
	keys := make([]string, len(params))
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys) // 对键进行排序
	var builder strings.Builder
	for _, k := range keys {
		if params[k] != "" {
			builder.WriteString(k)
			builder.WriteString(params[k])
		}
	}

	//签名步骤二：在string后添加请求主体，最后在首尾加上client_secret
	signStr := secret + builder.String() + body + secret

	//签名步骤三：MD5加密,所有字符转为大写
	return strings.ToUpper(kit.GetMd5(signStr))
}

// getYearMonthToDay 查询指定年份指定月份有多少天
// @params year int 指定年份
// @params month int 指定月份
func GetYearMonthToDay(year int, month int) int {
	// 有31天的月份
	day31 := map[int]bool{
		1:  true,
		3:  true,
		5:  true,
		7:  true,
		8:  true,
		10: true,
		12: true,
	}
	if day31[month] == true {
		return 31
	}
	// 有30天的月份
	day30 := map[int]bool{
		4:  true,
		6:  true,
		9:  true,
		11: true,
	}
	if day30[month] == true {
		return 30
	}
	// 计算是平年还是闰年
	if (year%4 == 0 && year%100 != 0) || year%400 == 0 {
		// 得出2月的天数
		return 29
	}
	// 得出2月的天数
	return 28
}

// body为json字符串
func HttpPostToDigitalHealth(path string, body []byte, method string) (int, string) {
	var sb strings.Builder
	//accessKey := "7e02a37e71b911eca2dd000c29dd6f97"
	//secretKey := "2Dj4dvmxZkvfdjgz8ipMBUWoMpaeTzFzRxzn7KXBthkGAxsJ2zrX2Tup7EpEEdJRBJqFbLExyiFQ6hG4qwXhVSEHGEhvBnsM66X4jH23VM9bVQqtwPjXkvwdF4bTBsV4"
	accessKey := config.GetString("digital.health.access.key")
	secretKey := config.GetString("digital.health.secret.key")
	sb.WriteString("POST\n")
	sb.WriteString(path + "\n")
	sb.WriteString("\n")
	sb.WriteString(accessKey + "\n")
	sb.WriteString("\n")
	//println("str:", sb.String())
	signHMAC := func(secret, message string) string {
		hash := hmac.New(sha256.New, []byte(secret))
		hash.Write([]byte(message))
		hex.EncodeToString(hash.Sum(nil))
		return base64.StdEncoding.EncodeToString(hash.Sum(nil))
	}
	sign := signHMAC(secretKey, sb.String())

	signBody := signHMAC(secretKey, string(body))
	//println("SIGNATURE_BODY", signBody)
	url := config.GetString("digital.health.url")
	//url := "https://gateway.sit.rvet.cn"
	reader := bytes.NewReader(body)
	req, _ := http.NewRequest("POST", url+path, reader)
	req.Header.Add("X-HMAC-SIGNATURE", sign)
	req.Header.Add("X-HMAC-ALGORITHM", "hmac-sha256")
	req.Header.Add("X-HMAC-ACCESS-KEY", accessKey)
	req.Header.Add("X-HMAC-DIGEST", signBody)
	if method == "json" {
		req.Header.Set("Content-Type", "application/json")
	}

	if res, err := http.DefaultClient.Do(req); err != nil {
		return 0, err.Error()
	} else {
		defer res.Body.Close()
		b, _ := ioutil.ReadAll(res.Body)
		return res.StatusCode, string(b)
	}
}

func Round(x float64) int {
	return int(math.Floor(x + 0.5))
}

// 计算健康值 = 四舍五入(payAmount(四舍五入)*0.8)
func CalculateHealthVal(payAmount float64) int64 {
	return int64(math.Round(math.Round(payAmount) * 0.8))
}

// BcDiv 精度除法
func BcDiv(x, y float64, places int) float64 {
	a := big.NewFloat(x)
	b := big.NewFloat(y)
	c := new(big.Float).Quo(a, b)
	d, _ := c.Float64()
	return RoundPlace(d, places)
}

// RoundPlace 保留小数位
func RoundPlace(val float64, places int) float64 {
	var round float64
	pow := math.Pow(10, float64(places))
	digit := pow * val
	_, div := math.Modf(digit)
	if div >= 0.5 {
		round = math.Ceil(digit)
	} else {
		round = math.Floor(digit)
	}
	return round / pow
}

// a的n次方
func Exponent(a, n int) int {
	result := 1
	for i := n; i > 0; i >>= 1 {
		if i&1 != 0 {
			result *= a
		}
		a *= a
	}
	return result
}

// 手机号星号代替
func MobileReplaceWithStar(mobile string) string {
	if len(mobile) == 0 {
		return mobile
	}
	if len(mobile) < 8 {
		return mobile + strings.Repeat("*", 11-len(mobile))
	}
	return mobile[0:3] + "****" + mobile[7:]
}

// InterfaceToJSON interface 转化成 json 字符串
func InterfaceToJSON(req interface{}) string {
	reqByte, _ := json.Marshal(req)
	return string(reqByte)
}

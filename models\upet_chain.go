package models

// 店铺门店表
type UpetChain struct {
	ChainId                int32   `xorm:"not null pk autoincr comment('门店id') INT(11)" json:"chain_id" json:"chain_id"` // 门店id
	StoreId                int32   `json:"store_id"`                                                                     // 所属店铺id
	ChainUser              string  `json:"chain_user"`                                                                   // 登录名
	ChainPwd               string  `json:"chain_pwd"`                                                                    // 登录密码
	ChainName              string  `json:"chain_name"`                                                                   // 门店名称
	ChainErpName           string  `json:"chain_erp_name"`                                                               // ERP门店名称
	ChainImg               string  `json:"chain_img"`                                                                    // 门店图片
	AreaId1                int32   `json:"area_id_1"`                                                                    // 一级地区id
	AreaId2                int32   `json:"area_id_2"`                                                                    // 二级地区id
	AreaId3                int32   `json:"area_id_3"`                                                                    // 三级地区id
	AreaId4                int32   `json:"area_id_4"`                                                                    // 四级地区id
	AreaId                 int32   `json:"area_id"`                                                                      // 地区id
	AreaInfo               string  `json:"area_info"`                                                                    // 地区详情
	ChainAddress           string  `json:"chain_address"`                                                                // 详细地址
	ChainPhone             string  `json:"chain_phone"`                                                                  // 联系方式
	ChainOpeningHours      string  `json:"chain_opening_hours"`                                                          // 营业时间
	ChainTrafficLine       string  `json:"chain_traffic_line"`                                                           // 交通线路
	ChainBanner            string  `json:"chain_banner"`                                                                 // 门店横幅图片
	ChainState             int32   `json:"chain_state"`                                                                  // 0 已关闭  1 正常  2 待审核 3审核未通过 4待付保证金 5已付保证金 6保证金审核失败
	ChainLogo              string  `json:"chain_logo"`                                                                   // 门店LOGO
	ChainCycle             int32   `json:"chain_cycle"`                                                                  // 门店结算周期
	ChainCloseInfo         string  `json:"chain_close_info"`                                                             // 门店关闭原因
	ChainCheckInfo         string  `json:"chain_check_info"`                                                             // 审核意见
	ChainLat               float64 `json:"chain_lat"`                                                                    // 纬度
	ChainLng               float64 `json:"chain_lng"`                                                                    // 经度
	IsTransport            int32   `json:"is_transport"`                                                                 // 是否支持配送 0不支持  1支持
	TransportDistance      int32   `json:"transport_distance"`                                                           // 配送半径（单位：公里）
	StartAmountPrice       int32   `json:"start_amount_price"`                                                           // 起送金额
	TransportRule          int32   `json:"transport_rule"`                                                               // 配送规则  1 配送半径  2配送区域
	TransportAreas         string  `json:"transport_areas"`                                                              // 配送区域 数组
	IsForwardOrder         int32   `json:"is_forward_order"`                                                             // 是否转接订单 0 否   1是
	IsAutoForward          int32   `json:"is_auto_forward"`                                                              // 是否自动转接订单  0否   1是
	ExpressCity            string  `json:"express_city"`                                                                 // 支持发货城市
	IsSelfTake             int32   `json:"is_self_take"`                                                                 // 是否支持自提 0否  1是
	IsCollection           int32   `json:"is_collection"`                                                                // 是否支持代收货  0否   1是
	CollectionPrice        float64 `json:"collection_price"`                                                             // 代收货费用
	StoreName              string  `json:"store_name"`                                                                   // 所属店铺名称
	ChainApplyTime         int32   `json:"chain_apply_time"`                                                             // 门店申请时间
	ChainTime              int32   `json:"chain_time"`                                                                   // 开店时间
	ChainCloseTime         string  `json:"chain_close_time"`                                                             // 门店关闭时间
	IsOwn                  int32   `json:"is_own"`                                                                       // 是否自营
	PayCertificateType     string  `json:"pay_certificate_type"`                                                         // 保证金支付方式名称代码 offline线下支付
	PayingMoneyCertificate string  `json:"paying_money_certificate"`                                                     // 支付凭证
	PayingMoneyCertifExp   string  `json:"paying_money_certif_exp"`                                                      // 保证金支付说明
	ExpressCityName        string  `json:"express_city_name"`                                                            // 发货城市名称
	TransportFreight       float64 `json:"transport_freight"`                                                            // 配送费
	Lat                    string  `json:"lat"`
	Lng                    string  `json:"lng"`
	Jxaddress              string  `json:"jxaddress"`
	ChainErpId             string  `json:"chain_erp_id"`     // ERP门店编号
	ChainErpStatus         int32   `json:"chain_erp_status"` // 是否同步
	ChainErpTime           int32   `json:"chain_erp_time"`   // 同步时间
	ChainQrcode            string  `json:"chain_qrcode"`     // 店铺二维码
	TxLat                  string  `json:"tx_lat"`           // 腾讯经纬度
	TxLng                  string  `json:"tx_lng"`           // 腾讯经纬度
	ChainBrandId           int32   `json:"chain_brand_id"`   // 品牌id
	ChainKeywords          string  `json:"chain_keywords"`   // 门店服务关键字
	ChainTm                string  `json:"chain_tm"`         // 天猫核销资料
	ChainJd                string  `json:"chain_jd"`         // 京东核销资料
	ChainYz                string  `json:"chain_yz"`         // 有赞核销资料
	AccountId              string  `json:"account_id"`       // 财务编号
	RegionId               int32   `json:"region_id"`        // 大区标识
	ChainIsallow           int32   `json:"chain_isallow"`    // 是否允许核销0不允许1允许
}

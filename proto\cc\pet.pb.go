// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/pet.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PetListRequest struct {
	PetId                string   `protobuf:"bytes,1,opt,name=petId,proto3" json:"petId"`
	UserId               string   `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	Token                string   `protobuf:"bytes,3,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetListRequest) Reset()         { *m = PetListRequest{} }
func (m *PetListRequest) String() string { return proto.CompactTextString(m) }
func (*PetListRequest) ProtoMessage()    {}
func (*PetListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{0}
}

func (m *PetListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetListRequest.Unmarshal(m, b)
}
func (m *PetListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetListRequest.Marshal(b, m, deterministic)
}
func (m *PetListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetListRequest.Merge(m, src)
}
func (m *PetListRequest) XXX_Size() int {
	return xxx_messageInfo_PetListRequest.Size(m)
}
func (m *PetListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetListRequest proto.InternalMessageInfo

func (m *PetListRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetListRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type PetListResponse struct {
	Data                 []*PetInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PetListResponse) Reset()         { *m = PetListResponse{} }
func (m *PetListResponse) String() string { return proto.CompactTextString(m) }
func (*PetListResponse) ProtoMessage()    {}
func (*PetListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{1}
}

func (m *PetListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetListResponse.Unmarshal(m, b)
}
func (m *PetListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetListResponse.Marshal(b, m, deterministic)
}
func (m *PetListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetListResponse.Merge(m, src)
}
func (m *PetListResponse) XXX_Size() int {
	return xxx_messageInfo_PetListResponse.Size(m)
}
func (m *PetListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetListResponse proto.InternalMessageInfo

func (m *PetListResponse) GetData() []*PetInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetPetFlowerRequest struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetFlowerRequest) Reset()         { *m = GetPetFlowerRequest{} }
func (m *GetPetFlowerRequest) String() string { return proto.CompactTextString(m) }
func (*GetPetFlowerRequest) ProtoMessage()    {}
func (*GetPetFlowerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{2}
}

func (m *GetPetFlowerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetFlowerRequest.Unmarshal(m, b)
}
func (m *GetPetFlowerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetFlowerRequest.Marshal(b, m, deterministic)
}
func (m *GetPetFlowerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetFlowerRequest.Merge(m, src)
}
func (m *GetPetFlowerRequest) XXX_Size() int {
	return xxx_messageInfo_GetPetFlowerRequest.Size(m)
}
func (m *GetPetFlowerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetFlowerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetFlowerRequest proto.InternalMessageInfo

func (m *GetPetFlowerRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetPetFlowerRes struct {
	Data                 []*PetFlowerDict `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPetFlowerRes) Reset()         { *m = GetPetFlowerRes{} }
func (m *GetPetFlowerRes) String() string { return proto.CompactTextString(m) }
func (*GetPetFlowerRes) ProtoMessage()    {}
func (*GetPetFlowerRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{3}
}

func (m *GetPetFlowerRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetFlowerRes.Unmarshal(m, b)
}
func (m *GetPetFlowerRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetFlowerRes.Marshal(b, m, deterministic)
}
func (m *GetPetFlowerRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetFlowerRes.Merge(m, src)
}
func (m *GetPetFlowerRes) XXX_Size() int {
	return xxx_messageInfo_GetPetFlowerRes.Size(m)
}
func (m *GetPetFlowerRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetFlowerRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetFlowerRes proto.InternalMessageInfo

func (m *GetPetFlowerRes) GetData() []*PetFlowerDict {
	if m != nil {
		return m.Data
	}
	return nil
}

type PetFlowerDict struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	Orders               int32    `protobuf:"varint,5,opt,name=orders,proto3" json:"orders"`
	EnglishName          string   `protobuf:"bytes,6,opt,name=english_name,json=englishName,proto3" json:"english_name"`
	Path                 string   `protobuf:"bytes,7,opt,name=path,proto3" json:"path"`
	ParentCode           int32    `protobuf:"varint,8,opt,name=parent_code,json=parentCode,proto3" json:"parent_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetFlowerDict) Reset()         { *m = PetFlowerDict{} }
func (m *PetFlowerDict) String() string { return proto.CompactTextString(m) }
func (*PetFlowerDict) ProtoMessage()    {}
func (*PetFlowerDict) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{4}
}

func (m *PetFlowerDict) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetFlowerDict.Unmarshal(m, b)
}
func (m *PetFlowerDict) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetFlowerDict.Marshal(b, m, deterministic)
}
func (m *PetFlowerDict) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetFlowerDict.Merge(m, src)
}
func (m *PetFlowerDict) XXX_Size() int {
	return xxx_messageInfo_PetFlowerDict.Size(m)
}
func (m *PetFlowerDict) XXX_DiscardUnknown() {
	xxx_messageInfo_PetFlowerDict.DiscardUnknown(m)
}

var xxx_messageInfo_PetFlowerDict proto.InternalMessageInfo

func (m *PetFlowerDict) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetFlowerDict) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *PetFlowerDict) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PetFlowerDict) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PetFlowerDict) GetOrders() int32 {
	if m != nil {
		return m.Orders
	}
	return 0
}

func (m *PetFlowerDict) GetEnglishName() string {
	if m != nil {
		return m.EnglishName
	}
	return ""
}

func (m *PetFlowerDict) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *PetFlowerDict) GetParentCode() int32 {
	if m != nil {
		return m.ParentCode
	}
	return 0
}

type PetInfo struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId"`
	//宠物名称
	PetName string `protobuf:"bytes,4,opt,name=petName,proto3" json:"petName"`
	//宠物性别 0未知 1公 2母
	PetSex int32 `protobuf:"varint,5,opt,name=petSex,proto3" json:"petSex"`
	//宠物品种 -1未知 1000猫 1001狗 1002其他
	PetKindof int32 `protobuf:"varint,6,opt,name=petKindof,proto3" json:"petKindof"`
	//宠物品种
	PetVariety int32 `protobuf:"varint,7,opt,name=petVariety,proto3" json:"petVariety"`
	//是否绝育 -1未知,0未绝育,1已绝育
	PetNeutering int32 `protobuf:"varint,8,opt,name=petNeutering,proto3" json:"petNeutering"`
	//疫苗 -1未知,0未接种,1已接种
	PetVaccinated int32 `protobuf:"varint,9,opt,name=petVaccinated,proto3" json:"petVaccinated"`
	//驱虫  -1未知,0未驱虫,1 已驱虫
	PetDeworming int32 `protobuf:"varint,10,opt,name=petDeworming,proto3" json:"petDeworming"`
	//宠物体重,单位g
	PetWeight int32 `protobuf:"varint,11,opt,name=petWeight,proto3" json:"petWeight"`
	//宠物体长,单位mm
	PetLong int32 `protobuf:"varint,12,opt,name=petLong,proto3" json:"petLong"`
	//宠物体高,单位mm
	PetHeight int32 `protobuf:"varint,13,opt,name=petHeight,proto3" json:"petHeight"`
	//宠物来源
	PetSource int32 `protobuf:"varint,14,opt,name=petSource,proto3" json:"petSource"`
	//宠物状态 0正常,1死亡,2走失,4送人,8隐藏
	PetStatus int32 `protobuf:"varint,15,opt,name=petStatus,proto3" json:"petStatus"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,16,opt,name=petAvatar,proto3" json:"petAvatar"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,17,opt,name=petBirthday,proto3" json:"petBirthday"`
	//备注
	PetRemark string `protobuf:"bytes,18,opt,name=petRemark,proto3" json:"petRemark"`
	//创建时间
	CreateTime string `protobuf:"bytes,19,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,20,opt,name=updateTime,proto3" json:"updateTime"`
	//宠物faceId
	FaceId      string `protobuf:"bytes,21,opt,name=faceId,proto3" json:"faceId"`
	EnterSource string `protobuf:"bytes,22,opt,name=enterSource,proto3" json:"enterSource"`
	//宠物品种 未知 猫 狗 其他
	PetVarietyName string `protobuf:"bytes,23,opt,name=petVarietyName,proto3" json:"petVarietyName"`
	//宠物品种
	PetKindOfName string `protobuf:"bytes,24,opt,name=petKindOfName,proto3" json:"petKindOfName"`
	//最后一次驱虫记录
	LastExpellingParasiteRecord *VaccinateRecord `protobuf:"bytes,25,opt,name=lastExpellingParasiteRecord,proto3" json:"lastExpellingParasiteRecord"`
	//最后一次免疫记录
	LastImmunityRecord *VaccinateRecord `protobuf:"bytes,26,opt,name=lastImmunityRecord,proto3" json:"lastImmunityRecord"`
	//最后一次就诊记录
	LastDiagnosisRecord *LastDiagnosis `protobuf:"bytes,27,opt,name=lastDiagnosisRecord,proto3" json:"lastDiagnosisRecord"`
	//年龄
	AgeStr string `protobuf:"bytes,28,opt,name=ageStr,proto3" json:"ageStr"`
	//年龄换算
	AgeConversionStr string `protobuf:"bytes,29,opt,name=ageConversionStr,proto3" json:"ageConversionStr"`
	//0:就诊记录 1:上次就诊 2:体检提醒
	DiagnosisRemind int32 `protobuf:"varint,30,opt,name=diagnosisRemind,proto3" json:"diagnosisRemind"`
	//是否展示绝育 1是 0否
	NeuteringRemind int32 `protobuf:"varint,31,opt,name=neuteringRemind,proto3" json:"neuteringRemind"`
	//是否展示疫苗 1是 0否
	ImmunityRemind int32 `protobuf:"varint,32,opt,name=immunityRemind,proto3" json:"immunityRemind"`
	//是否展示驱虫 1是 0否
	ExpellingParasiteRemind int32    `protobuf:"varint,33,opt,name=expellingParasiteRemind,proto3" json:"expellingParasiteRemind"`
	SortWeight              int32    `protobuf:"varint,34,opt,name=sortWeight,proto3" json:"sortWeight"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *PetInfo) Reset()         { *m = PetInfo{} }
func (m *PetInfo) String() string { return proto.CompactTextString(m) }
func (*PetInfo) ProtoMessage()    {}
func (*PetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{5}
}

func (m *PetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetInfo.Unmarshal(m, b)
}
func (m *PetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetInfo.Marshal(b, m, deterministic)
}
func (m *PetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetInfo.Merge(m, src)
}
func (m *PetInfo) XXX_Size() int {
	return xxx_messageInfo_PetInfo.Size(m)
}
func (m *PetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PetInfo proto.InternalMessageInfo

func (m *PetInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetInfo) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetInfo) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetInfo) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *PetInfo) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *PetInfo) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

func (m *PetInfo) GetPetVariety() int32 {
	if m != nil {
		return m.PetVariety
	}
	return 0
}

func (m *PetInfo) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *PetInfo) GetPetVaccinated() int32 {
	if m != nil {
		return m.PetVaccinated
	}
	return 0
}

func (m *PetInfo) GetPetDeworming() int32 {
	if m != nil {
		return m.PetDeworming
	}
	return 0
}

func (m *PetInfo) GetPetWeight() int32 {
	if m != nil {
		return m.PetWeight
	}
	return 0
}

func (m *PetInfo) GetPetLong() int32 {
	if m != nil {
		return m.PetLong
	}
	return 0
}

func (m *PetInfo) GetPetHeight() int32 {
	if m != nil {
		return m.PetHeight
	}
	return 0
}

func (m *PetInfo) GetPetSource() int32 {
	if m != nil {
		return m.PetSource
	}
	return 0
}

func (m *PetInfo) GetPetStatus() int32 {
	if m != nil {
		return m.PetStatus
	}
	return 0
}

func (m *PetInfo) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *PetInfo) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *PetInfo) GetPetRemark() string {
	if m != nil {
		return m.PetRemark
	}
	return ""
}

func (m *PetInfo) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *PetInfo) GetFaceId() string {
	if m != nil {
		return m.FaceId
	}
	return ""
}

func (m *PetInfo) GetEnterSource() string {
	if m != nil {
		return m.EnterSource
	}
	return ""
}

func (m *PetInfo) GetPetVarietyName() string {
	if m != nil {
		return m.PetVarietyName
	}
	return ""
}

func (m *PetInfo) GetPetKindOfName() string {
	if m != nil {
		return m.PetKindOfName
	}
	return ""
}

func (m *PetInfo) GetLastExpellingParasiteRecord() *VaccinateRecord {
	if m != nil {
		return m.LastExpellingParasiteRecord
	}
	return nil
}

func (m *PetInfo) GetLastImmunityRecord() *VaccinateRecord {
	if m != nil {
		return m.LastImmunityRecord
	}
	return nil
}

func (m *PetInfo) GetLastDiagnosisRecord() *LastDiagnosis {
	if m != nil {
		return m.LastDiagnosisRecord
	}
	return nil
}

func (m *PetInfo) GetAgeStr() string {
	if m != nil {
		return m.AgeStr
	}
	return ""
}

func (m *PetInfo) GetAgeConversionStr() string {
	if m != nil {
		return m.AgeConversionStr
	}
	return ""
}

func (m *PetInfo) GetDiagnosisRemind() int32 {
	if m != nil {
		return m.DiagnosisRemind
	}
	return 0
}

func (m *PetInfo) GetNeuteringRemind() int32 {
	if m != nil {
		return m.NeuteringRemind
	}
	return 0
}

func (m *PetInfo) GetImmunityRemind() int32 {
	if m != nil {
		return m.ImmunityRemind
	}
	return 0
}

func (m *PetInfo) GetExpellingParasiteRemind() int32 {
	if m != nil {
		return m.ExpellingParasiteRemind
	}
	return 0
}

func (m *PetInfo) GetSortWeight() int32 {
	if m != nil {
		return m.SortWeight
	}
	return 0
}

type VaccinateRecord struct {
	//有值代表是手动添加的数据，否则是医院端记录数据
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//日期
	OperationDate string `protobuf:"bytes,2,opt,name=operationDate,proto3" json:"operationDate"`
	//医院名称
	ShopName string `protobuf:"bytes,3,opt,name=shopName,proto3" json:"shopName"`
	//产品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	//1疫苗记录 2驱虫记录
	Type int32 `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	//创建时间
	CreateTime           string   `protobuf:"bytes,6,opt,name=createTime,proto3" json:"createTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VaccinateRecord) Reset()         { *m = VaccinateRecord{} }
func (m *VaccinateRecord) String() string { return proto.CompactTextString(m) }
func (*VaccinateRecord) ProtoMessage()    {}
func (*VaccinateRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{6}
}

func (m *VaccinateRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VaccinateRecord.Unmarshal(m, b)
}
func (m *VaccinateRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VaccinateRecord.Marshal(b, m, deterministic)
}
func (m *VaccinateRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VaccinateRecord.Merge(m, src)
}
func (m *VaccinateRecord) XXX_Size() int {
	return xxx_messageInfo_VaccinateRecord.Size(m)
}
func (m *VaccinateRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_VaccinateRecord.DiscardUnknown(m)
}

var xxx_messageInfo_VaccinateRecord proto.InternalMessageInfo

func (m *VaccinateRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VaccinateRecord) GetOperationDate() string {
	if m != nil {
		return m.OperationDate
	}
	return ""
}

func (m *VaccinateRecord) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *VaccinateRecord) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *VaccinateRecord) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VaccinateRecord) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type LastDiagnosis struct {
	//疾病诊断
	MainSymptom string `protobuf:"bytes,1,opt,name=mainSymptom,proto3" json:"mainSymptom"`
	//开始时间
	StartTime string `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime"`
	//结束时间
	EndTime string `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime"`
	//机构id
	Orgid int64 `protobuf:"varint,4,opt,name=orgid,proto3" json:"orgid"`
	//挂号id
	RegId int64 `protobuf:"varint,5,opt,name=regId,proto3" json:"regId"`
	// 病例类型 【0-门诊；1-住院】
	MedType              int32    `protobuf:"varint,6,opt,name=medType,proto3" json:"medType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LastDiagnosis) Reset()         { *m = LastDiagnosis{} }
func (m *LastDiagnosis) String() string { return proto.CompactTextString(m) }
func (*LastDiagnosis) ProtoMessage()    {}
func (*LastDiagnosis) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{7}
}

func (m *LastDiagnosis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastDiagnosis.Unmarshal(m, b)
}
func (m *LastDiagnosis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastDiagnosis.Marshal(b, m, deterministic)
}
func (m *LastDiagnosis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastDiagnosis.Merge(m, src)
}
func (m *LastDiagnosis) XXX_Size() int {
	return xxx_messageInfo_LastDiagnosis.Size(m)
}
func (m *LastDiagnosis) XXX_DiscardUnknown() {
	xxx_messageInfo_LastDiagnosis.DiscardUnknown(m)
}

var xxx_messageInfo_LastDiagnosis proto.InternalMessageInfo

func (m *LastDiagnosis) GetMainSymptom() string {
	if m != nil {
		return m.MainSymptom
	}
	return ""
}

func (m *LastDiagnosis) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *LastDiagnosis) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *LastDiagnosis) GetOrgid() int64 {
	if m != nil {
		return m.Orgid
	}
	return 0
}

func (m *LastDiagnosis) GetRegId() int64 {
	if m != nil {
		return m.RegId
	}
	return 0
}

func (m *LastDiagnosis) GetMedType() int32 {
	if m != nil {
		return m.MedType
	}
	return 0
}

type NextImmunity struct {
	Age                  string   `protobuf:"bytes,1,opt,name=age,proto3" json:"age"`
	RecordTime           string   `protobuf:"bytes,2,opt,name=recordTime,proto3" json:"recordTime"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	ContentDetail        string   `protobuf:"bytes,4,opt,name=contentDetail,proto3" json:"contentDetail"`
	Tip                  string   `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NextImmunity) Reset()         { *m = NextImmunity{} }
func (m *NextImmunity) String() string { return proto.CompactTextString(m) }
func (*NextImmunity) ProtoMessage()    {}
func (*NextImmunity) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{8}
}

func (m *NextImmunity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextImmunity.Unmarshal(m, b)
}
func (m *NextImmunity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextImmunity.Marshal(b, m, deterministic)
}
func (m *NextImmunity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextImmunity.Merge(m, src)
}
func (m *NextImmunity) XXX_Size() int {
	return xxx_messageInfo_NextImmunity.Size(m)
}
func (m *NextImmunity) XXX_DiscardUnknown() {
	xxx_messageInfo_NextImmunity.DiscardUnknown(m)
}

var xxx_messageInfo_NextImmunity proto.InternalMessageInfo

func (m *NextImmunity) GetAge() string {
	if m != nil {
		return m.Age
	}
	return ""
}

func (m *NextImmunity) GetRecordTime() string {
	if m != nil {
		return m.RecordTime
	}
	return ""
}

func (m *NextImmunity) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NextImmunity) GetContentDetail() string {
	if m != nil {
		return m.ContentDetail
	}
	return ""
}

func (m *NextImmunity) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

type NextExpellingParasite struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date"`
	Tip                  string   `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NextExpellingParasite) Reset()         { *m = NextExpellingParasite{} }
func (m *NextExpellingParasite) String() string { return proto.CompactTextString(m) }
func (*NextExpellingParasite) ProtoMessage()    {}
func (*NextExpellingParasite) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{9}
}

func (m *NextExpellingParasite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextExpellingParasite.Unmarshal(m, b)
}
func (m *NextExpellingParasite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextExpellingParasite.Marshal(b, m, deterministic)
}
func (m *NextExpellingParasite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextExpellingParasite.Merge(m, src)
}
func (m *NextExpellingParasite) XXX_Size() int {
	return xxx_messageInfo_NextExpellingParasite.Size(m)
}
func (m *NextExpellingParasite) XXX_DiscardUnknown() {
	xxx_messageInfo_NextExpellingParasite.DiscardUnknown(m)
}

var xxx_messageInfo_NextExpellingParasite proto.InternalMessageInfo

func (m *NextExpellingParasite) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *NextExpellingParasite) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

type MedRecordListReq struct {
	//宠物id
	PetId string `protobuf:"bytes,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//当前页数
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页显示的数量
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MedRecordListReq) Reset()         { *m = MedRecordListReq{} }
func (m *MedRecordListReq) String() string { return proto.CompactTextString(m) }
func (*MedRecordListReq) ProtoMessage()    {}
func (*MedRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{10}
}

func (m *MedRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedRecordListReq.Unmarshal(m, b)
}
func (m *MedRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedRecordListReq.Marshal(b, m, deterministic)
}
func (m *MedRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedRecordListReq.Merge(m, src)
}
func (m *MedRecordListReq) XXX_Size() int {
	return xxx_messageInfo_MedRecordListReq.Size(m)
}
func (m *MedRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MedRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MedRecordListReq proto.InternalMessageInfo

func (m *MedRecordListReq) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *MedRecordListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MedRecordListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type MedRecordListRes struct {
	Total                int32            `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	PageIndex            int32            `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32            `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Data                 []*MedRecordList `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MedRecordListRes) Reset()         { *m = MedRecordListRes{} }
func (m *MedRecordListRes) String() string { return proto.CompactTextString(m) }
func (*MedRecordListRes) ProtoMessage()    {}
func (*MedRecordListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{11}
}

func (m *MedRecordListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedRecordListRes.Unmarshal(m, b)
}
func (m *MedRecordListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedRecordListRes.Marshal(b, m, deterministic)
}
func (m *MedRecordListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedRecordListRes.Merge(m, src)
}
func (m *MedRecordListRes) XXX_Size() int {
	return xxx_messageInfo_MedRecordListRes.Size(m)
}
func (m *MedRecordListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MedRecordListRes.DiscardUnknown(m)
}

var xxx_messageInfo_MedRecordListRes proto.InternalMessageInfo

func (m *MedRecordListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *MedRecordListRes) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MedRecordListRes) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *MedRecordListRes) GetData() []*MedRecordList {
	if m != nil {
		return m.Data
	}
	return nil
}

type MedRecordList struct {
	// 机构id
	Orgid int64 `protobuf:"varint,1,opt,name=orgid,proto3" json:"orgid"`
	// 挂号id
	RegId int64 `protobuf:"varint,2,opt,name=reg_id,json=regId,proto3" json:"reg_id"`
	// 病例类型 【0-门诊；1-住院】
	MedType int32 `protobuf:"varint,3,opt,name=med_type,json=medType,proto3" json:"med_type"`
	// 医生id
	PhysicianId int64 `protobuf:"varint,4,opt,name=physician_id,json=physicianId,proto3" json:"physician_id"`
	// 医生名称
	PhysicianName string `protobuf:"bytes,5,opt,name=physician_name,json=physicianName,proto3" json:"physician_name"`
	// 客户id
	CusLogicid int64 `protobuf:"varint,6,opt,name=cus_logicid,json=cusLogicid,proto3" json:"cus_logicid"`
	// 客户名称
	CusName string `protobuf:"bytes,7,opt,name=cus_name,json=cusName,proto3" json:"cus_name"`
	//  宠物id
	PetLogicid int64 `protobuf:"varint,8,opt,name=pet_logicid,json=petLogicid,proto3" json:"pet_logicid"`
	// 宠物名称
	PetName string `protobuf:"bytes,9,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	// 开始时间
	CreateTime string `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 挂号类型【1020 初诊； 1021-复诊】
	RecordType int32 `protobuf:"varint,12,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	// 机构名称
	HospitalName string `protobuf:"bytes,13,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	// 挂号类型文本
	RecordTypeText string `protobuf:"bytes,14,opt,name=record_type_text,json=recordTypeText,proto3" json:"record_type_text"`
	// 诊断信息
	MainSymptom          string   `protobuf:"bytes,15,opt,name=main_symptom,json=mainSymptom,proto3" json:"main_symptom"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MedRecordList) Reset()         { *m = MedRecordList{} }
func (m *MedRecordList) String() string { return proto.CompactTextString(m) }
func (*MedRecordList) ProtoMessage()    {}
func (*MedRecordList) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{12}
}

func (m *MedRecordList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedRecordList.Unmarshal(m, b)
}
func (m *MedRecordList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedRecordList.Marshal(b, m, deterministic)
}
func (m *MedRecordList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedRecordList.Merge(m, src)
}
func (m *MedRecordList) XXX_Size() int {
	return xxx_messageInfo_MedRecordList.Size(m)
}
func (m *MedRecordList) XXX_DiscardUnknown() {
	xxx_messageInfo_MedRecordList.DiscardUnknown(m)
}

var xxx_messageInfo_MedRecordList proto.InternalMessageInfo

func (m *MedRecordList) GetOrgid() int64 {
	if m != nil {
		return m.Orgid
	}
	return 0
}

func (m *MedRecordList) GetRegId() int64 {
	if m != nil {
		return m.RegId
	}
	return 0
}

func (m *MedRecordList) GetMedType() int32 {
	if m != nil {
		return m.MedType
	}
	return 0
}

func (m *MedRecordList) GetPhysicianId() int64 {
	if m != nil {
		return m.PhysicianId
	}
	return 0
}

func (m *MedRecordList) GetPhysicianName() string {
	if m != nil {
		return m.PhysicianName
	}
	return ""
}

func (m *MedRecordList) GetCusLogicid() int64 {
	if m != nil {
		return m.CusLogicid
	}
	return 0
}

func (m *MedRecordList) GetCusName() string {
	if m != nil {
		return m.CusName
	}
	return ""
}

func (m *MedRecordList) GetPetLogicid() int64 {
	if m != nil {
		return m.PetLogicid
	}
	return 0
}

func (m *MedRecordList) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *MedRecordList) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MedRecordList) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *MedRecordList) GetRecordType() int32 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *MedRecordList) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *MedRecordList) GetRecordTypeText() string {
	if m != nil {
		return m.RecordTypeText
	}
	return ""
}

func (m *MedRecordList) GetMainSymptom() string {
	if m != nil {
		return m.MainSymptom
	}
	return ""
}

type MedRecordInfoReq struct {
	//挂号id
	RegId int64 `protobuf:"varint,1,opt,name=reg_id,json=regId,proto3" json:"reg_id"`
	//当前病例的机构id
	Orgid                int32    `protobuf:"varint,2,opt,name=orgid,proto3" json:"orgid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MedRecordInfoReq) Reset()         { *m = MedRecordInfoReq{} }
func (m *MedRecordInfoReq) String() string { return proto.CompactTextString(m) }
func (*MedRecordInfoReq) ProtoMessage()    {}
func (*MedRecordInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{13}
}

func (m *MedRecordInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedRecordInfoReq.Unmarshal(m, b)
}
func (m *MedRecordInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedRecordInfoReq.Marshal(b, m, deterministic)
}
func (m *MedRecordInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedRecordInfoReq.Merge(m, src)
}
func (m *MedRecordInfoReq) XXX_Size() int {
	return xxx_messageInfo_MedRecordInfoReq.Size(m)
}
func (m *MedRecordInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MedRecordInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_MedRecordInfoReq proto.InternalMessageInfo

func (m *MedRecordInfoReq) GetRegId() int64 {
	if m != nil {
		return m.RegId
	}
	return 0
}

func (m *MedRecordInfoReq) GetOrgid() int32 {
	if m != nil {
		return m.Orgid
	}
	return 0
}

type MedRecordInfoRes struct {
	// 机构id
	Orgid int64 `protobuf:"varint,1,opt,name=orgid,proto3" json:"orgid"`
	// 病例分类 【0 门诊； 1 住院】
	MedType string `protobuf:"bytes,2,opt,name=med_type,json=medType,proto3" json:"med_type"`
	// 挂号id
	RegId int64 `protobuf:"varint,3,opt,name=reg_id,json=regId,proto3" json:"reg_id"`
	// 医生id
	PhysicianId int64 `protobuf:"varint,4,opt,name=physician_id,json=physicianId,proto3" json:"physician_id"`
	// 医生名称
	PhysicianName string `protobuf:"bytes,5,opt,name=physician_name,json=physicianName,proto3" json:"physician_name"`
	// 诊断
	MainSymptom string `protobuf:"bytes,6,opt,name=main_symptom,json=mainSymptom,proto3" json:"main_symptom"`
	// 客户id
	CusLogicid int64 `protobuf:"varint,7,opt,name=cus_logicid,json=cusLogicid,proto3" json:"cus_logicid"`
	// 客户名称
	CusName string `protobuf:"bytes,8,opt,name=cus_name,json=cusName,proto3" json:"cus_name"`
	// 宠物名称
	PetName string `protobuf:"bytes,9,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	// 宠物id
	PetLogicid int64 `protobuf:"varint,10,opt,name=pet_logicid,json=petLogicid,proto3" json:"pet_logicid"`
	// 开始时间
	CreateTime string `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,12,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 主诉
	ChiefComplaint string `protobuf:"bytes,13,opt,name=chief_complaint,json=chiefComplaint,proto3" json:"chief_complaint"`
	// 既往史
	PastHistory string `protobuf:"bytes,14,opt,name=past_history,json=pastHistory,proto3" json:"past_history"`
	//体况描述
	PhysicalDescription string `protobuf:"bytes,15,opt,name=physical_description,json=physicalDescription,proto3" json:"physical_description"`
	// 治疗计划
	TreatmentOpinion string `protobuf:"bytes,16,opt,name=treatment_opinion,json=treatmentOpinion,proto3" json:"treatment_opinion"`
	// 医嘱
	DoctorAdvice string `protobuf:"bytes,17,opt,name=doctor_advice,json=doctorAdvice,proto3" json:"doctor_advice"`
	// 医院名称
	HospitalName string `protobuf:"bytes,18,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	// 病例等级
	MedLevelText string `protobuf:"bytes,19,opt,name=med_level_text,json=medLevelText,proto3" json:"med_level_text"`
	// 病情紧急程度
	DiseaseUrgencyText string `protobuf:"bytes,20,opt,name=disease_urgency_text,json=diseaseUrgencyText,proto3" json:"disease_urgency_text"`
	// 体况等级
	PhysicalLevelText string `protobuf:"bytes,21,opt,name=physical_level_text,json=physicalLevelText,proto3" json:"physical_level_text"`
	// 治疗结果
	MedResultText string `protobuf:"bytes,22,opt,name=med_result_text,json=medResultText,proto3" json:"med_result_text"`
	// 出院诊断
	LeaveSymptom string `protobuf:"bytes,23,opt,name=leave_symptom,json=leaveSymptom,proto3" json:"leave_symptom"`
	// 病例分类 【0 门诊； 1 住院】
	MedTypeText string `protobuf:"bytes,24,opt,name=med_type_text,json=medTypeText,proto3" json:"med_type_text"`
	// 目前体况
	IllnessDesc string `protobuf:"bytes,25,opt,name=illness_desc,json=illnessDesc,proto3" json:"illness_desc"`
	// 住院病例描述
	PhysicalDesc        string                 `protobuf:"bytes,26,opt,name=physical_desc,json=physicalDesc,proto3" json:"physical_desc"`
	PhysicalExamination []*PhysicalExamination `protobuf:"bytes,27,rep,name=physical_examination,json=physicalExamination,proto3" json:"physical_examination"`
	MedMedias           []*MedMedias           `protobuf:"bytes,28,rep,name=med_medias,json=medMedias,proto3" json:"med_medias"`
	//具体治疗
	SpecificTreatments []*SpecificTreatments `protobuf:"bytes,29,rep,name=specific_treatments,json=specificTreatments,proto3" json:"specific_treatments"`
	//宠物信息
	PetInfo *MdPetInfo `protobuf:"bytes,30,opt,name=pet_info,json=petInfo,proto3" json:"pet_info"`
	//住院医嘱
	InpatientDoctorAdvice string   `protobuf:"bytes,31,opt,name=inpatient_doctor_advice,json=inpatientDoctorAdvice,proto3" json:"inpatient_doctor_advice"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *MedRecordInfoRes) Reset()         { *m = MedRecordInfoRes{} }
func (m *MedRecordInfoRes) String() string { return proto.CompactTextString(m) }
func (*MedRecordInfoRes) ProtoMessage()    {}
func (*MedRecordInfoRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{14}
}

func (m *MedRecordInfoRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedRecordInfoRes.Unmarshal(m, b)
}
func (m *MedRecordInfoRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedRecordInfoRes.Marshal(b, m, deterministic)
}
func (m *MedRecordInfoRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedRecordInfoRes.Merge(m, src)
}
func (m *MedRecordInfoRes) XXX_Size() int {
	return xxx_messageInfo_MedRecordInfoRes.Size(m)
}
func (m *MedRecordInfoRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MedRecordInfoRes.DiscardUnknown(m)
}

var xxx_messageInfo_MedRecordInfoRes proto.InternalMessageInfo

func (m *MedRecordInfoRes) GetOrgid() int64 {
	if m != nil {
		return m.Orgid
	}
	return 0
}

func (m *MedRecordInfoRes) GetMedType() string {
	if m != nil {
		return m.MedType
	}
	return ""
}

func (m *MedRecordInfoRes) GetRegId() int64 {
	if m != nil {
		return m.RegId
	}
	return 0
}

func (m *MedRecordInfoRes) GetPhysicianId() int64 {
	if m != nil {
		return m.PhysicianId
	}
	return 0
}

func (m *MedRecordInfoRes) GetPhysicianName() string {
	if m != nil {
		return m.PhysicianName
	}
	return ""
}

func (m *MedRecordInfoRes) GetMainSymptom() string {
	if m != nil {
		return m.MainSymptom
	}
	return ""
}

func (m *MedRecordInfoRes) GetCusLogicid() int64 {
	if m != nil {
		return m.CusLogicid
	}
	return 0
}

func (m *MedRecordInfoRes) GetCusName() string {
	if m != nil {
		return m.CusName
	}
	return ""
}

func (m *MedRecordInfoRes) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *MedRecordInfoRes) GetPetLogicid() int64 {
	if m != nil {
		return m.PetLogicid
	}
	return 0
}

func (m *MedRecordInfoRes) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MedRecordInfoRes) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *MedRecordInfoRes) GetChiefComplaint() string {
	if m != nil {
		return m.ChiefComplaint
	}
	return ""
}

func (m *MedRecordInfoRes) GetPastHistory() string {
	if m != nil {
		return m.PastHistory
	}
	return ""
}

func (m *MedRecordInfoRes) GetPhysicalDescription() string {
	if m != nil {
		return m.PhysicalDescription
	}
	return ""
}

func (m *MedRecordInfoRes) GetTreatmentOpinion() string {
	if m != nil {
		return m.TreatmentOpinion
	}
	return ""
}

func (m *MedRecordInfoRes) GetDoctorAdvice() string {
	if m != nil {
		return m.DoctorAdvice
	}
	return ""
}

func (m *MedRecordInfoRes) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *MedRecordInfoRes) GetMedLevelText() string {
	if m != nil {
		return m.MedLevelText
	}
	return ""
}

func (m *MedRecordInfoRes) GetDiseaseUrgencyText() string {
	if m != nil {
		return m.DiseaseUrgencyText
	}
	return ""
}

func (m *MedRecordInfoRes) GetPhysicalLevelText() string {
	if m != nil {
		return m.PhysicalLevelText
	}
	return ""
}

func (m *MedRecordInfoRes) GetMedResultText() string {
	if m != nil {
		return m.MedResultText
	}
	return ""
}

func (m *MedRecordInfoRes) GetLeaveSymptom() string {
	if m != nil {
		return m.LeaveSymptom
	}
	return ""
}

func (m *MedRecordInfoRes) GetMedTypeText() string {
	if m != nil {
		return m.MedTypeText
	}
	return ""
}

func (m *MedRecordInfoRes) GetIllnessDesc() string {
	if m != nil {
		return m.IllnessDesc
	}
	return ""
}

func (m *MedRecordInfoRes) GetPhysicalDesc() string {
	if m != nil {
		return m.PhysicalDesc
	}
	return ""
}

func (m *MedRecordInfoRes) GetPhysicalExamination() []*PhysicalExamination {
	if m != nil {
		return m.PhysicalExamination
	}
	return nil
}

func (m *MedRecordInfoRes) GetMedMedias() []*MedMedias {
	if m != nil {
		return m.MedMedias
	}
	return nil
}

func (m *MedRecordInfoRes) GetSpecificTreatments() []*SpecificTreatments {
	if m != nil {
		return m.SpecificTreatments
	}
	return nil
}

func (m *MedRecordInfoRes) GetPetInfo() *MdPetInfo {
	if m != nil {
		return m.PetInfo
	}
	return nil
}

func (m *MedRecordInfoRes) GetInpatientDoctorAdvice() string {
	if m != nil {
		return m.InpatientDoctorAdvice
	}
	return ""
}

type PhysicalExamination struct {
	// 体重
	Weight string `protobuf:"bytes,1,opt,name=weight,proto3" json:"weight"`
	// 温度
	Temperature string `protobuf:"bytes,2,opt,name=temperature,proto3" json:"temperature"`
	// 呼吸频率
	BreathingRate string `protobuf:"bytes,3,opt,name=breathing_rate,json=breathingRate,proto3" json:"breathing_rate"`
	// 心跳
	HeartRate            string   `protobuf:"bytes,4,opt,name=heart_rate,json=heartRate,proto3" json:"heart_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhysicalExamination) Reset()         { *m = PhysicalExamination{} }
func (m *PhysicalExamination) String() string { return proto.CompactTextString(m) }
func (*PhysicalExamination) ProtoMessage()    {}
func (*PhysicalExamination) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{15}
}

func (m *PhysicalExamination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhysicalExamination.Unmarshal(m, b)
}
func (m *PhysicalExamination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhysicalExamination.Marshal(b, m, deterministic)
}
func (m *PhysicalExamination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhysicalExamination.Merge(m, src)
}
func (m *PhysicalExamination) XXX_Size() int {
	return xxx_messageInfo_PhysicalExamination.Size(m)
}
func (m *PhysicalExamination) XXX_DiscardUnknown() {
	xxx_messageInfo_PhysicalExamination.DiscardUnknown(m)
}

var xxx_messageInfo_PhysicalExamination proto.InternalMessageInfo

func (m *PhysicalExamination) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *PhysicalExamination) GetTemperature() string {
	if m != nil {
		return m.Temperature
	}
	return ""
}

func (m *PhysicalExamination) GetBreathingRate() string {
	if m != nil {
		return m.BreathingRate
	}
	return ""
}

func (m *PhysicalExamination) GetHeartRate() string {
	if m != nil {
		return m.HeartRate
	}
	return ""
}

type MedMedias struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PicUrl               string   `protobuf:"bytes,2,opt,name=pic_url,json=picUrl,proto3" json:"pic_url"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MedMedias) Reset()         { *m = MedMedias{} }
func (m *MedMedias) String() string { return proto.CompactTextString(m) }
func (*MedMedias) ProtoMessage()    {}
func (*MedMedias) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{16}
}

func (m *MedMedias) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedMedias.Unmarshal(m, b)
}
func (m *MedMedias) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedMedias.Marshal(b, m, deterministic)
}
func (m *MedMedias) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedMedias.Merge(m, src)
}
func (m *MedMedias) XXX_Size() int {
	return xxx_messageInfo_MedMedias.Size(m)
}
func (m *MedMedias) XXX_DiscardUnknown() {
	xxx_messageInfo_MedMedias.DiscardUnknown(m)
}

var xxx_messageInfo_MedMedias proto.InternalMessageInfo

func (m *MedMedias) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MedMedias) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *MedMedias) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type SpecificTreatments struct {
	// 开单医生id
	PhysicianId int64 `protobuf:"varint,1,opt,name=physician_id,json=physicianId,proto3" json:"physician_id"`
	// 开单医生名称
	PhysicianName string `protobuf:"bytes,2,opt,name=physician_name,json=physicianName,proto3" json:"physician_name"`
	// 执行医生id
	ExecPhysicianId string `protobuf:"bytes,3,opt,name=exec_physician_id,json=execPhysicianId,proto3" json:"exec_physician_id"`
	// 执行医生名称
	ExecPhysicianName string `protobuf:"bytes,4,opt,name=exec_physician_name,json=execPhysicianName,proto3" json:"exec_physician_name"`
	// 治疗时间
	InsertTime string `protobuf:"bytes,15,opt,name=insert_time,json=insertTime,proto3" json:"insert_time"`
	// 病情描述
	Description          string   `protobuf:"bytes,16,opt,name=description,proto3" json:"description"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SpecificTreatments) Reset()         { *m = SpecificTreatments{} }
func (m *SpecificTreatments) String() string { return proto.CompactTextString(m) }
func (*SpecificTreatments) ProtoMessage()    {}
func (*SpecificTreatments) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{17}
}

func (m *SpecificTreatments) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpecificTreatments.Unmarshal(m, b)
}
func (m *SpecificTreatments) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpecificTreatments.Marshal(b, m, deterministic)
}
func (m *SpecificTreatments) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpecificTreatments.Merge(m, src)
}
func (m *SpecificTreatments) XXX_Size() int {
	return xxx_messageInfo_SpecificTreatments.Size(m)
}
func (m *SpecificTreatments) XXX_DiscardUnknown() {
	xxx_messageInfo_SpecificTreatments.DiscardUnknown(m)
}

var xxx_messageInfo_SpecificTreatments proto.InternalMessageInfo

func (m *SpecificTreatments) GetPhysicianId() int64 {
	if m != nil {
		return m.PhysicianId
	}
	return 0
}

func (m *SpecificTreatments) GetPhysicianName() string {
	if m != nil {
		return m.PhysicianName
	}
	return ""
}

func (m *SpecificTreatments) GetExecPhysicianId() string {
	if m != nil {
		return m.ExecPhysicianId
	}
	return ""
}

func (m *SpecificTreatments) GetExecPhysicianName() string {
	if m != nil {
		return m.ExecPhysicianName
	}
	return ""
}

func (m *SpecificTreatments) GetInsertTime() string {
	if m != nil {
		return m.InsertTime
	}
	return ""
}

func (m *SpecificTreatments) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

type MdPetInfo struct {
	//宠物名称
	PetName string `protobuf:"bytes,1,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物ID
	PetLogicid int64 `protobuf:"varint,2,opt,name=pet_logicid,json=petLogicid,proto3" json:"pet_logicid"`
	//宠物性别
	PetGenderText string `protobuf:"bytes,3,opt,name=pet_gender_text,json=petGenderText,proto3" json:"pet_gender_text"`
	//宠物品种
	PetKindofText string `protobuf:"bytes,5,opt,name=pet_kindof_text,json=petKindofText,proto3" json:"pet_kindof_text"`
	//宠物种类
	PetVarietyText string `protobuf:"bytes,6,opt,name=pet_variety_text,json=petVarietyText,proto3" json:"pet_variety_text"`
	//宠物颜色
	ColorTxt string `protobuf:"bytes,7,opt,name=color_txt,json=colorTxt,proto3" json:"color_txt"`
	//宠物年龄
	PetAge string `protobuf:"bytes,8,opt,name=pet_age,json=petAge,proto3" json:"pet_age"`
	//宠物体重
	PetWeight float32 `protobuf:"fixed32,9,opt,name=pet_weight,json=petWeight,proto3" json:"pet_weight"`
	//绝育状态
	PetNeuteringText string `protobuf:"bytes,10,opt,name=pet_neutering_text,json=petNeuteringText,proto3" json:"pet_neutering_text"`
	//宠物状态
	PetStatusText string `protobuf:"bytes,11,opt,name=pet_status_text,json=petStatusText,proto3" json:"pet_status_text"`
	//接种状态
	IsVaccinatedTxt string `protobuf:"bytes,12,opt,name=is_vaccinated_txt,json=isVaccinatedTxt,proto3" json:"is_vaccinated_txt"`
	//驱虫状态
	IsDewormingTxt       string   `protobuf:"bytes,13,opt,name=is_deworming_txt,json=isDewormingTxt,proto3" json:"is_deworming_txt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MdPetInfo) Reset()         { *m = MdPetInfo{} }
func (m *MdPetInfo) String() string { return proto.CompactTextString(m) }
func (*MdPetInfo) ProtoMessage()    {}
func (*MdPetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{18}
}

func (m *MdPetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MdPetInfo.Unmarshal(m, b)
}
func (m *MdPetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MdPetInfo.Marshal(b, m, deterministic)
}
func (m *MdPetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MdPetInfo.Merge(m, src)
}
func (m *MdPetInfo) XXX_Size() int {
	return xxx_messageInfo_MdPetInfo.Size(m)
}
func (m *MdPetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MdPetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MdPetInfo proto.InternalMessageInfo

func (m *MdPetInfo) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *MdPetInfo) GetPetLogicid() int64 {
	if m != nil {
		return m.PetLogicid
	}
	return 0
}

func (m *MdPetInfo) GetPetGenderText() string {
	if m != nil {
		return m.PetGenderText
	}
	return ""
}

func (m *MdPetInfo) GetPetKindofText() string {
	if m != nil {
		return m.PetKindofText
	}
	return ""
}

func (m *MdPetInfo) GetPetVarietyText() string {
	if m != nil {
		return m.PetVarietyText
	}
	return ""
}

func (m *MdPetInfo) GetColorTxt() string {
	if m != nil {
		return m.ColorTxt
	}
	return ""
}

func (m *MdPetInfo) GetPetAge() string {
	if m != nil {
		return m.PetAge
	}
	return ""
}

func (m *MdPetInfo) GetPetWeight() float32 {
	if m != nil {
		return m.PetWeight
	}
	return 0
}

func (m *MdPetInfo) GetPetNeuteringText() string {
	if m != nil {
		return m.PetNeuteringText
	}
	return ""
}

func (m *MdPetInfo) GetPetStatusText() string {
	if m != nil {
		return m.PetStatusText
	}
	return ""
}

func (m *MdPetInfo) GetIsVaccinatedTxt() string {
	if m != nil {
		return m.IsVaccinatedTxt
	}
	return ""
}

func (m *MdPetInfo) GetIsDewormingTxt() string {
	if m != nil {
		return m.IsDewormingTxt
	}
	return ""
}

type CreateOrUpdateRecordReq struct {
	// 记录id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	// 接种日期
	OperationDate string `protobuf:"bytes,3,opt,name=operation_date,json=operationDate,proto3" json:"operation_date"`
	// 接诊年份
	OperationYear string `protobuf:"bytes,4,opt,name=operation_year,json=operationYear,proto3" json:"operation_year"`
	// 接种机构
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	// 产品名称
	ProductName string `protobuf:"bytes,6,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 记录类型 1疫苗记录 2驱虫记录 3口腔 4体检  5洗护 6体况评分 7三围 8体重
	RecordType int64 `protobuf:"varint,7,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	//记录拍照
	RecordPhoto          string   `protobuf:"bytes,8,opt,name=record_photo,json=recordPhoto,proto3" json:"record_photo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateOrUpdateRecordReq) Reset()         { *m = CreateOrUpdateRecordReq{} }
func (m *CreateOrUpdateRecordReq) String() string { return proto.CompactTextString(m) }
func (*CreateOrUpdateRecordReq) ProtoMessage()    {}
func (*CreateOrUpdateRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{19}
}

func (m *CreateOrUpdateRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOrUpdateRecordReq.Unmarshal(m, b)
}
func (m *CreateOrUpdateRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOrUpdateRecordReq.Marshal(b, m, deterministic)
}
func (m *CreateOrUpdateRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOrUpdateRecordReq.Merge(m, src)
}
func (m *CreateOrUpdateRecordReq) XXX_Size() int {
	return xxx_messageInfo_CreateOrUpdateRecordReq.Size(m)
}
func (m *CreateOrUpdateRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOrUpdateRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOrUpdateRecordReq proto.InternalMessageInfo

func (m *CreateOrUpdateRecordReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CreateOrUpdateRecordReq) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *CreateOrUpdateRecordReq) GetOperationDate() string {
	if m != nil {
		return m.OperationDate
	}
	return ""
}

func (m *CreateOrUpdateRecordReq) GetOperationYear() string {
	if m != nil {
		return m.OperationYear
	}
	return ""
}

func (m *CreateOrUpdateRecordReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *CreateOrUpdateRecordReq) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CreateOrUpdateRecordReq) GetRecordType() int64 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *CreateOrUpdateRecordReq) GetRecordPhoto() string {
	if m != nil {
		return m.RecordPhoto
	}
	return ""
}

type DeleteRecordReq struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRecordReq) Reset()         { *m = DeleteRecordReq{} }
func (m *DeleteRecordReq) String() string { return proto.CompactTextString(m) }
func (*DeleteRecordReq) ProtoMessage()    {}
func (*DeleteRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{20}
}

func (m *DeleteRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRecordReq.Unmarshal(m, b)
}
func (m *DeleteRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRecordReq.Marshal(b, m, deterministic)
}
func (m *DeleteRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRecordReq.Merge(m, src)
}
func (m *DeleteRecordReq) XXX_Size() int {
	return xxx_messageInfo_DeleteRecordReq.Size(m)
}
func (m *DeleteRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRecordReq proto.InternalMessageInfo

func (m *DeleteRecordReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type PetBaseResponse struct {
	Code                 int64    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetBaseResponse) Reset()         { *m = PetBaseResponse{} }
func (m *PetBaseResponse) String() string { return proto.CompactTextString(m) }
func (*PetBaseResponse) ProtoMessage()    {}
func (*PetBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{21}
}

func (m *PetBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetBaseResponse.Unmarshal(m, b)
}
func (m *PetBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetBaseResponse.Marshal(b, m, deterministic)
}
func (m *PetBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetBaseResponse.Merge(m, src)
}
func (m *PetBaseResponse) XXX_Size() int {
	return xxx_messageInfo_PetBaseResponse.Size(m)
}
func (m *PetBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetBaseResponse proto.InternalMessageInfo

func (m *PetBaseResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PetBaseResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

// 新版宠物接口返回
type PetListRes struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetListRes) Reset()         { *m = PetListRes{} }
func (m *PetListRes) String() string { return proto.CompactTextString(m) }
func (*PetListRes) ProtoMessage()    {}
func (*PetListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{22}
}

func (m *PetListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetListRes.Unmarshal(m, b)
}
func (m *PetListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetListRes.Marshal(b, m, deterministic)
}
func (m *PetListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetListRes.Merge(m, src)
}
func (m *PetListRes) XXX_Size() int {
	return xxx_messageInfo_PetListRes.Size(m)
}
func (m *PetListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PetListRes.DiscardUnknown(m)
}

var xxx_messageInfo_PetListRes proto.InternalMessageInfo

type PetRecordListReq struct {
	PetId                string   `protobuf:"bytes,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	RecordType           int64    `protobuf:"varint,2,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetRecordListReq) Reset()         { *m = PetRecordListReq{} }
func (m *PetRecordListReq) String() string { return proto.CompactTextString(m) }
func (*PetRecordListReq) ProtoMessage()    {}
func (*PetRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{23}
}

func (m *PetRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRecordListReq.Unmarshal(m, b)
}
func (m *PetRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRecordListReq.Marshal(b, m, deterministic)
}
func (m *PetRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRecordListReq.Merge(m, src)
}
func (m *PetRecordListReq) XXX_Size() int {
	return xxx_messageInfo_PetRecordListReq.Size(m)
}
func (m *PetRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PetRecordListReq proto.InternalMessageInfo

func (m *PetRecordListReq) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetRecordListReq) GetRecordType() int64 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

type PetRecordListBaseResponse struct {
	Code                 int64               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 []*PetRecordListRes `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int32               `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PetRecordListBaseResponse) Reset()         { *m = PetRecordListBaseResponse{} }
func (m *PetRecordListBaseResponse) String() string { return proto.CompactTextString(m) }
func (*PetRecordListBaseResponse) ProtoMessage()    {}
func (*PetRecordListBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{24}
}

func (m *PetRecordListBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRecordListBaseResponse.Unmarshal(m, b)
}
func (m *PetRecordListBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRecordListBaseResponse.Marshal(b, m, deterministic)
}
func (m *PetRecordListBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRecordListBaseResponse.Merge(m, src)
}
func (m *PetRecordListBaseResponse) XXX_Size() int {
	return xxx_messageInfo_PetRecordListBaseResponse.Size(m)
}
func (m *PetRecordListBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRecordListBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetRecordListBaseResponse proto.InternalMessageInfo

func (m *PetRecordListBaseResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetRecordListBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PetRecordListBaseResponse) GetData() []*PetRecordListRes {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PetRecordListBaseResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type NewPetRecordDateBaseResponse struct {
	Code                 int64                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 []*NewPetRecordDateRes `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *NewPetRecordDateBaseResponse) Reset()         { *m = NewPetRecordDateBaseResponse{} }
func (m *NewPetRecordDateBaseResponse) String() string { return proto.CompactTextString(m) }
func (*NewPetRecordDateBaseResponse) ProtoMessage()    {}
func (*NewPetRecordDateBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{25}
}

func (m *NewPetRecordDateBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPetRecordDateBaseResponse.Unmarshal(m, b)
}
func (m *NewPetRecordDateBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPetRecordDateBaseResponse.Marshal(b, m, deterministic)
}
func (m *NewPetRecordDateBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPetRecordDateBaseResponse.Merge(m, src)
}
func (m *NewPetRecordDateBaseResponse) XXX_Size() int {
	return xxx_messageInfo_NewPetRecordDateBaseResponse.Size(m)
}
func (m *NewPetRecordDateBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPetRecordDateBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPetRecordDateBaseResponse proto.InternalMessageInfo

func (m *NewPetRecordDateBaseResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPetRecordDateBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *NewPetRecordDateBaseResponse) GetData() []*NewPetRecordDateRes {
	if m != nil {
		return m.Data
	}
	return nil
}

type PetRecordDateBaseResponse struct {
	Code                 int64             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 *PetRecordDateRes `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PetRecordDateBaseResponse) Reset()         { *m = PetRecordDateBaseResponse{} }
func (m *PetRecordDateBaseResponse) String() string { return proto.CompactTextString(m) }
func (*PetRecordDateBaseResponse) ProtoMessage()    {}
func (*PetRecordDateBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{26}
}

func (m *PetRecordDateBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRecordDateBaseResponse.Unmarshal(m, b)
}
func (m *PetRecordDateBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRecordDateBaseResponse.Marshal(b, m, deterministic)
}
func (m *PetRecordDateBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRecordDateBaseResponse.Merge(m, src)
}
func (m *PetRecordDateBaseResponse) XXX_Size() int {
	return xxx_messageInfo_PetRecordDateBaseResponse.Size(m)
}
func (m *PetRecordDateBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRecordDateBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetRecordDateBaseResponse proto.InternalMessageInfo

func (m *PetRecordDateBaseResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetRecordDateBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PetRecordDateBaseResponse) GetData() *PetRecordDateRes {
	if m != nil {
		return m.Data
	}
	return nil
}

type NewPetRecordDateRes struct {
	//创建时间
	CreateTime string `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//体况拍照记录
	RecordPhoto string `protobuf:"bytes,2,opt,name=record_photo,json=recordPhoto,proto3" json:"record_photo"`
	//产品名称/BCS评分/三围/体重
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//接种日期/驱虫日期
	OperationDate string `protobuf:"bytes,4,opt,name=operation_date,json=operationDate,proto3" json:"operation_date"`
	//记录类型 0 绝育  1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重
	RecordType int64 `protobuf:"varint,5,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	//红点展示
	Show bool `protobuf:"varint,6,opt,name=show,proto3" json:"show"`
	//距离3天提醒
	Remind bool `protobuf:"varint,7,opt,name=remind,proto3" json:"remind"`
	//天数
	DayNum               int32    `protobuf:"varint,8,opt,name=day_num,json=dayNum,proto3" json:"day_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPetRecordDateRes) Reset()         { *m = NewPetRecordDateRes{} }
func (m *NewPetRecordDateRes) String() string { return proto.CompactTextString(m) }
func (*NewPetRecordDateRes) ProtoMessage()    {}
func (*NewPetRecordDateRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{27}
}

func (m *NewPetRecordDateRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPetRecordDateRes.Unmarshal(m, b)
}
func (m *NewPetRecordDateRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPetRecordDateRes.Marshal(b, m, deterministic)
}
func (m *NewPetRecordDateRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPetRecordDateRes.Merge(m, src)
}
func (m *NewPetRecordDateRes) XXX_Size() int {
	return xxx_messageInfo_NewPetRecordDateRes.Size(m)
}
func (m *NewPetRecordDateRes) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPetRecordDateRes.DiscardUnknown(m)
}

var xxx_messageInfo_NewPetRecordDateRes proto.InternalMessageInfo

func (m *NewPetRecordDateRes) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *NewPetRecordDateRes) GetRecordPhoto() string {
	if m != nil {
		return m.RecordPhoto
	}
	return ""
}

func (m *NewPetRecordDateRes) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *NewPetRecordDateRes) GetOperationDate() string {
	if m != nil {
		return m.OperationDate
	}
	return ""
}

func (m *NewPetRecordDateRes) GetRecordType() int64 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *NewPetRecordDateRes) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

func (m *NewPetRecordDateRes) GetRemind() bool {
	if m != nil {
		return m.Remind
	}
	return false
}

func (m *NewPetRecordDateRes) GetDayNum() int32 {
	if m != nil {
		return m.DayNum
	}
	return 0
}

// 记录返回
type PetRecordListRes struct {
	Id    int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	// 年
	OperationYear string `protobuf:"bytes,3,opt,name=operation_year,json=operationYear,proto3" json:"operation_year"`
	// 接诊日期
	OperationDate string `protobuf:"bytes,4,opt,name=operation_date,json=operationDate,proto3" json:"operation_date"`
	// 接诊医院
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	// 接诊商品
	ProductName string `protobuf:"bytes,6,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 记录类型 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重
	RecordType int64  `protobuf:"varint,7,opt,name=record_type,json=recordType,proto3" json:"record_type"`
	CreateTime string `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime string `protobuf:"bytes,9,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//记录拍照
	RecordPhoto          string   `protobuf:"bytes,10,opt,name=record_photo,json=recordPhoto,proto3" json:"record_photo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetRecordListRes) Reset()         { *m = PetRecordListRes{} }
func (m *PetRecordListRes) String() string { return proto.CompactTextString(m) }
func (*PetRecordListRes) ProtoMessage()    {}
func (*PetRecordListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{28}
}

func (m *PetRecordListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRecordListRes.Unmarshal(m, b)
}
func (m *PetRecordListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRecordListRes.Marshal(b, m, deterministic)
}
func (m *PetRecordListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRecordListRes.Merge(m, src)
}
func (m *PetRecordListRes) XXX_Size() int {
	return xxx_messageInfo_PetRecordListRes.Size(m)
}
func (m *PetRecordListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRecordListRes.DiscardUnknown(m)
}

var xxx_messageInfo_PetRecordListRes proto.InternalMessageInfo

func (m *PetRecordListRes) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetRecordListRes) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetRecordListRes) GetOperationYear() string {
	if m != nil {
		return m.OperationYear
	}
	return ""
}

func (m *PetRecordListRes) GetOperationDate() string {
	if m != nil {
		return m.OperationDate
	}
	return ""
}

func (m *PetRecordListRes) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *PetRecordListRes) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PetRecordListRes) GetRecordType() int64 {
	if m != nil {
		return m.RecordType
	}
	return 0
}

func (m *PetRecordListRes) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetRecordListRes) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *PetRecordListRes) GetRecordPhoto() string {
	if m != nil {
		return m.RecordPhoto
	}
	return ""
}

type PetListNewRes struct {
	Code                 int64         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 []*PetInfoNew `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PetListNewRes) Reset()         { *m = PetListNewRes{} }
func (m *PetListNewRes) String() string { return proto.CompactTextString(m) }
func (*PetListNewRes) ProtoMessage()    {}
func (*PetListNewRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{29}
}

func (m *PetListNewRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetListNewRes.Unmarshal(m, b)
}
func (m *PetListNewRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetListNewRes.Marshal(b, m, deterministic)
}
func (m *PetListNewRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetListNewRes.Merge(m, src)
}
func (m *PetListNewRes) XXX_Size() int {
	return xxx_messageInfo_PetListNewRes.Size(m)
}
func (m *PetListNewRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PetListNewRes.DiscardUnknown(m)
}

var xxx_messageInfo_PetListNewRes proto.InternalMessageInfo

func (m *PetListNewRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetListNewRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PetListNewRes) GetData() []*PetInfoNew {
	if m != nil {
		return m.Data
	}
	return nil
}

type PetInfoNew struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物名称
	PetName string `protobuf:"bytes,4,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物性别 0未知 1公 2母
	PetSex int32 `protobuf:"varint,5,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//宠物品种 -1未知 1000猫 1001狗 1002其他
	PetKindof int32 `protobuf:"varint,6,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//宠物品种 -1未知 1000猫 1001狗 1002其他
	PetKindofStr string `protobuf:"bytes,7,opt,name=pet_kindof_str,json=petKindofStr,proto3" json:"pet_kindof_str"`
	//宠物品种
	PetVariety int32 `protobuf:"varint,8,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物类别
	PetVarietyStr string `protobuf:"bytes,9,opt,name=pet_variety_str,json=petVarietyStr,proto3" json:"pet_variety_str"`
	//是否绝育 -1未知,0未绝育,1已绝育
	PetNeutering int32 `protobuf:"varint,10,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//疫苗 -1未知,0未接种,1已接种
	PetVaccinated int32 `protobuf:"varint,11,opt,name=pet_vaccinated,json=petVaccinated,proto3" json:"pet_vaccinated"`
	//驱虫  -1未知,0未驱虫,1 已驱虫
	PetDeworming int32 `protobuf:"varint,12,opt,name=pet_deworming,json=petDeworming,proto3" json:"pet_deworming"`
	//宠物体重,单位g
	PetWeight int32 `protobuf:"varint,13,opt,name=pet_weight,json=petWeight,proto3" json:"pet_weight"`
	//宠物体长,单位mm
	PetLong int32 `protobuf:"varint,14,opt,name=pet_long,json=petLong,proto3" json:"pet_long"`
	//宠物体高,单位mm
	PetHeight int32 `protobuf:"varint,15,opt,name=pet_height,json=petHeight,proto3" json:"pet_height"`
	//宠物来源
	PetSource int32 `protobuf:"varint,16,opt,name=pet_source,json=petSource,proto3" json:"pet_source"`
	//宠物状态 0正常,1死亡,2走失,4送人,8隐藏
	PetStatus int32 `protobuf:"varint,17,opt,name=pet_status,json=petStatus,proto3" json:"pet_status"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,18,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,19,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	// 到家日期
	PetHomeday string `protobuf:"bytes,20,opt,name=pet_homeday,json=petHomeday,proto3" json:"pet_homeday"`
	//备注
	PetRemark string `protobuf:"bytes,21,opt,name=pet_remark,json=petRemark,proto3" json:"pet_remark"`
	//创建时间
	CreateTime string `protobuf:"bytes,22,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//更新时间
	UpdateTime string `protobuf:"bytes,23,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//宠物faceId
	FaceId string `protobuf:"bytes,24,opt,name=face_id,json=faceId,proto3" json:"face_id"`
	// 鼻纹识别宠物code
	PetCode string `protobuf:"bytes,25,opt,name=pet_code,json=petCode,proto3" json:"pet_code"`
	// sendTime这个字段是  t_scrm_pet_photo表的create_time字段取第一个
	SendTime string `protobuf:"bytes,26,opt,name=sendTime,proto3" json:"sendTime"`
	// 保险宠物faceId
	InsuranceFace_Id string `protobuf:"bytes,27,opt,name=insurance_face_Id,json=insuranceFaceId,proto3" json:"insurance_face_Id"`
	//  这个字段其实就是判断这个宠物有没有开通宠物保障卡
	EnsureCard bool `protobuf:"varint,28,opt,name=ensure_card,json=ensureCard,proto3" json:"ensure_card"`
	// t_scrm_pet_photo表的create_time字段取第一个
	ScrmPetPhoto []string `protobuf:"bytes,29,rep,name=scrm_pet_photo,json=scrmPetPhoto,proto3" json:"scrm_pet_photo"`
	//年龄
	AgeStr string `protobuf:"bytes,30,opt,name=ageStr,proto3" json:"ageStr"`
	//年龄换算
	AgeConversionStr string `protobuf:"bytes,31,opt,name=ageConversionStr,proto3" json:"ageConversionStr"`
	//最后一次驱虫记录
	LastExpellingParasiteRecordTime string `protobuf:"bytes,32,opt,name=last_expelling_parasite_record_time,json=lastExpellingParasiteRecordTime,proto3" json:"last_expelling_parasite_record_time"`
	//最后一次免疫记录
	LastImmunityRecordTime string `protobuf:"bytes,33,opt,name=last_immunity_record_time,json=lastImmunityRecordTime,proto3" json:"last_immunity_record_time"`
	//最后一次口腔记录时间
	LastMouthRecordTime string `protobuf:"bytes,34,opt,name=last_mouth_record_time,json=lastMouthRecordTime,proto3" json:"last_mouth_record_time"`
	// 最后一次体检记录时间
	LastExaminationRecordTime string `protobuf:"bytes,35,opt,name=last_examination_record_time,json=lastExaminationRecordTime,proto3" json:"last_examination_record_time"`
	// 宠物花色
	PetFlower string `protobuf:"bytes,36,opt,name=pet_flower,json=petFlower,proto3" json:"pet_flower"`
	//花色编码
	FlowerCode           string   `protobuf:"bytes,37,opt,name=flower_code,json=flowerCode,proto3" json:"flower_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetInfoNew) Reset()         { *m = PetInfoNew{} }
func (m *PetInfoNew) String() string { return proto.CompactTextString(m) }
func (*PetInfoNew) ProtoMessage()    {}
func (*PetInfoNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{30}
}

func (m *PetInfoNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetInfoNew.Unmarshal(m, b)
}
func (m *PetInfoNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetInfoNew.Marshal(b, m, deterministic)
}
func (m *PetInfoNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetInfoNew.Merge(m, src)
}
func (m *PetInfoNew) XXX_Size() int {
	return xxx_messageInfo_PetInfoNew.Size(m)
}
func (m *PetInfoNew) XXX_DiscardUnknown() {
	xxx_messageInfo_PetInfoNew.DiscardUnknown(m)
}

var xxx_messageInfo_PetInfoNew proto.InternalMessageInfo

func (m *PetInfoNew) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetInfoNew) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetInfoNew) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetInfoNew) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *PetInfoNew) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *PetInfoNew) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

func (m *PetInfoNew) GetPetKindofStr() string {
	if m != nil {
		return m.PetKindofStr
	}
	return ""
}

func (m *PetInfoNew) GetPetVariety() int32 {
	if m != nil {
		return m.PetVariety
	}
	return 0
}

func (m *PetInfoNew) GetPetVarietyStr() string {
	if m != nil {
		return m.PetVarietyStr
	}
	return ""
}

func (m *PetInfoNew) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *PetInfoNew) GetPetVaccinated() int32 {
	if m != nil {
		return m.PetVaccinated
	}
	return 0
}

func (m *PetInfoNew) GetPetDeworming() int32 {
	if m != nil {
		return m.PetDeworming
	}
	return 0
}

func (m *PetInfoNew) GetPetWeight() int32 {
	if m != nil {
		return m.PetWeight
	}
	return 0
}

func (m *PetInfoNew) GetPetLong() int32 {
	if m != nil {
		return m.PetLong
	}
	return 0
}

func (m *PetInfoNew) GetPetHeight() int32 {
	if m != nil {
		return m.PetHeight
	}
	return 0
}

func (m *PetInfoNew) GetPetSource() int32 {
	if m != nil {
		return m.PetSource
	}
	return 0
}

func (m *PetInfoNew) GetPetStatus() int32 {
	if m != nil {
		return m.PetStatus
	}
	return 0
}

func (m *PetInfoNew) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *PetInfoNew) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *PetInfoNew) GetPetHomeday() string {
	if m != nil {
		return m.PetHomeday
	}
	return ""
}

func (m *PetInfoNew) GetPetRemark() string {
	if m != nil {
		return m.PetRemark
	}
	return ""
}

func (m *PetInfoNew) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetInfoNew) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *PetInfoNew) GetFaceId() string {
	if m != nil {
		return m.FaceId
	}
	return ""
}

func (m *PetInfoNew) GetPetCode() string {
	if m != nil {
		return m.PetCode
	}
	return ""
}

func (m *PetInfoNew) GetSendTime() string {
	if m != nil {
		return m.SendTime
	}
	return ""
}

func (m *PetInfoNew) GetInsuranceFace_Id() string {
	if m != nil {
		return m.InsuranceFace_Id
	}
	return ""
}

func (m *PetInfoNew) GetEnsureCard() bool {
	if m != nil {
		return m.EnsureCard
	}
	return false
}

func (m *PetInfoNew) GetScrmPetPhoto() []string {
	if m != nil {
		return m.ScrmPetPhoto
	}
	return nil
}

func (m *PetInfoNew) GetAgeStr() string {
	if m != nil {
		return m.AgeStr
	}
	return ""
}

func (m *PetInfoNew) GetAgeConversionStr() string {
	if m != nil {
		return m.AgeConversionStr
	}
	return ""
}

func (m *PetInfoNew) GetLastExpellingParasiteRecordTime() string {
	if m != nil {
		return m.LastExpellingParasiteRecordTime
	}
	return ""
}

func (m *PetInfoNew) GetLastImmunityRecordTime() string {
	if m != nil {
		return m.LastImmunityRecordTime
	}
	return ""
}

func (m *PetInfoNew) GetLastMouthRecordTime() string {
	if m != nil {
		return m.LastMouthRecordTime
	}
	return ""
}

func (m *PetInfoNew) GetLastExaminationRecordTime() string {
	if m != nil {
		return m.LastExaminationRecordTime
	}
	return ""
}

func (m *PetInfoNew) GetPetFlower() string {
	if m != nil {
		return m.PetFlower
	}
	return ""
}

func (m *PetInfoNew) GetFlowerCode() string {
	if m != nil {
		return m.FlowerCode
	}
	return ""
}

type PetRecordDateRes struct {
	//最后一次驱虫记录
	LastExpellingParasiteRecordTime string `protobuf:"bytes,1,opt,name=last_expelling_parasite_record_time,json=lastExpellingParasiteRecordTime,proto3" json:"last_expelling_parasite_record_time"`
	//驱虫红点显示
	ExpellingParasiteRecordShow bool `protobuf:"varint,2,opt,name=expelling_parasite_record_show,json=expellingParasiteRecordShow,proto3" json:"expelling_parasite_record_show"`
	//最后一次免疫记录
	LastImmunityRecordTime string `protobuf:"bytes,3,opt,name=last_immunity_record_time,json=lastImmunityRecordTime,proto3" json:"last_immunity_record_time"`
	//免疫红点显示
	ImmunityRecordTimeShow bool `protobuf:"varint,4,opt,name=immunity_record_time_show,json=immunityRecordTimeShow,proto3" json:"immunity_record_time_show"`
	//最后一次口腔记录时间
	LastMouthRecordTime string `protobuf:"bytes,5,opt,name=last_mouth_record_time,json=lastMouthRecordTime,proto3" json:"last_mouth_record_time"`
	//口腔红点显示
	MouthRecordShow bool `protobuf:"varint,6,opt,name=mouth_record_show,json=mouthRecordShow,proto3" json:"mouth_record_show"`
	// 最后一次体检记录时间
	LastExaminationRecordTime string `protobuf:"bytes,7,opt,name=last_examination_record_time,json=lastExaminationRecordTime,proto3" json:"last_examination_record_time"`
	//体检 -红点显示
	ExaminationRecordShow bool `protobuf:"varint,8,opt,name=examination_record_show,json=examinationRecordShow,proto3" json:"examination_record_show"`
	// 绝育红点展示
	SterilizationShow    bool     `protobuf:"varint,9,opt,name=sterilization_show,json=sterilizationShow,proto3" json:"sterilization_show"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetRecordDateRes) Reset()         { *m = PetRecordDateRes{} }
func (m *PetRecordDateRes) String() string { return proto.CompactTextString(m) }
func (*PetRecordDateRes) ProtoMessage()    {}
func (*PetRecordDateRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_bae938ea341302af, []int{31}
}

func (m *PetRecordDateRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRecordDateRes.Unmarshal(m, b)
}
func (m *PetRecordDateRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRecordDateRes.Marshal(b, m, deterministic)
}
func (m *PetRecordDateRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRecordDateRes.Merge(m, src)
}
func (m *PetRecordDateRes) XXX_Size() int {
	return xxx_messageInfo_PetRecordDateRes.Size(m)
}
func (m *PetRecordDateRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRecordDateRes.DiscardUnknown(m)
}

var xxx_messageInfo_PetRecordDateRes proto.InternalMessageInfo

func (m *PetRecordDateRes) GetLastExpellingParasiteRecordTime() string {
	if m != nil {
		return m.LastExpellingParasiteRecordTime
	}
	return ""
}

func (m *PetRecordDateRes) GetExpellingParasiteRecordShow() bool {
	if m != nil {
		return m.ExpellingParasiteRecordShow
	}
	return false
}

func (m *PetRecordDateRes) GetLastImmunityRecordTime() string {
	if m != nil {
		return m.LastImmunityRecordTime
	}
	return ""
}

func (m *PetRecordDateRes) GetImmunityRecordTimeShow() bool {
	if m != nil {
		return m.ImmunityRecordTimeShow
	}
	return false
}

func (m *PetRecordDateRes) GetLastMouthRecordTime() string {
	if m != nil {
		return m.LastMouthRecordTime
	}
	return ""
}

func (m *PetRecordDateRes) GetMouthRecordShow() bool {
	if m != nil {
		return m.MouthRecordShow
	}
	return false
}

func (m *PetRecordDateRes) GetLastExaminationRecordTime() string {
	if m != nil {
		return m.LastExaminationRecordTime
	}
	return ""
}

func (m *PetRecordDateRes) GetExaminationRecordShow() bool {
	if m != nil {
		return m.ExaminationRecordShow
	}
	return false
}

func (m *PetRecordDateRes) GetSterilizationShow() bool {
	if m != nil {
		return m.SterilizationShow
	}
	return false
}

func init() {
	proto.RegisterType((*PetListRequest)(nil), "cc.PetListRequest")
	proto.RegisterType((*PetListResponse)(nil), "cc.PetListResponse")
	proto.RegisterType((*GetPetFlowerRequest)(nil), "cc.GetPetFlowerRequest")
	proto.RegisterType((*GetPetFlowerRes)(nil), "cc.GetPetFlowerRes")
	proto.RegisterType((*PetFlowerDict)(nil), "cc.PetFlowerDict")
	proto.RegisterType((*PetInfo)(nil), "cc.PetInfo")
	proto.RegisterType((*VaccinateRecord)(nil), "cc.VaccinateRecord")
	proto.RegisterType((*LastDiagnosis)(nil), "cc.LastDiagnosis")
	proto.RegisterType((*NextImmunity)(nil), "cc.NextImmunity")
	proto.RegisterType((*NextExpellingParasite)(nil), "cc.NextExpellingParasite")
	proto.RegisterType((*MedRecordListReq)(nil), "cc.MedRecordListReq")
	proto.RegisterType((*MedRecordListRes)(nil), "cc.MedRecordListRes")
	proto.RegisterType((*MedRecordList)(nil), "cc.MedRecordList")
	proto.RegisterType((*MedRecordInfoReq)(nil), "cc.MedRecordInfoReq")
	proto.RegisterType((*MedRecordInfoRes)(nil), "cc.MedRecordInfoRes")
	proto.RegisterType((*PhysicalExamination)(nil), "cc.PhysicalExamination")
	proto.RegisterType((*MedMedias)(nil), "cc.MedMedias")
	proto.RegisterType((*SpecificTreatments)(nil), "cc.SpecificTreatments")
	proto.RegisterType((*MdPetInfo)(nil), "cc.MdPetInfo")
	proto.RegisterType((*CreateOrUpdateRecordReq)(nil), "cc.CreateOrUpdateRecordReq")
	proto.RegisterType((*DeleteRecordReq)(nil), "cc.DeleteRecordReq")
	proto.RegisterType((*PetBaseResponse)(nil), "cc.PetBaseResponse")
	proto.RegisterType((*PetListRes)(nil), "cc.PetListRes")
	proto.RegisterType((*PetRecordListReq)(nil), "cc.PetRecordListReq")
	proto.RegisterType((*PetRecordListBaseResponse)(nil), "cc.PetRecordListBaseResponse")
	proto.RegisterType((*NewPetRecordDateBaseResponse)(nil), "cc.NewPetRecordDateBaseResponse")
	proto.RegisterType((*PetRecordDateBaseResponse)(nil), "cc.PetRecordDateBaseResponse")
	proto.RegisterType((*NewPetRecordDateRes)(nil), "cc.NewPetRecordDateRes")
	proto.RegisterType((*PetRecordListRes)(nil), "cc.PetRecordListRes")
	proto.RegisterType((*PetListNewRes)(nil), "cc.PetListNewRes")
	proto.RegisterType((*PetInfoNew)(nil), "cc.PetInfoNew")
	proto.RegisterType((*PetRecordDateRes)(nil), "cc.PetRecordDateRes")
}

func init() { proto.RegisterFile("cc/pet.proto", fileDescriptor_bae938ea341302af) }

var fileDescriptor_bae938ea341302af = []byte{
	// 3081 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0x4b, 0x6f, 0x1c, 0xc7,
	0xb5, 0xc6, 0xcc, 0xf0, 0x31, 0x73, 0x66, 0x86, 0x43, 0x36, 0x45, 0x71, 0x48, 0x4a, 0x22, 0xd5,
	0x92, 0x7c, 0x09, 0xd9, 0x57, 0xf7, 0x5e, 0x19, 0xd7, 0xb0, 0x91, 0x04, 0x86, 0x4d, 0xfa, 0x41,
	0x9b, 0x96, 0x89, 0x21, 0x95, 0xc0, 0xab, 0x46, 0xbb, 0xbb, 0x38, 0x53, 0xd0, 0xf4, 0x43, 0x5d,
	0x35, 0x14, 0xe9, 0x5d, 0x16, 0x59, 0x04, 0xd9, 0x19, 0xc8, 0x3e, 0x8b, 0xec, 0xb3, 0xce, 0x36,
	0xfb, 0x20, 0xcb, 0xe4, 0x27, 0xe4, 0x4f, 0x04, 0x08, 0xce, 0x39, 0xd5, 0xef, 0x21, 0x25, 0x28,
	0xc9, 0x6e, 0xfa, 0x3b, 0x8f, 0x7a, 0x7d, 0x75, 0xea, 0x54, 0x9d, 0x81, 0x9e, 0xe7, 0xfd, 0x4f,
	0x2c, 0xf4, 0x93, 0x38, 0x89, 0x74, 0x64, 0x35, 0x3d, 0xcf, 0x3e, 0x83, 0x95, 0x13, 0xa1, 0x8f,
	0xa5, 0xd2, 0x23, 0xf1, 0x72, 0x26, 0x94, 0xb6, 0x6e, 0xc1, 0x62, 0x2c, 0xf4, 0x91, 0x3f, 0x6c,
	0xec, 0x35, 0xf6, 0x3b, 0x23, 0xfe, 0xb0, 0x6e, 0xc3, 0xd2, 0x4c, 0x89, 0xe4, 0xc8, 0x1f, 0x36,
	0x09, 0x36, 0x5f, 0xa8, 0xad, 0xa3, 0x17, 0x22, 0x1c, 0xb6, 0x58, 0x9b, 0x3e, 0xec, 0xa7, 0x30,
	0xc8, 0xbc, 0xaa, 0x38, 0x0a, 0x95, 0xb0, 0x76, 0x61, 0xc1, 0x77, 0xb5, 0x3b, 0x6c, 0xec, 0xb5,
	0xf6, 0xbb, 0x4f, 0xbb, 0x4f, 0x3c, 0xef, 0xc9, 0x89, 0xd0, 0x47, 0xe1, 0x79, 0x34, 0x22, 0x81,
	0xfd, 0x2e, 0xac, 0x7f, 0x21, 0xf4, 0x89, 0xd0, 0x9f, 0x4f, 0xa3, 0x57, 0x22, 0x29, 0x74, 0x87,
	0x1b, 0x68, 0x14, 0x1b, 0xf8, 0x10, 0x06, 0x65, 0x65, 0x65, 0x3d, 0x2a, 0x35, 0xb0, 0x66, 0x1a,
	0x60, 0xf9, 0xa1, 0xf4, 0xb4, 0x69, 0xe6, 0x2f, 0x0d, 0xe8, 0x97, 0x70, 0x6b, 0x05, 0x9a, 0x92,
	0x47, 0xbb, 0x38, 0x6a, 0x4a, 0xdf, 0xb2, 0x60, 0xc1, 0x8b, 0x7c, 0x61, 0x06, 0x4a, 0xbf, 0x11,
	0x0b, 0xdd, 0x40, 0x98, 0x51, 0xd2, 0x6f, 0x9c, 0x12, 0xa5, 0x5d, 0x3d, 0x53, 0xc3, 0x05, 0xb2,
	0x35, 0x5f, 0x88, 0x47, 0x89, 0x2f, 0x12, 0x35, 0x5c, 0x64, 0x9c, 0xbf, 0xac, 0xfb, 0xd0, 0x13,
	0xe1, 0x78, 0x2a, 0xd5, 0xc4, 0x21, 0x5f, 0x4b, 0xe4, 0xab, 0x6b, 0xb0, 0x67, 0xe8, 0xd2, 0x82,
	0x85, 0xd8, 0xd5, 0x93, 0xe1, 0x32, 0x37, 0x83, 0xbf, 0xad, 0x5d, 0xe8, 0xc6, 0x6e, 0x22, 0x42,
	0xed, 0x50, 0xaf, 0xda, 0xe4, 0x13, 0x18, 0x3a, 0x88, 0x7c, 0x61, 0xff, 0xad, 0x03, 0xcb, 0x66,
	0x2a, 0x6b, 0x63, 0xc9, 0x16, 0xb3, 0x39, 0x7f, 0x31, 0x5b, 0xa5, 0xc5, 0x1c, 0xc2, 0x72, 0x2c,
	0x34, 0xf6, 0x84, 0x86, 0xd4, 0x19, 0xa5, 0x9f, 0x68, 0x11, 0x0b, 0x7d, 0x2a, 0x2e, 0xd3, 0x31,
	0xf1, 0x97, 0x75, 0x07, 0x3a, 0xb1, 0xd0, 0x5f, 0xcb, 0xd0, 0x8f, 0xce, 0x69, 0x40, 0x8b, 0xa3,
	0x1c, 0xb0, 0xee, 0x01, 0xc4, 0x42, 0xff, 0xdc, 0x4d, 0xa4, 0xd0, 0x57, 0x34, 0x28, 0xec, 0x79,
	0x86, 0x58, 0x36, 0xf4, 0xb0, 0x01, 0x31, 0xd3, 0x22, 0x91, 0xe1, 0xd8, 0x8c, 0xad, 0x84, 0x59,
	0x0f, 0xa1, 0x4f, 0x16, 0x9e, 0x27, 0x43, 0x57, 0x0b, 0x7f, 0xd8, 0x21, 0xa5, 0x32, 0x68, 0x3c,
	0x1d, 0x8a, 0x57, 0x51, 0x12, 0xa0, 0x27, 0xc8, 0x3c, 0x65, 0x98, 0xe9, 0xeb, 0x2f, 0x84, 0x1c,
	0x4f, 0xf4, 0xb0, 0x9b, 0xf5, 0x95, 0x01, 0x33, 0xf6, 0xe3, 0x28, 0x1c, 0x0f, 0x7b, 0x24, 0x4b,
	0x3f, 0x8d, 0xdd, 0x97, 0x6c, 0xd7, 0xcf, 0xec, 0x18, 0x30, 0xd2, 0xd3, 0x68, 0x96, 0x78, 0x62,
	0xb8, 0x92, 0x49, 0x19, 0x48, 0xa5, 0x4c, 0x93, 0x41, 0x2e, 0x65, 0xa6, 0xb0, 0xf4, 0x93, 0x0b,
	0x57, 0xbb, 0xc9, 0x70, 0x95, 0x66, 0x3c, 0x07, 0xac, 0x3d, 0xe8, 0xc6, 0x42, 0x7f, 0x2a, 0x13,
	0x3d, 0xf1, 0xdd, 0xab, 0xe1, 0x1a, 0xd3, 0xa5, 0x00, 0x19, 0xfb, 0x91, 0x08, 0xdc, 0xe4, 0xc5,
	0xd0, 0xca, 0xec, 0x19, 0xc0, 0xd9, 0xf7, 0x12, 0xe1, 0x6a, 0x71, 0x26, 0x03, 0x31, 0x5c, 0x27,
	0x71, 0x01, 0x41, 0xf9, 0x2c, 0xf6, 0x53, 0xf9, 0x2d, 0x96, 0xe7, 0x08, 0xae, 0xf9, 0xb9, 0xeb,
	0x89, 0x23, 0x7f, 0xb8, 0xc1, 0x2c, 0xe1, 0x2f, 0xec, 0x97, 0x08, 0xb5, 0x48, 0xcc, 0x98, 0x6f,
	0xa7, 0x34, 0xce, 0x20, 0xeb, 0x1d, 0x58, 0xc9, 0x57, 0x99, 0xe8, 0xb4, 0x49, 0x4a, 0x15, 0xd4,
	0xac, 0x2d, 0x92, 0xe5, 0xdb, 0x73, 0x52, 0x1b, 0x92, 0x5a, 0x19, 0xb4, 0x9e, 0xc3, 0xce, 0xd4,
	0x55, 0xfa, 0xb3, 0xcb, 0x58, 0x4c, 0xa7, 0x32, 0x1c, 0x9f, 0xb8, 0x89, 0xab, 0xa4, 0x16, 0x23,
	0xe1, 0x45, 0x89, 0x3f, 0xdc, 0xda, 0x6b, 0xec, 0x77, 0x9f, 0xae, 0xe3, 0x7e, 0xcf, 0x08, 0xc1,
	0xa2, 0xd1, 0x4d, 0x76, 0xd6, 0x01, 0x58, 0x28, 0x3e, 0x0a, 0x82, 0x59, 0x28, 0xf5, 0x95, 0xf1,
	0xb6, 0x7d, 0xbd, 0xb7, 0x39, 0xea, 0xd6, 0x01, 0xac, 0x23, 0x7a, 0x28, 0xdd, 0x71, 0x18, 0x29,
	0xa9, 0x8c, 0x97, 0x1d, 0xf2, 0x42, 0x31, 0xe8, 0xb8, 0x24, 0x9e, 0xa7, 0x8d, 0x13, 0xed, 0x8e,
	0xc5, 0xa9, 0x4e, 0x86, 0x77, 0x78, 0xa2, 0xf9, 0xcb, 0x7a, 0x0c, 0xab, 0xee, 0x58, 0x1c, 0x44,
	0xe1, 0x85, 0x48, 0x94, 0x8c, 0x42, 0xd4, 0xb8, 0x4b, 0x1a, 0x35, 0xdc, 0xda, 0x87, 0x81, 0x9f,
	0xbb, 0x0d, 0x64, 0xe8, 0x0f, 0xef, 0x11, 0xdd, 0xaa, 0x30, 0x6a, 0x86, 0xe9, 0xee, 0x32, 0x9a,
	0xbb, 0xac, 0x59, 0x81, 0x71, 0x19, 0x65, 0x36, 0x5c, 0x52, 0xdc, 0x23, 0xc5, 0x0a, 0x6a, 0x7d,
	0x08, 0x9b, 0xa2, 0x3e, 0xc9, 0x64, 0x70, 0x9f, 0x0c, 0xae, 0x13, 0x23, 0x05, 0x55, 0x94, 0xa4,
	0x7b, 0xd2, 0xe6, 0x00, 0x91, 0x23, 0xf6, 0x1f, 0x1b, 0x30, 0xa8, 0x2c, 0x43, 0x21, 0xc4, 0xb5,
	0x28, 0xc4, 0x3d, 0x84, 0x7e, 0x14, 0x8b, 0xc4, 0xd5, 0x32, 0x0a, 0x0f, 0x5d, 0x9d, 0xc6, 0xed,
	0x32, 0x68, 0x6d, 0x43, 0x5b, 0x4d, 0xa2, 0xf8, 0x59, 0x1e, 0xc4, 0xb3, 0x6f, 0xda, 0x68, 0x49,
	0xe4, 0xcf, 0xbc, 0x62, 0xe8, 0x2b, 0x42, 0x18, 0x97, 0xf5, 0x55, 0x2c, 0x4c, 0xf0, 0xa3, 0xdf,
	0x95, 0xed, 0xb5, 0x54, 0xdd, 0x5e, 0xf6, 0x1f, 0x1a, 0xd0, 0x2f, 0x2d, 0x3e, 0xb6, 0x13, 0xb8,
	0x32, 0x3c, 0xbd, 0x0a, 0x62, 0x1d, 0x05, 0xe6, 0x40, 0x2b, 0x42, 0xb8, 0xa1, 0x95, 0x76, 0x13,
	0x4d, 0x2e, 0x79, 0x1c, 0x39, 0x80, 0x21, 0x4a, 0x84, 0x3e, 0xc9, 0x78, 0x08, 0xe9, 0x27, 0x86,
	0xf9, 0x28, 0x19, 0x4b, 0x9f, 0xfa, 0xde, 0x1a, 0xf1, 0x07, 0xa2, 0x89, 0x18, 0x1f, 0xf9, 0xd4,
	0xed, 0xd6, 0x88, 0x3f, 0xd0, 0x4b, 0x20, 0xfc, 0x33, 0x1c, 0x0e, 0x07, 0xec, 0xf4, 0xd3, 0xfe,
	0xb1, 0x01, 0xbd, 0x67, 0xe2, 0x32, 0xe3, 0xb8, 0xb5, 0x0a, 0x2d, 0x77, 0x2c, 0x4c, 0x47, 0xf1,
	0x27, 0x0e, 0x3a, 0xa1, 0x65, 0x28, 0xf4, 0xb0, 0x80, 0xa0, 0x73, 0x2f, 0x0a, 0xb5, 0x08, 0x75,
	0xda, 0x45, 0xf3, 0x89, 0xcb, 0x64, 0x7e, 0x1e, 0x0a, 0xed, 0xca, 0xa9, 0x99, 0xe6, 0x32, 0x88,
	0x2d, 0x6a, 0x19, 0x53, 0x87, 0x3b, 0x23, 0xfc, 0x69, 0xff, 0x0c, 0x36, 0xb0, 0x4f, 0xb5, 0x5d,
	0x8c, 0x6b, 0x82, 0xa1, 0xca, 0xf4, 0x8e, 0x7e, 0xa7, 0xe6, 0xcd, 0xdc, 0x5c, 0xc0, 0xea, 0x37,
	0xc2, 0x67, 0xea, 0x98, 0x2c, 0xc7, 0xda, 0xa0, 0xc3, 0xcc, 0x91, 0x95, 0x14, 0xe7, 0x2e, 0x40,
	0xec, 0x8e, 0x85, 0x23, 0x43, 0x5f, 0x5c, 0x92, 0x0f, 0x0c, 0xd6, 0xee, 0x58, 0x1c, 0x21, 0x60,
	0xed, 0x00, 0x7d, 0x38, 0x4a, 0xfe, 0xc0, 0xf3, 0xbf, 0x38, 0x6a, 0x23, 0x70, 0x2a, 0x7f, 0x10,
	0xf6, 0x6f, 0x1a, 0xb5, 0x76, 0x14, 0xa7, 0x2e, 0xda, 0x9d, 0x9a, 0xf3, 0x98, 0x3f, 0xfe, 0x95,
	0x66, 0xb2, 0x1c, 0x67, 0x21, 0xcf, 0x71, 0xca, 0xad, 0x72, 0x8e, 0xf3, 0xf7, 0x16, 0xf4, 0x4b,
	0x78, 0x4e, 0x90, 0x46, 0x91, 0x20, 0x1b, 0xb0, 0x94, 0x88, 0x31, 0x4e, 0x44, 0xb3, 0xc8, 0x90,
	0x2d, 0x68, 0x07, 0xc2, 0x77, 0x88, 0xf1, 0xad, 0x12, 0x45, 0x30, 0x87, 0x89, 0x27, 0x57, 0x4a,
	0x7a, 0xd2, 0x0d, 0x9d, 0x8c, 0x6f, 0xdd, 0x0c, 0x3b, 0xf2, 0xad, 0x47, 0xb0, 0x92, 0xab, 0x50,
	0xa2, 0xb3, 0x68, 0xa2, 0x7a, 0x8a, 0xd2, 0x96, 0xda, 0x85, 0xae, 0x37, 0x53, 0xce, 0x34, 0x1a,
	0x4b, 0x4f, 0xfa, 0x44, 0xc5, 0xd6, 0x08, 0xbc, 0x99, 0x3a, 0x66, 0x04, 0x7b, 0x81, 0x0a, 0xe4,
	0x61, 0xd9, 0x70, 0x69, 0xa6, 0x52, 0x5b, 0x5c, 0xc0, 0xd4, 0xb6, 0xcd, 0xb6, 0x74, 0x5e, 0x67,
	0xb6, 0xa8, 0x40, 0xb6, 0x9d, 0x72, 0x26, 0x83, 0xed, 0xd2, 0x26, 0x75, 0x34, 0x52, 0x18, 0x6a,
	0xc7, 0xe2, 0x16, 0xb4, 0x45, 0xe8, 0xb3, 0xb4, 0x5b, 0xde, 0x66, 0xbb, 0xd0, 0x65, 0xae, 0xf3,
	0xdc, 0x70, 0x9e, 0x90, 0xd2, 0x1f, 0xa7, 0xe7, 0x01, 0xf4, 0x27, 0x91, 0x8a, 0xa5, 0x76, 0xa7,
	0xdc, 0x78, 0x9f, 0x1c, 0xf4, 0x52, 0x90, 0x7a, 0xb0, 0x0f, 0xab, 0x05, 0x2f, 0x8e, 0x16, 0x97,
	0x9a, 0x12, 0x87, 0xce, 0x68, 0x25, 0x77, 0x75, 0x26, 0x2e, 0x35, 0xce, 0x36, 0x46, 0x07, 0x47,
	0x99, 0x88, 0x31, 0xa8, 0x45, 0x0c, 0xfb, 0xe3, 0x02, 0xef, 0x28, 0x99, 0x66, 0x7e, 0x9b, 0x65,
	0x6d, 0x14, 0x97, 0x35, 0xe3, 0x00, 0x73, 0x8e, 0x3f, 0xec, 0xbf, 0x76, 0x6a, 0x1e, 0xd4, 0x35,
	0x74, 0x29, 0xf2, 0x82, 0xb7, 0x58, 0xc6, 0x8b, 0xbc, 0xc9, 0x56, 0xb1, 0xc9, 0x7f, 0x1f, 0x5d,
	0xaa, 0x53, 0xb1, 0x54, 0x0f, 0x9e, 0x15, 0x46, 0x2d, 0xdf, 0xc8, 0xa8, 0x76, 0x99, 0x51, 0x37,
	0x13, 0xa6, 0x48, 0x36, 0xa8, 0x91, 0xad, 0xc2, 0xa8, 0xee, 0x8d, 0x8c, 0xea, 0x95, 0x19, 0xf5,
	0x5f, 0x30, 0xf0, 0x26, 0x52, 0x9c, 0x3b, 0x5e, 0x14, 0xc4, 0x53, 0x57, 0x86, 0xda, 0x50, 0x66,
	0x85, 0xe0, 0x83, 0x14, 0xa5, 0x99, 0x74, 0x95, 0x76, 0x26, 0x52, 0xe9, 0x28, 0xb9, 0x32, 0x84,
	0xe9, 0x22, 0xf6, 0x25, 0x43, 0xd6, 0xff, 0xc1, 0x2d, 0x9e, 0x33, 0x77, 0xea, 0xf8, 0x42, 0x79,
	0x89, 0x8c, 0xf1, 0xf8, 0x33, 0xac, 0x59, 0x4f, 0x65, 0x87, 0xb9, 0xc8, 0x7a, 0x17, 0xd6, 0x34,
	0xf6, 0x33, 0xc0, 0xeb, 0x45, 0x14, 0xcb, 0x10, 0xf5, 0x39, 0x11, 0x5d, 0xcd, 0x04, 0xdf, 0x32,
	0x8e, 0xe4, 0xf6, 0x23, 0x4f, 0x47, 0x89, 0xe3, 0xfa, 0x17, 0xd2, 0x13, 0x26, 0x23, 0xed, 0x31,
	0xf8, 0x09, 0x61, 0xf5, 0x1d, 0x60, 0xcd, 0xd9, 0x01, 0x0f, 0x61, 0x05, 0x89, 0x34, 0x15, 0x17,
	0x62, 0xca, 0xfc, 0xe7, 0xec, 0xb4, 0x17, 0x08, 0xff, 0x18, 0x41, 0x62, 0xff, 0xff, 0xc2, 0x2d,
	0x5f, 0x2a, 0xe1, 0x2a, 0xe1, 0xcc, 0x92, 0xb1, 0x08, 0xbd, 0x2b, 0xd6, 0xe5, 0x4c, 0xd5, 0x32,
	0xb2, 0xe7, 0x2c, 0x22, 0x8b, 0x27, 0x90, 0x8d, 0xb2, 0xe8, 0x9c, 0xd3, 0xd7, 0xb5, 0x54, 0x94,
	0xb7, 0xf0, 0x0e, 0x0c, 0xb0, 0x1f, 0x89, 0x50, 0xb3, 0xa9, 0x66, 0x5d, 0xce, 0x66, 0xfb, 0x01,
	0xee, 0x08, 0x44, 0x49, 0xef, 0x01, 0xf4, 0xa7, 0xc2, 0xbd, 0x10, 0x19, 0xfb, 0x38, 0x9d, 0xed,
	0x11, 0x98, 0xd2, 0xcf, 0x86, 0x7e, 0xba, 0x3b, 0xd8, 0xd5, 0xd0, 0x50, 0x54, 0x94, 0x36, 0xb4,
	0x9c, 0x4e, 0x43, 0xa1, 0x14, 0xad, 0x10, 0xe5, 0xae, 0x9d, 0x51, 0xd7, 0x60, 0xb8, 0x32, 0xd8,
	0x56, 0x69, 0x15, 0x29, 0x23, 0xed, 0x8c, 0x7a, 0xc5, 0xe5, 0xb3, 0xbe, 0x2a, 0x2c, 0xb5, 0xb8,
	0x74, 0x03, 0xcc, 0x8f, 0x70, 0xe9, 0x76, 0xe8, 0x5c, 0xd8, 0xa4, 0xbb, 0xaf, 0x91, 0x7f, 0x96,
	0x8b, 0x73, 0x0e, 0x14, 0x40, 0xeb, 0x3d, 0x00, 0xec, 0x77, 0x20, 0x7c, 0xe9, 0xaa, 0xe1, 0x1d,
	0xf2, 0xd0, 0x37, 0x27, 0xcb, 0x37, 0x04, 0x8e, 0x3a, 0x41, 0xfa, 0xd3, 0xfa, 0x02, 0xd6, 0x55,
	0x2c, 0x3c, 0x79, 0x2e, 0x3d, 0x27, 0x63, 0x88, 0x1a, 0xde, 0x25, 0xb3, 0xdb, 0x68, 0x76, 0x6a,
	0xc4, 0x67, 0x99, 0x74, 0x64, 0xa9, 0x1a, 0x66, 0xed, 0xf3, 0x8e, 0x93, 0xe1, 0x79, 0x44, 0x99,
	0x6a, 0xda, 0xa8, 0x9f, 0xbe, 0x0a, 0xe0, 0x06, 0xa4, 0x3b, 0xed, 0x07, 0xb0, 0x29, 0xc3, 0xd8,
	0xd5, 0x12, 0x49, 0x5a, 0x66, 0xe0, 0x2e, 0xcd, 0xcd, 0x46, 0x26, 0x3e, 0x2c, 0x50, 0xd1, 0xfe,
	0x6d, 0x03, 0xd6, 0xe7, 0xcc, 0x02, 0xa6, 0xdb, 0xaf, 0x38, 0xe1, 0xe4, 0xe3, 0xdf, 0x7c, 0x61,
	0x7a, 0xa6, 0x45, 0x40, 0x59, 0xe3, 0x2c, 0x49, 0x23, 0x5c, 0x11, 0xc2, 0x58, 0xf5, 0x3d, 0x8e,
	0x60, 0x22, 0xc3, 0xb1, 0x93, 0x60, 0xf2, 0xc1, 0x49, 0x4e, 0x3f, 0x43, 0x47, 0x98, 0x85, 0xdc,
	0x05, 0x98, 0x08, 0x37, 0xd1, 0xac, 0xc2, 0x79, 0x4e, 0x87, 0x10, 0x14, 0xdb, 0xc7, 0xd0, 0xc9,
	0xa6, 0xb6, 0x96, 0xcd, 0x6e, 0xc2, 0x72, 0x2c, 0x3d, 0x67, 0x96, 0x4c, 0xd3, 0x87, 0x96, 0x58,
	0x7a, 0xcf, 0x93, 0x29, 0xf6, 0x3a, 0xe1, 0x8b, 0x9e, 0xb9, 0xb3, 0xf3, 0x97, 0xfd, 0x8f, 0x06,
	0x58, 0xf5, 0x29, 0xaf, 0x45, 0xde, 0xc6, 0x9b, 0x44, 0xde, 0xe6, 0xbc, 0xc8, 0xfb, 0x18, 0xd6,
	0xc4, 0xa5, 0xf0, 0x9c, 0x92, 0x3b, 0xee, 0xc3, 0x00, 0x05, 0x27, 0x05, 0x97, 0x4f, 0x60, 0xbd,
	0xa2, 0x1b, 0xe6, 0x19, 0xf5, 0x5a, 0x49, 0x3b, 0x8d, 0xad, 0x32, 0x54, 0x22, 0xd1, 0x1c, 0x1c,
	0x39, 0x52, 0x01, 0x43, 0x14, 0x1f, 0xf7, 0xa0, 0x5b, 0x0c, 0x65, 0x1c, 0x9a, 0x8a, 0x90, 0xfd,
	0xa7, 0x16, 0x74, 0x32, 0xd2, 0x94, 0xe2, 0x78, 0xe3, 0xc6, 0x38, 0xde, 0xac, 0xc5, 0xf1, 0x77,
	0x60, 0x80, 0x0a, 0x63, 0x11, 0xfa, 0x22, 0xe1, 0x2d, 0xdc, 0xca, 0xee, 0xa3, 0x5f, 0x10, 0x9a,
	0x46, 0x0d, 0xd4, 0x7b, 0x41, 0x6f, 0x1c, 0xac, 0xb7, 0x58, 0xba, 0xb7, 0x46, 0xe7, 0xa4, 0xb7,
	0x0f, 0xab, 0xa8, 0x77, 0xc1, 0x17, 0x5e, 0x56, 0x5c, 0xaa, 0xde, 0x83, 0x49, 0x73, 0x07, 0x3a,
	0x5e, 0x34, 0x8d, 0x12, 0x47, 0x5f, 0x6a, 0x93, 0xeb, 0xb4, 0x09, 0x38, 0xbb, 0xd4, 0xc4, 0x08,
	0xa1, 0x1d, 0x4c, 0xc4, 0xdb, 0x86, 0x11, 0x42, 0x7f, 0x32, 0x26, 0x9a, 0xa1, 0xc0, 0x70, 0x19,
	0x4f, 0xad, 0x66, 0xf1, 0x41, 0xe3, 0x3d, 0xb0, 0x68, 0x2a, 0xd2, 0x4b, 0x1d, 0x77, 0x80, 0xf3,
	0x9d, 0xd5, 0xe2, 0x13, 0x4b, 0x71, 0x50, 0xfc, 0x84, 0xc5, 0xaa, 0xdd, 0x6c, 0x50, 0xfc, 0x5c,
	0x41, 0x7a, 0x8f, 0x61, 0x4d, 0x2a, 0xe7, 0x22, 0x7b, 0x79, 0xa1, 0x2e, 0xf3, 0xa1, 0x36, 0x90,
	0x2a, 0x7f, 0x91, 0x39, 0xe3, 0x09, 0x90, 0x18, 0xe8, 0xcc, 0x03, 0x0c, 0xa9, 0x9a, 0xd3, 0x4d,
	0xaa, 0xec, 0x5d, 0xe6, 0xec, 0x52, 0xdb, 0x3f, 0x36, 0x61, 0xf3, 0x80, 0x0e, 0xcc, 0x6f, 0x93,
	0xe7, 0xf4, 0x02, 0x61, 0xee, 0xdc, 0xe2, 0x65, 0x6d, 0x87, 0xe4, 0xd9, 0x7b, 0xe9, 0x4d, 0xeb,
	0x11, 0xac, 0x64, 0x37, 0x3e, 0xc7, 0x2f, 0xec, 0xcd, 0xf2, 0x3d, 0xb0, 0xa4, 0x76, 0x25, 0xdc,
	0x24, 0xbd, 0x87, 0x64, 0xe8, 0x77, 0xc2, 0x4d, 0x70, 0x45, 0xf0, 0x7a, 0x58, 0x4c, 0x48, 0xf2,
	0xfb, 0x22, 0xee, 0x2d, 0xbe, 0x1c, 0x96, 0x1e, 0xf2, 0x8a, 0x17, 0xc6, 0x4a, 0xa6, 0x68, 0x72,
	0x91, 0x42, 0xa6, 0x78, 0x1f, 0x7a, 0x46, 0x21, 0x9e, 0x44, 0x3a, 0x32, 0x4b, 0x6b, 0x8c, 0x4e,
	0x10, 0xb2, 0xef, 0xc3, 0xe0, 0x50, 0x4c, 0xc5, 0x0d, 0x73, 0x61, 0x7f, 0x4d, 0xef, 0xac, 0x9f,
	0xba, 0x4a, 0x64, 0xef, 0xac, 0xe9, 0xeb, 0x25, 0x2b, 0xf1, 0xeb, 0xe5, 0x2a, 0xb4, 0x02, 0x35,
	0x4e, 0xaf, 0x45, 0x81, 0x1a, 0x9b, 0xcb, 0x93, 0x9b, 0xbe, 0x67, 0xd2, 0xad, 0xa1, 0x07, 0x90,
	0x3f, 0xda, 0xda, 0x5f, 0xc1, 0xea, 0x89, 0xd0, 0x6f, 0x74, 0x71, 0xaa, 0x0c, 0xb6, 0x59, 0x1d,
	0xac, 0xfd, 0xcb, 0x06, 0x6c, 0x95, 0x9c, 0xbd, 0x45, 0x8f, 0xf7, 0xb3, 0x1e, 0xe3, 0x49, 0x73,
	0xcb, 0x3c, 0xef, 0x96, 0x2e, 0x5c, 0x3c, 0x8e, 0xfc, 0xda, 0xb5, 0x50, 0xb8, 0x76, 0xd9, 0x2f,
	0xe1, 0xce, 0x33, 0xf1, 0x2a, 0x33, 0x41, 0x32, 0xbc, 0x45, 0x2f, 0xde, 0x2d, 0xf5, 0x82, 0x0e,
	0xda, 0xaa, 0xd7, 0xac, 0x23, 0xf6, 0x8b, 0xc2, 0xa8, 0xdf, 0xb2, 0xbd, 0xfd, 0xc2, 0x3a, 0x55,
	0x47, 0x5d, 0x6e, 0xec, 0xd7, 0x4d, 0x58, 0x9f, 0xd3, 0x95, 0x6a, 0x76, 0xda, 0xa8, 0x65, 0xa7,
	0x55, 0x26, 0x36, 0x6b, 0x4c, 0xac, 0x11, 0xbe, 0x55, 0x27, 0x7c, 0x7d, 0xfb, 0x2d, 0xcc, 0xdb,
	0x7e, 0x15, 0xaa, 0x2c, 0xd6, 0xf6, 0x85, 0x05, 0x0b, 0x6a, 0x12, 0xbd, 0xa2, 0x3d, 0xd5, 0x1e,
	0xd1, 0x6f, 0x73, 0xf4, 0xc9, 0x90, 0x73, 0xfa, 0xf6, 0xc8, 0x7c, 0x61, 0x64, 0xf4, 0xdd, 0x2b,
	0x27, 0x9c, 0x05, 0xe6, 0xe5, 0x78, 0xc9, 0x77, 0xaf, 0x9e, 0xcd, 0x02, 0xfb, 0xcf, 0xcd, 0x1a,
	0x79, 0xd5, 0x5b, 0xc5, 0x11, 0x0a, 0x10, 0xad, 0x79, 0x01, 0xe2, 0x0d, 0xc7, 0xfb, 0x1f, 0x8f,
	0x23, 0x95, 0xe5, 0x6d, 0xd7, 0x96, 0x77, 0x17, 0xba, 0xfc, 0xa6, 0xcb, 0x0a, 0x9d, 0xda, 0x33,
	0x6f, 0x75, 0xfd, 0xa1, 0x1e, 0x89, 0xbe, 0xa3, 0x92, 0x09, 0x4e, 0xe4, 0x33, 0xf1, 0x0a, 0xe7,
	0xf2, 0xcd, 0xc8, 0x6b, 0x97, 0x36, 0xcb, 0x4a, 0xa1, 0xe4, 0x83, 0x6e, 0x98, 0xb6, 0xbf, 0x03,
	0x8a, 0x3a, 0x06, 0xac, 0xd5, 0x2f, 0xae, 0x59, 0xa4, 0x4d, 0x58, 0x9e, 0x29, 0x91, 0xe4, 0x99,
	0x48, 0x5a, 0xc1, 0x28, 0x9e, 0xff, 0x95, 0x12, 0x86, 0x39, 0x47, 0x55, 0xad, 0x86, 0x61, 0xce,
	0xd1, 0x17, 0xd7, 0x14, 0x31, 0x1e, 0xd2, 0x63, 0x76, 0x7a, 0xdc, 0x2b, 0x9d, 0x98, 0x13, 0xba,
	0x97, 0xa9, 0x9c, 0xea, 0x24, 0xcd, 0x2e, 0xcc, 0x61, 0x9f, 0x55, 0x69, 0xf2, 0x5a, 0x87, 0x39,
	0x60, 0xd3, 0x6c, 0x00, 0xfd, 0x74, 0xb2, 0x03, 0xd6, 0x28, 0xa1, 0xa3, 0x07, 0xf4, 0x26, 0x9e,
	0x1f, 0xdb, 0x85, 0x52, 0x46, 0x5e, 0x14, 0x79, 0xc4, 0x7d, 0xca, 0x8f, 0x61, 0x53, 0xcf, 0xa8,
	0x54, 0x45, 0x8c, 0xaf, 0xec, 0x04, 0x36, 0x2f, 0x16, 0xe5, 0xb2, 0x48, 0x39, 0x8d, 0xe8, 0x57,
	0xeb, 0x22, 0x66, 0x46, 0xa7, 0x51, 0x38, 0x36, 0xe5, 0x8d, 0xac, 0x30, 0x62, 0x2c, 0x27, 0x6c,
	0x39, 0xa8, 0x56, 0x46, 0x8c, 0x58, 0x71, 0x99, 0x60, 0xb5, 0x5a, 0x1a, 0x49, 0xc5, 0x5c, 0x1b,
	0x59, 0xab, 0xd6, 0x46, 0x8c, 0xd8, 0xe5, 0xe2, 0x88, 0x55, 0x2d, 0x8e, 0xdc, 0xa7, 0x82, 0x8f,
	0xf3, 0x7d, 0x5a, 0x1d, 0x59, 0xaf, 0x57, 0x47, 0xcc, 0x92, 0x4c, 0xa2, 0x40, 0xa0, 0x86, 0x29,
	0x70, 0x60, 0xff, 0x18, 0x49, 0x9b, 0x30, 0x69, 0xf5, 0x46, 0xb5, 0x7e, 0x52, 0xd9, 0x5a, 0xb7,
	0x5f, 0xb7, 0xb5, 0x36, 0x6b, 0x5b, 0x6b, 0x13, 0x96, 0xcf, 0x5d, 0x4f, 0x20, 0x4d, 0x87, 0xa5,
	0x12, 0x8a, 0x99, 0x54, 0xda, 0x43, 0x5b, 0x19, 0x4d, 0x0f, 0x70, 0x1b, 0x6d, 0x43, 0x5b, 0xa5,
	0xaf, 0xbc, 0xdb, 0x26, 0x60, 0xa4, 0xaf, 0x05, 0x98, 0x7c, 0x85, 0x6a, 0x96, 0xb8, 0xa1, 0x27,
	0x1c, 0xf2, 0x7c, 0xc4, 0xb5, 0x06, 0x4c, 0xbe, 0x52, 0xc1, 0xe7, 0xdc, 0xc4, 0x2e, 0x74, 0x05,
	0x42, 0xc2, 0xf1, 0xdc, 0xc4, 0xa7, 0xca, 0x42, 0x7b, 0x04, 0x0c, 0x1d, 0xb8, 0x89, 0x8f, 0xbc,
	0x56, 0x5e, 0x12, 0x38, 0xd8, 0x11, 0xde, 0xf9, 0x78, 0x89, 0xeb, 0x8c, 0x7a, 0x88, 0x9e, 0x08,
	0xcd, 0xa1, 0x3f, 0xaf, 0x4d, 0xdc, 0x7b, 0x6d, 0x6d, 0x62, 0xf7, 0x9a, 0xda, 0xc4, 0x31, 0x3c,
	0x98, 0xba, 0x4a, 0x3b, 0x59, 0x15, 0xc0, 0x89, 0xcd, 0x23, 0xae, 0x93, 0x06, 0x37, 0x1c, 0xed,
	0x1e, 0x99, 0xef, 0xde, 0x50, 0xb3, 0xa1, 0x49, 0xf8, 0x08, 0xb6, 0xc8, 0x5b, 0x5a, 0x84, 0x28,
	0xf9, 0xb8, 0x4f, 0x3e, 0x6e, 0xd7, 0x2b, 0x35, 0x64, 0xfa, 0x3e, 0x90, 0xc4, 0x09, 0xa2, 0x99,
	0x9e, 0x94, 0xec, 0x6c, 0x7e, 0x23, 0x41, 0xe9, 0x37, 0x28, 0x2c, 0x18, 0x7d, 0x0c, 0x77, 0x4c,
	0xef, 0xb3, 0x2b, 0x64, 0xc9, 0xf4, 0x01, 0x99, 0x6e, 0x71, 0xb7, 0xf3, 0xbb, 0x76, 0xee, 0xc0,
	0xd0, 0xec, 0x9c, 0x2a, 0xce, 0xc3, 0x87, 0x19, 0xcd, 0xb8, 0x04, 0x8d, 0x0b, 0xc5, 0x22, 0xa6,
	0xc3, 0x23, 0x66, 0x11, 0x43, 0x54, 0xdf, 0xfd, 0xfd, 0x42, 0xe1, 0x34, 0x4b, 0x8f, 0xf5, 0x37,
	0x9c, 0xd3, 0xc6, 0x9b, 0xcd, 0xe9, 0x01, 0xdc, 0xbb, 0xde, 0x11, 0x9d, 0xc7, 0x4d, 0xe2, 0xcf,
	0x8e, 0x98, 0xef, 0xe4, 0x14, 0x8f, 0xe9, 0x1b, 0x17, 0xa6, 0x75, 0xe3, 0xc2, 0x7c, 0x04, 0x5b,
	0xf3, 0xac, 0xb8, 0xe9, 0x05, 0x6a, 0xfa, 0xb6, 0xac, 0x99, 0x51, 0xab, 0xd7, 0xaf, 0xe9, 0xe2,
	0xf5, 0x6b, 0xfa, 0x18, 0xd6, 0x4a, 0xfa, 0x85, 0x94, 0x63, 0x10, 0xe4, 0xba, 0xd4, 0xc0, 0xeb,
	0xd6, 0x7f, 0xf9, 0x75, 0xeb, 0xff, 0x01, 0x6c, 0xce, 0xb1, 0xa5, 0x26, 0xdb, 0xd4, 0xe4, 0x86,
	0xa8, 0xda, 0x51, 0xc3, 0xff, 0x0d, 0x96, 0xc2, 0x78, 0x3f, 0x95, 0x3f, 0xb0, 0x25, 0x99, 0x74,
	0xc8, 0x64, 0xad, 0x24, 0x41, 0xf5, 0xa7, 0xbf, 0x5a, 0xa4, 0x93, 0xf4, 0x54, 0x24, 0xf4, 0x10,
	0xf7, 0xff, 0x00, 0xfc, 0x0f, 0x09, 0x7a, 0xff, 0xb7, 0xcc, 0xe1, 0x5b, 0xf8, 0xa3, 0xc7, 0xf6,
	0x7a, 0x09, 0x33, 0x69, 0xe9, 0x07, 0xd0, 0xcf, 0xcd, 0xf0, 0x44, 0x9e, 0x67, 0xb9, 0x56, 0xc0,
	0x4c, 0x46, 0xf0, 0x53, 0xe8, 0x15, 0xff, 0x90, 0x61, 0x51, 0x6a, 0x3c, 0xe7, 0xff, 0x1c, 0xdc,
	0x6a, 0xf5, 0xbf, 0x1b, 0x3f, 0xa9, 0xd5, 0x2b, 0xea, 0xa5, 0x0d, 0xf1, 0x72, 0x7b, 0x1e, 0x5a,
	0x36, 0xa6, 0x47, 0x80, 0xb2, 0x9a, 0x79, 0x15, 0xdf, 0x9e, 0x87, 0x2a, 0xeb, 0x4b, 0xb8, 0x35,
	0xef, 0xe2, 0x69, 0xed, 0xa0, 0xf6, 0x35, 0x57, 0xd2, 0x6c, 0xe6, 0x4a, 0x09, 0xfd, 0x87, 0xd0,
	0x2b, 0x5e, 0xd7, 0x2c, 0x52, 0xaa, 0x5c, 0xe0, 0xe6, 0x5b, 0x1e, 0x52, 0x7a, 0x55, 0x1d, 0x7d,
	0xf5, 0xf6, 0xb5, 0x7d, 0xb7, 0x86, 0x96, 0xbc, 0x7c, 0x46, 0x2b, 0xc7, 0x57, 0x00, 0xca, 0x3d,
	0xe7, 0xad, 0xdc, 0xde, 0xbc, 0x1b, 0x4b, 0xc9, 0xcd, 0xc7, 0x29, 0x6f, 0xae, 0xf5, 0x71, 0xb7,
	0x76, 0x0b, 0x29, 0x3a, 0xf8, 0x7e, 0x89, 0xfe, 0x5c, 0xf4, 0xfe, 0x3f, 0x03, 0x00, 0x00, 0xff,
	0xff, 0xe4, 0xdb, 0xc8, 0x49, 0x6c, 0x24, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PetServiceClient is the client API for PetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PetServiceClient interface {
	//获取宠物列表
	GetPetList(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetListResponse, error)
	// 新版获取宠物列表数据
	GetPetListNew(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetListNewRes, error)
	// 获取宠物花色
	GetPetFlower(ctx context.Context, in *GetPetFlowerRequest, opts ...grpc.CallOption) (*GetPetFlowerRes, error)
	// 互联网医院-病例列表
	MedRecordList(ctx context.Context, in *MedRecordListReq, opts ...grpc.CallOption) (*MedRecordListRes, error)
	// 互联网医院-病例详情
	MedRecordInfo(ctx context.Context, in *MedRecordInfoReq, opts ...grpc.CallOption) (*MedRecordInfoRes, error)
	// 新增编辑记录
	CreateOrUpdateRecord(ctx context.Context, in *CreateOrUpdateRecordReq, opts ...grpc.CallOption) (*PetBaseResponse, error)
	// 删除记录
	DeleteRecord(ctx context.Context, in *DeleteRecordReq, opts ...grpc.CallOption) (*PetBaseResponse, error)
	// 获取记录数据
	PetRecordList(ctx context.Context, in *PetRecordListReq, opts ...grpc.CallOption) (*PetRecordListBaseResponse, error)
	// 获取记录的时间，前端展示
	GetNewPetDate(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*NewPetRecordDateBaseResponse, error)
	//APP 调用接口
	GetPetDate(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetRecordDateBaseResponse, error)
}

type petServiceClient struct {
	cc *grpc.ClientConn
}

func NewPetServiceClient(cc *grpc.ClientConn) PetServiceClient {
	return &petServiceClient{cc}
}

func (c *petServiceClient) GetPetList(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetListResponse, error) {
	out := new(PetListResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/GetPetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) GetPetListNew(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetListNewRes, error) {
	out := new(PetListNewRes)
	err := c.cc.Invoke(ctx, "/cc.PetService/GetPetListNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) GetPetFlower(ctx context.Context, in *GetPetFlowerRequest, opts ...grpc.CallOption) (*GetPetFlowerRes, error) {
	out := new(GetPetFlowerRes)
	err := c.cc.Invoke(ctx, "/cc.PetService/GetPetFlower", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) MedRecordList(ctx context.Context, in *MedRecordListReq, opts ...grpc.CallOption) (*MedRecordListRes, error) {
	out := new(MedRecordListRes)
	err := c.cc.Invoke(ctx, "/cc.PetService/MedRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) MedRecordInfo(ctx context.Context, in *MedRecordInfoReq, opts ...grpc.CallOption) (*MedRecordInfoRes, error) {
	out := new(MedRecordInfoRes)
	err := c.cc.Invoke(ctx, "/cc.PetService/MedRecordInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) CreateOrUpdateRecord(ctx context.Context, in *CreateOrUpdateRecordReq, opts ...grpc.CallOption) (*PetBaseResponse, error) {
	out := new(PetBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/CreateOrUpdateRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) DeleteRecord(ctx context.Context, in *DeleteRecordReq, opts ...grpc.CallOption) (*PetBaseResponse, error) {
	out := new(PetBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/DeleteRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) PetRecordList(ctx context.Context, in *PetRecordListReq, opts ...grpc.CallOption) (*PetRecordListBaseResponse, error) {
	out := new(PetRecordListBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/PetRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) GetNewPetDate(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*NewPetRecordDateBaseResponse, error) {
	out := new(NewPetRecordDateBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/GetNewPetDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) GetPetDate(ctx context.Context, in *PetListRequest, opts ...grpc.CallOption) (*PetRecordDateBaseResponse, error) {
	out := new(PetRecordDateBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.PetService/GetPetDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetServiceServer is the server API for PetService service.
type PetServiceServer interface {
	//获取宠物列表
	GetPetList(context.Context, *PetListRequest) (*PetListResponse, error)
	// 新版获取宠物列表数据
	GetPetListNew(context.Context, *PetListRequest) (*PetListNewRes, error)
	// 获取宠物花色
	GetPetFlower(context.Context, *GetPetFlowerRequest) (*GetPetFlowerRes, error)
	// 互联网医院-病例列表
	MedRecordList(context.Context, *MedRecordListReq) (*MedRecordListRes, error)
	// 互联网医院-病例详情
	MedRecordInfo(context.Context, *MedRecordInfoReq) (*MedRecordInfoRes, error)
	// 新增编辑记录
	CreateOrUpdateRecord(context.Context, *CreateOrUpdateRecordReq) (*PetBaseResponse, error)
	// 删除记录
	DeleteRecord(context.Context, *DeleteRecordReq) (*PetBaseResponse, error)
	// 获取记录数据
	PetRecordList(context.Context, *PetRecordListReq) (*PetRecordListBaseResponse, error)
	// 获取记录的时间，前端展示
	GetNewPetDate(context.Context, *PetListRequest) (*NewPetRecordDateBaseResponse, error)
	//APP 调用接口
	GetPetDate(context.Context, *PetListRequest) (*PetRecordDateBaseResponse, error)
}

// UnimplementedPetServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPetServiceServer struct {
}

func (*UnimplementedPetServiceServer) GetPetList(ctx context.Context, req *PetListRequest) (*PetListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetList not implemented")
}
func (*UnimplementedPetServiceServer) GetPetListNew(ctx context.Context, req *PetListRequest) (*PetListNewRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetListNew not implemented")
}
func (*UnimplementedPetServiceServer) GetPetFlower(ctx context.Context, req *GetPetFlowerRequest) (*GetPetFlowerRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetFlower not implemented")
}
func (*UnimplementedPetServiceServer) MedRecordList(ctx context.Context, req *MedRecordListReq) (*MedRecordListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MedRecordList not implemented")
}
func (*UnimplementedPetServiceServer) MedRecordInfo(ctx context.Context, req *MedRecordInfoReq) (*MedRecordInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MedRecordInfo not implemented")
}
func (*UnimplementedPetServiceServer) CreateOrUpdateRecord(ctx context.Context, req *CreateOrUpdateRecordReq) (*PetBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateRecord not implemented")
}
func (*UnimplementedPetServiceServer) DeleteRecord(ctx context.Context, req *DeleteRecordReq) (*PetBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecord not implemented")
}
func (*UnimplementedPetServiceServer) PetRecordList(ctx context.Context, req *PetRecordListReq) (*PetRecordListBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PetRecordList not implemented")
}
func (*UnimplementedPetServiceServer) GetNewPetDate(ctx context.Context, req *PetListRequest) (*NewPetRecordDateBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewPetDate not implemented")
}
func (*UnimplementedPetServiceServer) GetPetDate(ctx context.Context, req *PetListRequest) (*PetRecordDateBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetDate not implemented")
}

func RegisterPetServiceServer(s *grpc.Server, srv PetServiceServer) {
	s.RegisterService(&_PetService_serviceDesc, srv)
}

func _PetService_GetPetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetPetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/GetPetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetPetList(ctx, req.(*PetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_GetPetListNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetPetListNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/GetPetListNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetPetListNew(ctx, req.(*PetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_GetPetFlower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetFlowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetPetFlower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/GetPetFlower",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetPetFlower(ctx, req.(*GetPetFlowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_MedRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MedRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).MedRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/MedRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).MedRecordList(ctx, req.(*MedRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_MedRecordInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MedRecordInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).MedRecordInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/MedRecordInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).MedRecordInfo(ctx, req.(*MedRecordInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_CreateOrUpdateRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).CreateOrUpdateRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/CreateOrUpdateRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).CreateOrUpdateRecord(ctx, req.(*CreateOrUpdateRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_DeleteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).DeleteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/DeleteRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).DeleteRecord(ctx, req.(*DeleteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_PetRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).PetRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/PetRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).PetRecordList(ctx, req.(*PetRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_GetNewPetDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetNewPetDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/GetNewPetDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetNewPetDate(ctx, req.(*PetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_GetPetDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetPetDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetService/GetPetDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetPetDate(ctx, req.(*PetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PetService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.PetService",
	HandlerType: (*PetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetList",
			Handler:    _PetService_GetPetList_Handler,
		},
		{
			MethodName: "GetPetListNew",
			Handler:    _PetService_GetPetListNew_Handler,
		},
		{
			MethodName: "GetPetFlower",
			Handler:    _PetService_GetPetFlower_Handler,
		},
		{
			MethodName: "MedRecordList",
			Handler:    _PetService_MedRecordList_Handler,
		},
		{
			MethodName: "MedRecordInfo",
			Handler:    _PetService_MedRecordInfo_Handler,
		},
		{
			MethodName: "CreateOrUpdateRecord",
			Handler:    _PetService_CreateOrUpdateRecord_Handler,
		},
		{
			MethodName: "DeleteRecord",
			Handler:    _PetService_DeleteRecord_Handler,
		},
		{
			MethodName: "PetRecordList",
			Handler:    _PetService_PetRecordList_Handler,
		},
		{
			MethodName: "GetNewPetDate",
			Handler:    _PetService_GetNewPetDate_Handler,
		},
		{
			MethodName: "GetPetDate",
			Handler:    _PetService_GetPetDate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/pet.proto",
}

package models

import (
	"order-center/proto/oc"
	"time"
)

const (
	InvoiceStatusWait     = 0 // 未开票
	InvoiceStatusSuccess  = 1 // 开票成功
	InvoiceStatusIng      = 2 // 开票中
	InvoiceStatusFail     = 3 // 开票失败
	InvoiceStatusDisabled = 8 // 开票禁用
	InvoiceStatusExpire   = 9 // 开票过期
)

type OrderInvoice struct {
	Id          int                      `json:"id"`
	OrderSn     string                   `json:"order_sn"`          // 订单号
	OrderNo     string                   `json:"order_no"`          // 申请单号
	Type        int                      `json:"type"`              // 0-未知，1-个人，2-企业
	Status      int                      `json:"status"`            // 开票状态，0未开票、1开票成功、2开票中、3开票失败,
	CompanyCode string                   `json:"company_code"`      // 财务编码
	Source      int                      `json:"source"`            // 开票给中台的订单来源：1 子龙 2 R1 3 阿闻商城 4 阿闻商城-分销 5 本地生活-门店仓 6  本地生活-前置仓 7 本地生活-前置虚拟仓
	FailReason  string                   `json:"fail_reason"`       // 失败原因
	Apply       *oc.CreateInvoiceRequest `xorm:"json" json:"apply"` // 申请json
	Invoices    string                   `json:"invoices"`          // 发票信息
	CreatedAt   time.Time                `xorm:"DATETIME created" json:"created_at"`
	UpdatedAt   time.Time                `xorm:"DATETIME updated" json:"updated_at"`
}

package gredis

import (
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"time"
)

// NewDC 创建一个redis客户端，这不是连接池，注意用完要关闭
func NewDC() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr:        config.GetString("redis.Addr"),
		Password:    config.GetString("redis.Password"),
		DB:          cast.ToInt(config.GetString("redis.DB")),
		IdleTimeout: 30 * time.Second,
	})
}

package services

//CreateOrderStepFour
//商城相关的函数，主要用于临时处理商城的逻辑 ，当前仅有内部调用 先写成函数
//后续商城功能迁移到平台时 可以将相关的函数封装成rpc方法
//v2.9.10商城秒杀活动添加
//主要迁移商城支付前下单写入数据的逻辑方法： _createOrderStep4
import (
	"errors"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"
	"order-center/utils/sn"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/techoner/gophp"
	kit "github.com/tricobbler/rp-kit"
)

// ChannelRet 通用信道通信的数据结构
type ChannelRet struct {
	Err error
	Msg string
}

type UPetService struct {
	session    *xorm.Session
	orderGoods []*models.UpetOrderGoods //订单商品
}

// CreateMallVirtualOrder 创建商城虚拟订单 将数据写入商城相关表中
func (u *UPetService) CreateMallVirtualOrder(params *oc.MtAddOrderRequest, UPetMemberInfo *models.UPetMemberPlus, GrpcContext *models.GrpcContext) (err error, msg string) {
	//source first_order open_id dis_id

	//小程序(阿闻智慧门店)
	var orderFrom = 2
	if params.Source == 1 {
		orderFrom = 3
	} else if params.Source == 2 {
		orderFrom = 5
	} else if params.Source == 3 {
		orderFrom = 6
	}

	if params.Source == 1 && UPetMemberInfo.WeixinMiniOpenidshop == "" && params.OpenId != "" {
		updateMember := &models.UpetMember{
			WeixinMiniOpenidshop: params.OpenId,
		}
		_, err = u.session.Cols("weixin_mini_openidshop").Update(updateMember)
		if err != nil {
			return err, "更新商城用户weixin_mini_openidshop失败"
		}
	}

	//获取虚拟商品信息
	goodInfo, err := GetUPetVirtualGoodsInfo(cast.ToInt64(params.OrderProductModel[0].Sku), "", params.OrgId)
	if err != nil {
		return err, "获取虚拟商品信息出错"
	}
	if goodInfo.GoodsId == 0 {
		return errors.New("该商品不符合购买条件，可能的原因有：下架、不存在、过期等"), "该商品不符合购买条件，可能的原因有：下架、不存在、过期等"
	}
	if goodInfo.GoodsStorage == 0 {
		return errors.New("该商品库存不足"), "该商品库存不足"
	}
	if goodInfo.StoreId == UPetMemberInfo.StoreId {
		return errors.New("不允许自己店铺的商品"), "不允许自己店铺的商品"
	}
	if goodInfo.VirtualLimit > goodInfo.GoodsStorage {
		goodInfo.VirtualLimit = goodInfo.GoodsStorage
	}
	if params.OrderProductModel[0].Specs == "" {
		goodInfo.GoodsSpec, err = GetUPetGoodsSpec(goodInfo.SpecName, goodInfo.GoodsSpec)
		if err != nil {
			glog.Error(params.ReceiverPhone, "商品规格解码失败", kit.JsonEncode(params))
		}
	} else {
		//直接读前端的值
		goodInfo.GoodsSpec = params.OrderProductModel[0].Specs
	}
	//获取门店信息

	var (
		addVrOrder    models.UpetVrOrder
		orderSn       sn.UPetPaySN                //不要惊讶 商城使用的订单号使用的是与支付订单号一样的生成方法
		nowUnixTime   = time.Now().Unix()         //当前时间
		disMemberInfo = new(models.DisMemberInfo) //分销员信息
	)
	//
	orderSn.MemberId = UPetMemberInfo.MemberId
	addVrOrder.OrderSn = cast.ToInt64(orderSn.Generate())

	addVrOrder.BuyerId = UPetMemberInfo.MemberId
	addVrOrder.BuyerName = UPetMemberInfo.MemberName
	addVrOrder.BuyerPhone = UPetMemberInfo.MemberMobile

	addVrOrder.BuyerMsg = params.BuyerMemo
	addVrOrder.AddTime = nowUnixTime
	addVrOrder.OrderState = 10
	addVrOrder.OrderAmount = kit.FenToYuan(params.Total)

	addVrOrder.StoreId = goodInfo.StoreId
	addVrOrder.StoreName = goodInfo.StoreName
	addVrOrder.GoodsId = goodInfo.GoodsId
	addVrOrder.GoodsName = goodInfo.GoodsName
	addVrOrder.GoodsPrice = goodInfo.GoodsPrice
	addVrOrder.GoodsImage = goodInfo.GoodsImage
	addVrOrder.GcId = goodInfo.GcId
	addVrOrder.VrIndate = goodInfo.VirtualIndate
	addVrOrder.VrInvalidRefund = goodInfo.VirtualInvalidRefund
	addVrOrder.ChainId = goodInfo.ChainId //todo 是否需要前端传值呢？？ 是否要显示会员价  会员价需要加相关逻辑

	addVrOrder.GoodsNum = params.OrderProductModel[0].Number
	addVrOrder.CommisRate = 200
	addVrOrder.OrderFrom = orderFrom
	addVrOrder.OrderType = params.OrderType
	addVrOrder.OrderCommon = "" //order_common 哪里来 是否需要
	if params.DisType == 0 {
		params.DisType = 5
	}
	addVrOrder.DisType = params.DisType

	//ignore_push
	distributeIsUse, err := GetUPetConfig("distribute_isuse")
	if err != nil {
		glog.Error("写入商城虚拟订单数据：查询分销配置distribute_isuse出错", err, kit.JsonEncode(params))
	}

	//如果是开启了分销
	if distributeIsUse == "1" {
		disMemberInfo, err = GetDisMemberInfo(UPetMemberInfo.MemberId) //todo 没有获得分销的数据
		if err != nil {
			glog.Error("写入商城虚拟订单数据：查询分销员信息出错", err, kit.JsonEncode(params))
		}
	}

	//分销配置打开 且商品是分销商品 且支付价格大于1元
	productCommonInfo, err := GetUPetGoodsCommonInfo(goodInfo.GoodsCommonid, "is_dis,dis_commis_rate")
	if err != nil {
		glog.Error(goodInfo.GoodsId, goodInfo.GoodsCommonid, "写入商城订单数据：查询商城商品spu信息失败", err)
	}

	if distributeIsUse == "1" && productCommonInfo.IsDis == 1 && disMemberInfo.DisMemberId > 0 && params.OrderProductModel[0].PayPrice > 100 {
		addVrOrder.IsDis = 1
		addVrOrder.DisMemberId = disMemberInfo.DisMemberId
		addVrOrder.OutMemberId = disMemberInfo.OutsideMemberId
		addVrOrder.DisCommisRate = productCommonInfo.DisCommisRate
		if UPetMemberInfo.MemberId == disMemberInfo.DisMemberId {
			addVrOrder.DisType = 3
		}
	}
	addVrOrder.ChainId = disMemberInfo.ChainId
	addVrOrder.GoodsSpec = goodInfo.GoodsSpec
	affected, err := u.session.Insert(addVrOrder)
	if err != nil {
		return err, "写入虚拟订单出错"
	}
	if affected == 0 {
		return errors.New("写入虚拟订单出错"), ""
	}
	//todo 库存报警 RealTimePush
	//todo 变更库存和销量 mq
	//todo 生成库存快照 mq
	//todo 发送兑换码到手机 RealTimePush
	return nil, ""
}

// CreateUPetMallOrder 创建订单 将数据写入商城相关表中
func (u *UPetService) CreateUPetMallOrder(params *oc.MtAddOrderRequest, GrpcContext *models.GrpcContext) (err error, msg string) {
	//库存检查 仅使用与秒杀订单  当有其他类型订单时 此处需要做兼容处理
	//V2.9.10 新增 秒杀订单判断一次商城商品的库存
	//主要作用：避免秒杀订单存在虚拟库存但实际库存不足时走了后面的代码流程
	for i, orderProduct := range params.OrderProductModel {
		if !(orderProduct.ProductType == 3 && orderProduct.CombineType == 31) { //非实实组合商品
			stockCheckRes, err, msg := CheckMallStock(
				params.OrderProductModel[i].Sku,    //商品sku
				params.OrderProductModel[i].Number, //购买数量
			)
			if err != nil {
				glog.Errorf("cart_service-CreateUPetMallOrder-CheckMallStock err: %v", err)
				return err, "存检测出错"
			}
			//库存检测不通过 直接返回
			if !stockCheckRes {
				return errors.New(msg), "商品实际库存不足"
			}
		}
	}

	var (
		paySn          sn.UPetPaySN
		orderSn        sn.UPetOrderSN
		addPay         models.UpetOrderPay //新增支付
		addOrder       models.UpetOrders   //新增订单
		updateOrder    models.UpetOrders
		addOrderLog    models.UpetOrderLog         //订单日志
		addOrderCommon models.UpetOrderCommon      //订单扩展表
		disMemberInfo  = new(models.DisMemberInfo) //分销员信息
		nowUnixTime    = time.Now().Unix()         //当前时间
	)
	//生成支付数据 插入upet_order_pay表
	//参数中的MemberId 为北京的scrmUserId 需要获得商城的用户Id
	UPetMemberInfo, err := GetUPetMemberInfoByScrmUserId(params.MemberId,
		"member_id,member_name,member_email,member_mobile,member_isvip,member_isbzk")

	if err != nil {
		return err, "获取商城的用户信息失败"
	}
	if UPetMemberInfo.MemberId == 0 {
		return errors.New("未获取到商城用户信息"), "未获取到商城用户信息"
	}
	// v6.23.0 秒杀活动优化 - 秒杀下单数上限
	//秒杀活动id
	promotionId := params.OrderProductModel[0].PromotionId
	seckillOrderCnt := 0
	_, err = u.session.SQL("select seckill_order_cnt  from dc_activity.promotion where id=?", promotionId).Get(&seckillOrderCnt)
	if err != nil {
		return err, "获取秒杀活动信息失败"
	}
	if params.OrderType == 12 && seckillOrderCnt > 0 {
		memberSeckillOrderCnt := 0
		sql := `select
				count(a.order_sn)
			from
				dc_order.order_main a
			left join dc_order.order_product b on
				a.order_sn = b.order_sn
			where
			 a.parent_order_sn ="" and a.order_type = 12 and a.order_status!=0 and b.promotion_id =? and b.sku_id =? and a.member_id=? `
		_, err := u.session.SQL(sql, promotionId, cast.ToInt32(params.OrderProductModel[0].Sku), params.MemberId).Get(&memberSeckillOrderCnt)

		if err != nil {
			glog.Infof("计算该用户秒杀活动商品下单数失败,错误：%s", err.Error())
			return err, "计算该用户秒杀活动商品下单数失败"
		}

		glog.Infof("秒杀下单数上限：%s| %d|%d|%s|%d|%d", sql, promotionId, cast.ToInt32(params.OrderProductModel[0].Sku), params.MemberId, memberSeckillOrderCnt, seckillOrderCnt)
		if memberSeckillOrderCnt >= seckillOrderCnt {
			return errors.New("已超秒杀活动商品下单上限，可选择其它活动商品"), "达到秒杀下单数上限"
		}
	}
	if UPetMemberInfo.MemberMobile == "" {
		UPetMemberInfo.MemberMobile = params.MemberTel
	}

	paySn.MemberId = UPetMemberInfo.MemberId
	//支付单号生成
	addPay.PaySn = cast.ToInt64(paySn.Generate())
	addPay.BuyerId = UPetMemberInfo.MemberId

	affected, err := u.session.Insert(&addPay)
	if err != nil {
		return err, "插入支付数据出错"
	}
	if affected == 0 {
		return errors.New("插入支付数据失败"), "插入支付数据失败"
	}

	//--------------循环插入开始（当前只有秒杀订单 仅有一个商品 不存在循环根据门店拆单的情况）----

	//生成订单数据 插入 upet_orders 表
	//查询商品信息
	orderSkuId := cast.ToInt64(params.OrderProductModel[0].Sku)
	productInfo, err := GetUPetGoodsInfo(orderSkuId, "gc_id,goods_sku,store_id,spec_name,goods_image,goods_spec,goods_type,goods_barcode,warehouse_type,is_dis,dis_commis_rate", params.OrgId)
	if err != nil {
		return errors.New("获取商品信息失败" + err.Error()), "获取商品信息失败"
	}
	//修改入参

	orderSn.PayId = addPay.PayId
	addOrder.OrderSn = cast.ToInt64(orderSn.Generate())
	addOrder.PaySn = addPay.PaySn          //生成的支付号
	addOrder.StoreId = productInfo.StoreId //写死瑞鹏自营门店 也可以使用传过来的shopid
	addOrder.StoreName = params.ShopName
	addOrder.BuyerId = UPetMemberInfo.MemberId
	addOrder.BuyerName = UPetMemberInfo.MemberName
	addOrder.BuyerEmail = GrpcContext.UserInfo.Email
	addOrder.EncryptMobile = utils.MobileEncrypt(UPetMemberInfo.MemberMobile)
	addOrder.BuyerPhone = MobileReplaceWithStar(UPetMemberInfo.MemberMobile)
	addOrder.AddTime = nowUnixTime
	if params.DisType == 0 {
		params.DisType = 5
	}
	addOrder.DisType = params.DisType

	addOrder.OrderType = params.OrderType

	if GrpcContext.Channel.UserAgent == 1 || GrpcContext.Channel.UserAgent == 2 {
		addOrder.OrderFrom = 3 //来源App
	} else {
		addOrder.OrderFrom = 7
	}

	addOrder.PaymentCode = params.PayType //目前下单未支付 写入的是空数据  商城支付后自己写入
	addOrder.OrderState = 10              //写死未支付状态 暂时不存在到店自提（到店自提会默认已支付）的情况

	addOrder.OrderAmount = kit.FenToYuan(params.Total)      //订单总价 = 商品总价 + 运费 - 优惠
	addOrder.GoodsAmount = kit.FenToYuan(params.GoodsTotal) //商品总价 未加运费，不加包装费，减优惠金额，美团不减优惠金额
	addOrder.ShippingFee = kit.FenToYuan(params.Freight)    //运费
	addOrder.RefundAmount = 0.00

	affected, err = u.session.Insert(&addOrder)
	if err != nil {
		return err, "插入订单数据出错"
	}
	if affected == 0 {
		return errors.New("插入订单数据失败"), "插入订单数据失败"
	}
	//赋值orderId

	//插入 订单信息扩展表 upet_order_common
	addOrderCommon.OrderId = addOrder.OrderId
	addOrderCommon.StoreId = productInfo.StoreId                    //写死瑞鹏自营门店
	addOrderCommon.PromotionTotal = kit.FenToYuan(params.Privilege) // 活动总优惠金额（代金券，满减，平台红包）
	addOrderCommon.OrderMessage = params.BuyerMemo                  //买家留言
	addOrderCommon.InvoiceInfo = params.Invoice                     //发票暂时没实现  字段为空

	//收件信息处理处理
	var (
		street   string
		areaInfo string
	)
	//选择了已经保存的收件地址
	if params.AddressId > 0 {
		//city_id true_name area_info address 18319034357 tel_phone mob_phone
		addressInfo, err := GetUPetAddress(params.AddressId, "city_id,true_name,area_info,address,tel_phone,mob_phone,house_info")
		if err != nil {
			//如果出错 不中断订单流程  则需要与通过订单与日志中的AddressId进行数据补充 如果没有数据 则联系客户处理
			glog.Error(addOrder.OrderSn, params.AddressId, "商城下单查询收件信息失败", err)
		} else {
			if addressInfo.HouseInfo == "" {
				return errors.New("收货地址没有详细门牌号，请重新编辑地址再下单"), "收货地址没有详细门牌号，请重新编辑地址再下单"
			}
			addOrderCommon.ReciverCityId = addressInfo.CityId
			params.ReceiverAddress = addressInfo.AreaInfo + " " + addressInfo.Address
			params.ReceiverPhone = addressInfo.MobPhone
			params.TelPhone = addressInfo.TelPhone
			params.ReceiverName = addressInfo.TrueName
			street = addressInfo.Address    //街道
			areaInfo = addressInfo.AreaInfo //省市区
			//从areaInfo中切割出省市区  如果切割不出来 则当做错误进行处理
			areaSlice := strings.Split(areaInfo, " ")

			//省市区不全的情况下写日志
			if len(areaSlice) < 3 {
				glog.Error(addOrder.OrderSn, "CreateUPetMallOrder-电商下单写入平台收件地址省市区数据不正确")
			}
			if len(areaSlice) >= 1 {
				params.ReceiverState = areaSlice[0]
			}
			if len(areaSlice) >= 2 {
				params.ReceiverCity = areaSlice[1]
			}
			if len(areaSlice) >= 3 {
				params.ReceiverDistrict = areaSlice[2]
			}
		}
	} else {
		addressSlice := strings.Split(params.ReceiverAddress, " ") //todo 联调
		if len(addressSlice) > 3 {
			//使用循环避免 address中后面出现多处空格
			for i := 3; i < len(addressSlice); i++ {
				street = street + addressSlice[i]
			}
		}
		if len(addressSlice) < 3 {
			glog.Error(addOrder.OrderSn, "CreateUPetMallOrder-no-addressId-电商下单写入平台收件地址省市区数据不正确")
		}
		if params.ReceiverState == "" && len(addressSlice) >= 1 {
			params.ReceiverState = addressSlice[0]
		}
		if params.ReceiverCity == "" && len(addressSlice) >= 2 {
			params.ReceiverCity = addressSlice[1]
		}
		if params.ReceiverDistrict == "" && len(addressSlice) >= 3 {
			params.ReceiverDistrict = addressSlice[2]
		}

		areaInfo = params.ReceiverState + " " + params.ReceiverCity + " " + params.ReceiverDistrict //省市区
		addOrderCommon.ReciverCityId = GetAreaIdByName(params.ReceiverCity)
	}

	phone := MobileReplaceWithStar(params.ReceiverPhone)
	if params.TelPhone != "" { //座机
		phone = phone + "," + params.TelPhone
	}
	receiverInfo := map[string]interface{}{
		"phone":          phone,
		"mob_phone":      MobileReplaceWithStar(params.ReceiverPhone), //手机
		"encrypt_mobile": utils.MobileEncrypt(params.ReceiverPhone),
		"tel_phone":      params.TelPhone,
		"address":        params.ReceiverAddress,
		"area":           areaInfo,
		"street":         street,
	}

	byteReceiverInfo, err := gophp.Serialize(receiverInfo) //序列化信息
	if err != nil {
		//如果出错 不中断订单流程  可通过订单相关数据重新写入收件信息
		glog.Error(addOrder.OrderSn, "写入商城订单数据：序列化收件地址出错", err, kit.JsonEncode(receiverInfo))
	}

	addOrderCommon.ReciverInfo = string(byteReceiverInfo)
	addOrderCommon.ReciverName = params.ReceiverName
	addOrderCommon.ReciverDateMsg = params.ReceiverDateMsg

	affected, err = u.session.Insert(&addOrderCommon)
	if err != nil {
		return err, "写入商城订单扩展表出错"
	}
	if affected == 0 {
		return errors.New("写入商城订单扩展表失败"), "写入商城订单扩展表失败"
	}

	//添加订单日志  插入 upet_order_log 表
	addOrderLog.OrderId = addOrder.OrderId
	addOrderLog.LogRole = "买家"
	addOrderLog.LogMsg = "生成订单"
	addOrderLog.LogUser = UPetMemberInfo.MemberName
	addOrderLog.LogOrderstate = "10"
	addOrderLog.LogTime = nowUnixTime

	affected, err = u.session.Insert(&addOrderLog)
	//写失败只写日志 并不返回错误 流程继续
	if err != nil {
		//如果出错 不中断订单流程  可重写
		glog.Error(addOrder.OrderSn, "写入商城订单数据：写入商城订单日志出错", err)
	}
	if affected == 0 {
		//如果出错 不中断订单流程  可重写
		glog.Error(addOrder.OrderSn, "写入商城订单数据：写入商城订单日志失败")
	}

	//添加订单日志  插入 upet_order_goods 表
	addOrderGoods := make([]*models.UpetOrderGoods, len(params.OrderProductModel))
	//获取分销开关配置
	distributeIsUse, err := GetUPetConfig("distribute_isuse")
	if err != nil {
		//如果出错 不中断订单流程  会导致无法产生分销 后期处理
		glog.Error(addOrder.OrderSn, "写入商城订单数据：查询分销配置distribute_isuse出错", err)
	}

	//如果是开启了分销
	if distributeIsUse == "1" {
		disMemberInfo, err = GetDisMemberInfo(UPetMemberInfo.MemberId)
		//如果出错 不中断订单流程  导致无法产生分销收益到相关人员 后期处理
		if err != nil {
			glog.Error(addOrder.OrderSn, "写入商城订单数据：查询分销员信息出错", err)
		}
	}
	var (
		orderIsDis          bool //是否是分销订单
		emptyOrderPromotion bool //传过来的order_promotions 是否为空 如果为空的话  商品如果参加了活动要将活动信息写到param.orderPromotions
		promotionMap        = make(map[string]struct{})
		isGroupGoods        int
	)

	if len(params.OrderPromotion) == 0 {
		emptyOrderPromotion = true
	}

	//实时组合商品写进电商order_goods表的数据是组合商品
	product := &oc.OrderProductModel{}
	if len(params.OrderProductModel) > 0 {
		for i, orderProduct := range params.OrderProductModel {
			if orderProduct.ProductType == 3 && orderProduct.CombineType == 31 { //实实组合商品
				product = params.OrderProductModel[i]
				isGroupGoods = 1
			}
		}
		if len(product.Sku) == 0 {
			product = params.OrderProductModel[0]
		}
	}
	//商品循环开始  秒杀只有一个商品如果存在多个 则需要使用循环----------
	//前端传过来的商品信息 仅有一个商品
	//product := params.OrderProductModel[0]
	var productItem models.UpetOrderGoods
	productItem.OrderId = addOrder.OrderId
	productItem.GoodsId = cast.ToInt64(product.Sku)
	productItem.GoodsCommonid = cast.ToInt(product.ProductId)

	productItem.StoreId = productInfo.StoreId
	productItem.GoodsName = product.ProductName

	productItem.GoodsPrice = kit.FenToYuan(product.PayPrice)
	productItem.GoodsPayPrice = kit.FenToYuan(product.PayPrice * product.Number)

	productItem.AddTime = nowUnixTime
	productItem.BuyerId = UPetMemberInfo.MemberId
	//去掉param中的image中带参数的缩略图
	index := strings.Index(product.Image, "|")
	if index > 0 {
		product.Image = product.Image[0:index]
	}
	productItem.GoodsImage = product.Image //不能使用param中的img 因为里面带了缩略图参数  在页面显示中php脚本还会加上其他参数导致无法显示 直接读电商库里的图片
	productItem.GoodsNum = product.Number
	productItem.CommisRate = 200 //写死的
	productItem.GcId = productInfo.GcId
	productItem.Sku = productInfo.GoodsSku
	productItem.IsGroupGoods = isGroupGoods

	if err != nil {
		//读取失败会导致分销失败  可以后续处理 不阻止下单
		glog.Error(addOrder.OrderSn, productItem.GoodsId, productItem.GoodsCommonid, "写入商城订单数据：查询商城商品spu信息失败", err)
	}

	//分销配置打开 且商品是分销商品 且支付价格大于1元
	//修改入参 订单类型
	params.OrderPayType = "03"
	if distributeIsUse == "1" && productInfo.IsDis == 1 && disMemberInfo.DisMemberId > 0 && product.PayPrice >= 100 {
		orderIsDis = true //一个商品存在分销 则是分销订单
		productItem.IsDis = 1
		productItem.DisCommisRate = productInfo.DisCommisRate
		productItem.OutMemberId = disMemberInfo.OutsideMemberId
		productItem.DisMemberId = cast.ToInt(disMemberInfo.DisMemberId)
		productItem.ChainId = cast.ToInt(disMemberInfo.ChainId)
		//修改入参 订单类型
		params.OrderPayType = "04"
		// 内部分销员，判断绑定客服
		if disMemberInfo.ChainId > 0 {
			chainBind := new(models.UpetChainBind)
			if has, err := u.session.Where("chain_member_mobile = ?", disMemberInfo.MemberMobile).
				Select("member_id,cash_ratio").Get(chainBind); err != nil {
				return err, "查询客服绑定关系出错"
			} else if has && chainBind.MemberId != disMemberInfo.DisMemberId {
				productItem.CustomerServiceId = int(chainBind.MemberId)
				productItem.CustomerServiceRate = chainBind.CashRatio
			}
		}
	}
	if product.Specs == "" {
		//如果没有 读取商城里的数据
		//修改入参  商品数据的Specs
		product.Specs, err = GetUPetGoodsSpec(productInfo.SpecName, productInfo.GoodsSpec)
		if err != nil {
			//如果出错 不影响下单  可后续补数据
			glog.Error(addOrder.OrderSn, productItem.GoodsId, "写入商城订单数据：解码商品规格信息失败", err)
		}
	}
	productItem.GoodsSpec = product.Specs

	//如果参数的order_promotion数据则写入
	if emptyOrderPromotion && product.PromotionId > 0 {
		checkHasKey := cast.ToString(product.PromotionType) + "-" + cast.ToString(product.PromotionId)
		if _, ok := promotionMap[checkHasKey]; !ok {
			params.OrderPromotion = append(params.OrderPromotion, &oc.OrderPromotionModel{
				PromotionId:   product.PromotionId,
				PromotionType: product.PromotionType,
			})
			promotionMap[checkHasKey] = struct{}{}
		}
		//todo 非秒杀 计算优惠费用
	}

	affected, err = u.session.Insert(&productItem)

	//放入列表 用于后续处理
	addOrderGoods[0] = &productItem
	if err != nil {
		return err, "写入商城订单商品表出错"
	}
	if affected == 0 {
		return errors.New("写入商城订单商品表失败"), "写入商城订单扩展表失败"
	}
	//商品循环结束----------
	//针对组合商品依次给加上recId
	for _, orderProduct := range params.OrderProductModel {
		orderProduct.MallOrderProductId = cast.ToInt64(productItem.RecId)
	}
	//修改入参-让数据适配落地平台- 部分参数以前在商城后端做的需要在此处处理 -Specs OrderPayType 在上面的逻辑中已修改------------------------------------------------
	//product.ProductType = 1
	if productInfo.GoodsType > 0 { //组合商品
		product.ProductType = 3
	}
	product.MallOrderProductId = cast.ToInt64(productItem.RecId)
	//秒杀订单
	product.PromotionType = models.ParsePromotionTypeFromOrderType(params.OrderType)
	product.BarCode = productInfo.GoodsBarcode
	product.WarehouseType = productInfo.WarehouseType

	params.OrderSn = cast.ToString(addOrder.OrderSn)
	params.PaySn = cast.ToString(addPay.PaySn)
	params.OrderId = cast.ToInt32(addOrder.OrderId)

	//写失败只写日志 并不返回错误 流程继续

	// ---------------循环结束-------------

	//订单分销信息更新
	if orderIsDis {
		if UPetMemberInfo.MemberId == disMemberInfo.DisMemberId {
			updateOrder.DisType = 3
		}
		updateOrder.IsDis = 1
	} else {
		updateOrder.DisType = addOrder.DisType
	}
	updateOrder.ChainId = disMemberInfo.ChainId
	//更新分销相关字段
	_, err = u.session.Where("order_id=?", addOrder.OrderId).Cols("is_dis,dis_type,chain_id").Update(updateOrder)
	if err != nil {
		return err, "更新订单分销信息失败"
	}

	u.orderGoods = addOrderGoods

	//在此处提交 订单
	return nil, ""
}

// AfterCreateMallOrder 创建商城订单后的处理
// 处理失败不阻止下单 只打印相关日志
func (u *UPetService) AfterCreateMallOrder() {
	//更新商品的售卖信息
	defer kit.CatchPanic()
	if len(u.orderGoods) == 0 {
		return
	}
	for _, product := range u.orderGoods {
		//获取商品信息
		productInfo, err := GetUPetGoodsInfo(cast.ToInt64(product.GoodsId), "goods_id,is_virtual,goods_salenum,goods_storage,goods_serial,goods_body,goods_commonid,spec_name,goods_spec,gc_id", product.StoreId)

		if err != nil {
			glog.Error(product.OrderId, product.GoodsId, "AfterCreateMallOrder-商城下单后更新商品查询出错", err)
			continue
		}

		if productInfo.GoodsId == 0 {
			glog.Error(product.OrderId, product.GoodsId, "AfterCreateMallOrder-商城下单后更新商品的销售量未查询到商品信息")
			continue
		}

		//更新商品的售卖数 库存信息
		var updateGoods models.UpetGoods
		updateField := "goods_salenum"
		updateGoods.GoodsSalenum = productInfo.GoodsSalenum + product.GoodsNum
		glog.Info("秒杀订单更新销量", product.GoodsId, product.OrderId, updateGoods.GoodsSalenum, u.orderGoods)
		if productInfo.IsVirtual == 1 {
			updateField = updateField + ",goods_storage"
			updateGoods.GoodsStorage = productInfo.GoodsStorage - product.GoodsNum
		}

		_, err = u.session.Cols(updateField).Where("goods_id = ? AND store_id=?", product.GoodsId, product.StoreId).Update(&updateGoods)
		if err != nil {
			glog.Error(product.OrderId, "AfterCreateMallOrder-商城下单后更新商品的销售量字段出错", err)
		}

		_, err = u.session.Exec("UPDATE upet_goods_common SET  sale_count=sale_count + ? WHERE goods_commonid = ?", product.GoodsNum, product.GoodsCommonid)
		if err != nil {
			glog.Error(product.OrderId, "AfterCreateMallOrder-商城下单后更新商品扩展表的销售量字段出错", err)
		}

		//生成快照信息  生成快照 不写file_dir字段  该字段是电商写入的  bbc后台直接读该文件即可渲染快照信息，
		//golang改版是直接将 文件需要的内容的三个字段PlateidTop，PlateidBottom，GoodsBody写入快照表 并标识source为1，
		//BBC后台读取时将三个字段的内容渲染到一个文件来完成快照信息的渲染
		productCommonInfo, err := GetUPetGoodsCommonInfo(productInfo.GoodsCommonid, "brand_name,goods_attr,goods_custom,goods_body,plateid_top,plateid_bottom")
		if err != nil {
			glog.Error(product.OrderId, product.GoodsId, productInfo.GoodsCommonid, "AfterCreateMallOrder-商城下单后更新商品spu查询出错", err)
			continue
		}

		addSnap := new(models.UpetOrderSnapshot)
		addSnap.RecId = product.RecId
		addSnap.GoodsId = product.GoodsId
		addSnap.CreateTime = time.Now().Unix()

		addSnap.PlateidTop, err = GetStorePlateInfoByID(productCommonInfo.PlateidTop)
		if err != nil {
			glog.Error(product.OrderId, product.GoodsId, "AfterCreateMallOrder-商城下单后更新快照查询PlateidTop出错", err)
		}
		addSnap.PlateidBottom, err = GetStorePlateInfoByID(productCommonInfo.PlateidBottom)
		if err != nil {
			glog.Error(product.OrderId, product.GoodsId, "AfterCreateMallOrder-商城下单后更新快照查询PlateidBottom出错", err)
		}
		addSnap.GoodsBody = productInfo.GoodsBody

		//商品的属性信息，电商使用二维化的序列化字符串保存  需要进行解码
		goodsAttr := make(map[string]string)
		GoodsAttrMap, err := UnSerializeAttrInfo(productCommonInfo.GoodsAttr)
		if err != nil {
			glog.Error(product.OrderId, productInfo.GoodsCommonid, "AfterCreateMallOrder-生成订单快照解码商品属性出错", err)
		} else if len(GoodsAttrMap) > 0 {
			for key, val := range GoodsAttrMap {
				goodsAttr[key] = val
			}
		}

		GoodsCustomMap, err := UnSerializeAttrInfo(productCommonInfo.GoodsCustom)
		if err != nil {
			glog.Error(product.OrderId, productInfo.GoodsCommonid, "生成订单快照解码good_custom属性出错", err)
		} else if len(GoodsCustomMap) > 0 {
			for key, val := range GoodsCustomMap {
				goodsAttr[key] = val
			}
		}
		serializeGoodsAttr, err := gophp.Serialize(goodsAttr)

		if err != nil {
			glog.Error(product.OrderId, productInfo.GoodsCommonid, "生成订单快照解码商品属性出错", err)
		}

		goodsAttr["货号"] = productInfo.GoodsSerial
		goodsAttr["品牌"] = productCommonInfo.BrandName

		addSnap.GoodsAttr = string(serializeGoodsAttr)
		addSnap.Source = 1

		affected, err := u.session.Insert(addSnap)
		if err != nil {
			glog.Error(product.OrderId, product.GoodsId, "生成订单快照出错", err)
		}
		if affected == 0 {
			glog.Error(product.OrderId, "生成订单快照失败")
		}
	}
}

package dto

import "time"

type RefundOrderPayDto struct {
	//商户号
	MerchantId string `json:"merchantId,omitempty"`
	//交易流水号
	TradeNo string `json:"tradeNo,omitempty"`
	//退款金额，以分为单位
	RefundAmt int `json:"refundAmt,omitempty"`
	//后台回调地址
	CallbackUrl string `json:"callbackUrl,omitempty"`
	//商户私有域：交易返回时原样返回给商户网站，给商户备用
	BackParam string `json:"backParam,omitempty"`
	//扩展信息：预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo,omitempty"`
	//客户端 IP ：如 127.0.0.1
	ClientIP string `json:"clientIP,omitempty"`
	//签名
	Sign     string `json:"sign,omitempty"`
	RefundId string `json:"refundId"` //商户退款订单号
}

// 退款查询
type PayRefundQueryRequest struct {
	MerchantId    string `json:"merchantId"`    // 商户号
	TransactionNo string `json:"transactionNo"` // 支付中心流水号
	ClientIP      string `json:"clientIP"`      // 客户端 IP
	RefundId      string `json:"refundId"`      // 退款订单号
	RefundAmt     int32  `json:"refundAmt"`     // 退款金额
	Sign          string `json:"sign"`          // 签名
}

type PayRefundQueryResponse struct {
	Code    int    `json:"code,omitempty"`
	Message string `json:"message,omitempty"`
	Data    struct {
		BackParam     string `json:"backParam"`
		CallbackUrl   string `json:"callbackUrl"`
		ClientIP      string `json:"clientIP"`
		ExtendInfo    string `json:"extendInfo"`
		RefundAmt     string `json:"refundAmt"`
		RefundId      string `json:"refundId"`
		RspCode       string `json:"rspCode"` //退款状态（退款状态 0：未退款 1：退款成功 2：退款处理中 3：退款失败）
		RspMessage    string `json:"rspMessage"`
		TransactionNo string `json:"transactionNo"`
		MerchantId    string `json:"merchantId"`
		Sign          string `json:"sign"`
	} `json:"data,omitempty"`
}

type RefundOrderQueryDto struct {
	//商户号
	MerchantId string `json:"merchantId,omitempty"`
	//交易流水号
	TradeNo string `json:"tradeNo,omitempty"`
	//签名
	Sign string `json:"sign,omitempty"`
	// 用于签名， 如果是逍宠的， 用的是阿闻的签名 AppId传9
	AppId int32 `json:"appId"`
}

type QueryResult struct {
	Code    int     `json:"code"`
	Message string  `json:"message"`
	ResData DataDto `json:"data"`
}

type DataDto []struct {
	OrderID    string `json:"order_id"`
	OrderTime  string `json:"order_time"`
	PayTradeNo string `json:"pay_trade_no"`
	TotalPrice int    `json:"total_price"`
	PayPrice   int    `json:"pay_price"`
	Refund     int    `json:"refund"`
	// 	订单状态 0：未支付 1：已支付 2：部分退款 3：全部退款
	PayStatus    int             `json:"pay_status"`
	Status       string          `json:"status"`
	RefundDetail []*RefundDetail `json:"refund_detail"`
}

type RefundDetail struct {
	OrderID       string `json:"order_id"`
	RefundTradeNo string `json:"refund_trade_no"`
	//退款状态 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	RefundStatus int    `json:"refund_status"`
	RefundAmount int    `json:"refund_amount"`
	Status       string `json:"status"`
}

type MStoreCardUseRecord struct {
	Id                  int64     `json:"id" xorm:"pk autoincr not null comment('唯一数据id') BIGINT 'id'"`
	ChainId             int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId            int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RecordId            int64     `json:"record_id" xorm:"not null default 0 comment('卡记录id') BIGINT 'record_id'"`
	CardId              int64     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo              string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName            string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	OperateType         string    `json:"operate_type" xorm:"not null default '' comment('操作类型:CONSUMPTION消费,TOP_UP充值,REFUND退款,RECONCILIATION调账,RETURN_CARD退卡') VARCHAR(16) 'operate_type'"`
	BalanceChange       float64   `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	PrincipalChange     float64   `json:"principal_change" xorm:"not null default '0.0000' comment('本金变动') DECIMAL(18) 'principal_change'"`
	GiftChange          float64   `json:"gift_change" xorm:"not null default '0.0000' comment('赠送金变动') DECIMAL(18) 'gift_change'"`
	Balance             float64   `json:"balance" xorm:"not null default '0.0000' comment('本金余额') DECIMAL(18) 'balance'"`
	GiftBalance         float64   `json:"gift_balance" xorm:"not null default '0.0000' comment('赠送金余额') DECIMAL(18) 'gift_balance'"`
	ReturnableGift      float64   `json:"returnable_gift" xorm:"not null default '0.0000' comment('可退赠金') DECIMAL(18) 'returnable_gift'"`
	ReturnablePrincipal float64   `json:"returnable_principal" xorm:"not null default '0.0000' comment('可退本金') DECIMAL(18) 'returnable_principal'"`
	OrderId             int64     `json:"order_id" xorm:"not null default 0 comment('订单id') BIGINT 'order_id'"`
	OrderNo             string    `json:"order_no" xorm:"not null default '' comment('订单编号') VARCHAR(32) 'order_no'"`
	CustomerId          int64     `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	EmployeeId          int64     `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	GiftPackage         string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	Remark              string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(200) 'remark'"`
	Status              string    `json:"status" xorm:"not null default '' comment('数据状态') VARCHAR(16) 'status'"`
	IsDeleted           bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy           int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime         time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy           int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime         time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

package tasks

import (
	"encoding/json"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/models"
	"order-center/services"
	"order-center/utils"
	"time"
)

func refundDeliveryTask() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "order-center:task:lock:refund_order_delivery"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	timingProcessingRefundDelivery()
}
func timingProcessingRefundDelivery() bool {
	conn := services.GetDBConn()
	var refundDeliveryList []models.RefundOrderDelivery
	err := conn.Where("status = ?", 0).Find(&refundDeliveryList)
	if err != nil {
		return false
	}

	for _, refund := range refundDeliveryList {
		if refund.DeliveryFee > 0 {
			url := utils.PayCenterUrl + "/pay/refund"
			refundOrderPayDto := dto.RefundOrderPayDto{
				MerchantId:  utils.MerchantId,
				TradeNo:     refund.PaySn, //原来的销售单号
				RefundAmt:   refund.DeliveryFee,
				CallbackUrl: "https://awen.sit.rvet.cn",
				ClientIP:    utils.GetClientIp(),
			}
			jsonData := kit.JsonEncode(refundOrderPayDto)
			glog.Info("配送费退款参数：order_sn:"+refund.OrderSn+",parent_order_sn:"+refund.ParentOrderSn, jsonData)
			//签名并获取 组装From 参数
			_, fromData := utils.PayCenterSign(jsonData)

			//获取结果
			result, err := utils.HttpPost(url, []byte(fromData), utils.ContentTypeToForm)
			glog.Info("配送费退款-支付中心返回结果：", refund.OrderSn, string(result))
			if err != nil {
				glog.Error("配送费退款-支付中心错误:"+jsonData, err.Error())
				continue
			}
			var response dto.RefundOrderPayResponse
			err = json.Unmarshal(result, &response)
			if err != nil {
				glog.Error("配送费退款-支付中心错误:"+jsonData+":"+string(result), err)
				continue
			}

			if response.Code != 200 {
				glog.Error("支付中心退款支付接口返回不成功, ", jsonData, ", ", string(result), response)
				updateRefundDeliveryStatus(refund.Id, 2, response.Message, 0, "")
			} else {
				updateRefundDeliveryStatus(refund.Id, 1, "", refund.DeliveryFee, response.Data.RefundId)
			}
		} else {
			updateRefundDeliveryStatus(refund.Id, 2, "配送费为0，不可退", 0, "")
		}
	}
	return true
}
func updateRefundDeliveryStatus(id int64, status int, failReason string, refundDeliveryFee int, refundId string) error {
	conn := services.GetDBConn()
	_, err := conn.Where("id = ?", id).Cols("status", "fail_reason", "refund_delivery_fee", "refund_id").Update(models.RefundOrderDelivery{
		Status:            status,
		FailReason:        failReason,
		RefundDeliveryFee: refundDeliveryFee,
		RefundId:          refundId,
	})
	if err != nil {
		return err
	}
	return nil
}

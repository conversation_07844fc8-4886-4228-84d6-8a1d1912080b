syntax = "proto3";

package ac;

import "ac/activity_model.proto";

service CycleBuyService {
  //周期购活动列表 boss
  rpc GetCycleBuyList(CycleBuyListRequest) returns (CycleBuyListResponse);
  //创建周期购活动
  rpc CreateCycleBuy(CycleBuyRequest) returns (baseResponse);
  //更新周期购活动
  rpc UpdateCycleBuy(CycleBuyRequest) returns (baseResponse);
  //周期购活动详情
  rpc GetCycleBuyDetail(CycleBuyIdRequest) returns (CycleBuyDetailResponse);
  //终止周期购活动
  rpc StopCycleBuy(CycleBuyIdRequest) returns (baseResponse);
  //统计周期购活动每个月未发货商品数
  rpc CycleBuyUnshipStatistics(CycleBuyUnshipStatisticsRequest) returns (CycleBuyUnshipStatisticsListResponse);

  //获取周期购商品列表 boss
  rpc GetCycleBuyProductList (GetCycleBuyProductListRequest) returns (GetCycleBuyProductListResponse);
  //创建周期购商品
  rpc CreateCycleBuyProduct (CreateCycleBuyProductRequest) returns (baseResponse);
  //更新周期购商品
  rpc UpdateCycleBuyProduct (UpdateCycleBuyProductRequest) returns (baseResponse);
  //获取周期购商品详情
  rpc GetCycleBuyProductDetail(CycleBuyProductDetailRequest) returns (GetCycleBuyProductDetailResponse);
  //删除周期购商品
  rpc DeleteCycleBuyProduct(CycleBuyProductIdRequest) returns (baseResponse);
  // 获取可以参加周期购活动的阿闻电商渠道的商品
  rpc GetCycleBuyUPetProductSelectList(GetCycleBuyUPetProductSelectListRequest) returns (GetCycleBuyUPetProductSelectListResponse);
  // 周期购下单回调
  rpc CycleBuyOrderStaticCallBack(CycleBuyOrderStaticRequest) returns (baseResponse);


  // 周期购活动商品列表 product-api
  rpc CycleBuyProductList (CycleBuyProductListRequest) returns (CycleBuyProductListResponse);
  // 收藏 周期购活动商品
  rpc AddCycleBuyCollect(AddCycleBuyCollectRequest) returns (baseResponse);
  // 周期购活动商品 收藏列表
  rpc CycleBuyCollectList(CycleBuyCollectListRequest) returns (CycleBuyCollectListResponse);
  // 周期购活动商品 删除收藏
  rpc DelCycleBuyCollect(DelCycleBuyCollectRequest) returns (baseResponse);
  // 周期购活动 商品 收藏状态
  rpc CycleBuyCollectDetail(DelCycleBuyCollectRequest) returns (CycleBuyCollectDetailResponse);

  // 我的周期购主订单列表 mall用
  rpc GetCycleOrderList(CycleOrderListRequest) returns (CycleOrderListResponse);

}


//周期购列表请求
message CycleBuyListRequest {
  //活动状态：-5待添加商品审核，-4审核失败，-3:待提交，-2:待审核, -1 删除 0未开始 1进行中 2已结束 3已终止,如果要获取全部状态的活动，该参数传99
  int32 status = 1;
  string title = 2;
  int32 page_index = 3;
  int32 page_size = 4;
  //排序
  string order_by = 6;
}

//创建周期购活动请求
message CycleBuyRequest {
  // 活动id
  int32 id = 1;
  // 活动名称
  string title = 2;
  // 活动开始时间
  string begin_date = 3;
  // 活动结束时间
  string end_date = 4;
  // 活动状态：-3:待提交，-2:待审核, -1 删除 0未开始 1进行中 2已结束 3已终止
  int32 status = 5;
  //活动是否免邮费
  int32 is_shipping_free = 6;
  //配送周期类型 1=3期 2=6期 3=12期
  string cycle_ids = 7;
  //渠道id
  int32 channel_id = 8;
  //创建时间
  string create_time = 9;
  //更新时间
  string update_time = 10;
  // 参加活动的商品数量
  int32 product_count = 11;
  // 创建和编辑活动时，记录操作人
  //用户Id，即userno
  string  user_id = 12;
  //userName,即登录人姓名
  string user_name = 13;
  //异常数量统计
  int32  exception_count = 14;
  //商品总数
  int32  goods_total = 15;
  // 审核原因
  string check_reason = 16;
}
//周期购列表响应
message CycleBuyListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //列表
  repeated CycleBuyRequest list = 4;
  //总条数
  int32 total = 5;

}



//周期购ID
message CycleBuyIdRequest {
  int32 id = 1;
  string user_id = 2;
  string user_name = 3;

}
//周期购详情相应
message CycleBuyDetailResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  CycleBuyRequest detail = 4;
}

//统计周期购活动每个月未发货商品数 请求数据
message CycleBuyUnshipStatisticsRequest{
  int32 page_index = 1;
  int32 page_size = 2;
  string product_name = 3;
  //周期购活动id
  int32 cid = 4;

}
//统计周期购活动每个月未发货商品数 响应数据
message CycleBuyUnshipStatisticsListResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  int32 total = 4;

  repeated CycleProductTotal list = 5;

}
// 统计 周期购活动 未发货 商品总数
message CycleProductTotal{
  int32 sku_id = 1;
  string product_name = 2;
  int32 jan = 3;
  int32 feb = 4;
  int32 mar = 5;
  int32 apr = 6;
  int32 may = 7;
  int32 jun = 8;
  int32 jul = 9;
  int32 aug = 10;
  int32 sep = 11;
  int32 oct = 12;
  int32 nov = 13;
  int32 dec = 14;
  int32 cid = 15;
}
//周期购商品列表的请求数据
message GetCycleBuyProductListRequest {
  //周期购活动id
  int32 cid = 1;
  //产品名称
  string productName = 2;
  //商品sku id
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //商品的产品id
  int32 channelId = 5;
  //商品状态 -1删除 0默认
  int32 status = 6;
  //排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
  int32 orderBy = 7;
  //分页参数
  PaginationParam pagination = 8;
  //是否导出 1导出
  int32 export =9;
  // 1异常商品
  int32 type = 10;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 11;
}

//周期购商品列表
message GetCycleBuyProductListResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //周期购商品信息
  repeated CycleBuyProductData data = 5;
  //异常商品数
  int32 nonormal_num = 6;
  //正常商品数
  int32 normal_num = 7;
}

//新增周期购活动商品
message CreateCycleBuyProductRequest {
  //商品sku id
  int32 skuId = 1;
  //商品的产品id
  int32 productId = 2;
  //主体信息
  SaveCycleBuyProductData saveData = 3;
   //主体：1-阿闻，2-极宠家
   int32 org_id = 4;
}

// 更新周期购商品
message UpdateCycleBuyProductRequest {
  //需要更新的记录id
  int32 id = 1 ;
  //商品sku id
  int32 skuId = 2;
  //商品的产品id
  int32 productId = 3;
  //主体信息
  SaveCycleBuyProductData saveData = 4;
   //主体：1-阿闻，2-极宠家
   int32 org_id = 5;
}

// 周期购商品只需要id的请求
message CycleBuyProductDetailRequest {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 cid = 2;
  //商品skuId
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //渠道 1电商 5商城
  int32 channelId = 5;
   //主体：1-阿闻，2-极宠家
   int32 org_id = 6;
}

//获取周期购商品信息
message GetCycleBuyProductDetailResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //周期购商品信息
  CycleBuyProductDetailData data = 4;
}

//周期购商品只需要id的请求
message CycleBuyProductIdRequest {
  //活动商品信息记录id
  int32 id = 1;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 2;
}

//分页参数
message PaginationParam {
  //当前多少页 从1开始 必传且必须大于0
  int32 pageIndex = 1;
  //每页多少条数据 必传且必须大于0
  int32 pageSize = 2;
}

//周期购商品详细数据包含部分周期购信息
message CycleBuyProductDetailData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 cid = 2;
  //渠道ID
  int32 channelId = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 singlePrice = 7;
  //3期价 单位分
  int32 threePrice = 8;
  //6期价 单位分
  int32 sixPrice = 9;
  //12期价 单位分
  int32 twelvePrice = 10;
  //状态 -1删除 0默认
  int32 status = 11;
  //创建时间
  string createTime = 12;
  //更新时间
  string updateTime = 13;

  //周期购信息
  //开始时间
  string beginDate = 14;
  //结束时间
  string endDate = 15;
  //配送周期类型 1=3期 2=6期 3=12期
  string cycleIds = 16;
  //是否免邮费 0否1是
  int32 isShippingFree = 17;

  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 18;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 19;
}

//周期购商品数据
message CycleBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 cid = 2;
  //渠道ID
  int32 channelId = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 singlePrice = 7;
  //3期价 单位分
  int32 threePrice = 8;
  //6期价 单位分
  int32 sixPrice = 9;
  //12期价 单位分
  int32 twelvePrice = 10;
  //状态 -1删除 0默认
  int32 status = 11;
  // 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
  int32 canBeEdited = 12;
  // 是否可被删除 0 不可删除 1 可删除 用于boss后台
  int32 canBeDeleted = 13;
  // 商品图片
  string pic = 14;
  // 商品库存
  int32 stock = 15;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 16;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 17;
  //创建时间
  string createTime = 18;
  //更新时间
  string updateTime = 19;
  //商品3期折扣率
  double threePriceRatio = 34;
  //商品6期折扣率
  double sixPriceRatio = 35;
  //商品12期折扣率
  double twelvePriceRatio = 36;
  // 单价是否异常 1:异常 0：正常
  int32 isNormal = 37;
  // 3期是否异常 1:异常 0：正常
  int32 isNormalThree = 38;
  // 6期是否异常 1:异常 0：正常
  int32 isNormalSix = 39;
  // 12期是否异常 1:异常 0：正常
  int32 isNormalTwelve = 40;
  //R1集采价
  int32 R1PurchasePrice = 41;
  // 是否标记 1:是 0：否
  int32 is_mark = 42;
  // 标记为正常的异常折扣
  double MarkDiscount =43;
  // 标记为正常的采购价(分)
  int32 MarkPurchasePrice =44;
}

//添加/编辑周期购商品
message SaveCycleBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //所属活动id
  int32 cid = 2;
  //商品名称
  string productName = 6;
  //单买价 单位分
  int32 singlePrice = 7;
  //3期价 单位分
  int32 threePrice = 8;
  //6期价 单位分
  int32 sixPrice = 9;
  //12期价 单位分
  int32 twelvePrice = 10;
}

//阿闻电商参加周期购活动的商品
message GetCycleBuyUPetProductSelectListRequest {
  //活动id
  int32 cid = 1;
  //商品sku_id 对应商城的goods_id
  int32 skuId = 2;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 3;
  // 商品名称
  string productName = 4;
  //分页参数
  PaginationParam pagination = 5;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 6;
}

//阿闻电商参加周期购活动的商品
message GetCycleBuyUPetProductSelectListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //周期购商品信息
  repeated CycleBuySelectUPetProductData data = 5;
}

message CycleBuySelectUPetProductData {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 1;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 2;
  // 商品名称
  string productName = 3;
  // 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
  int32 timeConflict = 4;
  //商品图片
  string pic = 5;
  //库存
  int32 stock = 6;
  //价格 单位分
  int32 marketPrice = 7;
  //是否时虚拟产品 1是 0 否
  int32 isVirtual = 8;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 9;
  //组合商品的子商品信息
  repeated Child childSkuIds = 10;
}

//组合商品子商品讯息
message Child {
  int32 skuId = 1;
  //规则
  int32 ruleNum = 2;
  //是否为虚拟 0:不是 1：是虚拟
  int32 isVirtual = 3;
  //0不是药品仓 1药品仓
  int32 stockWarehouse = 7;
}



//周期购活动 商品列表请求数据 product-api用
message CycleBuyProductListRequest{
  // 页码
  int32 page_index = 1;
  // 每页显示条数
  int32 page_size = 2;
  // 渠道ID
  int32 channel_id = 3;
  //活动id 多个用逗号隔开
  string cids = 4;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 5;

}

//周期购活动 商品列表响应数据 product-api用
message CycleBuyProductListResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;

  repeated CycleBuyProduct data=5;
}
//周期购活动商品信息
message CycleBuyProduct{
  //活动id
  int32 cid = 1;
  //渠道id
  int32 channel_id=2;
  //商品skuID
  int32 sku_id = 3;
  //产品id
  int32 product_id = 4;
  //产品名称
  string product_name = 5;
  //商品图片
  string product_img = 6;
  // 单买价 单位分
  int32 SinglePrice = 7;
  //3期价 单位分
  int32 ThreePrice =  8;
  //6期价 单位分
  int32 SixPrice = 9;
  //12期价 单位分
  int32 TwelvePrice = 10;
  //R1集采价 单位分
  int32 R1PurchasePrice = 11;
  //商品折扣率
  double priceRatio = 12;
}

//收藏 周期购活动商品 请求参数
message AddCycleBuyCollectRequest{
  //商品skuID
  int32 sku_id = 1;
  //用户scrm_id
  string user_id = 2;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 3;
}

// 周期购活动 收藏列表
message CycleBuyCollectListRequest{
  // 页码
  int32 page_index = 1;
  // 每页显示条数
  int32 page_size = 2;
  // 排序 创建时间倒叙
  string order_by = 3;
  // 用户id
  string user_id = 4;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 5;
}

message CycleBuyCollectListResponse{
  int32 code = 1;
  string message =2;
  string error = 3;
  int32 total =4;
  repeated CycleBuyCollect data = 5;
}
message CycleBuyCollectDetailResponse {
  int32 code = 1;
  string message =2;
  string error = 3;
  //收藏状态：0未收藏，1已收藏
  int32 flag = 4;
}
message CycleBuyCollect{
int32 id = 1;
  //商品skuID
  int32 sku_id = 2;
  //用户scrm_id
  string user_id = 3;
  //产品名称
  string product_name = 4;
  //商品图片
  string product_img = 5;
  // 价格 单位分
  int32 price = 6;
  //状态 0失效 1正常 2删除
  int32 status = 7;
  //创建时间
  string create_time = 8;
  // 规格
  string goods_spec = 9;
  // 类型
  int32 goods_type = 10;
  // 是否是会员价
  int32 is_member_price = 11;
  // 会员价
  int32 member_price1 = 12;
  // 促销类型：2-限时折扣, 3-秒杀,闪购 5-拼团 6-周期购 7-新人专享 8-预售 9-新秒杀
  int32 goods_promotion_type = 13;
  int32 goods_promotion_price = 14;
  int32 new_people_price = 15;
}

// 周期购活动商品  取消收藏
message DelCycleBuyCollectRequest{
  int32 sku_id = 1;
  string user_id =2;
  int32 org_id = 3;
}


// 获取周期购订单列表请求参数
message CycleOrderListRequest {
    int32 page_index = 1;
    int32 page_size = 2;
    string user_id = 3;
    string open_id = 4;
}

// 获取周期购订单列表 响应数据
message CycleOrderListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated CycleOrderInfo data = 4;
}

// 我的周期购 订单列表项 数据
message CycleOrderInfo {
  int32  sku_id = 1;
  //商品名称
  string goods_name = 2;
  //商品图片
  string goods_image = 3;
  //商品单价 单位分
  int32 goods_price = 4;
  //商品数量
  int32 goods_num = 5;
  //订单状态
  int32  order_state = 6;
  //订单状态描述
  string order_state_desc = 7;
  //订单生成时间
  int64 order_add_time = 8;
  //周期购期数
  int32 cycle_num = 9;
  //erp订单号
  string erp_order_sn = 10;
  //支付单号
  string pay_sn = 11;
  //未支付订单 支付倒计时
  string order_end_pay_datetime = 12;
  //服务器时间
  string server_date_time = 13;
  //商品规格
  string goods_spec = 14;
  //订单id
  int64 order_id = 15;
  // 订单实付金额
  int64 order_amount = 16;
  // 周期购价格，单位分 该订单买的是几期购， 就对应几期购的价格
  int32  cycle_price = 17;
  // 主订单编号
  string parent_order_sn = 18;



}

//周期购订单回调
message CycleBuyOrderStaticRequest {
  // required 回调场景 1 创建订单
  int32 syncType = 1;
  //周期购活动的产品记录id 关联dc.activity中的group_buy_product表
  int32 productRecordId = 2;
  // required 周期购id
  int32 cid = 3;
  // required skuId
  int32 skuId = 4;
  //  商品id
  int32 productId = 5;
  //required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
  int32 channelId = 6;
}

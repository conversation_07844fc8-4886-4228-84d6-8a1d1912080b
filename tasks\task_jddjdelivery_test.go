package tasks

import (
	"testing"
)

func TestTaskJddjDelivery_TaskAutoJddjDelivery(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "京东到家定时拣货",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := TaskJddjDelivery{}
			s.TaskAutoJddjDelivery()
		})
	}
}

func TestTaskJddjDelivery_TaskAutoMtPick(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "阿闻，美团定时拣货",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := TaskJddjDelivery{}
			s.TaskAutoMtPick()
		})
	}
}

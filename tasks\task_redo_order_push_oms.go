package tasks

import (
	"context"
	"errors"
	"github.com/maybgit/glog"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/services"
)

//正向订单重推R1
type redoOMSOrder struct {
}

//重新推送到巨益OMS
func (e *redoOMSOrder) redoTask(taskData *models.OrderRedoTask, redo *redoTask) (err error) {

	//requestOrder := new(dto.DeliveryOrderCreateRequest)
	//err = json.Unmarshal([]byte(taskData.Params), requestOrder)
	//if err != nil {
	//	err = errors.New("json解析错误, " + err.Error() + ", json：" + taskData.Params)
	//	return
	//}
	//orderSn := requestOrder.DeliveryOrderRequest.DeliveryOrderCode
	//glog.Info(orderSn, "-自动重试推送订单至巨益OMS    ")
	//
	//omss := &services.OmsService{}
	//_, err = omss.OmsOrderSynchronize(requestOrder)
	//if err != nil {
	//	return err
	//}
	d := &services.OrderService{}
	model := new(oc.MtRePushThirdRequest)
	model.OrderSn = taskData.OrderSn
	out, err := d.MtRePushThird(context.Background(), model)
	if err != nil {
		return err
	}
	if out.Code != 200 {
		return errors.New(out.Message)
	}

	//正向单如果成功了，就自动找逆向单重推
	db := services.GetDBConn()
	rePush := make([]models.RefundOrder, 0)
	err = db.Where("order_sn = ? AND push_third = 0 and is_virtual=0", taskData.OrderSn).Find(&rePush)
	if err != nil {
		//正向订单重推成功了，就不阻塞了。认为完成了
		glog.Error("OMS逆向单重推查询数据库失败！")
	}
	for _, x := range rePush {
		after := &services.AfterSaleService{}
		modeLafter := new(oc.RefundRePushThirdRequest)
		modeLafter.RefundSn = x.RefundSn
		_, err = after.RefundRePushThird(context.Background(), modeLafter)
	}
	return nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/base.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_struct "github.com/golang/protobuf/ptypes/struct"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BaseRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRes) Reset()         { *m = BaseRes{} }
func (m *BaseRes) String() string { return proto.CompactTextString(m) }
func (*BaseRes) ProtoMessage()    {}
func (*BaseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{0}
}

func (m *BaseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRes.Unmarshal(m, b)
}
func (m *BaseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRes.Marshal(b, m, deterministic)
}
func (m *BaseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRes.Merge(m, src)
}
func (m *BaseRes) XXX_Size() int {
	return xxx_messageInfo_BaseRes.Size(m)
}
func (m *BaseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRes.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRes proto.InternalMessageInfo

func (m *BaseRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ActivityDeleteReq struct {
	// 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 活动id
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityDeleteReq) Reset()         { *m = ActivityDeleteReq{} }
func (m *ActivityDeleteReq) String() string { return proto.CompactTextString(m) }
func (*ActivityDeleteReq) ProtoMessage()    {}
func (*ActivityDeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{1}
}

func (m *ActivityDeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityDeleteReq.Unmarshal(m, b)
}
func (m *ActivityDeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityDeleteReq.Marshal(b, m, deterministic)
}
func (m *ActivityDeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityDeleteReq.Merge(m, src)
}
func (m *ActivityDeleteReq) XXX_Size() int {
	return xxx_messageInfo_ActivityDeleteReq.Size(m)
}
func (m *ActivityDeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityDeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityDeleteReq proto.InternalMessageInfo

func (m *ActivityDeleteReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ActivityDeleteReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 提交审核
type SubmitCheckReq struct {
	// 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 活动id
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitCheckReq) Reset()         { *m = SubmitCheckReq{} }
func (m *SubmitCheckReq) String() string { return proto.CompactTextString(m) }
func (*SubmitCheckReq) ProtoMessage()    {}
func (*SubmitCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{2}
}

func (m *SubmitCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitCheckReq.Unmarshal(m, b)
}
func (m *SubmitCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitCheckReq.Marshal(b, m, deterministic)
}
func (m *SubmitCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitCheckReq.Merge(m, src)
}
func (m *SubmitCheckReq) XXX_Size() int {
	return xxx_messageInfo_SubmitCheckReq.Size(m)
}
func (m *SubmitCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitCheckReq proto.InternalMessageInfo

func (m *SubmitCheckReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SubmitCheckReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 通用审核
type CheckReq struct {
	// 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 活动id
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	// 1审核通过、0审核不通过
	Pass int32 `protobuf:"varint,3,opt,name=pass,proto3" json:"pass"`
	// 审核理由
	CheckReason          string   `protobuf:"bytes,4,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckReq) Reset()         { *m = CheckReq{} }
func (m *CheckReq) String() string { return proto.CompactTextString(m) }
func (*CheckReq) ProtoMessage()    {}
func (*CheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{3}
}

func (m *CheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckReq.Unmarshal(m, b)
}
func (m *CheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckReq.Marshal(b, m, deterministic)
}
func (m *CheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckReq.Merge(m, src)
}
func (m *CheckReq) XXX_Size() int {
	return xxx_messageInfo_CheckReq.Size(m)
}
func (m *CheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckReq proto.InternalMessageInfo

func (m *CheckReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CheckReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CheckReq) GetPass() int32 {
	if m != nil {
		return m.Pass
	}
	return 0
}

func (m *CheckReq) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

// 异常标记
type ExceptionMarkReq struct {
	// 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 活动商品列表对应的id
	ActivityProductId int32 `protobuf:"varint,2,opt,name=activity_product_id,json=activityProductId,proto3" json:"activity_product_id"`
	// 活动商品列表对应的多个id
	Ids []int32 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids"`
	// 1标记为异常、0取消标记为异常
	State                int32    `protobuf:"varint,3,opt,name=state,proto3" json:"state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExceptionMarkReq) Reset()         { *m = ExceptionMarkReq{} }
func (m *ExceptionMarkReq) String() string { return proto.CompactTextString(m) }
func (*ExceptionMarkReq) ProtoMessage()    {}
func (*ExceptionMarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{4}
}

func (m *ExceptionMarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExceptionMarkReq.Unmarshal(m, b)
}
func (m *ExceptionMarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExceptionMarkReq.Marshal(b, m, deterministic)
}
func (m *ExceptionMarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExceptionMarkReq.Merge(m, src)
}
func (m *ExceptionMarkReq) XXX_Size() int {
	return xxx_messageInfo_ExceptionMarkReq.Size(m)
}
func (m *ExceptionMarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExceptionMarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExceptionMarkReq proto.InternalMessageInfo

func (m *ExceptionMarkReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ExceptionMarkReq) GetActivityProductId() int32 {
	if m != nil {
		return m.ActivityProductId
	}
	return 0
}

func (m *ExceptionMarkReq) GetIds() []int32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *ExceptionMarkReq) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

type SendWebhookNotifyReq struct {
	// 机器人地址key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	// 要发送的数据map
	Data                 *_struct.Struct `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SendWebhookNotifyReq) Reset()         { *m = SendWebhookNotifyReq{} }
func (m *SendWebhookNotifyReq) String() string { return proto.CompactTextString(m) }
func (*SendWebhookNotifyReq) ProtoMessage()    {}
func (*SendWebhookNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{5}
}

func (m *SendWebhookNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebhookNotifyReq.Unmarshal(m, b)
}
func (m *SendWebhookNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebhookNotifyReq.Marshal(b, m, deterministic)
}
func (m *SendWebhookNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebhookNotifyReq.Merge(m, src)
}
func (m *SendWebhookNotifyReq) XXX_Size() int {
	return xxx_messageInfo_SendWebhookNotifyReq.Size(m)
}
func (m *SendWebhookNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebhookNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebhookNotifyReq proto.InternalMessageInfo

func (m *SendWebhookNotifyReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SendWebhookNotifyReq) GetData() *_struct.Struct {
	if m != nil {
		return m.Data
	}
	return nil
}

type SendWebhookMarkdownNotifyReq struct {
	// 机器人地址key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	// 要发送的内容
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWebhookMarkdownNotifyReq) Reset()         { *m = SendWebhookMarkdownNotifyReq{} }
func (m *SendWebhookMarkdownNotifyReq) String() string { return proto.CompactTextString(m) }
func (*SendWebhookMarkdownNotifyReq) ProtoMessage()    {}
func (*SendWebhookMarkdownNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{6}
}

func (m *SendWebhookMarkdownNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebhookMarkdownNotifyReq.Unmarshal(m, b)
}
func (m *SendWebhookMarkdownNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebhookMarkdownNotifyReq.Marshal(b, m, deterministic)
}
func (m *SendWebhookMarkdownNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebhookMarkdownNotifyReq.Merge(m, src)
}
func (m *SendWebhookMarkdownNotifyReq) XXX_Size() int {
	return xxx_messageInfo_SendWebhookMarkdownNotifyReq.Size(m)
}
func (m *SendWebhookMarkdownNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebhookMarkdownNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebhookMarkdownNotifyReq proto.InternalMessageInfo

func (m *SendWebhookMarkdownNotifyReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SendWebhookMarkdownNotifyReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 异常商品计数
type RiskProductCountReq struct {
	// 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 查询的条件
	Condition            string   `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RiskProductCountReq) Reset()         { *m = RiskProductCountReq{} }
func (m *RiskProductCountReq) String() string { return proto.CompactTextString(m) }
func (*RiskProductCountReq) ProtoMessage()    {}
func (*RiskProductCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{7}
}

func (m *RiskProductCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskProductCountReq.Unmarshal(m, b)
}
func (m *RiskProductCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskProductCountReq.Marshal(b, m, deterministic)
}
func (m *RiskProductCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskProductCountReq.Merge(m, src)
}
func (m *RiskProductCountReq) XXX_Size() int {
	return xxx_messageInfo_RiskProductCountReq.Size(m)
}
func (m *RiskProductCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskProductCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_RiskProductCountReq proto.InternalMessageInfo

func (m *RiskProductCountReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RiskProductCountReq) GetCondition() string {
	if m != nil {
		return m.Condition
	}
	return ""
}

type RiskProductCountRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 计数，key活动id，value异常商品计数，不存在则为0
	Data                 map[int32]*RiskProductCountRes_CountData `protobuf:"bytes,3,rep,name=data,proto3" json:"data" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *RiskProductCountRes) Reset()         { *m = RiskProductCountRes{} }
func (m *RiskProductCountRes) String() string { return proto.CompactTextString(m) }
func (*RiskProductCountRes) ProtoMessage()    {}
func (*RiskProductCountRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{8}
}

func (m *RiskProductCountRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskProductCountRes.Unmarshal(m, b)
}
func (m *RiskProductCountRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskProductCountRes.Marshal(b, m, deterministic)
}
func (m *RiskProductCountRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskProductCountRes.Merge(m, src)
}
func (m *RiskProductCountRes) XXX_Size() int {
	return xxx_messageInfo_RiskProductCountRes.Size(m)
}
func (m *RiskProductCountRes) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskProductCountRes.DiscardUnknown(m)
}

var xxx_messageInfo_RiskProductCountRes proto.InternalMessageInfo

func (m *RiskProductCountRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RiskProductCountRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RiskProductCountRes) GetData() map[int32]*RiskProductCountRes_CountData {
	if m != nil {
		return m.Data
	}
	return nil
}

type RiskProductCountRes_CountData struct {
	// 异常商品数
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	// 总数
	Total                int32    `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RiskProductCountRes_CountData) Reset()         { *m = RiskProductCountRes_CountData{} }
func (m *RiskProductCountRes_CountData) String() string { return proto.CompactTextString(m) }
func (*RiskProductCountRes_CountData) ProtoMessage()    {}
func (*RiskProductCountRes_CountData) Descriptor() ([]byte, []int) {
	return fileDescriptor_54e87decef436226, []int{8, 0}
}

func (m *RiskProductCountRes_CountData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskProductCountRes_CountData.Unmarshal(m, b)
}
func (m *RiskProductCountRes_CountData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskProductCountRes_CountData.Marshal(b, m, deterministic)
}
func (m *RiskProductCountRes_CountData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskProductCountRes_CountData.Merge(m, src)
}
func (m *RiskProductCountRes_CountData) XXX_Size() int {
	return xxx_messageInfo_RiskProductCountRes_CountData.Size(m)
}
func (m *RiskProductCountRes_CountData) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskProductCountRes_CountData.DiscardUnknown(m)
}

var xxx_messageInfo_RiskProductCountRes_CountData proto.InternalMessageInfo

func (m *RiskProductCountRes_CountData) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RiskProductCountRes_CountData) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*BaseRes)(nil), "ac.BaseRes")
	proto.RegisterType((*ActivityDeleteReq)(nil), "ac.ActivityDeleteReq")
	proto.RegisterType((*SubmitCheckReq)(nil), "ac.SubmitCheckReq")
	proto.RegisterType((*CheckReq)(nil), "ac.CheckReq")
	proto.RegisterType((*ExceptionMarkReq)(nil), "ac.ExceptionMarkReq")
	proto.RegisterType((*SendWebhookNotifyReq)(nil), "ac.SendWebhookNotifyReq")
	proto.RegisterType((*SendWebhookMarkdownNotifyReq)(nil), "ac.SendWebhookMarkdownNotifyReq")
	proto.RegisterType((*RiskProductCountReq)(nil), "ac.RiskProductCountReq")
	proto.RegisterType((*RiskProductCountRes)(nil), "ac.RiskProductCountRes")
	proto.RegisterMapType((map[int32]*RiskProductCountRes_CountData)(nil), "ac.RiskProductCountRes.DataEntry")
	proto.RegisterType((*RiskProductCountRes_CountData)(nil), "ac.RiskProductCountRes.CountData")
}

func init() { proto.RegisterFile("ac/base.proto", fileDescriptor_54e87decef436226) }

var fileDescriptor_54e87decef436226 = []byte{
	// 579 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x53, 0x4b, 0x6f, 0xda, 0x4c,
	0x14, 0x15, 0x36, 0xfe, 0xf2, 0xf9, 0x3a, 0x89, 0x60, 0x42, 0x15, 0x17, 0xb1, 0x20, 0x5e, 0x21,
	0x55, 0x32, 0x12, 0x4d, 0x45, 0x95, 0x5d, 0x1b, 0xa2, 0xaa, 0x91, 0x5a, 0x55, 0x46, 0x55, 0xa5,
	0x6e, 0xd0, 0x30, 0xbe, 0x21, 0x16, 0xc4, 0x43, 0x3c, 0x63, 0x5a, 0x36, 0xfd, 0x0b, 0x5d, 0xf7,
	0xdf, 0x56, 0x33, 0x63, 0x28, 0xe6, 0x51, 0x35, 0xbb, 0xfb, 0x38, 0x73, 0xee, 0x6b, 0x0e, 0x9c,
	0x50, 0xd6, 0x1d, 0x53, 0x81, 0xe1, 0x3c, 0xe3, 0x92, 0x13, 0x8b, 0xb2, 0x66, 0x6b, 0xc2, 0xf9,
	0x64, 0x86, 0x5d, 0x1d, 0x19, 0xe7, 0x77, 0x5d, 0x21, 0xb3, 0x9c, 0x49, 0x83, 0x08, 0xfa, 0x70,
	0xf4, 0x96, 0x0a, 0x8c, 0x50, 0x10, 0x02, 0x55, 0xc6, 0x63, 0xf4, 0x2b, 0xed, 0x4a, 0xc7, 0x89,
	0xb4, 0x4d, 0x7c, 0x38, 0x7a, 0x40, 0x21, 0xe8, 0x04, 0x7d, 0xab, 0x5d, 0xe9, 0xb8, 0xd1, 0xca,
	0x0d, 0xfa, 0x50, 0x7f, 0xc3, 0x64, 0xb2, 0x48, 0xe4, 0x72, 0x80, 0x33, 0x94, 0x18, 0xe1, 0xa3,
	0xa2, 0x90, 0xcb, 0xf9, 0x9a, 0x42, 0xd9, 0xe4, 0x14, 0xac, 0x24, 0xd6, 0xaf, 0x9d, 0xc8, 0x4a,
	0xe2, 0xe0, 0x12, 0x4e, 0x87, 0xf9, 0xf8, 0x21, 0x91, 0xd7, 0xf7, 0xc8, 0xa6, 0xff, 0xfa, 0x0a,
	0xe1, 0xff, 0xa7, 0xe0, 0x15, 0x66, 0x4e, 0x85, 0xf0, 0x6d, 0x83, 0x51, 0x36, 0xb9, 0x80, 0x63,
	0xa6, 0x38, 0x46, 0x19, 0x52, 0xc1, 0x53, 0xbf, 0xaa, 0x27, 0xf2, 0x98, 0xe1, 0x55, 0xa1, 0xe0,
	0x07, 0xd4, 0x6e, 0xbe, 0x33, 0x9c, 0xcb, 0x84, 0xa7, 0x1f, 0x68, 0x76, 0xb0, 0x5c, 0x08, 0x67,
	0xb4, 0x98, 0x7e, 0x34, 0xcf, 0x78, 0x9c, 0x33, 0x39, 0x5a, 0xd7, 0xaf, 0xaf, 0x52, 0x9f, 0x4c,
	0xe6, 0x7d, 0x4c, 0x6a, 0x60, 0x27, 0xb1, 0xf0, 0xab, 0x6d, 0xbb, 0xe3, 0x44, 0xca, 0x24, 0x0d,
	0x70, 0x84, 0xa4, 0x12, 0x8b, 0x0e, 0x8d, 0x13, 0x7c, 0x86, 0xc6, 0x10, 0xd3, 0xf8, 0x0b, 0x8e,
	0xef, 0x39, 0x9f, 0x7e, 0xe4, 0x32, 0xb9, 0x5b, 0xaa, 0x1e, 0x6a, 0x60, 0x4f, 0x71, 0xa9, 0x5b,
	0x70, 0x23, 0x65, 0x92, 0x17, 0x50, 0x8d, 0xa9, 0xa4, 0xba, 0xa4, 0xd7, 0x3b, 0x0f, 0xcd, 0x95,
	0xc3, 0xd5, 0x95, 0xc3, 0xa1, 0xbe, 0x72, 0xa4, 0x41, 0xc1, 0x2d, 0xb4, 0x36, 0x68, 0xd5, 0x60,
	0x31, 0xff, 0x96, 0xfe, 0x8d, 0xde, 0x87, 0x23, 0xc6, 0x53, 0x89, 0xa9, 0x5c, 0x1d, 0xbe, 0x70,
	0x83, 0x77, 0x70, 0x16, 0x25, 0x62, 0x5a, 0xcc, 0x76, 0xcd, 0xf3, 0x54, 0x1e, 0xda, 0x52, 0x0b,
	0x5c, 0xc6, 0xd3, 0x38, 0x51, 0xdb, 0xd4, 0x73, 0xba, 0xd1, 0x9f, 0x40, 0xf0, 0xd3, 0xda, 0xc7,
	0xf4, 0xc4, 0x7f, 0x48, 0x5e, 0x15, 0x7b, 0xb0, 0xdb, 0x76, 0xc7, 0xeb, 0x5d, 0x84, 0x94, 0x85,
	0x7b, 0x48, 0xc3, 0x01, 0x95, 0xf4, 0x26, 0x95, 0xd9, 0xd2, 0x6c, 0xa4, 0xd9, 0x07, 0x57, 0xe7,
	0x54, 0x5c, 0xdd, 0x82, 0x29, 0xa7, 0x28, 0x69, 0x1c, 0x15, 0x95, 0x5c, 0xd2, 0x59, 0x71, 0x55,
	0xe3, 0x34, 0xbf, 0x82, 0xbb, 0xe6, 0xda, 0xdc, 0x9b, 0x63, 0xf6, 0xd6, 0x07, 0x67, 0x41, 0x67,
	0x39, 0x16, 0x77, 0x39, 0xd8, 0xcf, 0xba, 0x78, 0x64, 0xf0, 0x57, 0xd6, 0xeb, 0x4a, 0xef, 0x97,
	0x0d, 0x9e, 0x52, 0xe3, 0x10, 0xb3, 0x45, 0xc2, 0x90, 0x5c, 0xc2, 0x69, 0x59, 0x63, 0xe4, 0x99,
	0xe2, 0xdb, 0xd1, 0x5d, 0xd3, 0x53, 0xe1, 0x95, 0x8e, 0x43, 0xf0, 0x36, 0x04, 0x46, 0x88, 0xca,
	0x95, 0x15, 0x57, 0xc6, 0x07, 0xe0, 0x18, 0xe4, 0xb1, 0x8a, 0xee, 0xc7, 0xf4, 0xe0, 0xa4, 0xa4,
	0x0b, 0xd2, 0x50, 0xd9, 0x6d, 0xa9, 0x94, 0xdf, 0x0c, 0xa0, 0xb6, 0x3d, 0x39, 0x39, 0xdf, 0xbf,
	0x8f, 0xc7, 0xe6, 0x81, 0x84, 0x20, 0x57, 0x50, 0xdf, 0x51, 0x04, 0xf1, 0xf5, 0x4c, 0x7b, 0x84,
	0x52, 0xee, 0xe0, 0x16, 0x9e, 0x1f, 0xfc, 0xf6, 0xa4, 0xbd, 0xc5, 0xb1, 0xa3, 0x8a, 0x12, 0xd7,
	0xf8, 0x3f, 0xad, 0xac, 0x97, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xe3, 0xb3, 0x92, 0xba, 0x62,
	0x05, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BaseServiceClient is the client API for BaseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BaseServiceClient interface {
	// 活动删除
	ActivityDelete(ctx context.Context, in *ActivityDeleteReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 提交审核
	SubmitCheck(ctx context.Context, in *SubmitCheckReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 活动审核
	Check(ctx context.Context, in *CheckReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 异常标记
	ExceptionMark(ctx context.Context, in *ExceptionMarkReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 异常商品计数
	RiskProductCount(ctx context.Context, in *RiskProductCountReq, opts ...grpc.CallOption) (*RiskProductCountRes, error)
	// 发送企业微信机器人消息
	SendWebhookNotify(ctx context.Context, in *SendWebhookNotifyReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 发送企业微信机器人md消息
	SendWebhookMarkdownNotify(ctx context.Context, in *SendWebhookMarkdownNotifyReq, opts ...grpc.CallOption) (*BaseRes, error)
}

type baseServiceClient struct {
	cc *grpc.ClientConn
}

func NewBaseServiceClient(cc *grpc.ClientConn) BaseServiceClient {
	return &baseServiceClient{cc}
}

func (c *baseServiceClient) ActivityDelete(ctx context.Context, in *ActivityDeleteReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/ActivityDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) SubmitCheck(ctx context.Context, in *SubmitCheckReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/SubmitCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) Check(ctx context.Context, in *CheckReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/Check", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) ExceptionMark(ctx context.Context, in *ExceptionMarkReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/ExceptionMark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) RiskProductCount(ctx context.Context, in *RiskProductCountReq, opts ...grpc.CallOption) (*RiskProductCountRes, error) {
	out := new(RiskProductCountRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/RiskProductCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) SendWebhookNotify(ctx context.Context, in *SendWebhookNotifyReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/SendWebhookNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseServiceClient) SendWebhookMarkdownNotify(ctx context.Context, in *SendWebhookMarkdownNotifyReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.BaseService/SendWebhookMarkdownNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseServiceServer is the server API for BaseService service.
type BaseServiceServer interface {
	// 活动删除
	ActivityDelete(context.Context, *ActivityDeleteReq) (*BaseRes, error)
	// 提交审核
	SubmitCheck(context.Context, *SubmitCheckReq) (*BaseRes, error)
	// 活动审核
	Check(context.Context, *CheckReq) (*BaseRes, error)
	// 异常标记
	ExceptionMark(context.Context, *ExceptionMarkReq) (*BaseRes, error)
	// 异常商品计数
	RiskProductCount(context.Context, *RiskProductCountReq) (*RiskProductCountRes, error)
	// 发送企业微信机器人消息
	SendWebhookNotify(context.Context, *SendWebhookNotifyReq) (*BaseRes, error)
	// 发送企业微信机器人md消息
	SendWebhookMarkdownNotify(context.Context, *SendWebhookMarkdownNotifyReq) (*BaseRes, error)
}

// UnimplementedBaseServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBaseServiceServer struct {
}

func (*UnimplementedBaseServiceServer) ActivityDelete(ctx context.Context, req *ActivityDeleteReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityDelete not implemented")
}
func (*UnimplementedBaseServiceServer) SubmitCheck(ctx context.Context, req *SubmitCheckReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitCheck not implemented")
}
func (*UnimplementedBaseServiceServer) Check(ctx context.Context, req *CheckReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Check not implemented")
}
func (*UnimplementedBaseServiceServer) ExceptionMark(ctx context.Context, req *ExceptionMarkReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExceptionMark not implemented")
}
func (*UnimplementedBaseServiceServer) RiskProductCount(ctx context.Context, req *RiskProductCountReq) (*RiskProductCountRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskProductCount not implemented")
}
func (*UnimplementedBaseServiceServer) SendWebhookNotify(ctx context.Context, req *SendWebhookNotifyReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendWebhookNotify not implemented")
}
func (*UnimplementedBaseServiceServer) SendWebhookMarkdownNotify(ctx context.Context, req *SendWebhookMarkdownNotifyReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendWebhookMarkdownNotify not implemented")
}

func RegisterBaseServiceServer(s *grpc.Server, srv BaseServiceServer) {
	s.RegisterService(&_BaseService_serviceDesc, srv)
}

func _BaseService_ActivityDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).ActivityDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/ActivityDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).ActivityDelete(ctx, req.(*ActivityDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_SubmitCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).SubmitCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/SubmitCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).SubmitCheck(ctx, req.(*SubmitCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_Check_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).Check(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/Check",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).Check(ctx, req.(*CheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_ExceptionMark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExceptionMarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).ExceptionMark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/ExceptionMark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).ExceptionMark(ctx, req.(*ExceptionMarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_RiskProductCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskProductCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).RiskProductCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/RiskProductCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).RiskProductCount(ctx, req.(*RiskProductCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_SendWebhookNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendWebhookNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).SendWebhookNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/SendWebhookNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).SendWebhookNotify(ctx, req.(*SendWebhookNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseService_SendWebhookMarkdownNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendWebhookMarkdownNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseServiceServer).SendWebhookMarkdownNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BaseService/SendWebhookMarkdownNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseServiceServer).SendWebhookMarkdownNotify(ctx, req.(*SendWebhookMarkdownNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BaseService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.BaseService",
	HandlerType: (*BaseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ActivityDelete",
			Handler:    _BaseService_ActivityDelete_Handler,
		},
		{
			MethodName: "SubmitCheck",
			Handler:    _BaseService_SubmitCheck_Handler,
		},
		{
			MethodName: "Check",
			Handler:    _BaseService_Check_Handler,
		},
		{
			MethodName: "ExceptionMark",
			Handler:    _BaseService_ExceptionMark_Handler,
		},
		{
			MethodName: "RiskProductCount",
			Handler:    _BaseService_RiskProductCount_Handler,
		},
		{
			MethodName: "SendWebhookNotify",
			Handler:    _BaseService_SendWebhookNotify_Handler,
		},
		{
			MethodName: "SendWebhookMarkdownNotify",
			Handler:    _BaseService_SendWebhookMarkdownNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/base.proto",
}

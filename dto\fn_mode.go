package dto

type FnQueryFee struct {
	Distance   int `json:"distance"`
	GoodsInfos []struct {
		ActualDeliveryAmountCent   int64       `json:"actual_delivery_amount_cent"`
		BaseGoodsIcon              string      `json:"base_goods_icon"`
		BaseGoodsID                int         `json:"base_goods_id"`
		BaseGoodsName              string      `json:"base_goods_name"`
		CanAddTip                  int         `json:"can_add_tip"`
		DisableReason              interface{} `json:"disable_reason"`
		DisableReasonCode          interface{} `json:"disable_reason_code"`
		EstimateAcceptTime         interface{} `json:"estimate_accept_time"`
		IsValid                    int         `json:"is_valid"`
		NearDeliverCount           interface{} `json:"near_deliver_count"`
		OriginalDeliveryAmountCent int         `json:"original_delivery_amount_cent"`
		PredictDeliveryMinutes     int         `json:"predict_delivery_minutes"`
		PredictDeliveryTime        interface{} `json:"predict_delivery_time"`
		PriceDetail                struct {
			CategorySurchargeCent      int `json:"category_surcharge_cent"`
			DeliveryInsurePriceCent    int `json:"delivery_insure_price_cent"`
			DistancePriceCent          int `json:"distance_price_cent"`
			OrderPriceSurchargeCent    int `json:"order_price_surcharge_cent"`
			PressureSurchargeCent      int `json:"pressure_surcharge_cent"`
			RiverCrossingSurchargeCent int `json:"river_crossing_surcharge_cent"`
			StartPriceCent             int `json:"start_price_cent"`
			TemporarySurchargeCent     int `json:"temporary_surcharge_cent"`
			TimePeriodSurchargeCent    int `json:"time_period_surcharge_cent"`
			WeightPriceCent            int `json:"weight_price_cent"`
		} `json:"price_detail"`
		ServiceGoodsID          int         `json:"service_goods_id"`
		ServiceGoodsName        string      `json:"service_goods_name"`
		Slogan                  string      `json:"slogan"`
		TIndexID                string      `json:"t_index_id"`
		TotalDeliveryAmountCent int         `json:"total_delivery_amount_cent"`
		WarehouseID             interface{} `json:"warehouse_id"`
	} `json:"goods_infos"`
	Remark interface{} `json:"remark"`
	Time   int64       `json:"time"`
}

//蜂鸟添加订单后返回值
type AddDeliveryResponse struct {
	OrderId int64 `json:"order_id"`
}

//蜂鸟添加订单后返回值
type CarrierDriverModel struct {
	//骑手id
	CarrierDriverId string `json:"carrier_driver_id"`
	//骑手姓名
	CarrierDriverName string `json:"carrier_driver_name"`
	//骑手位置纬度
	CarrierDriverLatitude string `json:"carrier_driver_latitude"`
	//	骑手电话
	CarrierDriverPhone string `json:"carrier_driver_phone"`
	//骑手位置经度
	CarrierDriverLongitude string `json:"carrier_driver_longitude"`
}

package dto

// 第三方开票接口请求参数
type ThirdInvoiceParams struct {
	InvoiceApplySource int32         `json:"invoiceApplySource"` // 发票申请来源：1 子龙 2 R1 3 阿闻商城 4 阿闻商城-分销 5 本地生活-门店仓 6  本地生活-前置仓 7 本地生活-前置虚拟仓
	InvocieType        int32         `json:"invocieType"`        // 发票种类：1电子普票 2电子专票 3 纸质普票 4 纸质专票
	RelationOrderNo    string        `json:"relationOrderNo"`    // 关联订单号,多个用逗号隔开
	BuyerName          string        `json:"buyerName"`          // 购方抬头
	CompanyCode        string        `json:"companyCode"`        // 财务编码
	CustomerPhone      string        `json:"customerPhone"`      // 客户手机号
	BuyerTaxNum        string        `json:"buyerTaxNum"`        // 购方税号
	BuyerBank          string        `json:"buyerBank"`          // 购方开户行
	BuyerAccount       string        `json:"buyerAccount"`       // 购方银行账号
	NotifyEmail        string        `json:"notifyEmail"`        // 推送邮箱
	NotifyPhone        string        `json:"notifyPhone"`        // 推送手机号
	BuyerAdress        string        `json:"buyerAdress"`        // 购方开票地址
	PayedTime          string        `json:"payedTime"`          // 购方开票地址
	Details            []DetailsData `json:"details"`
}
type DetailsData struct {
	RpOrderNo       string  `json:"rpOrderNo"`       //内部订单号
	ProductName     string  `json:"productName"`     //商品名称
	ProductCode     string  `json:"productCode"`     //商品code
	TaxCode         string  `json:"taxCode"`         //税收编码
	ProductPrice    float64 `json:"productPrice"`    //含税单价
	ProductQuantity float64 `json:"productQuantity"` //数量
	ProductAmount   float64 `json:"productAmount"`   //含税实收金额，含税单价*数量
	ProductUnit     string  `json:"productUnit"`     //单位
	SpecType        string  `json:"specType"`        //规格型号
	RefundAmount    float64 `json:"refundAmount"`    //退款金额，退款的时候才有
}

// 第三方开票返回参数
type ThirdCreateInvoiceResponse struct {
	StatusCode    int32  `json:"statusCode"`
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	SystemError   string `json:"systemError"`
	BusinessError string `json:"businessError"`
	Extensions    string `json:"extensions"`
	Result        struct {
		Message     string `json:"message"`
		InvoiceNo   string `json:"invoiceNo"`
		InvoiceType string `json:"invoiceType"`
	} `json:"result"`
}

// 退款数据
type RefundData struct {
	OrderSn    string
	SkuId      string
	Amount     string
	Number     int32
	RefundType int32
}

type CalOrderDetailData struct {
	Code    int32
	Message string
	Data    []DetailsData
}

// 税收编码返回数据
type TaxCodeResponse struct {
	Code    int32       `json:"code"`
	Message string      `json:"message"`
	Data    TaxCodeList `json:"data"`
}
type TaxCodeList struct {
	List []TaxCodeListData `json:"list"`
}
type TaxCodeListData struct {
	TaxCode      string `json:"tax_code"`
	ItemCode     string `json:"item_code"`
	CategoryCode string `json:"category_code"`
	TaxRate      string `json:"tax_rate"`
}
type TaxCodeCategoryResponse struct {
	Code    int32                 `json:"code"`
	Message string                `json:"msg"`
	Data    []TaxCodeCategoryList `json:"data"`
}
type TaxCodeCategoryList struct {
	TaxCode      string `json:"tax_code"`
	CategoryCode string `json:"item_code"`
}
type R1TaxCodeResponse struct {
	Code    int32           `json:"code"`
	Message string          `json:"message"`
	Error   string          `json:"error"`
	Data    []R1TaxCodeData `json:"data"`
}
type R1TaxCodeData struct {
	SkuNo           string  `json:"skuNo"`
	TaxCategoryCode string  `json:"taxCategoryCode"`
	TaxValue        float32 `json:"taxValue"`
}

// 第三方退款红冲接口请求参数
type ThirdRefundInvoiceParams struct {
	OrderSource       int           `json:"orderSource"`       // 发票申请来源：1 子龙 2 R1 3 阿闻商城 4 阿闻商城-分销 5 本地生活-门店仓 6  本地生活-前置仓 7 本地生活-前置虚拟仓
	RpOrderNo         string        `json:"rpOrderNo"`         // 业务订单号
	RefundOrderNo     string        `json:"refundOrderNo"`     // 退款单号
	CompanyCode       string        `json:"companyCode"`       // 财务编码（NC编码）
	CustomerName      string        `json:"customerName"`      // 客户姓名
	CustomerPhone     string        `json:"customerPhone"`     // 客户手机号
	RefundTime        string        `json:"refundTime"`        // 退款时间（yyyy-MM-dd HH:mm:ss）
	OrderAmount       float64       `json:"orderAmount"`       // 订单金额，原订单金额
	OrderRefundAmount float64       `json:"orderRefundAmount"` // 退款金额，需红冲金额
	InvoiceOrderNos   string        `json:"invoiceOrderNos"`   // 开票时对应的申请单号
	InvoiceId         string        `json:"invoiceId"`         // 此退款对应的蓝票发票流水号
	InvoiceCode       string        `json:"invoiceCode"`       // 此退款单对应的蓝票发票发票代码
	InvoiceNumber     string        `json:"invoiceNumber"`     // 此退款对应的蓝票发票号码
	Details           []DetailsData `json:"details"`
}

// 发票回调数据
type InvoiceDetailData struct {
	OrderNo        string  `json:"orderNo"`
	InvoiceCode    string  `json:"invoiceCode"`
	TaxAmountTotal float64 `json:"taxAmountTotal"`
	PdfUrl         string  `json:"pdfUrl"`
	InvoiceType    int     `json:"invoiceType"`
	InvoiceStatus  int     `json:"invoiceStatus"`
	InvoiceId      string  `json:"invoiceId"`
	ErrorMessage   string  `json:"errorMessage"`
	Data           string  `json:"data"`
}

// 发票回调数据json解析，按需添加
type InvoiceCallbackData struct {
	BuyerPhone    string `json:"buyerPhone"`
	BuyerName     string `json:"buyerName"`
	BuyerAddress  string `json:"buyerAddress"`
	BuyerBank     string `json:"buyerBank"`
	BuyerAccount  string `json:"buyerAccount"`
	UserId        string `json:"userId"` // 开票员id
	InvoiceCode   string `json:"invoiceCode"`
	InvoiceNumber string `json:"invoiceNumber"`
}

// 订单分销联合查询结果
type UpetOrderChain struct {
	AccountId     string `json:"account_id"`
	WarehouseType int32  `json:"warehouse_type"`
}

type InvoiceTitleListData struct {
	CustomerId    int32  `json:"customerId"`
	CustomerPhone string `json:"customerPhone"`
	BuyerTaxNum   string `json:"buyerTaxNum"`
	BuyerName     string `json:"buyerName"`
	NotifyEmail   string `json:"notifyEmail"`
	UpdateTime    string `json:"updateTime"`
}

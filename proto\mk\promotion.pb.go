// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/promotion.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 基础请求
type BaseRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户所属店铺ID
	ShopId               string   `protobuf:"bytes,2,opt,name=shopId,proto3" json:"shopId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{0}
}

func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (m *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(m, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *BaseRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

// 根据ID查询请求
type QueryByIdRequest struct {
	// 单个实体Id
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryByIdRequest) Reset()         { *m = QueryByIdRequest{} }
func (m *QueryByIdRequest) String() string { return proto.CompactTextString(m) }
func (*QueryByIdRequest) ProtoMessage()    {}
func (*QueryByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{1}
}

func (m *QueryByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryByIdRequest.Unmarshal(m, b)
}
func (m *QueryByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryByIdRequest.Marshal(b, m, deterministic)
}
func (m *QueryByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryByIdRequest.Merge(m, src)
}
func (m *QueryByIdRequest) XXX_Size() int {
	return xxx_messageInfo_QueryByIdRequest.Size(m)
}
func (m *QueryByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryByIdRequest proto.InternalMessageInfo

func (m *QueryByIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除的请求
type DeleteRequest struct {
	// 多个实体Id
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	// 单个实体Id
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	// 当前登录用户信息
	UserId               string   `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	UserName             string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRequest) Reset()         { *m = DeleteRequest{} }
func (m *DeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRequest) ProtoMessage()    {}
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{2}
}

func (m *DeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRequest.Unmarshal(m, b)
}
func (m *DeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRequest.Marshal(b, m, deterministic)
}
func (m *DeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRequest.Merge(m, src)
}
func (m *DeleteRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRequest.Size(m)
}
func (m *DeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRequest proto.InternalMessageInfo

func (m *DeleteRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *DeleteRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeleteRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DeleteRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

// 更新是否启用的请求
type UpdateIsEnableRequest struct {
	// 多个实体Id
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	// 单个实体Id
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	// 是否启用
	IsEnable bool `protobuf:"varint,3,opt,name=isEnable,proto3" json:"isEnable"`
	// 当前登录用户信息
	UserId               string   `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	UserName             string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateIsEnableRequest) Reset()         { *m = UpdateIsEnableRequest{} }
func (m *UpdateIsEnableRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateIsEnableRequest) ProtoMessage()    {}
func (*UpdateIsEnableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{3}
}

func (m *UpdateIsEnableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIsEnableRequest.Unmarshal(m, b)
}
func (m *UpdateIsEnableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIsEnableRequest.Marshal(b, m, deterministic)
}
func (m *UpdateIsEnableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIsEnableRequest.Merge(m, src)
}
func (m *UpdateIsEnableRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateIsEnableRequest.Size(m)
}
func (m *UpdateIsEnableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIsEnableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIsEnableRequest proto.InternalMessageInfo

func (m *UpdateIsEnableRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *UpdateIsEnableRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateIsEnableRequest) GetIsEnable() bool {
	if m != nil {
		return m.IsEnable
	}
	return false
}

func (m *UpdateIsEnableRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UpdateIsEnableRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type PromotionSummaryReq struct {
	// 多个门店用逗号分隔
	ShopId               string   `protobuf:"bytes,2,opt,name=shopId,proto3" json:"shopId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSummaryReq) Reset()         { *m = PromotionSummaryReq{} }
func (m *PromotionSummaryReq) String() string { return proto.CompactTextString(m) }
func (*PromotionSummaryReq) ProtoMessage()    {}
func (*PromotionSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{4}
}

func (m *PromotionSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSummaryReq.Unmarshal(m, b)
}
func (m *PromotionSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSummaryReq.Marshal(b, m, deterministic)
}
func (m *PromotionSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSummaryReq.Merge(m, src)
}
func (m *PromotionSummaryReq) XXX_Size() int {
	return xxx_messageInfo_PromotionSummaryReq.Size(m)
}
func (m *PromotionSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSummaryReq proto.InternalMessageInfo

func (m *PromotionSummaryReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

type PromotionSummaryRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 活动明细
	Summary []*PromotionSummaryDto `protobuf:"bytes,3,rep,name=summary,proto3" json:"summary"`
	// 活动门店数量，仅多门店返回
	Shop                 *PromotionSummaryShopDto `protobuf:"bytes,4,opt,name=shop,proto3" json:"shop"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PromotionSummaryRes) Reset()         { *m = PromotionSummaryRes{} }
func (m *PromotionSummaryRes) String() string { return proto.CompactTextString(m) }
func (*PromotionSummaryRes) ProtoMessage()    {}
func (*PromotionSummaryRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{5}
}

func (m *PromotionSummaryRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSummaryRes.Unmarshal(m, b)
}
func (m *PromotionSummaryRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSummaryRes.Marshal(b, m, deterministic)
}
func (m *PromotionSummaryRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSummaryRes.Merge(m, src)
}
func (m *PromotionSummaryRes) XXX_Size() int {
	return xxx_messageInfo_PromotionSummaryRes.Size(m)
}
func (m *PromotionSummaryRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSummaryRes.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSummaryRes proto.InternalMessageInfo

func (m *PromotionSummaryRes) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionSummaryRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionSummaryRes) GetSummary() []*PromotionSummaryDto {
	if m != nil {
		return m.Summary
	}
	return nil
}

func (m *PromotionSummaryRes) GetShop() *PromotionSummaryShopDto {
	if m != nil {
		return m.Shop
	}
	return nil
}

// 查询当前用户的活动信息
type PromotionSummaryShopDto struct {
	// 有活动门店数量
	PromotionShopCount int32 `protobuf:"varint,1,opt,name=promotionShopCount,proto3" json:"promotionShopCount"`
	// 无活动门店数量
	NotPromotionShopCount int32 `protobuf:"varint,2,opt,name=notPromotionShopCount,proto3" json:"notPromotionShopCount"`
	// 生效活动数量
	PromotioningShopCount int32    `protobuf:"varint,3,opt,name=promotioningShopCount,proto3" json:"promotioningShopCount"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PromotionSummaryShopDto) Reset()         { *m = PromotionSummaryShopDto{} }
func (m *PromotionSummaryShopDto) String() string { return proto.CompactTextString(m) }
func (*PromotionSummaryShopDto) ProtoMessage()    {}
func (*PromotionSummaryShopDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{6}
}

func (m *PromotionSummaryShopDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSummaryShopDto.Unmarshal(m, b)
}
func (m *PromotionSummaryShopDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSummaryShopDto.Marshal(b, m, deterministic)
}
func (m *PromotionSummaryShopDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSummaryShopDto.Merge(m, src)
}
func (m *PromotionSummaryShopDto) XXX_Size() int {
	return xxx_messageInfo_PromotionSummaryShopDto.Size(m)
}
func (m *PromotionSummaryShopDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSummaryShopDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSummaryShopDto proto.InternalMessageInfo

func (m *PromotionSummaryShopDto) GetPromotionShopCount() int32 {
	if m != nil {
		return m.PromotionShopCount
	}
	return 0
}

func (m *PromotionSummaryShopDto) GetNotPromotionShopCount() int32 {
	if m != nil {
		return m.NotPromotionShopCount
	}
	return 0
}

func (m *PromotionSummaryShopDto) GetPromotioningShopCount() int32 {
	if m != nil {
		return m.PromotioningShopCount
	}
	return 0
}

// 根据登录用户按类别查询促销活动Dto
type PromotionSummaryDto struct {
	// 类别Id，1 满减活动 2 限时折扣 3 满减运费 10社区团购 11 拼团活动 12 赠险
	TypeId int32 `protobuf:"varint,1,opt,name=typeId,proto3" json:"typeId"`
	// 类别名称
	TypeName string `protobuf:"bytes,2,opt,name=typeName,proto3" json:"typeName"`
	// 活动数量
	TypeCount int32 `protobuf:"varint,3,opt,name=typeCount,proto3" json:"typeCount"`
	// 进行中的数量
	TypeInProgress int32 `protobuf:"varint,5,opt,name=typeInProgress,proto3" json:"typeInProgress"`
	// 未开始的数量
	TypeNotStart int32 `protobuf:"varint,6,opt,name=typeNotStart,proto3" json:"typeNotStart"`
	// 概览
	Summary              string   `protobuf:"bytes,7,opt,name=summary,proto3" json:"summary"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSummaryDto) Reset()         { *m = PromotionSummaryDto{} }
func (m *PromotionSummaryDto) String() string { return proto.CompactTextString(m) }
func (*PromotionSummaryDto) ProtoMessage()    {}
func (*PromotionSummaryDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{7}
}

func (m *PromotionSummaryDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionSummaryDto.Unmarshal(m, b)
}
func (m *PromotionSummaryDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionSummaryDto.Marshal(b, m, deterministic)
}
func (m *PromotionSummaryDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSummaryDto.Merge(m, src)
}
func (m *PromotionSummaryDto) XXX_Size() int {
	return xxx_messageInfo_PromotionSummaryDto.Size(m)
}
func (m *PromotionSummaryDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSummaryDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSummaryDto proto.InternalMessageInfo

func (m *PromotionSummaryDto) GetTypeId() int32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

func (m *PromotionSummaryDto) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *PromotionSummaryDto) GetTypeCount() int32 {
	if m != nil {
		return m.TypeCount
	}
	return 0
}

func (m *PromotionSummaryDto) GetTypeInProgress() int32 {
	if m != nil {
		return m.TypeInProgress
	}
	return 0
}

func (m *PromotionSummaryDto) GetTypeNotStart() int32 {
	if m != nil {
		return m.TypeNotStart
	}
	return 0
}

func (m *PromotionSummaryDto) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

//根据店铺Id查询满减活动
type PromotionQueryByShopIdRequest struct {
	//店铺Id
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 是否返回活动满减详细信息
	ShowDetail bool `protobuf:"varint,3,opt,name=showDetail,proto3" json:"showDetail"`
	// 活动类型 1 满减 2 限时折扣
	Types int32 `protobuf:"varint,6,opt,name=types,proto3" json:"types"`
	//分页信息--当前页
	PageIndex int32 `protobuf:"varint,7,opt,name=pageIndex,proto3" json:"pageIndex"`
	//分页信息--每页个数
	PageSize int32 `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize"`
	//置顶的商品id
	ProductId int32 `protobuf:"varint,9,opt,name=productId,proto3" json:"productId"`
	// 仓库id
	WarehouseId          int32    `protobuf:"varint,10,opt,name=warehouseId,proto3" json:"warehouseId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionQueryByShopIdRequest) Reset()         { *m = PromotionQueryByShopIdRequest{} }
func (m *PromotionQueryByShopIdRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionQueryByShopIdRequest) ProtoMessage()    {}
func (*PromotionQueryByShopIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{8}
}

func (m *PromotionQueryByShopIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionQueryByShopIdRequest.Unmarshal(m, b)
}
func (m *PromotionQueryByShopIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionQueryByShopIdRequest.Marshal(b, m, deterministic)
}
func (m *PromotionQueryByShopIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQueryByShopIdRequest.Merge(m, src)
}
func (m *PromotionQueryByShopIdRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionQueryByShopIdRequest.Size(m)
}
func (m *PromotionQueryByShopIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQueryByShopIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQueryByShopIdRequest proto.InternalMessageInfo

func (m *PromotionQueryByShopIdRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionQueryByShopIdRequest) GetShowDetail() bool {
	if m != nil {
		return m.ShowDetail
	}
	return false
}

func (m *PromotionQueryByShopIdRequest) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionQueryByShopIdRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PromotionQueryByShopIdRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PromotionQueryByShopIdRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PromotionQueryByShopIdRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

// 根据店铺Id查询满减活动
type QueryPromotionByShopIdReq struct {
	// 店铺Id
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 限时活动详情
	ShowDetail           bool     `protobuf:"varint,2,opt,name=showDetail,proto3" json:"showDetail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPromotionByShopIdReq) Reset()         { *m = QueryPromotionByShopIdReq{} }
func (m *QueryPromotionByShopIdReq) String() string { return proto.CompactTextString(m) }
func (*QueryPromotionByShopIdReq) ProtoMessage()    {}
func (*QueryPromotionByShopIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{9}
}

func (m *QueryPromotionByShopIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPromotionByShopIdReq.Unmarshal(m, b)
}
func (m *QueryPromotionByShopIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPromotionByShopIdReq.Marshal(b, m, deterministic)
}
func (m *QueryPromotionByShopIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPromotionByShopIdReq.Merge(m, src)
}
func (m *QueryPromotionByShopIdReq) XXX_Size() int {
	return xxx_messageInfo_QueryPromotionByShopIdReq.Size(m)
}
func (m *QueryPromotionByShopIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPromotionByShopIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPromotionByShopIdReq proto.InternalMessageInfo

func (m *QueryPromotionByShopIdReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *QueryPromotionByShopIdReq) GetShowDetail() bool {
	if m != nil {
		return m.ShowDetail
	}
	return false
}

// 计算减免金额请求
type PromotionCalcRequest struct {
	//用户所属店铺ID
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 渠道Id
	ChannelId int32 `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId"`
	//目的地坐标X 不传递或传递0，不计算运费
	DestinationX float64 `protobuf:"fixed64,3,opt,name=destinationX,proto3" json:"destinationX"`
	//目的地坐标Y 不传递或传递0，不计算运费
	DestinationY float64 `protobuf:"fixed64,4,opt,name=destinationY,proto3" json:"destinationY"`
	// 是否是新用户
	IsNewUser bool `protobuf:"varint,6,opt,name=isNewUser,proto3" json:"isNewUser"`
	// 用户id
	UserId string `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 相关商品
	PromotionProduct     []*PromotionCalcProductDto `protobuf:"bytes,5,rep,name=promotionProduct,proto3" json:"promotionProduct"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PromotionCalcRequest) Reset()         { *m = PromotionCalcRequest{} }
func (m *PromotionCalcRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionCalcRequest) ProtoMessage()    {}
func (*PromotionCalcRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{10}
}

func (m *PromotionCalcRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionCalcRequest.Unmarshal(m, b)
}
func (m *PromotionCalcRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionCalcRequest.Marshal(b, m, deterministic)
}
func (m *PromotionCalcRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionCalcRequest.Merge(m, src)
}
func (m *PromotionCalcRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionCalcRequest.Size(m)
}
func (m *PromotionCalcRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionCalcRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionCalcRequest proto.InternalMessageInfo

func (m *PromotionCalcRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionCalcRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PromotionCalcRequest) GetDestinationX() float64 {
	if m != nil {
		return m.DestinationX
	}
	return 0
}

func (m *PromotionCalcRequest) GetDestinationY() float64 {
	if m != nil {
		return m.DestinationY
	}
	return 0
}

func (m *PromotionCalcRequest) GetIsNewUser() bool {
	if m != nil {
		return m.IsNewUser
	}
	return false
}

func (m *PromotionCalcRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PromotionCalcRequest) GetPromotionProduct() []*PromotionCalcProductDto {
	if m != nil {
		return m.PromotionProduct
	}
	return nil
}

type GetDeliveryMoneyRequest struct {
	//用户所属店铺ID
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	//目的地坐标X 不传递或传递0，不计算运费
	DestinationX float64 `protobuf:"fixed64,3,opt,name=destinationX,proto3" json:"destinationX"`
	//目的地坐标Y 不传递或传递0，不计算运费
	DestinationY float64 `protobuf:"fixed64,4,opt,name=destinationY,proto3" json:"destinationY"`
	// 重量
	TotalWeight          float64  `protobuf:"fixed64,6,opt,name=totalWeight,proto3" json:"totalWeight"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeliveryMoneyRequest) Reset()         { *m = GetDeliveryMoneyRequest{} }
func (m *GetDeliveryMoneyRequest) String() string { return proto.CompactTextString(m) }
func (*GetDeliveryMoneyRequest) ProtoMessage()    {}
func (*GetDeliveryMoneyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{11}
}

func (m *GetDeliveryMoneyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeliveryMoneyRequest.Unmarshal(m, b)
}
func (m *GetDeliveryMoneyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeliveryMoneyRequest.Marshal(b, m, deterministic)
}
func (m *GetDeliveryMoneyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeliveryMoneyRequest.Merge(m, src)
}
func (m *GetDeliveryMoneyRequest) XXX_Size() int {
	return xxx_messageInfo_GetDeliveryMoneyRequest.Size(m)
}
func (m *GetDeliveryMoneyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeliveryMoneyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeliveryMoneyRequest proto.InternalMessageInfo

func (m *GetDeliveryMoneyRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *GetDeliveryMoneyRequest) GetDestinationX() float64 {
	if m != nil {
		return m.DestinationX
	}
	return 0
}

func (m *GetDeliveryMoneyRequest) GetDestinationY() float64 {
	if m != nil {
		return m.DestinationY
	}
	return 0
}

func (m *GetDeliveryMoneyRequest) GetTotalWeight() float64 {
	if m != nil {
		return m.TotalWeight
	}
	return 0
}

// 通过sku查询商品是否参与满减活动
type PromotionQueryBySkuIdsRequest struct {
	// 门店财务编码
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 渠道Id
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	// 商品Sku列表
	SkuIds []string `protobuf:"bytes,2,rep,name=skuIds,proto3" json:"skuIds"`
	// 是否返回满减明细列表
	IsReachReduceInfo    bool     `protobuf:"varint,4,opt,name=isReachReduceInfo,proto3" json:"isReachReduceInfo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionQueryBySkuIdsRequest) Reset()         { *m = PromotionQueryBySkuIdsRequest{} }
func (m *PromotionQueryBySkuIdsRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionQueryBySkuIdsRequest) ProtoMessage()    {}
func (*PromotionQueryBySkuIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{12}
}

func (m *PromotionQueryBySkuIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionQueryBySkuIdsRequest.Unmarshal(m, b)
}
func (m *PromotionQueryBySkuIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionQueryBySkuIdsRequest.Marshal(b, m, deterministic)
}
func (m *PromotionQueryBySkuIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQueryBySkuIdsRequest.Merge(m, src)
}
func (m *PromotionQueryBySkuIdsRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionQueryBySkuIdsRequest.Size(m)
}
func (m *PromotionQueryBySkuIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQueryBySkuIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQueryBySkuIdsRequest proto.InternalMessageInfo

func (m *PromotionQueryBySkuIdsRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionQueryBySkuIdsRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PromotionQueryBySkuIdsRequest) GetSkuIds() []string {
	if m != nil {
		return m.SkuIds
	}
	return nil
}

func (m *PromotionQueryBySkuIdsRequest) GetIsReachReduceInfo() bool {
	if m != nil {
		return m.IsReachReduceInfo
	}
	return false
}

/////////////////////////////////// PlayBillRequest  //////////////////////////////////////////////////////////////////////
type CreatePlayBillRequest struct {
	FinanceCode          []string `protobuf:"bytes,1,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	Playbillname         string   `protobuf:"bytes,2,opt,name=playbillname,proto3" json:"playbillname"`
	Playbillcontent      string   `protobuf:"bytes,3,opt,name=playbillcontent,proto3" json:"playbillcontent"`
	Template             int32    `protobuf:"varint,4,opt,name=template,proto3" json:"template"`
	Templateimg          string   `protobuf:"bytes,5,opt,name=templateimg,proto3" json:"templateimg"`
	PagePath             string   `protobuf:"bytes,6,opt,name=page_path,json=pagePath,proto3" json:"page_path"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePlayBillRequest) Reset()         { *m = CreatePlayBillRequest{} }
func (m *CreatePlayBillRequest) String() string { return proto.CompactTextString(m) }
func (*CreatePlayBillRequest) ProtoMessage()    {}
func (*CreatePlayBillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{13}
}

func (m *CreatePlayBillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePlayBillRequest.Unmarshal(m, b)
}
func (m *CreatePlayBillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePlayBillRequest.Marshal(b, m, deterministic)
}
func (m *CreatePlayBillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePlayBillRequest.Merge(m, src)
}
func (m *CreatePlayBillRequest) XXX_Size() int {
	return xxx_messageInfo_CreatePlayBillRequest.Size(m)
}
func (m *CreatePlayBillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePlayBillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePlayBillRequest proto.InternalMessageInfo

func (m *CreatePlayBillRequest) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

func (m *CreatePlayBillRequest) GetPlaybillname() string {
	if m != nil {
		return m.Playbillname
	}
	return ""
}

func (m *CreatePlayBillRequest) GetPlaybillcontent() string {
	if m != nil {
		return m.Playbillcontent
	}
	return ""
}

func (m *CreatePlayBillRequest) GetTemplate() int32 {
	if m != nil {
		return m.Template
	}
	return 0
}

func (m *CreatePlayBillRequest) GetTemplateimg() string {
	if m != nil {
		return m.Templateimg
	}
	return ""
}

func (m *CreatePlayBillRequest) GetPagePath() string {
	if m != nil {
		return m.PagePath
	}
	return ""
}

type GetPlayBillListRequest struct {
	FinanceCode          string   `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayBillListRequest) Reset()         { *m = GetPlayBillListRequest{} }
func (m *GetPlayBillListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPlayBillListRequest) ProtoMessage()    {}
func (*GetPlayBillListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{14}
}

func (m *GetPlayBillListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayBillListRequest.Unmarshal(m, b)
}
func (m *GetPlayBillListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayBillListRequest.Marshal(b, m, deterministic)
}
func (m *GetPlayBillListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayBillListRequest.Merge(m, src)
}
func (m *GetPlayBillListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPlayBillListRequest.Size(m)
}
func (m *GetPlayBillListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayBillListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayBillListRequest proto.InternalMessageInfo

func (m *GetPlayBillListRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

type GetSinglePlayBillListRequest struct {
	FinanceCode          string   `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	Playbillname         string   `protobuf:"bytes,2,opt,name=playbillname,proto3" json:"playbillname"`
	Stime                string   `protobuf:"bytes,3,opt,name=stime,proto3" json:"stime"`
	Etime                string   `protobuf:"bytes,4,opt,name=etime,proto3" json:"etime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSinglePlayBillListRequest) Reset()         { *m = GetSinglePlayBillListRequest{} }
func (m *GetSinglePlayBillListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSinglePlayBillListRequest) ProtoMessage()    {}
func (*GetSinglePlayBillListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{15}
}

func (m *GetSinglePlayBillListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSinglePlayBillListRequest.Unmarshal(m, b)
}
func (m *GetSinglePlayBillListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSinglePlayBillListRequest.Marshal(b, m, deterministic)
}
func (m *GetSinglePlayBillListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSinglePlayBillListRequest.Merge(m, src)
}
func (m *GetSinglePlayBillListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSinglePlayBillListRequest.Size(m)
}
func (m *GetSinglePlayBillListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSinglePlayBillListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSinglePlayBillListRequest proto.InternalMessageInfo

func (m *GetSinglePlayBillListRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GetSinglePlayBillListRequest) GetPlaybillname() string {
	if m != nil {
		return m.Playbillname
	}
	return ""
}

func (m *GetSinglePlayBillListRequest) GetStime() string {
	if m != nil {
		return m.Stime
	}
	return ""
}

func (m *GetSinglePlayBillListRequest) GetEtime() string {
	if m != nil {
		return m.Etime
	}
	return ""
}

type DeleteSinglePlayBillRequest struct {
	Playbillid           string   `protobuf:"bytes,1,opt,name=playbillid,proto3" json:"playbillid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSinglePlayBillRequest) Reset()         { *m = DeleteSinglePlayBillRequest{} }
func (m *DeleteSinglePlayBillRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteSinglePlayBillRequest) ProtoMessage()    {}
func (*DeleteSinglePlayBillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{16}
}

func (m *DeleteSinglePlayBillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSinglePlayBillRequest.Unmarshal(m, b)
}
func (m *DeleteSinglePlayBillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSinglePlayBillRequest.Marshal(b, m, deterministic)
}
func (m *DeleteSinglePlayBillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSinglePlayBillRequest.Merge(m, src)
}
func (m *DeleteSinglePlayBillRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteSinglePlayBillRequest.Size(m)
}
func (m *DeleteSinglePlayBillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSinglePlayBillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSinglePlayBillRequest proto.InternalMessageInfo

func (m *DeleteSinglePlayBillRequest) GetPlaybillid() string {
	if m != nil {
		return m.Playbillid
	}
	return ""
}

type DownloadPlayBillRequest struct {
	Playbillid           string   `protobuf:"bytes,1,opt,name=playbillid,proto3" json:"playbillid"`
	FinanceCode          string   `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	AccessToken          string   `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownloadPlayBillRequest) Reset()         { *m = DownloadPlayBillRequest{} }
func (m *DownloadPlayBillRequest) String() string { return proto.CompactTextString(m) }
func (*DownloadPlayBillRequest) ProtoMessage()    {}
func (*DownloadPlayBillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{17}
}

func (m *DownloadPlayBillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownloadPlayBillRequest.Unmarshal(m, b)
}
func (m *DownloadPlayBillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownloadPlayBillRequest.Marshal(b, m, deterministic)
}
func (m *DownloadPlayBillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadPlayBillRequest.Merge(m, src)
}
func (m *DownloadPlayBillRequest) XXX_Size() int {
	return xxx_messageInfo_DownloadPlayBillRequest.Size(m)
}
func (m *DownloadPlayBillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadPlayBillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadPlayBillRequest proto.InternalMessageInfo

func (m *DownloadPlayBillRequest) GetPlaybillid() string {
	if m != nil {
		return m.Playbillid
	}
	return ""
}

func (m *DownloadPlayBillRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *DownloadPlayBillRequest) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

// 响应数据
type BaseResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{18}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// 查询优惠信息
type PromotionQueryByShopIdResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 满减活动列表
	Data                 []*PromotionQueryByShopIdDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PromotionQueryByShopIdResponse) Reset()         { *m = PromotionQueryByShopIdResponse{} }
func (m *PromotionQueryByShopIdResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionQueryByShopIdResponse) ProtoMessage()    {}
func (*PromotionQueryByShopIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{19}
}

func (m *PromotionQueryByShopIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionQueryByShopIdResponse.Unmarshal(m, b)
}
func (m *PromotionQueryByShopIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionQueryByShopIdResponse.Marshal(b, m, deterministic)
}
func (m *PromotionQueryByShopIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQueryByShopIdResponse.Merge(m, src)
}
func (m *PromotionQueryByShopIdResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionQueryByShopIdResponse.Size(m)
}
func (m *PromotionQueryByShopIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQueryByShopIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQueryByShopIdResponse proto.InternalMessageInfo

func (m *PromotionQueryByShopIdResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionQueryByShopIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionQueryByShopIdResponse) GetData() []*PromotionQueryByShopIdDto {
	if m != nil {
		return m.Data
	}
	return nil
}

//计算减免金额请求
type PromotionReduceCalcResponse struct {
	// 代码 非 200 取 message 错误信息
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 减免金额
	ReduceMoney float64 `protobuf:"fixed64,3,opt,name=reduceMoney,proto3" json:"reduceMoney"`
	// 总金额
	TotalMoney float64 `protobuf:"fixed64,5,opt,name=totalMoney,proto3" json:"totalMoney"`
	// 实付金额
	ActualMoney float64 `protobuf:"fixed64,6,opt,name=actualMoney,proto3" json:"actualMoney"`
	// 优惠信息
	PromotionReduceList []*PromotionReduceDto `protobuf:"bytes,4,rep,name=promotionReduceList,proto3" json:"promotionReduceList"`
	// 参与减免金额的商品Sku列表
	SkuIds []string `protobuf:"bytes,7,rep,name=skuIds,proto3" json:"skuIds"`
	//总金额 以分为单位
	ReduceMoneyByMinUnit int32 `protobuf:"varint,10,opt,name=reduceMoneyByMinUnit,proto3" json:"reduceMoneyByMinUnit"`
	// 总金额 以分为单位
	TotalMoneyByMinUnit int32 `protobuf:"varint,11,opt,name=totalMoneyByMinUnit,proto3" json:"totalMoneyByMinUnit"`
	// 实付金额 以分为单位
	ActualMoneyByMinUnit int32    `protobuf:"varint,12,opt,name=actualMoneyByMinUnit,proto3" json:"actualMoneyByMinUnit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionReduceCalcResponse) Reset()         { *m = PromotionReduceCalcResponse{} }
func (m *PromotionReduceCalcResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionReduceCalcResponse) ProtoMessage()    {}
func (*PromotionReduceCalcResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{20}
}

func (m *PromotionReduceCalcResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionReduceCalcResponse.Unmarshal(m, b)
}
func (m *PromotionReduceCalcResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionReduceCalcResponse.Marshal(b, m, deterministic)
}
func (m *PromotionReduceCalcResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionReduceCalcResponse.Merge(m, src)
}
func (m *PromotionReduceCalcResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionReduceCalcResponse.Size(m)
}
func (m *PromotionReduceCalcResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionReduceCalcResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionReduceCalcResponse proto.InternalMessageInfo

func (m *PromotionReduceCalcResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionReduceCalcResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionReduceCalcResponse) GetReduceMoney() float64 {
	if m != nil {
		return m.ReduceMoney
	}
	return 0
}

func (m *PromotionReduceCalcResponse) GetTotalMoney() float64 {
	if m != nil {
		return m.TotalMoney
	}
	return 0
}

func (m *PromotionReduceCalcResponse) GetActualMoney() float64 {
	if m != nil {
		return m.ActualMoney
	}
	return 0
}

func (m *PromotionReduceCalcResponse) GetPromotionReduceList() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduceList
	}
	return nil
}

func (m *PromotionReduceCalcResponse) GetSkuIds() []string {
	if m != nil {
		return m.SkuIds
	}
	return nil
}

func (m *PromotionReduceCalcResponse) GetReduceMoneyByMinUnit() int32 {
	if m != nil {
		return m.ReduceMoneyByMinUnit
	}
	return 0
}

func (m *PromotionReduceCalcResponse) GetTotalMoneyByMinUnit() int32 {
	if m != nil {
		return m.TotalMoneyByMinUnit
	}
	return 0
}

func (m *PromotionReduceCalcResponse) GetActualMoneyByMinUnit() int32 {
	if m != nil {
		return m.ActualMoneyByMinUnit
	}
	return 0
}

//计算减免金额请求
type PromotionCalcResponse struct {
	// 代码 非 200 取 message 错误信息
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//优惠金额（参与商品优惠的金额，不包含运费优惠） 以分为单位
	ReduceMoneyByMinUnit int32 `protobuf:"varint,4,opt,name=reduceMoneyByMinUnit,proto3" json:"reduceMoneyByMinUnit"`
	// 总金额(商品总金额,不包含运费) 以分为单位
	TotalMoneyByMinUnit int32 `protobuf:"varint,5,opt,name=totalMoneyByMinUnit,proto3" json:"totalMoneyByMinUnit"`
	// 实付金额（商品的实收总金额,不包含运费） 以分为单位
	ActualMoneyByMinUnit int32 `protobuf:"varint,6,opt,name=actualMoneyByMinUnit,proto3" json:"actualMoneyByMinUnit"`
	// 总运费金额,以分为单位
	UpetDjMoneyByMinUnit int32 `protobuf:"varint,7,opt,name=upetDjMoneyByMinUnit,proto3" json:"upetDjMoneyByMinUnit"`
	// 总运费金额实收,以分为单位
	UpetActualDjMoneyByMinUnit int32 `protobuf:"varint,13,opt,name=upetActualDjMoneyByMinUnit,proto3" json:"upetActualDjMoneyByMinUnit"`
	// 总重量
	TotalWeight int32 `protobuf:"varint,3,opt,name=totalWeight,proto3" json:"totalWeight"`
	// 相关商品
	PromotionProduct []*PromotionCalcProductDto `protobuf:"bytes,8,rep,name=promotionProduct,proto3" json:"promotionProduct"`
	//符合条件的满减信息
	PromotionReduceList []*PromotionReduceDto `protobuf:"bytes,9,rep,name=promotionReduceList,proto3" json:"promotionReduceList"`
	//符合条件的满减运费信息
	ReduceDelivery *PromotionReduceDeliveryDto `protobuf:"bytes,10,opt,name=reduceDelivery,proto3" json:"reduceDelivery"`
	//符合条件的限时优惠信息
	TimeDiscount []*PromotionTimeDiscountDto `protobuf:"bytes,11,rep,name=timeDiscount,proto3" json:"timeDiscount"`
	// 优惠信息定义明细
	CalcList []*PromotionCalcDto `protobuf:"bytes,12,rep,name=calcList,proto3" json:"calcList"`
	//能参与限时折扣商品种类数量
	TimeDiscountProductCount int32 `protobuf:"varint,14,opt,name=timeDiscountProductCount,proto3" json:"timeDiscountProductCount"`
	//已经参与限时折扣的商品种类数量
	TimeDiscountCalcProductCount int32    `protobuf:"varint,15,opt,name=timeDiscountCalcProductCount,proto3" json:"timeDiscountCalcProductCount"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *PromotionCalcResponse) Reset()         { *m = PromotionCalcResponse{} }
func (m *PromotionCalcResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionCalcResponse) ProtoMessage()    {}
func (*PromotionCalcResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{21}
}

func (m *PromotionCalcResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionCalcResponse.Unmarshal(m, b)
}
func (m *PromotionCalcResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionCalcResponse.Marshal(b, m, deterministic)
}
func (m *PromotionCalcResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionCalcResponse.Merge(m, src)
}
func (m *PromotionCalcResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionCalcResponse.Size(m)
}
func (m *PromotionCalcResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionCalcResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionCalcResponse proto.InternalMessageInfo

func (m *PromotionCalcResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionCalcResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionCalcResponse) GetReduceMoneyByMinUnit() int32 {
	if m != nil {
		return m.ReduceMoneyByMinUnit
	}
	return 0
}

func (m *PromotionCalcResponse) GetTotalMoneyByMinUnit() int32 {
	if m != nil {
		return m.TotalMoneyByMinUnit
	}
	return 0
}

func (m *PromotionCalcResponse) GetActualMoneyByMinUnit() int32 {
	if m != nil {
		return m.ActualMoneyByMinUnit
	}
	return 0
}

func (m *PromotionCalcResponse) GetUpetDjMoneyByMinUnit() int32 {
	if m != nil {
		return m.UpetDjMoneyByMinUnit
	}
	return 0
}

func (m *PromotionCalcResponse) GetUpetActualDjMoneyByMinUnit() int32 {
	if m != nil {
		return m.UpetActualDjMoneyByMinUnit
	}
	return 0
}

func (m *PromotionCalcResponse) GetTotalWeight() int32 {
	if m != nil {
		return m.TotalWeight
	}
	return 0
}

func (m *PromotionCalcResponse) GetPromotionProduct() []*PromotionCalcProductDto {
	if m != nil {
		return m.PromotionProduct
	}
	return nil
}

func (m *PromotionCalcResponse) GetPromotionReduceList() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduceList
	}
	return nil
}

func (m *PromotionCalcResponse) GetReduceDelivery() *PromotionReduceDeliveryDto {
	if m != nil {
		return m.ReduceDelivery
	}
	return nil
}

func (m *PromotionCalcResponse) GetTimeDiscount() []*PromotionTimeDiscountDto {
	if m != nil {
		return m.TimeDiscount
	}
	return nil
}

func (m *PromotionCalcResponse) GetCalcList() []*PromotionCalcDto {
	if m != nil {
		return m.CalcList
	}
	return nil
}

func (m *PromotionCalcResponse) GetTimeDiscountProductCount() int32 {
	if m != nil {
		return m.TimeDiscountProductCount
	}
	return 0
}

func (m *PromotionCalcResponse) GetTimeDiscountCalcProductCount() int32 {
	if m != nil {
		return m.TimeDiscountCalcProductCount
	}
	return 0
}

//通过sku查询商品是否参与满减活动 数据响应
type PromotionQueryBySkuIdsResponse struct {
	// 代码 非 200 取 message 错误信息
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 关联关系
	Data []*PromotionSkuDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 关联关系
	PageCount            int64    `protobuf:"varint,4,opt,name=pageCount,proto3" json:"pageCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionQueryBySkuIdsResponse) Reset()         { *m = PromotionQueryBySkuIdsResponse{} }
func (m *PromotionQueryBySkuIdsResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionQueryBySkuIdsResponse) ProtoMessage()    {}
func (*PromotionQueryBySkuIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{22}
}

func (m *PromotionQueryBySkuIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionQueryBySkuIdsResponse.Unmarshal(m, b)
}
func (m *PromotionQueryBySkuIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionQueryBySkuIdsResponse.Marshal(b, m, deterministic)
}
func (m *PromotionQueryBySkuIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQueryBySkuIdsResponse.Merge(m, src)
}
func (m *PromotionQueryBySkuIdsResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionQueryBySkuIdsResponse.Size(m)
}
func (m *PromotionQueryBySkuIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQueryBySkuIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQueryBySkuIdsResponse proto.InternalMessageInfo

func (m *PromotionQueryBySkuIdsResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionQueryBySkuIdsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionQueryBySkuIdsResponse) GetData() []*PromotionSkuDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PromotionQueryBySkuIdsResponse) GetPageCount() int64 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

// 活动详情
type PromotionDetailResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	PromotionId int32  `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,11,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 活动优惠信息
	PromotionReduceList []*PromotionReduceDto `protobuf:"bytes,12,rep,name=promotionReduceList,proto3" json:"promotionReduceList"`
	//满减运费
	PromotionReduceDeliveryList []*PromotionReduceDeliveryDto `protobuf:"bytes,13,rep,name=promotionReduceDeliveryList,proto3" json:"promotionReduceDeliveryList"`
	//限时折扣
	PromotionTimeDiscountList []*PromotionTimeDiscountDto `protobuf:"bytes,14,rep,name=promotionTimeDiscountList,proto3" json:"promotionTimeDiscountList"`
	XXX_NoUnkeyedLiteral      struct{}                    `json:"-"`
	XXX_unrecognized          []byte                      `json:"-"`
	XXX_sizecache             int32                       `json:"-"`
}

func (m *PromotionDetailResponse) Reset()         { *m = PromotionDetailResponse{} }
func (m *PromotionDetailResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionDetailResponse) ProtoMessage()    {}
func (*PromotionDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{23}
}

func (m *PromotionDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionDetailResponse.Unmarshal(m, b)
}
func (m *PromotionDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionDetailResponse.Marshal(b, m, deterministic)
}
func (m *PromotionDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDetailResponse.Merge(m, src)
}
func (m *PromotionDetailResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionDetailResponse.Size(m)
}
func (m *PromotionDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDetailResponse proto.InternalMessageInfo

func (m *PromotionDetailResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionDetailResponse) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionDetailResponse) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *PromotionDetailResponse) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *PromotionDetailResponse) GetPromotionReduceList() []*PromotionReduceDto {
	if m != nil {
		return m.PromotionReduceList
	}
	return nil
}

func (m *PromotionDetailResponse) GetPromotionReduceDeliveryList() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.PromotionReduceDeliveryList
	}
	return nil
}

func (m *PromotionDetailResponse) GetPromotionTimeDiscountList() []*PromotionTimeDiscountDto {
	if m != nil {
		return m.PromotionTimeDiscountList
	}
	return nil
}

/////////////////////////////////// PlayBillResponse  //////////////////////////////////////////////////////////////////////
type CreatePlayBillResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePlayBillResponse) Reset()         { *m = CreatePlayBillResponse{} }
func (m *CreatePlayBillResponse) String() string { return proto.CompactTextString(m) }
func (*CreatePlayBillResponse) ProtoMessage()    {}
func (*CreatePlayBillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{24}
}

func (m *CreatePlayBillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePlayBillResponse.Unmarshal(m, b)
}
func (m *CreatePlayBillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePlayBillResponse.Marshal(b, m, deterministic)
}
func (m *CreatePlayBillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePlayBillResponse.Merge(m, src)
}
func (m *CreatePlayBillResponse) XXX_Size() int {
	return xxx_messageInfo_CreatePlayBillResponse.Size(m)
}
func (m *CreatePlayBillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePlayBillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePlayBillResponse proto.InternalMessageInfo

func (m *CreatePlayBillResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreatePlayBillResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreatePlayBillResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type GetPlayBillListResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Playbills            []*PlayBill `protobuf:"bytes,4,rep,name=playbills,proto3" json:"playbills"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPlayBillListResponse) Reset()         { *m = GetPlayBillListResponse{} }
func (m *GetPlayBillListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPlayBillListResponse) ProtoMessage()    {}
func (*GetPlayBillListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{25}
}

func (m *GetPlayBillListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayBillListResponse.Unmarshal(m, b)
}
func (m *GetPlayBillListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayBillListResponse.Marshal(b, m, deterministic)
}
func (m *GetPlayBillListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayBillListResponse.Merge(m, src)
}
func (m *GetPlayBillListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPlayBillListResponse.Size(m)
}
func (m *GetPlayBillListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayBillListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayBillListResponse proto.InternalMessageInfo

func (m *GetPlayBillListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPlayBillListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPlayBillListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPlayBillListResponse) GetPlaybills() []*PlayBill {
	if m != nil {
		return m.Playbills
	}
	return nil
}

type GetSinglePlayBillListResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Playbills            []*PlayBill `protobuf:"bytes,4,rep,name=playbills,proto3" json:"playbills"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSinglePlayBillListResponse) Reset()         { *m = GetSinglePlayBillListResponse{} }
func (m *GetSinglePlayBillListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSinglePlayBillListResponse) ProtoMessage()    {}
func (*GetSinglePlayBillListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{26}
}

func (m *GetSinglePlayBillListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSinglePlayBillListResponse.Unmarshal(m, b)
}
func (m *GetSinglePlayBillListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSinglePlayBillListResponse.Marshal(b, m, deterministic)
}
func (m *GetSinglePlayBillListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSinglePlayBillListResponse.Merge(m, src)
}
func (m *GetSinglePlayBillListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSinglePlayBillListResponse.Size(m)
}
func (m *GetSinglePlayBillListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSinglePlayBillListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSinglePlayBillListResponse proto.InternalMessageInfo

func (m *GetSinglePlayBillListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetSinglePlayBillListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetSinglePlayBillListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetSinglePlayBillListResponse) GetPlaybills() []*PlayBill {
	if m != nil {
		return m.Playbills
	}
	return nil
}

type PlayBill struct {
	Playbillid      string `protobuf:"bytes,1,opt,name=playbillid,proto3" json:"playbillid"`
	Playbillname    string `protobuf:"bytes,2,opt,name=playbillname,proto3" json:"playbillname"`
	Playbillcontent string `protobuf:"bytes,3,opt,name=playbillcontent,proto3" json:"playbillcontent"`
	//是否为门店码 1是 0否
	IsStore              int32    `protobuf:"varint,4,opt,name=is_store,json=isStore,proto3" json:"is_store"`
	Createdate           string   `protobuf:"bytes,5,opt,name=createdate,proto3" json:"createdate"`
	State                int32    `protobuf:"varint,6,opt,name=state,proto3" json:"state"`
	Num                  int32    `protobuf:"varint,7,opt,name=num,proto3" json:"num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayBill) Reset()         { *m = PlayBill{} }
func (m *PlayBill) String() string { return proto.CompactTextString(m) }
func (*PlayBill) ProtoMessage()    {}
func (*PlayBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{27}
}

func (m *PlayBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayBill.Unmarshal(m, b)
}
func (m *PlayBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayBill.Marshal(b, m, deterministic)
}
func (m *PlayBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayBill.Merge(m, src)
}
func (m *PlayBill) XXX_Size() int {
	return xxx_messageInfo_PlayBill.Size(m)
}
func (m *PlayBill) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayBill.DiscardUnknown(m)
}

var xxx_messageInfo_PlayBill proto.InternalMessageInfo

func (m *PlayBill) GetPlaybillid() string {
	if m != nil {
		return m.Playbillid
	}
	return ""
}

func (m *PlayBill) GetPlaybillname() string {
	if m != nil {
		return m.Playbillname
	}
	return ""
}

func (m *PlayBill) GetPlaybillcontent() string {
	if m != nil {
		return m.Playbillcontent
	}
	return ""
}

func (m *PlayBill) GetIsStore() int32 {
	if m != nil {
		return m.IsStore
	}
	return 0
}

func (m *PlayBill) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

func (m *PlayBill) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *PlayBill) GetNum() int32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type DownloadPlayBillResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Playbillqrcode       string   `protobuf:"bytes,4,opt,name=playbillqrcode,proto3" json:"playbillqrcode"`
	Template             int32    `protobuf:"varint,5,opt,name=template,proto3" json:"template"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownloadPlayBillResponse) Reset()         { *m = DownloadPlayBillResponse{} }
func (m *DownloadPlayBillResponse) String() string { return proto.CompactTextString(m) }
func (*DownloadPlayBillResponse) ProtoMessage()    {}
func (*DownloadPlayBillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{28}
}

func (m *DownloadPlayBillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownloadPlayBillResponse.Unmarshal(m, b)
}
func (m *DownloadPlayBillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownloadPlayBillResponse.Marshal(b, m, deterministic)
}
func (m *DownloadPlayBillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadPlayBillResponse.Merge(m, src)
}
func (m *DownloadPlayBillResponse) XXX_Size() int {
	return xxx_messageInfo_DownloadPlayBillResponse.Size(m)
}
func (m *DownloadPlayBillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadPlayBillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadPlayBillResponse proto.InternalMessageInfo

func (m *DownloadPlayBillResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DownloadPlayBillResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DownloadPlayBillResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *DownloadPlayBillResponse) GetPlaybillqrcode() string {
	if m != nil {
		return m.Playbillqrcode
	}
	return ""
}

func (m *DownloadPlayBillResponse) GetTemplate() int32 {
	if m != nil {
		return m.Template
	}
	return 0
}

type GetPromotionOperateLogListRequest struct {
	//门店财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//商品sku_id
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//活动名称
	PromotionName string `protobuf:"bytes,3,opt,name=promotion_name,json=promotionName,proto3" json:"promotion_name"`
	//操作类型 1撤销 2编辑活动 3 创建活动 4删除
	OperateType int32 `protobuf:"varint,4,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	//开始时间
	StartTime string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//当前页 从1开始  如不传默认为1
	PageIndex int32 `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页多少条数据  如不传 则默认为15条
	PageSize int32 `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//活动id
	PromotionId int32 `protobuf:"varint,9,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	//活动类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType        int32    `protobuf:"varint,10,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPromotionOperateLogListRequest) Reset()         { *m = GetPromotionOperateLogListRequest{} }
func (m *GetPromotionOperateLogListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPromotionOperateLogListRequest) ProtoMessage()    {}
func (*GetPromotionOperateLogListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{29}
}

func (m *GetPromotionOperateLogListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPromotionOperateLogListRequest.Unmarshal(m, b)
}
func (m *GetPromotionOperateLogListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPromotionOperateLogListRequest.Marshal(b, m, deterministic)
}
func (m *GetPromotionOperateLogListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPromotionOperateLogListRequest.Merge(m, src)
}
func (m *GetPromotionOperateLogListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPromotionOperateLogListRequest.Size(m)
}
func (m *GetPromotionOperateLogListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPromotionOperateLogListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPromotionOperateLogListRequest proto.InternalMessageInfo

func (m *GetPromotionOperateLogListRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GetPromotionOperateLogListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetPromotionOperateLogListRequest) GetPromotionName() string {
	if m != nil {
		return m.PromotionName
	}
	return ""
}

func (m *GetPromotionOperateLogListRequest) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *GetPromotionOperateLogListRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetPromotionOperateLogListRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetPromotionOperateLogListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetPromotionOperateLogListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPromotionOperateLogListRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *GetPromotionOperateLogListRequest) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

type GetPromotionOperateLogListResponse struct {
	Code                 int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PromotionOperateLog `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int32                  `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPromotionOperateLogListResponse) Reset()         { *m = GetPromotionOperateLogListResponse{} }
func (m *GetPromotionOperateLogListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPromotionOperateLogListResponse) ProtoMessage()    {}
func (*GetPromotionOperateLogListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{30}
}

func (m *GetPromotionOperateLogListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPromotionOperateLogListResponse.Unmarshal(m, b)
}
func (m *GetPromotionOperateLogListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPromotionOperateLogListResponse.Marshal(b, m, deterministic)
}
func (m *GetPromotionOperateLogListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPromotionOperateLogListResponse.Merge(m, src)
}
func (m *GetPromotionOperateLogListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPromotionOperateLogListResponse.Size(m)
}
func (m *GetPromotionOperateLogListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPromotionOperateLogListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPromotionOperateLogListResponse proto.InternalMessageInfo

func (m *GetPromotionOperateLogListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPromotionOperateLogListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPromotionOperateLogListResponse) GetData() []*PromotionOperateLog {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetPromotionOperateLogListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PromotionOperateLog struct {
	//操作类型 1撤销 2编辑活动 3 创建活动 4删除
	OperateType int32 `protobuf:"varint,1,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	//活动名称
	PromotionName string `protobuf:"bytes,2,opt,name=promotion_name,json=promotionName,proto3" json:"promotion_name"`
	//活动id
	PromotionId int32 `protobuf:"varint,3,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	//活动类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType int32 `protobuf:"varint,4,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//活动优惠信息
	PromotionInfo string `protobuf:"bytes,5,opt,name=promotion_info,json=promotionInfo,proto3" json:"promotion_info"`
	//门店名称
	ShopName string `protobuf:"bytes,6,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//门店财务编码
	FinanceCode string `protobuf:"bytes,7,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//商品sku_id
	SkuId int32 `protobuf:"varint,8,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//创建人用户名
	CreateUserName string `protobuf:"bytes,9,opt,name=create_user_name,json=createUserName,proto3" json:"create_user_name"`
	//创建人用户id
	CreateUserId string `protobuf:"bytes,10,opt,name=create_user_id,json=createUserId,proto3" json:"create_user_id"`
	//操作人用户id
	OperateUserId string `protobuf:"bytes,11,opt,name=operate_user_id,json=operateUserId,proto3" json:"operate_user_id"`
	//操作人用户名
	OperateUserName string `protobuf:"bytes,12,opt,name=operate_user_name,json=operateUserName,proto3" json:"operate_user_name"`
	//操作时间
	CreateTime string `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//id记录号
	Id                   int32    `protobuf:"varint,14,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionOperateLog) Reset()         { *m = PromotionOperateLog{} }
func (m *PromotionOperateLog) String() string { return proto.CompactTextString(m) }
func (*PromotionOperateLog) ProtoMessage()    {}
func (*PromotionOperateLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{31}
}

func (m *PromotionOperateLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionOperateLog.Unmarshal(m, b)
}
func (m *PromotionOperateLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionOperateLog.Marshal(b, m, deterministic)
}
func (m *PromotionOperateLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionOperateLog.Merge(m, src)
}
func (m *PromotionOperateLog) XXX_Size() int {
	return xxx_messageInfo_PromotionOperateLog.Size(m)
}
func (m *PromotionOperateLog) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionOperateLog.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionOperateLog proto.InternalMessageInfo

func (m *PromotionOperateLog) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *PromotionOperateLog) GetPromotionName() string {
	if m != nil {
		return m.PromotionName
	}
	return ""
}

func (m *PromotionOperateLog) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionOperateLog) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *PromotionOperateLog) GetPromotionInfo() string {
	if m != nil {
		return m.PromotionInfo
	}
	return ""
}

func (m *PromotionOperateLog) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *PromotionOperateLog) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *PromotionOperateLog) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *PromotionOperateLog) GetCreateUserName() string {
	if m != nil {
		return m.CreateUserName
	}
	return ""
}

func (m *PromotionOperateLog) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func (m *PromotionOperateLog) GetOperateUserId() string {
	if m != nil {
		return m.OperateUserId
	}
	return ""
}

func (m *PromotionOperateLog) GetOperateUserName() string {
	if m != nil {
		return m.OperateUserName
	}
	return ""
}

func (m *PromotionOperateLog) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PromotionOperateLog) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type StorePromotionOperateLogRequest struct {
	LogList              []*StorePromotionOperateLog `protobuf:"bytes,1,rep,name=log_list,json=logList,proto3" json:"log_list"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *StorePromotionOperateLogRequest) Reset()         { *m = StorePromotionOperateLogRequest{} }
func (m *StorePromotionOperateLogRequest) String() string { return proto.CompactTextString(m) }
func (*StorePromotionOperateLogRequest) ProtoMessage()    {}
func (*StorePromotionOperateLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{32}
}

func (m *StorePromotionOperateLogRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StorePromotionOperateLogRequest.Unmarshal(m, b)
}
func (m *StorePromotionOperateLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StorePromotionOperateLogRequest.Marshal(b, m, deterministic)
}
func (m *StorePromotionOperateLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StorePromotionOperateLogRequest.Merge(m, src)
}
func (m *StorePromotionOperateLogRequest) XXX_Size() int {
	return xxx_messageInfo_StorePromotionOperateLogRequest.Size(m)
}
func (m *StorePromotionOperateLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StorePromotionOperateLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StorePromotionOperateLogRequest proto.InternalMessageInfo

func (m *StorePromotionOperateLogRequest) GetLogList() []*StorePromotionOperateLog {
	if m != nil {
		return m.LogList
	}
	return nil
}

type StorePromotionOperateLog struct {
	OperateUserId        string   `protobuf:"bytes,1,opt,name=operate_user_id,json=operateUserId,proto3" json:"operate_user_id"`
	OperateUserName      string   `protobuf:"bytes,2,opt,name=operate_user_name,json=operateUserName,proto3" json:"operate_user_name"`
	OperateType          int32    `protobuf:"varint,3,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	PromotionName        string   `protobuf:"bytes,4,opt,name=promotion_name,json=promotionName,proto3" json:"promotion_name"`
	PromotionId          int32    `protobuf:"varint,5,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	PromotionType        int32    `protobuf:"varint,6,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	Params               string   `protobuf:"bytes,7,opt,name=params,proto3" json:"params"`
	ShopName             string   `protobuf:"bytes,8,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	FinanceCode          string   `protobuf:"bytes,9,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	SkuId                int32    `protobuf:"varint,10,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	CreateUserName       string   `protobuf:"bytes,11,opt,name=create_user_name,json=createUserName,proto3" json:"create_user_name"`
	CreateUserId         string   `protobuf:"bytes,12,opt,name=create_user_id,json=createUserId,proto3" json:"create_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StorePromotionOperateLog) Reset()         { *m = StorePromotionOperateLog{} }
func (m *StorePromotionOperateLog) String() string { return proto.CompactTextString(m) }
func (*StorePromotionOperateLog) ProtoMessage()    {}
func (*StorePromotionOperateLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_69e57e73d2ecac24, []int{33}
}

func (m *StorePromotionOperateLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StorePromotionOperateLog.Unmarshal(m, b)
}
func (m *StorePromotionOperateLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StorePromotionOperateLog.Marshal(b, m, deterministic)
}
func (m *StorePromotionOperateLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StorePromotionOperateLog.Merge(m, src)
}
func (m *StorePromotionOperateLog) XXX_Size() int {
	return xxx_messageInfo_StorePromotionOperateLog.Size(m)
}
func (m *StorePromotionOperateLog) XXX_DiscardUnknown() {
	xxx_messageInfo_StorePromotionOperateLog.DiscardUnknown(m)
}

var xxx_messageInfo_StorePromotionOperateLog proto.InternalMessageInfo

func (m *StorePromotionOperateLog) GetOperateUserId() string {
	if m != nil {
		return m.OperateUserId
	}
	return ""
}

func (m *StorePromotionOperateLog) GetOperateUserName() string {
	if m != nil {
		return m.OperateUserName
	}
	return ""
}

func (m *StorePromotionOperateLog) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *StorePromotionOperateLog) GetPromotionName() string {
	if m != nil {
		return m.PromotionName
	}
	return ""
}

func (m *StorePromotionOperateLog) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *StorePromotionOperateLog) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *StorePromotionOperateLog) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

func (m *StorePromotionOperateLog) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *StorePromotionOperateLog) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *StorePromotionOperateLog) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *StorePromotionOperateLog) GetCreateUserName() string {
	if m != nil {
		return m.CreateUserName
	}
	return ""
}

func (m *StorePromotionOperateLog) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseRequest)(nil), "mk.baseRequest")
	proto.RegisterType((*QueryByIdRequest)(nil), "mk.queryByIdRequest")
	proto.RegisterType((*DeleteRequest)(nil), "mk.deleteRequest")
	proto.RegisterType((*UpdateIsEnableRequest)(nil), "mk.updateIsEnableRequest")
	proto.RegisterType((*PromotionSummaryReq)(nil), "mk.promotionSummaryReq")
	proto.RegisterType((*PromotionSummaryRes)(nil), "mk.promotionSummaryRes")
	proto.RegisterType((*PromotionSummaryShopDto)(nil), "mk.promotionSummaryShopDto")
	proto.RegisterType((*PromotionSummaryDto)(nil), "mk.promotionSummaryDto")
	proto.RegisterType((*PromotionQueryByShopIdRequest)(nil), "mk.promotionQueryByShopIdRequest")
	proto.RegisterType((*QueryPromotionByShopIdReq)(nil), "mk.QueryPromotionByShopIdReq")
	proto.RegisterType((*PromotionCalcRequest)(nil), "mk.promotionCalcRequest")
	proto.RegisterType((*GetDeliveryMoneyRequest)(nil), "mk.GetDeliveryMoneyRequest")
	proto.RegisterType((*PromotionQueryBySkuIdsRequest)(nil), "mk.promotionQueryBySkuIdsRequest")
	proto.RegisterType((*CreatePlayBillRequest)(nil), "mk.CreatePlayBillRequest")
	proto.RegisterType((*GetPlayBillListRequest)(nil), "mk.GetPlayBillListRequest")
	proto.RegisterType((*GetSinglePlayBillListRequest)(nil), "mk.GetSinglePlayBillListRequest")
	proto.RegisterType((*DeleteSinglePlayBillRequest)(nil), "mk.DeleteSinglePlayBillRequest")
	proto.RegisterType((*DownloadPlayBillRequest)(nil), "mk.DownloadPlayBillRequest")
	proto.RegisterType((*BaseResponse)(nil), "mk.baseResponse")
	proto.RegisterType((*PromotionQueryByShopIdResponse)(nil), "mk.promotionQueryByShopIdResponse")
	proto.RegisterType((*PromotionReduceCalcResponse)(nil), "mk.promotionReduceCalcResponse")
	proto.RegisterType((*PromotionCalcResponse)(nil), "mk.promotionCalcResponse")
	proto.RegisterType((*PromotionQueryBySkuIdsResponse)(nil), "mk.promotionQueryBySkuIdsResponse")
	proto.RegisterType((*PromotionDetailResponse)(nil), "mk.promotionDetailResponse")
	proto.RegisterType((*CreatePlayBillResponse)(nil), "mk.CreatePlayBillResponse")
	proto.RegisterType((*GetPlayBillListResponse)(nil), "mk.GetPlayBillListResponse")
	proto.RegisterType((*GetSinglePlayBillListResponse)(nil), "mk.GetSinglePlayBillListResponse")
	proto.RegisterType((*PlayBill)(nil), "mk.PlayBill")
	proto.RegisterType((*DownloadPlayBillResponse)(nil), "mk.DownloadPlayBillResponse")
	proto.RegisterType((*GetPromotionOperateLogListRequest)(nil), "mk.GetPromotionOperateLogListRequest")
	proto.RegisterType((*GetPromotionOperateLogListResponse)(nil), "mk.GetPromotionOperateLogListResponse")
	proto.RegisterType((*PromotionOperateLog)(nil), "mk.PromotionOperateLog")
	proto.RegisterType((*StorePromotionOperateLogRequest)(nil), "mk.StorePromotionOperateLogRequest")
	proto.RegisterType((*StorePromotionOperateLog)(nil), "mk.StorePromotionOperateLog")
}

func init() { proto.RegisterFile("mk/promotion.proto", fileDescriptor_69e57e73d2ecac24) }

var fileDescriptor_69e57e73d2ecac24 = []byte{
	// 1945 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x19, 0x4d, 0x8f, 0x1b, 0x49,
	0x55, 0x6d, 0x4f, 0x8f, 0xed, 0x67, 0xc7, 0x99, 0x54, 0x26, 0x33, 0x4e, 0x32, 0x93, 0x9d, 0x69,
	0x41, 0x18, 0x2d, 0x30, 0xd9, 0x0c, 0x48, 0x48, 0xa0, 0x45, 0x90, 0x0c, 0x04, 0x4b, 0xbb, 0xcb,
	0xd0, 0x93, 0x08, 0x76, 0xb5, 0x92, 0xa9, 0xb8, 0x2b, 0x76, 0x33, 0xed, 0xae, 0x4e, 0x57, 0x99,
	0x60, 0x2e, 0x9c, 0x57, 0x1c, 0x10, 0x12, 0x9c, 0x38, 0x20, 0xf1, 0x0f, 0x38, 0xec, 0x0f, 0xe0,
	0x07, 0xc0, 0x1f, 0x40, 0xe2, 0xc2, 0x61, 0xff, 0x04, 0x07, 0x54, 0xaf, 0xaa, 0xbb, 0xab, 0xed,
	0xf6, 0x8c, 0x67, 0x32, 0xd2, 0xde, 0xfc, 0x5e, 0xbd, 0xf7, 0xea, 0x7d, 0xd7, 0x7b, 0x6d, 0x20,
	0x93, 0xb3, 0x47, 0x49, 0xca, 0x27, 0x5c, 0x86, 0x3c, 0x3e, 0x4c, 0x52, 0x2e, 0x39, 0xa9, 0x4d,
	0xce, 0xee, 0x75, 0x27, 0x67, 0x8f, 0x26, 0x3c, 0x60, 0x91, 0xc6, 0x79, 0xef, 0x43, 0xfb, 0x25,
	0x15, 0xcc, 0x67, 0xaf, 0xa7, 0x4c, 0x48, 0xb2, 0x05, 0xeb, 0x53, 0xc1, 0xd2, 0x7e, 0xd0, 0x73,
	0xf6, 0x9c, 0x83, 0x96, 0x6f, 0x20, 0x85, 0x17, 0x63, 0x9e, 0xf4, 0x83, 0x5e, 0x4d, 0xe3, 0x35,
	0xe4, 0x79, 0xb0, 0xf1, 0x7a, 0xca, 0xd2, 0xd9, 0x93, 0x59, 0x3f, 0xc8, 0x64, 0x74, 0xa1, 0x16,
	0x6a, 0x3a, 0xd7, 0xaf, 0x85, 0x81, 0xc7, 0xe0, 0x46, 0xc0, 0x22, 0x26, 0xf3, 0x4b, 0x36, 0xa0,
	0x1e, 0x06, 0xc2, 0xdc, 0xa0, 0x7e, 0xce, 0xb3, 0x58, 0x6a, 0xac, 0x95, 0xd4, 0xb8, 0x07, 0x4d,
	0xf5, 0xeb, 0x23, 0x3a, 0x61, 0x3d, 0x17, 0x4f, 0x72, 0xd8, 0xfb, 0xcc, 0x81, 0x3b, 0xd3, 0x24,
	0xa0, 0x92, 0xf5, 0xc5, 0x8f, 0x62, 0xfa, 0x32, 0xba, 0xc4, 0x7d, 0xf7, 0xa0, 0x19, 0x1a, 0xa6,
	0x5e, 0x7d, 0xcf, 0x39, 0x68, 0xfa, 0x39, 0x7c, 0x25, 0x5d, 0xbe, 0x09, 0xb7, 0x73, 0xe7, 0x9f,
	0x4e, 0x27, 0x13, 0x9a, 0xce, 0x7c, 0xf6, 0x7a, 0xa9, 0x17, 0xff, 0xee, 0x54, 0xd1, 0x0b, 0xb2,
	0x03, 0x6b, 0x43, 0x1e, 0x30, 0xd4, 0xbc, 0x7b, 0xd4, 0x3c, 0x9c, 0x9c, 0x1d, 0x2a, 0xd8, 0x47,
	0x2c, 0xe9, 0x41, 0x63, 0xc2, 0x84, 0xa0, 0x23, 0x66, 0xc4, 0x65, 0x20, 0x79, 0x0c, 0x0d, 0xa1,
	0xa5, 0xf4, 0xea, 0x7b, 0xf5, 0x83, 0xf6, 0xd1, 0xb6, 0x62, 0x9d, 0xbf, 0xe1, 0x58, 0x72, 0x3f,
	0xa3, 0x23, 0x8f, 0x60, 0x4d, 0x29, 0x83, 0x36, 0xb6, 0x8f, 0xee, 0x57, 0xd1, 0x9f, 0x8e, 0x79,
	0xa2, 0x78, 0x90, 0xd0, 0xfb, 0xdc, 0x81, 0xed, 0x25, 0x14, 0xe4, 0x10, 0x48, 0x71, 0x34, 0xe6,
	0xc9, 0x53, 0x3e, 0x8d, 0x25, 0x5a, 0xe1, 0xfa, 0x15, 0x27, 0xe4, 0xdb, 0x70, 0x27, 0xe6, 0xf2,
	0x64, 0x91, 0x45, 0x47, 0xa8, 0xfa, 0x50, 0x71, 0xe5, 0xb2, 0xc2, 0x78, 0x54, 0x70, 0xd5, 0x35,
	0x57, 0xe5, 0xa1, 0xf7, 0xcf, 0x0a, 0x5f, 0x2b, 0x9d, 0xb7, 0x60, 0x5d, 0xce, 0x12, 0x66, 0x32,
	0xdf, 0xf5, 0x0d, 0xa4, 0xc2, 0xac, 0x7e, 0x61, 0x98, 0xb5, 0x9b, 0x73, 0x98, 0xec, 0x40, 0x4b,
	0xfd, 0xb6, 0x6f, 0x2d, 0x10, 0xe4, 0x21, 0x74, 0x51, 0x46, 0x7c, 0x92, 0xf2, 0x51, 0xca, 0x84,
	0xc0, 0x34, 0x71, 0xfd, 0x39, 0x2c, 0xf1, 0xa0, 0x83, 0x12, 0xb9, 0x3c, 0x95, 0x34, 0x95, 0xbd,
	0x75, 0xa4, 0x2a, 0xe1, 0x54, 0xac, 0xb3, 0x88, 0x36, 0x74, 0xac, 0x0d, 0xe8, 0x7d, 0xe1, 0xc0,
	0x6e, 0x6e, 0xcf, 0xcf, 0x74, 0x2d, 0x9e, 0x62, 0x5a, 0x59, 0x35, 0x6d, 0xb2, 0xce, 0xb1, 0xb3,
	0x8e, 0x3c, 0x00, 0x10, 0x63, 0xfe, 0xe6, 0x98, 0x49, 0x1a, 0x46, 0x26, 0xed, 0x2d, 0x0c, 0xd9,
	0x04, 0x57, 0xe9, 0x20, 0x8c, 0x42, 0x1a, 0x50, 0x36, 0x27, 0x74, 0xc4, 0xfa, 0x71, 0xc0, 0x7e,
	0x83, 0xba, 0xb8, 0x7e, 0x81, 0x50, 0xde, 0x52, 0xc0, 0x69, 0xf8, 0x5b, 0xd6, 0x6b, 0xe2, 0x61,
	0x0e, 0x23, 0x67, 0xca, 0x83, 0xe9, 0x50, 0xf6, 0x83, 0x5e, 0xcb, 0x70, 0x66, 0x08, 0xb2, 0x07,
	0xed, 0x37, 0x34, 0x65, 0x63, 0x3e, 0x15, 0x2a, 0x08, 0x80, 0xe7, 0x36, 0xca, 0x3b, 0x85, 0xbb,
	0x68, 0x5f, 0x9e, 0x0a, 0x96, 0xa1, 0x2b, 0x1a, 0x59, 0x9b, 0x37, 0xd2, 0xfb, 0x53, 0x0d, 0x36,
	0x73, 0xf7, 0x3d, 0xa5, 0xd1, 0xf0, 0x22, 0xaf, 0xed, 0x40, 0x6b, 0x38, 0xa6, 0x71, 0xcc, 0xa2,
	0x7e, 0xd6, 0x41, 0x0a, 0x84, 0x8a, 0x65, 0xc0, 0x84, 0x0c, 0x63, 0xaa, 0xe4, 0xfd, 0x02, 0xbd,
	0xea, 0xf8, 0x25, 0xdc, 0x1c, 0xcd, 0xc7, 0x58, 0x72, 0x65, 0x9a, 0x8f, 0xd5, 0x2d, 0xa1, 0xf8,
	0x88, 0xbd, 0x79, 0x21, 0x58, 0x8a, 0xfe, 0x6f, 0xfa, 0x05, 0x82, 0x6c, 0x43, 0x43, 0xb5, 0x9a,
	0x41, 0x18, 0x98, 0x6c, 0xc8, 0x7a, 0xd2, 0x33, 0xd8, 0xc8, 0x8d, 0x39, 0xd1, 0xae, 0xed, 0xb9,
	0xd8, 0x01, 0xca, 0x15, 0xad, 0x0c, 0x35, 0xe7, 0xaa, 0xa2, 0x17, 0x98, 0xbc, 0xbf, 0x38, 0xb0,
	0xfd, 0x8c, 0xc9, 0x63, 0x16, 0x85, 0xbf, 0x66, 0xe9, 0xec, 0x43, 0x1e, 0xb3, 0xd9, 0x45, 0x9e,
	0xb9, 0x2e, 0xdb, 0xf7, 0xa0, 0x2d, 0xb9, 0xa4, 0xd1, 0xcf, 0x59, 0x38, 0x1a, 0xeb, 0x72, 0x70,
	0x7c, 0x1b, 0xa5, 0xb4, 0x5b, 0xcc, 0xf9, 0xb3, 0x69, 0x3f, 0x10, 0x97, 0x8a, 0x5e, 0x7d, 0x3e,
	0x7a, 0x8a, 0x0b, 0xc5, 0xf4, 0x6a, 0x7b, 0x75, 0xe4, 0x42, 0x88, 0x7c, 0x03, 0x6e, 0x85, 0xc2,
	0x67, 0x74, 0x38, 0xf6, 0x59, 0x30, 0x1d, 0xb2, 0x7e, 0xfc, 0x8a, 0xa3, 0xea, 0x4d, 0x7f, 0xf1,
	0xc0, 0xfb, 0xaf, 0x03, 0x77, 0x9e, 0xa6, 0x8c, 0x4a, 0x76, 0x12, 0xd1, 0xd9, 0x93, 0x30, 0x8a,
	0x32, 0xad, 0xf6, 0xa1, 0xf3, 0x2a, 0x8c, 0x69, 0x3c, 0x64, 0x03, 0xd3, 0xd7, 0xd5, 0x2d, 0x6d,
	0x83, 0x7b, 0xaa, 0x9a, 0xba, 0x07, 0x9d, 0x24, 0xa2, 0xb3, 0x97, 0x61, 0x14, 0xc5, 0x45, 0xcb,
	0x29, 0xe1, 0xc8, 0x01, 0xdc, 0xcc, 0xe0, 0x21, 0x8f, 0x25, 0x33, 0xcd, 0xa7, 0xe5, 0xcf, 0xa3,
	0xb1, 0x79, 0xb1, 0x49, 0x12, 0x51, 0xc9, 0x50, 0x5f, 0xd7, 0xcf, 0x61, 0x74, 0xb3, 0xf9, 0x1d,
	0x4e, 0x46, 0xe6, 0x09, 0xb3, 0x51, 0xe4, 0xbe, 0x2e, 0xf5, 0x41, 0x42, 0xe5, 0x18, 0xc3, 0xd0,
	0xd2, 0xd5, 0x7c, 0x42, 0xe5, 0xd8, 0xfb, 0x1e, 0x6c, 0x3d, 0x63, 0x32, 0xb3, 0xf0, 0x83, 0x50,
	0xc8, 0xe5, 0x56, 0x3a, 0x73, 0x56, 0x7a, 0x7f, 0x74, 0x60, 0xe7, 0x19, 0x93, 0xa7, 0x61, 0x3c,
	0x8a, 0xd8, 0xd5, 0x64, 0xac, 0xe4, 0xa9, 0x4d, 0x70, 0x85, 0x0c, 0x27, 0xcc, 0xf8, 0x47, 0x03,
	0x0a, 0xcb, 0x10, 0xab, 0x1f, 0x74, 0x0d, 0x78, 0xef, 0xc3, 0xfd, 0x63, 0x1c, 0x53, 0xca, 0x5a,
	0x65, 0x1a, 0x3d, 0x00, 0xc8, 0x44, 0x87, 0x59, 0x56, 0x59, 0x18, 0xef, 0x77, 0xb0, 0x7d, 0xcc,
	0xdf, 0xc4, 0x11, 0xa7, 0xc1, 0x25, 0x59, 0x17, 0x8c, 0xad, 0x2d, 0x1a, 0xbb, 0x0f, 0x1d, 0x3a,
	0x1c, 0x32, 0x21, 0x06, 0x92, 0x9f, 0xb1, 0xd8, 0xd8, 0xd3, 0xd6, 0xb8, 0xe7, 0x0a, 0xe5, 0x7d,
	0x0a, 0x1d, 0x3d, 0xc9, 0x89, 0x84, 0xc7, 0x82, 0x5d, 0x79, 0x78, 0x50, 0xde, 0x49, 0x53, 0x9e,
	0x66, 0x3e, 0x43, 0xc0, 0xfb, 0xbd, 0x03, 0x0f, 0x96, 0x3d, 0x33, 0x6f, 0x79, 0xe1, 0x63, 0x58,
	0x0b, 0xa8, 0xa4, 0x66, 0x54, 0xd9, 0x2d, 0x35, 0xaa, 0xd2, 0x4d, 0x38, 0x7c, 0x28, 0x52, 0xef,
	0xf3, 0x3a, 0xdc, 0xcf, 0x69, 0x74, 0xe9, 0xe9, 0xde, 0xfd, 0x96, 0xaa, 0xec, 0x41, 0x3b, 0x45,
	0x69, 0xd8, 0xf0, 0x4c, 0x07, 0xb3, 0x51, 0x2a, 0x96, 0xd8, 0x89, 0x34, 0x81, 0x8b, 0x04, 0x16,
	0x46, 0x49, 0xa0, 0x43, 0x39, 0xcd, 0x08, 0x4c, 0xf3, 0xb2, 0x50, 0xe4, 0x27, 0xd6, 0xfc, 0xa1,
	0x55, 0x57, 0x89, 0xdf, 0x5b, 0x43, 0xeb, 0xb7, 0x4a, 0xd6, 0xeb, 0x63, 0x65, 0x76, 0x15, 0x8b,
	0xd5, 0xae, 0x1a, 0xa5, 0x76, 0x75, 0x04, 0x9b, 0x96, 0xca, 0x4f, 0x66, 0x1f, 0x86, 0xf1, 0x8b,
	0x38, 0x94, 0xe6, 0x4d, 0xad, 0x3c, 0x23, 0xef, 0xc1, 0xed, 0xc2, 0x8a, 0x82, 0xa5, 0x8d, 0x2c,
	0x55, 0x47, 0xea, 0x16, 0xcb, 0xac, 0x82, 0xa5, 0xa3, 0x6f, 0xa9, 0x3a, 0xf3, 0xfe, 0xb5, 0x6e,
	0xcd, 0x6c, 0xd7, 0x12, 0xb1, 0x65, 0xb6, 0xae, 0x5d, 0xde, 0x56, 0xf7, 0xf2, 0xb6, 0xae, 0x2f,
	0xb7, 0x55, 0xf1, 0x4c, 0x13, 0x26, 0x8f, 0x7f, 0x35, 0xc7, 0xa3, 0x67, 0xa6, 0xca, 0x33, 0xf2,
	0x7d, 0xb8, 0xa7, 0xf0, 0x3f, 0x44, 0x79, 0x0b, 0x9c, 0x37, 0x90, 0xf3, 0x1c, 0x8a, 0xf9, 0xa7,
	0x53, 0x3f, 0x70, 0x36, 0xaa, 0x72, 0x42, 0x68, 0x5e, 0x61, 0x42, 0x58, 0x96, 0xc6, 0xad, 0xcb,
	0xa7, 0xf1, 0x8f, 0xa1, 0xab, 0xc3, 0x94, 0x4d, 0x1b, 0x98, 0xa8, 0xed, 0xa3, 0x07, 0x55, 0x42,
	0x0c, 0x89, 0x12, 0x36, 0xc7, 0x45, 0x7e, 0x00, 0x1d, 0xd5, 0xc8, 0x8f, 0x43, 0x31, 0xc4, 0x81,
	0xbc, 0x8d, 0xaa, 0xec, 0x94, 0xa4, 0x3c, 0xb7, 0x08, 0x94, 0x8c, 0x12, 0x07, 0x79, 0x0f, 0x9a,
	0x43, 0x1a, 0x0d, 0xd1, 0x90, 0x0e, 0x72, 0x6f, 0x2e, 0x38, 0x45, 0x71, 0xe5, 0x54, 0xe4, 0xbb,
	0xd0, 0xb3, 0x25, 0x18, 0xe7, 0xe8, 0x85, 0xa0, 0x8b, 0xde, 0x5f, 0x7a, 0x4e, 0x9e, 0xc0, 0x8e,
	0x7d, 0x66, 0x79, 0x5c, 0xf3, 0xdf, 0x44, 0xfe, 0x73, 0x69, 0xbc, 0xbf, 0x56, 0xb5, 0x65, 0x33,
	0x09, 0xbd, 0x65, 0x65, 0x7d, 0xad, 0xd4, 0x96, 0x6f, 0x97, 0x37, 0xc2, 0xb3, 0x69, 0xde, 0x8c,
	0xb3, 0x8d, 0x40, 0x2b, 0xad, 0xea, 0xae, 0xee, 0x17, 0x08, 0xef, 0x3f, 0x75, 0x6b, 0x4f, 0xd4,
	0x43, 0xf7, 0x75, 0xb4, 0xe9, 0x5c, 0x64, 0x3e, 0xc7, 0xd9, 0x28, 0x72, 0x88, 0xbb, 0x86, 0x06,
	0xcd, 0x4e, 0xbb, 0x51, 0xb2, 0x40, 0xa9, 0x5f, 0x90, 0x90, 0xc7, 0x00, 0xca, 0xcf, 0x3e, 0x8d,
	0x47, 0x4c, 0x98, 0xcc, 0xb9, 0xa5, 0x18, 0x4e, 0xec, 0xcc, 0xf1, 0x2d, 0xa2, 0x65, 0x05, 0xd0,
	0xb9, 0x7c, 0x01, 0xfc, 0x72, 0xe1, 0x31, 0xcb, 0x72, 0x1a, 0x25, 0xde, 0x40, 0x89, 0x17, 0x55,
	0xc3, 0x79, 0x22, 0xc8, 0x27, 0x70, 0xb7, 0xb2, 0x04, 0x50, 0x7e, 0x77, 0x85, 0x3a, 0x59, 0xce,
	0xee, 0x7d, 0x0a, 0x5b, 0xf3, 0xd3, 0xae, 0x09, 0x2f, 0xb1, 0xc2, 0xeb, 0x5e, 0x71, 0xee, 0xf8,
	0x4c, 0x2f, 0x22, 0xe5, 0x19, 0xf1, 0xfa, 0xe4, 0x93, 0x77, 0xa1, 0x95, 0x4d, 0x62, 0xc2, 0xbc,
	0xc1, 0x1d, 0x8c, 0x7b, 0x66, 0x4c, 0x71, 0xec, 0xfd, 0xc1, 0x81, 0xdd, 0x25, 0x53, 0xeb, 0x97,
	0xa4, 0xd1, 0xbf, 0x1d, 0x68, 0x66, 0xf8, 0x0b, 0xc7, 0xcc, 0xeb, 0x5d, 0x2d, 0xee, 0x42, 0x33,
	0x14, 0x03, 0x21, 0x79, 0x9a, 0xad, 0x16, 0x8d, 0x50, 0x9c, 0x2a, 0x50, 0x29, 0x32, 0xc4, 0x8c,
	0x08, 0xd4, 0xde, 0xa1, 0x17, 0x0b, 0x0b, 0xa3, 0xa7, 0x72, 0x75, 0x64, 0x3e, 0x2c, 0x20, 0x40,
	0x36, 0xa0, 0x1e, 0x4f, 0x27, 0xe6, 0x79, 0x54, 0x3f, 0xbd, 0xbf, 0x39, 0xd0, 0x5b, 0x9c, 0xa9,
	0xaf, 0xd1, 0xd5, 0x0f, 0xa1, 0x9b, 0x99, 0xf5, 0x3a, 0x45, 0x69, 0x7a, 0x23, 0x98, 0xc3, 0x96,
	0xd6, 0x28, 0xb7, 0xbc, 0x46, 0x79, 0x5f, 0xd4, 0x60, 0x5f, 0x25, 0x68, 0x56, 0x1f, 0x3f, 0x4d,
	0x58, 0x4a, 0x25, 0xfb, 0x80, 0x8f, 0x2e, 0xb9, 0xcf, 0xdc, 0xc1, 0x69, 0x6e, 0x90, 0x7f, 0x97,
	0x74, 0x71, 0x9a, 0x23, 0x5f, 0x85, 0x6e, 0x5e, 0x7b, 0x03, 0x8c, 0x9b, 0x36, 0xe1, 0x46, 0x8e,
	0xc5, 0x4f, 0x51, 0xfb, 0xd0, 0xe1, 0xfa, 0xe6, 0x81, 0x9c, 0x25, 0x59, 0x48, 0xda, 0x06, 0xf7,
	0x7c, 0x96, 0x30, 0xb2, 0x0b, 0x20, 0x24, 0x4d, 0xe5, 0x00, 0x77, 0x1f, 0x1d, 0x96, 0x16, 0x62,
	0x54, 0x4d, 0xab, 0x80, 0xb2, 0x38, 0xd0, 0x87, 0x7a, 0xd9, 0x6b, 0xb0, 0x38, 0xc0, 0xa3, 0x5d,
	0x00, 0x5c, 0x04, 0xc3, 0xea, 0x8f, 0x3e, 0xd9, 0x9e, 0x28, 0xaa, 0xbe, 0xfa, 0xec, 0x43, 0xa7,
	0xd0, 0x3f, 0xcc, 0x3e, 0xfc, 0x94, 0x9a, 0x75, 0xc9, 0x44, 0xd4, 0x5e, 0x4f, 0xaa, 0x85, 0x89,
	0x4a, 0x7f, 0xef, 0xcf, 0x0e, 0x78, 0xe7, 0x79, 0xfa, 0x4a, 0x89, 0xf1, 0xf5, 0xd2, 0x2b, 0xb7,
	0x5d, 0x6a, 0xf9, 0xc5, 0x05, 0xe6, 0xa5, 0xdb, 0x04, 0x17, 0x67, 0x29, 0xe3, 0x5d, 0x0d, 0x78,
	0xff, 0xab, 0xc3, 0xed, 0x0a, 0x9e, 0x85, 0x90, 0x38, 0x8b, 0x21, 0x59, 0x0c, 0x6e, 0x6d, 0x49,
	0x70, 0x4b, 0x3e, 0xac, 0xaf, 0xe2, 0xc3, 0xb5, 0x0a, 0x1f, 0x96, 0xc9, 0xc2, 0xf8, 0x15, 0x37,
	0x79, 0x50, 0x90, 0xf5, 0xe3, 0x57, 0x5c, 0x45, 0x54, 0x8c, 0x79, 0xa2, 0x55, 0x32, 0x9b, 0xbf,
	0x42, 0x64, 0xda, 0x94, 0x72, 0xb9, 0x71, 0x5e, 0x2e, 0x37, 0xed, 0x5c, 0x3e, 0x80, 0x0d, 0xdd,
	0x06, 0x06, 0xf8, 0xf9, 0x0a, 0xa5, 0xb7, 0x74, 0xc5, 0x69, 0xfc, 0x0b, 0xf3, 0x01, 0x9d, 0x7c,
	0x05, 0xba, 0x36, 0x65, 0xa8, 0x3f, 0x08, 0xb6, 0xfc, 0x4e, 0x41, 0xd7, 0x0f, 0xc8, 0x43, 0xb8,
	0x99, 0x79, 0x38, 0x23, 0x6b, 0x6b, 0x73, 0x0c, 0xda, 0xd0, 0xbd, 0x0b, 0xb7, 0x4a, 0x74, 0x78,
	0x71, 0x47, 0xf7, 0x35, 0x8b, 0x12, 0x6f, 0x7e, 0x07, 0xda, 0xe6, 0x66, 0xac, 0x84, 0x1b, 0x76,
	0xf7, 0xc2, 0x62, 0xd0, 0xff, 0x1d, 0x74, 0xf3, 0xbf, 0x37, 0x3e, 0x81, 0x77, 0xb0, 0xed, 0x55,
	0xa5, 0x8d, 0xa9, 0xfe, 0xef, 0x40, 0x33, 0xe2, 0xa3, 0x41, 0xa4, 0x5e, 0x5b, 0xa7, 0x78, 0x6d,
	0x97, 0xb2, 0x35, 0x22, 0x9d, 0xd3, 0xde, 0x3f, 0xea, 0xd0, 0x5b, 0x46, 0x55, 0x65, 0xbd, 0xb3,
	0xb2, 0xf5, 0xb5, 0x6a, 0xeb, 0xe7, 0x73, 0xb6, 0xbe, 0x4a, 0xce, 0xae, 0xad, 0x92, 0xb3, 0xee,
	0x2a, 0x39, 0xbb, 0x5e, 0x95, 0xb3, 0x5b, 0xb0, 0x9e, 0xd0, 0x94, 0x4e, 0x44, 0xf6, 0xb1, 0x53,
	0x43, 0xe5, 0x24, 0x6d, 0x5e, 0x90, 0xa4, 0xad, 0xf3, 0x92, 0x14, 0x2e, 0x4a, 0xd2, 0xf6, 0x8a,
	0x49, 0xda, 0x59, 0x4c, 0xd2, 0x97, 0xeb, 0xf8, 0x47, 0xdb, 0xb7, 0xfe, 0x1f, 0x00, 0x00, 0xff,
	0xff, 0x4e, 0x3c, 0x90, 0xaa, 0x92, 0x1b, 0x00, 0x00,
}

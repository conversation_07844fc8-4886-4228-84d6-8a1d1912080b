package models

type UpetSeller struct {
	SellerId        int    `xorm:"not null pk autoincr comment('卖家编号') INT(10)"`
	SellerName      string `xorm:"not null comment('卖家用户名') VARCHAR(50)"`
	MemberId        int    `xorm:"not null comment('用户编号') INT(10)"`
	SellerGroupId   int    `xorm:"not null comment('卖家组编号') INT(10)"`
	ChainBrandId    int    `xorm:"not null default 0 comment('对应chain_brand表id') INT(10)"`
	StoreId         int    `xorm:"not null comment('店铺编号') INT(10)"`
	IsAdmin         int    `xorm:"not null comment('是否管理员(0-不是 1-是)') TINYINT(3)"`
	SellerQuicklink string `xorm:"comment('卖家快捷操作') VARCHAR(255)"`
	LastLoginTime   int    `xorm:"comment('最后登录时间') INT(10)"`
	IsClient        int    `xorm:"not null default 0 comment('是否客户端用户 0-否 1-是') TINYINT(3)"`
	RegionId        int    `xorm:"default 0 comment('大区标识') INT(4)"`
}

package tasks

import (
	"encoding/json"
	"fmt"
	"testing"
)

func Test_pickupAutoUpdateStatus(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pickupAutoUpdateStatus()
		})
	}
}

type VInOutBound struct {
	Data []struct {
		BoundNo        string `json:"bound_no"`
		BoundType      string `json:"bound_type"`
		ChainId        string `json:"chain_id"`
		CreatedBy      string `json:"created_by"`
		CreatedTime    string `json:"created_time"`
		Id             string `json:"id"`
		IsDeleted      string `json:"is_deleted"`
		ItemRefId      string `json:"item_ref_id"`
		ItemRefNo      string `json:"item_ref_no"`
		ItemRefType    string `json:"item_ref_type"`
		ItemType       string `json:"item_type"`
		OccurrenceTime string `json:"occurrence_time"`
		Operator       string `json:"operator"`
		Remark         string `json:"remark"`
		SellAmount     string `json:"sell_amount"`
		TenantId       string `json:"tenant_id"`
		TotalAmount    string `json:"total_amount"`
		TotalNum       string `json:"total_num"`
		UpdatedBy      string `json:"updated_by"`
		UpdatedTime    string `json:"updated_time"`
	} `json:"data"`
}

func Test_pickupCheckLock(t *testing.T) {
	tests := []struct {
		name    string
		want    bool
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			str := `{
  "id": 0,
  "database": "eshop_saas",
  "table": "v_in_out_bound",
  "pkNames": [
    "id"
  ],
  "isDdl": false,
  "type": "INITINSERT",
  "es": 0,
  "ts": 1724296255479,
  "sql": "",
  "sqlType": null,
  "mysqlType": {
    "bound_no": "varchar",
    "bound_type": "varchar",
    "chain_id": "bigint",
    "created_by": "bigint",
    "created_time": "datetime",
    "id": "bigint",
    "is_deleted": "bit",
    "item_ref_id": "bigint",
    "item_ref_no": "varchar",
    "item_ref_type": "varchar",
    "item_type": "varchar",
    "occurrence_time": "datetime",
    "operator": "varchar",
    "remark": "varchar",
    "sell_amount": "decimal",
    "tenant_id": "bigint",
    "total_amount": "decimal",
    "total_num": "int",
    "updated_by": "bigint",
    "updated_time": "datetime"
  },
  "data": [
    {
      "bound_no": "RK2024082019374080979546",
      "bound_type": "INBOUND",
      "chain_id": "530185752454150978",
      "created_by": "530209928825068544",
      "created_time": "2024-08-20 19:37:54",
      "id": "532810621717337088",
      "is_deleted": "0",
      "item_ref_id": "0",
      "item_ref_no": "",
      "item_ref_type": "NONE",
      "item_type": "OTHER_IN",
      "occurrence_time": "2024-08-20 00:00:00",
      "operator": "老板",
      "remark": "",
      "sell_amount": "0.0000",
      "tenant_id": "530219708465609002",
      "total_amount": "1.0000",
      "total_num": "1",
      "updated_by": "530209928825068544",
      "updated_time": "2024-08-20 19:37:54"
    }
  ],
  "old": null,
  "gtid": ""
}`
			var mode = VInOutBound{}
			err := json.Unmarshal([]byte(str), &mode)
			fmt.Println(err.Error())
			got, err := pickupCheckLock()
			t.Log(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("pickupCheckLock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

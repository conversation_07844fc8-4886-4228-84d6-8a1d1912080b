package models

import (
	"time"
)

type OrderDetail struct {
	OrderSn                 string    `xorm:"not null pk comment('订单编号') VARCHAR(50)"`
	ChildChannelId          string    `xorm:"not null default '''' comment('子渠道id，命名规则为channel_id+子渠道编码') VARCHAR(10)"`
	ChildChannelName        string    `xorm:"not null comment('子渠道名称') VARCHAR(20)"`
	PerformanceStaffName    string    `xorm:"not null default '''' comment('业绩所属员工姓名') VARCHAR(30)"`
	PerformanceOperatorName string    `xorm:"not null default '''' comment('业绩操作人姓名') VARCHAR(30)"`
	PerformanceOperatorTime time.Time `xorm:"default 'NULL' comment('业绩操作时间') DATETIME"`
	NoticeTime              int64     `xorm:"not null default 0 comment('订单拣货提醒通知时间') INT(11)"`
	Invoice                 string    `xorm:"not null default '''' comment('发票信息') VARCHAR(255)"`
	BuyerMemo               string    `xorm:"not null default '''' comment('买家留言') VARCHAR(255)"`
	SellerMemo              string    `xorm:"not null default '''' comment('卖家留言') VARCHAR(255)"`
	GyDeliverStatus         int32     `xorm:"not null default 0 comment('管易发货状态,0未发货,1已发货,2部分发货') TINYINT(4)"`
	DeliveryRemark          string    `xorm:"not null default '''' comment('配送备注') VARCHAR(255)"`
	PushDelivery            int32     `xorm:"not null default 0 comment('是否推送美团配送,1是0否') TINYINT(4)"`
	PushDeliveryReason      string    `xorm:"not null default '''' comment('推送美团配送失败原因') VARCHAR(255)"`
	PushThirdOrder          int32     `xorm:"not null default 0 comment('是否推送子龙或全渠道,1是0否') TINYINT(4)"`
	PushThirdOrderReason    string    `xorm:"not null default '''' comment('推送子龙或全渠道失败原因') TEXT"`
	SplitOrderResult        int32     `xorm:"not null default 0 comment('拆分订单结果,0拆单中1成功2失败') TINYINT(4)"`
	SplitOrderFailReason    string    `xorm:"not null default '''' comment('拆分订单失败原因') VARCHAR(255)"`
	AcceptUsername          string    `xorm:"not null default '''' comment('接单人') VARCHAR(50)"`
	AcceptTime              time.Time `xorm:"default 'NULL' comment('美团接单时间') DATETIME"`
	IsPicking               int32     `xorm:"not null default 0 comment('是否拣货,0否1是') TINYINT(4)"`
	PickingTime             time.Time `xorm:"default 'NULL' comment('美团拣货时间') DATETIME"`
	ExpectedTime            time.Time `xorm:"default 'NULL' comment('预计送达时间') DATETIME"`
	LockedStock             int32     `xorm:"not null default 0 comment('是否锁定库存,0否1是') TINYINT(4)"`
	Extras                  string    `xorm:"not null default '''' comment('美团附加优惠信息json') VARCHAR(3000)"`
	RemindTime              string    `xorm:"not null default '''' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255)"`
	Latitude                float64   `xorm:"not null default 0.000000 comment('收货地址纬度') DOUBLE(10,6)"`
	Longitude               float64   `xorm:"not null default 0.000000 comment('收货地址经度') DOUBLE(10,6)"`
	PickupCode              string    `xorm:"not null default '''' comment('取货码') VARCHAR(30)"`
	PayType                 string    `xorm:"not null default '''' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20)"`
	IsAdjust                int32     `xorm:"not null default 0 comment('是否订单调整，1是0否') TINYINT(4)"`
	PowerId                 int32     `xorm:"not null default 0 comment('电商助力id') INT(11)"`
	CreateTime              time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime              time.Time `xorm:" default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
	DelGjpStatus            string    `xorm:"not null default '''' comment('管家婆状态NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货') VARCHAR(20)"`
	GoodsReturnDeliveryId   int64     `xorm:"not null default 0 comment('需要确认商品返回的配送单号（有需要确认的才会有值，没有则不需要）') BIGINT(32)"`
	PickupStationId         int32     `xorm:"default 0 comment('社区团购自提点id') INT(11)"`
	ConsultOrderSn          string    `xorm:"not null default '''' comment('医疗互联网订单号') VARCHAR(80)"`
	ShopDisMemberId         string    `xorm:"default null comment('店铺海报分销员id') VARCHAR(32)"`
	ShopDisChainId          int32     `xorm:"default 0 comment('店铺海报分销员业绩所属门店id') INT(11)"`
	BillCompletedTime       time.Time `xorm:"default 'NULL' comment('账单订单完成时间') DATETIME"`
	BillCanceledTime        time.Time `xorm:"default 'NULL' comment('账单订单取消时间') DATETIME"`
	TradeCreatedTime        time.Time `xorm:"default 'NULL' comment('账户时间') DATETIME"`
	TradePaymentTime        time.Time `xorm:"default 'NULL' comment('入账时间') DATETIME"`
	TradeTime               time.Time `xorm:"default 'NULL' comment('账单日期') DATETIME"`
}

func (om *OrderDetail) TableName() string {
	return "dc_order.order_detail"
}

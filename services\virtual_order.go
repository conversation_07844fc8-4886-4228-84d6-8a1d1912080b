package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"net/http"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/cc"
	"order-center/proto/et"
	"order-center/proto/oc"
	"order-center/utils"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//获取核销订单详情接口
func (o OrderService) GetVirtualOrderDetail(ctx context.Context, request *oc.GetVirtualOrderDetailRequest) (*oc.VirtualOrderResponse, error) {
	out := &oc.VirtualOrderResponse{
		Code: 200,
	}

	//todo 核销码查询到的订单号目前使用的是核销码表中的主键id，后期需改成订单号
	//1、查询虚拟待核销的虚拟订单信息
	sql := `SELECT 
			a.order_status orderstate,
			a.member_id memberid,
			a.order_sn orderid, 
			a.create_time createtime,
			a.pay_time pay_time,
			if(a.en_member_tel != '',en_member_tel,member_tel) membermobile,
			a.update_time lasttime,
			a.total ordermoney,
			a.pay_mode payway,
			a.user_agent useragent,
			a.channel_id,
			b.sku_id sku,
			b.pay_price sellprice,
			b.payment_total,
			b.number,
			b.marking_price,
			b.third_sku_id goodsid,
			b.image goodsimage,
			b.image siglegoodsimage,
			b.product_name 'name',
			c.verify_status chargeoff,
			c.verify_code chargeoffcode,
			c.verify_code_expiry_date expiredate,
			c.verify_shop chargeoffhospitalid,
			c.verify_time chargeofftime,
			c.verify_member_id chargeoffmemberid
			FROM dc_order.order_main a
			INNER JOIN dc_order.order_product b ON a.order_sn = b.order_sn
			INNER JOIN dc_order.order_verify_code c ON a.order_sn = c.order_sn
			WHERE c.verify_code = ?;`
	var oldOrderDetail dto.OldOrderDetail
	glog.Info("zx虚实核销 "+sql, request.WrittenOffCode)
	_, err := GetDBConn().SQL(sql, request.WrittenOffCode).Get(&oldOrderDetail)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return out, nil
	}

	oldOrderDetail.Membermobile = utils.MobileDecrypt(oldOrderDetail.Membermobile)

	//查询该订单下未核销的是否存在
	var notVerifiedCount int64
	checkSql := "SELECT count(1) FROM dc_order.order_verify_code WHERE order_sn = ? AND verify_status = 0 AND verify_code !=?"
	_, err = GetDBConn().SQL(checkSql, oldOrderDetail.Orderid, request.WrittenOffCode).Get(&notVerifiedCount)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return out, nil
	}

	//没有了未核销的  说明改核销码是最后一个核销的
	//Sellprice = 虚拟单总支付金额 - 支付单价 * （总数量-1）
	if notVerifiedCount == 0 {
		oldOrderDetail.Sellprice = oldOrderDetail.PaymentTotal - (oldOrderDetail.Number-1)*oldOrderDetail.Sellprice
	}
	//转换电商订单号 -- todo 后续删除该模块逻辑
	oldSql := "SELECT a.old_order_sn FROM dc_order.order_main a INNER JOIN dc_order.order_main b ON a.order_sn = b.parent_order_sn WHERE b.order_sn = ?;"
	oldOrderSnResult, _ := GetDBConn().Query(oldSql, oldOrderDetail.Orderid)
	if len(oldOrderSnResult) != 1 {
		out.Code = 400
		out.Message = fmt.Sprintf("核销码数据无效，参数：%s", request.WrittenOffCode)
		return out, nil
	}

	// 判断是否在退款状态
	sql = `SELECT 
	a.refund_state,b.verify_codes
	FROM dc_order.refund_order a 
	INNER JOIN dc_order.refund_order_product b ON a.refund_sn = b.refund_sn
	WHERE a.order_sn = ? and b.verify_codes like ? AND a.refund_state NOT IN(2,8,9);`
	if has, err := GetDBConn().SQL(sql, oldOrderDetail.Orderid, "%"+request.WrittenOffCode+"%").Exist(); has || err != nil {
		out.Code = 400
		if err != nil {
			out.Message = "查询退款状态出错 " + err.Error()
		} else {
			out.Message = "核销码已退款或退款中，不能核销"
		}
		return out, nil
	}

	oldOrderSnByte := oldOrderSnResult[0]["old_order_sn"]
	oldOrderDetail.Orderid = string(oldOrderSnByte)
	outOldOrderDetail, _ := json.Marshal(oldOrderDetail)
	out.OldOrderDetail = string(outOldOrderDetail)
	return out, nil
}

//核销订单
func (o OrderService) WrittenOffVirtualOrder(ctx context.Context, request *oc.WrittenOffVirtualOrderRequest) (*oc.VirtualOrderResponse, error) {
	glog.Infof("虚拟订单核销记录参数：%v", request)
	out := &oc.VirtualOrderResponse{
		Code: 200,
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	//todo 核销码查询到的订单号目前使用的是核销码表中的主键id，后期需改成订单号
	//1、查询虚拟待核销的虚拟订单信息
	sql := `SELECT  
			a.order_status orderstate,
			a.member_id memberid,
			a.member_tel membermobile,
			b.sku_id sku,
			b.pay_price sellprice,
			a.order_sn orderid,
			a.create_time createtime,
			a.pay_time pay_time,
			a.update_time lasttime,
			a.pay_amount ordermoney,
			a.total payway,
			a.user_agent useragent,
			a.channel_id,
			b.third_sku_id goodsid,
			b.image goodsimage,
			b.image siglegoodsimage,
			b.product_name 'name',
			c.verify_status chargeoff,
			c.verify_code chargeoffcode,
			c.verify_code_expiry_date expiredate,
			c.verify_shop chargeoffhospitalid,
			c.verify_time chargeofftime,
			c.verify_member_id chargeoffmemberid,
			d.order_status_child  parent_order_child_state,
			d.old_order_sn  old_order_sn
			FROM dc_order.order_main a
			INNER JOIN dc_order.order_product b ON a.order_sn = b.order_sn
			INNER JOIN dc_order.order_verify_code c ON a.order_sn = c.order_sn
			LEFT JOIN dc_order.order_main d ON (a.parent_order_sn!="" and  a.parent_order_sn = d.order_sn)
			WHERE c.verify_code = ?;`
	var oldOrderDetail dto.OldOrderDetail
	_, err := o.session.SQL(sql, request.VerifyCode).Get(&oldOrderDetail)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return out, nil
	}
	if oldOrderDetail.Orderid == "" {
		out.Code = 400
		out.Message = "订单不存在，或者已被核销"
		return out, nil
	}
	// 如果虚拟订单的主单的子状态为未接单，报错
	if oldOrderDetail.ParentOrderChildstate == 20101 {
		out.Code = 400
		out.Message = "订单未接单，无法核销"
		return out, nil
	}

	// 判断是否在退款状态
	sql = `SELECT 
	a.refund_state,b.verify_codes
	FROM dc_order.refund_order a 
	INNER JOIN dc_order.refund_order_product b ON a.refund_sn = b.refund_sn
	WHERE a.order_sn = ? and b.verify_codes like ? AND a.refund_state NOT IN(2,8,9);`
	if has, err := o.session.SQL(sql, oldOrderDetail.Orderid, "%"+request.VerifyCode+"%").Exist(); has || err != nil {
		out.Code = 400
		if err != nil {
			out.Message = "查询退款状态出错 " + err.Error()
		} else {
			out.Message = "核销码已退款或退款中，不能核销"
		}
		return out, nil
	}

	//虚拟订单的订单状态必须是30(已完成状态才能核销)，否则无法核销
	if oldOrderDetail.Orderstate != 30 {
		out.Code = 400
		out.Message = "订单状态不符，不能核销"
		return out, nil
	}
	if oldOrderDetail.Chargeoff != 0 {
		out.Code = 400
		out.Message = "核销状态不符，不能核销"
		return out, nil
	}
	format := "2006-01-02 15:04:05"
	now := time.Now().In(time.Local)
	expireDate, err := time.ParseInLocation(format, oldOrderDetail.Expiredate, time.Local)
	if err != nil {
		out.Code = 400
		out.Message = "核销码有效期错误"
		return out, nil
	}
	if now.After(expireDate) {
		out.Code = 400
		out.Message = "核销码已过期"
		return out, nil
	}

	o.session.Begin()
	upSql := "UPDATE dc_order.order_verify_code SET verify_status = 1,verify_member_id=?, verify_time = ?, verify_shop = ? WHERE verify_status = 0 AND verify_code = ? ;"
	sqlResult, err := o.session.Exec(upSql, request.MemberId, now.Format(format), request.StoreId, request.VerifyCode)
	if err != nil {
		o.session.Rollback()
		out.Code = 400
		out.Message = "核销失败，失败原因：" + err.Error()
		return out, nil
	}
	rowsAffected, err := sqlResult.RowsAffected()
	if err != nil {
		o.session.Rollback()
		out.Code = 400
		out.Message = "核销失败，失败原因：" + err.Error()
		return out, nil
	}
	if rowsAffected == 0 {
		o.session.Rollback()
		out.Code = 400
		out.Message = "数据库更新失败，无数据可更新"
		return out, nil
	}

	//更新虚拟订单状态
	count, err := o.session.Where("order_sn=? and verify_status=0", oldOrderDetail.Orderid).Count(new(models.OrderVerifyCode))
	if err != nil {
		o.session.Rollback()
		glog.Error(oldOrderDetail.Orderid, ", 更新虚拟订单状态失败, ", err)
		out.Code = 400
		out.Message = "核销失败，" + err.Error()
		return out, nil
	}
	//根据还剩余的未核销的核销码数量判断虚拟订单状态 更新子订单的状态  而非主订单
	writtenOffSql := "UPDATE dc_order.order_main SET order_status_child = ? WHERE order_sn = ?;"
	if count > 0 {
		o.session.Exec(writtenOffSql, 30102, oldOrderDetail.Orderid)
	} else {
		o.session.Exec(writtenOffSql, 30103, oldOrderDetail.Orderid)
	}

	err = o.session.Commit()
	if err != nil {
		o.session.Rollback()
		glog.Error(oldOrderDetail.Orderid, ", 更新虚拟订单状态失败(order_main), ", err)
		out.Code = 400
		out.Message = "核销失败，" + err.Error()
		return nil, err
	}

	go NotifyWrittenOffStatus(oldOrderDetail, request.StoreId, request.VerifyCode, kit.GetTimeNow(now))

	// 走勃林格核销流程
	go BlgVerify(oldOrderDetail, now.Format(format), request.StoreId)

	return out, nil
}

// BlgVerify 执行勃林格核销
func BlgVerify(detail dto.OldOrderDetail, verificationTime string, storeId string) error {
	// 检查sku_id，是否在配置范围内
	BlgSkuIds := config.GetString("blg_sku_ids") // （测试）blg_sku_ids: 1049819001 （生产）blg_sku_ids: 1106931001,1106931002,1106931003
	sku := detail.Sku
	if !strings.Contains(BlgSkuIds, sku) {
		return nil
	}

	// 填充参数
	glog.Info("请求勃林格核销行为数据接口，参数拼接detail=" + kit.JsonEncode(detail))

	// 获取到数据拼装参数
	dcDb := GetDcDBConn()
	//defer dcDb.Close()

	var store string
	_, err := dcDb.SQL("SELECT name FROM hospital_info WHERE hospitalid=?", storeId).Get(&store)
	if err != nil {
		glog.Error("BlgVerify查询医院信息数据异常：e=" + err.Error())
		return err
	}

	req := &et.BlgVerifyReq{
		OrderNo:          detail.OldOrderSn,
		Store:            store,
		VerificationTime: verificationTime,
		SkuNo:            detail.Sku,
		SkuName:          detail.Name,
		UserId:           detail.Memberid,
	}

	// 调用external，请求勃林格核销接口
	client := et.GetExternalClient()
	defer client.Close()
	glog.Info("请求勃林格核销行为数据接口，参数req=" + kit.JsonEncode(req))
	_, err = client.Blg.Verification(client.Ctx, req)
	if err != nil {
		glog.Error("请求勃林格核销接口异常，e=" + err.Error())
		return err
	}

	return nil
}

// GetVerifyCodes 通过第三方订单号查询
//查询条件包括
func (o OrderService) GetVerifyCodes(ctx context.Context, request *oc.GetVerifyCodesRequest) (*oc.GetVerifyCodesResponse, error) {
	glog.Info("查询核销码：", request)
	//查询拼团商品列表
	out := new(oc.GetVerifyCodesResponse)
	out.Code = http.StatusBadRequest

	var (
		queryResult []*oc.VerifyCodesInfo //查询结果集
		err         error
	)
	//todo 列表中需要增加是否有商品挂在下面的字段
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()
	countSql := `SELECT
			count(a.id) AS total
			FROM order_verify_code a 
			INNER JOIN order_main b ON a.order_sn = b.order_sn 
			INNER JOIN dc_order.order_product c ON b.order_sn = c.order_sn
			WHERE 1`
	sql := `SELECT
			a.order_sn,
			a.verify_code,
			a.verify_code_expiry_date,
			a.verify_status,
			a.verify_shop,
			a.verify_time,
			a.verify_member_id,
			b.order_status,
			b.member_id,
			b.create_time, 
			b.pay_time,
			b.total,
			b.pay_mode,
			c.sku_id,
			c.pay_price,
			c.payment_total,
			c.marking_price,
			c.third_sku_id,
			c.image,
			c.product_name,
			c.number 
			FROM order_verify_code a 
			INNER JOIN order_main b ON a.order_sn = b.order_sn 
			INNER JOIN dc_order.order_product c ON b.order_sn = c.order_sn
			WHERE 1`
	var whereBuffer bytes.Buffer
	if request.OldOrderSn != "" {
		//需要查询出子订单号
		var parentOrderSn string
		has, err := session.Table("order_main").
			Select("order_sn").
			Where("old_order_sn=?", request.OldOrderSn).
			Get(&parentOrderSn)
		if err != nil {
			glog.Error(request.OldOrderSn, "查询主订单号出错", err)
			return out, err
		}

		if has == false {
			out.Code = http.StatusOK
			return out, nil
		}
		whereBuffer.WriteString(" AND b.parent_order_sn='" + parentOrderSn + "'")
	}
	//如果有渠道订单号(子订单)
	if request.OrderSn != "" {
		whereBuffer.WriteString(" AND b.order_sn='" + request.OrderSn + "'")
	}

	if request.MemberId != "" {
		whereBuffer.WriteString(" AND b.member_id='" + request.MemberId + "'")
	}
	if request.ProductName != "" {
		whereBuffer.WriteString(" AND c.product_name LIKE '%" + request.ProductName + "%'")
	}

	if request.VerifyStatus != -1 {
		whereBuffer.WriteString(" AND a.verify_status=" + cast.ToString(request.VerifyStatus))
	}

	whereStr := whereBuffer.String()
	strSql := sql + whereStr
	countStrSql := countSql + whereStr

	_, err = session.SQL(countStrSql).Get(&out.Total)
	if err != nil {
		glog.Error("查询核销码条数出错", err)
		return out, err
	}

	limit := int(request.PageSize)
	if limit == 0 {
		limit = 15
	}
	if request.PageIndex == 0 {
		request.PageIndex = 1
	}
	start := int((request.PageIndex - 1) * request.PageSize)
	err = session.SQL(strSql+" LIMIT ? OFFSET ? ", limit, start).Find(&queryResult)
	if err != nil {
		glog.Error("查询核销码数据出错", err)
		return out, err
	}
	out.Code = http.StatusOK
	out.Data = queryResult
	return out, nil
}

// GetVerifyCodes 通过第三方订单号查询
//查询条件包括
func GetVerifyCodes(orderSn string, isVirtual int32) (queryResult []*models.OrderVerifyCode, err error) {
	//查询拼团商品列表
	if isVirtual == 0 {
		return
	}
	//todo 列表中需要增加是否有商品挂在下面的字段
	db := GetDBConn()
	session := db.NewSession()
	defer session.Close()

	sql := `SELECT
			verify_code,
			verify_code_expiry_date,
			verify_status,
			verify_shop,
			verify_time
			FROM order_verify_code 
			WHERE order_sn = ?`

	err = session.SQL(sql, orderSn).Find(&queryResult)
	if err != nil {
		glog.Error(orderSn, "查询订单核销码数据出错", err)
		return
	}
	return
}

func NotifyWrittenOffStatus(oldOrderDetail dto.OldOrderDetail, verifyShop, verifyCode, verifyTime string) (bool, error) {
	db := GetDBConn()

	// 处理核销通知
	go func() {
		var storeName, oldOrderSn string
		productName := oldOrderDetail.Name
		if has, err := db.Table("datacenter.store").Where("zilong_id = ?", verifyShop).Select("name").Get(&storeName); err != nil {
			glog.Error("NotifyWrittenOffStatus " + err.Error())
		} else if !has {
			glog.Error("NotifyWrittenOffStatus 门店未找到 " + verifyShop)
		}

		if has, err := db.Table("order_main").Alias("om").
			Join("inner", "order_main p", "om.parent_order_sn = p.order_sn").Where("om.order_sn = ?", oldOrderDetail.Orderid).
			Select("p.old_order_sn").Get(&oldOrderSn); err != nil {
			glog.Error("NotifyWrittenOffStatus " + err.Error())
		} else if !has {
			glog.Error("NotifyWrittenOffStatus 渠道订单号未找到 " + oldOrderDetail.Orderid)
		}

		if len([]rune(storeName)) > 20 {
			storeName = string([]rune(storeName)[:20])
		}
		if len([]rune(productName)) > 20 {
			productName = string([]rune(productName)[:20])
		}

		client := cc.GetCustomerCenterLongClient()
		_, _ = client.RPC.SendSubscribeMessage(client.Ctx, &cc.SendSubscribeMessageReq{
			ScrmUserId:  oldOrderDetail.Memberid,
			TemplateKey: "vr-code-use",
			Values: []*cc.MessageValue{
				{
					Type:  "string",
					Value: storeName,
				}, {
					Type:  "string",
					Value: verifyTime,
				}, {
					Type:  "string",
					Value: oldOrderSn,
				}, {
					Type:  "string",
					Value: productName,
				}, {
					Type:  "string",
					Value: "若有疑问，可电话反馈4000208888",
				},
			},
			Page: "app/mall/page/orderDetailVr?order_id=" + oldOrderSn,
		})
	}()

	//调用商城接口改成发布商城订单核销状态反馈信息
	var modelJson = fmt.Sprintf("{\"vr_usertime\":\"%s\",\"vr_code\":\"%s\",\"erp_order_id\":\"%s\",\"erp_chargeoffhospitalid\":\"%s\"}", verifyTime, verifyCode, oldOrderDetail.Orderid, verifyShop)
	glog.Info("核销通知电商参数：", modelJson)
	var sqlMq = fmt.Sprintf("INSERT INTO datacenter.sync_mq_info(id,queue,request,type,exchange,route_key) VALUES ('%s','%s','%s',3,'%s','%s');", kit.GetGuid32(), "dc-sz-chargeoff-to-mall", modelJson, "datacenter", "dc-sz-chargeoff-to-mall")
	sqlResult, err := db.Exec(sqlMq)
	if err != nil {
		glog.Error("核销通知失败，失败原因：：", err)
		return false, errors.New("核销通知失败，失败原因：" + err.Error())
	}
	rowsAffected, err := sqlResult.RowsAffected()
	if err != nil {
		glog.Error("核销通知失败，失败原因：：", err)
		return false, errors.New("核销通知失败，失败原因：" + err.Error())
	}
	if rowsAffected == 0 {
		glog.Error("核销通知失败，失败原因：：", err)
		return false, errors.New("核销通知失败，失败原因：" + err.Error())
	}
	return true, nil
}

// 通过财务编码核销订单
func (o OrderService) WrittenOffByFinancialCode(ctx context.Context, request *oc.WrittenOffByFinancialCodeRequest) (*oc.BaseResponse, error) {
	//连接池勿关闭
	db := GetDBConn()
	type Store struct {
		ZilongId string `json:"zilong_id"`
	}

	var store Store
	has, err := db.SQL("select zilong_id from datacenter.store where finance_code = ?", request.FinancialCode).Get(&store)

	if err != nil {
		return &oc.BaseResponse{Code: 400, Message: err.Error()}, nil
	}
	if !has {
		return &oc.BaseResponse{Code: 400, Message: "财务编码无效"}, nil
	}

	out, err := o.WrittenOffVirtualOrder(ctx, &oc.WrittenOffVirtualOrderRequest{VerifyCode: request.VerifyCode, StoreId: store.ZilongId})

	if err != nil {
		return &oc.BaseResponse{Code: 400, Message: err.Error()}, nil
	}

	return &oc.BaseResponse{Code: out.Code, Message: out.Message}, nil
}

// 查询电商虚拟订单基本信息
func (o OrderService) QueryMallVirtualOrderExtendInfo(ctx context.Context, request *oc.QueryMallVirtualOrderExtendInfoRequest) (out *oc.QueryMallVirtualOrderExtendInfoResponse, e error) {
	out = &oc.QueryMallVirtualOrderExtendInfoResponse{Code: 400}
	if request.OrderSn == "" {
		out.Message = "订单号错误"
		return
	}

	upetDb := GetUPetDBConn()
	vrOrder := &models.UpetVrOrder{}
	if has, err := upetDb.Where("order_sn=?", request.OrderSn).
		Select("order_id,order_sn,payment_time,vr_indate,add_time,erp_order_sn").
		Get(vrOrder); err != nil {
		glog.Error("QueryMallVirtualOrder 查询电商虚拟订单失败:", err.Error(), " order_sn=", request.OrderSn)
		out.Message = "查询订单失败"
		return
	} else if !has {
		out.Message = "订单不存在"
		return
	}

	tm := time.Unix(cast.ToInt64(vrOrder.VrIndate), 0)
	pm := time.Unix(cast.ToInt64(vrOrder.PaymentTime), 0)
	am := time.Unix(vrOrder.AddTime, 0)

	out.Data = &oc.QueryMallVirtualOrderExtendInfoResponse_Data{
		OrderId:     cast.ToInt64(vrOrder.OrderId),
		OrderSn:     cast.ToString(vrOrder.OrderSn),
		VrIndate:    tm.Format(kit.DATE_LAYOUT),
		PaymentTime: pm.Format(kit.DATETIME_LAYOUT),
		AddTime:     am.Format(kit.DATETIME_LAYOUT),
		MaxVrIndate: am.AddDate(0, 0, 364).Format(kit.DATE_LAYOUT),
		ErpOrderSn:  cast.ToString(vrOrder.ErpOrderSn),
	}
	out.Code = 200
	return
}

// 延长电商虚拟订单的兑换时间（不能提前，只能延后）
func (o OrderService) ExtendMallVirtualOrderVerifyCodeExpiryDate(ctx context.Context, request *oc.ExtendMallVirtualOrderVerifyCodeExpiryDateRequest) (out *oc.BaseResponse, e error) {
	out = &oc.BaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Error("ExtendMallVirtualOrderVerifyCodeExpiryDate 更新虚拟订单有效期失败，in:", kit.JsonEncode(request), " out:", kit.JsonEncode(out))
		}
	}()

	if request.OrderSn == "" || request.Date == "" {
		out.Message = "参数错误"
		return
	}
	if len(request.Date) == 10 {
		request.Date = request.Date + " 23:59:59"
	}
	inVrIndate, err := time.ParseInLocation(kit.DATETIME_LAYOUT, request.Date, time.Local)
	if err != nil {
		out.Error = err.Error()
		out.Message = "时间错误"
		return
	}
	info, _ := o.QueryMallVirtualOrderExtendInfo(ctx, &oc.QueryMallVirtualOrderExtendInfoRequest{OrderSn: request.OrderSn})
	if info.Code != 200 {
		out.Message = info.Message
		return
	}
	maxData, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, info.Data.MaxVrIndate+" 23:59:59", time.Local)
	if inVrIndate.After(maxData) {
		out.Message = "兑换截止日期不能超过" + info.Data.MaxVrIndate
		return
	}
	vrIndate, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, info.Data.VrIndate+" 23:59:59", time.Local)
	if vrIndate.After(inVrIndate) {
		out.Message = "兑换截止日期不能小于当前核销截止日期"
		return
	}

	//修改电商的过期时间，兑换码的过期时间
	upetSession := GetUPetDBConn().NewSession()
	defer upetSession.Close()

	upetSession.Begin()
	vrOrder := &models.UpetVrOrder{
		VrIndate: cast.ToInt(inVrIndate.Unix()),
	}
	if _, err := upetSession.Where("order_sn=?", request.OrderSn).Cols("vr_indate").Update(vrOrder); err != nil {
		upetSession.Rollback()
		out.Error = err.Error()
		out.Message = "更新电商虚拟订单兑换截止日期失败"
		return
	}
	if _, err := upetSession.Where("order_id = ?", info.Data.OrderId).Cols("vr_indate").Update(models.UpetVrOrderCode{
		VrIndate: inVrIndate.Unix(),
	}); err != nil {
		upetSession.Rollback()
		out.Error = err.Error()
		out.Message = "更新电商虚拟订单兑换截止日期失败"
		return
	}
	//修改订单中心的过期时间
	if len(info.Data.ErpOrderSn) > 0 {
		dbSession := GetDBConn().NewSession()
		defer dbSession.Close()

		dbSession.Begin()
		if _, err := dbSession.Where("order_sn = ?", info.Data.ErpOrderSn).
			Cols("verify_code_expiry_date").
			Update(models.OrderVerifyCode{
				VerifyCodeExpiryDate: inVrIndate,
			}); err != nil {
			out.Error = err.Error()
			out.Message = "更新虚拟订单中心兑换截止日期失败"
			upetSession.Rollback()
			return
		}
		dbSession.Commit()
	}
	upetSession.Commit()

	//记录谁成功修改了记录
	glog.Info(request, "修改虚拟订单有效期，原过期时间", info.Data.VrIndate)
	out.Code = 200
	out.Message = "修改成功"
	return
}

// QueryMallVirtualOrderWriteOffCodes 根据子龙用户编码查询未过期的核销码信息
func (o OrderService) QueryMallVirtualOrderWriteOffCodes(ctx context.Context, in *oc.QueryMallVirtualOrderWriteOffCodesRequest) (*oc.QueryMallVirtualOrderWriteOffCodesResponse, error) {
	out := &oc.QueryMallVirtualOrderWriteOffCodesResponse{Code: 400}

	if in.ScrmUserId == "" {
		out.Message = "用户Id不能为空"
		return out, nil
	}

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 0
	}

	db := GetUPetDBConn()
	var memberId int32
	if has, err := db.Table("upet_member").Where("scrm_user_id = ?", in.ScrmUserId).Select("member_id").Get(&memberId); err != nil {
		out.Message = "查询用户信息失败"
		return out, err
	} else if !has {
		out.Message = "用户不存在"
		return out, nil
	}

	session := db.Table("upet_vr_order_code").Alias("voc").
		Join("inner", "dc_order.order_verify_code do", "do.verify_code = voc.vr_code").
		Join("left", "upet_vr_order vo", "voc.order_id = vo.order_id").
		Where("vo.buyer_id = ? AND vo.refund_state IN(0,1) AND vo.payment_time > 0 AND vo.order_state > 10 AND vo.order_type != 17", memberId).
		Where("do.verify_status = 0 and voc.vr_state = 0 AND voc.refund_lock = 0 AND voc.vr_indate > unix_timestamp()")
	if in.VrOrderSn != "" {
		session.And("vo.order_sn =" + in.VrOrderSn)
	}

	switch in.Type {
	case 1: // 过滤储值卡
		session.Where(`vo.goods_id in (select tg.tag_goods_id from upet_tags t inner join upet_tags_goods tg on tg.tag_id = t.tag_id where t.tag_name = '储值卡')`)
	}

	if count, err := session.Clone().Count(); err != nil {
		out.Message = "查询核销码失败"
		return out, err
	} else if count > 0 {
		out.Total = count
		if err = session.Select("vo.order_sn,vo.goods_id,vo.goods_name,vo.goods_price,vo.goods_num,vo.payment_time,voc.vr_code,voc.pay_price,voc.vr_indate").
			Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).OrderBy("voc.rec_id DESC").
			Find(&out.Data); err != nil {
			out.Message = "查询核销码失败"
			return out, err
		}
	}

	out.Code = 200

	return out, nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/group_chat_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type EditGroupChatRequest struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ChatName             string   `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	ChatLink             string   `protobuf:"bytes,3,opt,name=chat_link,json=chatLink,proto3" json:"chat_link"`
	ProvinceId           int64    `protobuf:"varint,4,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	ProvinceName         string   `protobuf:"bytes,5,opt,name=province_name,json=provinceName,proto3" json:"province_name"`
	CityId               int64    `protobuf:"varint,6,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	CityName             string   `protobuf:"bytes,7,opt,name=city_name,json=cityName,proto3" json:"city_name"`
	AreaId               int64    `protobuf:"varint,8,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	AreaName             string   `protobuf:"bytes,9,opt,name=area_name,json=areaName,proto3" json:"area_name"`
	StreetId             int64    `protobuf:"varint,10,opt,name=street_id,json=streetId,proto3" json:"street_id"`
	StreetName           string   `protobuf:"bytes,11,opt,name=street_name,json=streetName,proto3" json:"street_name"`
	Type                 int32    `protobuf:"varint,12,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditGroupChatRequest) Reset()         { *m = EditGroupChatRequest{} }
func (m *EditGroupChatRequest) String() string { return proto.CompactTextString(m) }
func (*EditGroupChatRequest) ProtoMessage()    {}
func (*EditGroupChatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{0}
}

func (m *EditGroupChatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditGroupChatRequest.Unmarshal(m, b)
}
func (m *EditGroupChatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditGroupChatRequest.Marshal(b, m, deterministic)
}
func (m *EditGroupChatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditGroupChatRequest.Merge(m, src)
}
func (m *EditGroupChatRequest) XXX_Size() int {
	return xxx_messageInfo_EditGroupChatRequest.Size(m)
}
func (m *EditGroupChatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditGroupChatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditGroupChatRequest proto.InternalMessageInfo

func (m *EditGroupChatRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EditGroupChatRequest) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *EditGroupChatRequest) GetChatLink() string {
	if m != nil {
		return m.ChatLink
	}
	return ""
}

func (m *EditGroupChatRequest) GetProvinceId() int64 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *EditGroupChatRequest) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

func (m *EditGroupChatRequest) GetCityId() int64 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *EditGroupChatRequest) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *EditGroupChatRequest) GetAreaId() int64 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *EditGroupChatRequest) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

func (m *EditGroupChatRequest) GetStreetId() int64 {
	if m != nil {
		return m.StreetId
	}
	return 0
}

func (m *EditGroupChatRequest) GetStreetName() string {
	if m != nil {
		return m.StreetName
	}
	return ""
}

func (m *EditGroupChatRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type DeleteGroupChatRequest struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupChatRequest) Reset()         { *m = DeleteGroupChatRequest{} }
func (m *DeleteGroupChatRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupChatRequest) ProtoMessage()    {}
func (*DeleteGroupChatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{1}
}

func (m *DeleteGroupChatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupChatRequest.Unmarshal(m, b)
}
func (m *DeleteGroupChatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupChatRequest.Marshal(b, m, deterministic)
}
func (m *DeleteGroupChatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupChatRequest.Merge(m, src)
}
func (m *DeleteGroupChatRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupChatRequest.Size(m)
}
func (m *DeleteGroupChatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupChatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupChatRequest proto.InternalMessageInfo

func (m *DeleteGroupChatRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type QueryGroupChatRequest struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	PageIndex            int32    `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryGroupChatRequest) Reset()         { *m = QueryGroupChatRequest{} }
func (m *QueryGroupChatRequest) String() string { return proto.CompactTextString(m) }
func (*QueryGroupChatRequest) ProtoMessage()    {}
func (*QueryGroupChatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{2}
}

func (m *QueryGroupChatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryGroupChatRequest.Unmarshal(m, b)
}
func (m *QueryGroupChatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryGroupChatRequest.Marshal(b, m, deterministic)
}
func (m *QueryGroupChatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryGroupChatRequest.Merge(m, src)
}
func (m *QueryGroupChatRequest) XXX_Size() int {
	return xxx_messageInfo_QueryGroupChatRequest.Size(m)
}
func (m *QueryGroupChatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryGroupChatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryGroupChatRequest proto.InternalMessageInfo

func (m *QueryGroupChatRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QueryGroupChatRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *QueryGroupChatRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *QueryGroupChatRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type QueryGroupChatResponse struct {
	TotalCount           int32        `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	Data                 []*GroupChat `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryGroupChatResponse) Reset()         { *m = QueryGroupChatResponse{} }
func (m *QueryGroupChatResponse) String() string { return proto.CompactTextString(m) }
func (*QueryGroupChatResponse) ProtoMessage()    {}
func (*QueryGroupChatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{3}
}

func (m *QueryGroupChatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryGroupChatResponse.Unmarshal(m, b)
}
func (m *QueryGroupChatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryGroupChatResponse.Marshal(b, m, deterministic)
}
func (m *QueryGroupChatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryGroupChatResponse.Merge(m, src)
}
func (m *QueryGroupChatResponse) XXX_Size() int {
	return xxx_messageInfo_QueryGroupChatResponse.Size(m)
}
func (m *QueryGroupChatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryGroupChatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryGroupChatResponse proto.InternalMessageInfo

func (m *QueryGroupChatResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *QueryGroupChatResponse) GetData() []*GroupChat {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupChat struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ChatName             string   `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	ChatLink             string   `protobuf:"bytes,3,opt,name=chat_link,json=chatLink,proto3" json:"chat_link"`
	ProvinceId           int64    `protobuf:"varint,4,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	ProvinceName         string   `protobuf:"bytes,5,opt,name=province_name,json=provinceName,proto3" json:"province_name"`
	CityId               int64    `protobuf:"varint,6,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	CityName             string   `protobuf:"bytes,7,opt,name=city_name,json=cityName,proto3" json:"city_name"`
	AreaId               int64    `protobuf:"varint,8,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	AreaName             string   `protobuf:"bytes,9,opt,name=area_name,json=areaName,proto3" json:"area_name"`
	StreetId             int64    `protobuf:"varint,10,opt,name=street_id,json=streetId,proto3" json:"street_id"`
	StreetName           string   `protobuf:"bytes,11,opt,name=street_name,json=streetName,proto3" json:"street_name"`
	HospitalCodes        []string `protobuf:"bytes,12,rep,name=hospital_codes,json=hospitalCodes,proto3" json:"hospital_codes"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupChat) Reset()         { *m = GroupChat{} }
func (m *GroupChat) String() string { return proto.CompactTextString(m) }
func (*GroupChat) ProtoMessage()    {}
func (*GroupChat) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{4}
}

func (m *GroupChat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupChat.Unmarshal(m, b)
}
func (m *GroupChat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupChat.Marshal(b, m, deterministic)
}
func (m *GroupChat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupChat.Merge(m, src)
}
func (m *GroupChat) XXX_Size() int {
	return xxx_messageInfo_GroupChat.Size(m)
}
func (m *GroupChat) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupChat.DiscardUnknown(m)
}

var xxx_messageInfo_GroupChat proto.InternalMessageInfo

func (m *GroupChat) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupChat) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *GroupChat) GetChatLink() string {
	if m != nil {
		return m.ChatLink
	}
	return ""
}

func (m *GroupChat) GetProvinceId() int64 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *GroupChat) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

func (m *GroupChat) GetCityId() int64 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *GroupChat) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *GroupChat) GetAreaId() int64 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *GroupChat) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

func (m *GroupChat) GetStreetId() int64 {
	if m != nil {
		return m.StreetId
	}
	return 0
}

func (m *GroupChat) GetStreetName() string {
	if m != nil {
		return m.StreetName
	}
	return ""
}

func (m *GroupChat) GetHospitalCodes() []string {
	if m != nil {
		return m.HospitalCodes
	}
	return nil
}

type AddJoinWayReq struct {
	//活动类型 1，宠友服务群 2，抗疫补给站
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//群名称
	ChatName string `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	//省id
	ProvinceId int32 `protobuf:"varint,3,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	//省名称
	ProvinceName string `protobuf:"bytes,4,opt,name=province_name,json=provinceName,proto3" json:"province_name"`
	//市id
	CityId int32 `protobuf:"varint,5,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//市名称
	CityName string `protobuf:"bytes,6,opt,name=city_name,json=cityName,proto3" json:"city_name"`
	//区id
	AreaId int32 `protobuf:"varint,7,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	//区名称
	AreaName string `protobuf:"bytes,8,opt,name=area_name,json=areaName,proto3" json:"area_name"`
	//街道id
	StreetId int32 `protobuf:"varint,9,opt,name=street_id,json=streetId,proto3" json:"street_id"`
	//街道名称
	StreetName string `protobuf:"bytes,10,opt,name=street_name,json=streetName,proto3" json:"street_name"`
	//群ID，多个逗号分隔
	ChatId               string   `protobuf:"bytes,11,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddJoinWayReq) Reset()         { *m = AddJoinWayReq{} }
func (m *AddJoinWayReq) String() string { return proto.CompactTextString(m) }
func (*AddJoinWayReq) ProtoMessage()    {}
func (*AddJoinWayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{5}
}

func (m *AddJoinWayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddJoinWayReq.Unmarshal(m, b)
}
func (m *AddJoinWayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddJoinWayReq.Marshal(b, m, deterministic)
}
func (m *AddJoinWayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddJoinWayReq.Merge(m, src)
}
func (m *AddJoinWayReq) XXX_Size() int {
	return xxx_messageInfo_AddJoinWayReq.Size(m)
}
func (m *AddJoinWayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddJoinWayReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddJoinWayReq proto.InternalMessageInfo

func (m *AddJoinWayReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddJoinWayReq) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *AddJoinWayReq) GetProvinceId() int32 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *AddJoinWayReq) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

func (m *AddJoinWayReq) GetCityId() int32 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *AddJoinWayReq) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *AddJoinWayReq) GetAreaId() int32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *AddJoinWayReq) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

func (m *AddJoinWayReq) GetStreetId() int32 {
	if m != nil {
		return m.StreetId
	}
	return 0
}

func (m *AddJoinWayReq) GetStreetName() string {
	if m != nil {
		return m.StreetName
	}
	return ""
}

func (m *AddJoinWayReq) GetChatId() string {
	if m != nil {
		return m.ChatId
	}
	return ""
}

type ChatListReq struct {
	//群名称
	ChatName string `protobuf:"bytes,1,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	//群主名称
	ChatMaster           string   `protobuf:"bytes,2,opt,name=chat_master,json=chatMaster,proto3" json:"chat_master"`
	PageIndex            int32    `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatListReq) Reset()         { *m = ChatListReq{} }
func (m *ChatListReq) String() string { return proto.CompactTextString(m) }
func (*ChatListReq) ProtoMessage()    {}
func (*ChatListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{6}
}

func (m *ChatListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatListReq.Unmarshal(m, b)
}
func (m *ChatListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatListReq.Marshal(b, m, deterministic)
}
func (m *ChatListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatListReq.Merge(m, src)
}
func (m *ChatListReq) XXX_Size() int {
	return xxx_messageInfo_ChatListReq.Size(m)
}
func (m *ChatListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChatListReq proto.InternalMessageInfo

func (m *ChatListReq) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *ChatListReq) GetChatMaster() string {
	if m != nil {
		return m.ChatMaster
	}
	return ""
}

func (m *ChatListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ChatListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ChatListRes struct {
	Total                int32           `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Data                 []*ChatListData `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChatListRes) Reset()         { *m = ChatListRes{} }
func (m *ChatListRes) String() string { return proto.CompactTextString(m) }
func (*ChatListRes) ProtoMessage()    {}
func (*ChatListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{7}
}

func (m *ChatListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatListRes.Unmarshal(m, b)
}
func (m *ChatListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatListRes.Marshal(b, m, deterministic)
}
func (m *ChatListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatListRes.Merge(m, src)
}
func (m *ChatListRes) XXX_Size() int {
	return xxx_messageInfo_ChatListRes.Size(m)
}
func (m *ChatListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatListRes.DiscardUnknown(m)
}

var xxx_messageInfo_ChatListRes proto.InternalMessageInfo

func (m *ChatListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ChatListRes) GetData() []*ChatListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type ChatListData struct {
	//群ID
	ChatId string `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id"`
	//群名称
	ChatName string `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	//群公告
	Notice string `protobuf:"bytes,3,opt,name=notice,proto3" json:"notice"`
	//群创建时间
	ChatCreateTime int64 `protobuf:"varint,4,opt,name=chat_create_time,json=chatCreateTime,proto3" json:"chat_create_time"`
	//群主名称
	OwnerName            string   `protobuf:"bytes,5,opt,name=owner_name,json=ownerName,proto3" json:"owner_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatListData) Reset()         { *m = ChatListData{} }
func (m *ChatListData) String() string { return proto.CompactTextString(m) }
func (*ChatListData) ProtoMessage()    {}
func (*ChatListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{8}
}

func (m *ChatListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatListData.Unmarshal(m, b)
}
func (m *ChatListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatListData.Marshal(b, m, deterministic)
}
func (m *ChatListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatListData.Merge(m, src)
}
func (m *ChatListData) XXX_Size() int {
	return xxx_messageInfo_ChatListData.Size(m)
}
func (m *ChatListData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatListData.DiscardUnknown(m)
}

var xxx_messageInfo_ChatListData proto.InternalMessageInfo

func (m *ChatListData) GetChatId() string {
	if m != nil {
		return m.ChatId
	}
	return ""
}

func (m *ChatListData) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *ChatListData) GetNotice() string {
	if m != nil {
		return m.Notice
	}
	return ""
}

func (m *ChatListData) GetChatCreateTime() int64 {
	if m != nil {
		return m.ChatCreateTime
	}
	return 0
}

func (m *ChatListData) GetOwnerName() string {
	if m != nil {
		return m.OwnerName
	}
	return ""
}

type SetHospitalWechatRequest struct {
	// 医院code
	HospitalCode string `protobuf:"bytes,1,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	// 微信二维码地址
	WechatQrcode         string   `protobuf:"bytes,2,opt,name=wechat_qrcode,json=wechatQrcode,proto3" json:"wechat_qrcode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetHospitalWechatRequest) Reset()         { *m = SetHospitalWechatRequest{} }
func (m *SetHospitalWechatRequest) String() string { return proto.CompactTextString(m) }
func (*SetHospitalWechatRequest) ProtoMessage()    {}
func (*SetHospitalWechatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{9}
}

func (m *SetHospitalWechatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHospitalWechatRequest.Unmarshal(m, b)
}
func (m *SetHospitalWechatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHospitalWechatRequest.Marshal(b, m, deterministic)
}
func (m *SetHospitalWechatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHospitalWechatRequest.Merge(m, src)
}
func (m *SetHospitalWechatRequest) XXX_Size() int {
	return xxx_messageInfo_SetHospitalWechatRequest.Size(m)
}
func (m *SetHospitalWechatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHospitalWechatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetHospitalWechatRequest proto.InternalMessageInfo

func (m *SetHospitalWechatRequest) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *SetHospitalWechatRequest) GetWechatQrcode() string {
	if m != nil {
		return m.WechatQrcode
	}
	return ""
}

type LinkHospitalRequest struct {
	GroupChatId          int64    `protobuf:"varint,1,opt,name=group_chat_id,json=groupChatId,proto3" json:"group_chat_id"`
	HospitalCodes        []string `protobuf:"bytes,2,rep,name=hospital_codes,json=hospitalCodes,proto3" json:"hospital_codes"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LinkHospitalRequest) Reset()         { *m = LinkHospitalRequest{} }
func (m *LinkHospitalRequest) String() string { return proto.CompactTextString(m) }
func (*LinkHospitalRequest) ProtoMessage()    {}
func (*LinkHospitalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{10}
}

func (m *LinkHospitalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinkHospitalRequest.Unmarshal(m, b)
}
func (m *LinkHospitalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinkHospitalRequest.Marshal(b, m, deterministic)
}
func (m *LinkHospitalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinkHospitalRequest.Merge(m, src)
}
func (m *LinkHospitalRequest) XXX_Size() int {
	return xxx_messageInfo_LinkHospitalRequest.Size(m)
}
func (m *LinkHospitalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LinkHospitalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LinkHospitalRequest proto.InternalMessageInfo

func (m *LinkHospitalRequest) GetGroupChatId() int64 {
	if m != nil {
		return m.GroupChatId
	}
	return 0
}

func (m *LinkHospitalRequest) GetHospitalCodes() []string {
	if m != nil {
		return m.HospitalCodes
	}
	return nil
}

type HospitalGroupChatListRequest struct {
	HospitalCode         string   `protobuf:"bytes,1,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HospitalGroupChatListRequest) Reset()         { *m = HospitalGroupChatListRequest{} }
func (m *HospitalGroupChatListRequest) String() string { return proto.CompactTextString(m) }
func (*HospitalGroupChatListRequest) ProtoMessage()    {}
func (*HospitalGroupChatListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{11}
}

func (m *HospitalGroupChatListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HospitalGroupChatListRequest.Unmarshal(m, b)
}
func (m *HospitalGroupChatListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HospitalGroupChatListRequest.Marshal(b, m, deterministic)
}
func (m *HospitalGroupChatListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HospitalGroupChatListRequest.Merge(m, src)
}
func (m *HospitalGroupChatListRequest) XXX_Size() int {
	return xxx_messageInfo_HospitalGroupChatListRequest.Size(m)
}
func (m *HospitalGroupChatListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HospitalGroupChatListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HospitalGroupChatListRequest proto.InternalMessageInfo

func (m *HospitalGroupChatListRequest) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

type HospitalGroupChatListResponse struct {
	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 门店微信
	Wechat string `protobuf:"bytes,3,opt,name=wechat,proto3" json:"wechat"`
	// 门店微信群
	GroupList            []*HospitalGroupChatListResponse_GroupChat `protobuf:"bytes,4,rep,name=group_list,json=groupList,proto3" json:"group_list"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *HospitalGroupChatListResponse) Reset()         { *m = HospitalGroupChatListResponse{} }
func (m *HospitalGroupChatListResponse) String() string { return proto.CompactTextString(m) }
func (*HospitalGroupChatListResponse) ProtoMessage()    {}
func (*HospitalGroupChatListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{12}
}

func (m *HospitalGroupChatListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HospitalGroupChatListResponse.Unmarshal(m, b)
}
func (m *HospitalGroupChatListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HospitalGroupChatListResponse.Marshal(b, m, deterministic)
}
func (m *HospitalGroupChatListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HospitalGroupChatListResponse.Merge(m, src)
}
func (m *HospitalGroupChatListResponse) XXX_Size() int {
	return xxx_messageInfo_HospitalGroupChatListResponse.Size(m)
}
func (m *HospitalGroupChatListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HospitalGroupChatListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HospitalGroupChatListResponse proto.InternalMessageInfo

func (m *HospitalGroupChatListResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *HospitalGroupChatListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *HospitalGroupChatListResponse) GetWechat() string {
	if m != nil {
		return m.Wechat
	}
	return ""
}

func (m *HospitalGroupChatListResponse) GetGroupList() []*HospitalGroupChatListResponse_GroupChat {
	if m != nil {
		return m.GroupList
	}
	return nil
}

type HospitalGroupChatListResponse_GroupChat struct {
	// 微信群id
	GroupChatId int64 `protobuf:"varint,1,opt,name=group_chat_id,json=groupChatId,proto3" json:"group_chat_id"`
	// 微信群名称
	ChatName string `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name"`
	// 微信群链接
	ChatLink             string   `protobuf:"bytes,3,opt,name=chat_link,json=chatLink,proto3" json:"chat_link"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HospitalGroupChatListResponse_GroupChat) Reset() {
	*m = HospitalGroupChatListResponse_GroupChat{}
}
func (m *HospitalGroupChatListResponse_GroupChat) String() string { return proto.CompactTextString(m) }
func (*HospitalGroupChatListResponse_GroupChat) ProtoMessage()    {}
func (*HospitalGroupChatListResponse_GroupChat) Descriptor() ([]byte, []int) {
	return fileDescriptor_9812cc39abfee5c4, []int{12, 0}
}

func (m *HospitalGroupChatListResponse_GroupChat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HospitalGroupChatListResponse_GroupChat.Unmarshal(m, b)
}
func (m *HospitalGroupChatListResponse_GroupChat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HospitalGroupChatListResponse_GroupChat.Marshal(b, m, deterministic)
}
func (m *HospitalGroupChatListResponse_GroupChat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HospitalGroupChatListResponse_GroupChat.Merge(m, src)
}
func (m *HospitalGroupChatListResponse_GroupChat) XXX_Size() int {
	return xxx_messageInfo_HospitalGroupChatListResponse_GroupChat.Size(m)
}
func (m *HospitalGroupChatListResponse_GroupChat) XXX_DiscardUnknown() {
	xxx_messageInfo_HospitalGroupChatListResponse_GroupChat.DiscardUnknown(m)
}

var xxx_messageInfo_HospitalGroupChatListResponse_GroupChat proto.InternalMessageInfo

func (m *HospitalGroupChatListResponse_GroupChat) GetGroupChatId() int64 {
	if m != nil {
		return m.GroupChatId
	}
	return 0
}

func (m *HospitalGroupChatListResponse_GroupChat) GetChatName() string {
	if m != nil {
		return m.ChatName
	}
	return ""
}

func (m *HospitalGroupChatListResponse_GroupChat) GetChatLink() string {
	if m != nil {
		return m.ChatLink
	}
	return ""
}

func init() {
	proto.RegisterType((*EditGroupChatRequest)(nil), "ac.EditGroupChatRequest")
	proto.RegisterType((*DeleteGroupChatRequest)(nil), "ac.DeleteGroupChatRequest")
	proto.RegisterType((*QueryGroupChatRequest)(nil), "ac.QueryGroupChatRequest")
	proto.RegisterType((*QueryGroupChatResponse)(nil), "ac.QueryGroupChatResponse")
	proto.RegisterType((*GroupChat)(nil), "ac.GroupChat")
	proto.RegisterType((*AddJoinWayReq)(nil), "ac.AddJoinWayReq")
	proto.RegisterType((*ChatListReq)(nil), "ac.ChatListReq")
	proto.RegisterType((*ChatListRes)(nil), "ac.ChatListRes")
	proto.RegisterType((*ChatListData)(nil), "ac.ChatListData")
	proto.RegisterType((*SetHospitalWechatRequest)(nil), "ac.SetHospitalWechatRequest")
	proto.RegisterType((*LinkHospitalRequest)(nil), "ac.LinkHospitalRequest")
	proto.RegisterType((*HospitalGroupChatListRequest)(nil), "ac.HospitalGroupChatListRequest")
	proto.RegisterType((*HospitalGroupChatListResponse)(nil), "ac.HospitalGroupChatListResponse")
	proto.RegisterType((*HospitalGroupChatListResponse_GroupChat)(nil), "ac.HospitalGroupChatListResponse.GroupChat")
}

func init() { proto.RegisterFile("ac/group_chat_service.proto", fileDescriptor_9812cc39abfee5c4) }

var fileDescriptor_9812cc39abfee5c4 = []byte{
	// 930 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x56, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x06, 0xa9, 0x1f, 0x8b, 0x23, 0xc9, 0x71, 0x36, 0x89, 0xcd, 0x2a, 0x09, 0xaa, 0x30, 0x2d,
	0x20, 0xa0, 0x85, 0x03, 0xb8, 0xbd, 0xf4, 0x52, 0xa0, 0x51, 0x8a, 0x54, 0x41, 0x1b, 0x20, 0x74,
	0x01, 0x5f, 0x02, 0xa8, 0x1b, 0xee, 0xc0, 0x5e, 0x44, 0x22, 0x65, 0x72, 0x6d, 0x47, 0x79, 0x81,
	0xbe, 0x44, 0xaf, 0x7d, 0x98, 0x3e, 0x40, 0xdf, 0xa0, 0xc7, 0x3e, 0x44, 0x31, 0xb3, 0x24, 0x4d,
	0xd9, 0x94, 0x1c, 0xa4, 0xd7, 0xdc, 0xb8, 0xdf, 0xec, 0x37, 0xb3, 0xf3, 0xed, 0xcc, 0x70, 0xe1,
	0xbe, 0x8c, 0x9e, 0x1c, 0xa7, 0xc9, 0xd9, 0x62, 0x1a, 0x9d, 0x48, 0x33, 0xcd, 0x30, 0x3d, 0xd7,
	0x11, 0xee, 0x2f, 0xd2, 0xc4, 0x24, 0xc2, 0x95, 0xd1, 0x60, 0x4f, 0x46, 0x4f, 0x64, 0x64, 0xf4,
	0xb9, 0x36, 0xcb, 0xe9, 0x3c, 0x51, 0x38, 0xb3, 0xc6, 0xe0, 0x1f, 0x17, 0xee, 0xfe, 0xa8, 0xb4,
	0x79, 0x4e, 0xec, 0xf1, 0x89, 0x34, 0x21, 0x9e, 0x9e, 0x61, 0x66, 0xc4, 0x36, 0xb8, 0x5a, 0xf9,
	0xce, 0xd0, 0x19, 0x35, 0x42, 0x57, 0x2b, 0x71, 0x1f, 0x3c, 0xf6, 0x1d, 0xcb, 0x39, 0xfa, 0xee,
	0xd0, 0x19, 0x79, 0x61, 0x87, 0x80, 0x97, 0x72, 0x8e, 0xa5, 0x71, 0xa6, 0xe3, 0xb7, 0x7e, 0xe3,
	0xd2, 0xf8, 0xb3, 0x8e, 0xdf, 0x8a, 0xcf, 0xa1, 0xbb, 0x48, 0x93, 0x73, 0x1d, 0x47, 0x38, 0xd5,
	0xca, 0x6f, 0xb2, 0x4b, 0x28, 0xa0, 0x89, 0x12, 0x8f, 0xa1, 0x5f, 0x6e, 0x60, 0xf7, 0x2d, 0xf6,
	0xd0, 0x2b, 0x40, 0x0e, 0xb1, 0x07, 0x5b, 0x11, 0x1d, 0x5e, 0x2b, 0xbf, 0xcd, 0x1e, 0xda, 0xb4,
	0x9c, 0xd8, 0x83, 0x91, 0x81, 0x99, 0x5b, 0x79, 0x6c, 0x6d, 0x96, 0x05, 0x4b, 0xa6, 0x28, 0x89,
	0xd5, 0xb1, 0x2c, 0x5a, 0x5a, 0x16, 0x1b, 0x98, 0xe5, 0x59, 0x16, 0x01, 0x45, 0x3a, 0x99, 0x49,
	0x11, 0x0d, 0xf1, 0x80, 0x79, 0x1d, 0x0b, 0x4c, 0x14, 0xa5, 0x93, 0x1b, 0x99, 0xdb, 0x65, 0x2e,
	0x58, 0x88, 0xd9, 0x02, 0x9a, 0x66, 0xb9, 0x40, 0xbf, 0x37, 0x74, 0x46, 0xad, 0x90, 0xbf, 0x83,
	0x11, 0xec, 0x3e, 0xc3, 0x19, 0x1a, 0xbc, 0x49, 0xe7, 0xe0, 0x02, 0xee, 0xbd, 0x3a, 0xc3, 0x74,
	0x79, 0xe3, 0x85, 0x14, 0x61, 0xdc, 0xcb, 0x30, 0xe2, 0x21, 0xc0, 0x42, 0x1e, 0xe3, 0x54, 0xc7,
	0x0a, 0xdf, 0xf1, 0x45, 0xb4, 0x42, 0x8f, 0x90, 0x09, 0x01, 0x94, 0x17, 0x9b, 0x33, 0xfd, 0x1e,
	0xf9, 0x1e, 0x5a, 0x61, 0x87, 0x80, 0x43, 0xfd, 0x1e, 0x83, 0xd7, 0xb0, 0x7b, 0x35, 0x70, 0xb6,
	0x48, 0xe2, 0x0c, 0x29, 0x63, 0x93, 0x18, 0x39, 0x9b, 0x46, 0xc9, 0x59, 0x6c, 0xf8, 0x08, 0xad,
	0x10, 0x18, 0x1a, 0x13, 0x22, 0x1e, 0x41, 0x53, 0x49, 0x23, 0x7d, 0x77, 0xd8, 0x18, 0x75, 0x0f,
	0xfa, 0xfb, 0x32, 0xda, 0xbf, 0xf4, 0xc2, 0xa6, 0xe0, 0x5f, 0x17, 0xbc, 0x12, 0xfb, 0x54, 0x5c,
	0x1f, 0x5d, 0x5c, 0x5f, 0xc2, 0xf6, 0x49, 0x92, 0x2d, 0xb4, 0xbd, 0x0e, 0x85, 0x99, 0xdf, 0x1b,
	0x36, 0x46, 0x5e, 0xd8, 0x2f, 0xd0, 0x31, 0x81, 0xc1, 0x5f, 0x2e, 0xf4, 0x7f, 0x50, 0xea, 0x45,
	0xa2, 0xe3, 0x23, 0xb9, 0x0c, 0xf1, 0xb4, 0x2c, 0x17, 0xa7, 0x52, 0x2e, 0x1b, 0x65, 0xbf, 0xa2,
	0xac, 0x2d, 0xa6, 0x8d, 0xca, 0x36, 0x37, 0x2b, 0xdb, 0x62, 0x0f, 0xb5, 0xca, 0xb6, 0xd7, 0x2b,
	0xbb, 0x65, 0x59, 0x75, 0xca, 0x76, 0x36, 0x29, 0xeb, 0xd9, 0xf2, 0x5e, 0xa7, 0x2c, 0x5c, 0x53,
	0x96, 0x4e, 0x4a, 0x62, 0x68, 0x95, 0xcb, 0xde, 0xa6, 0xe5, 0x44, 0x05, 0xbf, 0x3b, 0xd0, 0x1d,
	0x73, 0xbd, 0x65, 0xd4, 0x8c, 0xab, 0xaa, 0x39, 0xd7, 0x55, 0x63, 0xe3, 0x5c, 0x66, 0x06, 0xd3,
	0x5c, 0x54, 0x20, 0xe8, 0x17, 0x46, 0xfe, 0x57, 0x8b, 0x4e, 0xaa, 0x07, 0xc9, 0xc4, 0x5d, 0x68,
	0x71, 0x13, 0xe6, 0x77, 0x6a, 0x17, 0xe2, 0x8b, 0x95, 0x66, 0xdc, 0xa1, 0x66, 0x2c, 0x48, 0xcf,
	0xa4, 0x91, 0x79, 0x3f, 0xfe, 0xe9, 0x40, 0xaf, 0x0a, 0x57, 0xd3, 0x77, 0xaa, 0xe9, 0x6f, 0x2e,
	0x92, 0x5d, 0x68, 0xc7, 0x89, 0xd1, 0x11, 0xe6, 0x8d, 0x99, 0xaf, 0xc4, 0x08, 0x76, 0x98, 0x14,
	0xa5, 0x28, 0x0d, 0x4e, 0x8d, 0xce, 0xcb, 0xa3, 0x11, 0x6e, 0x13, 0x3e, 0x66, 0xf8, 0x57, 0x3d,
	0xe7, 0x91, 0x95, 0x5c, 0xc4, 0x98, 0x56, 0x9b, 0xd3, 0x63, 0x84, 0x02, 0x04, 0x0a, 0xfc, 0x43,
	0x34, 0x3f, 0xe5, 0xc5, 0x7d, 0x84, 0x51, 0x65, 0x22, 0x3e, 0x86, 0xfe, 0x4a, 0x2f, 0xe4, 0x07,
	0xef, 0x55, 0x5b, 0x81, 0x36, 0x5d, 0x30, 0x6b, 0x7a, 0x9a, 0xf2, 0x26, 0x9b, 0x42, 0xcf, 0x82,
	0xaf, 0x18, 0x0b, 0x7e, 0x83, 0x3b, 0x34, 0x4d, 0x8a, 0x30, 0x45, 0x80, 0x00, 0xfa, 0x95, 0xbf,
	0x6a, 0x39, 0xb1, 0xba, 0xc7, 0xc5, 0x20, 0x9b, 0xa8, 0x9a, 0x86, 0x74, 0xeb, 0x1a, 0x72, 0x0c,
	0x0f, 0x0a, 0xef, 0xe5, 0x18, 0xcc, 0x0b, 0xea, 0x83, 0x73, 0x09, 0xfe, 0x70, 0xe1, 0xe1, 0x1a,
	0x2f, 0xf9, 0xa8, 0x16, 0xd0, 0x2c, 0xd9, 0x8d, 0x90, 0xbf, 0x85, 0x0f, 0x5b, 0x73, 0xcc, 0x32,
	0x79, 0x5c, 0xe4, 0x5e, 0x2c, 0xe9, 0xf6, 0xac, 0x0c, 0xc5, 0xed, 0xd9, 0x95, 0x78, 0x01, 0x60,
	0xf3, 0x9e, 0xe9, 0xcc, 0xf8, 0x4d, 0x2e, 0xa4, 0xaf, 0xa8, 0x90, 0x36, 0x06, 0xaf, 0xcc, 0x7c,
	0x8f, 0xe9, 0x64, 0x1c, 0xe8, 0xea, 0xdc, 0xff, 0x10, 0x41, 0x3f, 0xfa, 0x5f, 0x70, 0xf0, 0x77,
	0x13, 0x76, 0xca, 0x58, 0x87, 0xf6, 0x0d, 0x24, 0xbe, 0x87, 0xfe, 0xca, 0xfb, 0x46, 0xf8, 0x94,
	0x48, 0xdd, 0x93, 0x67, 0x70, 0x87, 0x2c, 0x4f, 0x65, 0x86, 0x45, 0x46, 0x2f, 0xf1, 0x42, 0x3c,
	0x87, 0xed, 0xd5, 0xdf, 0xa2, 0xf8, 0x8c, 0xb6, 0xd5, 0xfe, 0xa3, 0x07, 0x83, 0x3a, 0x53, 0x7e,
	0x35, 0x4f, 0xe1, 0xd6, 0x95, 0x27, 0x80, 0xe0, 0xed, 0xf5, 0xef, 0x82, 0xfa, 0xc3, 0x7c, 0x0b,
	0x70, 0x39, 0xd5, 0xc5, 0x6d, 0xda, 0xb2, 0x32, 0xe5, 0xeb, 0x59, 0x5f, 0x43, 0xa7, 0xb8, 0x2b,
	0x71, 0xab, 0x3a, 0x0f, 0x88, 0x71, 0x05, 0xc8, 0xc4, 0x01, 0xc0, 0xd1, 0xbb, 0xf5, 0xfb, 0x6b,
	0x23, 0x8c, 0xe1, 0xf6, 0xb5, 0x2e, 0x15, 0x0f, 0x68, 0xe7, 0xba, 0xe6, 0x1d, 0xf0, 0x60, 0x7a,
	0x53, 0xf1, 0x23, 0xbe, 0x83, 0x5e, 0xb5, 0x09, 0xc5, 0x1e, 0xed, 0xa8, 0x69, 0xcb, 0x1a, 0xea,
	0x6b, 0xb8, 0x57, 0x5b, 0x9a, 0x62, 0xb8, 0xa1, 0x6a, 0xad, 0xb3, 0x47, 0x37, 0xd6, 0xf5, 0x9b,
	0x36, 0x3f, 0x95, 0xbf, 0xf9, 0x2f, 0x00, 0x00, 0xff, 0xff, 0xf1, 0xfa, 0x4a, 0x5e, 0x66, 0x0b,
	0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GroupChatServiceClient is the client API for GroupChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GroupChatServiceClient interface {
	//新增\修改社区群
	EditGroupChat(ctx context.Context, in *EditGroupChatRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//查询社区群
	QueryGroupChat(ctx context.Context, in *QueryGroupChatRequest, opts ...grpc.CallOption) (*QueryGroupChatResponse, error)
	//删除社区群
	DeleteGroupChat(ctx context.Context, in *DeleteGroupChatRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//生成加群链接
	AddJoinWay(ctx context.Context, in *AddJoinWayReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//群列表(管理后台生成加群链接)
	ChatList(ctx context.Context, in *ChatListReq, opts ...grpc.CallOption) (*ChatListRes, error)
	//刷新群列表（所有群）
	WxChatList(ctx context.Context, in *ChatListReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 设置门店微信
	SetHospitalWechat(ctx context.Context, in *SetHospitalWechatRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//关联、取消微信群医院
	LinkHospital(ctx context.Context, in *LinkHospitalRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取医院微信群列表
	HospitalGroupChatList(ctx context.Context, in *HospitalGroupChatListRequest, opts ...grpc.CallOption) (*HospitalGroupChatListResponse, error)
}

type groupChatServiceClient struct {
	cc *grpc.ClientConn
}

func NewGroupChatServiceClient(cc *grpc.ClientConn) GroupChatServiceClient {
	return &groupChatServiceClient{cc}
}

func (c *groupChatServiceClient) EditGroupChat(ctx context.Context, in *EditGroupChatRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/EditGroupChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) QueryGroupChat(ctx context.Context, in *QueryGroupChatRequest, opts ...grpc.CallOption) (*QueryGroupChatResponse, error) {
	out := new(QueryGroupChatResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/QueryGroupChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) DeleteGroupChat(ctx context.Context, in *DeleteGroupChatRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/DeleteGroupChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) AddJoinWay(ctx context.Context, in *AddJoinWayReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/AddJoinWay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) ChatList(ctx context.Context, in *ChatListReq, opts ...grpc.CallOption) (*ChatListRes, error) {
	out := new(ChatListRes)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/ChatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) WxChatList(ctx context.Context, in *ChatListReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/WxChatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) SetHospitalWechat(ctx context.Context, in *SetHospitalWechatRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/SetHospitalWechat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) LinkHospital(ctx context.Context, in *LinkHospitalRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/LinkHospital", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupChatServiceClient) HospitalGroupChatList(ctx context.Context, in *HospitalGroupChatListRequest, opts ...grpc.CallOption) (*HospitalGroupChatListResponse, error) {
	out := new(HospitalGroupChatListResponse)
	err := c.cc.Invoke(ctx, "/ac.GroupChatService/HospitalGroupChatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupChatServiceServer is the server API for GroupChatService service.
type GroupChatServiceServer interface {
	//新增\修改社区群
	EditGroupChat(context.Context, *EditGroupChatRequest) (*BaseResponseNew, error)
	//查询社区群
	QueryGroupChat(context.Context, *QueryGroupChatRequest) (*QueryGroupChatResponse, error)
	//删除社区群
	DeleteGroupChat(context.Context, *DeleteGroupChatRequest) (*BaseResponseNew, error)
	//生成加群链接
	AddJoinWay(context.Context, *AddJoinWayReq) (*BaseResponseNew, error)
	//群列表(管理后台生成加群链接)
	ChatList(context.Context, *ChatListReq) (*ChatListRes, error)
	//刷新群列表（所有群）
	WxChatList(context.Context, *ChatListReq) (*BaseResponseNew, error)
	// 设置门店微信
	SetHospitalWechat(context.Context, *SetHospitalWechatRequest) (*BaseResponse, error)
	//关联、取消微信群医院
	LinkHospital(context.Context, *LinkHospitalRequest) (*BaseResponse, error)
	//获取医院微信群列表
	HospitalGroupChatList(context.Context, *HospitalGroupChatListRequest) (*HospitalGroupChatListResponse, error)
}

// UnimplementedGroupChatServiceServer can be embedded to have forward compatible implementations.
type UnimplementedGroupChatServiceServer struct {
}

func (*UnimplementedGroupChatServiceServer) EditGroupChat(ctx context.Context, req *EditGroupChatRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditGroupChat not implemented")
}
func (*UnimplementedGroupChatServiceServer) QueryGroupChat(ctx context.Context, req *QueryGroupChatRequest) (*QueryGroupChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryGroupChat not implemented")
}
func (*UnimplementedGroupChatServiceServer) DeleteGroupChat(ctx context.Context, req *DeleteGroupChatRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupChat not implemented")
}
func (*UnimplementedGroupChatServiceServer) AddJoinWay(ctx context.Context, req *AddJoinWayReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddJoinWay not implemented")
}
func (*UnimplementedGroupChatServiceServer) ChatList(ctx context.Context, req *ChatListReq) (*ChatListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChatList not implemented")
}
func (*UnimplementedGroupChatServiceServer) WxChatList(ctx context.Context, req *ChatListReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxChatList not implemented")
}
func (*UnimplementedGroupChatServiceServer) SetHospitalWechat(ctx context.Context, req *SetHospitalWechatRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHospitalWechat not implemented")
}
func (*UnimplementedGroupChatServiceServer) LinkHospital(ctx context.Context, req *LinkHospitalRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkHospital not implemented")
}
func (*UnimplementedGroupChatServiceServer) HospitalGroupChatList(ctx context.Context, req *HospitalGroupChatListRequest) (*HospitalGroupChatListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HospitalGroupChatList not implemented")
}

func RegisterGroupChatServiceServer(s *grpc.Server, srv GroupChatServiceServer) {
	s.RegisterService(&_GroupChatService_serviceDesc, srv)
}

func _GroupChatService_EditGroupChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditGroupChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).EditGroupChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/EditGroupChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).EditGroupChat(ctx, req.(*EditGroupChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_QueryGroupChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryGroupChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).QueryGroupChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/QueryGroupChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).QueryGroupChat(ctx, req.(*QueryGroupChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_DeleteGroupChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).DeleteGroupChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/DeleteGroupChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).DeleteGroupChat(ctx, req.(*DeleteGroupChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_AddJoinWay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddJoinWayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).AddJoinWay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/AddJoinWay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).AddJoinWay(ctx, req.(*AddJoinWayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_ChatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).ChatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/ChatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).ChatList(ctx, req.(*ChatListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_WxChatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).WxChatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/WxChatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).WxChatList(ctx, req.(*ChatListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_SetHospitalWechat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHospitalWechatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).SetHospitalWechat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/SetHospitalWechat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).SetHospitalWechat(ctx, req.(*SetHospitalWechatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_LinkHospital_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkHospitalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).LinkHospital(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/LinkHospital",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).LinkHospital(ctx, req.(*LinkHospitalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupChatService_HospitalGroupChatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HospitalGroupChatListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupChatServiceServer).HospitalGroupChatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.GroupChatService/HospitalGroupChatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupChatServiceServer).HospitalGroupChatList(ctx, req.(*HospitalGroupChatListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GroupChatService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.GroupChatService",
	HandlerType: (*GroupChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EditGroupChat",
			Handler:    _GroupChatService_EditGroupChat_Handler,
		},
		{
			MethodName: "QueryGroupChat",
			Handler:    _GroupChatService_QueryGroupChat_Handler,
		},
		{
			MethodName: "DeleteGroupChat",
			Handler:    _GroupChatService_DeleteGroupChat_Handler,
		},
		{
			MethodName: "AddJoinWay",
			Handler:    _GroupChatService_AddJoinWay_Handler,
		},
		{
			MethodName: "ChatList",
			Handler:    _GroupChatService_ChatList_Handler,
		},
		{
			MethodName: "WxChatList",
			Handler:    _GroupChatService_WxChatList_Handler,
		},
		{
			MethodName: "SetHospitalWechat",
			Handler:    _GroupChatService_SetHospitalWechat_Handler,
		},
		{
			MethodName: "LinkHospital",
			Handler:    _GroupChatService_LinkHospital_Handler,
		},
		{
			MethodName: "HospitalGroupChatList",
			Handler:    _GroupChatService_HospitalGroupChatList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/group_chat_service.proto",
}

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"reflect"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
)

func TestOrderService_QueryOrder(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *oc.QueryOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.QueryOrderResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				params: &oc.QueryOrderRequest{
					OrderSn: "6666",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &OrderService{}
			got, err := d.QueryOrder(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestOrderService_QueryGyPayOrder(t *testing.T) {
//	type fields struct {
//		BaseService       BaseService
//		AllChannelService AllChannelService
//	}
//	type args struct {
//		OrderSn string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *dto.PaymentParam
//		wantErr bool
//	}{
//		{args: args{OrderSn: "1586437854256623"}},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			o := &OrderService{
//				BaseService: tt.fields.BaseService,
//			}
//			got, err := o.QueryGyPayOrder(tt.args.OrderSn)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("QueryGyPayOrder() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("QueryGyPayOrder() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestOrderStatus(t *testing.T) {
	conn := GetDBConn()

	//查找有没有全部发货
	OrderProductCount := new(models.OrderProduct)
	productCount, err := conn.Where("order_sn = ?", "1587032907074293").Sum(OrderProductCount, "number")
	//已發貨
	deliver_num, err := conn.Where("order_sn = ?", "1587032907074293").Sum(OrderProductCount, "deliver_num")

	t.Log(productCount)
	t.Log(deliver_num)

	t.Log(fmt.Sprintf("%.2f", float64(19697/100)))

	return
	orderProduct := models.OrderProduct{}
	conn.Where("order_sn = ? and sku = ?", "1587032907074293", "777555").Get(&orderProduct)
	//orderProduct1 := models.OrderProduct{}
	orderProduct.DeliverStatus = 1
	//修改订单商品表状态
	_, err = conn.Where("order_sn = ? and sku = ?", "1587032907074293", "777555").Limit(1).Update(models.OrderProduct{
		DeliverNum:    orderProduct.DeliverNum + 5,
		DeliverStatus: 1,
	})

	t.Log(err)
}

func TestOrderStatus1(t *testing.T) {
	type args struct {
		DeliverParam *dto.DeliverParam
	}

	DeliverDetail := []dto.DeliverDetail{}
	DeliverDetail = append(DeliverDetail, dto.DeliverDetail{
		GoodsSku: "GYCESP002@001",
		Num:      1,
	})
	DeliverDetail = append(DeliverDetail, dto.DeliverDetail{
		GoodsSku: "GYCESP002@002",
		Num:      1,
	})
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{&dto.DeliverParam{
				Source:        2,
				OrderSn:       "1587451746046937",
				IsEntire:      0,
				DeliverDetail: DeliverDetail,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := OrderStatus(tt.args.DeliverParam)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("OrderStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_ReleaseStock(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.ReleaseStockRequest
	}
	var Detail []*oc.ReleaseStockRequest_ReleasePackage
	Detail = append(Detail, &oc.ReleaseStockRequest_ReleasePackage{
		Sku: "1000040001",
		Num: 2,
	})
	tests := []struct {
		name string
		args args
	}{
		{
			args: args{
				ctx: nil,
				params: &oc.ReleaseStockRequest{
					OrderId: "4000000033563801",
					Detail:  Detail,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.ReleaseStock(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("ReleaseStock() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_GyOrderQuery(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *oc.GyOrderQueryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GyOrderQueryResponse
		wantErr bool
	}{
		{args: args{
			ctx:    nil,
			params: &oc.GyOrderQueryRequest{Limit: 100, Datetime: "2020-04-28 21:30:04"},
		},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.GyOrderQuery(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GyOrderQuery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GyOrderQuery() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// 预订单列表查询
func BenchmarkBookingOrderList(b *testing.B) {
	type args struct {
		ctx    context.Context
		params *oc.BookingOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.BookingOrderResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				params: &oc.BookingOrderRequest{
					Pageindex: 1,
					Pagesize:  10,
					//OrderSource:  9,
					Shopids:      []string{"cx0004"},
					DeliveryTime: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		b.Run(tt.name, func(b *testing.B) {
			o := &OrderService{}
			for i := 0; i < b.N; i++ {
				_, err := o.BookingOrderList(tt.args.ctx, tt.args.params)
				if (err != nil) != tt.wantErr {
					b.Errorf("BookingOrderList() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
		})
	}
}

func TestOrderService_CancelAcceptOrder(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.CancelAcceptOrderRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx: context.Background(),
				params: &oc.CancelAcceptOrderRequest{
					OrderSn: "4100013289691126",
					Reason:  "库存不足",
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}

			ctx := metadata.NewIncomingContext(context.Background(), metadata.MD{
				"login_user_info": []string{kit.JsonEncodeBeuty(models.LoginUserInfo{
					UserName: "zhanshengde",
					UserNo:   "zhanshengde",
				})},
			})
			got, err := o.CancelAcceptOrder(ctx, tt.args.params)
			if err != nil {
				t.Errorf("CancelAcceptOrder() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_CancelOrder(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.CancelOrderRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx: context.Background(),
				params: &oc.CancelOrderRequest{
					OrderSn:          "27058051152432574",
					CancelReason:     "商家取消订单",
					IsRefund:         0,
					OrderStatus:      0,
					OrderStatusChild: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.CancelOrder(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("CancelOrder() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

// 超过24小时订单（骑手取消了，用户并未收到货，订单已完成）需要退款
func TestOrderService_CompleteOrderRefund(t *testing.T) {
	//1、订单取消传子订单号，已经对应的订单状态
	client := oc.GetOrderServiceClient()
	params := &oc.CancelOrderRequest{
		OrderSn:          "", //子订单号
		CancelReason:     "用户要求取消订单",
		IsRefund:         0,
		OrderStatus:      30,
		OrderStatusChild: 30101,
	}
	res, err := client.RPC.CancelOrder(context.Background(), params)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(res)
	//2、订单取消成功后，调退款接口，需要查询退款单号状态
	//if res.Code == 200{
	//	refundOrderPay := &oc.RefundOrderPayRequest{
	//		RefundOrderSn:        "",//退款单号
	//		ResType:              "取消订单，自动发起退款申请",
	//		OperationType:        "取消订单自动发起退款申请",
	//		OperationUser:        "后台",
	//		Reason:               "",
	//		XXX_NoUnkeyedLiteral: struct{}{},
	//		XXX_unrecognized:     nil,
	//		XXX_sizecache:        0,
	//	}
	//	_, err = client.ROC.RefundOrderPay(context.Background(), refundOrderPay)
	//	if err != nil {
	//		fmt.Println(err)
	//	}
	//}
}

func TestOrderService_AccomplishOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.AccomplishOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: nil,
				params: &oc.AccomplishOrderRequest{
					OrderSn:     "4100000012569878",
					ConfirmTime: "2022-1-17 19:08:05",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.AccomplishOrder(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AccomplishOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AccomplishOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_DeliveryNode(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.DeliveryNodeRequest
	}
	var myParamStr = `{"delivery_id":100010000620291820,"order_sn":"9964128297101919","status":50,"courier_name":"周翔","courier_phone":"138****2233","cancel_reason":"","create_time":"","mt_peisong_id":"","predict_delivery_time":"","delivery_type":2,"store_master_id":1,"latitude":"22.528508","longitude":"114.027208","is_exception":1}`
	param := new(oc.DeliveryNodeRequest)
	_ = json.Unmarshal([]byte(myParamStr), param)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "配送回调",
			args: args{
				ctx:    nil,
				params: param,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.DeliveryNode(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeliveryNode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeliveryNode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_MtRePushThird(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.MtRePushThirdRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx: nil,
				params: &oc.MtRePushThirdRequest{
					OrderSn: "4100000010789069",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.MtRePushThird(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("MtRePushThird() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_ApplyOrderList(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.ApplyOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				params: &oc.ApplyOrderListRequest{
					ChannelId: 1,
					UserAgent: 3,
					Keywords:  "4000000000912118",
					MemberId:  "fd12a584a245ef4e",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.ApplyOrderList(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Error(err)
				return
			}
			if got.Code != 200 {
				t.Errorf(got.Message)
			}
			t.Log(kit.JsonEncodeBeuty(got.Details))
		})
	}
}

func TestOrderService_GetOneOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.GetOneOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GetOneOrderResponse
		wantErr bool
	}{
		{args: args{
			ctx: nil,
			in:  &oc.GetOneOrderRequest{OrderSn: "4100000003888680"},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetOneOrder(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOneOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOneOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_SavePerformance(t *testing.T) {
	tests := []struct {
		name    string
		args    *oc.SavePerformanceRequest
		want    *empty.Empty
		wantErr error
	}{
		//{
		//	name: "SavePerformance_T1",
		//	args: &oc.SavePerformanceRequest{
		//		OrderSn:   "",
		//		StaffId:   "",
		//		StaffName: "",
		//	},
		//	wantErr: errors.New("订单编号不能为空"),
		//},
		{
			name: "保存业绩分配",
			args: &oc.SavePerformanceRequest{
				OrderSn:      "4000000001232367",
				StaffId:      "117",
				StaffName:    "角远磊",
				OperatorName: "lvhb1",
				OperatorId:   "U_28XFC85",
			},
			wantErr: errors.New(""),
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			_, err := o.SavePerformance(context.Background(), tt.args)
			if err != nil && !assert.EqualError(t, err, tt.wantErr.Error()) {
				t.Errorf("OrderService.SavePerformance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

//func TestOrderService_AwenOrderBaseDetail(t *testing.T) {
//	type fields struct {
//		CommonService CommonService
//	}
//	type args struct {
//		ctx    context.Context
//		params *oc.AwenOrderBaseDetailRequest
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *oc.AwenOrderBaseDetailResponse
//		wantErr bool
//	}{
//		{name: "AwenOrderBaseDetail"}, // TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			o := &OrderService{
//				CommonService: tt.fields.CommonService,
//			}
//			params := new(oc.AwenOrderBaseDetailRequest)
//			params.Orderid = "a4b45b59daa240a6916eeedcaab884e1"
//
//			got, err := o.AwenOrderBaseDetail(tt.args.ctx, params)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("OrderService.AwenOrderBaseDetail() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("OrderService.AwenOrderBaseDetail() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestOrderService_AcceptOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.AcceptOrderRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{name: "TEST"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			params := new(oc.AcceptOrderRequest)
			params.OrderSn = "770671233880722196"

			got, err := o.AcceptOrder(tt.args.ctx, params)
			if err != nil {
				t.Errorf("OrderService.AwenOrderBaseDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}

}

func TestOrderService_UPDateElmOrderPushDelivery(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.UPDateElmOrderPushDeliveryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TEST"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			params := new(oc.UPDateElmOrderPushDeliveryRequest)
			params.Oldordersn = "2131387684915454173"

			got, err := o.UPDateElmOrderPushDelivery(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderService.AwenOrderBaseDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderService.AwenOrderBaseDetail() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_OrderPayNotify(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.OrderPayNotifyRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TEST"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			params := new(oc.OrderPayNotifyRequest)
			params.OrderSn = "9964128298268757"
			params.PayTime = "2023-09-20 15:11:09"
			params.PayAmount = 1
			params.PayMode = 2
			params.PaySn = "20230920151107349856814366728893"

			got, err := o.OrderPayNotify(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderService.OrderPayNotify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderService.OrderPayNotify() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestOrderService_AwenOrderBaseDetail(t *testing.T) {
//	type fields struct {
//		CommonService CommonService
//	}
//	type args struct {
//		ctx    context.Context
//		params *oc.AwenOrderBaseDetailRequest
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *oc.AwenOrderBaseDetailResponse
//		wantErr bool
//	}{
//		{name: "AwenOrderBaseDetail"}, // TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			o := &OrderService{
//				CommonService: tt.fields.CommonService,
//			}
//			params := new(oc.AwenOrderBaseDetailRequest)
//			params.Orderid = "2b9a9a73df574705a53c0792d321ce0b"
//			got, err := o.AwenOrderBaseDetail(tt.args.ctx, params)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("OrderService.AwenOrderBaseDetail() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("OrderService.AwenOrderBaseDetail() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestOrderService_PickingOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.PickingOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "TEST"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			params := new(oc.PickingOrderRequest)
			params.OrderSn = "1599883176898159"

			got, err := o.PickingOrder(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderService.AwenOrderBaseDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderService.AwenOrderBaseDetail() = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestOrderService_AwenOrderList(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.AwenOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.AwenOrderListResponse
		wantErr bool
	}{
		{
			name: "阿闻管家订单分页列表",
			fields: fields{
				CommonService: CommonService{},
			},
			args: args{
				ctx: context.Background(),
				//params: &oc.AwenAllOrderListRequest{
				//	Pageindex: 1,
				//	Pagesize:  10,
				//	//Keyword:"1600828116746943",
				//	Searchtype: 0,
				//	Starttime:  "2020-11-23 00:00:00",
				//	Endtime:    "2020-11-25 23:59:59",
				//	UserNo:     "U_21YWWUO",
				//},
			},
			want:    &oc.AwenOrderListResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//o := &OrderService{
			//	CommonService: tt.fields.CommonService,
			//}
			//got, err := o.AwenOrderList(tt.args.ctx, tt.args.params)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("AwenOrderList() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//for _, v := range got.Details {
			//	t.Log(v)
			//}
		})
	}
}

func TestOrderService_GetRefundGoods(t *testing.T) {
	kit.IsDebug = true
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		RefundSn    string
		cancelType  int
		hasRefundSn int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*oc.RefundGoodsOrder
	}{
		{
			name: "",
			args: args{
				RefundSn:    "50000130458",
				cancelType:  0,
				hasRefundSn: 1,
			},
			//0000130458,990671233880342043,990671233880342043,0,1
			//50000130455,990671233880337866,990671233880337866,0,1
			//50000010149,4100000013786045,4100000013786045,1,0
			fields: fields{
				CommonService: CommonService{
					session:   GetDBConn().NewSession(),
					orderMain: GetOrderMainByOrderSn("990671233880342043"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			defer o.session.Close()
			if got := o.GetRefundGoods(tt.args.RefundSn, tt.args.cancelType, tt.args.hasRefundSn); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRefundGoods() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_AfterApplyOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.AfterApplyOrderRequest
	}
	var details []*oc.RefundGoodsOrder
	details = append(details, &oc.RefundGoodsOrder{
		GoodsId:      "1122dfasd",
		Quantity:     5,
		RefundAmount: "25.00",
		OcId:         "9dba79060d5f4ea69944467256b9e7ab",
	})
	//details = append(details, &oc.RefundGoodsOrder{
	//	GoodsId: "df213213dd",
	//	Quantity: 1,
	//	RefundAmount: "2",
	//	OcId: "e76b1ebd5715462d9437e03d4bd130b7",
	//})
	var refunds []*oc.RefundPayOrder
	refunds = append(refunds, &oc.RefundPayOrder{
		PayTypeCode: "predeposit",
		Payment:     "25.00",
		PayTime:     "2020-11-06 15:17:04",
	})
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "售后订单申请",
			fields: fields{
				CommonService: CommonService{},
			},
			args: args{
				ctx: context.Background(),
				params: &oc.AfterApplyOrderRequest{
					RefundSn:          "336101201106152143",
					OrderSn:           "1604647000178586",
					CreateTime:        kit.GetTimeNow(),
					Status:            "waitAgree",
					RefundTypeSn:      "RefundAndGoods",
					ReasonCode:        "01",
					RefundRemark:      "32423",
					RefundType:        2,
					DiscountAmount:    "0.00",
					RefundReason:      "165416",
					RefundAmount:      "25.00",
					OrderSource:       1,
					RefundPayOrder:    refunds,
					RefundGoodsOrders: details,
				},
			},
			want:    &oc.BaseResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.AfterApplyOrder(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AfterApplyOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AfterApplyOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_MtDeliveryNode(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.DeliveryNodeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "美团专送更新配送状态"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			o.session = GetDBConn().NewSession()
			app := oc.DeliveryNodeRequest{}
			app.CourierName = "杨阳"
			app.DeliveryId = 9964128298783905
			app.CourierPhone = "13826142230"
			app.Status = 30
			app.MtPeisongId = "9964128298783905"
			app.OrderSn = "9964128298783905"

			got, err := o.MtDeliveryNode(tt.args.ctx, &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderService.MtDeliveryNode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderService.MtDeliveryNode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_OrderIsExist(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.OrderIsExistRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.OrderIsExistResponse
		wantErr bool
	}{
		{
			name: "OrderIsExist - 存在",
			args: args{
				ctx: context.Background(),
				in: &oc.OrderIsExistRequest{
					OrderId: "1611213971771122",
				},
			},
			want: &oc.OrderIsExistResponse{
				Code:    200,
				IsExist: true,
			},
		},
		{
			name: "OrderIsExist - 不存在",
			args: args{
				ctx: context.Background(),
				in: &oc.OrderIsExistRequest{
					OrderId: "12483715",
				},
			},
			want: &oc.OrderIsExistResponse{
				Code:    200,
				IsExist: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.OrderIsExist(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderIsExist() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderIsExist() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestOrderService_AwenOrderDeliveryDetail(t *testing.T) {
//	type args struct {
//		ctx    context.Context
//		params *oc.AwenOrderDeliveryDetailRequest
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *oc.AwenOrderDeliveryDetailResponse
//		wantErr bool
//	}{
//		{
//			name: "AwenOrderDeliveryDetail",
//			args: args{
//				ctx: nil,
//				params: &oc.AwenOrderDeliveryDetailRequest{
//					Orderid: "1",
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			o := &OrderService{}
//			got, err := o.AwenOrderDeliveryDetail(tt.args.ctx, tt.args.params)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("AwenOrderDeliveryDetail() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			for _, v := range got.Deliverynodes {
//				t.Log(v)
//			}
//		})
//	}
//}

func TestOrderService_MtAcceptOrder(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.MtAcceptOrderRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "美团后台接单推送",
			args: args{
				ctx: nil,
				params: &oc.MtAcceptOrderRequest{
					OrderSn: "27009910806125806",
					Status:  "4",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.MtAcceptOrder(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("MtAcceptOrder() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_PrintBookingOrder(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.PrintBookingOrderRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家打印订单",
			args: args{
				ctx: context.Background(),
				params: &oc.PrintBookingOrderRequest{
					OrderId: "9964128298350971",
					Type:    0,
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.PrintBookingOrder(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("PrintBookingOrder() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_AwenParentOrderBaseDetail(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenAllOrderBaseDetailRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "父订单详情",
			args: args{
				ctx: context.Background(),
				params: &oc.AwenAllOrderBaseDetailRequest{
					OrderId: "6149840",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			got, err := o.AwenParentOrderBaseDetail(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenParentOrderBaseDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_AwenParentOrderList(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenParentOrderListRequest
	}
	param := &oc.AwenParentOrderListRequest{
		SearchType:   0,
		Keyword:      "",
		TimeType:     0,
		StartTime:    "2022-02-07 00:00:00",
		EndTime:      "2022-12-01 00:00:00",
		ProductName:  "",
		ChannelId:    0,
		OrderStatus:  0,
		OrderType:    "",
		DeliveryType: 0,
		PayMode:      0,
		PaySn:        "",
		PageIndex:    1,
		PageSize:     5,
		Shopids:      nil,
		UserNo:       "",
		AppChannel:   0,
		Ip:           "",
		IpLocation:   "",
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家-父订单列表",
			args: args{
				ctx:    context.Background(),
				params: param,
			},
		},
		{
			args: args{
				ctx: context.Background(),
				params: &oc.AwenParentOrderListRequest{
					QuerySpecial: 1,
					TimeType:     1,
					//StartTime:    "2022-04-30 10:10:10",
					//EndTime:      "2022-05-09 10:10:10",
					OrderGroupActivityId: 461,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			got, err := o.AwenParentOrderList(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenParentOrderList() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got.Details))
		})
	}
}

func TestOrderService_AwenMaterOrderList(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenMaterOrderListRequest
	}
	param := &oc.AwenMaterOrderListRequest{
		SearchType:   0,
		Keyword:      "",
		StartTime:    "2022-02-07 00:00:00",
		EndTime:      "2022-12-01 23:59:59",
		ProductName:  "",
		ChannelId:    0,
		OrderStatus:  0,
		OrderType:    "",
		DeliveryType: 0,
		PayMode:      0,
		PageIndex:    1,
		PageSize:     5,
		Shopids:      nil,
		UserNo:       "",
		AppChannel:   0,
		IpLocation:   "",
	}

	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家-实物订单列表",
			args: args{
				ctx:    context.Background(),
				params: param,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			got, err := o.AwenMaterOrderList(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenMaterOrderList() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got.Details))
		})
	}
}

func TestOrderService_AwenMaterOrderBaseDetail(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenAllOrderBaseDetailRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家-实物订单详情-基础信息",
			args: args{
				ctx: context.Background(),
				params: &oc.AwenAllOrderBaseDetailRequest{
					OrderId: "12389769",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			got, err := o.AwenMaterOrderBaseDetail(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenMaterOrderBaseDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_AwenVirtualOrderList(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenVirtualOrderListRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家-虚拟订单列表",
			args: args{
				ctx: context.Background(),
				params: &oc.AwenVirtualOrderListRequest{
					SearchType: 1,
					Keyword:    "4000000000890727",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			got, err := o.AwenVirtualOrderList(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenVirtualOrderList() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_AwenVirtualOrderBaseDetail(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.AwenAllOrderBaseDetailRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻管家-虚拟订单详情-基础信息",
			args: args{
				ctx: context.Background(),
				params: &oc.AwenAllOrderBaseDetailRequest{
					OrderId: "875226",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			got, err := o.AwenVirtualOrderBaseDetail(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AwenVirtualOrderBaseDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestOrderService_GetOrderProducts(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.GetOneOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GetOrderProductResponse
		wantErr bool
	}{
		{
			name: "TestOrderService_GetOrderProducts",
			args: args{
				ctx: context.TODO(),
				in: &oc.GetOneOrderRequest{
					OrderSn: "1597476251909990",
				},
			},
		}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetOrderProducts(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderProducts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOrderProducts() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_GetOrderAppChannel(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.GetOrderAppChannelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GetOrderAppChannelRes
		wantErr bool
	}{
		{name: "", args: args{req: &oc.GetOrderAppChannelReq{OrderSn: "3000000019696801"}}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetOrderAppChannel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderAppChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOrderAppChannel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_RePushOms(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *oc.MtAddOrderResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "重新推送OMS", args: args{
			ctx: context.TODO(),
			params: &oc.MtAddOrderResponse{
				OrderSn: "4100000012390824",
				PaySn:   "20211026150436019515306928274304",
			}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.RePushOms(context.Background(), tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("RePushOms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RePushOms() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_QueryAppChannelByOrderSn(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.QueryAppChannelByOrderSnReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.QueryAppChannelByOrderSnRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.TODO(),
				req: &oc.QueryAppChannelByOrderSnReq{
					OrderSn: "4100000012837541",
				},
			},
			fields: fields{
				CommonService: CommonService{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.QueryAppChannelByOrderSn(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryAppChannelByOrderSn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryAppChannelByOrderSn() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_DigitalHealthOrderCheck(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		c      context.Context
		params *oc.DigitalHealthOrderCheckRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.DigitalHealthOrderCheckResponse
		wantErr bool
	}{
		{name: "检验互联网医疗订单是否能下单",
			args: args{
				c: context.Background(),
				params: &oc.DigitalHealthOrderCheckRequest{
					ConsultOrderSn: "1020220516000044",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.DigitalHealthOrderCheck(tt.args.c, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("DigitalHealthOrderCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestOrderService_OrderSetDelivery(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.OrderSetDeliveryRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "根据主订单发货",
			args: args{
				in: &oc.OrderSetDeliveryRequest{
					OrderSn:      "4100000014612306", //主单号
					ExpressCode:  "STO",
					ShippingCode: "75892310994070",
					CourierPhone: "",
					CourierName:  "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.OrderSetDelivery(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Errorf("OrderSetDelivery() error = %s", err.Error())
				return
			}
			if gotOut.Code != 200 {
				t.Errorf("OrderSetDelivery() error = %s", gotOut.Message)
				return
			}
		})
	}
}

func TestOrderService_ConfirmDeliveredOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.ConfirmDeliveredOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseResponse
		wantErr bool
	}{
		{
			name: "设置订单已发货",
			args: args{
				in: &oc.ConfirmDeliveredOrderRequest{
					OrderSn: "4100000014612306", //主单号
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.ConfirmDeliveredOrder(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Errorf("ConfirmDeliveredOrder() error = %s", err.Error())
				return
			}
			if gotOut.Code != 200 {
				t.Errorf("ConfirmDeliveredOrder() error = %s", gotOut.Message)
				return
			}
		})
	}
}

func TestOrderService_UpdateOrderSku(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.UpdateOrderSkuReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{in: &oc.UpdateOrderSkuReq{
				OrderSn:             "4100000017869296",
				OrderProductModelId: "1809551",
				SkuId:               "1030183001",
				ThirdSkuId:          "C00241ALFJ",
				UserNo:              "EHR_NU_2WR07ES",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.UpdateOrderSku(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
			if !tt.wantErr(t, err, fmt.Sprintf("UpdateOrderSku(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
		})
	}
}

func TestOrderService_PrescribeCheck(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.OrderPrescribeCheckReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.OrderPrescribeCheckRes
	}{
		{
			args: args{in: &oc.OrderPrescribeCheckReq{
				ScrmId:      "aaaa",
				FinanceCode: "bbbbb",
				Skus: []*oc.OrderPrescribeSkuNum{
					{
						SkuId: 1047205099,
						Num:   1,
					}, {
						SkuId: 1047203099,
						Num:   1,
					}, {
						SkuId: 1047198001,
						Num:   1,
					},
				},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.PrescribeCheck(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)

			if err != nil {
				t.Errorf(fmt.Sprintf("PrescribeCheck(%v, %v),%v", tt.args.in, gotOut, err))
			}
		})
	}
}

func TestOrderService_MytOrderList(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.MytOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.MytBaseDataResponse
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "查询订单列表"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			in := oc.MytOrderListRequest{}
			in.PageSize = 10
			in.Page = 1
			in.StartTime = 1738252800
			in.EndTime = 1741536000
			in.ShopId = "5030012"
			got, err := o.MytOrderList(context.Background(), &in)
			if !tt.wantErr(t, err, fmt.Sprintf("MytOrderList(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "MytOrderList(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

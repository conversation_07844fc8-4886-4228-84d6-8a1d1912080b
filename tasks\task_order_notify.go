package tasks

import (
	"time"

	"order-center/models"
	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

type TaskOrderNotify struct {
	services.BaseService
}

func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */1 * * * *", func() { //每一分钟执行一次
		service := TaskOrderNotify{}
		service.taskDealOrderPayNotify()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s *TaskOrderNotify) taskDealOrderPayNotify() {
	redisClient := services.GetRedisConn()

	if result := redisClient.SetNX("task:lock:syncordernotify", time.Now().Unix(), time.Minute*1); result.Err() != nil {
		glog.Error(result.Err())
		return
	} else if !result.Val() {
		return
	}

	engine := services.GetDBConn()

	var paynotifylist []models.OrderPayNotify
	engine.Table("order_pay_notify").Where("deal_status = 0 and deal_num < 5").Find(&paynotifylist)
	if len(paynotifylist) == 0 {
		return
	}

	for _, item := range paynotifylist {
		services.DealOrderPayNotify(&item)
	}
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: ctc/contentCenter.proto

package ctc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *ContentResponse) Reset() {
	*x = ContentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentResponse) ProtoMessage() {}

func (x *ContentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentResponse.ProtoReflect.Descriptor instead.
func (*ContentResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{0}
}

func (x *ContentResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ContentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 文章保存
type ArticleSaveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模板id
	TemplateId int32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 一级分类id
	CategoryFirst int32 `protobuf:"varint,2,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	// 二级分类id
	CategorySecond int32 `protobuf:"varint,3,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	// 三级分类id
	CategoryThird int32 `protobuf:"varint,4,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title"`
	// 内容
	Content string `protobuf:"bytes,8,opt,name=content,proto3" json:"content"`
	// 医生code，接口中doctor_code
	DoctorCode string `protobuf:"bytes,9,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 执业证书编号
	DoctorCertNo string `protobuf:"bytes,10,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 标签集合
	TagJson string `protobuf:"bytes,11,opt,name=tag_json,json=tagJson,proto3" json:"tag_json"`
	// 分发渠道，1-百度小程序
	DisChannel []int32 `protobuf:"varint,12,rep,packed,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	// 是否显示广告，1-显示，0-不显示
	IsShowAds int32 `protobuf:"varint,13,opt,name=is_show_ads,json=isShowAds,proto3" json:"is_show_ads"`
	// 在线问诊入口图片地址
	OnlineAskUrl string `protobuf:"bytes,14,opt,name=online_ask_url,json=onlineAskUrl,proto3" json:"online_ask_url"`
	// 文章类型：1-图文，2-视频
	ArticleType int32 `protobuf:"varint,16,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 文章id
	ArticleId int32 `protobuf:"varint,17,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 状态，1-保存发布，2-保存草稿
	Status int32 `protobuf:"varint,18,opt,name=status,proto3" json:"status"`
}

func (x *ArticleSaveRequest) Reset() {
	*x = ArticleSaveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleSaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleSaveRequest) ProtoMessage() {}

func (x *ArticleSaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleSaveRequest.ProtoReflect.Descriptor instead.
func (*ArticleSaveRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{1}
}

func (x *ArticleSaveRequest) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *ArticleSaveRequest) GetCategoryFirst() int32 {
	if x != nil {
		return x.CategoryFirst
	}
	return 0
}

func (x *ArticleSaveRequest) GetCategorySecond() int32 {
	if x != nil {
		return x.CategorySecond
	}
	return 0
}

func (x *ArticleSaveRequest) GetCategoryThird() int32 {
	if x != nil {
		return x.CategoryThird
	}
	return 0
}

func (x *ArticleSaveRequest) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *ArticleSaveRequest) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *ArticleSaveRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleSaveRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ArticleSaveRequest) GetDoctorCode() string {
	if x != nil {
		return x.DoctorCode
	}
	return ""
}

func (x *ArticleSaveRequest) GetDoctorCertNo() string {
	if x != nil {
		return x.DoctorCertNo
	}
	return ""
}

func (x *ArticleSaveRequest) GetTagJson() string {
	if x != nil {
		return x.TagJson
	}
	return ""
}

func (x *ArticleSaveRequest) GetDisChannel() []int32 {
	if x != nil {
		return x.DisChannel
	}
	return nil
}

func (x *ArticleSaveRequest) GetIsShowAds() int32 {
	if x != nil {
		return x.IsShowAds
	}
	return 0
}

func (x *ArticleSaveRequest) GetOnlineAskUrl() string {
	if x != nil {
		return x.OnlineAskUrl
	}
	return ""
}

func (x *ArticleSaveRequest) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

func (x *ArticleSaveRequest) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleSaveRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ArticleTagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ArticleTagData) Reset() {
	*x = ArticleTagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleTagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleTagData) ProtoMessage() {}

func (x *ArticleTagData) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleTagData.ProtoReflect.Descriptor instead.
func (*ArticleTagData) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{2}
}

// 文章列表-后台
type ArticleListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	DoctorName     string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	ArticleType    int32  `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	CategoryFirst  int32  `protobuf:"varint,4,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	CategorySecond int32  `protobuf:"varint,5,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	CategoryThird  int32  `protobuf:"varint,6,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	DisChannel     int32  `protobuf:"varint,7,opt,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	PageIndex      int32  `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize       int32  `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Status         int32  `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	// 标签数据过滤，json字符串 [{"name":"年龄","tags":"幼年"}]
	Tags string `protobuf:"bytes,11,opt,name=tags,proto3" json:"tags"`
	//随机值
	RandNum int32 `protobuf:"varint,12,opt,name=randNum,proto3" json:"randNum"`
}

func (x *ArticleListRequest) Reset() {
	*x = ArticleListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListRequest) ProtoMessage() {}

func (x *ArticleListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListRequest.ProtoReflect.Descriptor instead.
func (*ArticleListRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{3}
}

func (x *ArticleListRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleListRequest) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ArticleListRequest) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

func (x *ArticleListRequest) GetCategoryFirst() int32 {
	if x != nil {
		return x.CategoryFirst
	}
	return 0
}

func (x *ArticleListRequest) GetCategorySecond() int32 {
	if x != nil {
		return x.CategorySecond
	}
	return 0
}

func (x *ArticleListRequest) GetCategoryThird() int32 {
	if x != nil {
		return x.CategoryThird
	}
	return 0
}

func (x *ArticleListRequest) GetDisChannel() int32 {
	if x != nil {
		return x.DisChannel
	}
	return 0
}

func (x *ArticleListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ArticleListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArticleListRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ArticleListRequest) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *ArticleListRequest) GetRandNum() int32 {
	if x != nil {
		return x.RandNum
	}
	return 0
}

type ArticleListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message    string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data       []*ArticleListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	TotalCount int64              `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
}

func (x *ArticleListResponse) Reset() {
	*x = ArticleListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListResponse) ProtoMessage() {}

func (x *ArticleListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListResponse.ProtoReflect.Descriptor instead.
func (*ArticleListResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{4}
}

func (x *ArticleListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleListResponse) GetData() []*ArticleListData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArticleListResponse) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ArticleListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文章id
	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章分类，1-图文，2-视频
	ArticleType int32 `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 医生姓名
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 最后发布时间
	LastPublishTime string `protobuf:"bytes,6,opt,name=last_publish_time,json=lastPublishTime,proto3" json:"last_publish_time"`
	// 最后更新时间
	UpdatedAt string `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 最后操作人
	LastOperator string `protobuf:"bytes,8,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator"`
	// 文章状态，0-未发布，1-已发布，2-下架
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status"`
}

func (x *ArticleListData) Reset() {
	*x = ArticleListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListData) ProtoMessage() {}

func (x *ArticleListData) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListData.ProtoReflect.Descriptor instead.
func (*ArticleListData) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{5}
}

func (x *ArticleListData) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleListData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleListData) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

func (x *ArticleListData) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ArticleListData) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ArticleListData) GetLastPublishTime() string {
	if x != nil {
		return x.LastPublishTime
	}
	return ""
}

func (x *ArticleListData) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *ArticleListData) GetLastOperator() string {
	if x != nil {
		return x.LastOperator
	}
	return ""
}

func (x *ArticleListData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ArticleSearchConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from"`
}

func (x *ArticleSearchConditionRequest) Reset() {
	*x = ArticleSearchConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleSearchConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleSearchConditionRequest) ProtoMessage() {}

func (x *ArticleSearchConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleSearchConditionRequest.ProtoReflect.Descriptor instead.
func (*ArticleSearchConditionRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{6}
}

func (x *ArticleSearchConditionRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type ArticleSearchConditionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message         string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	ArticleType     []*ArticleArr           `protobuf:"bytes,3,rep,name=article_type,json=articleType,proto3" json:"article_type"`
	DisChannel      []*ArticleArr           `protobuf:"bytes,4,rep,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	Status          []*ArticleArr           `protobuf:"bytes,5,rep,name=status,proto3" json:"status"`
	ArticleCategory []*FirstArticleCategory `protobuf:"bytes,6,rep,name=article_category,json=articleCategory,proto3" json:"article_category"`
}

func (x *ArticleSearchConditionResponse) Reset() {
	*x = ArticleSearchConditionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleSearchConditionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleSearchConditionResponse) ProtoMessage() {}

func (x *ArticleSearchConditionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleSearchConditionResponse.ProtoReflect.Descriptor instead.
func (*ArticleSearchConditionResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{7}
}

func (x *ArticleSearchConditionResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleSearchConditionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleSearchConditionResponse) GetArticleType() []*ArticleArr {
	if x != nil {
		return x.ArticleType
	}
	return nil
}

func (x *ArticleSearchConditionResponse) GetDisChannel() []*ArticleArr {
	if x != nil {
		return x.DisChannel
	}
	return nil
}

func (x *ArticleSearchConditionResponse) GetStatus() []*ArticleArr {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ArticleSearchConditionResponse) GetArticleCategory() []*FirstArticleCategory {
	if x != nil {
		return x.ArticleCategory
	}
	return nil
}

type ArticleArr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
}

func (x *ArticleArr) Reset() {
	*x = ArticleArr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleArr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleArr) ProtoMessage() {}

func (x *ArticleArr) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleArr.ProtoReflect.Descriptor instead.
func (*ArticleArr) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{8}
}

func (x *ArticleArr) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArticleArr) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 小程序列表
type ArticleListMiniResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message    string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data       []*ArticleListMiniData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	TotalCount int64                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
}

func (x *ArticleListMiniResponse) Reset() {
	*x = ArticleListMiniResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleListMiniResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListMiniResponse) ProtoMessage() {}

func (x *ArticleListMiniResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListMiniResponse.ProtoReflect.Descriptor instead.
func (*ArticleListMiniResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{9}
}

func (x *ArticleListMiniResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleListMiniResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleListMiniResponse) GetData() []*ArticleListMiniData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArticleListMiniResponse) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ArticleListMiniData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章分类，1-图文，2-视频
	ArticleType int32 `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 医生姓名
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 医生职称
	DoctorPositionName string `protobuf:"bytes,5,opt,name=doctor_position_name,json=doctorPositionName,proto3" json:"doctor_position_name"`
	// 医生所属医院
	Hospital string `protobuf:"bytes,6,opt,name=hospital,proto3" json:"hospital"`
	// 医生执业证书编号
	DoctorCertNo string `protobuf:"bytes,7,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,8,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,9,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 医生头像
	HeadImg string `protobuf:"bytes,10,opt,name=head_img,json=headImg,proto3" json:"head_img"`
	// 最后发布日期
	PublishTime string `protobuf:"bytes,11,opt,name=publish_time,json=publishTime,proto3" json:"publish_time"`
}

func (x *ArticleListMiniData) Reset() {
	*x = ArticleListMiniData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleListMiniData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleListMiniData) ProtoMessage() {}

func (x *ArticleListMiniData) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleListMiniData.ProtoReflect.Descriptor instead.
func (*ArticleListMiniData) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{10}
}

func (x *ArticleListMiniData) GetArticleId() int64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleListMiniData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleListMiniData) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

func (x *ArticleListMiniData) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ArticleListMiniData) GetDoctorPositionName() string {
	if x != nil {
		return x.DoctorPositionName
	}
	return ""
}

func (x *ArticleListMiniData) GetHospital() string {
	if x != nil {
		return x.Hospital
	}
	return ""
}

func (x *ArticleListMiniData) GetDoctorCertNo() string {
	if x != nil {
		return x.DoctorCertNo
	}
	return ""
}

func (x *ArticleListMiniData) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *ArticleListMiniData) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *ArticleListMiniData) GetHeadImg() string {
	if x != nil {
		return x.HeadImg
	}
	return ""
}

func (x *ArticleListMiniData) GetPublishTime() string {
	if x != nil {
		return x.PublishTime
	}
	return ""
}

// 文章详情
type ArticleDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	From      string `protobuf:"bytes,2,opt,name=from,proto3" json:"from"`
}

func (x *ArticleDetailRequest) Reset() {
	*x = ArticleDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetailRequest) ProtoMessage() {}

func (x *ArticleDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetailRequest.ProtoReflect.Descriptor instead.
func (*ArticleDetailRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{11}
}

func (x *ArticleDetailRequest) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleDetailRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type ArticleDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文章详情信息
	Detail *ArticleDetail `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
}

func (x *ArticleDetailResponse) Reset() {
	*x = ArticleDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetailResponse) ProtoMessage() {}

func (x *ArticleDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetailResponse.ProtoReflect.Descriptor instead.
func (*ArticleDetailResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{12}
}

func (x *ArticleDetailResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleDetailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleDetailResponse) GetDetail() *ArticleDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type ArticleDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模板id
	TemplateId int32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 一级分类id
	CategoryFirst int32 `protobuf:"varint,2,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	// 二级分类id
	CategorySecond int32 `protobuf:"varint,3,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	// 三级分类id
	CategoryThird int32 `protobuf:"varint,4,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title"`
	// 内容
	Content string `protobuf:"bytes,8,opt,name=content,proto3" json:"content"`
	// 医生code，接口中doctor_code
	DoctorCode string `protobuf:"bytes,9,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 执业证书编号
	DoctorCertNo string `protobuf:"bytes,10,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 标签集合
	TagJson string `protobuf:"bytes,11,opt,name=tag_json,json=tagJson,proto3" json:"tag_json"`
	// 分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app
	DisChannel    int32   `protobuf:"varint,12,opt,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	DisChannelArr []int32 `protobuf:"varint,24,rep,packed,name=dis_channel_arr,json=disChannelArr,proto3" json:"dis_channel_arr"`
	// 是否显示广告，1-显示，0-不显示
	IsShowAds int32 `protobuf:"varint,13,opt,name=is_show_ads,json=isShowAds,proto3" json:"is_show_ads"`
	// 在线问诊入口图片地址
	OnlineAskUrl string `protobuf:"bytes,14,opt,name=online_ask_url,json=onlineAskUrl,proto3" json:"online_ask_url"`
	// 文章类型：1-图文，2-视频
	ArticleType int32 `protobuf:"varint,16,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 文章id
	ArticleId int32 `protobuf:"varint,17,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 最后更新时间
	UpdatedAt string `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	//医生名称
	DoctorName string `protobuf:"bytes,20,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,21,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生头像
	DoctorImg string `protobuf:"bytes,22,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院名称
	HospitalName string `protobuf:"bytes,23,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
}

func (x *ArticleDetail) Reset() {
	*x = ArticleDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetail) ProtoMessage() {}

func (x *ArticleDetail) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetail.ProtoReflect.Descriptor instead.
func (*ArticleDetail) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{13}
}

func (x *ArticleDetail) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *ArticleDetail) GetCategoryFirst() int32 {
	if x != nil {
		return x.CategoryFirst
	}
	return 0
}

func (x *ArticleDetail) GetCategorySecond() int32 {
	if x != nil {
		return x.CategorySecond
	}
	return 0
}

func (x *ArticleDetail) GetCategoryThird() int32 {
	if x != nil {
		return x.CategoryThird
	}
	return 0
}

func (x *ArticleDetail) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *ArticleDetail) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *ArticleDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleDetail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ArticleDetail) GetDoctorCode() string {
	if x != nil {
		return x.DoctorCode
	}
	return ""
}

func (x *ArticleDetail) GetDoctorCertNo() string {
	if x != nil {
		return x.DoctorCertNo
	}
	return ""
}

func (x *ArticleDetail) GetTagJson() string {
	if x != nil {
		return x.TagJson
	}
	return ""
}

func (x *ArticleDetail) GetDisChannel() int32 {
	if x != nil {
		return x.DisChannel
	}
	return 0
}

func (x *ArticleDetail) GetDisChannelArr() []int32 {
	if x != nil {
		return x.DisChannelArr
	}
	return nil
}

func (x *ArticleDetail) GetIsShowAds() int32 {
	if x != nil {
		return x.IsShowAds
	}
	return 0
}

func (x *ArticleDetail) GetOnlineAskUrl() string {
	if x != nil {
		return x.OnlineAskUrl
	}
	return ""
}

func (x *ArticleDetail) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

func (x *ArticleDetail) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleDetail) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *ArticleDetail) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ArticleDetail) GetDoctorLevel() string {
	if x != nil {
		return x.DoctorLevel
	}
	return ""
}

func (x *ArticleDetail) GetDoctorImg() string {
	if x != nil {
		return x.DoctorImg
	}
	return ""
}

func (x *ArticleDetail) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

// 文章发布、下架
type ArticleStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	Status    int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
}

func (x *ArticleStatusRequest) Reset() {
	*x = ArticleStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleStatusRequest) ProtoMessage() {}

func (x *ArticleStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleStatusRequest.ProtoReflect.Descriptor instead.
func (*ArticleStatusRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{14}
}

func (x *ArticleStatusRequest) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleStatusRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 文章操作历史查询
type ArticleEditHistoryListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文章id
	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 创建人id
	CreateId string `protobuf:"bytes,3,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 页
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 页大小
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
}

func (x *ArticleEditHistoryListRequest) Reset() {
	*x = ArticleEditHistoryListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleEditHistoryListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleEditHistoryListRequest) ProtoMessage() {}

func (x *ArticleEditHistoryListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleEditHistoryListRequest.ProtoReflect.Descriptor instead.
func (*ArticleEditHistoryListRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{15}
}

func (x *ArticleEditHistoryListRequest) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleEditHistoryListRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ArticleEditHistoryListRequest) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *ArticleEditHistoryListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ArticleEditHistoryListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ArticleEditHistoryListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ArticleEditRecord `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总条数
	TotalCount int32 `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
}

func (x *ArticleEditHistoryListResponse) Reset() {
	*x = ArticleEditHistoryListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleEditHistoryListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleEditHistoryListResponse) ProtoMessage() {}

func (x *ArticleEditHistoryListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleEditHistoryListResponse.ProtoReflect.Descriptor instead.
func (*ArticleEditHistoryListResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{16}
}

func (x *ArticleEditHistoryListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleEditHistoryListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleEditHistoryListResponse) GetData() []*ArticleEditRecord {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArticleEditHistoryListResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ArticleEditRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 文章id
	ArticleId int32 `protobuf:"varint,2,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 操作内容
	OperationData string `protobuf:"bytes,4,opt,name=operation_data,json=operationData,proto3" json:"operation_data"`
	// 操作人编号
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 操作人名称
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 操作时间
	CreatedAt string `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *ArticleEditRecord) Reset() {
	*x = ArticleEditRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleEditRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleEditRecord) ProtoMessage() {}

func (x *ArticleEditRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleEditRecord.ProtoReflect.Descriptor instead.
func (*ArticleEditRecord) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{17}
}

func (x *ArticleEditRecord) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArticleEditRecord) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleEditRecord) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ArticleEditRecord) GetOperationData() string {
	if x != nil {
		return x.OperationData
	}
	return ""
}

func (x *ArticleEditRecord) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *ArticleEditRecord) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *ArticleEditRecord) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// 文章推荐
type ArticleRecommendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
}

func (x *ArticleRecommendRequest) Reset() {
	*x = ArticleRecommendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleRecommendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleRecommendRequest) ProtoMessage() {}

func (x *ArticleRecommendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleRecommendRequest.ProtoReflect.Descriptor instead.
func (*ArticleRecommendRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{18}
}

func (x *ArticleRecommendRequest) GetArticleId() int64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type ArticleRecommendResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总数
	TotalCount int32               `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	Data       []*ArticleRecommend `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
}

func (x *ArticleRecommendResponse) Reset() {
	*x = ArticleRecommendResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleRecommendResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleRecommendResponse) ProtoMessage() {}

func (x *ArticleRecommendResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleRecommendResponse.ProtoReflect.Descriptor instead.
func (*ArticleRecommendResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{19}
}

func (x *ArticleRecommendResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleRecommendResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleRecommendResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ArticleRecommendResponse) GetData() []*ArticleRecommend {
	if x != nil {
		return x.Data
	}
	return nil
}

type ArticleRecommend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文章id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 文章标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章封面
	Cover string `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover"`
	// 医生编码
	DoctorCode string `protobuf:"bytes,4,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 医生名称
	DoctorName string `protobuf:"bytes,5,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 证书
	Certificate string `protobuf:"bytes,6,opt,name=certificate,proto3" json:"certificate"`
	// 医院名称
	HospitalName string `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	// 医生职称
	DoctorLevel string `protobuf:"bytes,8,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	// 医生头像
	DoctorImg string `protobuf:"bytes,9,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	// 文章类型 1-图文，2-视频
	ArticleType int32 `protobuf:"varint,10,opt,name=article_type,json=articleType,proto3" json:"article_type"`
}

func (x *ArticleRecommend) Reset() {
	*x = ArticleRecommend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleRecommend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleRecommend) ProtoMessage() {}

func (x *ArticleRecommend) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleRecommend.ProtoReflect.Descriptor instead.
func (*ArticleRecommend) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{20}
}

func (x *ArticleRecommend) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArticleRecommend) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleRecommend) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *ArticleRecommend) GetDoctorCode() string {
	if x != nil {
		return x.DoctorCode
	}
	return ""
}

func (x *ArticleRecommend) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ArticleRecommend) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *ArticleRecommend) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

func (x *ArticleRecommend) GetDoctorLevel() string {
	if x != nil {
		return x.DoctorLevel
	}
	return ""
}

func (x *ArticleRecommend) GetDoctorImg() string {
	if x != nil {
		return x.DoctorImg
	}
	return ""
}

func (x *ArticleRecommend) GetArticleType() int32 {
	if x != nil {
		return x.ArticleType
	}
	return 0
}

// 类目新增编辑
type ArticleCategorySaveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类目id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 类目名称
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// 上级id
	ParentId int32 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	// 是否可用 0 不可 , 1可用
	Valid int32 `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	// 创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 创建人名字
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 类目层级 1 一级 ， 2 二级 ， 3 三级
	Level int32 `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
}

func (x *ArticleCategorySaveRequest) Reset() {
	*x = ArticleCategorySaveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategorySaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategorySaveRequest) ProtoMessage() {}

func (x *ArticleCategorySaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategorySaveRequest.ProtoReflect.Descriptor instead.
func (*ArticleCategorySaveRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{21}
}

func (x *ArticleCategorySaveRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArticleCategorySaveRequest) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *ArticleCategorySaveRequest) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ArticleCategorySaveRequest) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *ArticleCategorySaveRequest) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *ArticleCategorySaveRequest) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *ArticleCategorySaveRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type ArticleCategorySaveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    int32  `protobuf:"varint,3,opt,name=data,proto3" json:"data"`
}

func (x *ArticleCategorySaveResponse) Reset() {
	*x = ArticleCategorySaveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategorySaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategorySaveResponse) ProtoMessage() {}

func (x *ArticleCategorySaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategorySaveResponse.ProtoReflect.Descriptor instead.
func (*ArticleCategorySaveResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{22}
}

func (x *ArticleCategorySaveResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleCategorySaveResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleCategorySaveResponse) GetData() int32 {
	if x != nil {
		return x.Data
	}
	return 0
}

// 类目删除
type ArticleCategoryDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类目id
	CategoryId int32 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	Level      int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level"`
}

func (x *ArticleCategoryDeleteRequest) Reset() {
	*x = ArticleCategoryDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategoryDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategoryDeleteRequest) ProtoMessage() {}

func (x *ArticleCategoryDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategoryDeleteRequest.ProtoReflect.Descriptor instead.
func (*ArticleCategoryDeleteRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{23}
}

func (x *ArticleCategoryDeleteRequest) GetCategoryId() int32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ArticleCategoryDeleteRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type ArticleCategoryDeleteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *ArticleCategoryDeleteResponse) Reset() {
	*x = ArticleCategoryDeleteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategoryDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategoryDeleteResponse) ProtoMessage() {}

func (x *ArticleCategoryDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategoryDeleteResponse.ProtoReflect.Descriptor instead.
func (*ArticleCategoryDeleteResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{24}
}

func (x *ArticleCategoryDeleteResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleCategoryDeleteResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 类目查询
type ArticleCategoryListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ArticleCategoryListRequest) Reset() {
	*x = ArticleCategoryListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategoryListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategoryListRequest) ProtoMessage() {}

func (x *ArticleCategoryListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategoryListRequest.ProtoReflect.Descriptor instead.
func (*ArticleCategoryListRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{25}
}

type ArticleCategoryListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*FirstArticleCategory `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

func (x *ArticleCategoryListResponse) Reset() {
	*x = ArticleCategoryListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCategoryListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCategoryListResponse) ProtoMessage() {}

func (x *ArticleCategoryListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCategoryListResponse.ProtoReflect.Descriptor instead.
func (*ArticleCategoryListResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{26}
}

func (x *ArticleCategoryListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleCategoryListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleCategoryListResponse) GetData() []*FirstArticleCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

// 一级类目
type FirstArticleCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类目id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 类目名称
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// 上级id
	ParentId int32 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	// 是否可用
	Valid int32 `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	// 创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 创建人姓名
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 类目层级
	Level int32 `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	// 子类目
	Child []*SecondArticleCategory `protobuf:"bytes,8,rep,name=child,proto3" json:"child"`
}

func (x *FirstArticleCategory) Reset() {
	*x = FirstArticleCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FirstArticleCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirstArticleCategory) ProtoMessage() {}

func (x *FirstArticleCategory) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirstArticleCategory.ProtoReflect.Descriptor instead.
func (*FirstArticleCategory) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{27}
}

func (x *FirstArticleCategory) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FirstArticleCategory) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *FirstArticleCategory) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *FirstArticleCategory) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *FirstArticleCategory) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *FirstArticleCategory) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *FirstArticleCategory) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *FirstArticleCategory) GetChild() []*SecondArticleCategory {
	if x != nil {
		return x.Child
	}
	return nil
}

// 二级类目
type SecondArticleCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32                   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName string                  `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId     int32                   `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid        int32                   `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId     string                  `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName   string                  `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level        int32                   `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	Child        []*ThirdArticleCategory `protobuf:"bytes,8,rep,name=child,proto3" json:"child"`
}

func (x *SecondArticleCategory) Reset() {
	*x = SecondArticleCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecondArticleCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecondArticleCategory) ProtoMessage() {}

func (x *SecondArticleCategory) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecondArticleCategory.ProtoReflect.Descriptor instead.
func (*SecondArticleCategory) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{28}
}

func (x *SecondArticleCategory) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SecondArticleCategory) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *SecondArticleCategory) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *SecondArticleCategory) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *SecondArticleCategory) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *SecondArticleCategory) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *SecondArticleCategory) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *SecondArticleCategory) GetChild() []*ThirdArticleCategory {
	if x != nil {
		return x.Child
	}
	return nil
}

// 三级类目
type ThirdArticleCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId     int32  `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid        int32  `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId     string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName   string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level        int32  `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
}

func (x *ThirdArticleCategory) Reset() {
	*x = ThirdArticleCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdArticleCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdArticleCategory) ProtoMessage() {}

func (x *ThirdArticleCategory) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdArticleCategory.ProtoReflect.Descriptor instead.
func (*ThirdArticleCategory) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{29}
}

func (x *ThirdArticleCategory) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ThirdArticleCategory) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *ThirdArticleCategory) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ThirdArticleCategory) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *ThirdArticleCategory) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *ThirdArticleCategory) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *ThirdArticleCategory) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type QueryDoctorListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//关键字搜索，现只支持医生名称
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword"`
}

func (x *QueryDoctorListRequest) Reset() {
	*x = QueryDoctorListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDoctorListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDoctorListRequest) ProtoMessage() {}

func (x *QueryDoctorListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDoctorListRequest.ProtoReflect.Descriptor instead.
func (*QueryDoctorListRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{30}
}

func (x *QueryDoctorListRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type QueryDoctorListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ScrmDoctor `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

func (x *QueryDoctorListResponse) Reset() {
	*x = QueryDoctorListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDoctorListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDoctorListResponse) ProtoMessage() {}

func (x *QueryDoctorListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDoctorListResponse.ProtoReflect.Descriptor instead.
func (*QueryDoctorListResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{31}
}

func (x *QueryDoctorListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QueryDoctorListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QueryDoctorListResponse) GetData() []*ScrmDoctor {
	if x != nil {
		return x.Data
	}
	return nil
}

type ScrmDoctor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生名称
	DoctorName string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,3,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生性别
	DoctorSex string `protobuf:"bytes,4,opt,name=doctor_sex,json=doctorSex,proto3" json:"doctor_sex"`
	//医生头像
	DoctorImg string `protobuf:"bytes,5,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院编号
	HospitalCode string `protobuf:"bytes,6,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//医院名称
	HospitalName string `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
}

func (x *ScrmDoctor) Reset() {
	*x = ScrmDoctor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrmDoctor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrmDoctor) ProtoMessage() {}

func (x *ScrmDoctor) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrmDoctor.ProtoReflect.Descriptor instead.
func (*ScrmDoctor) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{32}
}

func (x *ScrmDoctor) GetDoctorCode() string {
	if x != nil {
		return x.DoctorCode
	}
	return ""
}

func (x *ScrmDoctor) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *ScrmDoctor) GetDoctorLevel() string {
	if x != nil {
		return x.DoctorLevel
	}
	return ""
}

func (x *ScrmDoctor) GetDoctorSex() string {
	if x != nil {
		return x.DoctorSex
	}
	return ""
}

func (x *ScrmDoctor) GetDoctorImg() string {
	if x != nil {
		return x.DoctorImg
	}
	return ""
}

func (x *ScrmDoctor) GetHospitalCode() string {
	if x != nil {
		return x.HospitalCode
	}
	return ""
}

func (x *ScrmDoctor) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

type ArticleCensusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	//结束时间
	EndTime string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	//页码
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页条数，默认20
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//关键字 现支持文章标题
	Keyword string `protobuf:"bytes,6,opt,name=keyword,proto3" json:"keyword"`
}

func (x *ArticleCensusRequest) Reset() {
	*x = ArticleCensusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCensusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCensusRequest) ProtoMessage() {}

func (x *ArticleCensusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCensusRequest.ProtoReflect.Descriptor instead.
func (*ArticleCensusRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{33}
}

func (x *ArticleCensusRequest) GetBeginTime() string {
	if x != nil {
		return x.BeginTime
	}
	return ""
}

func (x *ArticleCensusRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ArticleCensusRequest) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ArticleCensusRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ArticleCensusRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArticleCensusRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type ArticleCensusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//总条数
	TotalCount int32 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	//文章访问统计
	Data []*ArticleCensus `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
}

func (x *ArticleCensusResponse) Reset() {
	*x = ArticleCensusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCensusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCensusResponse) ProtoMessage() {}

func (x *ArticleCensusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCensusResponse.ProtoReflect.Descriptor instead.
func (*ArticleCensusResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{34}
}

func (x *ArticleCensusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleCensusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleCensusResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ArticleCensusResponse) GetData() []*ArticleCensus {
	if x != nil {
		return x.Data
	}
	return nil
}

type ArticleCensus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	//文章标题
	ArticleTitle string `protobuf:"bytes,2,opt,name=article_title,json=articleTitle,proto3" json:"article_title"`
	//独立访问量
	UniqueVisitor int32 `protobuf:"varint,3,opt,name=unique_visitor,json=uniqueVisitor,proto3" json:"unique_visitor"`
	//页面访问量
	PageView int32 `protobuf:"varint,4,opt,name=page_view,json=pageView,proto3" json:"page_view"`
	//视频播放量
	VideoView int32 `protobuf:"varint,5,opt,name=video_view,json=videoView,proto3" json:"video_view"`
}

func (x *ArticleCensus) Reset() {
	*x = ArticleCensus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleCensus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleCensus) ProtoMessage() {}

func (x *ArticleCensus) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleCensus.ProtoReflect.Descriptor instead.
func (*ArticleCensus) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{35}
}

func (x *ArticleCensus) GetArticleId() int64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleCensus) GetArticleTitle() string {
	if x != nil {
		return x.ArticleTitle
	}
	return ""
}

func (x *ArticleCensus) GetUniqueVisitor() int32 {
	if x != nil {
		return x.UniqueVisitor
	}
	return 0
}

func (x *ArticleCensus) GetPageView() int32 {
	if x != nil {
		return x.PageView
	}
	return 0
}

func (x *ArticleCensus) GetVideoView() int32 {
	if x != nil {
		return x.VideoView
	}
	return 0
}

type BoehringereCensusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	//结束时间
	EndTime string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
}

func (x *BoehringereCensusRequest) Reset() {
	*x = BoehringereCensusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoehringereCensusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoehringereCensusRequest) ProtoMessage() {}

func (x *BoehringereCensusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoehringereCensusRequest.ProtoReflect.Descriptor instead.
func (*BoehringereCensusRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{36}
}

func (x *BoehringereCensusRequest) GetBeginTime() string {
	if x != nil {
		return x.BeginTime
	}
	return ""
}

func (x *BoehringereCensusRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type BoehringereCensusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//文章访问统计
	Data []*BoehringereCensus `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

func (x *BoehringereCensusResponse) Reset() {
	*x = BoehringereCensusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoehringereCensusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoehringereCensusResponse) ProtoMessage() {}

func (x *BoehringereCensusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoehringereCensusResponse.ProtoReflect.Descriptor instead.
func (*BoehringereCensusResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{37}
}

func (x *BoehringereCensusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BoehringereCensusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BoehringereCensusResponse) GetData() []*BoehringereCensus {
	if x != nil {
		return x.Data
	}
	return nil
}

type BoehringereCensus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//类型子级（0为标题，1为内容一，以此类推）
	TypeChild int32 `protobuf:"varint,2,opt,name=type_child,json=typeChild,proto3" json:"type_child"`
	//访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	//点击量
	TotalCount int32 `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
}

func (x *BoehringereCensus) Reset() {
	*x = BoehringereCensus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoehringereCensus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoehringereCensus) ProtoMessage() {}

func (x *BoehringereCensus) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoehringereCensus.ProtoReflect.Descriptor instead.
func (*BoehringereCensus) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{38}
}

func (x *BoehringereCensus) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BoehringereCensus) GetTypeChild() int32 {
	if x != nil {
		return x.TypeChild
	}
	return 0
}

func (x *BoehringereCensus) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BoehringereCensus) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type AddVisitRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//访问内容id（type=1时为文章id）
	ContentId int32 `protobuf:"varint,1,opt,name=content_id,json=contentId,proto3" json:"content_id"`
	// 类型 1：文章 2：视频
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	// 用户编号或openId
	UserNo string `protobuf:"bytes,4,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// IP
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip"`
}

func (x *AddVisitRecordRequest) Reset() {
	*x = AddVisitRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVisitRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVisitRecordRequest) ProtoMessage() {}

func (x *AddVisitRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVisitRecordRequest.ProtoReflect.Descriptor instead.
func (*AddVisitRecordRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{39}
}

func (x *AddVisitRecordRequest) GetContentId() int32 {
	if x != nil {
		return x.ContentId
	}
	return 0
}

func (x *AddVisitRecordRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddVisitRecordRequest) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *AddVisitRecordRequest) GetUserNo() string {
	if x != nil {
		return x.UserNo
	}
	return ""
}

func (x *AddVisitRecordRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type BoehringereChickRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 类型子级（0为标题，1为内容一，以此类推）
	TypeChild int32 `protobuf:"varint,2,opt,name=type_child,json=typeChild,proto3" json:"type_child"`
	// 用户编号
	UserNo string `protobuf:"bytes,3,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// IP
	Ip string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
}

func (x *BoehringereChickRecord) Reset() {
	*x = BoehringereChickRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoehringereChickRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoehringereChickRecord) ProtoMessage() {}

func (x *BoehringereChickRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoehringereChickRecord.ProtoReflect.Descriptor instead.
func (*BoehringereChickRecord) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{40}
}

func (x *BoehringereChickRecord) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BoehringereChickRecord) GetTypeChild() int32 {
	if x != nil {
		return x.TypeChild
	}
	return 0
}

func (x *BoehringereChickRecord) GetUserNo() string {
	if x != nil {
		return x.UserNo
	}
	return ""
}

func (x *BoehringereChickRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type CategoryBarRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//0 默认查一级分类 1 二级分类
	Type int32  `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	Tags string `protobuf:"bytes,2,opt,name=tags,proto3" json:"tags"`
}

func (x *CategoryBarRequest) Reset() {
	*x = CategoryBarRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryBarRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryBarRequest) ProtoMessage() {}

func (x *CategoryBarRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryBarRequest.ProtoReflect.Descriptor instead.
func (*CategoryBarRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{41}
}

func (x *CategoryBarRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CategoryBarRequest) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

type CategoryBarResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*Category `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

func (x *CategoryBarResponse) Reset() {
	*x = CategoryBarResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryBarResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryBarResponse) ProtoMessage() {}

func (x *CategoryBarResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryBarResponse.ProtoReflect.Descriptor instead.
func (*CategoryBarResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{42}
}

func (x *CategoryBarResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CategoryBarResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CategoryBarResponse) GetData() []*Category {
	if x != nil {
		return x.Data
	}
	return nil
}

type Category struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId     int32  `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid        int32  `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId     string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName   string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level        int32  `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
}

func (x *Category) Reset() {
	*x = Category{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{43}
}

func (x *Category) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Category) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *Category) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *Category) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *Category) GetCreateId() string {
	if x != nil {
		return x.CreateId
	}
	return ""
}

func (x *Category) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *Category) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{44}
}

type ArticleTagGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签组名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 多个标签，用逗号分割
	Tags string `protobuf:"bytes,2,opt,name=tags,proto3" json:"tags"`
}

func (x *ArticleTagGroup) Reset() {
	*x = ArticleTagGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleTagGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleTagGroup) ProtoMessage() {}

func (x *ArticleTagGroup) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleTagGroup.ProtoReflect.Descriptor instead.
func (*ArticleTagGroup) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{45}
}

func (x *ArticleTagGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ArticleTagGroup) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

type ArticleMiniFilterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 200正常，400错误
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 标签组数据
	TagGroups []*ArticleTagGroup `protobuf:"bytes,3,rep,name=tag_groups,json=tagGroups,proto3" json:"tag_groups"`
}

func (x *ArticleMiniFilterResponse) Reset() {
	*x = ArticleMiniFilterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleMiniFilterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleMiniFilterResponse) ProtoMessage() {}

func (x *ArticleMiniFilterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleMiniFilterResponse.ProtoReflect.Descriptor instead.
func (*ArticleMiniFilterResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{46}
}

func (x *ArticleMiniFilterResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ArticleMiniFilterResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ArticleMiniFilterResponse) GetTagGroups() []*ArticleTagGroup {
	if x != nil {
		return x.TagGroups
	}
	return nil
}

type GetStatisticsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//文章条数
	ArticleCount int32 `protobuf:"varint,1,opt,name=articleCount,proto3" json:"articleCount"`
	//类目条数
	CategoryCount int32 `protobuf:"varint,2,opt,name=categoryCount,proto3" json:"categoryCount"`
}

func (x *GetStatisticsResponse) Reset() {
	*x = GetStatisticsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatisticsResponse) ProtoMessage() {}

func (x *GetStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatisticsResponse.ProtoReflect.Descriptor instead.
func (*GetStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{47}
}

func (x *GetStatisticsResponse) GetArticleCount() int32 {
	if x != nil {
		return x.ArticleCount
	}
	return 0
}

func (x *GetStatisticsResponse) GetCategoryCount() int32 {
	if x != nil {
		return x.CategoryCount
	}
	return 0
}

type AddClickRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackId     string `protobuf:"bytes,1,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	AccountId   string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	CampaignId  string `protobuf:"bytes,3,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id"`
	UnitId      string `protobuf:"bytes,4,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
	CreativeId  string `protobuf:"bytes,5,opt,name=creative_id,json=creativeId,proto3" json:"creative_id"`
	Os          int64  `protobuf:"varint,6,opt,name=os,proto3" json:"os"`
	Imei        string `protobuf:"bytes,7,opt,name=imei,proto3" json:"imei"`
	CallbackUrl string `protobuf:"bytes,8,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url"`
	Mac1        string `protobuf:"bytes,9,opt,name=mac1,proto3" json:"mac1"`
	Idfa        string `protobuf:"bytes,10,opt,name=idfa,proto3" json:"idfa"`
	Caid        string `protobuf:"bytes,11,opt,name=caid,proto3" json:"caid"`
	Aaid        string `protobuf:"bytes,12,opt,name=aaid,proto3" json:"aaid"`
	AndroidId   string `protobuf:"bytes,13,opt,name=android_id,json=androidId,proto3" json:"android_id"`
	Oaid        string `protobuf:"bytes,14,opt,name=oaid,proto3" json:"oaid"`
	Ip          string `protobuf:"bytes,15,opt,name=ip,proto3" json:"ip"`
	Ua          string `protobuf:"bytes,16,opt,name=ua,proto3" json:"ua"`
	Ts          int64  `protobuf:"varint,17,opt,name=ts,proto3" json:"ts"`
	ShopId      int64  `protobuf:"varint,18,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	UpMid       int64  `protobuf:"varint,19,opt,name=up_mid,json=upMid,proto3" json:"up_mid"`
}

func (x *AddClickRecordRequest) Reset() {
	*x = AddClickRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClickRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClickRecordRequest) ProtoMessage() {}

func (x *AddClickRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClickRecordRequest.ProtoReflect.Descriptor instead.
func (*AddClickRecordRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{48}
}

func (x *AddClickRecordRequest) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *AddClickRecordRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AddClickRecordRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *AddClickRecordRequest) GetUnitId() string {
	if x != nil {
		return x.UnitId
	}
	return ""
}

func (x *AddClickRecordRequest) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *AddClickRecordRequest) GetOs() int64 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *AddClickRecordRequest) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *AddClickRecordRequest) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *AddClickRecordRequest) GetMac1() string {
	if x != nil {
		return x.Mac1
	}
	return ""
}

func (x *AddClickRecordRequest) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *AddClickRecordRequest) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *AddClickRecordRequest) GetAaid() string {
	if x != nil {
		return x.Aaid
	}
	return ""
}

func (x *AddClickRecordRequest) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *AddClickRecordRequest) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *AddClickRecordRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AddClickRecordRequest) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *AddClickRecordRequest) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *AddClickRecordRequest) GetShopId() int64 {
	if x != nil {
		return x.ShopId
	}
	return 0
}

func (x *AddClickRecordRequest) GetUpMid() int64 {
	if x != nil {
		return x.UpMid
	}
	return 0
}

type FindClickRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Imei string `protobuf:"bytes,1,opt,name=imei,proto3" json:"imei"`
	Idfa string `protobuf:"bytes,2,opt,name=idfa,proto3" json:"idfa"`
	Oaid string `protobuf:"bytes,3,opt,name=oaid,proto3" json:"oaid"`
	Ip   string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Ua   string `protobuf:"bytes,5,opt,name=ua,proto3" json:"ua"`
}

func (x *FindClickRecordsRequest) Reset() {
	*x = FindClickRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindClickRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindClickRecordsRequest) ProtoMessage() {}

func (x *FindClickRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindClickRecordsRequest.ProtoReflect.Descriptor instead.
func (*FindClickRecordsRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{49}
}

func (x *FindClickRecordsRequest) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *FindClickRecordsRequest) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *FindClickRecordsRequest) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *FindClickRecordsRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FindClickRecordsRequest) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

type BiliAdClickRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TrackId    string `protobuf:"bytes,2,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	AccountId  string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	CampaignId string `protobuf:"bytes,4,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id"`
	UnitId     string `protobuf:"bytes,5,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
	CreativeId string `protobuf:"bytes,6,opt,name=creative_id,json=creativeId,proto3" json:"creative_id"`
	Os         int64  `protobuf:"varint,7,opt,name=os,proto3" json:"os"`
	Imei       string `protobuf:"bytes,8,opt,name=imei,proto3" json:"imei"`
	Mac1       string `protobuf:"bytes,9,opt,name=mac1,proto3" json:"mac1"`
	Idfa       string `protobuf:"bytes,11,opt,name=idfa,proto3" json:"idfa"`
	Caid       string `protobuf:"bytes,12,opt,name=caid,proto3" json:"caid"`
	Aaid       string `protobuf:"bytes,13,opt,name=aaid,proto3" json:"aaid"`
	AndroidId  string `protobuf:"bytes,14,opt,name=android_id,json=androidId,proto3" json:"android_id"`
	Oaid       string `protobuf:"bytes,15,opt,name=oaid,proto3" json:"oaid"`
	Ip         string `protobuf:"bytes,16,opt,name=ip,proto3" json:"ip"`
	Ua         string `protobuf:"bytes,17,opt,name=ua,proto3" json:"ua"`
	Ts         int64  `protobuf:"varint,18,opt,name=ts,proto3" json:"ts"`
	ShopId     int64  `protobuf:"varint,19,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	UpMid      int64  `protobuf:"varint,20,opt,name=up_mid,json=upMid,proto3" json:"up_mid"`
}

func (x *BiliAdClickRecord) Reset() {
	*x = BiliAdClickRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BiliAdClickRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BiliAdClickRecord) ProtoMessage() {}

func (x *BiliAdClickRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BiliAdClickRecord.ProtoReflect.Descriptor instead.
func (*BiliAdClickRecord) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{50}
}

func (x *BiliAdClickRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BiliAdClickRecord) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *BiliAdClickRecord) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *BiliAdClickRecord) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *BiliAdClickRecord) GetUnitId() string {
	if x != nil {
		return x.UnitId
	}
	return ""
}

func (x *BiliAdClickRecord) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *BiliAdClickRecord) GetOs() int64 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *BiliAdClickRecord) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BiliAdClickRecord) GetMac1() string {
	if x != nil {
		return x.Mac1
	}
	return ""
}

func (x *BiliAdClickRecord) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BiliAdClickRecord) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BiliAdClickRecord) GetAaid() string {
	if x != nil {
		return x.Aaid
	}
	return ""
}

func (x *BiliAdClickRecord) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BiliAdClickRecord) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BiliAdClickRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BiliAdClickRecord) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BiliAdClickRecord) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *BiliAdClickRecord) GetShopId() int64 {
	if x != nil {
		return x.ShopId
	}
	return 0
}

func (x *BiliAdClickRecord) GetUpMid() int64 {
	if x != nil {
		return x.UpMid
	}
	return 0
}

type FindClickRecordsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*BiliAdClickRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
}

func (x *FindClickRecordsResponse) Reset() {
	*x = FindClickRecordsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindClickRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindClickRecordsResponse) ProtoMessage() {}

func (x *FindClickRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindClickRecordsResponse.ProtoReflect.Descriptor instead.
func (*FindClickRecordsResponse) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{51}
}

func (x *FindClickRecordsResponse) GetList() []*BiliAdClickRecord {
	if x != nil {
		return x.List
	}
	return nil
}

type SaveConversionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BiliAdClickId string `protobuf:"bytes,1,opt,name=bili_ad_click_id,json=biliAdClickId,proto3" json:"bili_ad_click_id"`
	ConvType      string `protobuf:"bytes,2,opt,name=conv_type,json=convType,proto3" json:"conv_type"`
	ConvTime      int64  `protobuf:"varint,3,opt,name=conv_time,json=convTime,proto3" json:"conv_time"`
	ConvValue     int64  `protobuf:"varint,4,opt,name=conv_value,json=convValue,proto3" json:"conv_value"`
	ConvCount     int64  `protobuf:"varint,5,opt,name=conv_count,json=convCount,proto3" json:"conv_count"`
	Imei          string `protobuf:"bytes,6,opt,name=imei,proto3" json:"imei"`
	Idfa          string `protobuf:"bytes,7,opt,name=idfa,proto3" json:"idfa"`
	Oaid          string `protobuf:"bytes,8,opt,name=oaid,proto3" json:"oaid"`
	Mac           string `protobuf:"bytes,9,opt,name=mac,proto3" json:"mac"`
	ClientIp      string `protobuf:"bytes,10,opt,name=client_ip,json=clientIp,proto3" json:"client_ip"`
	Model         string `protobuf:"bytes,11,opt,name=model,proto3" json:"model"`
	TrackId       string `protobuf:"bytes,12,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	Ua            string `protobuf:"bytes,13,opt,name=ua,proto3" json:"ua"`
	ResultCode    string `protobuf:"bytes,14,opt,name=result_code,json=resultCode,proto3" json:"result_code"`
	ResultMessage string `protobuf:"bytes,15,opt,name=result_message,json=resultMessage,proto3" json:"result_message"`
}

func (x *SaveConversionRequest) Reset() {
	*x = SaveConversionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ctc_contentCenter_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveConversionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveConversionRequest) ProtoMessage() {}

func (x *SaveConversionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ctc_contentCenter_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveConversionRequest.ProtoReflect.Descriptor instead.
func (*SaveConversionRequest) Descriptor() ([]byte, []int) {
	return file_ctc_contentCenter_proto_rawDescGZIP(), []int{52}
}

func (x *SaveConversionRequest) GetBiliAdClickId() string {
	if x != nil {
		return x.BiliAdClickId
	}
	return ""
}

func (x *SaveConversionRequest) GetConvType() string {
	if x != nil {
		return x.ConvType
	}
	return ""
}

func (x *SaveConversionRequest) GetConvTime() int64 {
	if x != nil {
		return x.ConvTime
	}
	return 0
}

func (x *SaveConversionRequest) GetConvValue() int64 {
	if x != nil {
		return x.ConvValue
	}
	return 0
}

func (x *SaveConversionRequest) GetConvCount() int64 {
	if x != nil {
		return x.ConvCount
	}
	return 0
}

func (x *SaveConversionRequest) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *SaveConversionRequest) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *SaveConversionRequest) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *SaveConversionRequest) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *SaveConversionRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *SaveConversionRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *SaveConversionRequest) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *SaveConversionRequest) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *SaveConversionRequest) GetResultCode() string {
	if x != nil {
		return x.ResultCode
	}
	return ""
}

func (x *SaveConversionRequest) GetResultMessage() string {
	if x != nil {
		return x.ResultMessage
	}
	return ""
}

var File_ctc_contentCenter_proto protoreflect.FileDescriptor

var file_ctc_contentCenter_proto_rawDesc = []byte{
	0x0a, 0x17, 0x63, 0x74, 0x63, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x63, 0x74, 0x63, 0x22, 0x3f,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0xb9, 0x04, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x74, 0x68, 0x69, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x68, 0x69, 0x72, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x6f, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69,
	0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0a, 0x64, 0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x73, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x73, 0x6b, 0x55, 0x72,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x22, 0x88, 0x03,
	0x0a, 0x12, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x68, 0x69, 0x72, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x54, 0x68, 0x69, 0x72, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x22, 0x8e, 0x01, 0x0a, 0x13, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb1, 0x02, 0x0a, 0x0f, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x33, 0x0a,
	0x1d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x22, 0xa3, 0x02, 0x0a, 0x1e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x74, 0x63, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x72, 0x72, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x72, 0x72, 0x52, 0x0a, 0x64,
	0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x74, 0x63, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x72, 0x72, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x30, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x41, 0x72, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x96, 0x01, 0x0a, 0x17, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xfa, 0x02, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x65, 0x72,
	0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x43, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55,
	0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x49, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x22, 0x71, 0x0a, 0x15, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xeb,
	0x05, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x68,
	0x69, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x54, 0x68, 0x69, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55,
	0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x65,
	0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x43, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67,
	0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67,
	0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x72, 0x72, 0x18, 0x18, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d,
	0x64, 0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x72, 0x72, 0x12, 0x1e, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x64, 0x73, 0x12, 0x24, 0x0a,
	0x0e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x73, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x73, 0x6b,
	0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4d, 0x0a, 0x14,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x1d,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x1e, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xda, 0x01, 0x0a, 0x11, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x38, 0x0a, 0x17, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x94,
	0x01, 0x0a, 0x18, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xbc, 0x02, 0x0a, 0x10, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x65, 0x72, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x6f,
	0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6d, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6d,
	0x67, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x1a, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x5f, 0x0a, 0x1b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x55, 0x0a, 0x1c, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x4d, 0x0a, 0x1d, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x1c, 0x0a, 0x1a, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x7a, 0x0a, 0x1b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x84, 0x02, 0x0a, 0x14, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x30, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x22, 0x84, 0x02, 0x0a, 0x15, 0x53, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2f, 0x0a,
	0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x22, 0xd2,
	0x01, 0x0a, 0x14, 0x54, 0x68, 0x69, 0x72, 0x64, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x22, 0x32, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x6c, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x53, 0x63, 0x72, 0x6d, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf9, 0x01, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x6d, 0x44, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64,
	0x6f, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xc0, 0x01, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x65, 0x6e,
	0x73, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x74,
	0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb6, 0x01, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x22, 0x54,
	0x0a, 0x18, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43, 0x65, 0x6e,
	0x73, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x75, 0x0a, 0x19, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67,
	0x65, 0x72, 0x65, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x81, 0x01, 0x0a, 0x11,
	0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43, 0x65, 0x6e, 0x73, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x43,
	0x68, 0x69, 0x6c, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x8d, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22,
	0x74, 0x0a, 0x16, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43, 0x68,
	0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x3c, 0x0a, 0x12, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x42, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x22, 0x66, 0x0a, 0x13, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42,
	0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc6, 0x01, 0x0a, 0x08,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x39, 0x0a, 0x0f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x61, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22,
	0x7e, 0x0a, 0x19, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x74, 0x61,
	0x67, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x09, 0x74, 0x61, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22,
	0x61, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xd6, 0x03, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x6f,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x63, 0x31,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x63, 0x31, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x64, 0x66, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x75,
	0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x68, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x68,
	0x6f, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x70, 0x5f, 0x6d, 0x69, 0x64, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x75, 0x70, 0x4d, 0x69, 0x64, 0x22, 0x75, 0x0a, 0x17, 0x46,
	0x69, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x75, 0x61, 0x22, 0xbf, 0x03, 0x0a, 0x11, 0x42, 0x69, 0x6c, 0x69, 0x41, 0x64, 0x43, 0x6c, 0x69,
	0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65,
	0x69, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x63, 0x31, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x61, 0x63, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x61, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x75, 0x70, 0x5f, 0x6d, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x75,
	0x70, 0x4d, 0x69, 0x64, 0x22, 0x46, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x63,
	0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x42, 0x69, 0x6c, 0x69, 0x41, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x03, 0x0a,
	0x15, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x10, 0x62, 0x69, 0x6c, 0x69, 0x5f, 0x61,
	0x64, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x62, 0x69, 0x6c, 0x69, 0x41, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x76, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x76, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x76, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x76, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x76,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6e, 0x76, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x64, 0x66, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f,
	0x61, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x75, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xad, 0x09, 0x0a, 0x14,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53,
	0x61, 0x76, 0x65, 0x12, 0x17, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x74, 0x63,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4d,
	0x69, 0x6e, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x11, 0x2e, 0x63, 0x74, 0x63, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0f,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x12,
	0x17, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61,
	0x0a, 0x16, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x19, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x16, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64,
	0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x69, 0x74, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x45,
	0x64, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x1c, 0x2e, 0x63, 0x74, 0x63,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x61, 0x76, 0x65, 0x12, 0x1f,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5e, 0x0a, 0x15, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x74, 0x63,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x58, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x74, 0x63, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0f, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x74,
	0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x61, 0x72, 0x12, 0x17, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x42, 0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x11, 0x2e, 0x63,
	0x74, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xbf, 0x02, 0x0a, 0x0d,
	0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a,
	0x0e, 0x41, 0x64, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x64, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x74,
	0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4e, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67,
	0x65, 0x72, 0x65, 0x43, 0x68, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1b,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65,
	0x43, 0x68, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x1a, 0x14, 0x2e, 0x63, 0x74,
	0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x65, 0x6e, 0x73,
	0x75, 0x73, 0x12, 0x19, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x65, 0x6e, 0x73, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x11, 0x42, 0x6f, 0x65,
	0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x12, 0x1d,
	0x2e, 0x63, 0x74, 0x63, 0x2e, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65,
	0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x42, 0x6f, 0x65, 0x68, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x65, 0x43,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xe8, 0x01,
	0x0a, 0x0d, 0x42, 0x69, 0x6c, 0x69, 0x41, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x42, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e,
	0x63, 0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1c, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x74, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x1a, 0x5a, 0x18, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ctc_contentCenter_proto_rawDescOnce sync.Once
	file_ctc_contentCenter_proto_rawDescData = file_ctc_contentCenter_proto_rawDesc
)

func file_ctc_contentCenter_proto_rawDescGZIP() []byte {
	file_ctc_contentCenter_proto_rawDescOnce.Do(func() {
		file_ctc_contentCenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_ctc_contentCenter_proto_rawDescData)
	})
	return file_ctc_contentCenter_proto_rawDescData
}

var file_ctc_contentCenter_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_ctc_contentCenter_proto_goTypes = []interface{}{
	(*ContentResponse)(nil),                // 0: ctc.ContentResponse
	(*ArticleSaveRequest)(nil),             // 1: ctc.ArticleSaveRequest
	(*ArticleTagData)(nil),                 // 2: ctc.ArticleTagData
	(*ArticleListRequest)(nil),             // 3: ctc.ArticleListRequest
	(*ArticleListResponse)(nil),            // 4: ctc.ArticleListResponse
	(*ArticleListData)(nil),                // 5: ctc.ArticleListData
	(*ArticleSearchConditionRequest)(nil),  // 6: ctc.ArticleSearchConditionRequest
	(*ArticleSearchConditionResponse)(nil), // 7: ctc.ArticleSearchConditionResponse
	(*ArticleArr)(nil),                     // 8: ctc.ArticleArr
	(*ArticleListMiniResponse)(nil),        // 9: ctc.ArticleListMiniResponse
	(*ArticleListMiniData)(nil),            // 10: ctc.ArticleListMiniData
	(*ArticleDetailRequest)(nil),           // 11: ctc.ArticleDetailRequest
	(*ArticleDetailResponse)(nil),          // 12: ctc.ArticleDetailResponse
	(*ArticleDetail)(nil),                  // 13: ctc.ArticleDetail
	(*ArticleStatusRequest)(nil),           // 14: ctc.ArticleStatusRequest
	(*ArticleEditHistoryListRequest)(nil),  // 15: ctc.ArticleEditHistoryListRequest
	(*ArticleEditHistoryListResponse)(nil), // 16: ctc.ArticleEditHistoryListResponse
	(*ArticleEditRecord)(nil),              // 17: ctc.ArticleEditRecord
	(*ArticleRecommendRequest)(nil),        // 18: ctc.ArticleRecommendRequest
	(*ArticleRecommendResponse)(nil),       // 19: ctc.ArticleRecommendResponse
	(*ArticleRecommend)(nil),               // 20: ctc.ArticleRecommend
	(*ArticleCategorySaveRequest)(nil),     // 21: ctc.ArticleCategorySaveRequest
	(*ArticleCategorySaveResponse)(nil),    // 22: ctc.ArticleCategorySaveResponse
	(*ArticleCategoryDeleteRequest)(nil),   // 23: ctc.ArticleCategoryDeleteRequest
	(*ArticleCategoryDeleteResponse)(nil),  // 24: ctc.ArticleCategoryDeleteResponse
	(*ArticleCategoryListRequest)(nil),     // 25: ctc.ArticleCategoryListRequest
	(*ArticleCategoryListResponse)(nil),    // 26: ctc.ArticleCategoryListResponse
	(*FirstArticleCategory)(nil),           // 27: ctc.FirstArticleCategory
	(*SecondArticleCategory)(nil),          // 28: ctc.SecondArticleCategory
	(*ThirdArticleCategory)(nil),           // 29: ctc.ThirdArticleCategory
	(*QueryDoctorListRequest)(nil),         // 30: ctc.QueryDoctorListRequest
	(*QueryDoctorListResponse)(nil),        // 31: ctc.QueryDoctorListResponse
	(*ScrmDoctor)(nil),                     // 32: ctc.ScrmDoctor
	(*ArticleCensusRequest)(nil),           // 33: ctc.ArticleCensusRequest
	(*ArticleCensusResponse)(nil),          // 34: ctc.ArticleCensusResponse
	(*ArticleCensus)(nil),                  // 35: ctc.ArticleCensus
	(*BoehringereCensusRequest)(nil),       // 36: ctc.BoehringereCensusRequest
	(*BoehringereCensusResponse)(nil),      // 37: ctc.BoehringereCensusResponse
	(*BoehringereCensus)(nil),              // 38: ctc.BoehringereCensus
	(*AddVisitRecordRequest)(nil),          // 39: ctc.AddVisitRecordRequest
	(*BoehringereChickRecord)(nil),         // 40: ctc.BoehringereChickRecord
	(*CategoryBarRequest)(nil),             // 41: ctc.CategoryBarRequest
	(*CategoryBarResponse)(nil),            // 42: ctc.CategoryBarResponse
	(*Category)(nil),                       // 43: ctc.Category
	(*EmptyRequest)(nil),                   // 44: ctc.EmptyRequest
	(*ArticleTagGroup)(nil),                // 45: ctc.ArticleTagGroup
	(*ArticleMiniFilterResponse)(nil),      // 46: ctc.ArticleMiniFilterResponse
	(*GetStatisticsResponse)(nil),          // 47: ctc.GetStatisticsResponse
	(*AddClickRecordRequest)(nil),          // 48: ctc.AddClickRecordRequest
	(*FindClickRecordsRequest)(nil),        // 49: ctc.FindClickRecordsRequest
	(*BiliAdClickRecord)(nil),              // 50: ctc.BiliAdClickRecord
	(*FindClickRecordsResponse)(nil),       // 51: ctc.FindClickRecordsResponse
	(*SaveConversionRequest)(nil),          // 52: ctc.SaveConversionRequest
}
var file_ctc_contentCenter_proto_depIdxs = []int32{
	5,  // 0: ctc.ArticleListResponse.data:type_name -> ctc.ArticleListData
	8,  // 1: ctc.ArticleSearchConditionResponse.article_type:type_name -> ctc.ArticleArr
	8,  // 2: ctc.ArticleSearchConditionResponse.dis_channel:type_name -> ctc.ArticleArr
	8,  // 3: ctc.ArticleSearchConditionResponse.status:type_name -> ctc.ArticleArr
	27, // 4: ctc.ArticleSearchConditionResponse.article_category:type_name -> ctc.FirstArticleCategory
	10, // 5: ctc.ArticleListMiniResponse.data:type_name -> ctc.ArticleListMiniData
	13, // 6: ctc.ArticleDetailResponse.detail:type_name -> ctc.ArticleDetail
	17, // 7: ctc.ArticleEditHistoryListResponse.data:type_name -> ctc.ArticleEditRecord
	20, // 8: ctc.ArticleRecommendResponse.data:type_name -> ctc.ArticleRecommend
	27, // 9: ctc.ArticleCategoryListResponse.data:type_name -> ctc.FirstArticleCategory
	28, // 10: ctc.FirstArticleCategory.child:type_name -> ctc.SecondArticleCategory
	29, // 11: ctc.SecondArticleCategory.child:type_name -> ctc.ThirdArticleCategory
	32, // 12: ctc.QueryDoctorListResponse.data:type_name -> ctc.ScrmDoctor
	35, // 13: ctc.ArticleCensusResponse.data:type_name -> ctc.ArticleCensus
	38, // 14: ctc.BoehringereCensusResponse.data:type_name -> ctc.BoehringereCensus
	43, // 15: ctc.CategoryBarResponse.data:type_name -> ctc.Category
	45, // 16: ctc.ArticleMiniFilterResponse.tag_groups:type_name -> ctc.ArticleTagGroup
	50, // 17: ctc.FindClickRecordsResponse.list:type_name -> ctc.BiliAdClickRecord
	1,  // 18: ctc.ContentCenterService.ArticleSave:input_type -> ctc.ArticleSaveRequest
	3,  // 19: ctc.ContentCenterService.ArticleList:input_type -> ctc.ArticleListRequest
	44, // 20: ctc.ContentCenterService.ArticleMiniFilter:input_type -> ctc.EmptyRequest
	3,  // 21: ctc.ContentCenterService.ArticleListMini:input_type -> ctc.ArticleListRequest
	11, // 22: ctc.ContentCenterService.ArticleDetail:input_type -> ctc.ArticleDetailRequest
	6,  // 23: ctc.ContentCenterService.ArticleSearchCondition:input_type -> ctc.ArticleSearchConditionRequest
	14, // 24: ctc.ContentCenterService.ArticleStatus:input_type -> ctc.ArticleStatusRequest
	15, // 25: ctc.ContentCenterService.ArticleEditHistoryList:input_type -> ctc.ArticleEditHistoryListRequest
	18, // 26: ctc.ContentCenterService.ArticleRecommend:input_type -> ctc.ArticleRecommendRequest
	21, // 27: ctc.ContentCenterService.ArticleCategorySave:input_type -> ctc.ArticleCategorySaveRequest
	23, // 28: ctc.ContentCenterService.ArticleCategoryDelete:input_type -> ctc.ArticleCategoryDeleteRequest
	25, // 29: ctc.ContentCenterService.ArticleCategoryList:input_type -> ctc.ArticleCategoryListRequest
	30, // 30: ctc.ContentCenterService.QueryDoctorList:input_type -> ctc.QueryDoctorListRequest
	41, // 31: ctc.ContentCenterService.CategoryBar:input_type -> ctc.CategoryBarRequest
	44, // 32: ctc.ContentCenterService.GetStatistics:input_type -> ctc.EmptyRequest
	39, // 33: ctc.CensusService.AddVisitRecord:input_type -> ctc.AddVisitRecordRequest
	40, // 34: ctc.CensusService.AddBoehringereChickRecord:input_type -> ctc.BoehringereChickRecord
	33, // 35: ctc.CensusService.ArticleCensus:input_type -> ctc.ArticleCensusRequest
	36, // 36: ctc.CensusService.BoehringereCensus:input_type -> ctc.BoehringereCensusRequest
	48, // 37: ctc.BiliAdService.AddClickRecord:input_type -> ctc.AddClickRecordRequest
	49, // 38: ctc.BiliAdService.FindClickRecords:input_type -> ctc.FindClickRecordsRequest
	52, // 39: ctc.BiliAdService.SaveConversion:input_type -> ctc.SaveConversionRequest
	0,  // 40: ctc.ContentCenterService.ArticleSave:output_type -> ctc.ContentResponse
	4,  // 41: ctc.ContentCenterService.ArticleList:output_type -> ctc.ArticleListResponse
	46, // 42: ctc.ContentCenterService.ArticleMiniFilter:output_type -> ctc.ArticleMiniFilterResponse
	9,  // 43: ctc.ContentCenterService.ArticleListMini:output_type -> ctc.ArticleListMiniResponse
	12, // 44: ctc.ContentCenterService.ArticleDetail:output_type -> ctc.ArticleDetailResponse
	7,  // 45: ctc.ContentCenterService.ArticleSearchCondition:output_type -> ctc.ArticleSearchConditionResponse
	0,  // 46: ctc.ContentCenterService.ArticleStatus:output_type -> ctc.ContentResponse
	16, // 47: ctc.ContentCenterService.ArticleEditHistoryList:output_type -> ctc.ArticleEditHistoryListResponse
	19, // 48: ctc.ContentCenterService.ArticleRecommend:output_type -> ctc.ArticleRecommendResponse
	22, // 49: ctc.ContentCenterService.ArticleCategorySave:output_type -> ctc.ArticleCategorySaveResponse
	24, // 50: ctc.ContentCenterService.ArticleCategoryDelete:output_type -> ctc.ArticleCategoryDeleteResponse
	26, // 51: ctc.ContentCenterService.ArticleCategoryList:output_type -> ctc.ArticleCategoryListResponse
	31, // 52: ctc.ContentCenterService.QueryDoctorList:output_type -> ctc.QueryDoctorListResponse
	42, // 53: ctc.ContentCenterService.CategoryBar:output_type -> ctc.CategoryBarResponse
	47, // 54: ctc.ContentCenterService.GetStatistics:output_type -> ctc.GetStatisticsResponse
	0,  // 55: ctc.CensusService.AddVisitRecord:output_type -> ctc.ContentResponse
	0,  // 56: ctc.CensusService.AddBoehringereChickRecord:output_type -> ctc.ContentResponse
	34, // 57: ctc.CensusService.ArticleCensus:output_type -> ctc.ArticleCensusResponse
	37, // 58: ctc.CensusService.BoehringereCensus:output_type -> ctc.BoehringereCensusResponse
	0,  // 59: ctc.BiliAdService.AddClickRecord:output_type -> ctc.ContentResponse
	51, // 60: ctc.BiliAdService.FindClickRecords:output_type -> ctc.FindClickRecordsResponse
	0,  // 61: ctc.BiliAdService.SaveConversion:output_type -> ctc.ContentResponse
	40, // [40:62] is the sub-list for method output_type
	18, // [18:40] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_ctc_contentCenter_proto_init() }
func file_ctc_contentCenter_proto_init() {
	if File_ctc_contentCenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ctc_contentCenter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleSaveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleTagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleSearchConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleSearchConditionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleArr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleListMiniResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleListMiniData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleEditHistoryListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleEditHistoryListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleEditRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleRecommendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleRecommendResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleRecommend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategorySaveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategorySaveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategoryDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategoryDeleteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategoryListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCategoryListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FirstArticleCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecondArticleCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdArticleCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDoctorListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDoctorListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrmDoctor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCensusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCensusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleCensus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoehringereCensusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoehringereCensusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoehringereCensus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVisitRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoehringereChickRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryBarRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryBarResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Category); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleTagGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleMiniFilterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatisticsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClickRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindClickRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BiliAdClickRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindClickRecordsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ctc_contentCenter_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveConversionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ctc_contentCenter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_ctc_contentCenter_proto_goTypes,
		DependencyIndexes: file_ctc_contentCenter_proto_depIdxs,
		MessageInfos:      file_ctc_contentCenter_proto_msgTypes,
	}.Build()
	File_ctc_contentCenter_proto = out.File
	file_ctc_contentCenter_proto_rawDesc = nil
	file_ctc_contentCenter_proto_goTypes = nil
	file_ctc_contentCenter_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ContentCenterServiceClient is the client API for ContentCenterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ContentCenterServiceClient interface {
	// 文章新增或修改
	ArticleSave(ctx context.Context, in *ArticleSaveRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 文章列表
	ArticleList(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListResponse, error)
	// 文章小程序过滤数据
	ArticleMiniFilter(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ArticleMiniFilterResponse, error)
	// 文章列表-小程序
	ArticleListMini(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListMiniResponse, error)
	// 文章详情
	ArticleDetail(ctx context.Context, in *ArticleDetailRequest, opts ...grpc.CallOption) (*ArticleDetailResponse, error)
	// 文章搜索条件
	ArticleSearchCondition(ctx context.Context, in *ArticleSearchConditionRequest, opts ...grpc.CallOption) (*ArticleSearchConditionResponse, error)
	// 文章发布、下架
	ArticleStatus(ctx context.Context, in *ArticleStatusRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	//查询文章操作记录
	ArticleEditHistoryList(ctx context.Context, in *ArticleEditHistoryListRequest, opts ...grpc.CallOption) (*ArticleEditHistoryListResponse, error)
	//推送文章概要
	ArticleRecommend(ctx context.Context, in *ArticleRecommendRequest, opts ...grpc.CallOption) (*ArticleRecommendResponse, error)
	// 类目新增或修改
	ArticleCategorySave(ctx context.Context, in *ArticleCategorySaveRequest, opts ...grpc.CallOption) (*ArticleCategorySaveResponse, error)
	// 类目删除
	ArticleCategoryDelete(ctx context.Context, in *ArticleCategoryDeleteRequest, opts ...grpc.CallOption) (*ArticleCategoryDeleteResponse, error)
	// 类目查询
	ArticleCategoryList(ctx context.Context, in *ArticleCategoryListRequest, opts ...grpc.CallOption) (*ArticleCategoryListResponse, error)
	// 医生列表查询
	QueryDoctorList(ctx context.Context, in *QueryDoctorListRequest, opts ...grpc.CallOption) (*QueryDoctorListResponse, error)
	// 获取类目
	CategoryBar(ctx context.Context, in *CategoryBarRequest, opts ...grpc.CallOption) (*CategoryBarResponse, error)
	// 获取二级类目以及文章统计数据
	GetStatistics(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetStatisticsResponse, error)
}

type contentCenterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContentCenterServiceClient(cc grpc.ClientConnInterface) ContentCenterServiceClient {
	return &contentCenterServiceClient{cc}
}

func (c *contentCenterServiceClient) ArticleSave(ctx context.Context, in *ArticleSaveRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleList(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListResponse, error) {
	out := new(ArticleListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleMiniFilter(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ArticleMiniFilterResponse, error) {
	out := new(ArticleMiniFilterResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleMiniFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleListMini(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListMiniResponse, error) {
	out := new(ArticleListMiniResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleListMini", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleDetail(ctx context.Context, in *ArticleDetailRequest, opts ...grpc.CallOption) (*ArticleDetailResponse, error) {
	out := new(ArticleDetailResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleSearchCondition(ctx context.Context, in *ArticleSearchConditionRequest, opts ...grpc.CallOption) (*ArticleSearchConditionResponse, error) {
	out := new(ArticleSearchConditionResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleSearchCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleStatus(ctx context.Context, in *ArticleStatusRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleEditHistoryList(ctx context.Context, in *ArticleEditHistoryListRequest, opts ...grpc.CallOption) (*ArticleEditHistoryListResponse, error) {
	out := new(ArticleEditHistoryListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleEditHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleRecommend(ctx context.Context, in *ArticleRecommendRequest, opts ...grpc.CallOption) (*ArticleRecommendResponse, error) {
	out := new(ArticleRecommendResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategorySave(ctx context.Context, in *ArticleCategorySaveRequest, opts ...grpc.CallOption) (*ArticleCategorySaveResponse, error) {
	out := new(ArticleCategorySaveResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategorySave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategoryDelete(ctx context.Context, in *ArticleCategoryDeleteRequest, opts ...grpc.CallOption) (*ArticleCategoryDeleteResponse, error) {
	out := new(ArticleCategoryDeleteResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategoryDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategoryList(ctx context.Context, in *ArticleCategoryListRequest, opts ...grpc.CallOption) (*ArticleCategoryListResponse, error) {
	out := new(ArticleCategoryListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) QueryDoctorList(ctx context.Context, in *QueryDoctorListRequest, opts ...grpc.CallOption) (*QueryDoctorListResponse, error) {
	out := new(QueryDoctorListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/QueryDoctorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) CategoryBar(ctx context.Context, in *CategoryBarRequest, opts ...grpc.CallOption) (*CategoryBarResponse, error) {
	out := new(CategoryBarResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/CategoryBar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) GetStatistics(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetStatisticsResponse, error) {
	out := new(GetStatisticsResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/GetStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContentCenterServiceServer is the server API for ContentCenterService service.
type ContentCenterServiceServer interface {
	// 文章新增或修改
	ArticleSave(context.Context, *ArticleSaveRequest) (*ContentResponse, error)
	// 文章列表
	ArticleList(context.Context, *ArticleListRequest) (*ArticleListResponse, error)
	// 文章小程序过滤数据
	ArticleMiniFilter(context.Context, *EmptyRequest) (*ArticleMiniFilterResponse, error)
	// 文章列表-小程序
	ArticleListMini(context.Context, *ArticleListRequest) (*ArticleListMiniResponse, error)
	// 文章详情
	ArticleDetail(context.Context, *ArticleDetailRequest) (*ArticleDetailResponse, error)
	// 文章搜索条件
	ArticleSearchCondition(context.Context, *ArticleSearchConditionRequest) (*ArticleSearchConditionResponse, error)
	// 文章发布、下架
	ArticleStatus(context.Context, *ArticleStatusRequest) (*ContentResponse, error)
	//查询文章操作记录
	ArticleEditHistoryList(context.Context, *ArticleEditHistoryListRequest) (*ArticleEditHistoryListResponse, error)
	//推送文章概要
	ArticleRecommend(context.Context, *ArticleRecommendRequest) (*ArticleRecommendResponse, error)
	// 类目新增或修改
	ArticleCategorySave(context.Context, *ArticleCategorySaveRequest) (*ArticleCategorySaveResponse, error)
	// 类目删除
	ArticleCategoryDelete(context.Context, *ArticleCategoryDeleteRequest) (*ArticleCategoryDeleteResponse, error)
	// 类目查询
	ArticleCategoryList(context.Context, *ArticleCategoryListRequest) (*ArticleCategoryListResponse, error)
	// 医生列表查询
	QueryDoctorList(context.Context, *QueryDoctorListRequest) (*QueryDoctorListResponse, error)
	// 获取类目
	CategoryBar(context.Context, *CategoryBarRequest) (*CategoryBarResponse, error)
	// 获取二级类目以及文章统计数据
	GetStatistics(context.Context, *EmptyRequest) (*GetStatisticsResponse, error)
}

// UnimplementedContentCenterServiceServer can be embedded to have forward compatible implementations.
type UnimplementedContentCenterServiceServer struct {
}

func (*UnimplementedContentCenterServiceServer) ArticleSave(context.Context, *ArticleSaveRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleSave not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleList(context.Context, *ArticleListRequest) (*ArticleListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleList not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleMiniFilter(context.Context, *EmptyRequest) (*ArticleMiniFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleMiniFilter not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleListMini(context.Context, *ArticleListRequest) (*ArticleListMiniResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleListMini not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleDetail(context.Context, *ArticleDetailRequest) (*ArticleDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleDetail not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleSearchCondition(context.Context, *ArticleSearchConditionRequest) (*ArticleSearchConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleSearchCondition not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleStatus(context.Context, *ArticleStatusRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleStatus not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleEditHistoryList(context.Context, *ArticleEditHistoryListRequest) (*ArticleEditHistoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleEditHistoryList not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleRecommend(context.Context, *ArticleRecommendRequest) (*ArticleRecommendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleRecommend not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategorySave(context.Context, *ArticleCategorySaveRequest) (*ArticleCategorySaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategorySave not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategoryDelete(context.Context, *ArticleCategoryDeleteRequest) (*ArticleCategoryDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategoryDelete not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategoryList(context.Context, *ArticleCategoryListRequest) (*ArticleCategoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategoryList not implemented")
}
func (*UnimplementedContentCenterServiceServer) QueryDoctorList(context.Context, *QueryDoctorListRequest) (*QueryDoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryDoctorList not implemented")
}
func (*UnimplementedContentCenterServiceServer) CategoryBar(context.Context, *CategoryBarRequest) (*CategoryBarResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategoryBar not implemented")
}
func (*UnimplementedContentCenterServiceServer) GetStatistics(context.Context, *EmptyRequest) (*GetStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatistics not implemented")
}

func RegisterContentCenterServiceServer(s *grpc.Server, srv ContentCenterServiceServer) {
	s.RegisterService(&_ContentCenterService_serviceDesc, srv)
}

func _ContentCenterService_ArticleSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleSaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleSave(ctx, req.(*ArticleSaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleList(ctx, req.(*ArticleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleMiniFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleMiniFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleMiniFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleMiniFilter(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleListMini_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleListMini(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleListMini",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleListMini(ctx, req.(*ArticleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleDetail(ctx, req.(*ArticleDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleSearchCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleSearchConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleSearchCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleSearchCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleSearchCondition(ctx, req.(*ArticleSearchConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleStatus(ctx, req.(*ArticleStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleEditHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleEditHistoryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleEditHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleEditHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleEditHistoryList(ctx, req.(*ArticleEditHistoryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleRecommend(ctx, req.(*ArticleRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategorySave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategorySaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategorySave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategorySave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategorySave(ctx, req.(*ArticleCategorySaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategoryDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategoryDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategoryDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategoryDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategoryDelete(ctx, req.(*ArticleCategoryDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategoryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategoryList(ctx, req.(*ArticleCategoryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_QueryDoctorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDoctorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).QueryDoctorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/QueryDoctorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).QueryDoctorList(ctx, req.(*QueryDoctorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_CategoryBar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CategoryBarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).CategoryBar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/CategoryBar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).CategoryBar(ctx, req.(*CategoryBarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_GetStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).GetStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/GetStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).GetStatistics(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ContentCenterService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.ContentCenterService",
	HandlerType: (*ContentCenterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ArticleSave",
			Handler:    _ContentCenterService_ArticleSave_Handler,
		},
		{
			MethodName: "ArticleList",
			Handler:    _ContentCenterService_ArticleList_Handler,
		},
		{
			MethodName: "ArticleMiniFilter",
			Handler:    _ContentCenterService_ArticleMiniFilter_Handler,
		},
		{
			MethodName: "ArticleListMini",
			Handler:    _ContentCenterService_ArticleListMini_Handler,
		},
		{
			MethodName: "ArticleDetail",
			Handler:    _ContentCenterService_ArticleDetail_Handler,
		},
		{
			MethodName: "ArticleSearchCondition",
			Handler:    _ContentCenterService_ArticleSearchCondition_Handler,
		},
		{
			MethodName: "ArticleStatus",
			Handler:    _ContentCenterService_ArticleStatus_Handler,
		},
		{
			MethodName: "ArticleEditHistoryList",
			Handler:    _ContentCenterService_ArticleEditHistoryList_Handler,
		},
		{
			MethodName: "ArticleRecommend",
			Handler:    _ContentCenterService_ArticleRecommend_Handler,
		},
		{
			MethodName: "ArticleCategorySave",
			Handler:    _ContentCenterService_ArticleCategorySave_Handler,
		},
		{
			MethodName: "ArticleCategoryDelete",
			Handler:    _ContentCenterService_ArticleCategoryDelete_Handler,
		},
		{
			MethodName: "ArticleCategoryList",
			Handler:    _ContentCenterService_ArticleCategoryList_Handler,
		},
		{
			MethodName: "QueryDoctorList",
			Handler:    _ContentCenterService_QueryDoctorList_Handler,
		},
		{
			MethodName: "CategoryBar",
			Handler:    _ContentCenterService_CategoryBar_Handler,
		},
		{
			MethodName: "GetStatistics",
			Handler:    _ContentCenterService_GetStatistics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}

// CensusServiceClient is the client API for CensusService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CensusServiceClient interface {
	//添加访问记录
	AddVisitRecord(ctx context.Context, in *AddVisitRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	//添加勃林格访问记录
	AddBoehringereChickRecord(ctx context.Context, in *BoehringereChickRecord, opts ...grpc.CallOption) (*ContentResponse, error)
	// 文章统计
	ArticleCensus(ctx context.Context, in *ArticleCensusRequest, opts ...grpc.CallOption) (*ArticleCensusResponse, error)
	// 勃林格统计
	BoehringereCensus(ctx context.Context, in *BoehringereCensusRequest, opts ...grpc.CallOption) (*BoehringereCensusResponse, error)
}

type censusServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCensusServiceClient(cc grpc.ClientConnInterface) CensusServiceClient {
	return &censusServiceClient{cc}
}

func (c *censusServiceClient) AddVisitRecord(ctx context.Context, in *AddVisitRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/AddVisitRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) AddBoehringereChickRecord(ctx context.Context, in *BoehringereChickRecord, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/AddBoehringereChickRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) ArticleCensus(ctx context.Context, in *ArticleCensusRequest, opts ...grpc.CallOption) (*ArticleCensusResponse, error) {
	out := new(ArticleCensusResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/ArticleCensus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) BoehringereCensus(ctx context.Context, in *BoehringereCensusRequest, opts ...grpc.CallOption) (*BoehringereCensusResponse, error) {
	out := new(BoehringereCensusResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/BoehringereCensus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CensusServiceServer is the server API for CensusService service.
type CensusServiceServer interface {
	//添加访问记录
	AddVisitRecord(context.Context, *AddVisitRecordRequest) (*ContentResponse, error)
	//添加勃林格访问记录
	AddBoehringereChickRecord(context.Context, *BoehringereChickRecord) (*ContentResponse, error)
	// 文章统计
	ArticleCensus(context.Context, *ArticleCensusRequest) (*ArticleCensusResponse, error)
	// 勃林格统计
	BoehringereCensus(context.Context, *BoehringereCensusRequest) (*BoehringereCensusResponse, error)
}

// UnimplementedCensusServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCensusServiceServer struct {
}

func (*UnimplementedCensusServiceServer) AddVisitRecord(context.Context, *AddVisitRecordRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddVisitRecord not implemented")
}
func (*UnimplementedCensusServiceServer) AddBoehringereChickRecord(context.Context, *BoehringereChickRecord) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBoehringereChickRecord not implemented")
}
func (*UnimplementedCensusServiceServer) ArticleCensus(context.Context, *ArticleCensusRequest) (*ArticleCensusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCensus not implemented")
}
func (*UnimplementedCensusServiceServer) BoehringereCensus(context.Context, *BoehringereCensusRequest) (*BoehringereCensusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BoehringereCensus not implemented")
}

func RegisterCensusServiceServer(s *grpc.Server, srv CensusServiceServer) {
	s.RegisterService(&_CensusService_serviceDesc, srv)
}

func _CensusService_AddVisitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVisitRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).AddVisitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/AddVisitRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).AddVisitRecord(ctx, req.(*AddVisitRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_AddBoehringereChickRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoehringereChickRecord)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).AddBoehringereChickRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/AddBoehringereChickRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).AddBoehringereChickRecord(ctx, req.(*BoehringereChickRecord))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_ArticleCensus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCensusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).ArticleCensus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/ArticleCensus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).ArticleCensus(ctx, req.(*ArticleCensusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_BoehringereCensus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoehringereCensusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).BoehringereCensus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/BoehringereCensus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).BoehringereCensus(ctx, req.(*BoehringereCensusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CensusService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.CensusService",
	HandlerType: (*CensusServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddVisitRecord",
			Handler:    _CensusService_AddVisitRecord_Handler,
		},
		{
			MethodName: "AddBoehringereChickRecord",
			Handler:    _CensusService_AddBoehringereChickRecord_Handler,
		},
		{
			MethodName: "ArticleCensus",
			Handler:    _CensusService_ArticleCensus_Handler,
		},
		{
			MethodName: "BoehringereCensus",
			Handler:    _CensusService_BoehringereCensus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}

// BiliAdServiceClient is the client API for BiliAdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BiliAdServiceClient interface {
	// 添加B站广告点击记录
	AddClickRecord(ctx context.Context, in *AddClickRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 查找B站广告点击记录
	FindClickRecords(ctx context.Context, in *FindClickRecordsRequest, opts ...grpc.CallOption) (*FindClickRecordsResponse, error)
	// 保存推送B站广告归因记录
	SaveConversion(ctx context.Context, in *SaveConversionRequest, opts ...grpc.CallOption) (*ContentResponse, error)
}

type biliAdServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBiliAdServiceClient(cc grpc.ClientConnInterface) BiliAdServiceClient {
	return &biliAdServiceClient{cc}
}

func (c *biliAdServiceClient) AddClickRecord(ctx context.Context, in *AddClickRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/AddClickRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biliAdServiceClient) FindClickRecords(ctx context.Context, in *FindClickRecordsRequest, opts ...grpc.CallOption) (*FindClickRecordsResponse, error) {
	out := new(FindClickRecordsResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/FindClickRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biliAdServiceClient) SaveConversion(ctx context.Context, in *SaveConversionRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/SaveConversion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BiliAdServiceServer is the server API for BiliAdService service.
type BiliAdServiceServer interface {
	// 添加B站广告点击记录
	AddClickRecord(context.Context, *AddClickRecordRequest) (*ContentResponse, error)
	// 查找B站广告点击记录
	FindClickRecords(context.Context, *FindClickRecordsRequest) (*FindClickRecordsResponse, error)
	// 保存推送B站广告归因记录
	SaveConversion(context.Context, *SaveConversionRequest) (*ContentResponse, error)
}

// UnimplementedBiliAdServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBiliAdServiceServer struct {
}

func (*UnimplementedBiliAdServiceServer) AddClickRecord(context.Context, *AddClickRecordRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddClickRecord not implemented")
}
func (*UnimplementedBiliAdServiceServer) FindClickRecords(context.Context, *FindClickRecordsRequest) (*FindClickRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindClickRecords not implemented")
}
func (*UnimplementedBiliAdServiceServer) SaveConversion(context.Context, *SaveConversionRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveConversion not implemented")
}

func RegisterBiliAdServiceServer(s *grpc.Server, srv BiliAdServiceServer) {
	s.RegisterService(&_BiliAdService_serviceDesc, srv)
}

func _BiliAdService_AddClickRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddClickRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).AddClickRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/AddClickRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).AddClickRecord(ctx, req.(*AddClickRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiliAdService_FindClickRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindClickRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).FindClickRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/FindClickRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).FindClickRecords(ctx, req.(*FindClickRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiliAdService_SaveConversion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveConversionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).SaveConversion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/SaveConversion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).SaveConversion(ctx, req.(*SaveConversionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BiliAdService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.BiliAdService",
	HandlerType: (*BiliAdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddClickRecord",
			Handler:    _BiliAdService_AddClickRecord_Handler,
		},
		{
			MethodName: "FindClickRecords",
			Handler:    _BiliAdService_FindClickRecords_Handler,
		},
		{
			MethodName: "SaveConversion",
			Handler:    _BiliAdService_SaveConversion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}

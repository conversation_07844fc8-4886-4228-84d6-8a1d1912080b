// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/dinner.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DinnerResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerResponse) Reset()         { *m = DinnerResponse{} }
func (m *DinnerResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerResponse) ProtoMessage()    {}
func (*DinnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{0}
}

func (m *DinnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerResponse.Unmarshal(m, b)
}
func (m *DinnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerResponse.Marshal(b, m, deterministic)
}
func (m *DinnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerResponse.Merge(m, src)
}
func (m *DinnerResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerResponse.Size(m)
}
func (m *DinnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerResponse proto.InternalMessageInfo

func (m *DinnerResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type DinnerEmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerEmptyRequest) Reset()         { *m = DinnerEmptyRequest{} }
func (m *DinnerEmptyRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerEmptyRequest) ProtoMessage()    {}
func (*DinnerEmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{1}
}

func (m *DinnerEmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerEmptyRequest.Unmarshal(m, b)
}
func (m *DinnerEmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerEmptyRequest.Marshal(b, m, deterministic)
}
func (m *DinnerEmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerEmptyRequest.Merge(m, src)
}
func (m *DinnerEmptyRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerEmptyRequest.Size(m)
}
func (m *DinnerEmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerEmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerEmptyRequest proto.InternalMessageInfo

type DinnerSettingsData struct {
	// 是否开启活动，'0'关闭、'1'开启
	Enable string `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable"`
	// 支付成功微页面链接
	WepageLinkPaySuccess string `protobuf:"bytes,2,opt,name=wepage_link_pay_success,json=wepageLinkPaySuccess,proto3" json:"wepage_link_pay_success"`
	// 消费兑换比例，每10份兑换
	ConsumeDonateRate    string   `protobuf:"bytes,3,opt,name=consume_donate_rate,json=consumeDonateRate,proto3" json:"consume_donate_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSettingsData) Reset()         { *m = DinnerSettingsData{} }
func (m *DinnerSettingsData) String() string { return proto.CompactTextString(m) }
func (*DinnerSettingsData) ProtoMessage()    {}
func (*DinnerSettingsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{2}
}

func (m *DinnerSettingsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSettingsData.Unmarshal(m, b)
}
func (m *DinnerSettingsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSettingsData.Marshal(b, m, deterministic)
}
func (m *DinnerSettingsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSettingsData.Merge(m, src)
}
func (m *DinnerSettingsData) XXX_Size() int {
	return xxx_messageInfo_DinnerSettingsData.Size(m)
}
func (m *DinnerSettingsData) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSettingsData.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSettingsData proto.InternalMessageInfo

func (m *DinnerSettingsData) GetEnable() string {
	if m != nil {
		return m.Enable
	}
	return ""
}

func (m *DinnerSettingsData) GetWepageLinkPaySuccess() string {
	if m != nil {
		return m.WepageLinkPaySuccess
	}
	return ""
}

func (m *DinnerSettingsData) GetConsumeDonateRate() string {
	if m != nil {
		return m.ConsumeDonateRate
	}
	return ""
}

type DinnerSettingsResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 设置项
	Data                 *DinnerSettingsData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DinnerSettingsResponse) Reset()         { *m = DinnerSettingsResponse{} }
func (m *DinnerSettingsResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerSettingsResponse) ProtoMessage()    {}
func (*DinnerSettingsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{3}
}

func (m *DinnerSettingsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSettingsResponse.Unmarshal(m, b)
}
func (m *DinnerSettingsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSettingsResponse.Marshal(b, m, deterministic)
}
func (m *DinnerSettingsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSettingsResponse.Merge(m, src)
}
func (m *DinnerSettingsResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerSettingsResponse.Size(m)
}
func (m *DinnerSettingsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSettingsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSettingsResponse proto.InternalMessageInfo

func (m *DinnerSettingsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerSettingsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DinnerSettingsResponse) GetData() *DinnerSettingsData {
	if m != nil {
		return m.Data
	}
	return nil
}

type DinnerSettingValueRequest struct {
	// 参数key，支付成功微页面链接 wepage_link_pay_success
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSettingValueRequest) Reset()         { *m = DinnerSettingValueRequest{} }
func (m *DinnerSettingValueRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerSettingValueRequest) ProtoMessage()    {}
func (*DinnerSettingValueRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{4}
}

func (m *DinnerSettingValueRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSettingValueRequest.Unmarshal(m, b)
}
func (m *DinnerSettingValueRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSettingValueRequest.Marshal(b, m, deterministic)
}
func (m *DinnerSettingValueRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSettingValueRequest.Merge(m, src)
}
func (m *DinnerSettingValueRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerSettingValueRequest.Size(m)
}
func (m *DinnerSettingValueRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSettingValueRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSettingValueRequest proto.InternalMessageInfo

func (m *DinnerSettingValueRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SettingUpdateRequest struct {
	// 配置名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 配置值
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettingUpdateRequest) Reset()         { *m = SettingUpdateRequest{} }
func (m *SettingUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*SettingUpdateRequest) ProtoMessage()    {}
func (*SettingUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{5}
}

func (m *SettingUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettingUpdateRequest.Unmarshal(m, b)
}
func (m *SettingUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettingUpdateRequest.Marshal(b, m, deterministic)
}
func (m *SettingUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettingUpdateRequest.Merge(m, src)
}
func (m *SettingUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_SettingUpdateRequest.Size(m)
}
func (m *SettingUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SettingUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SettingUpdateRequest proto.InternalMessageInfo

func (m *SettingUpdateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SettingUpdateRequest) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type DinnerSettingValueResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 参数值，注意是字符串类型
	Value                string   `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSettingValueResponse) Reset()         { *m = DinnerSettingValueResponse{} }
func (m *DinnerSettingValueResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerSettingValueResponse) ProtoMessage()    {}
func (*DinnerSettingValueResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{6}
}

func (m *DinnerSettingValueResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSettingValueResponse.Unmarshal(m, b)
}
func (m *DinnerSettingValueResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSettingValueResponse.Marshal(b, m, deterministic)
}
func (m *DinnerSettingValueResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSettingValueResponse.Merge(m, src)
}
func (m *DinnerSettingValueResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerSettingValueResponse.Size(m)
}
func (m *DinnerSettingValueResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSettingValueResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSettingValueResponse proto.InternalMessageInfo

func (m *DinnerSettingValueResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerSettingValueResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DinnerSettingValueResponse) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// 活动状态
type DinnerStateRequest struct {
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerStateRequest) Reset()         { *m = DinnerStateRequest{} }
func (m *DinnerStateRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerStateRequest) ProtoMessage()    {}
func (*DinnerStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{7}
}

func (m *DinnerStateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerStateRequest.Unmarshal(m, b)
}
func (m *DinnerStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerStateRequest.Marshal(b, m, deterministic)
}
func (m *DinnerStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerStateRequest.Merge(m, src)
}
func (m *DinnerStateRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerStateRequest.Size(m)
}
func (m *DinnerStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerStateRequest proto.InternalMessageInfo

func (m *DinnerStateRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type DinnerStateData struct {
	// 当前状态，会先判断活动有效性， 0关闭了、1结束了、2未开始、3未参加无消费记录，4未参加有消费记录、5参加过、6未登录
	State int32 `protobuf:"varint,1,opt,name=state,proto3" json:"state"`
	// 捐赠商品id，当state=3有效
	GoodsId string `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	// 可捐赠份数，当state=4有效
	Qty int32 `protobuf:"varint,3,opt,name=qty,proto3" json:"qty"`
	// 捐赠总人数，自带单位如万
	DonateNumber         string   `protobuf:"bytes,4,opt,name=donate_number,json=donateNumber,proto3" json:"donate_number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerStateData) Reset()         { *m = DinnerStateData{} }
func (m *DinnerStateData) String() string { return proto.CompactTextString(m) }
func (*DinnerStateData) ProtoMessage()    {}
func (*DinnerStateData) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{8}
}

func (m *DinnerStateData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerStateData.Unmarshal(m, b)
}
func (m *DinnerStateData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerStateData.Marshal(b, m, deterministic)
}
func (m *DinnerStateData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerStateData.Merge(m, src)
}
func (m *DinnerStateData) XXX_Size() int {
	return xxx_messageInfo_DinnerStateData.Size(m)
}
func (m *DinnerStateData) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerStateData.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerStateData proto.InternalMessageInfo

func (m *DinnerStateData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *DinnerStateData) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *DinnerStateData) GetQty() int32 {
	if m != nil {
		return m.Qty
	}
	return 0
}

func (m *DinnerStateData) GetDonateNumber() string {
	if m != nil {
		return m.DonateNumber
	}
	return ""
}

type DinnerStateResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 捐赠状态
	Data                 *DinnerStateData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DinnerStateResponse) Reset()         { *m = DinnerStateResponse{} }
func (m *DinnerStateResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerStateResponse) ProtoMessage()    {}
func (*DinnerStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{9}
}

func (m *DinnerStateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerStateResponse.Unmarshal(m, b)
}
func (m *DinnerStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerStateResponse.Marshal(b, m, deterministic)
}
func (m *DinnerStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerStateResponse.Merge(m, src)
}
func (m *DinnerStateResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerStateResponse.Size(m)
}
func (m *DinnerStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerStateResponse proto.InternalMessageInfo

func (m *DinnerStateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerStateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DinnerStateResponse) GetData() *DinnerStateData {
	if m != nil {
		return m.Data
	}
	return nil
}

type DinnerDonateRequest struct {
	// 捐赠类型 1消费、2分享、3没养宠物免费、4支付1分钱捐赠
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerDonateRequest) Reset()         { *m = DinnerDonateRequest{} }
func (m *DinnerDonateRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerDonateRequest) ProtoMessage()    {}
func (*DinnerDonateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{10}
}

func (m *DinnerDonateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerDonateRequest.Unmarshal(m, b)
}
func (m *DinnerDonateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerDonateRequest.Marshal(b, m, deterministic)
}
func (m *DinnerDonateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerDonateRequest.Merge(m, src)
}
func (m *DinnerDonateRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerDonateRequest.Size(m)
}
func (m *DinnerDonateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerDonateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerDonateRequest proto.InternalMessageInfo

func (m *DinnerDonateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DinnerDonateRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

// 活动状态
type DinnerDonateInfoRequest struct {
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerDonateInfoRequest) Reset()         { *m = DinnerDonateInfoRequest{} }
func (m *DinnerDonateInfoRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerDonateInfoRequest) ProtoMessage()    {}
func (*DinnerDonateInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{11}
}

func (m *DinnerDonateInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerDonateInfoRequest.Unmarshal(m, b)
}
func (m *DinnerDonateInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerDonateInfoRequest.Marshal(b, m, deterministic)
}
func (m *DinnerDonateInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerDonateInfoRequest.Merge(m, src)
}
func (m *DinnerDonateInfoRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerDonateInfoRequest.Size(m)
}
func (m *DinnerDonateInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerDonateInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerDonateInfoRequest proto.InternalMessageInfo

func (m *DinnerDonateInfoRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type DinnerDonateInfo struct {
	// 捐赠排名
	Rank int32 `protobuf:"varint,1,opt,name=rank,proto3" json:"rank"`
	// 剩余分享次数
	RemainShareTimes int32 `protobuf:"varint,2,opt,name=remain_share_times,json=remainShareTimes,proto3" json:"remain_share_times"`
	// 剩余可捐赠份数
	RemainDonateQty int32 `protobuf:"varint,3,opt,name=remain_donate_qty,json=remainDonateQty,proto3" json:"remain_donate_qty"`
	// 已捐赠数量
	DonatedQty int32 `protobuf:"varint,4,opt,name=donated_qty,json=donatedQty,proto3" json:"donated_qty"`
	// 消费总金额，元
	ConsumeTotal float64 `protobuf:"fixed64,5,opt,name=consume_total,json=consumeTotal,proto3" json:"consume_total"`
	// 消费兑换比例，每10份兑换
	ConsumeDonateRate    int32    `protobuf:"varint,6,opt,name=consume_donate_rate,json=consumeDonateRate,proto3" json:"consume_donate_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerDonateInfo) Reset()         { *m = DinnerDonateInfo{} }
func (m *DinnerDonateInfo) String() string { return proto.CompactTextString(m) }
func (*DinnerDonateInfo) ProtoMessage()    {}
func (*DinnerDonateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{12}
}

func (m *DinnerDonateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerDonateInfo.Unmarshal(m, b)
}
func (m *DinnerDonateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerDonateInfo.Marshal(b, m, deterministic)
}
func (m *DinnerDonateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerDonateInfo.Merge(m, src)
}
func (m *DinnerDonateInfo) XXX_Size() int {
	return xxx_messageInfo_DinnerDonateInfo.Size(m)
}
func (m *DinnerDonateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerDonateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerDonateInfo proto.InternalMessageInfo

func (m *DinnerDonateInfo) GetRank() int32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *DinnerDonateInfo) GetRemainShareTimes() int32 {
	if m != nil {
		return m.RemainShareTimes
	}
	return 0
}

func (m *DinnerDonateInfo) GetRemainDonateQty() int32 {
	if m != nil {
		return m.RemainDonateQty
	}
	return 0
}

func (m *DinnerDonateInfo) GetDonatedQty() int32 {
	if m != nil {
		return m.DonatedQty
	}
	return 0
}

func (m *DinnerDonateInfo) GetConsumeTotal() float64 {
	if m != nil {
		return m.ConsumeTotal
	}
	return 0
}

func (m *DinnerDonateInfo) GetConsumeDonateRate() int32 {
	if m != nil {
		return m.ConsumeDonateRate
	}
	return 0
}

type DinnerDonateInfoResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 捐赠信息
	Data                 *DinnerDonateInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DinnerDonateInfoResponse) Reset()         { *m = DinnerDonateInfoResponse{} }
func (m *DinnerDonateInfoResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerDonateInfoResponse) ProtoMessage()    {}
func (*DinnerDonateInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{13}
}

func (m *DinnerDonateInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerDonateInfoResponse.Unmarshal(m, b)
}
func (m *DinnerDonateInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerDonateInfoResponse.Marshal(b, m, deterministic)
}
func (m *DinnerDonateInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerDonateInfoResponse.Merge(m, src)
}
func (m *DinnerDonateInfoResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerDonateInfoResponse.Size(m)
}
func (m *DinnerDonateInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerDonateInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerDonateInfoResponse proto.InternalMessageInfo

func (m *DinnerDonateInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerDonateInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DinnerDonateInfoResponse) GetData() *DinnerDonateInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type DinnerSubscribeRequest struct {
	// 订阅的按钮id
	ButtonId int32 `protobuf:"varint,1,opt,name=button_id,json=buttonId,proto3" json:"button_id"`
	// 订阅的状态 'accept'、'reject'、'ban'、'filter'
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 前端不需要传，会从token解析
	Openid               string   `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSubscribeRequest) Reset()         { *m = DinnerSubscribeRequest{} }
func (m *DinnerSubscribeRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerSubscribeRequest) ProtoMessage()    {}
func (*DinnerSubscribeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{14}
}

func (m *DinnerSubscribeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSubscribeRequest.Unmarshal(m, b)
}
func (m *DinnerSubscribeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSubscribeRequest.Marshal(b, m, deterministic)
}
func (m *DinnerSubscribeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSubscribeRequest.Merge(m, src)
}
func (m *DinnerSubscribeRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerSubscribeRequest.Size(m)
}
func (m *DinnerSubscribeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSubscribeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSubscribeRequest proto.InternalMessageInfo

func (m *DinnerSubscribeRequest) GetButtonId() int32 {
	if m != nil {
		return m.ButtonId
	}
	return 0
}

func (m *DinnerSubscribeRequest) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *DinnerSubscribeRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *DinnerSubscribeRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type DinnerSubscribeStateRequest struct {
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSubscribeStateRequest) Reset()         { *m = DinnerSubscribeStateRequest{} }
func (m *DinnerSubscribeStateRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerSubscribeStateRequest) ProtoMessage()    {}
func (*DinnerSubscribeStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{15}
}

func (m *DinnerSubscribeStateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSubscribeStateRequest.Unmarshal(m, b)
}
func (m *DinnerSubscribeStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSubscribeStateRequest.Marshal(b, m, deterministic)
}
func (m *DinnerSubscribeStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSubscribeStateRequest.Merge(m, src)
}
func (m *DinnerSubscribeStateRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerSubscribeStateRequest.Size(m)
}
func (m *DinnerSubscribeStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSubscribeStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSubscribeStateRequest proto.InternalMessageInfo

func (m *DinnerSubscribeStateRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type DinnerSubscribeStateData struct {
	// 订阅的按钮id，1-9
	ButtonId int32 `protobuf:"varint,1,opt,name=button_id,json=buttonId,proto3" json:"button_id"`
	// 是否订阅了 true or false
	IsSubscribed bool `protobuf:"varint,3,opt,name=is_subscribed,json=isSubscribed,proto3" json:"is_subscribed"`
	// 当is_subscribed = false 时返回需要订阅的模板id
	TemplateId           string   `protobuf:"bytes,4,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSubscribeStateData) Reset()         { *m = DinnerSubscribeStateData{} }
func (m *DinnerSubscribeStateData) String() string { return proto.CompactTextString(m) }
func (*DinnerSubscribeStateData) ProtoMessage()    {}
func (*DinnerSubscribeStateData) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{16}
}

func (m *DinnerSubscribeStateData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSubscribeStateData.Unmarshal(m, b)
}
func (m *DinnerSubscribeStateData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSubscribeStateData.Marshal(b, m, deterministic)
}
func (m *DinnerSubscribeStateData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSubscribeStateData.Merge(m, src)
}
func (m *DinnerSubscribeStateData) XXX_Size() int {
	return xxx_messageInfo_DinnerSubscribeStateData.Size(m)
}
func (m *DinnerSubscribeStateData) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSubscribeStateData.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSubscribeStateData proto.InternalMessageInfo

func (m *DinnerSubscribeStateData) GetButtonId() int32 {
	if m != nil {
		return m.ButtonId
	}
	return 0
}

func (m *DinnerSubscribeStateData) GetIsSubscribed() bool {
	if m != nil {
		return m.IsSubscribed
	}
	return false
}

func (m *DinnerSubscribeStateData) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

type DinnerSubscribeStateResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订阅状态数据
	Data                 []*DinnerSubscribeStateData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *DinnerSubscribeStateResponse) Reset()         { *m = DinnerSubscribeStateResponse{} }
func (m *DinnerSubscribeStateResponse) String() string { return proto.CompactTextString(m) }
func (*DinnerSubscribeStateResponse) ProtoMessage()    {}
func (*DinnerSubscribeStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{17}
}

func (m *DinnerSubscribeStateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSubscribeStateResponse.Unmarshal(m, b)
}
func (m *DinnerSubscribeStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSubscribeStateResponse.Marshal(b, m, deterministic)
}
func (m *DinnerSubscribeStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSubscribeStateResponse.Merge(m, src)
}
func (m *DinnerSubscribeStateResponse) XXX_Size() int {
	return xxx_messageInfo_DinnerSubscribeStateResponse.Size(m)
}
func (m *DinnerSubscribeStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSubscribeStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSubscribeStateResponse proto.InternalMessageInfo

func (m *DinnerSubscribeStateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DinnerSubscribeStateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DinnerSubscribeStateResponse) GetData() []*DinnerSubscribeStateData {
	if m != nil {
		return m.Data
	}
	return nil
}

type DinnerSubscribeSendRequest struct {
	// 要发送的订阅id
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	// 发送的模板数据，json字符串
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	// 跳转的页面
	Page string `protobuf:"bytes,3,opt,name=page,proto3" json:"page"`
	// 签名
	Sign                 string   `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DinnerSubscribeSendRequest) Reset()         { *m = DinnerSubscribeSendRequest{} }
func (m *DinnerSubscribeSendRequest) String() string { return proto.CompactTextString(m) }
func (*DinnerSubscribeSendRequest) ProtoMessage()    {}
func (*DinnerSubscribeSendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_56ca13cbea607a3b, []int{18}
}

func (m *DinnerSubscribeSendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DinnerSubscribeSendRequest.Unmarshal(m, b)
}
func (m *DinnerSubscribeSendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DinnerSubscribeSendRequest.Marshal(b, m, deterministic)
}
func (m *DinnerSubscribeSendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DinnerSubscribeSendRequest.Merge(m, src)
}
func (m *DinnerSubscribeSendRequest) XXX_Size() int {
	return xxx_messageInfo_DinnerSubscribeSendRequest.Size(m)
}
func (m *DinnerSubscribeSendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DinnerSubscribeSendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DinnerSubscribeSendRequest proto.InternalMessageInfo

func (m *DinnerSubscribeSendRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *DinnerSubscribeSendRequest) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *DinnerSubscribeSendRequest) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *DinnerSubscribeSendRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func init() {
	proto.RegisterType((*DinnerResponse)(nil), "ac.DinnerResponse")
	proto.RegisterType((*DinnerEmptyRequest)(nil), "ac.DinnerEmptyRequest")
	proto.RegisterType((*DinnerSettingsData)(nil), "ac.DinnerSettingsData")
	proto.RegisterType((*DinnerSettingsResponse)(nil), "ac.DinnerSettingsResponse")
	proto.RegisterType((*DinnerSettingValueRequest)(nil), "ac.DinnerSettingValueRequest")
	proto.RegisterType((*SettingUpdateRequest)(nil), "ac.SettingUpdateRequest")
	proto.RegisterType((*DinnerSettingValueResponse)(nil), "ac.DinnerSettingValueResponse")
	proto.RegisterType((*DinnerStateRequest)(nil), "ac.DinnerStateRequest")
	proto.RegisterType((*DinnerStateData)(nil), "ac.DinnerStateData")
	proto.RegisterType((*DinnerStateResponse)(nil), "ac.DinnerStateResponse")
	proto.RegisterType((*DinnerDonateRequest)(nil), "ac.DinnerDonateRequest")
	proto.RegisterType((*DinnerDonateInfoRequest)(nil), "ac.DinnerDonateInfoRequest")
	proto.RegisterType((*DinnerDonateInfo)(nil), "ac.DinnerDonateInfo")
	proto.RegisterType((*DinnerDonateInfoResponse)(nil), "ac.DinnerDonateInfoResponse")
	proto.RegisterType((*DinnerSubscribeRequest)(nil), "ac.DinnerSubscribeRequest")
	proto.RegisterType((*DinnerSubscribeStateRequest)(nil), "ac.DinnerSubscribeStateRequest")
	proto.RegisterType((*DinnerSubscribeStateData)(nil), "ac.DinnerSubscribeStateData")
	proto.RegisterType((*DinnerSubscribeStateResponse)(nil), "ac.DinnerSubscribeStateResponse")
	proto.RegisterType((*DinnerSubscribeSendRequest)(nil), "ac.DinnerSubscribeSendRequest")
}

func init() { proto.RegisterFile("ac/dinner.proto", fileDescriptor_56ca13cbea607a3b) }

var fileDescriptor_56ca13cbea607a3b = []byte{
	// 878 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xdd, 0x8e, 0xdb, 0x44,
	0x14, 0x56, 0x36, 0xeb, 0x6d, 0x72, 0x36, 0xd9, 0x4d, 0x67, 0xa3, 0x8d, 0x9b, 0x5d, 0xda, 0xc8,
	0xbd, 0x20, 0xaa, 0x20, 0x45, 0x41, 0x45, 0x48, 0x40, 0x85, 0x60, 0x11, 0x8a, 0x04, 0x88, 0x3a,
	0x85, 0x5b, 0x33, 0xf1, 0x0c, 0xc1, 0x6c, 0x3c, 0x76, 0x3d, 0x93, 0xa2, 0x48, 0xc0, 0x4b, 0xf0,
	0x1a, 0x3c, 0x1f, 0xd7, 0x68, 0xfe, 0x9c, 0xf1, 0xc6, 0xd9, 0x4a, 0xb9, 0x9b, 0x39, 0xbf, 0xdf,
	0x9c, 0xef, 0x1c, 0x1f, 0xc3, 0x39, 0x8e, 0x9f, 0x93, 0x84, 0x31, 0x5a, 0x4c, 0xf2, 0x22, 0x13,
	0x19, 0x3a, 0xc2, 0x71, 0xf0, 0x12, 0xce, 0x6e, 0x94, 0x2c, 0xa4, 0x3c, 0xcf, 0x18, 0xa7, 0x08,
	0xc1, 0x71, 0x9c, 0x11, 0xea, 0x37, 0x46, 0x8d, 0xb1, 0x17, 0xaa, 0x33, 0xf2, 0xe1, 0x41, 0x4a,
	0x39, 0xc7, 0x4b, 0xea, 0x1f, 0x8d, 0x1a, 0xe3, 0x76, 0x68, 0xaf, 0x41, 0x1f, 0x90, 0xf6, 0xff,
	0x26, 0xcd, 0xc5, 0x26, 0xa4, 0x6f, 0xd6, 0x94, 0x8b, 0xe0, 0x9f, 0x86, 0x15, 0xcf, 0xa9, 0x10,
	0x09, 0x5b, 0xf2, 0x1b, 0x2c, 0x30, 0xba, 0x84, 0x13, 0xca, 0xf0, 0x62, 0xa5, 0x83, 0xb7, 0x43,
	0x73, 0x43, 0x2f, 0x60, 0xf0, 0x07, 0xcd, 0xf1, 0x92, 0x46, 0xab, 0x84, 0xdd, 0x46, 0x39, 0xde,
	0x44, 0x7c, 0x1d, 0xc7, 0x94, 0x73, 0x93, 0xae, 0xaf, 0xd5, 0xdf, 0x25, 0xec, 0xf6, 0x47, 0xbc,
	0x99, 0x6b, 0x1d, 0x9a, 0xc0, 0x45, 0x9c, 0x31, 0xbe, 0x4e, 0x69, 0x44, 0x32, 0x86, 0x05, 0x8d,
	0x0a, 0x2c, 0xa8, 0xdf, 0x54, 0x2e, 0x0f, 0x8d, 0xea, 0x46, 0x69, 0x42, 0x2c, 0x68, 0x50, 0xc0,
	0x65, 0x15, 0xd4, 0x61, 0x6f, 0x46, 0xcf, 0xe0, 0x98, 0x60, 0x81, 0x55, 0xa2, 0xd3, 0xe9, 0xe5,
	0x04, 0xc7, 0x93, 0xdd, 0xc7, 0x86, 0xca, 0x26, 0x78, 0x0e, 0x8f, 0x2a, 0xba, 0x9f, 0xf1, 0x6a,
	0x4d, 0x4d, 0x99, 0x64, 0x5a, 0x86, 0x53, 0x5b, 0x0d, 0x75, 0x0e, 0xbe, 0x84, 0xbe, 0x31, 0xfd,
	0x29, 0x27, 0x12, 0xf9, 0x7e, 0x5b, 0xd4, 0x07, 0xef, 0xad, 0x8c, 0x67, 0x00, 0xea, 0x4b, 0xf0,
	0x0b, 0x0c, 0xeb, 0x52, 0x1e, 0xf4, 0xd4, 0x32, 0x43, 0xd3, 0xcd, 0xf0, 0x61, 0xc9, 0xae, 0x70,
	0x10, 0x0e, 0xe0, 0x01, 0x8f, 0x8b, 0x34, 0x4a, 0x88, 0xa5, 0x57, 0x5e, 0x67, 0x24, 0xd8, 0xc0,
	0xb9, 0x63, 0xae, 0x3a, 0xa1, 0x0f, 0x1e, 0x97, 0x17, 0x03, 0x43, 0x5f, 0xd0, 0x23, 0x68, 0x2d,
	0xb3, 0x8c, 0x70, 0x19, 0xc2, 0x00, 0x51, 0xf7, 0x19, 0x41, 0x3d, 0x68, 0xbe, 0x11, 0x1b, 0x05,
	0xc3, 0x0b, 0xe5, 0x11, 0x3d, 0x85, 0xae, 0x61, 0x9d, 0xad, 0xd3, 0x05, 0x2d, 0xfc, 0x63, 0xe5,
	0xd1, 0xd1, 0xc2, 0x1f, 0x94, 0x2c, 0x58, 0xc1, 0x45, 0x05, 0xe9, 0x41, 0x45, 0x78, 0xbf, 0xc2,
	0xf7, 0x85, 0xc3, 0xb7, 0x7d, 0x8f, 0x21, 0xfb, 0x2b, 0x9b, 0xcd, 0x34, 0xdd, 0x96, 0x3a, 0xb1,
	0xc9, 0xcb, 0x6c, 0xf2, 0xec, 0x16, 0xeb, 0xa8, 0x52, 0xac, 0x29, 0x0c, 0xdc, 0x18, 0x33, 0xf6,
	0x6b, 0xf6, 0xce, 0x02, 0xff, 0xd7, 0x80, 0xde, 0x5d, 0x27, 0x99, 0xb5, 0xc0, 0xec, 0xd6, 0x66,
	0x95, 0x67, 0xf4, 0x01, 0xa0, 0x82, 0xa6, 0x38, 0x61, 0x11, 0xff, 0x0d, 0x17, 0x34, 0x12, 0x49,
	0x4a, 0xf5, 0x8c, 0x79, 0x61, 0x4f, 0x6b, 0xe6, 0x52, 0xf1, 0x5a, 0xca, 0xd1, 0x33, 0x78, 0x68,
	0xac, 0x4d, 0xa1, 0xb7, 0x0c, 0x9c, 0x6b, 0x85, 0x4e, 0xf7, 0x4a, 0x6c, 0xd0, 0x13, 0x38, 0xd5,
	0x46, 0x44, 0x59, 0x1d, 0x2b, 0x2b, 0x30, 0xa2, 0x57, 0x9a, 0x2e, 0x3b, 0xac, 0x22, 0x13, 0x78,
	0xe5, 0x7b, 0xa3, 0xc6, 0xb8, 0x11, 0x76, 0x8c, 0xf0, 0xb5, 0x94, 0xed, 0x9b, 0xe8, 0x13, 0x15,
	0xad, 0x76, 0xa2, 0xfd, 0xdd, 0x62, 0x1d, 0xc4, 0xf1, 0xb8, 0xc2, 0x71, 0x7f, 0xcb, 0xb1, 0x13,
	0x59, 0x93, 0xfc, 0x67, 0xf9, 0x15, 0x59, 0x2f, 0x78, 0x5c, 0x24, 0x8b, 0x92, 0xe7, 0x2b, 0x68,
	0x2f, 0xd6, 0x42, 0x64, 0xcc, 0x32, 0xe4, 0x85, 0x2d, 0x2d, 0x98, 0x91, 0x6d, 0xc7, 0x9b, 0x59,
	0xd5, 0x1d, 0xef, 0x50, 0xda, 0x74, 0x29, 0x95, 0x9f, 0xca, 0x2c, 0xa7, 0x2c, 0x21, 0xa6, 0xad,
	0xcd, 0x2d, 0xf8, 0x04, 0xae, 0xee, 0x64, 0xdf, 0x37, 0x83, 0xd5, 0xb6, 0xfa, 0xcb, 0x56, 0xaa,
	0xea, 0xa7, 0x86, 0xf1, 0x5e, 0xdc, 0x4f, 0xa1, 0x9b, 0xf0, 0x88, 0x5b, 0x2f, 0x8d, 0xb3, 0x15,
	0x76, 0x12, 0x5e, 0x46, 0x22, 0x92, 0x7d, 0x41, 0xd3, 0x7c, 0x25, 0x19, 0x2b, 0x21, 0x83, 0x15,
	0xcd, 0x48, 0xf0, 0x37, 0x5c, 0xd7, 0xc3, 0x3e, 0x88, 0xac, 0x8f, 0x4a, 0xb2, 0x9a, 0xe3, 0xd3,
	0xe9, 0xb5, 0x33, 0x90, 0x3b, 0x8f, 0x33, 0xa4, 0xfd, 0x5e, 0x7e, 0x13, 0x4b, 0x0b, 0xca, 0x88,
	0xad, 0x5a, 0x0f, 0x9a, 0x09, 0xe1, 0x66, 0xa8, 0xe4, 0x51, 0xe2, 0x51, 0x19, 0x74, 0x62, 0x75,
	0x96, 0x32, 0xb9, 0x84, 0x0c, 0x51, 0xea, 0x2c, 0x65, 0x3c, 0x59, 0x32, 0xf3, 0x62, 0x75, 0x9e,
	0xfe, 0xeb, 0x41, 0xd7, 0x7e, 0x80, 0x8b, 0xb7, 0x49, 0x4c, 0xd1, 0x4b, 0x68, 0xd9, 0xd5, 0x80,
	0x9c, 0x75, 0xe1, 0xae, 0xcc, 0xe1, 0x70, 0x77, 0x8d, 0x94, 0xd5, 0xf9, 0x1c, 0xce, 0xac, 0x4c,
	0x2f, 0x05, 0xb4, 0x67, 0xe9, 0x0c, 0xd1, 0x56, 0x5e, 0x7a, 0x7f, 0x0f, 0x1d, 0x77, 0x13, 0xa0,
	0xf7, 0x76, 0x7c, 0xdd, 0xa5, 0x34, 0x7c, 0xbc, 0x4f, 0x6d, 0xc2, 0x7d, 0x0a, 0x9e, 0xaa, 0x6e,
	0x05, 0x83, 0xd3, 0x83, 0xc3, 0xc1, 0x8e, 0xdc, 0x78, 0xbe, 0x80, 0x13, 0x3d, 0x4d, 0x68, 0x70,
	0x77, 0xbe, 0xac, 0x6f, 0x1d, 0xfe, 0x6f, 0x01, 0x9c, 0xcf, 0xda, 0x55, 0xed, 0x68, 0x1a, 0xf7,
	0xeb, 0x7a, 0xa5, 0x09, 0xf4, 0x19, 0xb4, 0x4b, 0xfa, 0xd1, 0xb0, 0xa6, 0x6b, 0xee, 0x43, 0x31,
	0x87, 0xb3, 0x6a, 0x77, 0xa1, 0x27, 0xfb, 0xfa, 0xce, 0x86, 0x19, 0xed, 0x37, 0x30, 0x41, 0xbf,
	0x86, 0x6e, 0xa5, 0x21, 0xd1, 0xe3, 0x3a, 0x97, 0x6d, 0xa7, 0xd6, 0x22, 0xfb, 0x02, 0xba, 0x95,
	0x3f, 0x06, 0xe4, 0x4b, 0xa3, 0xba, 0x9f, 0x88, 0x3a, 0xf7, 0xc5, 0x89, 0xfa, 0x19, 0xfc, 0xf8,
	0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x51, 0x05, 0x70, 0xa0, 0x1f, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DinnerServiceClient is the client API for DinnerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DinnerServiceClient interface {
	// 设置列表
	Settings(ctx context.Context, in *DinnerEmptyRequest, opts ...grpc.CallOption) (*DinnerSettingsResponse, error)
	// 设置更新
	SettingsUpdate(ctx context.Context, in *DinnerSettingsData, opts ...grpc.CallOption) (*DinnerResponse, error)
	// 参数值
	SettingValue(ctx context.Context, in *DinnerSettingValueRequest, opts ...grpc.CallOption) (*DinnerSettingValueResponse, error)
	// 活动状态
	State(ctx context.Context, in *DinnerStateRequest, opts ...grpc.CallOption) (*DinnerStateResponse, error)
	// 捐赠
	Donate(ctx context.Context, in *DinnerDonateRequest, opts ...grpc.CallOption) (*DinnerResponse, error)
	// 捐赠信息
	DonateInfo(ctx context.Context, in *DinnerDonateInfoRequest, opts ...grpc.CallOption) (*DinnerDonateInfoResponse, error)
	// 订阅消息
	Subscribe(ctx context.Context, in *DinnerSubscribeRequest, opts ...grpc.CallOption) (*DinnerResponse, error)
	// 查询所有按钮订阅状态
	SubscribeState(ctx context.Context, in *DinnerSubscribeStateRequest, opts ...grpc.CallOption) (*DinnerSubscribeStateResponse, error)
	// 发送订阅消息
	SubscribeSend(ctx context.Context, in *DinnerSubscribeSendRequest, opts ...grpc.CallOption) (*DinnerResponse, error)
	// 单条配置更新
	SettingUpdate(ctx context.Context, in *SettingUpdateRequest, opts ...grpc.CallOption) (*DinnerResponse, error)
}

type dinnerServiceClient struct {
	cc *grpc.ClientConn
}

func NewDinnerServiceClient(cc *grpc.ClientConn) DinnerServiceClient {
	return &dinnerServiceClient{cc}
}

func (c *dinnerServiceClient) Settings(ctx context.Context, in *DinnerEmptyRequest, opts ...grpc.CallOption) (*DinnerSettingsResponse, error) {
	out := new(DinnerSettingsResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/Settings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) SettingsUpdate(ctx context.Context, in *DinnerSettingsData, opts ...grpc.CallOption) (*DinnerResponse, error) {
	out := new(DinnerResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/SettingsUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) SettingValue(ctx context.Context, in *DinnerSettingValueRequest, opts ...grpc.CallOption) (*DinnerSettingValueResponse, error) {
	out := new(DinnerSettingValueResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/SettingValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) State(ctx context.Context, in *DinnerStateRequest, opts ...grpc.CallOption) (*DinnerStateResponse, error) {
	out := new(DinnerStateResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/State", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) Donate(ctx context.Context, in *DinnerDonateRequest, opts ...grpc.CallOption) (*DinnerResponse, error) {
	out := new(DinnerResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/Donate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) DonateInfo(ctx context.Context, in *DinnerDonateInfoRequest, opts ...grpc.CallOption) (*DinnerDonateInfoResponse, error) {
	out := new(DinnerDonateInfoResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/DonateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) Subscribe(ctx context.Context, in *DinnerSubscribeRequest, opts ...grpc.CallOption) (*DinnerResponse, error) {
	out := new(DinnerResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/Subscribe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) SubscribeState(ctx context.Context, in *DinnerSubscribeStateRequest, opts ...grpc.CallOption) (*DinnerSubscribeStateResponse, error) {
	out := new(DinnerSubscribeStateResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/SubscribeState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) SubscribeSend(ctx context.Context, in *DinnerSubscribeSendRequest, opts ...grpc.CallOption) (*DinnerResponse, error) {
	out := new(DinnerResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/SubscribeSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dinnerServiceClient) SettingUpdate(ctx context.Context, in *SettingUpdateRequest, opts ...grpc.CallOption) (*DinnerResponse, error) {
	out := new(DinnerResponse)
	err := c.cc.Invoke(ctx, "/ac.DinnerService/SettingUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DinnerServiceServer is the server API for DinnerService service.
type DinnerServiceServer interface {
	// 设置列表
	Settings(context.Context, *DinnerEmptyRequest) (*DinnerSettingsResponse, error)
	// 设置更新
	SettingsUpdate(context.Context, *DinnerSettingsData) (*DinnerResponse, error)
	// 参数值
	SettingValue(context.Context, *DinnerSettingValueRequest) (*DinnerSettingValueResponse, error)
	// 活动状态
	State(context.Context, *DinnerStateRequest) (*DinnerStateResponse, error)
	// 捐赠
	Donate(context.Context, *DinnerDonateRequest) (*DinnerResponse, error)
	// 捐赠信息
	DonateInfo(context.Context, *DinnerDonateInfoRequest) (*DinnerDonateInfoResponse, error)
	// 订阅消息
	Subscribe(context.Context, *DinnerSubscribeRequest) (*DinnerResponse, error)
	// 查询所有按钮订阅状态
	SubscribeState(context.Context, *DinnerSubscribeStateRequest) (*DinnerSubscribeStateResponse, error)
	// 发送订阅消息
	SubscribeSend(context.Context, *DinnerSubscribeSendRequest) (*DinnerResponse, error)
	// 单条配置更新
	SettingUpdate(context.Context, *SettingUpdateRequest) (*DinnerResponse, error)
}

// UnimplementedDinnerServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDinnerServiceServer struct {
}

func (*UnimplementedDinnerServiceServer) Settings(ctx context.Context, req *DinnerEmptyRequest) (*DinnerSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Settings not implemented")
}
func (*UnimplementedDinnerServiceServer) SettingsUpdate(ctx context.Context, req *DinnerSettingsData) (*DinnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SettingsUpdate not implemented")
}
func (*UnimplementedDinnerServiceServer) SettingValue(ctx context.Context, req *DinnerSettingValueRequest) (*DinnerSettingValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SettingValue not implemented")
}
func (*UnimplementedDinnerServiceServer) State(ctx context.Context, req *DinnerStateRequest) (*DinnerStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method State not implemented")
}
func (*UnimplementedDinnerServiceServer) Donate(ctx context.Context, req *DinnerDonateRequest) (*DinnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Donate not implemented")
}
func (*UnimplementedDinnerServiceServer) DonateInfo(ctx context.Context, req *DinnerDonateInfoRequest) (*DinnerDonateInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DonateInfo not implemented")
}
func (*UnimplementedDinnerServiceServer) Subscribe(ctx context.Context, req *DinnerSubscribeRequest) (*DinnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}
func (*UnimplementedDinnerServiceServer) SubscribeState(ctx context.Context, req *DinnerSubscribeStateRequest) (*DinnerSubscribeStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeState not implemented")
}
func (*UnimplementedDinnerServiceServer) SubscribeSend(ctx context.Context, req *DinnerSubscribeSendRequest) (*DinnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeSend not implemented")
}
func (*UnimplementedDinnerServiceServer) SettingUpdate(ctx context.Context, req *SettingUpdateRequest) (*DinnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SettingUpdate not implemented")
}

func RegisterDinnerServiceServer(s *grpc.Server, srv DinnerServiceServer) {
	s.RegisterService(&_DinnerService_serviceDesc, srv)
}

func _DinnerService_Settings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).Settings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/Settings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).Settings(ctx, req.(*DinnerEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_SettingsUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerSettingsData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).SettingsUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/SettingsUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).SettingsUpdate(ctx, req.(*DinnerSettingsData))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_SettingValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerSettingValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).SettingValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/SettingValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).SettingValue(ctx, req.(*DinnerSettingValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_State_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).State(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/State",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).State(ctx, req.(*DinnerStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_Donate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerDonateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).Donate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/Donate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).Donate(ctx, req.(*DinnerDonateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_DonateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerDonateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).DonateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/DonateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).DonateInfo(ctx, req.(*DinnerDonateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_Subscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).Subscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/Subscribe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).Subscribe(ctx, req.(*DinnerSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_SubscribeState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerSubscribeStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).SubscribeState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/SubscribeState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).SubscribeState(ctx, req.(*DinnerSubscribeStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_SubscribeSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DinnerSubscribeSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).SubscribeSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/SubscribeSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).SubscribeSend(ctx, req.(*DinnerSubscribeSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DinnerService_SettingUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettingUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DinnerServiceServer).SettingUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.DinnerService/SettingUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DinnerServiceServer).SettingUpdate(ctx, req.(*SettingUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DinnerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.DinnerService",
	HandlerType: (*DinnerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Settings",
			Handler:    _DinnerService_Settings_Handler,
		},
		{
			MethodName: "SettingsUpdate",
			Handler:    _DinnerService_SettingsUpdate_Handler,
		},
		{
			MethodName: "SettingValue",
			Handler:    _DinnerService_SettingValue_Handler,
		},
		{
			MethodName: "State",
			Handler:    _DinnerService_State_Handler,
		},
		{
			MethodName: "Donate",
			Handler:    _DinnerService_Donate_Handler,
		},
		{
			MethodName: "DonateInfo",
			Handler:    _DinnerService_DonateInfo_Handler,
		},
		{
			MethodName: "Subscribe",
			Handler:    _DinnerService_Subscribe_Handler,
		},
		{
			MethodName: "SubscribeState",
			Handler:    _DinnerService_SubscribeState_Handler,
		},
		{
			MethodName: "SubscribeSend",
			Handler:    _DinnerService_SubscribeSend_Handler,
		},
		{
			MethodName: "SettingUpdate",
			Handler:    _DinnerService_SettingUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/dinner.proto",
}

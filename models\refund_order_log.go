package models

import (
	"time"
)

type RefundOrderLog struct {
	Id              int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	RefundSn        string    `xorm:"default 'NULL' comment('退款单号') index VARCHAR(36)"`
	OldOrderSn      string    `xorm:"not null default '''' comment('渠道订单号') VARCHAR(50)"`
	Ctime           time.Time `xorm:"default 'NULL' comment('申请时间') index DATETIME"`
	Reason          string    `xorm:"default 'NULL' comment('申请退款的原因') VARCHAR(500)"`
	Money           int       `xorm:"default NULL comment('退款金额') INT(11)"`
	Pictures        string    `xorm:"default 'NULL' comment('用户申请退款时上传的退款图片，多个图片url以英文逗号隔开') VARCHAR(1000)"`
	ApplyOpUserType string    `xorm:"default 'NULL' comment('推送当前仅退款或退货退款流程的发起方，是用户还是商家；仅适用于支持退货退款的商家。') VARCHAR(100)"`
	OperationType   string    `xorm:"default 'NULL' comment('操作类型') VARCHAR(100)"`
	Operationer     string    `xorm:"default 'NULL' comment('操作人') VARCHAR(100)"`
	NotifyType      string    `xorm:"default 'NULL' comment('通知类型，part：发起部分退款；agree：确认退款；reject：驳回退款；cancelRefund：用户取消退款申请；cancelRefundComplaint：用户取消退款申诉；') VARCHAR(50)"`
	ResType         string    `xorm:"default 'NULL' comment('退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。') index VARCHAR(100)"`
	CreateTime      time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime      time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

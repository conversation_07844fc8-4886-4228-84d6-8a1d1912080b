syntax = "proto3";
import "dgc/order.proto";
// 医生可抢订单相关
package dgc;

// @Desc    	在线问诊 抢单模块
// <AUTHOR>
// @Date		2021-10-12
service PreemptService {
  // @Desc    	医生端-抢单列表
  // <AUTHOR>
  // @Date		2021-10-09
  rpc PreemptList(PreemptListRequest) returns (PreemptListResponse) {}
  // @Desc    	医生端-清单操作
  // <AUTHOR>
  // @Date		2021-10-12
  rpc PreemptOperate(PreemptOperateRequest) returns (PreemptOperateResponse){}


}

message PreemptListRequest{
}
//医生端-接诊操作
message PreemptOperateRequest{
  string order_sn = 1;
}
//医生端-接诊操作
message PreemptOperateResponse{
  int64 affect_rows = 1;
  string scrm_user_id = 2;
}


//抢单列表
message PreemptListResponse {
  //未被抢
  repeated Order normal = 1;
  //已被抢
  repeated Order preempted = 2;
}

package services

const (
	//渠道Id
	ChannelAwenId        = iota + 1 //阿闻
	ChannelMtId                     //美团
	ChannelElmId                    //饿了么
	ChannelJddjId                   //京东到家
	ChannelMallId                   //阿闻电商
	ChannelStore                    //门店
	ChannelBaiDu                    //百度
	ChannelH5                       //h5
	ChannelDigitalHealth            //互联网医院
	ChannelIdOfflineShop = 100      // 线下门店
	ChannelWJTZ          = 420      //物竞天择
	/************************* mq start ***************************/
	MQExchange = "ordercenter"

	//订单导出队列
	OrderExportTaskQueue = "dc-sz-order-center-export-task"
	//OrderExportTaskQueue = "dc-sz-order-center-export-task-test"
	//OrderExportTaskQueue = "dc-sz-order-center-export-test-task-xrp"

	//阿闻已支付订单
	AwenPayedOrderKey = "oc:payed:"

	//超时未支付取消任务路由及队列
	AwenNoPayCancelExchange = "ordercenter.dlx"
	AwenNoPayCancelRoute    = "order.nopay"
	AwenNoPayCancelQueue    = "order-center:order-no-pay-queue-dlx"

	//配送没接单取消切换队列
	DeliveryOrderNoPickExchange = "ordercenter.dlx"
	DeliveryOrderNoPickRoute    = "order.delivery.noPick"
	DeliveryOrderNoPickQueue    = "order-center:orderDelivery-no-pick-queue-dlx"

	// 订单推送子龙失败重推
	QueueOrderRePushToZiLongExchange = "ordercenter.delayed"
	QueueOrderRePushToZiLongRoute    = "zilong.repush"
	QueueOrderRePushToZiLongQueue    = "order-center:order-push-zilong"

	// 订单推送互联网医疗
	QueuePushDigitalHealthOrder = "dc-sz-order-center-pushDigitalHealthOrder"
	/************************* mq end ***************************/

	/************************* redis start ***************************/
	//备货时间到期自动备货完成
	SelfCollectionPicking = "order-center:cancel-order-picking-time"

	//AwenNoPayFiveCancel = "order-center:order-five-no-pay-cancel"
	//AwenNoPayCancelList = "order-center:order-no-pay-cancel"
	/************************* redis end ***************************/

	//主体ID标识
	DSMainId   = "1" //电商
	JCJMainId  = "2" //极宠家
	CSYMainId  = "3" //宠商云
	BLKYMainId = "4" //百林康源
	SAASMainId = "6" //saas连锁主体ID

	// AppChennel 店铺主体
	SaasAppChannel = 12
)

var (
	appkeyCon    = "" // "000000200321085863"
	appsecretCon = "" //"e29841979ffb437ab9bf92ed6c3fc0a4"
	token        = "" // "c1246fbe87944deda1bd2afdcad9be99"
	apiUrl       = "" // http://local.gjpqqd.com:5918/Service/ERPService.asmx/EMallApi" + "?"
	goodsurl     = ""
	systemid     = "2"

	appkeyConNew    = "" // "000000200321085863"
	appsecretConNew = "" //"e29841979ffb437ab9bf92ed6c3fc0a4"
	tokenNew        = "" // "c1246fbe87944deda1bd2afdcad9be99"

	orderApiUrl = ""

	//渠道名称
	ChannelName = map[int32]string{
		ChannelAwenId: "阿闻到家",
		ChannelMtId:   "美团",
		ChannelElmId:  "饿了么",
		ChannelJddjId: "京东到家",
	}

	//用作逍宠打印小票
	PrintChannelName = map[int32]string{
		ChannelAwenId: "小程序商城",
		ChannelMtId:   "美团",
		ChannelElmId:  "饿了么",
		ChannelJddjId: "京东到家",
	}

	//渠道门店关系redis key映射
	RelationKeyMap = map[int][]string{
		ChannelAwenId: {"store:relation:dctozl", "store:relation:zltodc"},
		ChannelMtId:   {"store:relation:dctomt", "store:relation:mttodc"},
		ChannelElmId:  {"store:relation:dctoele", "store:relation:eletodc"},
		ChannelJddjId: {"store:relation:dctojddj", "store:relation:jddjtodc"},
	}

	//配送类型和映射
	DeliveryTypeKeyMap = map[int]string{
		0: "美团",
		1: "闪送",
		2: "自配",
		3: "达达",
		4: "蜂鸟",
		5: "麦芽田",
	}

	exceptionml = map[int32]string{
		10001: "顾客电话关机",
		10002: "顾客电话已停机",
		10003: "顾客电话无人接听",
		10004: "顾客电话为空号",
		10005: "顾客留错电话",
		10006: "联系不上顾客其他原因",
		10101: "顾客更改收货地址",
		10201: "送货地址超区",
		10203: "顾客要求延迟配送",
		10202: "顾客拒收货品",
		10401: "商家关店/未营业",
	}

	omsApiUrl = "http://qimenapi.tbsandbox.com/router/qimen/service"

	//定义映射map
	PayMode = map[int32]string{
		1: "支付宝",
		2: "微信",
		3: "美团",
		4: "其它",
		5: "饿了么",
		6: "京东支付",
		8: "储值卡支付",
	}
	OrderFrom = map[int32]string{
		ChannelAwenId:          "阿闻到家",
		ChannelMtId:            "美团",
		ChannelElmId:           "饿了么",
		ChannelJddjId:          "京东到家",
		ChannelJddjId*100 + 20: "物竞天择",
		ChannelDigitalHealth:   "互联网医院",
		ChannelIdOfflineShop:   "线下门店",
	}
	UserAgent = map[int32]string{
		1: "Android",
		2: "iOS",
		3: "小程序",
		4: "公众号",
		5: "Web",
		6: "其它",
		7: "竖屏",
	}
	OrderType = map[int32]string{
		1: "普通订单",
		2: "预订订单",
		3: "门店自提",
		4: "拼团订单",
		5: "门店配送",
	}
	DeliveryType = map[int32]string{
		1: "快递",
		2: "外卖",
		3: "自提",
		4: "同城送",
		5: "外卖", //商家自配 属于外卖
		6: "店内销售",
	}
	OrderStatusMap = map[int32]string{
		20101: "未接单",
		20102: "已接单",
		20103: "配送中",
		20104: "已送达",
		20105: "已取货",
		20106: "已完成",
		20107: "已取消",
		10201: "未付款",
		20201: "待发货",
		20202: "全部发货",
		20203: "确认收货",
		20204: "部分发货",
	}

	//订单状态：0已取消,10(默认)未付款,20已付款,30已完成
	OrderMainStatusMap = map[int32]string{
		0:  "已取消",
		10: "未付款",
		20: "已付款",
		30: "已完成",
	}
	//仓库所属
	Source = map[int32]string{
		1: "前置仓",
		2: "管易",
		3: "门店仓",
		4: "瑞鹏oms",
		5: "电商仓",
	}
	//退款类型
	Refundtype = map[int32]string{
		1: "仅退款",
		2: "退款退货",
	}
	// 订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
	RefundState = map[int32]string{
		1: "退款中",
		2: "退款关闭",
		3: "退款成功",
		4: "退款处理中",
		5: "退款处理中",
		6: "初审通过",
		7: "终审通过",
		8: "退款失败",
	}

	//优惠活动类型
	FreightType = map[int32]struct{}{
		16:   {},
		21:   {},
		25:   {},
		30:   {},
		48:   {},
		54:   {},
		304:  {},
		310:  {},
		4011: {},
		507:  {},
		508:  {},
		512:  {},
		515:  {},
		516:  {},
		528:  {},
	}

	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)
	Category = map[int32]string{
		1: "电商仓",
		2: "区域仓",
		3: "门店仓",
		4: "前置仓",
		5: "前置仓虚拟仓",
	}
	//广告用户行为类型(1-页面访问，2-下单，3-付费，4-下单，5-付费)
	MpActionType = map[int32]string{
		1: "VIEW_CONTENT",
		2: "COMPLETE_ORDER",
		3: "PURCHASE",
	}
)

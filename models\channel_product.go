package models

import (
	"time"
)

type ChannelProduct struct {
	Id                   int32     `xorm:"not null pk comment('商品id') INT(11)"`
	ChannelId            int32     `xorm:"not null pk comment('渠道id') INT(11)"`
	CategoryId           int32     `xorm:"default NULL comment('分类id') INT(11)"`
	BrandId              int32     `xorm:"default NULL comment('品牌id') INT(11)"`
	Name                 string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(255)"`
	ShortName            string    `xorm:"default 'NULL' comment('商品短标题') VARCHAR(255)"`
	Code                 string    `xorm:"default 'NULL' comment('商品编号') VARCHAR(36)"`
	BarCode              string    `xorm:"default 'NULL' comment('商品条码') VARCHAR(36)"`
	CreateDate           time.Time `xorm:"default comment('商品添加日期') DATETIME created"`
	UpdateDate           time.Time `xorm:"default 'NULL' comment('商品最后更新日期') DATETIME updated"`
	IsDel                int32     `xorm:"default 0 comment('是否删除') INT(11)"`
	IsGroup              int32     `xorm:"default 0 comment('是否为组合商品') INT(11)"`
	Pic                  string    `xorm:"default 'NULL' comment('商品图片（多图）') VARCHAR(1000)"`
	SellingPoint         string    `xorm:"default 'NULL' comment('商品卖点') VARCHAR(200)"`
	Video                string    `xorm:"default 'NULL' comment('商品视频地址') VARCHAR(500)"`
	ContentPc            string    `xorm:"default 'NULL' comment('电脑端详情内容') TEXT"`
	ContentMobile        string    `xorm:"default 'NULL' comment('手机端详情内容') TEXT"`
	IsDiscount           int32     `xorm:"default NULL comment('是否参与优惠折扣') INT(11)"`
	ProductType          int32     `xorm:"default NULL comment('商品类别（1-实物商品，2-虚拟商品）') INT(11)"`
	IsUse                int32     `xorm:"default 0 comment('商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）') INT(11)"`
	DelDate              time.Time `xorm:"default 'NULL' comment('删除时间') DATETIME"`
	ChannelCategoryId    int32     `xorm:"not null default 0 comment('渠道的分类id') INT(11)"`
	ChannelCategoryName  string    `xorm:"default 'NULL' comment('渠道的分类名称') VARCHAR(100)"`
	CategoryName         string    `xorm:"default 'NULL' comment('分类名称') VARCHAR(100)"`
	ChannelName          string    `xorm:"default 'NULL' comment('渠道名称（美团，饿了么，阿闻，京东）') VARCHAR(50)"`
	LastEditUser         string    `xorm:"default 'NULL' comment('最后编辑用户') VARCHAR(50)"`
	ChannelTagId         int32     `xorm:"default 'NULL' comment('渠道商品类目id') INT(11)"`
	IsRecommend          int32     `xorm:"not null default 0 comment('是否是推荐商品,1是0否') TINYINT(1)"`
	UseRange             string    `xorm:"default 'NULL' comment('商品应用范围（1电商，2前置仓，3门店仓）,字符串拼接') VARCHAR(100)"`
	GroupType            int32     `xorm:"default NULL comment('组合类型(1:实实组合,2:虚虚组合,3.虚实组合)') INT(11)"`
	TermType             int32     `xorm:"default NULL comment('只有虚拟商品才有值(1.有效期至多少  2.有效期天数)') INT(11)"`
	TermValue            int32     `xorm:"default NULL comment('如果term_type=1 存：时间戳  如果term_type=2 存多少天') INT(11)"`
	VirtualInvalidRefund int32     `xorm:"default NULL comment('是否支持过期退款 1：是 0：否') INT(11)"`
}

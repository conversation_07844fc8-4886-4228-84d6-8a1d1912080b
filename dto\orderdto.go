package dto

// 联合查询用的实体
type RefundOrderGoodsAndGood struct {
	Id           int32   `xorm:"not null default 0 comment('主键Id') INT(11)"`
	RefundSn     string  `xorm:"default 'NULL' comment('退款单号') VARCHAR(50)"`
	SkuId        string  `xorm:"not null default '''' comment('商品id') VARCHAR(20)"`
	Quantity     int     `xorm:"not null default 0 comment('商品数量') INT(11)"`
	RefundAmount string  `xorm:"not null default '''' comment('退款金额') VARCHAR(20)"`
	Itemcode     string  `xorm:"default 'NULL' comment('管易专用 商品代码') VARCHAR(50)"`
	Skucode      string  `xorm:"default 'NULL' comment('管易专用 带规格的商品此字段必填') VARCHAR(50)"`
	OcId         string  `xorm:"default 'NULL' comment('订单明细Id (全渠道需要)') VARCHAR(50)"`
	Barcode      string  `xorm:"default 'NULL' comment('管易专用 商品条码') VARCHAR(50)"`
	ProductName  string  `xorm:"default 'NULL' comment('退款商品名称') VARCHAR(200)"`
	Spec         string  `xorm:"default 'NULL' comment('商品sku的规格名称') VARCHAR(200)"`
	RefundPrice  int     `xorm:"default NULL comment('如本次部分退款是按件部分退，则此金额为单件商品sku的退款金额') INT(11)"`
	BoxPrice     int     `xorm:"default NULL comment('当前商品sku需使用包装盒的单价，即单个包装盒的价格，单位是元。') INT(11)"`
	BoxNum       float32 `xorm:"default NULL comment('单件商品sku需使用的包装盒数量，同商家同步商品时维护的此相同字段的信息。') FLOAT(10,2)"`
	Tkcount      int     `xorm:"not null default 0 comment('商品入库数量') INT(11)"`
	ProductPrice int     `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	Number       int     `xorm:"not null default 0 comment('销售数量') INT(11)"`
	Specs        string  `xorm:"default 'NULL' comment('订单商品表的sku的规格名称') VARCHAR(200)"`
	PromotionId  int     `xorm:"not null default 0 comment('折扣活动id') INT(11)"`
}

type OrderProductAndRet struct {
	Id             string `xorm:"not null pk default '''' VARCHAR(50)"`
	OrderId        string `xorm:"not null default '''' comment('订单id') VARCHAR(50)"`
	OrderSn        string `xorm:"not null default '''' comment('主订单号') index VARCHAR(50)"`
	SkuId          string `xorm:"not null default '''' comment('sku') VARCHAR(50)"`
	ParentSkuId    string `xorm:"not null default '''' comment('ParentSkuId') VARCHAR(50)"`
	OrderProductId int64  `xorm:"not null default 0 comment('订单商品表主键id') INT(11)"`
	ProductId      string `xorm:"not null default '''' comment('电商商品id') VARCHAR(50)"`
	ProductName    string `xorm:"not null default '''' comment('商品名称') VARCHAR(200)"`
	BarCode        string `xorm:"not null default '''' comment('商品编码') VARCHAR(50)"`
	Price          int    `xorm:"not null default 0 comment('商品单价') INT(11)"`
	PayPrice       int    `xorm:"not null default 0 comment('实际支付单价（美团）') INT(11)"`
	Number         int    `xorm:"not null default 0 comment('数量') INT(11)"`
	ProductType    int32  `xorm:"not null default 1 comment('商品类别 1-实物商品，2-虚拟商品，3-组合商品') INT(11)"`
	Specs          string `xorm:"not null default '''' comment('规格') VARCHAR(50)"`
	PaymentTotal   int    `xorm:"not null default 0 comment('实际支付总金额（减总优惠金额）') INT(11)"`
	Privilege      int    `xorm:"not null default 0 comment('商家优惠金额') INT(11)"`
	PrivilegePt    int    `xorm:"not null default 0 comment('平台优惠金额') INT(11)"`
	PrivilegeTotal int    `xorm:"not null default 0 comment('总优惠金额') INT(11)"`
	Freight        int    `xorm:"not null default 0 comment('邮费') INT(11)"`
	MarkingPrice   int    `xorm:"not null default 0 comment('原价') INT(11)"`
	DeliverStatus  int    `xorm:"not null default 0 comment('发货状态0未发货  1已发货') TINYINT(4)"`
	DeliverNum     int    `xorm:"not null default 0 comment('发货数量') INT(11)"`
	RefundNum      int    `xorm:"not null default 0 comment('退货数量') INT(11)"`
	Image          string `xorm:"default 'NULL' comment('商品图片') VARCHAR(500)"`
	Quantity       int    `xorm:"not null default 0 comment('商品数量') INT(11)"`
	Tkcount        int    `xorm:"not null default 0 comment('商品入库数量') INT(11)"`
	RefundAmount   string `xorm:"not null default '''' comment('退款金额') VARCHAR(20)"`
}

// 发货参数
type DeliverParam struct {
	//来源 1全渠道  2管易
	Source int
	//订单号
	OrderSn string
	//是否整单发货  0否 1是
	IsEntire int
	//子订单（商品）信息
	DeliverDetail []DeliverDetail
}

// 发货明细
type DeliverDetail struct {
	GoodsSku string
	Num      int32
}

// 发货订单信息
type OrderStore struct {
	//是否完成
	Isfinish string
	//仓库编码
	Code string
	// 仓库所属
	Source  int32
	Orderid string //订单id
	//商品ID
	Goodslist []Goods
}

// 发货订单商品信息
type Goods struct {
	//商品编码
	Goodsid string
}

// 推送子龙订单返回
type ZiLongPushOrderResponse struct {
	Result        ZiLongResult `json:"result"`
	Message       string       `json:"message"`
	SystemError   interface{}  `json:"systemError"`
	BusinessError interface{}  `json:"businessError"`
	StatusCode    int          `json:"statusCode"`
	//Extensions    string `json:"extensions"`
	Success bool `json:"success"`
}

// 返回结果
type ZiLongResult struct {
	OrderNumber       string              `json:"orderNumber"`
	StockErrorDetails []StockErrorDetails `json:"stockErrorDetails"`
}

type StockErrorDetails struct {
	ItemCode    string `json:"orderNumber"`
	ProductName string `json:"productName"`
}

// 推送子龙订单请求
type ZiLongPushOrderRequest struct {
	OrdersMaster OrdersMaster `json:"ordersMaster"`
}

type OrdersMaster struct {
	OrgID                  int             `json:"orgId"`
	PayedDate              string          `json:"payedDate"`
	ActulyPayed            float64         `json:"actulyPayed"`
	PresentPayedAmount     float64         `json:"presentPayedAmount"`
	OrderCenterOrderNumber string          `json:"orderCenterOrderNumber"`
	OrderSource            int             `json:"orderSource"`
	ThirdOrderNumber       string          `json:"thirdOrderNumber"`
	DispatchinFee          float64         `json:"dispatchinFee"`
	PackingFee             float64         `json:"packingFee"`
	PrescriptionId         string          `json:"prescriptionId"`
	OrdersDetails          []OrdersDetails `json:"ordersDetails"`
	Difference             float64         `json:"difference"`
	//出库机构Id
	StoreOrgId int `json:"storeOrgId"`
}

type OrdersDetails struct {
	ItemCode    string  `json:"itemCode"`
	UnitPrice   float64 `json:"unitPrice"`
	Count       int32   `json:"count"`
	DetailId    string  `json:"detailId"`
	ActulyPayed float64 `json:"actulyPayed"`
}

type OrderDiscountExtra struct {
	ActDetailId int64   `json:"act_detail_id"`
	MtCharge    float64 `json:"mt_charge"`
	PoiCharge   float64 `json:"poi_charge"`
	ReduceFee   float64 `json:"reduce_fee"`
	Remark      string  `json:"remark"`
	Type        int     `json:"type"`
}

type PayCenterResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

// 子龙退款单请求 完成后
type RmaMasterRequest struct {
	RmaMaster RmaMaster `json:"rmaMaster"`
}

// 子龙退款单请求 完成后
type RmaMaster struct {
	OrgID                  int          `json:"orgId"`
	OrderCenterOrderNumber string       `json:"orderCenterOrderNumber"`
	OrderCenterRmaNumber   string       `json:"orderCenterRmaNumber"`
	RmaDate                string       `json:"rmaDate"`
	DispatchinFee          float64      `json:"dispatchinFee"`
	PackingFee             float64      `json:"packingFee"`
	RmaDetails             []RmaDetails `json:"rmaDetails"`
	Difference             float64      `json:"difference"`
	RefundAmount           float64      `json:"refundAmount"`
	//出库机构Id
	StoreOrgId int `json:"storeOrgId"`
}

// 子龙退款单请求 完成后
type RmaDetails struct {
	ItemCode  string  `json:"itemCode"`
	RmaCount  int     `json:"rmaCount"`
	DetailId  string  `json:"detailId"`
	RmaRefund float64 `json:"rmaRefund"`
}

// 子龙退款单请求 完成前
type CancelMasterRequest struct {
	CancelMaster CancelMaster `json:"ordersMaster"`
}

// 子龙退款单请求 完成前
type CancelMaster struct {
	OrgID                  int             `json:"orgId"`
	OrderCenterOrderNumber string          `json:"orderCenterOrderNumber"`
	OrderCenterRmaNumber   string          `json:"orderCenterRmaNumber"`
	CancelDate             string          `json:"cancelDate"`
	DispatchinFee          float64         `json:"dispatchinFee"`
	PackingFee             float64         `json:"packingFee"`
	CancelDetails          []CancelDetails `json:"ordersDetails"`
}

// 子龙退款单请求 完成前
type CancelDetails struct {
	ItemCode  string  `json:"itemCode"`
	Count     int     `json:"count"`
	DetailId  string  `json:"detailId"`
	RmaRefund float64 `json:"rmaRefund"`
}

type AwenOrderPayForm struct {
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	TransType int32 `json:"transType,omitempty"`
	//商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
	OutTradeNo string `json:"outTradeNo,omitempty"`
	//商户订单号
	OrderId string `json:"orderId,omitempty"`
	//实付金额 单位分
	PayPrice int32 `json:"payPrice"`
	//订单金额 单位分
	TotalPrice int32 `json:"totalPrice"`
	//优惠金额 单位分
	Discount int32 `json:"discount"`
	//微信用户标识 JSAPI 支付时必传
	Openid string `json:"openid,omitempty"`
	//子商户公众账号ID 微信小程序必传
	SubAppId string `json:"subAppId,omitempty"`
	//商品编号
	ProductId string `json:"productId,omitempty"`
	//商品名称 最长 32 字节
	ProductName string `json:"productName,omitempty"`
	//商品描述
	ProductDesc string `json:"productDesc,omitempty"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl,omitempty"`
	//客户端 IP
	ClientIP string `json:"clientIP,omitempty"`
	//商户号
	MerchantId string `json:"merchantId,omitempty"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo,omitempty"`
	//订单有效时间（分钟）
	ValidTime int `json:"validTime,omitempty"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"orderPayType"`
	// appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家 9,SAAS
	AppId int32 `json:"appId"`
}

type ExpressInfoItem struct {
	Time    string `json:"time"`
	Status  string `json:"status"`
	Context string `json:"context"`
}

type MartExpressInfo struct {
	Code  int32             `json:"code"`
	Datas []ExpressInfoItem `json:"datas"`
}

// 订单对应的商品退款明细
type RefundOrderGoods struct {
	OrderSn        string `json:"order_sn"`
	SkuId          string `json:"sku_id"`
	ParentSkuId    string `json:"parent_sku_id"`
	Tkcount        int32  `json:"tkcount"`
	OrderProductId int64  `json:"order_product_id"`
	RefundPrice    int32  `json:"refund_price"`
	RefundAmount   string `json:"refund_amount"`
	ProductPrice   int32  `json:"product_price"`
}

// 推送子龙订单完成-request
type OrderCompletedToZiLongRequest struct {
	OrgId                  int    `json:"orgId"`
	OrderCenterOrderNumber string `json:"orderCenterOrderNumber"`
}

// 冻结子龙订单 Request
type ZiLongFreezeOrderRequest struct {
	OrgId                  int    `json:"orgId"`
	OrderCenterOrderNumber string `json:"orderCenterOrderNumber"`
	OrderCenterOrderStatus int    `json:"orderCenterOrderStatus"`
}

// OrderPushThird 正逆向推送第三方状态
type OrderPushThird struct {
	PushThirdOrder int
	PushThird      int
	ChannelId      int
	OrderSource    int
}

type LogisticsInfo struct {
	ExpressCompany string `json:"expressCompany" form:"expressCompany" query:"expressCompany"`
	ExpressNumber  string `json:"expressNumber" form:"expressNumber" query:"expressNumber"`
	Reason         string `json:"reason" form:"reason" query:"reason"`
}

type PEpayConfig struct {
	Id            int    `json:"id" xorm:"pk not null comment('主键id') BIGINT 'id'"`
	TenantId      int    `json:"tenant_id" xorm:"not null comment('租户id') BIGINT 'tenant_id'"`
	AppId         string `json:"app_id" xorm:"not null default '' comment('appId') VARCHAR(64) 'app_id'"`
	AppType       string `json:"app_type" xorm:"not null default 'NORMAL' comment('app类型: NORMAL-基础支付信息; WX_MINI-微信小程序') VARCHAR(64) 'app_type'"`
	AppMerchantId string `json:"app_merchant_id" xorm:"not null default '' comment('支付商户号 电银商户号') VARCHAR(64) 'app_merchant_id'"`
}

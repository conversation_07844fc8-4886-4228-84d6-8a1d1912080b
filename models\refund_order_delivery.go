package models

import "time"

type RefundOrderDelivery struct {
	Id                int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn           string    `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	ParentOrderSn     string    `xorm:"not null default '''' comment('父订单号') VARCHAR(50)"`
	PaySn             string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	DeliveryFee       int       `xorm:"default 0 comment('订单配送费金额') INT(11)"`
	RefundDeliveryFee int       `xorm:"default 0 comment('配送费退款金额') INT(11)"`
	RefundReason      string    `xorm:"default '''' comment('退款原因') VARCHAR(255)"`
	FailReason        string    `xorm:"default '''' comment('失败原因') VARCHAR(255)"`
	Status            int       `xorm:"default 0 comment('状态0处理中 1 退款成功 2 退款失败') INT(11)"`
	CreateNo          string    `xorm:"default '''' comment('创建人编号') VARCHAR(50)"`
	CreateName        string    `xorm:"default '''' comment('创建人姓名') VARCHAR(50)"`
	CreateIp          string    `xorm:"default '''' comment('创建人ip') VARCHAR(50)"`
	PlaceTime         time.Time `xorm:" default 'current_timestamp()' comment('下单时间') DATETIME created"`
	CreateTime        time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime        time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	RefundId          string    `xorm:"default '''' comment('电银退款流水号') VARCHAR(50)"`
}

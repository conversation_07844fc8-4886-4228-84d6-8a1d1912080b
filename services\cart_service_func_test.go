package services

import (
	"github.com/stretchr/testify/assert"
	"order-center/models"
	"order-center/proto/oc"
	"testing"
)

func TestTimingProcessingMtSubmitOrder(t *testing.T) {
	type args struct {
		isRealTime bool
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				isRealTime: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TimingProcessingMtSubmitOrder(tt.args.isRealTime); got != tt.want {
				t.Errorf("TimingProcessingMtSubmitOrder() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_pickupValidateStation(t *testing.T) {
	type args struct {
		ctx *models.GrpcContext
		in  *oc.MtAddOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: &models.GrpcContext{
					UserInfo: models.LoginUserInfo{},
					Channel: models.PlatformChannel{
						ChannelId: 0,
						UserAgent: 3,
					},
				},
				in: &oc.MtAddOrderRequest{ShopId: "RP0243", PickupStationId: 1000},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := pickupValidateStation(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("pickupValidateStation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_getFinanceCodeByChannel(t *testing.T) {
	type args struct {
		channelId      int
		channelStoreId string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				channelId:      3,
				channelStoreId: "6824006998089739852",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getFinanceCodeByChannel(tt.args.channelId, tt.args.channelStoreId), "getFinanceCodeByChannel(%v, %v)", tt.args.channelId, tt.args.channelStoreId)
		})
	}
}

func Test_submitCheckBlackList(t *testing.T) {
	type args struct {
		params *oc.MtAddOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{params: &oc.MtAddOrderRequest{
				IsVirtual:     0,
				MemberTel:     "17704021685",
				ReceiverPhone: "17704021685",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := submitCheckBlackList(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("submitCheckBlackList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkOrderRisk(t *testing.T) {
	type args struct {
		orderMain *models.OrderMain
		ops       []*models.OrderProduct
		wantErr   bool
	}

	param := args{
		orderMain: new(models.OrderMain),
	}

	db := GetDBConn()
	db.ShowSQL(true)

	db.Where("order_sn = ?", "4100000014574410").Get(param.orderMain)
	db.Where("order_sn = ?", "4100000014574410").Find(&param.ops)

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: param,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkOrderRisk(tt.args.orderMain, tt.args.ops)
			if (err != nil) != tt.wantErr {
				t.Errorf("submitCheckBlackList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

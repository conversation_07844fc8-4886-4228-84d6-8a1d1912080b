package services

import (
	"errors"

	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

type channelElmOrder struct {
	order      *models.Order
	oldOrderSn string
	etClient   *et.Client
}

func (c channelElmOrder) PickOrder() (err error) {
	var res *et.ElmOrderPickcompleteResponse
	res, err = c.etClient.ELMORDER.ElmOrderPickcomplete(c.etClient.Ctx, &et.ElmOrderPickcompleteRequest{
		OrderId:    c.oldOrderSn,
		AppChannel: c.order.AppChannel,
	})
	if err != nil {

		glog.Error("外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, "，手动拣货通知饿了么错误！", err)
	}
	if res.Code != 200 {
		err = errors.New(res.Error)
		glog.Warning("外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, "，手动拣货通知饿了么错误！", kit.JsonEncode(res))
	}

	return
}

func (c channelElmOrder) SyncDeliveryNode(params *oc.DeliveryNodeRequest) (err error) {

	//状态变更通知饿了么
	if params.Status == 30 || params.Status == 50 {
		status := 20
		if params.Status == 50 {
			status = 30
		}
		inpar := et.ElmOrderCompleteRequest{
			OrderId:    c.oldOrderSn,
			Phone:      params.CourierPhone,
			AppChannel: c.order.AppChannel,
			SelfStatus: int32(status),
		}
		var res *et.ElmOrderCompleteResponse
		res, err = c.etClient.ELMORDER.ElmOrderComplete(c.etClient.Ctx, &inpar)
		if err != nil {
			glog.Error(c.oldOrderSn, ", 通知饿了么订单状态错误, ", err.Error())
		} else if res.Code != 200 {
			err = errors.New(res.Error + "-" + res.Message)
			glog.Error(c.oldOrderSn, ", 通知饿了么订单状态错误, ", kit.JsonEncode(res))
		} else {
			glog.Info(c.oldOrderSn, ", 通知饿了么订单状态成功")
		}
	}

	////通知饿了么订单送达
	//if params.Status == 50 {
	//	inpar := et.ElmOrderCompleteRequest{
	//		OrderId:    c.oldOrderSn,
	//		Phone:      params.CourierPhone,
	//		AppChannel: c.order.AppChannel,
	//	}
	//	var res *et.ElmOrderCompleteResponse
	//	res, err = c.etClient.ELMORDER.ElmOrderComplete(c.etClient.Ctx, &inpar)
	//	if err != nil {
	//		glog.Error(c.oldOrderSn, ", 通知饿了么订单送达错误, ", err.Error())
	//	} else if res.Code != 200 {
	//		err = errors.New(res.Error + "-" + res.Message)
	//		glog.Error(c.oldOrderSn, ", 通知饿了么订单送达错误, ", kit.JsonEncode(res))
	//	} else {
	//		glog.Info(c.oldOrderSn, ", 通知饿了么订单送达成功")
	//	}
	//}

	return
}

func (c channelElmOrder) CancelOrder(params *oc.CancelAcceptOrderRequest) (err error) {
	//if c.order.OrderStatus == 30 {
	//	err = errors.New("订单已经完成，商家不允许取消订单")
	//	return
	//}

	inpar := &et.ELMOrderPartreFundRequest{
		OrderId:       c.oldOrderSn,
		RefundType:    "1",
		ReasonCode:    "7001",
		ReasonRemarks: params.Reason,
		AppChannel:    c.order.AppChannel,
	}

	var res *et.ELMBaseResponse
	res, err = c.etClient.ELMORDER.ELMOrderPartreFund(c.etClient.Ctx, inpar)

	if err != nil {
		glog.Error(c.oldOrderSn, "调用饿了么取消订单失败, ", err)
	} else if res.Code != 200 {
		err = errors.New(res.Error)
		glog.Error(c.oldOrderSn, "调用饿了么取消订单失败, ", err)
	} else {
		glog.Info("调用饿了么取消订单返回：", c.oldOrderSn, kit.JsonEncode(res))
	}

	return
}

func (c channelElmOrder) ApplyPartRefund(params *oc.OrderApplyPartRefundRequest) (err error) {

	//if c.order.OrderStatus == 30 {
	//	err = errors.New("订单已经完成，商家不允许取消订单")
	//	return
	//}

	inpar := &et.ELMOrderPartreFundRequest{
		OrderId:       c.oldOrderSn,
		RefundType:    "2",
		ReasonCode:    "7001",
		ReasonRemarks: params.Reason,
		AppChannel:    c.order.AppChannel,
	}

	for _, i2 := range params.FoodData {
		//饿了么的退款商品
		FoodData := et.PartreFundProducts{
			SubBizOrderId: i2.SubBizOrderId,
			Number:        cast.ToString(i2.Count),
			CustomSkuId:   i2.SkuId,
			PlatformSkuId: i2.PlatformSkuId,
		}
		inpar.RefundProductList = append(inpar.RefundProductList, &FoodData)
	}

	var res *et.ELMBaseResponse
	res, err = c.etClient.ELMORDER.ELMOrderPartreFund(c.etClient.Ctx, inpar)

	if err != nil {
		glog.Error(c.oldOrderSn, "调用饿了么发起部分退款失败, ", err)
	} else if res.Code != 200 {
		err = errors.New(res.Error + "-" + res.Message)
		glog.Error(c.oldOrderSn, "调用饿了么发起部分退款失败, ", err)
	} else {
		glog.Info("调用饿了么发起部分退款返回：", c.oldOrderSn, kit.JsonEncode(res))
	}

	return
}

func (c channelElmOrder) AgreeRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	var res *et.ELMBaseResponse
	res, err = c.etClient.ELMORDER.ELMAgreereFund(c.etClient.Ctx, &et.ELMOAnswerFundRequest{
		OrderId:       params.OrderId,
		RefundOrderId: refundOrder.OldRefundSn,
		AppChannel:    c.order.AppChannel,
	})
	if err != nil {
		glog.Error(c.oldOrderSn, ", 调用饿了么同意退款接口失败, ", err)
	} else if res.Code != 200 {
		err = errors.New(res.Error + "-" + res.Message)
		glog.Error(c.oldOrderSn, ", 调用饿了么同意退款接口失败, ", kit.JsonEncode(res))
	} else {
		glog.Info(c.oldOrderSn, "调用饿了么同意退款接口成功, ", kit.JsonEncode(res))
	}

	return
}

func (c channelElmOrder) RejectRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	return
}

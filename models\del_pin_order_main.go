package models

import "time"

type PinOrderMain struct {
	Id                   int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	GroupBuyId           int64     `xorm:"not null default 0 comment('拼团活动ID（对应dc_activity.group_buy主键）') INT(11)"`
	GroupBuyProductId           int64     `xorm:"not null default 0 comment('拼团商品活动ID（对应dc_activity.group_buy_product主键）') INT(11)"`
	MaxParticipantNumber int32     `xorm:"not null default 0 comment('最大参与人数') INT(11)"`
	ParticipantNumber    int32     `xorm:"not null default 0 comment('参与人数') INT(11)"`
	IsMockSuccess        int32     `xorm:"not null default 0 comment('是否模拟成团 1 是 0 否') INT(1)"`
	MockNumber    int32     `xorm:"not null default 0 comment('凑团人数（参团人数达到时才开启凑团，0为没限制）') INT(11)"`
	ChannelId            int32     `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	PinHeadOrderSn       string    `xorm:"not null default '''' comment('拼主订单号ID') VARCHAR(55)"`
	PinHeadPortrait      string    `xorm:"not null default '''' comment('拼主头像') VARCHAR(255)"`
	PinHeadOpenId        string    `xorm:"not null default '''' comment('拼主openid') VARCHAR(50)"`
	PinHeadUserId        string    `xorm:"not null default '''' comment('拼主userid') VARCHAR(50)"`
	PinSkuId             string    `xorm:"not null default '''' comment('拼团商品SKUID') VARCHAR(50)"`
	Status               int32     `xorm:"not null default 0 comment('团状态：10拼团进行中 20 拼团成功 30 拼团失败') INT(2)"`
	CreateTime           time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	UpdateTime           time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
	StartTime            time.Time `xorm:"default 'current_timestamp()' comment('开始时间')"`
	EndTime              time.Time `xorm:"default 'current_timestamp()' comment('结束时间')"`
	SuccessTime          time.Time `xorm:"default 'current_timestamp()' comment('成团时间')"`
}

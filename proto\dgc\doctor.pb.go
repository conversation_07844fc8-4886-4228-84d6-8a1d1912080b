// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/doctor.proto

// 问诊订单相关

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetDoctorProtocolStateRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDoctorProtocolStateRequest) Reset()         { *m = GetDoctorProtocolStateRequest{} }
func (m *GetDoctorProtocolStateRequest) String() string { return proto.CompactTextString(m) }
func (*GetDoctorProtocolStateRequest) ProtoMessage()    {}
func (*GetDoctorProtocolStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{0}
}

func (m *GetDoctorProtocolStateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorProtocolStateRequest.Unmarshal(m, b)
}
func (m *GetDoctorProtocolStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorProtocolStateRequest.Marshal(b, m, deterministic)
}
func (m *GetDoctorProtocolStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorProtocolStateRequest.Merge(m, src)
}
func (m *GetDoctorProtocolStateRequest) XXX_Size() int {
	return xxx_messageInfo_GetDoctorProtocolStateRequest.Size(m)
}
func (m *GetDoctorProtocolStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorProtocolStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorProtocolStateRequest proto.InternalMessageInfo

type GetDoctorProtocolStateResponse struct {
	ProtocolState        int32    `protobuf:"varint,1,opt,name=protocol_state,json=protocolState,proto3" json:"protocol_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDoctorProtocolStateResponse) Reset()         { *m = GetDoctorProtocolStateResponse{} }
func (m *GetDoctorProtocolStateResponse) String() string { return proto.CompactTextString(m) }
func (*GetDoctorProtocolStateResponse) ProtoMessage()    {}
func (*GetDoctorProtocolStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{1}
}

func (m *GetDoctorProtocolStateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorProtocolStateResponse.Unmarshal(m, b)
}
func (m *GetDoctorProtocolStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorProtocolStateResponse.Marshal(b, m, deterministic)
}
func (m *GetDoctorProtocolStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorProtocolStateResponse.Merge(m, src)
}
func (m *GetDoctorProtocolStateResponse) XXX_Size() int {
	return xxx_messageInfo_GetDoctorProtocolStateResponse.Size(m)
}
func (m *GetDoctorProtocolStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorProtocolStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorProtocolStateResponse proto.InternalMessageInfo

func (m *GetDoctorProtocolStateResponse) GetProtocolState() int32 {
	if m != nil {
		return m.ProtocolState
	}
	return 0
}

type EditDoctorProtocolStateRequest struct {
	ProtocolState        int32    `protobuf:"varint,1,opt,name=protocol_state,json=protocolState,proto3" json:"protocol_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditDoctorProtocolStateRequest) Reset()         { *m = EditDoctorProtocolStateRequest{} }
func (m *EditDoctorProtocolStateRequest) String() string { return proto.CompactTextString(m) }
func (*EditDoctorProtocolStateRequest) ProtoMessage()    {}
func (*EditDoctorProtocolStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{2}
}

func (m *EditDoctorProtocolStateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditDoctorProtocolStateRequest.Unmarshal(m, b)
}
func (m *EditDoctorProtocolStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditDoctorProtocolStateRequest.Marshal(b, m, deterministic)
}
func (m *EditDoctorProtocolStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditDoctorProtocolStateRequest.Merge(m, src)
}
func (m *EditDoctorProtocolStateRequest) XXX_Size() int {
	return xxx_messageInfo_EditDoctorProtocolStateRequest.Size(m)
}
func (m *EditDoctorProtocolStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditDoctorProtocolStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditDoctorProtocolStateRequest proto.InternalMessageInfo

func (m *EditDoctorProtocolStateRequest) GetProtocolState() int32 {
	if m != nil {
		return m.ProtocolState
	}
	return 0
}

type EditDoctorProtocolStateResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditDoctorProtocolStateResponse) Reset()         { *m = EditDoctorProtocolStateResponse{} }
func (m *EditDoctorProtocolStateResponse) String() string { return proto.CompactTextString(m) }
func (*EditDoctorProtocolStateResponse) ProtoMessage()    {}
func (*EditDoctorProtocolStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{3}
}

func (m *EditDoctorProtocolStateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditDoctorProtocolStateResponse.Unmarshal(m, b)
}
func (m *EditDoctorProtocolStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditDoctorProtocolStateResponse.Marshal(b, m, deterministic)
}
func (m *EditDoctorProtocolStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditDoctorProtocolStateResponse.Merge(m, src)
}
func (m *EditDoctorProtocolStateResponse) XXX_Size() int {
	return xxx_messageInfo_EditDoctorProtocolStateResponse.Size(m)
}
func (m *EditDoctorProtocolStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EditDoctorProtocolStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EditDoctorProtocolStateResponse proto.InternalMessageInfo

type GetDispatchDoctorByOpenIdReq struct {
	OpenId               string   `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDispatchDoctorByOpenIdReq) Reset()         { *m = GetDispatchDoctorByOpenIdReq{} }
func (m *GetDispatchDoctorByOpenIdReq) String() string { return proto.CompactTextString(m) }
func (*GetDispatchDoctorByOpenIdReq) ProtoMessage()    {}
func (*GetDispatchDoctorByOpenIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{4}
}

func (m *GetDispatchDoctorByOpenIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDispatchDoctorByOpenIdReq.Unmarshal(m, b)
}
func (m *GetDispatchDoctorByOpenIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDispatchDoctorByOpenIdReq.Marshal(b, m, deterministic)
}
func (m *GetDispatchDoctorByOpenIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDispatchDoctorByOpenIdReq.Merge(m, src)
}
func (m *GetDispatchDoctorByOpenIdReq) XXX_Size() int {
	return xxx_messageInfo_GetDispatchDoctorByOpenIdReq.Size(m)
}
func (m *GetDispatchDoctorByOpenIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDispatchDoctorByOpenIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDispatchDoctorByOpenIdReq proto.InternalMessageInfo

func (m *GetDispatchDoctorByOpenIdReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

//用户端获取问诊价格請求
type GetDoctorPriceRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDoctorPriceRequest) Reset()         { *m = GetDoctorPriceRequest{} }
func (m *GetDoctorPriceRequest) String() string { return proto.CompactTextString(m) }
func (*GetDoctorPriceRequest) ProtoMessage()    {}
func (*GetDoctorPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{5}
}

func (m *GetDoctorPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorPriceRequest.Unmarshal(m, b)
}
func (m *GetDoctorPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorPriceRequest.Marshal(b, m, deterministic)
}
func (m *GetDoctorPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorPriceRequest.Merge(m, src)
}
func (m *GetDoctorPriceRequest) XXX_Size() int {
	return xxx_messageInfo_GetDoctorPriceRequest.Size(m)
}
func (m *GetDoctorPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorPriceRequest proto.InternalMessageInfo

//用户端获取问诊价格返回
type GetDoctorPriceResponse struct {
	//快速图文价格 单位分
	QuickImageTextPrice int32 `protobuf:"varint,1,opt,name=quick_image_text_price,json=quickImageTextPrice,proto3" json:"quick_image_text_price"`
	//免费图文问诊时长（单位分钟）
	FreeImageTextDuration int32 `protobuf:"varint,2,opt,name=free_image_text_duration,json=freeImageTextDuration,proto3" json:"free_image_text_duration"`
	//快速图文问诊时长（单位分钟）
	QuickImageTextDuration int32    `protobuf:"varint,3,opt,name=quick_image_text_duration,json=quickImageTextDuration,proto3" json:"quick_image_text_duration"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetDoctorPriceResponse) Reset()         { *m = GetDoctorPriceResponse{} }
func (m *GetDoctorPriceResponse) String() string { return proto.CompactTextString(m) }
func (*GetDoctorPriceResponse) ProtoMessage()    {}
func (*GetDoctorPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{6}
}

func (m *GetDoctorPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorPriceResponse.Unmarshal(m, b)
}
func (m *GetDoctorPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorPriceResponse.Marshal(b, m, deterministic)
}
func (m *GetDoctorPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorPriceResponse.Merge(m, src)
}
func (m *GetDoctorPriceResponse) XXX_Size() int {
	return xxx_messageInfo_GetDoctorPriceResponse.Size(m)
}
func (m *GetDoctorPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorPriceResponse proto.InternalMessageInfo

func (m *GetDoctorPriceResponse) GetQuickImageTextPrice() int32 {
	if m != nil {
		return m.QuickImageTextPrice
	}
	return 0
}

func (m *GetDoctorPriceResponse) GetFreeImageTextDuration() int32 {
	if m != nil {
		return m.FreeImageTextDuration
	}
	return 0
}

func (m *GetDoctorPriceResponse) GetQuickImageTextDuration() int32 {
	if m != nil {
		return m.QuickImageTextDuration
	}
	return 0
}

type DoctorIsForbiddenRequest struct {
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	IsForbidden          int32    `protobuf:"varint,2,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorIsForbiddenRequest) Reset()         { *m = DoctorIsForbiddenRequest{} }
func (m *DoctorIsForbiddenRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorIsForbiddenRequest) ProtoMessage()    {}
func (*DoctorIsForbiddenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{7}
}

func (m *DoctorIsForbiddenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorIsForbiddenRequest.Unmarshal(m, b)
}
func (m *DoctorIsForbiddenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorIsForbiddenRequest.Marshal(b, m, deterministic)
}
func (m *DoctorIsForbiddenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorIsForbiddenRequest.Merge(m, src)
}
func (m *DoctorIsForbiddenRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorIsForbiddenRequest.Size(m)
}
func (m *DoctorIsForbiddenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorIsForbiddenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorIsForbiddenRequest proto.InternalMessageInfo

func (m *DoctorIsForbiddenRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *DoctorIsForbiddenRequest) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

type DoctorTipsRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorTipsRequest) Reset()         { *m = DoctorTipsRequest{} }
func (m *DoctorTipsRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorTipsRequest) ProtoMessage()    {}
func (*DoctorTipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{8}
}

func (m *DoctorTipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorTipsRequest.Unmarshal(m, b)
}
func (m *DoctorTipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorTipsRequest.Marshal(b, m, deterministic)
}
func (m *DoctorTipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorTipsRequest.Merge(m, src)
}
func (m *DoctorTipsRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorTipsRequest.Size(m)
}
func (m *DoctorTipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorTipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorTipsRequest proto.InternalMessageInfo

type GetDoctorInfoRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DoctorCode           string   `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDoctorInfoRequest) Reset()         { *m = GetDoctorInfoRequest{} }
func (m *GetDoctorInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetDoctorInfoRequest) ProtoMessage()    {}
func (*GetDoctorInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{9}
}

func (m *GetDoctorInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorInfoRequest.Unmarshal(m, b)
}
func (m *GetDoctorInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetDoctorInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorInfoRequest.Merge(m, src)
}
func (m *GetDoctorInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetDoctorInfoRequest.Size(m)
}
func (m *GetDoctorInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorInfoRequest proto.InternalMessageInfo

func (m *GetDoctorInfoRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetDoctorInfoRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

type GetDoctorInfoResponse struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//名称
	DoctorName string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,3,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生擅长
	DoctorSpeciality string `protobuf:"bytes,4,opt,name=doctor_speciality,json=doctorSpeciality,proto3" json:"doctor_speciality"`
	//医生简介
	DoctorPresent string `protobuf:"bytes,5,opt,name=doctor_present,json=doctorPresent,proto3" json:"doctor_present"`
	//医院code
	HospitalCode string `protobuf:"bytes,6,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//医生照片
	DoctorImg string `protobuf:"bytes,7,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院名称
	HospitalName string `protobuf:"bytes,8,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//医生tag
	TagName string `protobuf:"bytes,9,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	//找医生图文问诊费用/分
	ImageTextPrice int32 `protobuf:"varint,10,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//找医生电话问诊费用/分
	PhonePrice int32 `protobuf:"varint,11,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//找医生视频问诊费用/分
	VideoPrice int32 `protobuf:"varint,12,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	//是否开始图文 0 未开启 1开启
	ImageTextStatus int32 `protobuf:"varint,13,opt,name=image_text_status,json=imageTextStatus,proto3" json:"image_text_status"`
	//是否开启视频 0 未开启 1开启
	VideoPriceStatus int32 `protobuf:"varint,14,opt,name=video_price_status,json=videoPriceStatus,proto3" json:"video_price_status"`
	//是否电话 0 未开启 1开启
	PhonePriceStatus int32 `protobuf:"varint,15,opt,name=phone_price_status,json=phonePriceStatus,proto3" json:"phone_price_status"`
	//图片问诊 分钟
	ImageTextDuration int32 `protobuf:"varint,16,opt,name=image_text_duration,json=imageTextDuration,proto3" json:"image_text_duration"`
	//电话问诊 分钟
	PhoneDuration int32 `protobuf:"varint,17,opt,name=phone_duration,json=phoneDuration,proto3" json:"phone_duration"`
	//视频问诊 分钟
	VideoDuration int32 `protobuf:"varint,18,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration"`
	//用户与医生最后的订单号
	LatestOrderSn string `protobuf:"bytes,19,opt,name=latest_order_sn,json=latestOrderSn,proto3" json:"latest_order_sn"`
	//用户与医生最后的问诊状态 1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
	LatestDiagnoseState  int32    `protobuf:"varint,20,opt,name=latest_diagnose_state,json=latestDiagnoseState,proto3" json:"latest_diagnose_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDoctorInfoResponse) Reset()         { *m = GetDoctorInfoResponse{} }
func (m *GetDoctorInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetDoctorInfoResponse) ProtoMessage()    {}
func (*GetDoctorInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{10}
}

func (m *GetDoctorInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDoctorInfoResponse.Unmarshal(m, b)
}
func (m *GetDoctorInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDoctorInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetDoctorInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDoctorInfoResponse.Merge(m, src)
}
func (m *GetDoctorInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetDoctorInfoResponse.Size(m)
}
func (m *GetDoctorInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDoctorInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDoctorInfoResponse proto.InternalMessageInfo

func (m *GetDoctorInfoResponse) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetDoctorSpeciality() string {
	if m != nil {
		return m.DoctorSpeciality
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetDoctorPresent() string {
	if m != nil {
		return m.DoctorPresent
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetImageTextStatus() int32 {
	if m != nil {
		return m.ImageTextStatus
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetVideoPriceStatus() int32 {
	if m != nil {
		return m.VideoPriceStatus
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetPhonePriceStatus() int32 {
	if m != nil {
		return m.PhonePriceStatus
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetImageTextDuration() int32 {
	if m != nil {
		return m.ImageTextDuration
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetPhoneDuration() int32 {
	if m != nil {
		return m.PhoneDuration
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetVideoDuration() int32 {
	if m != nil {
		return m.VideoDuration
	}
	return 0
}

func (m *GetDoctorInfoResponse) GetLatestOrderSn() string {
	if m != nil {
		return m.LatestOrderSn
	}
	return ""
}

func (m *GetDoctorInfoResponse) GetLatestDiagnoseState() int32 {
	if m != nil {
		return m.LatestDiagnoseState
	}
	return 0
}

type ScrmDoctorListExportRequest struct {
	//搜索医生 医生姓名或手机号
	DoctorName string `protobuf:"bytes,1,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//任职医院
	HospitalName string `protobuf:"bytes,2,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//医生状态
	DoctorStatus int32 `protobuf:"varint,3,opt,name=doctor_status,json=doctorStatus,proto3" json:"doctor_status"`
	//问诊项目
	DiagnoseProject int32 `protobuf:"varint,4,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式
	DiagnoseForm         int32    `protobuf:"varint,5,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmDoctorListExportRequest) Reset()         { *m = ScrmDoctorListExportRequest{} }
func (m *ScrmDoctorListExportRequest) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctorListExportRequest) ProtoMessage()    {}
func (*ScrmDoctorListExportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{11}
}

func (m *ScrmDoctorListExportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctorListExportRequest.Unmarshal(m, b)
}
func (m *ScrmDoctorListExportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctorListExportRequest.Marshal(b, m, deterministic)
}
func (m *ScrmDoctorListExportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctorListExportRequest.Merge(m, src)
}
func (m *ScrmDoctorListExportRequest) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctorListExportRequest.Size(m)
}
func (m *ScrmDoctorListExportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctorListExportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctorListExportRequest proto.InternalMessageInfo

func (m *ScrmDoctorListExportRequest) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ScrmDoctorListExportRequest) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ScrmDoctorListExportRequest) GetDoctorStatus() int32 {
	if m != nil {
		return m.DoctorStatus
	}
	return 0
}

func (m *ScrmDoctorListExportRequest) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *ScrmDoctorListExportRequest) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

type HospitalListRequest struct {
	//医院名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HospitalListRequest) Reset()         { *m = HospitalListRequest{} }
func (m *HospitalListRequest) String() string { return proto.CompactTextString(m) }
func (*HospitalListRequest) ProtoMessage()    {}
func (*HospitalListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{12}
}

func (m *HospitalListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HospitalListRequest.Unmarshal(m, b)
}
func (m *HospitalListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HospitalListRequest.Marshal(b, m, deterministic)
}
func (m *HospitalListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HospitalListRequest.Merge(m, src)
}
func (m *HospitalListRequest) XXX_Size() int {
	return xxx_messageInfo_HospitalListRequest.Size(m)
}
func (m *HospitalListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HospitalListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HospitalListRequest proto.InternalMessageInfo

func (m *HospitalListRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HospitalListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *HospitalListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type HospitalListResponse struct {
	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	//列表数据
	Data                 []*ScrmHospital `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HospitalListResponse) Reset()         { *m = HospitalListResponse{} }
func (m *HospitalListResponse) String() string { return proto.CompactTextString(m) }
func (*HospitalListResponse) ProtoMessage()    {}
func (*HospitalListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{13}
}

func (m *HospitalListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HospitalListResponse.Unmarshal(m, b)
}
func (m *HospitalListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HospitalListResponse.Marshal(b, m, deterministic)
}
func (m *HospitalListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HospitalListResponse.Merge(m, src)
}
func (m *HospitalListResponse) XXX_Size() int {
	return xxx_messageInfo_HospitalListResponse.Size(m)
}
func (m *HospitalListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HospitalListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HospitalListResponse proto.InternalMessageInfo

func (m *HospitalListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *HospitalListResponse) GetData() []*ScrmHospital {
	if m != nil {
		return m.Data
	}
	return nil
}

type ScrmHospital struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医院code
	HospitalCode string `protobuf:"bytes,2,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//医院名称
	HospitalName string `protobuf:"bytes,3,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//1中心医院，2专科医院，3社区医院，4全科医院，5小型全科医院，6中型全科医院，7宠物店
	HospitalType int32 `protobuf:"varint,4,opt,name=hospital_type,json=hospitalType,proto3" json:"hospital_type"`
	//网络医院（虚拟）医院
	HospitalInternetId string `protobuf:"bytes,5,opt,name=hospital_internet_id,json=hospitalInternetId,proto3" json:"hospital_internet_id"`
	//医院简称
	HospitalShortName string `protobuf:"bytes,6,opt,name=hospital_short_name,json=hospitalShortName,proto3" json:"hospital_short_name"`
	//医院特长
	HospitalSpeciality string `protobuf:"bytes,7,opt,name=hospital_speciality,json=hospitalSpeciality,proto3" json:"hospital_speciality"`
	//医院简称
	HospitalPresent string `protobuf:"bytes,8,opt,name=hospital_present,json=hospitalPresent,proto3" json:"hospital_present"`
	//医院图片
	HospitalImg string `protobuf:"bytes,9,opt,name=hospital_img,json=hospitalImg,proto3" json:"hospital_img"`
	//医院封面图
	HospitalAvatar       string   `protobuf:"bytes,10,opt,name=hospital_avatar,json=hospitalAvatar,proto3" json:"hospital_avatar"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmHospital) Reset()         { *m = ScrmHospital{} }
func (m *ScrmHospital) String() string { return proto.CompactTextString(m) }
func (*ScrmHospital) ProtoMessage()    {}
func (*ScrmHospital) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{14}
}

func (m *ScrmHospital) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmHospital.Unmarshal(m, b)
}
func (m *ScrmHospital) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmHospital.Marshal(b, m, deterministic)
}
func (m *ScrmHospital) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmHospital.Merge(m, src)
}
func (m *ScrmHospital) XXX_Size() int {
	return xxx_messageInfo_ScrmHospital.Size(m)
}
func (m *ScrmHospital) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmHospital.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmHospital proto.InternalMessageInfo

func (m *ScrmHospital) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScrmHospital) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *ScrmHospital) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ScrmHospital) GetHospitalType() int32 {
	if m != nil {
		return m.HospitalType
	}
	return 0
}

func (m *ScrmHospital) GetHospitalInternetId() string {
	if m != nil {
		return m.HospitalInternetId
	}
	return ""
}

func (m *ScrmHospital) GetHospitalShortName() string {
	if m != nil {
		return m.HospitalShortName
	}
	return ""
}

func (m *ScrmHospital) GetHospitalSpeciality() string {
	if m != nil {
		return m.HospitalSpeciality
	}
	return ""
}

func (m *ScrmHospital) GetHospitalPresent() string {
	if m != nil {
		return m.HospitalPresent
	}
	return ""
}

func (m *ScrmHospital) GetHospitalImg() string {
	if m != nil {
		return m.HospitalImg
	}
	return ""
}

func (m *ScrmHospital) GetHospitalAvatar() string {
	if m != nil {
		return m.HospitalAvatar
	}
	return ""
}

type DoctorStatusRequest struct {
	//医生手机号
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorStatusRequest) Reset()         { *m = DoctorStatusRequest{} }
func (m *DoctorStatusRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorStatusRequest) ProtoMessage()    {}
func (*DoctorStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{15}
}

func (m *DoctorStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorStatusRequest.Unmarshal(m, b)
}
func (m *DoctorStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorStatusRequest.Marshal(b, m, deterministic)
}
func (m *DoctorStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorStatusRequest.Merge(m, src)
}
func (m *DoctorStatusRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorStatusRequest.Size(m)
}
func (m *DoctorStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorStatusRequest proto.InternalMessageInfo

func (m *DoctorStatusRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type DoctorStatusResponse struct {
	//在线状态 1在线 0不在线
	OnLine               int32    `protobuf:"varint,1,opt,name=on_line,json=onLine,proto3" json:"on_line"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorStatusResponse) Reset()         { *m = DoctorStatusResponse{} }
func (m *DoctorStatusResponse) String() string { return proto.CompactTextString(m) }
func (*DoctorStatusResponse) ProtoMessage()    {}
func (*DoctorStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{16}
}

func (m *DoctorStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorStatusResponse.Unmarshal(m, b)
}
func (m *DoctorStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorStatusResponse.Marshal(b, m, deterministic)
}
func (m *DoctorStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorStatusResponse.Merge(m, src)
}
func (m *DoctorStatusResponse) XXX_Size() int {
	return xxx_messageInfo_DoctorStatusResponse.Size(m)
}
func (m *DoctorStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorStatusResponse proto.InternalMessageInfo

func (m *DoctorStatusResponse) GetOnLine() int32 {
	if m != nil {
		return m.OnLine
	}
	return 0
}

type DoctorOnlineChangeRequest struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//是否在线:0-否，1-是
	OnLine               int32    `protobuf:"varint,2,opt,name=on_line,json=onLine,proto3" json:"on_line"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorOnlineChangeRequest) Reset()         { *m = DoctorOnlineChangeRequest{} }
func (m *DoctorOnlineChangeRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorOnlineChangeRequest) ProtoMessage()    {}
func (*DoctorOnlineChangeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{17}
}

func (m *DoctorOnlineChangeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorOnlineChangeRequest.Unmarshal(m, b)
}
func (m *DoctorOnlineChangeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorOnlineChangeRequest.Marshal(b, m, deterministic)
}
func (m *DoctorOnlineChangeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorOnlineChangeRequest.Merge(m, src)
}
func (m *DoctorOnlineChangeRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorOnlineChangeRequest.Size(m)
}
func (m *DoctorOnlineChangeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorOnlineChangeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorOnlineChangeRequest proto.InternalMessageInfo

func (m *DoctorOnlineChangeRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *DoctorOnlineChangeRequest) GetOnLine() int32 {
	if m != nil {
		return m.OnLine
	}
	return 0
}

type RegisterDoctorMiniProgramReq struct {
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	JsCode               string   `protobuf:"bytes,2,opt,name=js_code,json=jsCode,proto3" json:"js_code"`
	DoctorMobile         string   `protobuf:"bytes,3,opt,name=doctor_mobile,json=doctorMobile,proto3" json:"doctor_mobile"`
	OpenId               string   `protobuf:"bytes,4,opt,name=open_id,json=openId,proto3" json:"open_id"`
	ComeFrom             string   `protobuf:"bytes,5,opt,name=come_from,json=comeFrom,proto3" json:"come_from"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterDoctorMiniProgramReq) Reset()         { *m = RegisterDoctorMiniProgramReq{} }
func (m *RegisterDoctorMiniProgramReq) String() string { return proto.CompactTextString(m) }
func (*RegisterDoctorMiniProgramReq) ProtoMessage()    {}
func (*RegisterDoctorMiniProgramReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{18}
}

func (m *RegisterDoctorMiniProgramReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterDoctorMiniProgramReq.Unmarshal(m, b)
}
func (m *RegisterDoctorMiniProgramReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterDoctorMiniProgramReq.Marshal(b, m, deterministic)
}
func (m *RegisterDoctorMiniProgramReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterDoctorMiniProgramReq.Merge(m, src)
}
func (m *RegisterDoctorMiniProgramReq) XXX_Size() int {
	return xxx_messageInfo_RegisterDoctorMiniProgramReq.Size(m)
}
func (m *RegisterDoctorMiniProgramReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterDoctorMiniProgramReq.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterDoctorMiniProgramReq proto.InternalMessageInfo

func (m *RegisterDoctorMiniProgramReq) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *RegisterDoctorMiniProgramReq) GetJsCode() string {
	if m != nil {
		return m.JsCode
	}
	return ""
}

func (m *RegisterDoctorMiniProgramReq) GetDoctorMobile() string {
	if m != nil {
		return m.DoctorMobile
	}
	return ""
}

func (m *RegisterDoctorMiniProgramReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *RegisterDoctorMiniProgramReq) GetComeFrom() string {
	if m != nil {
		return m.ComeFrom
	}
	return ""
}

type LogoutDoctorMiniProgramReq struct {
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogoutDoctorMiniProgramReq) Reset()         { *m = LogoutDoctorMiniProgramReq{} }
func (m *LogoutDoctorMiniProgramReq) String() string { return proto.CompactTextString(m) }
func (*LogoutDoctorMiniProgramReq) ProtoMessage()    {}
func (*LogoutDoctorMiniProgramReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{19}
}

func (m *LogoutDoctorMiniProgramReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogoutDoctorMiniProgramReq.Unmarshal(m, b)
}
func (m *LogoutDoctorMiniProgramReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogoutDoctorMiniProgramReq.Marshal(b, m, deterministic)
}
func (m *LogoutDoctorMiniProgramReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogoutDoctorMiniProgramReq.Merge(m, src)
}
func (m *LogoutDoctorMiniProgramReq) XXX_Size() int {
	return xxx_messageInfo_LogoutDoctorMiniProgramReq.Size(m)
}
func (m *LogoutDoctorMiniProgramReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LogoutDoctorMiniProgramReq.DiscardUnknown(m)
}

var xxx_messageInfo_LogoutDoctorMiniProgramReq proto.InternalMessageInfo

func (m *LogoutDoctorMiniProgramReq) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

type EmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyRequest) Reset()         { *m = EmptyRequest{} }
func (m *EmptyRequest) String() string { return proto.CompactTextString(m) }
func (*EmptyRequest) ProtoMessage()    {}
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{20}
}

func (m *EmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyRequest.Unmarshal(m, b)
}
func (m *EmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyRequest.Marshal(b, m, deterministic)
}
func (m *EmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyRequest.Merge(m, src)
}
func (m *EmptyRequest) XXX_Size() int {
	return xxx_messageInfo_EmptyRequest.Size(m)
}
func (m *EmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyRequest proto.InternalMessageInfo

type InternetDoctorListRequest struct {
	//搜索医生 姓名或手机号
	DoctorName string `protobuf:"bytes,1,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternetDoctorListRequest) Reset()         { *m = InternetDoctorListRequest{} }
func (m *InternetDoctorListRequest) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorListRequest) ProtoMessage()    {}
func (*InternetDoctorListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{21}
}

func (m *InternetDoctorListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorListRequest.Unmarshal(m, b)
}
func (m *InternetDoctorListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorListRequest.Marshal(b, m, deterministic)
}
func (m *InternetDoctorListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorListRequest.Merge(m, src)
}
func (m *InternetDoctorListRequest) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorListRequest.Size(m)
}
func (m *InternetDoctorListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorListRequest proto.InternalMessageInfo

func (m *InternetDoctorListRequest) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *InternetDoctorListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *InternetDoctorListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type InternetDoctorListResponse struct {
	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	//列表数据
	Data                 []*InternetDoctorInfoResponse `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *InternetDoctorListResponse) Reset()         { *m = InternetDoctorListResponse{} }
func (m *InternetDoctorListResponse) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorListResponse) ProtoMessage()    {}
func (*InternetDoctorListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{22}
}

func (m *InternetDoctorListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorListResponse.Unmarshal(m, b)
}
func (m *InternetDoctorListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorListResponse.Marshal(b, m, deterministic)
}
func (m *InternetDoctorListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorListResponse.Merge(m, src)
}
func (m *InternetDoctorListResponse) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorListResponse.Size(m)
}
func (m *InternetDoctorListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorListResponse proto.InternalMessageInfo

func (m *InternetDoctorListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *InternetDoctorListResponse) GetData() []*InternetDoctorInfoResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

type InternetDoctorForbiddenRequest struct {
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	IsForbidden          int32    `protobuf:"varint,2,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternetDoctorForbiddenRequest) Reset()         { *m = InternetDoctorForbiddenRequest{} }
func (m *InternetDoctorForbiddenRequest) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorForbiddenRequest) ProtoMessage()    {}
func (*InternetDoctorForbiddenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{23}
}

func (m *InternetDoctorForbiddenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorForbiddenRequest.Unmarshal(m, b)
}
func (m *InternetDoctorForbiddenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorForbiddenRequest.Marshal(b, m, deterministic)
}
func (m *InternetDoctorForbiddenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorForbiddenRequest.Merge(m, src)
}
func (m *InternetDoctorForbiddenRequest) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorForbiddenRequest.Size(m)
}
func (m *InternetDoctorForbiddenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorForbiddenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorForbiddenRequest proto.InternalMessageInfo

func (m *InternetDoctorForbiddenRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *InternetDoctorForbiddenRequest) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

type InternetDoctorInfoRequest struct {
	//医生编号
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternetDoctorInfoRequest) Reset()         { *m = InternetDoctorInfoRequest{} }
func (m *InternetDoctorInfoRequest) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorInfoRequest) ProtoMessage()    {}
func (*InternetDoctorInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{24}
}

func (m *InternetDoctorInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorInfoRequest.Unmarshal(m, b)
}
func (m *InternetDoctorInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorInfoRequest.Marshal(b, m, deterministic)
}
func (m *InternetDoctorInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorInfoRequest.Merge(m, src)
}
func (m *InternetDoctorInfoRequest) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorInfoRequest.Size(m)
}
func (m *InternetDoctorInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorInfoRequest proto.InternalMessageInfo

func (m *InternetDoctorInfoRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

type InternetDoctorInfoResponse struct {
	//医生姓名
	DoctorName string `protobuf:"bytes,1,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	//是否开启在线问诊服务:0-否，1-是
	OnLine int32 `protobuf:"varint,3,opt,name=on_line,json=onLine,proto3" json:"on_line"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `protobuf:"varint,4,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	//创建日期
	CreateTime string `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//最后更新时间
	UpdateTime string `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//医生编号
	DoctorCode           string   `protobuf:"bytes,7,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternetDoctorInfoResponse) Reset()         { *m = InternetDoctorInfoResponse{} }
func (m *InternetDoctorInfoResponse) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorInfoResponse) ProtoMessage()    {}
func (*InternetDoctorInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{25}
}

func (m *InternetDoctorInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorInfoResponse.Unmarshal(m, b)
}
func (m *InternetDoctorInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorInfoResponse.Marshal(b, m, deterministic)
}
func (m *InternetDoctorInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorInfoResponse.Merge(m, src)
}
func (m *InternetDoctorInfoResponse) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorInfoResponse.Size(m)
}
func (m *InternetDoctorInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorInfoResponse proto.InternalMessageInfo

func (m *InternetDoctorInfoResponse) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *InternetDoctorInfoResponse) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *InternetDoctorInfoResponse) GetOnLine() int32 {
	if m != nil {
		return m.OnLine
	}
	return 0
}

func (m *InternetDoctorInfoResponse) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

func (m *InternetDoctorInfoResponse) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *InternetDoctorInfoResponse) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *InternetDoctorInfoResponse) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

type InternetDoctorAddRequest struct {
	//姓名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//手机号
	Mobile               string   `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternetDoctorAddRequest) Reset()         { *m = InternetDoctorAddRequest{} }
func (m *InternetDoctorAddRequest) String() string { return proto.CompactTextString(m) }
func (*InternetDoctorAddRequest) ProtoMessage()    {}
func (*InternetDoctorAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{26}
}

func (m *InternetDoctorAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternetDoctorAddRequest.Unmarshal(m, b)
}
func (m *InternetDoctorAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternetDoctorAddRequest.Marshal(b, m, deterministic)
}
func (m *InternetDoctorAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternetDoctorAddRequest.Merge(m, src)
}
func (m *InternetDoctorAddRequest) XXX_Size() int {
	return xxx_messageInfo_InternetDoctorAddRequest.Size(m)
}
func (m *InternetDoctorAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InternetDoctorAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InternetDoctorAddRequest proto.InternalMessageInfo

func (m *InternetDoctorAddRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InternetDoctorAddRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type DiagnoseSystemInfoResponse struct {
	//主键ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医生排班时间内不接单开关：0关，1开
	WorkOnOff int32 `protobuf:"varint,2,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	//免费图文问诊时长（单位分钟）
	FreeImageTextDuration int32 `protobuf:"varint,3,opt,name=free_image_text_duration,json=freeImageTextDuration,proto3" json:"free_image_text_duration"`
	//快速图文问诊时长（单位分钟）
	QuickImageTextDuration int32 `protobuf:"varint,4,opt,name=quick_image_text_duration,json=quickImageTextDuration,proto3" json:"quick_image_text_duration"`
	//找医生图文问诊时长（单位分钟）
	FindPhoneDuration int32 `protobuf:"varint,5,opt,name=find_phone_duration,json=findPhoneDuration,proto3" json:"find_phone_duration"`
	//找医生视频问诊时长（单位分钟）
	FindVideoDuration    int32    `protobuf:"varint,6,opt,name=find_video_duration,json=findVideoDuration,proto3" json:"find_video_duration"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseSystemInfoResponse) Reset()         { *m = DiagnoseSystemInfoResponse{} }
func (m *DiagnoseSystemInfoResponse) String() string { return proto.CompactTextString(m) }
func (*DiagnoseSystemInfoResponse) ProtoMessage()    {}
func (*DiagnoseSystemInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{27}
}

func (m *DiagnoseSystemInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseSystemInfoResponse.Unmarshal(m, b)
}
func (m *DiagnoseSystemInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseSystemInfoResponse.Marshal(b, m, deterministic)
}
func (m *DiagnoseSystemInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseSystemInfoResponse.Merge(m, src)
}
func (m *DiagnoseSystemInfoResponse) XXX_Size() int {
	return xxx_messageInfo_DiagnoseSystemInfoResponse.Size(m)
}
func (m *DiagnoseSystemInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseSystemInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseSystemInfoResponse proto.InternalMessageInfo

func (m *DiagnoseSystemInfoResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiagnoseSystemInfoResponse) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

func (m *DiagnoseSystemInfoResponse) GetFreeImageTextDuration() int32 {
	if m != nil {
		return m.FreeImageTextDuration
	}
	return 0
}

func (m *DiagnoseSystemInfoResponse) GetQuickImageTextDuration() int32 {
	if m != nil {
		return m.QuickImageTextDuration
	}
	return 0
}

func (m *DiagnoseSystemInfoResponse) GetFindPhoneDuration() int32 {
	if m != nil {
		return m.FindPhoneDuration
	}
	return 0
}

func (m *DiagnoseSystemInfoResponse) GetFindVideoDuration() int32 {
	if m != nil {
		return m.FindVideoDuration
	}
	return 0
}

type DiagnoseSetRequest struct {
	//医生排班时间内不接单开关：0关，1开
	WorkOnOff            int32    `protobuf:"varint,1,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseSetRequest) Reset()         { *m = DiagnoseSetRequest{} }
func (m *DiagnoseSetRequest) String() string { return proto.CompactTextString(m) }
func (*DiagnoseSetRequest) ProtoMessage()    {}
func (*DiagnoseSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{28}
}

func (m *DiagnoseSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseSetRequest.Unmarshal(m, b)
}
func (m *DiagnoseSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseSetRequest.Marshal(b, m, deterministic)
}
func (m *DiagnoseSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseSetRequest.Merge(m, src)
}
func (m *DiagnoseSetRequest) XXX_Size() int {
	return xxx_messageInfo_DiagnoseSetRequest.Size(m)
}
func (m *DiagnoseSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseSetRequest proto.InternalMessageInfo

func (m *DiagnoseSetRequest) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

//医生列表请求参数
type ScrmDoctorListRequest struct {
	//搜索医生 医生姓名或手机号
	DoctorName string `protobuf:"bytes,1,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//任职医院
	HospitalName string `protobuf:"bytes,2,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//医生状态
	DoctorStatus int32 `protobuf:"varint,3,opt,name=doctor_status,json=doctorStatus,proto3" json:"doctor_status"`
	//问诊项目
	DiagnoseProject int32 `protobuf:"varint,4,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式
	DiagnoseForm int32 `protobuf:"varint,5,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmDoctorListRequest) Reset()         { *m = ScrmDoctorListRequest{} }
func (m *ScrmDoctorListRequest) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctorListRequest) ProtoMessage()    {}
func (*ScrmDoctorListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{29}
}

func (m *ScrmDoctorListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctorListRequest.Unmarshal(m, b)
}
func (m *ScrmDoctorListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctorListRequest.Marshal(b, m, deterministic)
}
func (m *ScrmDoctorListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctorListRequest.Merge(m, src)
}
func (m *ScrmDoctorListRequest) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctorListRequest.Size(m)
}
func (m *ScrmDoctorListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctorListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctorListRequest proto.InternalMessageInfo

func (m *ScrmDoctorListRequest) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ScrmDoctorListRequest) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ScrmDoctorListRequest) GetDoctorStatus() int32 {
	if m != nil {
		return m.DoctorStatus
	}
	return 0
}

func (m *ScrmDoctorListRequest) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *ScrmDoctorListRequest) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *ScrmDoctorListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ScrmDoctorListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ScrmDoctorListResponse struct {
	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	// 医生列表
	Data                 []*ScrmDoctorSettingInfo `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ScrmDoctorListResponse) Reset()         { *m = ScrmDoctorListResponse{} }
func (m *ScrmDoctorListResponse) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctorListResponse) ProtoMessage()    {}
func (*ScrmDoctorListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{30}
}

func (m *ScrmDoctorListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctorListResponse.Unmarshal(m, b)
}
func (m *ScrmDoctorListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctorListResponse.Marshal(b, m, deterministic)
}
func (m *ScrmDoctorListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctorListResponse.Merge(m, src)
}
func (m *ScrmDoctorListResponse) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctorListResponse.Size(m)
}
func (m *ScrmDoctorListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctorListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctorListResponse proto.InternalMessageInfo

func (m *ScrmDoctorListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ScrmDoctorListResponse) GetData() []*ScrmDoctorSettingInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type ScrmDoctorSettingInfo struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//兽医证号
	DoctorNum string `protobuf:"bytes,2,opt,name=doctor_num,json=doctorNum,proto3" json:"doctor_num"`
	//名称
	DoctorName string `protobuf:"bytes,3,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,4,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生特长
	DoctorSpeciality string `protobuf:"bytes,5,opt,name=doctor_speciality,json=doctorSpeciality,proto3" json:"doctor_speciality"`
	//介绍
	DoctorPresent string `protobuf:"bytes,6,opt,name=doctor_present,json=doctorPresent,proto3" json:"doctor_present"`
	//医院ID
	HospitalId string `protobuf:"bytes,7,opt,name=hospital_id,json=hospitalId,proto3" json:"hospital_id"`
	//电话
	DoctorPhone string `protobuf:"bytes,8,opt,name=doctor_phone,json=doctorPhone,proto3" json:"doctor_phone"`
	//医生照片
	DoctorImg string `protobuf:"bytes,9,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医生性别 0未知 1男 2女
	DoctorSex int32 `protobuf:"varint,10,opt,name=doctor_sex,json=doctorSex,proto3" json:"doctor_sex"`
	//医院名称
	HospitalName string `protobuf:"bytes,11,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//更新时间
	UpdateTime string `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//财务编码
	EppCode string `protobuf:"bytes,13,opt,name=epp_code,json=eppCode,proto3" json:"epp_code"`
	//大区
	Region string `protobuf:"bytes,14,opt,name=region,proto3" json:"region"`
	//城市
	HospitalCity string `protobuf:"bytes,15,opt,name=hospital_city,json=hospitalCity,proto3" json:"hospital_city"`
	//是否开启在线问诊功能:0-否，1-是
	OpenDiagnose int32 `protobuf:"varint,16,opt,name=open_diagnose,json=openDiagnose,proto3" json:"open_diagnose"`
	//是否开启在线问诊服务:0-否，1-是
	OpenDiagnoseService int32 `protobuf:"varint,17,opt,name=open_diagnose_service,json=openDiagnoseService,proto3" json:"open_diagnose_service"`
	//医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
	DiagnoseForms int32 `protobuf:"varint,18,opt,name=diagnose_forms,json=diagnoseForms,proto3" json:"diagnose_forms"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `protobuf:"varint,19,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	//是否接受抢单：0-否，1-是
	AcceptPreempt int32 `protobuf:"varint,20,opt,name=accept_preempt,json=acceptPreempt,proto3" json:"accept_preempt"`
	//是否接受派单(指用户能找到该医生)：0-否，1-是
	AcceptSend int32 `protobuf:"varint,21,opt,name=accept_send,json=acceptSend,proto3" json:"accept_send"`
	//后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
	WorkOnOff int32 `protobuf:"varint,22,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	//快速图文问诊费用（单位分）
	QuickImageTextPrice int32 `protobuf:"varint,23,opt,name=quick_image_text_price,json=quickImageTextPrice,proto3" json:"quick_image_text_price"`
	//图文问诊费用（单位分）
	ImageTextPrice int32 `protobuf:"varint,24,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//电话问诊费用（单位分）
	PhonePrice int32 `protobuf:"varint,25,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//视频问诊费用（单位分）
	VideoPrice           int32    `protobuf:"varint,26,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmDoctorSettingInfo) Reset()         { *m = ScrmDoctorSettingInfo{} }
func (m *ScrmDoctorSettingInfo) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctorSettingInfo) ProtoMessage()    {}
func (*ScrmDoctorSettingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{31}
}

func (m *ScrmDoctorSettingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctorSettingInfo.Unmarshal(m, b)
}
func (m *ScrmDoctorSettingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctorSettingInfo.Marshal(b, m, deterministic)
}
func (m *ScrmDoctorSettingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctorSettingInfo.Merge(m, src)
}
func (m *ScrmDoctorSettingInfo) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctorSettingInfo.Size(m)
}
func (m *ScrmDoctorSettingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctorSettingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctorSettingInfo proto.InternalMessageInfo

func (m *ScrmDoctorSettingInfo) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorNum() string {
	if m != nil {
		return m.DoctorNum
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorSpeciality() string {
	if m != nil {
		return m.DoctorSpeciality
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorPresent() string {
	if m != nil {
		return m.DoctorPresent
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetHospitalId() string {
	if m != nil {
		return m.HospitalId
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorPhone() string {
	if m != nil {
		return m.DoctorPhone
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetDoctorSex() int32 {
	if m != nil {
		return m.DoctorSex
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetEppCode() string {
	if m != nil {
		return m.EppCode
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetHospitalCity() string {
	if m != nil {
		return m.HospitalCity
	}
	return ""
}

func (m *ScrmDoctorSettingInfo) GetOpenDiagnose() int32 {
	if m != nil {
		return m.OpenDiagnose
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetOpenDiagnoseService() int32 {
	if m != nil {
		return m.OpenDiagnoseService
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetDiagnoseForms() int32 {
	if m != nil {
		return m.DiagnoseForms
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetAcceptPreempt() int32 {
	if m != nil {
		return m.AcceptPreempt
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetAcceptSend() int32 {
	if m != nil {
		return m.AcceptSend
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetQuickImageTextPrice() int32 {
	if m != nil {
		return m.QuickImageTextPrice
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *ScrmDoctorSettingInfo) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

type ScrmDoctor struct {
	//主键ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医生ID
	DoctorCode string `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生姓名
	DoctorName string `protobuf:"bytes,3,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//手机号
	DoctorPhone string `protobuf:"bytes,4,opt,name=doctor_phone,json=doctorPhone,proto3" json:"doctor_phone"`
	//医生职级
	DoctorLevel string `protobuf:"bytes,5,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//任职医院
	HospitalName string `protobuf:"bytes,6,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//上次操作时间
	UpdateTime string `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//财务编码
	EppCode string `protobuf:"bytes,8,opt,name=epp_code,json=eppCode,proto3" json:"epp_code"`
	//大区
	Region string `protobuf:"bytes,9,opt,name=region,proto3" json:"region"`
	//城市
	HospitalCity string `protobuf:"bytes,10,opt,name=hospital_city,json=hospitalCity,proto3" json:"hospital_city"`
	//状态 0正常 4停职 8待离职 16离职 64禁用 -1已删'
	DoctorStatus          int32                  `protobuf:"varint,11,opt,name=doctor_status,json=doctorStatus,proto3" json:"doctor_status"`
	DiagnoseDoctorSetting *DiagnoseDoctorSetting `protobuf:"bytes,12,opt,name=diagnose_doctor_setting,json=diagnoseDoctorSetting,proto3" json:"diagnose_doctor_setting"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *ScrmDoctor) Reset()         { *m = ScrmDoctor{} }
func (m *ScrmDoctor) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctor) ProtoMessage()    {}
func (*ScrmDoctor) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{32}
}

func (m *ScrmDoctor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctor.Unmarshal(m, b)
}
func (m *ScrmDoctor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctor.Marshal(b, m, deterministic)
}
func (m *ScrmDoctor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctor.Merge(m, src)
}
func (m *ScrmDoctor) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctor.Size(m)
}
func (m *ScrmDoctor) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctor.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctor proto.InternalMessageInfo

func (m *ScrmDoctor) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScrmDoctor) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorPhone() string {
	if m != nil {
		return m.DoctorPhone
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *ScrmDoctor) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ScrmDoctor) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *ScrmDoctor) GetEppCode() string {
	if m != nil {
		return m.EppCode
	}
	return ""
}

func (m *ScrmDoctor) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *ScrmDoctor) GetHospitalCity() string {
	if m != nil {
		return m.HospitalCity
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorStatus() int32 {
	if m != nil {
		return m.DoctorStatus
	}
	return 0
}

func (m *ScrmDoctor) GetDiagnoseDoctorSetting() *DiagnoseDoctorSetting {
	if m != nil {
		return m.DiagnoseDoctorSetting
	}
	return nil
}

type DiagnoseDoctorSetting struct {
	//图文问诊费用（单位分）
	ImageTextPrice int32 `protobuf:"varint,1,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//电话问诊费用（单位分）
	PhonePrice int32 `protobuf:"varint,2,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//视频问诊费用（单位分）
	VideoPrice int32 `protobuf:"varint,3,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	//快速问诊图文问诊费用（单位分）
	QuickImageTextPrice int32 `protobuf:"varint,4,opt,name=quick_image_text_price,json=quickImageTextPrice,proto3" json:"quick_image_text_price"`
	//是否被禁用：0-否，1-是
	IsForbidden          int32    `protobuf:"varint,5,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseDoctorSetting) Reset()         { *m = DiagnoseDoctorSetting{} }
func (m *DiagnoseDoctorSetting) String() string { return proto.CompactTextString(m) }
func (*DiagnoseDoctorSetting) ProtoMessage()    {}
func (*DiagnoseDoctorSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{33}
}

func (m *DiagnoseDoctorSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseDoctorSetting.Unmarshal(m, b)
}
func (m *DiagnoseDoctorSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseDoctorSetting.Marshal(b, m, deterministic)
}
func (m *DiagnoseDoctorSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseDoctorSetting.Merge(m, src)
}
func (m *DiagnoseDoctorSetting) XXX_Size() int {
	return xxx_messageInfo_DiagnoseDoctorSetting.Size(m)
}
func (m *DiagnoseDoctorSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseDoctorSetting.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseDoctorSetting proto.InternalMessageInfo

func (m *DiagnoseDoctorSetting) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *DiagnoseDoctorSetting) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *DiagnoseDoctorSetting) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

func (m *DiagnoseDoctorSetting) GetQuickImageTextPrice() int32 {
	if m != nil {
		return m.QuickImageTextPrice
	}
	return 0
}

func (m *DiagnoseDoctorSetting) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

//问诊列表请求参数
type DiagnoseListRequest struct {
	//问诊日期开始
	StartDate string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date"`
	//问诊结束日期
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	//问诊单号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//搜索医生 医生姓名或手机号
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//问诊项目
	DiagnoseProject int32 `protobuf:"varint,5,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//订单状态
	State int32 `protobuf:"varint,6,opt,name=state,proto3" json:"state"`
	//问诊形式
	DiagnoseForm int32 `protobuf:"varint,7,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseListRequest) Reset()         { *m = DiagnoseListRequest{} }
func (m *DiagnoseListRequest) String() string { return proto.CompactTextString(m) }
func (*DiagnoseListRequest) ProtoMessage()    {}
func (*DiagnoseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{34}
}

func (m *DiagnoseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseListRequest.Unmarshal(m, b)
}
func (m *DiagnoseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseListRequest.Marshal(b, m, deterministic)
}
func (m *DiagnoseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseListRequest.Merge(m, src)
}
func (m *DiagnoseListRequest) XXX_Size() int {
	return xxx_messageInfo_DiagnoseListRequest.Size(m)
}
func (m *DiagnoseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseListRequest proto.InternalMessageInfo

func (m *DiagnoseListRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *DiagnoseListRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *DiagnoseListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *DiagnoseListRequest) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *DiagnoseListRequest) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *DiagnoseListRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *DiagnoseListRequest) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *DiagnoseListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *DiagnoseListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//问诊列表返回数据
type DiagnoseListResponse struct {
	Total                int32           `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Data                 []*DiagnoseInfo `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DiagnoseListResponse) Reset()         { *m = DiagnoseListResponse{} }
func (m *DiagnoseListResponse) String() string { return proto.CompactTextString(m) }
func (*DiagnoseListResponse) ProtoMessage()    {}
func (*DiagnoseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{35}
}

func (m *DiagnoseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseListResponse.Unmarshal(m, b)
}
func (m *DiagnoseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseListResponse.Marshal(b, m, deterministic)
}
func (m *DiagnoseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseListResponse.Merge(m, src)
}
func (m *DiagnoseListResponse) XXX_Size() int {
	return xxx_messageInfo_DiagnoseListResponse.Size(m)
}
func (m *DiagnoseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseListResponse proto.InternalMessageInfo

func (m *DiagnoseListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DiagnoseListResponse) GetData() []*DiagnoseInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiagnoseInfo struct {
	//问诊ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//订单ID
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//问诊时间
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//医生名称
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生手机号
	DoctorPhone string `protobuf:"bytes,5,opt,name=doctor_phone,json=doctorPhone,proto3" json:"doctor_phone"`
	//医生职级
	DoctorLevel string `protobuf:"bytes,6,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医院名称
	HospitalName string `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//客户姓名
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//客户手机号
	UserPhone string `protobuf:"bytes,9,opt,name=user_phone,json=userPhone,proto3" json:"user_phone"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,10,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,11,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//医生接入时间
	DoctorJoin string `protobuf:"bytes,12,opt,name=doctor_join,json=doctorJoin,proto3" json:"doctor_join"`
	//订单状态
	State int32 `protobuf:"varint,13,opt,name=state,proto3" json:"state"`
	//用户所在城市
	UserCity string `protobuf:"bytes,14,opt,name=user_city,json=userCity,proto3" json:"user_city"`
	//支付费用
	PayAmount int32 `protobuf:"varint,15,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//医生首次回复时间
	FirstReplyTime string `protobuf:"bytes,16,opt,name=first_reply_time,json=firstReplyTime,proto3" json:"first_reply_time"`
	//首次回复间隔时间
	IntervalTime         string   `protobuf:"bytes,17,opt,name=interval_time,json=intervalTime,proto3" json:"interval_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseInfo) Reset()         { *m = DiagnoseInfo{} }
func (m *DiagnoseInfo) String() string { return proto.CompactTextString(m) }
func (*DiagnoseInfo) ProtoMessage()    {}
func (*DiagnoseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{36}
}

func (m *DiagnoseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseInfo.Unmarshal(m, b)
}
func (m *DiagnoseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseInfo.Marshal(b, m, deterministic)
}
func (m *DiagnoseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseInfo.Merge(m, src)
}
func (m *DiagnoseInfo) XXX_Size() int {
	return xxx_messageInfo_DiagnoseInfo.Size(m)
}
func (m *DiagnoseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseInfo proto.InternalMessageInfo

func (m *DiagnoseInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiagnoseInfo) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *DiagnoseInfo) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *DiagnoseInfo) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *DiagnoseInfo) GetDoctorPhone() string {
	if m != nil {
		return m.DoctorPhone
	}
	return ""
}

func (m *DiagnoseInfo) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *DiagnoseInfo) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *DiagnoseInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DiagnoseInfo) GetUserPhone() string {
	if m != nil {
		return m.UserPhone
	}
	return ""
}

func (m *DiagnoseInfo) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *DiagnoseInfo) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *DiagnoseInfo) GetDoctorJoin() string {
	if m != nil {
		return m.DoctorJoin
	}
	return ""
}

func (m *DiagnoseInfo) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *DiagnoseInfo) GetUserCity() string {
	if m != nil {
		return m.UserCity
	}
	return ""
}

func (m *DiagnoseInfo) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *DiagnoseInfo) GetFirstReplyTime() string {
	if m != nil {
		return m.FirstReplyTime
	}
	return ""
}

func (m *DiagnoseInfo) GetIntervalTime() string {
	if m != nil {
		return m.IntervalTime
	}
	return ""
}

//在线问诊医生列表
type DoctorListRequest struct {
	//页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//页数
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//城市
	HospitalCity string `protobuf:"bytes,3,opt,name=hospital_city,json=hospitalCity,proto3" json:"hospital_city"`
	//职称
	DoctorLevel string `protobuf:"bytes,4,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//特长
	DoctorSpeciality string `protobuf:"bytes,5,opt,name=doctor_speciality,json=doctorSpeciality,proto3" json:"doctor_speciality"`
	//价格区间最小价格
	MinPrice int32 `protobuf:"varint,6,opt,name=min_price,json=minPrice,proto3" json:"min_price"`
	//价格区间最大价格
	MaxPrice int32 `protobuf:"varint,7,opt,name=max_price,json=maxPrice,proto3" json:"max_price"`
	//坐标longitude
	Longitude string `protobuf:"bytes,8,opt,name=longitude,proto3" json:"longitude"`
	//坐标latitude
	Latitude string `protobuf:"bytes,9,opt,name=latitude,proto3" json:"latitude"`
	//医生名称/医院名称
	Key                  string   `protobuf:"bytes,10,opt,name=key,proto3" json:"key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorListRequest) Reset()         { *m = DoctorListRequest{} }
func (m *DoctorListRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorListRequest) ProtoMessage()    {}
func (*DoctorListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{37}
}

func (m *DoctorListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorListRequest.Unmarshal(m, b)
}
func (m *DoctorListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorListRequest.Marshal(b, m, deterministic)
}
func (m *DoctorListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorListRequest.Merge(m, src)
}
func (m *DoctorListRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorListRequest.Size(m)
}
func (m *DoctorListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorListRequest proto.InternalMessageInfo

func (m *DoctorListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *DoctorListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DoctorListRequest) GetHospitalCity() string {
	if m != nil {
		return m.HospitalCity
	}
	return ""
}

func (m *DoctorListRequest) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *DoctorListRequest) GetDoctorSpeciality() string {
	if m != nil {
		return m.DoctorSpeciality
	}
	return ""
}

func (m *DoctorListRequest) GetMinPrice() int32 {
	if m != nil {
		return m.MinPrice
	}
	return 0
}

func (m *DoctorListRequest) GetMaxPrice() int32 {
	if m != nil {
		return m.MaxPrice
	}
	return 0
}

func (m *DoctorListRequest) GetLongitude() string {
	if m != nil {
		return m.Longitude
	}
	return ""
}

func (m *DoctorListRequest) GetLatitude() string {
	if m != nil {
		return m.Latitude
	}
	return ""
}

func (m *DoctorListRequest) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type DoctorListResponse struct {
	//总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	//列表数据
	List                 []*DoctorListResponse_DoctorList `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *DoctorListResponse) Reset()         { *m = DoctorListResponse{} }
func (m *DoctorListResponse) String() string { return proto.CompactTextString(m) }
func (*DoctorListResponse) ProtoMessage()    {}
func (*DoctorListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{38}
}

func (m *DoctorListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorListResponse.Unmarshal(m, b)
}
func (m *DoctorListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorListResponse.Marshal(b, m, deterministic)
}
func (m *DoctorListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorListResponse.Merge(m, src)
}
func (m *DoctorListResponse) XXX_Size() int {
	return xxx_messageInfo_DoctorListResponse.Size(m)
}
func (m *DoctorListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorListResponse proto.InternalMessageInfo

func (m *DoctorListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DoctorListResponse) GetList() []*DoctorListResponse_DoctorList {
	if m != nil {
		return m.List
	}
	return nil
}

//医生结构体
type DoctorListResponse_DoctorList struct {
	//医生code
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生名称
	DoctorName string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生等级
	DoctorLevel string `protobuf:"bytes,3,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生tag
	TagName string `protobuf:"bytes,4,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	//医生图片
	DoctorImg string `protobuf:"bytes,5,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//快速问诊费用/分
	QuickImageTextPrice int32 `protobuf:"varint,6,opt,name=quick_image_text_price,json=quickImageTextPrice,proto3" json:"quick_image_text_price"`
	//找医生图文问诊费用/分
	ImageTextPrice int32 `protobuf:"varint,7,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//找医生电话问诊费用/分
	PhonePrice int32 `protobuf:"varint,8,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//找医生视频问诊费用/分
	VideoPrice int32 `protobuf:"varint,9,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	//医院名称
	HospitalName string `protobuf:"bytes,10,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//医生专长
	DoctorSpeciality     string   `protobuf:"bytes,11,opt,name=doctor_speciality,json=doctorSpeciality,proto3" json:"doctor_speciality"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorListResponse_DoctorList) Reset()         { *m = DoctorListResponse_DoctorList{} }
func (m *DoctorListResponse_DoctorList) String() string { return proto.CompactTextString(m) }
func (*DoctorListResponse_DoctorList) ProtoMessage()    {}
func (*DoctorListResponse_DoctorList) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{38, 0}
}

func (m *DoctorListResponse_DoctorList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorListResponse_DoctorList.Unmarshal(m, b)
}
func (m *DoctorListResponse_DoctorList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorListResponse_DoctorList.Marshal(b, m, deterministic)
}
func (m *DoctorListResponse_DoctorList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorListResponse_DoctorList.Merge(m, src)
}
func (m *DoctorListResponse_DoctorList) XXX_Size() int {
	return xxx_messageInfo_DoctorListResponse_DoctorList.Size(m)
}
func (m *DoctorListResponse_DoctorList) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorListResponse_DoctorList.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorListResponse_DoctorList proto.InternalMessageInfo

func (m *DoctorListResponse_DoctorList) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetQuickImageTextPrice() int32 {
	if m != nil {
		return m.QuickImageTextPrice
	}
	return 0
}

func (m *DoctorListResponse_DoctorList) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *DoctorListResponse_DoctorList) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *DoctorListResponse_DoctorList) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

func (m *DoctorListResponse_DoctorList) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *DoctorListResponse_DoctorList) GetDoctorSpeciality() string {
	if m != nil {
		return m.DoctorSpeciality
	}
	return ""
}

type RegisterDoctorMiniProgramRes struct {
	DoctorCode           string   `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	DoctorName           string   `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	DoctorMobile         string   `protobuf:"bytes,3,opt,name=doctor_mobile,json=doctorMobile,proto3" json:"doctor_mobile"`
	IsInternetDoctor     bool     `protobuf:"varint,4,opt,name=is_internet_doctor,json=isInternetDoctor,proto3" json:"is_internet_doctor"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterDoctorMiniProgramRes) Reset()         { *m = RegisterDoctorMiniProgramRes{} }
func (m *RegisterDoctorMiniProgramRes) String() string { return proto.CompactTextString(m) }
func (*RegisterDoctorMiniProgramRes) ProtoMessage()    {}
func (*RegisterDoctorMiniProgramRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{39}
}

func (m *RegisterDoctorMiniProgramRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterDoctorMiniProgramRes.Unmarshal(m, b)
}
func (m *RegisterDoctorMiniProgramRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterDoctorMiniProgramRes.Marshal(b, m, deterministic)
}
func (m *RegisterDoctorMiniProgramRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterDoctorMiniProgramRes.Merge(m, src)
}
func (m *RegisterDoctorMiniProgramRes) XXX_Size() int {
	return xxx_messageInfo_RegisterDoctorMiniProgramRes.Size(m)
}
func (m *RegisterDoctorMiniProgramRes) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterDoctorMiniProgramRes.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterDoctorMiniProgramRes proto.InternalMessageInfo

func (m *RegisterDoctorMiniProgramRes) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *RegisterDoctorMiniProgramRes) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *RegisterDoctorMiniProgramRes) GetDoctorMobile() string {
	if m != nil {
		return m.DoctorMobile
	}
	return ""
}

func (m *RegisterDoctorMiniProgramRes) GetIsInternetDoctor() bool {
	if m != nil {
		return m.IsInternetDoctor
	}
	return false
}

type OrderList struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//用户id
	ScrmUserId string `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//用户名称
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//用户头像
	UserAvatar string `protobuf:"bytes,4,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,5,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,6,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,7,opt,name=symptom,proto3" json:"symptom"`
	//补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
	SymptomDesc string `protobuf:"bytes,8,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//订单编号
	OrderSn string `protobuf:"bytes,9,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//最后对话时间
	LastReplyTime string `protobuf:"bytes,10,opt,name=last_reply_time,json=lastReplyTime,proto3" json:"last_reply_time"`
	//问诊时间
	CreateTime           string   `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderList) Reset()         { *m = OrderList{} }
func (m *OrderList) String() string { return proto.CompactTextString(m) }
func (*OrderList) ProtoMessage()    {}
func (*OrderList) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{40}
}

func (m *OrderList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderList.Unmarshal(m, b)
}
func (m *OrderList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderList.Marshal(b, m, deterministic)
}
func (m *OrderList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderList.Merge(m, src)
}
func (m *OrderList) XXX_Size() int {
	return xxx_messageInfo_OrderList.Size(m)
}
func (m *OrderList) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderList.DiscardUnknown(m)
}

var xxx_messageInfo_OrderList proto.InternalMessageInfo

func (m *OrderList) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *OrderList) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *OrderList) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *OrderList) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *OrderList) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *OrderList) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *OrderList) GetSymptom() string {
	if m != nil {
		return m.Symptom
	}
	return ""
}

func (m *OrderList) GetSymptomDesc() string {
	if m != nil {
		return m.SymptomDesc
	}
	return ""
}

func (m *OrderList) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderList) GetLastReplyTime() string {
	if m != nil {
		return m.LastReplyTime
	}
	return ""
}

func (m *OrderList) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

//开启接单、关闭接单
type DoctorSettingSwitchRequest struct {
	OperateType          int32    `protobuf:"varint,1,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorSettingSwitchRequest) Reset()         { *m = DoctorSettingSwitchRequest{} }
func (m *DoctorSettingSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingSwitchRequest) ProtoMessage()    {}
func (*DoctorSettingSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{41}
}

func (m *DoctorSettingSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingSwitchRequest.Unmarshal(m, b)
}
func (m *DoctorSettingSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingSwitchRequest.Marshal(b, m, deterministic)
}
func (m *DoctorSettingSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingSwitchRequest.Merge(m, src)
}
func (m *DoctorSettingSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingSwitchRequest.Size(m)
}
func (m *DoctorSettingSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingSwitchRequest proto.InternalMessageInfo

func (m *DoctorSettingSwitchRequest) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

//开启接单、关闭接单
type DoctorSettingSwitchResponse struct {
	AffectRows           int64    `protobuf:"varint,1,opt,name=affect_rows,json=affectRows,proto3" json:"affect_rows"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorSettingSwitchResponse) Reset()         { *m = DoctorSettingSwitchResponse{} }
func (m *DoctorSettingSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingSwitchResponse) ProtoMessage()    {}
func (*DoctorSettingSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{42}
}

func (m *DoctorSettingSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingSwitchResponse.Unmarshal(m, b)
}
func (m *DoctorSettingSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingSwitchResponse.Marshal(b, m, deterministic)
}
func (m *DoctorSettingSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingSwitchResponse.Merge(m, src)
}
func (m *DoctorSettingSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingSwitchResponse.Size(m)
}
func (m *DoctorSettingSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingSwitchResponse proto.InternalMessageInfo

func (m *DoctorSettingSwitchResponse) GetAffectRows() int64 {
	if m != nil {
		return m.AffectRows
	}
	return 0
}

// 在线问诊 配置 医生接单设置信息
type DoctorSettingRequest struct {
	//医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
	DiagnoseForms string `protobuf:"bytes,1,opt,name=diagnose_forms,json=diagnoseForms,proto3" json:"diagnose_forms"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `protobuf:"varint,2,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	//是否接受抢单：0-否，1-是
	AcceptPreempt int32 `protobuf:"varint,3,opt,name=accept_preempt,json=acceptPreempt,proto3" json:"accept_preempt"`
	//是否接受派单(指用户能找到该医生)：0-否，1-是
	AcceptSend int32 `protobuf:"varint,4,opt,name=accept_send,json=acceptSend,proto3" json:"accept_send"`
	//后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
	WorkOnOff int32 `protobuf:"varint,5,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	//设置可电话/视频的开始时间
	StartTime string `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//设置可电话/视频的结束时间
	EndTime string `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//图文问诊费用（单位分）
	ImageTextPrice int32 `protobuf:"varint,8,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//电话问诊费用（单位分）
	PhonePrice int32 `protobuf:"varint,9,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//视频问诊费用（单位分）
	VideoPrice int32 `protobuf:"varint,10,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	//医生编号
	DoctorCode           string   `protobuf:"bytes,11,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorSettingRequest) Reset()         { *m = DoctorSettingRequest{} }
func (m *DoctorSettingRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingRequest) ProtoMessage()    {}
func (*DoctorSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{43}
}

func (m *DoctorSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingRequest.Unmarshal(m, b)
}
func (m *DoctorSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingRequest.Marshal(b, m, deterministic)
}
func (m *DoctorSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingRequest.Merge(m, src)
}
func (m *DoctorSettingRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingRequest.Size(m)
}
func (m *DoctorSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingRequest proto.InternalMessageInfo

func (m *DoctorSettingRequest) GetDiagnoseForms() string {
	if m != nil {
		return m.DiagnoseForms
	}
	return ""
}

func (m *DoctorSettingRequest) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

func (m *DoctorSettingRequest) GetAcceptPreempt() int32 {
	if m != nil {
		return m.AcceptPreempt
	}
	return 0
}

func (m *DoctorSettingRequest) GetAcceptSend() int32 {
	if m != nil {
		return m.AcceptSend
	}
	return 0
}

func (m *DoctorSettingRequest) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

func (m *DoctorSettingRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DoctorSettingRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *DoctorSettingRequest) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *DoctorSettingRequest) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *DoctorSettingRequest) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

func (m *DoctorSettingRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

// 在线问诊 配置 医生接单设置信息
type DoctorSettingReponse struct {
	AffectRows           int64    `protobuf:"varint,1,opt,name=affect_rows,json=affectRows,proto3" json:"affect_rows"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorSettingReponse) Reset()         { *m = DoctorSettingReponse{} }
func (m *DoctorSettingReponse) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingReponse) ProtoMessage()    {}
func (*DoctorSettingReponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{44}
}

func (m *DoctorSettingReponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingReponse.Unmarshal(m, b)
}
func (m *DoctorSettingReponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingReponse.Marshal(b, m, deterministic)
}
func (m *DoctorSettingReponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingReponse.Merge(m, src)
}
func (m *DoctorSettingReponse) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingReponse.Size(m)
}
func (m *DoctorSettingReponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingReponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingReponse proto.InternalMessageInfo

func (m *DoctorSettingReponse) GetAffectRows() int64 {
	if m != nil {
		return m.AffectRows
	}
	return 0
}

//获取在线问诊 医生接单设置信息
type DoctorSettingInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorSettingInfoRequest) Reset()         { *m = DoctorSettingInfoRequest{} }
func (m *DoctorSettingInfoRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingInfoRequest) ProtoMessage()    {}
func (*DoctorSettingInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{45}
}

func (m *DoctorSettingInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingInfoRequest.Unmarshal(m, b)
}
func (m *DoctorSettingInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingInfoRequest.Marshal(b, m, deterministic)
}
func (m *DoctorSettingInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingInfoRequest.Merge(m, src)
}
func (m *DoctorSettingInfoRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingInfoRequest.Size(m)
}
func (m *DoctorSettingInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingInfoRequest proto.InternalMessageInfo

//获取在线问诊 医生接单设置信息
type DoctorSettingInfoResponse struct {
	Info                 *DiagnoseSetting `protobuf:"bytes,1,opt,name=info,proto3" json:"info"`
	Done                 bool             `protobuf:"varint,2,opt,name=done,proto3" json:"done"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DoctorSettingInfoResponse) Reset()         { *m = DoctorSettingInfoResponse{} }
func (m *DoctorSettingInfoResponse) String() string { return proto.CompactTextString(m) }
func (*DoctorSettingInfoResponse) ProtoMessage()    {}
func (*DoctorSettingInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{46}
}

func (m *DoctorSettingInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorSettingInfoResponse.Unmarshal(m, b)
}
func (m *DoctorSettingInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorSettingInfoResponse.Marshal(b, m, deterministic)
}
func (m *DoctorSettingInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorSettingInfoResponse.Merge(m, src)
}
func (m *DoctorSettingInfoResponse) XXX_Size() int {
	return xxx_messageInfo_DoctorSettingInfoResponse.Size(m)
}
func (m *DoctorSettingInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorSettingInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorSettingInfoResponse proto.InternalMessageInfo

func (m *DoctorSettingInfoResponse) GetInfo() *DiagnoseSetting {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *DoctorSettingInfoResponse) GetDone() bool {
	if m != nil {
		return m.Done
	}
	return false
}

// 在线问诊医生接单设置信息
type DiagnoseSetting struct {
	//自增id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生类别：1门店医生，2互联网医生
	DoctorType int32 `protobuf:"varint,3,opt,name=doctor_type,json=doctorType,proto3" json:"doctor_type"`
	//否开启在线问诊功能:0-否，1-是
	OpenDiagnose int32 `protobuf:"varint,4,opt,name=open_diagnose,json=openDiagnose,proto3" json:"open_diagnose"`
	//是否开启在线问诊服务:0-否，1-是
	OpenDiagnoseService int32 `protobuf:"varint,5,opt,name=open_diagnose_service,json=openDiagnoseService,proto3" json:"open_diagnose_service"`
	//医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
	DiagnoseForms string `protobuf:"bytes,6,opt,name=diagnose_forms,json=diagnoseForms,proto3" json:"diagnose_forms"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `protobuf:"varint,7,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	//是否接受抢单：0-否，1-是
	AcceptPreempt int32 `protobuf:"varint,8,opt,name=accept_preempt,json=acceptPreempt,proto3" json:"accept_preempt"`
	//是否接受派单(指用户能找到该医生)：0-否，1-是
	AcceptSend int32 `protobuf:"varint,9,opt,name=accept_send,json=acceptSend,proto3" json:"accept_send"`
	//后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
	WorkOnOff int32 `protobuf:"varint,10,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	//设置可电话/视频的开始时间
	StartTime string `protobuf:"bytes,11,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//设置可电话/视频的结束时间
	EndTime string `protobuf:"bytes,12,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//图文问诊费用（单位分）
	ImageTextPrice int32 `protobuf:"varint,13,opt,name=image_text_price,json=imageTextPrice,proto3" json:"image_text_price"`
	//电话问诊费用（单位分）
	PhonePrice int32 `protobuf:"varint,14,opt,name=phone_price,json=phonePrice,proto3" json:"phone_price"`
	//视频问诊费用（单位分）
	VideoPrice int32 `protobuf:"varint,15,opt,name=video_price,json=videoPrice,proto3" json:"video_price"`
	//创建日期
	CreateTime string `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//最后更新时间
	UpdateTime           string   `protobuf:"bytes,17,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseSetting) Reset()         { *m = DiagnoseSetting{} }
func (m *DiagnoseSetting) String() string { return proto.CompactTextString(m) }
func (*DiagnoseSetting) ProtoMessage()    {}
func (*DiagnoseSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{47}
}

func (m *DiagnoseSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseSetting.Unmarshal(m, b)
}
func (m *DiagnoseSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseSetting.Marshal(b, m, deterministic)
}
func (m *DiagnoseSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseSetting.Merge(m, src)
}
func (m *DiagnoseSetting) XXX_Size() int {
	return xxx_messageInfo_DiagnoseSetting.Size(m)
}
func (m *DiagnoseSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseSetting.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseSetting proto.InternalMessageInfo

func (m *DiagnoseSetting) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiagnoseSetting) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *DiagnoseSetting) GetDoctorType() int32 {
	if m != nil {
		return m.DoctorType
	}
	return 0
}

func (m *DiagnoseSetting) GetOpenDiagnose() int32 {
	if m != nil {
		return m.OpenDiagnose
	}
	return 0
}

func (m *DiagnoseSetting) GetOpenDiagnoseService() int32 {
	if m != nil {
		return m.OpenDiagnoseService
	}
	return 0
}

func (m *DiagnoseSetting) GetDiagnoseForms() string {
	if m != nil {
		return m.DiagnoseForms
	}
	return ""
}

func (m *DiagnoseSetting) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

func (m *DiagnoseSetting) GetAcceptPreempt() int32 {
	if m != nil {
		return m.AcceptPreempt
	}
	return 0
}

func (m *DiagnoseSetting) GetAcceptSend() int32 {
	if m != nil {
		return m.AcceptSend
	}
	return 0
}

func (m *DiagnoseSetting) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

func (m *DiagnoseSetting) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DiagnoseSetting) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *DiagnoseSetting) GetImageTextPrice() int32 {
	if m != nil {
		return m.ImageTextPrice
	}
	return 0
}

func (m *DiagnoseSetting) GetPhonePrice() int32 {
	if m != nil {
		return m.PhonePrice
	}
	return 0
}

func (m *DiagnoseSetting) GetVideoPrice() int32 {
	if m != nil {
		return m.VideoPrice
	}
	return 0
}

func (m *DiagnoseSetting) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *DiagnoseSetting) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type GetDispatchDoctorReq struct {
	DoctorType           int32    `protobuf:"varint,1,opt,name=doctor_type,json=doctorType,proto3" json:"doctor_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDispatchDoctorReq) Reset()         { *m = GetDispatchDoctorReq{} }
func (m *GetDispatchDoctorReq) String() string { return proto.CompactTextString(m) }
func (*GetDispatchDoctorReq) ProtoMessage()    {}
func (*GetDispatchDoctorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{48}
}

func (m *GetDispatchDoctorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDispatchDoctorReq.Unmarshal(m, b)
}
func (m *GetDispatchDoctorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDispatchDoctorReq.Marshal(b, m, deterministic)
}
func (m *GetDispatchDoctorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDispatchDoctorReq.Merge(m, src)
}
func (m *GetDispatchDoctorReq) XXX_Size() int {
	return xxx_messageInfo_GetDispatchDoctorReq.Size(m)
}
func (m *GetDispatchDoctorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDispatchDoctorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDispatchDoctorReq proto.InternalMessageInfo

func (m *GetDispatchDoctorReq) GetDoctorType() int32 {
	if m != nil {
		return m.DoctorType
	}
	return 0
}

//统计类别：1服务待结算 2问诊订单 3我的排名 4客户评分，5抢单数 6派单数 7今日接单数  多个用英文逗号隔开，不传代表不需要统计信息
type StatisticsType struct {
	StatisticsType       string   `protobuf:"bytes,1,opt,name=statistics_type,json=statisticsType,proto3" json:"statistics_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatisticsType) Reset()         { *m = StatisticsType{} }
func (m *StatisticsType) String() string { return proto.CompactTextString(m) }
func (*StatisticsType) ProtoMessage()    {}
func (*StatisticsType) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{49}
}

func (m *StatisticsType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatisticsType.Unmarshal(m, b)
}
func (m *StatisticsType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatisticsType.Marshal(b, m, deterministic)
}
func (m *StatisticsType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatisticsType.Merge(m, src)
}
func (m *StatisticsType) XXX_Size() int {
	return xxx_messageInfo_StatisticsType.Size(m)
}
func (m *StatisticsType) XXX_DiscardUnknown() {
	xxx_messageInfo_StatisticsType.DiscardUnknown(m)
}

var xxx_messageInfo_StatisticsType proto.InternalMessageInfo

func (m *StatisticsType) GetStatisticsType() string {
	if m != nil {
		return m.StatisticsType
	}
	return ""
}

type DoctorInfoResponse struct {
	DoctorInfo           *DoctorInfo `protobuf:"bytes,1,opt,name=doctor_info,json=doctorInfo,proto3" json:"doctor_info"`
	Statistics           *Statistics `protobuf:"bytes,2,opt,name=statistics,proto3" json:"statistics"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DoctorInfoResponse) Reset()         { *m = DoctorInfoResponse{} }
func (m *DoctorInfoResponse) String() string { return proto.CompactTextString(m) }
func (*DoctorInfoResponse) ProtoMessage()    {}
func (*DoctorInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{50}
}

func (m *DoctorInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorInfoResponse.Unmarshal(m, b)
}
func (m *DoctorInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorInfoResponse.Marshal(b, m, deterministic)
}
func (m *DoctorInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorInfoResponse.Merge(m, src)
}
func (m *DoctorInfoResponse) XXX_Size() int {
	return xxx_messageInfo_DoctorInfoResponse.Size(m)
}
func (m *DoctorInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorInfoResponse proto.InternalMessageInfo

func (m *DoctorInfoResponse) GetDoctorInfo() *DoctorInfo {
	if m != nil {
		return m.DoctorInfo
	}
	return nil
}

func (m *DoctorInfoResponse) GetStatistics() *Statistics {
	if m != nil {
		return m.Statistics
	}
	return nil
}

// 医生基本信息
type DoctorInfo struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生类别:1门店医生，2互联网医生
	DoctorType int32 `protobuf:"varint,2,opt,name=doctor_type,json=doctorType,proto3" json:"doctor_type"`
	//兽医证号
	DoctorNum string `protobuf:"bytes,3,opt,name=doctor_num,json=doctorNum,proto3" json:"doctor_num"`
	//名称
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,5,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生特长
	DoctorSpeciality string `protobuf:"bytes,6,opt,name=doctor_speciality,json=doctorSpeciality,proto3" json:"doctor_speciality"`
	//介绍
	DoctorPresent string `protobuf:"bytes,7,opt,name=doctor_present,json=doctorPresent,proto3" json:"doctor_present"`
	//医院code
	HospitalCode string `protobuf:"bytes,8,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//电话
	DoctorPhone string `protobuf:"bytes,9,opt,name=doctor_phone,json=doctorPhone,proto3" json:"doctor_phone"`
	//医生照片
	DoctorImg string `protobuf:"bytes,10,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院名称
	HospitalName string `protobuf:"bytes,11,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//科室
	TagName string `protobuf:"bytes,12,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	//从业时间
	StartTime string `protobuf:"bytes,13,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//是否被禁用：是否被禁用:0-否，1-是
	IsForbidden int32 `protobuf:"varint,14,opt,name=is_forbidden,json=isForbidden,proto3" json:"is_forbidden"`
	//是否在线（互联网医生用）
	OnLine               int32    `protobuf:"varint,15,opt,name=on_line,json=onLine,proto3" json:"on_line"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorInfo) Reset()         { *m = DoctorInfo{} }
func (m *DoctorInfo) String() string { return proto.CompactTextString(m) }
func (*DoctorInfo) ProtoMessage()    {}
func (*DoctorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{51}
}

func (m *DoctorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorInfo.Unmarshal(m, b)
}
func (m *DoctorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorInfo.Marshal(b, m, deterministic)
}
func (m *DoctorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorInfo.Merge(m, src)
}
func (m *DoctorInfo) XXX_Size() int {
	return xxx_messageInfo_DoctorInfo.Size(m)
}
func (m *DoctorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorInfo proto.InternalMessageInfo

func (m *DoctorInfo) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *DoctorInfo) GetDoctorType() int32 {
	if m != nil {
		return m.DoctorType
	}
	return 0
}

func (m *DoctorInfo) GetDoctorNum() string {
	if m != nil {
		return m.DoctorNum
	}
	return ""
}

func (m *DoctorInfo) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *DoctorInfo) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *DoctorInfo) GetDoctorSpeciality() string {
	if m != nil {
		return m.DoctorSpeciality
	}
	return ""
}

func (m *DoctorInfo) GetDoctorPresent() string {
	if m != nil {
		return m.DoctorPresent
	}
	return ""
}

func (m *DoctorInfo) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *DoctorInfo) GetDoctorPhone() string {
	if m != nil {
		return m.DoctorPhone
	}
	return ""
}

func (m *DoctorInfo) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *DoctorInfo) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *DoctorInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DoctorInfo) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DoctorInfo) GetIsForbidden() int32 {
	if m != nil {
		return m.IsForbidden
	}
	return 0
}

func (m *DoctorInfo) GetOnLine() int32 {
	if m != nil {
		return m.OnLine
	}
	return 0
}

//统计结构体
type Statistics struct {
	//服务待结算(单位元)
	OrderLeftPrice int32 `protobuf:"varint,1,opt,name=order_left_price,json=orderLeftPrice,proto3" json:"order_left_price"`
	//问诊订单
	TotalOrderCnt int32 `protobuf:"varint,2,opt,name=total_order_cnt,json=totalOrderCnt,proto3" json:"total_order_cnt"`
	//我的排名
	MyRanking int32 `protobuf:"varint,3,opt,name=my_ranking,json=myRanking,proto3" json:"my_ranking"`
	//客户评分
	Score float32 `protobuf:"fixed32,4,opt,name=score,proto3" json:"score"`
	//抢单数统计
	PreemptCnt int64 `protobuf:"varint,5,opt,name=preempt_cnt,json=preemptCnt,proto3" json:"preempt_cnt"`
	//派单数统计
	SendCnt int64 `protobuf:"varint,6,opt,name=send_cnt,json=sendCnt,proto3" json:"send_cnt"`
	//今日接单数
	TodayCnt             int64    `protobuf:"varint,7,opt,name=today_cnt,json=todayCnt,proto3" json:"today_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Statistics) Reset()         { *m = Statistics{} }
func (m *Statistics) String() string { return proto.CompactTextString(m) }
func (*Statistics) ProtoMessage()    {}
func (*Statistics) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{52}
}

func (m *Statistics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Statistics.Unmarshal(m, b)
}
func (m *Statistics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Statistics.Marshal(b, m, deterministic)
}
func (m *Statistics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Statistics.Merge(m, src)
}
func (m *Statistics) XXX_Size() int {
	return xxx_messageInfo_Statistics.Size(m)
}
func (m *Statistics) XXX_DiscardUnknown() {
	xxx_messageInfo_Statistics.DiscardUnknown(m)
}

var xxx_messageInfo_Statistics proto.InternalMessageInfo

func (m *Statistics) GetOrderLeftPrice() int32 {
	if m != nil {
		return m.OrderLeftPrice
	}
	return 0
}

func (m *Statistics) GetTotalOrderCnt() int32 {
	if m != nil {
		return m.TotalOrderCnt
	}
	return 0
}

func (m *Statistics) GetMyRanking() int32 {
	if m != nil {
		return m.MyRanking
	}
	return 0
}

func (m *Statistics) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *Statistics) GetPreemptCnt() int64 {
	if m != nil {
		return m.PreemptCnt
	}
	return 0
}

func (m *Statistics) GetSendCnt() int64 {
	if m != nil {
		return m.SendCnt
	}
	return 0
}

func (m *Statistics) GetTodayCnt() int64 {
	if m != nil {
		return m.TodayCnt
	}
	return 0
}

type DiagnoseDetailsRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseDetailsRequest) Reset()         { *m = DiagnoseDetailsRequest{} }
func (m *DiagnoseDetailsRequest) String() string { return proto.CompactTextString(m) }
func (*DiagnoseDetailsRequest) ProtoMessage()    {}
func (*DiagnoseDetailsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{53}
}

func (m *DiagnoseDetailsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseDetailsRequest.Unmarshal(m, b)
}
func (m *DiagnoseDetailsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseDetailsRequest.Marshal(b, m, deterministic)
}
func (m *DiagnoseDetailsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseDetailsRequest.Merge(m, src)
}
func (m *DiagnoseDetailsRequest) XXX_Size() int {
	return xxx_messageInfo_DiagnoseDetailsRequest.Size(m)
}
func (m *DiagnoseDetailsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseDetailsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseDetailsRequest proto.InternalMessageInfo

func (m *DiagnoseDetailsRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DiagnoseDetailsResponse struct {
	DiagnoseDetails      *DiagnoseData `protobuf:"bytes,1,opt,name=diagnose_details,json=diagnoseDetails,proto3" json:"diagnose_details"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DiagnoseDetailsResponse) Reset()         { *m = DiagnoseDetailsResponse{} }
func (m *DiagnoseDetailsResponse) String() string { return proto.CompactTextString(m) }
func (*DiagnoseDetailsResponse) ProtoMessage()    {}
func (*DiagnoseDetailsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{54}
}

func (m *DiagnoseDetailsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseDetailsResponse.Unmarshal(m, b)
}
func (m *DiagnoseDetailsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseDetailsResponse.Marshal(b, m, deterministic)
}
func (m *DiagnoseDetailsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseDetailsResponse.Merge(m, src)
}
func (m *DiagnoseDetailsResponse) XXX_Size() int {
	return xxx_messageInfo_DiagnoseDetailsResponse.Size(m)
}
func (m *DiagnoseDetailsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseDetailsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseDetailsResponse proto.InternalMessageInfo

func (m *DiagnoseDetailsResponse) GetDiagnoseDetails() *DiagnoseData {
	if m != nil {
		return m.DiagnoseDetails
	}
	return nil
}

type DiagnoseData struct {
	//问诊ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//订单ID
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//问诊时间
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//医生名称
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生手机号
	DoctorPhone string `protobuf:"bytes,5,opt,name=doctor_phone,json=doctorPhone,proto3" json:"doctor_phone"`
	//医生职级
	DoctorLevel int32 `protobuf:"varint,6,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医院名称
	HospitalName string `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//客户姓名
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//客户手机号
	UserPhone string `protobuf:"bytes,9,opt,name=user_phone,json=userPhone,proto3" json:"user_phone"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,10,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,11,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//回复间隔(秒)
	ReplyInterval int32 `protobuf:"varint,12,opt,name=reply_interval,json=replyInterval,proto3" json:"reply_interval"`
	//订单状态
	State int32 `protobuf:"varint,13,opt,name=state,proto3" json:"state"`
	//用户所在城市
	UserCity string `protobuf:"bytes,14,opt,name=user_city,json=userCity,proto3" json:"user_city"`
	//支付费用
	PayAmount int32 `protobuf:"varint,15,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//是否已退款
	IsRefund int32 `protobuf:"varint,16,opt,name=is_refund,json=isRefund,proto3" json:"is_refund"`
	//支付时间
	PayTime              string   `protobuf:"bytes,17,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseData) Reset()         { *m = DiagnoseData{} }
func (m *DiagnoseData) String() string { return proto.CompactTextString(m) }
func (*DiagnoseData) ProtoMessage()    {}
func (*DiagnoseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{55}
}

func (m *DiagnoseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseData.Unmarshal(m, b)
}
func (m *DiagnoseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseData.Marshal(b, m, deterministic)
}
func (m *DiagnoseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseData.Merge(m, src)
}
func (m *DiagnoseData) XXX_Size() int {
	return xxx_messageInfo_DiagnoseData.Size(m)
}
func (m *DiagnoseData) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseData.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseData proto.InternalMessageInfo

func (m *DiagnoseData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiagnoseData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *DiagnoseData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *DiagnoseData) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *DiagnoseData) GetDoctorPhone() string {
	if m != nil {
		return m.DoctorPhone
	}
	return ""
}

func (m *DiagnoseData) GetDoctorLevel() int32 {
	if m != nil {
		return m.DoctorLevel
	}
	return 0
}

func (m *DiagnoseData) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *DiagnoseData) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DiagnoseData) GetUserPhone() string {
	if m != nil {
		return m.UserPhone
	}
	return ""
}

func (m *DiagnoseData) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *DiagnoseData) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *DiagnoseData) GetReplyInterval() int32 {
	if m != nil {
		return m.ReplyInterval
	}
	return 0
}

func (m *DiagnoseData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *DiagnoseData) GetUserCity() string {
	if m != nil {
		return m.UserCity
	}
	return ""
}

func (m *DiagnoseData) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *DiagnoseData) GetIsRefund() int32 {
	if m != nil {
		return m.IsRefund
	}
	return 0
}

func (m *DiagnoseData) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

type NotifyGrabOrderRequest struct {
	//经度
	Lng string `protobuf:"bytes,1,opt,name=lng,proto3" json:"lng"`
	//纬度
	Lat string `protobuf:"bytes,2,opt,name=lat,proto3" json:"lat"`
	//订单号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//用户id
	MemberId             string   `protobuf:"bytes,4,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyGrabOrderRequest) Reset()         { *m = NotifyGrabOrderRequest{} }
func (m *NotifyGrabOrderRequest) String() string { return proto.CompactTextString(m) }
func (*NotifyGrabOrderRequest) ProtoMessage()    {}
func (*NotifyGrabOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_79000948969dabd4, []int{56}
}

func (m *NotifyGrabOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyGrabOrderRequest.Unmarshal(m, b)
}
func (m *NotifyGrabOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyGrabOrderRequest.Marshal(b, m, deterministic)
}
func (m *NotifyGrabOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyGrabOrderRequest.Merge(m, src)
}
func (m *NotifyGrabOrderRequest) XXX_Size() int {
	return xxx_messageInfo_NotifyGrabOrderRequest.Size(m)
}
func (m *NotifyGrabOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyGrabOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyGrabOrderRequest proto.InternalMessageInfo

func (m *NotifyGrabOrderRequest) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *NotifyGrabOrderRequest) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *NotifyGrabOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *NotifyGrabOrderRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func init() {
	proto.RegisterType((*GetDoctorProtocolStateRequest)(nil), "dgc.GetDoctorProtocolStateRequest")
	proto.RegisterType((*GetDoctorProtocolStateResponse)(nil), "dgc.GetDoctorProtocolStateResponse")
	proto.RegisterType((*EditDoctorProtocolStateRequest)(nil), "dgc.EditDoctorProtocolStateRequest")
	proto.RegisterType((*EditDoctorProtocolStateResponse)(nil), "dgc.EditDoctorProtocolStateResponse")
	proto.RegisterType((*GetDispatchDoctorByOpenIdReq)(nil), "dgc.GetDispatchDoctorByOpenIdReq")
	proto.RegisterType((*GetDoctorPriceRequest)(nil), "dgc.GetDoctorPriceRequest")
	proto.RegisterType((*GetDoctorPriceResponse)(nil), "dgc.GetDoctorPriceResponse")
	proto.RegisterType((*DoctorIsForbiddenRequest)(nil), "dgc.DoctorIsForbiddenRequest")
	proto.RegisterType((*DoctorTipsRequest)(nil), "dgc.DoctorTipsRequest")
	proto.RegisterType((*GetDoctorInfoRequest)(nil), "dgc.GetDoctorInfoRequest")
	proto.RegisterType((*GetDoctorInfoResponse)(nil), "dgc.GetDoctorInfoResponse")
	proto.RegisterType((*ScrmDoctorListExportRequest)(nil), "dgc.ScrmDoctorListExportRequest")
	proto.RegisterType((*HospitalListRequest)(nil), "dgc.HospitalListRequest")
	proto.RegisterType((*HospitalListResponse)(nil), "dgc.HospitalListResponse")
	proto.RegisterType((*ScrmHospital)(nil), "dgc.ScrmHospital")
	proto.RegisterType((*DoctorStatusRequest)(nil), "dgc.DoctorStatusRequest")
	proto.RegisterType((*DoctorStatusResponse)(nil), "dgc.DoctorStatusResponse")
	proto.RegisterType((*DoctorOnlineChangeRequest)(nil), "dgc.DoctorOnlineChangeRequest")
	proto.RegisterType((*RegisterDoctorMiniProgramReq)(nil), "dgc.RegisterDoctorMiniProgramReq")
	proto.RegisterType((*LogoutDoctorMiniProgramReq)(nil), "dgc.LogoutDoctorMiniProgramReq")
	proto.RegisterType((*EmptyRequest)(nil), "dgc.EmptyRequest")
	proto.RegisterType((*InternetDoctorListRequest)(nil), "dgc.InternetDoctorListRequest")
	proto.RegisterType((*InternetDoctorListResponse)(nil), "dgc.InternetDoctorListResponse")
	proto.RegisterType((*InternetDoctorForbiddenRequest)(nil), "dgc.InternetDoctorForbiddenRequest")
	proto.RegisterType((*InternetDoctorInfoRequest)(nil), "dgc.InternetDoctorInfoRequest")
	proto.RegisterType((*InternetDoctorInfoResponse)(nil), "dgc.InternetDoctorInfoResponse")
	proto.RegisterType((*InternetDoctorAddRequest)(nil), "dgc.InternetDoctorAddRequest")
	proto.RegisterType((*DiagnoseSystemInfoResponse)(nil), "dgc.DiagnoseSystemInfoResponse")
	proto.RegisterType((*DiagnoseSetRequest)(nil), "dgc.DiagnoseSetRequest")
	proto.RegisterType((*ScrmDoctorListRequest)(nil), "dgc.ScrmDoctorListRequest")
	proto.RegisterType((*ScrmDoctorListResponse)(nil), "dgc.ScrmDoctorListResponse")
	proto.RegisterType((*ScrmDoctorSettingInfo)(nil), "dgc.ScrmDoctorSettingInfo")
	proto.RegisterType((*ScrmDoctor)(nil), "dgc.ScrmDoctor")
	proto.RegisterType((*DiagnoseDoctorSetting)(nil), "dgc.DiagnoseDoctorSetting")
	proto.RegisterType((*DiagnoseListRequest)(nil), "dgc.DiagnoseListRequest")
	proto.RegisterType((*DiagnoseListResponse)(nil), "dgc.DiagnoseListResponse")
	proto.RegisterType((*DiagnoseInfo)(nil), "dgc.DiagnoseInfo")
	proto.RegisterType((*DoctorListRequest)(nil), "dgc.DoctorListRequest")
	proto.RegisterType((*DoctorListResponse)(nil), "dgc.DoctorListResponse")
	proto.RegisterType((*DoctorListResponse_DoctorList)(nil), "dgc.DoctorListResponse.DoctorList")
	proto.RegisterType((*RegisterDoctorMiniProgramRes)(nil), "dgc.RegisterDoctorMiniProgramRes")
	proto.RegisterType((*OrderList)(nil), "dgc.OrderList")
	proto.RegisterType((*DoctorSettingSwitchRequest)(nil), "dgc.DoctorSettingSwitchRequest")
	proto.RegisterType((*DoctorSettingSwitchResponse)(nil), "dgc.DoctorSettingSwitchResponse")
	proto.RegisterType((*DoctorSettingRequest)(nil), "dgc.DoctorSettingRequest")
	proto.RegisterType((*DoctorSettingReponse)(nil), "dgc.DoctorSettingReponse")
	proto.RegisterType((*DoctorSettingInfoRequest)(nil), "dgc.DoctorSettingInfoRequest")
	proto.RegisterType((*DoctorSettingInfoResponse)(nil), "dgc.DoctorSettingInfoResponse")
	proto.RegisterType((*DiagnoseSetting)(nil), "dgc.DiagnoseSetting")
	proto.RegisterType((*GetDispatchDoctorReq)(nil), "dgc.GetDispatchDoctorReq")
	proto.RegisterType((*StatisticsType)(nil), "dgc.StatisticsType")
	proto.RegisterType((*DoctorInfoResponse)(nil), "dgc.DoctorInfoResponse")
	proto.RegisterType((*DoctorInfo)(nil), "dgc.DoctorInfo")
	proto.RegisterType((*Statistics)(nil), "dgc.Statistics")
	proto.RegisterType((*DiagnoseDetailsRequest)(nil), "dgc.DiagnoseDetailsRequest")
	proto.RegisterType((*DiagnoseDetailsResponse)(nil), "dgc.DiagnoseDetailsResponse")
	proto.RegisterType((*DiagnoseData)(nil), "dgc.DiagnoseData")
	proto.RegisterType((*NotifyGrabOrderRequest)(nil), "dgc.NotifyGrabOrderRequest")
}

func init() { proto.RegisterFile("dgc/doctor.proto", fileDescriptor_79000948969dabd4) }

var fileDescriptor_79000948969dabd4 = []byte{
	// 3416 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5b, 0x4b, 0x8f, 0x1b, 0xc7,
	0x11, 0xd6, 0xf2, 0xcd, 0xe2, 0x92, 0xdc, 0x9d, 0x7d, 0x91, 0x5c, 0x49, 0x2b, 0x8d, 0x22, 0x5b,
	0x4e, 0x1c, 0xc9, 0x90, 0x83, 0x18, 0x06, 0x1c, 0x1b, 0xb6, 0x56, 0x92, 0xd7, 0x91, 0x25, 0x81,
	0xbb, 0xb6, 0xe1, 0x1c, 0x3c, 0x18, 0x71, 0x9a, 0x54, 0x4b, 0x9c, 0x87, 0x67, 0x7a, 0xa5, 0xa5,
	0xaf, 0x39, 0xe4, 0x96, 0x1c, 0x73, 0x0b, 0x82, 0xe4, 0x94, 0x43, 0x4e, 0xc9, 0xcd, 0xa7, 0x5c,
	0xf3, 0x03, 0x82, 0x00, 0xf9, 0x0b, 0x39, 0xe6, 0x1a, 0x20, 0xe8, 0xd7, 0x4c, 0xf7, 0x3c, 0xc8,
	0x91, 0x1f, 0x01, 0x74, 0xe3, 0x54, 0x55, 0xf7, 0xf4, 0x74, 0x57, 0x7d, 0xf5, 0xe8, 0x22, 0x6c,
	0x38, 0xb3, 0xc9, 0x0d, 0xc7, 0x9f, 0x10, 0x3f, 0xbc, 0x1e, 0x84, 0x3e, 0xf1, 0x8d, 0xaa, 0x33,
	0x9b, 0x8c, 0xfa, 0x94, 0xec, 0x87, 0x0e, 0x12, 0x54, 0xf3, 0x00, 0x2e, 0xdc, 0x45, 0xe4, 0x90,
	0x09, 0x3e, 0xa4, 0x94, 0x89, 0x3f, 0x3f, 0x26, 0x36, 0x41, 0x63, 0xf4, 0xe5, 0x29, 0x8a, 0x88,
	0x79, 0x17, 0x2e, 0x16, 0x09, 0x44, 0x81, 0xef, 0x45, 0xc8, 0xb8, 0x0a, 0xbd, 0x40, 0x30, 0xac,
	0x88, 0x72, 0x06, 0x6b, 0x97, 0xd6, 0xae, 0xd5, 0xc7, 0xdd, 0x40, 0x15, 0xa7, 0x13, 0xdd, 0x76,
	0xf0, 0x92, 0x57, 0x95, 0x9d, 0xe8, 0x32, 0x1c, 0x14, 0x4e, 0xc4, 0x97, 0x64, 0xbe, 0x05, 0xe7,
	0xe9, 0xa2, 0x71, 0x14, 0xd8, 0x64, 0xf2, 0x98, 0x4b, 0x7e, 0xb0, 0x78, 0x10, 0x20, 0xef, 0xc8,
	0x19, 0xa3, 0x2f, 0x8d, 0x3d, 0x68, 0xfa, 0x01, 0xf2, 0x2c, 0xec, 0xb0, 0x57, 0xb4, 0xc7, 0x0d,
	0x9f, 0xf1, 0xcc, 0x3d, 0xd8, 0x51, 0xbe, 0x16, 0x4f, 0xe2, 0x6d, 0xf8, 0xdb, 0x1a, 0xec, 0xa6,
	0x39, 0xe2, 0xfb, 0xdf, 0x84, 0xdd, 0x2f, 0x4f, 0xf1, 0xe4, 0xa9, 0x85, 0x5d, 0x7b, 0x86, 0x2c,
	0x82, 0xce, 0x88, 0x15, 0x50, 0x09, 0xb1, 0xfc, 0x2d, 0xc6, 0x3d, 0xa2, 0xcc, 0x13, 0x74, 0x46,
	0xd8, 0x60, 0xe3, 0x2d, 0x18, 0x4c, 0x43, 0x84, 0xd4, 0x31, 0xce, 0x69, 0x68, 0x13, 0xec, 0x7b,
	0x83, 0x0a, 0x1b, 0xb6, 0x43, 0xf9, 0xf1, 0xa8, 0x43, 0xc1, 0x34, 0xde, 0x86, 0x61, 0xe6, 0x6d,
	0xf1, 0xc8, 0x2a, 0x1b, 0xb9, 0xab, 0xbf, 0x50, 0x0e, 0x35, 0xbf, 0x80, 0x01, 0x5f, 0xff, 0x51,
	0x74, 0xc7, 0x0f, 0x1f, 0x61, 0xc7, 0x41, 0x9e, 0xdc, 0xfb, 0x03, 0xe8, 0x70, 0x6d, 0xb1, 0x26,
	0xbe, 0x83, 0xc4, 0xae, 0x00, 0x27, 0xdd, 0xf2, 0x1d, 0x64, 0x5c, 0x86, 0x75, 0x1c, 0x59, 0x53,
	0x39, 0x4e, 0x2c, 0xb2, 0x83, 0x93, 0xa9, 0xcc, 0x2d, 0xd8, 0xe4, 0xf3, 0x9f, 0xe0, 0x20, 0x92,
	0x1b, 0xf7, 0x10, 0xb6, 0xe3, 0x7d, 0x3b, 0xf2, 0xa6, 0xbe, 0x7c, 0xe1, 0x1e, 0x34, 0x4f, 0x23,
	0x14, 0x2a, 0x47, 0x40, 0x1f, 0x8f, 0x9c, 0xf4, 0x4a, 0x2a, 0xe9, 0x95, 0x98, 0x7f, 0x68, 0x28,
	0x87, 0xc4, 0xa7, 0x14, 0x27, 0xb1, 0xf2, 0x23, 0x12, 0x01, 0xcf, 0x76, 0x53, 0x73, 0xdf, 0xb7,
	0x5d, 0xf6, 0x95, 0x42, 0x60, 0x8e, 0x9e, 0xa1, 0x39, 0xdb, 0xd0, 0xf6, 0x58, 0x0c, 0xba, 0x47,
	0x49, 0xc6, 0x8f, 0x60, 0x53, 0x88, 0x44, 0x01, 0x9a, 0x60, 0x7b, 0x8e, 0xc9, 0x62, 0x50, 0x63,
	0x72, 0x1b, 0x9c, 0x71, 0x1c, 0xd3, 0xa9, 0x4a, 0x0b, 0xe1, 0x20, 0x44, 0x11, 0xf2, 0xc8, 0xa0,
	0xce, 0x24, 0xbb, 0x8e, 0x50, 0x24, 0x46, 0x34, 0xae, 0x40, 0xf7, 0xb1, 0x1f, 0x05, 0x98, 0xd8,
	0x73, 0xbe, 0xf4, 0x06, 0x93, 0x5a, 0x97, 0x44, 0xb6, 0xf8, 0x0b, 0x20, 0x56, 0x6a, 0x61, 0x77,
	0x36, 0x68, 0x32, 0x89, 0x36, 0xa7, 0x1c, 0xb9, 0x33, 0x6d, 0x0e, 0xf6, 0x75, 0x2d, 0x7d, 0x0e,
	0xf6, 0x7d, 0x43, 0x68, 0x11, 0x7b, 0xc6, 0xf9, 0x6d, 0xc6, 0x6f, 0x12, 0x7b, 0xc6, 0x58, 0xd7,
	0x60, 0x23, 0xa3, 0xc0, 0xc0, 0x0e, 0xb9, 0x87, 0x75, 0xdd, 0x3d, 0x80, 0x4e, 0xf0, 0xd8, 0xf7,
	0x90, 0x10, 0xea, 0x30, 0x21, 0x60, 0xa4, 0x58, 0xe0, 0x19, 0x76, 0x90, 0x2f, 0x04, 0xd6, 0xb9,
	0x00, 0x23, 0x71, 0x81, 0x1f, 0xc2, 0xa6, 0xf2, 0x2e, 0x6a, 0xeb, 0xa7, 0xd1, 0xa0, 0xcb, 0xc4,
	0xfa, 0xf1, 0xcb, 0x8e, 0x19, 0xd9, 0x78, 0x1d, 0x0c, 0x65, 0x32, 0x29, 0xdc, 0x63, 0xc2, 0x1b,
	0xc9, 0x9c, 0x89, 0xb4, 0xb2, 0x36, 0x29, 0xdd, 0xe7, 0xd2, 0xc9, 0x12, 0x85, 0xf4, 0x75, 0xd8,
	0xca, 0x33, 0xa3, 0x0d, 0x26, 0xbe, 0x89, 0x33, 0xc6, 0x47, 0x11, 0x8a, 0xcd, 0x1e, 0x8b, 0x6e,
	0x0a, 0x84, 0xa2, 0x54, 0x55, 0x8c, 0x2f, 0x39, 0x16, 0x33, 0xb8, 0x18, 0xa3, 0xc6, 0x62, 0xaf,
	0x40, 0x7f, 0x6e, 0x13, 0x14, 0x11, 0x8b, 0x21, 0xb2, 0x15, 0x79, 0x83, 0x2d, 0xae, 0x1d, 0x9c,
	0xfc, 0x80, 0x52, 0x8f, 0x3d, 0xe3, 0x26, 0xec, 0x08, 0x39, 0x07, 0xdb, 0x33, 0xcf, 0x8f, 0x90,
	0x80, 0xc7, 0x6d, 0x8e, 0x2f, 0x9c, 0x79, 0x28, 0x78, 0x1c, 0x24, 0xff, 0xb9, 0x06, 0xfb, 0xc7,
	0x93, 0xd0, 0xe5, 0x56, 0x72, 0x0f, 0x47, 0xe4, 0xf6, 0x59, 0xe0, 0x87, 0x24, 0x6b, 0xef, 0x4c,
	0x17, 0xd6, 0x32, 0x96, 0x90, 0x51, 0xa7, 0x4a, 0x8e, 0x3a, 0x5d, 0x81, 0xae, 0xb4, 0x05, 0xbe,
	0xd1, 0x1c, 0x80, 0x84, 0x0d, 0x89, 0x4d, 0x7e, 0x0d, 0x36, 0xe2, 0x75, 0x07, 0xa1, 0xff, 0x04,
	0x4d, 0x08, 0xb3, 0x97, 0xfa, 0xb8, 0x2f, 0xe9, 0x0f, 0x39, 0x99, 0xcd, 0x27, 0x45, 0xa7, 0x7e,
	0xe8, 0x32, 0x6b, 0xa1, 0xf3, 0x09, 0xe2, 0x1d, 0x3f, 0x74, 0x4d, 0x04, 0x5b, 0x1f, 0x8a, 0x45,
	0xd0, 0xef, 0x92, 0x5f, 0x64, 0x40, 0x4d, 0xf9, 0x14, 0xf6, 0x9b, 0x9a, 0x4c, 0x40, 0x8f, 0x17,
	0x7b, 0x0e, 0x3a, 0x13, 0x90, 0xd5, 0xa6, 0x94, 0x23, 0x4a, 0x30, 0xf6, 0x81, 0x3d, 0x58, 0x11,
	0xfe, 0x0a, 0x89, 0xa5, 0xb7, 0x28, 0xe1, 0x18, 0x7f, 0x85, 0xcc, 0x63, 0xd8, 0xd6, 0x5f, 0x23,
	0x40, 0x66, 0x1b, 0xea, 0xc4, 0x27, 0xf6, 0x5c, 0xa0, 0x3b, 0x7f, 0x30, 0xae, 0x42, 0xcd, 0xb1,
	0x89, 0x3d, 0xa8, 0x5c, 0xaa, 0x5e, 0xeb, 0xdc, 0xdc, 0xbc, 0xee, 0xcc, 0x26, 0xd7, 0xe9, 0xfe,
	0xcb, 0x29, 0xc6, 0x8c, 0x6d, 0xfe, 0xb6, 0x0a, 0xeb, 0x2a, 0xd9, 0xe8, 0x41, 0x45, 0x20, 0x60,
	0x7d, 0x5c, 0xc1, 0x4e, 0x16, 0x09, 0x2a, 0x39, 0x48, 0x90, 0x39, 0x9b, 0x6a, 0xfe, 0xd9, 0xc4,
	0x42, 0x64, 0x11, 0x20, 0xb1, 0xe7, 0xb1, 0xd0, 0xc9, 0x22, 0x40, 0xc6, 0x1b, 0xb0, 0x1d, 0x0b,
	0x61, 0x8f, 0xa0, 0xd0, 0x43, 0x84, 0x42, 0x32, 0x47, 0x29, 0x43, 0xf2, 0x8e, 0x04, 0xeb, 0xc8,
	0xa1, 0x26, 0x13, 0x8f, 0x88, 0x1e, 0xfb, 0x21, 0xe1, 0x2b, 0xe0, 0x80, 0xb5, 0x29, 0x59, 0xc7,
	0x94, 0xc3, 0x96, 0x71, 0x43, 0x95, 0x4f, 0x00, 0xb3, 0xa9, 0xbf, 0x40, 0x81, 0xcc, 0xd7, 0x60,
	0x23, 0x1e, 0x20, 0x41, 0x93, 0x43, 0x59, 0x5f, 0xd2, 0x25, 0x6c, 0x5e, 0x86, 0xf5, 0x64, 0xf5,
	0xee, 0x4c, 0x20, 0x5a, 0x27, 0x5e, 0xb5, 0x3b, 0x33, 0x5e, 0x85, 0x78, 0x94, 0x65, 0x3f, 0xb3,
	0x89, 0x1d, 0x32, 0x50, 0x6b, 0x8f, 0x7b, 0x92, 0xfc, 0x3e, 0xa3, 0x9a, 0x3f, 0x86, 0xad, 0x43,
	0x45, 0x6b, 0xa5, 0x56, 0xed, 0x42, 0xc3, 0xf5, 0x1f, 0xe1, 0xb9, 0xd4, 0x2b, 0xf1, 0x64, 0xde,
	0x80, 0x6d, 0x5d, 0x5c, 0x68, 0x07, 0x8d, 0x2c, 0x3c, 0x6b, 0x8e, 0x3d, 0xe9, 0xfd, 0x1b, 0xbe,
	0x77, 0x0f, 0x7b, 0xc8, 0xfc, 0x04, 0x86, 0x7c, 0xc0, 0x03, 0x8f, 0x72, 0x6f, 0x3d, 0xb6, 0xbd,
	0x19, 0x2a, 0xed, 0x7d, 0x95, 0x69, 0x2b, 0xda, 0xb4, 0x7f, 0x59, 0x83, 0xf3, 0x63, 0x34, 0xc3,
	0x11, 0x41, 0x21, 0x9f, 0xff, 0x63, 0xec, 0xe1, 0x87, 0xa1, 0x3f, 0x0b, 0x6d, 0x97, 0x86, 0x3a,
	0x65, 0xa6, 0x7e, 0x12, 0xa9, 0xba, 0xd6, 0x78, 0x12, 0x49, 0x2d, 0x13, 0x23, 0xc5, 0x0e, 0x08,
	0x2d, 0xe3, 0xc4, 0x8f, 0x19, 0x4d, 0x8d, 0xa4, 0x6a, 0x6a, 0x24, 0x45, 0x6d, 0x6b, 0xe2, 0xbb,
	0xc8, 0x9a, 0x86, 0xbe, 0x2b, 0xd4, 0xa9, 0x45, 0x09, 0x77, 0x42, 0xdf, 0x35, 0x7f, 0x06, 0xa3,
	0x7b, 0xfe, 0xcc, 0x3f, 0x25, 0xdf, 0x68, 0xc9, 0x66, 0x0f, 0xd6, 0x6f, 0xbb, 0x01, 0x59, 0xc8,
	0x18, 0xe3, 0x0c, 0x86, 0x52, 0x43, 0x13, 0xbc, 0x2b, 0x8d, 0x74, 0xdf, 0x06, 0x24, 0x66, 0x30,
	0xca, 0x7b, 0xf3, 0x52, 0xa8, 0x78, 0x53, 0x83, 0x8a, 0x03, 0x06, 0x15, 0xfa, 0x24, 0x6a, 0x50,
	0x23, 0x80, 0xc3, 0x81, 0x8b, 0xba, 0xcc, 0xf7, 0x12, 0xc1, 0xbd, 0x93, 0xde, 0x48, 0x35, 0x62,
	0x5b, 0x79, 0x2c, 0xff, 0x59, 0x4b, 0xef, 0x46, 0x41, 0x74, 0x56, 0x70, 0x10, 0x89, 0xad, 0x55,
	0x54, 0x5b, 0x53, 0x95, 0xbf, 0xaa, 0x2a, 0x7f, 0xe6, 0x8b, 0x6a, 0x99, 0x2f, 0xa2, 0x2f, 0x9d,
	0x84, 0xc8, 0x26, 0xc8, 0x22, 0xd8, 0x45, 0x42, 0x11, 0x81, 0x93, 0x4e, 0xb0, 0xcb, 0x56, 0x75,
	0x1a, 0x38, 0xb1, 0x00, 0xc7, 0x31, 0xe0, 0x24, 0x29, 0xa0, 0x7e, 0x76, 0x33, 0xf3, 0xd9, 0x77,
	0x60, 0xa0, 0x7f, 0xf5, 0xfb, 0x8e, 0xb3, 0xcc, 0x29, 0x15, 0x7c, 0xa6, 0xf9, 0xfb, 0x0a, 0x8c,
	0x62, 0x27, 0xbe, 0x88, 0x08, 0x72, 0xb5, 0xed, 0x4b, 0x7b, 0x8a, 0x8b, 0xd0, 0x79, 0xee, 0x87,
	0x4f, 0x2d, 0xdf, 0xb3, 0xfc, 0xe9, 0x54, 0xea, 0x2d, 0x25, 0x3d, 0xf0, 0x1e, 0x4c, 0xa7, 0x4b,
	0x33, 0x8c, 0xea, 0x37, 0xce, 0x30, 0x6a, 0xcb, 0x32, 0x0c, 0xea, 0x1c, 0xa6, 0xd8, 0x73, 0xac,
	0x54, 0x90, 0xc4, 0xbd, 0xf8, 0x26, 0x65, 0x3d, 0xd4, 0x02, 0x25, 0x29, 0x9f, 0x8a, 0x96, 0x1a,
	0x89, 0xfc, 0xa7, 0x6a, 0xc4, 0x64, 0xfe, 0x04, 0x8c, 0x78, 0x87, 0x50, 0x6c, 0xe1, 0xa9, 0x9d,
	0x58, 0x4b, 0xed, 0x84, 0xf9, 0xeb, 0x0a, 0xec, 0xe8, 0xb1, 0xd0, 0xcb, 0x1f, 0x05, 0xa5, 0x50,
	0xab, 0xb1, 0x14, 0xb5, 0x9a, 0x29, 0xd4, 0xfa, 0x02, 0x76, 0xd3, 0xfb, 0xb1, 0x14, 0xb1, 0xae,
	0x6b, 0x88, 0x35, 0x8a, 0x83, 0x1b, 0xe1, 0x01, 0x11, 0x21, 0xd8, 0x9b, 0x31, 0x65, 0xe5, 0x60,
	0xf5, 0x75, 0x53, 0xdd, 0x70, 0x85, 0xbf, 0x1a, 0xa4, 0x92, 0x24, 0xc7, 0x3b, 0x75, 0xc5, 0x6e,
	0x8b, 0x24, 0xe7, 0xfe, 0xa9, 0x9b, 0x3e, 0xb0, 0xea, 0xca, 0x04, 0xae, 0x56, 0x32, 0x81, 0xab,
	0x97, 0x4e, 0xe0, 0x1a, 0x79, 0x09, 0xdc, 0x01, 0x74, 0x92, 0x48, 0xc4, 0x91, 0x20, 0x11, 0x07,
	0x22, 0x8e, 0xb2, 0x2e, 0x66, 0x1b, 0x22, 0xa2, 0x11, 0xeb, 0x62, 0x46, 0x91, 0xca, 0xef, 0xda,
	0xe9, 0xfc, 0x2e, 0x61, 0x47, 0xe8, 0x4c, 0x64, 0x66, 0x82, 0x7d, 0x8c, 0xce, 0xb2, 0x9a, 0xda,
	0xc9, 0xd1, 0xd4, 0x14, 0xd8, 0xad, 0x67, 0xc0, 0x6e, 0x08, 0x2d, 0x14, 0x04, 0xfc, 0x70, 0xba,
	0x3c, 0x3f, 0x44, 0x41, 0xc0, 0x4e, 0x66, 0x17, 0x1a, 0x21, 0x9a, 0x51, 0xf3, 0xec, 0x71, 0xd8,
	0xe2, 0x4f, 0x7a, 0xc4, 0x4a, 0xb7, 0xb2, 0x9f, 0x8a, 0x58, 0xe9, 0x36, 0x5e, 0x81, 0x2e, 0x0b,
	0x13, 0xa4, 0x0a, 0x8b, 0x14, 0x6b, 0x9d, 0x12, 0xa5, 0x45, 0xd3, 0x3c, 0x47, 0x13, 0xb2, 0x22,
	0x14, 0x3e, 0xa3, 0x09, 0x24, 0x4f, 0xb2, 0xb6, 0x54, 0xe1, 0x63, 0xce, 0x62, 0xe7, 0xa3, 0xda,
	0x4a, 0x24, 0x53, 0x2d, 0xd5, 0x58, 0xa2, 0x8c, 0xa7, 0xd8, 0xca, 0x7a, 0x8a, 0xab, 0xd0, 0xb3,
	0x27, 0x13, 0x14, 0xd0, 0xdc, 0x17, 0x21, 0x37, 0x20, 0x22, 0xbd, 0xea, 0x72, 0xea, 0x43, 0x4e,
	0xa4, 0x5b, 0x28, 0xc4, 0x22, 0xe4, 0x39, 0x83, 0x1d, 0x9e, 0xdb, 0x72, 0xd2, 0x31, 0xf2, 0x32,
	0xb8, 0xbc, 0x9b, 0xc6, 0xe5, 0xe2, 0x72, 0xd1, 0x5e, 0x71, 0xb9, 0x28, 0x2f, 0x39, 0x1f, 0x94,
	0x49, 0xce, 0x87, 0xab, 0x92, 0xf3, 0x51, 0x3a, 0x39, 0x37, 0xbf, 0xae, 0x02, 0x24, 0xd6, 0x9b,
	0xf1, 0x3b, 0xab, 0xea, 0x33, 0x2f, 0x62, 0xa3, 0xdc, 0x16, 0x6a, 0x59, 0x5b, 0x48, 0x9b, 0x71,
	0x3d, 0x6b, 0xc6, 0x19, 0x85, 0x6f, 0xac, 0x56, 0xf8, 0xe6, 0x52, 0x85, 0x6f, 0x15, 0x29, 0x7c,
	0x7b, 0xb9, 0xc2, 0x43, 0xbe, 0xc2, 0xeb, 0x3e, 0xa1, 0x93, 0xe3, 0x13, 0xc6, 0xb0, 0x17, 0x2b,
	0x6f, 0x6c, 0xdb, 0x0c, 0x2c, 0x99, 0x69, 0x4a, 0xa8, 0x95, 0x3a, 0xaf, 0xc1, 0xe9, 0x78, 0xc7,
	0xc9, 0x23, 0x9b, 0xff, 0x5a, 0x83, 0x9d, 0xdc, 0x01, 0xb9, 0x3a, 0xb4, 0x56, 0x46, 0x87, 0x2a,
	0xab, 0x74, 0xa8, 0x9a, 0x29, 0xf0, 0x14, 0x2b, 0x79, 0xad, 0x58, 0xc9, 0xd3, 0x46, 0x5a, 0xcf,
	0x06, 0xa8, 0x7f, 0xae, 0xc0, 0x96, 0xfc, 0x3a, 0xd5, 0x91, 0x5f, 0x00, 0x88, 0x88, 0x1d, 0x12,
	0xcb, 0x91, 0x65, 0xe3, 0xf6, 0xb8, 0xcd, 0x28, 0x87, 0x36, 0xe1, 0xa7, 0xec, 0x39, 0x9c, 0x59,
	0x11, 0xa7, 0xec, 0x39, 0x92, 0x15, 0x57, 0x5f, 0xb8, 0xaa, 0x36, 0x7d, 0x51, 0x77, 0x49, 0x29,
	0x72, 0x2d, 0xa3, 0xc8, 0x79, 0x3e, 0xbd, 0x9e, 0xef, 0xd3, 0xb7, 0xa1, 0xce, 0x6b, 0x36, 0xdc,
	0x53, 0xf3, 0x87, 0xac, 0xa7, 0x6f, 0xae, 0xf4, 0xf4, 0xad, 0xa5, 0x9e, 0xbe, 0x9d, 0x2d, 0x62,
	0xe8, 0xdb, 0xf5, 0xc2, 0x45, 0x0c, 0x39, 0x5c, 0x71, 0xef, 0x7f, 0xad, 0xc1, 0xba, 0x4a, 0xce,
	0x40, 0x84, 0xba, 0xa7, 0x95, 0xcc, 0x9e, 0xaa, 0xf1, 0x78, 0x35, 0x2f, 0x1e, 0x5f, 0xbe, 0xe9,
	0x69, 0xf4, 0xa8, 0xaf, 0x46, 0x8f, 0x46, 0x09, 0xf4, 0x68, 0xe6, 0xa0, 0xc7, 0x3e, 0xb4, 0x59,
	0x8d, 0x5a, 0x29, 0xa7, 0xb6, 0x28, 0x41, 0xa6, 0x8d, 0x8c, 0xc9, 0x57, 0x21, 0xdc, 0x35, 0xa5,
	0xf0, 0x35, 0xe4, 0xe9, 0x06, 0x94, 0x8c, 0xf7, 0x3a, 0x39, 0x5a, 0x90, 0xec, 0xcb, 0x13, 0x1f,
	0x7b, 0xd2, 0x75, 0x73, 0xd2, 0x47, 0x3e, 0xf6, 0x12, 0x0d, 0xeb, 0xaa, 0x1a, 0x26, 0x3f, 0x81,
	0x01, 0x55, 0x2f, 0xf9, 0x04, 0x06, 0x52, 0x4c, 0xb3, 0x16, 0x96, 0xed, 0xfa, 0xa7, 0x1e, 0x11,
	0x45, 0xd2, 0x76, 0x60, 0x2f, 0xde, 0x67, 0x04, 0x0a, 0x18, 0x53, 0x1c, 0x46, 0xc4, 0x0a, 0x51,
	0x30, 0x5f, 0xf0, 0x03, 0xdb, 0xe0, 0xc5, 0x13, 0x46, 0x1f, 0x53, 0x32, 0x3b, 0xb4, 0x2b, 0xd0,
	0x65, 0xd5, 0xa3, 0x67, 0xf6, 0x9c, 0x8b, 0x6d, 0xf2, 0xdd, 0x94, 0x44, 0x2a, 0x64, 0xfe, 0xbd,
	0x22, 0xef, 0x07, 0x52, 0x96, 0xab, 0x68, 0xf7, 0xda, 0x52, 0xed, 0xae, 0xe8, 0xda, 0x9d, 0x45,
	0xe2, 0x6a, 0x0e, 0x12, 0x7f, 0xd7, 0x11, 0xe1, 0x3e, 0xb4, 0x5d, 0xec, 0x09, 0x34, 0xe3, 0xd6,
	0xdc, 0x72, 0xb1, 0xc7, 0x21, 0x8c, 0x32, 0xed, 0x33, 0xc1, 0x14, 0x61, 0xb7, 0x6b, 0x9f, 0x71,
	0xe6, 0x79, 0x68, 0xcf, 0x7d, 0x6f, 0x86, 0xc9, 0x69, 0xec, 0x6c, 0x12, 0x82, 0x31, 0x82, 0xd6,
	0xdc, 0x26, 0x9c, 0xc9, 0xb5, 0x29, 0x7e, 0x36, 0x36, 0xa0, 0xfa, 0x14, 0x49, 0x47, 0x43, 0x7f,
	0x9a, 0xbf, 0xac, 0x81, 0x51, 0x3a, 0x7e, 0xff, 0x29, 0xd4, 0xe6, 0x38, 0xa2, 0xa1, 0x2b, 0xb5,
	0x6b, 0x93, 0xdb, 0x75, 0x66, 0xb0, 0x4a, 0x62, 0xf2, 0xa3, 0xdf, 0x55, 0x01, 0x12, 0xe2, 0xff,
	0xe7, 0x7a, 0x45, 0xbd, 0xa1, 0xa8, 0xe9, 0x37, 0x14, 0x7a, 0x80, 0x5c, 0x4f, 0x07, 0xc8, 0xc5,
	0x3e, 0xa7, 0xf1, 0x62, 0x81, 0x55, 0xb3, 0x8c, 0x53, 0x6c, 0xad, 0x72, 0x8a, 0xed, 0x8c, 0x53,
	0xcc, 0x60, 0x0e, 0xe4, 0x60, 0x4e, 0xae, 0x2e, 0x76, 0xf2, 0x75, 0x71, 0x55, 0xf5, 0x2f, 0xfa,
	0x0e, 0x8e, 0xac, 0x54, 0x15, 0xf0, 0x75, 0x30, 0x70, 0x94, 0x14, 0x90, 0x39, 0x8f, 0x1d, 0x5f,
	0x6b, 0xbc, 0x81, 0x23, 0xbd, 0x3c, 0x62, 0xfe, 0xb7, 0x02, 0x6d, 0x76, 0xb7, 0x51, 0x4e, 0xab,
	0x2e, 0xc1, 0x7a, 0x34, 0x09, 0x5d, 0x4b, 0x5e, 0x17, 0x8a, 0x35, 0x52, 0xda, 0x27, 0xfc, 0xca,
	0x50, 0xc3, 0xe9, 0x6a, 0x0a, 0xa7, 0x69, 0x08, 0x48, 0x99, 0xa2, 0xfa, 0x2b, 0x1c, 0x0a, 0x25,
	0xf1, 0xca, 0xef, 0x8b, 0x78, 0xf1, 0x0c, 0x52, 0x37, 0x72, 0x90, 0x7a, 0x00, 0xcd, 0x68, 0xe1,
	0x06, 0xc4, 0x77, 0x85, 0x53, 0x91, 0x8f, 0x54, 0xfd, 0xc5, 0x4f, 0xcb, 0x41, 0xd1, 0x44, 0x26,
	0x81, 0x82, 0x76, 0x88, 0xa2, 0x89, 0xe6, 0x3a, 0xdb, 0xba, 0xeb, 0x64, 0xd7, 0x45, 0x3a, 0x1a,
	0x83, 0xbc, 0x2e, 0x52, 0xc1, 0x38, 0xe5, 0x62, 0x3b, 0x69, 0x17, 0x6b, 0xbe, 0x07, 0x23, 0x2d,
	0x32, 0x3c, 0x7e, 0x8e, 0xc9, 0xe4, 0xb1, 0x04, 0xe4, 0xcb, 0x40, 0xb3, 0xb2, 0x90, 0x8d, 0x5f,
	0x04, 0x32, 0x44, 0xec, 0x08, 0xda, 0xc9, 0x22, 0x40, 0xe6, 0xbb, 0xb0, 0x9f, 0x3b, 0x41, 0x52,
	0xe8, 0xb3, 0xa7, 0x53, 0x34, 0x21, 0x56, 0xe8, 0x3f, 0x8f, 0xd8, 0x04, 0xd5, 0x31, 0x70, 0xd2,
	0xd8, 0x7f, 0x1e, 0x99, 0xbf, 0xa9, 0xc6, 0xd5, 0x73, 0x11, 0xcc, 0x26, 0x1d, 0x00, 0xa9, 0x6c,
	0x6e, 0x4d, 0x64, 0xdb, 0x4b, 0xb3, 0xb9, 0x4a, 0x99, 0x6c, 0xae, 0x5a, 0x22, 0x9b, 0xab, 0xad,
	0xca, 0xe6, 0xea, 0xe9, 0x6c, 0x2e, 0x0e, 0x3c, 0x95, 0xea, 0x21, 0x0f, 0x3c, 0xe3, 0xf4, 0xc2,
	0x73, 0xd4, 0xe4, 0x83, 0x06, 0x9e, 0x8c, 0x95, 0x87, 0x3c, 0xad, 0x32, 0xc8, 0xd3, 0x5e, 0x85,
	0x3c, 0x90, 0x41, 0x9e, 0x94, 0x8d, 0x75, 0x32, 0x35, 0xcc, 0xb7, 0x32, 0x07, 0x52, 0xf2, 0x28,
	0x47, 0xb2, 0xa7, 0x40, 0xad, 0x02, 0x89, 0xb2, 0xfc, 0xe7, 0xf2, 0xca, 0x43, 0xe3, 0x09, 0x25,
	0xb9, 0x06, 0x35, 0xec, 0x4d, 0x7d, 0x36, 0x65, 0xe7, 0xe6, 0xb6, 0x16, 0x6b, 0xca, 0x45, 0x30,
	0x09, 0xc3, 0x80, 0x9a, 0xe3, 0x8b, 0x8b, 0x8f, 0xd6, 0x98, 0xfd, 0x36, 0xff, 0x51, 0x83, 0x7e,
	0x4a, 0xfa, 0xdb, 0x24, 0xaa, 0x4c, 0xd1, 0x45, 0x16, 0xc3, 0x49, 0xec, 0x76, 0x2c, 0x53, 0xb5,
	0xa8, 0xbd, 0x48, 0xd5, 0xa2, 0xfe, 0x22, 0x55, 0x8b, 0x46, 0x19, 0x3d, 0x6f, 0x96, 0xd1, 0xf3,
	0x56, 0x09, 0x3d, 0x6f, 0xaf, 0xd2, 0x73, 0x58, 0xae, 0xe7, 0x9d, 0x65, 0x7a, 0xbe, 0xbe, 0x5a,
	0xcf, 0xbb, 0x65, 0xf4, 0xbc, 0xb7, 0x4a, 0xcf, 0xfb, 0x79, 0x7a, 0xae, 0x42, 0xdf, 0xc6, 0xaa,
	0x6a, 0xff, 0x66, 0xba, 0x1e, 0x40, 0x0d, 0x21, 0xd3, 0x39, 0xa4, 0xdf, 0x49, 0x29, 0xa0, 0xa8,
	0xe8, 0x8a, 0xf9, 0x36, 0xf4, 0x68, 0x56, 0x8f, 0x23, 0x82, 0x27, 0x11, 0xd3, 0x9e, 0x57, 0xa1,
	0x1f, 0xc5, 0x94, 0x64, 0x58, 0x7b, 0xdc, 0x8b, 0x34, 0x41, 0xf3, 0xb9, 0x0c, 0xe5, 0x34, 0x03,
	0x79, 0x23, 0x7e, 0xa3, 0x62, 0x27, 0x7d, 0x25, 0x76, 0x63, 0xd2, 0x62, 0x09, 0x2c, 0x0d, 0xbb,
	0xc1, 0xce, 0x48, 0xcc, 0xcc, 0xf4, 0x5d, 0x0e, 0x48, 0x56, 0x36, 0x56, 0x44, 0xcc, 0x5f, 0xd5,
	0x64, 0x7c, 0x57, 0xae, 0x38, 0x9b, 0xda, 0x84, 0x4a, 0xc6, 0x60, 0xf4, 0xea, 0x6d, 0x75, 0x45,
	0xf5, 0xb6, 0xb6, 0x32, 0x3e, 0xac, 0x97, 0x8c, 0xd5, 0x1b, 0xa5, 0xab, 0xb7, 0xcd, 0x52, 0xed,
	0x37, 0xad, 0x9c, 0x4b, 0xf7, 0x74, 0xde, 0xd9, 0x5e, 0x55, 0xc1, 0x85, 0x95, 0x1d, 0x3a, 0x9d,
	0x15, 0x1d, 0x3a, 0xeb, 0x99, 0xf8, 0x57, 0xb1, 0xc1, 0x6e, 0xda, 0x06, 0xd3, 0x68, 0xd1, 0xcb,
	0xa2, 0x85, 0x72, 0x93, 0xd6, 0xd7, 0xae, 0x91, 0xff, 0xbd, 0x06, 0x90, 0x28, 0x09, 0xb5, 0x59,
	0x1e, 0x85, 0xcc, 0xd1, 0x34, 0x55, 0x2a, 0x62, 0xf4, 0x7b, 0x68, 0x2a, 0x6c, 0xf6, 0x15, 0xe8,
	0xb3, 0x1c, 0x43, 0xb4, 0xb0, 0x4c, 0x3c, 0x22, 0xd4, 0xa2, 0xcb, 0xc8, 0x2c, 0xcc, 0xbb, 0xe5,
	0xb1, 0x34, 0xcf, 0x5d, 0x58, 0xa1, 0xed, 0x3d, 0xc5, 0xde, 0x4c, 0x40, 0x6d, 0xdb, 0x5d, 0x8c,
	0x39, 0x81, 0x25, 0xaf, 0x13, 0x3f, 0xe4, 0x3a, 0x51, 0x19, 0xf3, 0x07, 0x06, 0x08, 0x1c, 0xc0,
	0xd8, 0xc4, 0x75, 0xee, 0x7d, 0x04, 0x89, 0xce, 0x3a, 0x84, 0x16, 0xc5, 0x33, 0xc6, 0x6d, 0x30,
	0x6e, 0x93, 0x3e, 0x53, 0xd6, 0x3e, 0xb4, 0x89, 0xef, 0xd8, 0x0b, 0xc6, 0x6b, 0x32, 0x5e, 0x8b,
	0x11, 0x6e, 0x79, 0xc4, 0xbc, 0x06, 0xbb, 0x71, 0x8d, 0x0c, 0x11, 0x1b, 0xcf, 0xe3, 0xfb, 0xfe,
	0x94, 0x13, 0x31, 0x3f, 0x83, 0xbd, 0x8c, 0xa4, 0x30, 0xd0, 0x77, 0x94, 0xb8, 0xd1, 0xe1, 0x3c,
	0x61, 0xa5, 0x7a, 0xe5, 0xe4, 0xd0, 0x26, 0x76, 0x12, 0x4a, 0x8a, 0x59, 0xcc, 0x3f, 0x2a, 0x45,
	0x14, 0x2a, 0xf1, 0x52, 0x14, 0x51, 0xea, 0x2f, 0x79, 0x11, 0xe5, 0x2a, 0xf4, 0x78, 0xf4, 0x2c,
	0x0b, 0x13, 0xa2, 0x37, 0xad, 0xcb, 0xa8, 0x47, 0x82, 0xf8, 0x3d, 0x94, 0x52, 0xf6, 0xa1, 0x8d,
	0x23, 0x2b, 0x44, 0xd3, 0x53, 0xcf, 0x11, 0x77, 0x1f, 0x2d, 0x1c, 0x8d, 0xd9, 0x33, 0x3d, 0x69,
	0x3a, 0x56, 0xf1, 0x48, 0xcd, 0xc0, 0x66, 0xb1, 0xbc, 0x49, 0x60, 0xf7, 0xbe, 0x4f, 0xf0, 0x74,
	0x71, 0x37, 0xb4, 0x1f, 0x31, 0x63, 0x92, 0x8a, 0xba, 0x01, 0xd5, 0xb9, 0x37, 0x13, 0x20, 0x4d,
	0x7f, 0x32, 0x8a, 0x4d, 0x84, 0xae, 0xd0, 0x9f, 0xcb, 0x6a, 0x9b, 0xfb, 0xd0, 0x76, 0x91, 0xfb,
	0x88, 0x67, 0x54, 0x5c, 0x3f, 0x5a, 0x9c, 0x70, 0xe4, 0xdc, 0xfc, 0x93, 0x01, 0x5d, 0x19, 0xb9,
	0xf1, 0x80, 0xe5, 0x3d, 0xad, 0x10, 0xb0, 0x9b, 0xa9, 0x20, 0xb0, 0x35, 0x8d, 0xf6, 0x0a, 0x2a,
	0x0b, 0xe6, 0x39, 0xe3, 0x1d, 0xcd, 0xd3, 0x6c, 0xa5, 0xbc, 0x12, 0x75, 0x1e, 0xda, 0x68, 0xd5,
	0x13, 0xaa, 0xa3, 0x4f, 0x70, 0x10, 0x69, 0xaf, 0x57, 0x5a, 0x4d, 0x47, 0x3c, 0x88, 0xfc, 0xc0,
	0x8e, 0xe2, 0xde, 0xdc, 0xfb, 0xe8, 0xb9, 0x79, 0xce, 0xf8, 0x10, 0xba, 0x5a, 0xbf, 0xa8, 0x31,
	0x64, 0x82, 0x79, 0x6d, 0xa9, 0xa3, 0x51, 0x1e, 0x2b, 0x5e, 0xc7, 0xcf, 0xa1, 0xa7, 0x37, 0x01,
	0x1b, 0x29, 0x79, 0xb5, 0x67, 0x78, 0xb4, 0x9f, 0xcb, 0x8b, 0x27, 0xbb, 0x9b, 0x6c, 0x32, 0x0f,
	0x60, 0x87, 0xca, 0x77, 0xe9, 0x89, 0xd1, 0x28, 0x97, 0x25, 0x27, 0x3a, 0x91, 0x75, 0x35, 0xf5,
	0xa6, 0xf5, 0x42, 0x76, 0x84, 0xfa, 0x9d, 0x17, 0x8b, 0xd8, 0xf1, 0xf2, 0x7e, 0x11, 0x37, 0x44,
	0xa9, 0x49, 0x9e, 0x71, 0x90, 0x1d, 0xa8, 0xe5, 0x8f, 0xa3, 0x4b, 0xc5, 0x02, 0xf1, 0xdc, 0x47,
	0xb0, 0x99, 0x89, 0xb2, 0x94, 0x53, 0x49, 0x47, 0x5f, 0xcb, 0x54, 0xe3, 0x73, 0x18, 0x16, 0xb6,
	0x7a, 0x1b, 0x97, 0xf3, 0xa7, 0x54, 0x5a, 0xc1, 0x97, 0x4d, 0x3d, 0x81, 0x61, 0x61, 0x71, 0x45,
	0x4c, 0xbd, 0xac, 0xf5, 0x6a, 0xb4, 0x52, 0x24, 0x32, 0xcf, 0x19, 0x63, 0xd8, 0x2b, 0x68, 0x85,
	0x12, 0x5b, 0x5d, 0xdc, 0x28, 0x55, 0xa8, 0xf0, 0xf7, 0x93, 0xe4, 0x48, 0xb8, 0x1b, 0x63, 0x5f,
	0x77, 0x49, 0x9a, 0xd3, 0x1b, 0x9d, 0xcf, 0x67, 0xc6, 0x1b, 0x71, 0x3b, 0x71, 0x55, 0xcc, 0xfe,
	0x07, 0x9a, 0xbc, 0x8a, 0x00, 0xc3, 0x1c, 0x8e, 0x6a, 0x3d, 0x7a, 0xdb, 0x81, 0x91, 0x6e, 0x25,
	0x50, 0xa7, 0xda, 0xcf, 0xe5, 0xc5, 0x93, 0xbd, 0x0b, 0x1d, 0x25, 0x01, 0x34, 0xf6, 0xd2, 0x09,
	0xe4, 0x2a, 0x50, 0xf8, 0x48, 0x69, 0x25, 0x89, 0x9b, 0x6d, 0x0c, 0xee, 0xb9, 0xd5, 0xe6, 0xb2,
	0xd1, 0x81, 0x3e, 0x73, 0xa6, 0x31, 0x87, 0x9d, 0xe1, 0x30, 0xb9, 0x0f, 0x49, 0x75, 0x02, 0x09,
	0x43, 0x2c, 0xea, 0x10, 0x2a, 0x5c, 0xdf, 0xa7, 0xb0, 0x57, 0xd0, 0xf0, 0x65, 0x5c, 0xc9, 0x99,
	0x31, 0xdd, 0x0e, 0x56, 0x38, 0xef, 0x67, 0x60, 0x64, 0x7b, 0xb4, 0x8c, 0x8b, 0x85, 0x5d, 0x68,
	0xea, 0x26, 0x14, 0x37, 0x77, 0x99, 0xe7, 0x0c, 0x2b, 0xe9, 0x5e, 0xca, 0xb6, 0xc4, 0xe5, 0xbe,
	0x40, 0x3d, 0xed, 0x83, 0x42, 0x7e, 0xfc, 0x82, 0x7b, 0x32, 0x4d, 0x52, 0x3b, 0x28, 0x0d, 0x15,
	0xc8, 0x72, 0x5a, 0x2b, 0x0b, 0xf7, 0x81, 0xea, 0xb4, 0x7a, 0x17, 0x3b, 0x50, 0x61, 0x4b, 0x6d,
	0x01, 0xd5, 0xb1, 0x57, 0xeb, 0xf6, 0xe4, 0xd3, 0xa8, 0x5d, 0xc2, 0x62, 0x9a, 0x9c, 0xfe, 0x64,
	0x31, 0x4d, 0x5e, 0x4b, 0x31, 0x3b, 0x95, 0xed, 0xbc, 0x6e, 0x6d, 0xe3, 0x52, 0x8e, 0x11, 0x68,
	0x8d, 0xdc, 0xab, 0xcc, 0xe4, 0x0e, 0xf4, 0x53, 0x01, 0x84, 0x80, 0x82, 0xfc, 0xb0, 0x62, 0x89,
	0xb9, 0x6c, 0x66, 0xfe, 0x3b, 0xa2, 0xf9, 0x98, 0xec, 0x7f, 0x4a, 0x0a, 0xe7, 0x9a, 0x68, 0x7f,
	0xa5, 0x51, 0xfe, 0xbf, 0x63, 0x98, 0x69, 0x8f, 0x99, 0xfd, 0x97, 0xd0, 0xe8, 0xca, 0x52, 0x99,
	0xf8, 0xc3, 0xa7, 0xb0, 0x57, 0xf0, 0x2f, 0x21, 0x61, 0x3f, 0xcb, 0xff, 0x8c, 0x34, 0xfa, 0xc1,
	0x72, 0x21, 0xf9, 0x9e, 0x47, 0x0d, 0xf6, 0xe7, 0xa4, 0x37, 0xff, 0x17, 0x00, 0x00, 0xff, 0xff,
	0xb0, 0x40, 0xa7, 0xf1, 0x71, 0x35, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DoctorServiceClient is the client API for DoctorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DoctorServiceClient interface {
	// 获取医生列表
	DoctorList(ctx context.Context, in *DoctorListRequest, opts ...grpc.CallOption) (*DoctorListResponse, error)
	// 获取医生信息
	DoctorInfo(ctx context.Context, in *StatisticsType, opts ...grpc.CallOption) (*DoctorInfoResponse, error)
	// 问诊列表提示语（提示内容：已在门店排班不支持接单，待空闲时将继续为你推送问诊订单）
	DoctorTips(ctx context.Context, in *DoctorTipsRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 用户端获取医生信息
	GetDoctorInfo(ctx context.Context, in *GetDoctorInfoRequest, opts ...grpc.CallOption) (*GetDoctorInfoResponse, error)
	// 用户端获取问诊价格
	GetDoctorPrice(ctx context.Context, in *GetDoctorPriceRequest, opts ...grpc.CallOption) (*GetDoctorPriceResponse, error)
	// 在线问诊 医生接单设置信息配置
	DoctorSetting(ctx context.Context, in *DoctorSettingRequest, opts ...grpc.CallOption) (*DoctorSettingReponse, error)
	// 获取医生 接单设置信息
	DoctorSettingInfo(ctx context.Context, in *DoctorSettingInfoRequest, opts ...grpc.CallOption) (*DoctorSettingInfoResponse, error)
	// 开启接单、关闭接单
	DoctorSettingSwitch(ctx context.Context, in *DoctorSettingSwitchRequest, opts ...grpc.CallOption) (*DoctorSettingSwitchResponse, error)
	// 获取可派单的互联网医生信息一个
	GetDispatchDoctor(ctx context.Context, in *GetDispatchDoctorReq, opts ...grpc.CallOption) (*DoctorInfoResponse, error)
	// 根据医生openid获取医生信息
	GetDispatchDoctorByOpenId(ctx context.Context, in *GetDispatchDoctorByOpenIdReq, opts ...grpc.CallOption) (*DoctorInfoResponse, error)
	// 注册医生端小程序
	RegisterDoctorMiniProgram(ctx context.Context, in *RegisterDoctorMiniProgramReq, opts ...grpc.CallOption) (*RegisterDoctorMiniProgramRes, error)
	// 注销医生端小程序
	LogoutDoctorMiniProgram(ctx context.Context, in *LogoutDoctorMiniProgramReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//获取问诊详情
	DiagnoseDetails(ctx context.Context, in *DiagnoseDetailsRequest, opts ...grpc.CallOption) (*DiagnoseDetailsResponse, error)
	//获取问诊列表
	DiagnoseList(ctx context.Context, in *DiagnoseListRequest, opts ...grpc.CallOption) (*DiagnoseListResponse, error)
	//获取SCRM医生列表
	ScrmDoctorList(ctx context.Context, in *ScrmDoctorListRequest, opts ...grpc.CallOption) (*ScrmDoctorListResponse, error)
	//医生接单设置
	DiagnoseSet(ctx context.Context, in *DiagnoseSetRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//获取医生接单设置
	DiagnoseSystemInfo(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*DiagnoseSystemInfoResponse, error)
	//新增互联网医生
	DiagnoseInternetDoctorAdd(ctx context.Context, in *InternetDoctorAddRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//启用禁用互联网医生
	InternetDoctorForbidden(ctx context.Context, in *InternetDoctorForbiddenRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//获取互联网医生信息
	InternetDoctorInfo(ctx context.Context, in *InternetDoctorInfoRequest, opts ...grpc.CallOption) (*InternetDoctorInfoResponse, error)
	//获取互联网医生列表
	DiagnoseInternetDoctorList(ctx context.Context, in *InternetDoctorListRequest, opts ...grpc.CallOption) (*InternetDoctorListResponse, error)
	//启用禁用互联网医生
	DoctorOnlineChange(ctx context.Context, in *DoctorOnlineChangeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//医生接单后台 医生信息状态
	DoctorStatus(ctx context.Context, in *DoctorStatusRequest, opts ...grpc.CallOption) (*DoctorStatusResponse, error)
	//平台医院列表
	HospitalList(ctx context.Context, in *HospitalListRequest, opts ...grpc.CallOption) (*HospitalListResponse, error)
	//导出列表
	ScrmDoctorListExport(ctx context.Context, in *ScrmDoctorListExportRequest, opts ...grpc.CallOption) (*ScrmDoctorListResponse, error)
	//通知医生抢单
	NotifyGrabOrder(ctx context.Context, in *NotifyGrabOrderRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//医生禁用启用
	DoctorIsForbidden(ctx context.Context, in *DoctorIsForbiddenRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//获取医生协议状态
	GetDoctorProtocolState(ctx context.Context, in *GetDoctorProtocolStateRequest, opts ...grpc.CallOption) (*GetDoctorProtocolStateResponse, error)
	//编辑医生协议状态
	EditDoctorProtocolState(ctx context.Context, in *EditDoctorProtocolStateRequest, opts ...grpc.CallOption) (*EditDoctorProtocolStateResponse, error)
}

type doctorServiceClient struct {
	cc *grpc.ClientConn
}

func NewDoctorServiceClient(cc *grpc.ClientConn) DoctorServiceClient {
	return &doctorServiceClient{cc}
}

func (c *doctorServiceClient) DoctorList(ctx context.Context, in *DoctorListRequest, opts ...grpc.CallOption) (*DoctorListResponse, error) {
	out := new(DoctorListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorInfo(ctx context.Context, in *StatisticsType, opts ...grpc.CallOption) (*DoctorInfoResponse, error) {
	out := new(DoctorInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorTips(ctx context.Context, in *DoctorTipsRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorTips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) GetDoctorInfo(ctx context.Context, in *GetDoctorInfoRequest, opts ...grpc.CallOption) (*GetDoctorInfoResponse, error) {
	out := new(GetDoctorInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/GetDoctorInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) GetDoctorPrice(ctx context.Context, in *GetDoctorPriceRequest, opts ...grpc.CallOption) (*GetDoctorPriceResponse, error) {
	out := new(GetDoctorPriceResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/GetDoctorPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorSetting(ctx context.Context, in *DoctorSettingRequest, opts ...grpc.CallOption) (*DoctorSettingReponse, error) {
	out := new(DoctorSettingReponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorSettingInfo(ctx context.Context, in *DoctorSettingInfoRequest, opts ...grpc.CallOption) (*DoctorSettingInfoResponse, error) {
	out := new(DoctorSettingInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorSettingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorSettingSwitch(ctx context.Context, in *DoctorSettingSwitchRequest, opts ...grpc.CallOption) (*DoctorSettingSwitchResponse, error) {
	out := new(DoctorSettingSwitchResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorSettingSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) GetDispatchDoctor(ctx context.Context, in *GetDispatchDoctorReq, opts ...grpc.CallOption) (*DoctorInfoResponse, error) {
	out := new(DoctorInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/GetDispatchDoctor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) GetDispatchDoctorByOpenId(ctx context.Context, in *GetDispatchDoctorByOpenIdReq, opts ...grpc.CallOption) (*DoctorInfoResponse, error) {
	out := new(DoctorInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/GetDispatchDoctorByOpenId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) RegisterDoctorMiniProgram(ctx context.Context, in *RegisterDoctorMiniProgramReq, opts ...grpc.CallOption) (*RegisterDoctorMiniProgramRes, error) {
	out := new(RegisterDoctorMiniProgramRes)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/RegisterDoctorMiniProgram", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) LogoutDoctorMiniProgram(ctx context.Context, in *LogoutDoctorMiniProgramReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/LogoutDoctorMiniProgram", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseDetails(ctx context.Context, in *DiagnoseDetailsRequest, opts ...grpc.CallOption) (*DiagnoseDetailsResponse, error) {
	out := new(DiagnoseDetailsResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseList(ctx context.Context, in *DiagnoseListRequest, opts ...grpc.CallOption) (*DiagnoseListResponse, error) {
	out := new(DiagnoseListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) ScrmDoctorList(ctx context.Context, in *ScrmDoctorListRequest, opts ...grpc.CallOption) (*ScrmDoctorListResponse, error) {
	out := new(ScrmDoctorListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/ScrmDoctorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseSet(ctx context.Context, in *DiagnoseSetRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseSystemInfo(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*DiagnoseSystemInfoResponse, error) {
	out := new(DiagnoseSystemInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseSystemInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseInternetDoctorAdd(ctx context.Context, in *InternetDoctorAddRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseInternetDoctorAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) InternetDoctorForbidden(ctx context.Context, in *InternetDoctorForbiddenRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/InternetDoctorForbidden", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) InternetDoctorInfo(ctx context.Context, in *InternetDoctorInfoRequest, opts ...grpc.CallOption) (*InternetDoctorInfoResponse, error) {
	out := new(InternetDoctorInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/InternetDoctorInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DiagnoseInternetDoctorList(ctx context.Context, in *InternetDoctorListRequest, opts ...grpc.CallOption) (*InternetDoctorListResponse, error) {
	out := new(InternetDoctorListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DiagnoseInternetDoctorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorOnlineChange(ctx context.Context, in *DoctorOnlineChangeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorOnlineChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorStatus(ctx context.Context, in *DoctorStatusRequest, opts ...grpc.CallOption) (*DoctorStatusResponse, error) {
	out := new(DoctorStatusResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) HospitalList(ctx context.Context, in *HospitalListRequest, opts ...grpc.CallOption) (*HospitalListResponse, error) {
	out := new(HospitalListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/HospitalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) ScrmDoctorListExport(ctx context.Context, in *ScrmDoctorListExportRequest, opts ...grpc.CallOption) (*ScrmDoctorListResponse, error) {
	out := new(ScrmDoctorListResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/ScrmDoctorListExport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) NotifyGrabOrder(ctx context.Context, in *NotifyGrabOrderRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/NotifyGrabOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) DoctorIsForbidden(ctx context.Context, in *DoctorIsForbiddenRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/DoctorIsForbidden", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) GetDoctorProtocolState(ctx context.Context, in *GetDoctorProtocolStateRequest, opts ...grpc.CallOption) (*GetDoctorProtocolStateResponse, error) {
	out := new(GetDoctorProtocolStateResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/GetDoctorProtocolState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *doctorServiceClient) EditDoctorProtocolState(ctx context.Context, in *EditDoctorProtocolStateRequest, opts ...grpc.CallOption) (*EditDoctorProtocolStateResponse, error) {
	out := new(EditDoctorProtocolStateResponse)
	err := c.cc.Invoke(ctx, "/dgc.DoctorService/EditDoctorProtocolState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoctorServiceServer is the server API for DoctorService service.
type DoctorServiceServer interface {
	// 获取医生列表
	DoctorList(context.Context, *DoctorListRequest) (*DoctorListResponse, error)
	// 获取医生信息
	DoctorInfo(context.Context, *StatisticsType) (*DoctorInfoResponse, error)
	// 问诊列表提示语（提示内容：已在门店排班不支持接单，待空闲时将继续为你推送问诊订单）
	DoctorTips(context.Context, *DoctorTipsRequest) (*BaseResponseNew, error)
	// 用户端获取医生信息
	GetDoctorInfo(context.Context, *GetDoctorInfoRequest) (*GetDoctorInfoResponse, error)
	// 用户端获取问诊价格
	GetDoctorPrice(context.Context, *GetDoctorPriceRequest) (*GetDoctorPriceResponse, error)
	// 在线问诊 医生接单设置信息配置
	DoctorSetting(context.Context, *DoctorSettingRequest) (*DoctorSettingReponse, error)
	// 获取医生 接单设置信息
	DoctorSettingInfo(context.Context, *DoctorSettingInfoRequest) (*DoctorSettingInfoResponse, error)
	// 开启接单、关闭接单
	DoctorSettingSwitch(context.Context, *DoctorSettingSwitchRequest) (*DoctorSettingSwitchResponse, error)
	// 获取可派单的互联网医生信息一个
	GetDispatchDoctor(context.Context, *GetDispatchDoctorReq) (*DoctorInfoResponse, error)
	// 根据医生openid获取医生信息
	GetDispatchDoctorByOpenId(context.Context, *GetDispatchDoctorByOpenIdReq) (*DoctorInfoResponse, error)
	// 注册医生端小程序
	RegisterDoctorMiniProgram(context.Context, *RegisterDoctorMiniProgramReq) (*RegisterDoctorMiniProgramRes, error)
	// 注销医生端小程序
	LogoutDoctorMiniProgram(context.Context, *LogoutDoctorMiniProgramReq) (*BaseResponseNew, error)
	//获取问诊详情
	DiagnoseDetails(context.Context, *DiagnoseDetailsRequest) (*DiagnoseDetailsResponse, error)
	//获取问诊列表
	DiagnoseList(context.Context, *DiagnoseListRequest) (*DiagnoseListResponse, error)
	//获取SCRM医生列表
	ScrmDoctorList(context.Context, *ScrmDoctorListRequest) (*ScrmDoctorListResponse, error)
	//医生接单设置
	DiagnoseSet(context.Context, *DiagnoseSetRequest) (*BaseResponseNew, error)
	//获取医生接单设置
	DiagnoseSystemInfo(context.Context, *EmptyRequest) (*DiagnoseSystemInfoResponse, error)
	//新增互联网医生
	DiagnoseInternetDoctorAdd(context.Context, *InternetDoctorAddRequest) (*BaseResponseNew, error)
	//启用禁用互联网医生
	InternetDoctorForbidden(context.Context, *InternetDoctorForbiddenRequest) (*BaseResponseNew, error)
	//获取互联网医生信息
	InternetDoctorInfo(context.Context, *InternetDoctorInfoRequest) (*InternetDoctorInfoResponse, error)
	//获取互联网医生列表
	DiagnoseInternetDoctorList(context.Context, *InternetDoctorListRequest) (*InternetDoctorListResponse, error)
	//启用禁用互联网医生
	DoctorOnlineChange(context.Context, *DoctorOnlineChangeRequest) (*BaseResponseNew, error)
	//医生接单后台 医生信息状态
	DoctorStatus(context.Context, *DoctorStatusRequest) (*DoctorStatusResponse, error)
	//平台医院列表
	HospitalList(context.Context, *HospitalListRequest) (*HospitalListResponse, error)
	//导出列表
	ScrmDoctorListExport(context.Context, *ScrmDoctorListExportRequest) (*ScrmDoctorListResponse, error)
	//通知医生抢单
	NotifyGrabOrder(context.Context, *NotifyGrabOrderRequest) (*BaseResponseNew, error)
	//医生禁用启用
	DoctorIsForbidden(context.Context, *DoctorIsForbiddenRequest) (*BaseResponseNew, error)
	//获取医生协议状态
	GetDoctorProtocolState(context.Context, *GetDoctorProtocolStateRequest) (*GetDoctorProtocolStateResponse, error)
	//编辑医生协议状态
	EditDoctorProtocolState(context.Context, *EditDoctorProtocolStateRequest) (*EditDoctorProtocolStateResponse, error)
}

// UnimplementedDoctorServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDoctorServiceServer struct {
}

func (*UnimplementedDoctorServiceServer) DoctorList(ctx context.Context, req *DoctorListRequest) (*DoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorList not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorInfo(ctx context.Context, req *StatisticsType) (*DoctorInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorInfo not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorTips(ctx context.Context, req *DoctorTipsRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorTips not implemented")
}
func (*UnimplementedDoctorServiceServer) GetDoctorInfo(ctx context.Context, req *GetDoctorInfoRequest) (*GetDoctorInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDoctorInfo not implemented")
}
func (*UnimplementedDoctorServiceServer) GetDoctorPrice(ctx context.Context, req *GetDoctorPriceRequest) (*GetDoctorPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDoctorPrice not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorSetting(ctx context.Context, req *DoctorSettingRequest) (*DoctorSettingReponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorSetting not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorSettingInfo(ctx context.Context, req *DoctorSettingInfoRequest) (*DoctorSettingInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorSettingInfo not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorSettingSwitch(ctx context.Context, req *DoctorSettingSwitchRequest) (*DoctorSettingSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorSettingSwitch not implemented")
}
func (*UnimplementedDoctorServiceServer) GetDispatchDoctor(ctx context.Context, req *GetDispatchDoctorReq) (*DoctorInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDispatchDoctor not implemented")
}
func (*UnimplementedDoctorServiceServer) GetDispatchDoctorByOpenId(ctx context.Context, req *GetDispatchDoctorByOpenIdReq) (*DoctorInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDispatchDoctorByOpenId not implemented")
}
func (*UnimplementedDoctorServiceServer) RegisterDoctorMiniProgram(ctx context.Context, req *RegisterDoctorMiniProgramReq) (*RegisterDoctorMiniProgramRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterDoctorMiniProgram not implemented")
}
func (*UnimplementedDoctorServiceServer) LogoutDoctorMiniProgram(ctx context.Context, req *LogoutDoctorMiniProgramReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogoutDoctorMiniProgram not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseDetails(ctx context.Context, req *DiagnoseDetailsRequest) (*DiagnoseDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseDetails not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseList(ctx context.Context, req *DiagnoseListRequest) (*DiagnoseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseList not implemented")
}
func (*UnimplementedDoctorServiceServer) ScrmDoctorList(ctx context.Context, req *ScrmDoctorListRequest) (*ScrmDoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScrmDoctorList not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseSet(ctx context.Context, req *DiagnoseSetRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseSet not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseSystemInfo(ctx context.Context, req *EmptyRequest) (*DiagnoseSystemInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseSystemInfo not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseInternetDoctorAdd(ctx context.Context, req *InternetDoctorAddRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseInternetDoctorAdd not implemented")
}
func (*UnimplementedDoctorServiceServer) InternetDoctorForbidden(ctx context.Context, req *InternetDoctorForbiddenRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InternetDoctorForbidden not implemented")
}
func (*UnimplementedDoctorServiceServer) InternetDoctorInfo(ctx context.Context, req *InternetDoctorInfoRequest) (*InternetDoctorInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InternetDoctorInfo not implemented")
}
func (*UnimplementedDoctorServiceServer) DiagnoseInternetDoctorList(ctx context.Context, req *InternetDoctorListRequest) (*InternetDoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseInternetDoctorList not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorOnlineChange(ctx context.Context, req *DoctorOnlineChangeRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorOnlineChange not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorStatus(ctx context.Context, req *DoctorStatusRequest) (*DoctorStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorStatus not implemented")
}
func (*UnimplementedDoctorServiceServer) HospitalList(ctx context.Context, req *HospitalListRequest) (*HospitalListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HospitalList not implemented")
}
func (*UnimplementedDoctorServiceServer) ScrmDoctorListExport(ctx context.Context, req *ScrmDoctorListExportRequest) (*ScrmDoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScrmDoctorListExport not implemented")
}
func (*UnimplementedDoctorServiceServer) NotifyGrabOrder(ctx context.Context, req *NotifyGrabOrderRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyGrabOrder not implemented")
}
func (*UnimplementedDoctorServiceServer) DoctorIsForbidden(ctx context.Context, req *DoctorIsForbiddenRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorIsForbidden not implemented")
}
func (*UnimplementedDoctorServiceServer) GetDoctorProtocolState(ctx context.Context, req *GetDoctorProtocolStateRequest) (*GetDoctorProtocolStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDoctorProtocolState not implemented")
}
func (*UnimplementedDoctorServiceServer) EditDoctorProtocolState(ctx context.Context, req *EditDoctorProtocolStateRequest) (*EditDoctorProtocolStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditDoctorProtocolState not implemented")
}

func RegisterDoctorServiceServer(s *grpc.Server, srv DoctorServiceServer) {
	s.RegisterService(&_DoctorService_serviceDesc, srv)
}

func _DoctorService_DoctorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorList(ctx, req.(*DoctorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatisticsType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorInfo(ctx, req.(*StatisticsType))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorTipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorTips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorTips(ctx, req.(*DoctorTipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_GetDoctorInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDoctorInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).GetDoctorInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/GetDoctorInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).GetDoctorInfo(ctx, req.(*GetDoctorInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_GetDoctorPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDoctorPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).GetDoctorPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/GetDoctorPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).GetDoctorPrice(ctx, req.(*GetDoctorPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorSetting(ctx, req.(*DoctorSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorSettingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorSettingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorSettingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorSettingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorSettingInfo(ctx, req.(*DoctorSettingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorSettingSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorSettingSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorSettingSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorSettingSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorSettingSwitch(ctx, req.(*DoctorSettingSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_GetDispatchDoctor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDispatchDoctorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).GetDispatchDoctor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/GetDispatchDoctor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).GetDispatchDoctor(ctx, req.(*GetDispatchDoctorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_GetDispatchDoctorByOpenId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDispatchDoctorByOpenIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).GetDispatchDoctorByOpenId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/GetDispatchDoctorByOpenId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).GetDispatchDoctorByOpenId(ctx, req.(*GetDispatchDoctorByOpenIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_RegisterDoctorMiniProgram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterDoctorMiniProgramReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).RegisterDoctorMiniProgram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/RegisterDoctorMiniProgram",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).RegisterDoctorMiniProgram(ctx, req.(*RegisterDoctorMiniProgramReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_LogoutDoctorMiniProgram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutDoctorMiniProgramReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).LogoutDoctorMiniProgram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/LogoutDoctorMiniProgram",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).LogoutDoctorMiniProgram(ctx, req.(*LogoutDoctorMiniProgramReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiagnoseDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseDetails(ctx, req.(*DiagnoseDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiagnoseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseList(ctx, req.(*DiagnoseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_ScrmDoctorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScrmDoctorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).ScrmDoctorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/ScrmDoctorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).ScrmDoctorList(ctx, req.(*ScrmDoctorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiagnoseSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseSet(ctx, req.(*DiagnoseSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseSystemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseSystemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseSystemInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseSystemInfo(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseInternetDoctorAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternetDoctorAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseInternetDoctorAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseInternetDoctorAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseInternetDoctorAdd(ctx, req.(*InternetDoctorAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_InternetDoctorForbidden_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternetDoctorForbiddenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).InternetDoctorForbidden(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/InternetDoctorForbidden",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).InternetDoctorForbidden(ctx, req.(*InternetDoctorForbiddenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_InternetDoctorInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternetDoctorInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).InternetDoctorInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/InternetDoctorInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).InternetDoctorInfo(ctx, req.(*InternetDoctorInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DiagnoseInternetDoctorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternetDoctorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DiagnoseInternetDoctorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DiagnoseInternetDoctorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DiagnoseInternetDoctorList(ctx, req.(*InternetDoctorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorOnlineChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorOnlineChangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorOnlineChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorOnlineChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorOnlineChange(ctx, req.(*DoctorOnlineChangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorStatus(ctx, req.(*DoctorStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_HospitalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HospitalListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).HospitalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/HospitalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).HospitalList(ctx, req.(*HospitalListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_ScrmDoctorListExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScrmDoctorListExportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).ScrmDoctorListExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/ScrmDoctorListExport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).ScrmDoctorListExport(ctx, req.(*ScrmDoctorListExportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_NotifyGrabOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGrabOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).NotifyGrabOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/NotifyGrabOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).NotifyGrabOrder(ctx, req.(*NotifyGrabOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_DoctorIsForbidden_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorIsForbiddenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).DoctorIsForbidden(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/DoctorIsForbidden",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).DoctorIsForbidden(ctx, req.(*DoctorIsForbiddenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_GetDoctorProtocolState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDoctorProtocolStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).GetDoctorProtocolState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/GetDoctorProtocolState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).GetDoctorProtocolState(ctx, req.(*GetDoctorProtocolStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DoctorService_EditDoctorProtocolState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditDoctorProtocolStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoctorServiceServer).EditDoctorProtocolState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.DoctorService/EditDoctorProtocolState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoctorServiceServer).EditDoctorProtocolState(ctx, req.(*EditDoctorProtocolStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DoctorService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.DoctorService",
	HandlerType: (*DoctorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoctorList",
			Handler:    _DoctorService_DoctorList_Handler,
		},
		{
			MethodName: "DoctorInfo",
			Handler:    _DoctorService_DoctorInfo_Handler,
		},
		{
			MethodName: "DoctorTips",
			Handler:    _DoctorService_DoctorTips_Handler,
		},
		{
			MethodName: "GetDoctorInfo",
			Handler:    _DoctorService_GetDoctorInfo_Handler,
		},
		{
			MethodName: "GetDoctorPrice",
			Handler:    _DoctorService_GetDoctorPrice_Handler,
		},
		{
			MethodName: "DoctorSetting",
			Handler:    _DoctorService_DoctorSetting_Handler,
		},
		{
			MethodName: "DoctorSettingInfo",
			Handler:    _DoctorService_DoctorSettingInfo_Handler,
		},
		{
			MethodName: "DoctorSettingSwitch",
			Handler:    _DoctorService_DoctorSettingSwitch_Handler,
		},
		{
			MethodName: "GetDispatchDoctor",
			Handler:    _DoctorService_GetDispatchDoctor_Handler,
		},
		{
			MethodName: "GetDispatchDoctorByOpenId",
			Handler:    _DoctorService_GetDispatchDoctorByOpenId_Handler,
		},
		{
			MethodName: "RegisterDoctorMiniProgram",
			Handler:    _DoctorService_RegisterDoctorMiniProgram_Handler,
		},
		{
			MethodName: "LogoutDoctorMiniProgram",
			Handler:    _DoctorService_LogoutDoctorMiniProgram_Handler,
		},
		{
			MethodName: "DiagnoseDetails",
			Handler:    _DoctorService_DiagnoseDetails_Handler,
		},
		{
			MethodName: "DiagnoseList",
			Handler:    _DoctorService_DiagnoseList_Handler,
		},
		{
			MethodName: "ScrmDoctorList",
			Handler:    _DoctorService_ScrmDoctorList_Handler,
		},
		{
			MethodName: "DiagnoseSet",
			Handler:    _DoctorService_DiagnoseSet_Handler,
		},
		{
			MethodName: "DiagnoseSystemInfo",
			Handler:    _DoctorService_DiagnoseSystemInfo_Handler,
		},
		{
			MethodName: "DiagnoseInternetDoctorAdd",
			Handler:    _DoctorService_DiagnoseInternetDoctorAdd_Handler,
		},
		{
			MethodName: "InternetDoctorForbidden",
			Handler:    _DoctorService_InternetDoctorForbidden_Handler,
		},
		{
			MethodName: "InternetDoctorInfo",
			Handler:    _DoctorService_InternetDoctorInfo_Handler,
		},
		{
			MethodName: "DiagnoseInternetDoctorList",
			Handler:    _DoctorService_DiagnoseInternetDoctorList_Handler,
		},
		{
			MethodName: "DoctorOnlineChange",
			Handler:    _DoctorService_DoctorOnlineChange_Handler,
		},
		{
			MethodName: "DoctorStatus",
			Handler:    _DoctorService_DoctorStatus_Handler,
		},
		{
			MethodName: "HospitalList",
			Handler:    _DoctorService_HospitalList_Handler,
		},
		{
			MethodName: "ScrmDoctorListExport",
			Handler:    _DoctorService_ScrmDoctorListExport_Handler,
		},
		{
			MethodName: "NotifyGrabOrder",
			Handler:    _DoctorService_NotifyGrabOrder_Handler,
		},
		{
			MethodName: "DoctorIsForbidden",
			Handler:    _DoctorService_DoctorIsForbidden_Handler,
		},
		{
			MethodName: "GetDoctorProtocolState",
			Handler:    _DoctorService_GetDoctorProtocolState_Handler,
		},
		{
			MethodName: "EditDoctorProtocolState",
			Handler:    _DoctorService_EditDoctorProtocolState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/doctor.proto",
}

package dto

type MytResponse struct {
	Page      int            `json:"page"`
	Total     int            `json:"total"`
	TotalPage int            `json:"total_page"`
	IsLast    bool           `json:"is_last"`
	Data      []MytOrderData `json:"data"`
}

type MytOrderData struct {
	Order         MytOrder        `json:"order"`
	OrderGoods    []MytOrderGoods `json:"order_goods"`
	OrderCustomer Customer        `json:"order_customer"`
	UpdateTime    int64           `json:"update_time"`
}

type MytOrder struct {
	OrderId      string      `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	OrderSn      int         `protobuf:"varint,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	ShopId       string      `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	ShopName     string      `protobuf:"bytes,4,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	OriginTag    string      `protobuf:"bytes,5,opt,name=origin_tag,json=originTag,proto3" json:"origin_tag"`
	Category     string      `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	IsPreOrder   bool        `protobuf:"varint,7,opt,name=is_pre_order,json=isPreOrder,proto3" json:"is_pre_order"`
	TotalPrice   int         `protobuf:"varint,8,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	BalancePrice int         `protobuf:"varint,9,opt,name=balance_price,json=balancePrice,proto3" json:"balance_price"`
	CreateTime   int64       `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	DeliveryTime int64       `protobuf:"varint,11,opt,name=delivery_time,json=deliveryTime,proto3" json:"delivery_time"`
	DeliveryEnd  int64       `protobuf:"varint,12,opt,name=delivery_end,json=deliveryEnd,proto3" json:"delivery_end"`
	DeliveryType int         `protobuf:"varint,13,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	IsPicker     bool        `protobuf:"varint,14,opt,name=is_picker,json=isPicker,proto3" json:"is_picker"`
	PickTime     int64       `protobuf:"varint,15,opt,name=pick_time,json=pickTime,proto3" json:"pick_time"`
	UserRemark   string      `protobuf:"bytes,16,opt,name=user_remark,json=userRemark,proto3" json:"user_remark"`
	Greeting     string      `protobuf:"bytes,17,opt,name=greeting,proto3" json:"greeting"`
	PickupCode   string      `protobuf:"bytes,18,opt,name=pickup_code,json=pickupCode,proto3" json:"pickup_code"`
	OrderFee     MytOrderFee `json:"order_fee"`
}

type MytOrderFee struct {
	TotalFee    int           `json:"total_fee"`
	SendFee     int           `json:"send_fee"`
	PackageFee  int           `json:"package_fee"`
	DiscountFee int           `json:"discount_fee"`
	ShopFee     int           `json:"shop_fee"`
	UserFee     int           `json:"user_fee"`
	PayType     int           `json:"pay_type"`
	NeedInvoice bool          `json:"need_invoice"`
	Invoice     MytInvoice    `json:"invoice"`
	Commission  float64       `json:"commission"`
	Activity    []MytActivity `json:"activity"`
	IsFirst     bool          `json:"is_first"`
	IsFavorite  bool          `json:"is_favorite"`
}

type MytInvoice struct {
	Type     int    `json:"type"`
	Title    string `json:"title"`
	TaxerID  string `json:"taxer_id"`
	Email    string `json:"email"`
	FormType int    `json:"form_type"`
}

type MytActivity struct {
	Type     int    `json:"type"`
	Title    string `json:"title"`
	Merchant int    `json:"merchant"`
	Reduce   int    `json:"reduce"`
}

type MytOrderGoods struct {
	GoodsID              string `json:"goods_id"`
	GoodsName            string `json:"goods_name"`
	Thumb                string `json:"thumb"`
	SKUId                string `json:"sku_id"`
	Unit                 string `json:"unit"`
	Weight               int    `json:"weight"`
	UPC                  string `json:"upc"`
	ShelfNo              string `json:"shelf_no"`
	Number               int    `json:"number"`
	GoodsPrice           int    `json:"goods_price"`
	GoodsTotalFee        int    `json:"goods_total_fee"`
	PackageNumber        int    `json:"package_number"`
	PackagePrice         int    `json:"package_price"`
	PackageTotalFee      int    `json:"package_total_fee"`
	ReduceFee            int    `json:"reduce_fee"`
	DiscountFee          int    `json:"discount_fee"`
	DiscountPlatformFee  int    `json:"discount_platform_fee"`
	DiscountMerchantFee  int    `json:"discount_merchant_fee"`
	DiscountAgentFee     int    `json:"discount_agent_fee"`
	DiscountLogisticsFee int    `json:"discount_logistics_fee"`
	TotalFee             int    `json:"total_fee"`
}

type Customer struct {
	RealName     string `json:"real_name"`
	Phone        string `json:"phone"`
	SecretPhone  string `json:"secret_phone"`
	OrderPhone   string `json:"order_phone"`
	ReservePhone string `json:"reserve_phone"`
	Address      string `json:"address"`
	Longitude    string `json:"longitude"`
	Latitude     string `json:"latitude"`
}

package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"strconv"
	"strings"
	"time"
)

// 父订单-导出订单数据
type ParentOrderExport struct {
	F           *excelize.File
	SheetName   string
	storeMap    map[string]*dac.StoreInfo
	taskParams  *oc.AwenParentOrderListRequest
	OrgId       int32
	FinanceCode string
}

// 逻辑
func (e *ParentOrderExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenParentOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []*oc.AwenParentOrderExport

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	e.taskParams.Orgid = cast.ToInt64(e.OrgId)
	//e.taskParams.FinancialCode = e.FinanceCode
	nowCount := 0
	for {
		details, err = services.AwenParentOrderExport(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		orderList = append(orderList, details...)
		nowCount++
		glog.Info(e.taskParams.UserNo, " details length - pagesize length", len(details), e.taskParams.PageSize, " 跑了多少次"+cast.ToString(nowCount))
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)
	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}
	var n string
	for k := range orderList {
		n = strconv.Itoa(k + 2)
		if orderList[k].AppChannel == 12 {
			// 订单类型
			switch orderList[k].OrderType {
			case 22:
				e.F.SetCellValue(e.SheetName, "A"+n, "次卡")
			case 23:
				e.F.SetCellValue(e.SheetName, "A"+n, "储值卡")
			default:
				e.F.SetCellValue(e.SheetName, "A"+n, "普通订单")
			}
			// 订单号
			e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].OrderSn)
			// 外部订单号
			e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
			// 下单时间
			e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].CreateTime)
			// 支付流水号
			e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].PaySn)
			// 支付时间
			e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].PayTime)
			// 实收金额（实收金额）
			e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].PayAmount))
			// 商家预计收入
			e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(orderList[k].ActualReceiveTotal))
			// 订单原价（商品总额）
			e.F.SetCellValue(e.SheetName, "I"+n, kit.FenToYuan(orderList[k].GoodsTotal))
			// 商家补贴
			e.F.SetCellValue(e.SheetName, "J"+n, "-"+cast.ToString(kit.FenToYuan(orderList[k].Privilege-orderList[k].PlatformPayedAmount)))

			// 退款金额
			e.F.SetCellValue(e.SheetName, "K"+n, orderList[k].RefundAmount)

			//商品实付金额
			e.F.SetCellValue(e.SheetName, "L"+n, kit.FenToYuan(orderList[k].GoodsPayTotal))

			//配送费
			e.F.SetCellValue(e.SheetName, "M"+n, kit.FenToYuan(orderList[k].Freight))

			// 包装费
			e.F.SetCellValue(e.SheetName, "N"+n, kit.FenToYuan(orderList[k].PackingCost))

			//商家配送费优惠
			e.F.SetCellValue(e.SheetName, "O"+n, 0-kit.FenToYuan(orderList[k].FreightPrivilege))

			//平台补贴
			e.F.SetCellValue(e.SheetName, "P"+n, 0-kit.FenToYuan(orderList[k].PlatformPayedAmount))

			//平台配送费优惠
			e.F.SetCellValue(e.SheetName, "Q"+n, 0-kit.FenToYuan(orderList[k].PlatformFreightPrivilege))

			//平台服务费
			e.F.SetCellValue(e.SheetName, "R"+n, 0-kit.FenToYuan(orderList[k].ServiceCharge))

			//履约服务费
			e.F.SetCellValue(e.SheetName, "S"+n, 0-kit.FenToYuan(orderList[k].ContractFee))
			//平台实际分摊补贴
			e.F.SetCellValue(e.SheetName, "T"+n, kit.FenToYuan(orderList[k].PrivilegePt))
			// 支付方式 1支付宝 2微信 3美团支付（宠物saas需要读取dc_order.order_payment表）
			e.F.SetCellValue(e.SheetName, "U"+n, strings.Trim(orderList[k].PayModeText, ","))

			// 所属连锁名称
			e.F.SetCellValue(e.SheetName, "V"+n, orderList[k].ChainName)
			//  店铺名称
			e.F.SetCellValue(e.SheetName, "W"+n, orderList[k].ShopName)
			//  店铺id
			e.F.SetCellValue(e.SheetName, "X"+n, orderList[k].ShopId)
			// 店铺类型
			e.F.SetCellValue(e.SheetName, "Y"+n, orderList[k].TenantType)
			//  新零售运营
			e.F.SetCellValue(e.SheetName, "Z"+n, orderList[k].TenantRetailOperationType)
			//订单来源
			orderFromName := services.OrderFrom[orderList[k].ChannelId]
			if orderFromName == "阿闻到家" {
				orderFromName = "小程序"
			}
			e.F.SetCellValue(e.SheetName, "AA"+n, orderFromName)
			// 销售渠道
			if _, ok := services.UserAgent[orderList[k].UserAgent]; ok {
				e.F.SetCellValue(e.SheetName, "AB"+n, services.UserAgent[orderList[k].UserAgent])
			} else {
				e.F.SetCellValue(e.SheetName, "AB"+n, "其它")
			}
			// 用户ID
			e.F.SetCellValue(e.SheetName, "AC"+n, orderList[k].MemberId)
			// 顾客类型，默认0,1-新顾客，2-老顾客
			if (orderList[k].ChannelId == services.ChannelAwenId || orderList[k].ChannelId == services.ChannelDigitalHealth) && utils.CampareTime(orderList[k].CreateTime, "2021-06-10 00:00:00") {
				if orderList[k].IsNewCustomer == 1 {
					e.F.SetCellValue(e.SheetName, "AD"+n, "新顾客")
				} else if orderList[k].IsNewCustomer == 2 {
					e.F.SetCellValue(e.SheetName, "AD"+n, "老顾客")
				} else {
					e.F.SetCellValue(e.SheetName, "AD"+n, "打标中")
				}
			}

			// 收货人姓名
			e.F.SetCellValue(e.SheetName, "AE"+n, orderList[k].ReceiverName)
			// 收货人联系方式
			e.F.SetCellValue(e.SheetName, "AF"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip))
			// 收货人地址
			e.F.SetCellValue(e.SheetName, "AG"+n, orderList[k].ReceiverAddress)
			// 备注
			e.F.SetCellValue(e.SheetName, "AH"+n, orderList[k].BuyerMemo)
			// 配送方式
			e.F.SetCellValue(e.SheetName, "AI"+n, services.DeliveryType[orderList[k].DeliveryType])
			// 订单状态
			e.F.SetCellValue(e.SheetName, "AJ"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
			//  活动类型
			e.F.SetCellValue(e.SheetName, "AK"+n, orderList[k].ActivityType)
			//  拣货人
			e.F.SetCellValue(e.SheetName, "AL"+n, orderList[k].PickUserId)
			// 拣货时间
			e.F.SetCellValue(e.SheetName, "AM"+n, orderList[k].PickingTime)
			// 仓库类型(门店类型)
			e.F.SetCellValue(e.SheetName, "AN"+n, orderList[k].Category)
			// 仓库名称
			e.F.SetCellValue(e.SheetName, "AO"+n, orderList[k].WarehouseName)
			//店铺支付配送费
			e.F.SetCellValue(e.SheetName, "AP"+n, orderList[k].StorePayDeliveryAmount)

			//预计送达时间
			e.F.SetCellValue(e.SheetName, "AQ"+n, orderList[k].ExpectedTime)
			//账单订单完成时间
			e.F.SetCellValue(e.SheetName, "AR"+n, orderList[k].BillCompletedTime)
			//账单订单取消时间
			e.F.SetCellValue(e.SheetName, "AS"+n, orderList[k].BillCanceledTime)
			//账户时间
			e.F.SetCellValue(e.SheetName, "AT"+n, orderList[k].TradeCreatedTime)
			//入账时间
			e.F.SetCellValue(e.SheetName, "AU"+n, orderList[k].TradePaymentTime)
			//账单日期
			e.F.SetCellValue(e.SheetName, "AV"+n, orderList[k].TradeTime)

		} else {
			// 订单号
			e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
			// 外部订单号
			e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].OldOrderSn)
			// 商品筛选
			switch orderList[k].GroupType {
			case 1:
				e.F.SetCellValue(e.SheetName, "C"+n, "有实实组合")
			case 2:
				e.F.SetCellValue(e.SheetName, "C"+n, "有虚虚组合")
			case 3:
				e.F.SetCellValue(e.SheetName, "C"+n, "有虚实组合")
			case 0:
				e.F.SetCellValue(e.SheetName, "C"+n, "无组合商品")
			}
			// 下单时间
			e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].CreateTime)
			// 支付流水号
			e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].PaySn)
			// 支付时间
			e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].PayTime)
			// 实收金额（实收金额）
			e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].Total))

			e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(orderList[k].ActualReceiveTotal))
			// 订单原价
			e.F.SetCellValue(e.SheetName, "I"+n, kit.FenToYuan(orderList[k].GoodsTotal))
			// 商家补贴
			e.F.SetCellValue(e.SheetName, "J"+n, "-"+cast.ToString(kit.FenToYuan(orderList[k].Privilege-orderList[k].PlatformPayedAmount)))

			// 优惠金额
			//e.f.SetCellValue(e.sheetName, "J"+n, kit.FenToYuan(orderList[k].Privilege-orderList[k].PlatformPayedAmount))
			// 退款金额
			e.F.SetCellValue(e.SheetName, "K"+n, orderList[k].RefundAmount)

			//商品实付金额
			e.F.SetCellValue(e.SheetName, "L"+n, kit.FenToYuan(orderList[k].GoodsPayTotal))

			//配送费
			e.F.SetCellValue(e.SheetName, "M"+n, kit.FenToYuan(orderList[k].Freight))

			// 包装费
			e.F.SetCellValue(e.SheetName, "N"+n, kit.FenToYuan(orderList[k].PackingCost))

			//商家配送费优惠
			e.F.SetCellValue(e.SheetName, "O"+n, 0-kit.FenToYuan(orderList[k].FreightPrivilege))

			//平台补贴
			e.F.SetCellValue(e.SheetName, "P"+n, 0-kit.FenToYuan(orderList[k].PlatformPayedAmount))

			//平台配送费优惠
			e.F.SetCellValue(e.SheetName, "Q"+n, 0-kit.FenToYuan(orderList[k].PlatformFreightPrivilege))

			//平台服务费
			e.F.SetCellValue(e.SheetName, "R"+n, 0-kit.FenToYuan(orderList[k].ServiceCharge))

			//履约服务费
			e.F.SetCellValue(e.SheetName, "S"+n, 0-kit.FenToYuan(orderList[k].ContractFee))

			// 支付方式 1支付宝 2微信 3美团支付
			e.F.SetCellValue(e.SheetName, "T"+n, services.PayMode[orderList[k].PayMode])
			// 大区
			if value, ok := e.storeMap[orderList[k].ShopId]; ok {
				e.F.SetCellValue(e.SheetName, "U"+n, value.Bigregion)
				e.F.SetCellValue(e.SheetName, "V"+n, value.City)
				e.F.SetCellValue(e.SheetName, "W"+n, value.Name)
				//财务编码
				e.F.SetCellValue(e.SheetName, "X"+n, value.FinanceCode)
			}
			//订单来源
			e.F.SetCellValue(e.SheetName, "Y"+n, services.OrderFrom[orderList[k].ChannelId])
			// 销售渠道
			if _, ok := services.UserAgent[orderList[k].UserAgent]; ok {
				e.F.SetCellValue(e.SheetName, "Z"+n, services.UserAgent[orderList[k].UserAgent])
			} else {
				e.F.SetCellValue(e.SheetName, "Z"+n, "其它")
			}
			// 用户ID
			e.F.SetCellValue(e.SheetName, "AA"+n, orderList[k].MemberId)
			// 顾客类型，默认0,1-新顾客，2-老顾客
			if (orderList[k].ChannelId == services.ChannelAwenId || orderList[k].ChannelId == services.ChannelDigitalHealth) && utils.CampareTime(orderList[k].CreateTime, "2021-06-10 00:00:00") {
				if orderList[k].IsNewCustomer == 1 {
					e.F.SetCellValue(e.SheetName, "AB"+n, "新顾客")
				} else if orderList[k].IsNewCustomer == 2 {
					e.F.SetCellValue(e.SheetName, "AB"+n, "老顾客")
				} else {
					e.F.SetCellValue(e.SheetName, "AB"+n, "打标中")
				}
			}
			// 收货人姓名
			e.F.SetCellValue(e.SheetName, "AC"+n, orderList[k].ReceiverName)
			// 收货人联系方式
			// 收货人地址
			if _, ok := phoneExportMap[orderList[k].ShopId]; ok {
				createTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, orderList[k].CreateTime)
				compareStrTime := "2022-04-01  00:00:00"
				compareTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, compareStrTime)
				if createTime.After(compareTime) {
					e.F.SetCellValue(e.SheetName, "AD"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip))
					e.F.SetCellValue(e.SheetName, "AE"+n, orderList[k].ReceiverAddress)
				} else {
					e.F.SetCellValue(e.SheetName, "AD"+n, "")
					e.F.SetCellValue(e.SheetName, "AE"+n, "")
				}
			} else {
				e.F.SetCellValue(e.SheetName, "AD"+n, "")
				e.F.SetCellValue(e.SheetName, "AE"+n, "")
			}
			// 备注
			e.F.SetCellValue(e.SheetName, "AF"+n, orderList[k].BuyerMemo)
			// 配送方式
			e.F.SetCellValue(e.SheetName, "AG"+n, services.DeliveryType[orderList[k].DeliveryType])
			// 订单状态
			e.F.SetCellValue(e.SheetName, "AH"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
			// 活动类型
			e.F.SetCellValue(e.SheetName, "AI"+n, orderList[k].ActivityType)
			// 业绩归属人
			e.F.SetCellValue(e.SheetName, "AJ"+n, orderList[k].PerformanceStaffName)
			// 业绩分配人
			e.F.SetCellValue(e.SheetName, "AK"+n, orderList[k].PerformanceOperatorName)
			// 业绩归属人所属门店编码
			e.F.SetCellValue(e.SheetName, "AL"+n, orderList[k].PerformanceFinanceCode)
			// 业绩归属人所属门店名称
			e.F.SetCellValue(e.SheetName, "AM"+n, orderList[k].PerformanceChainName)
			// 业绩分配时间
			e.F.SetCellValue(e.SheetName, "AN"+n, orderList[k].PerformanceOperatorTime)
			// 仓库类型(门店类型)
			e.F.SetCellValue(e.SheetName, "AO"+n, orderList[k].Category)
			// 仓库名称
			e.F.SetCellValue(e.SheetName, "AP"+n, orderList[k].WarehouseName)
			//店铺支付配送费
			e.F.SetCellValue(e.SheetName, "AQ"+n, orderList[k].StorePayDeliveryAmount)
			//店铺类型
			ShopType := "新瑞鹏"
			if orderList[k].AppChannel != 1 {
				ShopType = "TP代运营"
			}
			e.F.SetCellValue(e.SheetName, "AR"+n, ShopType)
			//提货点名称
			e.F.SetCellValue(e.SheetName, "AS"+n, orderList[k].PickupStationName)
			//提货点地址
			e.F.SetCellValue(e.SheetName, "AT"+n, orderList[k].PickupStationAddress)
			//预计送达时间
			e.F.SetCellValue(e.SheetName, "AU"+n, orderList[k].ExpectedTime)
			//账单订单完成时间
			e.F.SetCellValue(e.SheetName, "AV"+n, orderList[k].BillCompletedTime)
			//账单订单取消时间
			e.F.SetCellValue(e.SheetName, "AW"+n, orderList[k].BillCanceledTime)
			//账户时间
			e.F.SetCellValue(e.SheetName, "AX"+n, orderList[k].TradeCreatedTime)
			//入账时间
			e.F.SetCellValue(e.SheetName, "AY"+n, orderList[k].TradePaymentTime)
			//账单日期
			e.F.SetCellValue(e.SheetName, "AZ"+n, orderList[k].TradeTime)
			//平台实际分摊补贴
			e.F.SetCellValue(e.SheetName, "BA"+n, kit.FenToYuan(orderList[k].PrivilegePt))

		}
	}

	e.F.Save()

	return
}

func toBase26(n int64) string {
	if n == 0 {
		return "A"
	}

	result := ""
	for n > 0 {
		n-- // 为了使得结果从 1 开始而不是 0，我们需要减 1
		remainder := n % 26
		result = string(rune('A'+remainder)) + result
		n /= 26
	}

	return result
}

// 设置表头
func (e *ParentOrderExport) SetSheetName() {
	var nameList []string
	if e.OrgId == 6 {
		nameList = []string{
			"订单类型", "订单号", "渠道订单号", "下单时间", "支付流水号", "支付时间", "用户实付", "商家预计收入", "商品总额", "商家补贴",
			"退款金额", "商品实付总金额", "配送费", "包装费", "商家配送费优惠", "平台补贴", "平台配送费优惠", "平台服务费", "履约服务费", "平台实际分摊补贴",
			"支付方式", "所属连锁", "店铺名称", "店铺ID", "店铺类型", "新零售运营", "订单来源", "销售渠道", "用户ID", "顾客类型",
			"收货人", "收货人联系方式", "收货人地址", "备注", "配送方式", "订单状态", "活动类型", "拣货人", "拣货时间", "仓库类型",
			"仓库名称", "门店支付配送费", "预计送达时间", "账单订单完成时间", "账单订单取消时间", "账户时间", "入账时间", "账单日期",
		}
	} else {
		nameList = []string{
			"订单号", "外部订单号", "商品筛选", "下单时间", "支付流水号", "支付时间", "用户实付", "商家预计收入", "商品总额", "商家补贴", "退款金额",
			"商品实付总金额", "配送费", "包装费", "商家配送费优惠", "平台补贴", "平台配送费优惠", "平台服务费", "履约服务费",
			"支付方式", "大区",
			"城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "用户ID", "顾客类型", "收货人", "收货人联系方式", "收货人地址", "配送备注", "配送方式", "订单状态", "活动类型",
			"业绩归属人", "业绩分配人", "业绩归属人所属门店编码", "业绩归属人所属门店名称", "业绩分配时间",
			"仓库类型", "仓库名称", "门店支付配送费", "店铺类型", "提货点名称", "提货点地址", "预计送达时间", "账单订单完成时间", "账单订单取消时间", "账户时间", "入账时间", "账单日期",
			"平台实际分摊补贴",
		}
	}

	for i := 0; i < len(nameList); i++ {

		cloName := toBase26(cast.ToInt64(i+1)) + "1"
		e.F.SetCellValue(e.SheetName, cloName, nameList[i])
		//if i > 25 {
		//	j := i - 26
		//	e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		//} else {
		//	e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		//}
	}
}

// 上传至oss生成下载链接
func (e *ParentOrderExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("订单列表-导出订单数据(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

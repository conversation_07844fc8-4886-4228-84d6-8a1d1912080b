package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"order-center/proto/cc"
	"order-center/utils"
	"time"
)

// VIP虚拟卡导出
type VirtualCardExport struct {
	F          *excelize.File
	SheetName  string
	taskParams *cc.VirtualCardListReq
	writer     *excelize.StreamWriter
}

// VIP虚拟卡导出
func (e *VirtualCardExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(cc.VirtualCardListReq)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	//o := services.OrderService{}
	var ret *cc.VirtualCardListRes

	k := 0
	for {
		client := cc.GetCustomerCenterLongClient()
		ret, err = client.VirtualCard.VirtualCardList(client.Ctx, e.taskParams)
		//ret, err = o.OrderDeliveryReportList(context.Background(), e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		for i := 0; i < len(ret.List); i++ {
			k++

			axis := fmt.Sprintf("A%d", k+1)
			//"批次号", "卡模版Id", "所属组织", "卡号", "卡状态", "卡券生成时间", "卡券到期时间", "兑换用户手机号", "兑换用户id", "用户兑换时间",
			_ = e.writer.SetRow(axis, []interface{}{
				ret.List[i].BatchId,                                 // 批次号
				ret.List[i].TemplateId,                              // 卡模版Id
				ret.List[i].OrgName,                                 // 所属组织
				"FY" + cast.ToString(ret.List[i].CardId),            //卡号
				ret.List[i].StatusName,                              //卡状态
				ret.List[i].CreateTime,                              //卡券生成时间
				ret.List[i].ExpireTime,                              //卡券到期时间
				utils.MobileReplaceWithStar(ret.List[i].UserMobile), //兑换用户手机号
				ret.List[i].UserId,                                  //兑换用户id
				ret.List[i].UseTime,                                 //用户兑换时间

			})
		}
		if len(ret.List) < int(e.taskParams.PageSize) {
			break
		}
	}
	nums = k
	_ = e.writer.Flush()
	return

}

// VIP虚拟卡导出设置表头
func (e *VirtualCardExport) SetSheetName() {
	nameList := []interface{}{
		"批次号", "卡模版Id", "所属组织", "卡号", "卡状态", "卡券生成时间", "卡券到期时间", "兑换用户手机号", "兑换用户id", "用户兑换时间",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e *VirtualCardExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("VIP虚拟卡(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu1(e.F, fileName)
}

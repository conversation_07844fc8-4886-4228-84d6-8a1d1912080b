package models

import (
	"time"
)

type RefundOrderLogistics struct {
	Id               int       `xorm:"not null pk autoincr comment('id') INT(11)"`
	RefundSn         string    `xorm:"not null default '''' comment('退款单号') VARCHAR(50)"`
	ExpressNo        string    `xorm:"not null comment('物流单号') VARCHAR(50)"`
	ExpressCompanyId int       `xorm:"not null comment('物流公司ID') INT(11)"`
	CreateTime       time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime       time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME"`
}

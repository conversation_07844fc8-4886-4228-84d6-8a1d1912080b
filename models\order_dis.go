package models

import (
	"time"
)

type OrderDis struct {
	Id             int       `xorm:"not null pk autoincr INT(10)"`
	OrderId        int       `xorm:"not null comment('order_product.id') INT(11)"`
	ParentOrderSn  string    `xorm:"default 'NULL' comment('父单号order_main.parent_order_sn') VARCHAR(50)"`
	OrderSn        string    `xorm:"not null default '''' comment('order_product.order_sn') index VARCHAR(50)"`
	ProductId      string    `xorm:"default 'NULL' comment('商品id') VARCHAR(50)"`
	SkuId          string    `xorm:"default 'NULL' comment('商品skuid') VARCHAR(50)"`
	ProductName    string    `xorm:"default 'NULL' comment('商品名称') VARCHAR(200)"`
	Image          string    `xorm:"default 'NULL' comment('商品图片') VARCHAR(5000)"`
	IsVirtual      int       `xorm:"default 0 comment('是否是虚拟订单0默认实物订单，1虚拟订单') TINYINT(1)"`
	VerifyNum      int       `xorm:"default 0 comment('虚拟订单已核销数量') INT(11)"`
	DisMemberId    string    `xorm:"default 'NULL' comment('分销会员ID') VARCHAR(50)"`
	PayGoodsAmount int       `xorm:"default NULL comment('已支付金额(分)') INT(10)"`
	RefundAmount   int       `xorm:"default NULL comment('退款金额(分)') INT(10)"`
	DisCommisRate  int       `xorm:"default NULL comment('分销佣金比例，单位%') INT(10)"`
	Commission     int       `xorm:"not null default 0 comment('佣金(分)') INT(11)"`
	Status         int       `xorm:"not null default 0 comment('状态 0未结算 1已结算') TINYINT(4)"`
	DisAt          time.Time `xorm:"default 'NULL' comment('结算时间') DATETIME"`
	CreateTime     time.Time `xorm:"default 'current_timestamp()' comment('添加时间') DATETIME"`
	UpdateTime     time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

package services

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"order-center/dto"
	"order-center/models"
	"order-center/pkg/http/miniprogram"
	"order-center/proto/dac"
	"order-center/proto/et"
	"order-center/proto/ic"
	"order-center/proto/oc"
	"order-center/utils"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc/metadata"

	"github.com/go-redis/redis"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

type OrderService struct {
	CommonService
}

const (
	OrderAppChannelRedisKey = "order-center:order:app-channel:"
	OrderTypePickupOrder    = "999" //特殊需求下的产物。代表查询社区拼团的订单，自提点大于0的部分（pickup_station_id）
)

// 根据订单号获取appChannel
func (o *OrderService) GetOrderAppChannel(ctx context.Context, req *oc.GetOrderAppChannelReq) (*oc.GetOrderAppChannelRes, error) {

	out := new(oc.GetOrderAppChannelRes)
	out.Code = 200
	out.Message = "Success"
	conn := GetDBConn()
	appChannel := 0
	_, err := conn.SQL("SELECT  app_channel  FROM `dc_order`.`order_main`  WHERE old_order_sn=?", req.OrderSn).Get(&appChannel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error("GetOrderAppChannel", kit.JsonEncode(req), err)
		return nil, err
	}
	out.Data = &oc.OrderAppChannelData{AppChannel: int32(appChannel)}
	return out, nil
}

// QueryAppChannelByOrderSn
// 根据订单号查询订单的appChannel
// 饿了么版本添加，该版本区别于GetOrderAppChannel的是在查询的地方更新缓存 并且只返回appChannel
// todo 后期修改美团时 删除GetOrderAppChannel方法 全部改用本方法
func (o *OrderService) QueryAppChannelByOrderSn(ctx context.Context, req *oc.QueryAppChannelByOrderSnReq) (*oc.QueryAppChannelByOrderSnRes, error) {

	out := new(oc.QueryAppChannelByOrderSnRes)
	out.Code = 200
	out.Message = "Success"
	glog.Info(req.OrderSn, "查询订单app_channel QueryAppChannelByOrderSn", req)
	if req.OrderSn == "" {
		out.Code = 400
		out.Message = "参数非法"
		return out, nil
	}

	conn := GetDBConn()
	var appChannel int32
	has, err := conn.SQL("SELECT  app_channel  FROM `dc_order`.`order_main`  WHERE old_order_sn=?", req.OrderSn).Get(&appChannel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error("QueryAppChannelByOrderSn 查词出错", kit.JsonEncode(req), err)
		return out, err
	}
	if !has {
		out.Code = 400
		out.Message = "未查询到订单数据"
		glog.Info("QueryAppChannelByOrderSn 未查询到订单数据", kit.JsonEncode(req), err)
		return out, nil
	}
	//如果为0 表示是瑞鹏的老订单 一定是属于数据瑞鹏的数据
	if appChannel == 0 {
		appChannel = 1
	}
	out.AppChannel = appChannel

	redisConn := GetRedisConn()
	key := OrderAppChannelRedisKey + req.OrderSn
	redisConn.SetNX(key, appChannel, 24*time.Hour) //24小时
	glog.Info(req.OrderSn, "查询订单app_channel返回参数", req)
	return out, nil
}

func (o *OrderService) GetTodayOrderSum(ctx context.Context, req *oc.GetTodayOrderSumReq) (*oc.GetTodayOrderSumRes, error) {
	out := new(oc.GetTodayOrderSumRes)
	out.Code = 200
	out.Message = "Success"
	conn := GetDBConn()
	orderSum := oc.TodayOrderSumData{}

	sql := `SELECT 
		SUM(GiveOrderAmountSum) give_order_amount_sum,SUM(GiveOrderNumSum) give_order_num_sum,SUM(PaymentAmountSum) payment_amount_sum,SUM(PayedNumSum) payed_num_sum 
		FROM 
		(
			SELECT SUM(a.total) AS GiveOrderAmountSum,COUNT(*) AS GiveOrderNumSum,0 PaymentAmountSum,0 PayedNumSum 
			FROM dc_order.order_main a INNER JOIN dc_order.order_detail b ON a.order_sn =b.order_sn 
			WHERE a.channel_id IN(1,5,9) AND a.order_type!=8 AND a.is_push_tencent=1 AND
			((a.parent_order_sn = '' AND b.split_order_result=0) OR (a.parent_order_sn != '' AND b.split_order_result=1))
			AND a.create_time between ? AND ?  
		UNION  
			SELECT 0 GiveOrderAmountSum,0 GiveOrderNumSum,SUM(a.total)AS PaymentAmountSum,COUNT(*) AS PayedNumSum 
			FROM dc_order.order_main a INNER JOIN dc_order.order_detail b ON a.order_sn = b.order_sn 
			WHERE a.channel_id IN(1,5,9) AND a.order_type!=8 AND a.is_push_tencent=1 AND
			((a.parent_order_sn = '' AND b.split_order_result=0) OR (a.parent_order_sn != '' AND b.split_order_result=1))
			AND a.pay_time between ? AND ? 
		) ab;`
	_, err := conn.SQL(sql, req.StartTime, req.StopTime, req.StartTime, req.StopTime).Get(&orderSum)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		glog.Error("GetTodayOrderSum", kit.JsonEncode(req), err)
		return nil, err
	}

	out.OrderSum = &orderSum
	return out, nil
}

// OrderPayCompleteTemporary 临时提供修改完成订单的接口
func (o OrderService) OrderPayCompleteTemporary(ctx context.Context, params *oc.GetOneOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("临时提供修改完成订单的接口请求参数：", params)
	out := new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	conn := GetDBConn()
	orderModel := models.OrderMain{}
	_, err := conn.Where("order_sn = ?", params.OrderSn).Get(&orderModel)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error(err)
		return out, nil
	}

	if orderModel.Id == 0 {
		out.Message = "查询不到订单信息"
		glog.Error("查询不到订单信息:" + params.OrderSn)
		return out, nil
	}

	if orderModel.OrderStatus != 10 {
		out.Message = "该订单不是未付款订单"
		return out, nil
	}

	orderModel.OrderStatus = 20
	_, err1 := conn.Where("order_sn = ?", orderModel.OrderSn).Update(orderModel)
	if err1 != nil {
		out.Message = "修改错误"
		return out, nil
	}

	//记录订单流转日志——已支付
	SaveOrderLog([]*models.OrderLog{{
		OrderSn: orderModel.OrderSn,
		LogType: models.OrderLogPayedOrder,
	}})

	return out, nil
}

// AfterApplyOrder 售后订单申请
func (o *OrderService) AfterApplyOrder(ctx context.Context, params *oc.AfterApplyOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("取消未支付订单请求参数：", params, ", ", kit.GetTimeNow())
	out := new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	o.orderMain = GetOrderMainByOrderSn(params.OrderSn, "id,old_order_sn,channel_id")
	if o.orderMain.Id == 0 {
		glog.Error("查询不到订单信息:" + params.OrderSn)
		out.Message = "查询不到订单信息"
		return out, nil
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()
	o.session.Begin()

	id := ""
	out, id = o.SaveRefundOrder(params)
	if out.Code != 200 {
		o.session.Rollback()
		return out, nil
	}
	if params.OrderSource == 1 && o.orderMain.OrderType != 21 {
		glog.Info("取消未支付订单请求管易方法：", kit.GetTimeNow())
		model := SetAfterOrderRequest(params)
		omsService := OmsService{}
		grpcRes, err := omsService.AfterOrderSynchronizeToOms(ctx, model)
		glog.Info("取消未支付订单请求管易方法结束时间：", kit.GetTimeNow())
		if err != nil {
			o.session.Rollback()
			out.Code = 400
			out.Message = "调用oms接口失败" + err.Error()
			glog.Error("调用oms接口失败:" + params.OrderSn + ", err: " + err.Error())
			return out, nil
		}
		if grpcRes.Code != 0 {
			o.session.Rollback()
			out.Code = grpcRes.Code
			out.Message = grpcRes.Message

			glog.Error("调用oms接口失败:" + out.Message)
			return out, nil
		}
	}
	if params.OrderSource == 2 {
		exchange := "ordercenter"
		key := "dc-sz-order-apply-after"
		res := utils.PublishRabbitMQ(key, id, exchange)
		isPush := 1
		if res == false {
			o.session.Rollback()
			isPush = 0
			out.Code = 400
			out.Message = "推送队列失败"
			return out, nil
		}

		mqInfo := &models.MqInfo{
			Exchange: exchange,
			Quene:    key,
			Content:  id,
			Ispush:   isPush,
		}
		_, err := o.session.Insert(mqInfo)
		if err != nil {
			o.session.Rollback()
			out.Code = 400
			out.Message = "实例化队列失败"
			return out, nil
		}
	}

	o.session.Commit()

	// 发布退货扣减积分mq
	IntegralOperation(params.RefundSn, false)
	glog.Info("取消未支付订单请求方法结束时间：", kit.GetTimeNow())
	return out, nil
}

// CancelUnpaidOrder 电商取消未支付订单 orderId为父订单号
func (o OrderService) CancelUnpaidOrder(ctx context.Context, params *oc.CancelUnpaidOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("电商取消未支付订单请求参数：", params)
	out := new(oc.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	var orders []*models.OrderMain
	err := o.session.Where("old_order_sn = ? AND channel_id IN(5)", params.OrderId).Find(&orders)
	if err != nil {
		glog.Error("取消未支付父订单查询错误:", err)
		out.Code = 400
		out.Message = "取消未支付父订单查询错误"
		out.Error = err.Error()
		return out, nil
	}

	if len(orders) == 0 {
		glog.Error("取消未支付父订单查询为空:", params.OrderId)
		out.Code = 400
		out.Message = "取消未支付父订单查询为空"
		return out, nil
	}

	o.session.Begin()

	for _, orderMain := range orders {
		if orderMain.OrderStatus != 10 {
			o.session.Rollback()
			glog.Error("取消未支付订单查询错误:当前订单不能取消", orderMain.OrderSn)
			out.Code = 400
			out.Message = "当前订单不能取消。"
			return out, nil
		}

		o.orderMain = orderMain
		//电商取消未支付订单
		err = o.ShopNoPayCancel()
		if err != nil {
			o.session.Rollback()
			glog.Error("电商取消未支付订单失败：", orderMain.OrderSn, " ", err.Error())
			out.Code = 400
			out.Message = "更新订单状态失败。"
			out.Error = err.Error()
			return out, nil
		}

	}

	err = o.session.Commit()
	if err != nil {
		out.Code = 400
		out.Message = "更新订单状态失败。"
		out.Error = err.Error()
		return out, nil
	}

	if len(orders) > 0 {
		glog.Info("PushOrderStatusToTencent:" + params.OrderId + ":CancelUnpaidOrder")
		go PushOrderStatusToTencent(orders[0].OldOrderSn, 0)
	}

	glog.Info("电商取消未支付订单执行成功：", params.OrderId)
	return out, nil
}

// 支付mq通知后 订单查询
func (o OrderService) QueryOrder(ctx context.Context, params *oc.QueryOrderRequest) (*oc.QueryOrderResponse, error) {
	out := oc.QueryOrderResponse{Code: 200}

	orderModel := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.invoice")
	if orderModel.Id == 0 {
		out.Code = 400
		out.Message = "订单不存在！"
		return &out, nil
	}
	//orderModel := models.Order{}
	//ok, _ := db.SQL("select a.*,b.invoice from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.order_sn=?", params.OrderSn).Get(&orderModel)
	//if !ok {
	//	out.Code = 400
	//	out.Message = "订单不存在！"
	//	return &out, nil
	//}
	out.OrderModel = &oc.OrderModel{
		OrderId:          cast.ToString(orderModel.OrderMain.Id),
		OldOrderSn:       orderModel.OldOrderSn,
		OrderSn:          orderModel.OrderMain.OrderSn,
		OrderStatus:      orderModel.OrderStatus,
		ShopName:         orderModel.ShopName,
		MemberId:         orderModel.MemberId,
		MemberName:       orderModel.MemberName,
		MemberTel:        orderModel.MemberTel,
		ReceiverName:     orderModel.ReceiverName,
		ReceiverState:    orderModel.ReceiverState,
		ReceiverCity:     orderModel.ReceiverCity,
		ReceiverDistrict: orderModel.ReceiverDistrict,
		ReceiverAddress:  orderModel.ReceiverAddress,
		ReceiverPhone:    orderModel.ReceiverPhone,
		Privilege:        orderModel.Privilege,
		PayType:          orderModel.PayType,
		ReceiverMobile:   orderModel.ReceiverMobile,
		//GjpStatus:        orderModel.GjpStatus,
		Total:      orderModel.Total,
		GoodsTotal: orderModel.GoodsTotal,
		IsPay:      orderModel.IsPay,
		CreateTime: kit.GetTimeNow(orderModel.CreateTime),
		PayTime:    kit.GetTimeNow(orderModel.PayTime),
		OrderType:  orderModel.OrderType,
		Freight:    orderModel.Freight,
		Source:     orderModel.Source,
		Invoice:    orderModel.Invoice,
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()
	o.orderMain = orderModel.OrderMain

	orderProducts := o.GetOrderProduct()
	//var orderProducts []models.OrderProduct
	//o.session.Where("order_sn = ?", params.OrderSn).Find(&orderProducts)

	for _, i2 := range orderProducts {
		product := oc.OrderProductModel{
			OrderSn:      i2.OrderSn,
			Sku:          i2.SkuId,
			ProductId:    i2.ProductId,
			ProductName:  i2.ProductName,
			BarCode:      i2.BarCode,
			Price:        i2.DiscountPrice,
			Number:       i2.Number,
			Specs:        i2.Specs,
			PaymentTotal: i2.PaymentTotal,
			Privilege:    i2.Privilege,
			Freight:      i2.Freight,
			MarkingPrice: i2.MarkingPrice,
		}
		out.OrderModel.OrderProductModel = append(out.OrderModel.OrderProductModel, &product)
	}

	return &out, nil
}

// 往MQ里面写数据
func (o *OrderService) InsertMQ(ctx context.Context, params *oc.MqContent) (*oc.BaseResponse, error) {
	db := GetDBConn()
	out := oc.BaseResponse{Code: 200, Message: ""}
	_, err := db.Insert(models.MqInfo{
		Exchange: params.Exchange,
		Quene:    params.Quene,
		Content:  params.Content,
		Ispush:   0,
		Lastdate: time.Time{},
	})
	if err != nil {
		glog.Error("MQ写入数据失败！" + err.Error())
		out.Code = 400
		out.Message = "订单查询失败"
		out.Error = "订单查询失败"
		return &out, err
	}
	return &out, nil
}

// 电商释放库存
func (o *OrderService) ReleaseStock(ctx context.Context, params *oc.ReleaseStockRequest) (*oc.BaseResponse, error) {
	glog.Infof("ReleaseStock 释放库存调用！ ordersn:%s %s", params.OrderId, kit.JsonEncode(params))
	out := oc.BaseResponse{Code: 200, Message: "释放成功！"}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	o.orderMain = new(models.OrderMain)
	ok, err := o.session.Where("order_sn=? or old_order_sn=?", params.OrderId, params.OrderId).Get(o.orderMain)
	if err != nil {
		glog.Error(params.OrderId, ", 电商释放库存查询订单失败, ", err)
		out.Code = 400
		out.Message = "订单查询失败"
		out.Error = err.Error()
		return &out, nil
	}
	if !ok {
		glog.Errorf("ReleaseStock 订单不存在！ ordersn:%s", params.OrderId)
		out.Code = 400
		out.Message = "订单查询失败"
		out.Error = "订单不存在！"
		return &out, nil
	}

	var goodsInfo []*ic.OrderGoodsInfo
	for _, i2 := range params.Detail {
		goodsInfo = append(goodsInfo, &ic.OrderGoodsInfo{
			GoodsId: i2.Sku,
			Number:  i2.Num,
		})
	}
	o.FreedStock(goodsInfo)

	return &out, nil
}

// 管易订单查询接口
func (o *OrderService) GyOrderQuery(ctx context.Context, params *oc.GyOrderQueryRequest) (*oc.GyOrderQueryResponse, error) {
	out := oc.GyOrderQueryResponse{Code: 200, Message: "ok"}
	dbConn := GetDBConn()

	var orderModel []models.Order
	dbConn.SQL("select a.*,b.pay_type,b.buyer_memo,b.seller_memo from order_main a inner join order_detail b on a.order_sn=b.order_sn where order_status = 20 and source = 2 and pay_time >= ? limit ?", params.Datetime, params.Limit).Find(&orderModel)

	for _, i2 := range orderModel {
		var products []models.OrderProduct
		var GyOrderProducts []*oc.GyOrderProduct
		dbConn.Where("order_sn = ?", i2.OrderSn).Find(&products)
		for _, i3 := range products {
			SplitArr := strings.Split(i3.SkuId, "@")
			if len(SplitArr) < 2 {
				return nil, errors.New("sku格式错误！" + i3.SkuId)
			}
			ItemCode := SplitArr[0]
			SkuCode := SplitArr[1]
			var isGift int32
			if i3.DiscountPrice == 0 {
				isGift = 1
			}
			GyOrderProduct := oc.GyOrderProduct{
				OrderSn:      i3.OrderSn,
				ThirdSpuId:   ItemCode,
				ThirdSkuId:   SkuCode,
				ProductName:  i3.ProductName,
				Price:        i3.DiscountPrice,
				Number:       i3.Number,
				Specs:        i3.Specs,
				PaymentTotal: i3.PaymentTotal,
				Privilege:    i3.Privilege,
				Freight:      i2.Freight,
				IsGift:       isGift,
			}
			GyOrderProducts = append(GyOrderProducts, &GyOrderProduct)
		}

		order := oc.GyOrderModel{
			OrderSn:          i2.OrderSn,
			ShopName:         i2.ShopName,
			MemberName:       i2.MemberName,
			ReceiverName:     i2.ReceiverName,
			ReceiverState:    i2.ReceiverState,
			ReceiverCity:     i2.ReceiverCity,
			ReceiverDistrict: i2.ReceiverDistrict,
			ReceiverAddress:  i2.ReceiverAddress,
			ReceiverPhone:    i2.ReceiverPhone,
			Privilege:        i2.Privilege,
			//PayType:          i2.PayType,
			ReceiverMobile: i2.ReceiverMobile,
			Total:          i2.Total,
			GoodsTotal:     i2.GoodsTotal,
			CreateTime:     kit.GetTimeNow(i2.CreateTime),
			PayTime:        kit.GetTimeNow(i2.PayTime),
			OrderType:      i2.OrderType,
			Freight:        i2.Freight,
			WarehouseCode:  i2.WarehouseCode,
			BuyerMemo:      i2.BuyerMemo,
			SellerMemo:     i2.SellerMemo,
			OrderProduct:   GyOrderProducts,
		}
		out.Details = append(out.Details, &order)
	}

	return &out, nil
}

//快递订单信息同步
//TODO: 待删除
//func (o *OrderService) Del_GySyncOrderExpress(ctx context.Context, params *oc.GySyncOrderExpressRequest) (*oc.BaseResponse, error) {
//	out := oc.BaseResponse{Code: 200, Message: "ok"}
//	session := GetDBConn().NewSession()
//	defer session.Close()
//	session.Begin()
//
//	for _, i2 := range params.GySyncOrderExpress {
//		orderExpress := models.OrderExpress{
//			OrderId:     i2.OrderId,
//			SubOrderId:  i2.SubOrderId,
//			ExpressNo:   i2.ExpressNo,
//			ExpressName: i2.ExpressName,
//			ExpressCode: i2.ExpressCode,
//			CreateDate:  time.Now(),
//		}
//
//		_, err := session.Insert(&orderExpress)
//		if err != nil {
//			out.Code = 400
//			out.Message = "保存失败order id ： " + i2.OrderId
//			out.Error = err.Error()
//			session.Rollback()
//			return &out, err
//		}
//		var orderExpressProducts []models.OrderExpressProduct
//		for _, i3 := range i2.OrderProducts {
//			orderExpressProduct := models.OrderExpressProduct{
//				ExpressId:   orderExpress.Id,
//				ProductId:   i3.ProductId,
//				ProductName: i3.ProductName,
//				SkuId:       i3.ThirdSpuId + "@" + i3.ThirdSkuId,
//				SpecName:    i3.SpecName,
//				ThirdSpuId:  i3.ThirdSpuId,
//				ThirdSkuId:  i3.ThirdSkuId,
//				Count:       int(i3.Count),
//				CreateDate:  time.Now(),
//			}
//			orderExpressProducts = append(orderExpressProducts, orderExpressProduct)
//		}
//
//		_, err = session.Insert(orderExpressProducts)
//		if err != nil {
//			out.Code = 400
//			out.Message = "保存明细失败order id ： " + i2.OrderId
//			out.Error = err.Error()
//			session.Rollback()
//			return &out, err
//		}
//	}
//
//	err := session.Commit()
//	if err != nil {
//		out.Code = 400
//		out.Message = "数据保存失败"
//		out.Error = err.Error()
//		return &out, err
//	}
//	return &out, nil
//}

// 管易查询退款订单请求
func (o OrderService) GyQueryRefundOrder(ctx context.Context, params *oc.GyQueryRefundOrderRequest) (*oc.GyQueryRefundOrderResponse, error) {
	out := oc.GyQueryRefundOrderResponse{Code: 200, Message: "ok"}
	dbConn := GetDBConn()

	var orderModel []models.RefundOrder
	dbConn.Where("order_source = 2 and create_time >= ?", params.Datetime).Limit(int(params.Limit)).Find(&orderModel)

	var GyRefundOrder []*oc.GyRefundOrder
	for _, i2 := range orderModel {
		var products []models.RefundOrderProduct
		dbConn.Where("refund_sn = ?", i2.RefundSn).Find(&products)

		var GyRefundOrderProducts []*oc.GyRefundOrderGoods
		for _, i3 := range products {
			GyRefundOrderProduct := oc.GyRefundOrderGoods{
				Refundorderid: i3.RefundSn,
				Goodsid:       i3.SkuId,
				Quantity:      i3.Quantity,
				Refundamount:  i3.RefundAmount,
				Itemcode:      i3.Itemcode,
				Skucode:       i3.Skucode,
				Barcode:       i3.Barcode,
			}
			GyRefundOrderProducts = append(GyRefundOrderProducts, &GyRefundOrderProduct)
		}

		order := oc.GyRefundOrder{
			Id:              cast.ToString(i2.Id),
			Refundsn:        i2.RefundSn,
			Ordersn:         i2.OrderSn,
			Createtime:      kit.GetTimeNow(i2.CreateTime),
			Refundtypesn:    i2.RefundTypeSn,
			Refundtype:      i2.RefundType,
			Discountamount:  i2.DiscountAmount,
			Postfee:         i2.Freight,
			Refundreason:    i2.RefundReason,
			Warehouseincode: i2.WarehouseCode,
			Expressname:     i2.ExpressName,
			Expressnum:      i2.ExpressNum,
			Refundamount:    i2.RefundAmount,
			Ordersource:     i2.OrderSource,
			RefundState:     i2.RefundState,
			OrderProduct:    GyRefundOrderProducts,
		}
		GyRefundOrder = append(GyRefundOrder, &order)
	}
	out.Details = GyRefundOrder
	return &out, nil
}

// 阿闻管家预订单列表查询
func (o OrderService) BookingOrderList(ctx context.Context, params *oc.BookingOrderRequest) (*oc.BookingOrderResponse, error) {
	//glog.Info("预订单列表查询参数：", params)
	out := oc.BookingOrderResponse{Code: 200, Message: "ok"}
	conn := GetDBConn()

	if params.Pageindex <= 0 || params.Pagesize <= 0 {
		glog.Error("预订单列表查询参数错误: ", params)
		out.Code = 400
		out.Message = "分页参数错误"
		return &out, nil
	}
	if len(params.Shopids) == 0 {
		out.Code = 400
		out.Message = "无门店权限"
		return &out, nil
	}
	sqlParams := make([]interface{}, 0)
	var sql strings.Builder
	var sqlCount strings.Builder
	sql.WriteString(`
		select a.id, a.order_sn,a.confirm_time, a.order_status_child, a.shop_id, a.shop_name, a.goods_total, a.service_charge, 
		a.deliver_time, a.order_type, a.old_order_sn gy_order_sn, a.receiver_name, a.create_time, a.receiver_mobile, a.delivery_type,
		a.order_status, a.total, a.privilege, a.receiver_phone,  a.user_agent, a.channel_id, a.pay_mode, a.pay_sn, a.pay_time,
		b.expected_time, b.is_picking, b.accept_time, b.picking_time, b.extras, b.push_delivery, b.push_third_order, 
		b.push_delivery_reason, b.push_third_order_reason, b.performance_staff_name performance_staff,a.actual_receive_total
		from order_main a
		inner join order_detail b on a.order_sn=b.order_sn
		where if(a.delivery_type = 3 and a.order_status not in (0,30), a.is_pay = 1, 1=1) 
		and a.order_type in (2, 3) and a.order_status not in (0, 30) and a.shop_id in (
	`)
	for i := 0; i < len(params.Shopids); i++ {
		sql.WriteString("?")
		if i < len(params.Shopids)-1 {
			sql.WriteString(",")
		}
		sqlParams = append(sqlParams, params.Shopids[i])
	}
	sql.WriteString(") ")

	sqlCount.WriteString(`
		select count(1) 
		from order_main a 
		inner join order_detail b on a.order_sn=b.order_sn
		where if(a.delivery_type = 3 and a.order_status not in (0,30), a.is_pay = 1, 1=1) 
		and a.order_type in (2, 3) and a.order_status not in (0, 30) and a.shop_id in (
	`)
	for i := 0; i < len(params.Shopids); i++ {
		sqlCount.WriteString("?")
		if i < len(params.Shopids)-1 {
			sqlCount.WriteString(",")
		}
	}
	sqlCount.WriteString(") ")

	if params.ChannelId > 0 {
		sql.WriteString("and a.channel_id = ? ")
		sqlCount.WriteString("and channel_id = ? ")
		sqlParams = append(sqlParams, params.ChannelId)
	}
	if params.DeliveryTime == 1 {
		sql.WriteString("and DATE_ADD(b.expected_time, INTERVAL -75 MINUTE) <= NOW() and b.expected_time >= NOW() ")
		sqlCount.WriteString("and DATE_ADD(b.expected_time, INTERVAL -75 MINUTE) <= NOW() and b.expected_time >= NOW() ")
	}

	if _, err := conn.SQL(sqlCount.String(), sqlParams...).Get(&out.TotalCount); err != nil {
		err = errors.New("预订单查询订单总数错误, err: " + err.Error())
		glog.Error(err)
		return nil, err
	}
	if out.TotalCount == 0 {
		return &out, nil
	}

	sql.WriteString(`
		GROUP BY a.id 
		ORDER BY if(a.order_status_child=20101, 0, 1), if(b.push_third_order = 0 and a.order_status=20, 0, 1), a.create_time DESC 
		LIMIT ?, ?
	`)
	sqlParams = append(sqlParams, int((params.Pageindex-1)*params.Pagesize))
	sqlParams = append(sqlParams, int(params.Pagesize))
	if err := conn.SQL(sql.String(), sqlParams...).Find(&out.Details); err != nil {
		glog.Error("订单列表查询预订单查询错误, err: " + err.Error())
		out.Code = 400
		out.Message = "预订单查询错误, err: " + err.Error()
		out.Error = err.Error()
		return &out, nil
	}

	var orderSnList []string
	for _, v := range out.Details {
		if v.OrderType == 3 {
			v.ReceiverName = ""
		}
		if v.IsPicking == 1 {
			v.PickingTime = v.PickingTime[5:16]
		}

		if len(v.ExpectedTime) > 0 {
			v.ExpectedTime = v.ExpectedTime[5:16]
		}
		orderSnList = append(orderSnList, v.OrderSn)
	}

	var goodsLsit []oc.OrderProductModel
	var goodsParams = make([]interface{}, 0)
	var sqlGoods strings.Builder
	sqlGoods.WriteString(`
		select order_sn, sku_id sku, product_name, payment_total, number, specs, image, 
		marking_price,discount_price price from order_product where order_sn in (
	`)
	sqlGoods.WriteString("")
	for i := 0; i < len(orderSnList); i++ {
		sqlGoods.WriteString("?")
		if i < len(orderSnList)-1 {
			sqlGoods.WriteString(",")
		}
		goodsParams = append(goodsParams, orderSnList[i])
	}
	sqlGoods.WriteString(")")
	if err := conn.SQL(sqlGoods.String(), goodsParams...).Find(&goodsLsit); err != nil {
		glog.Error("订单列表查询预订单查询商品信息错误, err: ", err.Error())
		out.Code = 400
		out.Message = "预订单查询商品信息失败"
		return &out, nil
	}

	for _, order := range out.Details {
		order.IsPartButton = 1
		//是否显示部分退款按钮
		if len(order.ConfirmTime) > 0 {
			confirmTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, order.ConfirmTime, time.Local)
			d, _ := time.ParseDuration("-24h")
			d1 := time.Now().Add(d)
			if confirmTime.Before(d1) {
				order.IsPartButton = 0
			}
		}

		var OriginGoodsTotal int32 //原商品总价
		for _, goods := range goodsLsit {
			if order.OrderId != goods.OrderId {
				continue
			}
			productModel := &oc.OrderProductModel{
				Id:           goods.Id,
				OrderId:      goods.OrderId,
				OrderSn:      goods.OrderSn,
				Sku:          goods.Sku,
				ProductId:    goods.ProductId,
				ProductName:  goods.ProductName,
				BarCode:      goods.BarCode,
				Price:        goods.Price,
				Number:       goods.Number,
				Specs:        goods.Specs,
				PaymentTotal: goods.PaymentTotal,
				Privilege:    goods.Privilege,
				Freight:      goods.Freight,
				Image:        goods.Image,
				MarkingPrice: goods.MarkingPrice,
			}
			OriginGoodsTotal = OriginGoodsTotal + productModel.Price*productModel.Number
			order.Orderproductmodel = append(order.Orderproductmodel, productModel)
		}
		//前端的商品总额是不包含所有其他优惠运费服务费的，而后端的商品总额是减去运费的。所以要加上
		order.GoodsTotal = OriginGoodsTotal
	}

	return &out, nil
}

func (o OrderService) PrintOrderDetail(orderId string) (err error) {
	glog.Info("订单打印订单参数：", orderId)
	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	order := GetOrderByOrderSn(orderId, "order_main.*, buyer_memo, packing_cost, expected_time, extras, pickup_code")
	if order.Id == 0 {
		return errors.New("订单打印查询无订单信息")
	}

	o.orderMain = order.OrderMain

	var setup models.StoreBusinessSetup
	has, err := o.session.Where("finance_code = ?", order.ShopId).Get(&setup)
	if err != nil {
		return fmt.Errorf("查询店铺打印设置失败: %v", err)
	}
	if !has {
		return fmt.Errorf("店铺打印设置不存在")
	}
	//设置的本地打印不需要打印
	if setup.WhatPrint == 1 {
		return nil
	}

	goodsList := o.GetOrderProduct()
	if len(goodsList) == 0 {
		glog.Error("订单打印查询商品信息错误, 订单商品查询失败")
		return errors.New("订单打印查询商品信息失败，订单商品查询失败")
	}

	ShopSet, err := o.GetShopSet()
	if err != nil {
		return errors.New("查询店铺设置失败, " + err.Error())
	}

	var count int32
	_, err = o.session.SQL("select count(1) from `order_main` where (parent_order_sn = '' or parent_order_sn = order_sn) and channel_id = ? and shop_id=? and create_time between ? and ? ",
		order.ChannelId, order.ShopId, order.CreateTime.Format(kit.DATE_LAYOUT)+" 00:00:00", kit.GetTimeNow(order.CreateTime)).Get(&count)
	if err != nil {
		glog.Error(o.orderMain.OrderSn, ", 订单打印查询订单号码错误, ", err.Error())
		return errors.New("订单打印查询订单号码失败")
	}
	if count == 0 {
		return errors.New("订单打印查询订单号码为0")
	}

	printOrder := oc.PrintBookingOrder{
		PrintExtent:     ShopSet.RetInfo.PrintExtent,
		OrderId:         order.OrderMain.OrderSn,                     // 订单编号
		ShopName:        order.ShopName,                              // 商铺名称
		Remark:          order.BuyerMemo,                             // 买家备注
		Name:            order.ReceiverName,                          // 客户姓名
		CreateTime:      order.CreateTime.Format("2006-01-02 15:04"), // 下单时间
		PackAmount:      order.PackingCost,                           // 包装费
		DeliveryAmount:  order.Freight,                               // 配送费
		Amount:          order.PayAmount,                             // 支付金额
		ReductionAmount: order.Privilege,                             // 优惠金额
		PickupCode:      order.PickupCode,                            //取货码
		ChannelId:       order.ChannelId,                             //订单来源
		DeliveryType:    order.DeliveryType,                          //配送方式
		ReceiverAddress: order.ReceiverAddress,                       //收件地址
		Mobile:          order.ReceiverMobile,                        //客户手机
		Number:          count,                                       //订单号码
	}

	if order.DeliveryType == 3 {
		printOrder.IsFetch = 1                                               // 自取
		printOrder.FetchTime = order.ExpectedTime.Format("2006-01-02 15:04") // 自取时间
	}

	var goods *oc.PrintOrderGoods
	childGoods := map[string][]*oc.PrintOrderChildGoods{}
	for _, v := range goodsList {
		// 商品总数只计算子商品
		if v.ProductType == 1 || v.ProductType == 2 {
			printOrder.GoodsNumber += v.Number //商品总数
		}
		if len(v.ParentSkuId) > 0 {
			childGoods[v.ParentSkuId] = append(childGoods[v.ParentSkuId], &oc.PrintOrderChildGoods{
				GoodsName:   v.ProductName,
				Count:       v.Number,
				Amount:      v.DiscountPrice,
				Sku:         v.SkuId,
				Upc:         v.BarCode,
				ParentSkuId: v.ParentSkuId,
			})
		} else {
			printOrder.GoodsAmount += v.Number * v.DiscountPrice //商品总额
			goods = &oc.PrintOrderGoods{
				GoodsName:   v.ProductName,
				Count:       v.Number,
				Amount:      v.DiscountPrice,
				Sku:         v.SkuId,
				Upc:         v.BarCode,
				ParentSkuId: v.ParentSkuId,
			}
			printOrder.Goods = append(printOrder.Goods, goods)
		}
	}

	if len(printOrder.Goods) > 0 {
		for _, v := range printOrder.Goods {
			if _, ok := childGoods[v.Sku]; ok {
				v.ChildGoods = childGoods[v.Sku]
			}
		}
	}

	printOrder.ShopReductionAmount = "0.0"
	printOrder.MtReductionAmount = "0.0"

	orderPromotions := o.GetOrderPromotion()
	//拼装优惠信息
	for _, item := range orderPromotions {
		active := oc.OrderPrivilegeModel{}
		active.ActiveName = dto.OrderPrivilegeActiveType(item.PromotionType).String()
		if order.OrderMain.ChannelId == ChannelAwenId || order.OrderMain.OrgId == 6 {
			active.ActiveName = item.PromotionTitle //如果是阿闻到家，则取表里面的活动名
		}
		if active.ActiveName == "UNKNOWN" {
			active.ActiveName = item.PromotionTitle
		}
		active.ReduceFee = cast.ToString(item.PromotionFee)
		active.MtCharge = cast.ToString(item.PtCharge)
		active.PoiCharge = cast.ToString(item.PoiCharge)
		active.Remark = item.PromotionTitle

		printOrder.OrderPrivilegeModel = append(printOrder.OrderPrivilegeModel, &active)

		// 门店新客户立减
		if item.PromotionType == 22 {
			printOrder.ShopReductionAmount = cast.ToString(kit.FenToYuan(item.PoiCharge))
		}
		// 美团减免
		if item.PromotionType == 25 {
			printOrder.MtReductionAmount = cast.ToString(kit.FenToYuan(item.PtCharge))
		}
	}

	//开票配置读取redis中相关的值 v6.3.7修改  之前读取的是配置中心的invoice_enable参数
	//redisClient := GetRedisConn()

	//var invoiceEnable string
	//if order.ChannelId == ChannelMallId {
	//	invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_mall").Val()
	//} else {
	//	invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_o2o").Val()
	//}

	//if invoiceEnable == "1" {
	//	if canInvoice, err := CheckCanInvoiceByOrder(order.OrderMain); canInvoice && err == nil {
	//		envVersion := "release"
	//		if kit.IsDebug {
	//			envVersion = "trial"
	//		}
	//		// 获取开票小程序码
	//		//开票是否显示二维码
	//		invoiceQrCodeEnable := "1"
	//		if order.ChannelId != ChannelMallId {
	//			invoiceQrCodeEnable = redisClient.HGet("set:invoice_set", "invoice_qr_code_o2o").Val()
	//		}
	//		if invoiceQrCodeEnable == "1" {
	//			invoiceQrCode, err := miniprogram.AWen.GetWxaCodeUnLimit(map[string]interface{}{
	//				"scene": order.OrderMain.OrderSn + "_" + order.MemberTel,
	//				"width": 300,
	//				// "is_hyaline": true,
	//				"page":        "app/invoice/apply-form",
	//				"check_path":  !kit.IsDebug, // page 有数量上限（60000个）请勿滥用
	//				"env_version": envVersion,   // 仅发布后线上为正式版
	//			})
	//			if err == nil {
	//				printOrder.InvoiceQrCode = base64.StdEncoding.EncodeToString(invoiceQrCode)
	//			}
	//		}
	//	}
	//}

	//得到打印机信息
	var printerManage models.PrinterManage
	if _, err = o.session.Table("eshop.printer_manage").Where("store_id=?", order.OrderMain.ShopId).Get(&printerManage); err != nil {
		glog.Error(o.orderMain.OrderSn, ", 订单打印查询打印机信息错误, ", err.Error())
		return errors.New("订单打印查询打印机信息错误失败")
	}

	if printerManage.PrinterSn == "" {
		glog.Error(o.orderMain.OrderSn, ", 该门店未配置打印机")
		return errors.New("该门店未配置打印机")
	}

	feieYunService := utils.FeieYunService{}
	content := "<BR><CB>***给拣货员***</CB><BR>"
	content += fmt.Sprintf("<B><BOLD>#%d</BOLD></B>   %s<BR>", printOrder.Number, PrintChannelName[printOrder.ChannelId])
	content += fmt.Sprintf("%s<BR>", printOrder.ShopName)
	content += fmt.Sprintf("<C><BC128_C>%s</BC128_C></C><BR>", order.OrderMain.OldOrderSn)
	content += fmt.Sprintf("<C>订单编号：%s</C>", order.OrderMain.OldOrderSn)
	content += "--------------------------------<BR>"
	content += fmt.Sprintf("备注：%s<BR>", printOrder.Remark)
	content += "--------------------------------<BR>"
	for _, goodsInfo := range printOrder.Goods {
		//58mm的机器,一行打印16个汉字,32个字
		nameArr := feieYunService.SplitStringByNum(goodsInfo.GoodsName, 16)
		for i := 0; i < len(nameArr); i++ {
			if i == 0 {
				content += fmt.Sprintf("%s   *%d   %s<BR>", nameArr[i], goodsInfo.Count, fmt.Sprintf("%0.2f", float64(goodsInfo.Amount)/100))
			} else {
				content += fmt.Sprintf("%s<BR>", nameArr[i])
			}
		}

		content += "货架码<BR>"
		content += fmt.Sprintf("SKU       %s<BR>", goodsInfo.Sku)
		content += fmt.Sprintf("UPC       %s<BR><BR>", goodsInfo.Upc)
	}

	content += fmt.Sprintf("商品总数            %d<BR><BR>", printOrder.GoodsNumber)
	content += fmt.Sprintf("<CB><BOLD>****#%s****</BOLD></CB><BR><BR><CUT>", printOrder.PickupCode)

	if err = feieYunService.PrintOrder(printerManage.PrinterSn, content); err != nil {
		glog.Error(o.orderMain.OrderSn, ", 请求飞鹅打印机接口失败：, ", err.Error())
		return errors.New("请求飞鹅打印机接口失败" + err.Error())
	}

	content = ""
	content = "<BR><CB>***商家联***</CB><BR>"
	content += fmt.Sprintf("<B><BOLD>#%d</BOLD></B>   %s<BR>", printOrder.Number, PrintChannelName[printOrder.ChannelId])
	content += fmt.Sprintf("%s<BR>", printOrder.ShopName)
	content += fmt.Sprintf("订单编号：%s<BR>", order.OrderMain.OldOrderSn)
	content += fmt.Sprintf("<C>-在线支付[%0.2f元]</C><BR>", float64(printOrder.Amount)/100)
	content += fmt.Sprintf("下单时间：%s<BR><BR>", printOrder.CreateTime)
	content += "为了保护隐私，顾客电话已隐藏，您可登录商家端查看或骑手端查看<BR><BR>"
	content += fmt.Sprintf("%s<BR>", utils.MobileReplaceWithStar(printOrder.Mobile))
	content += fmt.Sprintf("%s<BR>", printOrder.Name)
	content += fmt.Sprintf("%s<BR>", printOrder.ReceiverAddress)
	content += "--------------------------------<BR>"
	content += fmt.Sprintf("备注：%s<BR>", printOrder.Remark)
	content += "--------------------------------<BR>"
	for _, goodsInfo := range printOrder.Goods {
		//58mm的机器,一行打印16个汉字,32个字
		nameArr := feieYunService.SplitStringByNum(goodsInfo.GoodsName, 16)
		for i := 0; i < len(nameArr); i++ {
			if i == 0 {
				content += fmt.Sprintf("%s   *%d   %s<BR>", nameArr[i], goodsInfo.Count, fmt.Sprintf("%0.2f", float64(goodsInfo.Amount)/100))
			} else {
				content += fmt.Sprintf("%s<BR>", nameArr[i])
			}
		}

		content += "货架码<BR>"
		content += fmt.Sprintf("SKU       %s<BR>", goodsInfo.Sku)
		content += fmt.Sprintf("UPC       %s<BR><BR>", goodsInfo.Upc)
	}
	content += "-----------其他-------------<BR><BR>"
	content += fmt.Sprintf("包装费                     %0.2f<BR>", float64(printOrder.PackAmount)/100)
	content += fmt.Sprintf("配送费                     %0.2f<BR>", float64(printOrder.DeliveryAmount)/100)
	content += fmt.Sprintf("[商家减%s元]               %s<BR>", printOrder.ShopReductionAmount, printOrder.ShopReductionAmount)
	//活动信息
	for _, orderPrivilege := range printOrder.OrderPrivilegeModel {
		content += fmt.Sprintf("%s               %0.2f<BR>", orderPrivilege.ActiveName, cast.ToFloat64(orderPrivilege.ReduceFee)/100)
	}

	content += "--------------------------------<BR>"
	content += fmt.Sprintf("原价：                  %0.2f元<BR>", float64(printOrder.GoodsAmount)/100)
	content += "<BOLD>--------------------------------</BOLD><BR>"
	content += fmt.Sprintf("商品总数：               %d件<BR><BR>", printOrder.GoodsNumber)
	content += fmt.Sprintf("<CB><BOLD>****#%s****</BOLD></CB><BR><BR><BR><CUT>", printOrder.PickupCode)
	//content += "<B><BOLD>备注：虚拟商品请到店核销享受服务</BOLD></B><BR><BR><BR><CUT>"

	if err = feieYunService.PrintOrder(printerManage.PrinterSn, content); err != nil {
		glog.Error(o.orderMain.OrderSn, ", 请求飞鹅打印机接口失败：, ", err.Error())
		return errors.New("请求飞鹅打印机接口失败" + err.Error())
	}
	return nil
}

// 阿闻管家打印订单
func (o OrderService) PrintBookingOrder(ctx context.Context, params *oc.PrintBookingOrderRequest) (*oc.PrintBookingOrderResponse, error) {
	out := oc.PrintBookingOrderResponse{Code: 200, Message: "ok"}
	glog.Info("订单打印订单参数：", params)

	if len(params.OrderId) == 0 {
		out.Code = 400
		out.Message = "订单号为空"
		return &out, nil
	}

	order := GetOrderByOrderSn(params.OrderId, "order_main.*, buyer_memo, packing_cost, expected_time, extras, pickup_code")
	if order.Id == 0 {
		out.Code = 400
		out.Message = "订单打印查询无订单信息"
		return &out, nil
	}

	var setup models.StoreBusinessSetup
	has, err := GetDBConn().Where("finance_code = ?", order.ShopId).Get(&setup)
	if err != nil {
		return nil, fmt.Errorf("查询店铺打印设置失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("店铺打印设置不存在")
	}

	//如果是逍宠订单，则调用云打印
	if order.OrderMain.OrgId == 6 && setup.WhatPrint == 2 {
		err := o.PrintOrderDetail(params.OrderId)
		if err != nil {
			out.Code = 400
			out.Message = err.Error()
			return &out, nil
		}
		out.Code = 200
		out.Message = "打印成功"
		return &out, nil
	}

	o.orderMain = order.OrderMain

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	var count int32
	_, err = o.session.SQL("select count(1) from `order_main` where (parent_order_sn = '' or parent_order_sn = order_sn) and channel_id = ? and create_time between ? and ? ",
		order.ChannelId, order.CreateTime.Format(kit.DATE_LAYOUT)+" 00:00:00", kit.GetTimeNow(order.CreateTime)).Get(&count)
	if err != nil {
		glog.Error(o.orderMain.OrderSn, ", 订单打印查询订单号码错误, ", err.Error())
		out.Code = 400
		out.Message = "订单打印查询订单号码失败"
		out.Error = err.Error()
		return &out, nil
	}
	if count == 0 {
		out.Code = 400
		out.Message = "订单打印查询订单号码为0"
		return &out, nil
	}

	goodsList := o.GetOrderProduct()
	if len(goodsList) == 0 {
		glog.Error("订单打印查询商品信息错误, 订单商品查询失败")
		out.Code = 400
		out.Message = "订单打印查询商品信息失败，订单商品查询失败"
		out.Error = out.Message
		return &out, nil
	}

	ShopSet, err := o.GetShopSet()
	if err != nil {
		out.Code = 400
		out.Message = "查询店铺设置失败, " + err.Error()
		out.Error = err.Error()
		return &out, err
	}

	printOrder := oc.PrintBookingOrder{
		PrintExtent:     ShopSet.RetInfo.PrintExtent,
		OrderId:         order.OrderMain.OrderSn,                     // 订单编号
		ShopName:        order.ShopName,                              // 商铺名称
		Remark:          order.BuyerMemo,                             // 买家备注
		Name:            order.ReceiverName,                          // 客户姓名
		CreateTime:      order.CreateTime.Format("2006-01-02 15:04"), // 下单时间
		PackAmount:      order.PackingCost,                           // 包装费
		DeliveryAmount:  order.Freight,                               // 配送费
		Amount:          order.PayAmount,                             // 支付金额
		ReductionAmount: order.Privilege,                             // 优惠金额
		PickupCode:      order.PickupCode,                            //取货码
		ChannelId:       order.ChannelId,                             //订单来源
		DeliveryType:    order.DeliveryType,                          //配送方式
		ReceiverAddress: order.ReceiverAddress,                       //收件地址
		Mobile:          order.ReceiverMobile,                        //客户手机
		Number:          count,                                       //订单号码
	}

	if order.DeliveryType == 3 {
		printOrder.IsFetch = 1                                               // 自取
		printOrder.FetchTime = order.ExpectedTime.Format("2006-01-02 15:04") // 自取时间
	}

	var goods *oc.PrintOrderGoods
	childGoods := map[string][]*oc.PrintOrderChildGoods{}
	for _, v := range goodsList {
		// 商品总数只计算子商品
		if v.ProductType == 1 || v.ProductType == 2 {
			printOrder.GoodsNumber += v.Number //商品总数
		}
		if len(v.ParentSkuId) > 0 {
			childGoods[v.ParentSkuId] = append(childGoods[v.ParentSkuId], &oc.PrintOrderChildGoods{
				GoodsName:   v.ProductName,
				Count:       v.Number,
				Amount:      v.DiscountPrice,
				Sku:         v.SkuId,
				Upc:         v.BarCode,
				ParentSkuId: v.ParentSkuId,
			})
		} else {
			printOrder.GoodsAmount += v.Number * v.DiscountPrice //商品总额
			goods = &oc.PrintOrderGoods{
				GoodsName:   v.ProductName,
				Count:       v.Number,
				Amount:      v.DiscountPrice,
				Sku:         v.SkuId,
				Upc:         v.BarCode,
				ParentSkuId: v.ParentSkuId,
			}
			printOrder.Goods = append(printOrder.Goods, goods)
		}
	}

	if len(printOrder.Goods) > 0 {
		for _, v := range printOrder.Goods {
			if _, ok := childGoods[v.Sku]; ok {
				v.ChildGoods = childGoods[v.Sku]
			}
		}
	}

	printOrder.ShopReductionAmount = "0.0"
	printOrder.MtReductionAmount = "0.0"

	orderPromotions := o.GetOrderPromotion()
	//拼装优惠信息
	for _, item := range orderPromotions {
		active := oc.OrderPrivilegeModel{}
		active.ActiveName = dto.OrderPrivilegeActiveType(item.PromotionType).String()
		if order.OrderMain.ChannelId == ChannelAwenId {
			active.ActiveName = item.PromotionTitle //如果是阿闻到家，则取表里面的活动名
		}
		if active.ActiveName == "UNKNOWN" {
			active.ActiveName = item.PromotionTitle
		}
		active.ReduceFee = cast.ToString(item.PromotionFee)
		active.MtCharge = cast.ToString(item.PtCharge)
		active.PoiCharge = cast.ToString(item.PoiCharge)
		active.Remark = item.PromotionTitle

		printOrder.OrderPrivilegeModel = append(printOrder.OrderPrivilegeModel, &active)

		// 门店新客户立减
		if item.PromotionType == 22 {
			printOrder.ShopReductionAmount = cast.ToString(kit.FenToYuan(item.PoiCharge))
		}
		// 美团减免
		if item.PromotionType == 25 {
			printOrder.MtReductionAmount = cast.ToString(kit.FenToYuan(item.PtCharge))
		}
	}

	//开票配置读取redis中相关的值 v6.3.7修改  之前读取的是配置中心的invoice_enable参数
	redisClient := GetRedisConn()

	var invoiceEnable string
	if order.ChannelId == ChannelMallId {
		invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_mall").Val()
	} else {
		invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_o2o").Val()
	}

	if invoiceEnable == "1" {
		if canInvoice, err := CheckCanInvoiceByOrder(order.OrderMain); canInvoice && err == nil {
			envVersion := "release"
			if kit.IsDebug {
				envVersion = "trial"
			}
			// 获取开票小程序码
			//开票是否显示二维码
			invoiceQrCodeEnable := "1"
			if order.ChannelId != ChannelMallId {
				invoiceQrCodeEnable = redisClient.HGet("set:invoice_set", "invoice_qr_code_o2o").Val()
			}
			if invoiceQrCodeEnable == "1" {
				invoiceQrCode, err := miniprogram.AWen.GetWxaCodeUnLimit(map[string]interface{}{
					"scene": order.OrderMain.OrderSn + "_" + order.MemberTel,
					"width": 300,
					// "is_hyaline": true,
					"page":        "app/invoice/apply-form",
					"check_path":  !kit.IsDebug, // page 有数量上限（60000个）请勿滥用
					"env_version": envVersion,   // 仅发布后线上为正式版
				})
				if err == nil {
					printOrder.InvoiceQrCode = base64.StdEncoding.EncodeToString(invoiceQrCode)
				}
			}
		}
	}

	out.Detail = &printOrder
	return &out, nil
}

// 配送报表列表
func (o OrderService) OrderDeliveryReportList(ctx context.Context, params *oc.AwenParentOrderListRequest) (*oc.DeliveryReportResponse, error) {
	glog.Info("配送报表查询参数", kit.JsonEncode(params))
	out := oc.DeliveryReportResponse{
		Code:    200,
		Message: "ok",
	}

	//连接池勿关闭
	dbConn := GetDBConn()
	//dbConn.ShowSQL(true)

	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_delivery_report").
		Join("inner", "order_main", "order_main.order_sn=order_delivery_report.parent_order_sn").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("inner", "order_delivery_report_price b", "b.order_sn=order_delivery_report.order_sn").
		Join("left", "datacenter.store_relation sr", "sr.channel_id=order_main.channel_id AND sr.finance_code=order_main.shop_id").
		//Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		//Join("inner", "datacenter.store", "order_main.shop_id=datacenter.store.finance_code").
		//Join("left", "dc_dispatch.warehouse w", "w.id = order_main.warehouse_id").
		//Join("left", "order_main zd", "zd.parent_order_sn = order_main.order_sn").
		Join("left", "order_exception oe", "order_delivery_report.order_sn = oe.order_sn AND order_main.order_status=20 and oe.is_show=1")
	session.Where("1=1").Where("order_main.org_id=?", params.Orgid)

	//session.Join("left", "order_main_group", "order_main_group.parent_order_sn = order_main.order_sn").
	//	Join("left", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id")

	if params.OrderDeliveryFilter != 0 {
		if params.OrderDeliveryFilter == 1 {
			session.And("oe.delivery_id is null")
		} else if params.OrderDeliveryFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("oe.delivery_id is not null")
		}
	}

	//订单搜索
	if len(params.Keyword) > 0 && params.QuerySpecial == 0 {
		switch params.SearchType {
		case 1: //订单号
			session.And("`order_main`.order_sn like ?", params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
		case 3: //收货人姓名
			session.And("`order_main`.receiver_name like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_mobile = ?", utils.MobileEncrypt(params.Keyword))
		case 5: //买家手机号
			session.And("`order_main`.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
		case 6: //店铺名称
			session.And("`order_main`.shop_name like ?", "%"+params.Keyword+"%")
		case 7: //子订单号
			//session.And("order_main.order_sn in (select parent_order_sn from order_main where order_sn = ?)", params.Keyword)
			session.And("`order_delivery_report`.order_sn like ?", params.Keyword+"%")
		default: //default case
		}
	}

	//异常订单搜索
	if params.OrderFilter > 0 {
		if params.OrderFilter == 1 {
			//session.And("order_detail.push_delivery=1 and order_detail.push_third_order=1 and order_detail.split_order_result in(0,1)")
			session.And("(order_detail.push_delivery_reason='' or order_detail.push_delivery=1) and (order_detail.push_third_order_reason='' or order_detail.push_third_order=1) and (order_detail.split_order_fail_reason='' or order_detail.split_order_result in(0,1))")
		} else if params.OrderFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("(order_detail.push_delivery_reason<>'' and order_detail.push_delivery=0) or (order_detail.push_third_order_reason<>'' and order_detail.push_third_order=0) or (order_detail.split_order_fail_reason<>'' and order_detail.split_order_result=2)")
		}
	}

	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}
	//销售渠道
	if params.SaleChannel > 0 {
		session.And("`order_main`.user_agent = ?", params.SaleChannel)
	}
	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	if params.QuerySpecial == 0 {
		if params.TimeType == 1 {
			//完成时间
			if len(params.StartTime) > 0 {
				session.And("`order_main`.confirm_time > ?", params.StartTime)
			}
			if len(params.EndTime) > 0 {
				session.And("`order_main`.confirm_time <= ?", params.EndTime)
			}
		} else {
			//下单时间
			if len(params.StartTime) > 0 {
				session.And("`order_main`.create_time > ?", params.StartTime)
			}
			if len(params.EndTime) > 0 {
				session.And("`order_main`.create_time <= ?", params.EndTime)
			}
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		session.And("order_main.pay_mode = ?", params.PayMode)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	} else if len(params.UserNo) > 0 { //筛选用户权限门店
		session.Where("order_main.shop_id in (select finance_code from datacenter.store_user_authority where user_no = ?)", params.UserNo)
	}
	//自提点
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}

	countSession := session.Clone()
	defer countSession.Close()
	totalCount, err := countSession.Distinct("`order_delivery_report`.id").Count()
	if err != nil {
		err = errors.New("配送报表,数据库查询失败，" + err.Error())
		glog.Error("配送报表,数据库查询失败", err)
		return nil, err
	}
	out.TotalCount = int32(totalCount)
	if out.TotalCount == 0 {
		//out.Details = []*oc.SimpleOrderList{}
		return &out, nil
	}

	selectStr := `
		order_delivery_report.order_sn,
		order_main.old_order_sn,
		order_delivery_report.parent_order_sn,
		order_delivery_report.distance,
		order_delivery_report.last_delivery,
		order_delivery_report.last_delivery_type,
        order_main.shop_name,
		MAX(CASE WHEN b.delivery_type=0  THEN delivery_price ELSE '' END) AS mt_price,
		MAX(CASE WHEN b.delivery_type=3  THEN delivery_price ELSE '' END) AS dada_price,
		MAX(CASE WHEN b.delivery_type=5  THEN delivery_price ELSE '' END) AS sf_price,
		MAX(CASE WHEN b.delivery_type=4  THEN delivery_price ELSE '' END) AS fn_price,
        sr.channel_store_id,
         order_main.order_status_child,
         order_main.create_time,
         order_main.delivery_type,
         order_main.app_channel,
        order_main.shop_id,
        order_main.total_weight,
        if(oe.delivery_id IS null,0,1) as is_exception_delivery,
        CASE child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id
		`

	if err = session.Select(selectStr).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_delivery_report`.id").
		OrderBy("`order_delivery_report`.`create_time` DESC ").
		Find(&out.Details); err != nil {
		glog.Error("订单列表查询查询失败", err)
		return &out, err
	}

	return &out, nil
}

// 阿闻管家-父订单列表
func (o OrderService) AwenParentOrderList(ctx context.Context, params *oc.AwenParentOrderListRequest) (*oc.AwenParentOrderListResponse, error) {
	logPrefix := "阿闻管家-父订单列表===="
	glog.Info(logPrefix, kit.JsonEncode(params))
	out := oc.AwenParentOrderListResponse{
		Code:    200,
		Message: "ok",
	}

	//连接池勿关闭
	dbConn := GetDBConn()
	//dbConn.ShowSQL(true)

	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
		Join("inner", "datacenter.store", "order_main.shop_id=datacenter.store.finance_code").
		Join("left", "dc_dispatch.warehouse w", "w.id = order_main.warehouse_id").
		Join("left", "order_main zd", "zd.parent_order_sn = order_main.order_sn").
		Join("left", "order_main child", "child.parent_order_sn = order_main.order_sn"). // 添加子订单关联
		Join("left", "refund_order a", " a.order_sn=order_main.order_sn and a.refund_state in (1,5)").
		Join("left", "refund_order b", " b.order_sn=zd.order_sn and b.refund_state in (1,5)").
		Join("left", "order_exception oe", "zd.order_sn = oe.order_sn AND zd.order_status=20 and oe.is_show=1")
	session.Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ''")

	session.Join("left", "order_main_group", "order_main_group.parent_order_sn = order_main.order_sn").
		Join("left", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id")

	if len(params.FinancialCode) > 0 {
		session.Where("order_main.shop_id=?", params.FinancialCode)
	}
	if params.QuerySpecial == 1 {
		session.Where("order_main.order_type = 15")
		if params.OrderGroupActivityId > 0 {
			session.Where("order_group_activity.id = ?", params.OrderGroupActivityId)
		}
		if len(params.Keyword) > 0 {
			switch params.SearchType {
			case 1: //团员主订单号
				session.Where("order_main.order_sn like ?", params.Keyword+"%")
			case 2: //团员收货人姓名
				session.Where("IF(order_group_activity.final_take_type=1, order_main_group.receiver_name, order_main.receiver_name) like ?", "%"+params.Keyword+"%")
			case 3: //团员收货人手机号
				session.Where("IF(order_group_activity.final_take_type=1, order_main_group.en_receiver_mobile, order_main.en_receiver_phone)  = ?", utils.MobileEncrypt(params.Keyword))
			case 4: //团员下单手机号
				session.Where("order_main.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
			}
		}
		if len(params.StartTime) > 0 && len(params.EndTime) > 0 || params.OrderGroupActivityId < 1 {
			switch params.TimeType {
			case 1:
				session.Where("order_group_activity.created_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
			case 2:
				session.Where("order_group_activity.status = 1 and order_group_activity.group_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
			case 3:
				session.Where("order_group_activity.status = 2 and order_group_activity.group_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
			}
		}
	}
	if params.CombineType == 1 || params.CombineType == 2 || params.CombineType == 3 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("`order_product`.combine_type = ? and `order_product`.product_type = ?  ", params.CombineType, 3)
	} else if params.CombineType == 4 {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
		session.And("NOT EXISTS(SELECT op.order_sn FROM order_product op WHERE op.order_sn=`order_main`.order_sn and op.product_type = 3) ")
	} else {
		session.Join("left", "order_product", "`order_main`.order_sn=order_product.order_sn")
	}
	if params.OrderDeliveryFilter != 0 {
		if params.OrderDeliveryFilter == 1 {
			session.And("oe.delivery_id is null")
		} else if params.OrderDeliveryFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("oe.delivery_id is not null")
		}
	}

	//订单搜索
	if len(params.Keyword) > 0 && params.QuerySpecial == 0 {
		switch params.SearchType {
		case 1: //订单号
			session.And("`order_main`.order_sn like ?", params.Keyword+"%")
		case 2: //外部订单
			session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
		case 3: //收货人姓名
			session.And("`order_main`.receiver_name like ?", "%"+params.Keyword+"%")
		case 4: //收货人手机号
			session.And("`order_main`.en_receiver_mobile = ?", utils.MobileEncrypt(params.Keyword))
		case 5: //买家手机号
			session.And("`order_main`.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
		case 6: //店铺名称
			session.And("`order_main`.shop_name like ?", "%"+params.Keyword+"%")
		case 7: //子订单号
			session.And("order_main.order_sn in (select parent_order_sn from order_main where order_sn = ?)", params.Keyword)
		default: //default case
		}
	}

	//异常订单搜索
	if params.OrderFilter > 0 {
		if params.OrderFilter == 1 {
			//session.And("order_detail.push_delivery=1 and order_detail.push_third_order=1 and order_detail.split_order_result in(0,1)")
			session.And("(order_detail.push_delivery_reason='' or order_detail.push_delivery=1) and (order_detail.push_third_order_reason='' or order_detail.push_third_order=1) and (order_detail.split_order_fail_reason='' or order_detail.split_order_result in(0,1))")
		} else if params.OrderFilter == 2 {
			//session.And("order_detail.push_delivery=0 or order_detail.push_third_order=0 or order_detail.split_order_result=2")
			session.And("(order_detail.push_delivery_reason<>'' and order_detail.push_delivery=0) or (order_detail.push_third_order_reason<>'' and order_detail.push_third_order=0) or (order_detail.split_order_fail_reason<>'' and order_detail.split_order_result=2)")
		}
	}

	if params.QuerySpecial == 0 {
		if params.TimeType == 1 {
			//完成时间
			if len(params.StartTime) > 0 {
				session.And("`order_main`.confirm_time > ?", params.StartTime)
			}
			if len(params.EndTime) > 0 {
				session.And("`order_main`.confirm_time <= ?", params.EndTime)
			}
		} else {
			//下单时间
			if len(params.StartTime) > 0 {
				session.And("`order_main`.create_time > ?", params.StartTime)
			}
			if len(params.EndTime) > 0 {
				session.And("`order_main`.create_time <= ?", params.EndTime)
			}
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}
	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}
	//销售渠道
	if params.SaleChannel > 0 {
		session.And("`order_main`.user_agent = ?", params.SaleChannel)
	}
	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式

	if params.PayMode > 0 {
		if params.Orgid == 6 {
			session.Join("left", "order_payment", "order_main.order_sn=order_payment.order_sn").Where("order_payment.pay_type=?", params.PayMode)
		} else {
			session.And("order_main.pay_mode = ?", params.PayMode)
		}

	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}

	//如果是逍宠，则不做权限判断，只查询当前的门店订单
	session.And("order_main.org_id = ?", params.Orgid)
	if params.Orgid == 6 {
		session.In("order_main.shop_id", params.Shopids)
	} else {
		//登录用户有权限的所有门店id(财务编码)
		if len(params.Shopids) > 0 {
			session.In("`order_main`.shop_id", params.Shopids)
		} else if len(params.UserNo) > 0 { //筛选用户权限门店
			session.Where("order_main.shop_id in (select finance_code from datacenter.store_user_authority where user_no = ?)", params.UserNo)
		}
	}

	//自提点
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}

	countSession := session.Clone()
	defer countSession.Close()
	totalCount, err := countSession.Distinct("`order_main`.id").Count()
	if err != nil {
		err = errors.New("订单列表查询查询失败,数据库查询失败，" + err.Error())
		glog.Error("订单列表查询查询失败,数据库查询失败", err)
		return nil, err
	}
	out.TotalCount = int32(totalCount)
	if out.TotalCount == 0 {
		out.Details = []*oc.SimpleOrderList{}
		return &out, nil
	}

	selectStr := `CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		child.order_sn as child_order_sn,
		a.refund_sn,b.refund_sn refund_sn1,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.split_order_result,
		order_detail.split_order_fail_reason,
		order_detail.expected_time,
		order_detail.is_picking,
		order_detail.is_adjust,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
		order_detail.performance_operator_time,
		order_detail.pickup_code,
		order_detail.buyer_memo,
		order_detail.goods_return_delivery_id,
		order_detail.is_new_customer,
		datacenter.store.bigregion,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		w.category,
        if(oe.delivery_id IS null,0,1) as is_exception_delivery
		`

	selectStr += `,
			order_group_activity.id as order_group_activity_id,
			order_group_activity.member_id as order_group_member_id,
			order_detail.shop_dis_member_id,
			order_detail.shop_dis_chain_id,
			order_main_group.receiver_name as group_member_receiver_name,
			order_main_group.receiver_mobile as group_member_receiver_mobile,
			order_main_group.en_receiver_mobile as group_member_en_receiver_mobile,
			order_main_group.receiver_address as group_member_receiver_address
		`

	if err = session.Select(selectStr).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1), `order_main`.`create_time` DESC ").
		Find(&out.Details); err != nil {
		glog.Error("订单列表查询查询失败", err)
		return &out, err
	}

	var orderGroupActivityIds []int32
	var shopDisMemberIds []string
	var orderSns []string
	var shopDisChainId []int32

	for i, v := range out.Details {
		orderSns = append(orderSns, v.OrderSn)
		if v.OrderGroupActivityId > 0 {
			orderGroupActivityIds = append(orderGroupActivityIds, v.OrderGroupActivityId)
		}
		if len(v.ShopDisMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.ShopDisMemberId)
		}
		if len(v.OrderGroupMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.OrderGroupMemberId)
		}
		if v.ShopDisChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.ShopDisChainId)
		}
		if v.RefundSn1 != "" {
			out.Details[i].RefundSn = v.RefundSn1
		}
	}

	//获取积分
	//var integralData []models.OrderIntegral
	//if len(orderSns) > 0 {
	//	dbConn.Table("order_integral").In("order_sn", orderSns).Find(&integralData)
	//}

	orderProductMap := map[string][]*models.OrderProduct{}
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	orderGroupActivityMap := map[int32]*models.OrderGroupActivity{}
	upetMemberChainMap := map[string]*models.UpetMemberChain{}
	disCommissionMap := map[string]*models.OrderDis{}
	upetChainMap := map[int32]*models.UpetChain{}

	orderPerformanceMap, _ := FindOrderPerformanceMapByOrderSn(dbConn, orderSns)
	for _, v := range orderPerformanceMap {
		if v.PerformanceChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.PerformanceChainId)
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(6)
	//2:查询商品从表，补全订单的商品信息，1个订单可能下面对应多个商品
	go func() {
		defer wg.Done()
		var dbproducts []*models.OrderProduct
		if err = dbConn.
			In("order_sn", orderSns).
			Find(&dbproducts); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}

		for _, v := range dbproducts {
			orderProductMap[v.OrderSn] = append(orderProductMap[v.OrderSn], v)
		}
	}()
	//3:查询优惠券从表，补全订单的优惠信息，1个订单可能下面对应多个商品
	go func() {
		defer wg.Done()
		var promotions []*models.OrderPromotion
		if err = dbConn.Select("*").Where("promotion_fee > 0").
			In("order_sn", orderSns).
			Find(&promotions); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}

		for _, v := range promotions {
			orderPromotionMap[v.OrderSn] = append(orderPromotionMap[v.OrderSn], v)
		}
	}()

	// 补全团长制拼团的信息
	go func() {
		defer wg.Done()
		if len(orderGroupActivityIds) > 0 {
			var orderGroupActivity []*models.OrderGroupActivity
			if err = dbConn.Select("").
				Table(&models.OrderGroupActivity{}).
				In("id", orderGroupActivityIds).
				Find(&orderGroupActivity); err != nil {
				glog.Error("订单列表查询团长制拼团的信息查询失败，", err.Error())
			}
			for k, v := range orderGroupActivity {
				orderGroupActivityMap[cast.ToInt32(v.Id)] = orderGroupActivity[k]
			}
		}
	}()

	// 补全业绩 与 绑定门店的信息
	go func() {
		defer wg.Done()
		if len(shopDisMemberIds) > 0 {
			upetMemberChain := &models.UpetMemberChain{}
			upetMemberChainList, err := upetMemberChain.FindInScrmUserId(GetUPetDBConn(), shopDisMemberIds)
			if err != nil {
				glog.Error("订单列表查询业绩会员信息查询失败，", err.Error())
			}
			for k, v := range upetMemberChainList {
				upetMemberChainMap[v.ScrmUserId] = upetMemberChainList[k]
			}
		}
	}()

	// 补全佣金信息
	go func() {
		defer wg.Done()
		var orderDis []*models.OrderDis
		if err = dbConn.Table("order_dis").
			In("parent_order_sn", orderSns).
			Select("parent_order_sn, sum(commission) as commission").
			GroupBy("parent_order_sn").
			Find(&orderDis); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}
		for k, v := range orderDis {
			disCommissionMap[v.ParentOrderSn] = orderDis[k]
		}
	}()

	go func() {
		defer wg.Done()
		upetChainMap, _ = FindUpetChainMap(shopDisChainId)
	}()
	wg.Wait()

	now := time.Now()
	//判断是否有 修改货号 发起配送 按钮权限
	has, _ := dbConn.Table("sc_stock.auth").Where("user_no=? and button_permission=1", params.UserNo).Exist()
	for _, order := range out.Details {
		// 是否显示 修改货号 按钮
		order.IsShow = 0
		if has && order.PushThirdOrder == 0 && order.OrderStatus != 0 {
			order.IsShow = 1
		}
		if err := dbConn.Table("order_log").
			Select("*").
			Where("order_sn = ?", order.OrderSn).
			Desc("id").
			Find(&order.FootMarkModel); err != nil {
			glog.Errorf("订单列表查询父订单脚列表印详情查询失败！ order_sn:%s ", order.OrderSn)
			out.Code = 400
			out.Message = "查询订单脚印详情失败"
			return &out, nil
		}
		if len(order.FootMarkModel) > 0 {
			for _, v := range order.FootMarkModel {
				v.LogName = v.OperateTypeName + models.OrderLodMap[int(v.LogType)]
			}
		}
		//for _, v := range integralData {
		//	if order.OrderSn == v.OrderSn {
		//		order.Integral = int64(v.Integral)
		//		break
		//	}
		//}

		// 推送成功后隐藏异常原因
		if order.PushThirdOrder == 1 {
			order.PushThirdOrderReason = ""
		}

		// 前端根据push_third_order = 0来显示重新推单
		// 当没有接单时，不显示重新发单按钮
		if (order.OrderStatusChild == 20101 && order.PushThirdOrder == 0) || order.OrderStatus == 0 {
			order.PushThirdOrder = 1
		}

		if order.PushDelivery == 1 {
			order.PushDeliveryReason = ""
		}
		if order.SplitOrderResult == 1 {
			order.SplitOrderFailReason = ""
		}

		if order.OrderType == 3 {
			order.ReceiverName = ""
		}

		// 是否显示部分退款按钮
		order.IsPartButton = func() int32 {
			if len(order.ConfirmTime) == 0 || params.Orgid == cast.ToInt64(SAASMainId) {
				return 1
			}
			confirmTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, order.ConfirmTime, time.Local)
			// 24小时内允许退款
			if confirmTime.Add(24 * time.Hour).After(now) {
				return 1
			}
			// 到家的订单，如果有权限可以在2个月内退款
			if order.ChannelId == ChannelAwenId || order.ChannelId == ChannelDigitalHealth {
				// 超过2个月，AddDate不会按预期的增加月份，只会按1个月30天增加
				if confirmTime.AddDate(0, 0, 364).Before(now) {
					return 0
				}
				if has {
					return 1
				}
				// 物流配送的 完成后15天内可以退款
				if order.DeliveryType == 1 && confirmTime.Add(360*time.Hour).After(now) {
					return 1
				}
			}
			//美团14天可以退款
			if order.ChannelId == ChannelMtId {
				if confirmTime.AddDate(0, 0, 14).Before(now) {
					return 0
				}
				if has {
					return 1
				}
			}
			//饿了么2天可以退款
			if order.ChannelId == ChannelElmId {
				if confirmTime.AddDate(0, 0, 2).Before(now) {
					return 0
				}
				if has {
					return 1
				}
			}
			return 0
		}()

		// 是否显示发起配送按钮
		order.IsRedelivery = func() int32 {
			//`order_main`.order_status_child  =20102
			//`order_main`.order_status=20
			//order_main.order_type   类型判断，自提，预订单，普通订单
			//`order_detail`.expected_time    预计送达时间
			//`order_detail`.push_delivery=0
			orderStatusChild := order.OrderStatusChild
			orderStatus := order.OrderStatus
			orderType := order.OrderType
			expectedTime := order.ExpectedTime
			pushDelivery := order.PushDelivery
			pushThirdOrder := order.PushThirdOrder
			channelId := order.ChannelId
			logisticsCode := order.LogisticsCode
			isSelfDistribution := false
			if (channelId == ChannelMtId && !strings.Contains("2002,1001,1004,2010,3001,1007", logisticsCode)) || //不等于美团专送的，其余的就是自配送
				(channelId == ChannelElmId && logisticsCode == "6") ||
				(channelId == ChannelJddjId && logisticsCode == "2938") || channelId == ChannelAwenId {
				isSelfDistribution = true
			}
			if !isSelfDistribution {
				return 0
			}

			if orderStatus == 20 && orderStatusChild == 20102 && pushDelivery == 0 && pushThirdOrder == 1 {
				if orderType == 1 {
					return 1
				} else if orderType == 2 && len(expectedTime) > 0 {
					t, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, expectedTime, time.Local)
					duration1 := t.Sub(time.Now())
					duration2 := 45 * time.Minute
					if duration1 < duration2 {
						return 1
					}
				}
			}
			return 0
		}()

		//预计送达时间，不需要秒
		if len(order.ExpectedTime) > 16 {
			order.ExpectedTime = order.ExpectedTime[0:16]
		}

		//预计送达时间，如果是实物订单，则只显示时分秒，其他订单类型显示完整日期
		if order.OrderType == 1 {
			if len(order.ExpectedTime) > 0 {
				s := order.ExpectedTime
				strArr := strings.Split(s, " ")
				order.ExpectedTime = strArr[1]
			}
		}

		//var childProductModel *oc.SimpleChildOrderProductList
		//combinedOrderProductMap := map[string][]*oc.SimpleChildOrderProductList{}
		var childrenSku []*models.OrderProduct
		var productModel *oc.SimpleOrderProductList
		order.OrderProductModel = make([]*oc.SimpleOrderProductList, 0)

		//查找订单的商品
		for _, v := range orderProductMap[order.OrderSn] {
			if len(v.ParentSkuId) == 0 {
				productModel = &oc.SimpleOrderProductList{
					Id:                     cast.ToString(v.Id),
					OrderSn:                v.OrderSn,
					ProductId:              v.ProductId,
					ProductType:            v.ProductType,
					ParentSkuId:            v.ParentSkuId,
					ProductName:            v.ProductName,
					Image:                  v.Image,
					SkuId:                  v.SkuId,
					ThirdSkuId:             v.ThirdSkuId,
					MarkingPrice:           v.MarkingPrice,
					DiscountPrice:          v.DiscountPrice,
					PayPrice:               v.PayPrice,
					Number:                 v.Number,
					PaymentTotal:           v.PaymentTotal,
					BarCode:                v.BarCode,
					ChildOrderProductModel: make([]*oc.SimpleChildOrderProductList, 0),
					Specs:                  v.Specs,
					LocationCode:           v.LocationCode,
					BuyType:                v.BuyType,
				}
				if len(v.ChildrenSku) > 0 {
					err = kit.JsonDecode([]byte(v.ChildrenSku), &childrenSku)
					if err != nil {
						glog.Error("订单列表查询,父订单列表反序列化失败,order_sn=", order.OrderSn)
					} else {
						for _, v1 := range childrenSku {
							productModel.ChildOrderProductModel = append(productModel.ChildOrderProductModel, &oc.SimpleChildOrderProductList{
								//Id:          cast.ToString(v.Id),
								Id:          getOrderProductModelId(v1.OrderSn, v1.SkuId, v1.ParentSkuId, v1.ProductName, v1.ProductType),
								OrderSn:     v1.OrderSn,
								ProductId:   v1.ProductId,
								ProductType: v1.ProductType,
								ParentSkuId: v1.ParentSkuId,

								ProductName:   v1.ProductName,
								Image:         v1.Image,
								SkuId:         v1.SkuId,
								ThirdSkuId:    v1.ThirdSkuId,
								MarkingPrice:  v1.MarkingPrice,
								PayPrice:      v1.PayPrice,
								DiscountPrice: v1.DiscountPrice,
								Number:        v1.Number,
								PaymentTotal:  v1.PaymentTotal,
								BarCode:       v1.BarCode,
							})
						}
					}
				}
				order.OrderProductModel = append(order.OrderProductModel, productModel)
				childrenSku = []*models.OrderProduct{}
				productModel = &oc.SimpleOrderProductList{}
			}
		}

		//团长拼团制的一些信息
		if order.OrderType == 15 {
			order.GroupActivityModel = &oc.SimpleOrderGroupActivity{}
			if v, ok := orderGroupActivityMap[order.OrderGroupActivityId]; ok {
				order.GroupActivityModel.DeliverDays = v.DeliverDays
				order.GroupActivityModel.FinalTakeType = v.FinalTakeType
				order.GroupActivityModel.Status = v.Status
				order.GroupActivityModel.DisChainName = v.DisChainName
				order.GroupActivityModel.MemberName = v.MemberName
				order.GroupActivityModel.ReceiverAddress = v.ReceiverAddress
				order.GroupActivityModel.ReceiverMobile = v.ReceiverMobile
				order.GroupActivityModel.EnReceiverMobile = v.EnReceiverMobile
				order.GroupActivityModel.EndTime = v.EndTime.AddDate(0, 0, int(v.DeliverDays)).Format(kit.DATE_LAYOUT_SHORT_CN)
			}
			if v, ok := disCommissionMap[order.OrderSn]; ok {
				order.GroupActivityModel.DisCommission = cast.ToInt32(v.Commission)
			}
			if v, ok := upetMemberChainMap[order.OrderGroupMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.GroupActivityModel.StaffName = v.BillUserName
				} else {
					order.GroupActivityModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
			}
			//业绩的信息
			order.ShopDisModel = &oc.SimpleOrderShopDis{}
			if v, ok := upetMemberChainMap[order.ShopDisMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.ShopDisModel.StaffName = v.BillUserName
				} else {
					order.ShopDisModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
				order.ShopDisModel.MemberAreaName = v.MemberAreaName
			}
			if v, ok := upetChainMap[order.ShopDisChainId]; ok {
				order.ShopDisModel.ChainName = v.ChainName
				order.ShopDisModel.FinanceCode = v.AccountId
			}

			//如果非团长待收，处理一下团员地址
			if order.GroupActivityModel.FinalTakeType == 0 {
				order.GroupMemberReceiverName = order.ReceiverName
				order.GroupMemberReceiverMobile = order.ReceiverMobile
				order.GroupMemberReceiverAddress = order.ReceiverAddress
			}
		} else {
			if order.OrgId == 6 {
				var commissionPerformances []models.CommissionPerformance
				err := dbConn.Where("order_no = ?", order.OrderSn).Find(&commissionPerformances)
				if err != nil {
					glog.Error("查询员工业绩失败", err)
				} else if len(commissionPerformances) > 0 {
					// 拼接多个员工的业绩归属
					var staffNames []string
					for _, cp := range commissionPerformances {
						if cp.RealName != "" && !contains(staffNames, cp.RealName) {
							staffNames = append(staffNames, cp.RealName)
						}
					}

					// 使用第一条记录的时间
					operatorTime := commissionPerformances[0].OrderTime.Format(kit.DATETIME_LAYOUT)
					operator := commissionPerformances[0].Operator
					// 拼接员工名称和操作员
					order.PerformanceStaffName = strings.Join(staffNames, ",")
					order.PerformanceOperatorName = operator
					order.PerformanceOperatorTime = operatorTime
					order.PerformanceChainName = order.ShopName
					order.PerformanceFinanceCode = order.ShopId
				}
			} else if v, ok := orderPerformanceMap[order.OrderSn]; ok {
				order.PerformanceStaffName = v.StaffName
				order.PerformanceOperatorName = v.OperatorName
				order.PerformanceOperatorTime = v.CreateTime.Format(kit.DATETIME_LAYOUT)
				if v2, ok := upetChainMap[v.PerformanceChainId]; ok {
					order.PerformanceChainName = v2.ChainName
					order.PerformanceFinanceCode = v2.AccountId
				} else if order.PerformanceOperatorName != "系统分配" {
					order.PerformanceChainName = order.ShopName
					order.PerformanceFinanceCode = order.ShopId
				}
			}
		}
	}

	return &out, nil
}

// contains 检查字符串切片中是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 阿闻管家-父订单详情-基础信息
func (o OrderService) AwenParentOrderBaseDetail(ctx context.Context, params *oc.AwenAllOrderBaseDetailRequest) (*oc.AwenParentOrderBaseDetailResponse, error) {
	out := oc.AwenParentOrderBaseDetailResponse{Code: 200, Message: "ok"}
	dbConn := GetDBConn()

	orderModel := &models.Order{}
	if ok, err := dbConn.SQL("select * from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.OrderId).
		Get(orderModel); err != nil {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "查询订单详情失败"
		return &out, nil
	} else if !ok {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	orderSn := orderModel.OrderMain.OrderSn

	orderModelDetail := oc.ParentCombineOrderDetail{
		OrderId:              int32(orderModel.Id),
		OrderSn:              orderSn,
		OldOrderSn:           orderModel.OldOrderSn,
		PaySn:                orderModel.PaySn,
		CreateTime:           kit.GetTimeNow(orderModel.CreateTime),
		OrderType:            orderModel.OrderType,
		ChannelId:            orderModel.ChannelId,
		PerformanceStaffName: orderModel.PerformanceStaffName,
		OrderStatus:          orderModel.OrderStatus,
		OrderStatusChild:     orderModel.OrderStatusChild,
		DeliveryType:         orderModel.DeliveryType,
		ReceiverName:         orderModel.ReceiverName,
		ReceiverState:        orderModel.ReceiverState,
		ReceiverCity:         orderModel.ReceiverCity,
		ReceiverDistrict:     orderModel.ReceiverDistrict,
		ReceiverAddress:      orderModel.ReceiverAddress,
		ReceiverPhone:        orderModel.ReceiverPhone,
		EnReceiverPhone:      orderModel.EnReceiverPhone,
		ReceiverMobile:       orderModel.ReceiverMobile,
		EnReceiverMobile:     orderModel.EnReceiverMobile,
		EnMemberTel:          orderModel.EnMemberTel,
		DeliveryRemark:       orderModel.DeliveryRemark,
		GoodsTotal:           orderModel.GoodsTotal,
		UserReceiveTotal:     orderModel.PayTotal,
		ActualReceiveTotal:   orderModel.ActualReceiveTotal,
		GoodsPayTotal:        orderModel.GoodsPayTotal,
		Freight:              orderModel.Freight,
		PackingCost:          orderModel.PackingCost,
		ServiceCharge:        orderModel.ServiceCharge,
		ContractFee:          orderModel.ContractFee,
		Total:                orderModel.Total,
		IsPicking:            orderModel.IsPicking,
		LogisticsCode:        orderModel.LogisticsCode,
		UserAgent:            orderModel.UserAgent,
		ShopName:             orderModel.ShopName,
		ShopId:               orderModel.ShopId,
		ExpectedTime:         orderModel.ExpectedTime.Format(kit.DATE_LAYOUT + " " + kit.TIME_LAYOUT_SHORT),
		PayMode:              orderModel.PayMode,
		MemberId:             orderModel.MemberId,
		MemberTel:            orderModel.MemberTel,
		Source:               orderModel.Source,
		FreightPrivilege:     orderModel.FreightPrivilege,
	}

	if !orderModel.PayTime.IsZero() {
		orderModelDetail.PayTime = kit.GetTimeNow(orderModel.PayTime)
	}

	if (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.DeliveryType == 3 {
		orderModelDetail.ReceiverName = ""
	}

	noSelfDistribution := 0

	if orderModelDetail.DeliveryType != 3 {
		if orderModelDetail.ChannelId == ChannelElmId {
			if orderModelDetail.LogisticsCode != "6" {
				noSelfDistribution = 1
			}
		} else if orderModelDetail.ChannelId == ChannelJddjId {
			if orderModelDetail.LogisticsCode != "2938" {
				noSelfDistribution = 1
			}
		} else if orderModelDetail.ChannelId == ChannelMtId {
			//美团专配
			if strings.Contains("2002,1001,1004,2010,3001,1007", orderModelDetail.LogisticsCode) {
				noSelfDistribution = 1
			}
		}
	}
	orderModelDetail.IsZp = cast.ToInt32(noSelfDistribution)

	//协程处理
	var wg = new(sync.WaitGroup)
	wg.Add(3)

	go func() {
		defer func() {
			wg.Done()
		}()
		//取配送流程最新一条记录并且骑手姓名不为空的数据
		childOrderSn := []string{}
		//获取实物子订单号
		if err := dbConn.Table("order_main").Select("order_sn").
			Where("parent_order_sn = ?", orderSn).
			And("is_virtual = 0").
			//And("parent_order_sn = order_sn OR parent_order_sn != ?", "").
			Find(&childOrderSn); err != nil {
			glog.Errorf("AwenParentOrderBaseDetail获取子订单集查询失败！ order_id:%s; order_sn:%s", params.OrderId, orderSn)
			return
		}

		//取配送流程最新一条记录
		delivery := models.OrderDeliveryRecord{}
		if ok, err := dbConn.In("order_sn", childOrderSn).
			Desc("create_time").
			Get(&delivery); err != nil {
			glog.Errorf("AwenOrderBaseDetail订单配送详情查询失败！ order_id:%s order_sn:%s", params.OrderId, orderSn)
			return
		} else if ok {
			orderModelDetail.OrderDeliveryType = cast.ToInt32(delivery.DeliveryType)
			orderModelDetail.DeliveryId = delivery.DeliveryId
			orderModelDetail.DeliveryIdStr = cast.ToString(delivery.DeliveryId)
			deliveryNode := models.OrderDeliveryNode{}
			if ok, err = dbConn.Where("delivery_id = ?", delivery.DeliveryId).
				Desc("id").
				Get(&deliveryNode); err != nil {
				glog.Errorf("AwenOrderBaseDetail订单配送详情查询失败！ order_id:%s order_sn:%s", params.OrderId, orderSn)
				return
			} else if ok {
				orderModelDetail.DeliveryStatus = deliveryNode.DeliveryStatus
				if len(deliveryNode.CourierName) > 0 {
					orderModelDetail.CourierName = deliveryNode.CourierName
					orderModelDetail.CourierPhone = deliveryNode.CourierPhone
				}
			} else {
				if orderModel.OrgId == 6 {
					orderModelDetail.DeliveryStatus = 99
				}

			}
		}
	}()

	go func() {
		defer func() {
			wg.Done()
		}()
		//获取脚印信息
		if err := dbConn.Table("order_log").
			Select("*").
			Where("order_sn = ?", orderSn).
			In("log_type", models.ForwardLogSlice).
			Desc("id").
			Find(&orderModelDetail.FootMarkModel); err != nil {
			glog.Errorf("AwenParentOrderBaseDetail订单脚印详情查询失败！ order_id:%s; order_sn:%s", params.OrderId, orderSn)
			return
		}
		if len(orderModelDetail.FootMarkModel) > 0 {
			var logType int
			for _, v := range orderModelDetail.FootMarkModel {
				logType = int(v.LogType)
				v.LogName = v.OperateTypeName + models.OrderLodMap[logType]
				if logType == models.OrderLogPushedOrder {
					if orderModel.Source == 4 {
						v.LogName = "已成功推送至OMS系统"
					} else if orderModel.Source == 3 {
						v.LogName = "已成功推送至子龙系统"
					} else if orderModel.Source == 5 {
						v.LogName = "已成功推送至巨益OMS"
					}
				} else if logType == models.OrderLogPushOrderFailed {
					if orderModel.Source == 4 {
						v.LogName = "推送OMS系统失败"
					} else if orderModel.Source == 3 {
						v.LogName = "推送子龙系统失败"
					} else if orderModel.Source == 5 {
						v.LogName = "推送巨益OMS失败"
					}
				}
			}
		}
	}()

	//找出子单的物流信息
	var orderExpress []*models.OrderExpress
	go func() {
		defer func() {
			wg.Done()
		}()
		if orderModel.DeliveryType != 1 {
			return
		}
		var childOrderSn []string
		dbConn.Table("order_main").Where("parent_order_sn=?", orderModel.OrderSn).Cols("order_sn").Find(&childOrderSn)
		dbConn.Table("order_express").In("order_sn", childOrderSn).Find(&orderExpress)
		for _, v := range orderExpress {
			orderModelDetail.Express = append(orderModelDetail.Express, &oc.OrderExpress{
				Id:          cast.ToInt32(v.Id),
				ExpressNo:   v.ExpressNo,
				ExpressName: v.ExpressName,
				Num:         v.Num,
			})
		}
	}()

	wg.Wait()

	//优惠活动解析
	var PresentPayedAmount int32  //商家承担的优惠金额
	var PlatformPayedAmount int32 //美团平台承担的优惠金额
	var promotionFee int32        // 活动优惠价格
	var orderPromotions []models.OrderPromotion
	if err := dbConn.Where("order_sn = ? ", orderSn).
		Find(&orderPromotions); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "查询订单商品优惠信息失败"
		return &out, nil
	} else if len(orderPromotions) > 0 {
		for _, item := range orderPromotions {
			active := oc.OrderPrivilegeModel{}
			active.ActiveName = dto.OrderPrivilegeActiveType(item.PromotionType).String()
			if orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth {
				active.ActiveName = item.PromotionTitle //如果是阿闻到家，则取表里面的活动名
			}
			if active.ActiveName == "UNKNOWN" {
				active.ActiveName = item.PromotionTitle
			}
			active.ReduceFee = cast.ToString(item.PromotionFee)
			active.MtCharge = cast.ToString(item.PtCharge)
			active.PoiCharge = cast.ToString(item.PoiCharge)
			active.Remark = item.PromotionTitle
			PresentPayedAmount += item.PoiCharge
			PlatformPayedAmount += item.PtCharge
			promotionFee += item.PromotionFee
			orderModelDetail.OrderPrivilegeModel = append(orderModelDetail.OrderPrivilegeModel, &active)
		}
	}
	//总优惠金额
	if orderModel.ChannelId == ChannelAwenId && orderModel.OrgId == 6 {
		//宠物saas小程序
		orderModelDetail.Privilege = promotionFee
	} else {
		orderModelDetail.Privilege = PresentPayedAmount + PlatformPayedAmount
	}

	//订单商品列表信息
	var orderProducts []models.OrderProduct
	if err := dbConn.Table("order_product").
		Where("order_sn = ?", orderSn).
		Find(&orderProducts); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "查询订单商品信息失败"
		return &out, nil
	} else if len(orderProducts) > 0 {
		//var childProductModel *oc.SimpleChildOrderProductList
		//combinedOrderProductMap := map[string][]*oc.SimpleChildOrderProductList{}
		var childrenSku []*models.OrderProduct
		var productModel *oc.SimpleOrderProductList
		orderModelDetail.OrderProductModel = make([]*oc.SimpleOrderProductList, 0)
		for _, v := range orderProducts {
			if len(v.ParentSkuId) == 0 {
				productModel = &oc.SimpleOrderProductList{
					Id:                     cast.ToString(v.Id),
					OrderSn:                v.OrderSn,
					ProductId:              v.ProductId,
					ProductType:            v.ProductType,
					ParentSkuId:            v.ParentSkuId,
					PrivilegePt:            v.PrivilegePt,
					ProductName:            v.ProductName,
					Image:                  v.Image,
					SkuId:                  v.SkuId,
					ThirdSkuId:             v.ThirdSkuId,
					MarkingPrice:           v.MarkingPrice,
					DiscountPrice:          v.DiscountPrice,
					PayPrice:               v.PayPrice,
					Number:                 v.Number,
					PaymentTotal:           v.PaymentTotal,
					BarCode:                v.BarCode,
					Specs:                  v.Specs,
					LocationCode:           v.LocationCode,
					ChildOrderProductModel: make([]*oc.SimpleChildOrderProductList, 0),
				}
				if len(v.ChildrenSku) > 0 {
					err = kit.JsonDecode([]byte(v.ChildrenSku), &childrenSku)
					if err != nil {
						glog.Error("父订单列表反序列化失败,order_sn=", orderSn)
					} else {
						for _, v1 := range childrenSku {
							productModel.ChildOrderProductModel = append(productModel.ChildOrderProductModel, &oc.SimpleChildOrderProductList{
								//Id:          cast.ToString(v.Id),
								OrderSn:       v1.OrderSn,
								ProductId:     v1.ProductId,
								ProductType:   v1.ProductType,
								ParentSkuId:   v1.ParentSkuId,
								PrivilegePt:   v1.PrivilegePt,
								ProductName:   v1.ProductName,
								Image:         v1.Image,
								SkuId:         v1.SkuId,
								ThirdSkuId:    v1.ThirdSkuId,
								MarkingPrice:  v1.MarkingPrice,
								PayPrice:      v1.PayPrice,
								DiscountPrice: v1.DiscountPrice,
								Number:        v1.Number,
								PaymentTotal:  v1.PaymentTotal,
								BarCode:       v1.BarCode,
							})
						}

					}
				}
				orderModelDetail.OrderProductModel = append(orderModelDetail.OrderProductModel, productModel)
				childrenSku = []*models.OrderProduct{}
				productModel = &oc.SimpleOrderProductList{}
			}
		}

	}

	// 物竞天择渠道
	if orderModel.ChannelId == 4 && len(orderModel.ChildChannelId) > 0 {
		orderModelDetail.ChannelId = cast.ToInt32(orderModel.ChildChannelId)
	}

	out.OrderDetail = &orderModelDetail

	return &out, nil
}

// TODO del 阿闻管家-父订单详情-物流信息
func (o OrderService) AwenParentOrderDeliveryDetail(ctx context.Context, params *oc.AwenOrderDeliveryDetailRequest) (*oc.AwenOrderDeliveryDetailResponse, error) {
	out := oc.AwenOrderDeliveryDetailResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	var order models.Order
	_, err := dbConn.SQL("select a.*,b.accept_time,b.is_picking,b.picking_time from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryDetail查询订单信息失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "查询订单信息失败"
		return &out, nil
	}

	var deliverynodes []*oc.OrderDeliveryNodeModel
	if (order.ChannelId == ChannelAwenId || order.ChannelId == ChannelDigitalHealth) && order.DeliveryType == 3 {
		if order.CancelTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:      order.OrderMain.OrderSn,
				CreateTime:   kit.GetTimeNow(order.CancelTime),
				CancelReason: order.CancelReason,
				NodeDesc:     "订单已取消",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.ConfirmTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.ConfirmTime),
				NodeDesc:   "顾客已取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 && order.IsPicking == 1 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.PickingTime),
				NodeDesc:   "待顾客取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.AcceptTime),
				NodeDesc:   "商家接单",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
	} else {
		var nodes []models.OrderDeliveryNode
		session := dbConn.Table("`order_delivery_node`").
			Join("inner", "`order_main`", "`order_delivery_node`.order_sn=`order_main`.order_sn").
			Select(" `order_delivery_node`.order_sn,`order_delivery_node`.`delivery_status`,`order_delivery_node`.content,`order_delivery_node`.courier_name,`order_delivery_node`.courier_phone,`order_delivery_node`.cancel_reason,`order_delivery_node`.create_time").
			Where("`order_main`.id = ?", params.Orderid).
			Desc("`order_delivery_node`.create_time")
		if err = session.Find(&nodes); err != nil {
			glog.Errorf("AwenOrderDeliveryDetail订单配送流程查询失败！ order_id:%s ", params.Orderid)
			out.Code = 400
			out.Message = "订单配送流程查询失败"
			return &out, nil
		}
		var deliverynode *oc.OrderDeliveryNodeModel
		for _, item := range nodes {
			deliverynode = &oc.OrderDeliveryNodeModel{
				OrderSn:      item.OrderSn,
				CreateTime:   kit.GetTimeNow(item.CreateTime),
				NodeDesc:     item.Content,
				CourierName:  item.CourierName,
				CourierPhone: item.CourierPhone,
				CancelReason: item.CancelReason,
			}
			deliverynodes = append(deliverynodes, deliverynode)
		}
	}
	out.Deliverynodes = deliverynodes

	return &out, nil
}

// TODO del阿闻管家-父订单详情-发货状态
func (o OrderService) AwenParentOrderDeliveryState(ctx context.Context, params *oc.AwenOrderDeliveryStateRequest) (*oc.AwenOrderDeliveryStateResponse, error) {
	out := oc.AwenOrderDeliveryStateResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	order := models.Order{}
	ok, err := dbConn.SQL(`
	SELECT
	order_main.id,
	order_main.order_status,
	order_main.order_status_child,
	order_main.create_time,
	order_main.deliver_time,
	order_main.confirm_time,
	order_main.cancel_time,
	order_detail.expected_time,
	order_detail.accept_time,
	order_detail.picking_time,
	order_detail.pickup_code 
	FROM
	order_main
	INNER JOIN order_detail ON order_main.order_sn = order_detail.order_sn 
	WHERE id = ?`, params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单配送状态查询失败"
		return &out, nil
	}
	if !ok {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	deliverystate := oc.DeliveryState{
		OrderId:             cast.ToString(order.Id),
		OrderStatus:         order.OrderStatus,
		OrderStatusChild:    order.OrderStatusChild,
		SuggestDeliveryTime: kit.GetTimeNow(order.ExpectedTime),
		OrderCreateTime:     kit.GetTimeNow(order.CreateTime),
		OrderAcceptTime:     kit.GetTimeNow(order.AcceptTime),
		OrderDeliverTime:    kit.GetTimeNow(order.DeliverTime),
		OrderConfirmTime:    kit.GetTimeNow(order.ConfirmTime),
		OrderFinishTime:     kit.GetTimeNow(order.ConfirmTime),
		CancelTime:          kit.GetTimeNow(order.CancelTime),
		OrderPickingTime:    kit.GetTimeNow(order.PickingTime),
		PickupCode:          order.PickupCode,
	}
	//过滤时间默认值
	if deliverystate.SuggestDeliveryTime == "0001-01-01 00:00:00" {
		deliverystate.SuggestDeliveryTime = ""
	} else {
		//截取预计送达时间时间
		s := deliverystate.SuggestDeliveryTime[0:16] //去掉秒
		strArr := strings.Split(s, " ")
		deliverystate.SuggestDeliveryTime = strArr[1]
	}
	if deliverystate.OrderCreateTime == "0001-01-01 00:00:00" {
		deliverystate.OrderCreateTime = ""
	}
	if deliverystate.OrderAcceptTime == "0001-01-01 00:00:00" {
		deliverystate.OrderAcceptTime = ""
	}
	if deliverystate.OrderDeliverTime == "0001-01-01 00:00:00" {
		deliverystate.OrderDeliverTime = ""
	}
	if deliverystate.OrderConfirmTime == "0001-01-01 00:00:00" {
		deliverystate.OrderConfirmTime = ""
	}
	if deliverystate.OrderFinishTime == "0001-01-01 00:00:00" {
		deliverystate.OrderFinishTime = ""
	}
	if deliverystate.CancelTime == "0001-01-01 00:00:00" {
		deliverystate.CancelTime = ""
	}
	if deliverystate.OrderPickingTime == "0001-01-01 00:00:00" {
		deliverystate.OrderPickingTime = ""
	}

	out.Deliverystate = &deliverystate

	return &out, nil
}

// 阿闻管家-实物订单列表
func (o OrderService) AwenMaterOrderList(ctx context.Context, params *oc.AwenMaterOrderListRequest) (*oc.AwenMaterOrderListResponse, error) {
	out := oc.AwenMaterOrderListResponse{
		Code:    200,
		Message: "ok",
	}
	//连接池勿关闭
	dbConn := GetDBConn()
	session := dbConn.NewSession()
	defer session.Close()

	simpleGroup := &oc.SimpleOrderGroupActivity{}
	//订单表，商品表，品牌表, 业绩表关联查询
	if params.OrderGroupActivityId > 0 { // 团id
		// 检测团是否存在
		if _, err := dbConn.SQL("select id,member_name,receiver_mobile,receiver_address,commission_rate as dis_commission,status,dis_chain_name,final_take_type,end_time,deliver_days from `order_group_activity` where id = ?", params.OrderGroupActivityId).Get(simpleGroup); err != nil {
			glog.Error("AwenMaterOrderList团长拼团实物订单获取团活动出错：", err, params)
			return &out, err
		} else if simpleGroup.Id < 1 {
			err = errors.New("团信息不存在")
			glog.Error("AwenMaterOrderList团长拼团实物订单未查询到团信息：", err, params)
			return nil, err
		}
		session.Table("order_main").
			Join("inner", "order_main_group", "order_main_group.parent_order_sn=order_main.parent_order_sn").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
			Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
			Join("left", "dc_dispatch.warehouse w", "w.id = order_main.warehouse_id").
			Join("left", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id").
			Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn != ?", "").
			Where("order_main.org_id=?", params.OrgId)

		session.Where("order_main.order_type = ? and order_main_group.order_group_activity_id = ?", 15, params.OrderGroupActivityId)
	} else {
		session.Table("order_main").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
			Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
			Join("left", "dc_dispatch.warehouse w", "w.id = order_main.warehouse_id").
			Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn != ?", "")
	}
	session.And("`order_main`.is_virtual = 0")

	//订单搜索
	if len(params.Keyword) > 0 {
		if params.OrderGroupActivityId > 0 {
			switch params.SearchType {
			case 1: // 团员订单号（支持父子单号）
				session.And("`order_main`.order_sn = ? OR `order_main`.parent_order_sn = ?", params.Keyword, params.Keyword)
			case 2: //团员收货人姓名
				if simpleGroup.FinalTakeType == 1 {
					session.Where("order_main_group.receiver_name like ?", "%"+params.Keyword+"%")
				} else {
					session.Where("order_main.receiver_name like ?", "%"+params.Keyword+"%")
				}
			case 3: //团员收货人手机号
				if simpleGroup.FinalTakeType == 1 {
					session.Where("order_main_group.en_receiver_mobile  = ?", utils.MobileEncrypt(params.Keyword))
				} else {
					session.Where("order_main.en_receiver_phone= ?", utils.MobileEncrypt(params.Keyword))
				}
			case 4: //团员下单手机号
				session.Where("order_main.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
			default:
			}
		} else {
			switch params.SearchType {
			case 1: //子订单号
				session.And("`order_main`.order_sn like ?", params.Keyword+"%")
			case 2: //外部订单
				session.And("`order_main`.parent_order_sn like ?", params.Keyword+"%")
			case 3: //外部订单
				session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
			case 4: //收货人手机号
				session.And("order_main.en_receiver_phone= ?", utils.MobileEncrypt(params.Keyword))
			default: //default case
			}
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua",
			"`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.confirm_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.confirm_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}

	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}
	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 10:
			session.And("`order_main`.order_status = ?", params.OrderStatus)
		case 20201:
			session.In("`order_main`.order_status_child", 20201, 20204)
		default:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	//订单类型
	if len(params.OrderType) > 0 {
		if params.OrderType == OrderTypePickupOrder {
			session.Where("`order_detail`.pickup_station_id > 0")
		} else {
			session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
		}
	}

	//配送方式
	if params.DeliveryType > 0 {
		if params.DeliveryType == 2 {
			session.In("`order_main`.delivery_type", []int32{2, 5})
		} else {
			session.And("`order_main`.delivery_type = ?", params.DeliveryType)
		}
	}
	//支付方式
	if params.PayMode > 0 {
		session.And("order_main.pay_mode = ?", params.PayMode)
	}
	//店铺类型
	if params.AppChannel > 0 {
		if params.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}
	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}
	//自提点
	if params.PickupStationId > 0 {
		session.Where("`order_detail`.pickup_station_id", params.PickupStationId)
	}

	countSession := session.Clone()
	defer countSession.Close()
	totalCount, err := countSession.Distinct("`order_main`.id").Count()
	if err != nil {
		err = errors.New("数据库查询失败，" + err.Error())
		glog.Error(err)
		return nil, err
	}
	out.TotalCount = int32(totalCount)
	if out.TotalCount == 0 {
		out.Details = []*oc.MaterSimpleOrderList{}
		return &out, nil
	}
	selectStr := `CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.split_order_result,
		order_detail.split_order_fail_reason,
		order_detail.expected_time,
		order_detail.is_picking,
		order_detail.performance_staff_name,
		order_detail.performance_operator_time,
		order_detail.pickup_code,
		order_detail.buyer_memo,
		order_detail.shop_dis_member_id,
		order_detail.shop_dis_chain_id,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
        w.category
		`
	if params.OrderGroupActivityId > 0 {
		selectStr += `,
			order_group_activity.id as order_group_activity_id,
			order_group_activity.member_id as order_group_member_id,
			order_main_group.receiver_name as group_member_receiver_name,
			order_main_group.receiver_mobile as group_member_receiver_mobile,
			order_main_group.receiver_address as group_member_receiver_address`
	}

	if err = session.Select(selectStr).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1), `order_main`.`create_time` DESC ").
		Find(&out.Details); err != nil {
		glog.Error(err)
		return &out, err
	}

	var orderSns []string
	var shopDisMemberIds []string
	var shopDisChainId []int32
	var parentOrderSns []string

	for _, v := range out.Details {
		orderSns = append(orderSns, v.OrderSn)
		if v.ParentOrderSn != "" {
			parentOrderSns = append(parentOrderSns, v.ParentOrderSn)
		}
		if len(v.ShopDisMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.ShopDisMemberId)
		}
		if len(v.OrderGroupMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.OrderGroupMemberId)
		}
		if v.ShopDisChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.ShopDisChainId)
		}
	}

	orderProductMap := map[string][]*models.OrderProduct{}
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	upetMemberChainMap := map[string]*models.UpetMemberChain{}
	disCommissionMap := map[string]*models.OrderDis{}
	upetChainMap := make(map[int32]*models.UpetChain)
	orderGroupActivityMap := map[string]*models.OrderGroupActivityAndMainGroup{}

	orderPerformanceMap, _ := FindOrderPerformanceMapByOrderSn(dbConn, parentOrderSns)
	for _, v := range orderPerformanceMap {
		if v.PerformanceMemberId != "" {
			shopDisMemberIds = append(shopDisMemberIds, v.PerformanceMemberId)
		}
		if v.PerformanceChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.PerformanceChainId)
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(6)

	go func() {
		defer wg.Done()
		var dbproducts []*models.OrderProduct
		if err = dbConn.
			In("order_sn", orderSns).
			Find(&dbproducts); err != nil {
			glog.Error("数据库查询失败，", err.Error())
		}

		for _, v := range dbproducts {
			orderProductMap[v.OrderSn] = append(orderProductMap[v.OrderSn], v)
		}
	}()
	//3:查询优惠券从表，补全订单的优惠信息，1个订单可能下面对应多个商品
	go func() {
		defer wg.Done()
		var promotions []*models.OrderPromotion
		if err = dbConn.Select("*").Where("promotion_fee > 0").
			In("order_sn", orderSns).
			Find(&promotions); err != nil {
			glog.Error("数据库查询失败，", err.Error())
		}

		for _, v := range promotions {
			orderPromotionMap[v.OrderSn] = append(orderPromotionMap[v.OrderSn], v)
		}
	}()
	//4:拼团相关信息补充
	// 补全业绩 与 绑定门店的信息
	go func() {
		defer wg.Done()
		if len(shopDisMemberIds) > 0 {
			upetMemberChain := &models.UpetMemberChain{}
			upetMemberChainList, err := upetMemberChain.FindInScrmUserId(GetUPetDBConn(), shopDisMemberIds)
			if err != nil {
				glog.Error("订单列表查询业绩会员信息查询失败，", err.Error())
			}
			for k, v := range upetMemberChainList {
				upetMemberChainMap[v.ScrmUserId] = upetMemberChainList[k]
			}
		}
	}()

	// 补全团信息
	go func() {
		defer wg.Done()
		if len(parentOrderSns) > 0 {
			var orderGroupActivity []*models.OrderGroupActivityAndMainGroup
			if err = dbConn.Select("a.*,b.parent_order_sn").
				Table("order_group_activity").Alias("a").Join("INNER", "order_main_group as b", "a.id = b.order_group_activity_id").
				In("b.parent_order_sn", parentOrderSns).
				Find(&orderGroupActivity); err != nil {
				glog.Error("AwenParentOrderList实物订单列表查询团长制拼团的信息查询失败，", err.Error())
			}
			for k, v := range orderGroupActivity {
				orderGroupActivityMap[v.ParentOrderSn] = orderGroupActivity[k]
			}
		}
	}()

	// 补全佣金信息
	go func() {
		defer wg.Done()
		var orderDis []*models.OrderDis
		if err = dbConn.Table("order_dis").
			In("parent_order_sn", orderSns).
			Select("parent_order_sn, sum(commission) as commission").
			GroupBy("parent_order_sn").
			Find(&orderDis); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}
		for k, v := range orderDis {
			disCommissionMap[v.ParentOrderSn] = orderDis[k]
		}
	}()

	go func() {
		defer wg.Done()
		upetChainMap, _ = FindUpetChainMap(shopDisChainId)
	}()

	wg.Wait()

	for _, order := range out.Details {
		if order.PushThirdOrder > 0 || order.OrderStatus == 0 {
			order.PushThirdOrderReason = ""
		}

		if err = dbConn.Table("order_log").
			Select("*").
			Where("order_sn = ?", order.OrderSn).
			Desc("create_time").
			Find(&order.FootMarkModel); err != nil {
			glog.Warningf("父订单脚列表印详情查询失败！ order_sn:%s ", order.OrderSn)
		}
		if len(order.FootMarkModel) > 0 {
			for _, v := range order.FootMarkModel {
				v.LogName = v.OperateTypeName + models.OrderLodMap[int(v.LogType)]
			}
		}

		if order.OrderType == 3 {
			order.ReceiverName = ""
		}

		order.IsPartButton = 1
		//是否显示部分退款按钮
		if len(order.ConfirmTime) > 0 {
			confirmTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, order.ConfirmTime, time.Local)
			d, _ := time.ParseDuration("-24h")
			now := time.Now()
			if confirmTime.Before(now.Add(d)) {
				order.IsPartButton = 0
			}
		}

		//预计送达时间，不需要秒
		if len(order.ExpectedTime) > 16 {
			order.ExpectedTime = order.ExpectedTime[0:16]
		}

		//预计送达时间，如果是实物订单，则只显示时分秒，其他订单类型显示完整日期
		if order.OrderType == 1 {
			if len(order.ExpectedTime) > 0 {
				s := order.ExpectedTime
				strArr := strings.Split(s, " ")
				order.ExpectedTime = strArr[1]
			}
		}

		var productModel *oc.MaterSimpleOrderProductList
		order.OrderProductModel = make([]*oc.MaterSimpleOrderProductList, 0)

		//查找订单的商品
		for _, v := range orderProductMap[order.OrderSn] {
			productModel = &oc.MaterSimpleOrderProductList{
				Id:            cast.ToString(v.Id),
				OrderSn:       v.OrderSn,
				ProductId:     v.ProductId,
				ProductType:   v.ProductType,
				ParentSkuId:   v.ParentSkuId,
				ProductName:   v.ProductName,
				Image:         v.Image,
				SkuId:         v.SkuId,
				ThirdSkuId:    v.ThirdSkuId,
				MarkingPrice:  v.MarkingPrice,
				PayPrice:      v.PayPrice,
				DiscountPrice: v.DiscountPrice,
				Number:        v.Number,
				PaymentTotal:  v.PaymentTotal,
				BarCode:       v.BarCode,
				//ParentProductName: v.ParentProductName,
			}
			if len(v.ParentSkuId) > 0 {
				var product models.OrderProduct
				if _, err := dbConn.Table("order_product").
					Where("sku_id = ?", v.ParentSkuId).
					Get(&product); err == nil {
					productModel.ParentProductName = product.ProductName
				}
			}
			order.OrderProductModel = append(order.OrderProductModel, productModel)
		}

		//团长拼团制的一些信息
		if order.OrderType == 15 {
			order.GroupActivityModel = new(oc.SimpleOrderGroupActivity)
			if g, ok := orderGroupActivityMap[order.ParentOrderSn]; ok {
				order.GroupActivityModel.EndTime = g.EndTime.AddDate(0, 0, int(g.DeliverDays)).Format(kit.DATE_LAYOUT_SHORT_CN)
				order.GroupActivityModel.DeliverDays = g.DeliverDays
				order.GroupActivityModel.Id = g.Id
				order.GroupActivityModel.Status = g.Status
				order.GroupActivityModel.FinalTakeType = g.FinalTakeType
				order.GroupActivityModel.DisChainName = g.DisChainName
				order.GroupActivityModel.MemberName = g.MemberName
				order.GroupActivityModel.ReceiverAddress = g.ReceiverAddress
				order.GroupActivityModel.ReceiverMobile = g.ReceiverMobile
			}
			if v, ok := disCommissionMap[order.OrderSn]; ok {
				order.GroupActivityModel.DisCommission = cast.ToInt32(v.Commission)
			} else {
				order.GroupActivityModel.DisCommission = 0
			}
			if v, ok := upetMemberChainMap[order.OrderGroupMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.GroupActivityModel.StaffName = v.BillUserName
				} else {
					order.GroupActivityModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
			}
			//业绩的信息
			order.ShopDisModel = &oc.SimpleOrderShopDis{}
			if v, ok := upetMemberChainMap[order.ShopDisMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.ShopDisModel.StaffName = v.BillUserName
				} else {
					order.ShopDisModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
				order.ShopDisModel.MemberAreaName = v.MemberAreaName
			}
			if v, ok := upetChainMap[order.ShopDisChainId]; ok {
				order.ShopDisModel.ChainName = v.ChainName
				order.ShopDisModel.FinanceCode = v.AccountId
			}

			//如果非团长待收，处理一下团员地址
			if order.GroupActivityModel.FinalTakeType == 0 {
				order.GroupMemberReceiverName = order.ReceiverName
				order.GroupMemberReceiverMobile = order.ReceiverMobile
				order.GroupMemberReceiverAddress = order.ReceiverAddress
			}
		} else {
			if v, ok := orderPerformanceMap[order.ParentOrderSn]; ok {
				order.PerformanceStaffName = v.StaffName
				order.PerformanceOperatorName = v.OperatorName
				order.PerformanceOperatorTime = v.CreateTime.Format(kit.DATETIME_LAYOUT)
				if v2, ok := upetChainMap[v.PerformanceChainId]; ok {
					order.PerformanceChainName = v2.ChainName
					order.PerformanceFinanceCode = v2.AccountId
				} else if order.PerformanceOperatorName != "系统分配" {
					order.PerformanceChainName = order.ShopName
					order.PerformanceFinanceCode = order.ShopId
				}
			}
		}
	}
	return &out, nil
}

// 阿闻管家-实物订单详情-基础信息
func (o OrderService) AwenMaterOrderBaseDetail(ctx context.Context, params *oc.AwenAllOrderBaseDetailRequest) (*oc.AwenMaterOrderBaseDetailResponse, error) {
	out := oc.AwenMaterOrderBaseDetailResponse{Code: 200, Message: "ok"}
	dbConn := GetDBConn()

	o.session = dbConn.NewSession()

	orderModel := &models.Order{}
	if ok, err := dbConn.SQL("select * from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.OrderId).
		Get(orderModel); err != nil {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "查询订单详情失败"
		return &out, nil
	} else if !ok {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	orderSn := orderModel.OrderMain.OrderSn
	parentOrder := GetOrderMainByOrderSn(orderModel.ParentOrderSn)
	orderModelDetail := oc.MaterCombineOrderDetail{
		OrderId:              int32(orderModel.Id),
		OrderSn:              orderSn,
		OldOrderSn:           orderModel.OldOrderSn,
		PaySn:                orderModel.PaySn,
		CreateTime:           kit.GetTimeNow(orderModel.CreateTime),
		OrderType:            orderModel.OrderType,
		ChannelId:            orderModel.ChannelId,
		PerformanceStaffName: orderModel.PerformanceStaffName,
		OrderStatus:          orderModel.OrderStatus,
		OrderStatusChild:     orderModel.OrderStatusChild,
		DeliveryType:         orderModel.DeliveryType,
		ReceiverName:         orderModel.ReceiverName,
		ReceiverState:        orderModel.ReceiverState,
		ReceiverCity:         orderModel.ReceiverCity,
		ReceiverDistrict:     orderModel.ReceiverDistrict,
		ReceiverAddress:      orderModel.ReceiverAddress,
		ReceiverPhone:        orderModel.ReceiverPhone,
		EnReceiverPhone:      orderModel.EnReceiverPhone,
		DeliveryRemark:       orderModel.DeliveryRemark,
		Freight:              parentOrder.Freight,
		PackingCost:          parentOrder.PackingCost,
		ServiceCharge:        parentOrder.ServiceCharge,
		ContractFee:          parentOrder.ContractFee,
		FreightPrivilege:     parentOrder.FreightPrivilege,
		PtPrivilege:          parentOrder.PtChargeTotal,
		PtFreightPrivilege:   orderModel.PtFreightPrivilege,
		GoodsPayTotal:        orderModel.GoodsPayTotal,
		UserReceiveTotal:     orderModel.PayTotal,
		ActualReceiveTotal:   orderModel.ActualReceiveTotal,
		ParentOrderSn:        orderModel.ParentOrderSn,
		BuyerMemo:            orderModel.BuyerMemo,
		LogisticsCode:        orderModel.LogisticsCode,
		UserAgent:            orderModel.UserAgent,
		ShopName:             orderModel.ShopName,
		ShopId:               orderModel.ShopId,
		ExpectedTime:         orderModel.ExpectedTime.Format(kit.DATE_LAYOUT + " " + kit.TIME_LAYOUT_SHORT),
		PayMode:              orderModel.PayMode,
		MemberId:             orderModel.MemberId,
		MemberTel:            orderModel.MemberTel,
		EnMemberTel:          orderModel.EnMemberTel,
		Source:               orderModel.Source,
	}

	o.orderMain = GetOrderMainByOrderSn(orderModel.OrderSn)

	//优惠活动解析
	var PresentPayedAmount int32 //商家承担的优惠金额
	var orderPromotions []models.OrderPromotion
	//查询活动
	if err := dbConn.Where("order_sn = ? ", parentOrder.OrderSn).
		Find(&orderPromotions); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "查询订单商品优惠信息失败"
		return &out, nil
	} else if len(orderPromotions) > 0 {
		for _, item := range orderPromotions {
			PresentPayedAmount += item.PoiCharge
			active := oc.OrderPrivilegeModel{}
			active.ActiveName = dto.OrderPrivilegeActiveType(item.PromotionType).String()
			if orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth {
				active.ActiveName = item.PromotionTitle //如果是阿闻到家，则取表里面的活动名
			}
			if active.ActiveName == "UNKNOWN" {
				active.ActiveName = item.PromotionTitle
			}
			active.ReduceFee = cast.ToString(item.PromotionFee)
			active.MtCharge = cast.ToString(item.PtCharge)
			active.PoiCharge = cast.ToString(item.PoiCharge)
			active.Remark = item.PromotionTitle
			orderModelDetail.OrderPrivilegeModel = append(orderModelDetail.OrderPrivilegeModel, &active)
		}
	}

	if !orderModel.PayTime.IsZero() {
		orderModelDetail.PayTime = kit.GetTimeNow(orderModel.PayTime)
	}

	if (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.DeliveryType == 3 {
		orderModelDetail.ReceiverName = ""
	}

	//协程处理
	var wg = new(sync.WaitGroup)
	wg.Add(3)

	go func() {
		defer func() {
			wg.Done()
		}()
		//取配送流程最新一条记录
		delivery := models.OrderDeliveryRecord{}
		if ok, err := dbConn.Where("order_sn = ?", orderSn).
			Desc("create_time").
			Get(&delivery); err != nil {
			glog.Errorf("AwenOrderBaseDetail订单配送详情查询失败！ order_id:%s order_sn:%s", params.OrderId, orderSn)
			return
		} else if ok {
			orderModelDetail.DeliveryId = delivery.DeliveryId
			deliveryNode := models.OrderDeliveryNode{}
			if ok, err := dbConn.Where("delivery_id = ?", delivery.DeliveryId).
				Desc("id").
				Get(&deliveryNode); err != nil {
				glog.Errorf("AwenOrderBaseDetail订单配送详情查询失败！ order_id:%s order_sn:%s", params.OrderId, orderSn)
				return
			} else if ok {
				orderModelDetail.DeliveryStatus = deliveryNode.DeliveryStatus
				if len(deliveryNode.CourierName) > 0 {
					orderModelDetail.CourierName = deliveryNode.CourierName
					orderModelDetail.CourierPhone = deliveryNode.CourierPhone
				}
			}
		}
	}()

	go func() {
		defer func() {
			wg.Done()
		}()
		//获取脚印信息
		if err := dbConn.Table("order_log").
			Select("*").
			Where("order_sn = ?", orderSn).
			In("log_type", models.ForwardLogSlice).
			Desc("id").
			Find(&orderModelDetail.FootMarkModel); err != nil {
			glog.Errorf("AwenParentOrderBaseDetail订单脚印详情查询失败！ order_id:%s; order_sn:%s", params.OrderId, orderSn)
			return
		}
		if len(orderModelDetail.FootMarkModel) > 0 {
			var logType int
			for _, v := range orderModelDetail.FootMarkModel {
				logType = int(v.LogType)
				v.LogName = v.OperateTypeName + models.OrderLodMap[logType]
				if logType == models.OrderLogPushedOrder {
					if orderModel.Source == 4 {
						v.LogName = "已成功推送至OMS系统"
					} else if orderModel.Source == 3 {
						v.LogName = "已成功推送至子龙系统"
					} else if orderModel.Source == 5 {
						v.LogName = "已成功推送至巨益OMS"
					}
				} else if logType == models.OrderLogPushOrderFailed {
					if orderModel.Source == 4 {
						v.LogName = "推送OMS系统失败"
					} else if orderModel.Source == 3 {
						v.LogName = "推送子龙系统失败"
					} else if orderModel.Source == 5 {
						v.LogName = "推送巨益OMS失败"
					}
				}
			}
		}
	}()

	//物流信息
	var orderExpress []*models.OrderExpress
	go func() {
		defer func() {
			wg.Done()
		}()
		if orderModel.DeliveryType != 1 {
			return
		}
		dbConn.Table("order_express").Where("order_sn=?", orderModel.OrderSn).Find(&orderExpress)
		for _, v := range orderExpress {
			orderModelDetail.Express = append(orderModelDetail.Express, &oc.OrderExpress{
				Id:          cast.ToInt32(v.Id),
				ExpressNo:   v.ExpressNo,
				ExpressName: v.ExpressName,
				Num:         v.Num,
			})
		}
	}()

	wg.Wait()

	//获取积分
	var integralData models.OrderIntegral
	if _, err := dbConn.Table("order_integral").Where("order_sn=?", orderSn).Get(&integralData); err != nil {
		glog.Errorf("AwenParentOrderBaseDetail积分查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "查询订积分失败"
		return &out, nil
	}
	orderModelDetail.Integral = int32(integralData.Integral)
	orderModelDetail.IntegralType = int32(integralData.IntegralType)

	//var OriginGoodsTotal int32
	//订单商品列表信息
	var orderProducts []models.OrderProduct
	if err := dbConn.Table("order_product").
		Where("order_sn = ?", orderSn).
		Find(&orderProducts); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "查询订单商品信息失败"
		return &out, nil
	} else if len(orderProducts) > 0 {
		var productModelSlice []*oc.MaterSimpleOrderProductList
		var productModel *oc.MaterSimpleOrderProductList

		for _, v := range orderProducts {
			productModel = &oc.MaterSimpleOrderProductList{
				Id:            cast.ToString(v.Id),
				OrderSn:       v.OrderSn,
				ProductId:     v.ProductId,
				ProductType:   v.ProductType,
				ParentSkuId:   v.ParentSkuId,
				PrivilegePt:   v.PrivilegePt,
				ProductName:   v.ProductName,
				Image:         v.Image,
				SkuId:         v.SkuId,
				ThirdSkuId:    v.ThirdSkuId,
				MarkingPrice:  v.MarkingPrice,
				DiscountPrice: v.DiscountPrice,
				PayPrice:      v.PayPrice,
				Number:        v.Number,
				PaymentTotal:  v.PaymentTotal,
				BarCode:       v.BarCode,
			}
			if len(v.ParentSkuId) > 0 {
				var product models.OrderProduct
				if _, err := dbConn.Table("order_product").
					Where("sku_id = ?", v.ParentSkuId).
					Get(&product); err == nil {
					productModel.ParentProductName = product.ProductName
				}
			}

			productModelSlice = append(productModelSlice, productModel)
		}
		orderModelDetail.OrderProductModel = productModelSlice
	}

	// 物竞天择渠道
	if orderModel.ChannelId == 4 && len(orderModel.ChildChannelId) > 0 {
		orderModelDetail.ChannelId = cast.ToInt32(orderModel.ChildChannelId)
	}

	out.OrderDetail = &orderModelDetail

	return &out, nil
}

// TODO del阿闻管家-实物订单详情-物流信息
func (o OrderService) AwenMaterOrderDeliveryDetail(ctx context.Context, params *oc.AwenOrderDeliveryDetailRequest) (*oc.AwenOrderDeliveryDetailResponse, error) {
	out := oc.AwenOrderDeliveryDetailResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	var order models.Order
	_, err := dbConn.SQL("select a.*,b.accept_time,b.is_picking,b.picking_time from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryDetail查询订单信息失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "查询订单信息失败"
		return &out, nil
	}

	var deliverynodes []*oc.OrderDeliveryNodeModel
	if (order.ChannelId == ChannelAwenId || order.ChannelId == ChannelDigitalHealth) && order.DeliveryType == 3 {
		if order.CancelTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:      order.OrderMain.OrderSn,
				CreateTime:   kit.GetTimeNow(order.CancelTime),
				CancelReason: order.CancelReason,
				NodeDesc:     "订单已取消",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.ConfirmTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.ConfirmTime),
				NodeDesc:   "顾客已取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 && order.IsPicking == 1 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.PickingTime),
				NodeDesc:   "待顾客取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.AcceptTime),
				NodeDesc:   "商家接单",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
	} else {
		var nodes []models.OrderDeliveryNode
		session := dbConn.Table("`order_delivery_node`").
			Join("inner", "`order_main`", "`order_delivery_node`.order_sn=`order_main`.order_sn").
			Select(" `order_delivery_node`.order_sn,`order_delivery_node`.`delivery_status`,`order_delivery_node`.content,`order_delivery_node`.courier_name,`order_delivery_node`.courier_phone,`order_delivery_node`.cancel_reason,`order_delivery_node`.create_time").
			Where("`order_main`.id = ?", params.Orderid).
			Desc("`order_delivery_node`.create_time")
		if err = session.Find(&nodes); err != nil {
			glog.Errorf("AwenOrderDeliveryDetail订单配送流程查询失败！ order_id:%s ", params.Orderid)
			out.Code = 400
			out.Message = "订单配送流程查询失败"
			return &out, nil
		}
		var deliverynode *oc.OrderDeliveryNodeModel
		for _, item := range nodes {
			deliverynode = &oc.OrderDeliveryNodeModel{
				OrderSn:      item.OrderSn,
				CreateTime:   kit.GetTimeNow(item.CreateTime),
				NodeDesc:     item.Content,
				CourierName:  item.CourierName,
				CourierPhone: item.CourierPhone,
				CancelReason: item.CancelReason,
			}
			deliverynodes = append(deliverynodes, deliverynode)
		}
	}
	out.Deliverynodes = deliverynodes

	return &out, nil
}

// TODO del阿闻管家-实物订单详情-发货状态
func (o OrderService) AwenMaterOrderDeliveryState(ctx context.Context, params *oc.AwenOrderDeliveryStateRequest) (*oc.AwenOrderDeliveryStateResponse, error) {
	out := oc.AwenOrderDeliveryStateResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	order := models.Order{}
	ok, err := dbConn.SQL(`
	SELECT
	order_main.id,
	order_main.order_status,
	order_main.order_status_child,
	order_main.create_time,
	order_main.deliver_time,
	order_main.confirm_time,
	order_main.cancel_time,
	order_detail.expected_time,
	order_detail.accept_time,
	order_detail.picking_time,
	order_detail.pickup_code 
	FROM
	order_main
	INNER JOIN order_detail ON order_main.order_sn = order_detail.order_sn 
	WHERE id = ?`, params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单配送状态查询失败"
		return &out, nil
	}
	if !ok {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	deliverystate := oc.DeliveryState{
		OrderId:             cast.ToString(order.Id),
		OrderStatus:         order.OrderStatus,
		OrderStatusChild:    order.OrderStatusChild,
		SuggestDeliveryTime: kit.GetTimeNow(order.ExpectedTime),
		OrderCreateTime:     kit.GetTimeNow(order.CreateTime),
		OrderAcceptTime:     kit.GetTimeNow(order.AcceptTime),
		OrderDeliverTime:    kit.GetTimeNow(order.DeliverTime),
		OrderConfirmTime:    kit.GetTimeNow(order.ConfirmTime),
		OrderFinishTime:     kit.GetTimeNow(order.ConfirmTime),
		CancelTime:          kit.GetTimeNow(order.CancelTime),
		OrderPickingTime:    kit.GetTimeNow(order.PickingTime),
		PickupCode:          order.PickupCode,
	}
	//过滤时间默认值
	if deliverystate.SuggestDeliveryTime == "0001-01-01 00:00:00" {
		deliverystate.SuggestDeliveryTime = ""
	} else {
		//截取预计送达时间时间
		s := deliverystate.SuggestDeliveryTime[0:16] //去掉秒
		strArr := strings.Split(s, " ")
		deliverystate.SuggestDeliveryTime = strArr[1]
	}
	if deliverystate.OrderCreateTime == "0001-01-01 00:00:00" {
		deliverystate.OrderCreateTime = ""
	}
	if deliverystate.OrderAcceptTime == "0001-01-01 00:00:00" {
		deliverystate.OrderAcceptTime = ""
	}
	if deliverystate.OrderDeliverTime == "0001-01-01 00:00:00" {
		deliverystate.OrderDeliverTime = ""
	}
	if deliverystate.OrderConfirmTime == "0001-01-01 00:00:00" {
		deliverystate.OrderConfirmTime = ""
	}
	if deliverystate.OrderFinishTime == "0001-01-01 00:00:00" {
		deliverystate.OrderFinishTime = ""
	}
	if deliverystate.CancelTime == "0001-01-01 00:00:00" {
		deliverystate.CancelTime = ""
	}
	if deliverystate.OrderPickingTime == "0001-01-01 00:00:00" {
		deliverystate.OrderPickingTime = ""
	}

	out.Deliverystate = &deliverystate

	return &out, nil
}

// 阿闻管家-虚拟订单列表
func (o OrderService) AwenVirtualOrderList(ctx context.Context, params *oc.AwenVirtualOrderListRequest) (*oc.AwenVirtualOrderListResponse, error) {
	out := oc.AwenVirtualOrderListResponse{
		Code:    200,
		Message: "ok",
	}

	//连接池勿关闭
	dbConn := GetDBConn()
	session := dbConn.NewSession()
	defer session.Close()

	simpleGroup := &oc.SimpleOrderGroupActivity{}
	//订单表，商品表，品牌表, 业绩表关联查询
	if params.OrderGroupActivityId > 0 {
		if _, err := dbConn.SQL("select id,member_id,member_name,receiver_mobile,en_receiver_mobile,receiver_address,commission_rate as dis_commission,status,dis_chain_name,final_take_type,end_time,deliver_days from `order_group_activity` where id = ?", params.OrderGroupActivityId).Get(simpleGroup); err != nil {
			glog.Error("AwenMaterOrderList团长拼团实物订单获取团活动出错：", err, params)
			return &out, err
		} else if simpleGroup.Id < 1 {
			err = errors.New("团信息不存在")
			glog.Error("AwenMaterOrderList团长拼团实物订单未查询到团信息：", err, params)
			return nil, err
		}
		session.Table("order_main").
			Join("inner", "order_main_group", "order_main_group.parent_order_sn=order_main.parent_order_sn").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			Join("left", "`datacenter`.`pickup_station`", "pickup_station.id=order_detail.pickup_station_id").
			Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
			Join("left", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id").
			Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn != ?", "")
		session.Where("order_main.order_type = ? and order_main_group.order_group_activity_id = ?", 15, params.OrderGroupActivityId)
	} else {
		session.Table("order_main").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			Join("inner", "order_product", "`order_main`.order_sn=order_product.order_sn").
			Join("inner", "order_verify_code", "order_main.order_sn=order_verify_code.order_sn").
			Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn != ?", "")
	}
	session.And("`order_main`.is_virtual = 1")

	//订单搜索
	if len(params.Keyword) > 0 {
		if params.OrderGroupActivityId > 0 {
			switch params.SearchType {
			case 1: // 团员订单号（支持父子单号）
				session.And("`order_main`.order_sn = ? OR `order_main`.parent_order_sn = ?", params.Keyword, params.Keyword)
			case 2: //团员收货人姓名
				if simpleGroup.FinalTakeType == 1 {
					session.Where("order_main_group.receiver_name like ?", "%"+params.Keyword+"%")
				} else {
					session.Where("order_main.receiver_name like ?", "%"+params.Keyword+"%")
				}
			case 3: //团员收货人手机号
				if simpleGroup.FinalTakeType == 1 {
					session.Where("order_main.en_receiver_mobile= ?", utils.MobileEncrypt(params.Keyword))
				} else {
					session.Where("order_main.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
				}
			case 4: //团员下单手机号
				session.Where("order_main.en_member_tel = ?", utils.MobileEncrypt(params.Keyword))
			default:
			}
		} else {
			switch params.SearchType {
			case 1: //子订单号
				session.And("`order_main`.order_sn like ?", params.Keyword+"%")
			case 2: //外部订单
				session.And("`order_main`.parent_order_sn like ?", params.Keyword+"%")
			case 3: //外部订单
				session.And("`order_main`.old_order_sn like ?", params.Keyword+"%")
			case 4: //收货人手机号
				session.Where("order_main.en_receiver_phone = ?", utils.MobileEncrypt(params.Keyword))
			default: //default case
			}
		}
	}

	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.`store_user_authority` sua", "`order_main`.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	if params.TimeType == 1 {
		session.And("order_verify_code.verify_status = 1")
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_verify_code`.verify_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_verify_code`.verify_time <= ?", params.EndTime)
		}
	} else if params.TimeType == 2 { //核销时间
		session.And("order_verify_code.verify_status = 1")
		//完成时间
		if len(params.StartTime) > 0 {
			session.And("`order_verify_code`.verify_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_verify_code`.verify_time <= ?", params.EndTime)
		}
	} else {
		//下单时间
		if len(params.StartTime) > 0 {
			session.And("`order_main`.create_time > ?", params.StartTime)
		}
		if len(params.EndTime) > 0 {
			session.And("`order_main`.create_time <= ?", params.EndTime)
		}
	}

	//商品名称
	if len(params.ProductName) > 0 {
		session.And("`order_product`.product_name like ?", "%"+params.ProductName+"%")
	}

	//订单来源
	if params.ChannelId > 0 {
		if params.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(params.ChannelId))
		} else {
			if params.ChannelId == 4 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", params.ChannelId)
			}
		}
	}

	//订单状态
	if params.OrderStatus > 0 {
		switch params.OrderStatus {
		case 30100:
			session.And("`order_main`.order_status = 0")
		case 30101:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30102:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		case 30103:
			session.And("`order_main`.order_status_child = ?", params.OrderStatus)
		}
	}

	//订单类型
	if len(params.OrderType) > 0 {
		session.In("`order_main`.order_type", strings.Split(params.OrderType, ","))
	}

	//支付方式
	if params.PayMode > 0 {
		session.And("`order_main`.pay_mode = ?", params.PayMode)
	}

	//如果选择的是完成时间，把未全部核销的订单筛除
	if params.TimeType == 1 { //完成时间
		session.And("`order_main`.order_status_child=30103")
	}

	//登录用户有权限的所有门店id(财务编码)
	if len(params.Shopids) > 0 {
		session.In("`order_main`.shop_id", params.Shopids)
	}

	countSession := session.Clone()
	defer countSession.Close()
	totalCount, err := countSession.Distinct("`order_main`.id").Count()
	if err != nil {
		err = errors.New("数据库查询失败，" + err.Error())
		glog.Error(err)
		return nil, err
	}
	out.TotalCount = int32(totalCount)
	if out.TotalCount == 0 {
		out.Details = []*oc.VirtualSimpleOrderList{}
		return &out, nil
	}

	selectStr := `CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.split_order_result,
		order_detail.split_order_fail_reason,
		order_detail.expected_time,
		order_detail.is_picking,
		order_detail.performance_staff_name,
		order_detail.performance_operator_time,
		order_detail.pickup_code,
		order_detail.shop_dis_member_id,
		order_detail.shop_dis_chain_id,
		order_detail.buyer_memo
		`
	if params.OrderGroupActivityId > 0 {
		selectStr += `,
      		order_group_activity.id as order_group_activity_id,
			order_group_activity.member_id as order_group_member_id,
			order_main_group.receiver_name as group_member_receiver_name,
			order_main_group.receiver_mobile as group_member_receiver_mobile,
			order_main_group.receiver_address as group_member_receiver_address`
	}

	if err = session.Select(selectStr).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("`order_main`.id").
		OrderBy("if(`order_main`.order_status_child=20101, 0, 1), if(`order_detail`.push_third_order=0 and `order_main`.order_status=20, 0, 1), if(`order_main`.order_type in (2,3) and `order_main`.order_status NOT IN (0, 30), 0, 1), `order_main`.`create_time` DESC ").
		Find(&out.Details); err != nil {
		glog.Error(err)
		return &out, err
	}

	var orderSns []string
	var shopDisMemberIds []string
	var shopDisChainId []int32
	var parentOrderSns []string
	for _, v := range out.Details {
		orderSns = append(orderSns, v.OrderSn)
		if v.ParentOrderSn != "" {
			parentOrderSns = append(parentOrderSns, v.ParentOrderSn)
		}
		if len(v.ShopDisMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.ShopDisMemberId)
		}
		if len(v.OrderGroupMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.OrderGroupMemberId)
		}
		if v.ShopDisChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.ShopDisChainId)
		}
	}

	orderProductMap := map[string][]*models.OrderProduct{}
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	upetMemberChainMap := map[string]*models.UpetMemberChain{}
	disCommissionMap := map[string]*models.OrderDis{}
	upetChainMap := make(map[int32]*models.UpetChain)
	orderGroupActivityMap := map[string]*models.OrderGroupActivityAndMainGroup{}

	orderPerformanceMap, _ := FindOrderPerformanceMapByOrderSn(dbConn, parentOrderSns)
	for _, v := range orderPerformanceMap {
		if v.PerformanceMemberId != "" {
			shopDisMemberIds = append(shopDisMemberIds, v.PerformanceMemberId)
		}
		if v.PerformanceChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.PerformanceChainId)
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(6)
	//2:查询商品从表，补全订单的商品信息，1个订单可能下面对应多个商品
	go func() {
		defer wg.Done()
		var dbproducts []*models.OrderProduct
		if err = dbConn.
			In("order_sn", orderSns).
			Find(&dbproducts); err != nil {
			glog.Error("数据库查询失败，", err.Error())
		}

		for _, v := range dbproducts {
			orderProductMap[v.OrderSn] = append(orderProductMap[v.OrderSn], v)
		}
	}()
	//3:查询优惠券从表，补全订单的优惠信息，1个订单可能下面对应多个商品
	go func() {
		defer wg.Done()
		var promotions []*models.OrderPromotion
		if err = dbConn.Select("").
			In("order_sn", orderSns).
			Find(&promotions); err != nil {
			glog.Error("数据库查询失败，", err.Error())
		}

		for _, v := range promotions {
			orderPromotionMap[v.OrderSn] = append(orderPromotionMap[v.OrderSn], v)
		}
	}()
	//4:拼团相关信息补充
	// 补全业绩 与 绑定门店的信息
	go func() {
		defer wg.Done()
		if len(shopDisMemberIds) > 0 {
			upetMemberChain := &models.UpetMemberChain{}
			upetMemberChainList, err := upetMemberChain.FindInScrmUserId(GetUPetDBConn(), shopDisMemberIds)
			if err != nil {
				glog.Error("订单列表查询业绩会员信息查询失败，", err.Error())
			}
			for k, v := range upetMemberChainList {
				upetMemberChainMap[v.ScrmUserId] = upetMemberChainList[k]
			}
		}
	}()

	// 补全团信息
	go func() {
		defer wg.Done()
		if len(parentOrderSns) > 0 {
			var orderGroupActivity []*models.OrderGroupActivityAndMainGroup
			if err = dbConn.Select("a.*,b.parent_order_sn").
				Table("order_group_activity").Alias("a").Join("INNER", "order_main_group as b", "a.id = b.order_group_activity_id").
				In("b.parent_order_sn", parentOrderSns).
				Find(&orderGroupActivity); err != nil {
				glog.Error("AwenParentOrderList实物订单列表查询团长制拼团的信息查询失败，", err.Error())
			}
			for k, v := range orderGroupActivity {
				orderGroupActivityMap[v.ParentOrderSn] = orderGroupActivity[k]
			}
		}
	}()

	// 补全佣金信息
	go func() {
		defer wg.Done()
		var orderDis []*models.OrderDis
		if err = dbConn.Table("order_dis").
			In("parent_order_sn", orderSns).
			Select("parent_order_sn, sum(commission) as commission").
			GroupBy("parent_order_sn").
			Find(&orderDis); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}
		for k, v := range orderDis {
			disCommissionMap[v.ParentOrderSn] = orderDis[k]
		}
	}()

	go func() {
		defer wg.Done()
		upetChainMap, _ = FindUpetChainMap(shopDisChainId)
	}()
	wg.Wait()

	now := time.Now()
	for _, order := range out.Details {
		//订单状态判断逻辑和前端约定：order_status_child:30100已取消,30101待核销,30102部分核销,30103已完成(已核销)
		if order.OrderStatus == 0 {
			order.OrderStatusChild = 30100
		}
		if err = dbConn.Table("order_log").
			Select("*").
			Where("order_sn = ?", order.OrderSn).
			Desc("create_time").
			Find(&order.FootMarkModel); err != nil {
			glog.Errorf("父订单脚列表印详情查询失败！ order_sn:%s ", order.OrderSn)
			out.Code = 400
			out.Message = "查询订单脚印详情失败"
			return &out, nil
		}
		if len(order.FootMarkModel) > 0 {
			for _, v := range order.FootMarkModel {
				v.LogName = models.OrderLodMap[int(v.LogType)]
			}
		}

		if order.OrderType == 3 {
			order.ReceiverName = ""
		}
		order.IsPartButton = 1
		//是否显示部分退款按钮
		if len(order.ConfirmTime) > 0 {
			confirmTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, order.ConfirmTime, time.Local)
			d, _ := time.ParseDuration("-24h")
			if confirmTime.Before(now.Add(d)) {
				order.IsPartButton = 0
			}
		}
		//预计送达时间，不需要秒
		if len(order.ExpectedTime) > 16 {
			order.ExpectedTime = order.ExpectedTime[0:16]
		}

		//预计送达时间，如果是实物订单，则只显示时分秒，其他订单类型显示完整日期
		if order.OrderType == 1 {
			if len(order.ExpectedTime) > 0 {
				s := order.ExpectedTime
				strArr := strings.Split(s, " ")
				order.ExpectedTime = strArr[1]
			}
		}

		var productModel *oc.VirtualSimpleOrderProductList
		order.OrderProductModel = make([]*oc.VirtualSimpleOrderProductList, 0)

		//order.GoodsTotal = 0
		//查找订单的商品
		for _, v := range orderProductMap[order.OrderSn] {
			productModel = &oc.VirtualSimpleOrderProductList{
				Id:            cast.ToString(v.Id),
				OrderSn:       v.OrderSn,
				ProductId:     v.ProductId,
				ProductType:   v.ProductType,
				ParentSkuId:   v.ParentSkuId,
				ProductName:   v.ProductName,
				Image:         v.Image,
				SkuId:         v.SkuId,
				ThirdSkuId:    v.ThirdSkuId,
				MarkingPrice:  v.MarkingPrice,
				PayPrice:      v.PayPrice,
				DiscountPrice: v.DiscountPrice,
				Number:        v.Number,
				PaymentTotal:  v.PaymentTotal,
				BarCode:       v.BarCode,
			}
			if len(v.ParentSkuId) > 0 {
				var product models.OrderProduct
				if _, err := dbConn.Table("order_product").
					Where("sku_id = ?", v.ParentSkuId).
					Get(&product); err == nil {
					productModel.ParentProductName = product.ProductName
				}
			}
			order.OrderProductModel = append(order.OrderProductModel, productModel)
		}

		//优惠活动解析
		var PresentPayedAmount, PlatformPayedAmount int32 //商家承担的优惠金额, 美团平台承担的优惠金额
		for _, item := range orderPromotionMap[order.OrderSn] {
			PlatformPayedAmount += item.PtCharge
			PresentPayedAmount += item.PoiCharge
		}
		//团长拼团制的一些信息
		if order.OrderType == 15 {
			order.GroupActivityModel = new(oc.SimpleOrderGroupActivity)
			if g, ok := orderGroupActivityMap[order.ParentOrderSn]; ok {
				order.GroupActivityModel.EndTime = g.EndTime.AddDate(0, 0, int(g.DeliverDays)).Format(kit.DATE_LAYOUT_SHORT_CN)
				order.GroupActivityModel.DeliverDays = g.DeliverDays
				order.GroupActivityModel.Id = g.Id
				order.GroupActivityModel.Status = g.Status
				order.GroupActivityModel.FinalTakeType = g.FinalTakeType
				order.GroupActivityModel.DisChainName = g.DisChainName
				order.GroupActivityModel.MemberName = g.MemberName
				order.GroupActivityModel.ReceiverAddress = g.ReceiverAddress
				order.GroupActivityModel.ReceiverMobile = g.ReceiverMobile
				order.GroupActivityModel.EnReceiverMobile = g.EnReceiverMobile
			}
			if v, ok := disCommissionMap[order.OrderSn]; ok {
				order.GroupActivityModel.DisCommission = cast.ToInt32(v.Commission)
			} else {
				order.GroupActivityModel.DisCommission = 0
			}

			if v, ok := upetMemberChainMap[order.OrderGroupMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.GroupActivityModel.StaffName = v.BillUserName
				} else {
					order.GroupActivityModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
			}
			//业绩的信息
			order.ShopDisModel = &oc.SimpleOrderShopDis{}
			if v, ok := upetMemberChainMap[order.ShopDisMemberId]; ok {
				if len(v.BillUserName) > 0 {
					order.ShopDisModel.StaffName = v.BillUserName
				} else {
					order.ShopDisModel.StaffName = MobileReplaceWithStar(v.MemberMobile)
				}
				order.ShopDisModel.MemberAreaName = v.MemberAreaName
			}
			if v, ok := upetChainMap[order.ShopDisChainId]; ok {
				order.ShopDisModel.ChainName = v.ChainName
				order.ShopDisModel.FinanceCode = v.AccountId
			}

			//如果非团长待收，处理一下团员地址
			if order.GroupActivityModel.FinalTakeType == 0 {
				order.GroupMemberReceiverName = order.ReceiverName
				order.GroupMemberReceiverMobile = order.ReceiverMobile
				order.GroupMemberReceiverAddress = order.ReceiverAddress
			}
		} else {
			if v, ok := orderPerformanceMap[order.ParentOrderSn]; ok {
				order.PerformanceStaffName = v.StaffName
				order.PerformanceOperatorName = v.OperatorName
				order.PerformanceOperatorTime = v.CreateTime.Format(kit.DATETIME_LAYOUT)
				if v2, ok := upetChainMap[v.PerformanceChainId]; ok {
					order.PerformanceChainName = v2.ChainName
					order.PerformanceFinanceCode = v2.AccountId
				} else if order.PerformanceOperatorName != "系统分配" {
					order.PerformanceChainName = order.ShopName
					order.PerformanceFinanceCode = order.ShopId
				}
			}
		}
	}

	//out.TotalCount = out.TotalCount - cast.ToInt32(delCount)
	return &out, nil
}

// 阿闻管家-虚拟订单详情-基础信息
func (o OrderService) AwenVirtualOrderBaseDetail(ctx context.Context, params *oc.AwenAllOrderBaseDetailRequest) (*oc.AwenVirtualOrderBaseDetailResponse, error) {
	out := oc.AwenVirtualOrderBaseDetailResponse{Code: 200, Message: "ok"}
	dbConn := GetDBConn()

	orderModel := &models.Order{}
	if ok, err := dbConn.SQL("select * from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.OrderId).
		Get(orderModel); err != nil {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "查询订单详情失败"
		return &out, nil
	} else if !ok {
		glog.Errorf("AwenOrderBaseDetail订单查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	orderSn := orderModel.OrderMain.OrderSn

	orderModelDetail := oc.VirtualCombineOrderDetail{
		OrderId:              int32(orderModel.Id),
		OrderSn:              orderSn,
		OldOrderSn:           orderModel.OldOrderSn,
		ParentOrderSn:        orderModel.ParentOrderSn,
		PaySn:                orderModel.PaySn,
		PayMode:              orderModel.PayMode,
		CreateTime:           kit.GetTimeNow(orderModel.CreateTime),
		OrderType:            orderModel.OrderType,
		ChannelId:            orderModel.ChannelId,
		PerformanceStaffName: orderModel.PerformanceStaffName,
		ReceiverName:         orderModel.ReceiverName,
		ReceiverPhone:        orderModel.ReceiverPhone,
		EnReceiverPhone:      orderModel.EnReceiverPhone,
		OrderStatusChild:     orderModel.OrderStatusChild,
		BuyerMemo:            orderModel.BuyerMemo,
		UserAgent:            orderModel.UserAgent,
		ShopName:             orderModel.ShopName,
		ShopId:               orderModel.ShopId,
		MemberId:             orderModel.MemberId,
		MemberTel:            orderModel.MemberTel,
		EnMemberTel:          orderModel.EnMemberTel,
	}

	if !orderModel.PayTime.IsZero() {
		orderModelDetail.PayTime = kit.GetTimeNow(orderModel.PayTime)
	}

	//订单状态判断逻辑和前端约定：order_status_child:30100已取消,30101待核销,30102部分核销,30103已完成(已核销)
	if orderModel.OrderStatus == 0 {
		orderModelDetail.OrderStatusChild = 30100
	}

	if (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.DeliveryType == 3 {
		orderModelDetail.ReceiverName = ""
	}

	//核销信息
	var verify []models.OrderVerifyCode
	if err := dbConn.SQL("SELECT * FROM `order_verify_code` WHERE order_sn = ? ORDER BY `create_time` DESC ", orderSn).
		Find(&verify); err != nil {
		glog.Errorf("AwenParentOrderBaseDetail订单核销详情查询失败！ order_id:%s ", params.OrderId)
		out.Code = 400
		out.Message = "查询订单脚印详情失败"
		return &out, nil
	} else {
		if len(verify) > 0 {
			var orderVerifySlice []*oc.OrderVerifyCode
			client := dac.GetDataCenterClient()
			for _, v := range verify {
				if v.VerifyStatus == 0 {
					if v.VerifyCodeExpiryDate.Before(time.Now()) {
						v.VerifyStatus = 3
					}
				}
				if v.VerifyStatus == 0 {
					v.VerifyCode = v.VerifyCode[0:3] + "*****" + v.VerifyCode[8:]
				}
				orderVerifyCode := &oc.OrderVerifyCode{
					VerifyCode:           v.VerifyCode,
					VerifyCodeExpiryDate: v.VerifyCodeExpiryDate.Format(kit.DATETIME_LAYOUT),
					VerifyStatus:         v.VerifyStatus,
					VerifyTime:           v.VerifyTime.Format(kit.DATETIME_LAYOUT),
					VerifyShop:           v.VerifyShop,
					VerifyMemberId:       v.VerifyMemberId,
				}
				if orderVerifyCode.VerifyCodeExpiryDate == "0001-01-01 00:00:00" {
					orderVerifyCode.VerifyCodeExpiryDate = ""
				}
				if orderVerifyCode.VerifyTime == "0001-01-01 00:00:00" {
					orderVerifyCode.VerifyTime = ""
				}

				storeInfo, err := client.RPC.QueryStoreInfo(context.Background(), &dac.StoreInfoRequest{
					StoreCode: []string{v.VerifyShop},
					ChannelId: 1,
				})
				if err != nil {
					glog.Error(orderSn, ", AwenVirtualOrderBaseDetail 获取仓库信息失败, ", err, " FinanceCode : "+v.VerifyShop)
				} else {
					if len(storeInfo.Details) > 0 {
						orderVerifyCode.VerifyShopName = storeInfo.Details[0].Name
					}
				}
				orderVerifySlice = append(orderVerifySlice, orderVerifyCode)
			}
			orderModelDetail.OrderVerifyInfo = orderVerifySlice
		}
	}

	//订单商品列表信息
	var orderProducts []models.OrderProduct
	if err := dbConn.Table("order_product").
		Where("order_sn = ?", orderSn).
		Find(&orderProducts); err != nil {
		glog.Error(err)
		out.Code = 400
		out.Message = "查询订单商品信息失败"
		return &out, nil
	} else if len(orderProducts) > 0 {
		var productModelSlice []*oc.VirtualSimpleOrderProductList
		var productModel *oc.VirtualSimpleOrderProductList
		for _, v := range orderProducts {
			productModel = &oc.VirtualSimpleOrderProductList{
				Id:            cast.ToString(v.Id),
				OrderSn:       v.OrderSn,
				ProductId:     v.ProductId,
				ProductType:   v.ProductType,
				ParentSkuId:   v.ParentSkuId,
				ProductName:   v.ProductName,
				Image:         v.Image,
				SkuId:         v.SkuId,
				ThirdSkuId:    v.ThirdSkuId,
				MarkingPrice:  v.MarkingPrice,
				DiscountPrice: v.DiscountPrice,
				PayPrice:      v.PayPrice,
				Number:        v.Number,
				PaymentTotal:  v.PaymentTotal,
				BarCode:       v.BarCode,
				PrivilegePt:   v.PrivilegePt,
			}
			if len(v.ParentSkuId) > 0 {
				var product models.OrderProduct
				if _, err := dbConn.Table("order_product").
					Where("sku_id = ?", v.ParentSkuId).
					Get(&product); err == nil {
					productModel.ParentProductName = product.ProductName
				}
			}
			productModelSlice = append(productModelSlice, productModel)
		}
		orderModelDetail.OrderProductModel = productModelSlice
	}
	//商品总额:前端的商品总额是不包含所有其他优惠运费服务费的，通过购买的商品原价和数量得出
	//ordermodeldetail.GoodsTotal = OriginGoodsTotal

	// 物竞天择渠道
	if orderModel.ChannelId == 4 && len(orderModel.ChildChannelId) > 0 {
		orderModelDetail.ChannelId = cast.ToInt32(orderModel.ChildChannelId)
	}

	out.OrderDetail = &orderModelDetail

	return &out, nil
}

// TODO del阿闻管家-虚拟订单详情-物流信息
func (o OrderService) AwenVirtualOrderDeliveryDetail(ctx context.Context, params *oc.AwenOrderDeliveryDetailRequest) (*oc.AwenOrderDeliveryDetailResponse, error) {
	out := oc.AwenOrderDeliveryDetailResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	var order models.Order
	_, err := dbConn.SQL("select a.*,b.accept_time,b.is_picking,b.picking_time from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.id=?", params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryDetail查询订单信息失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "查询订单信息失败"
		return &out, nil
	}

	var deliverynodes []*oc.OrderDeliveryNodeModel
	if (order.ChannelId == ChannelAwenId || order.ChannelId == ChannelDigitalHealth) && order.DeliveryType == 3 {
		if order.CancelTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:      order.OrderMain.OrderSn,
				CreateTime:   kit.GetTimeNow(order.CancelTime),
				CancelReason: order.CancelReason,
				NodeDesc:     "订单已取消",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.ConfirmTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.ConfirmTime),
				NodeDesc:   "顾客已取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 && order.IsPicking == 1 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.PickingTime),
				NodeDesc:   "待顾客取货",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
		if order.AcceptTime.Year() >= 2020 {
			var deliverynode = oc.OrderDeliveryNodeModel{
				OrderSn:    order.OrderMain.OrderSn,
				CreateTime: kit.GetTimeNow(order.AcceptTime),
				NodeDesc:   "商家接单",
			}
			deliverynodes = append(deliverynodes, &deliverynode)
		}
	} else {
		var nodes []models.OrderDeliveryNode
		session := dbConn.Table("`order_delivery_node`").
			Join("inner", "`order_main`", "`order_delivery_node`.order_sn=`order_main`.order_sn").
			Select(" `order_delivery_node`.order_sn,`order_delivery_node`.`delivery_status`,`order_delivery_node`.content,`order_delivery_node`.courier_name,`order_delivery_node`.courier_phone,`order_delivery_node`.cancel_reason,`order_delivery_node`.create_time").
			Where("`order_main`.id = ?", params.Orderid).
			Desc("`order_delivery_node`.create_time")
		if err = session.Find(&nodes); err != nil {
			glog.Errorf("AwenOrderDeliveryDetail订单配送流程查询失败！ order_id:%s ", params.Orderid)
			out.Code = 400
			out.Message = "订单配送流程查询失败"
			return &out, nil
		}
		var deliverynode *oc.OrderDeliveryNodeModel
		for _, item := range nodes {
			deliverynode = &oc.OrderDeliveryNodeModel{
				OrderSn:      item.OrderSn,
				CreateTime:   kit.GetTimeNow(item.CreateTime),
				NodeDesc:     item.Content,
				CourierName:  item.CourierName,
				CourierPhone: item.CourierPhone,
				CancelReason: item.CancelReason,
			}
			deliverynodes = append(deliverynodes, deliverynode)
		}
	}
	out.Deliverynodes = deliverynodes

	return &out, nil
}

// TODO del阿闻管家-虚拟订单详情-发货状态
func (o OrderService) AwenVirtualOrderDeliveryState(ctx context.Context, params *oc.AwenOrderDeliveryStateRequest) (*oc.AwenOrderDeliveryStateResponse, error) {
	out := oc.AwenOrderDeliveryStateResponse{Code: 200, Message: "ok"}

	dbConn := GetDBConn()

	order := models.Order{}
	ok, err := dbConn.SQL(`
	SELECT
	order_main.id,
	order_main.order_status,
	order_main.order_status_child,
	order_main.create_time,
	order_main.deliver_time,
	order_main.confirm_time,
	order_main.cancel_time,
	order_detail.expected_time,
	order_detail.accept_time,
	order_detail.picking_time,
	order_detail.pickup_code 
	FROM
	order_main
	INNER JOIN order_detail ON order_main.order_sn = order_detail.order_sn 
	WHERE id =`, params.Orderid).Get(&order)
	if err != nil {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单配送状态查询失败"
		return &out, nil
	}
	if !ok {
		glog.Errorf("AwenOrderDeliveryState订单配送状态查询失败！ order_id:%s ", params.Orderid)
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	deliverystate := oc.DeliveryState{
		OrderId:             cast.ToString(order.Id),
		OrderStatus:         order.OrderStatus,
		OrderStatusChild:    order.OrderStatusChild,
		SuggestDeliveryTime: kit.GetTimeNow(order.ExpectedTime),
		OrderCreateTime:     kit.GetTimeNow(order.CreateTime),
		OrderAcceptTime:     kit.GetTimeNow(order.AcceptTime),
		OrderDeliverTime:    kit.GetTimeNow(order.DeliverTime),
		OrderConfirmTime:    kit.GetTimeNow(order.ConfirmTime),
		OrderFinishTime:     kit.GetTimeNow(order.ConfirmTime),
		CancelTime:          kit.GetTimeNow(order.CancelTime),
		OrderPickingTime:    kit.GetTimeNow(order.PickingTime),
		PickupCode:          order.PickupCode,
	}
	//过滤时间默认值
	if deliverystate.SuggestDeliveryTime == "0001-01-01 00:00:00" {
		deliverystate.SuggestDeliveryTime = ""
	} else {
		//截取预计送达时间时间
		s := deliverystate.SuggestDeliveryTime[0:16] //去掉秒
		strArr := strings.Split(s, " ")
		deliverystate.SuggestDeliveryTime = strArr[1]
	}
	if deliverystate.OrderCreateTime == "0001-01-01 00:00:00" {
		deliverystate.OrderCreateTime = ""
	}
	if deliverystate.OrderAcceptTime == "0001-01-01 00:00:00" {
		deliverystate.OrderAcceptTime = ""
	}
	if deliverystate.OrderDeliverTime == "0001-01-01 00:00:00" {
		deliverystate.OrderDeliverTime = ""
	}
	if deliverystate.OrderConfirmTime == "0001-01-01 00:00:00" {
		deliverystate.OrderConfirmTime = ""
	}
	if deliverystate.OrderFinishTime == "0001-01-01 00:00:00" {
		deliverystate.OrderFinishTime = ""
	}
	if deliverystate.CancelTime == "0001-01-01 00:00:00" {
		deliverystate.CancelTime = ""
	}
	if deliverystate.OrderPickingTime == "0001-01-01 00:00:00" {
		deliverystate.OrderPickingTime = ""
	}

	out.Deliverystate = &deliverystate

	return &out, nil
}

// 阿闻管家订单详情之支付信息
// TODO: 待删除
func (o OrderService) Del_AwenOrderPayDetail(ctx context.Context, params *oc.AwenOrderPayDetailRequest) (*oc.AwenOrderPayDetailResponse, error) {
	out := new(oc.AwenOrderPayDetailResponse)
	out.Code = 200
	out.Message = "ok"

	dbConn := GetDBConn()
	//订单表，商品表，品牌表关联查询
	session := dbConn.Table("order").
		Join("INNER", "order_payinfo", "sku_third.product_id=product.id").
		Select("sku_third.`id`, sku_third.`sku_id`, sku_third.`third_sku_id`,sku_third.`product_id`,product.`name` as productname,product.`bar_code`").
		Where("1=1")

	if len(params.Orderid) > 0 {
		session.And("order_id = ?", params.Orderid)
	}

	if _, err := session.Get(&out.Paydetail); err != nil {
		glog.Error(err)
		return out, err
	}

	return out, nil
}

// 阿闻管家订单详情之店铺信息
// TODO: 待删除
func (o OrderService) Del_AwenOrderStoreDetail(ctx context.Context, params *oc.AwenOrderStoreDetailRequest) (*oc.AwenOrderStoreDetailResponse, error) {
	out := oc.AwenOrderStoreDetailResponse{Code: 200, Message: "ok"}

	return &out, nil
}

// 阿闻管家订单详情之买家信息
// TODO: 待删除
func (o OrderService) Del_AwenOrderBuyerDetail(ctx context.Context, params *oc.AwenOrderBuyerDetailRequest) (*oc.AwenOrderBuyerDetailResponse, error) {
	out := oc.AwenOrderBuyerDetailResponse{Code: 200, Message: "ok"}

	return &out, nil
}

// 阿闻管家接单接口(包含预订单和实物订单)
func (o OrderService) AcceptOrder(ctx context.Context, params *oc.AcceptOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("手动接单：", params.OrderSn)
	out := oc.BaseResponse{Code: 400}

	//连接池勿关闭
	redisConn := GetRedisConn()

	lockCard := "lock:order_" + params.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		out.Message = "该订单暂不可操作，请重试！"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	orderModel := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.locked_stock")
	if orderModel.Id == 0 {
		out.Message = "订单不存在！"
		return &out, nil
	}
	if orderModel.OrderStatusChild != 20101 {
		out.Message = "该订单不是未接单状态！"
		return &out, nil
	}
	// 记录订单来源
	var source string
	if md, success := metadata.FromIncomingContext(ctx); success && len(md.Get("source")) > 0 {
		source = md.Get("source")[0]
	}
	if source == "admin-accept" && orderModel.OrderType == 15 { // 后台拼团团长订单
		out.Message = "拼团团长订单不允许手动接单！"
		return &out, nil
	}

	var err error

	o.orderMain = orderModel.OrderMain
	o.session = GetDBConn().NewSession()
	defer o.session.Close()
	o.session.Begin()

	if orderModel.LockedStock == 0 {
		if orderModel.OrgId == 6 {
			//如果是逍宠
			freezeOut := FreezeCommitOrder(orderModel.OrderSn, orderModel.ShopId, o.session)
			if freezeOut.Code != 200 {
				out.Message = freezeOut.ErrorMsg
				out.Error = freezeOut.ErrorMsg
				return &out, nil
			}
		} else {
			//如果是第三方的订单先查询一下库存再锁库存
			switch orderModel.ChannelId {
			case ChannelMtId, ChannelElmId, ChannelJddjId:
				o.OrderQueryInventory()
			}

			//锁库存
			err = o.OrderLockInventory()
			if err != nil {
				out.Message = err.Error()
				out.Error = err.Error()
				return &out, nil
			}
		}

		o.session.Exec(`
			update order_detail 
			inner join order_main on order_detail.order_sn = order_main.order_sn 
			set locked_stock=1 
			where (order_main.order_sn=? or order_main.parent_order_sn=?) 
			and order_main.is_virtual=0
		`, orderModel.OrderSn)
	}

	realOrder := CommonService{
		orderMain: new(models.OrderMain),
	}
	realOrder.session = GetDBConn().NewSession()
	defer realOrder.session.Close()

	_, err = realOrder.session.Where("parent_order_sn=? and is_virtual=0", orderModel.OrderSn).Get(realOrder.orderMain)
	if err != nil {
		glog.Error(orderModel.OldOrderSn, ", 接单后查询实物子订单失败, ", err)
	}

	var code string
	switch orderModel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		glog.Info("调用第三方渠道接单：", orderModel.OrderSn)
		code, err = MtOrderConfirm(orderModel.OldOrderSn, orderModel.ChannelId, orderModel.AppChannel)
		glog.Info("调用第三方渠道接单结果：", orderModel.OrderSn, code, err)
		if err != nil {
			o.session.Rollback()
			out.Error = out.Message
			glog.Error("调用接单接口失败！ ", orderModel.OldOrderSn, "，"+orderModel.OrderSn, err.Error())
			return &out, nil
		}
		if code == "808" { //美团已经接过单 更新订单状态为已接单就好了
			orderMainUp := &models.OrderMain{
				OrderStatusChild: 20102,
			}
			orderDetailUp := &models.OrderDetail{
				AcceptTime:     time.Now(),
				AcceptUsername: "平台自动接单",
			}
			_, err = o.session.In("order_sn", []string{o.orderMain.OrderSn, realOrder.orderMain.OrderSn}).Update(orderDetailUp)
			if err != nil {
				glog.Error("后台接单时808更新订单详情自动接单状态出错:" + o.orderMain.OrderSn + " " + err.Error())
			}
			//实物子单也要更新状态
			_, err = o.session.In("order_sn", []string{o.orderMain.OrderSn, realOrder.orderMain.OrderSn}).Update(orderMainUp)
			if err != nil {
				glog.Error("后台接单时808更新自动接单状态出错1:" + o.orderMain.OrderSn + " " + err.Error())
			}
		}
	}

	if err = o.session.Commit(); err != nil {
		glog.Error("阿闻管家接单接口commit", orderModel.OldOrderSn, err.Error())
	}

	//if orderModel.ChannelId == ChannelMtId || orderModel.ChannelId == ChannelElmId || orderModel.ChannelId == ChannelJddjId {
	//	//第三订单推单
	//_ = o.ThirdPushOrder()
	//} else { //阿闻推单
	//阿闻和京东没有接单回调，直接处理推送第三方 其他渠道等待回调（MtAcceptOrder）后推送第三方
	//此处 阿闻与京东 如果已经推送过第三方 但是接单更新失败了 可能会后台手动接单 导致重新推单与发配送
	//美团与饿了么的回调中因为加了推单判断 所以没问题
	if orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth || orderModel.ChannelId == ChannelJddjId {
		//实物子订单推送第三方
		if realOrder.orderMain != nil {
			realOrder.AcceptPushThird("商家后台")
		}
		if (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.DeliveryType == 3 {
			go func() {
				defer kit.CatchPanic()

				// 查询自提备货时间
				dacClient := dac.GetDataCenterClient()
				setup, _ := dacClient.RPC.ShopBusinessSetupGet(dacClient.Ctx, &dac.ShopBusinessSetupGetRequest{
					Finance_Code: orderModel.ShopId,
					Channel_Id:   1,
				})

				stockTime := int32(15)
				if setup != nil && setup.Data.StockUpTime > 0 {
					stockTime = setup.Data.StockUpTime
				}
				// 阿闻自提订单店铺备货时长写入redis
				if orderModel.OrgId != cast.ToInt32(SAASMainId) {
					redisConn.ZAdd(SelfCollectionPicking, redis.Z{Score: float64(stockTime), Member: realOrder.orderMain.OrderSn})
				}

			}()
		}
	}
	//}

	//打印订单
	go o.PrintOrderDetail(params.OrderSn)

	glog.Info("接单接口调用完成！", orderModel.OldOrderSn)
	out.Code = 200
	return &out, nil
}

// 阿闻管家后台取消订单接口 saas-v1.0
func (o OrderService) CancelAcceptOrder(ctx context.Context, params *oc.CancelAcceptOrderRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	//判断是否拆单中
	IsSplitRes := OrderIsSplit([]string{params.OrderSn})
	if _, ok := IsSplitRes[params.OrderSn]; ok {
		if IsSplitRes[params.OrderSn] == 0 {
			glog.Info("订单拆单中不能取消：" + kit.JsonEncode(params))
			out.Message = "拆单中不能取消"
			return &out, nil
		}
	}
	glog.Info("取消订单：" + kit.JsonEncode(params))

	//根据订单推送的标记，取消对应的数据(子龙，全渠道，美团配送)
	//连接池勿关闭
	redisConn := GetRedisConn()

	lockCard := "lock:order_" + params.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 20*time.Second).Val()
	if !lockRes {
		out.Message = "您操作太快了，请稍后再试！"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	//用户校验  saas-v1.0
	userInfo := o.LoadLoginUserInfo(ctx)

	parentOrderModel := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.push_delivery,order_detail.push_third_order,order_detail.locked_stock")
	if parentOrderModel.Id == 0 {
		out.Message = "该订单不存在！"
		return &out, nil
	}
	if parentOrderModel.ChannelId == ChannelMallId {
		out.Message = "电商订单不能取消！"
		return &out, nil
	}
	if parentOrderModel.OrderStatus == 0 {
		out.Message = "该订单已取消！"
		return &out, nil
	}

	//阿闻订单需要支付后2分钟后才可取消
	if parentOrderModel.IsPay == 1 && (parentOrderModel.ChannelId == ChannelAwenId || parentOrderModel.ChannelId == ChannelDigitalHealth) {
		now := time.Now()
		d, _ := time.ParseDuration("-2m")
		d1 := now.Add(d)
		if parentOrderModel.PayTime.After(d1) {
			out.Message = "取消需要支付时间大于2分钟才能操作，请稍候"
			out.Error = "取消需要支付时间大于2分钟才能操作，请稍候！"
			return &out, nil
		}
	}

	//注意 第三方查询的依然是主订单
	childOrders := GetRefundOrderByParentOrderSn(parentOrderModel.OrderSn, parentOrderModel.ChannelId)

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	var RefundOrder []models.RefundOrder
	o.session.Table("refund_order").Join("inner", "order_main", "refund_order.order_sn = order_main.order_sn").
		Where("(order_main.parent_order_sn = ? or order_main.order_sn=?) and refund_order.refund_state in(1,5,6,7)", parentOrderModel.OrderSn, parentOrderModel.OrderSn).Find(&RefundOrder)
	if len(RefundOrder) > 0 {
		out.Message = "请把所有退款处理完再发起退款！"
		return &out, nil
	}

	var err error
	//调取第三方取消订单接口
	switch parentOrderModel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		err = NewChannelOrder(parentOrderModel).CancelOrder(params)
	}
	if err != nil {
		out.Message = err.Error()
		out.Error = err.Error()
		return &out, nil
	}

	realOrder := &CommonService{
		session: GetDBConn().NewSession(),
	}
	defer realOrder.session.Close()

	for _, order := range childOrders {
		realOrder.orderMain = GetOrderMainByOrderSn(order.OrderSn)
		realOrder.orderDetail = GetOrderDetailByOrderSn(order.OrderSn)
		if realOrder.orderMain.Id == 0 {
			glog.Error(order.ParentOrderSn, ", ", order.OrderSn, ", 查询子订单信息失败, ", err)
			out.Message = "通知第三方系统失败！查询子订单信息失败"
			out.Error = err.Error()
			return &out, nil
		}
		//虚拟订单不用判断是否已完成
		if realOrder.orderMain.IsVirtual == 1 {
			//子订单已取消或者已完成不用取消订单
			if realOrder.orderMain.OrderStatus == 0 {
				continue
			}
			codes := GetValidOrderVerifyCodes(realOrder.orderMain.OrderSn, 6)
			if len(codes) <= 0 {
				continue
			}
		} else {
			//if realOrder.orderMain.OrderStatus == 0 || (realOrder.orderMain.OrderStatus == 30 && realOrder.orderMain.ChannelId != 2) {
			if realOrder.orderMain.OrderStatus == 0 {
				continue
			}
		}

		DeliveryRecord := models.OrderDeliveryRecord{}
		//第三方订单 需要通过实物子单查询配送记录
		if isThirdChannel(realOrder.orderMain.ChannelId) {
			childRealOrder, err := GetChildRealOrderByOrderSn(realOrder.orderMain.OrderSn, "order_sn")
			if err != nil {
				glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "第三方订单取消订单时 查询实物子单号出错！", err)
			}
			if childRealOrder.OrderSn != "" {
				o.session.Where("order_sn = ?", childRealOrder.OrderSn).Desc("id").Get(&DeliveryRecord)
			}
		} else {
			o.session.Where("order_sn = ?", realOrder.orderMain.OrderSn).Desc("id").Get(&DeliveryRecord)
		}

		if realOrder.orderDetail.PushDelivery == 1 && realOrder.orderMain.OrderStatus != 30 {
			CancelDeliveryCommon(realOrder.orderMain.OrderSn, params.Reason, DeliveryRecord.DeliveryId, 0)
		}

		//生成退款单号
		refundSn := GetSn("refund")[0]
		realOrder.session.Begin()

		if realOrder.orderMain.IsVirtual == 0 {
			deliveryModel := models.OrderDeliveryNode{
				DeliveryId:     DeliveryRecord.DeliveryId,
				OrderSn:        realOrder.orderMain.OrderSn,
				DeliveryStatus: 98,
				Content:        "商家取消",
				CancelReason:   params.Reason,
				CreateTime:     time.Now(),
			}
			_, err = realOrder.session.Insert(&deliveryModel)
			if err != nil {
				glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, ", 取消订单更新配送信息接口，信息保存失败, ", err.Error())
			}
		}

		orderMainUp := models.OrderMain{
			OrderStatusChild: 20107,
			OrderStatus:      0,
			CancelReason:     params.Reason,
			CancelTime:       time.Now(),
		}
		_, err = realOrder.session.In("order_sn", realOrder.orderMain.OrderSn).
			Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").
			Update(orderMainUp)
		if err != nil {
			realOrder.session.Rollback()
			glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "订单取消状态更新失败, ", err)
			out.Message = "订单状态更新失败！"
			return &out, nil
		}

		//如果是第三方 则需要更改所有子单的状态
		if realOrder.orderMain.ChannelId == ChannelMtId || realOrder.orderMain.ChannelId == ChannelJddjId || realOrder.orderMain.ChannelId == ChannelElmId {
			//实物子单
			_, err = realOrder.session.Exec("UPDATE order_main SET order_status=?,order_status_child = ?,cancel_reason = ?,cancel_time = ? WHERE parent_order_sn=? AND is_virtual = 0", 0, 20107, params.Reason, time.Now(), realOrder.orderMain.OrderSn)
			if err != nil {
				realOrder.session.Rollback()
				glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "第三方订单子订单状态更新失败, ", err)
				out.Message = "第三方订单实物子订单状态更新失败！"
				return &out, nil
			}
			//虚拟子单
			_, err = realOrder.session.Exec("UPDATE order_main SET order_status=?,order_status_child = ?,cancel_reason = ?,cancel_time = ? WHERE parent_order_sn=? AND is_virtual = 1", 0, 30100, params.Reason, time.Now(), realOrder.orderMain.OrderSn)
			if err != nil {
				realOrder.session.Rollback()
				glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, "第三方订单子订单状态更新失败, ", err)
				out.Message = "第三方订单子虚拟订单状态更新失败！"
				return &out, nil
			}
		}

		var refundPushThirdFailReason string
		var refundPushThird int32

		//已接单的通知子龙，全渠道 //从上面移下来，先通知美团成功了再通知子龙
		//todo 去掉推送地方的判断，通过重试来处理，后续整理成单表处理
		glog.Info(realOrder.orderMain.OrderSn, "进入退款通知打印信息", realOrder.orderMain.IsVirtual, realOrder.orderMain.OrderStatusChild, kit.JsonEncode(realOrder.orderMain))
		if realOrder.orderMain.IsVirtual == 0 {
			if parentOrderModel.PushThirdOrder > 0 {
				// 通知第三方退货
				glog.Info(refundSn, ", ", " zx测试进入路径2")
				if err = realOrder.RefundNotice(params.Reason, refundSn, 1, 1); err != nil {
					refundPushThirdFailReason = err.Error()
					glog.Error(realOrder.orderMain.ParentOrderSn, ", ", realOrder.orderMain.OrderSn, ", 通知第三方系统失败！", err)
				} else {
					refundPushThird = 1
				}
			} else if parentOrderModel.LockedStock > 0 {
				if err = realOrder.FreedStock(nil); err != nil {
					glog.Error("订单取消释放库存失败", o.orderMain.OrderSn, " ", err.Error())
				}
				// 清除在途库存
				DeleteTransportationInventory(parentOrderModel.OrderMain.OrderSn, parentOrderModel.OrderMain.ChannelId)
			}
		}

		//先生成退款单 v6.5.0
		err = realOrder.SaveCancelRefundOrder(refundSn, params.Reason, userInfo.UserName, 1, "", refundPushThird, refundPushThirdFailReason)
		if err != nil {
			realOrder.session.Rollback()
			out.Message = "生成退款单失败！"
			out.Error = err.Error()
			return &out, nil
		}

		err = realOrder.session.Commit()
		if err != nil {
			glog.Error("商家取消订单Commit", realOrder.orderMain.OrderSn, err.Error())
		}

		if realOrder.orderMain.ChannelId == ChannelAwenId || realOrder.orderMain.ChannelId == ChannelDigitalHealth {
			refundOrderPay := new(oc.RefundOrderPayRequest)
			refundOrderPay.RefundOrderSn = refundSn
			refundOrderPay.ResType = "商家取消订单自动发起退款申请"
			refundOrderPay.OperationType = "商家取消订单自动发起退款申请"
			refundOrderPay.OperationUser = userInfo.UserName

			refundOrderService := RefundOrderService{}
			_, err = refundOrderService.RefundOrderPay(nil, refundOrderPay)
			if err != nil {
				glog.Error("商家取消订单自动发起退款申请返回错误："+refundSn, err)
			}
		}

	}

	orderMainUp := models.OrderMain{
		OrderStatusChild: 20107,
		OrderStatus:      0,
		CancelReason:     params.Reason,
		CancelTime:       time.Now(),
	}
	//主单更新状态
	_, err = realOrder.session.In("order_sn", parentOrderModel.OrderSn).
		Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").
		Update(orderMainUp)
	if err != nil {
		realOrder.session.Rollback()
		glog.Error(parentOrderModel.OrderSn, "订单取消状态更新失败, ", err)
		out.Message = "订单状态更新失败！"
		return &out, nil
	}

	err = realOrder.session.Commit()
	if err != nil {
		glog.Error("商家取消订单Commit", parentOrderModel.OrderSn, err.Error())
	}
	go func() {
		if realOrder.orderDetail.ConsultOrderSn != "" {
			_ = PushDigitalHealthOrder(realOrder.orderMain.OrderSn, realOrder.orderDetail.ConsultOrderSn, 3, 0)
		}
	}()
	//saas释放库存
	//pushOrderSn := parentOrderModel.ParentOrderSn
	//if pushOrderSn == "" {
	//	pushOrderSn = parentOrderModel.OrderSn
	//}

	if realOrder.orderMain.AppChannel == SaasAppChannel {
		var RefundOrder models.RefundOrder
		o.session.Table("refund_order").Join("inner", "order_main", "refund_order.order_sn = order_main.order_sn").
			Where("(order_main.parent_order_sn = ? or order_main.order_sn=?) and refund_order.refund_state =3", parentOrderModel.OrderSn, parentOrderModel.OrderSn).Get(&RefundOrder)
		glog.Info("CancelAcceptOrder，订单取消释放库存，订单号：", parentOrderModel.OrderSn, "，退款单号：", RefundOrder.RefundSn)
		go RefundStock(RefundOrder.RefundSn, parentOrderModel.OrderSn, parentOrderModel.ChannelId)
	}
	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 阿闻管家触发拣货完成接口(通知美团可以安排骑手，并更新订单状态) saas-v1.0
func (o OrderService) PickingOrder(ctx context.Context, params *oc.PickingOrderRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("手动拣货完成接口：", kit.JsonEncode(params))

	orderModel := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.accept_time,order_detail.push_delivery,order_detail.push_third_order")
	if orderModel.Id == 0 {
		out.Message = "该订单不存在！"
		return &out, nil
	}
	// if orderModel.OrgId == cast.ToInt32(SAASMainId) {
	// 	out.Message = "宠物saas订单不处理"
	// 	return &out, nil
	// }
	if orderModel.OrderStatusChild == 20201 {
		out.Message = "该订单未接单！"
		return &out, nil
	}
	if kit.GetTimeNow(orderModel.AcceptTime) == "0001-01-01 00:00:00" {
		out.Message = "没有接单时间！"
		return &out, nil
	}
	if time.Now().Before(orderModel.AcceptTime.Add(time.Minute * 3)) {
		out.Message = "接单后3分钟内不可拣货！"
		return &out, nil
	}

	var err error
	switch orderModel.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		err = NewChannelOrder(orderModel).PickOrder()
	}

	if err != nil {
		out.Message = "通知" + ChannelName[orderModel.ChannelId] + "拣货失败！"
		out.Error = err.Error()
		o.session.Rollback()
		return &out, nil
	}

	_, err = GetDBConn().Exec(`
		update order_detail 
		inner join order_main on order_detail.order_sn = order_main.order_sn 
		set is_picking=1,picking_time=now()
		where (order_main.order_sn=? or order_main.parent_order_sn=?) 
		and order_main.is_virtual=0
	`, orderModel.OrderSn, orderModel.OrderSn)
	if err != nil {
		out.Message = "更新订单详情信息失败！"
		out.Error = err.Error()
		return &out, nil
	}

	// 移除redis备货完成的订单
	if (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.DeliveryType == 3 {
		GetRedisConn().ZRem(SelfCollectionPicking, orderModel.OrderSn)
	}

	go func() {
		defer kit.CatchPanic()

		//数据中心，后台通知状态修改
		MessageUpdate(orderModel.OrderSn)

		//记录拣货操作日志
		SaveOrderLog([]*models.OrderLog{
			{
				OrderSn: orderModel.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: orderModel.ParentOrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		})
	}()

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 京东到家拣货完成接口
func (o OrderService) JddjPickingOrder(ctx context.Context, params *oc.PickingOrderRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	orderModel := &models.Order{}
	ok, err := o.session.SQL("select order_main.*,order_detail.is_picking from order_main inner join order_detail on order_main.order_sn=order_detail.order_sn where old_order_sn=? and order_status!=0", params.OrderSn).Get(orderModel)
	if err != nil {
		out.Message = "订单查询错误!"
		out.Error = err.Error()
		return &out, nil
	}
	if !ok {
		out.Message = "该订单不存在！"
		return &out, nil
	}
	if orderModel.OrderStatusChild == 20201 {
		out.Message = "该订单未接单！"
		return &out, nil
	}
	if orderModel.IsPicking != 0 {
		out.Message = "该订单已拣货完成！"
		return &out, nil
	}

	o.orderMain = orderModel.OrderMain
	o.session.Begin()

	_, err = o.session.ID(orderModel.Id).Update(&models.OrderMain{
		OrderStatusChild: orderModel.OrderStatusChild,
	})
	if err != nil {
		out.Message = "更新订单主信息失败！"
		out.Error = err.Error()
		o.session.Rollback()
		return &out, nil
	}

	_, err = o.session.ID(orderModel.OrderSn).Update(&models.OrderDetail{
		IsPicking:   1,
		PickingTime: time.Now(),
	})
	if err != nil {
		out.Message = "更新订单详情信息失败！"
		out.Error = err.Error()
		o.session.Rollback()
		return &out, nil
	}

	//京东到家
	//自配
	if orderModel.OrderType != 2 {
		if orderModel.LogisticsCode == "2938" {
			o.PushMpOrder()
		}
	}
	if orderModel.DeliveryType == 3 && orderModel.OrderType == 2 {
		//拣货完成且顾客自提接口
		if orderModel.LogisticsCode == "9999" {
			err = JddjOrderSelfMention(orderModel.OldOrderSn, params.StoreMasterId)
			if err != nil {
				glog.Error(orderModel.OldOrderSn, "，手动拣货推送京东到家拣货完成且顾客自提接口失败！", err)
			}

		}
	}

	err = o.session.Commit()
	if err != nil {
		glog.Error(orderModel.OldOrderSn, "，京东到家商家后台拣货完成接口commit", params.OrderSn, err)
	}

	go func() {
		defer kit.CatchPanic()

		//数据中心，后台通知状态修改
		MessageUpdate(orderModel.OrderSn)

		//记录拣货操作日志
		SaveOrderLog([]*models.OrderLog{
			{
				OrderSn: orderModel.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		})
	}()

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 保存业绩分配
func (o OrderService) SavePerformance(ctx context.Context, params *oc.SavePerformanceRequest) (*empty.Empty, error) {
	if len(params.OrderSn) == 0 {
		return nil, errors.New("订单编号不能为空")
	}
	if len(params.StaffId) == 0 {
		return nil, errors.New("员工id不能为空")
	}
	if len(params.StaffName) == 0 {
		return nil, errors.New("员工姓名不能为空")
	}
	if len(params.OperatorName) == 0 {
		return nil, errors.New("操作人不能为空")
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()
	o.session.Begin()

	//if has, err := o.session.Where("order_sn = ? and operator_name = '系统分配'", params.OrderSn).
	//	Exist(&models.OrderPerformance{}); err != nil {
	//	err = errors.New("修改业绩状态失败，" + err.Error())
	//	glog.Error(params.OrderSn, "，", err.Error())
	//	return nil, err
	//} else if has {
	//	return nil, errors.New("该订单已由系统分配，修改业绩失败")
	//}
	var err error
	if params.Orgid == 6 { //如果是逍宠，业务要往eshop_saas.p_emp_perf写
		//取出订单信息
		var orderMain models.OrderMain
		if _, err := o.session.Table("order_main").Where("order_sn = ? and org_id = ?", params.OrderSn, params.Orgid).Get(&orderMain); err != nil {
			err = errors.New("查询订单信息失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}

		if orderMain.Id == 0 {
			err = errors.New("订单信息不存在")
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}

		//宠物连锁SAAS用户信息
		var employee models.TEmployee
		if _, err := o.session.Table("eshop_saas.t_employee").Where("id = ?", params.StaffId).Get(&employee); err != nil {
			err = errors.New("查询用户信息失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}

		if employee.Id == 0 {
			err = errors.New("员工信息不存在")
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}

		//取出订单下商品的信息，每条商品插入一条分配业绩
		var orderProductList []models.OrderProduct
		if err := o.session.Table("order_product").Where("order_sn = ?", orderMain.OrderSn).
			Find(&orderProductList); err != nil {
			err = errors.New("查询订单商品信息失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}

		s, err := utils.NewSnowflake(int64(10), int64(5))
		if err != nil {
			err = errors.New("snowflake失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err.Error())
			o.session.Rollback()
			return nil, err
		}
		productType := "GOODS" //商品类型只有实体的
		for _, orderProduct := range orderProductList {

			//宠物连锁SAAS,判断是否有业绩分配记录，如果有更新，没有添加
			var pEmpPerf models.PEmpPerf
			if _, err := o.session.Table("eshop_saas.p_emp_perf").Where("order_no = ? and product_id = ?", orderMain.OrderSn, orderProduct.ProductId).
				Get(&pEmpPerf); err != nil {
				err = errors.New("修改业绩状态失败，" + err.Error())
				glog.Error(params.OrderSn, "，", err.Error())
				o.session.Rollback()
				return nil, err
			}

			if pEmpPerf.Id != 0 {
				if _, err := o.session.Table("eshop_saas.p_emp_perf").Cols("tenant_id,employee_id,employee_no,employee_mobile,employee_name,role_id,updated_by,updated_time").Where("id = ?", pEmpPerf.Id).
					Update(&models.PEmpPerf{
						TenantId:       employee.TenantId,
						EmployeeId:     employee.Id,
						EmployeeNo:     employee.EmployeeNo,
						EmployeeMobile: employee.Mobile,
						EmployeeName:   employee.RealName,
						RoleId:         employee.RoleId,
						UpdatedBy:      0,
						UpdatedTime:    time.Now(),
					}); err != nil {
					err = errors.New("修改业绩状态失败，" + err.Error())
					glog.Error(params.OrderSn, "，", err.Error())
					o.session.Rollback()
					return nil, err
				}
			} else {
				//找出员工的佣金设置
				var commisionRate float64
				var tenantPercentageEmployee models.TTenantPercentageEmployee
				if _, err := o.session.Table("eshop_saas.t_tenant_percentage_employee").Alias("tpe").Join("inner", "eshop_saas.t_tenant_percentage tp", "tp.id = tpe.percentage_id").Where("tpe.tenant_id = ? and tpe.employee_id = ? and tp.status = 'ENABLED'", employee.TenantId, employee.Id).Get(&tenantPercentageEmployee); err != nil {
					err = errors.New("查询提成信息失败，" + err.Error())
					glog.Error(params.OrderSn, "，", err.Error())
					o.session.Rollback()
				}

				var tenantPercentageValues models.TTenantPercentageValues
				if tenantPercentageEmployee.Id != 0 {
					if _, err := o.session.Table("eshop_saas.t_tenant_percentage_values").Where("percentage_id = ? and product_type = ?", tenantPercentageEmployee.PercentageId, productType).Get(&tenantPercentageValues); err != nil {
						err = errors.New("查询提成信息失败，" + err.Error())
						glog.Error(params.OrderSn, "，", err.Error())
						o.session.Rollback()
					}

					if tenantPercentageValues.FixAllProduct == 1 { //适用所有商品
						commisionRate = float64(tenantPercentageValues.PercentageValue) / 100
					} else {
						//如果不是适合所有商品，则查询是否适用该商品
						if isExist, err := o.session.Table("eshop_saas.t_tenant_percentage_product").Where("percentage_id = ? and product_id = ?", tenantPercentageEmployee.PercentageId, orderProduct.ProductId).Exist(); err != nil {
							err = errors.New("查询提成信息失败，" + err.Error())
							glog.Error(params.OrderSn, "，", err.Error())
							o.session.Rollback()
						} else if isExist {
							commisionRate = float64(tenantPercentageValues.PercentageValue) / 100
						}
					}
				}

				insertPEmpPerf := &models.PEmpPerf{
					Id:             s.NextVal(),
					TenantId:       employee.TenantId,
					EmployeeId:     employee.Id,
					EmployeeNo:     employee.EmployeeNo,
					EmployeeMobile: employee.Mobile,
					EmployeeName:   employee.RealName,
					RoleId:         employee.RoleId,
					OrderTime:      orderMain.CreateTime,
					CustomerId:     0,
					CustomerName:   orderMain.MemberName,
					CustomerMobile: orderMain.MemberTel,
					OrderId:        orderMain.Id,
					OrderNo:        orderMain.OrderSn,
					OrderDetailId:  0,
					RefundType:     "",
					RefundId:       0,
					RefundDetailId: 0,
					ProductId:      cast.ToInt64(orderProduct.ProductId),
					ProductType:    productType,
					SnapshotId:     0,
					SkuId:          cast.ToInt64(orderProduct.SkuId),
					ProductName:    orderProduct.ProductName,
					DiscountPrice:  float64(orderProduct.DiscountPrice) / 100,
					BuyCount:       cast.ToInt(orderProduct.Number),
					SubtotalAmount: float64(orderProduct.DiscountPrice*orderProduct.Number) / 100,
					PaymentAmount:  float64(orderProduct.PaymentTotal) / 100,
					CommisionRate:  commisionRate,
					CommisionType:  tenantPercentageValues.PerformanceWay,
					Performance:    float64(orderProduct.PaymentTotal) / 100,
					Commision:      0,
					IsDeleted:      0,
					CreatedBy:      0,
					CreatedTime:    time.Now(),
					UpdatedBy:      0,
					UpdatedTime:    time.Now(),
				}

				if tenantPercentageValues.PerformanceWay == models.PRODUCTTYPEPAYPRICE { //按实付计算佣金
					insertPEmpPerf.Commision = utils.RoundPlace(insertPEmpPerf.PaymentAmount*commisionRate, 2)
				} else { //按销售价计算佣金
					insertPEmpPerf.Commision = utils.RoundPlace(insertPEmpPerf.SubtotalAmount*commisionRate, 2)
				}

				//插入新的业绩分配
				if _, err = o.session.Table("eshop_saas.p_emp_perf").Insert(insertPEmpPerf); err != nil {
					err = errors.New("插入新的业绩分配失败，" + err.Error())
					glog.Error(params.OrderSn, "，", err)
					o.session.Rollback()
					return nil, err
				}
			}
		}

	} else {
		//修改之前的业绩操作状态为无效
		if _, err = o.session.Cols("performance_status").Where("order_sn = ?", params.OrderSn).Update(&models.OrderPerformance{
			PerformanceStatus: 0,
		}); err != nil {
			err = errors.New("修改业绩状态失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err)
			o.session.Rollback()
			return nil, err
		}

		//插入新的业绩分配
		if _, err = o.session.Insert(&models.OrderPerformance{
			OrderSn:           params.OrderSn,
			StaffId:           params.StaffId,
			StaffName:         params.StaffName,
			PerformanceStatus: 1,
			OperatorId:        params.OperatorId,
			OperatorName:      params.OperatorName,
		}); err != nil {
			err = errors.New("插入新的业绩分配失败，" + err.Error())
			glog.Error(params.OrderSn, "，", err)
			o.session.Rollback()
			return nil, err
		}
	}

	//冗余至订单详情表
	if _, err = o.session.Exec(`
		update order_detail 
		inner join order_main on order_main.order_sn=order_detail.order_sn 
		set performance_staff_name=?,performance_operator_name=?,performance_operator_time=?
		where order_main.order_sn=? or order_main.parent_order_sn=?
	`, params.StaffName, params.OperatorName, kit.GetTimeNow(), params.OrderSn, params.OrderSn); err != nil {
		err = errors.New("更新订单详情业绩分配信息失败，" + err.Error())
		glog.Error(params.OrderSn, "，", err)
		o.session.Rollback()
		return nil, err
	}

	o.session.Commit()

	go func() {
		defer kit.CatchPanic()

		//获取父订单所有子订单
		childrenOrders := GetOrderByParentOrderSn(params.OrderSn, "order_main.order_sn")
		orderLogs := []*models.OrderLog{
			{
				OrderSn: params.OrderSn,
				LogType: models.OrderLogSavePerformance,
			},
		}
		for _, childrenOrder := range childrenOrders {
			orderLogs = append(orderLogs, &models.OrderLog{
				OrderSn: childrenOrder.OrderSn,
				LogType: models.OrderLogSavePerformance,
			})
		}
		SaveOrderLog(orderLogs)
	}()
	return new(empty.Empty), nil
}

// 配送相关服务 ----------------------------
// 订单完成（订单完成调用）
func (o OrderService) AccomplishOrder(ctx context.Context, params *oc.AccomplishOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("收到订单完成：", kit.JsonEncode(params))
	out := &oc.BaseResponse{Code: 400}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	o.orderMain = &models.OrderMain{}
	ok, err := o.session.Where("old_order_sn = ? and order_status!=0 and channel_id != 5", params.OrderSn).Get(o.orderMain)
	if err != nil {
		out.Message = "订单查询错误!"
		out.Error = err.Error()
		glog.Error(params.OrderSn, "，订单完成：订单查询错误,", err)
		return out, nil
	}
	if !ok {
		out.Message = "该订单不存在！"
		glog.Error("订单完成：该订单不存在，", params.OrderSn)
		return out, nil
	}

	//完成更新，其他操作
	err = o.FinalizeOrder(params)
	if err != nil {
		o.session.Rollback()
		out.Message = err.Error()
		out.Error = err.Error()
		return out, nil
	}

	//主订单不加积分
	if o.orderMain.ParentOrderSn != "" && o.orderMain.OrgId != 6 {
		// 增加积分
		IntegralOperation(o.orderMain.OrderSn, true)
	}

	out.Code = 200
	out.Message = "ok"
	return out, nil
}

func (o OrderService) MpOrderRiderLocation(ctx context.Context, params *oc.RiderLocationRequest) (*oc.ExternalResponse, error) {

	//先查询是哪个配送
	db := GetDBConn()
	res := new(oc.ExternalResponse)
	res.Code = 400
	res.Message = "Success"
	//获取配送数据
	delivery := &models.OrderDeliveryRecord{}
	_, err := db.SQL("SELECT * FROM order_delivery_record WHERE delivery_id = ? ", params.DeliveryId).Get(delivery)
	if err != nil {
		glog.Error(params.DeliveryId, params.MtPeisongId, ",获取骑手配送信息失败，", err)
		res.Message = err.Error()
		return res, nil
	}
	etClient := et.GetExternalClient()
	switch delivery.DeliveryType {
	case 0: //美配
		appChannel := 0
		_, err := db.SQL("SELECT app_channel FROM dc_order.order_main WHERE order_sn = ? ", delivery.OrderSn).Get(&appChannel)
		locationRequest := &et.MpOrderRiderLocationRequest{
			MtPeisongId: delivery.MtPeisongId,
			DeliveryId:  delivery.DeliveryId,
			AppChannel:  cast.ToString(appChannel),
			OrderSn:     delivery.OrderSn,
		}
		locationRes, err := etClient.MPServer.MpOrderRiderLocation(etClient.Ctx, locationRequest)
		glog.Error(delivery.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
		if err != nil {
			glog.Error(delivery.OrderSn, ", 获取配送员位置出错", err.Error())
			res.Message = err.Error()
			return res, nil

		}
		if locationRes.Code != 200 {
			glog.Error(delivery.OrderSn, ", 获取配送员位置失败,", locationRes.Message)
			res.Message = locationRes.Message
			return res, nil
		}

		res.Data = locationRes.Data

	case 3: //达达
		//LogisticsSync.LogisticsProviderCode = "10002"
		//LogisticsSync.ThirdCarrierOrderId = params.MtPeisongId
		//env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
		//if env == "sit1" || env == "uat" || env == "dev" {
		//	//西藏自治区林芝市墨脱县金珠路
		//	LogisticsSync.Latitude = "29.33267"
		//	LogisticsSync.Longitude = "95.33735"
		//} else {
		//	//达达的骑手经纬度需要自己查询
		//	GetOrderPar := et.DaDaformalCancelRequst{}
		//	GetOrderPar.OrderId = params.OrderSn
		//	locationRes, err := etClient.DaDa.Query(etClient.Ctx, &GetOrderPar)
		//	glog.Error(c.order.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
		//	if err != nil {
		//		glog.Error(c.order.OrderSn, ", 获取配送员位置出错", err.Error())
		//		break
		//	}
		//	if locationRes.Code != 0 {
		//		glog.Error(c.order.OrderSn, ", 获取配送员位置失败,", locationRes.Error)
		//		break
		//	}
		//	//位置信息
		//	if locationRes.Data.TransporterLat != "" {
		//		LogisticsSync.Latitude = locationRes.Data.TransporterLat
		//		LogisticsSync.Longitude = locationRes.Data.TransporterLng
		//	}
		//}
	case 4: //蜂鸟

		GetOrderPar := et.FnCancelOrderRequst{}
		GetOrderPar.PartnerOrderCode = delivery.OrderSn
		locationRes, err := etClient.Fn.GetKnightInfo(etClient.Ctx, &GetOrderPar)
		glog.Error(delivery.OrderSn, ", 获取配送员位置结果", kit.JsonEncode(locationRes))
		if err != nil {
			glog.Error(delivery.OrderSn, ", 获取配送员位置出错", err.Error())
			return res, nil
			break
		}
		if locationRes.Code != "200" {
			glog.Error(delivery.OrderSn, ", 获取配送员位置失败,", locationRes.Msg)
			res.Message = locationRes.Msg
			return res, nil
		}
		CarrierDriverModel := dto.CarrierDriverModel{}
		err = json.Unmarshal([]byte(locationRes.BusinessData), &CarrierDriverModel)
		if err != nil {
			glog.Error(delivery.OrderSn, ", 解析蜂鸟配送员位置出错", err.Error())
		}
		courierLocation := new(dto.CourierLocation)
		courierLocation.Lng = cast.ToInt64(CarrierDriverModel.CarrierDriverLongitude)
		courierLocation.Lat = cast.ToInt64(CarrierDriverModel.CarrierDriverLatitude)
		//LogisticsSync.Latitude = CarrierDriverModel.CarrierDriverLatitude
		//LogisticsSync.Longitude = CarrierDriverModel.CarrierDriverLongitude
		str, err := json.Marshal(courierLocation)
		if err != nil {
			res.Message = "解析蜂鸟返回失败"
			return res, nil
		}
		res.Data = string(str)
		break
	default: //其他
		res.Message = "配送类型错误"
		return res, nil
	}
	res.Code = 200
	return res, nil
}

func randomMove(latitude, longitude float64) (string, string) {
	const earthRadius = 6371000 // 地球半径，单位：米

	// 将经纬度转换为弧度
	latRad := math.Pi * latitude / 180

	// 计算在当前经纬度上，10米对应的经纬度变化
	latDelta := 10 / earthRadius * (180 / math.Pi)
	lonDelta := latDelta / math.Cos(latRad)

	// 生成随机的新经纬度
	newLatitude := latitude + latDelta*rand.Float64()*2 - 1
	newLongitude := longitude + lonDelta*rand.Float64()*2 - 1

	return cast.ToString(newLatitude), cast.ToString(newLongitude)
}

// 美团配送更新配送信息接口(更新订单中心的订单状态：已送达，配送中等)
func (o OrderService) DeliveryNode(ctx context.Context, params *oc.DeliveryNodeRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info(params.OrderSn, ",DeliveryNode-更新配送信息：", paramsJson)
	out := &oc.BaseResponse{Code: 400}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	order := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.is_picking,order_detail.latitude,order_detail.longitude")
	//获取实物子订单
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
		session:   o.session,
	}

	//配送状态同步到第三方渠道
	switch order.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId:
		defer NewChannelOrder(order).SyncDeliveryNode(params)
	}

	oldOrderSn := order.OldOrderSn
	if order.ParentOrderSn == "" {
		mainOrderSn := order.OrderSn
		has, err := realOrder.session.SQL("SELECT * FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", mainOrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(mainOrderSn, ",第三方订单配送状态更新查询实物子单出错，", err)
			return out, errors.New("第三方订单配送状态更新查询实物子单出错")
		}
		if has == false {
			glog.Error(mainOrderSn, ",第三方订单配送状态更新未查询到实物子订单，", err)
			return out, errors.New("第三方订单配送状态更新未到查询实物子单")
		}
	} else {
		realOrder.orderMain = order.OrderMain
		parentMain := GetOrderMainByOrderSn(order.ParentOrderSn)
		oldOrderSn = parentMain.OldOrderSn
	}

	//如果是饿了么的，需要回传配送经纬度节点
	if order.ChannelId == ChannelElmId && (params.Status == 20 || params.Status == 30 || params.Status == 50) && order.AppChannel != 12 {
		//获取门店经纬度
		etClient := et.GetExternalClient()
		redisConn := GetRedisConn()
		elmId, err := redisConn.HGet("store:relation:dctoele", order.ShopId).Result()
		if err != nil {
			glog.Info("获取饿了么门店redis连接报错", err.Error(), " ShopId:", order.ShopId)

		}

		if elmId == "" {
			glog.Info("根据财务编码未获取到饿了么对应ID", order.ShopId)

		}
		paramsSync := new(et.ElmDeliveryLocationSyncRequest)
		paramsSync.OrderId = oldOrderSn
		paramsSync.AppChannel = order.AppChannel
		//已经完成传用户的位置
		if params.Status == 50 {
			paramsSync.Latitude = cast.ToString(order.Latitude)
			paramsSync.Longitude = cast.ToString(order.Longitude)
		} else {
			request := new(et.ElmStoreyGetRequest)
			request.ShopId = elmId
			request.FinanceCode = order.ShopId

			ret, err := etClient.ELMSTORE.GetElmStorey(context.Background(), request)
			if err != nil {
				glog.Info("获取饿了么店铺信息接口报错", order.ShopId, err.Error())
			}

			paramsSync.Latitude = cast.ToString(ret.Data.Latitude)
			paramsSync.Longitude = cast.ToString(ret.Data.Longitude)
			//骑手已接单  ,取店铺经纬度随意位移10米
			//if params.Status == 20 {
			//	paramsSync.Latitude, paramsSync.Longitude = randomMove(ret.Data.Latitude, ret.Data.Longitude)
			//}
		}

		_, err = etClient.ELMORDER.ElmDeliveryLocationSync(context.Background(), paramsSync)
		if err != nil {
			glog.Info("同步骑手位置给饿了么报错", order.ShopId, err.Error())
		}

		//异常单的处理不走下面的流程 仅同步到第三方
		if params.IsException == 1 {
			out.Code = 200
			return out, nil
		}
	}
	if order.Id == 0 {
		out.Message = "订单不存在"
		glog.Warning("更新配送信息接口，订单不存在：" + params.OrderSn)
		return out, nil
	}
	//o.orderMain = order.OrderMain

	deliveryType := "美团"

	//闪送回调没有配送ID，需要查询配送记录
	if params.DeliveryType == 1 {
		deliveryType = "闪送"

	} else if order.DeliveryType == 5 && params.DeliveryType == 3 { //订单是商家自配
		deliveryType = "商家自配"
	} else if params.DeliveryType == 3 {
		deliveryType = "达达"
	} else if params.DeliveryType == 4 {
		deliveryType = "蜂鸟"
	} else if params.DeliveryType == 5 {
		deliveryType = "顺丰"
	}

	//没有配送ID的需要自己查询
	if params.DeliveryType == 1 || params.DeliveryType == 4 {
		//订单流转日志
		deliveryRecord := models.OrderDeliveryRecord{}
		ok, err := o.session.Where("mt_peisong_id = ? and order_sn = ?", params.MtPeisongId, realOrder.orderMain.OrderSn).Get(&deliveryRecord)
		if err != nil {
			out.Message = "配送单查询错误"
			glog.Warning("更新配送信息接口，配送单不存在：" + realOrder.orderMain.OrderSn)
			return out, nil
		}
		if !ok {
			out.Message = "配送单不存在！"
			glog.Error("更新配送信息接口，配送单不存在，", realOrder.orderMain.OrderSn)
			return out, nil
		}
		params.DeliveryId = deliveryRecord.DeliveryId
	}

	//没有配送ID的需要自己查询
	if params.DeliveryType == 3 {
		//订单流转日志
		deliveryRecord := models.OrderDeliveryRecord{}
		ok, err := o.session.Where("order_sn = ? and delivery_type=3 ", realOrder.orderMain.OrderSn).OrderBy("create_time desc").Get(&deliveryRecord)
		if err != nil {
			out.Message = "配送单查询错误"
			glog.Warning("更新配送信息接口，配送单不存在："+realOrder.orderMain.OrderSn, err.Error())
			return out, nil
		}
		if !ok {
			out.Message = "配送单不存在！"
			glog.Error("更新配送信息接口，配送单不存在，", realOrder.orderMain.OrderSn)
			return out, nil
		}
		params.DeliveryId = deliveryRecord.DeliveryId
	}

	//订单流转日志
	var orderLogs []*models.OrderLog

	glog.Info("骑手更新配送信息mt：", paramsJson)
	var logType int
	var Content string
	var OrderStatusChild int32
	operate_type_name := "美团"
	if params.DeliveryType == 3 {
		operate_type_name = "达达"
	} else if params.DeliveryType == 4 {
		operate_type_name = "蜂鸟"
	} else if params.DeliveryType == 5 {
		operate_type_name = "顺丰"
	} else if params.DeliveryType == 2 {
		operate_type_name = "商家自配"
	} else if params.DeliveryType == 1 {
		operate_type_name = "闪送"
	}

	switch params.Status {
	case 0:
		Content = "调度中"
		OrderStatusChild = 20102
		logType = models.OrderLogSSDelivering
		//logType = models.OrderLogDelivering
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSDelivering
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfDelivering
		//}
	case 15:
		Content = "骑手已到店"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierArrived
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSCourierArrived
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfCourierArrived
		//}
	case 20:
		Content = "骑手已接单"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierAcceptedOrder
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSCourierAcceptedOrder
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfCourierAcceptedOrder
		//}
	case 30:
		Content = "骑手已取货"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierTakedProduct
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSCourierTakedProduct
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfCourierTakedProduct
		//}
	case 45:
		Content = "等待骑手送回商品"
		OrderStatusChild = 20103
		logType = models.OrderLogConfirmGoodsReturn
	case 50:
		Content = "已送达"
		OrderStatusChild = 20106
		//logType = models.OrderLogCourierDeliveryed
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSCourierDeliveryed
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfCourierDeliveryed
		//}
	case 99:
		Content = "骑手已取消"
		//OrderStatusChild = 20102 //美团取消订单也会推骑手取消，此状态可能会覆盖取消状态
		//logType = models.OrderLogCourierCanceled
		//if params.DeliveryType == 1 {
		//	logType = models.OrderLogSSCourierCanceled
		//}
		//if order.DeliveryType == 5 && params.DeliveryType == 3 {
		//	logType = models.OrderLogBySelfCourierCanceled
		//}
	case 101:
		Content = "妥投异常之物品返回中"
		logType = models.OrderLogreturning
	case 102:
		Content = "妥投异常之物品返回完成"
		logType = models.OrderLogreturned
	default:
		out.Message = "状态码不正确！"
		return out, nil
	}
	Content = deliveryType + Content

	orderLogs = append(orderLogs, []*models.OrderLog{
		{
			OrderSn:         realOrder.orderMain.ParentOrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         realOrder.orderMain.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
	}...)

	o.session.Begin()

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     params.DeliveryId,
		OrderSn:        realOrder.orderMain.OrderSn,
		DeliveryStatus: params.Status,
		Content:        Content,
		CourierName:    params.CourierName,
		CourierPhone:   params.CourierPhone,
		CancelReason:   params.CancelReason,
		CreateTime:     time.Now(),
	}
	_, err := o.session.Insert(&deliveryModel)
	if err != nil {
		glog.Error(realOrder.orderMain.OrderSn, ", 更新配送信息接口，信息保存失败, ", err)
	}

	orderModelUp := models.OrderMain{}
	orderModelUp.OrderStatusChild = OrderStatusChild
	if params.Status == 50 {
		orderModelUp.OrderStatus = 30
		orderModelUp.ConfirmTime = time.Now()
	}

	if params.Status == 0 {
		o.session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE id = ?", realOrder.orderMain.Id)
	}

	orderDetailUp := &models.OrderDetail{}

	//骑手接单后更新配送时间，拣货时间
	if params.Status == 20 {
		orderModelUp.DeliverTime = time.Now()
		if order.IsPicking == 0 && order.ChannelId != ChannelElmId {
			orderDetailUp.IsPicking = 1
			orderDetailUp.PickingTime = time.Now()

			if order.ChannelId == ChannelJddjId {
				err = JddjOrderSerllerDelivery(oldOrderSn, order.AppChannel)
				if err != nil {
					glog.Error("定时任务推送京东到家拣货完成且商家自送接口失败！", realOrder.orderMain.OrderSn, "，"+oldOrderSn, err)
				}
			}

			////饿了么通知拣货  改成定时任务拣货
			//if order.ChannelId == ChannelElmId {
			//	orderModel := GetOrderByOrderSn(params.OrderSn, "order_main.*,order_detail.accept_time,order_detail.push_delivery,order_detail.push_third_order")
			//	err = NewChannelOrder(orderModel).PickOrder()
			//	if err != nil {
			//		glog.Error("饿了么通知拣货失败！", realOrder.orderMain.OrderSn, "，"+oldOrderSn, err)
			//	}
			//}

		}
	}
	//等待骑手送回商品
	if params.Status == 45 {
		orderDetailUp.GoodsReturnDeliveryId = params.DeliveryId
	}

	//已付款订单才能更新订单状态
	if realOrder.orderMain.OrderStatus == 20 && OrderStatusChild > 0 {
		_, err = o.session.Where("order_status=20").In("order_sn", []string{realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn}).Update(&orderModelUp)
		if err != nil {
			glog.Error(realOrder.orderMain.OrderSn, "更新配送信息订单状态错误, ", err.Error())
		}

		_, err = o.session.In("order_sn", []string{realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn}).Update(orderDetailUp)
		if err != nil {
			glog.Error(realOrder.orderMain.OrderSn, ", 更新配送信息订单状态错误, ", err.Error())
		}

		glog.Info(realOrder.orderMain.OrderSn, ", 更新配送信息订单状态")
	} else {
		//TODO 此处很可能更新了GoodsReturnDeliveryId 字段  目前只有闪送支持货物送回确认 所以应该只有闪送的单子去更新
		if orderDetailUp.GoodsReturnDeliveryId > 0 {
			_, err = o.session.In("order_sn", []string{realOrder.orderMain.ParentOrderSn, realOrder.orderMain.OrderSn}).
				Cols("goods_return_delivery_id").Update(&models.OrderDetail{
				GoodsReturnDeliveryId: orderDetailUp.GoodsReturnDeliveryId,
			})
			if err != nil {
				glog.Error(realOrder.orderMain.OldOrderSn, ", 更新配送信息, ", err.Error())
			}
		}
	}

	err = o.session.Commit()
	if err != nil {
		glog.Error(realOrder.orderMain.OldOrderSn, ", 更新配送信息接口commit, ", err.Error())
	} else {
		//如果拣货完成了记录日志
		if orderDetailUp.IsPicking == 1 {
			orderLogs = append(orderLogs, []*models.OrderLog{
				{
					OrderSn: order.ParentOrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: order.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
			}...)
		}
	}

	//记录订单流转日志
	go SaveOrderLog(orderLogs)

	//推送订单状态到 腾讯有数
	go PushOrderStatusToTencent(order.OrderSn, 0)

	//已完成通知子龙
	if params.Status == 50 {
		if err = realOrder.FinalizeOrder(&oc.AccomplishOrderRequest{
			OrderSn:     params.OrderSn,
			ConfirmTime: kit.GetTimeNow(orderModelUp.ConfirmTime),
		}); err != nil {
			glog.Error("推送子龙失败：", params.OrderSn, " ", err.Error())
		}
		// 推送消息队列增加积分
		IntegralOperation(order.OrderSn, true)
	}

	//根据错误原因来判断是否要加入异常配送，如果是我们自己取消，进行切换配送的话，不需要进行下一个
	if (params.Status == 99 || params.Status == 102) && params.CancelReason != "阿闻超时未接单自动取消" {
		OrderException := OrderExceptionService{}
		DeliveryId := params.DeliveryId
		ExceptionRequest := oc.OrderExceptionRequest{
			DeliveryId:     fmt.Sprintf("%d", DeliveryId),
			MtPeisongId:    params.MtPeisongId,
			OrderId:        realOrder.orderMain.OrderSn,
			ExceptionDescr: "配送平台的配送订单被取消",
			ExceptionTime:  kit.GetTimeNow(),
			Source:         1,
			OrderStatus:    2,
		}
		res, err := OrderException.OrderExceptionAdd(nil, &ExceptionRequest)
		if err != nil || res.Code != 200 {
			glog.Error("异常单保存失败！", params.OrderSn)
		}
	}

	out.Code = 200
	out.Message = "ok"
	return out, nil
}

// 美团自带配送状态更新
func (o OrderService) MtDeliveryNode(ctx context.Context, params *oc.DeliveryNodeRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("美团骑手更新配送信息：", paramsJson)
	out := oc.BaseResponse{Code: 400}

	order := GetOrderByOldOrderSn(params.OrderSn, "order_main.*,order_detail.is_picking")
	//如果是SAAS的小程序，配送回来的订单是子订单号，要转化
	if order.OrgId == 6 && order.ChannelId == 1 {
		order = GetOrderByOldOrderSn(order.ParentOrderSn, "order_main.*,order_detail.is_picking")
	}

	if order.Id == 0 {
		out.Message = "订单不存在"
		glog.Error(params.OrderSn, ", 更新配送信息接口，订单不存在")
		return &out, nil
	}

	//查询子订单1
	realOrder := new(models.OrderMain)
	_, err := GetDBConn().SQL("SELECT id,order_sn,logistics_code FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", order.OrderSn).Get(realOrder)
	if err != nil {
		out.Message = "子订单不存在"
		glog.Error(params.OrderSn, ", 更新配送信息接口，子订单不存在")
		return &out, nil
	}
	if realOrder.Id == 0 {
		out.Message = "子订单不存在"
		glog.Error(params.OrderSn, ", 更新配送信息接口，子订单不存在")
		return &out, nil
	}

	//如果订单不是美团专送，回调回来的配送取消，直接过滤
	if !strings.Contains("2002,1001,1004,2010,3001,1007", realOrder.LogisticsCode) && params.Status == 99 && realOrder.OrgId != 6 {
		glog.Info(params.OrderSn, ", 不是美团专配的取消配送回调自动过滤")
		out.Code = 200
		return &out, nil
	}

	//此处进行了订单号的更替 将ordersn 改为主单号
	params.OrderSn = order.OrderSn

	o.orderMain = order.OrderMain

	//订单流转日志
	var orderLogs []*models.OrderLog
	operate_type_name := "美团"
	if params.IsMyt == 1 {
		operate_type_name = "麦芽田"
	}
	glog.Info("骑手更新配送信息2：", order.OldOrderSn, paramsJson)
	logType := models.OrderLogDelivering
	var Content string
	var OrderStatusChild int32
	switch params.Status {
	case 0:
		Content = "调度中"
		OrderStatusChild = 20102
		logType = models.OrderLogDelivering
		if params.IsMyt == 1 {
			logType = models.OrderLogSSDelivering
		}
	case 15:
		Content = "骑手已到店"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierArrived
		if params.IsMyt == 1 {
			logType = models.OrderLogSSCourierArrived
		}
	case 20:
		Content = "骑手已接单"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierAcceptedOrder
		if params.IsMyt == 1 {
			logType = models.OrderLogSSCourierAcceptedOrder
		}
	case 30:
		Content = "骑手已取货"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierTakedProduct
		if params.IsMyt == 1 {
			logType = models.OrderLogSSCourierTakedProduct
		}
	case 50:
		Content = "已送达"
		OrderStatusChild = 20106
		logType = models.OrderLogCourierDeliveryed
		if params.IsMyt == 1 {
			logType = models.OrderLogSSCourierDeliveryed
		}
	case 99:
		Content = "骑手已取消"
		//OrderStatusChild = 20102 //美团取消订单也会推骑手取消，此状态可能会覆盖取消状态
		logType = models.OrderLogCourierCanceled
	default:
		out.Message = "状态码不正确！"
		return &out, nil
	}

	orderLogs = append(orderLogs, []*models.OrderLog{
		{
			OrderSn:         order.ParentOrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         order.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         realOrder.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
	}...)

	o.session = GetDBConn().NewSession()
	defer o.session.Close()
	o.session.Begin()

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     params.DeliveryId,
		OrderSn:        params.OrderSn,
		DeliveryStatus: params.Status,
		Content:        Content,
		CourierName:    params.CourierName,
		CourierPhone:   params.CourierPhone,
		CancelReason:   params.CancelReason,
		CreateTime:     time.Now(),
	}
	_, err = o.session.Insert(&deliveryModel)

	if err != nil {
		glog.Error("更新配送信息接口，信息保存失败："+params.OrderSn, order.OldOrderSn, err)
	}
	deliveryRecord := new(models.OrderDeliveryRecord)
	has, err := o.session.Where("delivery_id=?", params.DeliveryId).Get(deliveryRecord)
	if err != nil {
		glog.Error("美团专送，查询配送单出错："+params.OrderSn, order.OldOrderSn, err)
	}
	//如果没有数据
	if !has && err == nil {
		deliveryRecord.DeliveryId = params.DeliveryId
		deliveryRecord.MtPeisongId = params.MtPeisongId
		deliveryRecord.OrderSn = realOrder.OrderSn
		deliveryRecord.DeliveryServiceCode = 3001
		_, err = o.session.Insert(deliveryRecord)
		if err != nil {
			glog.Error("美团专送，插入配送单出错："+params.OrderSn, order.OldOrderSn, err)
		}
	}
	orderMainUp := &models.OrderMain{
		OrderStatusChild: OrderStatusChild,
	}
	if params.Status == 50 {
		orderMainUp.OrderStatus = 30
		orderMainUp.ConfirmTime = time.Now()
	}

	if params.Status == 0 {
		o.session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE `old_order_sn` = ?", order.OldOrderSn)
		o.session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE `order_sn` = ?", realOrder.OrderSn)
	}
	orderDetailUp := &models.OrderDetail{}
	//骑手接单后更新配送时间，拣货时间
	if params.Status == 20 && order.OrgId != 6 {
		if order.IsPicking == 0 {
			orderDetailUp.IsPicking = 1
			orderDetailUp.PickingTime = time.Now()
		}
		orderMainUp.DeliverTime = time.Now()
	}

	//已付款订单才能更新订单状态
	if order.OrderStatus == 20 && OrderStatusChild > 0 {

		_, err = o.session.ID(order.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息订单状态失败1, ", err)
			return nil, errors.New("更新配送信息订单状态失败1，" + err.Error())
		}

		//更新实物子订单
		_, err = o.session.ID(realOrder.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息子订单状态失败1, ", err)
			return nil, errors.New("更新配送信息子订单状态失败1，" + err.Error())
		}

		_, err = o.session.ID(order.OrderSn).Update(orderDetailUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息订单详情状态失败2, ", err)
			return nil, errors.New("更新配送信息订单详情状态失败2，" + err.Error())
		}

		//更新实物子订单
		_, err = o.session.Where("order_sn=?", realOrder.OrderSn).Update(orderDetailUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息子订单状详情态失败1, ", err)
			return nil, errors.New("更新配送信息子订单详情状态失败1，" + err.Error())
		}

		glog.Info("更新配送信息订单状态 " + order.OldOrderSn)
	}

	err = o.session.Commit()
	if err != nil {
		glog.Error(order.OldOrderSn, ", 更新配送信息接口commit, ", err)
		return nil, errors.New("更新配送信息接口commit失败，" + err.Error())
	}

	//如果拣货完成了记录日志
	if orderDetailUp.IsPicking == 1 {
		orderLogs = append(orderLogs, []*models.OrderLog{
			{
				OrderSn: order.ParentOrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: order.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: realOrder.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		}...)
	}

	//记录订单流转日志
	go SaveOrderLog(orderLogs)

	if params.Status == 99 {
		distributionMode := "美团专送"
		if order.LogisticsCode == "8888" {
			distributionMode = "美团众包"
		}
		OrderException := OrderExceptionService{}
		DeliveryId := params.DeliveryId
		random := rand.Intn(899) + 100
		mtPeisongId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
		ExceptionRequest := oc.OrderExceptionRequest{
			DeliveryId:       fmt.Sprintf("%d", DeliveryId),
			MtPeisongId:      mtPeisongId,
			OrderId:          params.OrderSn,
			ExceptionDescr:   "配送平台的配送订单被取消",
			ExceptionTime:    kit.GetTimeNow(),
			CourierName:      params.CourierName,
			CourierPhone:     params.CourierPhone,
			Source:           1,
			OrderStatus:      2,
			DistributionMode: distributionMode,
		}
		res, err := OrderException.OrderExceptionAdd(nil, &ExceptionRequest)
		if err != nil || res.Code != 200 {
			glog.Error("异常单保存失败！", params.OrderSn)
		}
	} else {
		if params.Status != 0 {
			_, err = o.session.Table("order_exception").Where("delivery_id = ?", cast.ToString(params.DeliveryId)).Cols("is_show").Update(&models.OrderException{
				IsShow: 0,
			})
			if err != nil {
				glog.Error(order.OldOrderSn, "，有配送状态来了，修改异常订单状态失败，", err)
			}
		}
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 饿了么自带配送状态更新
func (o OrderService) ElmDeliveryNode(ctx context.Context, params *oc.ElmDeliveryNodeRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("饿了么骑手更新配送信息：", paramsJson)
	out := oc.BaseResponse{Code: 400}

	dbConn := GetDBConn()

	orderMain := &models.OrderMain{}
	ok, err := dbConn.Select("id,old_order_sn,order_sn,logistics_code").Where("old_order_sn= ?", params.OrderSn).Get(orderMain)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("更新配送信息接口:", params.OrderSn+err.Error())
		return &out, nil
	}
	params.OrderSn = orderMain.OrderSn
	if !ok {
		out.Message = "订单不存在"
		glog.Error("更新配送信息接口，订单不存在:" + params.OrderSn)
		return &out, nil
	}

	//查询子订单
	realOrder := new(models.OrderMain)
	ok, err = dbConn.SQL("SELECT id,order_sn FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", orderMain.OrderSn).Get(realOrder)

	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("更新配送信息接口:", params.OrderSn+err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "子订单不存在"
		glog.Error("更新配送信息接口，子订单不存在:" + params.OrderSn)
		return &out, nil
	}

	glog.Info("骑手更新配送信息2：", orderMain.OldOrderSn, paramsJson)
	logType := models.OrderLogDelivering
	var status int32
	if params.Status == 7 {
		status = 20
	} else if params.Status == 8 {
		status = 30
	} else if params.Status == 15 {
		status = 40
	} else if params.Status == 16 {
		status = 50
	} else if params.Status == 17 {
		status = 99
	}
	operate_type_name := "饿了么"

	//订单流转日志
	var orderLogs []*models.OrderLog

	var OrderStatusChild int32
	var Content string
	switch status {
	case 0:
		Content = "调度中"
		OrderStatusChild = 20102
		logType = models.OrderLogDelivering
	case 20:
		Content = "骑手已接单"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierAcceptedOrder
	case 30:
		Content = "骑手已取货"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierTakedProduct
	case 40:
		Content = "配送取消"
	case 50:
		Content = "已送达"
		OrderStatusChild = 20106
		logType = models.OrderLogCourierDeliveryed
	case 99:
		//Content = "骑手已取消"
		Content = "配送异常"
		//OrderStatusChild = 20102 //美团取消订单也会推骑手取消，此状态可能会覆盖取消状态
		logType = models.OrderLogCourierCanceled
	default:
		out.Code = 400
		out.Message = "状态码不正确！"
		return &out, nil
	}

	orderLogs = append(orderLogs, []*models.OrderLog{
		{
			OrderSn:         orderMain.ParentOrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         orderMain.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         realOrder.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
	}...)

	session := dbConn.NewSession()
	defer session.Close()
	session.Begin()

	deliveryRecord := new(models.OrderDeliveryRecord)
	has, err := dbConn.Where("order_sn=?", realOrder.OrderSn).Get(deliveryRecord)
	if err != nil {
		glog.Error("美团专送，查询配送单出错："+params.OrderSn, orderMain.OldOrderSn, err)
	}
	//如果没有数据
	if !has && err == nil {
		deliveryRecord.DeliveryId = params.DeliveryId
		deliveryRecord.MtPeisongId = params.MtPeisongId
		deliveryRecord.OrderSn = realOrder.OrderSn
		deliveryRecord.DeliveryServiceCode = 3002
		_, err = dbConn.Insert(deliveryRecord)
		if err != nil {
			glog.Error("美团专送，插入配送单出错："+params.OrderSn, orderMain.OldOrderSn, err)
		}
	}
	if deliveryRecord.DeliveryId != 0 {
		params.DeliveryId = deliveryRecord.DeliveryId
	}

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     params.DeliveryId,
		OrderSn:        params.OrderSn,
		DeliveryStatus: status,
		Content:        Content,
		CourierName:    params.CourierName,
		CourierPhone:   params.CourierPhone,
		CancelReason:   params.CancelReason,
		CreateTime:     time.Now(),
	}
	_, err = session.Insert(&deliveryModel)
	if err != nil {
		glog.Error(params.OrderSn, "，更新配送信息接口，信息保存失败，", err)
		session.Rollback()
		return nil, errors.New("数据库更新失败, " + err.Error())
	}

	orderMainUp := &models.OrderMain{
		OrderStatusChild: OrderStatusChild,
	}
	if status == 50 {
		orderMainUp.OrderStatus = 30
		orderMainUp.ConfirmTime = time.Now()
	}

	if status == 0 {
		_, err = session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE `order_sn` = ?", params.OrderSn)
		if err != nil {
			glog.Error(params.OrderSn, "，更新配送时间信息保存失败，", err)
			session.Rollback()
			return nil, errors.New("数据库更新失败, " + err.Error())
		}
		_, err = session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE `order_sn` = ?", realOrder.OrderSn)
		if err != nil {
			glog.Error(params.OrderSn, "，更新配送时间信息保存失败，", err)
			session.Rollback()
			return nil, errors.New("数据库更新失败, " + err.Error())
		}
	}

	orderDetailUp := &models.OrderDetail{
		IsPicking:   1,
		PickingTime: time.Now(),
	}
	//骑手接单后更新配送时间，拣货时间
	if status == 20 {
		orderDetail := GetOrderDetailByOrderSn(orderMain.OrderSn, "is_picking")
		if orderDetail.IsPicking == 0 {
			_, err = session.ID(orderMain.OrderSn).Update(orderDetailUp)
			if err != nil {
				glog.Error("更新配送信息订单状态错误 "+orderMain.OldOrderSn, err.Error())
				session.Rollback()
				return nil, errors.New("数据库更新失败, " + err.Error())
			}
		}

		realOrderDetail := GetOrderDetailByOrderSn(realOrder.OrderSn, "is_picking")
		if realOrderDetail.IsPicking == 0 {
			_, err = session.Where("order_sn=?", realOrder.OrderSn).Update(orderDetailUp)
			if err != nil {
				glog.Error("更新配送信息子订单状态错误 "+orderMain.OldOrderSn, err.Error())
				session.Rollback()
				return nil, errors.New("数据库更新失败, " + err.Error())
			}
		}
		orderMainUp.DeliverTime = time.Now()
	}

	//已付款订单才能更新订单状态
	if orderMain.OrderStatus == 20 && OrderStatusChild > 0 {
		_, err = session.ID(orderMain.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			glog.Error("更新配送信息订单状态错误 "+orderMain.OldOrderSn, err.Error())
			session.Rollback()
			return nil, errors.New("数据库更新失败, " + err.Error())
		}
		//更新实物子订单
		_, err = o.session.ID(realOrder.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(orderMain.OldOrderSn, ", 更新配送信息子订单状态失败1, ", err)
			return nil, errors.New("更新配送信息子订单状态失败1，" + err.Error())
		}
		glog.Info("更新配送信息订单状态 " + orderMain.OldOrderSn)
	}

	err = session.Commit()
	if err != nil {
		glog.Error(orderMain.OldOrderSn, "，更新配送信息接口commit失败，", err)
	} else {
		//如果拣货完成了记录日志
		if orderDetailUp.IsPicking == 1 {
			orderLogs = append(orderLogs, []*models.OrderLog{
				{
					OrderSn: orderMain.ParentOrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: orderMain.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: realOrder.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
			}...)
		}
	}

	//记录订单流转日志
	go SaveOrderLog(orderLogs)

	if status == 99 {
		distributionMode := "蜂鸟专送"
		if orderMain.LogisticsCode == "4" {
			distributionMode = "饿了么众包"
		}
		OrderException := OrderExceptionService{}
		DeliveryId := params.DeliveryId
		random := rand.Intn(899) + 100
		mtPeisongId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
		ExceptionRequest := oc.OrderExceptionRequest{
			DeliveryId:       fmt.Sprintf("%d", DeliveryId),
			MtPeisongId:      mtPeisongId,
			OrderId:          params.OrderSn,
			ExceptionDescr:   "配送平台的配送订单被取消",
			ExceptionTime:    kit.GetTimeNow(),
			CourierPhone:     params.CourierPhone,
			Source:           1,
			OrderStatus:      2,
			DistributionMode: distributionMode,
		}
		res, err := OrderException.OrderExceptionAdd(nil, &ExceptionRequest)
		if err != nil || res.Code != 200 {
			glog.Error("异常单保存失败！", params.OrderSn)
		}
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 京东到家自带配送状态更新
func (o OrderService) JddjDeliveryNode(ctx context.Context, params *oc.ElmDeliveryNodeRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("京东到家骑手更新配送信息：", paramsJson)
	out := oc.BaseResponse{Code: 400}

	//连接池勿关闭
	db := GetDBConn()

	order := models.Order{}
	ok, err := db.SQL("select order_main.*,order_detail.is_picking from order_main inner join order_detail on order_main.order_sn=order_detail.order_sn where old_order_sn= ? order by id desc", params.OrderSn).Get(&order)
	params.OrderSn = order.OrderSn
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("更新配送信息接口", params.OrderSn+err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "订单不存在"
		glog.Error("更新配送信息接口，订单不存在:" + params.OrderSn)
		return &out, nil
	}
	operate_type_name := "京东"
	//查询子订单
	realOrder := new(models.OrderMain)
	ok, err = o.session.SQL("SELECT id,order_sn FROM order_main WHERE parent_order_sn = ? AND is_virtual =0", order.OrderSn).Get(realOrder)
	if err != nil {
		out.Message = "子订单不存在"
		glog.Error(params.OrderSn, ", 更新配送信息接口，子订单不存在")
		return &out, nil
	}
	if !ok {
		out.Message = "子订单不存在"
		glog.Error("更新配送信息接口，子订单不存在:" + params.OrderSn)
		return &out, nil
	}

	glog.Info("骑手更新配送信息2：", order.OldOrderSn, paramsJson)
	logType := models.OrderLogDelivering
	var (
		Content                  string
		OrderStatusChild, status int32
	)
	if params.Status == 20 {
		status = 20
	} else if params.Status == 30 {
		status = 30
	} else if params.Status == 35 {
		status = 40
	} else if params.Status == 40 {
		status = 50
	} else if params.Status == 28 || params.Status == 35 || params.Status == 25 {
		status = 99
	}

	//订单流转日志
	var orderLogs []*models.OrderLog

	switch status {
	case 0:
		Content = "调度中"
		//OrderStatusChild = 20102
		logType = models.OrderLogDelivering
	case 20:
		Content = "骑手已接单"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierAcceptedOrder
	case 30:
		Content = "骑手已取货"
		OrderStatusChild = 20103
		logType = models.OrderLogCourierTakedProduct
	case 40:
		Content = "配送取消"
	case 50:
		Content = "已送达"
		OrderStatusChild = 20106
		logType = models.OrderLogCourierDeliveryed
	case 99:
		Content = "配送异常"
		logType = models.OrderLogCourierCanceled
	default:
		out.Code = 400
		out.Message = "状态码不正确！"
		return &out, nil
	}

	orderLogs = append(orderLogs, []*models.OrderLog{
		{
			OrderSn:         order.ParentOrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         order.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
		{
			OrderSn:         realOrder.OrderSn,
			LogType:         logType,
			OperateTypeName: operate_type_name,
		},
	}...)

	session := db.NewSession()
	defer session.Close()
	session.Begin()

	deliveryModel := models.OrderDeliveryNode{
		DeliveryId:     params.DeliveryId,
		OrderSn:        params.OrderSn,
		DeliveryStatus: status,
		Content:        Content,
		CourierName:    "-",
		CourierPhone:   params.CourierPhone,
		CancelReason:   params.CancelReason,
		CreateTime:     time.Now(),
	}
	_, err = session.Insert(&deliveryModel)
	if err != nil {
		glog.Error(params.OrderSn, "，更新配送信息接口，信息保存失败，", err)
	}

	deliveryRecord := new(models.OrderDeliveryRecord)
	has, err := o.session.Where("delivery_id=?", params.DeliveryId).Get(deliveryRecord)
	if err != nil {
		glog.Error("美团专送，查询配送单出错："+params.OrderSn, order.OldOrderSn, err)
	}
	//如果没有数据
	if !has && err == nil {
		deliveryRecord.DeliveryId = params.DeliveryId
		deliveryRecord.MtPeisongId = params.MtPeisongId
		deliveryRecord.OrderSn = realOrder.OrderSn
		deliveryRecord.DeliveryServiceCode = 3003
		_, err = o.session.Insert(deliveryRecord)
		if err != nil {
			glog.Error("美团专送，插入配送单出错："+params.OrderSn, order.OldOrderSn, err)
		}
	}

	orderMainUp := &models.OrderMain{
		OrderStatusChild: OrderStatusChild,
	}
	if status == 50 {
		orderMainUp.OrderStatus = 30
		orderMainUp.ConfirmTime = time.Now()
	}

	if status == 0 {
		session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE id = ?", order.Id)
		session.Exec("UPDATE `dc_order`.`order_main` SET `deliver_time` = NULL WHERE id = ?", realOrder.Id)
	}

	orderDetailUp := models.OrderDetail{}
	//骑手接单后更新配送时间，拣货时间
	if status == 20 {
		if order.IsPicking == 0 {
			orderDetailUp.IsPicking = 1
			orderDetailUp.PickingTime = time.Now()
		}
		orderMainUp.DeliverTime = time.Now()
	}

	//已付款订单才能更新订单状态
	if order.OrderStatus == 20 && OrderStatusChild > 0 {
		_, err = session.ID(order.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			glog.Info(order.OldOrderSn, ", 更新配送信息订单主信息错误, ", err)
		}
		//更新实物子订单
		_, err = session.ID(realOrder.Id).Where("order_status=20").Update(orderMainUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息子订单状态失败1, ", err)
			return nil, errors.New("更新配送信息子订单状态失败1，" + err.Error())
		}

		_, err = session.ID(order.OrderSn).Update(orderDetailUp)
		if err != nil {
			glog.Info(order.OldOrderSn, ", 更新配送信息订单详情信息错误, ", err)
		}
		//更新实物子订单
		_, err = session.Where("order_sn=?", realOrder.OrderSn).Update(orderMainUp)
		if err != nil {
			o.session.Rollback()
			glog.Error(order.OldOrderSn, ", 更新配送信息子订单状态失败1, ", err)
			return nil, errors.New("更新配送信息子订单状态失败1，" + err.Error())
		}
		glog.Info("更新配送信息订单状态 " + order.OldOrderSn)
	}

	err = session.Commit()
	if err != nil {
		glog.Error(order.OldOrderSn, "，更新配送信息接口commit失败，", err)
	} else {
		//如果拣货完成了记录日志
		if orderDetailUp.IsPicking == 1 {
			orderLogs = append(orderLogs, []*models.OrderLog{
				{
					OrderSn: order.ParentOrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: order.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
				{
					OrderSn: realOrder.OrderSn,
					LogType: models.OrderLogPickedOrder,
				},
			}...)
		}
	}

	//记录订单流转日志
	go SaveOrderLog([]*models.OrderLog{{
		OrderSn: order.OrderSn,
		LogType: logType,
	}})

	if status == 99 {
		distributionMode := "达达配送"
		if order.LogisticsCode == "9966" {
			distributionMode = "京东众包"
		}
		OrderException := OrderExceptionService{}
		DeliveryId := params.DeliveryId
		random := rand.Intn(899) + 100
		mtPeisongId := fmt.Sprintf("%d%d", time.Now().UnixNano()/1e6, random)
		ExceptionRequest := oc.OrderExceptionRequest{
			DeliveryId:       fmt.Sprintf("%d", DeliveryId),
			MtPeisongId:      mtPeisongId,
			OrderId:          params.OrderSn,
			ExceptionDescr:   "配送平台的配送订单被取消",
			ExceptionTime:    kit.GetTimeNow(),
			CourierName:      params.CourierName,
			CourierPhone:     params.CourierPhone,
			Source:           1,
			OrderStatus:      2,
			DistributionMode: distributionMode,
		}
		res, err := OrderException.OrderExceptionAdd(nil, &ExceptionRequest)
		if err != nil || res.Code != 200 {
			glog.Error("异常单保存失败！", params.OrderSn)
		}
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 取消配送
func (o OrderService) CancelDelivery(ctx context.Context, params *oc.CancelDeliveryRequest) (*oc.BaseResponse, error) {
	paramsJson := kit.JsonEncode(params)
	glog.Info("取消配送：", paramsJson)
	out := oc.BaseResponse{Code: 400}

	db := GetDBConn()

	//配送记录
	var record models.OrderDeliveryRecord

	ok, err := db.Where("delivery_id = ?", params.DeliveryId).Get(&record)
	if err != nil {
		glog.Error("取消配送,查询错误：", params.DeliveryId, params.OrderSn)
	}
	if !ok {
		out.Message = "配送订单不存在！"
		glog.Warning("取消配送,订单不存在：", params.OrderSn)
		return &out, nil
	}

	var orderModel models.Order

	ok, err = db.SQL("select a.*,b.push_third_order,b.push_delivery from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.order_sn=? order by a.id desc", record.OrderSn).Get(&orderModel)
	if err != nil {
		out.Message = "订单查询错误!"
		out.Error = err.Error()
		glog.Error("取消配送,查询错误：", record.OrderSn, err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "订单不存在！"
		glog.Warning("取消配送,订单不存在：", record.OrderSn)
		return &out, nil
	}

	if orderModel.PushDelivery == 1 && orderModel.OrderStatus != 30 {
		var exception models.OrderException
		ok, err := db.Table("order_exception").Where("delivery_id = ? and is_show = 1", cast.ToString(record.DeliveryId)).Get(&exception)
		if err != nil {
			out.Message = "订单查询错误!"
			out.Error = err.Error()
			glog.Error("取消配送,查询错误：", params.OrderSn, err.Error())
			return &out, nil
		}
		if ok {
			out.Message = "请到配送异常里操作!"
			return &out, nil
		}
		if params.CancelReason == "" {
			params.CancelReason = "商家主动取消"
		}
		addException := 0
		//调用取消订单
		deliveryConfig := new(models.DeliveryConfig)
		//如果是SAAS并且是小程序订单的话，就要判断是不是麦芽田订单，是的话就要调用完成接口
		if orderModel.ChannelId == 1 && orderModel.OrgId == 6 {
			_, err = db.Where("finance_code = ?", orderModel.ShopId).
				Where("channel_id = ?", orderModel.ChannelId).
				Where("org_id = ?", orderModel.OrgId).
				Get(deliveryConfig)

			if err != nil {
				out.Message = "查询配送配置出错!"
				out.Error = err.Error()
				glog.Error("取消配送,查询配送配置出错：", params.OrderSn, err.Error())
				return &out, nil
			}

			if deliveryConfig.ID == 0 {
			}

			//如果是平台配送或者是第三方配送，我们不需要发配送直接返回成功
			if deliveryConfig.ThirdType == 1 && deliveryConfig.DeliveryMethod == 2 {
				addException = 1
			}
		}

		//取消成功后不直接加入异常配送，等回调
		err = CancelDeliveryCommon(params.OrderSn, params.CancelReason, params.DeliveryId, addException, params.CancelReasonId)
		if err != nil {
			out.Message = err.Error()
			return &out, nil
		}
	}

	out.Code = 200
	return &out, nil
}

// 修改未发出配送为已发出
func (o OrderService) UPDateElmOrderPushDelivery(ctx context.Context, params *oc.UPDateElmOrderPushDeliveryRequest) (*oc.BaseResponse, error) {
	res := new(oc.BaseResponse)
	res.Code = 200
	res.Message = "Success"
	conn := GetDBConn()
	_, err := conn.Exec("update `order_detail` a inner join order_main b on a.order_sn=b.order_sn set push_delivery=1 where old_order_sn=? and order_status != 0", params.Oldordersn)
	if err != nil {
		res.Code = 400
		res.Message = err.Error()
		return res, err
	}
	return res, nil
}

//美团、饿了么、阿闻到家、京东到家 取消订单更新订单中心订单状态接口（注意：非阿闻后台手动取消调用）
/*
**1.没有退过款的，需要生成全额退款单
**2.通知第三方退款
**3.后台消息通知状态修改
**4.通知美团配送取消配送,并生成取消配送节点
**取消订单
 */
func (o OrderService) CancelOrder(ctx context.Context, params *oc.CancelOrderRequest) (*oc.BaseResponse, error) {
	glog.Info(params.OrderSn, "-取消订单：", "CancelOrder,", kit.JsonEncode(params))
	out := oc.BaseResponse{Code: 400}

	//连接池勿关闭
	redisConn := GetRedisConn()

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	orderModel := &models.Order{}

	sql := `SELECT 
			a.*,
			b.push_third_order,b.push_delivery,b.locked_stock
			FROM order_main a 
			INNER JOIN order_detail b ON a.order_sn=b.order_sn 
			WHERE a.old_order_sn=? ORDER BY a.id DESC`

	ok, err := o.session.SQL(sql, params.OrderSn).Get(orderModel)

	if err != nil {
		out.Message = "订单查询错误!"
		out.Error = err.Error()
		glog.Error("取消订单,查询错误：", params.OrderSn, err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "订单不存在！"
		glog.Warning("取消订单,订单不存在：", params.OrderSn)
		return &out, nil
	}

	if orderModel.OrderStatus == 0 {
		out.Message = "订单已取消！"
		glog.Warning("订单已取消！不可重复操作！", orderModel.OldOrderSn)
		return &out, nil
	}

	//并发操作锁
	lockCard := "lock:order_" + orderModel.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		out.Message = "订单不可操作"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	//传了状态的情况下比对状态
	if (params.OrderStatus != 0 && orderModel.OrderStatus != params.OrderStatus) ||
		(params.OrderStatusChild != 0 && orderModel.OrderStatusChild != params.OrderStatusChild) {
		out.Message = "订单状态不匹配！!!"
		glog.Warning(orderModel.OldOrderSn, "-订单状态不匹配！！", orderModel.OrderStatus, params.OrderStatus)
		return &out, nil
	}

	//阿闻订单需要支付后2分钟后才可取消
	if orderModel.IsPay == 1 && (orderModel.ChannelId == ChannelAwenId || orderModel.ChannelId == ChannelDigitalHealth) && orderModel.PayTime.After(time.Now().Add(-2*time.Minute)) {
		out.Message = "取消需要支付时间大于2分钟才能操作，请稍候"
		out.Error = out.Message
		return &out, nil
	}

	//如果不是自己调用的就需要去检查是否有未完成退款单
	if params.IsRefund != 1 {
		var refundOrderCheck []models.RefundOrder
		o.session.Where("old_order_sn=? AND refund_state in(1,5,6,7)", orderModel.OldOrderSn).Find(&refundOrderCheck)

		if len(refundOrderCheck) > 0 && params.IsRefund == 0 {
			out.Message = "请把所有退款处理完再发起退款！"
			glog.Warning(orderModel.OldOrderSn, "-请把所有退款处理完再发起退款！！")
			return &out, nil
		}
	}

	o.orderMain = orderModel.OrderMain
	//事务开启
	o.session.Begin()

	//更新主单的状态
	//阿闻更新的是子单 第三方更新的是主单
	//所以下面需要处理两个逻辑，第三方更新所有子单状态为取消 阿闻要看子单是否都取消 如果都取消要更新主单为取消
	if _, err = o.session.ID(orderModel.Id).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").
		Update(&models.OrderMain{
			OrderStatus:      0,
			OrderStatusChild: 20107,
			CancelReason:     params.CancelReason,
			CancelTime:       time.Now(),
		}); err != nil {
		o.session.Rollback()
		out.Message = "更新订单状态失败1！"
		out.Error = err.Error()
		glog.Error(orderModel.OldOrderSn, "-取消订单,更新订单状态失败1！", err.Error())
		return &out, nil
	}

	if params.IsAdjust > 0 {
		if _, err = o.session.ID(orderModel.OrderSn).Cols("is_adjust").Update(&models.OrderDetail{
			IsAdjust: params.IsAdjust,
		}); err != nil {
			o.session.Rollback()
			out.Message = "更新订单状态失败2！"
			out.Error = err.Error()
			glog.Error("取消订单,更新订单状态失败2！", orderModel.OldOrderSn, err.Error())
			return &out, nil
		}
	}

	//查询是否有未完成的全额退款单
	RefundOrder := models.RefundOrder{}
	hasRefundOrder, err := o.session.Where("old_order_sn = ? AND full_refund = 1 AND refund_state NOT IN(2,8,9)", orderModel.OldOrderSn).Get(&RefundOrder)
	if err != nil {
		glog.Error("退款订单查询错误！", orderModel.OldOrderSn, err.Error())
		out.Message = "退款订单查询错误!"
		out.Error = err.Error()
		return &out, nil
	}

	redisClient := GetRedisConn()
	//生成订单号
	RefundSn := params.Refundsn
	hasRefundSn := 1
	//来自于第三方的回调 params.Refundsn 会为空
	if len(params.Refundsn) == 0 {
		//此处存在一种可能 就是已经推送过了第三方 推送第三方是成功的
		//但是我们推送后的执行报错了 导致数据没有保存，此时重推机制过来时 创建了新的退款单 导致与推送到第三方的退款单对不上
		//解决方案 缓存全单退款的退款单号
		//目前我们推送后的执行报错主要有一种场景：第三方取消订单太快 而我们的主订单商品数据都还没有保存 导致插入退款单数据时查询主单数据出错
		//会在很快进行重试 所以缓存仅为10分钟，如有其他场景可以延长改缓存的时间

		//问题2 该退款单是第三方申请的的全额退款单 ，阿闻审核通过之后 ，第三方的回调先于我们本系统应答售后单调用 此时就会生成新的全额退款单号 并推送了第三方
		//但是却不能落单，而阿闻内部的调用却不能推送第三方成功  此时需要使用查询出来的单号 在没有
		if hasRefundOrder {
			RefundSn = RefundOrder.RefundSn
		} else {
			cacheRefundSn := redisClient.Get("order-center:cancel-order:refund-sn:" + o.orderMain.OrderSn).Val()
			if cacheRefundSn == "" {
				RefundSn = GetSn("refund")[0]
				//设置取消订单的退款单号 缓存时间为10分钟 因为目前这个问题主要的场景在第三方取消订单太快
				//而我们的主订单商品数据都还没有保存 导致插入退款单数据时查询主单数据出错 会在很快进行重试，如有其他场景可以延长改缓存的时间
				redisClient.Set("order-center:cancel-order:refund-sn:"+o.orderMain.OrderSn, RefundSn, 10*time.Minute)
			} else {
				// 如果该退款单因为其他退款单并发导致缓存的退款单号已存在 则还是重新生成
				ok, err = o.session.Where("refund_sn = ?", cacheRefundSn).Exist()
				if err != nil {
					glog.Error("查询退款单是否存在出错！", orderModel.OldOrderSn, err.Error())
				}
				if !ok { //不存在使用缓存
					RefundSn = cacheRefundSn
				} else {
					//存在重新生成
					RefundSn = GetSn("refund")[0]
				}
			}
		}
		hasRefundSn = 0
	}

	// 退款单是否推送到第三方标记
	var refundPushThird int32
	var refundPushThirdFailReason string

	if orderModel.IsVirtual == 0 {
		// 如果正向单推送到了第三方
		if orderModel.PushThirdOrder > 0 {
			//通知第三方退货
			//此处存在一种可能 就是已经推送过了第三方 推送第三方是失败的
			//但是我们推送后的执行报错了 导致数据没有保存，此时重推机制过来时 创建了新的退款单 导致与推送到第三方的退款单对不上
			//解决方案 RefundNotice方法如果推送第三方成功 则保存退款单号
			glog.Info(orderModel.OldOrderSn, ", ", " zx测试进入路径1")
			if err = o.RefundNotice(params.CancelReason, RefundSn, 0, hasRefundSn); err != nil {
				refundPushThirdFailReason = err.Error()
				glog.Error(orderModel.OldOrderSn, ", 取消订单,通知第三方系统失败！", err.Error())
			} else {
				refundPushThird = 1
			}
		} else if orderModel.LockedStock > 0 && orderModel.IsAdjust == 0 { // 没有推送到第三方取消的订单，正逆向订单都不推送，直接解冻库存
			if err = FreedStockByParentOrderSn(o.orderMain.GetParentOrderSn()); err != nil {
				glog.Error(o.orderMain.ParentOrderSn, "-订单取消释放库存失败", err.Error())
			}
			// 清除在途库存
			DeleteTransportationInventory(o.orderMain.GetParentOrderSn(), o.orderMain.ChannelId)
		}
	}

	if orderModel.IsPay == 1 {
		//没有找到退款单说明美团没有调用全额退款接口，需要自己生成退款单
		if !hasRefundOrder {
			//生成退款单
			err = o.SaveCancelRefundOrder(RefundSn, params.CancelReason, params.Operationer, 0, params.OldRefundSn, refundPushThird, refundPushThirdFailReason)
			if err != nil {
				o.session.Rollback()
				glog.Error(orderModel.OldOrderSn, "-生成退款单失败！", err.Error())
				out.Message = "生成退款单失败！"
				out.Error = err.Error()
				return &out, nil
			}
		}
	}

	//子订单列表
	orderList := make([]models.OrderMain, 0)
	var virtualChildOrderSnList []string

	//所有的子单都更新状态
	//查询父订单下的子订单状态是否全部取消
	//父订单 阿闻渠道传过来的是子订单 所以orderModel.ParentOrderSn即为父订单
	var parentOrderSn = orderModel.ParentOrderSn
	//第三方的订单传过来的是主订单 v6.0去掉了parentOrderSn  所以orderModel.OrderSn即为拆单后的父订单
	if orderModel.ChannelId == ChannelMtId || orderModel.ChannelId == ChannelJddjId || orderModel.ChannelId == ChannelElmId {
		parentOrderSn = orderModel.OrderSn
	}
	if parentOrderSn == "" {
		o.session.Rollback()
		glog.Error("zx取消订单参数错误20220725", kit.JsonEncode(orderModel), kit.JsonEncode(params))
		out.Message = "zx取消订单参数错误20220725!"
		out.Error = "zx取消订单参数错误20220725!"
		return &out, nil
	}
	err = o.session.Where("parent_order_sn = ?", parentOrderSn).Find(&orderList)
	if err != nil {
		o.session.Rollback()
		glog.Error(orderModel.OldOrderSn, "-退款订单查询主订单失败！", err.Error())
		out.Message = "退款订单查询主订单错误!"
		out.Error = err.Error()
		return &out, nil
	}
	if len(orderList) > 500000 {
		o.session.Rollback()
		glog.Error("zx取消订单1参数错误20220725", orderModel)
		out.Message = "zx取消订单1参数错误20220725!"
		out.Error = "zx取消订单参1数错误20220725!"
		return &out, nil
	}
	isCancel := true
	//阿闻渠道以为过来的是子单 循环所有父订单的子订单  看是否已经全部已取消
	//第三方顺便收集虚拟订单
	var thirdChildOrderSn []string
	for _, order := range orderList {
		//子订单是虚拟商品
		if order.IsVirtual == 1 {
			//第三方 将所有的子订单是虚拟商品的核销码退掉
			if isThirdChannel(orderModel.ChannelId) {
				virtualChildOrderSnList = append(virtualChildOrderSnList, order.OrderSn)
				thirdChildOrderSn = append(thirdChildOrderSn, order.OrderSn)
				//阿闻取消的是子订单 所有只有被取消的子订单才退掉核销码
			} else if order.OrderSn == orderModel.OrderSn {
				virtualChildOrderSnList = append(virtualChildOrderSnList, orderModel.OrderSn)
			}
		}
		if order.OrderStatus > 0 && order.OrderSn != orderModel.OrderSn {
			isCancel = false
		}
	}

	//此处判断 只有阿闻渠道会生效
	if isCancel || orderModel.AppChannel == SaasAppChannel {
		_, err = o.session.Exec("UPDATE order_main SET order_status=?,order_status_child = ?,cancel_reason = ?,cancel_time = ? WHERE order_sn=?", 0, 20107, params.CancelReason, time.Now(), orderModel.ParentOrderSn)
		if err != nil {
			o.session.Rollback()
			out.Message = "更新主订单状态失败！"
			out.Error = err.Error()
			glog.Error(orderModel.OldOrderSn, "-取消订单,更新主订单状态失败！", err.Error())
			return &out, nil
		}
	}

	//第三方订单 子单需要全部改为取消状态
	//所有的虚拟子订单子状态改为已取消
	if isThirdChannel(orderModel.ChannelId) {
		_, err = o.session.Exec("UPDATE order_main SET order_status=?,order_status_child = ?,cancel_reason = ?,cancel_time = ? WHERE parent_order_sn=?", 0, 20107, params.CancelReason, time.Now(), parentOrderSn)

		if err != nil {
			o.session.Rollback()
			out.Message = "更新子单状态失败！"
			out.Error = err.Error()
			glog.Error(orderModel.OldOrderSn, "-取消订单,第三方订单取消更新子单状态失败！", err.Error())
			return &out, nil
		}
		_, err = o.session.Exec("UPDATE order_main SET order_status_child = ? WHERE parent_order_sn=? AND is_virtual = 1", 30100, parentOrderSn)

		if err != nil {
			o.session.Rollback()
			out.Message = "更新虚拟子单子状态失败！"
			out.Error = err.Error()
			glog.Error(orderModel.OldOrderSn, "-取消订单,第三方订单更新虚拟子单子状态失败！", err.Error())
			return &out, nil
		}

		//更新所有退款的组合商品的子商品的退款数量
		var skuList []string
		err = o.session.SQL(`select b.sku_id FROM refund_order a JOIN refund_order_product b ON a.refund_sn = b.refund_sn WHERE a.order_sn = ?`, o.orderMain.OrderSn).Find(&skuList)
		if err != nil {
			glog.Error(o.orderMain.OrderSn, "-取消订单查询图款商品数据出错", err)
		}
		if len(skuList) > 0 {
			_, err = o.session.Exec("UPDATE order_product SET refund_num=number WHERE order_sn ='" + o.orderMain.OrderSn + "' AND parent_sku_id IN('" + strings.Join(skuList, ",") + "')")
			if err != nil {
				glog.Error(o.orderMain.OrderSn, "-取消订单更新组合商品子商品退款数量出错", err)
			}
		}
	}

	childOrderSn := o.orderMain.OrderSn
	if o.orderMain.ParentOrderSn == "" {
		o.session.Table("order_main").Select("order_sn").Where("parent_order_sn = ? and is_virtual = 0", o.orderMain.OrderSn).Get(&childOrderSn)
	}
	dr := models.OrderDeliveryRecord{}
	o.session.Where("order_sn = ?", childOrderSn).Desc("id").Get(&dr)

	if orderModel.IsVirtual == 0 {
		if orderModel.PushDelivery == 1 && orderModel.OrderStatus != 30 {
			CancelDeliveryCommon(orderModel.OrderSn, params.CancelReason, dr.DeliveryId, 0)
		}
		deliveryModel := models.OrderDeliveryNode{
			DeliveryId:     dr.DeliveryId,
			OrderSn:        o.orderMain.OrderSn,
			DeliveryStatus: 98,
			Content:        "商家取消",
			CancelReason:   params.CancelReason,
		}
		if _, err = o.session.Insert(&deliveryModel); err != nil {
			glog.Error(o.orderMain.OldOrderSn, "-取消订单更新配送信息接口，信息保存失败：", err.Error())
		}
		//删除在途库存
		if params.IsAdjust != 1 {
			go DeleteTransportationInventory(o.orderMain.OrderSn, orderModel.ChannelId)
		}
	}

	//如果子订单有虚拟商品 虚拟订单将核销码更新为已退款
	if len(virtualChildOrderSnList) > 0 {
		if _, err = o.session.Exec("UPDATE order_verify_code SET verify_status=2 WHERE verify_status=0 AND order_sn IN ('" + strings.Join(virtualChildOrderSnList, "','") + "')"); err != nil {
			o.session.Rollback()
			glog.Error(o.orderMain.OldOrderSn, "-退款更新核销码状态出错, ", err.Error())
			out.Message = "更新核销码退款状态失败"
			out.Error = err.Error()
			return &out, nil
		}
	}

	//更新商品退款数量
	_, err = o.session.Exec(`
		update order_product a 
		inner join refund_order_product b on a.id=b.order_product_id
		set refund_num=number
		where order_sn=?
	`, o.orderMain.OrderSn)
	if err != nil {
		o.session.Rollback()
		glog.Error(o.orderMain.OldOrderSn, "- 更新商品退款数量失败, ", err)
		out.Message = "更新商品退款数量失败"
		out.Error = err.Error()
		return &out, nil
	}

	if err = o.session.Commit(); err != nil {
		glog.Error(orderModel.OldOrderSn, "-美团用户申请取消订单commit，", err.Error())
	}

	//异步通知后台状态修改
	go MessageUpdate(o.orderMain.OrderSn)

	if o.orderMain.ChannelId != 5 {
		glog.Info("PushOrderStatusToTencent:" + o.orderMain.OrderSn + ":CancelOrder")
		go PushOrderStatusToTencent(o.orderMain.OrderSn, 0)
	}

	//查询退款记录中是否存在“退款中”的记录，有则更新为“退款成功”
	if o.orderMain.IsPay == 1 {
		_, err := o.UpdateRefundState(ctx, &oc.AccomplishOrderRequest{
			OrderSn: o.orderMain.OrderSn,
		})
		if err != nil {
			glog.Error(o.orderMain.OrderSn, "-修复退款订单状态出错：", err.Error())
		}
	}
	//事务提交后，再调用释放库存，避免查不到数据
	glog.Info("CancelOrder，订单取消释放库存，订单号：", o.orderMain.OrderSn, "，退款单号：", RefundSn, ",是否推单：", orderModel.PushThirdOrder)
	if orderModel.IsVirtual == 0 && params.IsAdjust != 1 && orderModel.AppChannel == SaasAppChannel {
		if orderModel.PushThirdOrder == 1 {
			go RefundStock(RefundSn, o.orderMain.OrderSn, orderModel.ChannelId)
		} else {
			go UnFreeze(o.orderMain.OrderSn, orderModel.ShopId)
		}
	}
	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// todo 旧的在线问诊已经不需要，代码测试过后删除
func (o OrderService) DiagnoseCancelOrder(ctx context.Context, params *oc.CancelOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("取消订单：", kit.JsonEncode(params))
	out := oc.BaseResponse{Code: 400}

	//连接池勿关闭
	redisConn := GetRedisConn()

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	o.orderMain = GetOrderMainByOrderSn(params.OrderSn)

	if o.orderMain.Id <= 0 {
		out.Message = "订单不存在！"
		glog.Warning("取消订单,订单不存在：", params.OrderSn)
		return &out, nil
	}

	if o.orderMain.OrderStatus == 0 {
		out.Message = "订单已取消！"
		glog.Warning("订单已取消！不可重复操作！", o.orderMain.OldOrderSn)
		return &out, nil
	}

	//并发操作锁
	lockCard := "lock:order_" + o.orderMain.OrderSn
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		out.Code = 200
		out.Message = "lockOk"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	//事务开启
	o.session.Begin()

	if _, err := o.session.ID(o.orderMain.Id).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
		OrderStatus:      0,
		OrderStatusChild: 20107,
		CancelReason:     params.CancelReason,
		CancelTime:       time.Now(),
	}); err != nil {
		o.session.Rollback()
		out.Message = "更新订单状态失败1！"
		out.Error = err.Error()
		glog.Error("美团取消订单,更新订单状态失败1！", o.orderMain.OldOrderSn, err.Error())
		return &out, nil
	}

	//生成订单号
	RefundSn := params.Refundsn
	if len(params.Refundsn) == 0 {
		RefundSn = GetSn("refund")[0]
	}

	if o.orderMain.IsPay == 1 {
		//生成退款单
		err := o.SaveCancelRefundOrder(RefundSn, params.CancelReason, params.Operationer, 0, params.OldRefundSn, 0, "")
		if err != nil {
			o.session.Rollback()
			glog.Error("生成退款单失败！", o.orderMain.OldOrderSn, err.Error())
			out.Message = "生成退款单失败！"
			out.Error = err.Error()
			return &out, nil
		}
	}

	if err := o.session.Commit(); err != nil {
		glog.Error("在线问诊订单用户申请取消订单commit，", o.orderMain.OldOrderSn, err.Error())
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

//TODO: 待删除
//美团用户在申请取消订单更新订单中心订单状态接口(注意时长，如果订单推送了子龙，则需要调用子龙退款接口。参见：取消订单推送API)
//取消订单
//func (o OrderService) Del_ElmCancelOrder(ctx context.Context, params *oc.CancelOrderRequest) (*oc.BaseResponse, error) {
//	glog.Info("取消订单（美团推送）：", kit.JsonEncode(params))
//	out := oc.BaseResponse{Code: 400}
//
//	o.session = GetDBConn().NewSession()
//	defer o.session.Close()
//
//	orderModel := &models.Order{}
//	ok, err := o.session.SQL("select a.*,b.push_third_order,b.push_delivery from order_main a inner join order_detail b on a.order_sn=b.order_sn where a.old_order_sn=? order by a.id desc").Get(orderModel)
//	if err != nil {
//		out.Message = "订单查询错误!"
//		out.Error = err.Error()
//		glog.Error("取消订单,查询错误：", params.OrderSn, err.Error())
//		return &out, nil
//	}
//	if !ok {
//		out.Message = "订单不存在！"
//		glog.Info("取消订单,订单不存在", params.OrderSn)
//		return &out, nil
//	}
//
//	if orderModel.OrderType == 0 {
//		out.Message = "订单已取消！"
//		glog.Info("订单已取消！不可重复操作！", params.OrderSn)
//		return &out, nil
//	}
//
//	o.session.Begin()
//
//	_, err = o.session.Where("old_order_sn = ?", params.OrderSn).Cols("order_status", "order_status_child", "cancel_reason", "cancel_time").Update(&models.OrderMain{
//		OrderStatus:      0,
//		OrderStatusChild: 20107,
//		CancelReason:     params.CancelReason,
//		CancelTime:       time.Now(),
//	})
//	if err != nil {
//		o.session.Rollback()
//		out.Message = "更新订单状态失败！"
//		out.Error = err.Error()
//		glog.Error("美团取消订单,更新订单状态失败！", params.OrderSn, err.Error())
//		return &out, nil
//	}
//
//	//生成订单号
//	RefundSn := params.Refundsn
//	if RefundSn == "" {
//		RefundSn = GetSn("refund")[0]
//	}
//
//	//没有退过款的才调用此接口
//	if params.IsRefund == 0 {
//		if orderModel.PushThirdOrder > 0 && orderModel.Source == 3 {
//			RefundGoodsOrders := o.GetRefundGoods(RefundSn, 0, 1)
//			if len(RefundGoodsOrders) == 0 {
//				o.session.Rollback()
//				glog.Error("取消订单，获取退款商品失败！", params.OrderSn)
//				out.Message = "取消订单，获取退款商品失败！"
//				out.Error = "取消订单，获取退款商品失败！"
//				return &out, nil
//			}
//			err = o.PushZiLongRefundOrderUf(RefundGoodsOrders, RefundSn, 0)
//			if err != nil {
//				glog.Error("美团取消订单,通知子龙系统失败！", params.OrderSn, err.Error())
//			}
//		} else if orderModel.PushThirdOrder > 0 && orderModel.Source == 1 {
//			//通知全渠道退货
//			refundOrder := models.RefundOrder{}
//			//查询日志
//			var isOk bool
//			isOk, err = o.session.SQL("select * from `refund_order` where refund_sn=? and refund_state in (1,5,6,7)", RefundSn).Get(&refundOrder)
//			if err != nil || !isOk {
//				if err != nil {
//					glog.Error("售后单还不存在就接到了其他的回调处理:" + RefundSn + err.Error())
//				}
//				o.session.Rollback()
//				out.Message = "退款单还不存在就接到了其他的回调处理"
//				out.Error = "退款单还不存在就接到了其他的回调处理"
//				return &out, nil
//			}
//			has := true
//			//1整单退款 2部分退款
//			if refundOrder.FullRefund == 2 {
//				has = false
//			}
//			err = o.AllChannelRefundNotice(RefundSn, has)
//			if err != nil {
//				glog.Error("美团取消订单,通知全渠道系统失败！", params.OrderSn, err.Error())
//			}
//		}
//	}
//
//	DeliveryRecord := models.OrderDeliveryRecord{}
//	o.session.Where("order_id = ?", orderModel.OrderSn).OrderBy("create_time desc").Get(&DeliveryRecord)
//
//	if orderModel.PushDelivery == 1 {
//		//通知美团配送取消配送
//		etClient := et.GetExternalClient()
//
//		inpar := et.MpOrderDeleteRequest{}
//		inpar.DeliveryId = DeliveryRecord.DeliveryId
//		inpar.MtPeisongId = DeliveryRecord.MtPeisongId
//		inpar.CancelReasonId = "399"
//		inpar.CancelReason = params.CancelReason
//		glog.Info("美团用户申请取消订单,通知美团配送请求：", params.OrderSn, kit.JsonEncode(inpar))
//		res, err := etClient.MPServer.MpOrderDelete(etClient.Ctx, &inpar)
//		if err != nil {
//			glog.Error("美团用户申请取消订单调用错误", params.OrderSn, err.Error())
//		}
//		glog.Info("美团用户申请取消订单,通知美团配送返回：", params.OrderSn, kit.JsonEncode(res))
//		if res.Code != 200 {
//			glog.Error("美团用户申请取消订单调用错误 ", params.OrderSn)
//		}
//	}
//
//	deliveryModel := models.OrderDeliveryNode{
//		DeliveryId:   DeliveryRecord.DeliveryId,
//		OrderSn:      orderModel.OrderSn,
//		Status:       99,
//		Content:      "用户或商家取消订单",
//		CancelReason: params.CancelReason,
//		CreateTime:   time.Now(),
//	}
//	_, err = o.session.Insert(&deliveryModel)
//	if err != nil {
//		glog.Error("取消订单更新配送信息接口，信息保存失败:"+params.OrderSn, err.Error())
//	}
//
//	err = o.session.Commit()
//	if err != nil {
//		glog.Error("美团用户申请取消订单commit", params.OrderSn, err.Error())
//	}
//
//	//数据中心，后台通知状态修改
//	go MessageUpdate(orderModel.OrderSn)
//
//	out.Code = 200
//	out.Message = "ok"
//	return &out, nil
//}

// 催单
func (o OrderService) ReminderOrder(ctx context.Context, params *oc.ReminderOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("催单（美团推送）：", kit.JsonEncode(params))
	out := oc.BaseResponse{Code: 400}

	o.orderMain = GetOrderMainByOldOrderSn(params.OrderSn, "id")
	if o.orderMain.Id == 0 {
		out.Message = "该订单不存在！"
		return &out, nil
	}

	_, err := GetDBConn().ID(o.orderMain.OrderSn).Update(&models.OrderDetail{
		RemindTime: params.RemindTime,
	})
	if err != nil {
		out.Message = "更新订单失败！"
		out.Error = err.Error()
		glog.Error("催单，更新订单失败 ", params.OrderSn)
		return &out, nil
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 美团后台接单推送
// 美团与饿了么接单后会回调
// 第三方收到我方接单后的回调
func (o OrderService) MtAcceptOrder(ctx context.Context, params *oc.MtAcceptOrderRequest) (*oc.BaseResponse, error) {
	glog.Info("后台接单推送（MtAcceptOrder）：", kit.JsonEncode(params))
	out := oc.BaseResponse{Code: 400}

	if params.Status != "4" {
		out.Code = 200
		out.Message = "ok"
		return &out, nil
	}

	//连接池勿关闭
	redisConn := GetRedisConn()

	lockCard := "lock:order_" + params.OrderSn
	//上锁时 刚好正向订单还没有推送第三方 且正在获取锁时无法 获得导致推送第三方失败
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 1*time.Minute).Val()
	if !lockRes {
		glog.Error(params.OrderSn, ",MtAcceptOrder接单未获取订单锁")
		out.Code = 200
		out.Message = "ok"
		return &out, nil
	}
	defer redisConn.Del(lockCard)

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	o.orderMain = new(models.OrderMain)
	ok, err := o.session.Where("old_order_sn=? and order_status!=0", params.OrderSn).Get(o.orderMain)
	if err != nil {
		glog.Error(params.OrderSn, ",MtAcceptOrder订单查询错误")
		out.Message = "订单查询错误!"
		out.Error = err.Error()
		return &out, nil
	}
	if !ok {
		glog.Error(params.OrderSn, ",MtAcceptOrder订单不存在")
		out.Message = "订单不存在！"
		return &out, nil
	}

	if o.orderMain.OrderStatusChild != 20101 {
		glog.Error(params.OrderSn, ",MtAcceptOrder该订单不是未接单状态")
		out.Message = "该订单不是未接单状态！"
		return &out, nil
	}

	orderDetail := GetOrderDetailByOrderSn(o.orderMain.OrderSn)
	//未推送第三方的时候才推送 因为在回调回来之前可能已经在推送第三方了
	if orderDetail.PushThirdOrder == 0 {
		realOrder := CommonService{
			orderMain: new(models.OrderMain),
		}
		realOrder.session = GetDBConn().NewSession()
		defer realOrder.session.Close()

		_, err = realOrder.session.Select("/*FORCE_MASTER*/ *").Where("parent_order_sn=? and is_virtual=0", o.orderMain.OrderSn).Get(realOrder.orderMain)
		if err != nil {
			glog.Error(o.orderMain.OldOrderSn, ", 接单后查询实物子订单失败, ", err)
		} else {
			//实物子订单推送第三方
			realOrder.AcceptPushThird("商家后台")
		}
	}

	//异步通知后台状态修改
	go MessageUpdate(o.orderMain.OrderSn)

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 查询个渠道的配送费信息
func (o OrderService) SearchDelivery(ctx context.Context, params *oc.MtRePushThirdRequest) (*oc.SearchDeliveryPriceResponse, error) {
	glog.Info("查询配送费收到参数  ", kit.JsonEncode(params))

	out := &oc.SearchDeliveryPriceResponse{
		Code:    400,
		Message: "ok",
	}
	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	realOrder := new(models.Order)

	has, err := o.session.SQL("select a.*,b.push_third_order,b.expected_time,b.push_delivery from order_main a "+
		"inner join order_detail b on a.order_sn=b.order_sn where (a.parent_order_sn = ? or a.order_sn = ?) AND a.parent_order_sn != '' AND a.is_virtual=0", params.OrderSn, params.OrderSn).Get(realOrder)
	if err != nil {
		glog.Error(params.OrderSn, ", 查询个渠道的配送费信息查询实物子订单出错, ", err)
		out.Message = "查询个渠道的配送费信息：" + err.Error()
		return out, nil
	}
	if !has {
		glog.Error(params.OrderSn, ", 查询个渠道的配送费信息查询实物子订单失败,未找到订单信息")
		out.Message = "查询个渠道的配送费信息：未找到订单信息"
		return out, nil
	}
	o.orderMain = realOrder.OrderMain
	o.orderDetail = GetOrderDetailByOrderSn(o.orderMain.OrderSn)
	priceMap, err := o.SearchDeliveryPrice()
	if err != nil {
		glog.Error(params.OrderSn, ", 查询个渠道的配送费信息查询实物子订单失败,", err.Error())
		out.Message = "查询个渠道的配送费信息：" + err.Error()
		return out, nil
	}

	minDeliverType := 999999
	minPrice := float64(999999)
	//有成功的，找出价格最低的
	for _, k := range priceMap {

		//查询成功的配送渠道
		if k.IsOk {

			if k.DeliveryFee < minPrice {
				minPrice = k.DeliveryFee
				minDeliverType = k.DeliveryType
			}
		}

	}

	for k := range priceMap {
		item := oc.DeliveryPriceInfo{}
		item.DeliveryName = DeliveryTypeKeyMap[priceMap[k].DeliveryType]
		item.DeliveryType = cast.ToInt32(priceMap[k].DeliveryType)
		item.DeliveryPrice = "查询失败"
		item.IsOk = priceMap[k].IsOk
		if priceMap[k].IsOk {
			item.DeliveryPrice = cast.ToString(priceMap[k].DeliveryFee)
		}
		item.IsMinPrice = false
		if priceMap[k].DeliveryType == minDeliverType {
			item.IsMinPrice = true
		}

		out.Data = append(out.Data, &item)
	}
	out.Code = 200
	return out, nil
}

// 发配送
func (o OrderService) PushDelivery(ctx context.Context, params *oc.MtRePushThirdRequest) (*oc.BaseResponse, error) {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "ok",
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	realOrder := new(models.Order)

	has, err := o.session.SQL("select a.*,b.push_third_order,b.expected_time,b.push_delivery from order_main a "+
		"inner join order_detail b on a.order_sn=b.order_sn where (a.parent_order_sn = ? or a.order_sn = ?) AND a.parent_order_sn != '' AND a.is_virtual=0", params.OrderSn, params.OrderSn).Get(realOrder)
	if err != nil {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单出错, ", err)
		out.Message = "推送第三方失败：" + err.Error()
		return out, nil
	}
	if !has {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单失败,未找到订单信息")
		out.Message = "推送第三方失败：未找到订单信息"
		return out, nil
	}
	o.orderMain = realOrder.OrderMain
	o.orderDetail = GetOrderDetailByOrderSn(o.orderMain.OrderSn)

	o.PushMpOrder()
	out.Code = 200
	return out, nil
}

// 重新发起推送到第三方
func (o OrderService) MtRePushThird(ctx context.Context, params *oc.MtRePushThirdRequest) (*oc.BaseResponse, error) {
	glog.Info("重新发起推送到第三方收到参数  ", kit.JsonEncode(params))

	out := &oc.BaseResponse{
		Code:    400,
		Message: "ok",
	}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	realOrder := new(models.Order)

	has, err := o.session.SQL("select a.*,b.push_third_order,b.expected_time,b.push_delivery from order_main a "+
		"inner join order_detail b on a.order_sn=b.order_sn where (a.parent_order_sn = ? or a.order_sn = ?) AND a.parent_order_sn != '' AND a.is_virtual=0", params.OrderSn, params.OrderSn).Get(realOrder)
	if err != nil {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单出错, ", err)
		out.Message = "推送第三方失败：" + err.Error()
		return out, nil
	}
	if !has {
		glog.Error(params.OrderSn, ", 重新推送第三方查询实物子订单失败,未找到订单信息")
		out.Message = "推送第三方失败：未找到订单信息"
		return out, nil
	}
	o.orderMain = realOrder.OrderMain
	o.orderDetail = GetOrderDetailByOrderSn(o.orderMain.OrderSn)

	// 订单取消后不允许重新发单
	if realOrder.OrderStatus == 0 {
		out.Code = 200
		out.Message = "订单取消状态不允许重新发单"
		return out, nil
	}

	if o.orderDetail.PushThirdOrder == 1 {
		out.Code = 200
		out.Message = "订单已推送"
		return out, nil
	}

	err = o.PushThirdOrder(true)
	//推送第三方失败的时候  如果提示的是已完成
	if err != nil {
		out.Message = "推送第三方失败：" + err.Error()

		_, err = o.session.In("order_sn", []string{realOrder.ParentOrderSn, realOrder.OrderSn}).
			Cols("push_third_order,push_third_order_reason").Update(&models.OrderDetail{
			PushThirdOrderReason: err.Error(),
			PushThirdOrder:       0,
		})
		if err != nil {
			glog.Error(realOrder.ParentOrderSn, ", ", realOrder.OrderSn, ", 更新订单推送第三方失败原因错误, ", err)
		}

		return out, nil
	}

	if realOrder.Source == 3 && !realOrder.ConfirmTime.IsZero() {
		if err = o.FinalizeOrder(&oc.AccomplishOrderRequest{
			OrderSn:     realOrder.OrderSn,
			ConfirmTime: kit.GetTimeNow(),
		}); err != nil {
			glog.Error(realOrder.OrderSn, ", 推送子龙失败, ", err)
		}
	}

	//非自提且未发单且未超过配送时间的自配送订单 发配送
	if realOrder.DeliveryType != 3 && realOrder.OrderStatus == 20 && (o.IsSelfDistribution() || realOrder.ChannelId == ChannelAwenId) &&
		realOrder.PushDelivery == 0 &&
		realOrder.DeliverTime.Unix() >= time.Now().Unix() {
		o.PushMpOrder()
	}

	var orderDetailUp = &models.OrderDetail{
		PushThirdOrder: 1,
	}
	// 自提订单重新发单后自动变为待取货状态
	if (realOrder.ChannelId == ChannelAwenId || realOrder.ChannelId == ChannelDigitalHealth) && realOrder.DeliveryType == 3 {
		orderDetailUp.IsPicking = 1
		orderDetailUp.PickingTime = time.Now()
	}

	_, err = o.session.In("order_sn", []string{realOrder.ParentOrderSn, realOrder.OrderSn}).Update(orderDetailUp)
	if err != nil {
		glog.Error(realOrder.ParentOrderSn, ", ", realOrder.OrderSn, ", 更新订单推送第三方状态失败, ", err)
	} else {
		glog.Info(realOrder.ParentOrderSn, ", ", realOrder.OrderSn, ", 重新发单成功")
		go DeleteTransportationInventory(o.orderMain.ParentOrderSn, o.orderMain.ChannelId, true)
	}

	out.Code = 200
	return out, nil
}

// 售后申请单列表
func (o OrderService) ApplyOrderList(ctx context.Context, in *oc.ApplyOrderListRequest) (*oc.AwenOrderListResponse, error) {
	out := &oc.AwenOrderListResponse{Code: 200, Message: "ok"}

	//只允许查询自己的订单
	if len(in.MemberId) == 0 {
		out.Code = 400
		out.Message = "会员id不能为空"
		return out, nil
	}

	dbConn := GetDBConn()

	//订单表，商品表，品牌表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("inner", "order_product", "order_main.order_sn=order_product.order_sn").
		Join("left", "refund_order", "order_main.order_sn=refund_order.order_sn").
		Where("member_id=?", in.MemberId).
		//排除取消订单（整单退款的订单也会被置为取消状态，要筛选出来）
		And("order_main.order_status > 0 or (order_main.order_status = 0 and refund_order.full_refund=1)").
		//订单状态排除未付款
		And("order_main.order_status != 10").
		And("order_main.parent_order_sn != ''").
		GroupBy("order_main.id").Desc("order_main.id")

	//订单搜索
	if len(in.Keywords) > 0 {
		session.And("order_main.parent_order_sn = ? or order_main.order_sn = ? ", in.Keywords, in.Keywords)
	}

	//只返回指定时间内的订单
	if len(in.CreateTimeGt) > 0 {
		session.And("order_main.create_time >= ?", in.CreateTimeGt)
	}

	//订单来源
	//UserAgent=3的时候，兼容7，临时用
	if in.UserAgent == 3 || in.UserAgent == 1 || in.UserAgent == 2 {
		//todo 把渠道去掉
		//session.And("order_main.channel_id = ? and user_agent in(1,2,3,7)", in.ChannelId)
		session.And("user_agent in(1,2,3,7)")
	} else {
		session.And("user_agent = ?", in.UserAgent)
	}

	// 不展示商城订单
	session.And("order_main.channel_id != 5")

	countSession := session.Clone()
	defer countSession.Close()

	_, err := countSession.Select("count(*) totalcount").Get(&out.TotalCount)
	if err != nil {
		err = errors.New(kit.RunFuncName() + "数据库查询失败，" + err.Error())
		glog.Error(err)
		return nil, err
	}
	if out.TotalCount == 0 {
		return out, nil
	}

	if err = session.Select("order_main.*").
		Limit(int(in.PageIndex), int(in.PageIndex*in.PageSize-in.PageSize)).
		Find(&out.Details); err != nil {
		err = errors.New(kit.RunFuncName() + "数据库查询失败，" + err.Error())
		glog.Error(err)
		return nil, err
	}

	orderSnSlice := make([]string, len(out.Details))
	for k, item := range out.Details {
		orderSnSlice[k] = item.OrderSn
	}

	//2:查询商品从表，补全订单的商品信息，1个订单可能下面对应多个商品
	var orderProducts []*models.OrderProduct
	dbConn.In("order_sn", orderSnSlice).Find(&orderProducts)

	orderPromotionMap := map[string]models.OrderPromotion{}
	func() {
		var orderPromotions []models.OrderPromotion
		dbConn.Where("promotion_type=3").In("order_sn", orderSnSlice).Find(&orderPromotions)
		for _, v := range orderPromotions {
			orderPromotionMap[v.OrderSn] = v
		}
	}()

	for _, order := range out.Details {
		//运费减去满减运费优惠
		order.Freight -= orderPromotionMap[order.OrderSn].PromotionFee

		var productmodels []*oc.OrderProductModel
		//查找订单的商品
		for _, dbproduct := range orderProducts {
			if order.OrderSn != dbproduct.OrderSn {
				continue
			}

			productmodel := oc.OrderProductModel{
				Id:           cast.ToString(dbproduct.Id),
				OrderSn:      dbproduct.OrderSn,
				Sku:          dbproduct.SkuId,
				ParentSkuId:  dbproduct.ParentSkuId,
				ProductId:    dbproduct.ProductId,
				ProductName:  dbproduct.ProductName,
				BarCode:      dbproduct.BarCode,
				Price:        dbproduct.DiscountPrice,
				PayPrice:     dbproduct.PayPrice,
				MarkingPrice: dbproduct.MarkingPrice,
				Number:       dbproduct.Number,
				Specs:        dbproduct.Specs,
				PaymentTotal: dbproduct.PaymentTotal,
				Privilege:    dbproduct.Privilege,
				Image:        dbproduct.Image,
				DeliverNum:   dbproduct.DeliverNum,
				RefundNum:    dbproduct.RefundNum,
			}
			if order.IsVirtual > 0 {
				overdueAndVerifiedCodes := GetValidOrderVerifyCodes(dbproduct.OrderSn, 4)
				verified := len(overdueAndVerifiedCodes)

				productmodel.UsedNum = int32(verified)
			}
			productmodels = append(productmodels, &productmodel)
		}
		order.Orderproductmodel = productmodels
	}
	return out, nil
}

// 获取订单信息请求
func (o OrderService) GetOneOrder(ctx context.Context, in *oc.GetOneOrderRequest) (*oc.GetOneOrderResponse, error) {
	out := oc.GetOneOrderResponse{Code: 200}

	order := GetOrderByOrderSn(in.OrderSn, "*")
	if order.Id == 0 {
		out.Code = 400
		out.Message = "订单不存在"
		return &out, nil
	}

	out.Order = &oc.OneOrderInfo{
		OrderId:              cast.ToString(order.Id),
		OldOrderSn:           order.OldOrderSn,
		OrderSn:              order.OrderSn,
		OrderStatus:          order.OrderStatus,
		OrderStatusChild:     order.OrderStatusChild,
		ShopId:               order.ShopId,
		ShopName:             order.ShopName,
		MemberId:             order.MemberId,
		MemberName:           order.MemberName,
		MemberTel:            order.MemberTel,
		ReceiverName:         order.ReceiverName,
		ReceiverState:        order.ReceiverState,
		ReceiverCity:         order.ReceiverCity,
		ReceiverDistrict:     order.ReceiverDistrict,
		ReceiverAddress:      order.ReceiverAddress,
		ReceiverPhone:        order.ReceiverPhone,
		Privilege:            order.Privilege,
		PayType:              order.PayType,
		ReceiverMobile:       order.ReceiverMobile,
		Total:                order.Total,
		GoodsTotal:           order.GoodsTotal,
		IsPay:                order.IsPay,
		CreateTime:           kit.GetTimeNow(order.CreateTime),
		ConfirmTime:          kit.GetTimeNow(order.ConfirmTime),
		AcceptTime:           kit.GetTimeNow(order.AcceptTime),
		IsPicking:            order.IsPicking,
		PickingTime:          kit.GetTimeNow(order.PickingTime),
		DeliverTime:          kit.GetTimeNow(order.DeliverTime),
		PayTime:              kit.GetTimeNow(order.PayTime),
		PaySn:                order.PaySn,
		OrderType:            order.OrderType,
		Freight:              order.Freight,
		Source:               order.Source,
		Invoice:              order.Invoice,
		WarehouseCode:        order.WarehouseCode,
		BuyerMemo:            order.BuyerMemo,
		SellerMemo:           order.SellerMemo,
		GyDeliverStatus:      order.GyDeliverStatus,
		GyOrderSn:            order.OldOrderSn,
		DeliveryType:         order.DeliveryType,
		DeliveryRemark:       order.DeliveryRemark,
		PushDelivery:         order.PushDelivery,
		PushThirdOrder:       order.PushThirdOrder,
		PushDeliveryReason:   order.PushDeliveryReason,
		PushThirdOrderReason: order.PushThirdOrderReason,
		Extras:               order.Extras,
		PackingCost:          order.PackingCost,
		PickupCode:           order.PickupCode,
		ServiceCharge:        order.ServiceCharge,
		CancelReason:         order.CancelReason,
		CancelTime:           kit.GetTimeNow(order.CancelTime),
		RemindTime:           order.RemindTime,
		Latitude:             order.Latitude,
		Longitude:            order.Longitude,
		TotalWeight:          order.TotalWeight,
		LogisticsCode:        order.LogisticsCode,
		LockedStock:          order.LockedStock,
		ChannelId:            order.ChannelId,
		UserAgent:            order.UserAgent,
		OrderPayType:         order.OrderPayType,
	}

	return &out, nil
}

// 获取订单商品信息请求
func (o *OrderService) GetOrderProducts(ctx context.Context, in *oc.GetOneOrderRequest) (*oc.GetOrderProductResponse, error) {
	out := oc.GetOrderProductResponse{Code: 200}

	orderProduct := GetOrderProductByOrderSn(in.OrderSn, "*")
	if len(orderProduct) == 0 {
		out.Message = "商品不存在"
		return &out, nil
	}

	var products []*oc.OrderProducts

	for _, v := range orderProduct {
		product := oc.OrderProducts{
			Id:             v.Id,
			OrderSn:        v.OrderSn,
			SkuId:          v.SkuId,
			ParentSkuId:    v.ParentSkuId,
			ThirdSkuId:     v.ThirdSkuId,
			ProductId:      v.ProductId,
			ProductName:    v.ProductName,
			ProductType:    v.ProductType,
			CombineType:    v.CombineType,
			Image:          v.Image,
			BarCode:        v.BarCode,
			MarkingPrice:   int64(v.MarkingPrice),
			DiscountPrice:  int64(v.DiscountPrice),
			PayPrice:       int64(v.PayPrice),
			Number:         int64(v.Number),
			Specs:          v.Specs,
			PaymentTotal:   int64(v.PaymentTotal),
			SkuPayTotal:    int64(v.SkuPayTotal),
			Privilege:      int64(v.Privilege),
			PrivilegePt:    int64(v.PrivilegePt),
			PrivilegeTotal: int64(v.PrivilegeTotal),
			Freight:        int64(v.Freight),
			DeliverStatus:  v.DeliverStatus,
			DeliverNum:     int64(v.DeliverNum),
			RefundNum:      int64(v.RefundNum),
		}

		products = append(products, &product)
	}

	out.OrderProducts = products
	return &out, nil
}

// 订单支付通知
func (o OrderService) OrderPayNotify(ctx context.Context, in *oc.OrderPayNotifyRequest) (*oc.BaseResponse, error) {
	glog.Info("收到支付通知：", in.OrderSn, " ", kit.JsonEncode(in))
	out := oc.BaseResponse{Code: 400}

	//连接池勿关闭
	db := GetDBConn()

	payTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, in.PayTime, time.Local)
	model := &models.OrderPayNotify{
		OrderSn:    in.OrderSn,
		PaySn:      in.PaySn,
		PayMode:    in.PayMode,
		PayTime:    payTime,
		PayAmount:  in.PayAmount,
		CreateTime: time.Now(),
	}
	ok, err := db.Insert(model)
	if err != nil {
		out.Message = "支付通知入库失败," + err.Error()
		return &out, nil
	}
	if ok <= 0 {
		out.Message = "支付通知入库失败"
		return &out, nil
	}

	//马上处理一次
	go DealOrderPayNotify(model)

	out.Code = 200
	return &out, nil
}

func (o OrderService) GetOrderPayInfo(ctx context.Context, in *oc.GetOneOrderRequest) (*oc.OrderPayInfoResponse, error) {
	out := new(oc.OrderPayInfoResponse)

	if in.OrderType == 4 {
		pinOrder := GetPinOrderGroupByPinOrderSn(in.OrderSn)
		out.PayPrice = int32(pinOrder.PayPrice)
		out.Privilege = int32(pinOrder.TotalPrice - pinOrder.PayPrice)
		out.OrderPayType = pinOrder.OrderPayType
		out.NotifyUrl = utils.GroupNotifyUrl
		// 商户号切换 虚拟商品拼团用新的微信商户号（利率：0.35%），实物商品拼图 用旧的微信商户号（利率：0.95%），
		if pinOrder.IsVirtual > 0 {
			out.MerchantId = utils.MerchantId
		} else {
			// 实物订单用老商户号
			out.MerchantId = "O-" + utils.MerchantId
		}

	} else {
		order := GetOrderByOrderSn(in.OrderSn, "*")
		if order.Id == 0 {
			return out, errors.New("找不到订单")
		}
		out.PayPrice = order.Total
		out.Privilege = order.Privilege
		out.OrderPayType = order.OrderPayType
		out.NotifyUrl = utils.NotifyUrl
		// 商户号切换   到店自提用新的微信商户号（利率：0.35%）,其他的 用旧的微信商户号（利率：0.95%），
		if order.DeliveryType == 3 { //到店自提 用新的微信商户号
			out.MerchantId = utils.MerchantId
		} else {
			out.MerchantId = "O-" + utils.MerchantId
		}

	}
	out.SubAppId = utils.SubAppId
	out.SecretKey = utils.StandardPaySecretKey

	return out, nil
}

// IntelligenceOrderSales 智慧中心前置仓订单销售统计数据的接口
func (o *OrderService) IntelligenceOrderSales(ctx context.Context, in *oc.GetIntelligenceOrderRequest) (*oc.IntelligenceOrderResponse, error) {
	var out = oc.IntelligenceOrderResponse{Code: 200}

	if len(in.FinanceCode) <= 0 {
		out.Code = 400
		out.Msg = "参数不能为空"
		return &out, nil
	}

	db := GetDBConn()

	year := time.Now().Year()
	month := time.Now().Month()
	var monthInt int
	if int(month) < 3 {
		//year = year - 1
		//monthInt = 12
	} else {
		//monthInt = int(month)
	}
	monthInt = int(month)
	sDate := cast.ToString(year) + "-1-1 0:0:0"
	eDate := fmt.Sprintf("%d-%d-31 23:59:59", cast.ToInt(year), monthInt)

	switch in.SaleType {
	case 1: //商品销售排行
		if result, err := db.QueryString(fmt.Sprintf("SELECT a.product_name as `name`, a.payment_total as `value` FROM (SELECT op.product_name, ROUND(sum(op.payment_total) / 100, 2) as payment_total FROM `order_product` op LEFT JOIN `order_main` o ON op.order_sn = o.order_sn WHERE o.order_status >=20 AND o.create_time >= ? AND o.create_time <= ? AND op.payment_total > 0 AND o.shop_id in('%s') group by op.product_id) a ORDER BY a.payment_total desc LIMIT 10", strings.Join(in.FinanceCode, "','")), sDate, eDate); err != nil {
			out.Code = 400
			out.Msg = err.Error()
			return &out, nil
		} else {
			var datas = []*oc.IntelligenceOrder{}
			for _, v := range result {
				datas = append(datas, &oc.IntelligenceOrder{Name: v["name"], Value: v["value"]})
			}
			out.Data = datas
		}
	case 2: //商品分类销售排行
		if result, err := db.QueryString(fmt.Sprintf("SELECT ROUND(sum(op.payment_total) / 100, 2) as `value` FROM `order_product` op LEFT JOIN `order_main` o ON op.order_sn = o.order_sn LEFT JOIN `order_ext` as oe ON oe.order_id = o.id WHERE o.order_status >= 20 AND o.create_time >= ? AND  o.create_time <= ? AND op.payment_total > 0 AND oe.category_id in(%s) AND o.shop_id in('%s') AND op.product_id = oe.product_id", in.Search, strings.Join(in.FinanceCode, "','")), sDate, eDate); err != nil {
			out.Code = 400
			out.Msg = err.Error()
			return &out, nil
		} else {
			var datas = []*oc.IntelligenceOrder{}
			for _, v := range result {
				datas = append(datas, &oc.IntelligenceOrder{Value: v["value"]})
			}
			out.Data = datas
		}
	case 3: //月份汇总
		if result, err := db.QueryString(fmt.Sprintf("SELECT a.`month` as `name`, a.payment_total as `value`, a.num FROM (SELECT cast(date_format(o.create_time,'%%c') as SIGNED) as `month`, ROUND(sum(op.payment_total) / 100, 2) as payment_total, count(payment_total) as num FROM `order_product` op LEFT JOIN `order_main` o ON op.order_sn = o.order_sn WHERE o.order_status >=20 AND o.create_time >= ? AND o.create_time <= ? AND op.payment_total > 0 AND o.shop_id in('%s') group by `month`) a ORDER BY `month` ASC LIMIT 12", strings.Join(in.FinanceCode, "','")), sDate, eDate); err != nil {
			out.Code = 400
			out.Msg = err.Error()
			return &out, nil
		} else {
			var datas = []*oc.IntelligenceOrder{}
			for _, v := range result {
				datas = append(datas, &oc.IntelligenceOrder{Name: v["name"], Value: v["value"], Num: v["num"]})
			}
			out.Data = datas
		}
	case 4: //门店营收
		if result, err := db.QueryString(fmt.Sprintf("SELECT ROUND(sum(op.payment_total) / 100, 2) as `value` FROM `order_product` op LEFT JOIN `order_main` o ON op.order_sn = o.order_sn WHERE o.shop_id in('%s') AND o.order_status >=20 AND o.create_time >= ? AND  o.create_time <= ? AND op.payment_total > 0", strings.Join(in.FinanceCode, "','")), sDate, eDate); err != nil {
			out.Code = 400
			out.Msg = err.Error()
			return &out, nil
		} else {
			var datas = []*oc.IntelligenceOrder{}
			for _, v := range result {
				var value string
				if v["value"] == "" {
					value = "0"
				} else {
					value = v["value"]
				}
				datas = append(datas, &oc.IntelligenceOrder{Value: value})
			}
			out.Data = datas
		}
	default: //渠道营收
		if result, err := db.QueryString(fmt.Sprintf("SELECT a.channel_id as `name`, a.payment_total as `value` FROM (SELECT o.channel_id, ROUND(sum(op.payment_total) / 100, 2) as payment_total FROM `order_product` op LEFT JOIN `order_main` o ON op.order_sn = o.order_sn WHERE o.order_status >=20 AND o.create_time >= ? AND  o.create_time <= ? AND op.payment_total > 0 AND o.shop_id in('%s') group by o.channel_id) a ORDER BY a.payment_total desc LIMIT 50", strings.Join(in.FinanceCode, "','")), sDate, eDate); err != nil {
			out.Code = 400
			out.Msg = err.Error()
			return &out, nil
		} else {
			var datas = []*oc.IntelligenceOrder{}
			for _, v := range result {
				var name string //1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
				if v["name"] == "1" {
					name = "阿闻到家"
				} else if v["name"] == "2" {
					name = "美团"
				} else if v["name"] == "3" {
					name = "饿了么"
				} else if v["name"] == "4" {
					name = "京东到家"
				} else if v["name"] == "5" {
					name = "阿闻电商"
				} else {
					//暂时过滤
					name = "门店"
					continue
				}
				datas = append(datas, &oc.IntelligenceOrder{Name: name, Value: v["value"]})
			}
			out.Data = datas
		}
	}

	return &out, nil
}

// 判断第三方订单是否存在
func (o OrderService) OrderIsExist(ctx context.Context, in *oc.OrderIsExistRequest) (*oc.OrderIsExistResponse, error) {
	var out = oc.OrderIsExistResponse{
		Code:    200,
		Message: "",
	}

	orderModel := models.OrderMain{}
	ok, err := GetDBConn().Where("old_order_sn = ?", in.OrderId).Or("order_sn = ?", in.OrderId).Exist(&orderModel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = "执行错误"
	}

	out.IsExist = ok
	return &out, err
}

// 京东调整订单取消原订单
func (o OrderService) JddjAdjustCancelOrder(ctx context.Context, request *oc.JddjAdjustOrderRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 200, Message: "ok"}
	glog.Info("京东订单调整：订单ID:" + request.OrderId)

	orderModel := &models.Order{}
	if len(request.OrderId) > 0 {
		orderModel = GetOrderById(cast.ToInt64(request.OrderId), "order_main.*,is_adjust")
		if orderModel.Id == 0 {
			glog.Errorf("京东订单调整查询订单失败！ order_id:%s ", request.OrderId)
			out.Code = 400
			out.Message = "订单不存在"
			return &out, nil
		}
	} else {
		if ok, err := GetDBConn().SQL("select order_main.*,is_adjust from order_main inner join order_detail on order_main.order_sn=order_detail.order_sn where old_order_sn = ? and order_status_child = 20102", request.OldOrderSn).OrderBy("id desc").Get(orderModel); err != nil {
			glog.Errorf("京东订单调整查询订单失败！ old_order_sn:%s ", request.OrderId)
			out.Code = 400
			out.Message = "查询订单失败"
			return &out, nil
		} else if !ok {
			glog.Errorf("京东订单调整查询订单失败！ old_order_sn:%s ", request.OrderId)
			out.Code = 400
			out.Message = "订单不存在"
			return &out, nil
		}
	}

	if orderModel.IsAdjust == 1 {
		return &out, nil
	}

	reqOrder := oc.CancelOrderRequest{
		OrderSn:      orderModel.OldOrderSn,
		CancelReason: "订单调整",
		Operationer:  request.UserName,
		IsAdjust:     1,
	}
	resp, err := o.CancelOrder(context.Background(), &reqOrder)
	if err != nil {
		out.Code = 400
		out.Message = "取消订单失败"
		glog.Error("京东订单调整查询订单失败，调用取消订单失败，", err, "，", orderModel.OldOrderSn)
	}

	if resp.Code != 200 {
		out.Code = 400
		out.Message = "取消订单失败"
	}
	return &out, nil
}

// 保存第三方回调原数据
func (o OrderService) SaveOrderOriginData(ctx context.Context, request *oc.SaveOrderOriginDataRequest) (*oc.BaseResponse, error) {
	db := GetDBConn()
	out := oc.BaseResponse{Code: 200, Message: ""}
	orderModel := &models.OrderOriginData{}
	orderModel.OldOrderSn = request.OldOrderSn
	orderModel.ChannelId = request.ChannelId
	orderModel.BodyJson = request.BodyJson
	orderModel.CreateTime = time.Now().Local()
	_, err := db.Insert(orderModel)
	if err != nil {
		glog.Error("保存第三方回调原数据写入数据失败！" + err.Error())
		out.Code = 400
		out.Message = "保存失败"
		out.Error = "保存失败"
		return &out, err
	}
	return &out, nil
}

func (o OrderService) GetHealthOrder(ctx context.Context, req *oc.GetHealthOrderReq) (*oc.GetHealthOrderResponse, error) {
	out := oc.GetHealthOrderResponse{Code: 200}

	//连接池勿关闭
	db := GetDBConn()
	model := oc.HealthOrderInfo{}
	strSql := "SELECT  a.order_sn,user_id AS scrm_user_id,category_code,a.order_type,b.pet_id AS scrm_pet_id,b.ensure_code FROM  `order_main` a JOIN  order_meal b ON a.order_sn=b.`order_sn`  WHERE a.order_type=6 AND a.order_sn=?"
	ok, err := db.SQL(strSql, req.OrderSn).Get(&model)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return &out, nil
	}
	if !ok {
		out.Message = "订单不存在"
		return &out, nil
	}
	out.OrderDetail = &model
	return &out, nil
}

func (o OrderService) PushSplitResultToMall(ctx context.Context, req *oc.GetHealthOrderReq) (*oc.BaseResponse, error) {
	oldOrderSns := strings.Split(req.OrderSn, ",")
	for _, oldOrderSn := range oldOrderSns {
		if len(oldOrderSn) == 0 {
			continue
		}

		PushSplitResultToMall(GetOrderMainByOldOrderSn(oldOrderSn))
		glog.Info(oldOrderSn, ", 子订单手动推送电商成功")
	}

	return &oc.BaseResponse{
		Code: 200,
	}, nil
}

// 确认商品已送回
func (o OrderService) ConfirmGoodsReturn(ctx context.Context, request *oc.ConfirmGoodsReturnRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	glog.Info("确认商品送回：订单ID:" + request.OrderSn)

	db := GetDBConn()

	//配送记录
	var record models.OrderDeliveryRecord

	ok, err := db.Where("delivery_id = ?", request.DeliveryId).Get(&record)
	if err != nil {
		glog.Error("确认商品送回,查询错误：", request.DeliveryId, request.OrderSn)
		out.Message = "配送订单查询失败！"
		return &out, nil
	}
	var deliveryNode models.OrderDeliveryNode
	if ok, err = db.Where("delivery_id = ?", request.DeliveryId).
		Desc("create_time").
		Get(&deliveryNode); err != nil {
		glog.Errorf("确认商品送回，查询配送记录错误！ order_sn:%s delivery_id:%s", request.OrderSn, cast.ToString(request.DeliveryId))
		out.Message = "配送订单查询失败！"
		return &out, nil
	}

	if ok {
		if deliveryNode.DeliveryStatus == 99 {
			//请求的是父订单号，发配送的是子订单号
			_, err = db.In("order_sn", []string{request.OrderSn, record.OrderSn}).Cols("goods_return_delivery_id").Update(&models.OrderDetail{
				GoodsReturnDeliveryId: 0,
			})
			if err != nil {
				glog.Error(request.OrderSn, ", 更新订单送回, ", err.Error())
			}
			out.Code = 200
			out.Message = "已经送回"
			return &out, nil
		}
	}
	if !ok {
		out.Message = "配送订单不存在！"
		glog.Warning("确认商品送回,订单不存在：", request.OrderSn)
		return &out, nil
	}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	iss := et.IssOrderNoRequest{
		IssOrderNo: record.MtPeisongId,
	}

	res, err := etClient.ShanSong.ConfirmGoodsReturn(etClient.Ctx, &iss)
	if err != nil {
		glog.Error("推送确认商品送回错误！ ", request.OrderSn, err.Error())
		out.Message = "推送闪送取消配送单失败"
		return &out, nil
	}
	if res.Code != 200 {
		out.Message = res.Error
		return &out, nil
	}

	//请求的是父订单号，发配送的是子订单号
	_, err = db.In("order_sn", []string{request.OrderSn, record.OrderSn}).Cols("goods_return_delivery_id").Update(&models.OrderDetail{
		GoodsReturnDeliveryId: 0,
	})
	if err != nil {
		glog.Error(request.OrderSn, ", 更新订单送回失败, ", err.Error())
		out.Message = "失败"
		return &out, nil
	}

	out.Code = 200
	return &out, nil
}

// 推送积分
func (o OrderService) PushIntegral(ctx context.Context, req *oc.PushIntegralRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 200}

	//连接池勿关闭
	orderMain := GetOrderMainByOldOrderSn(req.OrderSn)
	if orderMain != nil {
		if orderMain.ChannelId == ChannelMallId {

			//虚拟订单不做积分操作
			if orderMain.IsVirtual == 1 {
				return &out, nil
			}

			var integral oc.IntegralNotify
			if req.PayType == 0 {
				integral.Integraltype = 61
			} else {
				integral.Integraltype = 62
				integral.MemberId = ""
				integral.RefundAmount = req.PayPrice
			}
			integral.Oldordersn = orderMain.OrderSn
			integral.Notifytime = kit.GetTimeNow()
			integral.Mobile = utils.MobileDecrypt(orderMain.EnMemberTel)
			integral.Orderfrom = orderMain.ChannelId

			integral.TotalAmount = req.PayPrice

			glog.Info("PushIntegral 推送积分", kit.JsonEncode(integral))
			integral.OrgId = orderMain.OrgId
			PublishIntegralChange(integral)
		}
	} else {
		out.Code = 400
		out.Message = "未找到该订单"
	}
	return &out, nil
}

// 强行重推OMS,有无库存的都可以处理
func (o OrderService) RePushOms(ctx context.Context, params *oc.MtAddOrderResponse) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 200}
	//主仓
	warehouseId := 1294
	if params.Code == 888 {
		//药品仓
		warehouseId = 1683
	}

	mainOrder := &models.OrderMain{}
	childOrder := &models.OrderMain{}
	item := &models.OrderMain{}
	db := GetDBConn()
	if _, err := db.Select("*").Where("old_order_sn=?", params.OrderSn).Get(item); err != nil {
		glog.Error(params.OrderSn, ", 重推OMS查询订单信息失败, ", err)
		out.Code = 400
		out.Error = err.Error()
	}
	paySn := ""
	//如果查询出来的是主单号，推送OMS需要推送的是子单号，所以要出来子单号
	if item.ParentOrderSn == "" {
		mainOrder = item
		if _, err := db.Select("*").Where("parent_order_sn=?", mainOrder.OrderSn).Get(childOrder); err != nil {
			glog.Error(params.OrderSn, ", 重推OMS查询订单信息失败, ", err)
			out.Code = 400
			out.Error = err.Error()
		}
	} else {
		childOrder = item
		if _, err := db.Select("*").Where("order_sn=?", childOrder.ParentOrderSn).Get(mainOrder); err != nil {
			glog.Error(params.OrderSn, ", 重推OMS查询订单信息失败, ", err)
			out.Code = 400
			out.Error = err.Error()
		}
	}
	paySn = mainOrder.OldOrderSn
	glog.Info("重推OMS推单参数", " 主单:", mainOrder, " 子单:", childOrder)
	if len(paySn) == 0 {
		glog.Error(params.OrderSn, ", 重推OMS失败, ", "没有查询到到交易单号")
		out.Code = 400
		out.Error = "没有查询到交易单号！"
	}

	skuIds := ""
	StockOrder := ""
	//根据子单查询库存是否足够，如果够的话，直接修改支付通知，如果不够的话，先修改库存，然后再修改支付通知
	if len(childOrder.OrderSn) > 0 { //如果有子单用子单去查询库存，没有的话，用主单

		StockOrder = childOrder.OrderSn
	} else {
		StockOrder = mainOrder.OrderSn
	}

	ss := db.Where("order_sn = ? and product_type=1", StockOrder)
	var orderProducts []models.OrderProduct

	err := ss.Find(&orderProducts)
	if err != nil {
		glog.Error(params.OrderSn, ", 重推OMS查询订单商品信息失败：", err)
		out.Code = 400
		out.Error = err.Error()
	}
	in := ic.GetStockInfoRequest{}
	in.Source = 1
	in.IsNeedPull = 0

	for k := range orderProducts {
		itempar := ic.ProductsInfo{
			SkuId:        cast.ToInt32(orderProducts[k].SkuId),
			Type:         2,
			IsAllVirtual: 0,
		}
		in.ProductsInfo = append(in.ProductsInfo, &itempar)
	}

	client := ic.GetInventoryServiceClient()
	res, err := client.RPC.GetStockInfo(client.Ctx, &in)
	if err != nil {
		glog.Error(params.OrderSn, ", 重推OMS查询库存失败：", err)
		out.Code = 400
		out.Error = err.Error()
		return &out, nil
	}
	if res.Code != 200 {
		glog.Error(params.OrderSn, "重推OMS查询库存失败：", res.Error+res.Message)
		out.Code = 400
		out.Error = err.Error()
		return &out, nil
	}

	for _, x := range res.GoodsInfo.ProductsInfo {
		if x.Stock <= 0 {
			skuIds += "'" + cast.ToString(x.SkuId) + "',"
		}
	}

	sqlorder := "update dc_order.order_pay_notify set deal_num =0,deal_status=0 where order_sn=?"

	//说明都有库存，直接修改支付通知就可以了
	if skuIds == "" {
		_, err := db.Exec(sqlorder, paySn)
		if err != nil {
			glog.Error(params.OrderSn, ", 重推OMS修改支付通知错误：", err)
			out.Code = 400
			out.Error = err.Error()
			return &out, nil
		}
	} else {
		skuIds = strings.TrimRight(skuIds, ",")
		sqlWarehouse := "update dc_order.warehouse_goods set stock=1000 where warehouse_id=? and goodsid in (" + skuIds + ")"
		glog.Info("OMS重推改库存 :", sqlWarehouse)
		session := db.NewSession()
		session.Begin()
		_, err := session.Exec(sqlWarehouse, warehouseId)
		if err != nil {
			glog.Error(params.OrderSn, ", 重推OMS 修改库存存失败：", err)
			out.Code = 400
			out.Error = err.Error()
			session.Rollback()
			return &out, nil
		}
		session.Exec(sqlorder, paySn)
		if err != nil {
			glog.Error(params.OrderSn, ", 重推OMS 修改库存存失败：", err)
			out.Code = 400
			out.Error = err.Error()
			session.Rollback()
			return &out, nil
		}
		session.Commit()
	}
	//修改库存为100个

	return &out, nil
}

// 修复推送失败订单数据
func (c OrderService) UpdateOrderData(ctx context.Context, req *oc.UpdateOrderDataRequest) (*oc.UpdateOrderDataRespond, error) {
	MarkOrders := map[string]bool{
		"4100004398859429": true, "4100004399152761": true, "4100004397973481": true, "4100004397701525": true, "4100004397828023": true,
		"4100004398659384": true, "4100004398307657": true, "4100004401003656": true, "4100004398110818": true, "4100004404425201": true,
		"4100004398671542": true, "4100004398677561": true, "4100004399297866": true, "4100004397700387": true, "4100004398656834": true,
		"4100004398549963": true, "4100004397761668": true, "4100004397776589": true, "4100004397810191": true, "4100004398073445": true,
		"4100004397817551": true, "4100004397887344": true, "4100004397913109": true, "4100004398459795": true, "4100004398497256": true,
		"4100004398758145": true, "4100004398361109": true, "4100004397563414": true, "4100004398155763": true,
		"4100004397713565": true, "4100004398461320": true, "4100004398761421": true, "4100004397842448": true, "4100004397608834": true,
		"4100004399065898": true, "4100004398605414": true, "4100004397632112": true, "4100004397768952": true,
		"4100004398778800": true, "4100004402303148": true, "4100004401041834": true, "4100004398899877": true,
		"4100004398547345": true, "4100004398077868": true, "4100004397827457": true, "4100004399216515": true, "4100004399048074": true,
		"4100004398810509": true, "4100004398525277": true, "4100004398177523": true, "4100004398807919": true, "4100004397773417": true,
		"4100004399080468": true, "4100004398805374": true, "4100004398336204": true,
		"4100004398548409": true, "4100004397696960": true,
		"4100004398345313": true, "4100004397973959": true,
	}
	ret := new(oc.UpdateOrderDataRespond)
	ret.Code = 200

	// 获取有问题的数据
	//engine :=  kit.NewDBEngine("mayb:89o7jhi86Jhfhj54HT6@(s2b2c-master.mysql.polardb.rds.aliyuncs.com:3339)/dc_order?charset=utf8mb4")
	//db := engine.Engine.(*xorm.Engine)

	c.session = GetDBConn().NewSession()

	var orders []models.OrderMain
	session := GetDBConn().Table("order_main").
		Join("inner", "order_detail", "order_main.parent_order_sn = order_detail.order_sn").
		Where("order_detail.push_third_order_reason like ?", "授权令牌%").
		And("order_detail.accept_time > ? and  order_main.is_virtual = 0", req.Date)

	//单个
	if req.Type == 1 {
		session = session.And("order_detail.order_sn =  ? ", req.OrderSn)
	}

	err := session.Find(&orders)
	if err != nil {
		return nil, err
	}

	var success []string
	var failed []string

	//处理数据
	for _, order := range orders {

		if order.OrderStatus == 0 {
			continue
		}

		_, ok := MarkOrders[order.ParentOrderSn]
		if ok {
			continue
		}

		//处理手机号
		if len(order.ReceiverPhone) >= 11 {
			order.ReceiverPhone = MobileReplaceWithStar(order.ReceiverPhone)
		}
		if len(order.ReceiverMobile) >= 11 {
			order.ReceiverMobile = MobileReplaceWithStar(order.ReceiverMobile)
		}

		//处理数据
		c.orderMain = &order
		OrderParam := c.MakeData(order)
		glog.Info("推送全渠道接口参数：", kit.JsonEncode(c.orderMain))

		gjpOrder := dto.Orders{}
		gjpOrder.Orders = append(gjpOrder.Orders, OrderParam)
		glog.Info("推送全渠道参数：order_sn：", order.OldOrderSn, kit.JsonEncode(gjpOrder))
		res, err := c.OrderSynchronizeNew(&gjpOrder)
		glog.Info("全渠道推送返回参数: ", order.OldOrderSn, kit.JsonEncode(res), err)

		if err != nil || res.Code < 0 {
			failed = append(failed, order.OldOrderSn)
			continue
		}

		_, err = GetDBConn().
			SQL("update order_detail set push_third_order=1 where order_sn=? or order_sn=?", order.OrderSn, order.ParentOrderSn).
			Exec()

		if err != nil {
			glog.Info("UpdateOrderData 修改状态失败:", order.OldOrderSn)
		}
		success = append(success, order.OldOrderSn)
	}
	ret.Message = "操作成功订单 <> " + strings.Join(success, ",") + "  ---  " + "操作失败订单 <> " + strings.Join(failed, ",")

	return ret, nil
}

func (c OrderService) MakeData(orderMain models.OrderMain) dto.OrderParam {
	orderProductModel := c.GetAllOrderProduct()

	//FreightPrivilege := c.FreightCal()
	Freight := orderMain.Freight - c.FreightCal()

	TotalMoneystr := fmt.Sprintf("%.2f", kit.FenToYuan(orderMain.Total))

	var orderParmas []dto.OrderDetailsParam
	deliverLog := dto.DeliverLog{}
	var PaymentTotal int32
	for _, goods := range orderProductModel {
		Payment := goods.Number * goods.PayPrice
		PaymentTotal += Payment
		OrderDetailsParam := dto.OrderDetailsParam{
			Oid:          cast.ToString(goods.Id),
			Barcode:      goods.SkuId, //todo 不用转全渠道id，全渠道商品与商品中心以skuid做关联
			Eshopgoodsid: goods.SkuId, //todo 不用转全渠道id，全渠道商品与商品中心以skuid做关联
			Num:          fmt.Sprintf("%d", goods.Number),
			Payment:      fmt.Sprintf("%.2f", float64(Payment)/100),
			Weight:       "0",
			Size:         "0",
		}

		orderParmas = append(orderParmas, OrderDetailsParam)
		deliverLog.Goodslist = append(deliverLog.Goodslist, struct{ GoodsId string }{GoodsId: goods.SkuId})
	}

	OrderParam := dto.OrderParam{
		Tid:              orderMain.OrderSn,
		Weight:           "0",
		Size:             "0",
		Buyernick:        "",
		Buyermessage:     orderMain.OldOrderSn, //方便对账去掉买家留言，传美团单号
		Sellermemo:       orderMain.WarehouseCode,
		Total:            TotalMoneystr, //实付价格加优惠金额
		Privilege:        "0",
		Postfee:          fmt.Sprintf("%.2f", kit.FenToYuan(Freight+orderMain.PackingCost)), //加上包装费，不然全渠道总价对不上
		Receivername:     orderMain.ReceiverName,
		Receiverstate:    orderMain.ReceiverState,
		Receivercity:     orderMain.ReceiverCity,
		Receiverdistrict: orderMain.ReceiverDistrict,
		Receiveraddress:  orderMain.ReceiverAddress,
		Receiverphone:    orderMain.ReceiverPhone,
		Receivermobile:   orderMain.ReceiverMobile,
		Created:          kit.GetTimeNow(),
		Status:           "Payed",
		Type:             "NoCod",
		Paytime:          kit.GetTimeNow(),
		Btypecode:        HashGet("store:relation:dctoqqd", orderMain.ShopId), //根据门店财务编码获取redis数据
		Details:          orderParmas,
	}

	return OrderParam
}

// UpdateOrderSku 修改订单商品货号
func (o OrderService) UpdateOrderSku(ctx context.Context, in *oc.UpdateOrderSkuReq) (out *oc.BaseResponse, e error) {
	out = &oc.BaseResponse{Code: 400}

	defer func() {
		glog.Info("Order UpdateOrderSku，入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	session := GetDBConn().NewSession()
	defer session.Close()

	if has, err := session.Table("sc_stock.auth").Where("user_no=? and button_permission = 1", in.UserNo).Exist(); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "没有修改货号权限"
		return
	}

	in.SkuId = strings.TrimSpace(in.SkuId)
	in.ThirdSkuId = strings.TrimSpace(in.ThirdSkuId)

	// 判断填写的skuId和商品货号是否正确
	var skuThird models.SkuThird
	if _, err := session.Table("dc_product.sku_third").Where("sku_id=? and third_sku_id=?", in.SkuId, in.ThirdSkuId).Get(&skuThird); err != nil {
		out.Message = err.Error()
		return
	} else if skuThird.Id == 0 {
		out.Message = "skuId或商品货号与平台商品库的该商品不对应，请重新填写"
		return
	}

	order := new(models.Order)
	if _, err := session.Table("order_main").Alias("m").
		Join("inner", "order_detail d", "m.order_sn = d.order_sn").
		Select("m.order_sn,m.order_status,d.push_third_order,m.channel_id,m.warehouse_id,d.locked_stock").
		Where("m.order_sn=?", in.OrderSn).Get(order); err != nil {
		out.Message = err.Error()
		return
	} else if order.OrderSn == "" {
		out.Message = "订单号不存在"
		return
	} else if order.OrderStatus == 0 || order.PushThirdOrder > 0 {
		out.Message = "已取消或者推送到第三方的订单不允许修改货号"
		return
	}

	// 查询该订单商品是否为实物
	orderProduct := new(models.OrderProduct)
	if _, err := session.Table("order_product").Where("order_sn = ? and id = ?", in.OrderSn, in.OrderProductModelId).Get(orderProduct); err != nil {
		out.Message = err.Error()
		return
	} else if orderProduct.Id == 0 {
		out.Message = "OrderProductModelId参数有误"
		return
	} else if orderProduct.ProductType != 1 {
		out.Message = "非实物商品不可以修改货号"
		return
	} else if orderProduct.SkuId == in.SkuId && orderProduct.ThirdSkuId == in.ThirdSkuId {
		out.Message = "skuId及货号未变更"
		return
	}

	// 查询商品信息
	var product models.ChannelProduct
	if _, err := session.Table("dc_product.channel_product").Where("id=? and channel_id = ?", skuThird.ProductId, order.ChannelId).Get(&product); err != nil {
		out.Message = err.Error()
		return
	} else if product.Id == 0 {
		out.Message = "商品不存在，请重新填写。"
		return
	} else if product.IsDel == 1 {
		out.Message = "商品已被移除至回收站。"
		return
	} else if product.ProductType != 1 {
		out.Message = "只允许修改为实物商品"
		return
	}

	// 子订单商品
	var ops []*models.OrderProduct
	if err := session.Table("dc_order.order_main").Alias("om").
		Join("inner", "dc_order.order_product op", "op.order_sn = om.order_sn").
		Select("op.*").Where("om.parent_order_sn = ? and op.sku_id = ? and op.parent_sku_id = ? and op.payment_total = ? and op.privilege_pt = ?",
		in.OrderSn, orderProduct.SkuId, orderProduct.ParentSkuId, orderProduct.PaymentTotal, orderProduct.PrivilegePt).Limit(1).Find(&ops); err != nil {
		out.Message = err.Error()
		return
	}
	ops = append(ops, orderProduct)

	// 规格
	var specValue string
	if _, err := session.Table("dc_product.sku_value").Alias("v").Join("inner", "dc_product.spec_value s", "s.id = v.spec_value_id").
		Where("v.product_id = ? and v.sku_id = ?", skuThird.ProductId, skuThird.SkuId).
		Select("s.value").Get(&specValue); err != nil {
		out.Message = "查询规格值出错 " + err.Error()
		return
	}

	// 条码
	var barCode string
	if _, err := session.Table("dc_product.channel_sku").Where("channel_id = ? and id= ?", order.ChannelId, in.SkuId).Select("bar_code").Get(&barCode); err != nil {
		out.Message = "查询条码出错 " + err.Error()
		return
	}

	_ = session.Begin()
	for _, op := range ops {
		if _, err := session.ID(op.Id).MustCols("bar_code").Update(&models.OrderProduct{
			ProductId:   cast.ToString(product.Id),
			SkuId:       in.SkuId,
			ThirdSkuId:  in.ThirdSkuId,
			ProductName: product.Name + " " + specValue,
			Image:       strings.Split(product.Pic, ",")[0],
			BarCode:     barCode,
		}); err != nil {
			out.Message = err.Error()
			_ = session.Rollback()
			return
		}

		// 如果变更了skuId，新老sku需同步变更sku_pay_total
		if in.SkuId != op.SkuId {
			if _, err := session.Exec(`update dc_order.order_product op
    inner join (select sum(payment_total+privilege_pt) sku_pay_total,order_sn,sku_id from dc_order.order_product where order_sn = ? and sku_id in (?,?)
group by order_sn,sku_id) t on t.order_sn = op.order_sn and t.sku_id = op.sku_id
set op.sku_pay_total = t.sku_pay_total;`, op.OrderSn, op.SkuId, in.SkuId); err != nil {
				out.Message = err.Error()
				_ = session.Rollback()
				return
			}
		}

		// 如果是组合商品子商品修改，还要修改组合商品
		if op.ParentSkuId != "" {
			var cps []*models.OrderProduct
			if err := session.Where("order_sn=? and parent_sku_id=?", op.OrderSn, op.ParentSkuId).Find(&cps); err != nil {
				out.Message = err.Error()
				_ = session.Rollback()
				return
			}

			groupOp := models.OrderProduct{
				ChildrenSku: kit.JsonEncode(cps),
			}
			for _, cp := range cps {
				groupOp.SkuPayTotal += cp.SkuPayTotal
			}

			if _, err := session.Where("order_sn=? and sku_id=?", op.OrderSn, op.ParentSkuId).
				Update(groupOp); err != nil {
				out.Message = err.Error()
				_ = session.Rollback()
				return
			}
		}
	}

	client := ic.GetInventoryServiceClient()
	var source int32
	switch order.ChannelId {
	case ChannelAwenId, ChannelMtId, ChannelElmId, ChannelJddjId:
		source = 2
	case ChannelMallId:
		source = 1
	case ChannelDigitalHealth:
		source = 3
	}

	// 如果订单已经锁库成功，则变更锁库
	if order.LockedStock > 0 {
		// skuId 变更了
		if orderProduct.SkuId != in.SkuId {
			freezeStock := new(models.OrderFreezeStock)
			// 一个订单一个sku只有一条记录
			if _, err := session.Table("order_freeze_stock").Where("order_sn = ? and sku_id = ?", in.OrderSn, orderProduct.SkuId).
				Get(freezeStock); err != nil {
				out.Message = err.Error()
				_ = session.Rollback()
				return
			} else if freezeStock.Stock > 0 {
				// 变更锁库sku
				if _, err := session.ID(freezeStock.Id).Update(&models.OrderFreezeStock{
					SkuId: in.SkuId,
					Stock: orderProduct.Number,
				}); err != nil {
					out.Message = err.Error()
					_ = session.Rollback()
					return
				}

				// 多个sku时，差额锁库
				if freezeStock.Stock > orderProduct.Number {
					if _, err := session.Insert(&models.OrderFreezeStock{
						OrderSn:     in.OrderSn,
						SkuId:       orderProduct.SkuId,
						Stock:       freezeStock.Stock - orderProduct.Number,
						WarehouseId: order.WarehouseId,
					}); err != nil {
						out.Message = err.Error()
						_ = session.Rollback()
						return
					}

					freezeStock.Stock = orderProduct.Number
				}

				// 冻结库存
				if res, err := client.RPC.FreezeStock(client.Ctx, &ic.FreezeRequest{
					OrderId: in.OrderSn,
					GoodsList: []*ic.OrderGoodsInfo{
						{
							GoodsId:       in.SkuId,
							Number:        orderProduct.Number,
							WarehouseId:   order.WarehouseId,
							WarehouseType: orderProduct.WarehouseType,
						},
					},
					Source: source,
				}); err != nil {
					_ = session.Rollback()
					out.Message = err.Error()
					return
				} else if res.Code != 200 {
					if strings.Contains(res.Message, "库存不足") {
						out.Message = "货号：" + in.ThirdSkuId + "的库存不足，修改失败"
					} else {
						out.Message = res.Message
					}
					_ = session.Rollback()
					return
				}

				// 解冻原sku库存
				freedReq := &ic.FreedStockRequest{
					Source:  source,
					OrderId: in.OrderSn,
					GoodsList: []*ic.OrderGoodsInfo{
						{
							GoodsId:       orderProduct.SkuId,
							Number:        freezeStock.Stock,
							WarehouseId:   order.WarehouseId,
							WarehouseType: orderProduct.WarehouseType,
						},
					},
				}
				if res, err := client.RPC.FreedStock(client.Ctx, freedReq); err != nil {
					glog.Info("Order UpdateOrderSku 入参：", kit.JsonEncode(freedReq), "，解冻库存失败：", err.Error())
				} else if res.Code != 200 {
					glog.Info("Order UpdateOrderSku 入参：", kit.JsonEncode(freedReq), "，解冻库存失败：", kit.JsonEncode(res))
				}
			}
		}
	} else { // 原先没有锁定库存，那么只需查当前sku是否满足条件
		var stock int32 // 需要的库存
		if _, err := session.Table("dc_order.order_product").Where("order_sn = ? and sku_id = ?", in.OrderSn, in.SkuId).
			Select("sum(number)").Get(&stock); err != nil {
			_ = session.Rollback()
			out.Message = err.Error()
			return
		}

		// 先查当前库存
		if rs, err := client.RPC.GetStockInfo(client.Ctx, &ic.GetStockInfoRequest{
			Source: source,
			ProductsInfo: []*ic.ProductsInfo{{
				Type:  2,
				SkuId: skuThird.SkuId,
			}},
			Stockwarehouse: []int32{order.WarehouseId},
		}); err != nil {
			_ = session.Rollback()
			out.Message = err.Error()
			return
		} else {
			var hasStock bool
			var currentStock int32
			for _, wg := range rs.GoodsInWarehouse {
				if wg.SkuId == skuThird.SkuId {
					currentStock = wg.Stock
					if wg.Stock >= stock {
						hasStock = true
					}
					break
				}
			}
			if !hasStock {
				out.Message = fmt.Sprintf("skuId %d库存不足，当前库存：%d", skuThird.SkuId, currentStock)
				_ = session.Rollback()
				return
			}
		}
	}

	if err := session.Commit(); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// 退款记录表中 "退款中"更新为“已退款” 并推送第三方
func (o OrderService) UpdateRefundState(ctx context.Context, params *oc.AccomplishOrderRequest) (*oc.BaseResponse, error) {
	out := &oc.BaseResponse{Code: 400}

	o.session = GetDBConn().NewSession()
	defer o.session.Close()

	refundOrder := models.RefundOrder{}
	ok, err := o.session.Where("order_sn = ? and refund_state=1", params.OrderSn).Get(&refundOrder)
	if err != nil {
		out.Message = "退款订单查询出错!"
		out.Error = err.Error()
		glog.Error(params.OrderSn, "，更新订单退款状态：订单查询错误,", err)
		return out, nil
	}

	if ok {
		//完成更新，其他操作
		_, err = o.session.Exec("update refund_order set refund_state=3,refunded_time=? where id=?", time.Now().Format(kit.DATETIME_LAYOUT), refundOrder.Id)
		if err != nil {
			glog.Error("更新退款订单状态出错：", params.OrderSn)
			return out, nil
		}
		if refundOrder.PushThird < 1 {
			o.RefundPushThird(refundOrder)
		}
	}

	out.Code = 200
	out.Message = "ok"
	return out, nil
}

// 获取订单商品信息
func getOrderProductModelId(orderSn string, skuId string, parentSkuid string, productName string, productType int32) string {
	var orderProduct models.OrderProduct
	db := GetDBConn()

	db.Table("order_product").
		Where("order_sn=? and sku_id=? and parent_sku_id=? and product_name=? and product_type=?", orderSn, skuId, parentSkuid, productName, productType).
		Get(&orderProduct)

	if orderProduct.Id > 0 {
		return cast.ToString(orderProduct.Id)
	} else {
		return "0"
	}
}

// DigitalHealthOrderCheck 检验互联网订单是否可下单
func (o OrderService) DigitalHealthOrderCheck(c context.Context, params *oc.DigitalHealthOrderCheckRequest) (*oc.DigitalHealthOrderCheckResponse, error) {
	out := oc.DigitalHealthOrderCheckResponse{IsHad: 0}

	db := GetDBConn()

	count, err := db.Table("order_main").Alias("om").
		Join("inner", "order_detail od", "om.order_sn = od.order_sn").
		Where("od.consult_order_sn = ? and parent_order_sn = '' ", params.ConsultOrderSn).
		And("om.order_status = 10 OR (om.order_status = 0 and od.split_order_result = 1)").Count()
	if err != nil {
		glog.Errorf("校验互联网医疗订单是否可下单异常：：consult_order_sn：%s，error：%s", params.ConsultOrderSn, err.Error())
		return nil, err
	}
	if count > 0 {
		out.IsHad = 1
	}
	return &out, nil
}

// 根据订单号获取订单关联的信息
func (o OrderService) GetOrderRelationInfo(c context.Context, in *oc.GetOrderRelationInfoRequest) (out *oc.GetOrderRelationInfoResponse, err error) {
	out = new(oc.GetOrderRelationInfoResponse)
	if in.OrderSn == "" && in.ParentOrderSn == "" {
		return
	}

	var orderMain models.OrderMain
	session := GetDBConn().NewSession()
	defer session.Close()

	if in.OrderSn != "" {
		session.Where("order_sn = ? OR old_order_sn = ?", in.OrderSn, in.OrderSn)
	}
	if in.ParentOrderSn != "" { // 有些活动在前端是子单号
		session.Where("parent_order_sn = ? OR old_order_sn = ? OR order_sn = ? ", in.ParentOrderSn, in.ParentOrderSn, in.ParentOrderSn)
	}
	if _, err = session.Select("order_sn,member_id").Get(&orderMain); err != nil {
		glog.Error("GetOrderRelationInfo获取订单信息失败：", err, in)
		return
	}

	// order_main表无数据，查询拼团订单表
	if orderMain.OrderSn == "" {

		var pinOrder models.PinOrderGroup
		_, err = session.SQL("select pin_order_sn,parent_pin_order_sn,user_id from dc_order.pin_order_group where pin_order_sn = ? or parent_pin_order_sn = ?", in.ParentOrderSn, in.ParentOrderSn).Get(&pinOrder)
		if pinOrder.ParentPinOrderSn != "" {
			out.MemberId = pinOrder.UserId
			// 获取订单商品信息
			err = session.SQL("SELECT pin_sku_id as sku_id FROM pin_order_main WHERE pin_head_order_sn = ?", pinOrder.ParentPinOrderSn).Find(&out.OrderProduct)
		} else { // 拼团查不到，查询电商订单表
			upetDb := GetUPetDBConn()
			err = upetDb.SQL("SELECT goods_id as sku_id  FROM upet_order_goods WHERE order_id IN (SELECT order_id FROM upet_orders WHERE order_sn = ? ) ", in.ParentOrderSn).Find(&out.OrderProduct)
		}
		glog.Info("GetOrderRelationInfo结果拼团：", err, out, in)
		return
	} else {

		out.MemberId = orderMain.MemberId
		// 获取订单商品信息
		err = session.SQL("SELECT sku_id,product_id FROM order_product WHERE order_sn = ?", orderMain.OrderSn).Find(&out.OrderProduct)
		glog.Info("GetOrderRelationInfo结果：", err, out, in)
		return
	}
}

// BOSS 设置订单发货
func (o *OrderService) OrderSetDelivery(ctx context.Context, in *oc.OrderSetDeliveryRequest) (out *oc.BaseResponse, e error) {
	out = &oc.BaseResponse{Code: 200, Message: "发货成功"}
	//查主订单信息
	orderMain := GetOrderMainByOrderSn(in.OrderSn)

	if orderMain.Id < 1 {
		out.Code = 400
		out.Message = "订单不存在"
		return
	}
	//查实物单
	childOrder, err := GetChildRealOrderByOrderSn(in.OrderSn)
	if err != nil {
		out.Code = 400
		out.Message = "查询实物订单信息发生错误， " + err.Error()
		return
	}
	if childOrder.Id < 1 {
		out.Code = 400
		out.Message = "未查询到实物订单信息"
		return
	}

	session := GetDBConn().NewSession()
	defer session.Close()

	if orderMain.DeliveryType == 1 { //快递配送
		if childOrder.OrderStatusChild != 20201 {
			out.Code = 400
			out.Message = "发货失败，订单不是待发货状态"
			return
		}
		if has, err := session.Table("order_express").Where("order_sn = ? AND delivery_from = 1", childOrder.OrderSn).Exist(); err != nil {
			out.Code = 400
			out.Message = "发货失败，" + err.Error()
			return
		} else if has {
			out.Code = 400
			out.Message = "发货失败，已从巨益oms发货"
			return
		}
		express := &models.UpetExpress{}
		_, err := GetUPetDBConn().Where("e_code_kdniao=?", in.ExpressCode).Get(express)
		if err != nil {
			out.Code = 400
			out.Message = "查询物流信息错误"
			return
		}
		product := GetOrderProductByOrderSn(childOrder.OrderSn, "*")
		if len(product) == 0 {
			out.Code = 400
			out.Message = "未查询到订单商品信息"
			return
		}
		delp := &dto.DeliverParam{
			Source:        1,
			OrderSn:       childOrder.OrderSn,
			IsEntire:      1,
			DeliverDetail: []dto.DeliverDetail{},
		}
		num := int32(0)
		for _, v := range product {
			delp.DeliverDetail = append(delp.DeliverDetail, dto.DeliverDetail{
				GoodsSku: v.SkuId,
				Num:      v.Number,
			})
			num += v.Number
		}
		session.Begin()
		orderExpress := &models.OrderExpress{
			OrderSn:      childOrder.OrderSn,
			ExpressNo:    in.ShippingCode,
			ExpressCode:  in.ExpressCode,
			ExpressName:  express.EName,
			Num:          num,
			DeliveryTime: time.Now(),
			CreateTime:   time.Now(),
		}
		if _, err := session.Insert(orderExpress); err != nil {
			glog.Error(childOrder.OrderSn, "保存快递记录失败", err.Error(), kit.JsonEncode(orderExpress))
			out.Code = 400
			out.Message = "保存快递记录失败"
			return
		}
		if isok, err := OrderStatus(delp); err != nil {
			session.Rollback()
			glog.Error(childOrder.OrderSn, "调用OrderStatus发货函数失败："+kit.JsonEncode(delp))
			out.Code = 400
			out.Message = "发货失败，" + err.Error()
		} else if !isok {
			session.Rollback()
			glog.Error(childOrder.OrderSn, "调用OrderStatus发货函数失败："+kit.JsonEncode(delp))
			out.Code = 400
			out.Message = "发货失败"
			return
		}
		session.Commit()
	} else if orderMain.DeliveryType == 5 { //商家自配
		if childOrder.OrderStatusChild != 20102 {
			out.Code = 400
			out.Message = "发货失败，订单不是已接单状态"
			return
		}
		DeliveryId := GetSn("delivery")[0]
		orderException := &models.OrderException{}
		oe := OrderExceptionService{}
		if has, err := session.Where("order_sn=?", childOrder.OrderSn).Get(orderException); err != nil {
			out.Code = 400
			out.Message = "发货失败，" + err.Error()
			return
		} else if has {
			DeliveryId = orderException.DeliveryId
		} else {
			exceptionRequest := oc.OrderExceptionRequest{
				DeliveryId:       DeliveryId,
				OrderId:          childOrder.OrderSn,
				ExceptionDescr:   "商家自配",
				DistributionMode: "商家自配",
				ExceptionTime:    kit.GetTimeNow(),
				Source:           1,
				OrderStatus:      3,
				IsHide:           true,
			}
			res, err := oe.OrderExceptionAdd(nil, &exceptionRequest)
			if err != nil {
				glog.Info("商家自配"+childOrder.OrderSn+" 配送异常添加失败：", err.Error())
				out.Code = 400
				out.Message = "发货失败，" + err.Error()
				return
			} else if res.Code != 200 {
				glog.Info("商家自配"+childOrder.OrderSn+" 配送异常添加失败：", res.Message+res.Error)
				out.Code = 400
				out.Message = "发货失败，" + res.Message
				return
			}
		}
		DeliveryRecord := models.OrderDeliveryRecord{
			DeliveryId:          cast.ToInt64(DeliveryId),
			MtPeisongId:         "",
			OrderSn:             childOrder.OrderSn,
			DeliveryServiceCode: 1005,
		}
		if _, err = session.Insert(&DeliveryRecord); err != nil {
			glog.Info("商家自配"+childOrder.OrderSn+" 添加配送记录失败：", err.Error())
			out.Code = 400
			out.Message = "发货失败，" + err.Error()
			return
		}
		if res, err := oe.OrderOwnDeliver(ctx, &oc.OrderExceptionRequest{
			DeliveryId:   DeliveryId,
			MtPeisongId:  "",
			CourierName:  in.CourierName,
			CourierPhone: in.CourierPhone,
			OrderId:      childOrder.OrderSn,
			Source:       1,
			IsHide:       true,
		}); err != nil {
			glog.Info("商家自配"+childOrder.OrderSn+" 配送异常添加失败：", err.Error())
			out.Code = 400
			out.Message = "发货失败，" + err.Error()
			return
		} else if res.Code != 200 {
			glog.Info("商家自配"+childOrder.OrderSn+" 配送异常添加失败：", res.Message+res.Error)
			out.Code = 400
			out.Message = "发货失败，" + res.Message
			return
		}
	}
	return
}

// BOSS 订单确认已送货，会修改订单为已完成
func (o *OrderService) ConfirmDeliveredOrder(ctx context.Context, in *oc.ConfirmDeliveredOrderRequest) (out *oc.BaseResponse, e error) {
	out = &oc.BaseResponse{Code: 200}
	//查主订单信息
	orderMain := GetOrderMainByOrderSn(in.OrderSn)
	if orderMain.Id < 1 {
		out.Code = 400
		out.Message = "订单不存在"
		return
	}
	//查实物单
	childOrder, err := GetChildRealOrderByOrderSn(in.OrderSn)
	if err != nil {
		out.Code = 400
		out.Message = "查询实物订单信息发生错误， " + err.Error()
		return
	}
	if childOrder.Id < 1 {
		out.Code = 400
		out.Message = "未查询到实物订单信息"
		return
	}

	if orderMain.DeliveryType == 1 {
		if !(childOrder.OrderStatusChild == 20202 || childOrder.OrderStatusChild == 20204) {
			out.Code = 400
			out.Message = "订单不是发货状态"
			return
		}
		if childOrder.OrderStatusChild == 20204 {
			out.Code = 400
			out.Message = "该订单为部分发货，不能设置为完成"
			return
		}
	} else if orderMain.DeliveryType == 5 {
		if childOrder.OrderStatusChild != 20103 {
			out.Code = 400
			out.Message = "订单状态不是配送中"
			return
		}
		oe := OrderExceptionService{}
		if res, err := oe.GoodArrive(ctx, &oc.ExceptionOrderStatusRequest{
			OrderId:       childOrder.OrderSn,
			StoreMasterId: childOrder.AppChannel,
		}); err != nil {
			out.Code = 400
			out.Message = "确认送达失败, " + err.Error()
			return
		} else if res.Code != 200 {
			out.Code = 400
			out.Message = "确认送达失败，" + res.Message
			return
		}
	}

	return o.AccomplishOrder(ctx, &oc.AccomplishOrderRequest{
		OrderSn:     childOrder.OrderSn,
		ConfirmTime: time.Now().Format(kit.DATETIME_LAYOUT),
	})
}

// PrescribeCheck 检查是否开过处方
func (o *OrderService) PrescribeCheck(ctx context.Context, in *oc.OrderPrescribeCheckReq) (out *oc.OrderPrescribeCheckRes, e error) {
	out = &oc.OrderPrescribeCheckRes{Code: 400, Data: new(oc.OrderPrescribeCheckRes_Data)}
	defer func() {
		if out.Code != 200 {
			glog.Info("Order PrescribeCheck 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.Skus) == 0 {
		out.Message = "药品不能为空"
		return
	}
	if len(in.FinanceCode) == 0 {
		out.Message = "财务编码不能为空"
		return
	}

	skuIds := make([]string, len(in.Skus))
	skuNums := make(map[int32]int32)
	for i, sku := range in.Skus {
		skuIds[i] = cast.ToString(sku.SkuId)
		skuNums[sku.SkuId] += sku.Num
	}
	idsStr := strings.Join(skuIds, ",")

	type SkuDrug struct {
		SkuId       int32
		Count       int32
		ParentSkuId int32
	}
	var sds []*SkuDrug

	db := GetDBConn()
	if err := db.SQL(fmt.Sprintf(`select t.sku_id,t.count,t.parent_sku_id from 
(select id as sku_id,product_id,0 as count,0 as parent_sku_id from dc_product.sku s where s.id in (%s)
union all
select group_sku_id as sku_id,group_product_id as product_id,count,sku_id as parent_sku_id from dc_product.sku_group sg where sg.sku_id in (%s) and product_type = 1
) t join dc_product.product p on p.id = t.product_id
where p.product_type = 1 and p.is_prescribed_drug = 1;`, idsStr, idsStr)).Find(&sds); err != nil {
		out.Message = err.Error()
		return
	} else if len(sds) == 0 { // 没有处方药
		out.Code = 200
		return
	}

	qds := make(map[int32]*oc.OrderPrescribeSkuNum)
	var ids []int32
	for _, sd := range sds {
		var num int32
		// 是组合商品子商品
		if sd.ParentSkuId > 0 {
			num = skuNums[sd.ParentSkuId] * sd.Count
		} else {
			num = skuNums[sd.SkuId]
		}
		if qd, has := qds[sd.SkuId]; has {
			qd.Num += num
		} else {
			ids = append(ids, sd.SkuId)
			qds[sd.SkuId] = &oc.OrderPrescribeSkuNum{
				SkuId: sd.SkuId,
				Num:   num,
			}
		}
	}
	out.Data.Skus = make([]*oc.OrderPrescribeSkuNum, len(qds))
	var i int32
	for _, qd := range qds {
		out.Data.Skus[i] = qd
		i++
	}

	cacheKey := prescribeCacheKey(in.ScrmId, in.FinanceCode, out.Data.Skus)
	rc := GetRedisConn()

	// 获取缓存的处方单号
	out.Data.ConsultOrderSn = rc.Get(cacheKey).Val()

	// 如果存在处方单号，还要判断一下处方单号是否用过
	if len(out.Data.ConsultOrderSn) > 0 {
		if has, err := db.Table("order_detail").Where("consult_order_sn = ?", out.Data.ConsultOrderSn).Exist(); err != nil {
			out.Message = err.Error()
			return
		} else if has { // 处方单号使用过了，清空数据
			rc.Del(cacheKey)
			out.Data.ConsultOrderSn = ""
		}
	}

	out.Code = 200
	return
}

// ReDelivery 订单列表-发起配送
func (o OrderService) ReDelivery(ctx context.Context, req *oc.ReDeliveryRequest) (*oc.OrderExceptionResponse, error) {
	parentOrderSn := req.OrderSn
	resp := new(oc.OrderExceptionResponse)
	if len(parentOrderSn) == 0 {
		resp.Message = "参数异常，order_sn不能为空"
		return resp, nil
	}

	// 根据父订单号查询到到子订单（实物订单）
	order, err := getSubUnVirtualOrder(parentOrderSn)
	if err != nil {
		return nil, err
	}
	//var c CommonService
	//c.orderMain = order

	err = o.AotuPushDelivery(order.OrderSn, dto.DeliverPriceRes{})
	if err != nil {
		return nil, err
	}

	resp.Message = "发起配送成功"
	resp.Code = 200
	return resp, nil
}

// 根据父订单号获取到实物子订单
func getSubUnVirtualOrder(parentOrderSn string) (*models.OrderMain, error) {
	db := GetDBConn()
	orderInfo := models.OrderMain{}
	db.Table("order_main").Where("parent_order_sn=? AND is_virtual=0", parentOrderSn).Get(&orderInfo) //子单信息
	if orderInfo.Id == 0 {
		glog.Error("订单信息不存在：parent_order_sn=" + parentOrderSn)
		return nil, errors.New("没有找到对应的子订单信息")
	}

	return &orderInfo, nil
}

// 麦芽田 订单列表
func (o OrderService) MytOrderList(ctx context.Context, req *oc.MytOrderListRequest) (*oc.MytBaseDataResponse, error) {
	resp := new(oc.MytBaseDataResponse)
	db := GetDBConn()
	//先根据第三方店铺ID查询财务编码
	resp.Code = 400

	deliveryConfig := new(models.DeliveryConfig)
	_, err := db.Where("store_id = ?", req.ShopId).
		Where("channel_id = ?", 1).
		Where("org_id = ?", 6).
		Get(deliveryConfig)

	if err != nil {
		resp.Message = "查询到当前渠道的配送方式错误"
		return resp, nil
	}
	if deliveryConfig.ID == 0 {
		resp.Message = "未查询到当前渠道的配送方式"
		return resp, nil
	}
	if deliveryConfig.ThirdType != 1 && deliveryConfig.DeliveryMethod != 2 {
		resp.Message = "当前配置的不是第三方配送麦芽田"
		return resp, nil
	}
	tBegin := time.Unix(req.StartTime, 0)
	tEnd := time.Unix(req.EndTime, 0)
	m := make([]models.Order, 0)
	count, err := db.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where(" order_main.org_id=6 and order_main.channel_id=1 and order_main.delivery_type=2 and parent_order_sn!=''").
		Where("order_main.shop_id=? and order_main.create_time>? and order_main.create_time<=?", deliveryConfig.FinanceCode, tBegin, tEnd).
		OrderBy("order_main.id desc").Limit(int(req.PageSize), int((req.Page-1)*req.PageSize)).
		FindAndCount(&m)
	if err != nil {
		glog.Error(req, ", 查询订单总信息失败, ", err)
		resp.Message = "查询订单总信息失败" + err.Error()
		return resp, nil
	}
	now := time.Now()
	totalPages := (cast.ToInt32(count) + req.PageSize - 1) / req.PageSize
	data := dto.MytResponse{}
	data.Total = cast.ToInt(count)
	data.IsLast = req.Page >= totalPages
	//MytOrderData := make([]dto.MytOrderData, 0)

	orderSns := make([]string, 0)
	//查询订单商品数据
	for _, x := range m {
		orderSns = append(orderSns, x.OrderSn)
	}
	var orderProducts []models.OrderProduct
	orderProductMap := map[string][]models.OrderProduct{}
	if len(orderSns) > 0 {
		//主单商品
		err = db.Table("order_product").
			Join("inner", "order_detail", "order_product.order_sn=order_detail.order_sn").
			In("order_product.order_sn", orderSns).Find(&orderProducts)
		if err != nil {
			glog.Error(req, ", 查询订单商品信息失败, ", err)
			resp.Message = "查询订单商品信息失败" + err.Error()
			return resp, nil
		}

		//子订单号与商品的关系
		for _, product := range orderProducts {
			orderProductMap[product.OrderSn] = append(orderProductMap[product.OrderSn], product)
		}
	}

	for _, x := range m {
		item := dto.MytOrderData{}
		item.UpdateTime = now.Unix()
		total := cast.ToInt(x.PayTotal)
		//订单信息
		mytOrder := dto.MytOrder{}
		mytOrder.OrderId = x.OrderSn
		mytOrder.OrderSn = cast.ToInt(x.Id)
		mytOrder.PickupCode = x.PickupCode
		mytOrder.ShopId = req.ShopId
		mytOrder.Category = "chaoshi"
		mytOrder.IsPreOrder = false
		mytOrder.PickTime = x.PickingTime.Unix()
		mytOrder.OriginTag = "宠财神"
		mytOrder.TotalPrice = total
		mytOrder.BalancePrice = total
		mytOrder.ShopName = x.ShopName

		//订单费用信息
		MytOrderFee := dto.MytOrderFee{}

		MytOrderFee.TotalFee = total
		MytOrderFee.SendFee = cast.ToInt(x.Freight)
		MytOrderFee.PackageFee = cast.ToInt(x.PackingCost)
		MytOrderFee.ShopFee = total
		MytOrderFee.UserFee = total
		MytOrderFee.PayType = 2
		MytOrderFee.NeedInvoice = false
		//MytOrderFee.Invoice
		//MytOrderFee.Commission=0
		//MytOrderFee.Activity
		MytOrderFee.IsFirst = false
		MytOrderFee.IsFavorite = false
		mytOrder.OrderFee = MytOrderFee

		//订单商品信息
		if Products, exists := orderProductMap[x.OrderSn]; exists {
			for _, Product := range Products {
				orderGoods := dto.MytOrderGoods{
					GoodsID:              Product.ProductId,
					GoodsName:            Product.ProductName,
					Thumb:                Product.Image,
					SKUId:                Product.SkuId,
					Unit:                 Product.Specs,
					Weight:               kit.YuanToFen(Product.WeightForUnit), // 以克为单位
					UPC:                  Product.BarCode,
					ShelfNo:              Product.LocationCode,
					Number:               cast.ToInt(Product.Number),
					GoodsPrice:           cast.ToInt(Product.PayPrice),     // 以分为单位
					GoodsTotalFee:        cast.ToInt(Product.PaymentTotal), // 总费用
					PackageNumber:        0,
					PackagePrice:         0, // 包装费用
					PackageTotalFee:      0, // 包装总费用
					ReduceFee:            0,
					DiscountFee:          0,
					DiscountPlatformFee:  0,
					DiscountMerchantFee:  0,
					DiscountAgentFee:     0,
					DiscountLogisticsFee: 0,
					TotalFee:             cast.ToInt(Product.PaymentTotal), // 最终总费用
				}
				item.OrderGoods = append(item.OrderGoods, orderGoods)

			}
		}

		//客户信息
		customer := dto.Customer{}
		customer.OrderPhone = utils.MobileDecrypt(x.EnMemberTel)
		customer.RealName = x.ReceiverName
		customer.Phone = utils.MobileDecrypt(x.EnReceiverPhone)
		customer.ReservePhone = utils.MobileDecrypt(x.EnReceiverPhone)
		customer.SecretPhone = x.ReceiverPhone
		customer.Address = x.ReceiverAddress
		customer.Longitude = x.Lng
		customer.Latitude = x.Lat
		item.OrderCustomer = customer

		item.Order = mytOrder

		data.Data = append(data.Data, item)
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		glog.Error(req, ", 序列化失败, ", err)
		resp.Message = "序列化失败" + err.Error()
		return resp, nil
	}
	resp.Data = string(jsonData)
	resp.Message = "ok"
	resp.Code = 200
	return resp, nil
}

// 麦芽田 订单列表
func (o OrderService) MytOrderDetail(ctx context.Context, req *oc.MytOrderListRequest) (*oc.MytBaseDataResponse, error) {
	resp := new(oc.MytBaseDataResponse)
	db := GetDBConn()
	//先根据第三方店铺ID查询财务编码
	resp.Code = 400

	deliveryConfig := new(models.DeliveryConfig)
	_, err := db.Where("store_id = ?", req.ShopId).
		Where("channel_id = ?", 1).
		Where("org_id = ?", 6).
		Get(deliveryConfig)

	if err != nil {
		resp.Message = "查询到当前渠道的配送方式错误"
		return resp, nil
	}
	if deliveryConfig.ID == 0 {
		resp.Message = "未查询到当前渠道的配送方式"
		return resp, nil
	}
	if deliveryConfig.ThirdType != 1 && deliveryConfig.DeliveryMethod != 2 {
		resp.Message = "当前配置的不是第三方配送麦芽田"
		return resp, nil
	}
	m := models.Order{}
	have, err := db.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where(" order_main.org_id=6 and order_main.channel_id=1 and order_main.delivery_type=2 and parent_order_sn!=''").
		Where("order_main.shop_id=? and order_main.order_sn=?", deliveryConfig.FinanceCode, req.OrderId).
		Get(&m)
	if err != nil {
		glog.Error(req, ", 查询订单总信息失败, ", err)
		resp.Message = "查询订单总信息失败" + err.Error()
		return resp, nil
	}
	if !have {
		resp.Message = "订单不存在：" + req.OrderId
		return resp, nil
	}
	now := time.Now()

	orderSns := m.OrderSn
	var orderProducts []models.OrderProduct
	orderProductMap := map[string][]models.OrderProduct{}

	//主单商品
	err = db.Table("order_product").
		Join("inner", "order_detail", "order_product.order_sn=order_detail.order_sn").
		Where("order_product.order_sn=?", orderSns).Find(&orderProducts)
	if err != nil {
		glog.Error(req, ", 查询订单商品信息失败, ", err)
		resp.Message = "查询订单商品信息失败" + err.Error()
		return resp, nil
	}

	//子订单号与商品的关系
	for _, product := range orderProducts {
		orderProductMap[product.OrderSn] = append(orderProductMap[product.OrderSn], product)
	}

	item := dto.MytOrderData{}
	item.UpdateTime = now.Unix()
	total := cast.ToInt(m.PayTotal)
	//订单信息
	mytOrder := dto.MytOrder{}
	mytOrder.OrderId = m.OrderSn
	mytOrder.OrderSn = cast.ToInt(m.Id)
	mytOrder.PickupCode = m.PickupCode
	mytOrder.ShopId = req.ShopId
	mytOrder.Category = "chaoshi"
	mytOrder.IsPreOrder = false
	mytOrder.PickTime = m.PickingTime.Unix()
	mytOrder.UserRemark = m.BuyerMemo
	mytOrder.OriginTag = "宠财神"
	mytOrder.TotalPrice = total
	mytOrder.BalancePrice = total
	mytOrder.ShopName = m.ShopName

	//订单费用信息
	MytOrderFee := dto.MytOrderFee{}
	MytOrderFee.TotalFee = total
	MytOrderFee.SendFee = cast.ToInt(m.Freight)
	MytOrderFee.PackageFee = cast.ToInt(m.PackingCost)
	MytOrderFee.ShopFee = total
	MytOrderFee.UserFee = total
	MytOrderFee.PayType = 2
	MytOrderFee.NeedInvoice = false
	//MytOrderFee.Invoice
	//MytOrderFee.Commission=0
	//MytOrderFee.Activity
	MytOrderFee.IsFirst = false
	MytOrderFee.IsFavorite = false
	mytOrder.OrderFee = MytOrderFee

	//订单商品信息
	if Products, exists := orderProductMap[m.OrderSn]; exists {
		for _, Product := range Products {
			orderGoods := dto.MytOrderGoods{
				GoodsID:              Product.ProductId,
				GoodsName:            Product.ProductName,
				Thumb:                Product.Image,
				SKUId:                Product.SkuId,
				Unit:                 Product.Specs,
				Weight:               kit.YuanToFen(Product.WeightForUnit), // 以克为单位
				UPC:                  Product.BarCode,
				ShelfNo:              Product.LocationCode,
				Number:               cast.ToInt(Product.Number),
				GoodsPrice:           cast.ToInt(Product.PayPrice),     // 以分为单位
				GoodsTotalFee:        cast.ToInt(Product.PaymentTotal), // 总费用
				PackageNumber:        0,
				PackagePrice:         0, // 包装费用
				PackageTotalFee:      0, // 包装总费用
				ReduceFee:            0,
				DiscountFee:          0,
				DiscountPlatformFee:  0,
				DiscountMerchantFee:  0,
				DiscountAgentFee:     0,
				DiscountLogisticsFee: 0,
				TotalFee:             cast.ToInt(Product.PaymentTotal), // 最终总费用
			}
			item.OrderGoods = append(item.OrderGoods, orderGoods)

		}
	}

	//客户信息
	customer := dto.Customer{}
	customer.OrderPhone = utils.MobileDecrypt(m.EnMemberTel)
	customer.RealName = m.ReceiverName
	customer.Phone = utils.MobileDecrypt(m.EnReceiverPhone)
	customer.ReservePhone = utils.MobileDecrypt(m.EnReceiverPhone)
	customer.SecretPhone = m.ReceiverPhone
	customer.Address = m.ReceiverAddress
	customer.Longitude = m.Lng
	customer.Latitude = m.Lat
	item.OrderCustomer = customer

	item.Order = mytOrder
	jsonData, err := json.Marshal(item)
	if err != nil {
		glog.Error(req, ", 序列化失败, ", err)
		resp.Message = "序列化失败" + err.Error()
		return resp, nil
	}
	resp.Data = string(jsonData)
	resp.Message = "ok"
	resp.Code = 200
	return resp, nil
}

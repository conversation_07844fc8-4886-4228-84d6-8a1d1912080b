package models

import (
	"time"
)

type DelOrderPayinfo struct {
	PayId     string    `xorm:"not null pk default ''uuid()'' VARCHAR(50)"`
	OrderSn   string    `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	PaySn     string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	PayMode   int       `xorm:"not null default 0 comment('1支付宝  2微信 3美团 4其他') TINYINT(4)"`
	PayAmount int       `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	PayTime   time.Time `xorm:"default 'NULL' comment('支付时间') DATETIME"`
}

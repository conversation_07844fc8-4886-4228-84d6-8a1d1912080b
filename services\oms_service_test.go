package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/models"
	pt "order-center/proto/oc"
	"reflect"
	"testing"
)

func TestOmsService_OmsOrderDelivery(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *pt.OmsOrderDeliveryRequest
	}

	//定义模拟退货单
	var omsOrders []*pt.OmsOrder
	var omsOrderGoods []*pt.OmsOrderGood
	omsOrderGoods = append(omsOrderGoods, &pt.OmsOrderGood{
		Spu:      "",
		Sku:      "100165",
		Thirdspu: "",
		Thirdsku: "",
		Stock:    1,
	})
	omsOrders = append(omsOrders, &pt.OmsOrder{
		Orderid:          "1603699528153943",
		DeliveryBillCode: "DO234447786679040",
		DeliveryDate:     "2020-10-26 16:07:47",
		LogisticsCode:    "SF",
		LogisticsCompany: "顺丰",
		Goodslist:        omsOrderGoods,
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.OmsOrderDeliveryResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				params: &pt.OmsOrderDeliveryRequest{
					Omsorders: omsOrders,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			db := GetDBConn()
			repush := make([]models.RefundOrder, 0)
			db.ShowSQL()
			err := db.Where("order_sn = ? AND push_third = 0 and is_virtual=0", "4100000017963961").Find(&repush)
			if err != nil {

			}

			fmt.Println(repush)
			//row, err := GetWaitRedoTask(7, 1)

			//redoTask := new(models.OrderRedoTask)
			//redoTask.RedoType = models.RedoTypeOMSOrder //重试类型 1：正向订单重推巨益OMS 2:退款订单重新推送巨益OMS
			//redoTask.OrderSn = "111111121212"
			//redoTask.Params = "22222222"
			////redoTask.UpdateTime = time.Now()
			////redoTask.CreateTime = time.Now()
			////下次执行时间
			//nextRedoDuration := utils.GetNextOrderRedoTimeDuration(0)
			//redoTask.NextRedoTime = time.Now().Add(nextRedoDuration)
			//ReDoTaskAdd(redoTask)

			//updateTask := new(models.OrderRedoTask)
			//updateTask.Id = 1
			//repo := GetDBConn()
			//updateTask.RedoCount += 1
			//nextRedoDuration1 := utils.GetNextOrderRedoTimeDuration(updateTask.RedoCount)
			//updateTask.NextRedoTime = row[0].NextRedoTime.Add(nextRedoDuration1)
			////updateTask.UpdateTime = time.Now()
			//repo.ShowSQL()
			//_, err = repo.Where("id=?", updateTask.Id).Update(updateTask)
			//if err != nil {
			//
			//}

			omss := &OmsService{}
			got, err := omss.OmsOrderDelivery(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OmsOrderDelivery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OmsOrderDelivery() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOmsService_AfterOrderSynchronizeToOms(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *pt.AfterorderRequest
	}

	//定义模拟订单
	var details []*pt.OrderAfterorderDetails
	details = append(details, &pt.OrderAfterorderDetails{
		Oid: "d1437642813246a5919434f26ee2f14b", //自建商城发货单订单明细编号
		//Eshopgoodsname: "阿闻平台",         //网店商品名称
		//Eshopskuname:   "测试商品102105 L", //网店商品SKU名称
		Backqty:   "4",         //退货数量
		Backtotal: "36.00",     //退款金额
		Outeriid:  "bayer-001", //规格商家编码
	})
	//details = append(details, &pt.OrderAfterorderDetails{
	//	Oid:            "a2914b51d204480086e1a110bbcd6b29",      //自建商城发货单订单明细编号
	//	//Eshopgoodsname: "瑞鹏福强店", //网店商品名称
	//	//Eshopskuname:   "大号狗粮",  //网店商品SKU名称
	//	Backqty:        "5",     //退货数量
	//	Backtotal:      "0.75",    //退款金额
	//	Outeriid:       "A2020102105", //规格商家编码
	//})
	var orderAfterorders []*pt.OrderAfterorder
	orderAfterorders = append(orderAfterorders, &pt.OrderAfterorder{
		Rtid:           "262101210104094310", //自建商城线上售后单号
		Tid:            "1609316330325237",   //网店订单编号
		Total:          "36.00",              //订单总金额
		Privilege:      "0",                  //订单享受优惠的金额
		Postfee:        "0",                  //运费
		Created:        kit.GetTimeNow(),     //售后单创建时间
		Status:         "WaitAgree",          //售后单状态 WaitAgree= 待审核 Agree=审核同意 Refuse=拒绝 Invalid=作废 UnFinish=待处理完成 Finished=处理完成 AgreeInvalid=同意作废 RefuseInvalid=拒绝作废
		Aftsaletype:    "JustRefund",         //售后单类型  JustRefund=仅退款 RefundAndGoods=退款退货
		Reasoncode:     "01",                 //售后问题原因代码 01=无理由退换货 02=质量问题 03=损坏 04=错发 05=漏发
		Logistbillcode: "",                   //物流单号
		Aftsaleremark:  "包装破损",               //售后单备注
		Details:        details,              //售后单商品信息
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pt.AfterorderResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: nil,
				params: &pt.AfterorderRequest{
					Orders: orderAfterorders,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			omss := &OmsService{}
			app := pt.AfterorderRequest{}
			str := `{"Orders":[{"Rtid":"50000132526","Tid":"4100000015039493","Total":"112","Privilege":"30.00","Postfee":"0.00","Created":"2022-05-11 15:40:47","Status":"finished","Aftsaletype":"RefundAndGoods","Reasoncode":"01","Logistbillcode":"","Aftsaleremark":"","Details":[{"Oid":"9394380","Eshopgoodsname":"","Eshopskuname":"","Backqty":"1","Backtotal":"58.58","Outeriid":"1047018001"},{"Oid":"9394381","Eshopgoodsname":"","Eshopskuname":"","Backqty":"1","Backtotal":"21.96","Outeriid":"1047021001"},{"Oid":"9394382","Eshopgoodsname":"","Eshopskuname":"","Backqty":"1","Backtotal":"1.46","Outeriid":"112787"}]}]}`
			err := json.Unmarshal([]byte(str), &app)
			if err != nil {
				fmt.Print(err.Error())
			}
			got, err := omss.AfterOrderSynchronizeToOms(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("AfterOrderSynchronizeToOms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AfterOrderSynchronizeToOms() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOmsService_OmsOrderSynchronize(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *dto.DeliveryOrderCreateRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *dto.OmtBaseResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				params: &dto.DeliveryOrderCreateRequest{
					DeliveryOrderRequest: &dto.DeliveryOrderRequest{
						DeliveryOrderCode: "WY1234",
						OrderType:         "JYCK",
						WarehouseCode:     "OTHER",
						CreateTime:        "2020-10-16 15:40:00",
						PlaceOrderTime:    "2020-10-16 15:40:00",
						OperateTime:       "2020-10-16 15:40:00",
						ShopCode:          "rp001",
						ShopNick:          "阿闻平台",
						LogisticsCode:     "OTHER",
						//SenderInfo: dto.SenderInfo {
						//	Name: "巨星",
						//	Mobile: "12345678911",
						//	Province: "广东省",
						//	City: "深圳市",
						//	Area: "福田区",
						//	DetailAddress: "深圳市福田区京基时代大厦",
						//},
						ReceiverInfo: dto.ReceiverInfo{
							Name:          "巨星",
							Mobile:        "12345678911",
							Province:      "广东省",
							City:          "深圳市",
							Area:          "福田区",
							DetailAddress: "深圳市福田区京基时代大厦",
						},
					},
					OrderLines: []*dto.OrderLineRequest{
						&dto.OrderLineRequest{
							OwnerCode:   "WY123456",
							ItemCode:    "WY123456",
							PlanQty:     2,
							ItemName:    "好吃的",
							ActualPrice: "1.00",
						},
						&dto.OrderLineRequest{
							OwnerCode:   "WY654321",
							ItemCode:    "WY654321",
							PlanQty:     1,
							ItemName:    "好喝的",
							ActualPrice: "2.00",
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonByte := []byte(`{"XMLName":{"Space":"","Local":""},"DeliveryOrderRequest":{"DeliveryOrderCode":"4000000006328230","PreDeliveryOrderCode":"","PreDeliveryOrderId":"","OrderType":"JYCK","WarehouseCode":"JD01","OrderFlag":"","SourcePlatformCode":"","SourcePlatformName":"","CreateTime":"2021-04-09 23:02:38","PlaceOrderTime":"2021-04-09 23:02:38","PayTime":"2021-04-09 23:02:37 +0800 CST","PayNo":"20210409230229775091855009684846","OperatorCode":"","OperatorName":"","OperateTime":"2021-04-09 23:02:38","ShopCode":"JD01","ShopNick":"","SellerNick":"","BuyerNick":"","TotalAmount":"","ItemAmount":"","DiscountAmount":"","Freight":"","ArAmount":"","GotAmount":"","ServiceFee":"","LogisticsCode":"OTHER","LogisticsName":"","ExpressCode":"","LogisticsAreaCode":"","DeliveryRequirements":{"ScheduleType":0,"ScheduleDay":"","ScheduleStartTime":"","ScheduleEndTime":"","DeliveryType":""},"SenderInfo":{"Company":"","Name":"","ZipCode":"","Tel":"","Mobile":"","Email":"","CountryCode":"","Province":"","City":"","Area":"","Town":"","DetailAddress":""},"ReceiverInfo":{"Company":"","Name":"张安琪","ZipCode":"","Tel":"18292022034","Mobile":"18292022034","IdType":"","IdNumber":"","Email":"","CountryCode":"","Province":"陕西省","City":"西安市","Area":"雁塔区","Town":"","DetailAddress":"陕西省 西安市 雁塔区曲江新区曲江池东路988号西安凯悦酒店礼宾部"},"IsUrgency":"","InvoiceFlag":"","Invoices":null,"InsuranceFlag":"","Insurance":{"Type":"","Amount":""},"BuyerMessage":"","SellerMessage":"","Remark":"","ServiceCode":"","OwnerCode":"","LatestCollectionTime":"","LatestDeliveryTime":""},"OrderLines":[{"OrderLineNo":"","SourceOrderCode":"","SubSourceOrderCode":"","PayNo":"","OwnerCode":"1018311001","ItemCode":"","ItemId":"1018311001","InventoryType":"","ItemName":"博来恩2.5kg以内猫用 体内体外驱虫药滴剂（透明盒简装单支）（效期21年9月1日）） 单支拆售","ExtCode":"","PlanQty":2,"RetailPrice":"98.00","ActualPrice":"98.00","DiscountAmount":"","BatchCode":"","ProductDate":"","ExpireDate":"","ProduceCode":""}],"ExtendProps":null}`)
			json.Unmarshal(jsonByte, tt.args.params)

			omss := &OmsService{}
			got, err := omss.OmsOrderSynchronize(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("OmsOrderSynchronize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OmsOrderSynchronize() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOmsService_OmsOrderSynchronizeByOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		order *models.OrderMain
	}

	db := GetDBConn()
	var order models.OrderMain
	db.OrderBy("id desc").Get(&order)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *dto.DeliveryOrderCreateResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			fields: fields{CommonService: CommonService{session: db.NewSession()}},
			args:   args{order: &order},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ac := OmsService{
				CommonService: tt.fields.CommonService,
			}
			got, err := ac.OmsOrderSynchronizeByOrder(tt.args.order)
			if !tt.wantErr(t, err, fmt.Sprintf("OmsOrderSynchronizeByOrder(%v)", tt.args.order)) {
				return
			}
			assert.Equalf(t, tt.want, got, "OmsOrderSynchronizeByOrder(%v)", tt.args.order)
		})
	}
}

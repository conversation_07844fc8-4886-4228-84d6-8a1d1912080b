package models

import (
	"time"
)

type OrderSubscribeMessage struct {
	Id         int       `xorm:"not null pk autoincr comment('自增ID') INT(11)"`
	OrderSn    string    `xorm:"default '''' comment('订单ID') VARCHAR(55)"`
	OpenId     string    `xorm:"default 'NULL' comment('小程序ID') VARCHAR(55)"`
	UserId     string    `xorm:"default 'NULL' comment('用户ID') VARCHAR(55)"`
	TemplateId string    `xorm:"default '''' comment('微信模板ID') VARCHAR(55)"`
	Type       int       `xorm:"default NULL comment('消息类型：1拼团成功 2拼团失败 3订单状态 4售后单状态 5退款成功 6售后单审核不通过') INT(11)"`
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

// 微信消息订阅模板
type WechatSubscribeMessageTemplate struct {
	Id          int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	TemplateKey string    `json:"template_key" xorm:"not null default '' comment('微信消息key') VARCHAR(100) 'template_key'"`
	TemplateId  string    `json:"template_id" xorm:"not null default '' comment('微信消息模板id') VARCHAR(100) 'template_id'"`
	Content     string    `json:"content" xorm:"not null default '' comment('消息内容') VARCHAR(255) 'content'"`
	Comment     string    `json:"comment" xorm:"not null default '' comment('模板说明') VARCHAR(100) 'comment'"`
	CreateTime  time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime  time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}

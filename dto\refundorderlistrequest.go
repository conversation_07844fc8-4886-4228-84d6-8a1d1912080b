package dto

//退款管理列表
type RefundOrderListRequest struct {
	//订单编号
	OrderSn string `json:"order_sn"`
	//下单开始时间
	Starttime string `json:"starttime"`
	//下单结束时间
	Endtime string `json:"endtime"`
	//退款编号
	Refundsn string `json:"refundsn"`
	//退款类型1用户 2商家 3客服
	UserType int32 `json:"user_type"`
	//退款方式:1为退款,2为退货，默认为1
	Refundtype int32 `json:"refundtype"`
	//订单退款状态  1:退款中 2:退款关闭 3:退款成功
	RefundState int32 `json:"refund_state"`
	//快递单号
	ExpressNumber string `json:"express_number"`
	//商品名称
	Productname string `json:"productname"`
	//订单类型1实物  2虚拟
	Ordertype int32 `json:"ordertype"`
	//来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
	ChannelId int32 `json:"channel_id"`
	//销售渠道1小程序  2网站  3h5
	Salechannel int32 `json:"salechannel"`
	//登录用户所有权限的门店id
	Shopids []string `json:"shopids"`
	//当前页码
	Pageindex int32 `json:"pageindex"`
	//每页行数
	Pagesize int32 `json:"pagesize"`
	//用户编号
	UserNo string   `json:"UserNo"`
	//店铺类型
	AppChannel int32 `json:"app_channel"`
}
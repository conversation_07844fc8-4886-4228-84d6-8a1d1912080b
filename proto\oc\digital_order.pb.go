// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/digital_order.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//创建订单入参
type CreateDigitalOrderRequest struct {
	BuyUserId            string   `protobuf:"bytes,1,opt,name=buy_user_id,json=buyUserId,proto3" json:"buy_user_id"`
	SellUserId           string   `protobuf:"bytes,2,opt,name=sell_user_id,json=sellUserId,proto3" json:"sell_user_id"`
	NftId                string   `protobuf:"bytes,3,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	Price                float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	Status               int32    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	SellType             int32    `protobuf:"varint,6,opt,name=sell_type,json=sellType,proto3" json:"sell_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDigitalOrderRequest) Reset()         { *m = CreateDigitalOrderRequest{} }
func (m *CreateDigitalOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CreateDigitalOrderRequest) ProtoMessage()    {}
func (*CreateDigitalOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{0}
}

func (m *CreateDigitalOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDigitalOrderRequest.Unmarshal(m, b)
}
func (m *CreateDigitalOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDigitalOrderRequest.Marshal(b, m, deterministic)
}
func (m *CreateDigitalOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDigitalOrderRequest.Merge(m, src)
}
func (m *CreateDigitalOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CreateDigitalOrderRequest.Size(m)
}
func (m *CreateDigitalOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDigitalOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDigitalOrderRequest proto.InternalMessageInfo

func (m *CreateDigitalOrderRequest) GetBuyUserId() string {
	if m != nil {
		return m.BuyUserId
	}
	return ""
}

func (m *CreateDigitalOrderRequest) GetSellUserId() string {
	if m != nil {
		return m.SellUserId
	}
	return ""
}

func (m *CreateDigitalOrderRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *CreateDigitalOrderRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CreateDigitalOrderRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CreateDigitalOrderRequest) GetSellType() int32 {
	if m != nil {
		return m.SellType
	}
	return 0
}

//返回值
type DigitalOrderResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DigitalOrderResponse) Reset()         { *m = DigitalOrderResponse{} }
func (m *DigitalOrderResponse) String() string { return proto.CompactTextString(m) }
func (*DigitalOrderResponse) ProtoMessage()    {}
func (*DigitalOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{1}
}

func (m *DigitalOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalOrderResponse.Unmarshal(m, b)
}
func (m *DigitalOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalOrderResponse.Marshal(b, m, deterministic)
}
func (m *DigitalOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalOrderResponse.Merge(m, src)
}
func (m *DigitalOrderResponse) XXX_Size() int {
	return xxx_messageInfo_DigitalOrderResponse.Size(m)
}
func (m *DigitalOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalOrderResponse proto.InternalMessageInfo

func (m *DigitalOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DigitalOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DigitalOrderResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//更新订单参数
type UpdateDigitalOrderRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	PaySn                string   `protobuf:"bytes,3,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	PayAmount            float32  `protobuf:"fixed32,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDigitalOrderRequest) Reset()         { *m = UpdateDigitalOrderRequest{} }
func (m *UpdateDigitalOrderRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateDigitalOrderRequest) ProtoMessage()    {}
func (*UpdateDigitalOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{2}
}

func (m *UpdateDigitalOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDigitalOrderRequest.Unmarshal(m, b)
}
func (m *UpdateDigitalOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDigitalOrderRequest.Marshal(b, m, deterministic)
}
func (m *UpdateDigitalOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDigitalOrderRequest.Merge(m, src)
}
func (m *UpdateDigitalOrderRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateDigitalOrderRequest.Size(m)
}
func (m *UpdateDigitalOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDigitalOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDigitalOrderRequest proto.InternalMessageInfo

func (m *UpdateDigitalOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UpdateDigitalOrderRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateDigitalOrderRequest) GetPaySn() string {
	if m != nil {
		return m.PaySn
	}
	return ""
}

func (m *UpdateDigitalOrderRequest) GetPayAmount() float32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

//支付数字藏品订单参数
type PayDigitalOrderRequest struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	UserId               string   `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayDigitalOrderRequest) Reset()         { *m = PayDigitalOrderRequest{} }
func (m *PayDigitalOrderRequest) String() string { return proto.CompactTextString(m) }
func (*PayDigitalOrderRequest) ProtoMessage()    {}
func (*PayDigitalOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{3}
}

func (m *PayDigitalOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayDigitalOrderRequest.Unmarshal(m, b)
}
func (m *PayDigitalOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayDigitalOrderRequest.Marshal(b, m, deterministic)
}
func (m *PayDigitalOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayDigitalOrderRequest.Merge(m, src)
}
func (m *PayDigitalOrderRequest) XXX_Size() int {
	return xxx_messageInfo_PayDigitalOrderRequest.Size(m)
}
func (m *PayDigitalOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayDigitalOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayDigitalOrderRequest proto.InternalMessageInfo

func (m *PayDigitalOrderRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *PayDigitalOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PayDigitalOrderRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

//支付数字藏品订单返回值
type PayDigitalOrderResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayDigitalOrderResponse) Reset()         { *m = PayDigitalOrderResponse{} }
func (m *PayDigitalOrderResponse) String() string { return proto.CompactTextString(m) }
func (*PayDigitalOrderResponse) ProtoMessage()    {}
func (*PayDigitalOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{4}
}

func (m *PayDigitalOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayDigitalOrderResponse.Unmarshal(m, b)
}
func (m *PayDigitalOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayDigitalOrderResponse.Marshal(b, m, deterministic)
}
func (m *PayDigitalOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayDigitalOrderResponse.Merge(m, src)
}
func (m *PayDigitalOrderResponse) XXX_Size() int {
	return xxx_messageInfo_PayDigitalOrderResponse.Size(m)
}
func (m *PayDigitalOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayDigitalOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayDigitalOrderResponse proto.InternalMessageInfo

func (m *PayDigitalOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayDigitalOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayDigitalOrderResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//支付回调参数
type PayDigitalOrderNotifyRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	PaySn                string   `protobuf:"bytes,3,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	PayAmount            float32  `protobuf:"fixed32,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayDigitalOrderNotifyRequest) Reset()         { *m = PayDigitalOrderNotifyRequest{} }
func (m *PayDigitalOrderNotifyRequest) String() string { return proto.CompactTextString(m) }
func (*PayDigitalOrderNotifyRequest) ProtoMessage()    {}
func (*PayDigitalOrderNotifyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{5}
}

func (m *PayDigitalOrderNotifyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayDigitalOrderNotifyRequest.Unmarshal(m, b)
}
func (m *PayDigitalOrderNotifyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayDigitalOrderNotifyRequest.Marshal(b, m, deterministic)
}
func (m *PayDigitalOrderNotifyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayDigitalOrderNotifyRequest.Merge(m, src)
}
func (m *PayDigitalOrderNotifyRequest) XXX_Size() int {
	return xxx_messageInfo_PayDigitalOrderNotifyRequest.Size(m)
}
func (m *PayDigitalOrderNotifyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayDigitalOrderNotifyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayDigitalOrderNotifyRequest proto.InternalMessageInfo

func (m *PayDigitalOrderNotifyRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PayDigitalOrderNotifyRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PayDigitalOrderNotifyRequest) GetPaySn() string {
	if m != nil {
		return m.PaySn
	}
	return ""
}

func (m *PayDigitalOrderNotifyRequest) GetPayAmount() float32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

type OrderPayTestRequest struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderPayTestRequest) Reset()         { *m = OrderPayTestRequest{} }
func (m *OrderPayTestRequest) String() string { return proto.CompactTextString(m) }
func (*OrderPayTestRequest) ProtoMessage()    {}
func (*OrderPayTestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{6}
}

func (m *OrderPayTestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPayTestRequest.Unmarshal(m, b)
}
func (m *OrderPayTestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPayTestRequest.Marshal(b, m, deterministic)
}
func (m *OrderPayTestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPayTestRequest.Merge(m, src)
}
func (m *OrderPayTestRequest) XXX_Size() int {
	return xxx_messageInfo_OrderPayTestRequest.Size(m)
}
func (m *OrderPayTestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPayTestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPayTestRequest proto.InternalMessageInfo

func (m *OrderPayTestRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

//计算支付金额参数
type CalcOrderPaymentRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	Start                string   `protobuf:"bytes,2,opt,name=start,proto3" json:"start"`
	End                  string   `protobuf:"bytes,3,opt,name=end,proto3" json:"end"`
	Price                float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalcOrderPaymentRequest) Reset()         { *m = CalcOrderPaymentRequest{} }
func (m *CalcOrderPaymentRequest) String() string { return proto.CompactTextString(m) }
func (*CalcOrderPaymentRequest) ProtoMessage()    {}
func (*CalcOrderPaymentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{7}
}

func (m *CalcOrderPaymentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalcOrderPaymentRequest.Unmarshal(m, b)
}
func (m *CalcOrderPaymentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalcOrderPaymentRequest.Marshal(b, m, deterministic)
}
func (m *CalcOrderPaymentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalcOrderPaymentRequest.Merge(m, src)
}
func (m *CalcOrderPaymentRequest) XXX_Size() int {
	return xxx_messageInfo_CalcOrderPaymentRequest.Size(m)
}
func (m *CalcOrderPaymentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalcOrderPaymentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalcOrderPaymentRequest proto.InternalMessageInfo

func (m *CalcOrderPaymentRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *CalcOrderPaymentRequest) GetStart() string {
	if m != nil {
		return m.Start
	}
	return ""
}

func (m *CalcOrderPaymentRequest) GetEnd() string {
	if m != nil {
		return m.End
	}
	return ""
}

func (m *CalcOrderPaymentRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

//计算支付金额返回值
type CalcOrderPaymentResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Flag                 int32    `protobuf:"varint,3,opt,name=flag,proto3" json:"flag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalcOrderPaymentResponse) Reset()         { *m = CalcOrderPaymentResponse{} }
func (m *CalcOrderPaymentResponse) String() string { return proto.CompactTextString(m) }
func (*CalcOrderPaymentResponse) ProtoMessage()    {}
func (*CalcOrderPaymentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{8}
}

func (m *CalcOrderPaymentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalcOrderPaymentResponse.Unmarshal(m, b)
}
func (m *CalcOrderPaymentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalcOrderPaymentResponse.Marshal(b, m, deterministic)
}
func (m *CalcOrderPaymentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalcOrderPaymentResponse.Merge(m, src)
}
func (m *CalcOrderPaymentResponse) XXX_Size() int {
	return xxx_messageInfo_CalcOrderPaymentResponse.Size(m)
}
func (m *CalcOrderPaymentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalcOrderPaymentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalcOrderPaymentResponse proto.InternalMessageInfo

func (m *CalcOrderPaymentResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CalcOrderPaymentResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CalcOrderPaymentResponse) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

type FindOrderProductRequest struct {
	SkuStr               string   `protobuf:"bytes,1,opt,name=sku_str,json=skuStr,proto3" json:"sku_str"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	Start                string   `protobuf:"bytes,3,opt,name=start,proto3" json:"start"`
	End                  string   `protobuf:"bytes,4,opt,name=end,proto3" json:"end"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FindOrderProductRequest) Reset()         { *m = FindOrderProductRequest{} }
func (m *FindOrderProductRequest) String() string { return proto.CompactTextString(m) }
func (*FindOrderProductRequest) ProtoMessage()    {}
func (*FindOrderProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{9}
}

func (m *FindOrderProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindOrderProductRequest.Unmarshal(m, b)
}
func (m *FindOrderProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindOrderProductRequest.Marshal(b, m, deterministic)
}
func (m *FindOrderProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindOrderProductRequest.Merge(m, src)
}
func (m *FindOrderProductRequest) XXX_Size() int {
	return xxx_messageInfo_FindOrderProductRequest.Size(m)
}
func (m *FindOrderProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindOrderProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindOrderProductRequest proto.InternalMessageInfo

func (m *FindOrderProductRequest) GetSkuStr() string {
	if m != nil {
		return m.SkuStr
	}
	return ""
}

func (m *FindOrderProductRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *FindOrderProductRequest) GetStart() string {
	if m != nil {
		return m.Start
	}
	return ""
}

func (m *FindOrderProductRequest) GetEnd() string {
	if m != nil {
		return m.End
	}
	return ""
}

type FindOrderProductResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Flag                 int32    `protobuf:"varint,3,opt,name=flag,proto3" json:"flag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FindOrderProductResponse) Reset()         { *m = FindOrderProductResponse{} }
func (m *FindOrderProductResponse) String() string { return proto.CompactTextString(m) }
func (*FindOrderProductResponse) ProtoMessage()    {}
func (*FindOrderProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41bb75cda64e88fd, []int{10}
}

func (m *FindOrderProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindOrderProductResponse.Unmarshal(m, b)
}
func (m *FindOrderProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindOrderProductResponse.Marshal(b, m, deterministic)
}
func (m *FindOrderProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindOrderProductResponse.Merge(m, src)
}
func (m *FindOrderProductResponse) XXX_Size() int {
	return xxx_messageInfo_FindOrderProductResponse.Size(m)
}
func (m *FindOrderProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FindOrderProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FindOrderProductResponse proto.InternalMessageInfo

func (m *FindOrderProductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FindOrderProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FindOrderProductResponse) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

func init() {
	proto.RegisterType((*CreateDigitalOrderRequest)(nil), "oc.CreateDigitalOrderRequest")
	proto.RegisterType((*DigitalOrderResponse)(nil), "oc.DigitalOrderResponse")
	proto.RegisterType((*UpdateDigitalOrderRequest)(nil), "oc.UpdateDigitalOrderRequest")
	proto.RegisterType((*PayDigitalOrderRequest)(nil), "oc.PayDigitalOrderRequest")
	proto.RegisterType((*PayDigitalOrderResponse)(nil), "oc.PayDigitalOrderResponse")
	proto.RegisterType((*PayDigitalOrderNotifyRequest)(nil), "oc.PayDigitalOrderNotifyRequest")
	proto.RegisterType((*OrderPayTestRequest)(nil), "oc.OrderPayTestRequest")
	proto.RegisterType((*CalcOrderPaymentRequest)(nil), "oc.CalcOrderPaymentRequest")
	proto.RegisterType((*CalcOrderPaymentResponse)(nil), "oc.CalcOrderPaymentResponse")
	proto.RegisterType((*FindOrderProductRequest)(nil), "oc.FindOrderProductRequest")
	proto.RegisterType((*FindOrderProductResponse)(nil), "oc.FindOrderProductResponse")
}

func init() { proto.RegisterFile("oc/digital_order.proto", fileDescriptor_41bb75cda64e88fd) }

var fileDescriptor_41bb75cda64e88fd = []byte{
	// 589 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x55, 0xdf, 0x6a, 0xdb, 0x3e,
	0x14, 0xc6, 0x4e, 0xec, 0x34, 0xe7, 0x57, 0xf8, 0x0d, 0xb5, 0x89, 0x9d, 0x74, 0x1d, 0xc1, 0x57,
	0xb9, 0x59, 0x0a, 0xdb, 0x13, 0x6c, 0x2d, 0x83, 0x0e, 0xba, 0x15, 0xa7, 0xbd, 0xd9, 0x06, 0x46,
	0xb1, 0x95, 0x60, 0x9a, 0x48, 0x9e, 0x24, 0x0f, 0x04, 0xbb, 0x1c, 0xec, 0x1d, 0xf6, 0x32, 0x7b,
	0xb5, 0x21, 0xd9, 0xc9, 0xe2, 0xd8, 0xc9, 0xa0, 0x8c, 0xdd, 0xe9, 0xe8, 0xfc, 0xfb, 0xce, 0x27,
	0x7f, 0xc7, 0xd0, 0x67, 0xf1, 0x45, 0x92, 0x2e, 0x52, 0x89, 0x97, 0x11, 0xe3, 0x09, 0xe1, 0x93,
	0x8c, 0x33, 0xc9, 0x90, 0xcd, 0xe2, 0xe0, 0xa7, 0x05, 0x83, 0x4b, 0x4e, 0xb0, 0x24, 0x57, 0x45,
	0xc4, 0x7b, 0x1d, 0x10, 0x92, 0xcf, 0x39, 0x11, 0x12, 0x3d, 0x83, 0xff, 0x66, 0xb9, 0x8a, 0x72,
	0x41, 0x78, 0x94, 0x26, 0xbe, 0x35, 0xb2, 0xc6, 0xdd, 0xb0, 0x3b, 0xcb, 0xd5, 0xbd, 0x20, 0xfc,
	0x3a, 0x41, 0x23, 0x38, 0x16, 0x64, 0xb9, 0xdc, 0x04, 0xd8, 0x26, 0x00, 0xf4, 0x5d, 0x19, 0xd1,
	0x03, 0x97, 0xce, 0xa5, 0xf6, 0xb5, 0x8c, 0xcf, 0xa1, 0x73, 0x79, 0x9d, 0xa0, 0x53, 0x70, 0x32,
	0x9e, 0xc6, 0xc4, 0x6f, 0x8f, 0xac, 0xb1, 0x1d, 0x16, 0x06, 0xea, 0x83, 0x2b, 0x24, 0x96, 0xb9,
	0xf0, 0x9d, 0x91, 0x35, 0x76, 0xc2, 0xd2, 0x42, 0x67, 0xd0, 0x35, 0x6d, 0xa4, 0xca, 0x88, 0xef,
	0x1a, 0xd7, 0x91, 0xbe, 0xb8, 0x53, 0x19, 0x09, 0x22, 0x38, 0xad, 0x42, 0x17, 0x19, 0xa3, 0x82,
	0x20, 0x04, 0xed, 0x98, 0x25, 0xc4, 0x80, 0x76, 0x42, 0x73, 0x46, 0x3e, 0x74, 0x56, 0x44, 0x08,
	0xbc, 0x20, 0x25, 0xd4, 0xb5, 0x89, 0x06, 0x70, 0x64, 0xa8, 0x89, 0x04, 0x2d, 0x91, 0x76, 0x8c,
	0x3d, 0xa5, 0xc1, 0x37, 0x0b, 0x06, 0xf7, 0x59, 0xb2, 0x87, 0xa2, 0xed, 0x44, 0xab, 0x92, 0xb8,
	0x35, 0x8e, 0x5d, 0x19, 0xa7, 0x07, 0x6e, 0x86, 0xd5, 0xef, 0x4e, 0x4e, 0x86, 0xd5, 0x94, 0xa2,
	0x73, 0x00, 0x7d, 0x8d, 0x57, 0x2c, 0xa7, 0xb2, 0x24, 0xa6, 0x9b, 0x61, 0xf5, 0xca, 0x5c, 0x04,
	0x09, 0xf4, 0x6f, 0xb1, 0x6a, 0x82, 0xd0, 0x07, 0x97, 0x65, 0x84, 0x6e, 0x1e, 0xa8, 0xb4, 0x2a,
	0xd0, 0xec, 0x2a, 0x34, 0x0f, 0x3a, 0xeb, 0x37, 0x2b, 0x30, 0xb8, 0xb9, 0x79, 0xaf, 0xe0, 0x23,
	0x78, 0xb5, 0x2e, 0x8f, 0x22, 0x14, 0x41, 0x3b, 0xc1, 0x12, 0x97, 0xe5, 0xcd, 0x39, 0xf8, 0x6e,
	0xc1, 0xd3, 0x9d, 0xea, 0xef, 0x98, 0x4c, 0xe7, 0xea, 0x9f, 0x93, 0xf9, 0x1c, 0x4e, 0x4c, 0xfb,
	0x5b, 0xac, 0xee, 0x88, 0x90, 0x7f, 0x60, 0x32, 0x50, 0xe0, 0x5d, 0xe2, 0x65, 0xbc, 0x4e, 0x59,
	0x11, 0xba, 0x49, 0xd1, 0x12, 0x88, 0xf9, 0x6a, 0x47, 0x23, 0xa0, 0xef, 0x4a, 0x09, 0x9c, 0x82,
	0x23, 0x24, 0xe6, 0xb2, 0x64, 0xa8, 0x30, 0xd0, 0x13, 0x68, 0x11, 0xba, 0x66, 0x5f, 0x1f, 0x9b,
	0x35, 0x11, 0x7c, 0x02, 0xbf, 0xde, 0xfa, 0xb1, 0x2f, 0x32, 0x5f, 0xe2, 0x85, 0x69, 0xe9, 0x84,
	0xe6, 0x1c, 0x7c, 0x05, 0xef, 0x4d, 0x4a, 0x93, 0xa2, 0x3a, 0x67, 0x49, 0x1e, 0x6f, 0x06, 0xf3,
	0xa0, 0x23, 0x1e, 0xf2, 0x48, 0x48, 0xbe, 0x26, 0x43, 0x3c, 0xe4, 0x53, 0xc9, 0x6b, 0x13, 0xdb,
	0xfb, 0x27, 0x6e, 0x35, 0x4c, 0xdc, 0xde, 0x4c, 0xac, 0x67, 0xab, 0x77, 0xff, 0x5b, 0xb3, 0xbd,
	0xf8, 0xd1, 0x82, 0x93, 0xed, 0x4f, 0x6d, 0x4a, 0xf8, 0x17, 0xbd, 0x65, 0x6e, 0x00, 0xd5, 0x37,
	0x1e, 0x3a, 0x9f, 0xb0, 0x78, 0xb2, 0x77, 0x13, 0x0e, 0x7d, 0xed, 0x6e, 0x94, 0xc5, 0x0d, 0xa0,
	0xfa, 0x76, 0x28, 0xca, 0xed, 0xdd, 0x1a, 0x07, 0xca, 0xbd, 0x85, 0xff, 0x77, 0x24, 0x82, 0x86,
	0x3a, 0xb8, 0x59, 0xfb, 0xc3, 0xb3, 0x46, 0x5f, 0x59, 0x6b, 0x0a, 0xbd, 0x46, 0xb9, 0xa1, 0x51,
	0x43, 0x56, 0x45, 0x89, 0x07, 0x00, 0x5e, 0xc1, 0xf1, 0xb6, 0x74, 0x90, 0xa7, 0x23, 0x1b, 0xc4,
	0x74, 0x10, 0xda, 0x6b, 0xf7, 0x43, 0x7b, 0x72, 0xc1, 0xe2, 0x99, 0x6b, 0x7e, 0x45, 0x2f, 0x7f,
	0x05, 0x00, 0x00, 0xff, 0xff, 0x02, 0xd7, 0x21, 0xc9, 0xa4, 0x06, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DigitalOrderServiceClient is the client API for DigitalOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DigitalOrderServiceClient interface {
	//创建订单
	CreateDigitalOrder(ctx context.Context, in *CreateDigitalOrderRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error)
	//更新订单信息
	UpdateDigitalOrder(ctx context.Context, in *UpdateDigitalOrderRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error)
	//订单支付
	PayDigitalOrder(ctx context.Context, in *PayDigitalOrderRequest, opts ...grpc.CallOption) (*PayDigitalOrderResponse, error)
	//订单支付回调
	PayDigitalOrderNotify(ctx context.Context, in *PayDigitalOrderNotifyRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error)
	//测试支付
	OrderPayTest(ctx context.Context, in *OrderPayTestRequest, opts ...grpc.CallOption) (*PayDigitalOrderResponse, error)
}

type digitalOrderServiceClient struct {
	cc *grpc.ClientConn
}

func NewDigitalOrderServiceClient(cc *grpc.ClientConn) DigitalOrderServiceClient {
	return &digitalOrderServiceClient{cc}
}

func (c *digitalOrderServiceClient) CreateDigitalOrder(ctx context.Context, in *CreateDigitalOrderRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error) {
	out := new(DigitalOrderResponse)
	err := c.cc.Invoke(ctx, "/oc.DigitalOrderService/CreateDigitalOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalOrderServiceClient) UpdateDigitalOrder(ctx context.Context, in *UpdateDigitalOrderRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error) {
	out := new(DigitalOrderResponse)
	err := c.cc.Invoke(ctx, "/oc.DigitalOrderService/UpdateDigitalOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalOrderServiceClient) PayDigitalOrder(ctx context.Context, in *PayDigitalOrderRequest, opts ...grpc.CallOption) (*PayDigitalOrderResponse, error) {
	out := new(PayDigitalOrderResponse)
	err := c.cc.Invoke(ctx, "/oc.DigitalOrderService/PayDigitalOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalOrderServiceClient) PayDigitalOrderNotify(ctx context.Context, in *PayDigitalOrderNotifyRequest, opts ...grpc.CallOption) (*DigitalOrderResponse, error) {
	out := new(DigitalOrderResponse)
	err := c.cc.Invoke(ctx, "/oc.DigitalOrderService/PayDigitalOrderNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalOrderServiceClient) OrderPayTest(ctx context.Context, in *OrderPayTestRequest, opts ...grpc.CallOption) (*PayDigitalOrderResponse, error) {
	out := new(PayDigitalOrderResponse)
	err := c.cc.Invoke(ctx, "/oc.DigitalOrderService/OrderPayTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DigitalOrderServiceServer is the server API for DigitalOrderService service.
type DigitalOrderServiceServer interface {
	//创建订单
	CreateDigitalOrder(context.Context, *CreateDigitalOrderRequest) (*DigitalOrderResponse, error)
	//更新订单信息
	UpdateDigitalOrder(context.Context, *UpdateDigitalOrderRequest) (*DigitalOrderResponse, error)
	//订单支付
	PayDigitalOrder(context.Context, *PayDigitalOrderRequest) (*PayDigitalOrderResponse, error)
	//订单支付回调
	PayDigitalOrderNotify(context.Context, *PayDigitalOrderNotifyRequest) (*DigitalOrderResponse, error)
	//测试支付
	OrderPayTest(context.Context, *OrderPayTestRequest) (*PayDigitalOrderResponse, error)
}

// UnimplementedDigitalOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDigitalOrderServiceServer struct {
}

func (*UnimplementedDigitalOrderServiceServer) CreateDigitalOrder(ctx context.Context, req *CreateDigitalOrderRequest) (*DigitalOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDigitalOrder not implemented")
}
func (*UnimplementedDigitalOrderServiceServer) UpdateDigitalOrder(ctx context.Context, req *UpdateDigitalOrderRequest) (*DigitalOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDigitalOrder not implemented")
}
func (*UnimplementedDigitalOrderServiceServer) PayDigitalOrder(ctx context.Context, req *PayDigitalOrderRequest) (*PayDigitalOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayDigitalOrder not implemented")
}
func (*UnimplementedDigitalOrderServiceServer) PayDigitalOrderNotify(ctx context.Context, req *PayDigitalOrderNotifyRequest) (*DigitalOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayDigitalOrderNotify not implemented")
}
func (*UnimplementedDigitalOrderServiceServer) OrderPayTest(ctx context.Context, req *OrderPayTestRequest) (*PayDigitalOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderPayTest not implemented")
}

func RegisterDigitalOrderServiceServer(s *grpc.Server, srv DigitalOrderServiceServer) {
	s.RegisterService(&_DigitalOrderService_serviceDesc, srv)
}

func _DigitalOrderService_CreateDigitalOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDigitalOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalOrderServiceServer).CreateDigitalOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.DigitalOrderService/CreateDigitalOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalOrderServiceServer).CreateDigitalOrder(ctx, req.(*CreateDigitalOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalOrderService_UpdateDigitalOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDigitalOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalOrderServiceServer).UpdateDigitalOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.DigitalOrderService/UpdateDigitalOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalOrderServiceServer).UpdateDigitalOrder(ctx, req.(*UpdateDigitalOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalOrderService_PayDigitalOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayDigitalOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalOrderServiceServer).PayDigitalOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.DigitalOrderService/PayDigitalOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalOrderServiceServer).PayDigitalOrder(ctx, req.(*PayDigitalOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalOrderService_PayDigitalOrderNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayDigitalOrderNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalOrderServiceServer).PayDigitalOrderNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.DigitalOrderService/PayDigitalOrderNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalOrderServiceServer).PayDigitalOrderNotify(ctx, req.(*PayDigitalOrderNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalOrderService_OrderPayTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPayTestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalOrderServiceServer).OrderPayTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.DigitalOrderService/OrderPayTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalOrderServiceServer).OrderPayTest(ctx, req.(*OrderPayTestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DigitalOrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.DigitalOrderService",
	HandlerType: (*DigitalOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDigitalOrder",
			Handler:    _DigitalOrderService_CreateDigitalOrder_Handler,
		},
		{
			MethodName: "UpdateDigitalOrder",
			Handler:    _DigitalOrderService_UpdateDigitalOrder_Handler,
		},
		{
			MethodName: "PayDigitalOrder",
			Handler:    _DigitalOrderService_PayDigitalOrder_Handler,
		},
		{
			MethodName: "PayDigitalOrderNotify",
			Handler:    _DigitalOrderService_PayDigitalOrderNotify_Handler,
		},
		{
			MethodName: "OrderPayTest",
			Handler:    _DigitalOrderService_OrderPayTest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/digital_order.proto",
}

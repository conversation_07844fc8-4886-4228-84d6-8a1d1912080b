package tasks

import (
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"order-center/models"
	"order-center/services"
	"time"
)

//社区团购活动佣金计算定时任务
func groupAactivityDisTask() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "order-center:task:lock:group_activity_dis"
	if !redisConn.SetNX(lockCard, time.Now().Unix(), 20 * time.Minute).Val() {
		glog.Info("task is already running")
		return
	}
	defer redisConn.Del(lockCard)

	//订单佣金计算
	groupAactivityDis()
}

func groupAactivityDis() {
	//连接池勿关闭
	db := services.GetDBConn()
	session := db.NewSession()
	defer session.Close()
	date := time.Now().AddDate(0, 0, -1).Format("2006-01-02 15:04:05")
	var i = 1
	for {
		var orderInfo []models.OrderProductExt
		if err := session.Table("order_product").Alias("op").Select("op.id, om.parent_order_sn, op.order_sn, op.product_id, op.sku_id, op.product_name, op.image, op.product_type, op.combine_type, op.payment_total, op.commission_rate, op.refund_num, om.is_virtual, op.number, op.pay_price").
			Join("LEFT", "order_main om", "op.order_sn = om.order_sn").
			Where("om.order_status = 30 AND om.order_type = 15 AND (om.confirm_time <= ? OR om.is_virtual = 1) AND op.is_distribute = 1 AND op.is_settlement = 0 AND op.commission_rate > 0 AND parent_order_sn <> ''", date).Limit(500, (i - 1) * 500).OrderBy("op.id").Find(&orderInfo); err != nil {
			glog.Error("社区团购活动订单佣金计算定时任务失败1，错误：", err.Error())
			break
		} else if len(orderInfo) == 0 {
			glog.Info("社区团购活动订单佣金计算定时任务执行结束")
			break
		}

		for _, v := range orderInfo {
			session.Begin()
			glog.Info("社区团购活动订单佣金计算定时任务执行，订单ID：", v.ParentOrderSn)
			var isVirtual = 0
            if v.ProductType == 2 || (v.ProductType == 3 && (v.CombineType == 2 || v.CombineType == 3)) {
				isVirtual = 1
			}
			//取分销人
			var member_id = ""
			if _, err := db.Select("oga.member_id").Table("order_main_group").Alias("omg").Join("LEFT", "order_group_activity oga", "oga.id = omg.order_group_activity_id").
				Where("omg.parent_order_sn = ? AND oga.`status` = 1", v.ParentOrderSn).Get(&member_id); err != nil {
				glog.Error("社区团购活动订单佣金计算定时任务失败2，错误：", v.OrderSn, err.Error())
				session.Rollback()
				continue
			} else if member_id == "" {
                //只统计成功的团
				session.Rollback()
				continue
			}

			//查询退款金额
			var refundMoney int32 = 0
			if v.RefundNum > 0 {
				/*var refundOrder []models.RefundOrder
				if err := session.Select("refund_amount").Where("order_sn = ? AND refund_state = 3", v.OrderSn).Find(&refundOrder); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败3，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				} else {
					for _, val := range refundOrder {
						refundMoney += cast.ToInt32(cast.ToFloat32(val.RefundAmount) * 100)
					}
				}*/
				refundMoney = v.RefundNum * v.PayPrice
			}

			var commission = 0
			if v.IsVirtual == 1 {
				//虚拟商品
				//取已核销数量
				countCode, err := session.Table("order_verify_code").Where("verify_status = 1 AND order_sn = ?", v.OrderSn).Count()
				if err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败4，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				} else if countCode == 0 {
					continue
				}
				//取已结算数量
				var OrderDis models.OrderDis
                if hasOrderDis, err := session.Select("id, verify_num, commission").Table("order_dis").Where("order_id = ?", v.Id).Get(&OrderDis); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败5，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				} else if !hasOrderDis {
					//有已核销且没有佣金记录的
					if v.Number == cast.ToInt32(countCode) {
						commission = cast.ToInt(cast.ToFloat32(v.CommissionRate) / 100 * cast.ToFloat32(v.PaymentTotal))
					} else {
						commission = cast.ToInt(cast.ToFloat32(v.CommissionRate) / 100 * cast.ToFloat32(v.PayPrice * cast.ToInt32(countCode)))
					}
					//新增
					if _, err := session.Insert(&models.OrderDis{
						OrderId: cast.ToInt(v.Id), ParentOrderSn: v.ParentOrderSn, OrderSn: v.OrderSn, ProductId: v.ProductId, SkuId: v.SkuId, ProductName: v.ProductName, Image: v.Image,
						PayGoodsAmount: cast.ToInt(v.PaymentTotal), DisCommisRate: cast.ToInt(v.CommissionRate), Commission: commission, VerifyNum: cast.ToInt(countCode),
						IsVirtual: isVirtual, CreateTime: time.Now().Local(), DisMemberId: member_id, RefundAmount: cast.ToInt(refundMoney),
					}); err != nil {
						glog.Error("社区团购活动订单佣金计算定时任务失败6，错误：", v.OrderSn, err.Error())
						session.Rollback()
						continue
					}
					if v.Number - v.RefundNum <= cast.ToInt32(countCode) {
						//已核销完
						if _, err := session.Where("id = ?", v.Id).Cols("is_settlement").Update(&models.OrderProduct{IsSettlement: 1}); err != nil {
							glog.Error("社区团购活动订单佣金计算定时任务失败7，错误：", v.OrderSn, err.Error())
							session.Rollback()
							continue
						}
					}
				} else if OrderDis.VerifyNum >= 0 {
					var num = cast.ToInt(countCode) - OrderDis.VerifyNum
                    if num <= 0 {
                    	continue
					} else {
						commission = cast.ToInt(cast.ToFloat32(v.CommissionRate) / 100 * cast.ToFloat32(v.PayPrice * cast.ToInt32(num)))
					}
					//老数据重新算佣金
					if OrderDis.VerifyNum == 0 {
						OrderDis.Commission = 0
					}
					//更新
					if _, err := session.Where("id = ?", OrderDis.Id).Cols("commission, verify_num").Update(&models.OrderDis{
						Commission: OrderDis.Commission + commission, VerifyNum: cast.ToInt(countCode),
					}); err != nil {
						glog.Error("社区团购活动订单佣金计算定时任务失败8，错误：", v.OrderSn, err.Error())
						session.Rollback()
						continue
					}
					if v.Number - v.RefundNum <= cast.ToInt32(countCode) {
						//已核销完
						if _, err := session.Where("id = ?", v.Id).Cols("is_settlement").Update(&models.OrderProduct{IsSettlement: 1}); err != nil {
							glog.Error("社区团购活动订单佣金计算定时任务失败9，错误：", v.OrderSn, err.Error())
							session.Rollback()
							continue
						}
					}
				}

			} else {
				commission = cast.ToInt(cast.ToFloat32(v.CommissionRate) / 100 * cast.ToFloat32(v.PaymentTotal - refundMoney))
				if _, err := session.Insert(&models.OrderDis{
					OrderId: cast.ToInt(v.Id), ParentOrderSn: v.ParentOrderSn, OrderSn: v.OrderSn, ProductId: v.ProductId, SkuId: v.SkuId, ProductName: v.ProductName, Image: v.Image,
					PayGoodsAmount: cast.ToInt(v.PaymentTotal), DisCommisRate: cast.ToInt(v.CommissionRate), Commission: commission,
					IsVirtual: isVirtual, CreateTime: time.Now().Local(), DisMemberId: member_id, RefundAmount: cast.ToInt(refundMoney),
				}); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败10，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				}
				if _, err := session.Where("id = ?", v.Id).Cols("is_settlement").Update(&models.OrderProduct{IsSettlement: 1}); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败11，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				}
			}

			//更新分销员账户佣金
            var memberCommission models.MemberCommission
			if hasMemberCommission, err := db.Table("datacenter.member_commission").Where("member_id = ?", member_id).Get(&memberCommission); err != nil {
				glog.Error("社区团购活动订单佣金计算定时任务失败12，错误：", v.OrderSn, err.Error())
				session.Rollback()
				continue
			} else if hasMemberCommission {
				if _, err := session.Table("datacenter.member_commission").Where("member_id = ?", member_id).Update(&models.MemberCommission{CommissionCash: memberCommission.CommissionCash + commission, UpdateTime: time.Now().Local()}); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败13，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				}
			} else {
				if _, err := session.Table("datacenter.member_commission").Insert(&models.MemberCommission{MemberId: member_id, CommissionCash: commission, UpdateTime: time.Now().Local()}); err != nil {
					glog.Error("社区团购活动订单佣金计算定时任务失败14，错误：", v.OrderSn, err.Error())
					session.Rollback()
					continue
				}
			}

			session.Commit()
		}
		i++
	}
    return
}

//社区团购活动佣金统计定时任务
func groupAactivityCountTask() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "order-center:task:lock:group_activity_count"
	if !redisConn.SetNX(lockCard, time.Now().Unix(), 20 * time.Minute).Val() {
		glog.Info("task is already running")
		return
	}
	defer redisConn.Del(lockCard)

	//订单佣金统计
	groupAactivityCount()
}

func groupAactivityCount() {
	//连接池勿关闭
	db := services.GetDBConn()

	var i = 1
	for {
		var orderList []models.OrderGroupActivity
		//以团为单位计算
		if err := db.Select("oga.id").Table("order_group_activity").Alias("oga").Where("oga.`status` = 1 AND oga.is_dis = 1").OrderBy("oga.id").Limit(500, (i - 1) * 500).Find(&orderList); err != nil {
			glog.Error("社区团购活动订单佣金统计定时任务失败1，错误：", err.Error())
			continue
		} else if len(orderList) == 0 {
			//无数据时退出
			break
		}

		for _, v := range orderList {
			glog.Info("社区团购活动订单佣金统计定时任务执行，团ID：", v.Id)
			//团下边的参团订单
			//已结算
			var paidSet = 0
			if _, err := db.Select("SUM(od.commission) as commission").Table("order_main_group").Alias("omg").
				Join("LEFT", "order_dis od", "od.parent_order_sn = omg.parent_order_sn").
				Where("omg.order_group_activity_id = ?", v.Id).Get(&paidSet); err != nil {
				glog.Error("社区团购活动订单佣金统计定时任务失败2，错误：", err.Error())
				continue
			}
			//未结算
			var waitSet = 0
			var orderInfo []models.OrderProductExt
			if err := db.Select("op.id, op.order_sn, op.payment_total, op.commission_rate, op.refund_num, om.is_virtual, op.number, op.pay_price").Table("order_main_group").Alias("omg").
				Join("LEFT", "order_main om", "om.parent_order_sn = omg.parent_order_sn").
				Join("LEFT", "order_product op", "op.order_sn = om.order_sn").
				Where("om.order_status = 30 AND om.order_type = 15 AND op.is_distribute = 1 AND op.is_settlement = 0 AND op.commission_rate > 0 AND om.parent_order_sn <> '' AND omg.order_group_activity_id = ?", v.Id).Find(&orderInfo); err != nil {
				glog.Error("社区团购活动订单佣金统计定时任务失败3，错误：", err.Error())
				continue
			} else {
				for _, val := range orderInfo {
					//查询退款金额
					var refundMoney int32 = 0
					if val.RefundNum > 0 {
						/*var refundOrder []models.RefundOrder
						if err := db.Select("refund_amount").Where("order_sn = ? AND refund_state = 3", val.OrderSn).Find(&refundOrder); err != nil {
							glog.Error("社区团购活动订单佣金统计定时任务失败4，错误：", val.OrderSn, err.Error())
							continue
						} else {
							for _, val := range refundOrder {
								refundMoney += cast.ToInt32(cast.ToFloat32(val.RefundAmount) * 100)
							}
						}*/
						refundMoney = val.RefundNum * val.PayPrice
					}
                    if val.IsVirtual == 1 {
						//虚拟商品
						//取已核销数量
						countCode, err := db.Table("order_verify_code").Where("verify_status = 1 AND order_sn = ?", val.OrderSn).Count()
						if err != nil {
							glog.Error("社区团购活动订单佣金统计定时任务失败5，错误：", val.OrderSn, err.Error())
							continue
						}
						//取已结算数量
						var OrderDis models.OrderDis
						if hasOrderDis, err := db.Select("id, verify_num, commission").Table("order_dis").Where("order_id = ?", val.Id).Get(&OrderDis); err != nil {
							glog.Error("社区团购活动订单佣金统计定时任务失败6，错误：", val.OrderSn, err.Error())
							continue
						} else if !hasOrderDis {
							//有已核销且没有佣金记录的
							var num = val.Number - val.RefundNum - cast.ToInt32(countCode)
							if num > 0 {
								waitSet = waitSet + cast.ToInt(cast.ToFloat32(val.CommissionRate) / 100 * cast.ToFloat32(val.PayPrice * cast.ToInt32(num)))
							}
						} else if OrderDis.VerifyNum >= 0 {
							var num = cast.ToInt(val.Number - val.RefundNum) - OrderDis.VerifyNum
							if num > 0 {
								waitSet = waitSet + cast.ToInt(cast.ToFloat32(val.CommissionRate) / 100 * cast.ToFloat32(val.PayPrice * cast.ToInt32(num)))
							}
						}
					} else {
						waitSet = waitSet + cast.ToInt(cast.ToFloat32(val.CommissionRate) / 100 * cast.ToFloat32(val.PaymentTotal - refundMoney))
					}
				}
			}

			//更新
			if _, err := db.Where("id = ?", v.Id).Cols("wait_amount, paid_amount").Update(&models.OrderGroupActivity{WaitAmount: cast.ToInt32(waitSet), PaidAmount: cast.ToInt32(paidSet)}); err != nil {
				glog.Error("社区团购活动订单佣金统计定时任务失败7，错误：", v, err.Error())
				continue
			}
		}
		i++
	}
	glog.Info("社区团购活动订单佣金统计定时任务执行结束")
	return
}
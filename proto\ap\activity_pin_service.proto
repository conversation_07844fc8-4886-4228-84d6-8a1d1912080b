syntax = "proto3";

package ap;

import "ap/activity_pin_model.proto";


service PinGroupService {
  //获取拼团商品的订单统计 同步查询，后期有性能问题 可能需要通过异步数据处理
  rpc GetPinGroupProductOrderStatics(GetPinGroupProductOrderStaticsRequest) returns (GetPinGroupProductOrderStaticsResponse);
  //拼团订单列表
  rpc GroupOrderList (GetGroupListRequest) returns (GroupOrderListResponse);
  // 拼团订单详情
  rpc PinGroupOrderDetail(GetGroupDetailRequest) returns (GetGroupDetailResponse);
  // 商品参团信息
  rpc GroupParticipantList(GetGroupParticipantRequest) returns (GetGroupParticipantResponse);
  // 可参团订单列表
  rpc CanParticipantOrderList(GetGroupParticipantRequest) returns (GetCanParticipantOrderListResponse);
  // 订阅微信消息
  rpc NewSubscribeMessage(NewSubscribeMessageRequest) returns (BaseResponse);
  // 查询订阅微信小程序模板消息
  rpc SubscribeMessageList(SubscribeMessageListRequest) returns (SubscribeMessageListResponse);
  //创建拼团单
  rpc CreateGroupOrder(CreateGroupOrderRequest) returns (CreateGroupOrderResponse);
  //拼团单支付
  rpc PinOrderPay(PinOrderPayRequest) returns (UnifiedOrderResponse);
  //拼团单支付回调
  rpc PinOrderPayNotify(OrderPayNotifyRequest) returns (BaseResponse);
  //拼团单退款
  rpc RefundPinOrder(PinOrderRefundRequest) returns (BaseResponse);
  //拼团单退款回调
  rpc PinRefundNotify(RefundNotifyRequest) returns (BaseResponse);
  //拼团单取消
  rpc CancelPinOrder(CancelGroupOrderRequest) returns (CancelPinOrderResponse);
  //拼团终止活动
  rpc StopGroup(StopGroupRequest) returns (BaseResponse);
  //某个活动下某个商品被用户购买了多少件
  rpc GetUserOrderCount(GetUserOrderCountRequest) returns (GetUserOrderCountResponse);
  //获取团信息
  rpc GetPinOrderMainInfo(GetPinOrderMainInfoRequest) returns (GetPinOrderMainInfoResponse);

}

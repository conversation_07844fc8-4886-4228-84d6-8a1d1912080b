package tasks

import (
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/services"
	"time"
)

type TaskAdvertisementMpRecord struct {
}

func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task_advertisement_mp_record run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */3 * * * *", func() { //每一分钟执行一次
		service := TaskOrderNotify{}
		service.taskDealAdvertisementMpRecordNotify()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s *TaskOrderNotify) taskDealAdvertisementMpRecordNotify() {
	redisClient := services.GetRedisConn()

	if result := redisClient.SetNX("task:lock:taskDealMpNotify", time.Now().Unix(), time.Minute*3); result.Err() != nil {
		glog.Error(result.Err())
		return
	} else if !result.Val() {
		return
	}

	engine := services.GetDBConn()

	var paynotifylist []models.AdvertisementMpRecord
	engine.Table("advertisement_mp_record").Where("deal_status = 0 and deal_num < 5").Find(&paynotifylist)
	if len(paynotifylist) == 0 {
		return
	}

	for _, item := range paynotifylist {
		services.PushMpRecordNotify(&item)
	}
}


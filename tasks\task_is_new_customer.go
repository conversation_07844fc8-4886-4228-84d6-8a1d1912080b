package tasks

import (
	"order-center/services"
	"time"
)

// 新老客户判断更新
func UpdateIsNewCustomer() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()
	lockCard := "task:lock:isnewcustomer"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	newCustomerTimeStr := redisConn.Get("ordercenter:newcustomer:timestr").Val()
	if newCustomerTimeStr == "" {
		newCustomerTimeStr = "2021-06-10 00:00:00"
	}
	services.MakingTagForOrder(newCustomerTimeStr)
}

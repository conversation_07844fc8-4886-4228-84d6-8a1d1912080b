// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sh/distribution.proto

package sh

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DisResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisResponse) Reset()         { *m = DisResponse{} }
func (m *DisResponse) String() string { return proto.CompactTextString(m) }
func (*DisResponse) ProtoMessage()    {}
func (*DisResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{0}
}

func (m *DisResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisResponse.Unmarshal(m, b)
}
func (m *DisResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisResponse.Marshal(b, m, deterministic)
}
func (m *DisResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisResponse.Merge(m, src)
}
func (m *DisResponse) XXX_Size() int {
	return xxx_messageInfo_DisResponse.Size(m)
}
func (m *DisResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisResponse proto.InternalMessageInfo

func (m *DisResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type EmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyRequest) Reset()         { *m = EmptyRequest{} }
func (m *EmptyRequest) String() string { return proto.CompactTextString(m) }
func (*EmptyRequest) ProtoMessage()    {}
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{1}
}

func (m *EmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyRequest.Unmarshal(m, b)
}
func (m *EmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyRequest.Marshal(b, m, deterministic)
}
func (m *EmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyRequest.Merge(m, src)
}
func (m *EmptyRequest) XXX_Size() int {
	return xxx_messageInfo_EmptyRequest.Size(m)
}
func (m *EmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyRequest proto.InternalMessageInfo

type DisCategoriesRequest struct {
	// 上级分类id，第一级不传或者0
	ParentId             int32    `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisCategoriesRequest) Reset()         { *m = DisCategoriesRequest{} }
func (m *DisCategoriesRequest) String() string { return proto.CompactTextString(m) }
func (*DisCategoriesRequest) ProtoMessage()    {}
func (*DisCategoriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{2}
}

func (m *DisCategoriesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisCategoriesRequest.Unmarshal(m, b)
}
func (m *DisCategoriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisCategoriesRequest.Marshal(b, m, deterministic)
}
func (m *DisCategoriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisCategoriesRequest.Merge(m, src)
}
func (m *DisCategoriesRequest) XXX_Size() int {
	return xxx_messageInfo_DisCategoriesRequest.Size(m)
}
func (m *DisCategoriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisCategoriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisCategoriesRequest proto.InternalMessageInfo

func (m *DisCategoriesRequest) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

type DisCategoriesResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*DisCategoriesResponse_Category `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *DisCategoriesResponse) Reset()         { *m = DisCategoriesResponse{} }
func (m *DisCategoriesResponse) String() string { return proto.CompactTextString(m) }
func (*DisCategoriesResponse) ProtoMessage()    {}
func (*DisCategoriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{3}
}

func (m *DisCategoriesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisCategoriesResponse.Unmarshal(m, b)
}
func (m *DisCategoriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisCategoriesResponse.Marshal(b, m, deterministic)
}
func (m *DisCategoriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisCategoriesResponse.Merge(m, src)
}
func (m *DisCategoriesResponse) XXX_Size() int {
	return xxx_messageInfo_DisCategoriesResponse.Size(m)
}
func (m *DisCategoriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisCategoriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisCategoriesResponse proto.InternalMessageInfo

func (m *DisCategoriesResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisCategoriesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisCategoriesResponse) GetData() []*DisCategoriesResponse_Category {
	if m != nil {
		return m.Data
	}
	return nil
}

type DisCategoriesResponse_Category struct {
	// 分类id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 下级数量
	ChildrenCount        int32    `protobuf:"varint,3,opt,name=children_count,json=childrenCount,proto3" json:"children_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisCategoriesResponse_Category) Reset()         { *m = DisCategoriesResponse_Category{} }
func (m *DisCategoriesResponse_Category) String() string { return proto.CompactTextString(m) }
func (*DisCategoriesResponse_Category) ProtoMessage()    {}
func (*DisCategoriesResponse_Category) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{3, 0}
}

func (m *DisCategoriesResponse_Category) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisCategoriesResponse_Category.Unmarshal(m, b)
}
func (m *DisCategoriesResponse_Category) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisCategoriesResponse_Category.Marshal(b, m, deterministic)
}
func (m *DisCategoriesResponse_Category) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisCategoriesResponse_Category.Merge(m, src)
}
func (m *DisCategoriesResponse_Category) XXX_Size() int {
	return xxx_messageInfo_DisCategoriesResponse_Category.Size(m)
}
func (m *DisCategoriesResponse_Category) XXX_DiscardUnknown() {
	xxx_messageInfo_DisCategoriesResponse_Category.DiscardUnknown(m)
}

var xxx_messageInfo_DisCategoriesResponse_Category proto.InternalMessageInfo

func (m *DisCategoriesResponse_Category) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DisCategoriesResponse_Category) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisCategoriesResponse_Category) GetChildrenCount() int32 {
	if m != nil {
		return m.ChildrenCount
	}
	return 0
}

type DisSpuListRequest struct {
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 最小佣金
	MinRate float32 `protobuf:"fixed32,3,opt,name=min_rate,json=minRate,proto3" json:"min_rate"`
	// 最大佣金
	MaxRate float32 `protobuf:"fixed32,4,opt,name=max_rate,json=maxRate,proto3" json:"max_rate"`
	// 商品名称
	GoodsName string `protobuf:"bytes,5,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	// 商品sku
	SkuId string `protobuf:"bytes,6,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品spu
	SpuId string `protobuf:"bytes,7,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 3级分类id
	CategoryId int32 `protobuf:"varint,8,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	// 是否首页推荐，0全部、1否、2是
	IsRecommend int32 `protobuf:"varint,9,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	// 主体：1-默认，2-极宠家
	OrgId                int32    `protobuf:"varint,10,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuListRequest) Reset()         { *m = DisSpuListRequest{} }
func (m *DisSpuListRequest) String() string { return proto.CompactTextString(m) }
func (*DisSpuListRequest) ProtoMessage()    {}
func (*DisSpuListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{4}
}

func (m *DisSpuListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuListRequest.Unmarshal(m, b)
}
func (m *DisSpuListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuListRequest.Marshal(b, m, deterministic)
}
func (m *DisSpuListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuListRequest.Merge(m, src)
}
func (m *DisSpuListRequest) XXX_Size() int {
	return xxx_messageInfo_DisSpuListRequest.Size(m)
}
func (m *DisSpuListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuListRequest proto.InternalMessageInfo

func (m *DisSpuListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisSpuListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisSpuListRequest) GetMinRate() float32 {
	if m != nil {
		return m.MinRate
	}
	return 0
}

func (m *DisSpuListRequest) GetMaxRate() float32 {
	if m != nil {
		return m.MaxRate
	}
	return 0
}

func (m *DisSpuListRequest) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *DisSpuListRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *DisSpuListRequest) GetSpuId() string {
	if m != nil {
		return m.SpuId
	}
	return ""
}

func (m *DisSpuListRequest) GetCategoryId() int32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *DisSpuListRequest) GetIsRecommend() int32 {
	if m != nil {
		return m.IsRecommend
	}
	return 0
}

func (m *DisSpuListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisSpuListData struct {
	// 商品spu
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 图片地址
	ImageUrl string `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 商品价格
	Price float32 `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	// 商品分类
	Category string `protobuf:"bytes,5,opt,name=category,proto3" json:"category"`
	// 添加时间
	AddTime string `protobuf:"bytes,6,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	// 是否首页推荐，0不是、1是
	IsRecommend int32 `protobuf:"varint,7,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	// 首页排序
	Sort                 int32    `protobuf:"varint,8,opt,name=sort,proto3" json:"sort"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuListData) Reset()         { *m = DisSpuListData{} }
func (m *DisSpuListData) String() string { return proto.CompactTextString(m) }
func (*DisSpuListData) ProtoMessage()    {}
func (*DisSpuListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{5}
}

func (m *DisSpuListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuListData.Unmarshal(m, b)
}
func (m *DisSpuListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuListData.Marshal(b, m, deterministic)
}
func (m *DisSpuListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuListData.Merge(m, src)
}
func (m *DisSpuListData) XXX_Size() int {
	return xxx_messageInfo_DisSpuListData.Size(m)
}
func (m *DisSpuListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuListData.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuListData proto.InternalMessageInfo

func (m *DisSpuListData) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *DisSpuListData) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *DisSpuListData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisSpuListData) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *DisSpuListData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *DisSpuListData) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

func (m *DisSpuListData) GetIsRecommend() int32 {
	if m != nil {
		return m.IsRecommend
	}
	return 0
}

func (m *DisSpuListData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type DisSpuListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 商品Spu数据
	Data []*DisSpuListData `protobuf:"bytes,8,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,9,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuListResponse) Reset()         { *m = DisSpuListResponse{} }
func (m *DisSpuListResponse) String() string { return proto.CompactTextString(m) }
func (*DisSpuListResponse) ProtoMessage()    {}
func (*DisSpuListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{6}
}

func (m *DisSpuListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuListResponse.Unmarshal(m, b)
}
func (m *DisSpuListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuListResponse.Marshal(b, m, deterministic)
}
func (m *DisSpuListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuListResponse.Merge(m, src)
}
func (m *DisSpuListResponse) XXX_Size() int {
	return xxx_messageInfo_DisSpuListResponse.Size(m)
}
func (m *DisSpuListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuListResponse proto.InternalMessageInfo

func (m *DisSpuListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisSpuListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisSpuListResponse) GetData() []*DisSpuListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisSpuListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisSpuDetailRequest struct {
	// 商品spu
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuDetailRequest) Reset()         { *m = DisSpuDetailRequest{} }
func (m *DisSpuDetailRequest) String() string { return proto.CompactTextString(m) }
func (*DisSpuDetailRequest) ProtoMessage()    {}
func (*DisSpuDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{7}
}

func (m *DisSpuDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuDetailRequest.Unmarshal(m, b)
}
func (m *DisSpuDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuDetailRequest.Marshal(b, m, deterministic)
}
func (m *DisSpuDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuDetailRequest.Merge(m, src)
}
func (m *DisSpuDetailRequest) XXX_Size() int {
	return xxx_messageInfo_DisSpuDetailRequest.Size(m)
}
func (m *DisSpuDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuDetailRequest proto.InternalMessageInfo

func (m *DisSpuDetailRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *DisSpuDetailRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisSpuDetailRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisSpuDetailRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisSpuDetailResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// spu信息
	Spu *DisSpuListData `protobuf:"bytes,3,opt,name=spu,proto3" json:"spu"`
	// skus
	Skus []*DisSpuDetailResponse_Sku `protobuf:"bytes,4,rep,name=skus,proto3" json:"skus"`
	// sku总数
	Total                int32    `protobuf:"varint,5,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuDetailResponse) Reset()         { *m = DisSpuDetailResponse{} }
func (m *DisSpuDetailResponse) String() string { return proto.CompactTextString(m) }
func (*DisSpuDetailResponse) ProtoMessage()    {}
func (*DisSpuDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{8}
}

func (m *DisSpuDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuDetailResponse.Unmarshal(m, b)
}
func (m *DisSpuDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuDetailResponse.Marshal(b, m, deterministic)
}
func (m *DisSpuDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuDetailResponse.Merge(m, src)
}
func (m *DisSpuDetailResponse) XXX_Size() int {
	return xxx_messageInfo_DisSpuDetailResponse.Size(m)
}
func (m *DisSpuDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuDetailResponse proto.InternalMessageInfo

func (m *DisSpuDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisSpuDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisSpuDetailResponse) GetSpu() *DisSpuListData {
	if m != nil {
		return m.Spu
	}
	return nil
}

func (m *DisSpuDetailResponse) GetSkus() []*DisSpuDetailResponse_Sku {
	if m != nil {
		return m.Skus
	}
	return nil
}

func (m *DisSpuDetailResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisSpuDetailResponse_Sku struct {
	// 商品sku
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 图片链接
	ImageUrl string `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	// 规格值
	SpecName string `protobuf:"bytes,3,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	// 当前佣金
	CommissionRate float32 `protobuf:"fixed32,4,opt,name=commission_rate,json=commissionRate,proto3" json:"commission_rate"`
	// 日常佣金
	NormalCommissionRate float32 `protobuf:"fixed32,5,opt,name=normal_commission_rate,json=normalCommissionRate,proto3" json:"normal_commission_rate"`
	// 推广文案
	Write                string   `protobuf:"bytes,6,opt,name=write,proto3" json:"write"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuDetailResponse_Sku) Reset()         { *m = DisSpuDetailResponse_Sku{} }
func (m *DisSpuDetailResponse_Sku) String() string { return proto.CompactTextString(m) }
func (*DisSpuDetailResponse_Sku) ProtoMessage()    {}
func (*DisSpuDetailResponse_Sku) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{8, 0}
}

func (m *DisSpuDetailResponse_Sku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuDetailResponse_Sku.Unmarshal(m, b)
}
func (m *DisSpuDetailResponse_Sku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuDetailResponse_Sku.Marshal(b, m, deterministic)
}
func (m *DisSpuDetailResponse_Sku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuDetailResponse_Sku.Merge(m, src)
}
func (m *DisSpuDetailResponse_Sku) XXX_Size() int {
	return xxx_messageInfo_DisSpuDetailResponse_Sku.Size(m)
}
func (m *DisSpuDetailResponse_Sku) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuDetailResponse_Sku.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuDetailResponse_Sku proto.InternalMessageInfo

func (m *DisSpuDetailResponse_Sku) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisSpuDetailResponse_Sku) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *DisSpuDetailResponse_Sku) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *DisSpuDetailResponse_Sku) GetCommissionRate() float32 {
	if m != nil {
		return m.CommissionRate
	}
	return 0
}

func (m *DisSpuDetailResponse_Sku) GetNormalCommissionRate() float32 {
	if m != nil {
		return m.NormalCommissionRate
	}
	return 0
}

func (m *DisSpuDetailResponse_Sku) GetWrite() string {
	if m != nil {
		return m.Write
	}
	return ""
}

type DisSpuUpdateRequest struct {
	// 商品spu
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 是否首页推荐，true或者false（不更新不要传）
	IsRecommend *wrappers.BoolValue `protobuf:"bytes,2,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	// 首页排序（不更新不要传）
	Sort                 *wrappers.Int32Value `protobuf:"bytes,3,opt,name=sort,proto3" json:"sort"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DisSpuUpdateRequest) Reset()         { *m = DisSpuUpdateRequest{} }
func (m *DisSpuUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*DisSpuUpdateRequest) ProtoMessage()    {}
func (*DisSpuUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{9}
}

func (m *DisSpuUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuUpdateRequest.Unmarshal(m, b)
}
func (m *DisSpuUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuUpdateRequest.Marshal(b, m, deterministic)
}
func (m *DisSpuUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuUpdateRequest.Merge(m, src)
}
func (m *DisSpuUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_DisSpuUpdateRequest.Size(m)
}
func (m *DisSpuUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuUpdateRequest proto.InternalMessageInfo

func (m *DisSpuUpdateRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *DisSpuUpdateRequest) GetIsRecommend() *wrappers.BoolValue {
	if m != nil {
		return m.IsRecommend
	}
	return nil
}

func (m *DisSpuUpdateRequest) GetSort() *wrappers.Int32Value {
	if m != nil {
		return m.Sort
	}
	return nil
}

type DisSpuLogsRequest struct {
	// 商品spu
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuLogsRequest) Reset()         { *m = DisSpuLogsRequest{} }
func (m *DisSpuLogsRequest) String() string { return proto.CompactTextString(m) }
func (*DisSpuLogsRequest) ProtoMessage()    {}
func (*DisSpuLogsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{10}
}

func (m *DisSpuLogsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuLogsRequest.Unmarshal(m, b)
}
func (m *DisSpuLogsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuLogsRequest.Marshal(b, m, deterministic)
}
func (m *DisSpuLogsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuLogsRequest.Merge(m, src)
}
func (m *DisSpuLogsRequest) XXX_Size() int {
	return xxx_messageInfo_DisSpuLogsRequest.Size(m)
}
func (m *DisSpuLogsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuLogsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuLogsRequest proto.InternalMessageInfo

func (m *DisSpuLogsRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *DisSpuLogsRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisSpuLogsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type DisSpuLogsResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 操作日志
	Data []*DisSpuLogsResponse_SpuLog `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// sku总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuLogsResponse) Reset()         { *m = DisSpuLogsResponse{} }
func (m *DisSpuLogsResponse) String() string { return proto.CompactTextString(m) }
func (*DisSpuLogsResponse) ProtoMessage()    {}
func (*DisSpuLogsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{11}
}

func (m *DisSpuLogsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuLogsResponse.Unmarshal(m, b)
}
func (m *DisSpuLogsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuLogsResponse.Marshal(b, m, deterministic)
}
func (m *DisSpuLogsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuLogsResponse.Merge(m, src)
}
func (m *DisSpuLogsResponse) XXX_Size() int {
	return xxx_messageInfo_DisSpuLogsResponse.Size(m)
}
func (m *DisSpuLogsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuLogsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuLogsResponse proto.InternalMessageInfo

func (m *DisSpuLogsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisSpuLogsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisSpuLogsResponse) GetData() []*DisSpuLogsResponse_SpuLog {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisSpuLogsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisSpuLogsResponse_SpuLog struct {
	// 操作人
	UserName string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 操作时间
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 操作内容
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSpuLogsResponse_SpuLog) Reset()         { *m = DisSpuLogsResponse_SpuLog{} }
func (m *DisSpuLogsResponse_SpuLog) String() string { return proto.CompactTextString(m) }
func (*DisSpuLogsResponse_SpuLog) ProtoMessage()    {}
func (*DisSpuLogsResponse_SpuLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{11, 0}
}

func (m *DisSpuLogsResponse_SpuLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSpuLogsResponse_SpuLog.Unmarshal(m, b)
}
func (m *DisSpuLogsResponse_SpuLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSpuLogsResponse_SpuLog.Marshal(b, m, deterministic)
}
func (m *DisSpuLogsResponse_SpuLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSpuLogsResponse_SpuLog.Merge(m, src)
}
func (m *DisSpuLogsResponse_SpuLog) XXX_Size() int {
	return xxx_messageInfo_DisSpuLogsResponse_SpuLog.Size(m)
}
func (m *DisSpuLogsResponse_SpuLog) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSpuLogsResponse_SpuLog.DiscardUnknown(m)
}

var xxx_messageInfo_DisSpuLogsResponse_SpuLog proto.InternalMessageInfo

func (m *DisSpuLogsResponse_SpuLog) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DisSpuLogsResponse_SpuLog) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *DisSpuLogsResponse_SpuLog) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DisSkuUpdateRequest struct {
	// 商品sku
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 推广文案（不更新不要传）
	Write *wrappers.StringValue `protobuf:"bytes,2,opt,name=write,proto3" json:"write"`
	// 日常佣金（不更新不要传）
	NormalCommissionRate *wrappers.FloatValue `protobuf:"bytes,3,opt,name=normal_commission_rate,json=normalCommissionRate,proto3" json:"normal_commission_rate"`
	// 是否分销，true 添加分销、false 取消分销（不更新不要传）
	IsDis *wrappers.BoolValue `protobuf:"bytes,4,opt,name=is_dis,json=isDis,proto3" json:"is_dis"`
	//主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSkuUpdateRequest) Reset()         { *m = DisSkuUpdateRequest{} }
func (m *DisSkuUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*DisSkuUpdateRequest) ProtoMessage()    {}
func (*DisSkuUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{12}
}

func (m *DisSkuUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSkuUpdateRequest.Unmarshal(m, b)
}
func (m *DisSkuUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSkuUpdateRequest.Marshal(b, m, deterministic)
}
func (m *DisSkuUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSkuUpdateRequest.Merge(m, src)
}
func (m *DisSkuUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_DisSkuUpdateRequest.Size(m)
}
func (m *DisSkuUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSkuUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSkuUpdateRequest proto.InternalMessageInfo

func (m *DisSkuUpdateRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisSkuUpdateRequest) GetWrite() *wrappers.StringValue {
	if m != nil {
		return m.Write
	}
	return nil
}

func (m *DisSkuUpdateRequest) GetNormalCommissionRate() *wrappers.FloatValue {
	if m != nil {
		return m.NormalCommissionRate
	}
	return nil
}

func (m *DisSkuUpdateRequest) GetIsDis() *wrappers.BoolValue {
	if m != nil {
		return m.IsDis
	}
	return nil
}

func (m *DisSkuUpdateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisNotDisSkuListRequest struct {
	// 商品sku
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品spu
	SpuId string `protobuf:"bytes,2,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisNotDisSkuListRequest) Reset()         { *m = DisNotDisSkuListRequest{} }
func (m *DisNotDisSkuListRequest) String() string { return proto.CompactTextString(m) }
func (*DisNotDisSkuListRequest) ProtoMessage()    {}
func (*DisNotDisSkuListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{13}
}

func (m *DisNotDisSkuListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisNotDisSkuListRequest.Unmarshal(m, b)
}
func (m *DisNotDisSkuListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisNotDisSkuListRequest.Marshal(b, m, deterministic)
}
func (m *DisNotDisSkuListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisNotDisSkuListRequest.Merge(m, src)
}
func (m *DisNotDisSkuListRequest) XXX_Size() int {
	return xxx_messageInfo_DisNotDisSkuListRequest.Size(m)
}
func (m *DisNotDisSkuListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisNotDisSkuListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisNotDisSkuListRequest proto.InternalMessageInfo

func (m *DisNotDisSkuListRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *DisNotDisSkuListRequest) GetSpuId() string {
	if m != nil {
		return m.SpuId
	}
	return ""
}

func (m *DisNotDisSkuListRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisNotDisSkuListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisNotDisSkuListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisNotDisSkuListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisNotDisSkuListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*DisNotDisSkuListResponse_Sku `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisNotDisSkuListResponse) Reset()         { *m = DisNotDisSkuListResponse{} }
func (m *DisNotDisSkuListResponse) String() string { return proto.CompactTextString(m) }
func (*DisNotDisSkuListResponse) ProtoMessage()    {}
func (*DisNotDisSkuListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{14}
}

func (m *DisNotDisSkuListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisNotDisSkuListResponse.Unmarshal(m, b)
}
func (m *DisNotDisSkuListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisNotDisSkuListResponse.Marshal(b, m, deterministic)
}
func (m *DisNotDisSkuListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisNotDisSkuListResponse.Merge(m, src)
}
func (m *DisNotDisSkuListResponse) XXX_Size() int {
	return xxx_messageInfo_DisNotDisSkuListResponse.Size(m)
}
func (m *DisNotDisSkuListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisNotDisSkuListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisNotDisSkuListResponse proto.InternalMessageInfo

func (m *DisNotDisSkuListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisNotDisSkuListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisNotDisSkuListResponse) GetData() []*DisNotDisSkuListResponse_Sku {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisNotDisSkuListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisNotDisSkuListResponse_Sku struct {
	// 商品sku
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 图片地址
	ImageUrl string `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 商品售价
	Price                float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisNotDisSkuListResponse_Sku) Reset()         { *m = DisNotDisSkuListResponse_Sku{} }
func (m *DisNotDisSkuListResponse_Sku) String() string { return proto.CompactTextString(m) }
func (*DisNotDisSkuListResponse_Sku) ProtoMessage()    {}
func (*DisNotDisSkuListResponse_Sku) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{14, 0}
}

func (m *DisNotDisSkuListResponse_Sku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisNotDisSkuListResponse_Sku.Unmarshal(m, b)
}
func (m *DisNotDisSkuListResponse_Sku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisNotDisSkuListResponse_Sku.Marshal(b, m, deterministic)
}
func (m *DisNotDisSkuListResponse_Sku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisNotDisSkuListResponse_Sku.Merge(m, src)
}
func (m *DisNotDisSkuListResponse_Sku) XXX_Size() int {
	return xxx_messageInfo_DisNotDisSkuListResponse_Sku.Size(m)
}
func (m *DisNotDisSkuListResponse_Sku) XXX_DiscardUnknown() {
	xxx_messageInfo_DisNotDisSkuListResponse_Sku.DiscardUnknown(m)
}

var xxx_messageInfo_DisNotDisSkuListResponse_Sku proto.InternalMessageInfo

func (m *DisNotDisSkuListResponse_Sku) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisNotDisSkuListResponse_Sku) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *DisNotDisSkuListResponse_Sku) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisNotDisSkuListResponse_Sku) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type DisImportTemplateRequest struct {
	// 1批量导入分销商品(默认)、2批量导入限时佣金商品、3批量秒杀商品模板
	Type                 int32    `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportTemplateRequest) Reset()         { *m = DisImportTemplateRequest{} }
func (m *DisImportTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*DisImportTemplateRequest) ProtoMessage()    {}
func (*DisImportTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{15}
}

func (m *DisImportTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportTemplateRequest.Unmarshal(m, b)
}
func (m *DisImportTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportTemplateRequest.Marshal(b, m, deterministic)
}
func (m *DisImportTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportTemplateRequest.Merge(m, src)
}
func (m *DisImportTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_DisImportTemplateRequest.Size(m)
}
func (m *DisImportTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportTemplateRequest proto.InternalMessageInfo

func (m *DisImportTemplateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type DisImportTemplateResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	Template             []byte   `protobuf:"bytes,3,opt,name=template,proto3" json:"template"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportTemplateResponse) Reset()         { *m = DisImportTemplateResponse{} }
func (m *DisImportTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*DisImportTemplateResponse) ProtoMessage()    {}
func (*DisImportTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{16}
}

func (m *DisImportTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportTemplateResponse.Unmarshal(m, b)
}
func (m *DisImportTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportTemplateResponse.Marshal(b, m, deterministic)
}
func (m *DisImportTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportTemplateResponse.Merge(m, src)
}
func (m *DisImportTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_DisImportTemplateResponse.Size(m)
}
func (m *DisImportTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportTemplateResponse proto.InternalMessageInfo

func (m *DisImportTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisImportTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisImportTemplateResponse) GetTemplate() []byte {
	if m != nil {
		return m.Template
	}
	return nil
}

type DisImportListRequest struct {
	// 页码，不传默认为1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	// 每页数量，不传默认10
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 1批量导入分销商品（默认）、2批量导入限时佣金商品、3 批量秒杀商品模板
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 针对type=2时对应的活动id
	TypeId               int32    `protobuf:"varint,4,opt,name=type_id,json=typeId,proto3" json:"type_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportListRequest) Reset()         { *m = DisImportListRequest{} }
func (m *DisImportListRequest) String() string { return proto.CompactTextString(m) }
func (*DisImportListRequest) ProtoMessage()    {}
func (*DisImportListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{17}
}

func (m *DisImportListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportListRequest.Unmarshal(m, b)
}
func (m *DisImportListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportListRequest.Marshal(b, m, deterministic)
}
func (m *DisImportListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportListRequest.Merge(m, src)
}
func (m *DisImportListRequest) XXX_Size() int {
	return xxx_messageInfo_DisImportListRequest.Size(m)
}
func (m *DisImportListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportListRequest proto.InternalMessageInfo

func (m *DisImportListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisImportListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisImportListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DisImportListRequest) GetTypeId() int32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

type DisImportListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*DisImportListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportListResponse) Reset()         { *m = DisImportListResponse{} }
func (m *DisImportListResponse) String() string { return proto.CompactTextString(m) }
func (*DisImportListResponse) ProtoMessage()    {}
func (*DisImportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{18}
}

func (m *DisImportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportListResponse.Unmarshal(m, b)
}
func (m *DisImportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportListResponse.Marshal(b, m, deterministic)
}
func (m *DisImportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportListResponse.Merge(m, src)
}
func (m *DisImportListResponse) XXX_Size() int {
	return xxx_messageInfo_DisImportListResponse.Size(m)
}
func (m *DisImportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportListResponse proto.InternalMessageInfo

func (m *DisImportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisImportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisImportListResponse) GetData() []*DisImportListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisImportListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisImportListResponse_List struct {
	// 导入id，下载使用
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 导入时间
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 导入结果
	Result string `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	// 导入结果url
	ResultUrl            string   `protobuf:"bytes,4,opt,name=result_url,json=resultUrl,proto3" json:"result_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportListResponse_List) Reset()         { *m = DisImportListResponse_List{} }
func (m *DisImportListResponse_List) String() string { return proto.CompactTextString(m) }
func (*DisImportListResponse_List) ProtoMessage()    {}
func (*DisImportListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{18, 0}
}

func (m *DisImportListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportListResponse_List.Unmarshal(m, b)
}
func (m *DisImportListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportListResponse_List.Marshal(b, m, deterministic)
}
func (m *DisImportListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportListResponse_List.Merge(m, src)
}
func (m *DisImportListResponse_List) XXX_Size() int {
	return xxx_messageInfo_DisImportListResponse_List.Size(m)
}
func (m *DisImportListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportListResponse_List proto.InternalMessageInfo

func (m *DisImportListResponse_List) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DisImportListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *DisImportListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *DisImportListResponse_List) GetResultUrl() string {
	if m != nil {
		return m.ResultUrl
	}
	return ""
}

type DisImportRequest struct {
	// 文件字节流
	File []byte `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
	// 导入类型:1批量导入分销商品（默认）、2批量导入限时佣金商品、3 批量秒杀商品模板
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 活动id
	Id int32 `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	//主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportRequest) Reset()         { *m = DisImportRequest{} }
func (m *DisImportRequest) String() string { return proto.CompactTextString(m) }
func (*DisImportRequest) ProtoMessage()    {}
func (*DisImportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{19}
}

func (m *DisImportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportRequest.Unmarshal(m, b)
}
func (m *DisImportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportRequest.Marshal(b, m, deterministic)
}
func (m *DisImportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportRequest.Merge(m, src)
}
func (m *DisImportRequest) XXX_Size() int {
	return xxx_messageInfo_DisImportRequest.Size(m)
}
func (m *DisImportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportRequest proto.InternalMessageInfo

func (m *DisImportRequest) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

func (m *DisImportRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DisImportRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DisImportRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisImportDownloadResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	File                 []byte   `protobuf:"bytes,3,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisImportDownloadResponse) Reset()         { *m = DisImportDownloadResponse{} }
func (m *DisImportDownloadResponse) String() string { return proto.CompactTextString(m) }
func (*DisImportDownloadResponse) ProtoMessage()    {}
func (*DisImportDownloadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{20}
}

func (m *DisImportDownloadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisImportDownloadResponse.Unmarshal(m, b)
}
func (m *DisImportDownloadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisImportDownloadResponse.Marshal(b, m, deterministic)
}
func (m *DisImportDownloadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisImportDownloadResponse.Merge(m, src)
}
func (m *DisImportDownloadResponse) XXX_Size() int {
	return xxx_messageInfo_DisImportDownloadResponse.Size(m)
}
func (m *DisImportDownloadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisImportDownloadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisImportDownloadResponse proto.InternalMessageInfo

func (m *DisImportDownloadResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisImportDownloadResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisImportDownloadResponse) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

// 分销设置全局佣金
type DisSetGlobalCommissionRequest struct {
	// 默认分销佣金
	DefaultCommission    float32  `protobuf:"fixed32,1,opt,name=default_commission,json=defaultCommission,proto3" json:"default_commission"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisSetGlobalCommissionRequest) Reset()         { *m = DisSetGlobalCommissionRequest{} }
func (m *DisSetGlobalCommissionRequest) String() string { return proto.CompactTextString(m) }
func (*DisSetGlobalCommissionRequest) ProtoMessage()    {}
func (*DisSetGlobalCommissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{21}
}

func (m *DisSetGlobalCommissionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisSetGlobalCommissionRequest.Unmarshal(m, b)
}
func (m *DisSetGlobalCommissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisSetGlobalCommissionRequest.Marshal(b, m, deterministic)
}
func (m *DisSetGlobalCommissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisSetGlobalCommissionRequest.Merge(m, src)
}
func (m *DisSetGlobalCommissionRequest) XXX_Size() int {
	return xxx_messageInfo_DisSetGlobalCommissionRequest.Size(m)
}
func (m *DisSetGlobalCommissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisSetGlobalCommissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisSetGlobalCommissionRequest proto.InternalMessageInfo

func (m *DisSetGlobalCommissionRequest) GetDefaultCommission() float32 {
	if m != nil {
		return m.DefaultCommission
	}
	return 0
}

// 分销全局佣金获取
type DisGlobalCommissionResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	DefaultCommission    float32  `protobuf:"fixed32,3,opt,name=default_commission,json=defaultCommission,proto3" json:"default_commission"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisGlobalCommissionResponse) Reset()         { *m = DisGlobalCommissionResponse{} }
func (m *DisGlobalCommissionResponse) String() string { return proto.CompactTextString(m) }
func (*DisGlobalCommissionResponse) ProtoMessage()    {}
func (*DisGlobalCommissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{22}
}

func (m *DisGlobalCommissionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisGlobalCommissionResponse.Unmarshal(m, b)
}
func (m *DisGlobalCommissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisGlobalCommissionResponse.Marshal(b, m, deterministic)
}
func (m *DisGlobalCommissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisGlobalCommissionResponse.Merge(m, src)
}
func (m *DisGlobalCommissionResponse) XXX_Size() int {
	return xxx_messageInfo_DisGlobalCommissionResponse.Size(m)
}
func (m *DisGlobalCommissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisGlobalCommissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisGlobalCommissionResponse proto.InternalMessageInfo

func (m *DisGlobalCommissionResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisGlobalCommissionResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisGlobalCommissionResponse) GetDefaultCommission() float32 {
	if m != nil {
		return m.DefaultCommission
	}
	return 0
}

// 分销商品请求参数
type DisGoodsListRequest struct {
	// 搜索类型，1-商品名称，2-商品sku
	SearchType int32 `protobuf:"varint,1,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	// 搜索词
	SearchName string `protobuf:"bytes,2,opt,name=search_name,json=searchName,proto3" json:"search_name"`
	// 页码
	Page int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	// 每页个数
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 活动id
	ActivityId int64 `protobuf:"varint,5,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	//主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisGoodsListRequest) Reset()         { *m = DisGoodsListRequest{} }
func (m *DisGoodsListRequest) String() string { return proto.CompactTextString(m) }
func (*DisGoodsListRequest) ProtoMessage()    {}
func (*DisGoodsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{23}
}

func (m *DisGoodsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisGoodsListRequest.Unmarshal(m, b)
}
func (m *DisGoodsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisGoodsListRequest.Marshal(b, m, deterministic)
}
func (m *DisGoodsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisGoodsListRequest.Merge(m, src)
}
func (m *DisGoodsListRequest) XXX_Size() int {
	return xxx_messageInfo_DisGoodsListRequest.Size(m)
}
func (m *DisGoodsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisGoodsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisGoodsListRequest proto.InternalMessageInfo

func (m *DisGoodsListRequest) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *DisGoodsListRequest) GetSearchName() string {
	if m != nil {
		return m.SearchName
	}
	return ""
}

func (m *DisGoodsListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisGoodsListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisGoodsListRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisGoodsListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 分销商品响应参数
type DisGoodsListResponse struct {
	Code                 int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*DisGoodsListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int64               `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DisGoodsListResponse) Reset()         { *m = DisGoodsListResponse{} }
func (m *DisGoodsListResponse) String() string { return proto.CompactTextString(m) }
func (*DisGoodsListResponse) ProtoMessage()    {}
func (*DisGoodsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{24}
}

func (m *DisGoodsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisGoodsListResponse.Unmarshal(m, b)
}
func (m *DisGoodsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisGoodsListResponse.Marshal(b, m, deterministic)
}
func (m *DisGoodsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisGoodsListResponse.Merge(m, src)
}
func (m *DisGoodsListResponse) XXX_Size() int {
	return xxx_messageInfo_DisGoodsListResponse.Size(m)
}
func (m *DisGoodsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisGoodsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisGoodsListResponse proto.InternalMessageInfo

func (m *DisGoodsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisGoodsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisGoodsListResponse) GetData() []*DisGoodsListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisGoodsListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisGoodsListData struct {
	// 商品id
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 图片地址
	GoodsImage string `protobuf:"bytes,2,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	// 商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	// 规格
	SpecName string `protobuf:"bytes,4,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	// 价格
	GoodsPrice float32 `protobuf:"fixed32,5,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	// 日常佣金
	DisNormalCommisRate float32 `protobuf:"fixed32,6,opt,name=dis_normal_commis_rate,json=disNormalCommisRate,proto3" json:"dis_normal_commis_rate"`
	// 电商店铺名称
	StoreId int64 `protobuf:"varint,7,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	// 电商店铺名称
	ShopName             string   `protobuf:"bytes,8,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisGoodsListData) Reset()         { *m = DisGoodsListData{} }
func (m *DisGoodsListData) String() string { return proto.CompactTextString(m) }
func (*DisGoodsListData) ProtoMessage()    {}
func (*DisGoodsListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{25}
}

func (m *DisGoodsListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisGoodsListData.Unmarshal(m, b)
}
func (m *DisGoodsListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisGoodsListData.Marshal(b, m, deterministic)
}
func (m *DisGoodsListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisGoodsListData.Merge(m, src)
}
func (m *DisGoodsListData) XXX_Size() int {
	return xxx_messageInfo_DisGoodsListData.Size(m)
}
func (m *DisGoodsListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisGoodsListData.DiscardUnknown(m)
}

var xxx_messageInfo_DisGoodsListData proto.InternalMessageInfo

func (m *DisGoodsListData) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisGoodsListData) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *DisGoodsListData) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *DisGoodsListData) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *DisGoodsListData) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *DisGoodsListData) GetDisNormalCommisRate() float32 {
	if m != nil {
		return m.DisNormalCommisRate
	}
	return 0
}

func (m *DisGoodsListData) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *DisGoodsListData) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

// 分销限时佣金活动列表request
type DisLimitActivityListRequest struct {
	// 活动状态，1-进行中，2-未开始，3-已结束，4-已失效
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	// 搜索类型，1-名称搜索，2-活动id搜索
	SearchType int32 `protobuf:"varint,2,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	// 搜索词
	SearchName string `protobuf:"bytes,3,opt,name=search_name,json=searchName,proto3" json:"search_name"`
	// 页码
	Page int32 `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	// 每页条数
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体id
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityListRequest) Reset()         { *m = DisLimitActivityListRequest{} }
func (m *DisLimitActivityListRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityListRequest) ProtoMessage()    {}
func (*DisLimitActivityListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{26}
}

func (m *DisLimitActivityListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityListRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityListRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityListRequest.Merge(m, src)
}
func (m *DisLimitActivityListRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityListRequest.Size(m)
}
func (m *DisLimitActivityListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityListRequest proto.InternalMessageInfo

func (m *DisLimitActivityListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DisLimitActivityListRequest) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *DisLimitActivityListRequest) GetSearchName() string {
	if m != nil {
		return m.SearchName
	}
	return ""
}

func (m *DisLimitActivityListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisLimitActivityListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisLimitActivityListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 分销限时佣金活动列表Response
type DisLimitActivityListResponse struct {
	// code码
	Code                 int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*DisLimitActivityListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int64                       `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *DisLimitActivityListResponse) Reset()         { *m = DisLimitActivityListResponse{} }
func (m *DisLimitActivityListResponse) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityListResponse) ProtoMessage()    {}
func (*DisLimitActivityListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{27}
}

func (m *DisLimitActivityListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityListResponse.Unmarshal(m, b)
}
func (m *DisLimitActivityListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityListResponse.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityListResponse.Merge(m, src)
}
func (m *DisLimitActivityListResponse) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityListResponse.Size(m)
}
func (m *DisLimitActivityListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityListResponse proto.InternalMessageInfo

func (m *DisLimitActivityListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisLimitActivityListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisLimitActivityListResponse) GetData() []*DisLimitActivityListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisLimitActivityListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisLimitActivityListData struct {
	// 活动id
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 活动名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 活动开始时间
	StartTime string `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 活动结束时间
	EndTime string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 最后编辑人
	LastEditor string `protobuf:"bytes,5,opt,name=last_editor,json=lastEditor,proto3" json:"last_editor"`
	// 活动状态，进行中，未开始，已结束，已失效
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status"`
	// 活动状态，1-进行中，2-未开始，3-已结束，4-已失效
	StatusCode           int32    `protobuf:"varint,7,opt,name=status_code,json=statusCode,proto3" json:"status_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityListData) Reset()         { *m = DisLimitActivityListData{} }
func (m *DisLimitActivityListData) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityListData) ProtoMessage()    {}
func (*DisLimitActivityListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{28}
}

func (m *DisLimitActivityListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityListData.Unmarshal(m, b)
}
func (m *DisLimitActivityListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityListData.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityListData.Merge(m, src)
}
func (m *DisLimitActivityListData) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityListData.Size(m)
}
func (m *DisLimitActivityListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityListData.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityListData proto.InternalMessageInfo

func (m *DisLimitActivityListData) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityListData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisLimitActivityListData) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DisLimitActivityListData) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *DisLimitActivityListData) GetLastEditor() string {
	if m != nil {
		return m.LastEditor
	}
	return ""
}

func (m *DisLimitActivityListData) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *DisLimitActivityListData) GetStatusCode() int32 {
	if m != nil {
		return m.StatusCode
	}
	return 0
}

// 限时佣金活动新增/修改 request
type DisLimitActivityOperateRequest struct {
	// 活动id，编辑的时候传大于0的
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 活动名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 开始时间
	StartTime string `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//主体id
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityOperateRequest) Reset()         { *m = DisLimitActivityOperateRequest{} }
func (m *DisLimitActivityOperateRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityOperateRequest) ProtoMessage()    {}
func (*DisLimitActivityOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{29}
}

func (m *DisLimitActivityOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityOperateRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityOperateRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityOperateRequest.Merge(m, src)
}
func (m *DisLimitActivityOperateRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityOperateRequest.Size(m)
}
func (m *DisLimitActivityOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityOperateRequest proto.InternalMessageInfo

func (m *DisLimitActivityOperateRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityOperateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DisLimitActivityOperateRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DisLimitActivityOperateRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *DisLimitActivityOperateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 限时佣金活动失效 request
type DisLimitActivityStopRequest struct {
	// 活动id，编辑的时候传大于0的
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	//主体：1-阿闻，2-极宠家，3-福码购 4-百林康源
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityStopRequest) Reset()         { *m = DisLimitActivityStopRequest{} }
func (m *DisLimitActivityStopRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityStopRequest) ProtoMessage()    {}
func (*DisLimitActivityStopRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{30}
}

func (m *DisLimitActivityStopRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityStopRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityStopRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityStopRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityStopRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityStopRequest.Merge(m, src)
}
func (m *DisLimitActivityStopRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityStopRequest.Size(m)
}
func (m *DisLimitActivityStopRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityStopRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityStopRequest proto.InternalMessageInfo

func (m *DisLimitActivityStopRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityStopRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 活动分销商品列表request
type DisLimitActivityGoodsListRequest struct {
	// 搜索类型 1-商品名称搜索，2-商品sku搜索
	SearchType int32 `protobuf:"varint,1,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	// 搜索词
	SearchName string `protobuf:"bytes,2,opt,name=search_name,json=searchName,proto3" json:"search_name"`
	// 活动id
	ActivityId int64 `protobuf:"varint,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 页码
	Page int32 `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	// 每页条数
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityGoodsListRequest) Reset()         { *m = DisLimitActivityGoodsListRequest{} }
func (m *DisLimitActivityGoodsListRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityGoodsListRequest) ProtoMessage()    {}
func (*DisLimitActivityGoodsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{31}
}

func (m *DisLimitActivityGoodsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityGoodsListRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityGoodsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityGoodsListRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityGoodsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityGoodsListRequest.Merge(m, src)
}
func (m *DisLimitActivityGoodsListRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityGoodsListRequest.Size(m)
}
func (m *DisLimitActivityGoodsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityGoodsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityGoodsListRequest proto.InternalMessageInfo

func (m *DisLimitActivityGoodsListRequest) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *DisLimitActivityGoodsListRequest) GetSearchName() string {
	if m != nil {
		return m.SearchName
	}
	return ""
}

func (m *DisLimitActivityGoodsListRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityGoodsListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisLimitActivityGoodsListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DisLimitActivityGoodsListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 活动分销商品列表response
type DisLimitActivityGoodsListResponse struct {
	Code                 int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	ActivityInfo         *DisLimitActivityListData        `protobuf:"bytes,3,opt,name=activity_info,json=activityInfo,proto3" json:"activity_info"`
	Data                 []*DisLimitActivityGoodsListData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	Total                int64                            `protobuf:"varint,5,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *DisLimitActivityGoodsListResponse) Reset()         { *m = DisLimitActivityGoodsListResponse{} }
func (m *DisLimitActivityGoodsListResponse) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityGoodsListResponse) ProtoMessage()    {}
func (*DisLimitActivityGoodsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{32}
}

func (m *DisLimitActivityGoodsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityGoodsListResponse.Unmarshal(m, b)
}
func (m *DisLimitActivityGoodsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityGoodsListResponse.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityGoodsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityGoodsListResponse.Merge(m, src)
}
func (m *DisLimitActivityGoodsListResponse) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityGoodsListResponse.Size(m)
}
func (m *DisLimitActivityGoodsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityGoodsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityGoodsListResponse proto.InternalMessageInfo

func (m *DisLimitActivityGoodsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisLimitActivityGoodsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisLimitActivityGoodsListResponse) GetActivityInfo() *DisLimitActivityListData {
	if m != nil {
		return m.ActivityInfo
	}
	return nil
}

func (m *DisLimitActivityGoodsListResponse) GetData() []*DisLimitActivityGoodsListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisLimitActivityGoodsListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisLimitActivityGoodsListData struct {
	//sku商品id
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 图片地址
	Pic string `protobuf:"bytes,2,opt,name=pic,proto3" json:"pic"`
	// 商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	// 日常佣金
	NormalCommission float32 `protobuf:"fixed32,4,opt,name=normal_commission,json=normalCommission,proto3" json:"normal_commission"`
	// 活动佣金
	ActivityCommission float32 `protobuf:"fixed32,5,opt,name=activity_commission,json=activityCommission,proto3" json:"activity_commission"`
	// 电商店铺名称
	StoreId int64 `protobuf:"varint,6,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	// 电商店铺名称
	ShopName             string   `protobuf:"bytes,7,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityGoodsListData) Reset()         { *m = DisLimitActivityGoodsListData{} }
func (m *DisLimitActivityGoodsListData) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityGoodsListData) ProtoMessage()    {}
func (*DisLimitActivityGoodsListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{33}
}

func (m *DisLimitActivityGoodsListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityGoodsListData.Unmarshal(m, b)
}
func (m *DisLimitActivityGoodsListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityGoodsListData.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityGoodsListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityGoodsListData.Merge(m, src)
}
func (m *DisLimitActivityGoodsListData) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityGoodsListData.Size(m)
}
func (m *DisLimitActivityGoodsListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityGoodsListData.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityGoodsListData proto.InternalMessageInfo

func (m *DisLimitActivityGoodsListData) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisLimitActivityGoodsListData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *DisLimitActivityGoodsListData) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *DisLimitActivityGoodsListData) GetNormalCommission() float32 {
	if m != nil {
		return m.NormalCommission
	}
	return 0
}

func (m *DisLimitActivityGoodsListData) GetActivityCommission() float32 {
	if m != nil {
		return m.ActivityCommission
	}
	return 0
}

func (m *DisLimitActivityGoodsListData) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *DisLimitActivityGoodsListData) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

// 分销限时活动商品删除
type DisLimitActivityGoodsDeleteRequest struct {
	// 活动id
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 商品id
	SkuId int64 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityGoodsDeleteRequest) Reset()         { *m = DisLimitActivityGoodsDeleteRequest{} }
func (m *DisLimitActivityGoodsDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityGoodsDeleteRequest) ProtoMessage()    {}
func (*DisLimitActivityGoodsDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{34}
}

func (m *DisLimitActivityGoodsDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityGoodsDeleteRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityGoodsDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityGoodsDeleteRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityGoodsDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityGoodsDeleteRequest.Merge(m, src)
}
func (m *DisLimitActivityGoodsDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityGoodsDeleteRequest.Size(m)
}
func (m *DisLimitActivityGoodsDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityGoodsDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityGoodsDeleteRequest proto.InternalMessageInfo

func (m *DisLimitActivityGoodsDeleteRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityGoodsDeleteRequest) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisLimitActivityGoodsDeleteRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 商品限时佣金批量导入
type DisLimitActivityGoodsImportRequest struct {
	// 活动id
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 字节流
	File []byte `protobuf:"bytes,2,opt,name=file,proto3" json:"file"`
	//主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityGoodsImportRequest) Reset()         { *m = DisLimitActivityGoodsImportRequest{} }
func (m *DisLimitActivityGoodsImportRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityGoodsImportRequest) ProtoMessage()    {}
func (*DisLimitActivityGoodsImportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{35}
}

func (m *DisLimitActivityGoodsImportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityGoodsImportRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityGoodsImportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityGoodsImportRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityGoodsImportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityGoodsImportRequest.Merge(m, src)
}
func (m *DisLimitActivityGoodsImportRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityGoodsImportRequest.Size(m)
}
func (m *DisLimitActivityGoodsImportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityGoodsImportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityGoodsImportRequest proto.InternalMessageInfo

func (m *DisLimitActivityGoodsImportRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityGoodsImportRequest) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

func (m *DisLimitActivityGoodsImportRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 活动佣金设置，支持批量
type DisLimitActivityCommissionSetRequest struct {
	// 活动id
	ActivityId         int64                             `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	ActivityCommission []*DisLimitActivityCommissionData `protobuf:"bytes,2,rep,name=activity_commission,json=activityCommission,proto3" json:"activity_commission"`
	//主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityCommissionSetRequest) Reset()         { *m = DisLimitActivityCommissionSetRequest{} }
func (m *DisLimitActivityCommissionSetRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityCommissionSetRequest) ProtoMessage()    {}
func (*DisLimitActivityCommissionSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{36}
}

func (m *DisLimitActivityCommissionSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityCommissionSetRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityCommissionSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityCommissionSetRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityCommissionSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityCommissionSetRequest.Merge(m, src)
}
func (m *DisLimitActivityCommissionSetRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityCommissionSetRequest.Size(m)
}
func (m *DisLimitActivityCommissionSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityCommissionSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityCommissionSetRequest proto.InternalMessageInfo

func (m *DisLimitActivityCommissionSetRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityCommissionSetRequest) GetActivityCommission() []*DisLimitActivityCommissionData {
	if m != nil {
		return m.ActivityCommission
	}
	return nil
}

func (m *DisLimitActivityCommissionSetRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DisLimitActivityCommissionData struct {
	//sku商品id
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 活动佣金
	ActivityCommission   float32  `protobuf:"fixed32,2,opt,name=activity_commission,json=activityCommission,proto3" json:"activity_commission"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityCommissionData) Reset()         { *m = DisLimitActivityCommissionData{} }
func (m *DisLimitActivityCommissionData) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityCommissionData) ProtoMessage()    {}
func (*DisLimitActivityCommissionData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{37}
}

func (m *DisLimitActivityCommissionData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityCommissionData.Unmarshal(m, b)
}
func (m *DisLimitActivityCommissionData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityCommissionData.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityCommissionData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityCommissionData.Merge(m, src)
}
func (m *DisLimitActivityCommissionData) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityCommissionData.Size(m)
}
func (m *DisLimitActivityCommissionData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityCommissionData.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityCommissionData proto.InternalMessageInfo

func (m *DisLimitActivityCommissionData) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DisLimitActivityCommissionData) GetActivityCommission() float32 {
	if m != nil {
		return m.ActivityCommission
	}
	return 0
}

// 限时活动佣金日志
type DisLimitActivityLogRequest struct {
	// 活动id
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 页码
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 每页条数
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityLogRequest) Reset()         { *m = DisLimitActivityLogRequest{} }
func (m *DisLimitActivityLogRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityLogRequest) ProtoMessage()    {}
func (*DisLimitActivityLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{38}
}

func (m *DisLimitActivityLogRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityLogRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityLogRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityLogRequest.Merge(m, src)
}
func (m *DisLimitActivityLogRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityLogRequest.Size(m)
}
func (m *DisLimitActivityLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityLogRequest proto.InternalMessageInfo

func (m *DisLimitActivityLogRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityLogRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisLimitActivityLogRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 限时活动导入历史
type DisLimitActivityImportLogRequest struct {
	// 活动id
	ActivityId int64 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id"`
	// 页码
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 每页条数
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityImportLogRequest) Reset()         { *m = DisLimitActivityImportLogRequest{} }
func (m *DisLimitActivityImportLogRequest) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityImportLogRequest) ProtoMessage()    {}
func (*DisLimitActivityImportLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{39}
}

func (m *DisLimitActivityImportLogRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityImportLogRequest.Unmarshal(m, b)
}
func (m *DisLimitActivityImportLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityImportLogRequest.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityImportLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityImportLogRequest.Merge(m, src)
}
func (m *DisLimitActivityImportLogRequest) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityImportLogRequest.Size(m)
}
func (m *DisLimitActivityImportLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityImportLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityImportLogRequest proto.InternalMessageInfo

func (m *DisLimitActivityImportLogRequest) GetActivityId() int64 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DisLimitActivityImportLogRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DisLimitActivityImportLogRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type DisLimitActivityImportLogResponse struct {
	Code                 int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*DisLimitActivityImportLogData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int64                            `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *DisLimitActivityImportLogResponse) Reset()         { *m = DisLimitActivityImportLogResponse{} }
func (m *DisLimitActivityImportLogResponse) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityImportLogResponse) ProtoMessage()    {}
func (*DisLimitActivityImportLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{40}
}

func (m *DisLimitActivityImportLogResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityImportLogResponse.Unmarshal(m, b)
}
func (m *DisLimitActivityImportLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityImportLogResponse.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityImportLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityImportLogResponse.Merge(m, src)
}
func (m *DisLimitActivityImportLogResponse) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityImportLogResponse.Size(m)
}
func (m *DisLimitActivityImportLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityImportLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityImportLogResponse proto.InternalMessageInfo

func (m *DisLimitActivityImportLogResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisLimitActivityImportLogResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisLimitActivityImportLogResponse) GetData() []*DisLimitActivityImportLogData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisLimitActivityImportLogResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DisLimitActivityImportLogData struct {
	CreatedAt            string   `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Result               string   `protobuf:"bytes,2,opt,name=result,proto3" json:"result"`
	DownUrl              string   `protobuf:"bytes,3,opt,name=down_url,json=downUrl,proto3" json:"down_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityImportLogData) Reset()         { *m = DisLimitActivityImportLogData{} }
func (m *DisLimitActivityImportLogData) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityImportLogData) ProtoMessage()    {}
func (*DisLimitActivityImportLogData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{41}
}

func (m *DisLimitActivityImportLogData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityImportLogData.Unmarshal(m, b)
}
func (m *DisLimitActivityImportLogData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityImportLogData.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityImportLogData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityImportLogData.Merge(m, src)
}
func (m *DisLimitActivityImportLogData) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityImportLogData.Size(m)
}
func (m *DisLimitActivityImportLogData) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityImportLogData.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityImportLogData proto.InternalMessageInfo

func (m *DisLimitActivityImportLogData) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *DisLimitActivityImportLogData) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *DisLimitActivityImportLogData) GetDownUrl() string {
	if m != nil {
		return m.DownUrl
	}
	return ""
}

// 下载模板
type DisLimitActivityDownTemplateResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisLimitActivityDownTemplateResponse) Reset()         { *m = DisLimitActivityDownTemplateResponse{} }
func (m *DisLimitActivityDownTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*DisLimitActivityDownTemplateResponse) ProtoMessage()    {}
func (*DisLimitActivityDownTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fd1b394c44b6dd2, []int{42}
}

func (m *DisLimitActivityDownTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisLimitActivityDownTemplateResponse.Unmarshal(m, b)
}
func (m *DisLimitActivityDownTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisLimitActivityDownTemplateResponse.Marshal(b, m, deterministic)
}
func (m *DisLimitActivityDownTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisLimitActivityDownTemplateResponse.Merge(m, src)
}
func (m *DisLimitActivityDownTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_DisLimitActivityDownTemplateResponse.Size(m)
}
func (m *DisLimitActivityDownTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisLimitActivityDownTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisLimitActivityDownTemplateResponse proto.InternalMessageInfo

func (m *DisLimitActivityDownTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisLimitActivityDownTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DisLimitActivityDownTemplateResponse) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func init() {
	proto.RegisterType((*DisResponse)(nil), "sh.DisResponse")
	proto.RegisterType((*EmptyRequest)(nil), "sh.EmptyRequest")
	proto.RegisterType((*DisCategoriesRequest)(nil), "sh.DisCategoriesRequest")
	proto.RegisterType((*DisCategoriesResponse)(nil), "sh.DisCategoriesResponse")
	proto.RegisterType((*DisCategoriesResponse_Category)(nil), "sh.DisCategoriesResponse.Category")
	proto.RegisterType((*DisSpuListRequest)(nil), "sh.DisSpuListRequest")
	proto.RegisterType((*DisSpuListData)(nil), "sh.DisSpuListData")
	proto.RegisterType((*DisSpuListResponse)(nil), "sh.DisSpuListResponse")
	proto.RegisterType((*DisSpuDetailRequest)(nil), "sh.DisSpuDetailRequest")
	proto.RegisterType((*DisSpuDetailResponse)(nil), "sh.DisSpuDetailResponse")
	proto.RegisterType((*DisSpuDetailResponse_Sku)(nil), "sh.DisSpuDetailResponse.Sku")
	proto.RegisterType((*DisSpuUpdateRequest)(nil), "sh.DisSpuUpdateRequest")
	proto.RegisterType((*DisSpuLogsRequest)(nil), "sh.DisSpuLogsRequest")
	proto.RegisterType((*DisSpuLogsResponse)(nil), "sh.DisSpuLogsResponse")
	proto.RegisterType((*DisSpuLogsResponse_SpuLog)(nil), "sh.DisSpuLogsResponse.SpuLog")
	proto.RegisterType((*DisSkuUpdateRequest)(nil), "sh.DisSkuUpdateRequest")
	proto.RegisterType((*DisNotDisSkuListRequest)(nil), "sh.DisNotDisSkuListRequest")
	proto.RegisterType((*DisNotDisSkuListResponse)(nil), "sh.DisNotDisSkuListResponse")
	proto.RegisterType((*DisNotDisSkuListResponse_Sku)(nil), "sh.DisNotDisSkuListResponse.Sku")
	proto.RegisterType((*DisImportTemplateRequest)(nil), "sh.DisImportTemplateRequest")
	proto.RegisterType((*DisImportTemplateResponse)(nil), "sh.DisImportTemplateResponse")
	proto.RegisterType((*DisImportListRequest)(nil), "sh.DisImportListRequest")
	proto.RegisterType((*DisImportListResponse)(nil), "sh.DisImportListResponse")
	proto.RegisterType((*DisImportListResponse_List)(nil), "sh.DisImportListResponse.List")
	proto.RegisterType((*DisImportRequest)(nil), "sh.DisImportRequest")
	proto.RegisterType((*DisImportDownloadResponse)(nil), "sh.DisImportDownloadResponse")
	proto.RegisterType((*DisSetGlobalCommissionRequest)(nil), "sh.DisSetGlobalCommissionRequest")
	proto.RegisterType((*DisGlobalCommissionResponse)(nil), "sh.DisGlobalCommissionResponse")
	proto.RegisterType((*DisGoodsListRequest)(nil), "sh.DisGoodsListRequest")
	proto.RegisterType((*DisGoodsListResponse)(nil), "sh.DisGoodsListResponse")
	proto.RegisterType((*DisGoodsListData)(nil), "sh.DisGoodsListData")
	proto.RegisterType((*DisLimitActivityListRequest)(nil), "sh.DisLimitActivityListRequest")
	proto.RegisterType((*DisLimitActivityListResponse)(nil), "sh.DisLimitActivityListResponse")
	proto.RegisterType((*DisLimitActivityListData)(nil), "sh.DisLimitActivityListData")
	proto.RegisterType((*DisLimitActivityOperateRequest)(nil), "sh.DisLimitActivityOperateRequest")
	proto.RegisterType((*DisLimitActivityStopRequest)(nil), "sh.DisLimitActivityStopRequest")
	proto.RegisterType((*DisLimitActivityGoodsListRequest)(nil), "sh.DisLimitActivityGoodsListRequest")
	proto.RegisterType((*DisLimitActivityGoodsListResponse)(nil), "sh.DisLimitActivityGoodsListResponse")
	proto.RegisterType((*DisLimitActivityGoodsListData)(nil), "sh.DisLimitActivityGoodsListData")
	proto.RegisterType((*DisLimitActivityGoodsDeleteRequest)(nil), "sh.DisLimitActivityGoodsDeleteRequest")
	proto.RegisterType((*DisLimitActivityGoodsImportRequest)(nil), "sh.DisLimitActivityGoodsImportRequest")
	proto.RegisterType((*DisLimitActivityCommissionSetRequest)(nil), "sh.DisLimitActivityCommissionSetRequest")
	proto.RegisterType((*DisLimitActivityCommissionData)(nil), "sh.DisLimitActivityCommissionData")
	proto.RegisterType((*DisLimitActivityLogRequest)(nil), "sh.DisLimitActivityLogRequest")
	proto.RegisterType((*DisLimitActivityImportLogRequest)(nil), "sh.DisLimitActivityImportLogRequest")
	proto.RegisterType((*DisLimitActivityImportLogResponse)(nil), "sh.DisLimitActivityImportLogResponse")
	proto.RegisterType((*DisLimitActivityImportLogData)(nil), "sh.DisLimitActivityImportLogData")
	proto.RegisterType((*DisLimitActivityDownTemplateResponse)(nil), "sh.DisLimitActivityDownTemplateResponse")
}

func init() { proto.RegisterFile("sh/distribution.proto", fileDescriptor_0fd1b394c44b6dd2) }

var fileDescriptor_0fd1b394c44b6dd2 = []byte{
	// 2175 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x57, 0x4f, 0xcf, 0x97, 0x9f, 0x1d, 0xaf, 0x53, 0xb1, 0x9d, 0xf1, 0xd8, 0x8e, 0x9d, 0x56,
	0x76, 0xb1, 0x84, 0x18, 0xef, 0x3a, 0x1b, 0x84, 0x84, 0x40, 0x32, 0x99, 0xb0, 0x6b, 0x94, 0x35,
	0x30, 0x8e, 0x57, 0x02, 0x04, 0xa3, 0xce, 0x74, 0x79, 0x5c, 0x9a, 0x9e, 0xae, 0xde, 0xae, 0x9a,
	0xcd, 0xc7, 0x69, 0x0f, 0xfc, 0x09, 0x7b, 0x44, 0x1c, 0x40, 0xe2, 0x1f, 0xe0, 0x0f, 0x80, 0x3b,
	0xe2, 0xc6, 0x91, 0x0b, 0xe2, 0x88, 0xc4, 0x9d, 0xc3, 0x4a, 0xa8, 0x3e, 0xba, 0xbb, 0xfa, 0x6b,
	0x26, 0x99, 0x6c, 0xf6, 0xe4, 0xae, 0x57, 0x55, 0xaf, 0x5e, 0xbd, 0xfa, 0xbd, 0xcf, 0x31, 0x6c,
	0xb1, 0xeb, 0x63, 0x8f, 0x30, 0x1e, 0x91, 0xa7, 0x33, 0x4e, 0x68, 0xd0, 0x0b, 0x23, 0xca, 0x29,
	0xaa, 0xb1, 0xeb, 0xee, 0x9d, 0x31, 0xa5, 0x63, 0x1f, 0x1f, 0x4b, 0xca, 0xd3, 0xd9, 0xd5, 0xf1,
	0xb3, 0xc8, 0x0d, 0x43, 0x1c, 0x31, 0xb5, 0xc6, 0xf9, 0x3e, 0xac, 0xf6, 0x09, 0x1b, 0x60, 0x16,
	0xd2, 0x80, 0x61, 0x84, 0xa0, 0x3e, 0xa2, 0x1e, 0xee, 0x58, 0x87, 0xd6, 0x51, 0x63, 0x20, 0xbf,
	0x51, 0x07, 0x5a, 0x53, 0xcc, 0x98, 0x3b, 0xc6, 0x9d, 0xda, 0xa1, 0x75, 0xb4, 0x32, 0x88, 0x87,
	0xce, 0x3a, 0xac, 0x3d, 0x9a, 0x86, 0xfc, 0xc5, 0x00, 0x7f, 0x36, 0xc3, 0x8c, 0x3b, 0xf7, 0x61,
	0xb3, 0x4f, 0xd8, 0x43, 0x97, 0xe3, 0x31, 0x8d, 0x08, 0x66, 0x9a, 0x8e, 0x76, 0x61, 0x25, 0x74,
	0x23, 0x1c, 0xf0, 0x21, 0xf1, 0x34, 0xeb, 0xb6, 0x22, 0x9c, 0x79, 0xce, 0x3f, 0x2c, 0xd8, 0xca,
	0xed, 0x5a, 0x46, 0x18, 0xf4, 0x5d, 0xa8, 0x7b, 0x2e, 0x77, 0x3b, 0xf6, 0xa1, 0x7d, 0xb4, 0x7a,
	0xe2, 0xf4, 0xd8, 0x75, 0xaf, 0x94, 0x6d, 0x4f, 0x93, 0x5e, 0x0c, 0xe4, 0xfa, 0xee, 0x25, 0xb4,
	0x63, 0x0a, 0x5a, 0x87, 0x5a, 0x22, 0x61, 0x8d, 0x78, 0x42, 0x82, 0xc0, 0x9d, 0xc6, 0x47, 0xc9,
	0x6f, 0xf4, 0x2e, 0xac, 0x8f, 0xae, 0x89, 0xef, 0x45, 0x38, 0x18, 0x8e, 0xe8, 0x2c, 0xe0, 0x1d,
	0x5b, 0xae, 0xbf, 0x11, 0x53, 0x1f, 0x0a, 0xa2, 0xf3, 0x87, 0x1a, 0xdc, 0xec, 0x13, 0x76, 0x11,
	0xce, 0x1e, 0x13, 0xc6, 0x63, 0x4d, 0x20, 0xa8, 0x87, 0x42, 0x76, 0x7d, 0x25, 0xf1, 0xad, 0xb4,
	0x33, 0xc6, 0x43, 0x46, 0x5e, 0xaa, 0x93, 0xa4, 0x76, 0xc6, 0xf8, 0x82, 0xbc, 0xc4, 0x68, 0x07,
	0xda, 0x53, 0x12, 0x0c, 0x23, 0x97, 0x63, 0x79, 0x4e, 0x6d, 0xd0, 0x9a, 0x92, 0x60, 0xe0, 0x72,
	0x35, 0xe5, 0x3e, 0x57, 0x53, 0x75, 0x3d, 0xe5, 0x3e, 0x97, 0x53, 0xfb, 0x00, 0x63, 0x4a, 0x3d,
	0x36, 0x94, 0xd2, 0x37, 0xa4, 0xf4, 0x2b, 0x92, 0x72, 0x2e, 0xae, 0xb0, 0x05, 0x4d, 0x36, 0x99,
	0x89, 0xc7, 0x68, 0xca, 0xa9, 0x06, 0x9b, 0xcc, 0xce, 0x3c, 0x49, 0x0e, 0x25, 0xb9, 0xa5, 0xc9,
	0xa1, 0x20, 0x1f, 0xc0, 0xea, 0x48, 0x2b, 0x48, 0xcc, 0xb5, 0xa5, 0x84, 0x10, 0x93, 0xce, 0x3c,
	0x74, 0x17, 0xd6, 0x08, 0x1b, 0x46, 0x78, 0x44, 0xa7, 0x53, 0x1c, 0x78, 0x9d, 0x15, 0xb9, 0x62,
	0x55, 0xc0, 0x4a, 0x93, 0x04, 0x6b, 0x1a, 0x8d, 0xc5, 0x76, 0x90, 0x93, 0x0d, 0x1a, 0x8d, 0xcf,
	0x3c, 0xe7, 0x9f, 0x16, 0xac, 0xa7, 0x4a, 0xea, 0xbb, 0xdc, 0x35, 0x84, 0x50, 0x3a, 0xd2, 0x42,
	0xec, 0xc2, 0x0a, 0x99, 0x0a, 0x2d, 0xcd, 0x22, 0x5f, 0x3f, 0x47, 0x5b, 0x12, 0x2e, 0x23, 0x3f,
	0x79, 0x26, 0xdb, 0x78, 0xa6, 0x4d, 0x68, 0x84, 0x11, 0x19, 0xc5, 0xaa, 0x51, 0x03, 0xd4, 0x85,
	0x76, 0x2c, 0xb8, 0x56, 0x4b, 0x32, 0x16, 0xfa, 0x74, 0x3d, 0x6f, 0xc8, 0xc9, 0x14, 0x6b, 0xbd,
	0xb4, 0x5c, 0xcf, 0x7b, 0x42, 0xa6, 0xb8, 0x70, 0xc3, 0x56, 0xf1, 0x86, 0x08, 0xea, 0x8c, 0x46,
	0x5c, 0xab, 0x47, 0x7e, 0x3b, 0x5f, 0x58, 0x80, 0x4c, 0x0c, 0x2c, 0x85, 0xeb, 0xf7, 0x34, 0xae,
	0xdb, 0x12, 0xd7, 0x48, 0xe3, 0xda, 0x50, 0x99, 0xc2, 0xb1, 0xb8, 0x30, 0xa7, 0xdc, 0xf5, 0xb5,
	0xfa, 0xd5, 0xc0, 0xe1, 0x70, 0x4b, 0xad, 0xee, 0x63, 0xee, 0x12, 0x3f, 0xc6, 0x61, 0x85, 0x96,
	0x63, 0x78, 0xd6, 0xaa, 0xe0, 0x69, 0xe7, 0xe0, 0x99, 0xbe, 0x6b, 0xdd, 0x7c, 0xd7, 0x2f, 0x6c,
	0xe9, 0x09, 0x8c, 0x63, 0x97, 0xba, 0xfa, 0x3d, 0xb0, 0x59, 0x38, 0x93, 0x87, 0x96, 0xdf, 0x5c,
	0x4c, 0xa3, 0xf7, 0xa1, 0xce, 0x26, 0x33, 0xd6, 0xa9, 0x4b, 0x05, 0xed, 0xa5, 0xcb, 0xb2, 0x67,
	0xf7, 0x2e, 0x26, 0xb3, 0x81, 0x5c, 0x99, 0xaa, 0xaa, 0x61, 0xa8, 0xaa, 0xfb, 0x37, 0x0b, 0xec,
	0x8b, 0xc9, 0xcc, 0xb0, 0x8e, 0x58, 0x37, 0x93, 0x85, 0x08, 0xdc, 0x85, 0x15, 0x16, 0xe2, 0xd1,
	0xd0, 0x80, 0x61, 0x5b, 0x10, 0xa4, 0xb9, 0x7d, 0x0b, 0xde, 0x11, 0x28, 0x21, 0x8c, 0x11, 0x1a,
	0x98, 0xf6, 0xba, 0x9e, 0x92, 0xa5, 0xd9, 0x7e, 0x08, 0xdb, 0x01, 0x8d, 0xa6, 0xae, 0x3f, 0xcc,
	0xaf, 0x6f, 0xc8, 0xf5, 0x9b, 0x6a, 0xf6, 0x61, 0x76, 0xd7, 0x26, 0x34, 0x9e, 0x45, 0x84, 0xc7,
	0xa0, 0x55, 0x03, 0xe7, 0x77, 0x56, 0xfc, 0xf2, 0x97, 0xa1, 0xe7, 0x72, 0xbc, 0xe0, 0xe5, 0x7f,
	0x90, 0x43, 0x78, 0x4d, 0xea, 0xbc, 0xdb, 0x53, 0xe1, 0xa3, 0x17, 0x87, 0x8f, 0xde, 0x8f, 0x28,
	0xf5, 0x3f, 0x75, 0xfd, 0x19, 0xce, 0xa2, 0xff, 0x58, 0xa3, 0x5f, 0x3d, 0xd5, 0x6e, 0x61, 0xdb,
	0x59, 0xc0, 0xef, 0x9f, 0xa8, 0x7d, 0xca, 0x34, 0x7e, 0x95, 0x78, 0x47, 0x3a, 0x66, 0x5f, 0x33,
	0x2a, 0x9d, 0xff, 0xa4, 0x76, 0x27, 0xb9, 0x2f, 0x05, 0xbe, 0x0f, 0x32, 0xf1, 0x64, 0xdf, 0x40,
	0x9f, 0xc1, 0xb3, 0xa7, 0xc6, 0x79, 0x13, 0xac, 0x9b, 0xb8, 0xfa, 0x0d, 0x34, 0xd5, 0x2a, 0x21,
	0xf4, 0x8c, 0xe1, 0x48, 0xa1, 0xc4, 0x52, 0x28, 0x11, 0x04, 0x89, 0x92, 0x7d, 0x80, 0x51, 0x84,
	0x5d, 0x8e, 0xbd, 0xa1, 0xcb, 0xb5, 0x30, 0x2b, 0x9a, 0x72, 0xca, 0x85, 0xa0, 0x23, 0x1a, 0x70,
	0xac, 0xe3, 0xcd, 0xca, 0x20, 0x1e, 0x3a, 0x5f, 0xe9, 0x97, 0x9e, 0x94, 0xbc, 0x74, 0x09, 0x8e,
	0x4f, 0x62, 0xb8, 0xa8, 0x27, 0xde, 0x2b, 0xbc, 0xd5, 0x05, 0x8f, 0x48, 0x30, 0x56, 0x8f, 0xa5,
	0x96, 0xa2, 0x9f, 0x57, 0x02, 0xb3, 0xea, 0xc1, 0x7f, 0xec, 0x53, 0x97, 0x2b, 0x1e, 0xe5, 0xa8,
	0xfd, 0x00, 0x9a, 0x84, 0x0d, 0x3d, 0xc2, 0xa4, 0xb2, 0xe6, 0x43, 0xad, 0x41, 0x58, 0x9f, 0x30,
	0xc3, 0xd9, 0x34, 0x4c, 0x67, 0xf3, 0x47, 0x0b, 0x6e, 0xf7, 0x09, 0x3b, 0xa7, 0x5c, 0x69, 0xc1,
	0x8c, 0xb7, 0x59, 0x1d, 0x94, 0x44, 0xba, 0x9a, 0x19, 0xe9, 0xca, 0xe2, 0x48, 0x0c, 0xbe, 0x7a,
	0x15, 0xf8, 0x1a, 0x95, 0x2e, 0xb1, 0x69, 0x4a, 0xf9, 0x3f, 0x0b, 0x3a, 0x45, 0x29, 0x97, 0x42,
	0xe6, 0x87, 0x19, 0x64, 0x1e, 0x6a, 0x64, 0x96, 0x72, 0x56, 0x4e, 0x6f, 0x0e, 0x38, 0x47, 0xcb,
	0xfb, 0xbc, 0x57, 0x8e, 0xba, 0x4e, 0x4f, 0x5e, 0xfd, 0x6c, 0x1a, 0xd2, 0x88, 0x3f, 0xc1, 0xd3,
	0xd0, 0x37, 0x50, 0x8a, 0xa0, 0xce, 0x5f, 0x84, 0xc9, 0xd5, 0xc5, 0xb7, 0x83, 0x61, 0xa7, 0x64,
	0xfd, 0x52, 0xba, 0xea, 0x42, 0x9b, 0x6b, 0x0e, 0x52, 0xd0, 0xb5, 0x41, 0x32, 0x76, 0xb8, 0x0c,
	0x52, 0xea, 0x98, 0x37, 0x4a, 0xd2, 0xe2, 0x3b, 0xd8, 0xe9, 0x1d, 0xd0, 0x6d, 0x68, 0x89, 0xbf,
	0x69, 0x68, 0x6c, 0x8a, 0xe1, 0x99, 0x27, 0xcc, 0x75, 0x2b, 0x77, 0xec, 0x52, 0x37, 0x3b, 0xc9,
	0xa0, 0xe0, 0x8e, 0x46, 0x41, 0x91, 0x6d, 0x4f, 0x0e, 0xe6, 0x61, 0xc0, 0x87, 0xba, 0x58, 0x53,
	0xc8, 0x7e, 0x17, 0x78, 0xa4, 0x6d, 0x68, 0x46, 0x98, 0xcd, 0xfc, 0xd8, 0x21, 0xe9, 0x91, 0xd8,
	0xa6, 0xbe, 0x24, 0x6a, 0xea, 0x6a, 0x9b, 0xa2, 0x5c, 0x46, 0xbe, 0xe3, 0xc2, 0x46, 0x22, 0xa7,
	0xa1, 0xf1, 0x2b, 0xe2, 0xab, 0x9b, 0xaf, 0x0d, 0xe4, 0x77, 0xa2, 0xd4, 0x9a, 0xa1, 0x54, 0x25,
	0xa1, 0x9d, 0x48, 0x58, 0x91, 0x7e, 0xfc, 0xda, 0xc0, 0x4f, 0x9f, 0x3e, 0x0b, 0x7c, 0xea, 0x7a,
	0x4b, 0x6a, 0x39, 0x96, 0xcc, 0x4e, 0x25, 0x73, 0xce, 0x61, 0x5f, 0x58, 0x1a, 0xe6, 0x1f, 0xf9,
	0xf4, 0x69, 0xc6, 0xb1, 0xe9, 0xeb, 0x7c, 0x07, 0x90, 0x87, 0xaf, 0x5c, 0xa1, 0x82, 0xd4, 0x5f,
	0xca, 0x03, 0x6b, 0x83, 0x9b, 0x7a, 0x26, 0xdd, 0xe5, 0xbc, 0x84, 0xdd, 0x3e, 0x61, 0x45, 0x66,
	0x4b, 0x09, 0x5c, 0x7e, 0xb6, 0x5d, 0x75, 0xf6, 0x5f, 0x55, 0xf0, 0xf8, 0x48, 0xd4, 0x06, 0xa6,
	0x0d, 0x1c, 0xc0, 0x2a, 0xc3, 0x6e, 0x34, 0xba, 0x1e, 0x1a, 0xd6, 0x09, 0x8a, 0xf4, 0x44, 0x3c,
	0x45, 0xba, 0xc0, 0xa8, 0x90, 0xf4, 0x82, 0x73, 0xd3, 0x71, 0xda, 0x55, 0x56, 0x54, 0xcf, 0x59,
	0xd1, 0x01, 0xac, 0xba, 0x23, 0x4e, 0x3e, 0x27, 0xfc, 0x45, 0xec, 0xe3, 0xed, 0x01, 0xc4, 0xa4,
	0x33, 0xaf, 0xca, 0xb3, 0xfe, 0xd6, 0x92, 0x76, 0x6c, 0x5c, 0x61, 0x29, 0xc5, 0x1d, 0x65, 0xec,
	0x69, 0x53, 0xdb, 0x53, 0xc2, 0xb5, 0x2c, 0xd3, 0xae, 0x4b, 0x11, 0x75, 0xa6, 0xfd, 0x65, 0x4d,
	0x02, 0x3b, 0xb3, 0x21, 0xe7, 0x57, 0xed, 0xd8, 0xaf, 0x1e, 0xc0, 0xaa, 0xaa, 0xcf, 0xa4, 0x33,
	0x8d, 0x95, 0x27, 0x49, 0x67, 0x82, 0x92, 0x2b, 0xe0, 0xec, 0x7c, 0x01, 0x97, 0x49, 0x37, 0xeb,
	0xb9, 0x74, 0x33, 0x61, 0xae, 0x3c, 0xb1, 0x4a, 0x1d, 0x15, 0xbb, 0x9f, 0xc9, 0x22, 0xe8, 0x3e,
	0x6c, 0x7b, 0x84, 0x0d, 0x33, 0x11, 0x5d, 0x45, 0xf3, 0xa6, 0x5c, 0x7b, 0xcb, 0x13, 0xd1, 0x24,
	0x8d, 0xd9, 0x71, 0xb5, 0xc9, 0x38, 0x8d, 0x70, 0x5c, 0x1e, 0xda, 0x83, 0x96, 0x1c, 0xab, 0x28,
	0xc1, 0xae, 0x69, 0xa8, 0xa4, 0x69, 0x6b, 0x69, 0xae, 0x69, 0x28, 0xa4, 0x71, 0xfe, 0x62, 0x49,
	0x74, 0x3f, 0x26, 0x53, 0xc2, 0x4f, 0xf5, 0x5b, 0x9a, 0x40, 0xdb, 0x86, 0x26, 0xe3, 0x2e, 0x9f,
	0x31, 0xfd, 0x4c, 0x7a, 0x94, 0x07, 0x60, 0x6d, 0x11, 0x00, 0xed, 0x4a, 0x00, 0xbe, 0x69, 0xe4,
	0xfe, 0xd2, 0x82, 0xbd, 0xf2, 0x1b, 0x2c, 0x85, 0xb3, 0xf7, 0x33, 0x38, 0x8b, 0xcb, 0x95, 0x02,
	0xf7, 0x85, 0x78, 0xfb, 0xb7, 0x4a, 0x28, 0x4a, 0x37, 0xe6, 0x6d, 0xc9, 0x2a, 0xd8, 0x52, 0x59,
	0x67, 0x63, 0x1f, 0x80, 0x71, 0x37, 0xe2, 0xaa, 0x04, 0xd6, 0xa0, 0x93, 0x14, 0x59, 0x04, 0xef,
	0x40, 0x1b, 0x07, 0xba, 0x3e, 0x56, 0x98, 0x6b, 0xe1, 0x40, 0xd5, 0xc7, 0x07, 0xb0, 0xea, 0xbb,
	0x8c, 0x0f, 0xb1, 0x47, 0x38, 0x8d, 0x74, 0x65, 0x0d, 0x82, 0xf4, 0x48, 0x52, 0x8c, 0x57, 0x56,
	0x45, 0x8a, 0xf9, 0xca, 0xf2, 0x6b, 0x28, 0x35, 0xd8, 0xd2, 0xaf, 0x2c, 0x49, 0x0f, 0xa9, 0x87,
	0x9d, 0x3f, 0x59, 0x70, 0x27, 0x7f, 0xcb, 0x9f, 0x86, 0x38, 0x32, 0x32, 0x88, 0x6f, 0xf8, 0xae,
	0x15, 0x59, 0xe8, 0x65, 0x11, 0xe6, 0x17, 0x9c, 0x86, 0xaf, 0x2c, 0x64, 0xca, 0xb6, 0x66, 0xb2,
	0xfd, 0xbb, 0x05, 0x87, 0x79, 0xbe, 0x6f, 0xc1, 0x59, 0xe7, 0xc4, 0xb3, 0xcb, 0x74, 0xf8, 0xb5,
	0x18, 0xd3, 0xbf, 0x2c, 0xb8, 0x3b, 0xe7, 0x3e, 0x4b, 0x59, 0xd4, 0x29, 0xdc, 0x48, 0x85, 0x0f,
	0xae, 0xa8, 0x2e, 0x4a, 0xe6, 0x9b, 0xd6, 0x5a, 0x72, 0xb9, 0xe0, 0x8a, 0xa2, 0x07, 0xda, 0x28,
	0x55, 0x0f, 0xe1, 0x6e, 0xd9, 0xce, 0xb9, 0x91, 0xa0, 0x61, 0x5a, 0xe6, 0x57, 0x96, 0x4c, 0x10,
	0xaa, 0x77, 0x57, 0x85, 0x85, 0x0d, 0xb0, 0x43, 0x32, 0xd2, 0xd7, 0x13, 0x9f, 0x8b, 0xe2, 0xc0,
	0xb7, 0xe1, 0x66, 0xa1, 0x2e, 0xd3, 0xa9, 0xf7, 0x46, 0xbe, 0xea, 0x42, 0xc7, 0x70, 0x2b, 0x51,
	0x93, 0xb1, 0x5c, 0xc5, 0x07, 0x14, 0x4f, 0x19, 0x1b, 0x4c, 0x97, 0xdf, 0x9c, 0xe3, 0xf2, 0x5b,
	0x39, 0x97, 0xcf, 0xc0, 0x29, 0xbd, 0x7e, 0x1f, 0xfb, 0xf8, 0x35, 0xcc, 0x36, 0x55, 0x52, 0xcd,
	0x54, 0x52, 0x0a, 0x2c, 0xdb, 0x04, 0x56, 0x58, 0x71, 0x68, 0x36, 0xd1, 0x7c, 0x15, 0x5f, 0x21,
	0xf3, 0xbd, 0x9a, 0x91, 0x89, 0x56, 0x9c, 0xf8, 0x67, 0x0b, 0xee, 0xe5, 0x8f, 0x4c, 0xb5, 0x77,
	0x81, 0x5f, 0xfd, 0xd0, 0x8b, 0xf2, 0x97, 0xa9, 0x65, 0x3a, 0xd9, 0x15, 0xe7, 0x48, 0x34, 0x96,
	0xbd, 0x5e, 0x85, 0xd4, 0xd7, 0x45, 0x7f, 0x9a, 0x65, 0x56, 0x05, 0xce, 0xe3, 0x2a, 0x21, 0x2b,
	0xe0, 0xe3, 0x04, 0xd0, 0x2d, 0x58, 0x1f, 0x1d, 0xbf, 0xce, 0x4b, 0xbc, 0x5e, 0xd7, 0x87, 0x17,
	0x3d, 0xa5, 0xae, 0x86, 0xde, 0xe6, 0xa9, 0xbf, 0x2f, 0x71, 0x68, 0xc6, 0xb1, 0x4b, 0x39, 0xb4,
	0x07, 0x99, 0x14, 0xa1, 0xd4, 0x1b, 0x25, 0x47, 0x2c, 0xcc, 0x13, 0x3e, 0x2b, 0x3a, 0xa3, 0xcc,
	0xe6, 0x5c, 0x99, 0x67, 0x55, 0x97, 0x79, 0xb5, 0x4c, 0x99, 0xb7, 0x03, 0x6d, 0x8f, 0x3e, 0x0b,
	0x64, 0x91, 0xa7, 0x3b, 0x52, 0x62, 0x2c, 0x4a, 0xbc, 0xab, 0xa2, 0x61, 0x88, 0x32, 0xec, 0x0d,
	0x4b, 0xf9, 0x0d, 0xb0, 0xd3, 0xb3, 0xc4, 0xe7, 0xc9, 0x7f, 0xd7, 0x64, 0xf1, 0x92, 0xfc, 0xee,
	0x75, 0x81, 0xa3, 0xcf, 0x45, 0x82, 0x7b, 0x0a, 0x90, 0xfe, 0xee, 0x83, 0x3a, 0x25, 0x3f, 0x05,
	0x49, 0x34, 0x74, 0x77, 0x2a, 0x7f, 0x24, 0x42, 0xdf, 0x83, 0x96, 0x6e, 0x34, 0xa3, 0xad, 0x6c,
	0xe3, 0x39, 0xde, 0xbc, 0x9d, 0x27, 0xeb, 0x9d, 0x3f, 0x84, 0x95, 0xa4, 0xf7, 0x8c, 0x6e, 0x17,
	0xbb, 0xd1, 0x6a, 0x77, 0xa7, 0xaa, 0x4d, 0x8d, 0x1e, 0xc8, 0xfd, 0xaa, 0x95, 0x67, 0xee, 0xcf,
	0x34, 0xf7, 0xba, 0xef, 0xe8, 0x89, 0xbc, 0xc0, 0x74, 0xcc, 0x32, 0x02, 0xa7, 0xdd, 0xd5, 0x8c,
	0xc0, 0x66, 0x5b, 0x54, 0x1c, 0x38, 0x29, 0x1c, 0x38, 0x59, 0x74, 0xe0, 0x4f, 0xe0, 0x46, 0xa6,
	0xe5, 0x84, 0x76, 0xcb, 0x1b, 0x51, 0x6a, 0xfb, 0xde, 0xbc, 0x2e, 0x15, 0xfa, 0x04, 0xd6, 0xb3,
	0xdd, 0x1e, 0xb4, 0x97, 0xe9, 0x67, 0xe4, 0x9a, 0x46, 0xdd, 0xfd, 0x8a, 0x59, 0xcd, 0xee, 0x18,
	0x9a, 0x6a, 0x06, 0x6d, 0x66, 0x16, 0x56, 0xde, 0xe5, 0x14, 0x20, 0x6d, 0x9c, 0x24, 0x80, 0x29,
	0x74, 0x86, 0x12, 0xc0, 0x94, 0x34, 0x6f, 0xce, 0x65, 0x13, 0xb2, 0x2c, 0x4f, 0x45, 0xa5, 0x1e,
	0x3c, 0x9b, 0xc4, 0x16, 0x45, 0xfa, 0x85, 0x2c, 0x6a, 0x0b, 0xb9, 0x0b, 0x3a, 0xa8, 0xca, 0x6a,
	0x62, 0x4e, 0x87, 0xd5, 0x0b, 0x34, 0xeb, 0x8f, 0x8b, 0xac, 0x45, 0xaa, 0x5a, 0xce, 0xda, 0x48,
	0x62, 0x8b, 0x42, 0x5e, 0xcb, 0x46, 0x4b, 0x79, 0xa2, 0x83, 0xee, 0xcd, 0xcd, 0xa2, 0x62, 0x9e,
	0xef, 0x2e, 0x58, 0xa5, 0x4f, 0xfa, 0xb4, 0x98, 0x5e, 0x1b, 0x39, 0x05, 0x7a, 0xaf, 0x92, 0x4b,
	0x26, 0xe9, 0x28, 0xde, 0xe0, 0x97, 0x45, 0xef, 0x98, 0x89, 0xe1, 0xe8, 0x68, 0x7e, 0xf8, 0x4d,
	0xc3, 0x7c, 0x91, 0xf7, 0x27, 0xd2, 0x3b, 0xe5, 0x03, 0x20, 0xba, 0x53, 0xfa, 0x40, 0x49, 0x8c,
	0xaa, 0xb4, 0xd3, 0x2a, 0x15, 0x68, 0xa8, 0x57, 0xab, 0x60, 0x01, 0xf8, 0x1f, 0xc3, 0x76, 0x79,
	0x3b, 0x0b, 0xc5, 0x91, 0xa7, 0xba, 0xd5, 0x55, 0xe4, 0xf6, 0xb1, 0xea, 0x27, 0xe5, 0x59, 0x6d,
	0x88, 0x75, 0xe6, 0x3f, 0x0b, 0x74, 0x63, 0xb4, 0x55, 0xf6, 0xbd, 0x4e, 0x61, 0xcd, 0xec, 0xa7,
	0x24, 0xae, 0xa9, 0x00, 0xa1, 0x4e, 0x71, 0x22, 0x31, 0xca, 0x8d, 0x47, 0xcf, 0xc5, 0xe5, 0xcf,
	0x69, 0x3c, 0x8f, 0xe6, 0xfd, 0x38, 0x95, 0x73, 0x2c, 0xf9, 0xde, 0xe1, 0xd3, 0xa6, 0xdc, 0x73,
	0xff, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0x4f, 0x6c, 0x4f, 0x60, 0x71, 0x21, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DistributionServiceClient is the client API for DistributionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DistributionServiceClient interface {
	// 商品分类
	Categories(ctx context.Context, in *DisCategoriesRequest, opts ...grpc.CallOption) (*DisCategoriesResponse, error)
	// 分销商品spu
	SpuList(ctx context.Context, in *DisSpuListRequest, opts ...grpc.CallOption) (*DisSpuListResponse, error)
	// 分销商品spu详情
	SpuDetail(ctx context.Context, in *DisSpuDetailRequest, opts ...grpc.CallOption) (*DisSpuDetailResponse, error)
	// spu更新（包括 推荐/取消推荐到首页）
	SpuUpdate(ctx context.Context, in *DisSpuUpdateRequest, opts ...grpc.CallOption) (*DisResponse, error)
	// spu操作日志
	SpuLogs(ctx context.Context, in *DisSpuLogsRequest, opts ...grpc.CallOption) (*DisSpuLogsResponse, error)
	// sku更新（包括 添加分销、取消分销、推广文案变更、佣金变更）
	SkuUpdate(ctx context.Context, in *DisSkuUpdateRequest, opts ...grpc.CallOption) (*DisResponse, error)
	// 非分销sku列表
	NotDisSkuList(ctx context.Context, in *DisNotDisSkuListRequest, opts ...grpc.CallOption) (*DisNotDisSkuListResponse, error)
	// 下载导入模板
	ImportTemplate(ctx context.Context, in *DisImportTemplateRequest, opts ...grpc.CallOption) (*DisImportTemplateResponse, error)
	// 批量导入
	Import(ctx context.Context, in *DisImportRequest, opts ...grpc.CallOption) (*DisResponse, error)
	// 导入历史
	ImportList(ctx context.Context, in *DisImportListRequest, opts ...grpc.CallOption) (*DisImportListResponse, error)
	// 限时佣金活动新增、修改
	DisLimitActivityOperate(ctx context.Context, in *DisLimitActivityOperateRequest, opts ...grpc.CallOption) (*DisResponse, error)
	// 限时活动列表
	DisLimitActivityList(ctx context.Context, in *DisLimitActivityListRequest, opts ...grpc.CallOption) (*DisLimitActivityListResponse, error)
	// 限时佣金/活动新增/失效
	DisLimitActivityStop(ctx context.Context, in *DisLimitActivityStopRequest, opts ...grpc.CallOption) (*DisResponse, error)
	// 限时佣金活动商品列表
	DisLimitActivityGoodsList(ctx context.Context, in *DisLimitActivityGoodsListRequest, opts ...grpc.CallOption) (*DisLimitActivityGoodsListResponse, error)
	DisLimitActivityGoodsDelete(ctx context.Context, in *DisLimitActivityGoodsDeleteRequest, opts ...grpc.CallOption) (*DisResponse, error)
	DisLimitActivityCommissionSet(ctx context.Context, in *DisLimitActivityCommissionSetRequest, opts ...grpc.CallOption) (*DisResponse, error)
	DisLimitActivityLog(ctx context.Context, in *DisLimitActivityLogRequest, opts ...grpc.CallOption) (*DisSpuLogsResponse, error)
	DisLimitActivityGoodsImport(ctx context.Context, in *DisLimitActivityGoodsImportRequest, opts ...grpc.CallOption) (*DisResponse, error)
	DisSetGlobalCommission(ctx context.Context, in *DisSetGlobalCommissionRequest, opts ...grpc.CallOption) (*DisResponse, error)
	DisGlobalCommission(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*DisGlobalCommissionResponse, error)
	DisGoodsList(ctx context.Context, in *DisGoodsListRequest, opts ...grpc.CallOption) (*DisGoodsListResponse, error)
	ExportNoDisGoods(ctx context.Context, in *wrappers.Int32Value, opts ...grpc.CallOption) (*DisImportDownloadResponse, error)
}

type distributionServiceClient struct {
	cc *grpc.ClientConn
}

func NewDistributionServiceClient(cc *grpc.ClientConn) DistributionServiceClient {
	return &distributionServiceClient{cc}
}

func (c *distributionServiceClient) Categories(ctx context.Context, in *DisCategoriesRequest, opts ...grpc.CallOption) (*DisCategoriesResponse, error) {
	out := new(DisCategoriesResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/Categories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) SpuList(ctx context.Context, in *DisSpuListRequest, opts ...grpc.CallOption) (*DisSpuListResponse, error) {
	out := new(DisSpuListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/SpuList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) SpuDetail(ctx context.Context, in *DisSpuDetailRequest, opts ...grpc.CallOption) (*DisSpuDetailResponse, error) {
	out := new(DisSpuDetailResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/SpuDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) SpuUpdate(ctx context.Context, in *DisSpuUpdateRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/SpuUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) SpuLogs(ctx context.Context, in *DisSpuLogsRequest, opts ...grpc.CallOption) (*DisSpuLogsResponse, error) {
	out := new(DisSpuLogsResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/SpuLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) SkuUpdate(ctx context.Context, in *DisSkuUpdateRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/SkuUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) NotDisSkuList(ctx context.Context, in *DisNotDisSkuListRequest, opts ...grpc.CallOption) (*DisNotDisSkuListResponse, error) {
	out := new(DisNotDisSkuListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/NotDisSkuList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) ImportTemplate(ctx context.Context, in *DisImportTemplateRequest, opts ...grpc.CallOption) (*DisImportTemplateResponse, error) {
	out := new(DisImportTemplateResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/ImportTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) Import(ctx context.Context, in *DisImportRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/Import", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) ImportList(ctx context.Context, in *DisImportListRequest, opts ...grpc.CallOption) (*DisImportListResponse, error) {
	out := new(DisImportListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/ImportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityOperate(ctx context.Context, in *DisLimitActivityOperateRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityList(ctx context.Context, in *DisLimitActivityListRequest, opts ...grpc.CallOption) (*DisLimitActivityListResponse, error) {
	out := new(DisLimitActivityListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityStop(ctx context.Context, in *DisLimitActivityStopRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityStop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityGoodsList(ctx context.Context, in *DisLimitActivityGoodsListRequest, opts ...grpc.CallOption) (*DisLimitActivityGoodsListResponse, error) {
	out := new(DisLimitActivityGoodsListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityGoodsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityGoodsDelete(ctx context.Context, in *DisLimitActivityGoodsDeleteRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityGoodsDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityCommissionSet(ctx context.Context, in *DisLimitActivityCommissionSetRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityCommissionSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityLog(ctx context.Context, in *DisLimitActivityLogRequest, opts ...grpc.CallOption) (*DisSpuLogsResponse, error) {
	out := new(DisSpuLogsResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisLimitActivityGoodsImport(ctx context.Context, in *DisLimitActivityGoodsImportRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisLimitActivityGoodsImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisSetGlobalCommission(ctx context.Context, in *DisSetGlobalCommissionRequest, opts ...grpc.CallOption) (*DisResponse, error) {
	out := new(DisResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisSetGlobalCommission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisGlobalCommission(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*DisGlobalCommissionResponse, error) {
	out := new(DisGlobalCommissionResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisGlobalCommission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) DisGoodsList(ctx context.Context, in *DisGoodsListRequest, opts ...grpc.CallOption) (*DisGoodsListResponse, error) {
	out := new(DisGoodsListResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/DisGoodsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distributionServiceClient) ExportNoDisGoods(ctx context.Context, in *wrappers.Int32Value, opts ...grpc.CallOption) (*DisImportDownloadResponse, error) {
	out := new(DisImportDownloadResponse)
	err := c.cc.Invoke(ctx, "/sh.DistributionService/ExportNoDisGoods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DistributionServiceServer is the server API for DistributionService service.
type DistributionServiceServer interface {
	// 商品分类
	Categories(context.Context, *DisCategoriesRequest) (*DisCategoriesResponse, error)
	// 分销商品spu
	SpuList(context.Context, *DisSpuListRequest) (*DisSpuListResponse, error)
	// 分销商品spu详情
	SpuDetail(context.Context, *DisSpuDetailRequest) (*DisSpuDetailResponse, error)
	// spu更新（包括 推荐/取消推荐到首页）
	SpuUpdate(context.Context, *DisSpuUpdateRequest) (*DisResponse, error)
	// spu操作日志
	SpuLogs(context.Context, *DisSpuLogsRequest) (*DisSpuLogsResponse, error)
	// sku更新（包括 添加分销、取消分销、推广文案变更、佣金变更）
	SkuUpdate(context.Context, *DisSkuUpdateRequest) (*DisResponse, error)
	// 非分销sku列表
	NotDisSkuList(context.Context, *DisNotDisSkuListRequest) (*DisNotDisSkuListResponse, error)
	// 下载导入模板
	ImportTemplate(context.Context, *DisImportTemplateRequest) (*DisImportTemplateResponse, error)
	// 批量导入
	Import(context.Context, *DisImportRequest) (*DisResponse, error)
	// 导入历史
	ImportList(context.Context, *DisImportListRequest) (*DisImportListResponse, error)
	// 限时佣金活动新增、修改
	DisLimitActivityOperate(context.Context, *DisLimitActivityOperateRequest) (*DisResponse, error)
	// 限时活动列表
	DisLimitActivityList(context.Context, *DisLimitActivityListRequest) (*DisLimitActivityListResponse, error)
	// 限时佣金/活动新增/失效
	DisLimitActivityStop(context.Context, *DisLimitActivityStopRequest) (*DisResponse, error)
	// 限时佣金活动商品列表
	DisLimitActivityGoodsList(context.Context, *DisLimitActivityGoodsListRequest) (*DisLimitActivityGoodsListResponse, error)
	DisLimitActivityGoodsDelete(context.Context, *DisLimitActivityGoodsDeleteRequest) (*DisResponse, error)
	DisLimitActivityCommissionSet(context.Context, *DisLimitActivityCommissionSetRequest) (*DisResponse, error)
	DisLimitActivityLog(context.Context, *DisLimitActivityLogRequest) (*DisSpuLogsResponse, error)
	DisLimitActivityGoodsImport(context.Context, *DisLimitActivityGoodsImportRequest) (*DisResponse, error)
	DisSetGlobalCommission(context.Context, *DisSetGlobalCommissionRequest) (*DisResponse, error)
	DisGlobalCommission(context.Context, *EmptyRequest) (*DisGlobalCommissionResponse, error)
	DisGoodsList(context.Context, *DisGoodsListRequest) (*DisGoodsListResponse, error)
	ExportNoDisGoods(context.Context, *wrappers.Int32Value) (*DisImportDownloadResponse, error)
}

// UnimplementedDistributionServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDistributionServiceServer struct {
}

func (*UnimplementedDistributionServiceServer) Categories(ctx context.Context, req *DisCategoriesRequest) (*DisCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Categories not implemented")
}
func (*UnimplementedDistributionServiceServer) SpuList(ctx context.Context, req *DisSpuListRequest) (*DisSpuListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpuList not implemented")
}
func (*UnimplementedDistributionServiceServer) SpuDetail(ctx context.Context, req *DisSpuDetailRequest) (*DisSpuDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpuDetail not implemented")
}
func (*UnimplementedDistributionServiceServer) SpuUpdate(ctx context.Context, req *DisSpuUpdateRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpuUpdate not implemented")
}
func (*UnimplementedDistributionServiceServer) SpuLogs(ctx context.Context, req *DisSpuLogsRequest) (*DisSpuLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpuLogs not implemented")
}
func (*UnimplementedDistributionServiceServer) SkuUpdate(ctx context.Context, req *DisSkuUpdateRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkuUpdate not implemented")
}
func (*UnimplementedDistributionServiceServer) NotDisSkuList(ctx context.Context, req *DisNotDisSkuListRequest) (*DisNotDisSkuListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotDisSkuList not implemented")
}
func (*UnimplementedDistributionServiceServer) ImportTemplate(ctx context.Context, req *DisImportTemplateRequest) (*DisImportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportTemplate not implemented")
}
func (*UnimplementedDistributionServiceServer) Import(ctx context.Context, req *DisImportRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Import not implemented")
}
func (*UnimplementedDistributionServiceServer) ImportList(ctx context.Context, req *DisImportListRequest) (*DisImportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportList not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityOperate(ctx context.Context, req *DisLimitActivityOperateRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityOperate not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityList(ctx context.Context, req *DisLimitActivityListRequest) (*DisLimitActivityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityList not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityStop(ctx context.Context, req *DisLimitActivityStopRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityStop not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityGoodsList(ctx context.Context, req *DisLimitActivityGoodsListRequest) (*DisLimitActivityGoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityGoodsList not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityGoodsDelete(ctx context.Context, req *DisLimitActivityGoodsDeleteRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityGoodsDelete not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityCommissionSet(ctx context.Context, req *DisLimitActivityCommissionSetRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityCommissionSet not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityLog(ctx context.Context, req *DisLimitActivityLogRequest) (*DisSpuLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityLog not implemented")
}
func (*UnimplementedDistributionServiceServer) DisLimitActivityGoodsImport(ctx context.Context, req *DisLimitActivityGoodsImportRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisLimitActivityGoodsImport not implemented")
}
func (*UnimplementedDistributionServiceServer) DisSetGlobalCommission(ctx context.Context, req *DisSetGlobalCommissionRequest) (*DisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisSetGlobalCommission not implemented")
}
func (*UnimplementedDistributionServiceServer) DisGlobalCommission(ctx context.Context, req *EmptyRequest) (*DisGlobalCommissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisGlobalCommission not implemented")
}
func (*UnimplementedDistributionServiceServer) DisGoodsList(ctx context.Context, req *DisGoodsListRequest) (*DisGoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisGoodsList not implemented")
}
func (*UnimplementedDistributionServiceServer) ExportNoDisGoods(ctx context.Context, req *wrappers.Int32Value) (*DisImportDownloadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportNoDisGoods not implemented")
}

func RegisterDistributionServiceServer(s *grpc.Server, srv DistributionServiceServer) {
	s.RegisterService(&_DistributionService_serviceDesc, srv)
}

func _DistributionService_Categories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).Categories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/Categories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).Categories(ctx, req.(*DisCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_SpuList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSpuListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).SpuList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/SpuList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).SpuList(ctx, req.(*DisSpuListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_SpuDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSpuDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).SpuDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/SpuDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).SpuDetail(ctx, req.(*DisSpuDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_SpuUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSpuUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).SpuUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/SpuUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).SpuUpdate(ctx, req.(*DisSpuUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_SpuLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSpuLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).SpuLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/SpuLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).SpuLogs(ctx, req.(*DisSpuLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_SkuUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSkuUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).SkuUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/SkuUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).SkuUpdate(ctx, req.(*DisSkuUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_NotDisSkuList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisNotDisSkuListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).NotDisSkuList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/NotDisSkuList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).NotDisSkuList(ctx, req.(*DisNotDisSkuListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_ImportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisImportTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).ImportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/ImportTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).ImportTemplate(ctx, req.(*DisImportTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_Import_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).Import(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/Import",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).Import(ctx, req.(*DisImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_ImportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisImportListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).ImportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/ImportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).ImportList(ctx, req.(*DisImportListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityOperateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityOperate(ctx, req.(*DisLimitActivityOperateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityList(ctx, req.(*DisLimitActivityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityStop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityStopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityStop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityStop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityStop(ctx, req.(*DisLimitActivityStopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityGoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityGoodsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsList(ctx, req.(*DisLimitActivityGoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityGoodsDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityGoodsDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityGoodsDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsDelete(ctx, req.(*DisLimitActivityGoodsDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityCommissionSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityCommissionSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityCommissionSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityCommissionSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityCommissionSet(ctx, req.(*DisLimitActivityCommissionSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityLog(ctx, req.(*DisLimitActivityLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisLimitActivityGoodsImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisLimitActivityGoodsImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisLimitActivityGoodsImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisLimitActivityGoodsImport(ctx, req.(*DisLimitActivityGoodsImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisSetGlobalCommission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisSetGlobalCommissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisSetGlobalCommission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisSetGlobalCommission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisSetGlobalCommission(ctx, req.(*DisSetGlobalCommissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisGlobalCommission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisGlobalCommission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisGlobalCommission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisGlobalCommission(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_DisGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisGoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).DisGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/DisGoodsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).DisGoodsList(ctx, req.(*DisGoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistributionService_ExportNoDisGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrappers.Int32Value)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistributionServiceServer).ExportNoDisGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.DistributionService/ExportNoDisGoods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistributionServiceServer).ExportNoDisGoods(ctx, req.(*wrappers.Int32Value))
	}
	return interceptor(ctx, in, info, handler)
}

var _DistributionService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sh.DistributionService",
	HandlerType: (*DistributionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Categories",
			Handler:    _DistributionService_Categories_Handler,
		},
		{
			MethodName: "SpuList",
			Handler:    _DistributionService_SpuList_Handler,
		},
		{
			MethodName: "SpuDetail",
			Handler:    _DistributionService_SpuDetail_Handler,
		},
		{
			MethodName: "SpuUpdate",
			Handler:    _DistributionService_SpuUpdate_Handler,
		},
		{
			MethodName: "SpuLogs",
			Handler:    _DistributionService_SpuLogs_Handler,
		},
		{
			MethodName: "SkuUpdate",
			Handler:    _DistributionService_SkuUpdate_Handler,
		},
		{
			MethodName: "NotDisSkuList",
			Handler:    _DistributionService_NotDisSkuList_Handler,
		},
		{
			MethodName: "ImportTemplate",
			Handler:    _DistributionService_ImportTemplate_Handler,
		},
		{
			MethodName: "Import",
			Handler:    _DistributionService_Import_Handler,
		},
		{
			MethodName: "ImportList",
			Handler:    _DistributionService_ImportList_Handler,
		},
		{
			MethodName: "DisLimitActivityOperate",
			Handler:    _DistributionService_DisLimitActivityOperate_Handler,
		},
		{
			MethodName: "DisLimitActivityList",
			Handler:    _DistributionService_DisLimitActivityList_Handler,
		},
		{
			MethodName: "DisLimitActivityStop",
			Handler:    _DistributionService_DisLimitActivityStop_Handler,
		},
		{
			MethodName: "DisLimitActivityGoodsList",
			Handler:    _DistributionService_DisLimitActivityGoodsList_Handler,
		},
		{
			MethodName: "DisLimitActivityGoodsDelete",
			Handler:    _DistributionService_DisLimitActivityGoodsDelete_Handler,
		},
		{
			MethodName: "DisLimitActivityCommissionSet",
			Handler:    _DistributionService_DisLimitActivityCommissionSet_Handler,
		},
		{
			MethodName: "DisLimitActivityLog",
			Handler:    _DistributionService_DisLimitActivityLog_Handler,
		},
		{
			MethodName: "DisLimitActivityGoodsImport",
			Handler:    _DistributionService_DisLimitActivityGoodsImport_Handler,
		},
		{
			MethodName: "DisSetGlobalCommission",
			Handler:    _DistributionService_DisSetGlobalCommission_Handler,
		},
		{
			MethodName: "DisGlobalCommission",
			Handler:    _DistributionService_DisGlobalCommission_Handler,
		},
		{
			MethodName: "DisGoodsList",
			Handler:    _DistributionService_DisGoodsList_Handler,
		},
		{
			MethodName: "ExportNoDisGoods",
			Handler:    _DistributionService_ExportNoDisGoods_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sh/distribution.proto",
}

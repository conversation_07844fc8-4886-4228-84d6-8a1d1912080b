package miniprogram

import (
	"encoding/base64"
	"testing"
)

func TestMiniClient_GetWxaCodeUnLimit(t *testing.T) {
	type fields struct {
		AppId     string
		AppSecret string
	}
	type args struct {
		data map[string]interface{}
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantBuffer []byte
		wantErr    bool
	}{
		{
			name: "",
			args: args{map[string]interface{}{
				"scene": "1234234",
			}},
			fields: fields{
				AppId:     "",
				AppSecret: "",
			},
		}, {
			name: "",
			args: args{map[string]interface{}{
				"scene": "1234234",
			}},
			// 测试自主获取，这是阿闻uat环境配置，请注意不要用阿闻正式环境
			fields: fields{
				AppId:     "wx80e6a4fd1f79a85b",
				AppSecret: "fb5a8f34d7c4fa45b73b0559d17258ff",
			},
		}, {
			name: "",
			args: args{map[string]interface{}{
				"scene": "1234234",
			}},
			// 测试错误自主获取，请注意不要用阿闻正式环境
			fields: fields{
				AppId:     "123456",
				AppSecret: "123456",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mc := &MiniClient{
				AppId:     tt.fields.AppId,
				AppSecret: tt.fields.AppSecret,
			}
			gotBuffer, err := mc.GetWxaCodeUnLimit(tt.args.data)
			t.Log(base64.StdEncoding.EncodeToString(gotBuffer), err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWxaCodeUnLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

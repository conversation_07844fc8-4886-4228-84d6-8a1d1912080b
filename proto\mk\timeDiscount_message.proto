syntax = "proto3";

package mk;


import "mk/model.proto";

/////////////////////////////////////////////////  Dto  ///////////////////////////////////////////////////////////////////////////////

message weekDayDto{
    string beginTime = 2;
    string EndTime = 3;
}

// 限时促销与商品关联关系Dto
message timeDiscountProductDto {
    // 关联关系Id
    int32 timeDiscountProductId=13;
    //店铺Id
    string shopId=14;
    //店铺名称
    string shopName=1;
    //商品Id
    string productSkuId=15;
    // 商品名称
    string productName=2;
    // 原价
    double marketingPrice=3;
    // 折扣类型
    int32 discountType=16;
    // 折扣
    double discount=4;
    // 活动价
    double sellingPrice=5;
    // 每单限购
    int32 limitByOrder=6;
    // 当日活动库存
    int32 limitByStock=7;
    // 活动开始时间
    string BeginDate=8;
    // 活动结束时间
    string EndDate=10;
    // 周期循环
    repeated PromotionTime timeRanges = 9;
    int32 Week = 18;
    // 循环周期 0周日 1-6分别表示周一到周六
    repeated int32 weekDays = 19;
    // 用户类型
    int32 userType=11;
    // 状态
    promotionState state=12;
    // 最后更新时间
    string updateDate=17;
}

message timeDiscountUpdateRequestDto{
    // 促销活动Id
    int32 promotionId=1;
    // 活动基本信息
    promotionDto promotion=2;
    // 星期
    repeated PromotionTime timeRanges=3;
    // 相关商品
    promotionProductDto promotionProduct=5;
    // 限时折扣配置
    promotionTimeDiscountDto promotionTimeDiscount=6;
}

//限时折扣活动商品单日库存数量列表
message promotionProductLimitCountDto{
    // 促销活动Id
    int32 promotionId=1;
    // 商品sku
    string productSkuId=2;
    // 每单限购数量
    int32 limitCountByOrder=3;
    // 每日库存
    int32 limitCountByStock=4;
}

/////////////////////////////////////////////////  Request  ///////////////////////////////////////////////////////////////////////////////


// 更新
message timeDiscountUpdateRequest {
    //用户Id，即userno
    string userId=1;
    //用户Id，即登录人姓名
    string userName=2;

    repeated timeDiscountUpdateRequestDto updateParams = 3;
}

// 获取列表
message timeDiscountProductRequest {
    // 页索引
    int32 pageIndex=1;
    // 页大小
    int32 pageSize=2;
    // 店铺列表，逗号分割
    string shopIds=3;
    // 当前登录用户Id
    string userId =4;
    // 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
    promotionState state=5;
    // 开始日期
    string beginDate=6;
    // 截止日期日期
    string endDate=7;
    // 商品名称
    string productName=8;
    // 每单限购 0 不限制, 非0  限制多少数量
    int32 LimitCountByOrder=9;
    string user_name = 10;
    //活动id
    int64 promotion_id = 11;
    // 批量活动id，多个逗号分隔
    string promotion_ids = 12;
    // 批量活动id，多个逗号分隔
    string sku_id = 13;
}

// 查询限时折扣活动的单日库存请求
message queryLimitCountRequest {
    //门店id
    string shopid = 1;
    //多个活动
    string promotionids=2;
    //多个商品skuid
    string skuids=3;
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 限时促销与商品关联关系响应
message timeDiscountProductResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message = 2;
    // 总条数
    int32 total=3;
    // 分页数据
    repeated timeDiscountProductDto data=4;
}

// 限时促销 数据响应
message timeDiscountByIdResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message=2;    
 
    int32 promotionId =3;
    // 活动基本信息
    promotionDto promotion=4;
    // 活动时间
    repeated PromotionTime timeRanges=6;
    // 应用店铺
    repeated promotionShopDto shops=7;
    // 相关商品
    repeated promotionProductDto products=8;
    // 满减优惠
    repeated promotionTimeDiscountDto timeDiscounts=9;
}

// 查询限时折扣活动的单日库存响应
message queryLimitCountResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    int32 code = 1;
    // 不成功的错误信息
    string message=2;

    // 应用店铺
    repeated promotionProductLimitCountDto promotionLimits=7;
}
///////////////////////////////////////////////  Restructure /////////////////////////////////////////////////////////////////////////////////

message promotionTimeDiscountProductDto{
    promotionDto promotion = 1;
     repeated PromotionTime timeRanges = 2;
     promotionProductDto promotionProduct = 3;
     promotionTimeDiscountDto promotionTimeDiscount = 4;
}


//限时折扣-批量新增-Request
message  promotionTimeDiscountAddRequest{
    //用户Id，即userno
    string userId=1;
    //用户Id，即登录人姓名
    string userName=2;

    repeated promotionShopDto promotionShop = 3;

    repeated promotionTimeDiscountProductDto addParams = 4;
}

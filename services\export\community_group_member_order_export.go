package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"os"
	"strings"
	"time"
)

type CommunityGroupMemberOrderExport struct {
	F              *excelize.File
	SheetName      string
	exportFileName string
	storeMap       map[string]*dac.StoreInfo
	taskParams     *oc.CommunityGroupOrderListRequest
	writer         *excelize.StreamWriter
}

func (e *CommunityGroupMemberOrderExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.CommunityGroupOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 10000

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.ShopIds)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}
	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}

	var details []*oc.AwenCommunityGroupMemberOrderExport
	k := 0
	for {
		details, err = awenCommunityGroupMemberOrderExport(e.taskParams, 0)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		for i := 0; i < len(details); i++ {
			k++
			storeInfo := e.getStoreInfo(details[i].ShopId)
			_, ok := phoneExportMap[details[i].ShopId]
			axis := fmt.Sprintf("A%d", k+1)
			//店铺类型
			ShopType := "新瑞鹏"
			if details[i].AppChannel != 1 {
				ShopType = "TP代运营"
			}

			_ = e.writer.SetRow(axis, []interface{}{
				details[i].OrderSn,                                        // 订单号
				details[i].OldOrderSn,                                     // 外部订单号
				e.getGroupTypeText(details[i].GroupType),                  // 商品筛选
				details[i].CreateTime,                                     // 下单时间
				details[i].PaySn,                                          // 支付流水号
				details[i].PayTime,                                        // 支付时间
				kit.FenToYuan(details[i].Total),                           // 实收金额（实收金额）
				kit.FenToYuan(details[i].ActualReceiveTotal),              // 商家预计收入
				kit.FenToYuan(details[i].GoodsTotal + details[i].Freight), // 订单原价
				"-" + cast.ToString(kit.FenToYuan(details[i].Privilege-details[i].PlatformPayedAmount)), // 商家补贴
				// kit.FenToYuan(details[i].Privilege-details[i].PlatformPayedAmount), // 优惠金额
				details[i].RefundAmount, // 退款金额
				kit.FenToYuan(details[i].Total - details[i].PackingCost - (details[i].Freight - details[i].FreightPrivilege)), // 商品实付金额
				kit.FenToYuan(details[i].Freight),                          // 配送费
				kit.FenToYuan(details[i].PackingCost),                      // 包装费
				0 - kit.FenToYuan(details[i].FreightPrivilege),             // 配送费优惠
				0 - kit.FenToYuan(details[i].PlatformPayedAmount),          // 平台补贴
				0 - kit.FenToYuan(details[i].ServiceCharge),                // 平台服务费
				0 - kit.FenToYuan(details[i].ContractFee),                  // 履约服务费
				services.PayMode[details[i].PayMode],                       // 支付方式 1支付宝 2微信 3美团支付
				storeInfo.Bigregion,                                        // 大区
				storeInfo.City,                                             // 省
				storeInfo.Name,                                             // 门店名称
				storeInfo.FinanceCode,                                      // 财务编码
				services.OrderFrom[details[i].ChannelId],                   // 订单来源
				e.getUserAgent(details[i].UserAgent),                       // 销售渠道
				details[i].MemberId,                                        // 用户ID
				e.getCustomerTag(details[i]),                               // 顾客类型，默认0,1-新顾客，2-老顾客
				e.getReceiverName(details[i]),                              // 收货人姓名
				e.getReceiverMobile(ok, details[i]),                        // 收货人联系方式
				e.getReceiverAddress(ok, details[i]),                       // 收货人地址
				details[i].BuyerMemo,                                       // 备注
				services.DeliveryType[details[i].DeliveryType],             // 配送方式
				services.OrderStatusMap[details[i].OrderStatusChild],       // 订单状态
				details[i].ActivityType,                                    // 活动类型
				details[i].PerformanceStaffName,                            // 业务员
				details[i].PerformanceOperatorName,                         // 业绩分配人
				details[i].PerformanceOperatorTime,                         // 业绩分配时间
				details[i].Category,                                        // 仓库类型(门店类型)
				details[i].WarehouseName,                                   // 仓库名称
				details[i].StorePayDeliveryAmount,                          // 店铺支付配送费
				ShopType,                                                   // 店铺类型
				details[i].PickupStationName,                               // 提货点名称
				details[i].PickupStationAddress,                            // 提货点地址
				details[i].ExpectedTime,                                    // 送达时间
				e.getGroupStatusText(details[i].GroupActivityModel.Status), // 拼团状态
				details[i].GroupActivityModel.StaffName,                    // 分销员姓名
				details[i].GroupActivityModel.DisChainName,                 // 分销员单位
				details[i].ShopDisModel.StaffName,                          // 业绩所属人
				details[i].ShopDisModel.ChainName,                          // 业绩所属单位
				details[i].ShopDisModel.MemberAreaName,                     // 业绩所属人所在城市
				details[i].ShopDisModel.FinanceCode,                        // 业绩所属单位财务编码
				details[i].GroupActivityModel.MemberName,                   // 团长名
				details[i].GroupActivityModel.ReceiverMobile,               // 团长电话
				details[i].GroupActivityModel.ReceiverAddress,              // 团长地址
			})
		}
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}
	nums = k
	_ = e.writer.Flush()
	return
}

func (e *CommunityGroupMemberOrderExport) getGroupStatusText(i int32) string {
	v := ""
	if i == 0 {
		v = "拼团中"
	} else if i == 1 {
		v = "拼团成功"
	} else if i == 2 {
		v = "拼团失败"
	}
	return v
}

func (e *CommunityGroupMemberOrderExport) SetSheetName() {
	nameList := []interface{}{
		"订单号", "外部订单号", "商品筛选", "下单时间", "支付流水号", "支付时间", "用户实付", "商家预计收入", "订单原价", "优惠金额", "退款金额",
		"商品实付总金额", "配送费", "包装费", "配送费优惠", "平台补贴", "平台服务费", "履约服务费", "支付方式", "大区",
		"城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "用户ID", "顾客类型", "收货人", "收货人联系方式", "收货人地址",
		"配送备注", "配送方式", "订单状态", "活动类型", "业绩归属人", "业绩分配人", "业绩分配时间",
		"仓库类型", "仓库名称", "门店支付配送费", "店铺类型", "提货点名称", "提货点地址", "送达时间",
		"拼团状态", "分销员姓名", "分销员单位", "业绩所属人", "业绩所属单位", "业绩所属人所在城市", "业绩所属单位财务编码",
		"团长名", "团长电话", "团长地址",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e *CommunityGroupMemberOrderExport) GenerateDownUrl() (string, error) {
	fileName := fmt.Sprintf("团长拼团-导出团员订单数据(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	err := e.F.SaveAs(fileName)
	if err != nil {
		return "", errors.New("文件创建失败, " + err.Error())
	}
	defer os.Remove(fileName)

	//同步文件到七牛云
	url, err := utils.UploadExcelToQiNiu(fileName)
	if err != nil {
		return "", errors.New("文件上传失败, " + err.Error())
	}
	return url, nil
}

func (e *CommunityGroupMemberOrderExport) getGroupTypeText(i int32) (t string) {
	switch i {
	case 1:
		t = "有实实组合"
	case 2:
		t = "有虚虚组合"
	case 3:
		t = "有虚实组合"
	case 0:
		t = "无组合商品"
	default:
		t = ""
	}
	return
}

func (e *CommunityGroupMemberOrderExport) getStoreInfo(ShopId string) *dac.StoreInfo {
	storeInfo := &dac.StoreInfo{}
	if value, ok := e.storeMap[ShopId]; ok {
		storeInfo = value
	}
	return storeInfo
}

func (e *CommunityGroupMemberOrderExport) getUserAgent(userAgent int32) string {
	if _, ok := services.UserAgent[userAgent]; ok {
		return services.UserAgent[userAgent]
	} else {
		return "其它"
	}
}

func (e *CommunityGroupMemberOrderExport) getCustomerTag(v *oc.AwenCommunityGroupMemberOrderExport) string {
	if v.ChannelId == 1 && utils.CampareTime(v.CreateTime, "2021-06-10 00:00:00") {
		if v.IsNewCustomer == 1 {
			return "新顾客"
		} else if v.IsNewCustomer == 2 {
			return "老顾客"
		} else {
			return "打标中"
		}
	}
	return ""
}

func (e *CommunityGroupMemberOrderExport) getReceiverName(v *oc.AwenCommunityGroupMemberOrderExport) string {
	if v.GroupActivityModel.FinalTakeType == 1 {
		return v.GroupMemberReceiverName
	} else {
		return v.ReceiverName
	}
}

func (e *CommunityGroupMemberOrderExport) getReceiverMobile(ok bool, v *oc.AwenCommunityGroupMemberOrderExport) string {
	// 收货人联系方式
	if ok {
		if v.GroupActivityModel.FinalTakeType == 1 {
			return services.MobileDecrypt(v.GroupMemberEnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip)
		} else {
			return services.MobileDecrypt(v.EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip)
		}
	}
	return ""
}

func (e *CommunityGroupMemberOrderExport) getReceiverAddress(ok bool, v *oc.AwenCommunityGroupMemberOrderExport) string {
	// 收货人地址
	if ok {
		if v.GroupActivityModel.FinalTakeType == 1 {
			return v.GroupMemberReceiverAddress
		} else {
			return v.ReceiverAddress
		}
	}
	return ""
}

package services

import (
	"fmt"
	"github.com/Shopify/sarama"
	"github.com/stretchr/testify/assert"
	"order-center/dto"
	"regexp"
	"sync"
	"testing"
)

func TestFreeze(t *testing.T) {
	type args struct {
		orderSn string
		shopId  string
	}
	tests := []struct {
		name    string
		args    args
		wantOut FreezeResponse
	}{
		// TODO: Add test cases.
		{name: "锁库"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.wantOut, Freeze("9964128297212996", "530219708465609002", 1), "Freeze(%v, %v)", tt.args.orderSn, tt.args.shopId)
		})
	}
}

func TestRefundStock(t *testing.T) {
	type args struct {
		refundSn string
		shopId   string
	}
	tests := []struct {
		name    string
		args    args
		wantOut FreezeResponse
	}{
		// TODO: Add test cases.
		{name: "退货入库"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.wantOut, RefundStock("50000136795", "9964128298584156", 1), "RefundStock(%v, %v)", tt.args.refundSn, tt.args.shopId)
		})
	}
}

func TestQueryStock(t *testing.T) {
	type args struct {
		in dto.QueryStockReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut dto.QueryStockRes
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "查询库存"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var in = dto.QueryStockReq{}
			in.ShopId = "576534157590153975"
			skuIds := []int64{105522001, 111041001}
			in.SkuIds = skuIds
			gotOut := QueryStock(in)
			assert.Equalf(t, tt.wantOut, gotOut, "QueryStock(%v)", tt.args.in)
		})
	}
}

func TestUnFreeze(t *testing.T) {
	type args struct {
		orderSn string
		shopId  string
	}
	tests := []struct {
		name    string
		args    args
		wantOut FreezeResponse
	}{
		// TODO: Add test cases.
		{name: "释放库存"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.wantOut, UnFreeze("9964128298765354", "576534157590153975"), "UnFreeze(%v, %v)", tt.args.orderSn, tt.args.shopId)
		})
	}
}

func TestConsumerGroupHandler_ConsumeClaimTest(t *testing.T) {
	type fields struct {
		wg sync.WaitGroup
	}
	type args struct {
		sess  sarama.ConsumerGroupSession
		claim sarama.ConsumerGroupClaim
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "测试MQ"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ConsumerGroupHandler{
				wg: tt.fields.wg,
			}
			sss := "商品: aaa,bbb；skuId：【123,234】可用库存不足"
			re := regexp.MustCompile(`【(.*?)】`)
			matches := re.FindStringSubmatch(sss)

			if len(matches) > 1 {
				fmt.Println("提取到的内容:", matches[1])
			}
			tt.wantErr(t, c.ConsumeClaimTest(tt.args.sess, tt.args.claim), fmt.Sprintf("ConsumeClaimTest(%v, %v)", tt.args.sess, tt.args.claim))
		})
	}
}

func TestFreezeCommitOrder(t *testing.T) {
	type args struct {
		orderSn string
		shopId  string
	}
	tests := []struct {
		name    string
		args    args
		wantOut FreezeResponse
	}{
		// TODO: Add test cases.
		{name: "测试锁库"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//db := GetDBConn()
			//FreezeCommitOrder("9964128298343338", "", db.NewSession())
			Freeze("9964128298343338", "", 0)
		})
	}
}

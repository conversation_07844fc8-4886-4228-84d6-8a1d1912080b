package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
)

// 实物订单-导出订单数据
type OrderExport struct {
	F          *excelize.File
	SheetName  string
	storeMap   map[string]*dac.StoreInfo
	TaskParams *oc.AwenMaterOrderListRequest
}

// 逻辑
func (e *OrderExport) DataExport(taskParams string) (nums int, err error) {
	e.TaskParams = new(oc.AwenMaterOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.TaskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []*oc.AwenOrdeExport

	e.TaskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.TaskParams.PageSize = 5000
	for {
		details, err = services.AwenOrderExport(e.TaskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.TaskParams.PageIndex += 1
		orderList = append(orderList, details...)
		glog.Info(e.TaskParams.UserNo, " details length - pagesize length", len(details), e.TaskParams.PageSize)
		if len(details) < int(e.TaskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.TaskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.TaskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)
	n := "0"
	nowerr := 0
	var orderSn string
	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}

	defer func() {
		if r := recover(); r != nil {
			glog.Error("导出文件excel异常：", e.TaskParams.UserNo+"，"+orderSn+" 错误：", r, "行数:"+n, "堆帐:", string(debug.Stack()), orderList[nowerr])
			err = errors.New("导出文件excel异常")
			return
		}
	}()

	for k := range orderList {
		nowerr = k
		orderSn = orderList[k].OrderSn
		n = strconv.Itoa(k + 2)
		// 订单号
		e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
		// 父订单号
		e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].ParentOrderSn)
		// 外部订单号
		e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
		// 下单时间
		e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].CreateTime)
		// 支付流水号
		e.F.SetCellValue(e.SheetName, "E"+n, orderList[k].PaySn)
		// 支付时间
		e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].PayTime)

		//商家预计收入
		e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].ActualReceiveTotal))
		// 实付金额
		e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(orderList[k].PayTotal))
		// 商品实付总金额
		e.F.SetCellValue(e.SheetName, "I"+n, kit.FenToYuan(orderList[k].GoodsPayTotal))

		// 实收金额
		//e.F.SetCellValue(e.SheetName, "F"+n, kit.FenToYuan(orderList[k].Total))
		// 订单原价
		//e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].GoodsTotal+orderList[k].Freight))
		// 优惠金额
		//e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(orderList[k].Privilege))

		//商品实付金额
		//e.F.SetCellValue(e.SheetName, "G"+n, kit.FenToYuan(orderList[k].Total))

		//配送费
		e.F.SetCellValue(e.SheetName, "J"+n, kit.FenToYuan(orderList[k].Freight))

		// 包装费
		e.F.SetCellValue(e.SheetName, "K"+n, kit.FenToYuan(orderList[k].PackingCost))

		//商家配送费优惠
		e.F.SetCellValue(e.SheetName, "L"+n, 0-kit.FenToYuan(orderList[k].FreightPrivilege))

		//平台补贴
		e.F.SetCellValue(e.SheetName, "M"+n, 0-kit.FenToYuan(orderList[k].PlatformPayedAmount))

		//平台配送费优惠
		e.F.SetCellValue(e.SheetName, "N"+n, 0-kit.FenToYuan(orderList[k].PlatformFreightPrivilege))

		//平台服务费
		e.F.SetCellValue(e.SheetName, "O"+n, 0-kit.FenToYuan(orderList[k].ServiceCharge))

		//履约服务费
		e.F.SetCellValue(e.SheetName, "P"+n, 0-kit.FenToYuan(orderList[k].ContractFee))

		// 退款金额
		e.F.SetCellValue(e.SheetName, "Q"+n, orderList[k].RefundAmount)
		// 支付方式 1支付宝 2微信 3美团支付
		e.F.SetCellValue(e.SheetName, "R"+n, services.PayMode[orderList[k].PayMode])
		// 大区
		if value, ok := e.storeMap[orderList[k].ShopId]; ok {
			e.F.SetCellValue(e.SheetName, "S"+n, value.Bigregion)
			e.F.SetCellValue(e.SheetName, "T"+n, value.City)
			e.F.SetCellValue(e.SheetName, "U"+n, value.Name)
			//财务编码
			e.F.SetCellValue(e.SheetName, "V"+n, value.FinanceCode)
		}
		//订单来源
		e.F.SetCellValue(e.SheetName, "W"+n, services.OrderFrom[orderList[k].ChannelId])
		// 销售渠道
		if _, ok := services.UserAgent[orderList[k].UserAgent]; ok {
			e.F.SetCellValue(e.SheetName, "X"+n, services.UserAgent[orderList[k].UserAgent])
		} else {
			e.F.SetCellValue(e.SheetName, "X"+n, "其它")
		}
		// 用户ID
		e.F.SetCellValue(e.SheetName, "Y"+n, orderList[k].MemberId)
		// 顾客类型，默认0,1-新顾客，2-老顾客
		if (orderList[k].ChannelId == services.ChannelAwenId || orderList[k].ChannelId == services.ChannelDigitalHealth) && utils.CampareTime(orderList[k].CreateTime, "2021-06-10 00:00:00") {
			if orderList[k].IsNewCustomer == 1 {
				e.F.SetCellValue(e.SheetName, "Z"+n, "新顾客")
			} else if orderList[k].IsNewCustomer == 2 {
				e.F.SetCellValue(e.SheetName, "Z"+n, "老顾客")
			} else {
				e.F.SetCellValue(e.SheetName, "Z"+n, "打标中")
			}
		}

		// 收货人姓名
		e.F.SetCellValue(e.SheetName, "AA"+n, orderList[k].ReceiverName)

		// 收货人联系方式
		// 收货人联系方式
		// 收货人地址
		if _, ok := phoneExportMap[orderList[k].ShopId]; ok {
			createTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, orderList[k].CreateTime)
			compareStrTime := "2022-04-01  00:00:00"
			compareTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, compareStrTime)
			if createTime.After(compareTime) {
				e.F.SetCellValue(e.SheetName, "AB"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.TaskParams.UserNo, e.TaskParams.Ip))
				e.F.SetCellValue(e.SheetName, "AC"+n, orderList[k].ReceiverAddress)
			} else {
				e.F.SetCellValue(e.SheetName, "AB"+n, "")
				e.F.SetCellValue(e.SheetName, "AC"+n, "")
			}
		} else {
			e.F.SetCellValue(e.SheetName, "AB"+n, "")
			e.F.SetCellValue(e.SheetName, "AC"+n, "")
		}
		// 备注
		e.F.SetCellValue(e.SheetName, "AD"+n, orderList[k].BuyerMemo)

		// 配送方式
		e.F.SetCellValue(e.SheetName, "AE"+n, services.DeliveryType[orderList[k].DeliveryType])
		// 订单状态
		e.F.SetCellValue(e.SheetName, "AF"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
		// 活动类型
		if orderList[k] != nil && len(orderList[k].ActivityType) > 0 {
			err1 := e.F.SetCellValue(e.SheetName, "AG"+n, orderList[k].ActivityType)
			if err1 != nil {
				glog.Error("导出文件excel异常：", err1, orderList[k])
			}
		} else {
			e.F.SetCellValue(e.SheetName, "AG"+n, "")
		}
		// 业绩归属人
		e.F.SetCellValue(e.SheetName, "AH"+n, orderList[k].PerformanceStaffName)
		// 业绩分配人
		e.F.SetCellValue(e.SheetName, "AI"+n, orderList[k].PerformanceOperatorName)
		//业绩归属人所属门店编码
		e.F.SetCellValue(e.SheetName, "AJ"+n, orderList[k].PerformanceFinanceCode)
		//业绩归属人所属门店名称
		e.F.SetCellValue(e.SheetName, "AK"+n, orderList[k].PerformanceChainName)
		// 业绩分配时间
		e.F.SetCellValue(e.SheetName, "AL"+n, orderList[k].PerformanceOperatorTime)
		// 仓库类型(门店类型)
		e.F.SetCellValue(e.SheetName, "AM"+n, orderList[k].Category)
		// 仓库名称
		e.F.SetCellValue(e.SheetName, "AN"+n, orderList[k].WarehouseName)
		//店铺支付配送费
		e.F.SetCellValue(e.SheetName, "AO"+n, orderList[k].StorePayDeliveryAmount)
		//店铺类型
		ShopType := "新瑞鹏"
		if orderList[k].AppChannel != 1 {
			ShopType = "TP代运营"
		}

		e.F.SetCellValue(e.SheetName, "AP"+n, ShopType)

		//提货点名称
		e.F.SetCellValue(e.SheetName, "AQ"+n, orderList[k].PickupStationName)
		//提货点地址
		e.F.SetCellValue(e.SheetName, "AR"+n, orderList[k].PickupStationAddress)
		//送达时间
		e.F.SetCellValue(e.SheetName, "AS"+n, orderList[k].ExpectedTime)
		//平台实际分摊补贴
		e.F.SetCellValue(e.SheetName, "AT"+n, kit.FenToYuan(orderList[k].PrivilegePt))
	}

	e.F.Save()

	return
}

// 设置表头
func (e *OrderExport) SetSheetName() {
	nameList := []string{
		"订单号", "父订单号", "外部订单号", "下单时间", "支付流水号", "支付时间", "商家预计收入",
		//"订单原价", "优惠金额",
		"用户实付金额", "商品实付总金额", "配送费", "包装费", "商家配送费优惠", "平台补贴", "平台配送费优惠", "平台服务费", "履约服务费",
		"退款金额", "支付方式",
		"大区", "城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "用户ID", "顾客类型", "收货人", "收货人联系方式",
		"收货人地址", "配送备注", "配送方式", "订单状态", "活动类型", "业绩归属人", "业绩分配人", "业绩归属人所属门店编码", "业绩归属人所属门店名称", "业绩分配时间", "仓库类型", "仓库名称", "门店支付配送费", "店铺类型",
		"提货点名称", "提货点地址", "送达时间", "平台实际分摊补贴",
		//"商家预计收入", "用户实付金额",
	}
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *OrderExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("实物订单-导出订单数据(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
package proto;
option go_package = "oc";

service SayHelloService {
  rpc Test (HelloRequest) returns (HelloResponse);
}

message HelloRequest {
  string params = 1;
}
message HelloResponse {
  string params = 1;
}

service OcTask {
  //提供测试使用的，手动选择执行定时任务
  rpc ChooseTaskRun (TaskRunRequest) returns (BaseResponse);
}
message TaskRunRequest{
  // 1自动完成订单 2fix漏掉的部分自动完成 3核销码过期通知 4会员卡过期处理 5会员卡订单7天自动完成
  int32 data = 1;
  //参数信息
  string param = 2;
}

service RefundOrderService {
  //申请售后单
  rpc RefundOrderApply (RefundOrderApplyRequest) returns (RefundOrderApplyResponse) {
  };

  //申请售审核应答（例如美团，饿了么）
  rpc RefundOrderAnswer (RefundOrderAnswerRequest) returns (BaseResponse) {
  };

  //撤销售后单
  rpc RefundOrderCancel (RefundOrderCancelRequest) returns (BaseResponse) {
  };

  rpc RefundOrderPay (RefundOrderPayRequest) returns (BaseResponse) {
  };

  //异步存储美团售后单数据
  rpc SaveMtRefundOrderData (OrderRetrunRequest) returns (BaseResponse) {
  };

  //特殊-京东到家退款需要商家审核流程，为了统一后台处理，当成售后单处理
  rpc CancelOrderByJd (CancelOrderByJdRequest) returns (BaseResponse) {
  };

  //特殊-京东到家售后单可以修改但是单号是同一个，不符合现有逻辑 需先删除，后重新插入
  rpc DeleteRefundOrder (DeleteRefundOrderRequest) returns (BaseResponse) {
  };

  rpc RefundOrderPayCallBack (RefundOrderPayCallBackRequest) returns (BaseResponse) {
  };

  //获取商品对应子订单信息
  rpc SplitOrderRefundGoods (RefundOrderApplyRequest) returns (SplitOrderRefundGoodsResponse) {
  };
  //健康管理计划-确认退卡退款
  rpc HealthPlanRefundOrder (HealthPlanRefundOrderRequest) returns (BaseResponse) {
  };
  //健康管理计划-更新超时订单状态为失效
  rpc UpdateOrderStatus (UpdateOrderStatusRequest) returns (BaseResponse) {
  };



  // 查询退款单的列表
  rpc RefundOrderSnList(RefundOrderSnListVo) returns (RefundOrderSnListResp){};

  // 手动全退接口
  rpc RepeatRefundOrderPay (RefundOrderPayVo) returns (BaseResponse) {
  };

  // 手动完成
  rpc RefundOrderFinish (RefundOrderPayVo) returns (BaseResponse) {
  };

  // 查询操作记录
  rpc RefundRecordList (ExpressCompanyListRequest) returns (RefundRecordListResponse) {
  };

  // 批量退配送费
  rpc BatchRefundDeliveryFee (BatchRefundDeliveryFeeRequest) returns (BaseResponse) {
  };
  // 查询退配送费
  rpc GetRefundDeliveryFee (GerRefundDeliveryFeeRequest) returns (GerRefundDeliveryFeeResponse) {
  };
  // 导出退配送费
  rpc ExportRefundDeliveryFee (GerRefundDeliveryFeeRequest) returns (ExportRefundDeliveryFeeResponse) {
  };
}

//
message RefundOrderApplyRequest {
  // 订单号    必镇
  string order_id = 1;

  // 外部订单号 Id
  string external_order_id = 12;
  //  因***原因部分退款 必镇
  string reason = 2;
  //退款类型1为退款,2为退货  目前不支持退货
  int64 refund_type = 3;
  // 部分退款商品sku数据集合的json格式数组
  repeated RefundOrderGoodsData refund_order_goods_data = 4;
  //门店id(财务编码)
  string shop_id = 5;
  //1整单退款 2部分退款
  int32 full_refund = 6;
  //售后单备注
  string refund_remark = 7;

  //退款总金额
  float refund_amount = 8;
  //用户申请退款时上传的退款图片，多个图片url以英文逗号隔开
  string pictures = 9;
  //操作用户
  string operation_user = 10;
  //申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string res_type = 11;
  //推送当前仅退款或退货退款流程的发起方，是用户还是商家；仅适用于支持退货退款的商家。
  //1-用户
  //2-商家
  //3-客服
  //4-BD
  //5-系统
  //6-开放平台
  string applyOpUserType = 13;
  //渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家 (废弃))
  int32 order_from = 14;
  //操作描述
  string operation_type = 15;
  // 售后单号
  string refund_order_sn = 16;

  //来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 17;

  //京东到家 专属 售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）
  string refund_code = 18;

  //京东到家 专属 1：取消订单 2正常售后订单
  int32 is_cancal_order = 19;

  //渠道退款单号
  string old_refund_sn = 20;
  //平台承担的活动优惠金额
  float activity_pt_amount = 21;
  //本次退款的配送费
  float delivery_price = 22;
  //区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程
  string service_type = 23;
}

//售后订单同步 响应参数 订单信息List
message RefundOrderApplyResponse {
  //code
  int32 code = 1;
  //消息提示
  string message = 2;
  //退款退货单号
  string refund_order_sn = 3;
  //错误信息
  string error = 4;
  //下单时间(13位，毫秒)
  int64 create_time = 5;
}

message RefundOrderGoodsData {
  //商品唯一标识
  string sku_id = 1;
  //数量
  int32 quantity = 2;
  //总金额
  string refund_amount = 3;
  //饿了么专属字段，子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
  string sub_biz_order_id = 4;
  //退款商品名称
  string goods_name = 5;
  //商品sku的规格名称
  string spec = 6;
  //商品单价  如本次部分退款是按件部分退，则此金额为单件商品sku的退款金额
  float refund_price = 7;
  //实际支付单价 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
  float refund_reality_price = 8;
  // APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 9;
  //京东到家标识 商品促销类型（1203满赠，6买赠，1正品）
  int32 promotion_type = 10;
  //订单商品表主键id
  int64 order_product_id = 11;
  //订单商品的父sku 组合商品的子商品该字段有值
  string parent_sku_id = 12;
  //订单商品的父sku 组合商品的子商品该字段有值
  int64 platform_sku_id = 13;
}

message RefundOrderAnswerRequest {
  // 订单号
  string order_id = 1;
  // 外部订单号 Id
  string external_order_id = 2;
  // 售后单号
  string refund_order_sn = 3;
  // 同意OR拒绝原因
  string reason = 4;
  //申退款状态类型，参考值：1-同意；2-拒绝；
  int32 result_type = 5;
  //操作类型
  string operation_type = 6;
  //操作用户
  string operation_user = 7;
  //申退款状态类型 备注 1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；
  string result_type_note = 8;
  //渠道退款单号
  string old_refund_sn = 9;
  //平台承担的优惠金额
  double activity_pt_amount = 10;
}

message RefundOrderCancelRequest {
  string refund_order_sn = 1;
  //申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string res_type = 2;
  //操作类型
  string operation_type = 3;
  //操作用户
  string operation_user = 4;
  //原因
  string reason = 5;
}

message RefundOrderPayRequest {
  string refund_order_sn = 1;
  //申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string res_type = 2;
  //操作类型
  string operation_type = 3;
  //操作用户
  string operation_user = 4;
  //原因
  string reason = 5;
}

message RefundOrderPayCallBackRequest {
  string refund_order_sn = 1;
  //返回状态码  1：退款成功 3：退款失败
  string rsp_code = 2;
  //返回信息
  string rsp_message = 3;
}


message CancelOrderByJdRequest {
  //外部订单号
  string external_order_sn = 1;
}

message DeleteRefundOrderRequest {
  //售后单号
  string refund_sn = 1;
}

//全渠道同步模块
service AllChannelService {

  //售后订单同步 emall.afterorder.synchronize
  rpc AfterOrderSynchronize (AfterorderRequest) returns (AfterorderResponse) {
  }

  //订单状态同步 emall.orderstatus.synchroni
  rpc OrderStatusSynchronize (OrderStatusRequest) returns (OrderStatusResponse) {
  };

  //订单发货
  rpc SelfmallOrderSend (OrderSendRequest) returns (OrderSendResponse) {
  };

  rpc AfterorderStatus (AfterorderStatusRequest) returns (AfterorderStatusResponse) {
  };
}
//------------------------------反向接口------------------------------------------
//selfmall.afterorder.status.sync  售后单状态回写
message AfterorderStatusRequest {
  //网店订单编号
  string Tid = 1;
  //售后单状态 WaitAgree：待审核 Agree：审核同意  Refuse：拒绝 Invalid：作废 UnFinish：待处理完成 Finished：处理完成  AgreeInvalid：同意作废  RefuseInvalid：拒绝作废
  string status = 2;
}
//售后回写状态请求参数
message AfterorderStatusResponse {
  string Code = 1;
  string Message = 2;
  string Iserror = 3;
  string Errormsg = 4;
}
//selfmall.order.send 订单发货
message OrderSendRequest {
  //订单编号
  string Tid = 1;
  //物流公司编码（如圆通”YTO”，申通“STO”）
  string Companycode = 2;
  //是否拆单（ 0=未拆单，1=拆单，默认为0）
  string Issplit = 3;
  //物流单号
  string Outsid = 4;
  //子订单号（当 issplit = 1 时，必填。多个用 “,” 分隔）
  string Subtid = 5;
}
//发货响应参数
message OrderSendResponse {
  string Code = 1;
  string Message = 2;
  //成功的订单编号
  string Tid = 3;
  //是否拆单
  string Issplit = 4;
  //子订单号（子订单间用逗号“,”隔开）
  string Subtids = 5;
}

//-----------------------------反向接口-------------------------------------------

//库存查询emall.stock.get请求参数
message StockGetRequest {
  int32 page = 1;
  int32 pagesize = 2;
  string skucode = 3;
  string goodscode = 4;
  string whscode = 5;
  bool iscontainwhs = 6;
}

//库存查询响应参数
message StockGetResponse {
  int32 Code = 1;
  string Message = 2;
  int32 Total = 3;
  repeated Stock Stocks = 4;
}

message Stock {
  string Goodsid = 1;
  string Goodscode = 2;
  string Goodsname = 3;
  string Skuid = 4;
  string Skuname = 5;
  float Qty = 6;
  float Enablenum = 7;
  float Ensalenum = 8;
  string Whscode = 9;
  string Whsname = 10;
}

//售后订单同步 请求参数  emall.afterorder.synchronize
message AfterorderRequest {
  repeated OrderAfterorder Orders = 1;
}

//售后订单同步 订单信息
message OrderAfterorder {
  //自建商城线上售后单号
  string Rtid = 1;
  //网店订单编号
  string Tid = 2;
  //订单总金额
  string Total = 3;
  //订单享受优惠的金额
  string Privilege = 4;
  //运费
  string Postfee = 5;
  //售后单创建时间
  string Created = 6;
  //售后单状态 WaitAgree= 待审核 Agree=审核同意 Refuse=拒绝 Invalid=作废 UnFinish=待处理完成 Finished=处理完成 AgreeInvalid=同意作废 RefuseInvalid=拒绝作废
  string Status = 7;
  //售后单类型  JustRefund=仅退款 RefundAndGoods=退款退货
  string Aftsaletype = 8;
  //售后问题原因代码 01=无理由退换货 02=质量问题 03=损坏 04=错发 05=漏发
  string Reasoncode = 9;
  //物流单号
  string Logistbillcode = 10;
  //售后单备注
  string Aftsaleremark = 11;
  //售后单商品信息
  repeated OrderAfterorderDetails Details = 12;
}

//售后订单同步 订单信息List
message OrderAfterorderDetails {
  //自建商城发货单订单明细编号
  string Oid = 1;
  //网店商品名称
  string Eshopgoodsname = 2;
  //网店商品SKU名称
  string Eshopskuname = 3;
  //退货数量（不能为0）
  string Backqty = 4;
  //退款金额
  string Backtotal = 5;
  //规格商家编码
  string Outeriid = 6;
}

//售后订单同步 响应参数 订单信息List
message AfterorderResponse {

  int32 Code = 2;
  string Message = 3;
  repeated AfterorderRe ReOrderList = 1;
}

message AfterorderRe {
  //是否失败
  bool Iserror = 1;
  //自建商城发货单编号
  string Tid = 2;
  //管家婆全渠道售后单编号
  string Billcode = 3;
  //错误描述
  string Message = 4;
}

//------------------------------------------------------------------------
//订单同步 响应参数
message OrderInsertResponse {
  repeated Reorders orders = 1;
  //返回状态码   0 = 调用成功
  int32 code = 2;
  //返回状态描述
  string message = 3;
}

//订单同步
message Reorders {
  //网店订单编号
  string tid = 1;
  string billcode = 2;
  string message = 3;
}

//订单同步 emall.order.synchronize
message OrderInsertRequest {
  repeated OrderParam Orders = 1;
}


message OrderParam {
  //网店订单编号
  string Tid = 1;
  //重量
  string Weight = 2;
  //尺寸
  string Size = 3;
  //买家账号
  string Buyernick = 4;
  //卖家留言
  string Buyermessage = 5;
  //卖家备注
  string Sellermemo = 6;
  //订单总金额
  string Total = 7;
  //订单享受优惠的金额
  string Privilege = 8;
  //运费
  string Postfee = 9;
  //收货人名称
  string Receivername = 10;
  //收货省
  string Receiverstate = 11;
  //收货市
  string Receivercity = 12;
  //收货区
  string Receiverdistrict = 13;
  //收货地址
  string Receiveraddress = 14;
  //收货电话
  string Receiverphone = 15;
  //收货人手机号
  string Receivermobile = 16;
  //订单创建时间
  string Created = 17;
  //订单状态 NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string Status = 18;
  //订单类型（Cod=货到付款, NoCod=非货到付款）
  string Type = 19;
  //发票抬头
  string Invoicename = 20;
  //付款时间
  string Paytime = 21;
  //物流公司编码
  repeated string Logistbtypecode = 22;
  //往来单位编码
  string Btypecode = 23;
  //订单商品信息
  repeated OrderDetailsParam Details = 24;
  //卖家旗帜（数值型）
  string Sellerflag = 25;
}

//订单详情信息
message OrderDetailsParam {
  //网店订单明细编号
  string Oid = 1;
  //商品条码
  string Barcode = 2;
  //网店商品ID
  string Eshopgoodsid = 3;
  //网店商家编码
  string Outeriid = 4;
  //网店商品名称
  string Eshopgoodsname = 5;
  //网店商品SKU ID
  string Eshopskuid = 6;
  //网店商品SKU名称
  string Eshopskuname = 7;
  //商品ID
  string Numiid = 8;
  //规格ID
  string Skuid = 9;
  //基本单位数量
  string Num = 10;
  //商品总额
  string Payment = 11;
  //商品图片路径
  string Picpath = 12;
  //重量
  string Weight = 13;
  //尺寸，体积
  string Size = 14;
  //销售单位ID
  string Unitid = 15;
  //销售单位数量
  string Unitqty = 16;
}

//-----------------------------------------------------------

//售后订单同步模块请求参数
message OrderStatusRequest {
  //状态码
  repeated AfterOrder Orders = 1;
}
//订单状态同步请求参数详情信息
message AfterOrder {
  //自建商城订单编号
  string Tid = 1;
  //订单交易状态   NoPay = 未付款  Payed = 已付款  Sended = 已发货  TradeSuccess = 交易成功  TradeClosed = 交易关闭   PartSend = 部分发货
  string Status = 2;
  //错误信息
  string Refundstatus = 3;
}

//订单状态同步响应参数
message OrderStatusResponse {
  //返回状态码   0 = 调用成功
  int32 Code = 1;
  //返回状态描述
  string Message = 2;
  //操作人
  repeated OrderStatusOrders orders = 3;
}
//----------------------------------------------
//订单状态同步响应参数详情信息
message OrderStatusOrders {
  //自建商城订单编号
  string tid = 1;
  string status = 2;
  //同步结果描述
  string message = 3;
}

//要插入MQ的content，JSON数据
message MqContent {
  //自建商城订单编号
  string content = 1;
  string exchange = 2;
  string quene = 3;

}

//订单模块
service OrderService {
  //退货释放库存
  rpc InsertMQ (MqContent) returns (BaseResponse) {
  };

  //取消未支付订单
  rpc CancelUnpaidOrder (CancelUnpaidOrderRequest) returns (BaseResponse) {
  };

  //售后订单申请
  rpc AfterApplyOrder (AfterApplyOrderRequest) returns (BaseResponse) {
  };

  //订单查询
  rpc QueryOrder (QueryOrderRequest) returns (QueryOrderResponse) {
  };

  //退货释放库存
  rpc ReleaseStock (ReleaseStockRequest) returns (BaseResponse) {
  };

  //管易订单查询接口
  rpc GyOrderQuery (GyOrderQueryRequest) returns (GyOrderQueryResponse) {
  };

  //管易订单物流信息下发
  //rpc GySyncOrderExpress (GySyncOrderExpressRequest) returns (BaseResponse) {};

  //管易查询退款订单请求
  rpc GyQueryRefundOrder (GyQueryRefundOrderRequest) returns (GyQueryRefundOrderResponse) {
  };

  //阿闻管家订单中心详情之店铺信息接口
  //    rpc AwenOrderStoreDetail (AwenOrderStoreDetailRequest) returns (AwenOrderStoreDetailResponse) {};

  //阿闻管家订单中心详情之买家信息接口
  //    rpc AwenOrderBuyerDetail (AwenOrderBuyerDetailRequest) returns (AwenOrderBuyerDetailResponse) {};

  //阿闻管家预订单列表查询
  rpc BookingOrderList (BookingOrderRequest) returns (BookingOrderResponse) {
  };

  //阿闻管家预订单打印小票
  rpc PrintBookingOrder (PrintBookingOrderRequest) returns (PrintBookingOrderResponse) {
  };

  //阿闻管家接单(后台调用)
  rpc AcceptOrder (AcceptOrderRequest) returns (BaseResponse) {
  };

  //阿闻管家取消接单(后台调用)
  rpc CancelAcceptOrder (CancelAcceptOrderRequest) returns (BaseResponse) {
  };

  //阿闻管家拣货(后台调用)
  rpc PickingOrder (PickingOrderRequest) returns (BaseResponse) {
  };

  //京东到家拣货完成接口
  rpc JddjPickingOrder (PickingOrderRequest) returns (BaseResponse) {
  };

  //订单完成（美团推送）
  rpc AccomplishOrder (AccomplishOrderRequest) returns (BaseResponse) {
  };

  //同步配送状态，给order_api调用给用户显示的
  rpc MpOrderRiderLocation (RiderLocationRequest) returns (ExternalResponse);

  //配送状态新增
  rpc DeliveryNode (DeliveryNodeRequest) returns (BaseResponse) {
  };

  //美团专配状态新增
  rpc MtDeliveryNode (DeliveryNodeRequest) returns (BaseResponse) {
  };

  //饿了么配送状态新增
  rpc ElmDeliveryNode (ElmDeliveryNodeRequest) returns (BaseResponse) {
  };

  //京东到家配送状态新增
  rpc JddjDeliveryNode (ElmDeliveryNodeRequest) returns (BaseResponse) {
  };

  //取消订单
  rpc CancelOrder (CancelOrderRequest) returns (BaseResponse) {
  }

  //催单
  rpc ReminderOrder (ReminderOrderRequest) returns (BaseResponse) {
  }

  //美团后台接单推送
  rpc MtAcceptOrder (MtAcceptOrderRequest) returns (BaseResponse) {
  }

  //重新发起推送到第三方
  rpc PushDelivery (MtRePushThirdRequest) returns (BaseResponse) {
  }
  //重新发起推送到第三方
  rpc MtRePushThird (MtRePushThirdRequest) returns (BaseResponse) {
  }

  //查询配送费
  rpc SearchDelivery (MtRePushThirdRequest) returns (SearchDeliveryPriceResponse) {
  }


  //订单支付
  rpc AwenOrderPay (AwenOrderPayRequest) returns (BaseResponse) {
  }

  //订单支付信息查询
  rpc AwenOrderPayQuery (AwenOrderPayQueryRequest) returns (AwenOrderPayQueryResponse) {
  }

  //订单支付
  rpc AwenOrderB2CPay (AwenOrderB2CPayRequest) returns (BaseResponse) {
  }

  //取消订单(阿闻小程序)
  rpc AwenOrderCancle (AwenOrderCancleRequest) returns (BaseResponse) {
  }

  //快递公司获取
  rpc ExpressCompanyList (ExpressCompanyListRequest) returns (ExpressCompanyListResponse) {
  }

  //保存退货物流信息
  rpc ExpressInfoUpdate (ExpressInfoUpdateRequest) returns (BaseResponse) {
  }

  //物流路由对接
  rpc ExpressInfo (ExpressInfoRequest) returns (ExpressInfoResponse) {
  }

  //售后申请单列表
  rpc ApplyOrderList (ApplyOrderListRequest) returns (AwenOrderListResponse) {
  };

  //获取订单信息请求
  rpc GetOneOrder (GetOneOrderRequest) returns (GetOneOrderResponse) {
  };

  //获取订单商品信息请求
  rpc GetOrderProducts (GetOneOrderRequest) returns (GetOrderProductResponse) {
  };

  //获取退款订单商品信息请求
  rpc GetRefundOrderProducts (GetOneOrderRequest) returns (GetRefundOrderProductResponse) {
  };

  //订单支付通知
  rpc OrderPayNotify (OrderPayNotifyRequest) returns (BaseResponse) {
  };

  //修改未发出配送为已发出
  rpc UPDateElmOrderPushDelivery (UPDateElmOrderPushDeliveryRequest) returns (BaseResponse) {
  };

  //判断第三方订单是否存在返回
  rpc OrderIsExist (OrderIsExistRequest) returns (OrderIsExistResponse);

  //获取阿闻健康管理订单详情
  rpc GetHealthOrder (GetHealthOrderReq) returns (GetHealthOrderResponse);

  //保存业绩分配
  rpc SavePerformance (SavePerformanceRequest) returns (google.protobuf.Empty);

  //临时提供修改完成订单的接口
  rpc OrderPayCompleteTemporary (GetOneOrderRequest) returns (BaseResponse);

  //智慧中心前置仓订单销售统计数据
  rpc IntelligenceOrderSales (GetIntelligenceOrderRequest) returns (IntelligenceOrderResponse);

  //京东订单调整取消原订单
  rpc JddjAdjustCancelOrder (JddjAdjustOrderRequest) returns (BaseResponse);

  //保存第三方回调原数据
  rpc SaveOrderOriginData (SaveOrderOriginDataRequest) returns (BaseResponse);

  //根据核销码获取有效的虚拟商品订单信息
  rpc GetVirtualOrderDetail (GetVirtualOrderDetailRequest) returns (VirtualOrderResponse);
  //核销虚拟订单
  rpc WrittenOffVirtualOrder (WrittenOffVirtualOrderRequest) returns (VirtualOrderResponse);

  rpc WrittenOffByFinancialCode (WrittenOffByFinancialCodeRequest) returns (BaseResponse);
  //查询核销码
  rpc GetVerifyCodes (GetVerifyCodesRequest) returns (GetVerifyCodesResponse);

  //阿闻管家-订单中心-父订单列表接口
  rpc AwenParentOrderList (AwenParentOrderListRequest) returns (AwenParentOrderListResponse) {
  };
  //报表接口
  rpc OrderDeliveryReportList (AwenParentOrderListRequest) returns (DeliveryReportResponse) {
  };
  //阿闻管家-订单中心详情-父订单基础信息
  rpc AwenParentOrderBaseDetail (AwenAllOrderBaseDetailRequest) returns (AwenParentOrderBaseDetailResponse) {
  };
  //阿闻管家-订单中心详情-父订单物流信息接口
  rpc AwenParentOrderDeliveryDetail (AwenOrderDeliveryDetailRequest) returns (AwenOrderDeliveryDetailResponse) {
  };
  //阿闻管家-订单中心详情-父订单发货状态
  rpc AwenParentOrderDeliveryState (AwenOrderDeliveryStateRequest) returns (AwenOrderDeliveryStateResponse) {
  };

  //阿闻管家-订单中心-实物订单列表接口
  rpc AwenMaterOrderList (AwenMaterOrderListRequest) returns (AwenMaterOrderListResponse) {
  };
  //阿闻管家-订单中心详情-实物订单基础信息
  rpc AwenMaterOrderBaseDetail (AwenAllOrderBaseDetailRequest) returns (AwenMaterOrderBaseDetailResponse) {
  };
  //阿闻管家-订单中心详情-实物订单物流信息接口
  rpc AwenMaterOrderDeliveryDetail (AwenOrderDeliveryDetailRequest) returns (AwenOrderDeliveryDetailResponse) {
  };
  //阿闻管家-订单中心详情-实物订单发货状态
  rpc AwenMaterOrderDeliveryState (AwenOrderDeliveryStateRequest) returns (AwenOrderDeliveryStateResponse) {
  };

  //阿闻管家-订单中心-虚拟订单列表接口
  rpc AwenVirtualOrderList (AwenVirtualOrderListRequest) returns (AwenVirtualOrderListResponse) {
  };
  //阿闻管家-订单中心详情-虚拟订单基础信息
  rpc AwenVirtualOrderBaseDetail (AwenAllOrderBaseDetailRequest) returns (AwenVirtualOrderBaseDetailResponse) {
  };
  //阿闻管家-订单中心详情-虚拟订单物流信息接口
  rpc AwenVirtualOrderDeliveryDetail (AwenOrderDeliveryDetailRequest) returns (AwenOrderDeliveryDetailResponse) {
  };
  //阿闻管家-订单中心详情-虚拟订单发货状态
  rpc AwenVirtualOrderDeliveryState (AwenOrderDeliveryStateRequest) returns (AwenOrderDeliveryStateResponse) {
  };

  //拆单后订单推送电商
  rpc PushSplitResultToMall (GetHealthOrderReq) returns (BaseResponse) {};

  rpc GetTodayOrderSum(GetTodayOrderSumReq) returns(GetTodayOrderSumRes){};

  //取消配送
  rpc CancelDelivery(CancelDeliveryRequest) returns(BaseResponse){};

  //取消配送
  rpc ConfirmGoodsReturn(ConfirmGoodsReturnRequest) returns(BaseResponse){};

  //获取美团订单所属是阿闻自有orTP代运营
  rpc GetOrderAppChannel(GetOrderAppChannelReq) returns(GetOrderAppChannelRes){};
  //根据订单号获取appChannel tp代运营饿了么版本新增
  rpc QueryAppChannelByOrderSn(QueryAppChannelByOrderSnReq) returns(QueryAppChannelByOrderSnRes){};
  //给订单打标签
  rpc MarkingTagForOrder(MarkingTagForOrderReq) returns(BaseResponse){};

  rpc PushIntegral(PushIntegralRequest) returns(BaseResponse){};

  rpc RePushOms(MtAddOrderResponse) returns(BaseResponse){};

  //临时更新数据接口
  rpc UpdateOrderData(UpdateOrderDataRequest) returns (UpdateOrderDataRespond){};

  // 修改订单货号
  rpc UpdateOrderSku (UpdateOrderSkuReq) returns (BaseResponse) {};

  //获取订单支付信息
  rpc GetOrderPayInfo(GetOneOrderRequest) returns (OrderPayInfoResponse){};
  // 校验互联网医疗订单能否下单
  rpc DigitalHealthOrderCheck (DigitalHealthOrderCheckRequest) returns (DigitalHealthOrderCheckResponse) {};
  // 获取订单关联的信息
  rpc GetOrderRelationInfo (GetOrderRelationInfoRequest) returns (GetOrderRelationInfoResponse) {};

  //订单设置发货
  rpc OrderSetDelivery(OrderSetDeliveryRequest)returns(BaseResponse);
  //确认已送达，完成订单
  rpc ConfirmDeliveredOrder(ConfirmDeliveredOrderRequest)returns(BaseResponse);

  //查询电商虚拟订单信息
  rpc QueryMallVirtualOrderExtendInfo(QueryMallVirtualOrderExtendInfoRequest)returns(QueryMallVirtualOrderExtendInfoResponse);
  //延长虚拟订单的兑换有效期
  rpc ExtendMallVirtualOrderVerifyCodeExpiryDate(ExtendMallVirtualOrderVerifyCodeExpiryDateRequest)returns(BaseResponse);
  // 查询电商虚拟订单核销码可核销的列表
  rpc QueryMallVirtualOrderWriteOffCodes(QueryMallVirtualOrderWriteOffCodesRequest)returns(QueryMallVirtualOrderWriteOffCodesResponse);

  // 检查是否开过处方
  rpc PrescribeCheck(OrderPrescribeCheckReq) returns(OrderPrescribeCheckRes);
  // 开处方
  rpc Prescribe(OrderPrescribeReq) returns(OrderPrescribeRes);
  // 订单列表-发起配送
  rpc ReDelivery (ReDeliveryRequest) returns (OrderExceptionResponse) {
  };
  // 麦芽田订单列表查询
  rpc MytOrderList(MytOrderListRequest) returns(MytBaseDataResponse);
  // 麦芽田订单详情查询
  rpc MytOrderDetail(MytOrderListRequest) returns(MytBaseDataResponse);
}

message RiderLocationRequest {
  //配送活动标识
  int64 delivery_id = 1;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 2;
  //配送服务代码，详情见合同
  //飞速达: 4002
  //快速达: 4011
  //及时达: 4012
  //集中送: 4013
  //自由达: 4014
  int32 delivery_service_code = 3;
}


//临时更新数据接口
message UpdateOrderDataRequest{
  //单个推送 1, 全量推送 2
  int32  type = 1;
  //单个推送时赋值
  string order_sn = 2;
  // 全量推送时赋值 2022-03-20 11:22:43
  string date = 3;
}

message UpdateOrderDataRespond{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}


message MarkingTagForOrderReq{
  //打标开始时间
  string order_marking_time = 1;
}

message  AwenOrderPayResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //下单时间(13位，毫秒)
  int64 create_time = 4;
}

message GetOrderAppChannelReq{
  //美团订单号
  string order_sn = 1;
}
message QueryAppChannelByOrderSnReq{
  //美团订单号
  string order_sn = 1;
}

message GetOrderAppChannelRes{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单信息
  OrderAppChannelData data = 4;
}
message QueryAppChannelByOrderSnRes{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}


message OrderAppChannelData{
  //1.阿闻自有,2.TP代运营,0查不到门店
  int32 app_channel = 1;
}

message GetTodayOrderSumReq{
  //
  string start_time = 1;
  //
  string stop_time = 2;
}

message GetTodayOrderSumRes{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单信息
  TodayOrderSumData OrderSum = 4;
}

message TodayOrderSumData{
  //该日期的下单金额之和
  float give_order_amount_sum = 1;
  //该日期的下单数量之和
  int32 give_order_num_sum = 2;
  //该日期的支付金额之和
  float payment_amount_sum = 3;
  //该日期的支付数量之和
  int32 payed_num_sum = 4;
}

message GetHealthOrderReq {
  string order_sn = 1;
}

message GetHealthOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单信息
  HealthOrderInfo OrderDetail = 4;
}

message GetVirtualOrderDetailRequest {
  string written_off_code = 1;
}
message WrittenOffVirtualOrderRequest {
  string verify_code = 1;
  string store_id = 2;
  int32 source = 3;
  int32  user_agent = 4;
  string member_id = 5;
}
// 通过财务编码核销
message WrittenOffByFinancialCodeRequest {
  string verify_code = 1;
  string financial_code = 2;
}
message VirtualOrderResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string old_order_detail = 4;
  string old_order_goods = 5;
}
message HealthOrderInfo {
  string order_sn = 1;
  int32 order_type = 2;
  string scrm_user_id = 3;
  string scrm_pet_id = 4;
  string category_code = 5;
  string ensure_code = 6;
}


message SavePerformanceRequest {
  string order_sn = 1;
  string staff_id = 2;
  string staff_name = 3;
  string operator_id = 4;
  string operator_name = 5;
  //机构id
  int64 orgid = 6;
}

message UPDateElmOrderPushDeliveryRequest {
  string oldordersn = 1;
}


message ApplyOrderListRequest {
  int32 channel_id = 1;
  //渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏
  int32 user_agent = 2;
  string keywords = 3;
  int32 page_index = 4;
  int32 page_size = 5;
  string member_id = 6;
  string create_time_gt = 7;
}

message ExpressInfoUpdateRequest {
  //物流号(或快递单号)
  string express_no = 1;
  //物流信息
  string express_info = 2;
  //订单号 正向订单必填
  string order_id = 3;
  //物流公司id 售后单必填
  int32 express_company_id = 4;
  //服务单号  售后单必填
  string refund_sn = 5;
  //物流公司代码，正向订单必填
  string express_code = 6;
  //快递单记录id 正向单必填
  int32 order_express_id = 7;
  //更新订单类型 0售后单 1正向单
  int32 update_order_type = 8;
}
message ExpressInfoRequest {
  //物流单号
  string express_no = 1;
  //订单号
  string order_sn = 2;
  //服务单号
  string refund_sn = 3;
  // 搜索类型 默认空：售后服务物流 1:子单号 2：父单号
  string search_type = 4;
}
message ExpressInfoResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //物流号
  string express_no = 4;
  //物流信息列表
  repeated ExpressInfo dataList = 5;
  //物流公司
  string express_company = 6;
  //订单id
  string order_no = 7;
}
message ExpressInfo {
  //时间点
  string datetime = 1;
  //物流信息
  string info = 2;
  //状态
  string status = 3;
}
message ExpressCompanyListRequest {
  //每页大小
  int32 page_size = 1;
  //当前页
  int32 page = 2;
  //查询条件
  string keyword = 3;
  // 主体id
  int64 org_id = 4;
}
message ExpressCompanyListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated ExpressCompany dataList = 4;
}
message ExpressCompany {
  //id
  string id = 1;
  //物流公司名称
  string company = 2;
}
message AwenOrderPayRequest {
  //支付方式 1：微信 JSAPI
  int32 transType = 1;
  //商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
  string outTrade_no = 2;
  //商户订单号
  string order_id = 3;
  //实付金额 单位分
  int32 pay_price = 4;
  //订单金额 单位分
  int32 total_price = 5;
  //优惠金额 单位分
  int32 discount = 6;
  //微信用户标识 JSAPI 支付时必传
  string openid = 7;
  //子商户公众账号ID 微信小程序必传
  string sub_app_id = 8;
  //商品编号
  string product_id = 9;
  //商品名称 最长 32 字节
  string product_name = 10;
  //商品描述
  string product_desc = 11;
  //后台回调地址
  string offline_notify_url = 12;
  //客户端 IP
  string client_ip = 13;
  //商户号
  string merchant_id = 14;
  //扩展信息 预留字段，JSON 格式
  string extend_info = 15;

  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string order_pay_type = 16;
  // appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
  int32 app_id = 17;
}

message AwenOrderPayQueryRequest {
  string order_id = 1;
}
message AwenOrderPayQueryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  // 支付信息
  AwenOrderPayInfoData data = 3;
}
message AwenOrderPayInfoData {
  // 交易订单id
  string order_id = 1;
  // 订单状态   0-交易中，1-交易完成，2-交易失败
  int32 status = 2;
  // 交易流水号
  string trade_no = 4;
  // 支付金额
  int32 pay_price = 6;
  // 交易时间
  string add_time = 8;
  // 支付时间
  string pay_time = 9;
}

message AwenOrderB2CPayRequest {
  //订单号
  string mer_order_no = 1;
  //付款码
  string bar_code = 2;
  //支付方式 1：微信 2：支付宝 3: 银联
  int32 pay_type = 3;
  //实际支付金额
  int32 pay_amount = 4;
  //不参与优惠金额
  string undiscountable_amount = 5;
  //订单名称 银联时否
  string order_name = 6;
  //订单描述
  string order_desc = 7;
  //订单有效期单位 00-分 01-小时 02-日 03-月
  string validUnit = 8;
  //订单有效期单位 结合单位一起使用
  string validNum = 9;
  //外部订单号
  string out_order_no = 14;
  //订单总金额
  int32 total_amount = 15;
  //优惠
  int32 discount = 16;
  //后台回调地址（支付中心回调电商）
  string NotifyUrl = 17;
  //商户号
  string merc_id = 18;
  //机构号
  string org_id = 19;
  //传机具编号（tsn）
  string trm_sn = 20;
  //终端号，传标准终端绑定接口返回的dyTermNo
  string trm_id = 21;
  //竖屏来源区分 1-商城 0-默认到家
  int32 source =22;
  string location = 23;
}

message AwenOrderSubmitRequest {
  //商户或门店id
  string shop_id = 1;
  //渠道ID
  string channel_id = 2;
  //商户名称
  string shop_name = 3;
  //收件人
  string receiver_name = 4;
  //收件省
  string receiver_state = 5;
  //收件市
  string receiver_city = 6;
  //收件区
  string receiver_district = 7;
  //收件地址
  string receiver_address = 8;
  //收件电话
  string receiver_phone = 9;
  //总优惠金额
  int32 privilege = 10;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 11;
  //商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）
  int32 goods_total = 12;
  //总运费
  int32 freight = 13;
  //发票信息
  string invoice = 14;
  //买家留言
  string buyer_memo = 15;
  //商品
  repeated AwenOrderProductModel OrderProductModel = 16;
  //收货地址纬度
  double latitude = 17;
  //收货地址经度
  double longitude = 18;
  //总重量(非必填)
  int32 total_weight = 19;
  //参与优惠活动信息
  repeated PromotionOrder promotionOrderList = 20;
  //预计送达时间
  string expected_time = 21;
  //订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送
  int32 order_type = 22;
}

message AwenOrderProductModel {
  //sku
  string sku = 1;
  //商品id
  string product_id = 2;
  //商品名称
  string product_name = 3;
  //商品编码
  string bar_code = 4;
  //单价
  int32 price = 5;
  //数量
  int32 number = 6;
  //商品图片
  string image = 7;
  // 参与限时折扣的商品数量
  int32 discountcount = 8;
  // 促销活动Id
  int32 promotion_id = 9;
  // 折扣价格
  int32 discount_price = 10;
  // 促销活动Id
  int32 promotionType = 11;
}
message AwenOrderSubmitResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated SubmitProduct dataList = 4;
  //提交订单成功后，返回订单ID
  string order_sn = 5;

}
// 促销活动与订单关联关系
message PromotionOrder {
  //主键Id
  int64 id = 1;
  // 促销活动Id
  int32 promotionId = 2;
  // 活动类型
  int32 promotionType = 3;
  //促销活动满减Id
  int32 promotionReduceId = 4;
  // 促销活动优惠
  string promotionTitle = 5;
  //活动名称
  string promotionName = 6;
  //活动优惠金额
  int32 promotionFee = 7;
}
message SubmitProduct {
  string id = 1;
  //商品id
  string product_id = 2;
  //商品名称
  string product_name = 3;
  //商品编码
  string bar_code = 4;
  //1:无货 2:失效 3:下架
  int32 status = 5;
}
message AwenOrderCancleRequest {
  //订单ID
  string order_id = 1;
  //取消原因
  string cancel_reason = 2;
}
message ExpressAddressDelRequest {
  //id
  int32 id = 1;
}
message ExpressAddressSetRequest {
  //id
  int32 id = 1;
  //省
  string provice = 2;
  //市
  string city = 3;
  //县区
  string county = 4;
  //详细地址
  string detail = 5;
  //联系人
  string contacts_name = 6;
  //联系电话
  string mobile = 7;
  //是否默认地址
  bool default = 8;
}
message ExpressAddressListRequest {
  //每页大小
  int32 page_size = 1;
  //当前页
  int32 page = 2;
  //查询条件
  string keyword = 3;
}

message ExprssAddressListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated Address dataList = 4;
}
message Address {
  //id
  int32 id = 1;
  //省
  string provice = 2;
  //市
  string city = 3;
  //县区
  string county = 4;
  //详细地址
  string detail = 5;
  //联系人
  string contacts_name = 6;
  //联系电话
  string mobile = 7;
  //是否默认地址
  bool default = 8;
}

//售后订单申请
message AfterApplyOrderRequest {
  //退款单号
  string refund_sn = 1;
  //原始销售单号
  string order_sn = 2;
  //创建时间
  string create_time = 3;
  //管家婆审核状态
  string status = 4;
  //全渠道需要（售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货）
  string refund_type_sn = 5;
  //全渠道：01=无理由退换货 02=质量问题    03=损坏  04=错发  05=漏发
  //管易：01=快递问题 0002=赝品 0003=有色差 0004=尺码不对 0005=布料有瑕疵  0006=商品质量有问题 0007=其它  0009=不想要  9999=其它平台发货
  string reason_code = 6;
  //售后单备注
  string refund_remark = 7;
  //申请类型:1为退款,2为退货,默认为1
  int32 refund_type = 8;
  //售后单 商品集合
  repeated RefundGoodsOrder refundGoodsOrders = 9;
  //优惠金额
  string discount_amount = 10;
  //运费
  string post_fee = 11;
  //退款原因
  string refund_reason = 12;
  //退货快递名称 (管易)
  string express_name = 13;
  //退货快递单号 (管易)
  string express_num = 14;
  //退款金额
  string refund_amount = 15;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 order_source = 16;
  // 退款单 支付信息
  repeated RefundPayOrder refundPayOrder = 17;
}

// 退款单 支付信息
message RefundPayOrder {
  //支付方式代码
  string pay_type_code = 1;
  //支付金额
  string payment = 2;
  //支付时间
  string pay_time = 3;
  //对应帐号
  string account = 4;
}

// 退款单 商品集合
message RefundGoodsOrder {
  //主键ID
  string id = 1;
  //商品sku id 可废弃 使用sku_id代替
  string goods_id = 2;
  //退款数量
  int32 quantity = 3;
  //退款金额
  string refund_amount = 4;
  //订单明细Id
  string oc_id = 5;
  //管易必传 商品条码
  string barcode = 6;
  //退款单号
  string refundorderid = 7;
  //退款商品名称
  string food_name = 8;
  //商品sku的规格名称
  string spec = 9;
  double refund_price = 10;
  //当前商品sku需使用包装盒的单价，即单个包装盒的价格，单位是元。
  double box_price = 11;

  //单件商品sku需使用的包装盒数量，同商家同步商品时维护的此相同字段的信息。
  float box_num = 12;
  //审核退款数据量，库存加回去用这个数量
  int32 tkcount = 13;
  //当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
  float food_price = 14;

  //销售数量
  int32 selnumber = 15;
  //限时折扣活动id
  int32 promotion_id = 16;
  //商品sku_id
  string sku_id = 17;
  int64 order_product_id = 18;

}


message CancelUnpaidOrderRequest {
  string orderId = 1;
}

//通用返回
message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//购物车及下单
service CartService {
  //电商提交订单(通用返回)
  rpc SubmitOrder (NewAddOrderRequest) returns (NewAddOrderResponse) {
  }

  //统一提交订单
  rpc MtSubmitOrder (MtAddOrderRequest) returns (MtAddOrderResponse) {
  }

  //美团提交订单数据 （落地数据库）
  rpc MtSubmitOrderData (MtAddOrderRequest) returns (MtAddOrderResponse) {
  }

  //订单调整
  //  rpc JddjAdjustOrder (JddjAdjustOrderRequest) returns (BaseResponse) {}

  //健康管理订阅订单
  rpc HealthSubOrder (HealthSubOrderRequest) returns (HealthSubOrderResponse) {
  }

  //创建健康管理套餐于订单管理
  rpc UpdateOrderMeal (UpdateOrderMealRequest) returns (BaseResponse) {
  }

  //修改订单信息
  rpc MtUpdateOrder (MtAddOrderRequest) returns (MtAddOrderResponse) {
  }
  //DeliveryNodeRequest
}
message UpdateOrderMealRequest {
  //订单号
  string order_sn = 1;
  //卡号（健康订阅）
  string ensure_code = 2;
  //批次编码（健康订阅）
  string batch_code = 3;
}

message HealthSubOrderRequest {
  //scrm用户Id
  string scrm_user_id = 1;
  //scrm 宠物Id
  string scrm_pet_id = 2;
  //支付金额
  int32 pay_money = 3;
  //套餐编码
  string category_code = 4;
  //套餐名称
  string category_name = 5;
  //会员名称
  string member_name = 6;
  //member_tel
  string member_tel = 7;
  //卡号
  string ensure_code = 8;

}

message HealthSubOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单号
  string order_sn = 4;
}

//退货退款
service AfterSaleService {
  //查询可被部分退款的商品详情
  rpc OrderGetPartRefundFoods (OrderGetPartRefundFoodsRequest) returns (OrderPartRefuFoodsResponse) {
  }

  //发起部分退款
  rpc OrderApplyPartRefund (OrderApplyPartRefundRequest) returns (BaseResponse) {
  }

  //退款回调接口
  //    rpc OrderRetrun (OrderRetrunRequest) returns (BaseResponse) {}

  //退款审核同意
  rpc MtOrderRefundAgree (MtOrderRefundRequest) returns (ExternalResponse) {
  }

  //退款审核拒绝
  rpc MtOrderRefundReject (MtOrderRefundRequest) returns (ExternalResponse) {
  }

  //获取订单退款信息
  rpc OrderRetrunGetList (RetrunOrderListRequest) returns (RetrunOrderListResponse) {
  }

  //获取退款单信息
  rpc OrderRetrunGet (OrderRetrunGetRequest) returns (OrderRetrunGetResponse) {
  }

  //获取退款单订单详情
  rpc OrderRetrunGetDetail (RetrunOrderDetailRequest) returns (RetrunOrderDetailResponse) {
  }

  //退款管理列表
  rpc RefundOrderList (RefundOrderInfoRequest) returns (RefundOrderInfoResponse) {
  }

  //退款服务列表（用户端）
  rpc RefundOrderServiceList (RefundOrderServiceListRequest) returns (RefundOrderListResponse) {
  }

  //修复第三方退款数据（v6.0虚实版本之前的 手动脚本）
  rpc FixThirdRefundData (FixThirdRefundDataRequest) returns (BaseResponse) {
  }
  //退款重推子龙
  rpc ManualRefundToZiLong (ManualRefundToZiLongRequest) returns (BaseResponse) {
  }
  // 退款推第三方
  rpc RefundRePushThird (RefundRePushThirdRequest) returns(BaseResponse){}
}

message RefundOrderServiceListRequest {
  //当前页码
  int32 page_index = 1;
  //每页行数
  int32 page_size = 2;
  //订单编号
  repeated string order_sn = 3;
  //前端搜索关键词，可能是退款单编号也可能是订单编号
  string keywords = 4;
  //会员id
  string member_id = 5;
  //渠道id
  int32 channel_id = 6;
}

//退款推送参数
message OrderRetrunGetRequest {
  //退款单号
  string refundsn = 1;
}
//退款推送参数
message OrderRetrunGetResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团订单号，
  OrderRetrunInfo data = 4;
}

//退款推送参数
message OrderRetrunGetListRequest {
  //订单ID 15开头的，
  string order_id = 1;
}
//退款推送参数
message OrderRetrunGetListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团订单号，
  repeated OrderRetrunInfo data = 4;
}

//退款推送参数
message OrderRetrunInfo {
  //退款单号
  string refundsn = 1;
  //创建时间
  string createtime = 2;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功
  int32 refund_state = 3;
  //退款金额
  string refundamount = 4;
  //退款原因
  string refundreason = 5;
  //退款商品集合
  repeated RefundGoodsOrder refundGoodsOrders = 6;
  //退款日志
  repeated OrderRetrunLog loglist = 7;
  //支付金额
  float payamount = 8;
  //原订单编号
  string old_order_sn = 9;
  //配送费
  double freight = 10;
  //商品总额
  double goods_total = 11;
  //订单编号
  string order_sn = 12;
  //申请类型:1为退款,2为退货，默认为1
  int32 refundtype = 13;
  //退货快递单号
  string expressnum = 14;
  //门店ID，财务编码
  string shop_id = 15;
  //退货快递名称
  string expressname = 16;
  //申请人电话
  string apply_phone = 17;
  //申请人
  string apply_user = 18;
  // 积分
  int64 refund_integral = 19;
}
//退款日志
message OrderRetrunLog {
  //日志时间
  string ctime = 1;
  //日志备注
  string reason = 2;
  //凭证
  string pictures = 3;
  //退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  string res_type = 4;
  //操作類型
  string operation_type = 5;
  //操作人
  string operationer = 6;
  //通知类型，part：发起部分退款；agree：确认退款；reject：驳回退款；cancelRefund：用户取消退款申请；cancelRefundComplaint：用户取消退款申诉；pushThird：退单单推送第三方；
  string notify_type = 7;

}

//退款推送参数
message OrderRetrunRequest {
  //美团订单号
  string order_id = 1;
  //退款单号
  string refund_id = 2;
  //创建时间
  int32 ctime = 3;
  //申请退款的原因
  string reason = 4;
  //本次退款的合计金额，单位是元。
  string money = 5;
  //用户申请退款时上传的退款图片，多个图片url以英文逗号隔开，上限为9张图片。字段信息为json格式数组。
  string pictures = 6;
  //通知类型，apply-发起退款；agree-确认退款；reject-驳回退款；cancelRefund-用户取消退款申请；cancelRefundComplaint-用户取消退款申诉
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string notify_type = 7;
  //申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉；40-退款成功；41：退款失败
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string res_type = 8;
  //售后单 商品集合
  repeated RefundGoodsOrder refundGoodsOrders = 9;
  //推送当前售后单的状态类型，仅适用支持退货退款业务的商家：
  //1-已申请
  //10-初审已同意
  //11-初审已驳回
  //16-初审已申诉
  //17-初审申诉已同意
  //18-初审申诉已驳回
  //20-终审已发起（用户已发货）
  //21-终审已同意
  //22-终审已驳回
  //26-终审已申诉
  //27-终审申诉已同意
  //28-终审申诉已驳回
  //30-已取消
  //40-退款成功
  //41：退款失败
  string status = 10;
  //推送当前仅退款或退货退款流程的发起方，是用户还是商家；仅适用于支持退货退款的商家。
  //1-用户
  //2-商家
  //3-客服
  //4-BD
  //5-系统
  //6-开放平台
  string apply_op_user_type = 11;
  //物流信息，JSon格式
  string logistics_info = 12;
  //渠道id(1-阿闻，2-美团，3-饿了么)
  int32 order_from = 13;
  //部分退还是整单退，1整单 2部分
  int32 refun_type = 14;
  //操作类型
  string operation_type = 15;
  //操作人
  string operationer = 16;
  //申请类型
  string apply_type = 17;
  //平台承担的优惠金额
  double activity_pt_amount = 18;
  //未开通的场景： 0-退款流程或申诉流程
  //已开通退货退款场景：,1-仅退款流程,2-退款退货流程
  string  service_type =19;
  // 审核退款的原因
  string res_reason = 20;
}

message MtOrderRefundRequest {
  //美团订单号，当商家收到用户的退款申请，如同意/拒绝退款，商家可调用此接口操作确认此订单的退款申请。
  string order_id = 1;
  //确认/拒绝退款的原因
  string reason = 2;
  //商品集合 拒绝的时候不用传，同意的时候需要
  repeated RefundGoodsOrder refundGoodsOrders = 3;
  //操作人
  string operationer = 4;
  //退款单号
  string refundsn = 5;
  //外部订单号
  string external_order_id = 6;
  // 店铺主体Id
  int32 store_master_id = 7;

  // 一、“退货退款”驳回退货的原因（初审）
  // 1-已和用户沟通一致不退货
  // 2-商品发出时完好
  // 3-商品没有问题，买家未举证
  // 4-商品没有问题，买家举证无效
  // 5-商品已经影响二次销售
  // 6-申请时间已经超过售后服务时限
  // 7-不支持买家主观原因的退换货

  // 二、“退货退款”驳回退款的原因（终审）
  // 8-已和用户沟通一致不退款
  // 9-收到货物有破损
  // 10-未收到货物 11-买家未按要求寄出货物

  // 三、“仅退款”驳回退款的原因
  // 12-已和用户电话沟通
  // 13-商品已开始制作
  // 14-商品已经打包完成
  // 15-商品正在配送中
  // 16-商品无质量问题
  // 17-商品没有缺货少货问题
  // 18-商品打包完好

  // 四、驳回原因编码通用：
  // -1-其他 上传的原因编码如有以下任意情况，均需返回错误信息： -不在枚举值范围内。-与当前审核阶段不符。 -与退款服务类型（“仅退款”或“退货退款”）不符。
  int32 reject_reason_code = 8;
}

message ExternalResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  string data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;
}

service Gyservice {
  // 21订单新增（gy.erp.trade.add）
  rpc TradeAdd (PaymentParamRequest) returns (PaymentResultResponse) {
    option (google.api.http) = {
      post:"/jackpet/Gyservice/TradeAdd"
      body:"*"
    };
  }
  // 退货单新增(gy.erp.trade.return.add)
  rpc ReturnAdd (ReturnOrderAddParamRequest) returns (ReturnOrderAddResultResponse) {
    option (google.api.http) = {
      post:"/jackpet/Gyservice/ReturnAdd"
      body:"*"
    };
  }
  // 退款单新增（gy.erp.trade.refund.add）
  rpc RefundAdd (ReturnOrderRefundAddParamRequest) returns (ReturnOrderRefundAddResultResponse) {
    option (google.api.http) = {
      post:"/jackpet/Gyservice/RefundAdd"
      body:"*"
    };
  }
}
// 21订单新增（gy.erp.trade.add）
message PaymentParamRequest {
  //店铺代码
  string Shop_code = 1;
  //会员代码
  string Vip_code = 2;
  //平台单号
  string Platform_code = 3;
  //平台单号允许重复
  bool Repeat_allowed = 4;
  //仓库代码
  string Warehouse_code = 5;
  //业务员
  string Business_man_code = 6;
  //物流公司
  string Express_code = 7;
  //物流费用
  double Post_fee = 8;
  //币别代码
  string Currency_code = 9;
  //卖家备注
  string Seller_memo = 10;
  //是否货到付款
  bool Cod = 11;
  //拍单时间
  string Deal_datetime = 12;
  //订单类型
  string Order_type_code = 13;
  //预计发货时间
  string Plan_delivery_date = 14;
  //买家到付服务费
  double Cod_fee = 15; // float
  //其他服务费
  double Other_service_fee = 16; //float64
  //买家留言
  string Buyer_memo = 17;
  //二次备注
  string Seller_memo_late = 18;
  //附加信息
  string Extend_memo = 19;
  //是否淘宝家装订单
  bool Jz = 20;
  //是否手工指定批次
  bool Manual_specify_batch_no = 21;
  //是否强制指定为分销商订单
  bool Distribution_order = 22;
  //收货人
  string Receiver_name = 23;
  //固定电话
  string Receiver_phone = 24;
  //手机号码
  string Receiver_mobile = 25;
  //邮政编码
  string Receiver_zip = 26;
  //省名称
  string Receiver_province = 27;
  //市名称
  string Receiver_city = 28;
  //区名称
  string Receiver_district = 29;
  //收货地址
  string Receiver_address = 30;
  //真实姓名
  string VipRealName = 31;
  //身份证号
  string VipIdCard = 32;
  //电子邮箱
  string VipEmail = 33;
  //订单标记
  string Tag_code = 34;
  //商品明细
  repeated Item Details = 35;
  //支付明细
  repeated Payment Payments = 36;
  //发票信息
  repeated Invoice Invoices = 37;

}
// 21订单新增（gy.erp.trade.add）
message Item {
  //商品代码
  string Item_code = 1;
  //规格代码
  string Sku_code = 2;
  //标准单价
  string Origin_price = 3;
  //实际单价
  string Price = 4;
  //商品数量
  int32 Qty = 5;
  //退款状态
  int32 Refund = 6;
  //赠品
  bool Is_gift = 7;
  //备注
  string Note = 8;
  //子订单ID
  string Oid = 9;
  //预计发货日期
  string Plan_delivery_date = 10;
  //是否为预售
  bool Presale = 11;
}
// 21订单新增（gy.erp.trade.add）
message Payment {
  //支付方式
  string Pay_type_code = 1;
  //支付金额
  double Payment = 2; // float64
  //支付时间
  string Paytime = 3;
  //交易号
  string Pay_code = 4;
  //账号
  string Account = 5;

}
// 21订单新增（gy.erp.trade.add）
message Invoice {
  //发票种类
  int64 Invoice_type = 1;
  //发票抬头类型
  int32 Invoice_title_type = 2;
  //发票类型
  int32 Invoice_type_name = 3;
  //发票抬头
  string Invoice_title = 4;
  //发票内容
  string Invoice_content = 5;
  //纳税人识别号
  string Invoice_tex_payer_number = 6;
  //开户行
  string Invoice_bank_name = 7;
  //账号
  string Invoice_bank_account = 8;
  //地址
  string Invoice_address = 9;
  //电话
  string Invoice_phone = 10;
  //发票金额
  double Invoice_amount = 11; // float64


}
// 21订单新增（gy.erp.trade.add）
message PaymentResultResponse {
  //成功与否
  string Success = 1;
  //错误代码
  string ErrorCode = 2;
  //子错误代码
  string SubErrorCode = 3;
  //错误描述
  string ErrorDesc = 4;
  //子错误描述
  string SubErrorDesc = 5;
  //请求方法
  string RequestMethod = 6;
  //订单ID
  string Id = 7;
  //订单单据编号
  string Code = 8;
  //订单创建时间
  string Created = 9;

}

//退货单新增（gy.erp.trade.return.add）
message ReturnOrderAddParamRequest {
  //SignaureParam
  //店铺代码
  string Shop_code = 1;
  //会员代码
  string Vip_code = 2;
  //销售订单单据编号
  string Trade_code = 3;
  //销售订单平台单号
  string Trade_platform_code = 4;
  //业务员代码
  string Businessman_code = 5;
  //售后阶段
  int32 Refund_phase = 6;
  //售后类型
  int32 Return_type = 7;
  //退货原因代码
  string Type_code = 8;
  //退回仓库代码
  string Warehousein_code = 9;
  //退回快递名称
  string Express_name = 10;
  //退回运单号
  string Express_num = 11;

  //收货人
  string Receiver_name = 12;
  //收货人电话
  string Receiver_phone = 13;
  //收货人手机
  string Receiver_mobile = 14;
  //收货人邮编
  string Receiver_zip = 15;
  //收货人省信息
  string Receiver_province = 16;
  //收货人市信息
  string Receiver_city = 17;
  //收货人区信息
  string Receiver_district = 18;
  //收货人地址
  string Receiver_address = 19;
  //备注
  string Note = 20;
  //退入商品明细
  repeated Item_Deail Item_detail = 21;
  //退款明细
  repeated Refund Refund_detail = 22;
}

//退货单新增（gy.erp.trade.return.add）
message Item_Deail {
  //商品条码
  string Barcode = 1;
  //商品代码
  string Item_code = 2;
  //规格代码
  string Sku_code = 3;
  //数量
  int32 Qty = 4;
  //标准单价
  double OriginPrice = 5;
  //实际单价
  double Price = 6;
}

//退货单新增（gy.erp.trade.return.add）
message Refund {
  //支付方式代码
  string Pay_type_code = 1;
  //支付金额
  double Payment = 2;
  //支付时间
  string Pay_time = 3;
  //账号
  string Account = 4;

}

//退货单新增（gy.erp.trade.return.add）
message ReturnOrderAddResultResponse {
  //成功与否
  string Success = 1;
  //错误代码
  string ErrorCode = 2;
  //子错误代码
  string SubErrorCode = 3;
  //错误描述
  string ErrorDesc = 4;
  //子错误描述
  string SubErrorDesc = 5;
  //请求方法
  string RequestMethod = 6;
  // 添加的单号
  string Code = 7;
}

// 退款单新增（gy.erp.trade.refund.add）
message ReturnOrderRefundAddParamRequest {

  //退款单单号
  string Refund_code = 1;
  //退款单种类
  int32 Refund_type = 2;
  //退款原因
  string Refund_reason = 3;
  //关联订单单号
  string Trade_code = 4;
  //店铺代码
  string Shop_code = 5;
  //会员代码
  string Vip_code = 6;
  //单据类型代码
  string Type_code = 7;
  //退款支付方式代码
  string Payment_type_code = 8;
  //退款金额
  double Amount = 9;
  //备注
  string Note = 10;
  //退款商品列表
  repeated ReturnOrderRefundAddItem Item_detail = 11;
}
// 退款单新增（gy.erp.trade.refund.add）
message ReturnOrderRefundAddItem {
  //商品条码
  string Barcode = 1;
  //数量
  int32 Qty = 2;
  //单价
  double Price = 3;
  //备注
  string Note = 4;
}
// 退款单新增（gy.erp.trade.refund.add）
message ReturnOrderRefundAddResultResponse {
  //成功与否
  string Success = 1;
  //错误代码
  string ErrorCode = 2;
  //子错误代码
  string SubErrorCode = 3;
  //错误描述
  string ErrorDesc = 4;
  //子错误描述
  string SubErrorDesc = 5;
  //请求方法
  string RequestMethod = 6;
  // 添加的单号
  string Code = 7;
}

//提交订单(通用返回)
message SubmitOrderRequest {
  //商户id
  string shop_id = 1;
  //商户名称
  string shop_name = 2;
  //商户类型1：线上 2：线下
  int64 shop_type = 3;
  //用户id
  string member_id = 4;
  //用户名称
  string member_name = 5;
  //用户手机
  string member_tel = 6;
  //收件人
  string addressee = 7;
  //收件电话
  string addressee_tel = 8;
  //收件省市区
  string addressee_city = 9;
  //收件详细位置
  string address_details = 10;
  //优惠金额
  int64 discount_amount = 11;
  //买家备注
  string buyer_remark = 12;
  //配送方式1快递 2自提
  int64 delivery = 13;
  //订单类型：1、普通订单 2、多人拼团订单 3、分销供货订单 4、分销买家订单 5、酒店订单 6、积分兑换
  int64 order_type = 14;
  //运费
  int64 freight = 15;
  //来源 1宠医云 2ERP 3有赞 4阿闻商城 5阿闻宠物 6优宠商城 7极宠家
  int64 source = 16;
  //渠道1小程序 2网站 3门店
  int64 channel = 17;
  //是否分销1是 2否
  int64 distribution = 18;
  //分销员
  string distribution_member = 19;
  //活动类型0无 1限时折扣 2满级送 3拼团 4代金券 5秒杀 6平台红包/到店红包 7优惠套装 8预售 9积分换购
  int64 activity_type = 20;
  //商品信息
  repeated MinappProductInfo product_info = 21;
}

//订单商品列表
message MinappProductInfo {
  //商品ID
  string product_id = 1;
  //商品名称
  string product_name = 2;
  //spu
  string spu = 3;
  //sku
  string sku = 4;
  //规格
  string specs = 5;
  //优惠金额
  int64 discountamount = 6;
  //商品条码
  string bar_code = 7;
  //商品缩略图URL
  string main_img_url = 8;
  //单价
  int64 price = 9;
  //数量
  int64 number = 10;
  //1:虚拟订单 2：实物订单
  int64 product_attr = 11;
  //商品详情url
  string product_details_url = 12;
  //商品分类id
  string product_type = 13;
  //品牌id
  string brand_id = 14;
  //品牌名称
  string brand_name = 15;
  //品牌图片Url
  string brand_img_url = 16;
  //卖点
  string selling_point = 17;
  //划线价
  int64 marking_price = 18;
}

//提交订单商品列表
message SubmitOrderProduct {
  //商品id
  string productid = 1;
  //商品数量
  int64 productcount = 2;
}

message NewAddOrderRequest {
  Order order = 1;
  Common common = 2;
  repeated OrderGoods order_goods = 3;
}

message DemoOrder {
  Order order = 1;
  Common common = 2;
  repeated OrderGoods order_goods = 3;
}

message NewAddOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated DemoOrder demoorder = 4;
}

message Order {
  string order_sn = 1;
  string pay_sn = 2;
  int32 store_id = 3;
  string store_name = 4;
  string buyer_id = 5;
  string buyer_name = 6;
  string buyer_email = 7;
  string buyer_phone = 8;
  int32 add_time = 9;
  string payment_code = 10;
  int32 order_state = 11;
  int32 order_amount = 12;
  int32 shipping_fee = 13;
  int32 goods_amount = 14;
  int32 order_from = 15;
  int32 order_type = 16;
  int32 chain_id = 17;
  int32 rpt_amount = 18;
  int32 order_id = 19;
  int32 is_dis = 20;
}

message Common {
  int32 order_id = 1;
  int32 store_id = 2;
  string order_message = 3;
  int32 promotion_total = 4;
  ReciverInfo reciver_info = 5;
  string reciver_name = 6;
  int32 reciver_city_id = 7;
  string invoice_info = 8;
  string promotion_info = 9;
  string reciver_date_msg = 10;
  string reciver_info_serialize = 11;
}

message ReciverInfo {
  string phone = 1;
  string mob_phone = 2;
  string tel_phone = 3;
  string address = 4;
  string area = 5;
  string street = 6;
}

message OrderGoods {
  int32 order_id = 1;
  string goods_id = 2;
  int32 store_id = 3;
  string goods_name = 4;
  int32 goods_price = 5;
  int32 goods_num = 6;
  string goods_image = 7;
  string goods_spec = 8;
  string buyer_id = 9;
  string goods_commonid = 10;
  int32 add_time = 11;
  int32 goods_type = 12;
  int32 chain_id = 13;
  int32 promotions_id = 14;
  int32 commis_rate = 15;
  string gc_id = 16;
  string goods_contractid = 17;
  int32 goods_pay_price = 18;
  int32 is_dis = 19;
  int32 dis_member_id = 20;
  int32 dis_commis_rate = 21;
  string sku = 22;
  string rec_id = 23;
  int32 source = 24;
  string oc_id = 25;
  string out_member_id = 26;
}

//订单请求
message QueryOrderRequest {
  string order_sn = 1;
}

//订单查询返回
message QueryOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  OrderModel OrderModel = 4;
}

//判断第三方订单是否存在请求
message OrderIsExistRequest {
  //订单号/第三方订单号
  string order_id = 1;
}

//判断第三方订单是否存在返回
message OrderIsExistResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //第三方订单号是否存在
  bool is_exist = 4;
}

message OrderModel {
  //订单id
  string order_id = 1;
  //原电商订单号
  string old_order_sn = 2;
  //电商主订单号
  string order_sn = 3;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;
  int32 order_status = 4;
  //商户名称
  string shop_name = 5;
  //会员id
  string member_id = 6;
  //会员名称
  string member_name = 7;
  //会员手机号
  string member_tel = 8;
  //收件人
  string receiver_name = 9;
  //收件省
  string receiver_state = 10;
  //收件市
  string receiver_city = 11;
  //收件区
  string receiver_district = 12;
  //收件地址
  string receiver_address = 13;
  //收件电话
  string receiver_phone = 14;
  //总优惠金额
  int32 privilege = 15;
  //Cod=货到付款, NoCod=非货到付款
  string pay_type = 16;
  //收件手机
  string receiver_mobile = 17;
  //NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string gjp_status = 18;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 19;
  //商品总金额（未加运费）
  int32 goods_total = 20;
  //是否支付0否  1是
  int32 is_pay = 21;
  //创建时间
  string create_time = 22;
  //支付时间
  string pay_time = 23;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 24;
  //总运费
  int32 freight = 25;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 source = 26;
  //发票信息
  string invoice = 27;
  //商品
  repeated OrderProductModel OrderProductModel = 28;
  //alipay支付宝  wxpay微信 wxpay_jsapi 微信支付JSAPI alipay_native 支付宝移动支付
  string pay_mode = 29;
  //1支付宝 2微信
  string pay_mode_int = 30;
}

message OrderProductModel {
  string id = 1;
  //订单id
  string order_id = 2;
  //主订单号
  string order_sn = 3;
  //sku
  string sku = 4;
  //组合商品父级sku
  string parent_sku_id = 5;
  //商品id
  string product_id = 6;
  //商品名称
  string product_name = 7;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 8;
  //组合商品组合类型（组合商品组合类型: 0-非组合 1-实物实物 2-实物虚拟 3-虚拟虚拟）
  int32 combine_type = 9;
  //商品编码
  string bar_code = 10;
  //单价
  int32 price = 11;
  //数量
  int32 number = 12;
  //规格
  string specs = 13;
  //总金额(实际支付金额)
  int32 payment_total = 14;
  //商家优惠金额
  int32 privilege = 15;
  //邮费
  int32 freight = 16;
  //原价
  int32 marking_price = 17;
  //商品图片
  string image = 18;
  //发货数量
  int32 deliver_num = 19;
  //退货数量
  int32 refund_num = 20;
  //平台优惠金额
  int32 privilege_pt = 21;
  //总优惠金额
  int32 privilege_total = 22;
  //实际支付单价（美团）
  int32 pay_price = 23;
  //子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
  string sub_biz_order_id = 24;
  //参与限时折扣的活动id
  int32 promotion_id = 25;
  //货号
  string article_number = 26;
  //商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,8:第二件N折,9:拼团促销)
  int32 promotion_type = 27;
  //sku商品实际支付总金额
  int32 sku_pay_total = 28;
  //虚拟商品已经核销过的数量
  int32 used_num = 29;
  //只有虚拟商品才有值1有效期至多少2有效期天数
  int32 term_type = 30;
  //如果term_type=1存时间戳如果term_type=2存多少天
  int32 term_value = 31;
  //商城订单商品主键id
  int64 mall_order_product_id = 32;
  //是否支持过期退款 1：是  0：否
  int32 virtual_invalid_refund = 33;
  //是否是第三方商品信息 1：是  0：否 默认0
  int32 is_third_product = 34;
  // 药品仓标识（1：巨星药品仓, 0：否）
  int32 warehouse_type = 35;
  //组合子商品在单个组合中的售卖数量
  int32 group_item_num = 36;
  // 是否使用了虚拟库存
  int32 use_virtual_stock = 37;
  //商品分类名称
  string channel_category_name = 38;
  // 是否处方药
  int32 is_prescribed_drug = 39;
  //vip单价
  int32 vip_price = 40;
  // 饿了么多规格sku_id
  string sku_spec_id = 41;
  // 商品库位码
  string location_code = 42;
}

message ReleaseStockRequest {
  string order_id = 1;
  repeated ReleasePackage detail = 2;
  message ReleasePackage {
    string sku = 1;
    int32 num = 2;
  }
}

//管易订单查询接口
message GyOrderQueryRequest {
  int32 limit = 1;
  string datetime = 2;
}

//管易订单查询接口
message GyOrderQueryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated GyOrderModel details = 4;
}

message GyOrderModel {
  //主订单号
  string order_sn = 1;
  //商户名称
  string shop_name = 2;
  //会员名称
  string member_name = 3;
  //收件人
  string receiver_name = 4;
  //收件省
  string receiver_state = 5;
  //收件市
  string receiver_city = 6;
  //收件区
  string receiver_district = 7;
  //收件地址
  string receiver_address = 8;
  //收件电话
  string receiver_phone = 9;
  //总优惠金额
  int32 privilege = 10;
  //Cod=货到付款, NoCod=非货到付款
  string pay_type = 11;
  //alipay支付宝  wxpay微信 wxpay_jsapi 微信支付JSAPI alipay_native 支付宝移动支付
  string pay_mode = 12;
  //1支付宝  2微信
  int32 pay_mode_int = 13;
  //收件手机
  string receiver_mobile = 14;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 15;
  //商品总金额（未加运费）
  int32 goods_total = 16;
  //创建时间
  string create_time = 17;
  //支付时间
  string pay_time = 18;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 19;
  //总运费
  int32 freight = 20;
  //仓库代码
  string warehouse_code = 21;
  //买家留言
  string buyer_memo = 22;
  //卖家留言
  string seller_memo = 23;
  //商品明细
  repeated GyOrderProduct order_product = 24;
  //支付明细
  repeated GyPayFlow pay_flow = 25;
}

message GyOrderProduct {
  //商品明细的主键id
  string id = 1;
  //主订单号
  string order_sn = 2;
  //第三方spu
  string third_spu_id = 3;
  //第三方sku
  string third_sku_id = 4;
  //商品名称
  string product_name = 5;
  //单价
  int32 price = 6;
  //数量
  int32 number = 7;
  //规格
  string specs = 8;
  //总金额
  int32 payment_total = 9;
  //优惠金额
  int32 privilege = 10;
  //邮费
  int32 freight = 11;
  //是否是赠品
  int32 is_gift = 12;
}

message GyPayFlow {
  //支付单号
  string id = 1;
  //支付状态1成功 2失败
  int32 pay_status = 2;
  //支付方式1微信 2支付宝 3网银 4其他
  int32 pay_method = 3;
  //支付金额
  int32 pay_amount = 4;
  //创建时间
  string create_time = 5;
  //支付时间
  string pay_time = 6;
}

//管易订单物流信息下发
message GySyncOrderExpressRequest {
  repeated GySyncOrderExpress GySyncOrderExpress = 1;
}

message GySyncOrderExpress {
  //快递公司代码
  string express_code = 1;
  //快递公司名称
  string express_name = 2;
  //快递单号
  string express_no = 3;
  //订单id
  string order_id = 4;
  //子单
  string sub_order_id = 5;
  //快递商品明细
  repeated OrderExpressProduct order_products = 6;
}

message OrderExpressProduct {
  //商品名称
  string product_name = 1;
  //规格名称
  string spec_name = 2;
  //第三方spu
  string third_spu_id = 3;
  //第三方sku
  string third_sku_id = 4;
  //商品数量
  int32 count = 5;
  //商品id
  string product_id = 6;
}

//管易查询退款订单请求
message GyQueryRefundOrderRequest {
  int32 limit = 1;
  string datetime = 2;
}

//管易查询退款订单
message GyQueryRefundOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated GyRefundOrder details = 4;
}
//管易退款退货单
message GyRefundOrder {
  //商品明细的主键id -- 目前只支持订单明细的某个SKU
  string id = 1;
  //退款单号
  string refundsn = 2;
  //原销售订单号
  string ordersn = 3;
  //创建时间
  string createtime = 4;
  //售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货
  string refundtypesn = 5;
  //申请类型:1为退款,2为退货，默认为1
  int32 refundtype = 6;
  //优惠金额
  string discountamount = 7;
  //运费
  string postfee = 8;
  //退款原因
  string refundreason = 9;
  //对应仓库id
  string warehouseincode = 10;
  //退货快递名称
  string expressname = 11;
  //退货快递单号
  string expressnum = 12;
  //退款金额
  string refundamount = 13;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 ordersource = 14;
  //订单退款状态 0:未退款 1:退款完成 2:退款中
  int32 refund_state = 15;
  //商品明细
  repeated GyRefundOrderGoods order_product = 16;
}
//管易退款退货商品
message GyRefundOrderGoods {
  //退款主表Id
  string refundorderid = 1;
  //商品id
  string goodsid = 2;
  //商品数量
  int32 quantity = 3;
  //退款金额
  string refundamount = 4;
  //管易专用 商品代码
  string itemcode = 5;
  //管易专用 带规格的商品此字段必填
  string skucode = 6;
  //管易专用 商品条码
  string barcode = 7;
}

// 预订单查询请求参数
message BookingOrderRequest {
  int32 pageindex = 1;
  int32 pagesize = 2;
  // 订单来源 1阿闻到家  2美团  3饿了么
  int32 channel_id = 3;
  // 临近配送时间
  int32 delivery_time = 4;
  // 商户或门店ID
  repeated string shopids = 5;
  // 销售渠道
  int32 sale_source = 6;
}

//预订单查询响应参数
message BookingOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated SimpleCombineOrder details = 4;
  // 总数
  int32 total_count = 5;

}

// 预订单打印请求参数
message PrintBookingOrderRequest {
  // 订单ID
  string order_id = 1;
  // 1拣货员  2商家  3顾客
  int32 type = 2;
}

//预订单打印响应参数
message PrintBookingOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  PrintBookingOrder detail = 4;
}

// 预订单打印信息
message PrintBookingOrder {
  // 订单号
  string order_id = 1;
  // 商铺或门店名称
  string shop_name = 2;
  // 下单时间
  string create_time = 3;
  // 客户姓名
  string name = 4;
  // 客户电话
  string mobile = 5;
  // 客户备注
  string remark = 6;
  // 商品总数
  int32 goods_number = 7;
  // 支付金额
  int32 amount = 8;
  // 原价(商品总额)
  int32 goods_amount = 9;
  // 包装费
  int32 pack_amount = 10;
  // 配送费
  int32 delivery_amount = 11;
  // 美团减免金额
  string mt_reduction_amount = 12;
  // 优惠金额
  int32 reduction_amount = 13;
  // 门店立减
  string shop_reduction_amount = 14;
  // 是否自取 0不自取  1自取
  int32 is_fetch = 15;
  // 自取时间
  string fetch_time = 16;
  // 订单号码
  int32 number = 17;
  //取货码
  string pickup_code = 18;
  //来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 19;
  // 商品信息
  repeated PrintOrderGoods goods = 20;
  //打印长度mm
  int32 print_extent = 21;
  // 配送方式
  int32 delivery_type = 22;
  //收件地址
  string receiver_address = 23;
  // 优惠详情 (商家优惠 + 平台优惠)
  repeated OrderPrivilegeModel order_privilege_model = 24;
  // 开发票base64小程序码
  string invoice_qr_code = 25;
}

// 预订单打印商品信息
message PrintOrderGoods {
  // 商品名
  string goods_name = 1;
  // 数量
  int32 count = 2;
  // 金额
  int32 amount = 3;
  // 货架码
  string shelf_code = 4;
  // sku
  string sku = 5;
  // upc 商品条形码
  string upc = 6;
  // 组合商品父级skuid
  string parent_sku_id = 7;
  //子商品信息
  repeated PrintOrderChildGoods child_goods = 8;
}

//子商品信息
message PrintOrderChildGoods {
  // 商品名
  string goods_name = 1;
  // 数量
  int32 count = 2;
  // 金额
  int32 amount = 3;
  // 货架码
  string shelf_code = 4;
  // sku
  string sku = 5;
  // upc 商品条形码
  string upc = 6;
  // 组合商品父级skuid
  string parent_sku_id = 7;
}

// 阿闻订单列表导出请求参数
message AwenOrderExportRequest {
  //订单搜索类型
  int32 searchtype = 1;
  //订单搜索关键字
  string keyword = 2;
  //下单开始时间
  string starttime = 3;
  //下单结束时间
  string endtime = 4;
  //商品名称
  string productname = 5;
  //订单来源渠道 1阿闻到家  2美团  3饿了么
  int32 channelid = 6;
  //订单状态
  int32 orderstate = 7;
  //销售渠道
  int32 salechannel = 8;
  //订单类型(以逗号分隔)
  string ordertype = 9;
  //配送方式
  int32 deliverytype = 10;
  //支付方式
  int32 paymodeint = 11;
  //支付单号
  string paysn = 12;
  // 门店ID
  repeated string shopids = 13;
  //当前页码
  int32 pageindex = 14;
  //每页行数
  int32 pagesize = 15;
  //用户编号
  string user_no = 16;
  //时间类型，0下单时间，1完成时间
  int32 time_type = 17;
}

// 阿闻订单列表导出响应参数
message AwenOrdeExportrResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // 阿闻订单列表导出数据
  repeated AwenOrderExport details = 4;
}

// 阿闻订单列表导出数据
message AwenOrderExport {
  // 订单ID
  string OrderId = 1;
  // 订单号
  string OrderSn = 2;
  // 父订单号
  string OldOrderSn = 3;
  // 外部订单号
  string GyOrderSn = 4;
  // 下单时间
  string CreateTime = 5;
  // 支付流水号
  string PaySn = 6;
  // 支付时间
  string PayTime = 7;
  // 订单金额
  int32 Total = 8;
  // 优惠金额
  int32 Privilege = 9;
  // 支付方式 1支付宝 2微信 3美团支付
  int32 PayMode = 10;
  // 店铺名称
  string ShopName = 11;
  // 订单来源
  int32 OrderFrom = 12;
  // 销售渠道
  int32 SaleSource = 13;
  // 订单类型
  int32 OrderType = 14;
  // 收货人姓名
  string ReceiverName = 15;
  // 收货人联系方式
  string ReceiverMobile = 16;
  // 配送方式
  int32 DeliveryType = 17;
  // 订单状态
  int32 OrderStatusChild = 18;
  // 本地服务订单状态
  int32 LocalOrderStatus = 19;
  // 商城服务订单状态
  int32 ShopOrderStatus = 20;
  // 商品名称
  string ProductName = 21;
  // 品牌名称
  string BrandName = 22;
  // 退款状态
  int32 RefundState = 23;
  // 活动类型
  string ActivityType = 24;
  // 优惠信息
  string Extras = 25;
  // 来源渠道id  1阿闻到家  2美团  3饿了么
  int32 ChannelId = 26;
  // 销售渠道
  int32 UserAgent = 27;
  // 订单原价
  int32 OriginalPrice = 28;
  //平台服务费
  int32 service_charge = 29;
  // 实收金额
  int32 actual_receive_total = 30;
  //总运费
  int32 freight = 31;
  //店铺ID
  string shopId = 32;
  //业绩所属员工
  string performance_staff = 33;
  // 仓库所属1:(a8 or 全渠道)  2:管易  3:门店（子龙）
  int32 Source = 34;
  // 仓库代码
  string WarehouseCode = 35;
  //仓库名称
  string WarehouseName = 36;
  //业绩分配人
  string performance_operator_id = 37;
  //业绩分配人
  string performance_operator_name = 38;
  //业绩分配时间
  string performance_time = 39;
  //订单状态
  int32 order_status = 40;
  string receiver_address = 41;
  string buyer_memo = 42;
  //订单摊走的平台补贴
  string privilege_pt = 43;
}

//异常订单模块
service OrderExceptionService {
  //订单状态同步 emall.orderstatus.synchroni
  rpc OrderStatusException (ExceptionOrderStatusRequest) returns (ExceptionOrderStatusResponse) {
  };
  //修改订单是否显示
  rpc OrderIsShowException (ExceptionOrderStatusRequest) returns (ExceptionOrderStatusResponse) {
  };
  //异常订单添加
  rpc OrderExceptionAdd (OrderExceptionRequest) returns (OrderExceptionResponse) {
  };
  //异常订单查询
  rpc OrderExceptionList (OrderExceptionListRequest) returns (OrderExceptionListResponse) {
  };
  //取消配送
  rpc OrderCancel (ExceptionOrderStatusRequest) returns (ExceptionOrderStatusResponse) {
  };
  //再次发起配送
  rpc DistributionAgain (ExceptionOrderStatusRequest) returns (ExceptionOrderStatusResponse) {
  };
  //已送达
  rpc GoodArrive (ExceptionOrderStatusRequest) returns (ExceptionOrderStatusResponse) {
  };
  //发起自配
  rpc OrderOwnDeliver (OrderExceptionRequest) returns (OrderExceptionResponse) {
  };
}

message OrderExceptionListRequest {
  //每页大小
  int32 page_size = 1;
  //当前页
  int32 page = 2;
  //订单ID
  string order_id = 3;
  //备注
  string remarks = 4;
  //登录用户所有权限的门店id
  repeated string shopids = 5;
  //美团订单号
  string mt_order_sn = 6;
  //渠道
  int32 channel_id = 7;
  // 查看指定店铺的配送异常数据（用于连锁下面， 在界面的搜索条件里选择了一个店铺）
  string financial_code = 8;
}
message OrderExceptionListResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //总条数
  int32 total_count = 4;
  //异常列表
  repeated OrderExceptionRequest list = 5;
}

//异常订单信息
message OrderExceptionRequest {
  //配送活动标识
  string delivery_id = 1;
  //美团配送内部订单id
  string mt_peisong_id = 2;
  //订单id
  string order_id = 3;
  //异常ID，用来唯一标识一个订单异常信息
  string exception_id = 4;
  //订单异常代码
  int32 exception_code = 5;
  //备注
  string exception_descr = 6;
  //配送员上报订单异常的时间
  string exception_time = 7;
  //报订单异常的配送员电话
  string courier_name = 8;
  //报订单异常的配送员电话
  string courier_phone = 9;
  //数据怎么来的，1商家，2配送平台，3骑手上报
  int32 source = 10;
  //订单状态 1:未取消 2:未配送，3未送达,4.已送达
  int32 order_status = 11;
  //订单表主键
  string oid = 12;
  //原电商父订单号
  string old_order_sn = 13;
  //订单创建时间
  string order_time = 14;
  //配送方式
  string distribution_mode = 15;
  //来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 16;
  // 店铺主体Id
  int32 store_master_id = 17;
  // 是否隐藏
  bool is_hide = 18;
}

message OrderExceptionResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

//异常配送的请求
message ExceptionOrderStatusRequest {
  //订单id
  string order_id = 1;
  //状态代码
  int32 status = 2;
  //配送ID主键
  string delivery_id = 3;
  string shopId = 4;
  //美团配送内部订单id
  string mt_peisong_id = 5;
  //取消类型
  string cancel_type = 6;
  //取消原因
  string cancel_reason = 7;
  //自配送骑手名称
  string courier_name = 8;
  //配送员骑手电话
  string courier_phone = 9;
  //默认美配 0美配 1闪送
  int32 delivery_type = 10;
  // 店铺主体Id
  int32 store_master_id = 11;
}

// 订单列表-发起配送
message ReDeliveryRequest {
  // 订单编码
  string order_sn = 1;
}

//异常配送的返回
message ExceptionOrderStatusResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

//阿闻管家订单中心列表请求
message AwenOrderListRequest {
  //订单搜索类型
  int32 searchtype = 1;
  //订单搜索关键字
  string keyword = 2;
  //下单开始时间
  string starttime = 3;
  //下单结束时间
  string endtime = 4;
  //商品名称
  string productname = 5;
  //订单来源渠道 1阿闻到家  2美团  3饿了么
  int32 channel_id = 6;
  //订单状态1待付款2待发货3待收货4已完成5已退款6已取消
  int32 orderstate = 7;
  //销售渠道
  int32 salechannel = 8;
  //订单类型(以逗号隔开)
  string ordertype = 9;
  //配送方式1快递2外卖3自提4同城送
  int32 deliverytype = 10;
  //支付方式
  int32 paymodeint = 11;
  //支付单号
  string paysn = 12;
  //登录用户所有权限的门店id
  repeated string shopids = 13;
  //当前页码
  int32 pageindex = 14;
  //每页行数
  int32 pagesize = 15;
  //用户编号
  string user_no = 16;
  //时间类型，0下单时间，1完成时间
  int32 time_type = 17;
}

//阿闻管家订单中心列表响应
message AwenOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated SimpleCombineOrder details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//订单详情之订单基础信息请求
message AwenOrderBaseDetailRequest {
  //订单id
  string orderid = 1;
}

//订单详情之订单基础信息响应
message AwenOrderBaseDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  CombineOrderDetail orderdetail = 4;
}

//订单详情之物流信息请求
message AwenOrderDeliveryDetailRequest {
  //订单id
  string orderid = 1;
}

//订单详情之物流信息响应
message AwenOrderDeliveryDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //配送流程节点
  repeated OrderDeliveryNodeModel deliverynodes = 4;
}

//订单详情之支付信息请求
message AwenOrderPayDetailRequest {
  //订单id
  string orderid = 1;
}

//订单详情之支付信息响应
message AwenOrderPayDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  OrderPayDetail paydetail = 4;
}

//订单详情之发货状态信息请求
message AwenOrderDeliveryStateRequest {
  //订单id
  string orderid = 1;
}

//订单详情之发货状态信息响应
message AwenOrderDeliveryStateResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  DeliveryState deliverystate = 4;
}

//订单详情之店铺信息请求
message AwenOrderStoreDetailRequest {
  //订单id
  string orderid = 1;
}

//订单详情之店铺信息响应
message AwenOrderStoreDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  OrderModel ordermodel = 4;
}

//订单详情之买家信息请求
message AwenOrderBuyerDetailRequest {
  //订单id
  string orderid = 1;
}

//订单详情之买家信息响应
message AwenOrderBuyerDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  OrderModel ordermodel = 4;
}

//订单列表简化版订单实体
message SimpleCombineOrder {
  //订单id
  string order_id = 1;
  //父订单号
  string old_order_sn = 2;
  //订单号
  string order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 5;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 6;
  //商户或门店id
  string shop_id = 7;
  //商户名称
  string shop_name = 8;
  //收件人
  string receiver_name = 9;
  //收件电话
  string receiver_phone = 10;
  //收件手机
  string receiver_mobile = 11;
  //支付方式1支付宝2微信3美团4其他 5饿了么
  int32 pay_mode = 12;
  //NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string gjp_status = 13;
  //商品总金额（不包含运费不包含优惠不包含平台费用等）
  int32 goods_total = 14;
  //实际支付金额（商品总金额加上运费减优惠金额减平台费用等）
  int32 total = 15;
  //创建时间
  string create_time = 16;
  //配送方式1快递2外卖3自提4同城送
  int32 delivery_type = 17;
  // 预计送达时间
  string expected_time = 18;
  //外部订单
  string gy_order_sn = 19;
  //订单来源 1 WEB 2 mobile 3 宠医云 4 ERP 5 智慧门店 6 有赞 7 阿闻宠物 8 阿闻商城 9 美团
  int32 order_from = 20;
  //接单时间
  string accept_time = 21;
  //备货状态
  int32 is_picking = 22;
  //备货时间
  string picking_time = 23;
  //配送时间
  string deliver_time = 24;
  //订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 25;
  //是否推送子龙或全渠道成功1是0否
  int32 push_third_order = 26;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 27;
  //总优惠金额
  int32 privilege = 28;
  //备货时长(美团拣货时间-下单时间)
  string stock_time = 29;
  // 是否推送美团配送1是 0否
  int32 push_delivery = 30;
  // 推送美团配送失败原因
  string push_delivery_reason = 31;
  //平台服务费
  int32 service_charge = 32;
  // 实收金额
  int32 actual_receive_total = 33;
  // 优惠信息
  string extras = 34;
  //商品
  repeated OrderProductModel orderproductmodel = 35;
  //总运费
  int32 freight = 36;
  //美团送达或电商确认收货时间(已完成)
  string confirm_time = 37;
  // 来源渠道 1阿闻到家  2美团  3饿了么
  int32 channel_id = 38;
  // 销售渠道 1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏
  int32 user_agent = 39;
  // 订单支付时间
  string pay_time = 40;

  //是否显示 部分退款按钮 1：显示 0不显示
  int32 is_part_button = 41;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：elm9 饿了么快速，elm11 饿了么众包
  string logistics_code = 42;
  //业绩归属员工
  string performance_staff = 43;
  //仓库所属1:(a8 or 全渠道) 2:管易 3:门店（子龙）
  int32 source = 44;
  //仓库代码
  string warehouse_code = 45;
  //仓库名称
  string warehouse_name = 46;
  //流水号
  string pickup_code = 47;
  //是否订单调整1：是 0否
  int32 is_adjust = 48;
  //业绩分配时间
  string performance_time = 49;
  //积分
  int64 integral = 50;
  //是否虚拟订单1: 是 0：否
  int32 is_virtual = 51;
}

//订单详情
message CombineOrderDetail {
  //订单id
  int32 order_id = 1;
  //订单号
  string order_sn = 2;
  //父订单号
  string old_order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //下单时间
  string create_time = 5;
  //支付时间
  string pay_time = 6;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 7;
  //订单来源:渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 8;
  //业绩所属员工
  string performance_staff_name = 9;

  //配送状态: 0：待调度；20：已接单；30：已取货；50：已送达；99：已取消
  int32 delivery_status = 10;
  //配送方式1快递 2外卖 3自提
  int32 delivery_type = 11;
  //骑手(从delivery_node表取最后的一个节点的)
  string courier_name = 12;
  //骑手电话(从delivery_node表取最后的一个节点的)
  string courier_phone = 13;

  //收件人
  string receiver_name = 14;
  //收件电话
  string receiver_phone = 15;
  //收件地址
  string receiver_address = 16;
  //配送备注
  string delivery_remark = 17;

  // 商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 18;
  // 运费
  int32 freight = 19;
  // 包装费
  int32 packing_cost = 20;
  // 优惠详情 (商家优惠 + 平台优惠)
  repeated OrderPrivilegeModel orderprivilegemodel = 21;
  // 服务费(分)
  int32 service_charge = 22;
  // 用户实付
  int32 user_receive_total = 23;
  // 商家预计收入
  int32 actual_receive_total = 24;

  //商品
  repeated SimpleOrderProductList orderproductmodel = 25;

  //1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团 10饿了么
  int32 order_from = 26;
  //支付方式1支付宝2微信3美团4其他
  int32 pay_mode = 27;
  //顾客实际支付金额
  int32 total = 28;
  //总优惠金额
  int32 privilege = 29;
  //接单时间
  string accept_time = 30;
  //备货时长(美团拣货时间-下单时间)
  string stock_time = 31;
  //备货状态(是否拣货1是0否)
  int32 is_picking = 32;
  //取消时间
  string cancel_time = 33;
  //预计送达时间
  string expected_time = 34;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 35;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 36;
  //是否推送子龙或全渠道成功1是0否
  int32 push_third_order = 37;
  // 是否推送美团配送1是 0否
  int32 push_delivery = 38;
  //备货时间
  string picking_time = 39;
  //商户名称
  string shop_name = 40;
  //买家留言
  string buyer_memo = 41;
  //取货码
  string pickup_code = 42;
  //仓库所属1:(a8 or 全渠道) 2:管易 3:门店（子龙）
  int32 source = 43;
  //仓库代码
  string warehouse_code = 44;
  //仓库名称
  string warehouse_name = 45;
  //商户名称
  string shop_id = 46;
}

message CombineOrder {
  //订单id
  string order_id = 1;
  //原电商订单号
  string old_order_sn = 2;
  //电商主订单号
  string order_sn = 3;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 4;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 5;
  //商户或门店id
  string shop_id = 6;
  //商户名称
  string shop_name = 7;
  //会员id
  string member_id = 8;
  //会员名称
  string member_name = 9;
  //会员手机号
  string member_tel = 10;
  //收件人
  string receiver_name = 11;
  //收件省
  string receiver_state = 12;
  //收件市
  string receiver_city = 13;
  //收件区
  string receiver_district = 14;
  //收件地址
  string receiver_address = 15;
  //收件电话
  string receiver_phone = 16;
  //总优惠金额
  int32 privilege = 17;
  //Cod=货到付款, NoCod=非货到付款
  string pay_type = 18;
  //Cod=货到付款, NoCod=非货到付款
  string pay_mode = 19;
  //alipay支付宝  wxpay微信 wxpay_jsapi 微信支付JSAPI alipay_native 支付宝移动支付
  int32 pay_mode_int = 20;
  //收件手机
  string receiver_mobile = 21;
  //NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string gjp_status = 22;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 23;
  //商品总金额（未加运费）
  int32 goods_total = 24;
  //是否支付0否  1是
  int32 is_pay = 25;
  //创建时间
  string create_time = 26;
  //美团送达或电商确认收货时间(已完成)
  string confirm_time = 27;
  //美团接单时间
  string accept_time = 28;
  //是否拣货1是 0否
  int32 is_picking = 29;
  //美团拣货时间
  string picking_time = 30;
  //美团配送或电商发货时间
  string deliver_time = 31;
  //支付时间
  string pay_time = 32;
  //支付单号
  string pay_sn = 33;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 34;
  //总运费
  int32 freight = 35;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 source = 36;
  //发票信息
  string invoice = 37;
  //仓库代码
  string warehouse_code = 38;
  //买家留言
  string buyer_memo = 39;
  //卖家留言
  string seller_memo = 40;
  //管易发货状态0未发货 1已发货 2部分发货
  int32 gy_deliver_status = 41;
  //管易订单号
  string gy_order_sn = 42;
  //1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团
  int32 order_from = 43;
  //1快递 2外卖 3自提
  int32 delivery_type = 44;
  //配送备注
  string delivery_remark = 45;
  //是否推送美团配送1是 0否
  int32 push_delivery = 46;
  //是否推送子龙或全渠道1是 0否
  int32 push_third_order = 47;
  //推送美团配送失败原因
  string push_delivery_reason = 48;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 49;
  //附加优惠信息
  string extras = 50;
  //包装费
  int32 packing_cost = 51;
  //商品
  repeated OrderProductModel orderproductmodel = 52;
}

message OrderPayDetail {
  //订单id
  string order_id = 1;
  //支付订单号
  string pay_sn = 2;
  //支付方式1支付宝2微信
  int32 pay_mode = 3;
  //实际支付金额
  string pay_amount = 4;
  //支付时间
  string pay_time = 5;
  //商品总金额
  string goods_total = 6;
  //总优惠金额
  string privilege = 7;
}

//美团订单配送状态
message DeliveryState {
  //订单id
  string order_id = 1;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 2;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 3;
  //建议送达
  string suggest_delivery_time = 4;
  //生成订单时间
  string order_create_time = 5;
  //商家接单时间
  string order_accept_time = 6;
  //配送时间
  string order_deliver_time = 7;
  //已送达时间
  string order_confirm_time = 8;
  //已完成时间
  string order_finish_time = 9;
  //取消时间
  string cancel_time = 10;
  //取货码
  string pickup_code = 11;
  // 拣货完成时间
  string order_picking_time = 12;
}

//订单优惠活动
message OrderPrivilegeModel {
  //优惠活动名称
  string active_name = 1;
  //优惠金额
  string reduce_fee = 2;
  //美团平台承担的优惠金额
  string mt_charge = 3;
  //商家承担的优惠金额
  string poi_charge = 4;
  //备注
  string remark = 5;
}

//配送流程节点
message OrderDeliveryNodeModel {
  //订单号
  string order_sn = 1;
  //发生时间
  string create_time = 2;
  //配送节点描述
  string node_desc = 3;
  //配送员
  string courier_name = 4;
  //配送员手机号
  string courier_phone = 5;
  //取消原因详情
  string cancel_reason = 6;
}

//美团新增订单
message MtAddOrderRequest {
  //订单号
  string order_sn = 1;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 2;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 3;
  //商户或门店id
  string shop_id = 4;
  //商户名称
  string shop_name = 5;
  //收件人
  string receiver_name = 6;
  //收件省
  string receiver_state = 7;
  //收件市
  string receiver_city = 8;
  //收件区
  string receiver_district = 9;
  //收件地址
  string receiver_address = 10;
  //收件电话
  string receiver_phone = 11;
  //总优惠金额
  int32 privilege = 12;
  //Cod=货到付款, NoCod=非货到付款
  string pay_type = 13;
  //alipay支付宝  wxpay微信 wxpay_jsapi 微信支付JSAPI alipay_native 支付宝移动支付
  //string pay_mode = 14;
  //1支付宝  2微信
  //int32 pay_mode_int = 15;
  //收件手机
  string receiver_mobile = 16;
  //NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string gjp_status = 17;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 18;
  //商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）
  int32 goods_total = 19;
  //是否支付0否  1是
  int32 is_pay = 20;
  //创建时间
  string create_time = 21;
  //支付时间
  string pay_time = 22;
  //支付单号
  string pay_sn = 23;
  //订单类型1普通订单(默认),2预订订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 24;
  //总运费
  int32 freight = 25;
  //发票信息
  string invoice = 26;
  //买家留言
  string buyer_memo = 27;
  //卖家留言
  string seller_memo = 28;
  //1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团 10饿了么 11阿闻到家
  //int32 order_from = 29;
  //1快递 2外卖 3自提
  int32 delivery_type = 30;
  //配送备注
  string delivery_remark = 31;
  //附加优惠信息
  string extras = 32;
  //包装费
  int32 packing_cost = 33;
  //平台服务费
  int32 service_charge = 34;
  //商品
  repeated OrderProductModel OrderProductModel = 35;
  //支付信息
  repeated OrderPayInfoModel PayInfo = 36;
  //预计送达时间
  string expected_time = 37;
  //收货地址纬度
  double latitude = 38;
  //收货地址经度
  double longitude = 39;
  //取货码
  string pickup_code = 40;
  //总重量
  int32 total_weight = 41;
  //如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送
  string logistics_code = 42;
  //会员id
  string member_id = 43;
  //会员名称
  string member_name = 44;
  //member_tel
  string member_tel = 45;
  //是否订单调整
  int32 is_adjust = 46;
  //是否虚拟订单
  int32 is_virtual = 47;
  //是否拆单
  int32 is_split = 48;
  // 渠道订单子系统来源
  OrderChannelSrcTypeModel orderChannelSrcType = 49;
  //优惠信息
  repeated OrderPromotionModel orderPromotion = 50;
  //助力订单id（电商使用）
  int32 power_id = 51;
  //所有组合商品优惠
  int32 combine_privilege = 52;
  //1.推送到腾讯有数
  int32 is_push_tencent = 53;
  //1.阿闻自有,2.TP代运营
  int32 app_channel = 54;

  // 仓库的id信息
  int32 warehouse_id = 55;
  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string order_pay_type = 56;

  //以下参数是v2.9.10商城秒杀添加

  //1分享连接，2扫码 3 自己扫码自己 5 自主访问下单  商城分销用 原商城订单的dis_type字段
  int32 dis_type = 57 ;
  //希望送货时间 商城下单用 原商城订单的receiver_date_msg字段
  string receiver_date_msg = 58 ;
  //收件人座机 商城下单用 原商城订单的tel_phone字段
  string tel_phone = 59;
  //1 小程序(阿闻智慧门店) 2:阿闻宠物(北京那边用) //3阿闻商城(自用) 商城用 原商城虚拟订单的source字段
  int32 source = 60;
  //原商城虚拟订单first_order字段
  int32 first_order = 61;
  //原商城虚拟订单的open_id字段
  string  open_id = 62;
  //分销id 原商城虚拟订单的dis_id字段
  int32 dis_id = 63;
  //收货地址id 原商城虚拟订单的address_id字段
  int32 address_id = 64;
  //订单id
  int32 order_id = 65;
  //在线问诊信息
  DiagnoseData diagnose_data = 66;
  //提交订单时用户经度
  string lng = 67;
  //提交订单时用户纬度
  string lat = 68;
  // 社区团购站点id，分销员id（分销店铺和下单店铺不一致，会标记为空）
  int32 pickup_station_id = 69;
  int32 contract_fee = 70;
  // 社区团购字段，团购id、下单联系人、手机号，地址、昵称，头像
  int64 GroupId = 71;
  string GroupMobile = 72;
  string GroupName = 73;
  string GroupAddress = 76;
  // 昵称
  string nick_name = 74;
  // 头像完整地址
  string avatar_url = 75;
  //医疗互联网订单号\处方ID\推荐ID
  string consult_order_sn = 77;
  // 店铺海报推广员id
  string shop_dis_member_id = 78;
  // 店铺海报推广来源 0默认 1从爱省钱分享的店铺推广海报
  int32 shop_dis_member_from = 79;
  // 账单订单完成时间
  string bill_completed_time = 80;
  // 账单订单取消时间
  string bill_canceled_time = 81;
  //账户时间
  string trade_created_time = 82;
  //入账时间
  string trade_payment_time = 83;
  //账单日期
  string trade_time = 84;
  //本地生活的主体ID   `datacenter`.`organization_info` 主键ID
  int32 org_id=85;
  //订单流水号
  int32 order_index=86;
}

message DiagnoseData{
  //宠物id
  string pet_id = 2;
  //医生编号
  string doctor_code = 3;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 4;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 5;
  //免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
  int32 immune_status = 6;
  //症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
  string symptom = 7;
  //补充症状(选择【其他】关键词，输入框描述症状必填)
  string symptom_desc = 8;
  //症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
  int32 symptom_recent = 9;
  //宠物症状照片，多个用英文逗号隔开
  string image = 10;
  //是否就诊过：0未就诊，1就诊过
  int32 have_hospital = 11;
  //就诊过的医院名称
  string have_hospital_name = 12;
  //医生诊断结果与治疗方案
  string have_hospital_result = 13;
  //历史就诊的检查照片/药品照片
  string have_hospital_image = 14;
  //是否有其他病史：0否，1是
  int32 medical_history = 15;
  //其他病史信息
  string medical_history_info = 16;
  //宠物头像
  string pet_avatar = 17;
  //宠物名称
  string pet_name = 18;
  //宠物种类大分类
  string pet_kindof = 19;
  //宠物种类
  string pet_variety = 20;
  //宠物生日
  string pet_birthday = 21;
  //宠物性别 性别：1GG,2MM
  int32 pet_sex = 22;
  //1：已绝育 0：未绝育
  int32 pet_neutering = 23;
  //用户ScrmId
  string user_id = 24;
  //金额
  int32 amount = 25;
  //问诊时长
  int32 duration = 26;
  //医生类别：1门店医生，2互联网医生
  int32 doctor_type = 27;
}

message OrderChannelSrcTypeModel {
  // 子系统来源(渠道api文档定义)
  string src_type = 1;
  // 子系统来源名称(渠道api文档定义)
  string src_type_name = 2;
}

//优惠信息
message OrderPromotionModel {
  //活动id
  int32 promotion_id = 1;
  //活动类型  1满减
  int32 promotion_type = 2;
  //促销活动描述
  string promotion_title = 3;
  //商家优惠金额（分）
  int32 poi_charge = 4;
  //平台优惠金额（分）
  int32 pt_charge = 5;
  //优惠金额（分）
  int32 promotion_fee = 6;
}

message OrderPayInfoModel {
  string pay_id = 1;
  //订单号
  string order_sn = 2;
  //支付单号
  string pay_sn = 3;
  //1支付宝  2微信 3美团支付 4其他
  int32 pay_mode = 4;
  //实际支付金额
  int32 pay_amount = 5;
  //支付时间
  string pay_time = 6;
}

//接单接口(后台调用)
message AcceptOrderRequest {
  //订单号
  string order_sn = 1;
}

//取消接单(后台调用)
message CancelAcceptOrderRequest {
  //订单号
  string order_sn = 1;
  //取消原因
  string reason = 2;

  //操作用户
  string operation_user = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}

//拣货(后台调用)
message PickingOrderRequest {
  //订单号
  string order_sn = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

//订单完成（美团推送）
message AccomplishOrderRequest {
  //订单号
  string order_sn = 1;
  //完成时间
  string confirm_time = 2;
}

//配送状态新增
message DeliveryNodeRequest {
  // 配送活动标识
  int64 delivery_id = 1;
  // 订单号
  string order_sn = 2;
  //状态代码，可选值为0：15:骑手取货中 待调度；20：已接单；30：已取货；45：骑手送回中 50：已送达；99：已取消
  int32 status = 3;
  //配送员
  string courier_name = 4;
  //配送员手机号
  string courier_phone = 5;
  //取消原因
  string cancel_reason = 6;
  //时间戳
  string create_time = 7;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 8;
  //预计送达时间(注：只有“自由达”服务的订单状态回调有此字段)
  //即时单：只有骑手接单后，才会确定预计送达时间，因此状态为“已接单”、“已取货”、“已送达”时，此字段为非 0 值，其它状态下此值为 0；
  //预约单：下单成功即可确定预计送达时间，并且预计送达时间就是用户下单时传入的期望送达时间；
  //注：格式为 unix-timestamp，若预计送达时间还未确
  //定时，字段的值默认为 0；
  string predict_delivery_time = 9;
  //配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
  int32 delivery_type = 10;
  // 店铺主体Id
  int32 store_master_id = 11;
  //骑手当前的纬度，美团使用的是高德坐标系。
  string latitude = 12;
  //骑手当前的经度，美团使用的是高德坐标系。
  string longitude = 13;
  //是否异常配送单。
  int32 is_exception = 14;
  //是否是麦芽田的回调
  int32 is_myt = 15;
}

//配送状态新增
message ElmDeliveryNodeRequest {
  //配送活动标识
  int64 delivery_id = 1;
  //订单号
  string order_sn = 2;
  //状态代码，可选值为0：待调度；20：已接单；30：已取货；50：已送达；99：已取消
  int32 status = 3;
  //配送员
  string courier_name = 4;
  //配送员手机号
  string courier_phone = 5;
  //取消原因
  string cancel_reason = 6;
  //时间戳
  string create_time = 7;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 8;
  //预计送达时间(注：只有“自由达”服务的订单状态回调有此字段)
  //即时单：只有骑手接单后，才会确定预计送达时间，因此状态为“已接单”、“已取货”、“已送达”时，此字段为非 0 值，其它状态下此值为 0；
  //预约单：下单成功即可确定预计送达时间，并且预计送达时间就是用户下单时传入的期望送达时间；
  //注：格式为 unix-timestamp，若预计送达时间还未确
  //定时，字段的值默认为 0；
  string predict_delivery_time = 9;
}

//取消订单
message CancelOrderRequest {
  string order_sn = 1;
  string cancel_reason = 2;
  int32 is_refund = 3;
  //退款单号
  string refundsn = 4;
  int32 order_status = 5;
  int32 order_status_child = 6;
  //操作人
  string operationer = 7;
  //是否订单调整 0 否 1 是
  int32 is_adjust = 8;
  //外部退款单号
  string old_refund_sn = 9;
}

//取消订单
message CancelDeliveryRequest {
  //配送ID
  int64 delivery_id = 1;
  //取消原因
  string cancel_reason = 2;
  //订单号
  string order_sn = 3;
  //取消原因ID
  int32 cancel_reason_id = 4;
  //配送ID
  string delivery_str = 5;
}

//催单
message ReminderOrderRequest {
  string order_sn = 1;
  string remind_time = 2;
}

//美团后台接单推送
message MtAcceptOrderRequest {
  //订单号
  string order_sn = 1;
  //美团状态码1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消
  string status = 2;
}

//重新发起推送到 第三方
message MtRePushThirdRequest {
  string order_sn = 1;
}

message SearchDeliveryPriceResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  // 支付信息
  repeated DeliveryPriceInfo data = 3;
}
message DeliveryPriceInfo {
  //配送名称
  string  DeliveryName= 1;
  //配送价格
  string  DeliveryPrice= 2;
  //配送类型id 配送类型   0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风')
  int32  DeliveryType= 3;
  //是否成功获取到价格了
  bool IsOk =4;
  //是否最低价格 1
  bool IsMinPrice =5;

}

//查询可被部分退款的商品详情
message OrderGetPartRefundFoodsRequest {
  string order_sn = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

// 查询可被部分退款的商品详情 响应参数
message OrderPartRefuFoodsResponse {
  // 错误 码
  int32 code = 1;
  // 信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 商品信息
  repeated PartRefundFoodsList data = 4;
}
message PartRefundFoodsList {
  // APP方商品id  即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 1;
  //商品名称
  string food_name = 2;
  // 商品sku唯一标识码，字段信息限定长度不超过40个字符。
  string sku_id = 3;
  // 商品sku现价(单价)，单位是元。此字段信息为当前订单中用户实际支付单件商品sku的价格。
  float food_price = 4;
  //  可退的商品sku的数量 如果是虚拟商品 = 售卖的数量-已经退款的数量-已经核销的数量
  float count = 5;
  //规格
  string spec = 6;
  // 可退款价格（单价），单位是元。
  float refund_price = 9;
  // 销售数量
  float sale_count = 10;
  // 饿了么的商品子订单ID
  string sub_biz_order_id = 11;
  //京东到家标识 商品促销类型（1203满赠，6买赠，1正品）
  int32 promotion_type = 12;
  //订单商品表主键id
  int64 order_product_id = 13;
  //可退金额
  float surplus_refund_amount = 14;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 15;
  //父商品的sku_id
  string parent_sku_id = 16;
  //组合子商品在单个组合中的售卖数量
  int32 group_item_num = 17;
  //组合商品子商品集合
  repeated PartRefundFoodsList child_products = 18;
  //平台商品ID
  int64 platform_sku_id = 19;
}

//发起部分退款
message OrderApplyPartRefundRequest {
  // 订单号    必镇
  string order_id = 1;
  //  因***原因部分退款 必镇
  string reason = 2;
  //退款类型1为退款,2为退货  目前不支持退货
  int64 refund_type = 3;
  // 部分退款商品sku数据集合的json格式数组
  repeated ApplyPartRefundList food_data = 4;

  // 订单来源 (1-阿闻，2-美团，3-饿了么)
  int32 order_from = 5;

  //操作用户
  string operation_user = 6;

  //京东到家 专属 售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）
  string refund_code = 7;
  // 店铺主体Id
  int32 store_master_id = 8;
}

// 部分退款商品sku数据集合的json格式数组
message ApplyPartRefundList {
  //  APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 1;
  //   商品sku唯一标识码，如为单规格商品，没有维护规格sku_id，此字段可不传。
  string sku_id = 2;
  // 本次退款的商品数量：(1)当按件部分退款时，此字段必填，传入需退款商品sku的数量，为大于0的整数。(2)当part_refund_type=3时，即按克重退差价，则此字段非必填(如传了也不会生效)。
  float count = 3;
  //饿了么子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
  string sub_biz_order_id = 4;
  //京东到家标识 商品促销类型（1203满赠，6买赠，1正品）
  int32 promotion_type = 12;
  //平台商品ID
  int64 platform_sku_id = 5;
}

//退款管理列表
message RefundOrderListRequest {
  //订单编号
  string order_sn = 1;
  //下单开始时间
  string starttime = 2;
  //下单结束时间
  string endtime = 3;
  //退款编号
  string refundsn = 4;
  //退款类型1用户 2商家 3客服
  int32 user_type = 5;
  //退款方式:1为退款,2为退货，默认为1
  int32 refundtype = 6;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功
  int32 refund_state = 7;
  //快递单号
  string express_number = 8;
  //商品名称
  string productname = 9;
  //订单类型1实物  2虚拟
  int32 ordertype = 10;
  //来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 11;
  //销售渠道1小程序  2网站  3h5
  int32 salechannel = 12;
  //登录用户所有权限的门店id
  repeated string shopids = 13;
  //当前页码
  int32 pageindex = 14;
  //每页行数
  int32 pagesize = 15;
  //用户编号
  string UserNo = 16;
  //店铺类型
  int32 app_channel = 17;
}

message RefundOrderListResponse {
  // 错误 码
  int32 code = 1;
  // 信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 商品信息
  repeated RefundOrderList data = 4;
  //总条数
  int32 total_count = 5;
}

message RefundOrderList {
  //退款单号
  string refundsn = 1;
  //订单编号
  string order_sn = 2;
  //申请时间
  string createtime = 3;
  //快递单号
  string expressnum = 4;
  //申请类型:1为退款,2为退货，默认为1
  int32 refundtype = 5;
  //订单来源
  int32 order_from = 6;
  //退款状态
  int32 refund_state = 7;
  //备注
  string refundreason = 8;
  //商品
  repeated RefundOrderProductList product = 9;
  //refundamount
  string refundamount = 10;
  //配送费
  double freight = 11;
  //部分退还是整单退，1整单 2部分
  int32 fullrefund = 12;
  //商店名
  string shopname = 13;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 source = 14;
  //商店id
  string shopid = 15;
  //仓库代码
  string warehousecode = 16;
  //仓库类型:1-中心仓，2-区域仓，3-门店仓，4-前置仓
  int32 warehousecategory = 17;
  //仓库名
  string warehousename = 18;
  //渠道id，1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 19;
}

message RefundOrderProductList {
  //商品名称
  string food_name = 1;
  //订单金额
  int32 food_price = 2;
  //退货件数
  int32 quantity = 3;
  //退款金额(元)
  string refundamount = 4;
  //审核退款数据量，库存加回去用这个数量
  int32 tkcount = 5;
  //商品sku id 可废弃
  string goodsid = 6;
  //商品规格
  string spec = 7;
  //商品skuid
  string sku_id = 8;
  //商品spuid
  int32 product_id = 9;
}

message MtAddOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单号
  string order_sn = 4;
  //支付单号
  string pay_sn = 5;
  //订单id
  int32 order_id = 6;
  //在线问诊医生编号
  string doctor_code = 7;
}

//获取订单信息请求
message GetOneOrderRequest {
  //订单号
  string order_sn = 1;
  //订单类型
  int32 order_type = 2;
}

//获取订单商品信息
message GetOrderProductResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //商品信息
  repeated OrderProducts order_products = 4;
}

//获取退款订单商品信息
message GetRefundOrderProductResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //商品信息
  repeated RefundOrderProducts refund_order_products = 4;
}

//退款订单商品信息
message RefundOrderProducts {
  int64   id = 1;
  //退款单号
  string  refund_sn = 2;
  //商品skuid
  string  sku_id = 3;
  //订单商品表主键id
  int64   order_product_id = 4;
  //商品名称
  string  product_name = 5;
  //商品原优惠单价
  int64   product_price = 6;
  //商品原单价
  int64   marking_price = 7;
  //sku申请退款数量
  int64   quantity = 8;
  //sku实际退款数量
  int64   tkcount = 9;
  //sku总退款金额，单位元
  string  refund_amount = 10;
  //sku单件退款金额
  int64   refund_price = 11;
  //订单明细Id (全渠道需要)
  string  oc_id = 12;
  //商品sku的规格名称
  string  spec = 13;
  //退款核销码，多个以英文逗号相隔
  string  verify_codes = 14;
  //创建时间
  string  create_time = 15;
  //运费
  string  freight = 16;
}

//订单商品信息
message OrderProducts {
  int64   id = 1;
  //主订单号
  string  order_sn = 2;
  //商品skuid
  string  sku_id = 3;
  //组合商品父级skuid
  string  parent_sku_id = 4;
  //组合商品子商品集合
  string  children_sku = 5;
  //第三方货号
  string  third_sku_id = 6;
  //商品id
  string  product_id = 7;
  //商品名称
  string  product_name = 8;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32   product_type = 9;
  //组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟
  int32   combine_type = 10;
  //商品图片
  string  image = 11;
  //商品编码
  string  bar_code = 12;
  //商品原单价
  int64   marking_price = 13;
  //商品优惠单价
  int64   discount_price = 14;
  //商品均摊后实际支付单价
  int64   pay_price = 15;
  //数量
  int64   number = 16;
  //规格
  string  specs = 17;
  //sku实付总金额，discount_price*number
  int64   payment_total = 18;
  //包含平台优惠的sku实付总金额，payment_total+privilege_pt
  int64   sku_pay_total = 19;
  //商家优惠金额
  int64   privilege = 20;
  //平台优惠金额
  int64   privilege_pt = 21;
  //privilege_total
  int64   privilege_total = 22;
  //sku分摊运费
  int64   freight = 23;
  //发货状态0未发货  1已发货
  int32   deliver_status = 24;
  //发货数量
  int64   deliver_num = 25;
  //退货数量
  int64   refund_num = 26;
  //饿了么子订单ID
  string  sub_biz_order_id = 27;
  //限时折扣活动id
  int64   promotion_id = 28;
  //商品级别促销类型
  int64   promotion_type = 29;
  //佣金比例，单位%
  int32   commission_rate = 30;
  //是否分销
  int32   is_distribute = 31;
  //只有虚拟商品才有值(1.有效期至多少  2.有效期天数)
  int32   term_type = 32;
  //如果term_type=1 存：时间戳  如果term_type=2 存多少天
  int64   term_value = 33;
  //是否支持过期退款 1：是  0：否
  int32   virtual_invalid_refund = 34;
  //商城订单商品主键id
  int64   mall_order_product_id = 35;
  //订单id
  string  del_order_id = 36;
  //原主键，待删除
  string  del_id = 37;
  //创建时间
  string  create_time = 38;
  //更新时间
  string  update_time = 39;
}


//获取订单信息返回
message GetOneOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单信息
  OneOrderInfo Order = 4;
}

//获取订单信息
message OneOrderInfo {
  //订单id
  string order_id = 1;
  //原电商订单号
  string old_order_sn = 2;
  //电商主订单号
  string order_sn = 3;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 4;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 5;
  //商户或门店id
  string shop_id = 6;
  //商户名称
  string shop_name = 7;
  //会员id
  string member_id = 8;
  //会员名称
  string member_name = 9;
  //会员手机号
  string member_tel = 10;
  //收件人
  string receiver_name = 11;
  //收件省
  string receiver_state = 12;
  //收件市
  string receiver_city = 13;
  //收件区
  string receiver_district = 14;
  //收件地址
  string receiver_address = 15;
  //收件电话
  string receiver_phone = 16;
  //总优惠金额
  int32 privilege = 17;
  //Cod=货到付款, NoCod=非货到付款
  string pay_type = 18;
  //收件手机
  string receiver_mobile = 21;
  //NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
  string gjp_status = 22;
  //总金额（付款金额，加上运费，减优惠金额）
  int32 total = 23;
  //商品总金额（未加运费）
  int32 goods_total = 24;
  //是否支付0否  1是
  int32 is_pay = 25;
  //创建时间
  string create_time = 26;
  //送达或电商确认收货时间(已完成)
  string confirm_time = 27;
  //接单时间
  string accept_time = 28;
  //是否拣货1是 0否
  int32 is_picking = 29;
  //拣货时间
  string picking_time = 30;
  //配送或电商发货时间
  string deliver_time = 31;
  //支付时间
  string pay_time = 32;
  //支付单号
  string pay_sn = 33;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 34;
  //总运费
  int32 freight = 35;
  //仓库所属1:(a8 or 全渠道)  2:管易  3:门店
  int32 source = 36;
  //发票信息
  string invoice = 37;
  //仓库代码
  string warehouse_code = 38;
  //买家留言
  string buyer_memo = 39;
  //卖家留言
  string seller_memo = 40;
  //管易发货状态0未发货 1已发货 2部分发货
  int32 gy_deliver_status = 41;
  //管易订单号
  string gy_order_sn = 42;
  //1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团
  int32 order_from = 43;
  //1快递 2外卖 3自提
  int32 delivery_type = 44;
  //配送备注
  string delivery_remark = 45;
  //是否推送美团配送1是 0否
  int32 push_delivery = 46;
  //是否推送子龙或全渠道1是 0否
  int32 push_third_order = 47;
  //推送美团配送失败原因
  string push_delivery_reason = 48;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 49;
  //附加优惠信息
  string extras = 50;
  //包装费
  int32 packing_cost = 51;
  //取货码
  string pickup_code = 52;
  //服务费
  int32 service_charge = 53;
  //取消订单原因
  string cancel_reason = 54;
  //取消时间
  string cancel_time = 55;
  //催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）
  string remind_time = 56;
  //收货地址纬度
  double latitude = 57;
  //收货地址经度
  double longitude = 58;
  //总重量
  int32 total_weight = 59;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string logistics_code = 60;
  //是否锁定库存0否 1是
  int32 locked_stock = 61;
  //渠道id（datacenter.platform_channel表）
  int32 channel_id = 62;
  //来源
  int32 user_agent = 63;
  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string order_pay_type = 64;
}

message OrderPayInfoResponse {
  //总优惠金额
  int32 privilege = 1;
  //付款金额
  int32 pay_price = 2;
  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string order_pay_type = 3;
  //回调地址
  string notify_url = 4;
  //子商户公众账号 ID
  string sub_app_id = 5;
  //密钥
  string secret_key = 6;
  //商户号
  string merchant_id = 7;
}

// 订单列表导出商品数据
message OrderExportProduct {
  // 订单号
  string order_sn = 1;
  // SKU
  string sku = 2;
  // 商品名称
  string product_name = 3;
  // 订单状态
  int32 order_status_child = 4;
  // 销售数量
  int32 number = 5;
  // 支付金额(客户实际支付金额)
  int32 total = 6;
  // 退款金额
  string refundamount = 7;
  // 支付时间
  string pay_time = 8;
  // 门店id
  string shop_id = 9;
  // 门店名称
  string shop_name = 10;
  // 商品单价
  int32 price = 11;
  // 业绩所属员工
  string performance_staff = 12;
  // 渠道id
  int32 channel_id = 13;
  // 渠道分类
  string channel_category_name = 14;
  // 商品id
  string product_id = 15;
}

// 订单列表导出商品数据
message AwenOrderExportProductResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //返回数据
  repeated OrderExportProduct details = 4;
}

//订单支付通知
message OrderPayNotifyRequest {
  //订单号
  string order_sn = 1;
  //支付单号
  string pay_sn = 2;
  //支付类型1支付宝  2微信 3美团 4其他
  int32 pay_mode = 3;
  //支付时间
  string pay_time = 4;
  //实际支付金额
  int32 pay_amount = 5;
}

message JddjAdjustOrderRequest {
  //订单ID
  string order_id = 1;
  //外部订单ID
  string old_order_sn = 2;
  //用户名称
  string user_name = 3;
}

//OMS巨益订单对接模块
service OmsOrderService {
  //订单发货oms回传接口
  rpc OmsOrderDelivery (OmsOrderDeliveryRequest) returns (OmsOrderDeliveryResponse) {
  }

  //售后单同步到oms
  rpc AfterOrderSynchronizeToOms (AfterorderRequest) returns (AfterorderResponse) {
  }

  //瑞鹏OMS正向订单处理成功之后回调
  //一般用于 阿闻推送瑞鹏oms时 oms落单成功 但是推送供应链或者冻结库存失败是 oms重新处理成功之后回调阿闻，
  //让阿闻修改订单推送第三方的状态
  rpc RpOmsOrderDelivered (RpOmsOrderDeliveredRequest) returns (BaseResponse) {
  }

  //oms 重推
  rpc OmsRepush (OmsRepushRequest) returns (OmsRepushResponse) {
  };
}


//oms重推
message  OmsRepushRequest {
  string refundSn = 1;
}
message OmsRepushResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}


message RpOmsOrderDeliveredRequest{
  string order_sn = 1;
}

message OmsOrderDeliveryRequest {
  //订单
  repeated OmsOrder omsorders = 1;
}

// 订单列表导出商品数据
message OmsOrderDeliveryResponse {
  //状态码
  int32 code = 1;
  //错误信息描述
  string message = 2;
  //结果数据
  string data = 3;
}

//oms订单
message OmsOrder {
  //订单ID
  string orderid = 1;
  //发货单号
  string delivery_bill_code = 2;
  //发货时间
  string delivery_date = 3;
  //物流单号
  string logistics_code = 4;
  //物流公司简写
  string logistics_company = 5;
  //商品集合
  repeated OmsOrderGood goodslist = 6;
}

//oms订单商品
message OmsOrderGood {
  //商品spu码
  string spu = 1;
  //商品sku码
  string sku = 2;
  //第三方的spu(A8无)
  string thirdspu = 3;
  //第三方的sku(A8的货号)
  string thirdsku = 4;
  //发货数量
  int32 stock = 5;
}

message RefundOrderIntercept {
  // 订单号
  string orderid = 1;
  // 拦截结果 1成功，0失败
  int32 result = 2;
  // 发货状态，1发货前，0发货后
  int32 type = 3;
}

message RefundOrderInterceptResponse {
  int32 code = 1;
  string msg = 2;
  RefundOrderIntercept data = 3;
}

message GetIntelligenceOrderRequest {
  int32 sale_type = 1; //数据统计类型 1 商品销售排行 2 商品分类销售排行 3 月份汇总 4 门店营收 5 渠道营收
  repeated string finance_code = 2; //门店财务编码
  string search = 3; //条件
}

message IntelligenceOrderResponse {
  int32 code = 1;
  string msg = 2;
  repeated IntelligenceOrder data = 3;
}

message RefundHealthPlanCardRequest {
  //订单ID
  string order_id = 1;
  //退款金额
  float refund_amount = 2;
}

message RefundHealthPlanCardResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message HealthPlanRefundOrderRequest {
  //宠物编码
  string pet_id = 1;
  //卡号
  string ensure_code = 2;
  //批次号
  string batch_code = 3;
  //退款金额(分)
  int64 refund_amount = 4;
  //用户ID
  string user_id = 5;
}

message IntelligenceOrder {
  string name = 1; //月、渠道、大区、商品名
  string value = 2; //销售订单额
  string num = 3; //销售订单数
}
message SaveOrderOriginDataRequest {
  int32 channel_id = 1; //渠道ID
  string old_order_sn = 2; //原始订单号
  string body_json = 3; //内容
}

// 阿闻管家实物订单-导出订单数据
message AwenOrdeExport {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //外部订单号
  string old_order_sn = 3;
  //父订单号
  string parent_order_sn = 4;
  //创建时间
  string create_time = 5;
  //支付单号
  string pay_sn = 6;
  //支付时间
  string pay_time = 7;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 8;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 9;
  //总优惠金额(分)
  int32 privilege = 10;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付
  int32 pay_mode = 11;
  //收件区
  string receiver_district = 12;
  //收件市
  string receiver_city = 13;
  //商户名称
  string shop_name = 14;
  //商户或门店id(财务编码)
  string shop_id = 15;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 17;
  //收件人
  string receiver_name = 18;
  //收件手机
  string receiver_mobile = 19;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 20;
  // 活动类型
  string activity_type = 21;
  //业绩所属员工姓名
  string performance_staff_name = 22;
  //业绩操作人姓名
  string performance_operator_name = 23;
  //业绩分配时间
  string performance_operator_time = 24;
  //仓库类型(门店类型)
  string category = 25;
  //仓库名称
  string warehouse_name = 26;
  //总运费
  int32 freight = 27;
  //渠道id
  int32 channel_id = 28;
  //实收金额
  int32 actual_receive_total = 29;
  // 订单来源
  int32 order_from = 30;
  //服务费
  int32 service_charge = 31;
  //配送方式
  int32 delivery_type = 32;
  //门店支付配送费
  string store_pay_delivery_amount = 33;
  //会员ID
  string member_id = 34;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 35;
  //店铺类型
  int32 app_channel = 36;
  // 是否新老客户，默认老客户，1-新客户
  int32 is_new_customer = 37;
  //订单状态
  int32 order_status = 39;
  //退款金额
  string refund_amount = 40;
  string receiver_address = 41;
  string buyer_memo = 42;
  //预计送达时间
  string expected_time = 43;
  //自提点名称
  string pickup_station_name = 44;
  //自提点详细地址
  string pickup_station_address = 45;
  // 配送费优惠
  int32 freight_privilege = 46;
  // 包装费
  int32 packing_cost = 47;
  // 履约服务费
  int32 contract_fee = 48;
  // 平台补贴 platformPayedAmount
  int32  platform_payed_amount = 49;
  // 平台配送费优惠
  int32  platform_freight_privilege = 50;
  // 商品实付总金额
  int32  goods_pay_total = 51;
  // 订单实付金额
  int32  pay_total = 52;
  //业绩归属人所属门店编码
  string performance_finance_code = 53;
  //业绩归属人所属门店名称
  string performance_chain_name = 54;
  // 加密收货手机号
  string en_receiver_mobile = 55;
  //订单摊走的平台补贴
  int32 privilege_pt = 56;
  }
// 阿闻管家实物订单-导出(含商品明细)数据
message AwenOrderProductExport {
  // 订单号
  string order_sn = 1;
  // SKU
  string sku_id = 2;
  // 商品名称
  string product_name = 3;
  // 订单状态
  int32 order_status_child = 4;
  // 销售数量
  int32 number = 5;
  // 支付金额(客户实际支付金额)
  int32 total = 6;
  // 退款金额
  string refund_amount = 7;
  // 支付时间
  string pay_time = 8;
  // 门店id
  string shop_id = 9;
  // 门店名称
  string shop_name = 10;
  // 商品单价
  int32 pay_price = 11;
  // 业绩所属员工
  string performance_staff_name = 12;
  // 渠道id
  int32 channel_id = 13;
  // 渠道分类
  string channel_category_name = 14;
  // 商品id
  string product_id = 15;

  // 外部订单号
  string old_order_sn = 16;
  // 父订单号
  string parent_order_sn = 17;
  //父sku
  string parent_sku_id = 18;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）'
  int32 product_type = 19;
  // sku实付总金额，discount_price*number
  int32 payment_total = 20;
  // 包含平台优惠的sku实付总金额，payment_total+privilege_pt
  int32 sku_pay_total = 21;
  //店铺类型
  int32 app_channel = 22;
  //支付方式
  int32 pay_mode = 23;
  //订单状态
  int32 order_status = 24;
  ///
  int32 refund_state = 25;
  string receiver_address = 26;
  string receiver_mobile = 27;
  string buyer_memo = 28;
  string create_time = 29;
  string receiver_name = 30;
  //自提点名称
  string pickup_station_name = 31;
  //自提点详细地址
  string pickup_station_address = 32;
  //预计送达时间
  string expected_time = 33;
  //退款数量
  int32 refund_number = 34;
  //退款价格
  int32 refund_total = 35;
  //订单商品id
  int64 order_product_id = 36;
  // 加密收货手机号
  string en_receiver_mobile = 37;
  //订单摊走的平台补贴
  int32 privilege_pt = 38;
}

// 阿闻管家父订单-导出订单数据
message AwenParentOrderExport {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //原电商父订单号
  string old_order_sn = 3;
  //外部订单
  string gy_order_sn = 4;
  //创建时间
  string create_time = 5;
  //支付单号
  string pay_sn = 6;
  //支付时间
  string pay_time = 7;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 8;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 9;
  //总优惠金额(分)
  int32 privilege = 10;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付
  int32 pay_mode = 11;
  //收件区
  string receiver_district = 12;
  //收件市
  string receiver_city = 13;
  //商户名称
  string shop_name = 14;
  //商户或门店id(财务编码)
  string shop_id = 15;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 17;
  //收件人
  string receiver_name = 18;
  //收件手机
  string receiver_mobile = 19;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 20;
  // 活动类型
  string activity_type = 21;
  //业绩所属员工姓名
  string performance_staff_name = 22;
  //业绩操作人姓名
  string performance_operator_name = 23;
  //业绩分配时间
  string performance_operator_time = 24;
  //仓库类型(门店类型)
  string category = 25;
  //仓库名称
  string warehouse_name = 26;
  //总运费
  int32 freight = 27;
  //渠道id
  int32 channel_id = 28;
  //实收金额
  int32 actual_receive_total = 29;
  // 订单来源
  int32 order_from = 30;
  //平台服务费
  int32 service_charge = 31;
  //配送方式
  int32 delivery_type = 32;
  //门店支付配送费
  string store_pay_delivery_amount = 33;
  //会员ID
  string member_id = 34;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 35;
  //店铺类型
  int32 app_channel = 36;
  // 是否新老客户，默认老客户，1-新客户
  int32 is_new_customer = 37;
  // 商品组合类型0-非组合 1-实物实物 2-虚拟虚拟 3-虚拟实物
  int32 group_type = 38;
  // 商品id
  string product_id = 39;
  //状态
  int32 order_status = 40;
  //退款金额
  string refund_amount = 41;
  //退款状态
  int32 refund_state = 42;
  //退款状态
  string receiver_address = 43;
  string delivery_remark = 44;
  string buyer_memo = 45;
  //预计送达时间
  string expected_time = 46;
  //自提点名称
  string pickup_station_name = 47;
  //自提点详细地址
  string pickup_station_address = 48;
  // 配送费优惠
  int32 freight_privilege = 49;
  // 包装费
  int32 packing_cost = 50;
  // 履约服务费
  int32 contract_fee = 51;
  // 平台补贴 platformPayedAmount
  int32  platform_payed_amount = 52;
  // 平台配送费优惠
  int32  platform_freight_privilege = 53;
  // 商品实付总金额
  int32 goods_pay_total = 54;
  //业绩归属人所属门店编码
  string performance_finance_code = 55;
  //业绩归属人所属门店名称
  string performance_chain_name = 56;
  // 加密收货手机号
  string en_receiver_mobile = 57;
  // 账单订单完成时间
  string bill_completed_time = 58;
  // 账单订单取消时间
  string bill_canceled_time = 59;
  //账户时间
  string trade_created_time = 60;
  //入账时间
  string trade_payment_time = 61;
  //账单日期
  string trade_time = 62;
  //订单摊走的平台补贴
  int32 privilege_pt = 63;
  //订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购 21：电商VIP实物订单 22：次卡 23：储值卡  99助力订单',
  int32 order_type = 64;
  //实际支付金额
  int32 pay_amount = 65;
  // 连锁名称
  string chain_name = 66;
  // 店铺类型（eshop_saas.t_tenant.type 店铺类型：1-直营店、2-加盟店、3-其他）
  string tenant_type = 67;
  // 新零售运营 (eshop_saas.t_tenant_retail_cfg.operation_type运营模式：1-独立运营，2-代运营模式)
  string tenant_retail_operation_type = 68;
  // 支付方式描述
  string pay_mode_text = 69;
  //拣货人
  string pick_user_id = 70;
  // 拣货人名称
  string pick_user_name = 71;
  // 拣货时间
  string picking_time = 72;
}

// 阿闻管家父订单-导出(含商品明细)数据
message AwenParentOrderProductExport {
  // 订单号
  string order_sn = 1;
  // SKU
  string sku_id = 2;
  // 商品名称
  string product_name = 3;
  // 订单状态
  int32 order_status_child = 4;
  // 销售数量
  int32 number = 5;
  // 支付金额(客户实际支付金额)
  int32 total = 6;
  // 退款金额
  string refund_amount = 7;
  // 支付时间
  string pay_time = 8;
  // 门店id
  string shop_id = 9;
  // 门店名称
  string shop_name = 10;
  // 商品单价
  int32 pay_price = 11;
  // 业绩所属员工
  string performance_staff_name = 12;
  // 渠道id
  int32 channel_id = 13;
  // 渠道分类
  string channel_category_name = 14;
  // 商品id
  string product_id = 15;
  // 父订单号
  string old_order_sn = 16;
  // 外部订单号
  string gy_order_sn = 17;
  //父sku
  string parent_sku_id = 18;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）'
  int32 product_type = 19;
  // sku实付总金额，discount_price*number
  int32 payment_total = 20;
  // 包含平台优惠的sku实付总金额，payment_total+privilege_pt
  int32 sku_pay_total = 21;
  // 店铺类型
  int32 app_channel = 22;
  //会员ID
  string MemberId = 23;
  //支付方式
  int32 PayMode = 24;
  // 商品组合类型0-非组合 1-实物实物 2-虚拟虚拟 3-虚拟实物
  int32 group_type = 25;
  //状态
  int32 order_status = 26;
  //
  int32 refund_state = 27;
  string receiver_address = 28;
  string receiver_mobile = 29;
  string buyer_memo = 30;
  string create_time = 31;
  string receiver_name = 32;
  //自提点名称
  string pickup_station_name = 33;
  //自提点详细地址
  string pickup_station_address = 34;
  //预计送达时间
  string expected_time = 35;
  //退款数量
  int32 refund_number = 36;
  //退款价格
  int32 refund_total = 37;
  // 加密收货手机号
  string en_receiver_mobile = 38;
  //订单摊走的平台补贴
  int32 privilege_pt = 39;

  // 订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购 21：电商VIP实物订单 22：次卡 23：储值卡  99助力订单',
  int32 order_type = 40;
  // 原价
  int32  marking_price = 41;
  // 连锁名称
  string chain_name = 42;
  // 店铺类型（eshop_saas.t_tenant.type 店铺类型：1-直营店、2-加盟店、3-其他）
  string tenant_type = 43;
  // 新零售运营 (eshop_saas.t_tenant_retail_cfg.operation_type运营模式：1-独立运营，2-代运营模式)
  string tenant_retail_operation_type = 44;
}

// 阿闻管家虚拟订单-导出订单数据
message AwenVirtualOrderExport {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //外部单号
  string old_order_sn = 3;
  //父订单号
  string parent_order_sn = 4;
  //创建时间
  string create_time = 5;
  //支付单号
  string pay_sn = 6;
  //支付时间
  string pay_time = 7;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 8;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 9;
  //总优惠金额(分)
  int32 privilege = 10;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付
  int32 pay_mode = 11;
  //收件区
  string receiver_district = 12;
  //收件市
  string receiver_city = 13;
  //商户名称
  string shop_name = 14;
  //商户或门店id(财务编码)
  string shop_id = 15;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 17;
  //收件人
  string receiver_name = 18;
  //收件手机
  string receiver_mobile = 19;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 20;
  // 活动类型
  string activity_type = 21;
  //业绩所属员工姓名
  string performance_staff_name = 22;
  //业绩操作人姓名
  string performance_operator_name = 23;
  //业绩分配时间
  string performance_operator_time = 24;
  //仓库类型(门店类型)
  string category = 25;
  //仓库名称
  string warehouse_name = 26;
  //总运费
  int32 freight = 27;
  //渠道id
  int32 channel_id = 28;
  //实收金额
  int32 actual_receive_total = 29;
  // 订单来源
  int32 order_from = 30;
  //服务费
  int32 service_charge = 31;
  //订单状态：0已取消,10(默认)未付款,20已付款,30已完成
  int32 order_status = 32;
  //会员ID
  string member_id = 33;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 34;
  // 是否新老客户，默认老客户，1-新客户
  int32 is_new_customer = 35;
  // 退款金额
  string refund_amount = 36;
  //
  int32 refund_state = 37;
  //业绩归属人所属门店编码
  string performance_finance_code = 38;
  //业绩归属人所属门店名称
  string performance_chain_name = 39;
  //订单摊走的平台补贴
  int32 privilege_pt = 40;
}
// 阿闻管家虚拟订单-导出(含商品明细)数据
message AwenVirtualOrderProductExport {
  // 订单号
  string order_sn = 1;
  // SKU
  string sku_id = 2;
  // 商品名称
  string product_name = 3;
  // 子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 4;
  // 销售数量
  int32 number = 5;
  // 支付金额(客户实际支付金额)
  int32 total = 6;
  // 退款金额
  string refund_amount = 7;
  // 支付时间
  string pay_time = 8;
  // 门店id
  string shop_id = 9;
  // 门店名称
  string shop_name = 10;
  // 商品单价
  int32 pay_price = 11;
  // 业绩所属员工
  string performance_staff_name = 12;
  // 渠道id
  int32 channel_id = 13;
  // 渠道分类
  string channel_category_name = 14;
  // 商品id
  string product_id = 15;

  // 外部单号
  string old_order_sn = 16;
  // 父订单号
  string parent_order_sn = 17;
  //父sku
  string parent_sku_id = 18;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）'
  int32 product_type = 19;
  //订单状态：0已取消,10(默认)未付款,20已付款,30已完成
  int32 order_status = 20;
  // sku实付总金额，discount_price*number
  int32 payment_total = 21;
  // 包含平台优惠的sku实付总金额，payment_total+privilege_pt
  int32 sku_pay_total = 22;
  //支付方式
  int32 pay_mode = 23;
  //核销码
  string verify_code = 24;
  //核销状态, 0未核销, 1已核销, 2已退款
  string verify_status = 25;
  //核销时间
  string verify_time = 26;
  //核销地点财务编码
  string verify_shop = 27;
  //sku实际退款数量
  int32 tkcount = 28;
  //sku退款金额
  string refund_amount_sku = 29;
  //sku单件退款金额
  string refund_price = 30;
  //实际支付金额
  int32 payment_total_sku = 31;
  //财务编码
  string finance_codes = 32;
  //订单摊走的平台补贴
  int32 privilege_pt = 33;
}

//订单详情请求
message AwenAllOrderBaseDetailRequest {
  //订单id
  string order_id = 1;
}
//脚印模型
message Footmark {
  //主键id
  int32 id = 1;
  //订单号
  string order_sn = 2;
  //日志类型，1已下单,2已支付,3已拆单,4商家已接单,5已推送第三方,6调度中,7骑手已接单,8骑手已取货,9骑手已送达,10已完成,11用户发起售后申请,12商家发起售后申请,13客服发起售后申请,14BD发起售后申请,15系统发起售后申请,16开放平台发起售后申请,17商家终审通过,18退款成功
  int32 log_type = 3;
  //创建时间
  string create_time = 4;
  //更新时间
  string update_time = 5;
  //日志类型文案
  string log_name = 6;
  // 原因
  string reason = 7;
  //渠道名称
  string operate_type_name = 8;
}

//阿闻管家-订单中心-父订单列表请求
message AwenParentOrderListRequest {

  //订单搜索类型:1.订单号;2.外部单号;3.收货人姓名;4.收货人手机号;5.买家手机号;6.店铺名称;7.子订单号;
  int32 search_type = 1;
  //搜索关键字
  string keyword = 2;
  //时间类型:0.下单时间;1.完成时间;
  int32 time_type = 3;
  //下单范围开始时间
  string start_time = 4;
  //下单范围结束时间
  string end_time = 5;
  //商品名称
  string product_name = 6;
  //单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;9.互联网医院;100-线下门店;
  int32 channel_id = 7;
  //订单状态:10.未支付;20101.未接单;20102.已接单;20201.待发货;20202.已发货;20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;
  int32 order_status = 8;
  //销售渠道:1.Android;2.iOS;3.小程序;4.公众号;5.Web;6.其它;7.竖屏;
  int32 sale_channel = 9;
  //订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;15团长开团; 22次卡;23储值卡;999社区团购;
  string order_type = 10;
  //配送方式:1.快递;2.外卖;3.自提;4.同城送;
  int32 delivery_type = 11;
  //阿闻：支付方式:1.支付宝;2.微信;3.美团;4.其他;5.饿了么;6.京东支付;宠物saas这边是： 支付方式:1:现金,2:余额,3:押金,4:标记收款,5:自有微信,6:自有支付宝,7:自有POS：8:挂账,9:其它，10储值卡，11扫码支付 ,12小程序微信
  int32 pay_mode = 12;
  //支付单号
  string pay_sn = 13;
  //当前页码
  int32 page_index = 14;
  //每页行数
  int32 page_size = 15;
  //登录用户所有权限的门店id
  repeated string shopids = 16;
  //用户编号
  string user_no = 17;
  //店铺类型
  int32 app_channel = 18;
  // 商品组合类型0-全部 1-实物实物 2-虚拟虚拟 3-虚拟实物 4-无组合
  int32 combine_type = 19;
  //ip地址
  string ip = 20;
  //ip所属地
  string ip_location = 21;
  //异常订单筛选 0全部  1正常  2异常
  int32 order_filter = 22;
  //自提点
  int32 pickup_station_id = 23;

  //特殊查询 0默认 1团长拼团制拼团
  int32 query_special = 24;
  //团长制拼团id
  int32 order_group_activity_id = 25;
  //异常配送订单筛选 0全部  1正常  2异常
  int32 order_delivery_filter = 26;
  //机构id
  int64 orgid = 27;
  //财务编码
  string financial_code = 28;
}
//阿闻管家-订单中心-父订单列表响应
message DeliveryReportResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated DeliveryReport details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}
//报表数据
message DeliveryReport {
  //子订单号
  string order_sn = 1;
  //第三方订单号
  string old_order_sn = 2;
  //父订单号
  string parent_order_sn = 3;
  //距离（单位米）
  string distance = 4;
  //最终承运商
  string last_delivery = 5;
  //最终承运商配送类型
  int32 last_delivery_type = 6;
  //美团报价
  string mt_price = 7;
  //达达报价
  string dada_price = 8;
  //顺丰报价
  string sf_price = 9;
  //蜂鸟报价
  string fn_price = 10;
  //店铺名称
  string shop_name = 11;
  //订单来源:渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 12;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 13;
  //下单时间
  string create_time = 14;
  //配送方式:1快递 2外卖 3自提
  int32 delivery_type = 15;
  //店铺类型
  int32 app_channel = 16;
  //是否配送异常 0 否  1是
  int32 is_exception_delivery = 17;
  //重量
  string total_weight = 18;
  //第三方店铺ID
  string channel_store_id = 19;
  //门店财务编码
  string shop_id = 20;

}
//阿闻管家-订单中心-父订单列表响应
message AwenParentOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated SimpleOrderList details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}
//订单列表实体
message SimpleOrderList {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //原电商父订单号
  string old_order_sn = 3;
  //拆单前父订单号
  string parent_order_sn = 4;
  //订单状态：0已取消,10(默认)未付款,20已付款,30已完成
  int32 order_status = 5;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 6;
  //商户或门店id(财务编码)
  string shop_id = 7;
  //商户名称
  string shop_name = 8;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 9;
  //仓库代码
  string warehouse_code = 10;
  //仓库名称
  string warehouse_name = 11;
  //收件人
  string receiver_name = 12;
  //收件省
  string receiver_state = 13;
  //收件市
  string receiver_city = 14;
  //收件区
  string receiver_district = 15;
  //收件地址
  string receiver_address = 16;
  //收件电话
  string receiver_phone = 17;
  //收件手机
  string receiver_mobile = 18;
  //订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送
  int32 order_type = 19;
  //渠道id:1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,100宠物saas线下门店
  int32 channel_id = 20;
  //创建时间
  string create_time = 21;
  //预计送达时间
  string expected_time = 22;
  //完成时间
  string confirm_time = 23;
  //业绩所属员工
  string performance_staff_name = 24;
  //业绩操作时间
  string performance_operator_time = 25;
  //备货状态
  int32 is_picking = 26;
  //是否推送美团配送1是 0否
  int32 push_delivery = 27;
  //推送美团配送失败原因
  string push_delivery_reason = 28;
  //是否推送子龙或全渠道成功1是0否
  int32 push_third_order = 29;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 30;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string logistics_code = 31;
  //是否订单调整，1是0否
  int32 is_adjust = 32;
  //配送类型,1快递,2外卖,3自提,4同城送
  int32 delivery_type = 33;
  //是否显示 部分退款按钮 1：显示 0不显示
  int32 is_part_button = 34;
  //商品
  repeated SimpleOrderProductList order_product_model = 35;
  //脚印
  repeated Footmark foot_mark_model = 36;
  //支付时间
  string pay_time = 37;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 38;
  //拆分订单结果,0拆单中1成功2失败
  int32 split_order_result = 39;
  //拆分订单失败原因
  string split_order_fail_reason = 40;
  //取货码
  string pickup_code = 41;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 42;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 43;
  //买家留言
  string buyer_memo = 44;
  //骑手送回商品配送单号
  int64 goods_return_delivery_id = 45;
  //是否新老顾客，默认老顾客，1-新顾客
  int32 is_new_customer = 46;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 47;
  //总运费金额(分)
  int32 freight = 48;
  // 包装费(分)
  int32 packing_cost = 49;
  // 服务费(分)
  int32 service_charge = 50;
  // 商家预计收入
  int32 actual_receive_total = 51;
  //会员id
  string member_id = 52;
  //会员手机号
  string member_tel = 53;
  // 是否显示 修改货号 按钮
  int32 is_show = 54;
  //所属大区
  string bigregion = 55;
  //自提点名称
  string pickup_station_name = 56;
  //自提点详细地址
  string pickup_station_address = 57;
  // 团长拼团制团id
  int32 order_group_activity_id = 58;
  // 团长拼团制团长会员id
  string order_group_member_id = 59;
  // 业绩归属员工id
  string shop_dis_member_id = 60;
  // 团长信息
  SimpleOrderGroupActivity group_activity_model = 61;
  // 业绩归属
  SimpleOrderShopDis shop_dis_model = 62;
  //团长代收时团员的联系姓名
  string group_member_receiver_name = 63;
  //团长代收时团员的联系手机
  string group_member_receiver_mobile = 64;
  //团长代收时团员的地址
  string group_member_receiver_address = 65;
  //分销门店id
  int32 shop_dis_chain_id = 66;
  //业绩归属人所属门店编码
  string performance_operator_name = 67;
  //业绩归属人所属门店编码
  string performance_finance_code = 68;
  //业绩归属人所属门店名称
  string performance_chain_name = 69;
  //密文收件手机
  string en_receiver_mobile = 70;
  //是否配送异常 0 否  1是
  int32 is_exception_delivery = 71;
  //是否显示发送配货 0 否 1 是
  int32 is_redelivery = 72;
  //退款单号
  string refund_sn = 73;
  //退款单号
  string refund_sn1 = 74;
  // 总优惠金额（单位分）
  int32 privilege = 75;
  // 实际支付
  int32 pay_amount = 76;
  // 配送类型名称
  string logistics_name = 77;
  // 子订单号
  string child_order_sn = 78;
  // 主体id
  int32 org_id = 79;
}

message SimpleOrderGroupActivity {
  //团长名称
  string member_name = 1;
  //团长手机
  string receiver_mobile = 2;
  //团长地址
  string receiver_address = 3;
  //佣金 分
  int32 dis_commission = 4;
  //员工名称
  string staff_name = 5;
  //拼团状态
  int32 status = 6;
  //分销员单位
  string dis_chain_name = 7;
  //团长代收状态 0不代收 1代收
  int32 final_take_type = 8;
  // 团结束时间-成团时间
  string end_time = 9;
  // 成团后n天送达
  int32 deliver_days = 10;
  // 团id
  int64 id = 11;
  // 开团会员id
  string member_id = 12;
  // 加密手机号
  string en_receiver_mobile = 13;
}

message SimpleOrderShopDis {
  //业绩所属人
  string staff_name = 1;
  //业绩所属单位
  string chain_name = 2;
  //财务编码
  string finance_code = 3;
  //业绩所属人所在城市
  string member_area_name = 4;
}

//订单列表商品集合实体
message SimpleOrderProductList {
  //主键id
  string id = 1;
  //主订单号
  string order_sn = 2;
  //商品id
  string product_id = 3;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品） 4-服务 5-活体 6-寄养
  int32 product_type = 4;
  //组合商品父级skuid
  string parent_sku_id = 5;
  //商品名称
  string product_name = 6;
  //商品图片
  string image = 7;
  //商品skuid
  string sku_id = 8;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 9;
  //商品原单价
  int32 marking_price = 10;
  //商品优惠单价
  int32 discount_price = 11;
  //商品均摊后实际支付单价
  int32 pay_price = 12;
  //数量
  int32 number = 13;
  //金额(sku实付总金额)
  int32 payment_total = 14;
  //子商品
  repeated SimpleChildOrderProductList child_order_product_model = 15;
  //upc码
  string bar_code = 16;
  //平台补贴
  int32 privilege_pt = 17;
  // 商品规格
  string  specs = 18;
  // 库位
  string  location_code = 19;
  // 购买类型：0 普通购买，1次卡，2赠品   101 买储值卡 102续储值卡 103买次卡 104续次卡
  int32 buy_type = 20;

}
//订单列表商品集合子实体
message SimpleChildOrderProductList {
  //主键id
  string id = 1;
  //主订单号
  string order_sn = 2;
  //商品id
  string product_id = 3;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 4;
  //组合商品父级skuid
  string parent_sku_id = 5;

  //商品名称
  string product_name = 6;
  //商品图片
  string image = 7;
  //商品skuid
  string sku_id = 8;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 9;
  //商品原单价
  int32 marking_price = 10;
  //商品优惠单价
  int32 discount_price = 11;
  //商品均摊后实际支付单价
  int32 pay_price = 13;
  //数量
  int32 number = 14;
  //金额(sku实付总金额)
  int32 payment_total = 15;
  //upc码
  string bar_code = 16;
  //平台补贴
  int32 privilege_pt = 17;
}
//阿闻管家-订单中心-父订单详情响应
message AwenParentOrderBaseDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  ParentCombineOrderDetail order_detail = 4;
}
//父订单详情
message ParentCombineOrderDetail {
  //订单id
  int32 order_id = 1;
  //订单号
  string order_sn = 2;
  //父订单号
  string old_order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //下单时间
  string create_time = 5;
  //支付时间
  string pay_time = 6;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 7;
  //订单来源:渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 8;
  //业绩所属员工
  string performance_staff_name = 9;

  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 10;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 11;
  //配送状态: 0：待调度；20：已接单；30：已取货；50：已送达；99：已取消
  int32 delivery_status = 12;
  //配送方式:1快递 2外卖 3自提
  int32 delivery_type = 13;
  //骑手(从delivery_node表取最后的一个节点的)
  string courier_name = 14;
  //骑手电话(从delivery_node表取最后的一个节点的)
  string courier_phone = 15;

  //收件人
  string receiver_name = 16;
  //收件省
  string receiver_state = 17;
  //收件市
  string receiver_city = 18;
  //收件区
  string receiver_district = 19;
  //收件地址
  string receiver_address = 20;
  //收件电话
  string receiver_phone = 21;
  //收件手机
  string receiver_mobile = 22;
  //配送备注
  string delivery_remark = 23;
  // 商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 24;
  // 运费
  int32 freight = 25;
  // 包装费
  int32 packing_cost = 26;
  // 优惠详情 (商家优惠 + 平台优惠)
  repeated OrderPrivilegeModel order_privilege_model = 27;
  // 服务费(分)
  int32 service_charge = 28;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 29;
  //总优惠金额(分)
  int32 privilege = 30;
  // 用户实付
  int32 user_receive_total = 31;
  // 商家预计收入
  int32 actual_receive_total = 32;
  //是否拣货,0否1是
  int32 is_picking = 33;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string logistics_code = 34;
  //商品
  repeated SimpleOrderProductList order_product_model = 35;
  //脚印
  repeated Footmark foot_mark_model = 36;
  //买家留言
  string buyer_memo = 37;
  //配送id
  int64 delivery_id = 38;
  // 订单来源
  int32 user_agent = 39;
  // 预计送达时间
  string expected_time = 40;
  // 所属门店
  string shop_name = 41;
  string shop_id = 42;
  int32 pay_mode = 43;
  //履约费
  int32 contract_fee = 44;
  //会员id
  string member_id = 45;
  //会员手机号
  string member_tel = 46;
  int32 source = 47;
  //  运费优惠
  int32 freight_privilege = 48;
  // 订单发货记录
  repeated OrderExpress express = 49;
  // 商品实付金额
  int32 goods_pay_total = 50;
  //密文收件手机
  string en_receiver_mobile = 51;
  //会员手机号密文
  string en_member_tel = 52;
  //加密收件电话
  string en_receiver_phone = 53;
  //0美配 1 闪送 2自配 3达达 4蜂鸟 5顺风   默认0
  int32 order_delivery_type = 54;
  //配送id字符串
  string delivery_id_str = 55;
  //是否专配， 0否 1是
  int32 is_zp = 56;
}

//阿闻管家-订单中心-实物订单列表请求
message AwenMaterOrderListRequest {
  //订单搜索类型:1.子订单号;2.父订单号;3.外部单号;4.手机号;
  int32 search_type = 1;
  //搜索关键字
  string keyword = 2;
  //时间类型:0.下单时间;1.完成时间;
  int32 time_type = 3;
  //下单范围开始时间
  string start_time = 4;
  //下单范围结束时间
  string end_time = 5;
  //商品名称
  string product_name = 6;
  //订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;9.互联网医院;
  int32 channel_id = 7;
  //订单状态:1.未接单;2.已接单;3.配送中;4.已送达;5.已取货;6.已完成;7.已取消;
  int32 order_status = 8;
  //订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;999社区团购;
  string order_type = 9;
  //配送方式:1.快递;2.外卖;3.自提;4.同城送;
  int32 delivery_type = 10;
  //支付方式:1.支付宝;2.微信;3.美团;4.其他;5.饿了么;6.京东支付;
  int32 pay_mode = 11;
  //当前页码
  int32 page_index = 12;
  //每页行数
  int32 page_size = 13;
  //登录用户所有权限的门店id
  repeated string shopids = 14;
  //用户编号
  string user_no = 15;
  //店铺类型
  int32 app_channel = 16;
  //ip地址
  string ip = 17;
  //ip所属地
  string ip_location = 18;
  //自提点
  int32  pickup_station_id = 19;
  // 团长拼团id
  int32  order_group_activity_id = 20;
  // 主体id
  int64 org_id =21;
}
//阿闻管家-订单中心-实物订单列表响应
message AwenMaterOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated MaterSimpleOrderList details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}
//订单列表实体
message MaterSimpleOrderList {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //原电商父订单号
  string old_order_sn = 3;
  //拆单前父订单号
  string parent_order_sn = 4;
  //订单状态：0已取消,10(默认)未付款,20已付款,30已完成
  int32 order_status = 5;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 6;
  //商户或门店id(财务编码)
  string shop_id = 7;
  //商户名称
  string shop_name = 8;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 9;
  //仓库代码
  string warehouse_code = 10;
  //仓库名称
  string warehouse_name = 11;
  //收件人
  string receiver_name = 12;
  //收件省
  string receiver_state = 13;
  //收件市
  string receiver_city = 14;
  //收件区
  string receiver_district = 15;
  //收件地址
  string receiver_address = 16;
  //收件电话
  string receiver_phone = 17;
  //收件手机
  string receiver_mobile = 18;
  //订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送
  int32 order_type = 19;
  //渠道id:1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 20;
  //创建时间
  string create_time = 21;
  //预计送达时间
  string expected_time = 22;
  //完成时间
  string confirm_time = 23;
  //业绩所属员工
  string performance_staff_name = 24;
  //业绩操作时间
  string performance_operator_time = 25;
  //备货状态
  int32 is_picking = 26;
  //是否推送美团配送1是 0否
  int32 push_delivery = 27;
  //推送美团配送失败原因
  string push_delivery_reason = 28;
  //是否推送子龙或全渠道成功1是0否
  int32 push_third_order = 29;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 30;
  //是否显示 部分退款按钮 1：显示 0不显示
  int32 is_part_button = 31;
  //商品
  repeated MaterSimpleOrderProductList order_product_model = 32;
  //脚印
  repeated Footmark foot_mark_model = 33;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 34;
  //拆分订单结果,0拆单中1成功2失败
  int32 split_order_result = 35;
  //拆分订单失败原因
  string split_order_fail_reason = 36;
  //取货码
  string pickup_code = 37;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 38;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 39;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 40;
  //买家留言
  string buyer_memo = 41;
  //是否新老顾客，默认老顾客，1-新顾客
  int32 is_new_customer = 42;
  //总优惠金额(分)
  int32 freight = 43;
  // 包装费(分)
  int32 packing_cost = 44;
  // 服务费(分)
  int32 service_charge = 45;
  // 商家预计收入
  int32 actual_receive_total = 46;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string logistics_code = 47;
  //配送类型,1快递,2外卖,3自提,4同城送
  int32 delivery_type = 48;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
  int32 pay_mode = 49;
  //会员id
  string member_id = 50;
  //会员手机号
  string member_tel = 51;
  //自提点名称
  string pickup_station_name = 52;
  //自提点详细地址
  string pickup_station_address = 53;
  // 团长信息
  SimpleOrderGroupActivity group_activity_model = 54;
  // 业绩归属
  SimpleOrderShopDis shop_dis_model = 55;
  //团长代收时团员的联系姓名
  string group_member_receiver_name = 56;
  //团长代收时团员的联系手机
  string group_member_receiver_mobile = 57;
  //团长代收时团员的地址
  string group_member_receiver_address = 58;
  //分销门店id
  int32 shop_dis_chain_id = 59;
  //业绩分销员
  string shop_dis_member_id = 60;
  // 团长拼团制团长会员id
  string order_group_member_id = 61;
  //业绩分配人
  string performance_operator_name = 62;
  //业绩归属人所属门店编码
  string performance_finance_code = 63;
  //业绩归属人所属门店名称
  string performance_chain_name = 64;
  //收件电话加密
  string en_receiver_phone = 65;
  //收件手机加密
  string en_receiver_mobile = 66;
}
//订单列表商品集合实体
message MaterSimpleOrderProductList {
  //主键id
  string id = 1;
  //主订单号
  string order_sn = 2;
  //商品id
  string product_id = 3;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 4;
  //组合商品父级skuid
  string parent_sku_id = 5;

  //商品名称
  string product_name = 6;
  //商品图片
  string image = 7;
  //商品skuid
  string sku_id = 8;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 9;
  //商品原单价
  int32 marking_price = 10;
  //商品优惠单价
  int32 discount_price = 11;
  //商品均摊后实际支付单价
  int32 pay_price = 12;
  //数量
  int32 number = 13;
  //金额(sku实付总金额)
  int32 payment_total = 14;

  //组合商品名
  string parent_product_name = 15;
  //upc码
  string bar_code = 16;
  //平台补贴
  int32 privilege_pt = 17;
}
//阿闻管家-订单中心-实物订单详情响应
message AwenMaterOrderBaseDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  MaterCombineOrderDetail order_detail = 4;
}
//实物订单详情
message MaterCombineOrderDetail {
  //订单id
  int32 order_id = 1;
  //订单号
  string order_sn = 2;
  //父订单号
  string old_order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //下单时间
  string create_time = 5;
  //支付时间
  string pay_time = 6;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 7;
  //订单来源:渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 8;
  //业绩所属员工
  string performance_staff_name = 9;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成;
  int32 order_status = 10;
  //子状态：20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
  int32 order_status_child = 11;
  //配送状态: 0：待调度；20：已接单；30：已取货；50：已送达；99：已取消
  int32 delivery_status = 12;
  //配送方式:1快递 2外卖 3自提
  int32 delivery_type = 13;
  //骑手(从delivery_node表取最后的一个节点的)
  string courier_name = 14;
  //骑手电话(从delivery_node表取最后的一个节点的)
  string courier_phone = 15;
  //收件人
  string receiver_name = 16;
  //收件省
  string receiver_state = 17;
  //收件市
  string receiver_city = 18;
  //收件区
  string receiver_district = 19;
  //收件地址
  string receiver_address = 20;
  //收件电话
  string receiver_phone = 21;
  //收件手机
  string receiver_mobile = 22;
  //配送备注
  string delivery_remark = 23;
  //商品
  repeated MaterSimpleOrderProductList order_product_model = 33;
  //脚印
  repeated Footmark foot_mark_model = 34;
  //父订单号
  string parent_order_sn = 35;
  //积分
  int32 integral = 36;
  //积分类型
  int32 integral_type = 37;
  //买家留言
  string buyer_memo = 38;
  //配送id
  int64 delivery_id = 39;
  // 订单来源
  int32 user_agent = 40;
  // 预计送达时间
  string expected_time = 41;
  // 所属门店
  string shop_name = 42;
  string shop_id = 43;
  //配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string logistics_code = 44;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
  int32 pay_mode = 45;
  //运费
  int32 freight = 46;
  //打包费
  int32 packing_cost = 47;
  //服务费
  int32 service_charge = 48;
  //履约服务费
  int32 contract_fee = 49;
  // 配送费优惠
  int32 freight_privilege = 50;
  // 平台优惠
  int32 pt_privilege = 51;
  // 商家预计收入
  int32 actual_receive_total = 52;
  // 用户实付
  int32 user_receive_total = 53;
  //会员id
  string member_id = 54;
  //会员手机号
  string member_tel = 55;
  // 仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）4 瑞鹏oms
  int32 source = 56;
  // 优惠详情 (商家优惠 + 平台优惠)
  repeated OrderPrivilegeModel order_privilege_model = 57;
  // 订单发货记录
  repeated OrderExpress express = 58;
  // 商品实付金额
  int32 goods_pay_total = 59;
  // 平台配送费优惠
  int32 pt_freight_privilege = 60;
  //收件电话密文
  string en_receiver_phone = 61;
  //收件手机密文
  string en_receiver_mobile = 62;
  //会员手机号密文
  string en_member_tel = 63;
}

//阿闻管家-订单中心-虚拟订单列表请求
message AwenVirtualOrderListRequest {
  //订单搜索类型:1.子订单号;2.父订单号;3.外部单号;4.手机号;
  int32 search_type = 1;
  //搜索关键字
  string keyword = 2;
  //时间类型:0.下单时间;1.完成时间;
  int32 time_type = 3;
  //下单范围开始时间
  string start_time = 4;
  //下单范围结束时间
  string end_time = 5;
  //商品名称
  string product_name = 6;
  //订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;
  int32 channel_id = 7;
  //订单状态:30100已取消,30101待核销,30102部分核销,30103已完成(已核销)
  int32 order_status = 8;
  //订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;
  string order_type = 9;
  //当前页码
  int32 page_index = 10;
  //每页行数
  int32 page_size = 11;
  //登录用户所有权限的门店id
  repeated string shopids = 12;
  //用户编号
  string user_no = 13;
  //支付方式
  int32 pay_mode = 14;
  //ip地址
  string ip = 15;
  //ip所属地
  string ip_location = 16;
  // 拼团拼团id
  int32 order_group_activity_id = 17;
}
//阿闻管家-订单中心-虚拟订单列表响应
message AwenVirtualOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated VirtualSimpleOrderList details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}
//订单列表实体
message VirtualSimpleOrderList {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //原电商父订单号
  string old_order_sn = 3;
  //拆单前父订单号
  string parent_order_sn = 4;
  //订单状态：0已取消,10(默认)未付款,20已付款,30已完成
  int32 order_status = 5;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 6;
  //商户或门店id(财务编码)
  string shop_id = 7;
  //商户名称
  string shop_name = 8;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 9;
  //仓库代码
  string warehouse_code = 10;
  //仓库名称
  string warehouse_name = 11;
  //收件人
  string receiver_name = 12;
  //收件省
  string receiver_state = 13;
  //收件市
  string receiver_city = 14;
  //收件区
  string receiver_district = 15;
  //收件地址
  string receiver_address = 16;
  //收件电话
  string receiver_phone = 17;
  //收件手机
  string receiver_mobile = 18;
  //订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送
  int32 order_type = 19;
  //渠道id:1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 20;
  //创建时间
  string create_time = 21;
  //预计送达时间
  string expected_time = 22;
  //完成时间
  string confirm_time = 23;
  //业绩所属员工
  string performance_staff_name = 24;
  //业绩操作时间
  string performance_operator_time = 25;
  //备货状态
  int32 is_picking = 26;
  //是否推送美团配送1是 0否
  int32 push_delivery = 27;
  //推送美团配送失败原因
  string push_delivery_reason = 28;
  //是否推送子龙或全渠道成功1是0否
  int32 push_third_order = 29;
  //推送子龙或全渠道失败原因
  string push_third_order_reason = 30;

  //是否显示 部分退款按钮 1：显示 0不显示
  int32 is_part_button = 31;
  //商品
  repeated VirtualSimpleOrderProductList order_product_model = 32;
  //脚印
  repeated Footmark foot_mark_model = 33;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 34;
  //拆分订单结果,0拆单中1成功2失败
  int32 split_order_result = 35;
  //拆分订单失败原因
  string split_order_fail_reason = 36;
  //取货码
  string pickup_code = 37;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 38;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 39;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 40;
  //买家留言
  string buyer_memo = 41;
  //会员id
  string member_id = 46;
  //会员手机号
  string member_tel = 47;
  // 团长信息
  SimpleOrderGroupActivity group_activity_model = 48;
  // 业绩归属
  SimpleOrderShopDis shop_dis_model = 49;
  //团长代收时团员的联系姓名
  string group_member_receiver_name = 50;
  //团长代收时团员的联系手机
  string group_member_receiver_mobile = 51;
  //团长代收时团员的地址
  string group_member_receiver_address = 52;
  //分销门店id
  int32 shop_dis_chain_id = 53;
  //业绩分销员
  string shop_dis_member_id = 54;
  // 团长拼团制团长会员id
  string order_group_member_id = 55;
  //业绩分配人
  string performance_operator_name = 62;
  //业绩归属人所属门店编码
  string performance_finance_code = 63;
  //业绩归属人所属门店名称
  string performance_chain_name = 64;
  //收件电话加密
  string en_receiver_phone = 65;
  //收件手机加密
  string en_receiver_mobile = 66;
}
//订单列表商品集合实体
message VirtualSimpleOrderProductList {
  //主键id
  string id = 1;
  //主订单号
  string order_sn = 2;
  //商品id
  string product_id = 3;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 4;
  //组合商品父级skuid
  string parent_sku_id = 5;

  //商品名称
  string product_name = 6;
  //商品图片
  string image = 7;
  //商品skuid
  string sku_id = 8;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 9;
  //商品原单价
  int32 marking_price = 10;
  //商品优惠单价
  int32 discount_price = 11;
  //商品均摊后实际支付单价
  int32 pay_price = 12;
  //数量
  int32 number = 13;
  //金额(sku实付总金额)
  int32 payment_total = 14;

  //组合商品名
  string parent_product_name = 15;
  //upc码
  string bar_code = 16;
  //平台补贴
  int32 privilege_pt = 17;
}
//阿闻管家-订单中心-虚拟订单详情响应
message AwenVirtualOrderBaseDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  VirtualCombineOrderDetail order_detail = 4;
}
//虚拟订单详情
message VirtualCombineOrderDetail {
  //订单id
  int32 order_id = 1;
  //订单号
  string order_sn = 2;
  //父订单号
  string old_order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //下单时间
  string create_time = 5;
  //支付时间
  string pay_time = 6;
  //订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送
  int32 order_type = 7;
  //订单来源:渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 8;
  //业绩所属员工
  string performance_staff_name = 9;
  //收件人
  string receiver_name = 10;
  //收件电话
  string receiver_phone = 11;
  //收件手机
  string receiver_mobile = 12;
  //父订单号
  string parent_order_sn = 13;
  //商品
  repeated VirtualSimpleOrderProductList order_product_model = 14;
  //核销信息
  repeated OrderVerifyCode order_verify_info = 15;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 16;
  //买家留言
  string buyer_memo = 17;
  // 订单来源
  int32 user_agent = 18;
  // 所属门店
  string shop_name = 19;
  string shop_id = 20;
  //支付方式 1：支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
  int32 pay_mode = 21;
  //会员id
  string member_id = 46;
  //会员手机号
  string member_tel = 47;
  //收件电话密文
  string en_receiver_phone = 48;
  //收件手机密文
  string en_receiver_mobile = 49;
  //会员手机号密文
  string en_member_tel = 50;
}
//商品核销信息
message OrderVerifyCode{
  //核销码
  string verify_code = 1;
  //核销码有效期
  string verify_code_expiry_date = 2;
  //核销状态, 0未核销, 1已核销, 2已退款
  int32 verify_status = 3;
  //核销时间
  string verify_time = 4;
  //核销地点财务编码
  string verify_shop = 5;
  //核销地点名
  string verify_shop_name = 6;
  //核销人的用户id
  string verify_member_id = 7;
}

//阿闻管家-订单中心-退货退款列表请求
message RefundOrderInfoRequest {
  //订单搜索类型:1.原订单号;2.退款单号;
  int32 search_type = 1;
  //搜索关键字
  string keyword = 2;
  //下单范围开始时间
  string start_time = 3;
  //下单范围结束时间
  string end_time = 4;
  //退款发起人:1用户 2商家 3客服
  int32 user_type = 5;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款
  int32 refund_state = 6;
  //订单类型:1实物  2虚拟
  int32 order_type = 7;
  //来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店 9互联网医院
  int32 channel_id = 8;
  //登录用户所有权限的门店id
  repeated string shopids = 9;
  //当前页码
  int32 page_index = 10;
  //每页行数
  int32 page_size = 11;
  //用户编号
  string user_no = 12;
  //店铺类型
  int32 app_channel = 13;
  //退款单号
  string refundsn = 14;
  //推送第三方状态 0全部、1正常、2异常
  int32 push_third_state = 15;
  //退款类型:1为仅退款,2为退款退货
  int32 refund_type = 16;
  //机构id
  int64 orgid = 17;
  //财务编码
  string financial_code = 18;
}
message RefundOrderInfoResponse {
  // 错误 码
  int32 code = 1;
  // 信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 商品信息
  repeated RefundOrderInfo data = 4;
  //总条数
  int32 total_count = 5;
}
message RefundOrderInfo {
  //订单编号
  string order_sn = 1;
  //申请类型:1为仅退款,2为退款退货
  int32 refund_type = 2;
  //退款原因
  string refund_reason = 3;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款
  int32 refund_state = 4;
  //退款单号
  string refund_sn = 5;
  //申请时间
  string create_time = 6;
  //渠道id，1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
  int32 channel_id = 7;
  //商户或门店id(财务编码)
  string shop_id = 8;
  //商户名称
  string shop_name = 9;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 10;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 11;
  //仓库代码
  string warehouse_code = 12;
  //仓库名称
  string warehouse_name = 13;
  //是否是虚拟订单，0否1是
  int32 is_virtual = 14;
  //退款金额，单位元
  string refund_amount = 15;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 16;
  // 是否推送第三方 0未推、1已推送
  int32 push_third = 17;
  // 推送第三方失败原因
  string push_third_fail_reason = 18;
  // 能否重新推送
  bool can_re_push = 19;
  //商品
  repeated RefundOrderProductInfo product = 20;
}
message RefundOrderProductInfo {
  //主键id
  int32 id = 1;
  //退款单号
  string refund_sn = 2;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 4;
  //组合商品父级skuid
  string parent_sku_id = 5;
  //商品名称
  string product_name = 6;
  //商品skuid
  string sku_id = 7;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 8;
  //商品原单价
  int32 marking_price = 9;
  //商品原优惠单价
  int32 product_price = 10;
  //sku申请退款数量
  int32 quantity = 11;
  //退款金额(元)
  string refund_amount = 12;
  //sku单件退款金额
  int32 refund_price = 13;
  //退款数量
  int32 refund_num =14;
  //规格
  string spec = 15;
  // 条码
  string barcode = 16;
  // 库位
  string location_code = 17;
}

//退款详情——请求参数
message RetrunOrderDetailRequest {
  //退款单号
  string refund_sn = 1;
  //订单号
  string order_sn = 2;
}
//退款详情——响应
message RetrunOrderDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //退款信息
  RetrunOrderDetail data = 4;
}
//退款详情信息
message RetrunOrderDetail {
  //退款单号
  string refund_sn = 1;
  //创建时间
  string create_time = 2;
  //渠道id:1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 3;
  //收件人
  string receiver_name = 4;
  //收件电话
  string receiver_phone = 5;
  //收件手机
  string receiver_mobile = 6;
  //申请类型:1为仅退款,2为退款退货
  int32 refund_type = 7;
  //退款原因
  string refund_reason = 8;
  //退款金额
  string refund_amount = 9;
  //退还运费
  double freight = 10;
  // 积分
  int64 refund_integral = 11;

  //退货快递名称
  string express_name = 12;
  //退货快递单号
  string express_num = 13;
  //渠道订单号
  string old_order_sn = 14;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款
  int32 refund_state = 15;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 16;
  //订单号
  string order_sn = 17;
  //退款商品集合
  repeated RefundGoods refund_goods_orders = 18;
  //退款脚印
  repeated Footmark foot_log_list = 19;
  //退款日志
  repeated RetrunOrderLog log_list = 20;
  //平台服务费(分)
  int32 service_charge = 21;
  //平台补贴(分)
  int32 allowance = 22;
  //商家结算金额(分)
  int32 merchant_income = 23;
}
//退款商品集合
message RefundGoods {
  //主键ID
  int32 id = 1;
  //订单号
  string order_sn = 2;
  //是否是虚拟商品 0否 1是
  int32 is_virtual = 3;
  //已核销了的数量 仅当是虚拟商品时有效（is_virtual= 0）
  int32 verified_count = 4;
  //商品id
  string product_id = 5;
  //商品名称
  string product_name = 6;
  //单价
  int32 product_price = 7;
  //单价
  int32 marking_price = 8;
  //单价
  int32 refund_price = 9;
  //销售数量
  int32 number = 10;
  //退款金额
  string refund_amount = 11;
  //实付总金额
  int32 payment_total = 12;
  //退货数量
  int32 quantity = 13;
  //入库数量
  int32 tkcount = 14;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 15;
  //第三方sku(A8/子龙编码)
  string third_sku_id = 16;
  //商品skuid
  string sku_id = 17;
  //组合商品sku_ID
  string parent_sku_id = 18;
  //订单号类型 1主订单 2子订单
  int32 order_sn_type = 19;
  //退款子商品
  repeated RefundGoods child_refund_goods = 20;
}
//退款日志
message RetrunOrderLog {
  //日志时间
  string ctime = 1;
  //日志备注
  string reason = 2;
  //退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  string res_type = 4;
  //操作類型
  string operation_type = 5;
  //操作人
  string operationer = 6;
  //通知类型，part：发起部分退款；agree：确认退款；reject：驳回退款；cancelRefund：用户取消退款申请；cancelRefundComplaint：用户取消退款申诉；pushThird：退单单推送第三方；
  string notify_type = 7;

  //用户申请退款时上传的退款图片，多个图片url以英文逗号隔开
  string pictures = 8;
}

//获取订单退款信息有多个退款信息相关
message RetrunOrderListRequest {
  //退款单号
  string order_sn = 1;
}
message RetrunOrderListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //退款信息
  repeated RetrunOrderDetail data = 4;
}

//退款导出
message RefundOrderExport {
  //退款单号
  string refund_sn = 1;
  //子订单号
  string order_sn = 2;
  //父订单号
  string old_order_sn = 3;
  //外部订单号
  string gy_order_sn = 4;
  //退款方式
  int32 refund_type = 5;
  //退款金额
  string refund_amount = 6;
  //快递单号
  string express_num = 7;
  //订单来源
  int32 channel_id = 8;
  //退款状态
  int32 refund_state = 9;
  //创建时间
  string create_time = 10;
  //完成时间
  string update_time = 11;
  //退款商品名
  string product_name = 12;
  //备注
  string refund_remark = 13;
  //财务编码
  string shop_id = 14;
  //店铺名称
  string shop_name = 15;
  //仓库类型(门店类型)
  string category = 16;
  //仓库名称
  string warehouse_name = 17;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 18;
  //店铺类型
  int32 app_channel = 19;
  //订单类型
  string order_type_name = 20;
  //平台服务费(分)
  int32 service_charge = 21;
  //平台补贴(分)
  int32 allowance = 22;
  //商家结算金额(分)
  int32 merchant_income = 23;
  //父订单号
  string parent_order_sn = 24;
  //退款表中的order_sn
  string refund_order_sn = 25;
}


message SplitOrderRefundGoodsResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated SplitOrderRefundGoods order_goods = 4;
}
message SplitOrderRefundGoods {
  //子订单号
  string order_sn = 1;
  //子订单对应商品
  repeated RefundOrderGoodsData refund_order_goods_data = 2;
  //订单退款金额
  float total_refund_amount = 3;
  //是否全部退商品 0不是 1是
  int32 is_all_refund = 4;
}


message UpdateOrderStatusRequest {
  string update_time = 1;
}

message ConfirmGoodsReturnRequest{
  int64 delivery_id = 1;
  string order_sn = 2;
}

message PushIntegralRequest{
  //订单号
  string order_sn = 1;
  //支付金额
  int32 pay_price = 2;
  //支付类型 0：定金 1：尾款
  int32 pay_type = 3;
}

//获取核销码请求参数
message GetVerifyCodesRequest{
  //渠道订单号
  string old_order_sn = 1;
  //阿闻订单号
  string order_sn = 2;
  //会员id
  string member_id = 3;
  //核销状态 -1 所有 0未核销, 1已核销, 2已退款
  int32 verify_status = 4;
  //当前多少页 从1开始 不传默认为1
  int32 PageIndex = 5;
  //每页多少条数据 不传默认15
  int32 PageSize = 6;
  //商品名称
  string product_name = 7;
}
//获取核销码返回参数
message GetVerifyCodesResponse{
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated VerifyCodesInfo data = 4;
  //总的数据条数
  int32 total = 5;
}
//核销码数据
message VerifyCodesInfo {
  //所属阿闻订单号(子订单)
  string order_sn = 1;
  //核销码
  string  verify_code = 2;
  //核销码有效期
  string verify_code_expiry_date = 3;
  //核销状态,-1 所有 0未核销, 1已核销, 2已退款 不传默认为未核销
  int32 verify_status = 4;
  //核销门店的财务编码
  string verify_shop = 5;
  //核销时间
  string verify_time = 6;
  //核销人的用户id（该订单是哪个会员核销的）
  string verify_member_id = 7;
  //订单状态 已取消,10未付款,20已付款,30已完成
  int32 order_status = 8;
  //订单所属会员id（该订单是哪个会员买的）
  string  member_id = 9;
  //订单下单时间
  string create_time = 10;
  //订单支付时间
  string pay_time = 11;
  //订单实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额)单位：分
  int32 total = 12;
  //订单支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支
  int32 pay_mode = 13;
  //商品sku id
  string sku_id = 14;
  //商品均摊后实际支付单价 单位：分
  int32 pay_price = 15;
  //商品图片
  string image = 16;
  //商品名称
  string product_name = 17;
  //商品购买数量
  int32 number = 18;
  //商品third_sku id 第三方货号
  string third_sku_id = 19;
  //总支付金额
  int32 payment_total = 20;
  //市场单价
  int32 marking_price = 21;
}

//v6.0第三方订单数据修复方法入参
message FixThirdRefundDataRequest {
  //0 表示订单拆单与退款拆单都进行 1表示只执行订单拆单 2表示只执行退款拆单
  int32 step = 1;
  //订单外部单号
  string old_order_sn = 2;
  //退款单号
  string refund_sn = 3;
  //开始时间
  string create_time = 4;
}

//v6.0子龙订单重推方法
message ManualRefundToZiLongRequest {
  //0 部分退 1全退
  int32 full_refund = 1;
  //主单号
  string order_sn = 2;
  //退款单号
  string refund_sn = 3;
}


message RefundOrderSnListVo {
  // 主单号order_sn
  string order_sn = 1;

  // 退款单状态
  int32  refund_state = 2;

}


message RefundOrderSnListResp {
  int32 code = 1;
  string msg = 2;
  repeated RefundOrderData  data = 3;
}


message RefundOrderData {
  int64 id = 1;
  // 退款单号
  string refund_sn = 2;
  // 渠道退款单号
  string  old_refund_sn = 3;
  //原始订单号
  string order_sn = 4;
  // old_order_sn
  string old_order_sn = 5;
  // 售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货
  string refund_type_sn = 6;

  string reason_code = 7;
  string refund_remark = 8;
  int32 refund_type = 9;
  string refund_reason = 10;
  string discount_amount = 11;
  string freight = 12;

  string refund_amount = 13;
  string warehouse_code = 14;
  string shop_id = 15;
  string express_name = 16;
  string express_num = 17;
  int32 order_source = 18;
  string trade_code = 19;

  int32 refund_state = 20;
  string apply_op_user_type = 21;
  int32 channel_id = 22;
  int32 is_cancel_order = 23;

  int64 expect_refund_time = 24;

  string create_time = 25;
  string update_time = 26;

  int32 full_refund = 27;
  string del_express_info = 28;
  int32 del_order_from = 29;
  string del_status = 30;

  string del_id = 31;
  int32 app_channel = 32;

}


message RefundOrderPayVo {

  // 主单号 order_sn
  string order_sn = 1;
  //申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  //支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
  string res_type = 2;
  //操作类型
  string operation_type = 3;
  //操作用户
  string operation_user = 4;
  //原因
  string reason = 5;

  // ip地址
  string ip_addr = 6;
  // 用户名称
  string user_name = 7;
  string user_no = 8;
}


//通用返回
message RefundRecordListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //数据
  repeated  RefundRecordList data = 3;

  int32 total = 4;
}



message  RefundRecordList {
  int32 id = 1;
  // 操作类型，操作类型，1部分退款 2 手动退款 3 手动已完成
  int32 operate_type = 2;
  // 订单号
  string order_sn = 3;
  // 退款单号
  string refund_order_sn = 4;
  // 退款金额
  string refund_amount = 5;

  // 退款时间描述
  string order_describe = 6;
  // 退款原因
  string Operate_reason = 7;
  //  操作人
  string user_no = 8;

  string ip_addr = 9;
  //  操作人姓名
  string user_name = 10;
  // 退款状态 1进行中 2 退款失败 3退款成功
  int32 status = 11;

  //退款描述tip
  string detail = 12;
  // 退款参数退款参数
  string record_data = 13;

  string create_time = 14;
  string update_time = 15;
}


message BatchRefundDeliveryFeeRequest{
  string file_url = 1;
  string user_no = 2;
  string user_name = 3;
  string user_ip = 4;
}
message GerRefundDeliveryFeeRequest{
  string keyword = 1;
  int32 page_size = 2;
  int32 page_index = 3;
  int64 org_id = 4;
}
message GerRefundDeliveryFeeResponse{
  int32 total_count = 1;
  repeated RefundOrderDelivery data = 2;
}
message RefundOrderDelivery{
  int64 id = 1;
  //订单号
  string order_sn = 2;
  //父订单号
  string parent_order_sn = 3;
  //支付单号
  string pay_sn = 4;
  //订单配送费金额
  int32 delivery_fee = 5;
  //配送费退款金额
  int32 refund_delivery_fee = 6;
  //退款原因
  string refund_reason = 7;
  //失败原因
  string fail_reason = 8;
  //状态 0处理中 1 退款成功 2 退款失败
  int32 status = 9;
  //创建人编号
  string create_no = 10;
  //创建人姓名
  string create_name = 11;
  //创建人ip
  string create_ip = 12;
  //下单时间
  string place_time = 13;
  //导入时间
  string create_time = 14;
  //订单配送费金额
  string delivery_fee_str = 15;
  //配送费退款金额
  string refund_delivery_fee_str = 16;
}

message ExportRefundDeliveryFeeResponse{
  string file_url = 1;
}

message UpdateOrderSkuReq {
  //订单号
  string order_sn = 1;
  //订单商品列表ID，不是商品ID
  string order_product_model_id = 2;
  //skuid
  string sku_id = 3;
  //A8/子龙编码
  string third_sku_id = 4;
  //用户编号，不用前端传
  string user_no = 5;
}


// 阿闻管家父订单-导出订单数据
message AwenCommunityGroupMemberOrderExport {
  //订单id
  string id = 1;
  //订单号
  string order_sn = 2;
  //原电商父订单号
  string old_order_sn = 3;
  //外部订单
  string gy_order_sn = 4;
  //创建时间
  string create_time = 5;
  //支付单号
  string pay_sn = 6;
  //支付时间
  string pay_time = 7;
  //实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额）
  int32 total = 8;
  //商品总金额（去掉优惠，运费，包装费，服务费等的金额）
  int32 goods_total = 9;
  //总优惠金额(分)
  int32 privilege = 10;
  //支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付
  int32 pay_mode = 11;
  //收件区
  string receiver_district = 12;
  //收件市
  string receiver_city = 13;
  //商户名称
  string shop_name = 14;
  //商户或门店id(财务编码)
  string shop_id = 15;
  //销售渠道,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它,7-竖屏
  int32 user_agent = 17;
  //收件人
  string receiver_name = 18;
  //收件手机
  string receiver_mobile = 19;
  //子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;
  int32 order_status_child = 20;
  // 活动类型
  string activity_type = 21;
  //业绩所属员工姓名
  string performance_staff_name = 22;
  //业绩操作人姓名
  string performance_operator_name = 23;
  //业绩分配时间
  string performance_operator_time = 24;
  //仓库类型(门店类型)
  string category = 25;
  //仓库名称
  string warehouse_name = 26;
  //总运费
  int32 freight = 27;
  //渠道id
  int32 channel_id = 28;
  //实收金额
  int32 actual_receive_total = 29;
  // 订单来源
  int32 order_from = 30;
  //平台服务费
  int32 service_charge = 31;
  //配送方式
  int32 delivery_type = 32;
  //门店支付配送费
  string store_pay_delivery_amount = 33;
  //会员ID
  string member_id = 34;
  //仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
  int32 source = 35;
  //店铺类型
  int32 app_channel = 36;
  // 是否新老客户，默认老客户，1-新客户
  int32 is_new_customer = 37;
  // 商品组合类型0-非组合 1-实物实物 2-虚拟虚拟 3-虚拟实物
  int32 group_type = 38;
  // 商品id
  string product_id = 39;
  //状态
  int32 order_status = 40;
  //退款金额
  string refund_amount = 41;
  //退款状态
  int32 refund_state = 42;
  //退款状态
  string receiver_address = 43;
  string delivery_remark = 44;
  string buyer_memo = 45;
  //预计送达时间
  string expected_time = 46;
  //自提点名称
  string pickup_station_name = 47;
  //自提点详细地址
  string pickup_station_address = 48;
  // 配送费优惠
  int32 freight_privilege = 49;
  // 包装费
  int32 packing_cost = 50;
  // 履约服务费
  int32 contract_fee = 51;
  // 平台补贴 platformPayedAmount
  int32  platform_payed_amount = 52;
  // 团长拼团制团id
  int32 order_group_activity_id = 53;
  // 团长拼团制团长会员id
  string order_group_member_id = 54;
  // 业绩归属员工id
  string shop_dis_member_id = 55;
  // 团长信息
  SimpleOrderGroupActivity group_activity_model = 56;
  // 业绩归属
  SimpleOrderShopDis shop_dis_model = 57;
  //团长代收时团员的联系姓名
  string group_member_receiver_name = 58;
  //团长代收时团员的联系手机
  string group_member_receiver_mobile = 59;
  //团长代收时团员的地址
  string group_member_receiver_address = 60;
  //分销门店id
  int32 shop_dis_chain_id = 61;
  // 父订单号
  string parent_order_sn = 62;
  //业绩归属人所属门店编码
  string performance_finance_code = 63;
  //业绩归属人所属门店名称
  string performance_chain_name = 64;
  //团长代收时团员的联系手机
  string group_member_en_receiver_mobile = 65;
  //收件手机密文
  string en_receiver_mobile = 66;
}

//校验互联网医疗订单能否下单
message DigitalHealthOrderCheckRequest {
    string consult_order_sn = 1;
}
message DigitalHealthOrderCheckResponse {
    int32 isHad = 1;
}

message OrderExpress {
  //快递单号
  string express_no = 1;
  //商品数量
  int32 num = 2;
  //快递名称
  string express_name = 3;
  //记录id
  int32 id = 4;
}

// 退款单推第三方
message RefundRePushThirdRequest{
  // 退款单号
  string refund_sn = 1;
}

message GetOrderRelationInfoRequest {
  // 子单号
  string order_sn = 1;
  // 父单号，与子单号二选一
  string parent_order_sn = 2;
}

message GetOrderRelationInfoResponse {
  // 会员id
  string member_id = 1;
  // 子订单号
  string order_sn = 2;
  // 订单商品信息
  repeated GetOrderRelationInfoOrderProduct order_product = 3;
}
message GetOrderRelationInfoOrderProduct {
  string sku_id = 1;
  string product_id = 2;
}

message OrderSetDeliveryRequest{
  //订单号(主单号)
  string order_sn = 1;
  //快递公司代码
  string express_code =2;
  //物流编号
  string shipping_code = 3;
  //配送员手机号码
  string courier_phone = 4;
  //配送员名称
  string courier_name = 5;
}

message ConfirmDeliveredOrderRequest{
  //订单号(主单号)
  string order_sn = 1;
}


message QueryMallVirtualOrderExtendInfoRequest {
  string order_sn = 1;
}

message QueryMallVirtualOrderExtendInfoResponse {
  //状态码 200 成功 非200失败
  int32 code = 1;
  //错误消息
  string message = 2;
  //订单信息
  Data data = 4;

  message Data {
    int64 order_id = 1;
    //订单号
    string order_sn = 2;
    //支付时间
    string payment_time = 3;
    //当前核销截止日期
    string vr_indate = 4;
    //最长可延期兑换时间到
    string max_vr_indate = 5;
    // 阿闻订单号
    string erp_order_sn = 6;
    // 订单时间
    string add_time = 7;
  }
}

message ExtendMallVirtualOrderVerifyCodeExpiryDateRequest{
  //订单号
  string order_sn = 1;
  //延长的时间
  string date = 2;
  //操作用户 前端不用穿
  string user_no = 3;
  //操作用户 前端不用穿
  string user_name =4;
}

message QueryMallVirtualOrderWriteOffCodesRequest {
  // SCRM会员编码
  string scrm_user_id = 1;
  // 当前页
  int32  page_index = 2;
  // 每页记录数量
  int32 page_size = 3;
  // 订单号
  string vr_order_sn = 4;
  // 类型 不传默认全部，1、储值卡
  int32 type = 5;
}

message QueryMallVirtualOrderWriteOffCodesResponse {
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误描述
  string message = 2;
  //错误信息
  string error = 3;
  //总记录数
  int64  total = 4;
  //列表数据
  repeated Data data = 5;
  message Data {
    //订单编号
    string order_sn = 1;
    //sku_id
    int64 goods_id = 2;
    //商品名称
    string goods_name = 3;
    //商品价格
    float goods_price = 4;
    //商品数量
    int32 goods_num = 5;
    //兑换码
    string vr_code = 6;
    //实付金额
    float pay_price = 7;
    //过期时间戳(秒)
    int64 vr_indate = 8;
    //支付时间(秒)
    int64 payment_time = 9;
  }
}

message OrderPrescribeSkuNum {
  // sku_id
  int32 sku_id = 1;
  // 数量
  int32 num = 2;
}

message OrderPrescribeCheckReq {
  // 前端不需要传
  string scrm_id = 1;
  // 门店财务编码
  string finance_code = 2;
  // 药品信息
  repeated OrderPrescribeSkuNum skus = 3;
}

message OrderPrescribeCheckRes {
  // 状态码 200成功 非200失败
  int32 code = 1;
  // 错误描述
  string message = 2;
  message Data {
    // 药品信息，如果存在药品且没有处方单号，则表示可以开处方
    repeated OrderPrescribeSkuNum skus = 3;
    // 处方单号
    string consult_order_sn = 4;
  }
  Data data = 3;
}

message OrderPrescribeRes {
  // 状态码 200成功 非200失败
  int32 code = 1;
  // 错误描述
  string message = 2;
  // 处方单号
  string consult_order_sn = 4;
}

message OrderPrescribeReq{
  // 门店财务编码
  string finance_code = 1;
  // 门店名称
  string hospital_name = 2;
  // 宠物重量
  string pet_weight = 3;
  // 诊断信息
  repeated PrescriptionDiagnose diagnose = 4;
  // 阿闻卖药单的处方用户宠物信息 v4.9
  ConsultMemberPetInfo pet_info = 6;

  // 药品信息
  repeated OrderPrescribeSkuNum skus = 7;
}

// 宠物信息
message ConsultMemberPetInfo   {
  // 用户id
  string member_id = 1;
  // 用户名称
  string member_name = 2;
  // 用户头像
  string member_avatar = 3;
  // 用户手机号
  string member_mobile = 4;
  // 宠物id
  string pet_id = 5;
  // 宠物头像
  string pet_avatar = 6;
  // 宠物名称
  string pet_name = 7;
  // 宠物分类
  string pet_kindof = 8;
  // 宠物种类
  string pet_variety = 9;
  // 宠物生日
  string pet_birthday = 10;
  // 宠物年龄
  string pet_age = 11;
  // 是否绝育
  int32  pet_neutering = 12;
  // 宠物性别
  int32  pet_sex = 13;
  // 宠物种类code
  string pet_variety_code = 14;
  // 宠物分类类code
  string pet_kindof_code = 15;
}

// 处方诊断
message PrescriptionDiagnose {
  //病种编号
  string disease_code = 4;
  //诊断内容
  string diagnose_content = 5;
  // 是否拟：1确定 2不确定，写死1
  int64 is_sure = 2;
  // 位置：0-无 1-左、2-右、3-上、4-下、5-单侧、6-双侧，写死0
  int64 position = 3;
}

// 处方里开的药
message PrescriptionMedicine  {
  // 处方id
  string prescription_code = 1;
  // 组名
  string group_name = 2;
  // 药名编号
  int64 medicine_id = 3;
  // 投药方式编号
  string dosing_mode_code = 4;
  // 投药方式
  string dosing_mode = 5;
  // 投药单位编号
  string dosing_unit_code = 6;
  // 投药单位
  string dosing_unit = 7;
  // 用量
  float dosage = 8;
  // 投药频率编号
  string dosing_freq_code = 9;
  // 投药频率
  string dosing_freq = 10;
  // 投药天数
  int64 dosing_days = 11;
  // 药总量
  float drug_total = 12;
  // 下单数量
  int64 sale_num = 17;
  // 出入库单位
  string sell_unit = 18;
  // 备注
  string remark = 13;
  //商品skuid
  int32 sku_id = 14;
}


message MytOrderListRequest {
  //麦芽田店铺ID
  string shop_id = 1;               // Platform shop ID
  int64 start_time = 2;             // Start time in milliseconds
  int64 end_time = 3;               // End time in milliseconds
  int32 page = 4;                   // Page number (default is 1)
  int32 page_size = 5;              // Number of items per page (default is 100)
  //订单号
  string order_id = 6;              //
}

message MytBaseDataResponse {
  //code
  int32 code = 1;
  //消息提示
  string message = 2;
  //数据内容
  string data = 3;
}
package tasks

import "testing"

func Test_riskOrderNotify(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := riskOrderNotify(); (err != nil) != tt.wantErr {
				t.<PERSON><PERSON>rf("riskOrderNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_riskUserAutoUnlock(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := riskUserAutoUnlock(); (err != nil) != tt.wantErr {
				t.Errorf("riskUserAutoUnlock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

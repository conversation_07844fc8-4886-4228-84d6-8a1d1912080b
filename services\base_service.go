package services

import (
	"context"
	"encoding/json"
	"order-center/models"
	"runtime"

	_ "github.com/go-sql-driver/mysql"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type BaseService struct {
}

func initqqd() {
	appkeyCon = config.GetString("qqd.appkey")
	appsecretCon = config.GetString("qqd.appsecret")
	token = config.GetString("qqd.token")
	apiUrl = config.GetString("qqd.apiUrl")
	goodsurl = config.GetString("qqd.goodsUrl")
	systemid = config.GetString("Systemid")

	appkeyConNew = config.GetString("qqd.new.appkey")
	appsecretConNew = config.GetString("qqd.new.appsecret")
	tokenNew = config.GetString("qqd.new.token")
}

// 获取datacenter服务客户端
func (b *BaseService) GetPayCenterClient() *grpc.ClientConn {
	address := config.GetString("grpc.payCenter")
	if address == "" || runtime.GOOS == "windows" {
		address = "127.0.0.1:7036"
	}
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

func (b *BaseService) LoadLoginUserInfo(ctx context.Context) *models.LoginUserInfo {
	var userInfo models.LoginUserInfo
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if err := json.Unmarshal([]byte(md.Get("login_user_info")[0]), &userInfo); err != nil {
			glog.Error(err)
		}
	} else {
		glog.Error("grpc context 加载用户登录信息失败")
	}
	// 如果是宠物saas 过来的， userInfo.ScrmId代表的是线下门店端用户id
	if userInfo.ScrmId != "" {
		return &userInfo
	} else {
		if userInfo.UserName == "" {
			return nil
		} else {
			return &userInfo
		}
	}
}

func (b *BaseService) LoadGrpcContext(ctx context.Context) *models.GrpcContext {
	var GrpcContext models.GrpcContext
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &GrpcContext); err != nil {
			glog.Error(err)
		}
	} else {
		_, str, isOk := metadata.FromOutgoingContextRaw(ctx)
		if isOk {
			if err := json.Unmarshal([]byte(str[0][1]), &GrpcContext); err != nil {
				glog.Error(err)
			}
		} else {
			glog.Error("grpc context 加载用户登录信息失败")
		}

	}

	return &GrpcContext
}

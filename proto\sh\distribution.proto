syntax = "proto3";
package sh;

import "google/protobuf/wrappers.proto";

service DistributionService{
  // 商品分类
  rpc Categories (DisCategoriesRequest) returns (DisCategoriesResponse);
  // 分销商品spu
  rpc SpuList (DisSpuListRequest) returns (DisSpuListResponse);
  // 分销商品spu详情
  rpc SpuDetail (DisSpuDetailRequest) returns (DisSpuDetailResponse);
  // spu更新（包括 推荐/取消推荐到首页）
  rpc SpuUpdate (DisSpuUpdateRequest) returns (DisResponse);
  // spu操作日志
  rpc SpuLogs (DisSpuLogsRequest) returns (DisSpuLogsResponse);
  // sku更新（包括 添加分销、取消分销、推广文案变更、佣金变更）
  rpc SkuUpdate(DisSkuUpdateRequest) returns (DisResponse);
  // 非分销sku列表
  rpc NotDisSkuList(DisNotDisSkuListRequest) returns(DisNotDisSkuListResponse);
  // 下载导入模板
  rpc ImportTemplate(DisImportTemplateRequest) returns(DisImportTemplateResponse);
  // 批量导入
  rpc Import(DisImportRequest) returns(DisResponse);
  // 导入历史
  rpc ImportList(DisImportListRequest) returns(DisImportListResponse);

  // 限时佣金活动新增、修改
  rpc DisLimitActivityOperate(DisLimitActivityOperateRequest) returns (DisResponse);
  // 限时活动列表
  rpc DisLimitActivityList(DisLimitActivityListRequest) returns (DisLimitActivityListResponse);
  // 限时佣金/活动新增/失效
  rpc DisLimitActivityStop(DisLimitActivityStopRequest) returns (DisResponse);
  // 限时佣金活动商品列表
  rpc DisLimitActivityGoodsList(DisLimitActivityGoodsListRequest) returns (DisLimitActivityGoodsListResponse);
  rpc DisLimitActivityGoodsDelete(DisLimitActivityGoodsDeleteRequest) returns (DisResponse);
  rpc DisLimitActivityCommissionSet(DisLimitActivityCommissionSetRequest) returns (DisResponse);
  rpc DisLimitActivityLog(DisLimitActivityLogRequest) returns (DisSpuLogsResponse);
  rpc DisLimitActivityGoodsImport(DisLimitActivityGoodsImportRequest) returns (DisResponse);


  rpc DisSetGlobalCommission(DisSetGlobalCommissionRequest) returns (DisResponse);
  rpc DisGlobalCommission(EmptyRequest) returns (DisGlobalCommissionResponse);
  rpc DisGoodsList(DisGoodsListRequest) returns (DisGoodsListResponse);
  rpc ExportNoDisGoods(google.protobuf.Int32Value) returns (DisImportDownloadResponse);


}

message DisResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message EmptyRequest {
}

message DisCategoriesRequest {
  // 上级分类id，第一级不传或者0
  int32 parent_id = 1;
}

message DisCategoriesResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Category{
    // 分类id
    int32 id = 1;
    // 分类名称
    string name = 2;
    // 下级数量
    int32 children_count = 3;
  }

  repeated Category data = 3;
}

message DisSpuListRequest{
  // 页码，不传默认为1
  int32 page = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
  // 最小佣金
  float min_rate = 3;
  // 最大佣金
  float max_rate = 4;
  // 商品名称
  string goods_name = 5;
  // 商品sku
  string sku_id = 6;
  // 商品spu
  string spu_id = 7;
  // 3级分类id
  int32 category_id = 8;
  // 是否首页推荐，0全部、1否、2是
  int32 is_recommend = 9;
  // 主体：1-默认，2-极宠家
  int32 org_id = 10;
}

message DisSpuListData {
  // 商品spu
  int32 spu_id = 1;
  // 图片地址
  string image_url = 2;
  // 商品名称
  string name = 3;
  // 商品价格
  float price = 4;
  // 商品分类
  string category = 5;
  // 添加时间
  string add_time = 6;
  // 是否首页推荐，0不是、1是
  int32 is_recommend = 7;
  // 首页排序
  int32 sort = 8;
}

message DisSpuListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 商品Spu数据
  repeated DisSpuListData data = 8;
  // 总数
  int32 total = 9;
}

message DisSpuDetailRequest{
  // 商品spu
  int32 spu_id = 1;
  // 页码，不传默认为1
  int32 page = 2;
  // 每页数量，不传默认10
  int32 page_size = 3;
  //主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
  int32 org_id = 4;
}

message DisSpuDetailResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Sku {
    // 商品sku
    int32 sku_id = 1;
    // 图片链接
    string image_url = 2;
    // 规格值
    string spec_name = 3;
    // 当前佣金
    float commission_rate = 4;
    // 日常佣金
    float normal_commission_rate = 5;
    // 推广文案
    string write = 6;
  }

  // spu信息
  DisSpuListData spu = 3;
  // skus
  repeated Sku skus = 4;
  // sku总数
  int32 total = 5;
}

message DisSpuUpdateRequest {
  // 商品spu
  int32 spu_id = 1;
  // 是否首页推荐，true或者false（不更新不要传）
  google.protobuf.BoolValue is_recommend = 2;
  // 首页排序（不更新不要传）
  google.protobuf.Int32Value sort = 3;
}

message DisSpuLogsRequest{
  // 商品spu
  int32 spu_id = 1;
  // 页码，不传默认为1
  int32 page = 2;
  // 每页数量，不传默认10
  int32 page_size = 3;
}

message DisSpuLogsResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message SpuLog {
    // 操作人
    string user_name = 1;
    // 操作时间
    string created_at = 2;
    // 操作内容
    string content = 3;
  }

  // 操作日志
  repeated SpuLog data = 3;
  // sku总数
  int32 total = 4;
}

message DisSkuUpdateRequest{
  // 商品sku
  int32 sku_id = 1;
  // 推广文案（不更新不要传）
  google.protobuf.StringValue write = 2;
  // 日常佣金（不更新不要传）
  google.protobuf.FloatValue normal_commission_rate = 3;
  // 是否分销，true 添加分销、false 取消分销（不更新不要传）
  google.protobuf.BoolValue is_dis = 4;
  //主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
  int32 org_id = 5;
}

message DisNotDisSkuListRequest{
  // 商品sku
  string sku_id = 1;
  // 商品spu
  string spu_id = 2;
  // 商品名称
  string name = 3;
  // 页码，不传默认为1
  int32 page = 4;
  // 每页数量，不传默认10
  int32 page_size = 5;
  //主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
  int32 org_id = 6;
}

message DisNotDisSkuListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Sku {
    // 商品sku
    int32 sku_id = 1;
    // 图片地址
    string image_url = 2;
    // 商品名称
    string name = 3;
    // 商品售价
    float price = 4;
  }

  repeated Sku data = 3;
  // 总数
  int32 total = 4;
}

message DisImportTemplateRequest {
  // 1批量导入分销商品(默认)、2批量导入限时佣金商品、3批量秒杀商品模板
  int32 type = 1;
}

message DisImportTemplateResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 文件字节流
  bytes template = 3;
}

message DisImportListRequest{
  // 页码，不传默认为1
  int32 page = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
  // 1批量导入分销商品（默认）、2批量导入限时佣金商品、3 批量秒杀商品模板
  int32 type = 3;
  // 针对type=2时对应的活动id
  int32 type_id = 4;
}

message DisImportListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List {
    // 导入id，下载使用
    int32 id = 1;
    // 导入时间
    string created_at = 2;
    // 导入结果
    string result = 3;
    // 导入结果url
    string result_url = 4;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message DisImportRequest {
  // 文件字节流
  bytes file = 1;
  // 导入类型:1批量导入分销商品（默认）、2批量导入限时佣金商品、3 批量秒杀商品模板
  int32 type =2;
  // 活动id
  int32 id =3;
  //主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
  int32 org_id = 4;
}

message DisImportDownloadResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 文件字节流
  bytes file = 3;
}

// 分销设置全局佣金
message DisSetGlobalCommissionRequest{
  // 默认分销佣金
  float default_commission = 1;
}
// 分销全局佣金获取
message DisGlobalCommissionResponse{
  int32 code = 1;
  string message = 2;
  float default_commission = 3;
}

// 分销商品请求参数
message DisGoodsListRequest{
  // 搜索类型，1-商品名称，2-商品sku
  int32 search_type = 1;
  // 搜索词
  string search_name = 2;
  // 页码
  int32 page = 3;
  // 每页个数
  int32 page_size = 4;
  // 活动id
  int64 activity_id = 5;
  //主体：1-阿闻，2-极宠家，3-福码购 ，4-百林康源
  int32 org_id = 6;
}
// 分销商品响应参数
message DisGoodsListResponse{
  int32 code = 1;
  string message  = 2;
  repeated DisGoodsListData data = 3;
  int64 total = 4;
}
message DisGoodsListData {
  // 商品id
  int64 sku_id = 1;
  // 图片地址
  string goods_image = 2;
  // 商品名称
  string goods_name = 3;
  // 规格
  string spec_name = 4;
  // 价格
  float goods_price = 5;
  // 日常佣金
  float dis_normal_commis_rate = 6;
    // 电商店铺名称
    int64 store_id = 7;
    // 电商店铺名称
    string shop_name = 8;
}

/*************** 限时佣金活动开始 ****************/
// 分销限时佣金活动列表request
message DisLimitActivityListRequest{
  // 活动状态，1-进行中，2-未开始，3-已结束，4-已失效
  int32 status = 1;
  // 搜索类型，1-名称搜索，2-活动id搜索
  int32 search_type = 2;
  // 搜索词
  string search_name = 3;
  // 页码
  int32 page = 4;
  // 每页条数
  int32 page_size = 5;
  //主体id 
  int32 org_id = 6;
}
// 分销限时佣金活动列表Response
message DisLimitActivityListResponse{
  // code码
  int32 code = 1;
  string message  = 2;
  repeated DisLimitActivityListData data = 3;
  int64 total = 4;
}
message DisLimitActivityListData{
  // 活动id
  int64 activity_id = 1;
  // 活动名称
  string name = 2;
  // 活动开始时间
  string start_time = 3;
  // 活动结束时间
  string end_time =4;
  // 最后编辑人
  string last_editor = 5;
  // 活动状态，进行中，未开始，已结束，已失效
  string status =  6;
  // 活动状态，1-进行中，2-未开始，3-已结束，4-已失效
  int32 status_code = 7;
}

// 限时佣金活动新增/修改 request
message DisLimitActivityOperateRequest {
  // 活动id，编辑的时候传大于0的
  int64 activity_id = 1;
  // 活动名称
  string name = 2;
  // 开始时间
  string start_time = 3;
  // 结束时间
  string end_time = 4;
  //主体id 
  int32 org_id = 5;
}

// 限时佣金活动失效 request
message DisLimitActivityStopRequest{
  // 活动id，编辑的时候传大于0的
  int64 activity_id = 1;
  //主体：1-阿闻，2-极宠家，3-福码购 4-百林康源
  int32 org_id = 2;
}

// 活动分销商品列表request
message DisLimitActivityGoodsListRequest{
  // 搜索类型 1-商品名称搜索，2-商品sku搜索
  int32 search_type = 1;
  // 搜索词
  string search_name = 2;
  // 活动id
  int64 activity_id = 3;
  // 页码
  int32 page = 4;
  // 每页条数
  int32 page_size = 5;
  //主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
  int32 org_id = 6;
}
// 活动分销商品列表response
message DisLimitActivityGoodsListResponse{
  int32 code = 1;
  string message = 2;
  DisLimitActivityListData activity_info = 3;
  repeated DisLimitActivityGoodsListData data = 4;
  int64 total = 5;
}
message DisLimitActivityGoodsListData{
  //sku商品id
  int64 sku_id = 1;
  // 图片地址
  string pic = 2;
  // 商品名称
  string goods_name = 3;
  // 日常佣金
  float normal_commission = 4;
  // 活动佣金
  float activity_commission = 5;
  // 电商店铺名称
  int64 store_id = 6;
  // 电商店铺名称
  string shop_name = 7;
}

// 分销限时活动商品删除
message DisLimitActivityGoodsDeleteRequest{
  // 活动id
  int64 activity_id = 1;
  // 商品id
  int64 sku_id = 2;
  //主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
  int32 org_id = 3;
}

// 商品限时佣金批量导入
message DisLimitActivityGoodsImportRequest{
  // 活动id
  int64 activity_id = 1;
  // 字节流
  bytes file = 2;
  //主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
  int32 org_id = 3;
}

// 活动佣金设置，支持批量
message DisLimitActivityCommissionSetRequest{
  // 活动id
  int64 activity_id = 1;
  repeated DisLimitActivityCommissionData activity_commission = 2;
  //主体：1-阿闻，2-极宠家，3-福码购，4-百林康源
  int32 org_id = 3;
}
message DisLimitActivityCommissionData{
  //sku商品id
  int64 sku_id = 1;
  // 活动佣金
  float activity_commission =2;
}

// 限时活动佣金日志
message DisLimitActivityLogRequest{
  // 活动id
  int64 activity_id = 1;
  // 页码
  int32 page = 2;
  // 每页条数
  int32 page_size = 3;
}

// 限时活动导入历史
message DisLimitActivityImportLogRequest{
  // 活动id
  int64 activity_id = 1;
  // 页码
  int32 page = 2;
  // 每页条数
  int32 page_size = 3;
}
message DisLimitActivityImportLogResponse{
  int32 code = 1;
  string message = 2;
  repeated DisLimitActivityImportLogData data = 3;
  int64 total = 4;
}
message DisLimitActivityImportLogData{
  string created_at = 1;
  string result = 2;
  string down_url = 3;
}

// 下载模板
message DisLimitActivityDownTemplateResponse{
  int32 code = 1;
  string message = 2;
  string url = 3;
}

/*************** 限时佣金活动结束 ****************/
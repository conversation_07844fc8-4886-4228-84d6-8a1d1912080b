package models

import (
	"time"
)

type OrderVerifyCode struct {
	Id                   int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn              string    `xorm:"not null default '''' comment('订单编号') index VARCHAR(50)"`
	VerifyCode           string    `xorm:"not null default '''' comment('核销码') index VARCHAR(50)"`
	VerifyCodeExpiryDate time.Time `xorm:"default 'NULL' comment('核销码有效期') DATETIME"`
	VerifyStatus         int32     `xorm:"not null default 0 comment('核销状态, 0未核销, 1已核销, 2已退款') TINYINT(4)"`
	VerifyTime           time.Time `xorm:"default 'NULL' comment('核销时间') DATETIME"`
	VerifyShop           string    `xorm:"not null default '''' comment('核销地点财务编码') VARCHAR(30)"`
	SkuId                string    `xorm:"not null default '''' comment('商品skuId') VARCHAR(50)"`
	ParentSkuId          string    `xorm:"not null default '''' comment('父商品skuId') VARCHAR(50)"`
	GroupItemNum         int32     `xorm:"not null default 0 comment('组合子商品在单份组合中的售卖数量') INT(11)"`
	VerifyMemberId       string    `xorm:"not null default '''' comment('核销人的用户id') VARCHAR(50)"`
	CreateTime           time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime           time.Time `xorm:" default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
}

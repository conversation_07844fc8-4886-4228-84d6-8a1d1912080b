package services

import (
	"errors"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/techoner/gophp"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"strings"
	"time"
)

//商城部分的db查询
//后续如果太多 可以将相关的方法分散到model里去
//@version v2.9.10

//GetUPetGoodsCommonInfo 获取商品的扩展信息
func GetUPetGoodsCommonInfo(commonId int, fields string) (*models.UpetGoodsCommon, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetGoodsCommon)
	if _, err := GetUPetDBConn().Select(fields).Where("goods_commonid=?", commonId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//GetUPetGoodsInfo 获取商品信息
func GetUPetGoodsInfo(goodId int64, fields string, orgId int32) (*models.UpetGoods, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetGoods)
	if _, err := GetUPetDBConn().Select(fields).Where("goods_id=? AND store_id=?", goodId, orgId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//GetUPetVirtualGoodsInfo 获取有效的虚拟商品信息
func GetUPetVirtualGoodsInfo(goodId int64, fields string, orgId int32) (*models.UpetGoods, error) {
	if fields == "" {
		fields = "*"
	}
	now := time.Now().Unix()
	res := new(models.UpetGoods)
	_, err := GetUPetDBConn().
		Select(fields).
		Where("goods_id=? AND is_virtual = 1 AND goods_state = ? AND virtual_indate >= ? AND store_id=?", goodId, models.UPETGOODSSTATE1, now, orgId).
		Get(res)
	if err != nil {
		return res, err
	}
	return res, nil
}

//GetUPetMemberInfo 获取会员信息
//只返回审核通过的 distri_state = 2
func GetUPetMemberInfo(memberId int32, fields string) (*models.UpetMember, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetMember)
	if _, err := GetUPetDBConn().Select(fields).Where("member_id=? AND distri_state = 2", memberId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//GetUPetMemberInfoByScrmUserId
//通过ScrmUserId 获取商城用户的memberId
func GetUPetMemberInfoByScrmUserId(scrmUserId string, fields string) (*models.UpetMember, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetMember)
	if _, err := GetUPetDBConn().Select(fields).Where("scrm_user_id=?", scrmUserId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//根据分销id获取用户信息
func GetUPetMemberInfoByDisId(disId int32, fields string) (*models.UpetMember, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetMember)
	if _, err := GetUPetDBConn().Select(fields).
		Where("member_id = (SELECT member_id FROM upet_dis_goods WHERE distri_id = ? AND distri_goods_state = 1) AND distri_state = 2", disId).
		Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//GetOutMemberInfo 获取外部会员信息
func GetOutMemberInfo(memberId int32) int32 {
	var outChainId int32
	_, err := GetUPetDBConn().Table("upet_distri_outside_member").Select("chain_id").
		Where("member_id=? AND state = 1", memberId).
		Get(&outChainId)
	if err != nil {
		glog.Error(kit.RunFuncName(2), "查询外部医院分销员绑定信息出错", err)
	}
	return outChainId
}

//GetUPetSellerInfo 获取卖家信息
func GetUPetSellerInfo(memberId int32, fields string) (*models.UpetStore, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetStore)
	if _, err := GetUPetDBConn().Select(fields).Where("member_id=?", memberId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//获取门店信息
func GetUPetStoreInfo(storeId int32, fields string) (*models.UpetStore, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetStore)
	if _, err := GetUPetDBConn().Select(fields).Where("store_id=?", storeId).Get(res); err != nil {
		return res, err
	}
	return res, nil
}

//GetUPetConfig
//@version v2.9.10
//获取商城的配置项 后续如果使用的key增多 则将string改为hashmap 避免key过多的问题
func GetUPetConfig(key string) (string, error) {
	var res string
	redisConn := GetRedisConn()
	cacheKey := "upet:config:" + key
	res = redisConn.Get(cacheKey).Val()
	if res == "" { //没有数据则查询数据库
		has, err := GetUPetDBConn().Select("value").Table("upet_setting").Where("name=?", key).Get(&res)
		if err != nil {
			return res, err
		}
		//如果没有数据  则缓存设置为null 避免频繁查询数据库
		if !has {
			redisConn.Set(cacheKey, nil, 24*time.Hour)
		}
		if res != "" {
			//保存一天，因为配置的改动不在平台 而在BBC 所以修改时并不能更新缓存  测试时需要删除该缓存 并告知测试有24小时的缓存
			//后续如果bbc迁移到平台 则可以在修改配置时删除相应的缓存
			redisConn.Set(cacheKey, res, 24*time.Hour)
		}
	}
	return res, nil
}

//GetDisMemberInfo
//下单获取分销者id
//@version   v2.9.10
func GetDisMemberInfo(memberId int32) (*models.DisMemberInfo, error) {
	db := GetUPetDBConn()
	session := db.NewSession()
	defer session.Close()

	disMemberInfo := new(models.DisMemberInfo)

	disUserTime, err := GetUPetConfig("distri_user_time")
	if err != nil {
		return disMemberInfo, err
	}
	intDisUserTime := cast.ToInt64(disUserTime) * 86400
	createTime := time.Now().Unix() - intDisUserTime
	has, err := session.Table("upet_distri_member_temporary_fans").Select("dis_member_id").
		Where("member_id=? AND create_time>=?", memberId, createTime).
		Get(&disMemberInfo.DisMemberId)
	if err != nil {
		return disMemberInfo, err
	}

	//没有临时粉丝
	if !has {
		_, err = session.Table("upet_distri_member_fans").Select("dis_member_id").
			Where("member_id=? AND create_time>=? AND state = 1", memberId, createTime).
			Get(&disMemberInfo.DisMemberId)
		if err != nil {
			return disMemberInfo, err
		}
		//查询外部医院分销员
		disMemberInfo.OutsideMemberId = GetOutMemberInfo(memberId)
	} else {
		//如果存在临时粉丝则删除
		_, err = session.Table("upet_distri_member_temporary_fans").
			Where("member_id=?", memberId).
			Delete(&models.UpetDistriMemberTemporaryFans{})
		if err != nil {
			glog.Error(kit.RunFuncName(2), "删除临时粉丝出错", err)
		}
	}

	//没有查出 直接返回
	if disMemberInfo.DisMemberId == 0 {
		return disMemberInfo, nil
	}

	// 如果是分销员，还要判断是不是客服
	has, err = session.Table("upet_chain_bind").Where("member_id = ?", disMemberInfo.DisMemberId).Exist()
	if err != nil {
		return disMemberInfo, err
	} else if has {
		return new(models.DisMemberInfo), nil
	}

	//检测会员是否存在
	memberInfo, err := GetUPetMemberInfo(disMemberInfo.DisMemberId, "member_id,scrm_user_id,member_mobile,distri_chainid")
	if err != nil {
		return &models.DisMemberInfo{}, err
	}
	if memberInfo.MemberId == 0 {
		return &models.DisMemberInfo{}, nil
	}
	disMemberInfo.MemberMobile = memberInfo.MemberMobile
	disMemberInfo.ScrmUserId = memberInfo.ScrmUserId

	//分销人在member表中已经存在chainID
	if memberInfo.DistriChainid > 0 {
		disMemberInfo.ChainId = memberInfo.DistriChainid
		disMemberInfo.DisType = 1
	} else {
		disMemberInfo.ChainId = GetOutMemberInfo(disMemberInfo.DisMemberId)
		disMemberInfo.DisType = 2
	}

	return disMemberInfo, nil
}

//GetAreaIdByName 更具名称获取省或市的id
func GetAreaIdByName(name string) (areaId int) {
	_, err := GetUPetDBConn().Select("area_id").Table("upet_area").Where("area_name=?", name).Get(&areaId)
	if err != nil {
		glog.Error("GetAreaIdByName-err", err)
	}
	return
	//如果没有数据  则缓存设置为null 避免频繁查询数据库
}

//GetUPetAddress 更具名称获取省或市的id
func GetUPetAddress(id int32, fields string) (*models.UpetAddress, error) {
	if fields == "" {
		fields = "*"
	}
	res := new(models.UpetAddress)
	if _, err := GetUPetDBConn().Select(fields).Where("address_id=?", id).Get(res); err != nil {
		return res, err
	}
	return res, nil
	//如果没有数据  则缓存设置为null 避免频繁查询数据库
}

//GetUPetGoodsSpec upet_goods表中的spec信息的反序列化  输出商品的规格信息
func GetUPetGoodsSpec(specName string, goodsSpec string) (string, error) {
	specNameSli, err := UnSerializeInfo(specName)
	if err != nil {
		return "", errors.New("解码specName失败" + err.Error())
	}
	specValueSli, err := UnSerializeInfo(goodsSpec)
	if err != nil {
		return "", errors.New("解码goodsSpec失败" + err.Error())
	}
	specLen := len(specValueSli)
	specInfo := make([]string, specLen)

	if specLen > 0 && len(specNameSli) == specLen {
		for i, v := range specNameSli {
			specInfo[i] = v + ":" + specValueSli[i]
		}
		return strings.Join(specInfo, ","), nil
	}
	return "", nil
}

// UnSerializeInfo
//反序列化规格以及规格值被php序列化的内容
//输出切片
//仅适用于与一维的序列化
func UnSerializeInfo(value string) (str []string, err error) {
	var usValue interface{}
	if value != "N;" && value != "" {
		usValue, err = gophp.Unserialize([]byte(value))
		if err != nil {
			return
		}

		info, ok := usValue.(map[string]interface{})
		if !ok {
			return []string{}, errors.New("解码序列化信息失败")
		}

		for _, val := range info {
			str = append(str, cast.ToString(val))
		}
	}
	return
}

// UnSerializeAttrInfo
//反序列化规格以及规格值被php序列化的内容
//输出map
//主要处理upet_goods_common表中的goods_attr与 goods_custom字段
func UnSerializeAttrInfo(value string) (resMap map[string]string, err error) {
	var usValue interface{}

	resMap = make(map[string]string)
	if value == "" || value == "N;" {
		return resMap, nil
	}
	usValue, err = gophp.Unserialize([]byte(value))
	if err != nil {
		return
	}

	attrList, ok := usValue.(map[string]interface{})
	if !ok {
		return map[string]string{}, errors.New("解码序列化信息失败")
	}

	for _, attrItem := range attrList {
		attr, ok := attrItem.(map[string]interface{})
		if !ok {
			continue
		}
		var attrName string
		var attrVal string
		for i, v := range attr {
			if i == "name" {
				attrName = cast.ToString(v)
			} else {
				attrVal = cast.ToString(v)
			}
		}
		resMap[attrName] = attrVal
	}
	return
}

//GetStorePlateInfoByID 获取快照页面的模板信息
//来源与php同名方法
// 因为表的内容不由平台维护 所以暂时不使用缓存（缓存由电商维护 电商的缓存与平台的缓存不在相同的地方 内容更新或者缓存更新时 平台无法随着更新 所以实时读库）
func GetStorePlateInfoByID(plateId int) (string, error) {
	var plateContent string
	_, err := GetUPetDBConn().
		Table("upet_store_plate").
		Select("plate_content").
		Where("plate_id=?", plateId).
		Get(&plateContent)

	if err != nil {
		return plateContent, err
	}
	return plateContent, nil

}

func FindUpetChainMap(chainId []int32) (map[int32]*models.UpetChain, error) {
	upetChainMap := make(map[int32]*models.UpetChain)
	if len(chainId) == 0 {
		return upetChainMap, nil
	}
	err := GetUPetDBConn().Table("upet_chain").
		Select("chain_id,chain_name,account_id").
		In("chain_id", chainId).
		Find(upetChainMap)
	return upetChainMap, err
}

// 建立粉丝关系
// scrmUserId 下单会员
// disMemberId 需要关联的分销会员 请保证一定是分销员
func UpdateUpetDisMemberRelation(scrmUserId string, disMemberId int32) (err error) {
	db := GetUPetDBConn()
	session := db.NewSession()
	defer session.Close()

	m := &models.UpetMember{}
	if _, err = session.Table("upet_member").
		Select("member_id,distri_state").
		Where("scrm_user_id = ?", scrmUserId).
		Get(m); err != nil {
		return
	} else if m.MemberId == 0 { //找不到信息不记录
		return
	} else if 1 <= m.DistriState && m.DistriState <= 2 { //在申请中或是分销员不记录
		return
	}
	fans := &models.UpetDistriMemberFans{}
	if _, err = session.Table("upet_distri_member_fans").Select("dis_fans_id").
		Where("member_id = ? AND dis_member_id = ?", m.MemberId, disMemberId).
		OrderBy("dis_fans_id DESC").
		Get(fans); err != nil {
		return
	} else if fans.DisFansId > 0 { //不存在粉丝关系时添加
		fans.State = 1
		fans.CreateTime = cast.ToInt(time.Now().Unix())
		fans.UpdateTime = fans.CreateTime
		if _, err = session.Where("dis_fans_id=?", fans.DisFansId).
			Cols("state", "create_time", "update_time").
			Update(fans); err != nil {
			return
		}
	} else { //存在粉丝关系时延长时间
		fans.MemberId = cast.ToInt(m.MemberId)
		fans.DisMemberId = cast.ToInt(disMemberId)
		fans.State = 1
		fans.CreateTime = cast.ToInt(time.Now().Unix())
		fans.DisType = 1
		if _, err = session.Insert(fans); err != nil {
			return
		}
	}
	return
}

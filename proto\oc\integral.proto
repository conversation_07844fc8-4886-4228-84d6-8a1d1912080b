syntax = "proto3";
package oc;

// @Desc    积分查询请求
// <AUTHOR>
// @Date	2020-08-18
message AwIntegralQueryRequest{
  string StartTime = 1; // 起始时间
  string EndTime = 2; // 结束时间
}

// @Desc   	阿闻实物订单积分请求
// <AUTHOR>
// @Date	2020-08-18
message AwOrderIntegralRequest{
  string OrderSn = 1; // 订单编号集合
}

// @Desc    获取阿闻订单所属部门请求
// <AUTHOR>
// @Date	2020-08-18
message AwOrderStoreRequest{
  string StoreId = 1; // 门店编号集合
}

// @Desc    根据城市查询城市编号
// <AUTHOR>
// @Date	2020-08-18
message GetStoreCodeRequest{
  string CityName = 1; // 城市
}

// @Desc    阿闻积分请求结果响应
// <AUTHOR>
// @Date	2020-08-18
message AwIntegralResultResponse{
  int32   code = 1;    // 状态码
  string  message = 2;    // 消息
  string  jsondata = 3;  // 结果 Json 格式
}

// @Desc    积分服务
// <AUTHOR>
// @Date	2020-08-18
service IntegralService {
  // @Desc      阿闻积分查询
  // <AUTHOR>
  // @Date      2020-08-18
//  rpc AwIntegralQuery (AwIntegralQueryRequest) returns (AwIntegralResultResponse) {}

  // @Desc      获取阿闻实物订单积分
  // <AUTHOR>
  // @Date      2020-08-18
//  rpc AwOrderIntegral (AwOrderIntegralRequest) returns (AwIntegralResultResponse) {}

  // @Desc      获取阿闻订单所属部门信息
  // <AUTHOR>
  // @Date      2020-08-18
//  rpc AwOrderStore (AwOrderStoreRequest) returns (AwIntegralResultResponse) {}

  // @Desc      根据城市查询城市编号
  // <AUTHOR>
  // @Date      2020-08-18
  rpc GetStoreCode (GetStoreCodeRequest) returns (AwIntegralResultResponse) {}

  // @Desc      根据用户id获取用户的积分明细
  // <AUTHOR>
  // @Date      2021-07-21
  rpc GetIntegralListByMemberId (GetIntegralListByMemberIdReq) returns (GetIntegralListByMemberIdRes) {}

  // @Desc      根据用户id获取用户的积分明细
  // <AUTHOR>
  // @Date      2021-07-21
  rpc RecoverUserIntegral (RecoverUserIntegralReq) returns (BaseRes) {}
}
//通用返回
message BaseRes {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message RecoverUserIntegralReq  {
  string order_sn = 1; //原始订单号
  string member_id = 2; //用户id
  string refund_order_sn = 3; //退款单据号
  //主体ID
  int32 OrgId = 4;
}

message GetIntegralListByMemberIdReq  {
  int32 page_index = 1;//分页
  int32 page_size = 2; //分页
  int32 integral_type = 3; //积分类型
  string member_id = 4; //用户id
  int32 org_id = 5; //主体ID
}

message  GetIntegralListByMemberIdRes   {
  int32   code = 1;    // 状态码
  string  message = 2;    // 消息
  int64   integral_total = 3;   // 积分总额
  int64   integral_expired_total = 4; //积分到期总额
  int64      integral_list_count = 5;  //积分列表总数量
  repeated  IntegralList integral_list = 6;        //积分列表数据
  string   integral_expired_date = 7; //积分到期时间
}

message  IntegralList  {
  string  integral_name = 1; //积分添加原因
  int64   integral_count = 2; //积分值
  string  integral_date = 3;   //积分创建时间
}
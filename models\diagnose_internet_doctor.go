package models

import (
	"time"
)

//在线问诊互联网医生表
type DiagnoseInternetDoctor struct {
	//医生编号
	DoctorCode string `json:"doctor_code"`
	//医生姓名
	DoctorName string `json:"doctor_name"`
	//医生手机号
	Mobile string `json:"mobile"`
	//是否在线:0-否，1-是
	OnLine int32 `json:"on_line"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `json:"is_forbidden"`
	//创建日期
	CreateTime time.Time `json:"create_time" xorm:"<-"`
	//最后更新时间
	UpdateTime time.Time `json:"update_time" xorm:"<-"`
}

//是否在线:0-否，1-是
const (
	InternetDoctorIsOnLineNo = iota
	InternetDoctorIsOnLineYes
)

//是否被禁用：0-否，1-是
const (
	InternetDoctorIsForbiddenNo = iota
	InternetDoctorIsForbiddenYes
)

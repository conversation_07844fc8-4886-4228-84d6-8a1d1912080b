package services

import (
	"context"
	"encoding/json"
	"fmt"
	"order-center/proto/oc"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
)

//func TestAfterSaleService_OrderRetrun(t *testing.T) {
//	type fields struct {
//		BaseService BaseService
//	}
//	type args struct {
//		cgx           context.Context
//		retOrderModel *oc.OrderRetrunRequest
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *oc.BaseResponse
//		wantErr bool
//	}{
//		// TODO:
//		{name: "退款插入"},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			a := AfterSaleService{
//				BaseService: tt.fields.BaseService,
//			}
//			app := oc.OrderRetrunRequest{}
//			/*app.OrderId = "27010130207888817"
//			app.RefunType = 2
//			app.ResType = "商家同意退款"
//			app.NotifyType = "apply"
//			app.Pictures = "[\"http://p0.meituan.net/wmcomment/e6afcac2da6da9334cf2e7d428422be229123.jpg\",\"http://p0.meituan.net/wmcomment/1327353701b462f61e3da935f9454ac9222069.jpg\"]"
//			app.Reason = "申请原因"
//			app.OrderFrom = 2
//			app.Status = "已申请"
//			app.Ctime = 1592379818
//			app.Money = "15.3"
//			app.RefundId = "28513738505"*/
//
//			jsonstr := `{"order_id":"27010134086082316","refund_id":"29000313031","ctime":1595478640,"reason":"商家联系我说没货了","money":"0","pictures":"","notify_type":"apply","res_type":"等待处理中","refundGoodsOrders":null,"status":"","apply_op_user_type":"","logistics_info":"","order_from":2,"refun_type":1,"operation_type":"","operationer":"","apply_type":"用户申请退款"}`
//			json.Unmarshal([]byte(jsonstr), &app)
//
//			got, err := a.OrderRetrun(context.Background(), &app)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("AfterSaleService.OrderRetrun() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("AfterSaleService.OrderRetrun() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestAfterSaleService_OrderRetrunGetList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx     context.Context
		request *oc.RetrunOrderListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.RetrunOrderListRequest
		wantErr bool
	}{
		{name: "查询退款信息"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			app := oc.RetrunOrderListRequest{}
			app.OrderSn = "4100000007086444"

			got, err := a.OrderRetrunGetList(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("AfterSaleService.OrderRetrunGetList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AfterSaleService.OrderRetrunGetList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAfterSaleService_OrderRetrunGet(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.OrderRetrunGetRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "查询单个退款信息",
			args: args{
				ctx: context.Background(),
				request: &oc.OrderRetrunGetRequest{
					Refundsn: "50000007166",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			got, err := a.OrderRetrunGet(tt.args.ctx, tt.args.request)
			if err != nil {
				t.Errorf("AfterSaleService.OrderRetrunGet() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestAfterSaleService_MtOrderRefundReject(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *oc.MtOrderRefundRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.ExternalResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "拒绝退款"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			app := oc.MtOrderRefundRequest{}
			//app.OrderId = "2022170064000932"
			//app.Reason = "已和用户电话沟通"
			//app.Refundsn = "40199053627"
			//app.Operationer = "zhanm3"
			//app.RefundGoodsOrders = append(app.RefundGoodsOrders, &oc.RefundGoodsOrder{
			//	Refundorderid: "5e44837bb34b4573b283e72d5590ae7f",
			//	GoodsId:"1019923001",
			//	Quantity:1,
			//	RefundAmount:"0.10",
			//	OcId:"",
			//	FoodName:"测试JD&A8测试商品05",
			//	Spec:"",
			//	RefundPrice:0.1,
			//	BoxPrice:0,
			//	BoxNum:0,
			//	Tkcount:1,
			//	FoodPrice:0.1,
			//	Selnumber:1,
			//	PromotionId:0,
			//})

			strJson := `{"order_id":"2022264548000632","reason":"已和用户电话沟通","refundGoodsOrders":[{"id":"e5fbe380b49c4653ad1e0919b0069568","goods_id":"1019921001","quantity":1,"refund_amount":"0.12","oc_id":"","barcode":"","refundorderid":"234909152c7f46f9b3d9bd428588e971","product_name":"测试JD\u0026A8测试商品04","spec":"","refund_price":0.12,"box_price":0,"box_num":0,"tkcount":0,"product_price":0.12,"selnumber":5,"promotion_id":0}],"operationer":"zhanm3","refund_sn":"30568650"}`
			json.Unmarshal([]byte(strJson), &app)

			got, err := a.MtOrderRefundReject(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("AfterSaleService.MtOrderRefundReject() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AfterSaleService.MtOrderRefundReject() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAfterSaleService_MtOrderRefundAgree(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *oc.MtOrderRefundRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "同意退款",
			args: args{
				ctx: context.Background(),
				//params: &oc.MtOrderRefundRequest{
				//	OrderId:         "3300848792281795490",
				//	Reason:          "终审同意退款",
				//	Refundsn:        "50000135319",
				//	ExternalOrderId: "",
				//},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//jsonByte := []byte(`{"order_id":"27009950503219582","reason":"ty ","refundGoodsOrders":[{"id":"5328","goods_id":"","quantity":1,"refund_amount":"11.55","oc_id":"","barcode":"","refundorderid":"","food_name":"","spec":"","refund_price":0,"box_price":0,"box_num":0,"tkcount":1,"food_price":0,"selnumber":0,"promotion_id":0},{"id":"5329","goods_id":"","quantity":4,"refund_amount":"2.53","oc_id":"","barcode":"","refundorderid":"","food_name":"","spec":"","refund_price":0,"box_price":0,"box_num":0,"tkcount":4,"food_price":0,"selnumber":0,"promotion_id":0},{"id":"5330","goods_id":"","quantity":4,"refund_amount":"6.12","oc_id":"","barcode":"","refundorderid":"","food_name":"","spec":"","refund_price":0,"box_price":0,"box_num":0,"tkcount":4,"food_price":0,"selnumber":0,"promotion_id":0}],"operationer":"lvhb","refundsn":"50000000489","external_order_id":""}`)
			jsonByte := []byte(`{"order_id":"3300848792281795490","refundsn":"50000135319","reason":"终审同意退款","refundGoodsOrders":[{"id":"69730","order_sn":"4100000023985894","is_virtual":0,"verified_count":0,"product_id":"","product_name":"昵趣单支牛油果20g/支","product_price":500,"marking_price":0,"refund_price":0,"number":5,"refund_amount":"5","payment_total":2500,"quantity":1,"tkcount":1,"product_type":1,"third_sku_id":"","sku_id":"1010917001","parent_sku_id":"","order_sn_type":0,"child_refund_goods":null}]}`)
			json.Unmarshal(jsonByte, tt.args.params)

			a := AfterSaleService{}
			got, err := a.MtOrderRefundAgree(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("AfterSaleService.MtOrderRefundAgree() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestAfterSaleService_OrderApplyPartRefund(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.OrderApplyPartRefundRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}

			parma := oc.OrderApplyPartRefundRequest{}
			parmajson := `{"order_id":"27010010565132774","reason":"2","operation_user":"zhanm3","refund_code":"201","food_data":[{"app_food_code":"","count":2,"sku_id":"1020727001","promotion_type":0},{"app_food_code":"","count":1,"sku_id":"1020727001","promotion_type":0}],"refund_type":1}`
			json.Unmarshal([]byte(parmajson), &parma)
			got, err := a.OrderApplyPartRefund(tt.args.ctx, &parma)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderApplyPartRefund() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderApplyPartRefund() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAfterSaleService_RefundOrderServiceList(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *oc.RefundOrderServiceListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{
			name:   "t1",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				in: &oc.RefundOrderServiceListRequest{
					PageIndex: 1,
					PageSize:  5,
					MemberId:  "540571614736509963",
					//OrderSn:   []string{""},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}

			got, err := a.RefundOrderServiceList(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Error(err)
				return
			}
			if got.Code != 200 {
				t.Errorf(got.Message)
			}

			t.Log(got.Data)
		})
	}
}

func TestAfterSaleService_OrderGetPartRefundFoods(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.OrderGetPartRefundFoodsRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "OrderGetPartRefundFoods",
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			in := new(oc.OrderGetPartRefundFoodsRequest)
			in.OrderSn = "4100000013107768"
			in.StoreMasterId = 2
			got, err := a.OrderGetPartRefundFoods(tt.args.ctx, in)
			if err != nil {
				t.Errorf("AfterSaleService.OrderGetPartRefundFoods() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestAfterSaleService_RefundOrderList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.RefundOrderInfoRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "退款管理列表",
			args: args{
				ctx: context.Background(),
				in: &oc.RefundOrderInfoRequest{
					ChannelId:  0,
					PageSize:   10,
					PageIndex:  1,
					OrderType:  0,
					Keyword:    "50000132352",
					SearchType: 2,
					EndTime:    "2022-04-25 23:59:59",
					StartTime:  "2022-04-23 00:00:00",
					//UserNo: "U_MT1FSUB",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			got, err := a.RefundOrderList(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Errorf("RefundOrderList() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestAfterSaleService_OrderRetrunGetDetail(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.RetrunOrderDetailRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "退款订单详情",
			args: args{
				ctx: context.Background(),
				request: &oc.RetrunOrderDetailRequest{
					OrderSn:  "4100000015627534",
					RefundSn: "50000132755",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{}
			got, err := a.OrderRetrunGetDetail(tt.args.ctx, tt.args.request)
			if err != nil {
				t.Errorf("OrderRetrunGetDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncodeBeuty(got))
		})
	}
}

func TestAfterSaleService_FixThirdRefundData(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.FixThirdRefundDataRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试第三方退款数据修复",
			args: args{
				ctx: context.Background(),
				in: &oc.FixThirdRefundDataRequest{
					Step: 1,
					//OldOrderSn: "27010243543748918",
					//RefundSn:   "50000002248",
					CreateTime: "2021-08-12",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{
				CommonService: tt.fields.CommonService,
			}
			got, err := a.FixThirdRefundData(tt.args.ctx, tt.args.in)
			fmt.Println(got, err)
		})
	}
}

func TestAfterSaleService_ManualRefundToZiLong(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.ManualRefundToZiLongRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &oc.ManualRefundToZiLongRequest{
					RefundSn:   "50000014100",
					OrderSn:    "4100000014213414", //如果订单下单推送时推的是主单则订单号使用主单号
					FullRefund: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{
				CommonService: tt.fields.CommonService,
			}
			got, err := a.ManualRefundToZiLong(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ManualRefundToZiLong() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ManualRefundToZiLong() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAfterSaleService_RefundRePushThird(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.RefundRePushThirdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "",
			args: args{in: &oc.RefundRePushThirdRequest{
				RefundSn: "50000778277",
			}},
			fields: fields{CommonService: CommonService{session: GetDBConn().NewSession()}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AfterSaleService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := a.RefundRePushThird(tt.args.ctx, tt.args.in)
			t.Log(gotOut, err)
		})
	}
}

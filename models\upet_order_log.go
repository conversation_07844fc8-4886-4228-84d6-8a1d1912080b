package models

type UpetOrderLog struct {
	LogId         int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	OrderId       int    `xorm:"not null comment('订单id') INT(11)"`
	LogMsg        string `xorm:"default '' comment('文字描述') VARCHAR(150)"`
	LogTime       int64  `xorm:"not null comment('处理时间') INT(10)"`
	LogRole       string `xorm:"not null comment('操作角色') VARCHAR(10)"`
	LogUser       string `xorm:"default '' comment('操作人') VARCHAR(30)"`
	LogOrderstate string `xorm:"comment('订单状态：0(已取消)10:未付款;20:已付款;30:已发货;40:已收货;') ENUM('0','10','20','30','40')"`
}

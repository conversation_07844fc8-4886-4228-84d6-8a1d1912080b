package tasks

import (
	"fmt"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/et"
	"order-center/services"
	"runtime"
	"time"
)

/*
获取美团、饿了么、京东到家的
方案一：每次都跑所有门店
方案二：根据时间查询订单列表，关联的门店信息，再去跑。

各渠道参数：
	饿了么渠道：
		baidu_shop_id String	是	平台门店id	42267022123
		date          String	是	日期，查询传入日期当日账单。格式：10位时间戳。如传入为1632473565，查询为 2021-09-24 00:00:00 ~ 2021-09-24 23:59:59的账单。	1632473565
		page          Integer	是	页码，每页默认20个订单。	1 开始递增
		shop_id       String	是	第三方商户ID（商户自定义id）	S209030001

	美团渠道：
		order_id	  long		是	27061900338318741 订单号，商家可根据订单号查询订单当前的详细信息。注:该字段支持传入商家端打印小票上面的订单号，平台内部会转成对应真实的订单号

	京东到家渠道：
		订单号和门店都可以查询，建议使用订单号查询

*/

func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")
	task := cron.New(cron.WithSeconds())
	task.AddFunc("0 0 10 * * ? ", func() { autoUpdateElmOrderBillInfo("") }) //每天的10点跑
	task.AddFunc("0 0 12 * * ? ", func() { autoUpdateMtOrderBillInfo("") })  //每天的12点跑
	task.AddFunc("0 0 14 * * ? ", func() { autoUpdateJdOrderBillInfo("") })  //每天的14点跑
	task.Start()
}

type StoreAppChannel struct {
	ChannelStoreId string `json:"channel_store_id"`
	AppChannel     int32  `json:"app_channel"`
}

// 自动更新饿了么的对账单信息
func autoUpdateElmOrderBillInfo(param interface{}) {
	u1 := uuid.NewV4()
	logFix := fmt.Sprintf("autoUpdateElmOrderBillInfo =======  操作批次号：%s", fmt.Sprintf("%s", u1))
	//获取T+2之前的对账单信息
	billDate := time.Now().Add(-48 * time.Hour).Unix()
	if _billDate, err := time.ParseInLocation("2006-01-02", param.(string), time.Local); err == nil {
		billDate = _billDate.Unix()
	} else {
		glog.Error(logFix, "时间格式转换失败", err)
	}

	sql := `select b.channel_store_id,a.app_channel from datacenter.store a 
	inner join datacenter.store_relation b on a.finance_code = b.finance_code
	inner join datacenter.store_master c on a.app_channel = c.app_channel
	where c.is_deleted = 0 and b.channel_id = 3 and channel_store_id !='';`

	storeAppChannelList := make([]StoreAppChannel, 0)
	db := services.GetDBConn()
	err := db.SQL(sql).Find(&storeAppChannelList)
	if err != nil {
		glog.Error(logFix, "查询门店渠道等信息失败：", err)
		return
	}
	if len(storeAppChannelList) == 0 {
		return
	}
	glog.Info(logFix, "自动更新饿了么的对账单信息开始", "需要执行的次数：", len(storeAppChannelList))
	for _, channel := range storeAppChannelList {
		elmBillList := make([]*et.ElmBillOrderDetail, 0)
		elmBillList = getElmBillOrderDetailList(channel, 1, billDate, elmBillList)
		glog.Info(logFix, "记录需要更新的数据集合：", kit.JsonEncode(elmBillList))
		for _, bill := range elmBillList {
			glog.Info(logFix, "记录需要更新的数据集合：", kit.JsonEncode(bill))
			db.Table("dc_order.order_detail").Where("order_sn=(select order_sn from dc_order.order_main where old_order_sn = ?)", bill.OrderId).
				Update(map[string]interface{}{"trade_created_time": bill.TradeCreateTime, "trade_payment_time": bill.OrderCreateTime, "trade_time": bill.Date})
		}
	}
	glog.Info(logFix, "自动更新饿了么的对账单信息结束")
}

// 递归获取门店所有的订单
func getElmBillOrderDetailList(spc StoreAppChannel, pageIndex int32, billDate int64, elmBill []*et.ElmBillOrderDetail) []*et.ElmBillOrderDetail {
	externalClient := et.GetExternalClient()
	defer externalClient.Close()
	rpcReq := &et.ElmBillOrderDetailListReq{
		BaiduShopId: spc.ChannelStoreId,
		BillDate:    cast.ToString(billDate),
		Page:        pageIndex,
		ShopId:      spc.ChannelStoreId,
		AppChannel:  spc.AppChannel,
	}
	rpcRes, err := externalClient.ELMORDER.ElmBillOrderDetailList(externalClient.Ctx, rpcReq)
	glog.Info("记录需要更新的数据集合(getElmBillOrderDetailList)：", kit.JsonEncode(rpcReq), kit.JsonEncode(rpcRes), "错误信息：", err)
	if len(rpcRes.List) == 20 {
		elmBill = append(elmBill, rpcRes.List...)
		return getElmBillOrderDetailList(spc, pageIndex+1, billDate, elmBill)
	} else {
		elmBill = append(elmBill, rpcRes.List...)
		return elmBill
	}
}

type OrderAppChannel struct {
	OldOrderSn    string `json:"old_order_sn"`
	OrderSn       string `json:"order_sn"`
	ParentOrderSn string `json:"parent_order_sn"`
	AppChannel    int32  `json:"app_channel"`
}

// 自动更新美团的对账单信息
func autoUpdateMtOrderBillInfo(param interface{}) {
	//获取某个时刻以后得所有订单，循环查询信息
	u1 := uuid.NewV4()
	logFix := fmt.Sprintf("autoUpdateMtOrderBillInfo =======  操作批次号：%s", fmt.Sprintf("%s", u1))
	//获取T+2之前的对账单信息
	billDate := time.Now().Add(-72 * time.Hour)
	if _billDate, err := time.ParseInLocation("2006-01-02", param.(string), time.Local); err == nil {
		billDate = _billDate
	} else {
		glog.Error(logFix, "时间格式转换失败", err)
	}

	billDateStartStr := fmt.Sprintf("%s 00:00:00", billDate.Format("2006-01-02"))
	billDateEndStr := fmt.Sprintf("%s 23:59:59", billDate.Format("2006-01-02"))
	sql := `select a.old_order_sn,c.parent_order_sn,c.order_sn,c.app_channel from dc_order.order_main a
		inner join dc_order.order_main c on a.order_sn = c.parent_order_sn
		inner join dc_order.order_detail b on c.order_sn = b.order_sn
		where a.channel_id = 2 and a.create_time between ? and ?;`
	orderAppChannelList := make([]*OrderAppChannel, 0)
	db := services.GetDBConn()
	err := db.SQL(sql, billDateStartStr, billDateEndStr).Find(&orderAppChannelList)
	if err != nil {
		glog.Error(logFix, "查询需要同步账单的订单详细信息失败：", err)
		return
	}
	glog.Info(logFix, "自动更新美团的对账单信息开始", "需要执行的次数：", len(orderAppChannelList))
	for _, channel := range orderAppChannelList {
		getMtBillOrderDetailList(channel)
	}
	glog.Info(logFix, "自动更新美团的对账单信息结束")
}

// 递归获取门店所有的订单
func getMtBillOrderDetailList(oac *OrderAppChannel) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("getMtBillOrderDetailList 异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()
	externalClient := et.GetExternalClient()
	defer externalClient.Close()
	rpcReq := &et.MtOrderDetailRequest{
		OrderId:       oac.OldOrderSn,
		StoreMasterId: oac.AppChannel,
	}
	rpcRes, err := externalClient.MtOrder.GetMtOrderDetail(externalClient.Ctx, rpcReq)
	glog.Info("记录需要更新的数据集合(getMtBillOrderDetailList)：", kit.JsonEncode(rpcRes), "错误信息：", err)
	mapUpdateField := make(map[string]interface{}, 0)
	if rpcRes.MTOrderSDetail == nil {
		return
	}
	if rpcRes.MTOrderSDetail.OrderCompletedTime > 0 {
		orderCompletedTime := time.Unix(rpcRes.MTOrderSDetail.OrderCompletedTime, 0).Format("2006-01-02 15:04:05")
		mapUpdateField["bill_completed_time"] = orderCompletedTime
	}
	if rpcRes.MTOrderSDetail.OrderCancelTime > 0 {
		orderCancelTime := time.Unix(rpcRes.MTOrderSDetail.OrderCancelTime, 0).Format("2006-01-02 15:04:05")
		mapUpdateField["bill_canceled_time"] = orderCancelTime

	}
	if len(mapUpdateField) > 0 {
		db := services.GetDBConn()
		db.Table("dc_order.order_detail").Where("order_sn=?", oac.ParentOrderSn).
			Update(mapUpdateField)
	}
}

// 自动更新京东的对账单信息
func autoUpdateJdOrderBillInfo(param interface{}) {
	//获取某个时刻以后得所有订单，循环查询信息

	u1 := uuid.NewV4()
	logFix := fmt.Sprintf(
		"autoUpdateJdOrderBillInfo =======  操作批次号：%s", fmt.Sprintf("%s", u1))
	//获取T+2之前的对账单信息
	billDate := time.Now().Add(-48 * time.Hour)
	if _billDate, err := time.ParseInLocation("2006-01-02", param.(string), time.Local); err == nil {
		billDate = _billDate
	} else {
		glog.Error(logFix, "时间格式转换失败", err)
	}
	billDateStartStr := fmt.Sprintf("%s 00:00:00", billDate.Format("2006-01-02"))
	billDateEndStr := fmt.Sprintf("%s 23:59:59", billDate.Format("2006-01-02"))
	sql := `select a.old_order_sn,c.parent_order_sn,c.order_sn,c.app_channel from dc_order.order_main a
		inner join dc_order.order_main c on a.order_sn   =  c.parent_order_sn
		inner join dc_order.order_detail b on c.order_sn = b.order_sn
		where a.channel_id = 4 and a.create_time between ? and ?;`
	orderAppChannelList := make([]*OrderAppChannel, 0)
	db := services.GetDBConn()
	err := db.SQL(sql, billDateStartStr, billDateEndStr).Find(&orderAppChannelList)
	if err != nil {
		glog.Error(logFix, "查询需要同步账单的订单详细信息失败：", err)
		return
	}
	glog.Info(logFix, "自动更新京东的对账单信息开始", "需要执行的次数：", len(orderAppChannelList), "数据集合：", kit.JsonEncode(orderAppChannelList))
	for _, channel := range orderAppChannelList {
		getJdBillOrderDetailList(channel, billDate)
	}
	glog.Info(logFix, "自动更新京东的对账单信息结束")
}

func getJdBillOrderDetailList(oac *OrderAppChannel, billDate time.Time) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("getJdBillOrderDetailList 异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()
	externalClient := et.GetExternalClient()
	defer externalClient.Close()
	rpcReq := &et.JddjGetBalanceBillListReq{
		StoreMasterId:   oac.AppChannel,
		OrderIds:        []int64{cast.ToInt64(oac.OldOrderSn)},
		FinishStartTime: billDate.Format("20060102"),
		FinishEndTime:   billDate.AddDate(0, 0, 2).Format("20060102"),
	}
	rpcRes, err := externalClient.JddjOrder.JddjGetBalanceBillList(externalClient.Ctx, rpcReq)
	glog.Info("记录需要更新的数据集合(getJdBillOrderDetailList--JddjGetBalanceBillList)：", kit.JsonEncode(rpcRes), "错误信息：", err)
	mapUpdateField := make(map[string]interface{}, 0)
	if rpcRes.Data == nil {
		return
	}
	if len(rpcRes.Data.Result) > 0 {
		jdBillData := rpcRes.Data.Result[0]
		if jdBillData.BusinessFinishTime != "" {
			mapUpdateField["bill_completed_time"] = jdBillData.BusinessFinishTime
		}
	}

	_rpcReq := &et.JddjOrderDetailRequest{
		OrderId:       oac.OldOrderSn,
		StoreMasterId: oac.AppChannel,
	}
	_rpcRes, err := externalClient.JddjOrder.GetJddjOrderDetail(externalClient.Ctx, _rpcReq)
	glog.Info("记录需要更新的数据集合(getJdBillOrderDetailList--GetJddjOrderDetail)：", kit.JsonEncode(_rpcRes), "错误信息：", err)
	if len(_rpcRes.JddjData.JddjResult.JddjResultList) > 0 {
		jdDetailData := _rpcRes.JddjData.JddjResult.JddjResultList[0]
		if jdDetailData.OrderCancelTime != "" {
			mapUpdateField["bill_canceled_time"] = jdDetailData.OrderCancelTime
		}
	}

	if len(mapUpdateField) > 0 {
		db := services.GetDBConn()
		if _, err := db.Table("dc_order.order_detail").Where("order_sn=?", oac.ParentOrderSn).Update(mapUpdateField); err != nil {
			glog.Error("更新数据失败:", err, kit.JsonEncode(mapUpdateField), kit.JsonEncode(oac))
		}
	}
}

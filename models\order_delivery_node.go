package models

import (
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"time"
)

type OrderDeliveryNode struct {
	Id             int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	DeliveryId     int64     `xorm:"not null default 0 comment('配送活动标识') BIGINT(30)"`
	OrderSn        string    `xorm:"not null default '''' comment('订单号') VARCHAR(50)"`
	DeliveryStatus int32     `xorm:"not null default 0 comment('状态状态代码，可选值为0：待调度；20：已接单；30：已取货；50：已送达；99：已取消') INT(10)"`
	Content        string    `xorm:"not null default '''' comment('信息') VARCHAR(255)"`
	CourierName    string    `xorm:"not null default '''' comment('配送员') VARCHAR(50)"`
	CourierPhone   string    `xorm:"not null default '''' comment('配送员手机号') VARCHAR(50)"`
	CancelReason   string    `xorm:"not null default '''' comment('取消原因详情') VARCHAR(255)"`
	CreateTime     time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime     time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

// 转换到Dto
func (model *OrderDeliveryNode) ToUpetDjDeliverNodesDto() *oc.UpetDjDeliverNodesDto {
	var dto = new(oc.UpetDjDeliverNodesDto)
	dto.Message = model.Content
	dto.CreateTime = model.CreateTime.Format(kit.DATETIME_LAYOUT)
	return dto
}

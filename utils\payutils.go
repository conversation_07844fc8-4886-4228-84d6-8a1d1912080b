package utils

import (
	"encoding/json"
	"math"
	"net"
	"sort"
	"strconv"
	"strings"

	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"
)

const (
	ContentTypeToForm = "application/x-www-form-urlencoded"
)

var (
	//商户号
	MerchantId = config.GetString("merchant-id") // 生产和测试都是这个：AAECggEAcboPccMCaLS【以前的："AwwOQ0ZDQSBBQ1T0N"】
	//签名密钥
	SecretKey = config.GetString("secret-key") //"5fBgKs5UYD2t11PUzLxQqrRIBDwAwggEKAoIBAQDL2qWFfEVHQ8BAf8EBAMCBs"
	//支付中心地址
	PayCenterUrl = config.GetString("pay-center-url") //"http://api.rp-pet.com:7035"

	SubAppId = config.GetString("sub-app-id") //生产："wx8e6838374dbce594" 测试环境：wx8a769978c9217af6

	RefundPayCallbackUrl = config.GetString("refund-order-pay-callback-url") //external/refund-order/pay

	NotifyUrl = config.GetString("pay-center-notifyurl") //order-api/order/offlinenotify

	GroupNotifyUrl       = config.GetString("group-pay-notifyurl") //order-api/order/pinpaynotify
	StandardPaySecretKey = config.GetString("standardPaySecret")
)

// 支付中心加密,获得加密后的sign
// data = json格式字符串
func PayCenterSign(data string) (string, string) {
	mapInterface := make(map[string]interface{})
	if len(data) > 0 {
		mapInterface = JsonToMap(data)
	}
	// 初始化排序数组
	var array []string
	for key := range mapInterface {
		array = append(array, key)
	}
	// 按照字母编码排序
	sort.Strings(array)
	//拼接签名字符串
	var signStr strings.Builder
	var arrayData []string

	for i := 0; i < len(array); i++ {
		for key, item := range mapInterface {
			if array[i] == key {
				typeData := ""
				switch data := item.(type) {
				case string:
					typeData = data
				case bool:
					typeData = strconv.FormatBool(data)
				case int64:
					typeData = strconv.FormatInt(data, 10)
				case float64:
					isInt := IsInt(data)
					if isInt {
						typeData = strconv.FormatInt(int64(data), 10)
					} else {
						typeData = strconv.FormatFloat(data, 'f', -1, 64)
					}
				case []string:
					mapValue, error := json.Marshal(item)
					if error == nil {
						typeData = string(mapValue)
					}
				case []interface{}:
					mapValue, error := json.Marshal(item)
					if error == nil {
						typeData = string(mapValue)
					}
				case map[string]interface{}:
					mapValue, error := json.Marshal(item)
					if error == nil {
						typeData = string(mapValue)
					}
				case json.Number:
					typeData = string(data)
				}
				if len(typeData) > 0 {
					if i != 0 {
						signStr.WriteString("&")
					}
					signStr.WriteString(key + "=")
					signStr.WriteString(typeData)
					arrayData = append(arrayData, key+"="+typeData)
				}
			}
		}
	}
	//进行加密
	sign := strings.ToUpper(kit.GetMd5(kit.GetMd5(signStr.String()) + SecretKey))
	signStr.WriteString("&sign=" + sign)
	glog.Info("签名参数=======：", signStr.String(), "secketKey:", SecretKey, "sign:", sign)
	//glog.Info(sign)
	return sign, signStr.String()
}

// JsonToMap  Json 转换成 Map
func JsonToMap(jsonStr string) map[string]interface{} {
	var mapResult map[string]interface{}
	dec := json.NewDecoder(strings.NewReader(jsonStr))
	dec.UseNumber()
	dec.Decode(&mapResult)
	return mapResult
}

func IsInt(data float64) bool {
	b := math.Floor(data)
	result := false
	if b == data {
		result = true
	}
	return result

}

// 获取当前Ip
func GetClientIp() string {
	ipAddress := ""
	netInterfaces, err := net.Interfaces()
	if err != nil {
		glog.Error("net.Interfaces failed, err:", err.Error())
		return ipAddress
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) != 0 {
			address, _ := netInterfaces[i].Addrs()

			for _, address := range address {
				if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
					if ipNet.IP.To4() != nil {
						ipAddress = ipNet.IP.String()
						return ipAddress
					}
				}
			}
		}
	}
	return ipAddress

}

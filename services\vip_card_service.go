package services

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"
	"order-center/utils"
	"order-center/utils/vip_card"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/techoner/gophp/serialize"
	"github.com/xuri/excelize/v2"

	"github.com/labstack/gommon/log"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type VipCardOrderService struct {
	BaseService
}

var CycleTypeMap = map[int32]string{1: "年卡", 2: "季卡", 3: "月卡", 4: "周卡", 5: "日卡"}
var CardTypeMap = map[int32]string{1: "付费卡", 2: "试用会员卡"}
var PaymentCodeMap = map[string]string{"": "", "card": "储值卡", "wx_jsapi": "微信", "ali_native": "支付宝"}

// 获得健康会员卡实体卡订单列表
func (v *VipCardOrderService) GetPhysicalVipCardOrderList(ctx context.Context, in *oc.GetPhysicalVipCardOrderListRequest) (out *oc.GetPhysicalVipCardOrderListResponse, e error) {
	logPrefix := fmt.Sprintf("GetPhysicalVipCardOrderList====入参：%s", kit.JsonEncode(in))
	db := GetUPetDBConn()
	session := db.NewSession()
	PhysicalVipCardOrders, total, err := vip_card.GetPhysicalVipCardOrderList(session, in)
	if err != nil {
		return nil, err
	}
	//获取这一页所有的卡号，判断状态是否激活，只有没有激活的才可以显示修改卡号
	cardMap := make(map[int64]int, 0)
	var cardList []int64
	for _, order := range PhysicalVipCardOrders {
		if order.UpetOrders.VirtualCardId > 0 {
			cardList = append(cardList, order.UpetOrders.VirtualCardId)
		}

	}
	vipCards := make([]models.VipCardVirtual, 0)
	err1 := session.In("card_id", cardList).Find(&vipCards)
	if err1 != nil {
		glog.Error(logPrefix, "查询虚拟卡信息出错:", err.Error())
		return nil, errors.New("查询虚拟卡信息出错")
	}
	for _, card := range vipCards {
		if card.Status == 0 {
			cardMap[card.CardId] = 1
		} else {
			cardMap[card.CardId] = 0
		}
	}

	out = &oc.GetPhysicalVipCardOrderListResponse{Total: total}
	out.Data = make([]*oc.PhysicalOrderData, 0, total)
	for _, v := range PhysicalVipCardOrders {
		ReciverInfo, _ := serialize.UnMarshal([]byte(v.UpetOrderCommon.ReciverInfo))
		reciverInfoStr := kit.JsonEncode(ReciverInfo)
		reciverInfoStruct := models.ReciverInfo{}
		if err := kit.JsonDecode([]byte(reciverInfoStr), &reciverInfoStruct); err != nil {
			glog.Error(logPrefix, "解析收获信息失败:", err.Error())
			return nil, errors.New("解析收获信息失败")
		}
		VirtualCardId := ""
		if v.UpetOrders.VirtualCardId > 0 {
			VirtualCardId = fmt.Sprintf("FY%d", v.UpetOrders.VirtualCardId)
		}
		data := oc.PhysicalOrderData{
			OrderSn:           strconv.Itoa(int(v.UpetOrders.OrderSn)),
			PaymentTime:       utils.UnixToTimeFormat(kit.DATETIME_LAYOUT, int64(v.UpetOrders.PaymentTime)),
			OrderFrom:         int32(v.UpetOrders.OrderFrom),
			OrderFromText:     models.UpetOrderFromMap[v.UpetOrders.OrderFrom],
			VirtualCardId:     v.UpetOrders.VirtualCardId,
			VirtualCardIdText: VirtualCardId,
			OrderState:        int32(v.UpetOrders.OrderState),
			OrderStateText:    models.UpetOrderStateMap[v.UpetOrders.OrderState],
			ShippingCode:      v.UpetOrders.ShippingCode,
			ShippingExpressId: int32(v.UpetOrderCommon.ShippingExpressId),
			ECodeKdniao:       v.UpetExpress.ECodeKdniao,
		}

		//是否可以修改卡号赋值
		if k, ok := cardMap[v.UpetOrders.VirtualCardId]; ok {
			data.IsCanUp = int32(k)
		} else {
			data.IsCanUp = 1
		}

		data.Address = &oc.Address1{
			ReciverName:    v.UpetOrderCommon.ReciverName,
			MobPhone:       reciverInfoStruct.MobPhone,
			EncryptMobile:  reciverInfoStruct.EncryptMobile,
			ReciverAddress: reciverInfoStruct.Address,
		}
		data.Goods = make([]*oc.Goods, 0)
		goodsImage := v.UpetOrderGoods.GoodsImage
		if !strings.HasPrefix(v.UpetOrderGoods.GoodsImage, "http") {
			//获取电商图片域名
			bbcImgPath := config.GetString("bbc_img_path")
			goodsImage = bbcImgPath + goodsImage
		}
		data.Goods = append(data.Goods, &oc.Goods{
			GoodsId:    v.UpetOrderGoods.GoodsId,
			GoodsImage: goodsImage,
			GoodsName:  v.UpetOrderGoods.GoodsName,
			GoodsPrice: float32(v.UpetOrderGoods.GoodsPrice),
			GoodsNum:   v.UpetOrderGoods.GoodsNum,
		})
		out.Data = append(out.Data, &data)
	}
	return out, nil
}

// 获取健康会员卡实体卡订单详情
func (v *VipCardOrderService) GetPhysicalVipCardOrderDetail(ctx context.Context, in *oc.GetPhysicalVipCardOrderDetailRequest) (out *oc.GetPhysicalVipCardOrderDetailResponse, e error) {
	logPrefix := fmt.Sprintf("GetPhysicalVipCardOrderDetail====order-center====入参：%s", kit.JsonEncode(in))
	glog.Info(logPrefix)

	db := GetUPetDBConn()
	data := models.PhysicalVipCardOrders{}

	if has, e := db.Table("upet_orders").Alias("o").
		Join("inner", "upet_member m", "o.buyer_id=m.member_id").
		Join("inner", "upet_order_common oc", "o.order_id=oc.order_id").
		Join("inner", "upet_order_goods od", "o.order_id=od.order_id").
		Join("left", "upet_express e", "oc.shipping_express_id=e.id").
		Where("o.order_sn=?", in.OrderSn).
		Get(&data); e != nil {
		glog.Error(logPrefix, "查询数据失败：", e.Error())
		return nil, errors.New("查询数据失败")
	} else if !has {
		return nil, errors.New("订单数据不存在")
	}
	UpetOrderLog := make([]*models.UpetOrderLog, 0)
	if err := db.Where("order_id=?", data.UpetOrders.OrderId).Or("order_id=? and log_orderstate=?", data.UpetOrders.OrderFather, "20").OrderBy("log_id asc").Find(&UpetOrderLog); err != nil {
		glog.Error(logPrefix, "查询订单脚步失败：", err.Error())
		return nil, errors.New("查询订单脚步失败")
	}
	//组装返回数据
	ReciverInfo, _ := serialize.UnMarshal([]byte(data.UpetOrderCommon.ReciverInfo))
	reciverInfoStr := kit.JsonEncode(ReciverInfo)
	reciverInfoStruct := models.ReciverInfo{}
	if err := kit.JsonDecode([]byte(reciverInfoStr), &reciverInfoStruct); err != nil {
		glog.Error(logPrefix, "解析收获信息失败:", err.Error())
		return nil, errors.New("解析收获信息失败")
	}
	out = new(oc.GetPhysicalVipCardOrderDetailResponse)
	VirtualCardId := ""
	if data.UpetOrders.VirtualCardId > 0 {
		VirtualCardId = fmt.Sprintf("FY%d", data.UpetOrders.VirtualCardId)
	}
	ShippingTime := ""
	if data.UpetOrderCommon.ShippingTime > 0 {
		ShippingTime = time.Unix(int64(data.UpetOrderCommon.ShippingTime), 0).Format(kit.DATETIME_LAYOUT)
	}
	out.Data = &oc.PhysicalVipCardOrderDetail{
		OrderSn:           strconv.Itoa(int(data.UpetOrders.OrderSn)),
		PaymentTime:       utils.UnixToTimeFormat(kit.DATETIME_LAYOUT, int64(data.UpetOrders.PaymentTime)),
		OrderFrom:         int32(data.UpetOrders.OrderFrom),
		OrderFromText:     models.UpetOrderFromMap[data.UpetOrders.OrderFrom],
		VirtualCardId:     data.UpetOrders.VirtualCardId,
		VirtualCardIdText: VirtualCardId,
		OrderState:        int32(data.UpetOrders.OrderState),
		OrderStateText:    models.UpetOrderStateMap[data.UpetOrders.OrderState],
		PaymentCode:       data.UpetOrders.PaymentCode,
		PaymentCodeText:   models.UpetOrdersPaymentCode[data.UpetOrders.PaymentCode],
		OrderMessage:      data.UpetOrderCommon.OrderMessage,
		ScrmUserId:        data.UpetMember.ScrmUserId,
		BuyerPhone:        data.UpetOrders.BuyerPhone,
		EncryptMobile:     data.UpetOrders.EncryptMobile,
		BuyName:           data.UpetOrders.BuyerName,
		Address:           new(oc.Address1),
		Goods:             make([]*oc.Goods, 0),
		Delivery: &oc.DeliveryInfo{
			ShippingCode: data.UpetOrders.ShippingCode,
			EName:        data.UpetExpress.EName,
			ShippingTime: ShippingTime,
		},
		Steps: make([]*oc.UpetOrderLog, 0),
	}
	for _, v := range UpetOrderLog {
		LogOrderstate, _ := strconv.Atoi(v.LogOrderstate)
		log := oc.UpetOrderLog{
			OrderId:           int64(v.OrderId),
			LogTime:           time.Unix(v.LogTime, 0).Format(kit.DATETIME_LAYOUT),
			LogOrderstateText: models.UpetOrderStateMap[LogOrderstate],
		}
		out.Data.Steps = append(out.Data.Steps, &log)
	}
	pay := out.Data.Steps[0]
	out.Data.Steps[0] = out.Data.Steps[1]
	out.Data.Steps[1] = pay
	out.Data.Address = &oc.Address1{
		ReciverName:    data.UpetOrderCommon.ReciverName,
		MobPhone:       reciverInfoStruct.MobPhone,
		EncryptMobile:  reciverInfoStruct.EncryptMobile,
		ReciverAddress: reciverInfoStruct.Address,
	}
	out.Data.Goods = append(out.Data.Goods, &oc.Goods{
		GoodsId:    data.UpetOrderGoods.GoodsId,
		GoodsImage: data.UpetOrderGoods.GoodsImage,
		GoodsName:  data.UpetOrderGoods.GoodsName,
		GoodsPrice: float32(data.UpetOrderGoods.GoodsPrice),
		GoodsNum:   data.UpetOrderGoods.GoodsNum,
	})

	return out, nil
}

// 导出 健康会员卡实体卡订单
func (v *VipCardOrderService) GetPhysicalVipCardOrderExport(ctx context.Context, in *oc.GetPhysicalVipCardOrderListRequest) (out *oc.GetPhysicalVipCardOrderExportResponse, e error) {
	GrpcContext := v.LoadGrpcContext(ctx)
	UserNo := GrpcContext.UserInfo.UserNo
	RealName := GrpcContext.UserInfo.RealName
	logPrefix := fmt.Sprintf("健康会员卡实体卡订单导出GetPhysicalVipCardOrderExport====order-center====入参：UserNo:%s,RealName:%s,%s", UserNo, RealName, kit.JsonEncode(in))
	glog.Info(logPrefix)

	db := GetDcDBConn()
	session := db.NewSession()
	task := models.VipCardTask{
		UserNo: UserNo,
		Type:   2,
		Req:    kit.JsonEncode(in),
		State:  models.VCTaskStateIng,
	}

	if _, err := session.Insert(&task); err != nil {
		glog.Error(logPrefix, "导出订单失败", err.Error())
		return nil, errors.New("导出订单失败")
	}
	upetDb := GetUPetDBConn()
	upetSession := upetDb.NewSession()
	go func() {
		time.Sleep(10 * time.Second)
		vip_card.PhysicalVipCardOrderExportOp(upetSession, task.Id)
	}()
	return &oc.GetPhysicalVipCardOrderExportResponse{}, nil
}

// 会员卡实体卡订单导出结果列表
func (v *VipCardOrderService) PhysicalVipCardOrderExportList(ctx context.Context, in *oc.PhysicalVipCardOrderExportListRequest) (out *oc.PhysicalVipCardOrderExportListResponse, e error) {
	out = &oc.PhysicalVipCardOrderExportListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("会员卡实体卡订单导出结果列表 PhysicalVipCardOrderExportList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := GetDcDBConn()
	query := db.Table("datacenter.vip_card_task").Select("state,result,url,created_at").
		Where("user_no = ? and type = ?", v.LoadGrpcContext(ctx).UserInfo.UserNo, 2).
		OrderBy("id desc")
	if out.Total, e = query.Clone().Select("count(*)").Count(); e != nil {
		return nil, errors.New("统计数据总条数失败 " + e.Error())
	}
	out.Data = make([]*oc.PhysicalVipCardOrderExportListResponse_List, 0)
	if e = query.Limit(int(in.PageSize), int(in.PageSize*(in.PageIndex-1))).Find(&out.Data); e != nil {
		return nil, errors.New("查询数据失败 " + e.Error())
	}

	for _, v := range out.Data {
		v.StateText = models.VCTaskStateMap[v.State]

	}

	out.Code = 200
	return out, nil
}

// 健康会员卡实体卡 下载物流导入模板
func (v *VipCardOrderService) PVCExpressImportTemplate(ctx context.Context, in *oc.PVCExpressImportTemplateRequest) (out *oc.PVCExpressImportTemplateResponse, e error) {
	out = &oc.PVCExpressImportTemplateResponse{Code: 400}
	defer func() {
		if out.Code == 400 {
			glog.Info("健康会员卡实体卡 物流导入 PVCExpressImportTemplate 入参：", kit.JsonEncode(in), "，返回：", out.Code, out.Message)
		}
	}()
	expressName := ""
	sql := `select group_concat( concat_ws("-",e_name,e_code_kdniao)) as name  from upet_express where e_state='1' and id in ('43','29','8',"40","44","7","28","16","41","32","49","49") order by e_order asc,e_letter asc;`
	if result, err := GetUPetDBConn().Query(sql); err != nil {
		return nil, errors.New("查询快递公司出错 " + err.Error())
	} else if len(result) > 0 {
		expressName = string(result[0]["name"])
	}

	f := excelize.NewFile()

	// 文本样式
	styleId, _ := f.NewStyle(&excelize.Style{
		NumFmt: 49,
	})
	_ = f.SetColStyle("Sheet1", "A", styleId)
	_ = f.SetColStyle("Sheet1", "C", styleId)

	//todo 查找可用的物流名称
	_ = f.SetCellValue("Sheet1", "A1", "说明：订单号是实体卡下单的订单号；卡号是实体健康卡的卡号；快递公司名称：顺丰快递、韵达快递等；快递单号：发货的订单号")
	_ = f.SetCellValue("Sheet1", "A2", "快递公司名称-快递编码如下："+expressName)
	_ = f.SetCellValue("Sheet1", "A3", "订单号")
	_ = f.SetCellValue("Sheet1", "B3", "卡号")
	_ = f.SetCellValue("Sheet1", "C3", "快递公司名称")
	_ = f.SetCellValue("Sheet1", "D3", "快递单号")

	if buffer, err := f.WriteToBuffer(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Template = buffer.Bytes()
	}

	out.Code = 200
	return
}

// 健康会员卡实体卡 导入物流单号
func (v *VipCardOrderService) PVCExpressImport(ctx context.Context, in *oc.PVCExpressImportRequest) (out *oc.PVCExpressImportResponse, e error) {
	logPrefix := "健康会员卡实体卡导入物流单号===="

	file, e := excelize.OpenReader(bytes.NewReader(in.File))
	if e != nil {
		glog.Errorf("%s读取excel文件失败：%s", logPrefix, e.Error())
		return nil, errors.New("读取文件失败")
	}
	defer file.Close()

	//初始化导入处理器
	h := new(ImportHandler)
	h.DcDBSession = GetDcDBConn().NewSession()
	h.UpetDBSession = GetUPetDBConn().NewSession()
	h.RedisConn = GetRedisConn()
	h.File = file
	h.Task = &models.VipCardTask{
		Type:   1,
		State:  models.VCTaskStateSuccess,
		UserNo: v.LoadGrpcContext(ctx).UserInfo.UserNo,
	}
	h.VirtualCardIdMap = make(map[int64]int32)
	h.VipCardVirtualMap = make(map[int64]int32)
	h.VipCardOrderMap = make(map[int64]int32)
	glog.Info(logPrefix, "任务记录：", kit.JsonEncode(h.Task))
	h.LogPrefix = logPrefix
	e = h.ExpressImport()
	if e != nil {
		glog.Errorf("%s导入失败：%s", logPrefix, e.Error())
		return nil, errors.New("导入失败" + e.Error())
	}

	return &oc.PVCExpressImportResponse{}, nil

}

// 健康会员卡实体卡 编辑物流单号
func (v *VipCardOrderService) PVCExpressEdit(ctx context.Context, in *oc.PVCExpressEditRequest) (out *oc.PVCExpressEditResponse, e error) {
	logPrefix := fmt.Sprintf("健康会员卡实体卡编辑物流单号====入参：%s,操作人：%s", kit.JsonEncode(in), v.LoadGrpcContext(ctx).UserInfo.UserNo)
	glog.Info(logPrefix)
	out = &oc.PVCExpressEditResponse{}

	if len(in.ShippingCode) < 1 || len(in.ShippingEcode) < 1 {
		out.Message = "参数错误"
		return
	}

	upetDb := GetUPetDBConn()
	UpetOrders := new(models.UpetOrders)

	if has, err := upetDb.Table("upet_orders").Where("order_sn=?", in.OrderSn).Get(UpetOrders); err != nil {
		glog.Error(logPrefix, "查询订单失败：", err.Error())
		return nil, errors.New("查询订单失败 " + err.Error())
	} else if !has {
		return nil, errors.New("订单不存在")
	}
	UpetOrderCommon := new(models.UpetOrderCommon)
	if _, err := upetDb.Table("upet_order_common").Where("order_id=?", UpetOrders.OrderId).Get(UpetOrderCommon); err != nil {
		glog.Error(logPrefix, "查询订单扩展信息失败：", err.Error())
		return nil, errors.New("查询订单扩展信息失败 " + err.Error())
	}

	// if UpetOrders.OrderState != models.OrderStatePayed && UpetOrders.OrderState != models.OrderStateShipped {
	// 	glog.Error(logPrefix, fmt.Sprintf("订单状态%s不允许这个操作", models.UpetOrderStateMap[UpetOrders.OrderState]))
	// 	return nil, fmt.Errorf("订单状态%s不允许这个操作", models.UpetOrderStateMap[UpetOrders.OrderState])
	// }
	glog.Info(logPrefix, "修改前的订单数据：", kit.JsonEncode(UpetOrders), "，订单扩展信息：", kit.JsonEncode(UpetOrderCommon))
	var UpetExpress models.UpetExpress

	if has, err := upetDb.Table("upet_express").Where("e_code_kdniao = ?", in.ShippingEcode).Get(&UpetExpress); err != nil {
		glog.Error(logPrefix, "查询物流公司出错 "+err.Error())
		return nil, errors.New("查询物流公司出错 " + err.Error())

	} else if !has {
		glog.Error(logPrefix, "物流公司编码不存在")
		return nil, errors.New("物流公司编码不存在")
	}
	session := upetDb.NewSession()
	session.Begin()
	if _, err := session.Table("upet_orders").Cols("shipping_code").Where("order_sn=?", in.OrderSn).Update(map[string]interface{}{"shipping_code": in.ShippingCode}); err != nil {
		session.Rollback()
		glog.Error(logPrefix, "更新物流单号失败：", err.Error())
		return nil, errors.New("更新物流单号失败 " + err.Error())
	}
	if _, err := session.Table("upet_order_common").Cols("shipping_express_id").Where("order_id=?", UpetOrders.OrderId).
		Update(map[string]interface{}{"shipping_express_id": UpetExpress.Id}); err != nil {
		session.Rollback()
		glog.Error(logPrefix, "更新物流单号失败1：", err.Error())
		return nil, errors.New("更新物流单号失败 " + err.Error())
	}
	if _, err := session.Table("dc_order.order_express").Where("order_sn=?", in.OrderSn).Cols("express_no", "express_code", "express_name").
		Update(map[string]interface{}{"express_no": in.ShippingCode, "express_code": in.ShippingEcode, "express_name": UpetExpress.EName}); err != nil {
		session.Rollback()
		glog.Error(logPrefix, "更新物流单号失败2", err.Error())
		return nil, errors.New("更新物流失败 " + err.Error())
	}
	if _, err := session.Table("upet_order_demolition_pack").Cols("shipping_name", "shipping_code").Where("order_id=?", UpetOrders.OrderId).Update(map[string]interface{}{"shipping_name": in.ShippingEcode, "shipping_code": in.ShippingCode}); err != nil {
		session.Rollback()
		glog.Error(logPrefix, "更新物流单号失败3", err.Error())
		return nil, errors.New("更新物流失败 " + err.Error())
	}
	session.Commit()
	return out, nil
}

// 健康会员卡实体卡 编辑会员号
func (v *VipCardOrderService) PVCEdit(ctx context.Context, in *oc.PVCEditRequest) (out *oc.PVCEditResponse, e error) {
	logPrefix := fmt.Sprintf("健康会员卡实体卡编辑会员号====入参：%s,操作人：%s", kit.JsonEncode(in), v.LoadGrpcContext(ctx).UserInfo.UserNo)
	glog.Info(logPrefix)
	out = &oc.PVCEditResponse{}

	if len(in.OrderSn) < 1 || len(in.VirtualCard) < 1 {
		glog.Error(logPrefix, "参数错误")
		out.Message = "参数错误"
		return
	}
	VirtualCardIdStr := strings.Trim(in.VirtualCard, "FY")
	VirtualCardId, _ := strconv.Atoi(VirtualCardIdStr)

	if strings.Index(in.VirtualCard, "FY") != 0 {
		return nil, errors.New("会员卡号必须是FY开头,且10位数字")
	} else if len(VirtualCardIdStr) != 10 {
		return nil, errors.New("会员卡号必须是FY开头,且10位数字")
	}

	upetDb := GetUPetDBConn()
	UpetOrdersSli := make([]*models.UpetOrders, 0)
	var UpetOrder *models.UpetOrders    //订单号对应的订单信息
	var NewUpetOrder *models.UpetOrders //用于 判断 新会员卡号 是否已经存在
	VipCardOrderSli := make([]*models.VipCardOrder, 0)
	vipCardVirtual := new(models.VipCardVirtual) //新会员卡号是否有效

	if err := upetDb.Table("upet_orders").Where("order_sn=?", in.OrderSn).Or("virtual_card_id=?", VirtualCardId).Find(&UpetOrdersSli); err != nil {
		glog.Error(logPrefix, "查询数据失败 ", err.Error())
		return nil, errors.New("查询数据失败 " + err.Error())
	}
	for _, v := range UpetOrdersSli {
		value := v
		if strconv.Itoa(int(value.OrderSn)) == in.OrderSn {
			UpetOrder = value
		}

		if value.VirtualCardId == int64(VirtualCardId) && strconv.Itoa(int(value.OrderSn)) != in.OrderSn {
			NewUpetOrder = value
		}

	}

	if UpetOrder == nil {
		glog.Error(logPrefix, "找不到订单数据")
		return nil, errors.New("找不到订单数据")
	}
	glog.Info(logPrefix, "修改前的数据：", UpetOrder.VirtualCardId)

	// if UpetOrder.VirtualCardId == 0 {
	// 	glog.Error(logPrefix, "未导入过会员卡卡号")
	// 	return nil, errors.New("未导入过会员卡卡号")
	// }
	if UpetOrder.VirtualCardId == int64(VirtualCardId) {
		return nil, errors.New("请输入一个新的会员卡号")
	}
	if NewUpetOrder != nil {
		glog.Error(logPrefix, "新会员卡号已经存在")
		return nil, errors.New("新会员卡号已经存在")
	}

	//原虚拟卡号、新虚拟卡号 是否已经被使用
	if err := upetDb.Table("datacenter.vip_card_order").In("virtual_card_id", []int64{UpetOrder.VirtualCardId, int64(VirtualCardId)}).Find(&VipCardOrderSli); err != nil {
		glog.Error(logPrefix, "查询数据失败 ", err.Error())
		return nil, errors.New("查询数据失败 " + err.Error())
	}
	for _, v := range VipCardOrderSli {
		value := v
		if value.VirtualCardId == int64(VirtualCardId) {
			return nil, errors.New("新会员卡已经被使用")
		}
		if value.VirtualCardId == UpetOrder.VirtualCardId {
			return nil, errors.New("原会员卡已经被使用")
		}
	}

	//新虚拟卡号 ，是否是有效的状态
	if has, err := upetDb.Table("datacenter.vip_card_virtual").Where("card_id=?", VirtualCardId).Get(vipCardVirtual); err != nil {
		glog.Error(logPrefix, "查询数据失败 ", err.Error())
		return nil, errors.New("查询数据失败 " + err.Error())
	} else if !has {
		return nil, errors.New("会员卡不存在")
	}
	if vipCardVirtual.Status != 0 {
		return nil, errors.New("会员卡不可使用")
	}

	if _, err := upetDb.Table("upet_orders").Cols("virtual_card_id").Where("order_sn=?", in.OrderSn).Update(map[string]interface{}{"virtual_card_id": strings.Trim(in.VirtualCard, "FY")}); err != nil {
		glog.Error(logPrefix, "修改卡号失败 ", err.Error())
		return nil, errors.New("修改卡号失败 " + err.Error())
	}

	return out, nil

}

// 会员卡实体卡 批量导入结果查看
func (v *VipCardOrderService) PVCExpressImportList(ctx context.Context, in *oc.PVCExpressImportListRequest) (out *oc.PVCExpressImportListResponse, e error) {
	out = &oc.PVCExpressImportListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("会员卡实体卡订单发货信息导入结果列表 PVCExpressImportList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := GetDcDBConn()
	query := db.Table("datacenter.vip_card_task").Select("state,result,url,created_at").
		Where("user_no = ? and type = ?", v.LoadGrpcContext(ctx).UserInfo.UserNo, 1).
		OrderBy("id desc")
	if out.Total, e = query.Clone().Select("count(*)").Count(); e != nil {
		return nil, errors.New("统计数据总条数失败 " + e.Error())
	}
	out.Data = make([]*oc.PVCExpressImportListResponse_List, 0)
	if e = query.Limit(int(in.PageSize), int(in.PageSize*(in.PageIndex-1))).Find(&out.Data); e != nil {
		return nil, errors.New("查询数据失败 " + e.Error())
	}

	out.Code = 200
	return out, nil
}

// GetOrderList 会员卡订单列表
func (v *VipCardOrderService) GetOrderList(ctx context.Context, in *oc.GetOrderListRequest) (out *oc.GetOrderListResponse, e error) {
	out = &oc.GetOrderListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetOrderList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	in.UserMobile = strings.TrimSpace(in.UserMobile)
	in.OrderSn = strings.TrimSpace(in.OrderSn)
	in.UserId = strings.TrimSpace(in.UserId)
	in.CardName = strings.TrimSpace(in.CardName)
	in.PayTimeStart = strings.TrimSpace(in.PayTimeStart)
	in.PayTimeEnd = strings.TrimSpace(in.PayTimeEnd)
	in.EntityOrderSn = strings.TrimSpace(in.EntityOrderSn)

	db := GetUPetDBConn()

	session := db.Table("datacenter.vip_card_order").Alias("o").
		Join("left", "datacenter.store s", "s.zilong_id = o.store_id and o.store_id != 0").
		Join("left", "datacenter.vip_card_template t", "o.card_id = t.id").
		Join("left", "upet_vr_order uvo", "uvo.erp_order_sn = o.order_sn")

	if in.Type > 0 {
		session.And("o.state = ?", in.Type)
	}
	if in.OrderSn != "" {
		session.And("o.order_sn = ?", in.OrderSn)
	}
	if in.EntityOrderSn != "" {
		session.And("o.entity_order_sn = ?", in.EntityOrderSn)
	}
	if in.VirtualCardId > 0 {
		session.And("o.virtual_card_id=?", in.VirtualCardId)
	}
	if in.UserId != "" {
		session.And("o.user_id = ?", in.UserId)
	}
	if in.Ids != "" {
		session.In("o.id ", strings.Split(in.Ids, ","))
	}

	if in.UserMobile != "" {
		session.And("o.en_user_mobile = ?", utils.MobileEncrypt(in.UserMobile))
	}

	if in.State > 0 {
		session.And("o.state = ?", in.State)
	}

	if len(in.CardName) > 0 {
		session.And("`o`.card_name like ?", "%"+in.CardName+"%")
	}

	if in.Source > -1 {
		session.And("o.source = ?", in.Source)
	}

	if in.Type == 20 {
		if len(in.PayTimeStart) > 0 {
			session.And("o.refund_time >= ?", in.PayTimeStart)
		}
		if len(in.PayTimeEnd) > 0 {
			session.And("o.refund_time <= ?", in.PayTimeEnd)
		}
	} else {
		if len(in.PayTimeStart) > 0 {
			session.And("o.pay_time >= ?", in.PayTimeStart)
		}
		if len(in.PayTimeEnd) > 0 {
			session.And("o.pay_time <= ?", in.PayTimeEnd)
		}
	}
	// 是否过期：0-全部，1-未过期的，2-过期的
	if in.Expiry == 1 {
		session.And("o.expiry_date > NOW()")
	} else if in.Expiry == 2 {
		session.And("o.expiry_date <= NOW()")
	}
	session.And("o.is_delete = 0")

	if total, err := session.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		out.Total = int32(total)
		if in.Export == 0 {
			session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize))
		}
		if err = session.Select("o.*,s.name as store_name,t.or_id as org_id,uvo.payment_code,uvo.order_state").OrderBy("o.id " + in.Order + "").Find(&out.Data); err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
	}

	for _, data := range out.Data {
		// 卡类型名称
		data.CardTypeName = CardTypeMap[data.CardType]
		// 卡周期名称
		data.CycleName = CycleTypeMap[data.CardCycle]
		// 支付方式名称
		if data.Source == 0 || data.Source == 1 {
			data.PaymentCode = PaymentCodeMap[data.PaymentCode]
		} else {
			data.PaymentCode = ""
		}
		// 卡id转换
		if data.Source == 2 {
			data.TId = data.CardId
		} else {
			data.TId = data.CardId
		}
		// 卡号转换
		data.CardId = fmt.Sprintf("%s%d", "FY", data.VirtualCardId)
		// 分销人id兼容0
		if data.DisMemberId == "0" {
			data.DisMemberId = ""
		}
		// 已过期状态
		expiryDate, err := time.ParseInLocation(kit.DATETIME_LAYOUT, data.ExpiryDate, time.Local)
		data.IsExpiry = 0
		if err == nil && expiryDate.Unix() < time.Now().Unix() {
			data.IsExpiry = 1
		}
	}

	out.Code = 200
	return
}

func (v *VipCardOrderService) GetVipCardOrderEquity(ctx context.Context, in *oc.GetVipCardOrderEquityRequest) (out *oc.GetVipCardOrderEquityResponse, err error) {
	out = new(oc.GetVipCardOrderEquityResponse)
	detailData := oc.VipVrRefundDetailData{}

	// 获取会员购买记录
	err = getVipCardOrderByOrderSn(in.OrderSn, &detailData)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}

	// 获取权益相关信息
	err = getEquityInfoByOrderSn(in.OrderSn, &detailData)
	if err != nil {
		out.Message = err.Error()
		return out, err
	}

	out.Data = detailData.Equity
	out.Code = 200
	return out, nil
}

// GetVipVrRefundList 会员卡退费列表查询
func (v *VipCardOrderService) GetVipVrRefundList(ctx context.Context, in *oc.GetVrRefundListRequest) (out *oc.GetVrRefundListResponse, e error) {
	out = &oc.GetVrRefundListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipVrRefundList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	in.OrderSn = strings.TrimSpace(in.OrderSn)
	in.Mobile = strings.TrimSpace(in.Mobile)

	db := GetUPetDBConn()

	session := db.Table("upet_vr_refund").Alias("uvr").
		Join("left", "upet_vr_order uvo", "uvo.order_id = uvr.order_id").
		Join("left", "upet_member um", "um.member_id = uvr.buyer_id").
		Join("left", "datacenter.vip_card_template vct", "uvr.goods_id = vct.sku_id ")
	session.And("uvo.order_type = 17")
	if in.CardName != "" {
		session.And("vct.card_name=?", in.CardName)
	}
	if in.ErpOrderSn != "" {
		session.And("uvo.erp_order_sn = ?", in.ErpOrderSn)
	}
	if in.Mobile != "" {
		session.And("um.member_mobile = ?", in.Mobile)
	}
	if in.StartTime != "" {
		startTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, in.StartTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
		session.And("uvr.add_time >= ?", startTime.Unix())
	}
	if in.EndTime != "" {
		endTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, in.EndTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
		session.And("uvr.add_time <= ?", endTime.Unix())
	}
	if in.AdminState > 0 {
		// 解析状态对应的筛选条件
		adminState := in.AdminState
		switch adminState {
		case 2:
			session.And("uvr.admin_state = ?", 3)
		case 3:
			session.And("uvr.admin_state = ?", 2)
			session.And("uvr.apply_type = ?", 2)
			session.And("uvr.dy_state = ?", 2)
		case 4:
			session.And("uvr.admin_state = ?", 2)
			session.And("uvr.apply_type = ?", 2)
			session.And("uvr.dy_state <> ?", 2)
		case 5:
			session.And("uvr.admin_state = ?", 2)
			session.And("uvr.apply_type <> ?", 2)
		default:
			session.And("uvr.admin_state = ?", adminState)
		}
	}

	if total, err := session.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		out.Total = int32(total)
		if in.Export == 0 {
			session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize))
		}
		if err = session.Select("uvr.*," +
			"uvo.erp_order_sn,um.member_id,uvo.buyer_phone AS mobile,uvo.encrypt_mobile,vct.card_name").OrderBy("uvr.refund_id desc").Find(&out.Data); err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
	}

	for _, data := range out.Data {
		if t, _ := strconv.ParseInt(data.AdminTime, 10, 64); t > 0 {
			// 时间戳转格式
			adminTime := time.Unix(cast.ToInt64(data.AdminTime), 0)
			data.AdminTime = adminTime.Format(kit.DATETIME_LAYOUT)
		} else {
			data.AdminTime = ""
		}
		if t, _ := strconv.ParseInt(data.AddTime, 10, 64); t > 0 {
			addTime := time.Unix(cast.ToInt64(data.AddTime), 0)
			data.AddTime = addTime.Format(kit.DATETIME_LAYOUT)
		} else {
			data.AddTime = ""
		}

		// 反转state状态：0-全部（默认）；1-待审核；2-审批拒绝；3-退款成功；4-退款失败；5-注销成功；6-退款中
		applyType := data.ApplyType
		dyState := data.DyState
		state := data.AdminState // 退款状态:1-待审核；2-同意；3-不同意

		if state == 2 { // 同意
			if applyType == 2 { // 退款+注销
				if dyState == 0 || dyState == 2 { // 电银状态：0-默认，2-退款成功（默认的情况考虑线下退款不会维护该字段）
					// 电银退款成功
					data.AdminState = 3
				} else if dyState == 3 || dyState == 4 {
					// 电银退款失败
					data.AdminState = 4
				} else if dyState == 1 {
					// 电银退款中
					data.AdminState = 6
				}
			} else { // 仅注销
				data.AdminState = 5
			}
		} else if state == 3 { // 不同意
			data.AdminState = 2
		}
	}

	out.Code = 200
	return
}

func (v *VipCardOrderService) VipVrRefundDetail(ctx context.Context, in *oc.VipVrRefundDetailReq) (out *oc.VipVrRefundDetailResp, e error) {
	out = &oc.VipVrRefundDetailResp{Code: 400}

	// 获取参数
	refundId := in.RefundId
	detailData := oc.VipVrRefundDetailData{}

	// 获取申请退费记录的简要信息
	err := getVipVrRefundByOrderSn(refundId, &detailData)
	if err != nil {
		out.Message = err.Error()
		return
	}
	orderSn := detailData.ErpOrderSn

	// 获取会员购买记录
	err = getVipCardOrderByOrderSn(orderSn, &detailData)
	if err != nil {
		out.Message = err.Error()
	}

	// 获取权益相关信息
	err = getEquityInfoByOrderSn(orderSn, &detailData)
	if err != nil {
		out.Message = err.Error()
	}

	out.Data = &detailData
	out.Code = 200
	return
}

func getVipVrRefundByOrderSn(refundId int32, detailData *oc.VipVrRefundDetailData) error {
	session := GetUPetDBConn()
	isOk, err := session.SQL("SELECT uvr.refund_id,uvr.order_sn,uvo.erp_order_sn,uvr.apply_type,CONCAT(LEFT(um.member_mobile, 3), '****', RIGHT(um.member_mobile, 4)) AS mobile,um.scrm_user_id "+
		"FROM upet_vr_refund uvr "+
		"LEFT JOIN upet_member um ON um.member_id=uvr.buyer_id "+
		"LEFT JOIN upet_vr_order uvo ON uvo.order_id = uvr.order_id "+
		"WHERE uvr.refund_id=?", refundId).Get(detailData)
	if !isOk || err != nil {
		return errors.New("获取申请退费简要信息失败")
	}

	return nil
}

func getVipCardOrderByOrderSn(orderSn string, detailData *oc.VipVrRefundDetailData) error {
	session := GetUPetDBConn()
	order := new(oc.VipCardOrderData)
	isOk, err := session.SQL("SELECT vco.card_id,vco.card_name,vco.order_amount,vco.pay_time,vco.expiry_date,vco.user_id,um.member_id,vct.or_id,uvo.order_state "+
		"FROM datacenter.vip_card_order vco "+
		"LEFT JOIN upet_member um ON um.scrm_user_id=vco.user_id "+
		"LEFT JOIN datacenter.vip_card_template vct ON vct.id=vco.card_id "+
		"LEFT JOIN upet_vr_order uvo ON uvo.erp_order_sn=vco.order_sn "+
		"WHERE vco.order_sn=?", orderSn).Get(order)
	if !isOk || err != nil {
		return errors.New("获取会员卡订单数据失败简要信息失败")
	}
	detailData.Order = append(detailData.Order, order)
	return nil
}

func getEquityInfoByOrderSn(orderSn string, detailData *oc.VipVrRefundDetailData) error {
	// 初始化
	detailData.Advise = 1

	session := GetDcDBConn()
	var equityArr = make([]models.RefundDetailData, 0)
	err := session.SQL("SELECT vce.equity_name,vce.equity_type,vco.collection_type,vcec.equity_id,vce.collection_ids,vco.pay_time "+
		"FROM vip_card_order vco "+
		"INNER JOIN vip_card_equity_config vcec ON vcec.card_tid=vco.card_id AND vcec.refundable=0 "+
		"LEFT JOIN vip_card_equity vce ON vce.id=vcec.equity_id "+
		"WHERE vco.order_sn=?", orderSn).Find(&equityArr)
	if err != nil {
		glog.Errorf("获取申请退卡的相关权益简要信息失败-订单编号=%s-错误为%s", orderSn, err.Error())
		return errors.New("获取申请退卡的相关权益简要信息失败")
	}

	// 根据不同的权益类型，获取详细的数据
	if len(equityArr) == 0 {
		return errors.New("没有找到对应的权益记录")
	}

	var equityDataList = make([]*oc.EquityData, 0)
	for _, equity := range equityArr {
		// 需要过滤掉领取类型和订单类型不对应的记录
		collectionIds := equity.CollectionIds
		collectionType := strconv.FormatInt(int64(equity.CollectionType), 10)
		if len(equity.CollectionIds) > 0 && strings.Index(collectionIds, collectionType) == -1 {
			continue
		}

		var equityData = oc.EquityData{
			EquityName: equity.EquityName,
			EquityType: equity.EquityType,
			UsedState:  0,
		}

		// 查询权益使用记录
		var equityUsedArr = make([]models.EquityUsedRecord, 0)
		err := session.SQL("SELECT privilege_id,gift_order_sn,status "+
			"FROM vip_user_equity_record "+
			"WHERE order_sn=? AND equity_id=?", orderSn, equity.EquityId).Find(&equityUsedArr)
		if err != nil {
			return errors.New("权益对应的使用记录获取失败")
		}

		// 使用到权益使用记录且记录为0
		// 会员价优惠/商品特权/健康服务金 没有权益使用记录
		nonUsedEquity := []int32{4, 9, 10}
		isUsedEquity := true
		for _, equityType := range nonUsedEquity {
			if equity.EquityType == equityType {
				isUsedEquity = false
			}
		}
		if isUsedEquity && len(equityUsedArr) == 0 {
			// 没有权益使用记录，直接跳过获取权益使用详情操作
			equityDataList = append(equityDataList, &equityData)
			continue
		}

		order := detailData.Order[0]
		// 获取权益的使用详情
		equityType := equity.EquityType
		// 1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销)8-子龙打折卡 9-商品特权 10-健康服务金'
		//会员专享价	    4    会员价优惠
		//150元礼包	    5    开卡礼包
		//宠物医保	    7    宠物医保
		//家庭医生服务	6    家医服务
		//宠物体检券	    2    宠物体检券
		//门店商品专享折扣	8    门店商品折扣
		//医疗专享礼包	2    医疗专享礼包
		//豪华体检券	    2
		//看病特权	    8    医疗特权
		//到店服务特权	2    到店服务特权
		//商品特权	    9
		//健康服务金	    10
		switch equityType {
		case 4:
			// 会员专享价
			payPrice, _ := getGoodsPayPrice(order.MemberId, equity.PayTime, &equityData)
			if payPrice > 0 {
				equityData.UsedState = 1
				equityData.EquityContent = kit.JsonEncode(payPrice)
			}
		case 5:
			// 150元礼包
			if len(equityUsedArr) > 0 {
				equityData.UsedState = 1
				equityData.EquityContent = equityUsedArr[0].GiftOrderSn
			}
		case 2:
			// 宠物体检券、医疗礼包、月度返券、门店专享
			equityData.EquityContent, err = getZilongCouponDetail(equityUsedArr, &equityData)
		case 10:
			// 健康服务金
			healthFee, err := getHealthFee(orderSn, &equityData)
			if err != nil || len(healthFee) == 0 {
				log.Info("未能找到健康服务金的订单数据")
			} else {
				equityData.EquityContent = kit.JsonEncode(healthFee)
			}
		case 9:
			// 商品特权
			payPrice, _ := getGoodsPayPrice(order.MemberId, equity.PayTime, &equityData)
			if payPrice > 0 {
				equityData.UsedState = 1
			}
		case 7:
			// 宠物医保
			if equityUsedArr[0].Status == 1 || equityUsedArr[0].Status == 2 {
				stateMsg := "投保中"
				if equityUsedArr[0].Status == 2 {
					stateMsg = "投保成功"
				}
				equityData.UsedState = 1
				// 会员权益查看优化  如果是 投保成功， 则datacenter.vip_user_equity_record.gift_order_sn（保险订单号）去查datacenter.member_pet_property表里的保险单号
				insuranceData := struct {
					State                 string `json:"state"`                   //投保状态
					InsuranceOrderNumber  string `json:"insurance_order_number"`  //投保订单号
					InsurancePolicyNumber string `json:"insurance_policy_number"` //保险单号
				}{
					State:                stateMsg,
					InsuranceOrderNumber: equityUsedArr[0].GiftOrderSn,
				}
				InsurancePolicyNumber := ""
				_, err := session.SQL("select insurance_policy_number from datacenter.member_pet_property where insurance_order_number=?", equityUsedArr[0].GiftOrderSn).Get(&InsurancePolicyNumber)
				if err != nil {
					glog.Errorf("order-center-getEquityInfoByOrderSn查询保险单号失败，投保订单号：%s,错误：%s", equityUsedArr[0].GiftOrderSn, err.Error())
				}
				insuranceData.InsurancePolicyNumber = InsurancePolicyNumber
				equityData.EquityContent = kit.JsonEncode(insuranceData)

			}

		default:
			if len(equityUsedArr) > 0 {
				equityData.UsedState = 1
			}
		}
		equityDataList = append(equityDataList, &equityData)
	}

	// 填入系统推荐
	advise := 0
	glog.Info(fmt.Sprintf("判断是否自动审核，参数：equityDataList=%v", kit.JsonEncode(equityDataList)))
	if len(equityDataList) > 0 {
		detailData.Equity = equityDataList
		for _, equity := range equityDataList {
			if equity.UsedState == 1 {
				advise = 1
				break
			}
		}
	}
	glog.Info(fmt.Sprintf("判断是否自动审核，Advise=%v", advise))
	if advise == 0 {
		detailData.Advise = 0
	}
	glog.Info(fmt.Sprintf("判断是否自动审核，结果：Advise=%v", kit.JsonEncode(detailData)))

	return nil
}

func getZilongCouponDetail(equityUsedArr []models.EquityUsedRecord, equityData *oc.EquityData) (string, error) {
	var couponCodes = make([]string, 0)

	codePrivilegeIdMap := make(map[string]string)
	for _, equityUsed := range equityUsedArr {
		if len(equityUsed.PrivilegeId) == 0 {
			continue
		}
		codePrivilegeIdMap[equityUsed.GiftOrderSn] = equityUsed.PrivilegeId
		couponCodes = append(couponCodes, equityUsed.GiftOrderSn)
	}

	client := et.GetExternalClient()
	resp, err := client.ZiLong.GetVerifyDetail(client.Ctx, &et.GetVerifyDetailReq{
		CouponCodes: couponCodes,
	})
	if err != nil || len(resp.Data) == 0 {
		equityData.UsedState = 0
		log.Error("请求子龙接口，获取门店券使用记录失败或没有记录")
		return "", err
	}

	for _, data := range resp.Data {
		if data.Status == "2" {
			equityData.UsedState = 1
		}
		data.CouponId = codePrivilegeIdMap[data.CouponCode]
	}

	return kit.JsonEncode(resp.Data), nil
}

func getHealthFee(orderSn string, equityData *oc.EquityData) ([]*models.HealthFee, error) {
	var healthFee []*models.HealthFee

	//err := getOrderGoods(orderSn, healthFeeArr)

	vrHealthFee, err := getVrOrderGoods(orderSn)
	if err != nil || len(vrHealthFee) == 0 {
		return nil, err
	}
	healthFee = append(healthFee, vrHealthFee...)

	if len(healthFee) > 0 {
		equityData.UsedState = 1
		return healthFee, nil
	}

	return nil, nil
}

func getOrderGoods(orderSn string, healthFee []*models.HealthFee) error {
	/*healthFee := make([]models.HealthFee, 0)
	session := GetUPetDBConn().NewSession()
	isOk, err := session.SQL("SELECT g.goods_id,g.goods_name,o.order_sn,o.order_state "+
		"FROM upet_order_goods g "+
		"LEFT JOIN upet_orders o on g.order_id = o.order_id "+
		"WHERE g.goods_type =16 and g.order_id =? AND g.goods_id=? AND o.order_status>0;", orderSn, healthFee.GoodsId).Get(&healthFee)
	if isOk == false || err != nil {
		log.Error("没有健康服务金对应的实物商品信息")
		return err
	}*/
	return nil
}

func getVrOrderGoods(orderSn string) ([]*models.HealthFee, error) {
	vrHealthFee := make([]*models.HealthFee, 0)
	session := GetUPetDBConn().NewSession()
	err := session.SQL("SELECT uvo.goods_id,uvo.goods_name,ABS(SUM(mpgqd.generate_amount)) AS expense_price,uvo.order_sn,uvo.order_state "+
		"FROM datacenter.vip_card_order vco "+
		"LEFT JOIN datacenter.member_property_guarantee_quota_detail mpgqd on mpgqd.insurance_policy_number=vco.order_sn "+
		"LEFT JOIN upet_vr_order uvo ON uvo.order_id=SUBSTR(mpgqd.link_order_number,3) "+
		"WHERE vco.order_sn=? AND mpgqd.quota_type=2 AND uvo.order_promotion_type=17 AND uvo.order_state>10 AND mpgqd.sku_id>0 "+
		"GROUP BY mpgqd.link_order_number HAVING expense_price>0;", orderSn).Find(&vrHealthFee)
	if err != nil || len(vrHealthFee) == 0 {
		log.Error("没有健康服务金对应的虚拟商品信息")
		return vrHealthFee, err
	}

	return vrHealthFee, nil
}

func getGoodsPayPrice(buyerId int32, payTime time.Time, equityData *oc.EquityData) (float64, error) {
	if buyerId == 0 {
		log.Error("查询会员优化价失败，buyerId为空")
	}

	session := GetUPetDBConn()
	var payPrice sql.NullFloat64
	isOk, err := session.SQL("SELECT sum(g.goods_original_price-g.goods_pay_price) AS pay_price "+
		"FROM upet_order_goods g "+
		"LEFT JOIN upet_orders o ON g.order_id = o.order_id "+
		"WHERE o.order_state > 10 AND g.goods_type = 16 AND o.buyer_id=? AND o.payment_time>?", buyerId, payTime.Unix()).Get(&payPrice)
	if !isOk || err != nil {
		return 0, errors.New("获取申请退费简要信息失败")
	}

	if payPrice.Float64 > 0 {
		equityData.UsedState = 1
	}

	return payPrice.Float64, nil
}

// CreateVipVrRefund 创建会员卡退款申请
func (v *VipCardOrderService) CreateVipVrRefund(ctx context.Context, in *oc.CreateVrRefundRequest) (out *oc.CreateVrRefundResponse, e error) {
	out = &oc.CreateVrRefundResponse{Code: 400}

	// 校验参数
	mobile := strings.TrimSpace(in.Mobile)
	if len(mobile) == 0 {
		out.Message = "用户手机号无效"
		return
	}

	orderSn := strings.TrimSpace(in.OrderSn)
	if len(orderSn) == 0 {
		out.Message = "订单号不能为空"
		return
	}
	//判断手机号和订单是否符对应，并且订单号的状态是否对
	db := GetDcDBConn()
	order := new(models.VipCardOrder)
	selectSql := "SELECT * FROM vip_card_order WHERE order_sn=?"
	ok, err := db.SQL(selectSql, in.OrderSn).Get(order)
	if err != nil || !ok {
		out.Message = "没有找到对应的会员卡订单记录！"
		return
	}

	JmMobile := utils.MobileEncrypt(in.Mobile)
	if JmMobile != order.EnUserMobile {
		out.Message = "输入的手机号和订单不匹配！"
		return
	}
	//如果是激活或者门店赠送的话，只可以是仅注销
	if order.Source == 2 || order.Source == 3 {
		in.ApplyType = 1
	}
	//开始调用电商
	par := dto.RefundApplyReq{}
	par.ApplyType = int(in.ApplyType)
	par.ApplyUser = in.ApplyUser
	par.BuyerMessage = in.BuyerMessage
	par.OrderSn = in.OrderSn

	data := kit.JsonEncode(par)
	errStr := ReqstUpet(data, "vrOrderAppay")
	if errStr != "1" {
		out.Message = errStr
		return
	}
	//判断是否需要自动退款，先判断是否7天以内
	// 未发布前调整体验版
	beforeTime := order.CreateTime.AddDate(0, 0, 7)
	//如果当前时间小于订单创建时间+7天，说明是7天以内
	IsZd := false
	if time.Now().Before(beforeTime) {
		IsZd = true
	}

	// 调用是否使用过权益
	detailData := oc.VipVrRefundDetailData{}
	// 获取会员购买记录
	err = getVipCardOrderByOrderSn(orderSn, &detailData)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return
	}

	// 获取权益相关信息
	err = getEquityInfoByOrderSn(orderSn, &detailData)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return
	}

	glog.Info(fmt.Sprintf("判断是否自动审核会员卡退卡申请：IsZd=%v,advise=%d", IsZd, detailData.Advise))
	if IsZd && detailData.Advise == 0 {
		// 必要的延迟，避免线上的主从延迟导致的问题
		time.Sleep(100 * time.Microsecond)

		// 拿到卡信息
		//cardTemplate := new(models.VipCardTemplate)
		//if has, err := db.Where("id = ?", order.CardId).Get(cardTemplate); err != nil {
		//	out.Message = err.Error()
		//	return
		//} else if !has {
		//	out.Message = "未找到卡模板"
		//	return
		//}

		//查询主库退款单号
		type OrderInfo struct {
			RefundId     int32
			RefundAmount float32
		}
		var OrderInfoMap OrderInfo
		upDb := GetUPetDBConn()
		_, err = upDb.SQL("select /*FORCE_MASTER*/ a.refund_id,a.refund_amount from upet_vr_refund a "+
			"INNER JOIN `upet_vr_order` b ON a.order_id=b.order_id where b.erp_order_sn = ? and a.admin_state=1", in.OrderSn).
			Get(&OrderInfoMap)
		if err != nil {
			glog.Error("查询VIP退款单出错：", in.OrderSn, " ", err.Error())
			out.Message = "查询VIP退款单出错"
			return
		}
		if OrderInfoMap.RefundId == 0 {
			glog.Error("查询VIP退款单未查询到：", in.OrderSn)
			out.Message = "查询VIP退款单出错"
			return
		}

		price := int32(decimal.NewFromFloat32(OrderInfoMap.RefundAmount * 100).Round(0).IntPart())
		//调用自动审核
		c := CardService{}
		spPar := oc.RefundExamineReq{}
		spPar.RefundId = OrderInfoMap.RefundId
		spPar.RefundAmount = price
		spPar.AdminType = 1
		spPar.State = 2
		spPar.UserName = "admin"
		spPar.UserNo = "admin"
		ret, err := c.RefundExamine(context.Background(), &spPar)
		if err != nil {
			glog.Error("调用自动审核报错：", in.OrderSn, " ", err.Error())
			out.Message = "调用自动审核报错"
			return
		}
		if ret.Code != 200 {
			out.Message = ret.Message
			glog.Error("调用自动审核报错：", in.OrderSn, " ", ret.Message)
			return
		}

	}
	out.Code = 200
	return out, nil
}

// DelVipOrderCard 删除会员卡订单记录
func (v *VipCardOrderService) DelVipOrderCard(ctx context.Context, in *oc.DelVipOrderCardRequest) (out *oc.VcBaseResponse, err error) {
	out = &oc.VcBaseResponse{Code: 400}

	// 校验参数
	if in.Id <= 0 {
		out.Message = "参数id非法"
		return
	}

	db := GetDcDBConn()
	order := new(models.VipCardOrder)
	selectSql := "SELECT * FROM vip_card_order WHERE id=?"
	ok, err := db.SQL(selectSql, in.Id).Get(order)
	if err != nil || !ok {
		out.Message = "没有找到对应的会员卡订单记录"
		return
	}

	sql := "UPDATE vip_card_order SET is_delete=1 WHERE id=?"
	_, err = db.Exec(sql, in.Id)
	if err != nil {
		out.Message = "会员卡订单记录删除失败"
		return
	}

	go func() {
		// 添加一个删除记录
		payTime := order.PayTime.Format(kit.DATETIME_LAYOUT)
		insertDB := GetDcDBConn()
		_, e := insertDB.Exec("INSERT INTO vip_card_operate_log(order_sn, card_name, type, user_no, user_name, pay_time, create_time) "+
			"VALUES (?, ?, 1, ?, ?, ?, NOW());", order.OrderSn, order.CardName, in.UserNo, in.UserName, payTime)
		if e != nil {
			log.Error(e.Error())
		}
	}()

	out.Code = 200
	out.Message = "执行成功"
	return
}

// GetOrderOperateLogList 会员卡订单删除日志查询
func (v *VipCardOrderService) GetOrderOperateLogList(ctx context.Context, in *oc.GetOrderOperateLogListRequest) (out *oc.GetOrderOperateLogListResponse, e error) {
	out = &oc.GetOrderOperateLogListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetOrderOperateLogList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	in.StartTime = strings.TrimSpace(in.StartTime)
	in.EndTime = strings.TrimSpace(in.EndTime)

	db := GetDcDBConn()
	session := db.Table("vip_card_operate_log").Alias("vcol")
	session.And("vcol.type = 1")
	if len(in.StartTime) > 0 {
		session.And("vcol.create_time >= ?", in.StartTime)
	}
	if len(in.EndTime) > 0 {
		session.And("vcol.create_time <= ?", in.EndTime)
	}

	if total, err := session.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		out.Total = int32(total)
		session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize))
		if err = session.Select("vcol.*").OrderBy("id desc").Find(&out.Data); err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
	}

	out.Code = 200
	return
}

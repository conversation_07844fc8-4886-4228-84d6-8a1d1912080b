package dto

import "encoding/xml"

const (
	CommonlyDeliveryOrder      = "JYCK"
	ExchangeGoodsDeliveryOrder = "HHCK"
	MakeUpDeliveryOrder        = "BFCK"
	OtherDeliveryOrder         = "QTCK"
)

// ------------ 发货单创建请求参数 ------------- //
type DeliveryOrderCreateRequest struct {
	XMLName              xml.Name              `xml:"request"`
	DeliveryOrderRequest *DeliveryOrderRequest `xml:"deliveryOrder,omitempty"`        // 发货单信息
	OrderLines           []*OrderLineRequest   `xml:"orderLines>orderLine,omitempty"` // 订单列表
	ExtendProps          map[string]string     `xml:"extendProps,omitempty"`          // 扩展属性
}

// 	发货单信息
type DeliveryOrderRequest struct {
	DeliveryOrderCode    string               `xml:"deliveryOrderCode"`              // 出库单号(必填)
	PreDeliveryOrderCode string               `xml:"preDeliveryOrderCode,omitempty"` // 原出库单号(ERP分配)
	PreDeliveryOrderId   string               `xml:"preDeliveryOrderId,omitempty"`   // 原出库单号(WMS分配)
	OrderType            string               `xml:"orderType"`                      // 出库单类型(必填)(JYCK=一般交易出库单;HHCK=换货出库单;BFCK=补发出库单;QTCK=其他出 库 单)
	WarehouseCode        string               `xml:"warehouseCode"`                  // 仓库编码(必填)(统仓统配等无需ERP指定仓储编码的情况填OTHER)
	OrderFlag            string               `xml:"orderFlag,omitempty"`            // 订单标记(用字符串格式来表示订单标记列表:例如COD=货到付款;LIMIT=限时配 送;PRESELL=预 售;COMPLAIN=已投诉;SPLIT=拆单;EXCHANGE=换货;VISIT=上门;MODIFYTRANSPORT=是否 可改配送方式;CONSIGN = 物流宝代理发货;SELLER_AFFORD=是否卖家承担运费;FENXIAO=分销订 单)
	SourcePlatformCode   string               `xml:"sourcePlatformCode,omitempty"`   // 订单来源平台编码(TB=淘宝、TM=天猫、JD=京东、DD=当当、PP=拍拍、YX= 易讯、 EBAY=ebay、QQ=QQ网购、AMAZON=亚马逊、SN=苏宁、GM=国美、WPH=唯品会、JM=聚美、LF=乐蜂 、MGJ=蘑菇街、 JS=聚尚、PX=拍鞋、YT=银泰、YHD=1号店、VANCL=凡客、YL=邮乐、YG=优购、1688=阿 里巴巴、POS=POS门店、 MIA=蜜芽、OTHER=其他(只传英文编码))
	SourcePlatformName   string               `xml:"sourcePlatformName,omitempty"`   // 订单来源平台名称
	CreateTime           string               `xml:"createTime"`                     // 发货单创建时间(必填)(YYYY-MM-DD HH:MM:SS)
	PlaceOrderTime       string               `xml:"placeOrderTime"`                 // 前台订单/店铺订单的创建时间/下单时间(必填)
	PayTime              string               `xml:"payTime,omitempty"`              // 订单支付时间(YYYY-MM-DD HH:MM:SS)
	PayNo                string               `xml:"payNo,omitempty"`                // 支付平台交易号
	OperatorCode         string               `xml:"operatorCode,omitempty"`         // 操作员(审核员)编码
	OperatorName         string               `xml:"operatorName,omitempty"`         // 操作员(审核员)名称
	OperateTime          string               `xml:"operateTime"`                    // 操作(审核)时间(YYYY-MM-DD HH:MM:SS)(必填)
	ShopCode             string               `xml:"shopCode"`                       // 店铺编码(必填)
	ShopNick             string               `xml:"shopNick"`                       // 店铺名称(必填)
	SellerNick           string               `xml:"sellerNick,omitempty"`           // 卖家名称
	BuyerNick            string               `xml:"buyerNick,omitempty"`            // 买家昵称
	TotalAmount          string               `xml:"totalAmount,omitempty"`          // 订单总金额(订单总金额=应收金额+已收金额=商品总金额-订单折扣金额+快递费用 ;单位 元)
	ItemAmount           string               `xml:"itemAmount,omitempty"`           // 商品总金额(元)
	DiscountAmount       string               `xml:"discountAmount,omitempty"`       // 订单折扣金额(元)
	Freight              string               `xml:"freight,omitempty"`              // 快递费用(元)
	ArAmount             string               `xml:"arAmount,omitempty"`             // 应收金额(消费者还需要支付多少--货到付款时消费者还需要支付多少约定使用这个字 段;单位元 )
	GotAmount            string               `xml:"gotAmount,omitempty"`            // 已收金额(消费者已经支付多少;单位元)
	ServiceFee           string               `xml:"serviceFee,omitempty"`           // COD服务费
	LogisticsCode        string               `xml:"logisticsCode"`                  // 物流公司编码(必填)(SF=顺丰、EMS=标准快递、EYB=经济快件、ZJS=宅急送、YTO=圆通 、ZTO=中 通(ZTO)、HTKY=百世汇通、UC=优速、STO=申通、TTKDEX=天天快递、QFKD=全峰、FAST=快捷 、POSTB=邮政小包、 GTO=国通、YUNDA=韵达、JD=京东配送、DD=当当宅配、AMAZON=亚马逊物流、 OTHER=其他)
	LogisticsName        string               `xml:"logisticsName,omitempty"`        // 物流公司名称
	ExpressCode          string               `xml:"expressCode,omitempty"`          // 运单号
	LogisticsAreaCode    string               `xml:"logisticsAreaCode,omitempty"`    // 快递区域编码
	DeliveryRequirements DeliveryRequirements `xml:"deliveryRequirements,omitempty"` // 发货要求列表
	SenderInfo           SenderInfo           `xml:"senderInfo,omitempty"`           // 发件人信息
	ReceiverInfo         ReceiverInfo         `xml:"receiverInfo,omitempty"`         // 收件人信息
	IsUrgency            string               `xml:"isUrgency,omitempty"`            // 是否紧急(Y/N;默认为N)
	InvoiceFlag          string               `xml:"invoiceFlag,omitempty"`          // 是否需要发票(Y/N;默认为N)
	Invoices             []InvoiceInfo        `xml:"invoices,omitempty"`             // 发票信息
	InsuranceFlag        string               `xml:"insuranceFlag,omitempty"`        // 是否需要保险(Y/N;默认为N)
	Insurance            Insurance            `xml:"insurance,omitempty"`            // 保险信息
	BuyerMessage         string               `xml:"buyerMessage,omitempty"`         // 买家留言
	SellerMessage        string               `xml:"sellerMessage,omitempty"`        // 卖家留言
	Remark               string               `xml:"remark,omitempty"`               // 备注
	ServiceCode          string               `xml:"serviceCode,omitempty"`          // 服务编码
	OwnerCode            string               `xml:"ownerCode,omitempty"`            // 旧版本货主编码
	LatestCollectionTime string               `xml:"latestCollectionTime,omitempty"` // 最晚揽收时间, string (19) , YYYY-MM-DD HH:MM:SS
	LatestDeliveryTime   string               `xml:"latestDeliveryTime,omitempty"`   // 最晚发货时间, string (19) , YYYY-MM-DD HH:MM:SS
}

// 保险信息
type Insurance struct {
	Type   string `xml:"type,omitempty"`   // 保险类型
	Amount string `xml:"amount,omitempty"` // 保险金额
}

// 发票信息
type InvoiceInfo struct {
	Type       string     `xml:"type,omitempty"`      // 发票类型(INVOICE=普通发票;VINVOICE=增值税普通发票;EVINVOICE=电子增票;填写的 条件 是:invoiceFlag为Y)
	Header     string     `xml:"header,omitempty"`    // 发票抬头(填写的条件是:invoiceFlag为Y)
	Amount     string     `xml:"amount,omitempty"`    // 发票总金额(填写的条件是:invoiceFlag为Y)
	Content    string     `xml:"content,omitempty"`   // 发票内容(不推荐使用)
	TaxNumber  string     `xml:"taxNumber,omitempty"` // 税号
	DetailInfo DetailInfo `xml:"detail,omitempty"`    // 当content和detail同时存在时，优先处理detail的信息
}

// 当content和detail同时存在时，优先处理detail的信息
type DetailInfo struct {
	Items []ItemInfo `xml:"items,omitempty"` // 商品列表
}

// 商品列表
type ItemInfo struct {
	ItemName string `xml:"itemName,omitempty"` // 商品名称
	Unit     string `xml:"unit,omitempty"`     // 商品单位
	Price    string `xml:"price,omitempty"`    // 商品单价
	Quantity string `xml:"quantity,omitempty"` // 数量
	Amount   string `xml:"amount,omitempty"`   // 金额
}

// 收件人信息
type ReceiverInfo struct {
	Company       string `xml:"company,omitempty"`     // 公司名称
	Name          string `xml:"name"`                  // 姓名(必填)
	ZipCode       string `xml:"zipCode,omitempty"`     // 邮编
	Tel           string `xml:"tel,omitempty"`         // 固定电话
	Mobile        string `xml:"mobile"`                // 移动电话(必填)
	IdType        string `xml:"idType,omitempty"`      // 收件人证件类型(1-身份证、2-军官证、3-护照、4-其他)
	IdNumber      string `xml:"idNumber,omitempty"`    // 收件人证件号码
	Email         string `xml:"email,omitempty"`       // 邮箱
	CountryCode   string `xml:"countryCode,omitempty"` // 国家二字码
	Province      string `xml:"province"`              // 省份(必填)
	City          string `xml:"city"`                  // 城市(必填)
	Area          string `xml:"area,omitempty"`        // 区域(必填)
	Town          string `xml:"town,omitempty"`        // 村镇
	DetailAddress string `xml:"detailAddress"`         // 详细地址(必填)
}

// 	发件人信息
type SenderInfo struct {
	Company       string `xml:"company,omitempty"`     // 公司名称
	Name          string `xml:"name"`                  // 姓名(必填)
	ZipCode       string `xml:"zipCode,omitempty"`     // 邮编
	Tel           string `xml:"tel,omitempty"`         // 固定电话
	Mobile        string `xml:"mobile"`                // 移动电话(必填)
	Email         string `xml:"email,omitempty"`       // 邮箱
	CountryCode   string `xml:"countryCode,omitempty"` //	国家二字码
	Province      string `xml:"province"`              // 省份(必填)
	City          string `xml:"city"`                  // 城市(必填)
	Area          string `xml:"area,omitempty"`        // 区域(必填)
	Town          string `xml:"town,omitempty"`        // 村镇
	DetailAddress string `xml:"detailAddress"`         // 详细地址(必填)
}

// 	发货要求列表
type DeliveryRequirements struct {
	ScheduleType      int    `xml:"scheduleType,omitempty"`      // 投递时延要求(1=工作日;2=节假日;101=当日达;102=次晨达;103=次日达;104=预约达;105=隔日达)
	ScheduleDay       string `xml:"scheduleDay,omitempty"`       // 要求送达日期(YYYY-MM-DD)
	ScheduleStartTime string `xml:"scheduleStartTime,omitempty"` // 投递时间范围要求(开始时间;格式：HH:MM:SS)
	ScheduleEndTime   string `xml:"scheduleEndTime,omitempty"`   // 投递时间范围要求(结束时间;格式：HH:MM:SS)
	DeliveryType      string `xml:"deliveryType,omitempty"`      // 发货服务类型(PTPS:普通配送;LLPS:冷链配送;HBP:环保配)
}

// 订单列表
type OrderLineRequest struct {
	OrderLineNo        string `xml:"orderLineNo,omitempty"`        // 单据行号
	SourceOrderCode    string `xml:"sourceOrderCode,omitempty"`    // 交易平台订单
	SubSourceOrderCode string `xml:"subSourceOrderCode,omitempty"` // 交易平台子订单编码
	PayNo              string `xml:"payNo,omitempty"`              // 支付平台交易号(淘系订单传支付宝交易号)
	OwnerCode          string `xml:"ownerCode"`                    // 货主编码(必填,目前没用到,随便填)
	ItemCode           string `xml:"itemCode"`                     // 商品编码(必填)
	ItemId             string `xml:"itemId,omitempty"`             // 仓储系统商品编码
	InventoryType      string `xml:"inventoryType,omitempty"`      // 库存类型(ZP=正品;CC=残次;JS=机损;XS= 箱损;ZT=在途库存;默认为查所有类型的库存)
	ItemName           string `xml:"itemName,omitempty"`           // 商品名称
	ExtCode            string `xml:"extCode,omitempty"`            // 交易平台商品编码
	PlanQty            int32  `xml:"planQty"`                      // 应发商品数量(必填)
	RetailPrice        string `xml:"retailPrice,omitempty"`        // 零售价(零售价=实际成交价+单件商品折扣金额)
	ActualPrice        string `xml:"actualPrice"`                  // 实际成交价(必填)
	DiscountAmount     string `xml:"discountAmount,omitempty"`     // 单件商品折扣金额
	BatchCode          string `xml:"batchCode,omitempty"`          // 批次编码
	ProductDate        string `xml:"productDate,omitempty"`        // 生产日期(YYYY-MM-DD)
	ExpireDate         string `xml:"expireDate,omitempty"`         // 过期日期(YYYY-MM-DD)
	ProduceCode        string `xml:"produceCode,omitempty"`        // 生产批号
}

// ------------ 发货单创建响应参数 ------------- //
type DeliveryOrderCreateResponse struct {
	Flag            string                `xml:"flag"`            // 响应结果:success|failure
	Code            string                `xml:"code"`            // 响应码
	Message         string                `xml:"message"`         // 响应信息
	CreateTime      string                `xml:"createTime"`      // 订单创建时间(YYYY-MM-DD HH:MM:SS)
	DeliveryOrderId string                `xml:"deliveryOrderId"` // 出库单仓储系统编码
	WarehouseCode   string                `xml:"warehouseCode"`   // 仓库编码(统仓统配使用)
	LogisticsCode   string                `xml:"logisticsCode"`   // 物流公司编码(统仓统配使用)
	DeliveryOrders  []DeliveryOrderRespon `xml:"deliveryOrders"`  // 发货单信息
}

type DeliveryOrderRespon struct {
	DeliveryOrderId string            `xml:"deliveryOrderId"` // 出库单仓储系统编码
	WarehouseCode   string            `xml:"warehouseCode"`   // 仓库编码(统仓统配使用)
	LogisticsCode   string            `xml:"logisticsCode"`   // 物流公司编码(统仓统配使用)
	CreateTime      string            `xml:"createTime"`      // 订单创建时间(YYYY-MM-DD HH:MM:SS)
	OrderLines      []OrderLineRespon `xml:"orderLines"`      // 订单信息
}

type OrderLineRespon struct {
	OrderLineNo string `xml:"orderLineNo"` // 行号
	ItemCode    string `xml:"itemCode"`    // ERP商品编码
	ItemId      string `xml:"itemId"`      // WMS商品编码
	Quantity    string `xml:"quantity"`    // 数量
}

syntax = "proto3";

package mk;

//活动全局配置模板
message PromotionConfiguration {
  //id
  int32 id = 1;
  //门店id
  string shopid = 2;
  //类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotiontype = 3;
  //类型 1 不限制 2 自定义
  int32 buytype = 4;
  //购买总数
  int32 buycount = 5;
  //状态:1:正常;2:冻结;
  int32 status = 6;
  //修改人id
  string modifyid = 7;
  //修改时间
  string modifytime = 8;
  //创建人id
  string createid = 9;
  //创建时间
  string createtime = 10;
}


//获取活动全局配置详情 => Request
message GetPromotionConfigurationDetailRequest {
  //ids
  string ids = 1;
  //门店ids
  string shopIds = 2;
  //类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 3;
  //排序类型：createTimeDesc:根据创建时间倒序
  string sort = 4;
  //状态:1:正常;2:冻结;
  int32 status = 5;
}
//获取活动全局配置详情 => Response
message GetPromotionConfigurationDetailResponse {
  //状态码
  int32 code = 1;
  //响应信息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  repeated PromotionConfiguration promotionConfiguration_list = 4;
}

//创建活动全局配置 => Request
message PromotionConfigurationCreateInfoRequest {
  //类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 1;
  //类型 1 不限制 2 自定义
  int32 buy_type = 2;
  //购买总数
  int32 buy_count = 3;
  //修改人id
  string modify_id = 4;
  //创建人id
  string create_id = 5;
  //门店id
  string shopIds = 6;

}
//创建活动全局配置 => Response
message PromotionConfigurationCreateInfoResponse {
  //状态码
  int32 code = 1;
  //响应信息
  string message = 2;
  //错误信息
  string error = 3;
}

//更新活动全局配置信息 => Request
message PromotionConfigurationUpdateInfoRequest {
  //id
  string ids = 1;
  //门店id
  string shopId = 2;
  //类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 3;
  //类型 1 不限制 2 自定义
  int32 buy_type = 4;
  //购买总数
  int32 buy_count = 5;
  //修改人id
  string modify_id = 6;
}
//更新活动全局配置信息 => Response
message PromotionConfigurationUpdateInfoResponse {
  //状态码
  int32 code = 1;
  //响应信息
  string message = 2;
  //错误信息
  string error = 3;
}

//replace活动全局配置信息 => Request
message PromotionConfigurationReplaceInfoRequest {
  //门店ids
  string shopIds = 1;
  //类型 1 满减活动 2 限时折扣 3 满减运费
  int32 promotion_type = 2;
  //类型 1 不限制 2 自定义
  int32 buy_type = 3;
  //购买总数
  int32 buy_count = 4;
  //修改人id
  string modify_id = 5;
}
//replace活动全局配置信息 => Response
message PromotionConfigurationReplaceInfoResponse {
  //状态码
  int32 code = 1;
  //响应信息
  string message = 2;
  //错误信息
  string error = 3;
}
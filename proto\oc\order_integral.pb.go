// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/order_integral.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 积分处理通知
type IntegralNotify struct {
	// 父订单
	Oldordersn string `protobuf:"bytes,1,opt,name=Oldordersn,proto3" json:"Oldordersn"`
	// 订单号
	Ordersn string `protobuf:"bytes,2,opt,name=Ordersn,proto3" json:"Ordersn"`
	// 积分类型
	Integraltype int32 `protobuf:"varint,3,opt,name=Integraltype,proto3" json:"Integraltype"`
	// 通知创建时间
	Notifytime string `protobuf:"bytes,4,opt,name=Notifytime,proto3" json:"Notifytime"`
	// 订单来源
	Orderfrom int32 `protobuf:"varint,5,opt,name=Orderfrom,proto3" json:"Orderfrom"`
	// 买家手机号
	Mobile string `protobuf:"bytes,6,opt,name=Mobile,proto3" json:"Mobile"`
	// 退款单号
	Refundsn string `protobuf:"bytes,7,opt,name=Refundsn,proto3" json:"Refundsn"`
	// 父订单总金额
	TotalAmount int32 `protobuf:"varint,8,opt,name=TotalAmount,proto3" json:"TotalAmount"`
	// 退款金额
	RefundAmount int32 `protobuf:"varint,9,opt,name=RefundAmount,proto3" json:"RefundAmount"`
	// 用户编号
	MemberId string `protobuf:"bytes,10,opt,name=MemberId,proto3" json:"MemberId"`
	// 门店编码
	ShopCode string `protobuf:"bytes,11,opt,name=ShopCode,proto3" json:"ShopCode"`
	// 本次加/减积分的数量
	Integral int32 `protobuf:"varint,12,opt,name=integral,proto3" json:"integral"`
	// 本次加/减积分的数量
	OrgId                int32    `protobuf:"varint,13,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntegralNotify) Reset()         { *m = IntegralNotify{} }
func (m *IntegralNotify) String() string { return proto.CompactTextString(m) }
func (*IntegralNotify) ProtoMessage()    {}
func (*IntegralNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{0}
}

func (m *IntegralNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntegralNotify.Unmarshal(m, b)
}
func (m *IntegralNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntegralNotify.Marshal(b, m, deterministic)
}
func (m *IntegralNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntegralNotify.Merge(m, src)
}
func (m *IntegralNotify) XXX_Size() int {
	return xxx_messageInfo_IntegralNotify.Size(m)
}
func (m *IntegralNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_IntegralNotify.DiscardUnknown(m)
}

var xxx_messageInfo_IntegralNotify proto.InternalMessageInfo

func (m *IntegralNotify) GetOldordersn() string {
	if m != nil {
		return m.Oldordersn
	}
	return ""
}

func (m *IntegralNotify) GetOrdersn() string {
	if m != nil {
		return m.Ordersn
	}
	return ""
}

func (m *IntegralNotify) GetIntegraltype() int32 {
	if m != nil {
		return m.Integraltype
	}
	return 0
}

func (m *IntegralNotify) GetNotifytime() string {
	if m != nil {
		return m.Notifytime
	}
	return ""
}

func (m *IntegralNotify) GetOrderfrom() int32 {
	if m != nil {
		return m.Orderfrom
	}
	return 0
}

func (m *IntegralNotify) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *IntegralNotify) GetRefundsn() string {
	if m != nil {
		return m.Refundsn
	}
	return ""
}

func (m *IntegralNotify) GetTotalAmount() int32 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *IntegralNotify) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *IntegralNotify) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *IntegralNotify) GetShopCode() string {
	if m != nil {
		return m.ShopCode
	}
	return ""
}

func (m *IntegralNotify) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *IntegralNotify) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// @Desc    	积分查询请求
// <AUTHOR>
// @Date		2020-08-18
type OrderIntegralQueryRequest struct {
	StartTime            string   `protobuf:"bytes,1,opt,name=StartTime,proto3" json:"StartTime"`
	EndTime              string   `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime"`
	ShopName             string   `protobuf:"bytes,3,opt,name=ShopName,proto3" json:"ShopName"`
	Code                 string   `protobuf:"bytes,4,opt,name=Code,proto3" json:"Code"`
	City                 string   `protobuf:"bytes,5,opt,name=City,proto3" json:"City"`
	MobileTel            string   `protobuf:"bytes,6,opt,name=MobileTel,proto3" json:"MobileTel"`
	UserName             string   `protobuf:"bytes,7,opt,name=UserName,proto3" json:"UserName"`
	PageIndex            int32    `protobuf:"varint,8,opt,name=PageIndex,proto3" json:"PageIndex"`
	PageSize             int32    `protobuf:"varint,9,opt,name=PageSize,proto3" json:"PageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIntegralQueryRequest) Reset()         { *m = OrderIntegralQueryRequest{} }
func (m *OrderIntegralQueryRequest) String() string { return proto.CompactTextString(m) }
func (*OrderIntegralQueryRequest) ProtoMessage()    {}
func (*OrderIntegralQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{1}
}

func (m *OrderIntegralQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIntegralQueryRequest.Unmarshal(m, b)
}
func (m *OrderIntegralQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIntegralQueryRequest.Marshal(b, m, deterministic)
}
func (m *OrderIntegralQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIntegralQueryRequest.Merge(m, src)
}
func (m *OrderIntegralQueryRequest) XXX_Size() int {
	return xxx_messageInfo_OrderIntegralQueryRequest.Size(m)
}
func (m *OrderIntegralQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIntegralQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIntegralQueryRequest proto.InternalMessageInfo

func (m *OrderIntegralQueryRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetMobileTel() string {
	if m != nil {
		return m.MobileTel
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *OrderIntegralQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *OrderIntegralQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// @Desc    	积分导出请求
// <AUTHOR>
// @Date		2020-08-18
type OrderIntegralExportRequest struct {
	StartTime            string   `protobuf:"bytes,1,opt,name=StartTime,proto3" json:"StartTime"`
	EndTime              string   `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime"`
	ShopName             string   `protobuf:"bytes,3,opt,name=ShopName,proto3" json:"ShopName"`
	Code                 string   `protobuf:"bytes,4,opt,name=Code,proto3" json:"Code"`
	City                 string   `protobuf:"bytes,5,opt,name=City,proto3" json:"City"`
	MobileTel            string   `protobuf:"bytes,6,opt,name=MobileTel,proto3" json:"MobileTel"`
	UserName             string   `protobuf:"bytes,7,opt,name=UserName,proto3" json:"UserName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIntegralExportRequest) Reset()         { *m = OrderIntegralExportRequest{} }
func (m *OrderIntegralExportRequest) String() string { return proto.CompactTextString(m) }
func (*OrderIntegralExportRequest) ProtoMessage()    {}
func (*OrderIntegralExportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{2}
}

func (m *OrderIntegralExportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIntegralExportRequest.Unmarshal(m, b)
}
func (m *OrderIntegralExportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIntegralExportRequest.Marshal(b, m, deterministic)
}
func (m *OrderIntegralExportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIntegralExportRequest.Merge(m, src)
}
func (m *OrderIntegralExportRequest) XXX_Size() int {
	return xxx_messageInfo_OrderIntegralExportRequest.Size(m)
}
func (m *OrderIntegralExportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIntegralExportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIntegralExportRequest proto.InternalMessageInfo

func (m *OrderIntegralExportRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetMobileTel() string {
	if m != nil {
		return m.MobileTel
	}
	return ""
}

func (m *OrderIntegralExportRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

// @Desc    	积分响应
// <AUTHOR>
// @Date		2020-08-18
type OrderIntegralResponse struct {
	Code                 int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Totalcount           int64                        `protobuf:"varint,3,opt,name=totalcount,proto3" json:"totalcount"`
	Totalintegral        int64                        `protobuf:"varint,4,opt,name=totalintegral,proto3" json:"totalintegral"`
	Data                 []*OrderIntegralResponseData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *OrderIntegralResponse) Reset()         { *m = OrderIntegralResponse{} }
func (m *OrderIntegralResponse) String() string { return proto.CompactTextString(m) }
func (*OrderIntegralResponse) ProtoMessage()    {}
func (*OrderIntegralResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{3}
}

func (m *OrderIntegralResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIntegralResponse.Unmarshal(m, b)
}
func (m *OrderIntegralResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIntegralResponse.Marshal(b, m, deterministic)
}
func (m *OrderIntegralResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIntegralResponse.Merge(m, src)
}
func (m *OrderIntegralResponse) XXX_Size() int {
	return xxx_messageInfo_OrderIntegralResponse.Size(m)
}
func (m *OrderIntegralResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIntegralResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIntegralResponse proto.InternalMessageInfo

func (m *OrderIntegralResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderIntegralResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OrderIntegralResponse) GetTotalcount() int64 {
	if m != nil {
		return m.Totalcount
	}
	return 0
}

func (m *OrderIntegralResponse) GetTotalintegral() int64 {
	if m != nil {
		return m.Totalintegral
	}
	return 0
}

func (m *OrderIntegralResponse) GetData() []*OrderIntegralResponseData {
	if m != nil {
		return m.Data
	}
	return nil
}

// @Desc    	积分查询响应结果
// <AUTHOR>
// @Date		2020-08-18
type OrderIntegralResponseData struct {
	Ordertime            string   `protobuf:"bytes,1,opt,name=ordertime,proto3" json:"ordertime"`
	Orderon              string   `protobuf:"bytes,2,opt,name=orderon,proto3" json:"orderon"`
	Oldordersn           string   `protobuf:"bytes,3,opt,name=oldordersn,proto3" json:"oldordersn"`
	Userinfo             string   `protobuf:"bytes,4,opt,name=userinfo,proto3" json:"userinfo"`
	Shopname             string   `protobuf:"bytes,5,opt,name=shopname,proto3" json:"shopname"`
	Code                 string   `protobuf:"bytes,6,opt,name=code,proto3" json:"code"`
	City                 string   `protobuf:"bytes,7,opt,name=city,proto3" json:"city"`
	Integral             int64    `protobuf:"varint,8,opt,name=integral,proto3" json:"integral"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIntegralResponseData) Reset()         { *m = OrderIntegralResponseData{} }
func (m *OrderIntegralResponseData) String() string { return proto.CompactTextString(m) }
func (*OrderIntegralResponseData) ProtoMessage()    {}
func (*OrderIntegralResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{4}
}

func (m *OrderIntegralResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIntegralResponseData.Unmarshal(m, b)
}
func (m *OrderIntegralResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIntegralResponseData.Marshal(b, m, deterministic)
}
func (m *OrderIntegralResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIntegralResponseData.Merge(m, src)
}
func (m *OrderIntegralResponseData) XXX_Size() int {
	return xxx_messageInfo_OrderIntegralResponseData.Size(m)
}
func (m *OrderIntegralResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIntegralResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIntegralResponseData proto.InternalMessageInfo

func (m *OrderIntegralResponseData) GetOrdertime() string {
	if m != nil {
		return m.Ordertime
	}
	return ""
}

func (m *OrderIntegralResponseData) GetOrderon() string {
	if m != nil {
		return m.Orderon
	}
	return ""
}

func (m *OrderIntegralResponseData) GetOldordersn() string {
	if m != nil {
		return m.Oldordersn
	}
	return ""
}

func (m *OrderIntegralResponseData) GetUserinfo() string {
	if m != nil {
		return m.Userinfo
	}
	return ""
}

func (m *OrderIntegralResponseData) GetShopname() string {
	if m != nil {
		return m.Shopname
	}
	return ""
}

func (m *OrderIntegralResponseData) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *OrderIntegralResponseData) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *OrderIntegralResponseData) GetIntegral() int64 {
	if m != nil {
		return m.Integral
	}
	return 0
}

// @Desc    	订单积分同步请求
// <AUTHOR>
// @Date		2020-08-27
type MemberIntegralRecordRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=OrderSn,proto3" json:"OrderSn"`
	OrderAmount          int32    `protobuf:"varint,2,opt,name=OrderAmount,proto3" json:"OrderAmount"`
	OldOrderSn           string   `protobuf:"bytes,3,opt,name=OldOrderSn,proto3" json:"OldOrderSn"`
	ShopCode             string   `protobuf:"bytes,4,opt,name=ShopCode,proto3" json:"ShopCode"`
	Integral             int32    `protobuf:"varint,5,opt,name=Integral,proto3" json:"Integral"`
	CurrentStatus        int32    `protobuf:"varint,6,opt,name=CurrentStatus,proto3" json:"CurrentStatus"`
	IntegralType         int32    `protobuf:"varint,7,opt,name=IntegralType,proto3" json:"IntegralType"`
	CreateTime           string   `protobuf:"bytes,8,opt,name=CreateTime,proto3" json:"CreateTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberIntegralRecordRequest) Reset()         { *m = MemberIntegralRecordRequest{} }
func (m *MemberIntegralRecordRequest) String() string { return proto.CompactTextString(m) }
func (*MemberIntegralRecordRequest) ProtoMessage()    {}
func (*MemberIntegralRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{5}
}

func (m *MemberIntegralRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberIntegralRecordRequest.Unmarshal(m, b)
}
func (m *MemberIntegralRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberIntegralRecordRequest.Marshal(b, m, deterministic)
}
func (m *MemberIntegralRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberIntegralRecordRequest.Merge(m, src)
}
func (m *MemberIntegralRecordRequest) XXX_Size() int {
	return xxx_messageInfo_MemberIntegralRecordRequest.Size(m)
}
func (m *MemberIntegralRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberIntegralRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberIntegralRecordRequest proto.InternalMessageInfo

func (m *MemberIntegralRecordRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *MemberIntegralRecordRequest) GetOrderAmount() int32 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *MemberIntegralRecordRequest) GetOldOrderSn() string {
	if m != nil {
		return m.OldOrderSn
	}
	return ""
}

func (m *MemberIntegralRecordRequest) GetShopCode() string {
	if m != nil {
		return m.ShopCode
	}
	return ""
}

func (m *MemberIntegralRecordRequest) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *MemberIntegralRecordRequest) GetCurrentStatus() int32 {
	if m != nil {
		return m.CurrentStatus
	}
	return 0
}

func (m *MemberIntegralRecordRequest) GetIntegralType() int32 {
	if m != nil {
		return m.IntegralType
	}
	return 0
}

func (m *MemberIntegralRecordRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

// @Desc    	订单积分同步响应
// <AUTHOR>
// @Date		2020-08-27
type OrderIntegralSynchronizeResponse struct {
	Message              string   `protobuf:"bytes,1,opt,name=Message,proto3" json:"Message"`
	Result               bool     `protobuf:"varint,2,opt,name=Result,proto3" json:"Result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIntegralSynchronizeResponse) Reset()         { *m = OrderIntegralSynchronizeResponse{} }
func (m *OrderIntegralSynchronizeResponse) String() string { return proto.CompactTextString(m) }
func (*OrderIntegralSynchronizeResponse) ProtoMessage()    {}
func (*OrderIntegralSynchronizeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{6}
}

func (m *OrderIntegralSynchronizeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIntegralSynchronizeResponse.Unmarshal(m, b)
}
func (m *OrderIntegralSynchronizeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIntegralSynchronizeResponse.Marshal(b, m, deterministic)
}
func (m *OrderIntegralSynchronizeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIntegralSynchronizeResponse.Merge(m, src)
}
func (m *OrderIntegralSynchronizeResponse) XXX_Size() int {
	return xxx_messageInfo_OrderIntegralSynchronizeResponse.Size(m)
}
func (m *OrderIntegralSynchronizeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIntegralSynchronizeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIntegralSynchronizeResponse proto.InternalMessageInfo

func (m *OrderIntegralSynchronizeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OrderIntegralSynchronizeResponse) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

// @Desc    	历史订单请求
// <AUTHOR>
// @Date		2020-08-27
type HistoryOrderRequest struct {
	StartTime            string   `protobuf:"bytes,1,opt,name=StartTime,proto3" json:"StartTime"`
	EndTime              string   `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime"`
	OrderSn              string   `protobuf:"bytes,3,opt,name=OrderSn,proto3" json:"OrderSn"`
	MemberTle            string   `protobuf:"bytes,4,opt,name=MemberTle,proto3" json:"MemberTle"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HistoryOrderRequest) Reset()         { *m = HistoryOrderRequest{} }
func (m *HistoryOrderRequest) String() string { return proto.CompactTextString(m) }
func (*HistoryOrderRequest) ProtoMessage()    {}
func (*HistoryOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37a8b8e7f95493c, []int{7}
}

func (m *HistoryOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HistoryOrderRequest.Unmarshal(m, b)
}
func (m *HistoryOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HistoryOrderRequest.Marshal(b, m, deterministic)
}
func (m *HistoryOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistoryOrderRequest.Merge(m, src)
}
func (m *HistoryOrderRequest) XXX_Size() int {
	return xxx_messageInfo_HistoryOrderRequest.Size(m)
}
func (m *HistoryOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HistoryOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HistoryOrderRequest proto.InternalMessageInfo

func (m *HistoryOrderRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *HistoryOrderRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *HistoryOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *HistoryOrderRequest) GetMemberTle() string {
	if m != nil {
		return m.MemberTle
	}
	return ""
}

func init() {
	proto.RegisterType((*IntegralNotify)(nil), "oc.IntegralNotify")
	proto.RegisterType((*OrderIntegralQueryRequest)(nil), "oc.OrderIntegralQueryRequest")
	proto.RegisterType((*OrderIntegralExportRequest)(nil), "oc.OrderIntegralExportRequest")
	proto.RegisterType((*OrderIntegralResponse)(nil), "oc.OrderIntegralResponse")
	proto.RegisterType((*OrderIntegralResponseData)(nil), "oc.OrderIntegralResponseData")
	proto.RegisterType((*MemberIntegralRecordRequest)(nil), "oc.MemberIntegralRecordRequest")
	proto.RegisterType((*OrderIntegralSynchronizeResponse)(nil), "oc.OrderIntegralSynchronizeResponse")
	proto.RegisterType((*HistoryOrderRequest)(nil), "oc.HistoryOrderRequest")
}

func init() { proto.RegisterFile("oc/order_integral.proto", fileDescriptor_d37a8b8e7f95493c) }

var fileDescriptor_d37a8b8e7f95493c = []byte{
	// 786 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x56, 0xcb, 0x72, 0xd3, 0x3c,
	0x18, 0xad, 0x73, 0x69, 0x52, 0xb5, 0xfd, 0x17, 0x6a, 0xff, 0xbf, 0x6a, 0x7e, 0x2e, 0x19, 0x4f,
	0x17, 0x5d, 0x95, 0xa1, 0x3c, 0x41, 0xa7, 0x74, 0x86, 0x2c, 0x7a, 0xc1, 0x09, 0x4b, 0x60, 0x5c,
	0x5b, 0x49, 0x3d, 0x93, 0x58, 0x41, 0x96, 0xa1, 0xe9, 0x9e, 0x15, 0x1b, 0xf6, 0x3c, 0x00, 0x6f,
	0xc1, 0x43, 0xf0, 0x22, 0xbc, 0x02, 0xf3, 0x7d, 0xba, 0x58, 0x26, 0x81, 0x61, 0x86, 0x15, 0x3b,
	0x9f, 0x23, 0xe9, 0x93, 0x74, 0xce, 0xa7, 0x33, 0x26, 0x7b, 0x22, 0x79, 0x24, 0x64, 0xca, 0xe5,
	0xeb, 0x2c, 0x57, 0x7c, 0x22, 0xe3, 0xe9, 0xd1, 0x5c, 0x0a, 0x25, 0x68, 0x43, 0x24, 0xe1, 0xc7,
	0x26, 0xf9, 0x67, 0x60, 0xe8, 0x0b, 0xa1, 0xb2, 0xf1, 0x82, 0x3e, 0x20, 0xe4, 0x72, 0x9a, 0xe2,
	0x8a, 0x22, 0x67, 0x41, 0x3f, 0x38, 0xdc, 0x88, 0x3c, 0x86, 0x32, 0xd2, 0xb9, 0x34, 0x83, 0x0d,
	0x1c, 0xb4, 0x90, 0x86, 0x64, 0xcb, 0xd6, 0x52, 0x8b, 0x39, 0x67, 0xcd, 0x7e, 0x70, 0xd8, 0x8e,
	0x6a, 0x1c, 0x54, 0xd7, 0xfb, 0xa8, 0x6c, 0xc6, 0x59, 0x4b, 0x57, 0xaf, 0x18, 0x7a, 0x8f, 0x6c,
	0x60, 0xb9, 0xb1, 0x14, 0x33, 0xd6, 0xc6, 0x02, 0x15, 0x41, 0xff, 0x23, 0xeb, 0xe7, 0xe2, 0x3a,
	0x9b, 0x72, 0xb6, 0x8e, 0x2b, 0x0d, 0xa2, 0x3d, 0xd2, 0x8d, 0xf8, 0xb8, 0xcc, 0xd3, 0x22, 0x67,
	0x1d, 0x1c, 0x71, 0x98, 0xf6, 0xc9, 0xe6, 0x48, 0xa8, 0x78, 0x7a, 0x32, 0x13, 0x65, 0xae, 0x58,
	0x17, 0x6b, 0xfa, 0x14, 0x9c, 0x5b, 0xcf, 0x36, 0x53, 0x36, 0xf4, 0xb9, 0x7d, 0x0e, 0x76, 0x38,
	0xe7, 0xb3, 0x6b, 0x2e, 0x07, 0x29, 0x23, 0x7a, 0x07, 0x8b, 0x61, 0x6c, 0x78, 0x23, 0xe6, 0xa7,
	0x22, 0xe5, 0x6c, 0x53, 0x8f, 0x59, 0x0c, 0x63, 0x56, 0x76, 0xb6, 0x85, 0x75, 0x1d, 0xa6, 0xbb,
	0xa4, 0x7d, 0x29, 0x27, 0x83, 0x94, 0x6d, 0xe3, 0x80, 0x06, 0xe1, 0x87, 0x06, 0xd9, 0xc7, 0x1b,
	0x5b, 0xdd, 0x9e, 0x97, 0x5c, 0x2e, 0x22, 0xfe, 0xa6, 0xe4, 0x85, 0x02, 0x7d, 0x86, 0x2a, 0x96,
	0x6a, 0x04, 0xf2, 0x69, 0x73, 0x2a, 0x02, 0xbc, 0x39, 0xcb, 0x53, 0x1c, 0x33, 0xde, 0x18, 0x68,
	0xcf, 0x78, 0x11, 0xcf, 0xb4, 0x2f, 0xe6, 0x8c, 0x80, 0x29, 0x25, 0x2d, 0x3c, 0xbb, 0x76, 0x03,
	0xbf, 0x91, 0xcb, 0xd4, 0x02, 0x2d, 0x00, 0x2e, 0x53, 0x0b, 0xd8, 0x5b, 0xeb, 0x3d, 0xe2, 0x53,
	0x63, 0x40, 0x45, 0xc0, 0x0e, 0x2f, 0x0a, 0x2e, 0x71, 0x07, 0xe3, 0x81, 0xc5, 0xb0, 0xf2, 0x2a,
	0x9e, 0xf0, 0x41, 0x9e, 0xf2, 0x5b, 0xe3, 0x40, 0x45, 0xc0, 0x4a, 0x00, 0xc3, 0xec, 0x8e, 0x1b,
	0xed, 0x1d, 0x0e, 0xbf, 0x06, 0xa4, 0x57, 0x53, 0xe3, 0xec, 0x76, 0x2e, 0xa4, 0xfa, 0xab, 0xe5,
	0x08, 0xbf, 0x04, 0xe4, 0xdf, 0xda, 0xa5, 0x22, 0x5e, 0xcc, 0x45, 0x5e, 0xe0, 0x3e, 0x09, 0xec,
	0x1d, 0xa0, 0x0c, 0xf8, 0x0d, 0xb7, 0x98, 0xf1, 0xa2, 0x88, 0x27, 0xee, 0x16, 0x06, 0xc2, 0x63,
	0x52, 0xd0, 0xc7, 0x09, 0xb6, 0x2d, 0xdc, 0xa3, 0x19, 0x79, 0x0c, 0x3d, 0x20, 0xdb, 0x88, 0x5c,
	0x07, 0xb6, 0x70, 0x4a, 0x9d, 0xa4, 0x8f, 0x49, 0x2b, 0x8d, 0x55, 0xcc, 0xda, 0xfd, 0xe6, 0xe1,
	0xe6, 0xf1, 0xfd, 0x23, 0x91, 0x1c, 0xad, 0x3c, 0xdc, 0xd3, 0x58, 0xc5, 0x11, 0x4e, 0x0d, 0xbf,
	0x05, 0x3f, 0xf4, 0xa8, 0x3f, 0x07, 0x84, 0xc1, 0xb0, 0x50, 0x9e, 0x29, 0x8e, 0x80, 0xeb, 0x20,
	0x10, 0x2e, 0x3f, 0x0c, 0x84, 0xeb, 0x88, 0x2a, 0x79, 0xb4, 0x2d, 0x1e, 0x03, 0x92, 0x96, 0x05,
	0x97, 0x59, 0x3e, 0x16, 0xc6, 0x1c, 0x87, 0x61, 0xac, 0xb8, 0x11, 0xf3, 0x1c, 0xe4, 0xd6, 0x26,
	0x39, 0xec, 0x44, 0xd5, 0x1e, 0x69, 0x51, 0x81, 0x03, 0x43, 0x3b, 0x86, 0x03, 0x43, 0xfd, 0xb7,
	0xda, 0x45, 0xa5, 0x1c, 0x0e, 0x3f, 0x35, 0xc8, 0xff, 0xe6, 0xc1, 0xbb, 0x2b, 0x27, 0x42, 0xa6,
	0xb6, 0x11, 0x6d, 0x2a, 0x0e, 0x6d, 0x64, 0x5a, 0x08, 0xf9, 0x83, 0x9f, 0x26, 0x5c, 0x1a, 0x3a,
	0x7f, 0x3c, 0xca, 0x24, 0xae, 0x5d, 0xde, 0x74, 0x89, 0x6b, 0x2b, 0xf8, 0xf9, 0xd2, 0x5a, 0xce,
	0x17, 0x7b, 0x20, 0x13, 0x97, 0x0e, 0x83, 0xfd, 0xa7, 0xa5, 0x94, 0x3c, 0x57, 0x43, 0x15, 0xab,
	0xb2, 0x40, 0x01, 0xda, 0x51, 0x9d, 0xf4, 0x53, 0x7b, 0x04, 0xa9, 0xdd, 0xa9, 0xa7, 0xf6, 0xc8,
	0xa4, 0xf6, 0xa9, 0xe4, 0xb1, 0xe2, 0xf8, 0x96, 0xba, 0xfa, 0x84, 0x15, 0x13, 0x8e, 0x48, 0xbf,
	0xd6, 0x0e, 0xc3, 0x45, 0x9e, 0xdc, 0x48, 0x91, 0x67, 0x77, 0xdc, 0xb5, 0x36, 0x23, 0x9d, 0x73,
	0xd3, 0xc6, 0x46, 0x21, 0x03, 0x21, 0xd5, 0x23, 0x5e, 0x94, 0x53, 0x2d, 0x4e, 0x37, 0x32, 0x28,
	0x7c, 0x1f, 0x90, 0x9d, 0x67, 0x59, 0xa1, 0x84, 0x5c, 0x60, 0xf5, 0x3f, 0x7d, 0xf4, 0x9e, 0x47,
	0xcd, 0xba, 0x47, 0xf0, 0x94, 0xd1, 0xdc, 0xd1, 0xd4, 0x4a, 0x5c, 0x11, 0xc7, 0x9f, 0x1b, 0x64,
	0xb7, 0x7e, 0x3d, 0x2e, 0xdf, 0x66, 0x09, 0xa7, 0x57, 0x84, 0x2e, 0x27, 0x35, 0x5d, 0x7e, 0x41,
	0x7e, 0x82, 0xf7, 0xf6, 0x7f, 0xfa, 0xc0, 0xc2, 0x35, 0xfa, 0x8a, 0xec, 0xac, 0xd0, 0x90, 0x3e,
	0x84, 0x35, 0xbf, 0x68, 0xbf, 0xde, 0xc1, 0x52, 0xd1, 0x15, 0x16, 0x84, 0x6b, 0xf4, 0x25, 0xe9,
	0x9d, 0xbc, 0xf3, 0x35, 0xf5, 0xa7, 0xd3, 0x3d, 0xa8, 0xb2, 0x42, 0xf1, 0xdf, 0x2d, 0x7f, 0xbd,
	0x8e, 0x7f, 0x16, 0x4f, 0xbe, 0x07, 0x00, 0x00, 0xff, 0xff, 0x78, 0xcb, 0x79, 0xb4, 0x74, 0x08,
	0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OrderIntegralServiceClient is the client API for OrderIntegralService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OrderIntegralServiceClient interface {
	// @Desc    	订单积分查询
	// <AUTHOR>
	// @Date		2020-08-18
	OrderIntegralQuery(ctx context.Context, in *OrderIntegralQueryRequest, opts ...grpc.CallOption) (*OrderIntegralResponse, error)
	// @Desc    	积分同步
	// <AUTHOR>
	// @Date		2020-08-27
	IntegralSynchronize(ctx context.Context, in *MemberIntegralRecordRequest, opts ...grpc.CallOption) (*OrderIntegralSynchronizeResponse, error)
	// @Desc    	阿闻历史订单积分同步
	// <AUTHOR>
	// @Date		2020-08-18
	AwHistoryOrderIntegralSync(ctx context.Context, in *HistoryOrderRequest, opts ...grpc.CallOption) (*OrderIntegralSynchronizeResponse, error)
}

type orderIntegralServiceClient struct {
	cc *grpc.ClientConn
}

func NewOrderIntegralServiceClient(cc *grpc.ClientConn) OrderIntegralServiceClient {
	return &orderIntegralServiceClient{cc}
}

func (c *orderIntegralServiceClient) OrderIntegralQuery(ctx context.Context, in *OrderIntegralQueryRequest, opts ...grpc.CallOption) (*OrderIntegralResponse, error) {
	out := new(OrderIntegralResponse)
	err := c.cc.Invoke(ctx, "/oc.OrderIntegralService/OrderIntegralQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderIntegralServiceClient) IntegralSynchronize(ctx context.Context, in *MemberIntegralRecordRequest, opts ...grpc.CallOption) (*OrderIntegralSynchronizeResponse, error) {
	out := new(OrderIntegralSynchronizeResponse)
	err := c.cc.Invoke(ctx, "/oc.OrderIntegralService/IntegralSynchronize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderIntegralServiceClient) AwHistoryOrderIntegralSync(ctx context.Context, in *HistoryOrderRequest, opts ...grpc.CallOption) (*OrderIntegralSynchronizeResponse, error) {
	out := new(OrderIntegralSynchronizeResponse)
	err := c.cc.Invoke(ctx, "/oc.OrderIntegralService/AwHistoryOrderIntegralSync", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderIntegralServiceServer is the server API for OrderIntegralService service.
type OrderIntegralServiceServer interface {
	// @Desc    	订单积分查询
	// <AUTHOR>
	// @Date		2020-08-18
	OrderIntegralQuery(context.Context, *OrderIntegralQueryRequest) (*OrderIntegralResponse, error)
	// @Desc    	积分同步
	// <AUTHOR>
	// @Date		2020-08-27
	IntegralSynchronize(context.Context, *MemberIntegralRecordRequest) (*OrderIntegralSynchronizeResponse, error)
	// @Desc    	阿闻历史订单积分同步
	// <AUTHOR>
	// @Date		2020-08-18
	AwHistoryOrderIntegralSync(context.Context, *HistoryOrderRequest) (*OrderIntegralSynchronizeResponse, error)
}

// UnimplementedOrderIntegralServiceServer can be embedded to have forward compatible implementations.
type UnimplementedOrderIntegralServiceServer struct {
}

func (*UnimplementedOrderIntegralServiceServer) OrderIntegralQuery(ctx context.Context, req *OrderIntegralQueryRequest) (*OrderIntegralResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderIntegralQuery not implemented")
}
func (*UnimplementedOrderIntegralServiceServer) IntegralSynchronize(ctx context.Context, req *MemberIntegralRecordRequest) (*OrderIntegralSynchronizeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegralSynchronize not implemented")
}
func (*UnimplementedOrderIntegralServiceServer) AwHistoryOrderIntegralSync(ctx context.Context, req *HistoryOrderRequest) (*OrderIntegralSynchronizeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AwHistoryOrderIntegralSync not implemented")
}

func RegisterOrderIntegralServiceServer(s *grpc.Server, srv OrderIntegralServiceServer) {
	s.RegisterService(&_OrderIntegralService_serviceDesc, srv)
}

func _OrderIntegralService_OrderIntegralQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderIntegralQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderIntegralServiceServer).OrderIntegralQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.OrderIntegralService/OrderIntegralQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderIntegralServiceServer).OrderIntegralQuery(ctx, req.(*OrderIntegralQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderIntegralService_IntegralSynchronize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberIntegralRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderIntegralServiceServer).IntegralSynchronize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.OrderIntegralService/IntegralSynchronize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderIntegralServiceServer).IntegralSynchronize(ctx, req.(*MemberIntegralRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderIntegralService_AwHistoryOrderIntegralSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HistoryOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderIntegralServiceServer).AwHistoryOrderIntegralSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.OrderIntegralService/AwHistoryOrderIntegralSync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderIntegralServiceServer).AwHistoryOrderIntegralSync(ctx, req.(*HistoryOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _OrderIntegralService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.OrderIntegralService",
	HandlerType: (*OrderIntegralServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OrderIntegralQuery",
			Handler:    _OrderIntegralService_OrderIntegralQuery_Handler,
		},
		{
			MethodName: "IntegralSynchronize",
			Handler:    _OrderIntegralService_IntegralSynchronize_Handler,
		},
		{
			MethodName: "AwHistoryOrderIntegralSync",
			Handler:    _OrderIntegralService_AwHistoryOrderIntegralSync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/order_integral.proto",
}

package igc

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	//"integral-center/proto/oc"

	"sync"
	"time"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	//Integral oc.IntegralServiceClient
	PRC IntegralServiceClient
	IG  IntegralGoodsServiceClient
	IO  IntegralOrderServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

func GetIntegralServiceClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.integral-center")
	}
	//url = "10.1.1.242:11009"
	if url == "" {
		url = "127.0.0.1:11009"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("order-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.PRC = NewIntegralServiceClient(grpcClient.Conn)
		grpcClient.IG = NewIntegralGoodsServiceClient(grpcClient.Conn)
		grpcClient.IO = NewIntegralOrderServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}

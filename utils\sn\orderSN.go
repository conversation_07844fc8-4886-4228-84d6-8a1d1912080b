package sn

import (
	"math/rand"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/tricobbler/rp-kit/cast"
)

type orderSN struct {
	redis *redis.Client
	db    *xorm.Engine
}

func (s orderSN) Generate() string {
	preKey := "order-center:order-sn-number"
	isOk := s.redis.Exists(preKey)
	if isOk.Val() <= 0 {
		var orderSn string
		_, err := s.db.Table("order_main").Select("max(`order_sn`)").Get(&orderSn)
		if err != nil {
			glog.Error("查询数据库最大订单号异常, " + err.<PERSON>rror())
			panic(err)
		}
		s.redis.SetNX(preKey, orderSn, 0)
	}

	ran := rand.Intn(899) + 100
	var orderSn int64
	orderSn, err := s.redis.IncrBy(preKey, int64(ran)).Result()
	if err != nil {
		panic(err)
	}

	return cast.ToString(orderSn)
}

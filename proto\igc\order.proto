syntax = "proto3";
package proto;
option go_package = "igc";

service IntegralOrderService {
  // 积分订单列表
  rpc List(OrderListRequest) returns (OrderListResponse) {}
  // 积分订单详情
  rpc Detail(OrderRequest) returns (OrderDetailResponse) {}
  // 积分订单导出
  rpc Export(OrderListRequest) returns (OrderResponse) {}
  // 积分订单导出任务列表
  rpc ExportList(OrderExportListRequest) returns (OrderExportListResponse) {}
  // 快递公司列表
  rpc ExpressCompanies(OrderEmptyRequest) returns(ExpressCompaniesResponse){}
  // 发货或者更新物流
  rpc ExpressStore(ExpressStoreRequest) returns(OrderResponse){}
  // 批量导入物流模板
  rpc ExpressImportTemplate(OrderEmptyRequest) returns(ExpressImportTemplateResponse){}
  // 批量导入物流
  rpc ExpressImport(ExpressImportRequest) returns(OrderResponse);
  // 批量导入物流历史
  rpc ExpressImportList(ExpressImportListRequest) returns(ExpressImportListResponse);

  // --------以下是小程序的接口-----------
  // 立即兑换
  rpc AWStore(AWOrderStoreRequest) returns(OrderResponse);
  // 订单列表
  rpc AWList(AWOrderListRequest) returns(AWOrderListResponse);
  // 订单详情
  rpc AWDetail(AWOrderRequest) returns(AWOrderDetailResponse);
  // 确认收货
  rpc AWConfirmReceipt(AWOrderRequest) returns(OrderResponse);
  // 物流信息
  rpc AWDeliveryDetail(AWOrderRequest) returns(AWDeliveryDetailResponse);

  rpc IntegralChangeMessage(IntegralChangeMessageRequest) returns(OrderResponse);

}

message IntegralChangeMessageRequest{
  string member_id = 1;
  //积分类型
  int32 integral_type = 2;
  //积分
  int32 insert_integral = 3;
  //积分
  int32 org_id = 4;
}

message OrderEmptyRequest{
}

message OrderResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message OrderExpressRequest{
  int32 id = 1;
  // 物流名称
  string name = 2;
  // 物流单号
  string sn = 3;
}

message OrderListRequest{
  // 订单编号
  string order_sn = 1;
  // 手机号
  string mobile = 2;
  // 兑换开始日期
  string start_date = 4;
  // 兑换结束日期
  string end_date = 5;
  // 订单状态 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
  string state = 3;
  // 商品分类 1实物2虚拟3优惠券
  string type = 6;
  // 订单来源 1阿闻
  string from = 7;
  // 销售渠道 2小程序、3app
  string channel = 8;
  // 商品名称
  string goods_name = 9;
  // 每页数量，订单导出时不需要传
  int32 page_size = 10;
  // 当前页码，订单导出时不需要传
  int32 page_index = 11;
}

message GoodsListData{
  // 对应兑换商品id
  int32 product_id = 9;
  // scrm_info，实物、虚拟skuid，优惠券id
  string id = 1;
  // 商品名称
  string name = 2;
  // 商品价格
  float price = 3;
  // 商品货号
  string serial = 4;
  // 所需积分
  int32 integral = 5;
  // 兑换数量
  int32 qty = 6;
  // 图片
  string image = 7;
  // 总计
  int32 total = 8;
}

message OrderAddressData{
  // 收货人名称
  string name = 1;
  // 手机号
  string mobile = 2;
  // 加密手机号
  string encrypt_mobile = 9;
  // 省市区
  string area_info = 3;
  // 地址
  string address = 4;
  // 物流单号
  string shipping_code = 5;
  // 发货时间
  string shipping_time = 6;
  // 物流公司编码
  string shipping_ecode = 7;
  // 物流公司名称
  string shipping_ename = 8;
}

message OrderListData {
  // 订单编号
  string order_sn = 1;
  // 下单时间
  string add_time = 2;
  // 订单来源 阿闻/App
  string from = 3;
  // 会员账号
  string member_name = 4;
  // 会员手机号
  string member_mobile = 5;
  // 加密手机号
  string encrypt_mobile = 16;
  // scrmId
  string scrm_id = 6;
  // 备注
  string remark = 7;
  // 状态数字 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
  int32 state = 8;
  // 订单文本 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
  string state_text = 9;
  // 订单类型 1实物2虚拟3优惠券
  int32 type = 10;
  // 商品信息
  repeated GoodsListData goods = 11;
  // 地址及物流信息
  OrderAddressData address = 12;
  // 总积分
  int32 total = 13;
  // 完成时间
  string finnshed_time = 14;
  // 商品类型 1第三方商品(属type=1) 2自有商品(属type=1) 3门店卷(属type=3) 4:商城券（属type=4）
  int32 good_type = 15;
}

message OrderListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  repeated OrderListData data = 3;
  // 总数
  int32 total = 4;
}

message OrderCouponData{
  // 截至时间
  string end_time = 1;
  // 截至日期，精确到日
  string end_date = 2;
  // 兑换码
  string code = 3;
}

message OrderVerifyCodeData{
  // 截至日期
  string end_time = 1;
  // 截至日期，精确到日
  string end_date = 2;
  // 核销码
  string code = 3;
  // 当前状态 0:未使用 1:已使用 2:已过期
  int32 state = 4;
  // 核销时间
  string use_time = 5;
  // 核销门店
  string chain_name = 6;
}

message OrderStepData{
  // 时间
  string time = 1;
  // 描述
  string desc = 2;
}

message OrderDetailResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 订单信息
  OrderListData order = 3;
  // 兑换信息，仅当type=3有效
  OrderCouponData coupon = 4;
  // 核销码，仅当type=2有效
  OrderVerifyCodeData verify_code = 5;
  // 脚印信息
  repeated OrderStepData steps = 6;
}

message OrderExportListRequest{
  // 页码，不传默认为1
  int32 page_index = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
}

message OrderExportListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  message List {
    // 操作时间
    string created_at = 1;
    // 状态 0处理中、1成功、2失败
    int32 state = 2;
    // 状态文本
    string state_text = 3;
    // 当state = 1时返回链接
    string url = 4;
    // 当state = 2返回失败原因
    string result = 5;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message ExpressCompaniesResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List{
    // 快递公司代码
    string code = 1;
    // 快递公司名称
    string name = 2;
  }

  repeated List data = 3;
}

message ExpressStoreRequest{
  // 订单号
  string order_sn = 1;
  // 物流单号
  string shipping_code = 2;
  // 物流公司编码
  string shipping_ecode = 3;
}

// 批量导入物流模板
message ExpressImportTemplateResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 文件字节流
  bytes template = 3;
}

message ExpressImportRequest {
  // 文件字节流
  bytes file = 1;
}

message ExpressImportListRequest{
  // 页码，不传默认为1
  int32 page_index = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
}

message ExpressImportListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  message List {
    // 操作时间
    string created_at = 1;
    // 结果url
    string url = 4;
    // 导入结果文本
    string result = 5;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message AWOrderStoreRequest{
  // 商品id
  int32 goods_id = 1;
  // 地址id，仅实物订单需要
  int32 address_id = 2;
  // 前端不需要传
  string scrm_id = 3;
  //机构ID
  int32 org_id=4;

}

message AWOrderListRequest{
  // 商品分类 1实物、2虚拟、3优惠券
  string type = 1;
  // 前端不需要传
  string scrm_id = 5;
  // 每页数量
  int32 page_size = 2;
  // 当前页码
  int32 page_index = 3;
}

message AWOrderListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 订单信息
  repeated OrderListData data = 3;
  // 总数
  int32 total = 4;
}

message AWOrderDetailResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 订单信息
  OrderListData data = 3;
  // 兑换信息，仅当type=3有效
  OrderCouponData coupon = 4;
  // 核销码，仅当type=2有效
  OrderVerifyCodeData verify_code = 5;
}

message OrderRequest{
  // 订单编号
  string order_sn = 1;
}

message AWOrderRequest{
  // 订单编号
  string order_sn = 1;
  // 前端不需要传
  string scrm_id = 2;
}

message AWDeliveryDetailResponse{
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;

  // 快递公司
  string express_name = 3;
  // 快递单号
  string shipping_code = 4;

  string status = 5;
  string msg = 6;
  Result result = 7;
  message Node {
    // 发生时间
    string time = 1;
    // 流转描述
    string status = 2;
  }
  message Result {
    string type = 3;
    string number = 4;
    //配送流程节点
    repeated Node list = 5;
    //阿里云返回的物流名称
    string expName = 6;
  }
}
package dto

type Activity920Latest struct {
	Code  int `json:"code"`
	Datas struct {
		LotteryInfo struct {
			LotteryId        string `json:"lottery_id"`
			LotteryTitle     string `json:"lottery_title"`
			LotteryState     string `json:"lottery_state"`
			LotteryStartTime string `json:"lottery_start_time"`
			LotteryEndTime   string `json:"lottery_end_time"`
		} `json:"lottery_info"`
	} `json:"datas"`
}

type LuckyNumGetRequest struct {
	// 活动id
	LotteryId int32 `json:"lottery_id"`
	// 默认1-首次参加活动，2-分享，3-链接或海报进入，带分享用户id，4-下单，带订单号
	Type int32 `json:"type"`
	// 分享者用户id
	ShareMemberId int64 `json:"share_member_id"`
	// 订单号
	OrderSn string `json:"order_sn"`
	// 用户scrmid，下单消费赠送幸运码用
	ScrmUserId string `json:"scrm_user_id"`
}

type LuckyNumGetResponse struct {
	// 200-正常，400-错误
	Code int32 `json:"code"`
	// 错误提示信息
	Message string `json:"message"`
	// 内部错误信息
	Error string `json:"error"`
	// 幸运号
	Data string `json:"data"`
}

package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
	"time"
)

type Config struct {
	Id          int    `xorm:"not null pk autoincr comment('自增id') INT(11)"`
	Configname  string `xorm:"not null comment('名称') unique VARCHAR(50)"`
	Configvalue string `xorm:"default 'NULL' comment('实际的值') TEXT"`
	Type        int    `xorm:"default NULL comment('类型') INT(50)"`
	Remark      string `xorm:"default 'NULL' comment('备注') VARCHAR(100)"`
	Url         string `xorm:"default 'NULL' comment('地址') VARCHAR(100)"`
}

type PickupConfig struct {
	MaxDistance    int32  // 距离站点最远距离m
	StatusTemplate string // 状态通知模板
}

type PickupConfigCache struct {
	Config     *PickupConfig
	ExpireTime time.Time // 设置过期时间
}

var pcc *PickupConfigCache

// GetPickupConfig 获取社区团购所有配置
func GetPickupConfig(db *xorm.Engine) (*PickupConfig, error) {
	// 缓存存在且有效
	if pcc != nil && pcc.ExpireTime.After(time.Now()) {
		return pcc.Config, nil
	}
	var cs []*Config
	if err := db.Table("datacenter.config").In("configName", []string{
		"pickup_max_distance",
		"pickup_status_template",
	}).Select("configName,configValue").Find(&cs); err != nil {
		return nil, errors.New("查询全局配置出错 " + err.Error())
	}

	puc := new(PickupConfig)
	for _, c := range cs {
		switch c.Configname {
		case "pickup_max_distance":
			puc.MaxDistance = cast.ToInt32(c.Configvalue)
		case "pickup_status_template":
			puc.StatusTemplate = c.Configvalue
		}
	}

	pcc = &PickupConfigCache{
		Config:     puc,
		ExpireTime: time.Now().Add(10 * time.Second), // 缓存10秒钟有效
	}

	return puc, nil
}

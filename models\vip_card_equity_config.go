package models

import (
	"time"
)

type VipCardEquityConfig struct {
	CardTid      int       `xorm:"not null pk default 0 comment('卡付费周期id') INT(11)"`
	OrId         int       `xorm:"not null default 0 comment('所属大区') TINYINT(2)"`
	EquityId     int       `xorm:"not null pk default 0 comment('权益id') TINYINT(1)"`
	Status       int       `xorm:"not null default 1 comment('显示状态：0-即将上线，1-正常显示') TINYINT(1)"`
	ReceiveNum   int       `xorm:"not null default 1 comment('领取个数') TINYINT(2)"`
	PrivilegeIds string    `xorm:"default 'NULL' comment('权益值') TEXT"`
	Sort         int       `xorm:"not null default 0 comment('排序') SMALLINT(4)"`
	CreateTime   time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime   time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

type EquityConfigReceive struct {
	EquityId        int32
	EquityName      string
	PrivilegeIds    string
	ReceiveNum      int
	ReceiveType     int
	EquityType      int32
	UserId          string
	OrderSn         string
	CreateTime      string
	EquityShortName string
	CardTid         int
	OrId            int
	Source          int
}

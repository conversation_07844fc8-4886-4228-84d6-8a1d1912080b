package vip_card

import (
	"errors"
	"fmt"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// 获取健康卡会员卡订单列表
func GetPhysicalVipCardOrderList(session *xorm.Session, in *oc.GetPhysicalVipCardOrderListRequest) (out []*models.PhysicalVipCardOrders, total int32, err error) {
	logPrefix := fmt.Sprintf("GetPhysicalVipCardOrderList====入参：%s", kit.JsonEncode(in))
	glog.Info(logPrefix)

	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	in.MemberMobile = strings.TrimSpace(in.MemberMobile)
	in.OrderSn = strings.TrimSpace(in.OrderSn)
	in.PaymentTimeStart = strings.TrimSpace(in.PaymentTimeStart)
	in.PaymentTimeEnd = strings.TrimSpace(in.PaymentTimeEnd)

	sql := ` from upet_orders as a FORCE INDEX(update_time) where a.order_type=21 and a.order_father>0 and a.update_time>'2023-09-14'`
	value := make([]interface{}, 0)
	if len(in.OrderSn) > 0 {
		sql = fmt.Sprintf("%s and a.order_sn=?", sql)
		value = append(value, in.OrderSn)
	}

	if len(in.MemberMobile) > 0 {
		encrypt_mobile := utils.MobileEncrypt(in.MemberMobile)
		sql = fmt.Sprintf("%s and a.encrypt_mobile=?", sql)
		value = append(value, encrypt_mobile)
	}
	if len(in.PaymentTimeStart) > 0 && len(in.PaymentTimeEnd) > 0 {
		PaymentTimeStart, err := time.ParseInLocation(kit.DATETIME_LAYOUT, in.PaymentTimeStart, time.Local)
		if err != nil {
			glog.Error(logPrefix, "支付开始时间格式错误：", err.Error())
			return nil, 0, errors.New("支付开始时间格式错误")
		}
		PaymentTimeEnd, err := time.ParseInLocation(kit.DATETIME_LAYOUT, in.PaymentTimeEnd, time.Local)
		if err != nil {
			glog.Error(logPrefix, "支付结束时间格式错误：", err.Error())
			return nil, 0, errors.New("支付结束时间格式错误")
		}
		sql = fmt.Sprintf("%s and a.payment_time>=? and a.payment_time<=?", sql)
		value = append(value, PaymentTimeStart.Unix())
		value = append(value, PaymentTimeEnd.Unix())
	}
	if in.OrderState >= 0 {
		sql = fmt.Sprintf("%s and a.order_state=?", sql)
		value = append(value, in.OrderState)
	}

	//统计条数
	value1 := make([]interface{}, 0)
	value1 = append(value1, fmt.Sprintf("select count(order_id) as cnt %s", sql))
	value1 = append(value1, value...)

	if result, err := session.Query(value1...); err != nil {
		glog.Error(logPrefix, "统计列表条数失败：err:", err.Error())
		return nil, 0, errors.New("统计条数失败")
	} else {
		cnt := string(result[0]["cnt"])
		cnt1, _ := strconv.Atoi(cnt)
		total = int32(cnt1)
	}
	orderIdSli := make([]int, 0)
	value2 := make([]interface{}, 0)
	value2 = append(value2, fmt.Sprintf("select order_id %s  order by order_id desc  limit %d offset %d", sql, int(in.PageSize), int(in.PageSize*(in.PageIndex-1))))
	value2 = append(value2, value...)
	if result, err := session.Query(value2...); err != nil {
		glog.Error(logPrefix, "查询列表数据失败：err:", err.Error())
		return nil, 0, errors.New("查询列表数据失败")
	} else {
		for _, v := range result {
			orderIdStr := string(v["order_id"])
			orderId, _ := strconv.Atoi(orderIdStr)
			orderIdSli = append(orderIdSli, orderId)
		}
	}

	out = make([]*models.PhysicalVipCardOrders, 0, total)

	if len(orderIdSli) > 0 {
		if err = session.Table("upet_orders").Alias("a").Join("inner", "upet_member b", "a.buyer_id=b.member_id").
			Join("inner", "upet_order_common c", "a.order_id=c.order_id").
			Join("inner", "upet_order_goods d", "a.order_id=d.order_id").
			Join("left", "upet_express e", "c.shipping_express_id=e.id").
			In("a.order_id", orderIdSli).
			OrderBy("a.order_id desc").Find(&out); err != nil {
			glog.Error(logPrefix, "查询列表数据失败：err:", err.Error())
			return nil, 0, errors.New("查询列表数据失败")
		}

	}

	return out, total, nil
}

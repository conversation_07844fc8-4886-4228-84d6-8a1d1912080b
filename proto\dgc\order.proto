syntax = "proto3";
// 问诊订单相关
package dgc;

// @Desc    	在线问诊 订单模块
// <AUTHOR>
// @Date		2021-10-09
service OrderService {
  // @Desc    	医生端-我的订单列表
  // <AUTHOR>
  // @Date		2021-10-09
  rpc DoctorOrderList(DoctorOrderListRequest) returns (OrderListResponse) {}
  // @Desc    	医生端-待接诊订单列表
  // <AUTHOR>
  // @Date		2021-10-12
  rpc WaitOrderList(WaitOrderListRequest) returns (OrderListResponse){}
  // @Desc    	医生端-接诊操作
  // <AUTHOR>
  // @Date		2021-10-12
  rpc OrderAccept(OrderAcceptRequest) returns (OrderAcceptResponse){}

  // @Desc    	用户端-修改问诊订单状态
  // <AUTHOR>
  // @Date		2021-10-12
  rpc OrderStateChange(OrderStateChangeRequest) returns (BaseResponseNew){}
  //用户端-订单列表
  rpc UserOrderList(UserOrderListRequest) returns (OrderListResponse){}
  //用户端-订单详情
  rpc UserOrderDetail(UserOrderDetailRequest) returns (UserOrderDetailResponse){}
  //用户端-通过医生id和用户id获取最新的一条订单
  rpc GetLatestOrder(GetLatestOrderRequest) returns (GetLatestOrderResponse){}
  //用户端-获取用户是否存在有效订单
  rpc GetUserDiagnoseInfo(GetUserDiagnoseRequest) returns (GetUserDiagnoseResponse){}
  //获取问诊信息
  rpc GetDiagnoseInfo(GetDiagnoseInfoRequest) returns (GetDiagnoseInfoResponse){}
  //追加次数更新
  rpc DiagnoseAddFinishMessage(DiagnoseAddFinishMessageRequest) returns (DiagnoseAddFinishMessageResponse){}
}

//请求参数
message DiagnoseAddFinishMessageRequest{
    //订单号
    string order_sn = 1;
    //回复次数
    int32 number = 2;
}
//返回参数
message DiagnoseAddFinishMessageResponse{}

//请求参数
message GetDiagnoseInfoRequest{
    //订单号
    string order_sn = 1;
}
//返回参数
message GetDiagnoseInfoResponse{
    //订单信息
    string order_sn = 1;
    //宠物id
    string pet_id = 2;
    //宠物头像
    string pet_avatar = 3;
    //宠物名称
    string pet_name = 4;
    //宠物种类大分类
    string pet_kindof = 5;
    //宠物种类
    string  pet_variety = 6;
    //宠物生日
    string pet_birthday = 7;
    //宠物是否绝育 1：已绝育 0：未绝育
    int32 pet_neutering = 8;
    //宠物性别 1GG,2MM
    int32 pet_sex = 9;
    //免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
    int32 immune_status = 10;
    //症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
    string symptom = 11;
    //补充症状(选择【其他】关键词，输入框描述症状必填)
    string symptom_desc = 12;
    //症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
    int32 symptom_recent = 13;
    //宠物症状照片，多个用英文逗号隔开
    string image = 14;
    //是否就诊过：0未就诊，1就诊过
    int32 have_hospital = 15;
    //就诊过的医院名称
    string have_hospital_name = 16;
    //医生诊断结果与治疗方案
    string have_hospital_result = 17;
    //历史就诊的检查照片/药品照片
    string have_hospital_image = 18;
    //是否有其他病史：0否，1是
    int32 medical_history = 19;
    //其他病史信息
    string medical_history_info = 20;
}

//获取最新的一条订单请求参数
message GetLatestOrderRequest{
    //医生标识
    string doctor_id = 1;
    //用户标识
    string user_id = 2;
    //订单号
    string order_sn = 3;
}

//获取最新的一条订单返回参数
message GetLatestOrderResponse{
    //医生标识
    string doctor_id = 1;
    //医生头像
    string doctor_avatar = 2;
    //医生名称
    string doctor_name = 3;
    //医生所在医院
    string doctor_hospital = 4;
    //医生职级
    string doctor_level = 5;
    //用户id
    string user_id = 6;
    //用户头像
    string user_avatar = 7;
    //用户名称
    string user_name = 8;
    //订单信息
    string order_sn = 9;
    //问诊单状态：1待接入，2待回复，3问诊中，4已完成
    int32 diagnose_state = 10;
    //剩余时间
    int32 diagnose_residue_time = 11;
    //问诊项目：1-免费义诊，2-快速咨询，3-找医生
    int32 diagnose_project = 12;
    //问诊形式：1-图文，2-电话，3-视频
    int32 diagnose_form = 13;
    //宠物id
    string pet_id = 14;
    //宠物头像
    string pet_avatar = 15;
    //宠物名称
    string pet_name = 16;
    //宠物种类大分类
    string pet_kindof = 17;
    //宠物种类
    string  pet_variety = 18;
    //宠物生日
    string pet_birthday = 19;
    //宠物是否绝育 1：已绝育 0：未绝育
    int32 pet_neutering = 20;
    //宠物性别 1GG,2MM
    int32 pet_sex = 21;
    //已追加回复次数
    int32 diagnose_finish_message = 22;
    //订单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
    int32 order_state = 23;
}

message WaitOrderListRequest{

  //排序
  string order_by=1;
  // 当前多少页,从1开始
  int32 page_index = 2;
  // 每页多少条数据
 int32 page_size = 3;
}
//医生端-接诊操作
message OrderAcceptRequest{
  string order_sn = 1;
}
//医生端-接诊操作
message OrderAcceptResponse{
  int64 affect_rows = 1;
  string scrm_user_id = 2;
}
//医生端-我的订单列表
message DoctorOrderListRequest {
  // 当前多少页,从1开始
  int32 page_index = 1;
  // 每页多少条数据
  int32 page_size = 2;
  //排序（样例create_time asc）
  string order_by = 3;
  //根据下单时间搜索（低值）
  string min_create_time = 4;
  //根据下单时间搜索（高值）
  string max_create_time = 5;
}

//订单列表
message OrderListResponse {
  int64 total = 1;
  repeated Order list = 2;
}
message Order {
  int32 id = 1;
  //医生编号
  string doctor_code = 2;
  //用户id
  string scrm_user_id = 3;
  //用户名称
  string user_name = 4;
  //用户头像
  string user_avatar =5;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 6;
  //问诊项目文本
  string diagnose_project_text = 7;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 8;
  //问诊形式文本
  string diagnose_form_text = 9;
  //症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
  string symptom = 10;
  //症状文本
  string symptom_text = 11;
  //补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
  string symptom_desc = 12;
  //订单编号
  string order_sn = 13;
  //主订单状态：0已取消,10(默认)未付款,20已付款,30已完成'（order_main.order_status）
  int32 order_status = 14;
  //订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款(refund_order.refund_state)
  int32 refund_state = 15;

  //问诊单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
  int32 state = 16;
  // 问诊状态文本
  string state_text = 17;
  //问诊时间
  string create_time = 18;

  //宠物id
  string pet_id  = 20;
  //宠物头像
  string pet_avatar  = 21;
  //宠物名字
  string pet_name  = 22;
  //大种类
  string pet_kindof  = 23;
  //种类
  string pet_variety  = 24;

  //生日例如：2021-10-01 00:00:00
  string pet_birthday  = 25;
  //1：已绝育 0：未绝育
  int32 pet_neutering  = 26;
  //绝育文本：1：已绝育 0：未绝育
  string pet_neutering_text = 27;
  //性别：1GG,2MM
  int32  pet_sex  = 28;
  //性别文本：公， 母
  string pet_sex_text = 29;
  //医生名称
  string doctor_name = 30;
  //支付或问诊结束时间，状态为待支付时为支付结束时间，状态为待回复和问诊中时为问诊结束时间
  string end_time = 31;
  //问诊时长
  int32 duration = 32;
  //医生接入时间
  string doctor_join = 33;
  //正常结束问诊时间（医生接入时间+问诊时长）
  string countdown_finish_time = 34;
  //问诊单状态：1待接入，2待回复，3问诊中，4已完成
  int32 diagnose_state = 35;
  // 症状 信息（symptom等于其他时，则显示symptom_desc，否则显示symptom）
  string symptom_summarize = 36;
  //订单支付时间
  string pay_time = 37;
}

message AddDiagnoseOrderRequest{
    //订单号
    string order_sn = 1;
    //宠物id
	string pet_id = 2;
	//医生编号
	string doctor_code = 3;
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	int32 diagnose_project = 4;
	//问诊形式：1-图文，2-电话，3-视频
	int32 diagnose_form = 5;
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	int32 immune_status = 6;
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	string symptom = 7;
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	string symptom_desc = 8;
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	int32 symptom_recent = 9;
	//宠物症状照片，多个用英文逗号隔开
	string image = 10;
	//是否就诊过：0未就诊，1就诊过
	int32 have_hospital = 11;
	//就诊过的医院名称
	string have_hospital_name = 12;
	//医生诊断结果与治疗方案
	string have_hospital_result = 13;
	//是否有其他病史：0否，1是
	int32 medical_history = 14;
	//其他病史信息
	string medical_history_info = 15;
	//宠物头像
	string pet_avatar = 16;
	//宠物名称
	string pet_name = 17;
	//宠物种类
	string pet_variety = 18;
	//宠物生日
	string pet_birthday = 19;
	//宠物性别 性别：1GG,2MM
	int32 pet_sex = 20;
	//1：已绝育 0：未绝育
	int32 pet_neutering = 21;
    //用户ScrmId
	string user_id = 22;
	//金额
	int32 amount = 23;
	//问诊时长
	int32 duration = 24;
}
message BaseResponseNew {
  string Msg = 1;
}

message OrderStateChangeRequest{
    //操作类型：1结束问诊，2取消问诊
    int32 operate_type = 1;
    //订单号
    string order_sn  = 2;
}

//医生端-我的订单列表
message UserOrderListRequest {
  // 当前多少页,从1开始
  int32 page_index = 1;
  // 每页多少条数据
  int32 page_size = 2;
  //根据下单时间搜索（低值）
  string min_create_time = 3;
  //根据下单时间搜索（高值）
  string max_create_time = 4;
  //tab切换：0全部，1待支付，2待接诊/回复，3问诊中，4已退款
  int32 tab_type = 5;
}

message UserOrderDetailRequest{
    //订单编号
    string order_sn = 1;
}
message UserOrderDetailResponse{
  int32 id = 1;
  //医生编号
  string doctor_code = 2;
  //用户id
  string scrm_user_id = 3;
  //用户名称
  string user_name = 4;
  //用户头像
  string user_avatar =5;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 6;
  //问诊项目文本
  string diagnose_project_text = 7;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 8;
  //问诊形式文本
  string diagnose_form_text = 9;
  //症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
  string symptom = 10;
  //症状文本
  string symptom_text = 11;
  //补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
  string symptom_desc = 12;
  //订单编号
  string order_sn = 13;
  //主订单状态：0已取消,10(默认)未付款,20已付款,30已完成'（order_main.order_status）
  int32 order_status = 14;
  //最后对话时间
  string last_reply_time = 15;
  //问诊单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
  int32 state = 16;
  // 问诊状态文本
  string state_text = 17;
  //问诊时间
  string create_time = 18;
  //宠物id
  string pet_id  = 19;
  //宠物头像
  string pet_avatar  = 20;
  //宠物名字
  string pet_name  = 21;
  //种类
  string pet_variety  = 22;
  //生日例如：2021-10-01 00:00:00
  string pet_birthday  = 23;
  //生日文本：如0岁2月
  string pet_birthdays_text = 24;
  //1：已绝育 0：未绝育
  int32 pet_neutering  = 25;
  //绝育文本：1：已绝育 0：未绝育
  string pet_neutering_text = 26;
  //性别：1GG,2MM
  int32  pet_sex  = 27;
  //性别文本：公， 母
  string pet_sex_text = 28;
  //医生名称
  string doctor_name = 29;
  //医生照片
  string doctor_img = 30;
  //支付或问诊结束时间，状态为待支付时为支付结束时间，状态为待回复和问诊中时为问诊结束时间
  string end_time =31;

  //症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
  int32 symptom_recent = 32;
  //宠物症状照片，多个用英文逗号隔开
  string image = 33;
  //是否就诊过：0未就诊，1就诊过
  int32 have_hospital = 34;
  //就诊过的医院名称
   string have_hospital_name = 35;
  //医生诊断结果与治疗方案
  string have_hospital_result = 36;
  //历史就诊的检查照片/药品照片
 string have_hospital_image = 37;
  //是否有其他病史：0否，1是
  int32 medical_history = 38;
  //其他病史信息
  string medical_history_info = 39;
  //问诊金额
  int32 amount = 40;
  //问诊单状态：1待接入，2待回复，3问诊中，4已完成
  int32 diagnose_state = 41;
  //订单支付时间
  string pay_time = 42;
  //免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
  int32 immune_status = 43;
  //宠物种类大分类
  string pet_kindof = 44;
}

message GetUserDiagnoseRequest{
    //用户编号
    string user_id =1;
    //医生编号
    string doctor_code = 2;
    //问诊项目：1-免费义诊，2-快速咨询，3-找医生
    int32 diagnose_project =3;
}
message GetUserDiagnoseResponse{
    string order_sn = 1;
    string doctor_code = 2;
}

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/utils"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ahmetb/go-linq"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type UpetDjService struct {
	CommonService
}

// 查询函数封装
type UpetDjServiceQueryFunc func(db *xorm.Engine) error

// 执行事务函数封装
type UpetDjServiceTranFunc func(db *xorm.Session) error

// 开始查询
func (c *CommonService) BeginQuery(queryFunc UpetDjServiceQueryFunc) error {
	c.session = GetDBConn().NewSession()
	defer c.session.Close()

	err := queryFunc(GetDBConn())
	if err != nil {
		glog.Error(err, ", ", kit.RunFuncName(2))
	}

	return err
}

// 开始事务
func (c *CommonService) BeginTran(tranFunc UpetDjServiceTranFunc) error {
	// 开启回话
	var session = GetDBConn().NewSession()
	defer session.Close()
	session.Begin()

	//调用事务函数
	err := tranFunc(session)

	// 事务是否出错
	if err != nil {
		glog.Error(err)
		session.Rollback()
	} else {
		err = session.Commit()
	}

	return err
}

// 查询区域列表
func (service UpetDjService) QuerySectionList(ctx context.Context, req *oc.SectionQueryRequest) (*oc.SectionQueryResponse, error) {
	var response = new(oc.SectionQueryResponse)
	response.Code = utils.CODE_SUCCESS
	// 定义递归函数
	var recurseCity func(datas []models.Basecity, parentId int) []*oc.SectionDto
	recurseCity = func(datas []models.Basecity, parentId int) []*oc.SectionDto {
		var childDatas []*oc.SectionDto
		// 先查询直接子区域
		for _, city := range datas {
			if city.Parentid == parentId {
				childDatas = append(childDatas, city.ToSectionDto())
			}
		}
		// 递归查询子区域
		for _, city := range childDatas {
			city.ChildSectionDto = recurseCity(datas, int(city.Id))
		}
		return childDatas
	}

	// 查询函数封装
	var queryFunc = func(db *xorm.Engine) error {
		// 查询第一层数据
		var sections []models.Basecity
		err := db.Where("ParentId=?", req.ParentId).Find(&sections)
		if err == nil {

			for _, item := range sections {
				var dto = item.ToSectionDto()
				response.Data = append(response.Data, dto)
			}

			if req.IsChild {
				// 查询所有数据
				var allDatas []models.Basecity
				db.Find(&allDatas)
				// 递归获取子区域
				for _, item := range response.Data {
					item.ChildSectionDto = recurseCity(allDatas, int(item.Id))
				}
			}
		}
		return err
	}

	err := service.BeginQuery(queryFunc)

	return response, err
}

// 确认订单
func (service UpetDjService) ConfirmUpetDj(ctx context.Context, req *oc.UpetDjConfirmRequest) (*oc.UpetDjConfirmResponse, error) {
	var response = new(oc.UpetDjConfirmResponse)
	response.Code = utils.CODE_SUCCESS
	err := service.BeginQuery(func(engine *xorm.Engine) error {
		var orderMain models.OrderMain

		// 查询单据信息
		isGetOrder, err := engine.ID(req.OrderId).Get(&orderMain)
		if err != nil {
			response.Code = utils.CODE_SERVEREXCEPTION
			response.Message = err.Error()
			return err
		}
		if !isGetOrder {
			response.Message = "订单Id不存在"
			response.Code = utils.CODE_BUSINESSERROR
			return nil
		}
		if orderMain.OrderStatus == 30 {
			response.Message = "订单已经完成"
			response.Code = utils.CODE_BUSINESSERROR
			return nil
		}

		//// 物流配送时必须要全部发货才能操作确认收货
		//if orderMain.DeliveryType == 1 && orderMain.OrderStatusChild != 20202 {
		//	response.Message = "订单全部发货时才可以操作"
		//	response.Code = utils.CODE_BUSINESSERROR
		//	return nil
		//}

		//调用其他服务
		var orderService = new(OrderService)
		var params = new(oc.AccomplishOrderRequest)
		params.ConfirmTime = time.Now().Format(kit.DATETIME_LAYOUT)
		params.OrderSn = orderMain.OrderSn
		res, _ := orderService.AccomplishOrder(ctx, params)
		if res.Code != 200 {
			response.Code = utils.CODE_BUSINESSERROR
			response.Message = res.Message
		}
		return err
	})
	return response, err
}

// 查询到家订单列表
func (service UpetDjService) QueryUpetDjOrderList(ctx context.Context, req *oc.UpetDjOrderQueryRequest) (*oc.UpetDjOrderQueryResponse, error) {
	var response = new(oc.UpetDjOrderQueryResponse)
	response.Code = utils.CODE_SUCCESS
	err := service.BeginQuery(func(engine *xorm.Engine) error {

		var orderQuery *xorm.Session
		if req.State == oc.UpetDjOrderState_unVerify { //待核销
			orderQuery = engine.Table("order_main").Alias("a").
				Select("a.*,b.*").
				Join("inner", "order_detail b", "a.order_sn=b.order_sn").
				Join("inner", "order_verify_code c", "b.order_sn=c.order_sn AND c.verify_status = 0").
				And("parent_order_sn>0 or (b.split_order_result=0 and parent_order_sn=0)"). //所有订单都会拆单，所以只展示子订单
				GroupBy("a.order_sn")                                                       //order_verify_code表一个order_sn可能对应多个记录

		} else {
			orderQuery = engine.Table("order_main").Alias("a").
				Join("inner", "order_detail b", "a.order_sn=b.order_sn").
				And("parent_order_sn>0 or (b.split_order_result=0 and parent_order_sn=0)") //所有订单都会拆单，所以只展示子订单
		}
		if req.ChannelId > 0 {
			orderQuery = orderQuery.Where("a.channel_id=?", req.ChannelId) //只展示指定渠道
		} else {
			orderQuery = orderQuery.In("a.channel_id", ChannelAwenId, ChannelDigitalHealth, ChannelIdOfflineShop) //阿闻项目：展示阿闻小程序和互联网医院渠道 ， 宠物saas展示：小程序渠道和线下渠道
		}

		// 会员Id
		if len(req.MemberId) > 0 {
			orderQuery = orderQuery.Where("a.member_id=?", req.MemberId)
		}
		// 店铺Id
		if len(req.ShopId) > 0 {
			orderQuery = orderQuery.In("a.shop_id", req.ShopId)
		}
		//订单号
		if len(req.OrderSn) > 0 {
			orderQuery = orderQuery.Where("a.order_sn like ?", "%"+req.OrderSn+"%")
		}

		// 状态
		if req.State == oc.UpetDjOrderState_unPay {
			orderQuery = orderQuery.Where("a.order_status=?", 10)
		} else if req.State == oc.UpetDjOrderState_delivering {
			orderQuery = orderQuery.Where("a.order_status=? and (((select IFNULL(max(delivery_status),0) from order_delivery_node where order_sn=a.order_sn)=30) OR exists (select * from dc_order.order_exception where order_sn=a.order_sn and order_status=3) ) ", 20)
		} else if req.State == oc.UpetDjOrderState_payed {
			orderQuery = orderQuery.Where("a.order_status=?", 20)
		} else if req.State == oc.UpetDjOrderState_finished {
			orderQuery = orderQuery.Where("a.order_status=? or a.confirm_time is not null", 30)
		}

		// 查询分页数据
		var orderList []*models.Order
		total, err := orderQuery.Desc("a.id").Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).FindAndCount(&orderList)
		if err != nil {
			response.Code = utils.CODE_SERVEREXCEPTION
			return err
		}

		response.Data = make([]*oc.UpetDjOrderDto, len(orderList))
		response.Total = total
		if total == 0 {
			return nil
		}

		// 是否有下一页数据
		response.HasMore = int32(total) > req.PageIndex*req.PageSize

		// 异步获取数据
		var wg sync.WaitGroup
		for k, item := range orderList {
			wg.Add(1)
			k := k
			model := item
			// 启动协程获取子数据
			go func() {
				defer wg.Done()
				var dto = model.ToUpetDjOrderDto()

				dto.State = service.getUpetDjOrderDtoOrderStatus(model)
				// 查询订单商品信息
				dto.ProductList = service.getUpetDjOrderProductDtoByOrder(model)
				// 查询订单子的订单
				//dto.ChildOrderList = service.getUpetDjChildOrderByMainOrder(model.OrderMain, false)
				// 查询退款信息
				var refundOrder = service.getRefundByOrderSn(model.OrderMain.OrderSn, true)
				if len(refundOrder) > 0 {
					var lastReturnOrder = refundOrder[0]
					dto.ReturnState = lastReturnOrder.RefundState
					dto.ReturnWay = lastReturnOrder.FullRefund*10 + lastReturnOrder.RefundType
					dto.ReturnNo = lastReturnOrder.RefundSn
				}

				response.Data[k] = dto
			}()
		}
		wg.Wait()

		//重新排序
		linq.From(response.Data).OrderByDescending(func(x interface{}) interface{} { return x.(*oc.UpetDjOrderDto).CreateDateTime }).ToSlice(&response.Data)

		return err
	})

	return response, err
}

// 查询到家订单详情
func (service UpetDjService) QueryUpetDjOrderDetail(ctx context.Context, req *oc.UpetDjOrderDetailRequest) (*oc.UpetDjOrderDetailResponse, error) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("异常信息捕获QueryUpetDjOrderDetail ", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()

	// 数据响应
	var response = new(oc.UpetDjOrderDetailResponse)
	response.Code = utils.CODE_SUCCESS
	if req.MemberId == "" {
		response.Code = utils.CODE_BUSINESSERROR
		response.Message = "用户信息不能为空"
		return response, nil
	}
	err := service.BeginQuery(func(engine *xorm.Engine) (err error) {
		var orderInfo *models.Order // 订单信息
		if len(req.OrderId) > 0 {
			// 根据Id查询
			orderInfo = GetOrderByIdAndMemberId(cast.ToInt64(req.OrderId), req.MemberId)
		} else if len(req.OrderSn) > 0 {
			// 根据单号查询
			orderInfo = GetOrderByOrderSnAndMemberId(req.OrderSn, req.MemberId)
		} else {
			response.Code = utils.CODE_BUSINESSERROR
			response.Message = "请至少传递一个参数"
			return err
		}
		if orderInfo.Id == 0 {
			response.Code = utils.CODE_BUSINESSERROR
			response.Message = "订单信息不存在"
			return
		}

		//运费排除满减运费，优惠金额只包含店铺满减 -- 20201103 吕洪彪向郝妍确认
		service.orderMain = orderInfo.OrderMain
		orderPromotions := service.GetOrderPromotion()
		orderInfo.Privilege = 0
		for _, promotion := range orderPromotions {
			// 宠物saas  优惠类型 优惠活动类型:1改价,2特价,3储值卡,4满减,5优惠券,6次卡,7赠品,8储值卡,9抹零,10整单改价
			if orderInfo.AppChannel == 12 {
				orderInfo.Privilege += promotion.PromotionFee
				response.OrderPromotion = append(response.OrderPromotion, &oc.OrderPromotionDto{
					PromotionId:    int32(promotion.PromotionId),
					PromotionType:  promotion.PromotionType,
					PromotionTitle: promotion.PromotionTitle,
					PromotionFee:   promotion.PromotionFee,
					PoiCharge:      promotion.PoiCharge,
					PtCharge:       promotion.PtCharge,
					OrderSn:        promotion.OrderSn,
				})

			} else {
				if promotion.PromotionType == 1 { //店铺满减
					orderInfo.Privilege = promotion.PromotionFee
				}
				if promotion.PromotionType == 3 { //运费满减
					orderInfo.Freight -= promotion.PromotionFee
				}
			}

		}

		str := config.GetString("awen.order.exclude.shop")
		split := strings.Split(str, ",")

		//基础的配送信息
		response.OrderListInfo = orderInfo.ToUpetDjOrderDto()

		// 多个协程获取订单信息
		wg := sync.WaitGroup{}
		wg.Add(5)
		go func() {
			defer wg.Done()
			flag := false
			for _, v := range split {
				if v == response.OrderListInfo.ShopId {
					flag = true
					break
				}
			}
			// 是配置的门店 && 付过款的
			if flag && response.OrderListInfo.IsPay == 1 {
				// 在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款
				response.OrderListInfo.NoDisplayAfterPayment = 1
			} else {
				response.OrderListInfo.NoDisplayAfterPayment = 0
			}

			response.OrderListInfo.State = service.getUpetDjOrderDtoOrderStatus(orderInfo)
			response.OrderListInfo.IsApplyBtn = false

			if orderInfo.OrderStatus != 30 {
				// 15分钟待支付
				if orderInfo.OrderStatus == 10 {
					var remainSecondes = int32(math.Ceil(time.Now().Sub(orderInfo.CreateTime).Seconds()))
					if remainSecondes < 15*60 {
						response.RemainSeconds = 15*60 - remainSecondes
					}
					response.OrderListInfo.EnableCancel = true
				}

				// 5分钟待接单
				if orderInfo.OrderStatus == 20 && orderInfo.OrderStatusChild == 20101 {
					var remainSecondes = int32(math.Ceil(time.Now().Sub(orderInfo.PayTime).Seconds()))
					response.OrderListInfo.EnableCancel = remainSecondes > 2*60
					// 社区团购订单不显示倒计时
					if remainSecondes < 5*60 && orderInfo.PickupStationId == 0 {
						response.RemainSeconds = 5*60 - remainSecondes
					}
				}
				// 2分钟超时申请退款
				if orderInfo.OrderStatus == 20 && orderInfo.AcceptTime.Year() >= 2020 {
					var remainSecondes = int32(math.Ceil(time.Now().Sub(orderInfo.PayTime).Seconds()))
					if remainSecondes < 2*60 {
						response.RemainSeconds = 2*60 - remainSecondes
					}
				}
			}
			if orderInfo.OrderStatus >= 20 {
				response.OrderListInfo.PayMode = orderInfo.PayMode
				if orderInfo.AppChannel == 12 {
					orderPayment := make([]models.OrderPayment, 0)
					engine.Table("dc_order.order_payment").Where("order_sn=?", orderInfo.OrderSn).Find(&orderPayment)
					if len(orderPayment) > 0 {
						response.OrderListInfo.PayMode = int32(orderPayment[0].PayType)
					}
				}
			}
			if orderInfo.OrderStatus == 30 {
				// 加一天
				d, _ := time.ParseDuration("24h")
				if orderInfo.DeliveryType == 1 {
					//如果是物流配送，那么15天内可以申请退款
					d, _ = time.ParseDuration("360h") //加15天
				}
				millisecond1 := orderInfo.ConfirmTime.Add(d).UnixNano() / 1e6
				t := time.Now()
				millisecond2 := orderInfo.ConfirmTime.UnixNano() / 1e6
				millisecond3 := t.UnixNano() / 1e6
				if millisecond3 >= millisecond2 && millisecond3 < millisecond1 {
					response.OrderListInfo.IsApplyBtn = true
				}

			}
		}()
		go func() {
			defer wg.Done()
			// 查询店铺业务配置
			var shopBusinessSet = service.GetShopBusinessSetup(orderInfo.ShopId)
			if shopBusinessSet != nil {
				response.OrderListInfo.ShopMobile = shopBusinessSet.Mobile
				response.OrderListInfo.BusinessTimes = shopBusinessSet.BusinessTimes
				stockTime := shopBusinessSet.StockUpTime
				if stockTime == 0 {
					stockTime = 15
				}
				if orderInfo.DeliveryType == 3 && orderInfo.OrderStatusChild == 20102 && orderInfo.IsPicking == 0 {
					var stockSecondes = int32(math.Ceil(time.Now().Sub(orderInfo.AcceptTime).Seconds()))
					if stockSecondes < stockTime*60 {
						response.OrderListInfo.StockUpTime = stockTime*60 - stockSecondes
					}
				}
			}

			dcClient := dac.GetDataCenterClient()
			if storeRep, err := dcClient.RPC.QueryStoreInfo(dcClient.Ctx, &dac.StoreInfoRequest{
				FinanceCode: []string{orderInfo.ShopId},
			}); err != nil {
				glog.Error("获取门店信息失败；err: ", err)
				return
			} else if storeRep.Code != 200 {
				glog.Error("获取门店信息失败；err: ", storeRep)
				return
			} else if len(storeRep.Details) > 0 {
				response.OrderListInfo.ShopAddress = storeRep.Details[0].Address
			}

		}()
		go func() {
			defer wg.Done()
			// 获取配送信息
			response.DeliveryInfo = service.getUpetDjDeliverDto(ctx, orderInfo)

			//社区团购活动
			if orderInfo.OrderType == 15 {
				_ = service.BeginQuery(func(engine *xorm.Engine) error {
					var groupActivity models.OrderGroupActivityExt
					var OrderSn = orderInfo.ParentOrderSn
					if orderInfo.OrderStatus <= 20 && OrderSn == "" {
						OrderSn = orderInfo.OrderSn
					}
					if hasGroupActivity, err := engine.Select("oga.id, oga.`status`, oga.deliver_days, oga.member_id, oga.final_take_type, oga.receiver_name, oga.receiver_state, oga.receiver_city, oga.receiver_district, oga.receiver_address, oga.receiver_mobile, omg.receiver_name group_name, omg.receiver_mobile group_mobile, omg.receiver_address group_address").Table("order_main_group").Alias("omg").
						Join("LEFT", "order_group_activity oga", "omg.order_group_activity_id = oga.id").
						Where("omg.parent_order_sn = ?", OrderSn).Get(&groupActivity); err != nil {
						glog.Error("获取社区团购失败；err: ", err.Error())
						return nil
					} else if hasGroupActivity {
						response.OrderListInfo.GroupInfo = &oc.UpetDjOrderDto_GroupInfo{}
						response.OrderListInfo.GroupInfo.GroupId = cast.ToInt32(groupActivity.Id)
						response.OrderListInfo.GroupInfo.GroupStatus = groupActivity.Status
						response.OrderListInfo.GroupInfo.FinalTakeType = groupActivity.FinalTakeType
						if orderInfo.MemberId == groupActivity.MemberId {
							response.OrderListInfo.GroupInfo.GroupLeader = 1
						}
						response.OrderListInfo.GroupInfo.ReceiverName = groupActivity.ReceiverName
						response.OrderListInfo.GroupInfo.ReceiverMobile = groupActivity.ReceiverMobile
						response.OrderListInfo.GroupInfo.ReceiverState = groupActivity.ReceiverState
						response.OrderListInfo.GroupInfo.ReceiverCity = groupActivity.ReceiverCity
						response.OrderListInfo.GroupInfo.ReceiverDistrict = groupActivity.ReceiverDistrict
						response.OrderListInfo.GroupInfo.ReceiverAddress = groupActivity.ReceiverAddress
						response.OrderListInfo.GroupInfo.GroupName = groupActivity.GroupName
						response.OrderListInfo.GroupInfo.GroupMobile = groupActivity.GroupMobile
						response.OrderListInfo.GroupInfo.GroupAddress = groupActivity.GroupAddress
						if groupActivity.DeliverDays == 0 {
							response.OrderListInfo.GroupInfo.DeliverDays = "成团后当天"
						} else {
							response.OrderListInfo.GroupInfo.DeliverDays = fmt.Sprintf("成团后%d日内", groupActivity.DeliverDays)
						}
					}
					return nil
				})
			}

		}()
		go func() {
			defer wg.Done()
			// 查询订单商品信息
			response.OrderListInfo.ProductList = service.getUpetDjOrderProductDtoByOrder(orderInfo)
		}()
		go func() {
			defer wg.Done()
			// 查询退款
			var refundOrderList = service.getRefundByOrderSn(orderInfo.OrderSn, false)
			if len(refundOrderList) == 0 {
				return
			}
			// 查询退款明细商品信息
			for _, refundOrder := range refundOrderList {
				if refundOrder.RefundType == 1 {
					continue
				}
				if refundOrder.RefundState != 7 && refundOrder.RefundState != 8 {
					continue
				}
				//退款商品
				var refundProduct []models.RefundOrderProduct
				engine.Where("refund_sn=?", refundOrder.RefundSn).Find(&refundProduct)
				for _, product := range refundProduct {
					// 转换到Dto
					var dto = product.ToUpetDjRefundProductDto()
					if refundOrder.RefundType == 1 {
						dto.RefundCount = product.Quantity
					}
					//添加到集合
					response.RefundProductList = append(response.RefundProductList, dto)
				}
			}
			var lastRefundOrder = refundOrderList[0]
			var lastDto = lastRefundOrder.ToUpetDjRefundDto()

			if lastRefundOrder.RefundState != 9 {
				if lastRefundOrder.FullRefund == 1 && lastRefundOrder.RefundType == 1 && lastRefundOrder.RefundState != 3 && (lastRefundOrder.RefundReason == `计划有变，我不想要了` || lastRefundOrder.RefundReason == `我买错了/填错了`) {
					if lastRefundOrder.RefundState == 1 {
						lastDto.RefundState = 1
					} else {
						lastDto.RefundState = 2
					}

				} else if lastRefundOrder.RefundState == 2 {
					lastDto.RefundState = 8
				}
			}

			response.RefundInfo = lastDto
		}()

		wg.Wait()
		return err
	})

	return response, err
}

// 查询订单是否需要自动打印
func (service UpetDjService) QueryUpetDjOrderIsAutoPrint(ctx context.Context, req *oc.UpetDjOrderIsAutoPrintQueryRequest) (*oc.UpetDjOrderIsAutoPrintQueryResponse, error) {
	var response = new(oc.UpetDjOrderIsAutoPrintQueryResponse)
	response.Code = utils.CODE_SUCCESS
	// 开启查询
	err := service.BeginQuery(func(engint *xorm.Engine) error {
		var orderMain models.OrderMain
		isGet, err := engint.Where("order_sn=?", req.OrderSn).Get(&orderMain)
		if err != nil {
			response.Code = utils.CODE_SERVEREXCEPTION
			response.Message = err.Error()
		}
		if !isGet {
			response.Code = utils.CODE_BUSINESSERROR
			response.Message = "未找到单号对应单据信息"
		} else {
			//查询店铺设置信息
			shopBusinessSetup := service.GetShopBusinessSetupInfo(orderMain.ShopId)
			if shopBusinessSetup != nil {
				response.IsAutoPrint = shopBusinessSetup.AutoPrint
			}
		}

		return nil
	})

	return response, err
}

// 查询促销活动的订单统计信息
func (service UpetDjService) QueryPromotonOrderReport(ctx context.Context, req *oc.PromotonOrderReportRequest) (*oc.PromotionOrderReportReponse, error) {
	var response = new(oc.PromotionOrderReportReponse)
	// 查询数据
	err := service.BeginQuery(func(engine *xorm.Engine) error {
		//engine.SetMapper(names.SameMapper{})
		// 查询末班sql
		var tempSql = `select %s from order_promotion a inner join order_main b on a.order_sn=b.order_sn inner join order_detail c ON a.order_sn = c.order_sn 
                                                         where c.accept_time is not null and b.parent_order_sn = '' and  a.promotion_id=` + strconv.Itoa(int(req.PromotionId)) + " and b.pay_time>%s and b.pay_time<%s"
		// 昨日数据
		var yesterdayTotalCount = 0
		_, err := engine.SQL(fmt.Sprintf(tempSql, "count(1)", "date_add(current_date(),interval -1 day)", "current_date()")).Get(&yesterdayTotalCount)
		response.OrderCountYesterday = int32(yesterdayTotalCount)
		var yesterdayMoney float64 = 0
		_, err = engine.SQL(fmt.Sprintf(tempSql, "IFNULL(sum(b.total),0)", "date_add(current_date(),interval -1 day)", "current_date()")).Get(&yesterdayMoney)
		response.OrderMoneyYesterday = yesterdayMoney / 100
		// 全部数据
		var totalCount = 0
		_, err = engine.SQL(fmt.Sprintf(tempSql, "count(1)", "'"+req.StartDate+"'", "now()")).Get(&totalCount)
		response.OrderCountTotal = int64(totalCount)
		var totalMoney float64 = 0
		_, err = engine.SQL(fmt.Sprintf(tempSql, "IFNULL(sum(b.total),0)", "'"+req.StartDate+"'", "now()")).Get(&totalMoney)
		response.OrderMoneyTotal = totalMoney / 100

		// 总数量
		if totalCount > 0 {
			var dates []*oc.PromotionOrderReportWithDateListDto
			engine.SQL(`select calc_date,count(*) order_count,sum(total) order_money from 
								(select b.id,b.total,date_format(b.pay_time,'%Y-%m-%d') calc_date from order_promotion a inner join dc_order.order_main b on a.order_sn=b.order_sn inner join dc_order.order_detail c on b.order_sn=c.order_sn 
								                                                                  where c.accept_time is not null and b.parent_order_sn = '' and a.promotion_id=?) res
								group by calc_date `, req.PromotionId).Find(&dates)
			for _, item := range dates {
				item.OrderMoney = item.OrderMoney / 100
				response.OrderList = append(response.OrderList, item)
			}

		}

		return err

	})

	return response, err
}

// 查询商品Dto
func (service UpetDjService) getUpetDjOrderProductDtoByOrder(orderInfo *models.Order) []*oc.UpetDjOrderProductDto {
	var dtos []*oc.UpetDjOrderProductDto
	service.BeginQuery(func(engine *xorm.Engine) error {
		// 查询订单商品信息
		var productList []models.OrderProduct
		err := engine.Where("order_sn=?", orderInfo.OrderSn).Find(&productList)

		//父级商品
		parentProductMap := make(map[string]string, 0)
		var parentProductList []models.OrderProduct
		if len(orderInfo.ParentOrderSn) > 0 {
			err = engine.Where("order_sn=?", orderInfo.ParentOrderSn).And("product_type = 3").Find(&parentProductList)
			for _, parent := range parentProductList {
				parentProductMap[parent.SkuId] = parent.ProductId
			}
		}

		//如果是主订单需要遍历主父级商品
		if orderInfo.ParentOrderSn == "" {
			//TODO: 2021.3.31上uat后删除此判断，组合商品子商品全部从ChildrenSku字段解析
			if kit.GetTimeNow(orderInfo.CreateTime) > "2021-03-29 18:45:00" {
				for _, product := range productList {
					if len(product.ParentSkuId) == 0 {
						dto := product.ToUpetDjOrderProductDto()
						if len(product.ChildrenSku) > 0 {
							var childrenSkus []models.OrderProduct
							err = json.Unmarshal([]byte(product.ChildrenSku), &childrenSkus)
							if err != nil {
								glog.Error(orderInfo.OrderSn, ", 解析组合商品子商品失败, ", err, ", json: ", product.ChildrenSku)
								continue
							}

							for _, childrenProduct := range childrenSkus {
								dto.ChildProductList = append(dto.ChildProductList, childrenProduct.ToUpetDjOrderProductDto())
							}
						}

						dtos = append(dtos, dto)
						continue
					}
				}
			} else {
				//遍历父级商品
				for _, product := range productList {
					if len(product.ParentSkuId) == 0 {
						dtos = append(dtos, product.ToUpetDjOrderProductDto())
						continue
					}
				}

				//遍历子集商品
				for _, product := range productList {
					if len(product.ParentSkuId) == 0 {
						continue
					}
					for _, v := range dtos {
						if v.SkuId == product.ParentSkuId {
							v.ChildProductList = append(v.ChildProductList, product.ToUpetDjOrderProductDto())
						}
					}
				}
			}
		} else {
			for _, product := range productList {
				dto := product.ToUpetDjOrderProductDto()
				//查找父级
				if parentId, ok := parentProductMap[product.ParentSkuId]; ok {
					dto.ParentProductId = parentId
				}

				//虚拟商品查询核销码
				if dto.ProductType == 2 {
					verifyCodes := GetValidOrderVerifyCodes(orderInfo.OrderSn)

					dto.VerifyCodeList = make([]*oc.UpetDjProductVerifyCodeDto, len(verifyCodes))
					for k := range verifyCodes {
						//未核销的情况下判断是否过期
						if verifyCodes[k].VerifyStatus == 0 {
							if verifyCodes[k].VerifyCodeExpiryDate.Before(time.Now()) {
								verifyCodes[k].VerifyStatus = 3
							}
						}
						dto.VerifyCodeList[k] = &oc.UpetDjProductVerifyCodeDto{
							Id:                   verifyCodes[k].Id,
							ProductId:            dto.ProductId,
							OrderSn:              verifyCodes[k].OrderSn,
							VerifyCode:           verifyCodes[k].VerifyCode,
							VerifyCodeExpiryDate: kit.GetTimeNow(verifyCodes[k].VerifyCodeExpiryDate),
							VerifyStatus:         verifyCodes[k].VerifyStatus,
							VerifyTime:           kit.GetTimeNow(verifyCodes[k].VerifyTime),
							VerifyShop:           verifyCodes[k].VerifyShop,
							CreateTime:           kit.GetTimeNow(verifyCodes[k].CreateTime),
							UpdateTime:           kit.GetTimeNow(verifyCodes[k].UpdateTime),
						}
					}
				}

				dtos = append(dtos, dto)
			}
		}

		return err
	})

	return dtos
}

//查询子订单
//func (service UpetDjService) getUpetDjChildOrderByMainOrder(order *models.OrderMain, isCategoryInfo bool) []*oc.UpetDjOrderDto {
//	var childOrders []*oc.UpetDjOrderDto
//	service.BeginQuery(func(engine *xorm.Engine) error {
//		// 查询子订单商品信息
//		var productList []models.OrderProduct
//		err := engine.Where("order_sn=?", order.OrderSn).Find(&productList)
//		// 转换dto
//		for _, product := range productList {
//			var dto = product.ToUpetDjOrderProductDto()
//			// 查询商品信息
//			if isCategoryInfo {
//			}
//
//			dtos = append(dtos, dto)
//		}
//		return err
//	})
//
//	return dtos
//}

func (service UpetDjService) getUpetDjChildOrderByMainOrder(orderSns []string) []*oc.UpetDjOrderDto {
	var childOrders []*oc.UpetDjOrderDto
	service.BeginQuery(func(engine *xorm.Engine) error {
		// 查询所有子订单
		var (
			orderList      []models.Order           //所有子订单
			productList    []models.OrderProduct    //所有商品
			verifyCodeList []models.OrderVerifyCode //所有核销码
			err            error
		)

		if err = engine.Table("order_main").
			Select("order_main.*,buyer_memo,pickup_code").
			Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
			In("parent_order_sn", orderSns).Find(&orderList); err != nil {
			glog.Error("查询订单出错：" + err.Error())
			return nil
		}
		if err = engine.In("order_sn", orderSns).Find(&productList); err != nil {
			glog.Error("查询订单商品出错：" + err.Error())
			return nil
		}
		if err = engine.Where("1=1").In("order_sn", orderSns).Find(&verifyCodeList); err != nil {
			glog.Error("查询订单虚拟商品核销码出错：" + err.Error())
			return nil
		}

		for _, o := range orderList {
			//订单信息
			child := &oc.UpetDjOrderDto{
				OrderId:        cast.ToString(o.Id),
				OrderNo:        o.OrderSn,
				ShopId:         o.ShopId,
				ShopName:       o.ShopName,
				Remarks:        o.BuyerMemo,
				PickupCode:     o.PickupCode,
				TotalMoney:     float32(kit.FenToYuan(o.Total)),
				Privilege:      kit.FenToYuan(o.Privilege),
				CreateDateTime: o.CreateTime.Format(kit.DATETIME_LAYOUT),
			}

			//订单商品
			var childProducts []*oc.UpetDjOrderProductDto
			for _, p := range productList {
				if p.OrderSn == o.OrderSn {
					childProduct := &oc.UpetDjOrderProductDto{
						ProductId:          p.ProductId,
						ProductName:        p.ProductName,
						ProductCount:       p.Number,
						ProductPic:         p.Image,
						ProductPrice:       kit.FenToYuan(p.DiscountPrice),
						ProductActaulMoney: kit.FenToYuan(p.PaymentTotal),
						ProductSpecifica:   p.Specs,
						PromotionId:        int32(p.PromotionId),
						ProductActualPrice: kit.FenToYuan(p.PayPrice),
					}

					//核销码
					var childProductCodes []*oc.UpetDjProductVerifyCodeDto
					if p.ProductType == 2 {
						for _, v := range verifyCodeList {
							if p.OrderSn == v.OrderSn {
								childProductCode := &oc.UpetDjProductVerifyCodeDto{
									Id:                   v.Id,
									OrderSn:              v.OrderSn,
									VerifyCode:           v.VerifyCode,
									VerifyCodeExpiryDate: v.VerifyCodeExpiryDate.Format("2006/01/02 15:04:05"),
									VerifyStatus:         v.VerifyStatus,
									VerifyTime:           v.VerifyTime.Format("2006/01/02 15:04:05"),
									VerifyShop:           v.VerifyShop,
									CreateTime:           v.CreateTime.Format("2006/01/02 15:04:05"),
									UpdateTime:           v.UpdateTime.Format("2006/01/02 15:04:05"),
								}

								childProductCodes = append(childProductCodes, childProductCode)
							}
						}
						childProduct.VerifyCodeList = childProductCodes
					}
					childProducts = append(childProducts, childProduct)
				}
				child.ProductList = childProducts
			}
			childOrders = append(childOrders, child)
		}
		return err
	})

	return childOrders
}

// 查询退款单信息
func (service UpetDjService) getRefundByOrderSn(orderSn string, asc bool) []models.RefundOrder {
	var returnOrders []models.RefundOrder
	service.BeginQuery(func(engine *xorm.Engine) error {
		if asc {
			return engine.Where("order_sn=?", orderSn).Asc("create_time").Find(&returnOrders)
		} else {
			return engine.Where("order_sn=?", orderSn).Desc("create_time").Find(&returnOrders)
		}
	})
	return returnOrders
}

// 查询配送节点信息
func (service UpetDjService) getDeliveryNodes(orderSn string, asc bool) []models.OrderDeliveryNode {
	// 查询配送节点信息
	var deliveryNodes []models.OrderDeliveryNode
	service.BeginQuery(func(engine *xorm.Engine) error {
		if asc {
			return engine.Where("order_sn=?", orderSn).Asc("id").Find(&deliveryNodes)
		} else {
			return engine.Where("order_sn=?", orderSn).Desc("id").Find(&deliveryNodes)
		}
	})
	return deliveryNodes
}

// 查询配送节点信息根据配送单号
func (service UpetDjService) getDeliveryNodesByDeliveryId(deliveryId int64, asc bool) []models.OrderDeliveryNode {
	// 查询配送节点信息
	var deliveryNodes []models.OrderDeliveryNode
	service.BeginQuery(func(engine *xorm.Engine) error {
		if asc {
			return engine.Where("delivery_id=?", deliveryId).Asc("id").Find(&deliveryNodes)
		} else {
			return engine.Where("delivery_id=?", deliveryId).Desc("id").Find(&deliveryNodes)
		}
	})
	return deliveryNodes
}

// 查询配送信息
func (service UpetDjService) getDeliveryRecord(orderSn string, asc bool) []models.OrderDeliveryRecord {
	// 查询配送节点信息
	var deliveryRecords []models.OrderDeliveryRecord
	service.BeginQuery(func(engine *xorm.Engine) error {
		if asc {
			return engine.Where("order_sn=?", orderSn).Asc("id").Find(&deliveryRecords)
		} else {
			return engine.Where("order_sn=?", orderSn).Desc("id").Find(&deliveryRecords)
		}
	})
	return deliveryRecords
}

// 查询退款单信息
func (service UpetDjService) getRefundOrders(orderSn string) []models.RefundOrder {
	var refundOrders []models.RefundOrder
	service.BeginQuery(func(engine *xorm.Engine) error {
		return engine.Where("order_sn=?", orderSn).Select("refund_state").Find(&refundOrders)
	})
	return refundOrders
}

// 查询配送信息
func (service UpetDjService) getUpetDjDeliverDto(ctx context.Context, orderInfo *models.Order) *oc.UpetDjDeliveryDto {
	var deliveryDto = orderInfo.ToUpetDjDeliveryDto()
	// 查询店铺信息
	var shopInfo = service.GetShopInfo(orderInfo.ShopId)
	if shopInfo != nil {
		deliveryDto.ShopLongitude, _ = strconv.ParseFloat(shopInfo.PointX, 10)
		deliveryDto.ShopLatitude, _ = strconv.ParseFloat(shopInfo.PointY, 10)
	}
	// 查询配送节点信息
	deliveryDto.Nodes = service.getUpetDjDeliverNodesDto(orderInfo)
	// 查询美团配送标示
	service.BeginQuery(func(engine *xorm.Engine) error {
		var deliveryRecord models.OrderDeliveryRecord
		isexists, err := engine.Where("order_sn=?", orderInfo.OrderSn).Desc("id").Get(&deliveryRecord)
		if isexists {
			deliveryDto.DeliveryId = deliveryRecord.DeliveryId
			deliveryDto.DeliveryServiceCode = cast.ToString(deliveryRecord.DeliveryServiceCode)
			deliveryDto.DeliveryOrderId = deliveryRecord.MtPeisongId
			deliveryDto.DeliveryIdStr = cast.ToString(deliveryRecord.DeliveryId)
		}
		return err
	})

	//自提点处理
	if orderInfo.PickupStationId > 0 {
		service.BeginQuery(func(db *xorm.Engine) error {
			var pickStationRecord models.PickupStation
			exists, err := db.Table("datacenter.pickup_station").Where("id=?", orderInfo.PickupStationId).Get(&pickStationRecord)
			if exists {
				deliveryDto.PickupStationName = pickStationRecord.Name
				notice, _ := getPickupStationProcessNotice(db, orderInfo)
				deliveryDto.PickupStationAddress = fmt.Sprintf("%s（%s）", pickStationRecord.Address, notice)
			}
			return err
		})
	}

	// 快递配送发货记录
	if orderInfo.DeliveryType == 1 {
		service.BeginQuery(func(engine *xorm.Engine) error {
			var orderExpress []*models.OrderExpress
			err := engine.Where("order_sn=?", orderInfo.OrderSn).Find(&orderExpress)
			for _, v := range orderExpress {
				deliveryDto.Express = append(deliveryDto.Express, &oc.UpetDjDeliveryDto_OrderExpress{
					ExpressNo:   v.ExpressNo,
					Num:         v.Num,
					ExpressName: v.ExpressName,
				})
			}
			return err
		})
	}

	return deliveryDto
}

// 获取社区团购站点进度状态
func getPickupStationProcessNotice(db *xorm.Engine, orderInfo *models.Order) (state string, err error) {
	defer func() {
		if err != nil {
			glog.Info("社区团购 order-center getPickupStationState 订单号：", orderInfo.OrderSn, "，出错：", err.Error())
		}
	}()

	// 进度提示
	state = "拼团失败"

	// 已接单定义为拼团成功
	if !orderInfo.AcceptTime.IsZero() {
		return "拼团成功", nil
	} else if orderInfo.OrderStatus == 0 { // 取消定义为失败
		return
	}

	puc, err := models.GetPickupShopConfig(db, orderInfo.ShopId)
	if err != nil {
		return
	}
	//容错中途撤销活动导致panic
	if puc == nil {
		return
	}
	startTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, time.Now().Format(kit.DATE_LAYOUT)+" "+puc.EndTime+":00", time.Local)
	if err != nil {
		return
	}

	q := db.Table("dc_order.order_main").Alias("om").
		Join("inner", "dc_order.order_detail od", "om.order_sn = od.order_sn").
		Where("od.pickup_station_id = ? and om.shop_id = ?", orderInfo.PickupStationId, orderInfo.ShopId). // 当前门店站点
		Where("om.parent_order_sn != '' and order_status >= 20 and channel_id in (1,9)")                   // 已支付且是子单

	realEndTime := puc.EndTime
	// 当前时间在截止时间后
	if time.Now().After(startTime) {
		q.Where("om.pay_time > ?", startTime.Format(kit.DATETIME_LAYOUT)).
			Where("om.create_time > ?", startTime.Add(-1*time.Hour).Format(kit.DATETIME_LAYOUT)) // 充分利用索引
		realEndTime = "明日" + realEndTime
	} else {
		// 否则昨天到今天
		q.Where("pay_time > ?", startTime.Add(-24*time.Hour).Format(kit.DATETIME_LAYOUT)).
			Where("om.create_time > ?", startTime.Add(-25*time.Hour).Format(kit.DATETIME_LAYOUT)) // 充分利用索引
	}

	// 支付总金额，单位分
	var payAmountFenTotal int32
	if _, err = q.Select("sum(total)").Get(&payAmountFenTotal); err != nil {
		return
	}

	remainAmount := puc.StationDailyMin - float64(payAmountFenTotal)/100
	// 进度文案，还没满
	if remainAmount > 0.001 {
		return fmt.Sprintf("已支付%s元，%s前还需%s元即可成团",
			decimal.NewFromFloat(float64(payAmountFenTotal)/100).Round(2).String(), realEndTime,
			decimal.NewFromFloat(remainAmount).Round(2).String(),
		), nil
	}

	return "拼团成功", nil
}

// 查询订单轨迹dto
func (service UpetDjService) getUpetDjDeliverNodesDto(orderInfo *models.Order) []*oc.UpetDjDeliverNodesDto {
	var dtos []*oc.UpetDjDeliverNodesDto
	if orderInfo.CreateTime.Year() >= 2020 { // 下单信息
		var dto = new(oc.UpetDjDeliverNodesDto)
		dto.Message = "订单已提交"
		dto.CreateTime = orderInfo.CreateTime.Format(kit.DATETIME_LAYOUT)
		dtos = append(dtos, dto)
	}
	if orderInfo.PayTime.Year() >= 2020 { // 支付信息
		var dto = new(oc.UpetDjDeliverNodesDto)
		dto.Message = "订单已支付"
		dto.CreateTime = orderInfo.PayTime.Format(kit.DATETIME_LAYOUT)
		dtos = append(dtos, dto)
	}
	if orderInfo.AcceptTime.Year() >= 2020 { // 接单时间
		var dto = new(oc.UpetDjDeliverNodesDto)
		if orderInfo.DeliveryType != 3 {
			dto.Message = "商家已接单"
		} else {
			dto.Message = "商家已接单, 备货中"
		}
		dto.CreateTime = orderInfo.AcceptTime.Format(kit.DATETIME_LAYOUT)
		dtos = append(dtos, dto)
	}
	if orderInfo.AcceptTime.Year() >= 2020 && (orderInfo.ChannelId == ChannelAwenId || orderInfo.ChannelId == ChannelDigitalHealth) && orderInfo.DeliveryType == 3 && orderInfo.IsPicking == 1 {
		var dto = new(oc.UpetDjDeliverNodesDto)
		dto.Message = "备货完成, 待顾客取货"
		dto.CreateTime = orderInfo.PickingTime.Format(kit.DATETIME_LAYOUT)
		dtos = append(dtos, dto)
	}

	// 异常订单
	var exceptions = service.getOrderException(orderInfo.OrderSn, true)
	for _, item := range exceptions {
		var dto = new(oc.UpetDjDeliverNodesDto)
		dto.Message = item.ExceptionDescr
		dto.CreateTime = item.ExceptionTime
		dtos = append(dtos, dto)
	}
	if len(exceptions) == 0 {
		//美团配送信息
		var deliveryRecords = service.getDeliveryRecord(orderInfo.OrderSn, false)
		if len(deliveryRecords) > 0 {
			var deliveryNodes = service.getDeliveryNodesByDeliveryId(deliveryRecords[0].DeliveryId, false)
			for _, deliveryNode := range deliveryNodes {
				var dto = deliveryNode.ToUpetDjDeliverNodesDto()
				dtos = append(dtos, dto)
			}
			if len(deliveryNodes) == 0 && orderInfo.CancelTime.Year() >= 2020 { // 订单取消
				var dto = new(oc.UpetDjDeliverNodesDto)
				dto.Message = orderInfo.CancelReason
				dto.CreateTime = orderInfo.CancelTime.Format(kit.DATETIME_LAYOUT)
				dtos = append(dtos, dto)
			}
		}
	}

	// 快递配送发货记录
	if orderInfo.DeliveryType == 1 {
		service.BeginQuery(func(db *xorm.Engine) error {
			orderExpress := &models.OrderExpress{}
			has, err := db.Where("order_sn=?", orderInfo.OrderSn).Cols("delivery_time").Limit(1).Get(orderExpress)
			if has {
				dtos = append(dtos, &oc.UpetDjDeliverNodesDto{
					Message:    "已发货",
					CreateTime: orderExpress.DeliveryTime.Format(kit.DATETIME_LAYOUT),
				})
			}
			return err
		})
	}

	// 完成时间
	if orderInfo.ConfirmTime.Year() >= 2020 {
		var dto = new(oc.UpetDjDeliverNodesDto)
		dto.Message = "订单已完成"
		dto.CreateTime = orderInfo.ConfirmTime.Format(kit.DATETIME_LAYOUT)
		dtos = append(dtos, dto)
	}

	// 退款信息
	var refundLog = service.getRefundLogByOldOrderSn(orderInfo.OrderMain.OldOrderSn, true)
	if len(refundLog) > 0 {
		for _, item := range refundLog {
			var dto = new(oc.UpetDjDeliverNodesDto)
			if item.NotifyType == "first-agree" {
				dto.Message = "初审通过"
			} else if item.NotifyType == "first-reject" {
				dto.Message = "初审不通过"
			} else if item.NotifyType == "apply-refund-pay" {
				dto.Message = "退款成功"
			} else if len(item.OperationType) == 0 {
				continue
			} else {
				dto.Message = item.OperationType
			}
			dto.CreateTime = item.Ctime.Format(kit.DATETIME_LAYOUT)
			dtos = append(dtos, dto)
		}
	}
	linq.From(dtos).OrderBy(func(x interface{}) interface{} { return x.(*oc.UpetDjDeliverNodesDto).CreateTime }).ToSlice(&dtos)
	return dtos
}

// 查询订单状态
func (service UpetDjService) getUpetDjOrderDtoOrderStatus(orderInfo *models.Order) oc.UpetDjOrderState {
	if orderInfo.OrderStatus == 0 {
		if orderInfo.ConfirmTime.Year() >= 2020 {
			return oc.UpetDjOrderState_finished
		}
		return oc.UpetDjOrderState_unPayCancel
	}
	if orderInfo.OrderStatus == 10 {
		return oc.UpetDjOrderState_unPay
	}
	if orderInfo.OrderStatus == 20 {
		//拆单中
		if orderInfo.SplitOrderResult == 0 {
			return oc.UpetDjOrderState_splitIng
		}
		//拆单失败
		if orderInfo.SplitOrderResult == 2 {
			return oc.UpetDjOrderState_splitFail
		}
		if orderInfo.OrderStatusChild == 20101 {
			return oc.UpetDjOrderState_payedWaitShopReceive
		}
		if orderInfo.OrderStatusChild == 20102 {
			if orderInfo.DeliveryType == 3 && orderInfo.IsPicking == 1 {
				return oc.UpetDjOrderState_buyerSelfCollection
			} else if orderInfo.DeliveryType == 3 && orderInfo.IsPicking == 0 {
				return oc.UpetDjOrderState_payedShopReceivedPicking
			}
			return oc.UpetDjOrderState_payedShopReceived
		}
		// 待发货
		if orderInfo.OrderStatusChild == 20201 {
			return oc.UpetDjOrderState_expressWaitingDelivery
		}
		// 已发货
		if orderInfo.OrderStatusChild == 20202 || orderInfo.OrderStatusChild == 20204 {
			return oc.UpetDjOrderState_expressShipped
		}
		if orderInfo.DeliverTime.Year() >= 2020 {
			var deliveryRecords = service.getDeliveryRecord(orderInfo.OrderSn, false)
			if len(deliveryRecords) > 0 {
				var deliveryNodes = service.getDeliveryNodesByDeliveryId(deliveryRecords[0].DeliveryId, false)
				if len(deliveryNodes) > 0 {
					switch deliveryNodes[0].DeliveryStatus {
					case 20:
						return oc.UpetDjOrderState_deliveried
					case 30:
						return oc.UpetDjOrderState_delivering
					case 50:
						return oc.UpetDjOrderState_finished
					}
				}
			}
		} else {
			var deliveryRecords = service.getDeliveryRecord(orderInfo.OrderSn, false)
			if len(deliveryRecords) > 0 {
				var deliveryNodes = service.getDeliveryNodesByDeliveryId(deliveryRecords[0].DeliveryId, false)
				if len(deliveryNodes) > 0 {
					switch deliveryNodes[0].DeliveryStatus {
					case 0:
						return oc.UpetDjOrderState_waitDeliveried
					case 20:
						return oc.UpetDjOrderState_deliveried
					case 30:
						return oc.UpetDjOrderState_shipping
					case 55:
						return oc.UpetDjOrderState_finished
					}
				}
			}
		}
		if orderInfo.ConfirmTime.Year() >= 2020 {
			return oc.UpetDjOrderState_finished
		} else {
			var orderException = service.getOrderException(orderInfo.OrderSn, false)
			if len(orderException) > 0 {
				if orderException[0].OrderStatus != 3 {
					return oc.UpetDjOrderState_shipping
				} else {
					return oc.UpetDjOrderState_payedShopReceivedManual
				}
			}
			var orderExceptionForDelivered = service.getOrderExceptionForDelivered(orderInfo.OrderSn, false)
			if len(orderExceptionForDelivered.DeliveryId) > 0 {
				if orderExceptionForDelivered.OrderStatus != 3 {
					return oc.UpetDjOrderState_shipping
				} else {
					return oc.UpetDjOrderState_payedShopReceivedManual
				}
			}
		}

	}
	if orderInfo.OrderStatus == 30 {
		//虚拟订单如果还有未核销的核销码就展示未核销状态
		if orderInfo.IsVirtual == 1 && len(GetValidOrderVerifyCodes(orderInfo.OrderSn, 1)) > 0 {
			return oc.UpetDjOrderState_unVerify
		}
		return oc.UpetDjOrderState_finished
	}
	//退款状态
	//if orderInfo.OrderStatus >= 20 {
	//	//是否存在退款单
	//	var refundOrders = service.getRefundOrders(orderInfo.OrderSn)
	//	if len(refundOrders) > 0 {
	//		switch refundOrders[0].RefundState {
	//		case 1:
	//			return oc.UpetDjOrderState_refunding
	//		case 2:
	//			return oc.UpetDjOrderState_refundClosed
	//		case 3:
	//			return oc.UpetDjOrderState_refundSuccess
	//		case 6:
	//			return oc.UpetDjOrderState_refundFirstPass
	//		case 7:
	//			return oc.UpetDjOrderState_refundFinalPass
	//		case 8:
	//			return oc.UpetDjOrderState_refundFail
	//		case 9:
	//			return oc.UpetDjOrderState_refundUndo
	//		}
	//	}
	//}

	return oc.UpetDjOrderState_all
}

// 查询异常订单
func (service UpetDjService) getOrderException(orderNo string, asc bool) []models.OrderException {
	var models []models.OrderException
	service.BeginQuery(func(engine *xorm.Engine) error {
		var orderStr = "desc"
		if asc {
			orderStr = "asc"
		}
		return engine.SQL("select * from order_exception where order_sn=? and is_show = 1 order by exception_time "+orderStr, orderNo).Find(&models)
	})
	return models
}

// 查询是否有已送达异常订单
func (service UpetDjService) getOrderExceptionForDelivered(orderNo string, asc bool) models.OrderException {
	var models models.OrderException
	service.BeginQuery(func(engine *xorm.Engine) error {
		var orderStr = "desc"
		if asc {
			orderStr = "asc"
		}
		_, err := engine.SQL("select * from order_exception where order_sn=? order by exception_time "+orderStr, orderNo).Get(&models)
		return err
	})
	return models
}

// 查询退款日志
func (service UpetDjService) getRefundLogByOldOrderSn(orderNo string, asc bool) []models.RefundOrderLog {
	var models []models.RefundOrderLog

	service.BeginQuery(func(engine *xorm.Engine) error {
		if asc {
			return engine.Where("old_order_sn=?", orderNo).Asc("ctime").Find(&models)
		} else {
			return engine.Where("old_order_sn=?", orderNo).Desc("ctime").Find(&models)
		}
	})

	return models
}

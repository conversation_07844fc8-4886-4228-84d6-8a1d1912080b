package dto

// 订单优惠活动枚举
type OrderPrivilegeActiveType int32

const (
	Active_1    OrderPrivilegeActiveType = 1
	Active_2    OrderPrivilegeActiveType = 2
	Active_3    OrderPrivilegeActiveType = 3
	Active_4    OrderPrivilegeActiveType = 4
	Active_5    OrderPrivilegeActiveType = 5
	Active_6    OrderPrivilegeActiveType = 6
	Active_7    OrderPrivilegeActiveType = 7
	Active_8    OrderPrivilegeActiveType = 8
	Active_9    OrderPrivilegeActiveType = 9
	Active_10   OrderPrivilegeActiveType = 10
	Active_11   OrderPrivilegeActiveType = 11
	Active_12   OrderPrivilegeActiveType = 12
	Active_13   OrderPrivilegeActiveType = 13
	Active_14   OrderPrivilegeActiveType = 14
	Active_15   OrderPrivilegeActiveType = 15
	Active_16   OrderPrivilegeActiveType = 16
	Active_17   OrderPrivilegeActiveType = 17
	Active_18   OrderPrivilegeActiveType = 18
	Active_19   OrderPrivilegeActiveType = 19
	Active_20   OrderPrivilegeActiveType = 20
	Active_21   OrderPrivilegeActiveType = 21
	Active_22   OrderPrivilegeActiveType = 22
	Active_23   OrderPrivilegeActiveType = 23
	Active_24   OrderPrivilegeActiveType = 24
	Active_25   OrderPrivilegeActiveType = 25
	Active_100  OrderPrivilegeActiveType = 100
	Active_101  OrderPrivilegeActiveType = 101
	Active_102  OrderPrivilegeActiveType = 102
	Active_103  OrderPrivilegeActiveType = 103
	Active_26   OrderPrivilegeActiveType = 26
	Active_27   OrderPrivilegeActiveType = 27
	Active_50   OrderPrivilegeActiveType = 50
	Active_51   OrderPrivilegeActiveType = 51
	Active_52   OrderPrivilegeActiveType = 52
	Active_28   OrderPrivilegeActiveType = 28
	Active_29   OrderPrivilegeActiveType = 29
	Active_30   OrderPrivilegeActiveType = 30
	Active_31   OrderPrivilegeActiveType = 31
	Active_32   OrderPrivilegeActiveType = 32
	Active_33   OrderPrivilegeActiveType = 33
	Active_34   OrderPrivilegeActiveType = 34
	Active_35   OrderPrivilegeActiveType = 35
	Active_40   OrderPrivilegeActiveType = 40
	Active_41   OrderPrivilegeActiveType = 41
	Active_42   OrderPrivilegeActiveType = 42
	Active_117  OrderPrivilegeActiveType = 117
	Active_88   OrderPrivilegeActiveType = 88
	Active_45   OrderPrivilegeActiveType = 45
	Active_43   OrderPrivilegeActiveType = 43
	Active_46   OrderPrivilegeActiveType = 46
	Active_118  OrderPrivilegeActiveType = 118
	Active_66   OrderPrivilegeActiveType = 66
	Active_48   OrderPrivilegeActiveType = 48
	Active_53   OrderPrivilegeActiveType = 53
	Active_54   OrderPrivilegeActiveType = 54
	Active_55   OrderPrivilegeActiveType = 55
	Active_56   OrderPrivilegeActiveType = 56
	Active_57   OrderPrivilegeActiveType = 57
	Active_59   OrderPrivilegeActiveType = 59
	Active_201  OrderPrivilegeActiveType = 201
	Active_202  OrderPrivilegeActiveType = 202
	Active_304  OrderPrivilegeActiveType = 304
	Active_300  OrderPrivilegeActiveType = 300
	Active_305  OrderPrivilegeActiveType = 305
	Active_401  OrderPrivilegeActiveType = 401
	Active_402  OrderPrivilegeActiveType = 402
	Active_403  OrderPrivilegeActiveType = 403
	Active_404  OrderPrivilegeActiveType = 404
	Active_405  OrderPrivilegeActiveType = 405
	Active_406  OrderPrivilegeActiveType = 406
	Active_407  OrderPrivilegeActiveType = 407
	Active_408  OrderPrivilegeActiveType = 408
	Active_409  OrderPrivilegeActiveType = 409
	Active_410  OrderPrivilegeActiveType = 410
	Active_501  OrderPrivilegeActiveType = 501
	Active_503  OrderPrivilegeActiveType = 503
	Active_504  OrderPrivilegeActiveType = 504
	Active_505  OrderPrivilegeActiveType = 505
	Active_506  OrderPrivilegeActiveType = 506
	Active_507  OrderPrivilegeActiveType = 507
	Active_508  OrderPrivilegeActiveType = 508
	Active_510  OrderPrivilegeActiveType = 510
	Active_511  OrderPrivilegeActiveType = 511
	Active_512  OrderPrivilegeActiveType = 512
	Active_515  OrderPrivilegeActiveType = 515
	Active_516  OrderPrivilegeActiveType = 516
	Active_517  OrderPrivilegeActiveType = 517
	Active_528  OrderPrivilegeActiveType = 528
	Active_4011 OrderPrivilegeActiveType = 4011
)

func (activetype OrderPrivilegeActiveType) String() string {
	switch activetype {
	case Active_1:
		return "优惠码"
	case Active_2:
		return "满减"
	case Active_3:
		return "抵价券"
	case Active_4:
		return "套餐赠"
	case Active_5:
		return "满赠"
	case Active_6:
		return "超时赔付"
	case Active_7:
		return "特价菜"
	case Active_8:
		return "首单返优惠劵"
	case Active_9:
		return "使用优惠劵"
	case Active_10:
		return "运营发优惠劵"
	case Active_11:
		return "提前下单减"
	case Active_12:
		return "满返优惠劵"
	case Active_13:
		return "当面付返优惠券"
	case Active_14:
		return "随机返优惠券"
	case Active_15:
		return "兑换红包"
	case Active_16:
		return "满减配送费"
	case Active_17:
		return "折扣商品"
	case Active_18:
		return "美团专送减"
	case Active_19:
		return "使用点评优惠券"
	case Active_20:
		return "第2份半价活动"
	case Active_21:
		return "会员免配送费"
	case Active_22:
		return "门店新用户立减"
	case Active_23:
		return "买赠活动"
	case Active_24:
		return "AB新客活动"
	case Active_25:
		return "减配送费"
	case Active_100:
		return "满减商家优惠券"
	case Active_101:
		return "使用商家优惠券"
	case Active_102:
		return "供应链自定义"
	case Active_103:
		return "进店领券"
	case Active_26:
		return "满减AB"
	case Active_27:
		return "指定商品满减"
	case Active_50:
		return "微信钱包渠道首减"
	case Active_51:
		return "点评app渠道首减"
	case Active_52:
		return "美团app渠道首减"
	case Active_28:
		return "新客满减"
	case Active_29:
		return "新客满减AB"
	case Active_30:
		return "多阶梯满减配送费"
	case Active_31:
		return "满N件折"
	case Active_32:
		return "扫码购偶数件折扣"
	case Active_33:
		return "会员折扣/特价"
	case Active_34:
		return "买满件阶梯特价/折扣"
	case Active_35:
		return "组合特价/折扣"
	case Active_40:
		return "加价购"
	case Active_41:
		return "新客折扣菜"
	case Active_42:
		return "红包兑换"
	case Active_117:
		return "商品券"
	case Active_88:
		return "虚拟币"
	case Active_45:
		return "外卖拼团"
	case Active_43:
		return "X元M件"
	case Active_46:
		return "外卖加价购"
	case Active_118:
		return "商品折扣券"
	case Active_66:
		return "闪购会员折"
	case Active_48:
		return "拼团减配送费"
	case Active_53:
		return "新客专享减包装费"
	case Active_54:
		return "新客专享减配送费"
	case Active_55:
		return "第N件优惠"
	case Active_56:
		return "闪购爆品"
	case Active_57:
		return "新客专享减打包袋费"
	case Active_59:
		return "新客专享减配送费"
	case Active_201:
		return "会员首购优惠"
	case Active_202:
		return "会员限时特惠"
	case Active_304:
		return "减配送费劵"
	case Active_300:
		return "商家会员减配送费"
	case Active_305:
		return "联盟津贴"
	case Active_401:
		return "立减优惠"
	case Active_402:
		return "新用户立减"
	case Active_403:
		return "下单满赠"
	case Active_404:
		return "代金券优惠"
	case Active_405:
		return "礼金"
	case Active_406:
		return "商品特价"
	case Active_407:
		return "商品折扣"
	case Active_408:
		return "品类满减"
	case Active_409:
		return "商品直降"
	case Active_410:
		return "商品买赠"
	case Active_501:
		return "优惠码"
	case Active_503:
		return "优惠劵"
	case Active_504:
		return "满减"
	case Active_505:
		return "满折"
	case Active_506:
		return "首单优惠"
	case Active_507:
		return "VIP免运费"
	case Active_508:
		return "商家满免运费"
	case Active_510:
		return "满件减"
	case Active_511:
		return "满件折"
	case Active_512:
		return "首单地推满免运费"
	case Active_515:
		return "运费券"
	case Active_516:
		return "单品免运"
	case Active_517:
		return "用户积分抵扣金额"
	case Active_528:
		return "满减基础运费"
	case Active_4011:
		return "配送费满减"
	default:
		return "UNKNOWN"
	}
}

package services

import (
	"context"
	"encoding/json"
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

var iv InvoiceService

func TestInvoiceService_CreateInvoice(t *testing.T) {
	// 税收编码查询
	//res1, _ := iv.QueryTaxCode(&oc.TaxCodeRequest{
	//	//StructCode: "CX0004",
	//	StructCode: "CX0001",
	//	//NuanId:     []string{"50000303", "50000302"},
	//	ThirdSkuIds: []string{"S000HSXYSW"},
	//	Source: 3,
	//	Page:   1,
	//	Size:   20,
	//})
	//fmt.Println(res1)

	// 开票, order_sn=4100000013237979，R1编码测试数据
	res2, _ := iv.CreateInvoice(&oc.CreateInvoiceRequest{
		OrderSn:   "4100000013237979",
		InvoiceTt: 1,
		Mobile:    "18674221127",
		Email:     "<EMAIL>",
	})
	fmt.Println(res2)

	// 签名
	//appKey := config.GetString("R1Auth.AppId")
	//timeStamp := time.Now().UnixNano()
	//params, _ := json.Marshal(map[string]interface{}{
	//	"skuNoList": []string{"2143527001"},
	//})
	//sign := utils.A8Sign(map[string]string{
	//	"appKey":    appKey,
	//	"timestamp": cast.ToString(timeStamp),
	//}, string(params), config.GetString("R1Auth.Secret"))
	//
	//url := fmt.Sprintf("%sscm/open/api/sku/selectTax?appKey=%s&timestamp=%d&sign=%s", config.GetString("r1-open-url"), appKey, timeStamp, sign)
	//res, _ := utils.HttpPost(url, params, "application/json")
	//var resData dto.R1TaxCodeResponse
	//json.Unmarshal(res, &resData)
	//fmt.Println(resData)

}

func TestInvoiceService_RefundInvoice(t *testing.T) {

	var ctx context.Context
	res, _ := iv.RefundInvoice(ctx, &oc.RefundInvoiceRequest{
		RefundSn: "50000135528",
	})
	fmt.Println(res)
}

func TestInvoiceService_QueryInvoiceCompany(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.QueryInvoiceCompanyRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.QueryInvoiceCompanyResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{
				in: &oc.QueryInvoiceCompanyRequest{Keyword: "新瑞鹏"},
			},
		}, {
			name: "",
			args: args{
				in: &oc.QueryInvoiceCompanyRequest{Keyword: ""},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			got, err := iv.QueryInvoiceCompany(tt.args.ctx, tt.args.in)
			t.Log(got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryInvoiceCompany() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_InvoiceApply(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.CreateInvoiceRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *oc.InvoiceResponse
		wantErr  bool
	}{
		{
			name: "",
			args: args{in: &oc.CreateInvoiceRequest{
				OrderSn: "4100000014216099#1637244189",
			}},
		}, {
			name: "",
			args: args{in: &oc.CreateInvoiceRequest{
				OrderSn:   "4100000014216099",
				ScrmId:    "895e3b3a541241a6b9a5c04a4e0f6ec2",
				InvoiceTt: 1,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			gotResp, err := iv.InvoiceApply(tt.args.ctx, tt.args.in)
			t.Log(gotResp.Code, gotResp.Message, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvoiceApply() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_InvoiceDetail(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.InvoiceDetailRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *oc.InvoiceDetailResponse
		wantErr  bool
	}{
		{
			name: "",
			args: args{in: &oc.InvoiceDetailRequest{
				OrderSn: "4100000014216099",
				ScrmId:  "895e3b3a541241a6b9a5c04a4e0f6ec2",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			gotResp, err := iv.InvoiceDetail(tt.args.ctx, tt.args.in)
			t.Log(gotResp, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvoiceDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_InvoiceSendEmail(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.InvoiceSendEmailRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *oc.InvoiceResponse
		wantErr  bool
	}{
		{
			name: "",
			args: args{in: &oc.InvoiceSendEmailRequest{
				OrderSn: "4100000014282437",
				ScrmId:  "5c1ad0f2e4e94f39a43c263c9b287d6d",
				Email:   "<EMAIL>",
			}},
		}, {
			name: "",
			args: args{in: &oc.InvoiceSendEmailRequest{
				OrderSn: "111",
				ScrmId:  "222",
				Email:   "<EMAIL>",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			gotResp, err := iv.InvoiceSendEmail(tt.args.ctx, tt.args.in)
			t.Log(gotResp.Code, gotResp.Message, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvoiceSendEmail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_QueryInvoiceCompanyInfo(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.InvoiceCompanyInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.InvoiceCompanyInfoResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{
				in: &oc.InvoiceCompanyInfoRequest{Code: "5N8FH3"},
			},
		}, {
			name: "",
			args: args{
				in: &oc.InvoiceCompanyInfoRequest{Code: "123"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			got, err := iv.InvoiceCompanyInfo(tt.args.ctx, tt.args.in)
			t.Log(got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryInvoiceCompanyInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_InvoiceStatus(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.InvoiceStatusRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *oc.InvoiceStatusResponse
		wantErr  bool
	}{
		{
			args: args{in: &oc.InvoiceStatusRequest{
				OrderSn: "4100000014216099",
				ScrmId:  "895e3b3a541241a6b9a5c04a4e0f6ec2",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}
			gotResp, err := iv.InvoiceStatus(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotResp), err)

			if (err != nil) != tt.wantErr {
				t.Errorf("InvoiceStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestInvoiceService_QueryTaxCode(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		in *oc.TaxCodeRequest
	}
	jsonParam := `{"struct_code":"CX0011","third_sku_ids":["S00241B2NE","S0703XX1016"],"nuan_id":null,"page":1,"size":20,"source":3}`
	param := new(oc.TaxCodeRequest)
	_ = json.Unmarshal([]byte(jsonParam), param)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.TaxCodeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				in: param,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			iv := &InvoiceService{
				CommonService: tt.fields.CommonService,
			}

			got, err := iv.QueryTaxCode(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryTaxCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryTaxCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

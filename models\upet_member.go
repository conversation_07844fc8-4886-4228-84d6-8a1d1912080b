package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"strings"
	"time"
)

type UpetMember struct {
	MemberId              int32     `xorm:"not null pk autoincr comment('会员id') INT(11)"`
	ScrmUserId            string    `xorm:"default '' comment('用户ID作为唯一标示') index VARCHAR(32)"`
	MemberName            string    `xorm:"comment('会员名称') unique VARCHAR(50)"`
	MemberTruename        string    `xorm:"comment('真实姓名') VARCHAR(20)"`
	MemberAvatar          string    `xorm:"comment('会员头像') VARCHAR(255)"`
	MemberWxavatar        string    `xorm:"comment('微信头像') VARCHAR(255)"`
	MemberSex             int       `xorm:"comment('会员性别') TINYINT(1)"`
	MemberBirthday        time.Time `xorm:"comment('生日') DATE"`
	MemberPasswd          string    `xorm:"comment('会员密码') VARCHAR(32)"`
	MemberPaypwd          string    `xorm:"comment('支付密码') CHAR(32)"`
	MemberEmail           string    `xorm:"not null comment('会员邮箱') VARCHAR(100)"`
	MemberEmailBind       int       `xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT(4)"`
	MemberMobile          string    `xorm:"comment('手机号') index VARCHAR(11)"`
	MemberMobileBind      int       `xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT(4)"`
	MemberQq              string    `xorm:"comment('qq') VARCHAR(100)"`
	MemberWw              string    `xorm:"comment('阿里旺旺') VARCHAR(100)"`
	MemberLoginNum        int       `xorm:"not null default 1 comment('登录次数') INT(11)"`
	MemberTime            string    `xorm:"not null comment('会员注册时间') VARCHAR(10)"`
	MemberLoginTime       string    `xorm:"not null comment('当前登录时间') VARCHAR(10)"`
	MemberOldLoginTime    string    `xorm:"not null comment('上次登录时间') VARCHAR(10)"`
	MemberLoginIp         string    `xorm:"comment('当前登录ip') VARCHAR(20)"`
	MemberOldLoginIp      string    `xorm:"comment('上次登录ip') VARCHAR(20)"`
	MemberQqopenid        string    `xorm:"comment('qq互联id') VARCHAR(100)"`
	MemberQqinfo          string    `xorm:"comment('qq账号相关信息') TEXT"`
	MemberSinaopenid      string    `xorm:"comment('新浪微博登录id') VARCHAR(100)"`
	MemberSinainfo        string    `xorm:"comment('新浪账号相关信息序列化值') TEXT"`
	WeixinUnionid         string    `xorm:"comment('微信用户统一标识') VARCHAR(50)"`
	WeixinInfo            string    `xorm:"comment('微信用户相关信息') VARCHAR(255)"`
	MemberPoints          int       `xorm:"not null default 0 comment('会员积分') INT(11)"`
	AvailablePredeposit   float64   `xorm:"not null default 0.00 comment('预存款可用金额') DECIMAL(10,2)"`
	FreezePredeposit      float64   `xorm:"not null default 0.00 comment('预存款冻结金额') DECIMAL(10,2)"`
	AvailableRcBalance    float64   `xorm:"not null default 0.00 comment('可用充值卡余额') DECIMAL(10,2)"`
	FreezeRcBalance       float64   `xorm:"not null default 0.00 comment('冻结充值卡余额') DECIMAL(10,2)"`
	InformAllow           int       `xorm:"not null default 1 comment('是否允许举报(1可以/2不可以)') TINYINT(1)"`
	IsBuy                 int       `xorm:"not null default 1 comment('会员是否有购买权限 1为开启 0为关闭') TINYINT(1)"`
	IsAllowtalk           int       `xorm:"not null default 1 comment('会员是否有咨询和发送站内信的权限 1为开启 0为关闭') TINYINT(1)"`
	MemberState           int       `xorm:"not null default 1 comment('会员的开启状态 1为开启 0为关闭') TINYINT(1)"`
	MemberSnsvisitnum     int       `xorm:"not null default 0 comment('sns空间访问次数') INT(11)"`
	MemberAreaid          int       `xorm:"comment('地区ID') INT(11)"`
	MemberCityid          int       `xorm:"comment('城市ID') INT(11)"`
	MemberProvinceid      int       `xorm:"comment('省份ID') INT(11)"`
	MemberAreainfo        string    `xorm:"comment('地区内容') VARCHAR(255)"`
	MemberPrivacy         string    `xorm:"comment('隐私设定') TEXT"`
	MemberExppoints       int       `xorm:"not null default 0 comment('会员经验值') INT(11)"`
	TradAmount            float64   `xorm:"default 0.00 comment('可提现金额') DECIMAL(12,2)"`
	AuthMessage           string    `xorm:"comment('审核意见') VARCHAR(255)"`
	DistriState           int       `xorm:"default 0 comment('分销状态 0未申请 1待审核 2已通过 3未通过 4清退 5退出') index TINYINT(1)"`
	BillUserName          string    `xorm:"comment('收款人姓名') VARCHAR(255)"`
	BillTypeCode          string    `xorm:"comment('结算账户类型') VARCHAR(255)"`
	BillTypeNumber        string    `xorm:"comment('收款账号') VARCHAR(255)"`
	BillBankName          string    `xorm:"comment('开户行') VARCHAR(255)"`
	FreezeTrad            float64   `xorm:"default 0.00 comment('冻结佣金') DECIMAL(12,2)"`
	DistriCode            string    `xorm:"comment('分销代码') VARCHAR(255)"`
	DistriFormid          string    `xorm:"comment('小程序formId') VARCHAR(30)"`
	DistriChainid         int32     `xorm:"default 0 comment('分销门店ID') INT(11)"`
	DistriBrandid         int       `xorm:"default 0 comment('品牌ID') INT(11)"`
	DistriTime            int       `xorm:"comment('申请时间') INT(11)"`
	DistriHandleTime      int       `xorm:"comment('处理时间') INT(11)"`
	DistriShow            int       `xorm:"not null default 0 comment('分销中心是否显示 0不显示 1显示') TINYINT(1)"`
	QuitTime              int       `xorm:"comment('退出时间') INT(11)"`
	DistriApplyTimes      int       `xorm:"default 0 comment('申请次数') INT(11)"`
	DistriQuitTimes       int       `xorm:"default 0 comment('退出次数') INT(11)"`
	WeixinMpOpenid        string    `xorm:"comment('微信公众号OpenID') VARCHAR(50)"`
	IsCash                int       `xorm:"default 1 comment('是否允许提现，0否，1是') TINYINT(1)"`
	IdCardName            string    `xorm:"default '' comment('实名认证姓名') VARCHAR(20)"`
	IdCardCode            string    `xorm:"default '' comment('身份证号') VARCHAR(20)"`
	IdCardBind            int       `xorm:"not null default 0 comment('是否实名认证0否,1是') TINYINT(1)"`
	IdCardState           int       `xorm:"not null default 0 comment('审核状态0未申请，1待审核，2审核成功，3审核失败') TINYINT(1)"`
	IdCardExplain         string    `xorm:"default '' comment('审核说明') VARCHAR(50)"`
	IdCardImg             string    `xorm:"default '' comment('身份证正反面图片') VARCHAR(50)"`
	WeixinMiniOpenid      string    `xorm:"default '' comment('小程序openid(阿闻宠物-北京那边用)') index VARCHAR(50)"`
	WeixinMiniAddtime     int       `xorm:"default 0 comment('小程序绑定时间(阿闻宠物-北京那边用)') INT(11)"`
	WeixinMiniOpenidshop  string    `xorm:"default '' comment('小程序openid(阿闻智慧门店-自用)') index VARCHAR(50)"`
	WeixinMiniAddtimeshop int       `xorm:"default 0 comment('小程序绑定时间(阿闻智慧门店-自用)') INT(11)"`
	WeixinMiniOpenidasq   string    `xorm:"default '' comment('小程序openid(阿闻爱省钱-自用)') index VARCHAR(50)"`
	WeixinMiniAddtimeasq  int       `xorm:"default 0 comment('小程序绑定时间(阿闻爱省钱-自用)') INT(11)"`
	WeixinMiniAddtimemall int       `xorm:"default 0 comment('小程序绑定时间(阿闻商城-自用)') INT(11)"`
	EarnestMoney          float64   `xorm:"default 0.00 comment('保证金金额') DECIMAL(12,2)"`
	GevalCommentStatus    int       `xorm:"default 0 comment('0为电商 1为采集 2为宠医云 3阿闻智慧宠物医院4阿闻宠物小程序5阿闻爱省钱6阿闻商城7数据中心8佳雯会员') TINYINT(4)"`
	DisTradMoney          float64   `xorm:"default 0.00 comment('累计收益') DECIMAL(12,2)"`
	BillBankBranch        string    `xorm:"comment('开户银行支行名称') VARCHAR(30)"`
	MemberIsvip           int       `xorm:"default 0 comment('0.默认1.198会员') TINYINT(1)"`
	MemberIsbzk           int       `xorm:"default 0 comment('0.默认1.保障卡') TINYINT(1)"`
	MemberVipstime        int       `xorm:"default 0 comment('会员开始时间') INT(11)"`
	MemberVipetime        int       `xorm:"default 0 comment('会员过期时间') INT(11)"`
	MemberBzkstime        int       `xorm:"default 0 comment('保障卡开始时间') INT(11)"`
	MemberBzketime        int       `xorm:"default 0 comment('保障卡结束时间') INT(11)"`
	MemberIdentity        string    `xorm:"comment('分销员身份证') VARCHAR(50)"`
	MemberMobileBefore    string    `xorm:"comment('修改前手机号') VARCHAR(11)"`
	MemberVipstarttime    int       `xorm:"default 0 comment('电商198会员开始时间') INT(11)"`
	MemberVipendtime      int       `xorm:"default 0 comment('电商198会员结束时间') INT(11)"`
	NewcomerTag           int       `xorm:"comment('新用户标记，null未更新、1=新人、2，有可能成为新人、3=老用户') TINYINT(4)"`
	UserLevelId           int       `xorm:"default 0 comment('会员等级') tinyint(1)"`
	VipCardState          int       `xorm:"comment('付费会员:0-否，1-是') TINYINT(4)"`
}

//附加字段的会员信息
type UPetMemberPlus struct {
	UpetMember
	Level   int   //等级
	StoreId int32 //门店id
}

// 会员与门店分销的一些信息
type UpetMemberChain struct {
	MemberId       int64  `json:"member_id"`
	ScrmUserId     string `json:"scrm_user_id"`
	MemberMobile   string `json:"member_mobile"`  // 会员手机
	BillUserName   string `json:"bill_user_name"` //分销员真实姓名
	DistriChainid  int32  `json:"distri_chainid"`
	ChainAccountId string `json:"chain_account_id"` //门店财务编码
	ChainName      string `json:"chain_name"`       //门店名称
	MemberAreaName string `json:"member_area_name"` //会员所在城市
}

// 根据会员id获取分销员信息
func (u *UpetMemberChain) GetByScrmUserId(engine *xorm.Engine, ScrmUserId string) (bool, error) {
	fields := []string{
		"upet_member.member_id",
		"upet_member.scrm_user_id",
		"upet_member.member_mobile",
		"upet_member.bill_user_name",
		"upet_member.distri_chainid",
		"upet_chain.account_id as chain_account_id",
		"upet_chain.chain_name",
		"upet_area.area_name as member_area_name",
	}
	has, err := engine.
		Table("upet_member").
		Join("left", "upet_chain", "upet_member.distri_chainid=upet_chain.chain_id").
		Join("left", "upet_area", "upet_member.member_areaid=upet_area.area_id").
		Where("upet_member.scrm_user_id = ?", ScrmUserId).
		Select(strings.Join(fields, ",")).
		Get(u)
	return has, err
}

// 根据scrm_user_id获取分销员信息
func (u UpetMemberChain) FindInScrmUserId(engine *xorm.Engine, ScrmUserId []string) ([]*UpetMemberChain, error) {
	fields := []string{
		"upet_member.member_id",
		"upet_member.scrm_user_id",
		"upet_member.member_mobile",
		"upet_member.bill_user_name",
		"upet_member.distri_chainid",
		"upet_chain.account_id as chain_account_id",
		"upet_chain.chain_name",
		"upet_area.area_name as member_area_name",
	}
	var list []*UpetMemberChain
	err := engine.
		Table("upet_member").
		Join("left", "upet_chain", "upet_member.distri_chainid=upet_chain.chain_id").
		Join("left", "upet_area", "upet_member.member_areaid=upet_area.area_id").
		In("upet_member.scrm_user_id", ScrmUserId).
		Select(strings.Join(fields, ",")).
		Find(&list)
	return list, err
}

// GetDisMember 获取分销员
func (m UpetMember) GetDisMember(db *xorm.Engine, disId int32) (*UpetMember, error) {
	// 判断分销是否开启
	if has, err := db.Table("upet_setting").Where("name = 'distribute_isuse' and value > 0").Exist(); err != nil {
		return nil, err
	} else if !has {
		return nil, errors.New("未开启分销")
	}

	// 获取分销员
	disMember := new(UpetMember)

	if disId > 0 {
		if has, err := db.Table("upet_dis_goods").Alias("d").Join("inner", "upet_member m", "m.member_id = d.member_id").
			Where("d.distri_id = ?", disId).Select("m.*").Get(disMember); err != nil {
			return nil, err
		} else if !has {
			return nil, errors.New("dis_id无效")
		} else {
			if disMember.MemberId == m.MemberId {
				return nil, errors.New("自己扫自己不算分销")
			} else if disMember.DistriState != 2 {
				return nil, errors.New("分销员状态异常")
			}
		}
	} else { // 查粉丝关系表
		var fansDay int64 // 粉丝关系有效天数
		if _, err := db.Table("upet_setting").Where("name = 'distri_user_time'").Select("value").Get(&fansDay); err != nil {
			return nil, err
		} else if fansDay == 0 {
			return nil, errors.New("粉丝保护期0天，不处理分销关系")
		} else {
			if has, err := db.Table("upet_distri_member_fans").Alias("f").
				Join("inner", "upet_member m", "m.member_id = f.dis_member_id").
				Where("f.member_id = ? and f.state = 1 and f.create_time >= ?", m.MemberId, time.Now().Unix()-fansDay*24*60*60).
				Select("m.*").Get(disMember); err != nil {
				return nil, err
			} else if !has {
				return nil, errors.New("通过粉丝关系未找到分销员")
			} else if disMember.DistriState != 2 {
				return nil, errors.New("分销员状态异常")
			} else {
				if disMember.MemberId == m.MemberId {
					return nil, errors.New("分销员为本人，分销无效")
				}
			}
		}
	}

	if has, err := db.Table("upet_chain_bind").Where("member_id = ?", disMember.MemberId).Exist(); err != nil {
		return nil, err
	} else if has {
		return nil, errors.New("客服不允许分销")
	}

	return disMember, nil
}

// RefreshFans 刷新粉丝关系
func (m UpetMember) RefreshFans(db *xorm.Engine, disMemberId int32, disType int32) error {
	var fansDay int64

	if _, err := db.Table("upet_setting").Where("name = 'distri_user_time'").Select("value").Get(&fansDay); err != nil {
		return err
	} else if fansDay == 0 {
		return nil
	}

	fans := new(UpetDistriMemberFans)

	if has, err := db.Table("upet_distri_member_fans").
		Where("member_id = ? and state = 1 and create_time >= ?", m.MemberId, time.Now().Unix()-fansDay*24*60*60).
		Select("dis_fans_id,dis_member_id").Get(fans); err != nil {
		return err
	} else if has {
		if int32(fans.DisMemberId) == disMemberId {
			if _, err = db.ID(fans.DisFansId).Update(&UpetDistriMemberFans{
				State:      1,
				CreateTime: int(time.Now().Unix()),
				UpdateTime: int(time.Now().Unix()),
			}); err != nil {
				return err
			}
		}
	} else {
		if _, err = db.Insert(&UpetDistriMemberFans{
			DisMemberId: int(disMemberId),
			MemberId:    int(m.MemberId),
			State:       1,
			DisType:     int(disType),
			CreateTime:  int(time.Now().Unix()),
		}); err != nil {
			return err
		}
	}
	return nil
}

// RefreshOutsideMember 刷新外部代理人
func (m UpetMember) RefreshOutsideMember(db *xorm.Engine, disMember *UpetMember) error {
	// 存在有效的关联关系
	if has, err := db.Where("member_id = ? and state = 1", m.MemberId).Exist(&UpetDistriOutsideMember{}); err != nil {
		return err
	} else if has {
		return nil
	}

	outside := new(UpetDistriOutsideMember)

	if has, err := db.Where("member_id = ? and dis_member_id = ?", m.MemberId, disMember.MemberId).Get(outside); err != nil {
		return err
	} else if has {
		outside.ChainId = int(disMember.DistriChainid)
		outside.Addtime = int(time.Now().Unix())
		outside.Updatetime = int(time.Now().Unix())
		outside.State = 1
		if _, err = db.ID(outside.DmId).MustCols("chain_id").Update(outside); err != nil {
			return err
		}
	} else {
		if _, err = db.Insert(&UpetDistriOutsideMember{
			DisMemberId: int(disMember.MemberId),
			MemberId:    int(m.MemberId),
			ChainId:     int(disMember.DistriChainid),
			State:       1,
			Addtime:     int(time.Now().Unix()),
			Updatetime:  int(time.Now().Unix()),
		}); err != nil {
			return err
		}
	}

	return nil
}

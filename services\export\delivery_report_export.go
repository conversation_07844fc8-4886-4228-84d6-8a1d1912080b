package export

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"time"
)

// 配送报表
type DeliveryReportExport struct {
	F          *excelize.File
	SheetName  string
	taskParams *oc.AwenParentOrderListRequest
	writer     *excelize.StreamWriter
}

// 配送报表导出
func (e *DeliveryReportExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenParentOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	o := services.OrderService{}
	var ret *oc.DeliveryReportResponse
	k := 0
	for {
		ret, err = o.OrderDeliveryReportList(context.Background(), e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		for i := 0; i < len(ret.Details); i++ {
			k++
			IsException := "否"
			if ret.Details[i].IsExceptionDelivery == 1 {
				IsException = "是"
			}
			//重量千克
			WeightKg := fmt.Sprintf("%.2f", cast.ToFloat64(ret.Details[i].TotalWeight)/float64(1000)) // 保留2位小数
			//重量千米
			DistanceKm := fmt.Sprintf("%.2f", cast.ToFloat64(ret.Details[i].Distance)/float64(1000)) // 保留2位小数
			axis := fmt.Sprintf("A%d", k+1)
			//店铺类型
			ShopType := "新瑞鹏"
			if ret.Details[i].AppChannel != 1 {
				ShopType = "TP代运营"
			}
			//计算最终配送费  0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风'
			lasPrice := ""
			switch ret.Details[i].LastDeliveryType {
			case 0:
				lasPrice = ret.Details[i].MtPrice
				break
			case 3:
				lasPrice = ret.Details[i].DadaPrice
				break
			case 4:
				lasPrice = ret.Details[i].FnPrice
				break
			case 5:
				lasPrice = ret.Details[i].SfPrice
				break
			}

			_ = e.writer.SetRow(axis, []interface{}{
				ret.Details[i].OrderSn,       // 子订单
				ret.Details[i].ParentOrderSn, // 父订单
				ret.Details[i].OldOrderSn,    // 外部单号
				ret.Details[i].MtPrice,       //美团报价
				ret.Details[i].DadaPrice,     //达达报价
				ret.Details[i].FnPrice,       //蜂鸟报价
				//ret.Details[i].SfPrice,       //顺丰报价
				ret.Details[i].LastDelivery,                              //最终承运商
				ret.Details[i].ShopName,                                  //门店名称
				ret.Details[i].ShopId,                                    //门店名称
				services.OrderFrom[ret.Details[i].ChannelId],             //订单来源
				services.OrderStatusMap[ret.Details[i].OrderStatusChild], // 订单状态
				ret.Details[i].CreateTime,                                //下单时间
				services.DeliveryType[ret.Details[i].DeliveryType],       // 配送方式
				ShopType,                      // 店铺类型
				ret.Details[i].ChannelStoreId, // 第三方ID
				IsException,                   // 是否异常订单
				DistanceKm,                    // 配送距离
				WeightKg,                      //总重量
				lasPrice,                      //最终价格
			})
		}
		if len(ret.Details) < int(e.taskParams.PageSize) {
			break
		}
	}
	nums = k
	_ = e.writer.Flush()
	return

}

// 配送报表设置表头
func (e *DeliveryReportExport) SetSheetName() {
	nameList := []interface{}{
		"子订单", "父订单", "外部单号", "美团报价(元)", "达达报价（元）", "蜂鸟报价（元）", "最终承运方", "阿闻门店名称", "门店财务编码", "订单来源", "订单状态", "下单时间", "配送方式",
		"店铺类型", "三方平台店铺id", "配送异常", "配送距离(km)", "订单重量(kg)", "最终配送费",
	}
	_ = e.writer.SetRow("A1", nameList)
}

// 配送报表
func (e *DeliveryReportExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("配送报表(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu1(e.F, fileName)
}

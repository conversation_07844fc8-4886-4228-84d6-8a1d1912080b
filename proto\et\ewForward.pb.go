// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/ewForward.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type AddContactWayReq struct {
	Type                 int32    `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	Scene                int32    `protobuf:"varint,2,opt,name=scene,proto3" json:"scene"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	SkipVerify           bool     `protobuf:"varint,4,opt,name=skip_verify,json=skipVerify,proto3" json:"skip_verify"`
	State                string   `protobuf:"bytes,5,opt,name=state,proto3" json:"state"`
	User                 []string `protobuf:"bytes,6,rep,name=user,proto3" json:"user"`
	Party                []int32  `protobuf:"varint,7,rep,packed,name=party,proto3" json:"party"`
	IsTemp               bool     `protobuf:"varint,8,opt,name=is_temp,json=isTemp,proto3" json:"is_temp"`
	ExpiresIn            int32    `protobuf:"varint,9,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in"`
	ChatExpiresIn        int32    `protobuf:"varint,10,opt,name=chat_expires_in,json=chatExpiresIn,proto3" json:"chat_expires_in"`
	Unionid              string   `protobuf:"bytes,11,opt,name=unionid,proto3" json:"unionid"`
	IsExclusive          bool     `protobuf:"varint,12,opt,name=is_exclusive,json=isExclusive,proto3" json:"is_exclusive"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddContactWayReq) Reset()         { *m = AddContactWayReq{} }
func (m *AddContactWayReq) String() string { return proto.CompactTextString(m) }
func (*AddContactWayReq) ProtoMessage()    {}
func (*AddContactWayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8e8dbdb9ebd4af2f, []int{0}
}

func (m *AddContactWayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddContactWayReq.Unmarshal(m, b)
}
func (m *AddContactWayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddContactWayReq.Marshal(b, m, deterministic)
}
func (m *AddContactWayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddContactWayReq.Merge(m, src)
}
func (m *AddContactWayReq) XXX_Size() int {
	return xxx_messageInfo_AddContactWayReq.Size(m)
}
func (m *AddContactWayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddContactWayReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddContactWayReq proto.InternalMessageInfo

func (m *AddContactWayReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddContactWayReq) GetScene() int32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *AddContactWayReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AddContactWayReq) GetSkipVerify() bool {
	if m != nil {
		return m.SkipVerify
	}
	return false
}

func (m *AddContactWayReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *AddContactWayReq) GetUser() []string {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *AddContactWayReq) GetParty() []int32 {
	if m != nil {
		return m.Party
	}
	return nil
}

func (m *AddContactWayReq) GetIsTemp() bool {
	if m != nil {
		return m.IsTemp
	}
	return false
}

func (m *AddContactWayReq) GetExpiresIn() int32 {
	if m != nil {
		return m.ExpiresIn
	}
	return 0
}

func (m *AddContactWayReq) GetChatExpiresIn() int32 {
	if m != nil {
		return m.ChatExpiresIn
	}
	return 0
}

func (m *AddContactWayReq) GetUnionid() string {
	if m != nil {
		return m.Unionid
	}
	return ""
}

func (m *AddContactWayReq) GetIsExclusive() bool {
	if m != nil {
		return m.IsExclusive
	}
	return false
}

type AddContactWayResp struct {
	Errcode              int32    `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	Errmsg               string   `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	ConfigId             string   `protobuf:"bytes,3,opt,name=config_id,json=configId,proto3" json:"config_id"`
	QrCode               string   `protobuf:"bytes,4,opt,name=qr_code,json=qrCode,proto3" json:"qr_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddContactWayResp) Reset()         { *m = AddContactWayResp{} }
func (m *AddContactWayResp) String() string { return proto.CompactTextString(m) }
func (*AddContactWayResp) ProtoMessage()    {}
func (*AddContactWayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_8e8dbdb9ebd4af2f, []int{1}
}

func (m *AddContactWayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddContactWayResp.Unmarshal(m, b)
}
func (m *AddContactWayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddContactWayResp.Marshal(b, m, deterministic)
}
func (m *AddContactWayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddContactWayResp.Merge(m, src)
}
func (m *AddContactWayResp) XXX_Size() int {
	return xxx_messageInfo_AddContactWayResp.Size(m)
}
func (m *AddContactWayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddContactWayResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddContactWayResp proto.InternalMessageInfo

func (m *AddContactWayResp) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *AddContactWayResp) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *AddContactWayResp) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *AddContactWayResp) GetQrCode() string {
	if m != nil {
		return m.QrCode
	}
	return ""
}

func init() {
	proto.RegisterType((*AddContactWayReq)(nil), "et.AddContactWayReq")
	proto.RegisterType((*AddContactWayResp)(nil), "et.AddContactWayResp")
}

func init() { proto.RegisterFile("et/ewForward.proto", fileDescriptor_8e8dbdb9ebd4af2f) }

var fileDescriptor_8e8dbdb9ebd4af2f = []byte{
	// 372 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x92, 0xcd, 0xae, 0xd3, 0x30,
	0x10, 0x85, 0x95, 0xfe, 0xa4, 0xcd, 0xb4, 0x15, 0xc5, 0x2a, 0x60, 0x81, 0x10, 0xa1, 0x0b, 0x94,
	0x55, 0x91, 0x60, 0xcb, 0x06, 0x55, 0x45, 0xea, 0x0e, 0x05, 0x04, 0xcb, 0x28, 0xc4, 0xd3, 0x62,
	0x95, 0xc4, 0xee, 0xd8, 0xfd, 0x89, 0x78, 0x4b, 0x9e, 0x08, 0xd9, 0x49, 0xae, 0xee, 0xad, 0xee,
	0xce, 0xe7, 0xd3, 0xf1, 0x99, 0xd1, 0xb1, 0x81, 0xa1, 0x7d, 0x8f, 0x97, 0x2f, 0x8a, 0x2e, 0x39,
	0x89, 0x95, 0x26, 0x65, 0x15, 0xeb, 0xa1, 0x5d, 0xfe, 0xeb, 0xc1, 0xfc, 0xb3, 0x10, 0x6b, 0x55,
	0xd9, 0xbc, 0xb0, 0x3f, 0xf3, 0x3a, 0xc5, 0x23, 0x63, 0x30, 0xb0, 0xb5, 0x46, 0x1e, 0xc4, 0x41,
	0x32, 0x4c, 0xfd, 0x99, 0x2d, 0x60, 0x68, 0x0a, 0xac, 0x90, 0xf7, 0x3c, 0x6c, 0x04, 0x7b, 0x0e,
	0x21, 0x61, 0x99, 0xd3, 0x81, 0xf7, 0xe3, 0x20, 0x89, 0xd2, 0x56, 0xb1, 0x37, 0x30, 0x31, 0x07,
	0xa9, 0xb3, 0x33, 0x92, 0xdc, 0xd5, 0x7c, 0x10, 0x07, 0xc9, 0x38, 0x05, 0x87, 0x7e, 0x78, 0xe2,
	0xe3, 0x6c, 0x6e, 0x91, 0x0f, 0xfd, 0xbd, 0x46, 0xb8, 0xc1, 0x27, 0x83, 0xc4, 0xc3, 0xb8, 0x9f,
	0x44, 0xa9, 0x3f, 0x3b, 0xa7, 0xce, 0xc9, 0xd6, 0x7c, 0x14, 0xf7, 0xdd, 0x60, 0x2f, 0xd8, 0x0b,
	0x18, 0x49, 0x93, 0x59, 0x2c, 0x35, 0x1f, 0xfb, 0xf0, 0x50, 0x9a, 0xef, 0x58, 0x6a, 0xf6, 0x1a,
	0x00, 0xaf, 0x5a, 0x12, 0x9a, 0x4c, 0x56, 0x3c, 0xf2, 0xcb, 0x46, 0x2d, 0xd9, 0x56, 0xec, 0x1d,
	0x3c, 0x29, 0x7e, 0xe7, 0x36, 0xbb, 0xe7, 0x01, 0xef, 0x99, 0x39, 0xbc, 0xb9, 0xf3, 0x71, 0x18,
	0x9d, 0x2a, 0xa9, 0x2a, 0x29, 0xf8, 0xc4, 0x6f, 0xd8, 0x49, 0xf6, 0x16, 0xa6, 0xd2, 0x64, 0x78,
	0x2d, 0xfe, 0x9c, 0x8c, 0x3c, 0x23, 0x9f, 0xfa, 0xf1, 0x13, 0x69, 0x36, 0x1d, 0x5a, 0xfe, 0x85,
	0xa7, 0x37, 0x9d, 0x1a, 0xed, 0x12, 0x91, 0xa8, 0x50, 0xa2, 0xeb, 0xb5, 0x93, 0xae, 0x44, 0x24,
	0x2a, 0xcd, 0xde, 0x77, 0x1b, 0xa5, 0xad, 0x62, 0xaf, 0x20, 0x2a, 0x54, 0xb5, 0x93, 0xfb, 0x4c,
	0x8a, 0xb6, 0xdf, 0x71, 0x03, 0xb6, 0xc2, 0x15, 0x70, 0xa4, 0xcc, 0xc7, 0x0d, 0x9a, 0x5b, 0x47,
	0x5a, 0x2b, 0x81, 0x1f, 0xbe, 0xc2, 0x7c, 0xd3, 0x3d, 0xf4, 0x37, 0xa4, 0xb3, 0x2c, 0x90, 0x7d,
	0x82, 0xd9, 0x83, 0x85, 0xd8, 0x62, 0x85, 0x76, 0x75, 0xfb, 0xee, 0x2f, 0x9f, 0x3d, 0x42, 0x8d,
	0xfe, 0x15, 0xfa, 0xef, 0xf2, 0xf1, 0x7f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x86, 0x82, 0x0e, 0x63,
	0x44, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EwForwardServiceClient is the client API for EwForwardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EwForwardServiceClient interface {
	// 企微添加联系我方式
	AddContactWay(ctx context.Context, in *AddContactWayReq, opts ...grpc.CallOption) (*AddContactWayResp, error)
}

type ewForwardServiceClient struct {
	cc *grpc.ClientConn
}

func NewEwForwardServiceClient(cc *grpc.ClientConn) EwForwardServiceClient {
	return &ewForwardServiceClient{cc}
}

func (c *ewForwardServiceClient) AddContactWay(ctx context.Context, in *AddContactWayReq, opts ...grpc.CallOption) (*AddContactWayResp, error) {
	out := new(AddContactWayResp)
	err := c.cc.Invoke(ctx, "/et.EwForwardService/AddContactWay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EwForwardServiceServer is the server API for EwForwardService service.
type EwForwardServiceServer interface {
	// 企微添加联系我方式
	AddContactWay(context.Context, *AddContactWayReq) (*AddContactWayResp, error)
}

// UnimplementedEwForwardServiceServer can be embedded to have forward compatible implementations.
type UnimplementedEwForwardServiceServer struct {
}

func (*UnimplementedEwForwardServiceServer) AddContactWay(ctx context.Context, req *AddContactWayReq) (*AddContactWayResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddContactWay not implemented")
}

func RegisterEwForwardServiceServer(s *grpc.Server, srv EwForwardServiceServer) {
	s.RegisterService(&_EwForwardService_serviceDesc, srv)
}

func _EwForwardService_AddContactWay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddContactWayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EwForwardServiceServer).AddContactWay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.EwForwardService/AddContactWay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EwForwardServiceServer).AddContactWay(ctx, req.(*AddContactWayReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _EwForwardService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.EwForwardService",
	HandlerType: (*EwForwardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddContactWay",
			Handler:    _EwForwardService_AddContactWay_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/ewForward.proto",
}

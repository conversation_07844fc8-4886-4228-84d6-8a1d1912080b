// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/MINIProgramMSG.proto

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//小程序消息推送数据请求
type PushTemplateRequest struct {
	//小程序消息推送类型：1咨询提交成功通知（给用户看的） 2订单待支付通知  3医生接诊通知 4医生第一次回复消息通知 5医生回复消息通知 20新咨询单提醒(给医生看的) 21用户回复消息通知
	PushType int32 `protobuf:"varint,1,opt,name=push_type,json=pushType,proto3" json:"push_type"`
	//订单号
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateRequest) Reset()         { *m = PushTemplateRequest{} }
func (m *PushTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*PushTemplateRequest) ProtoMessage()    {}
func (*PushTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_732b96ca999cc6dc, []int{0}
}

func (m *PushTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateRequest.Unmarshal(m, b)
}
func (m *PushTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateRequest.Marshal(b, m, deterministic)
}
func (m *PushTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateRequest.Merge(m, src)
}
func (m *PushTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_PushTemplateRequest.Size(m)
}
func (m *PushTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateRequest proto.InternalMessageInfo

func (m *PushTemplateRequest) GetPushType() int32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *PushTemplateRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//小程序消息推送数据返回
type PushTemplateResponse struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateResponse) Reset()         { *m = PushTemplateResponse{} }
func (m *PushTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*PushTemplateResponse) ProtoMessage()    {}
func (*PushTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_732b96ca999cc6dc, []int{1}
}

func (m *PushTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateResponse.Unmarshal(m, b)
}
func (m *PushTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateResponse.Marshal(b, m, deterministic)
}
func (m *PushTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateResponse.Merge(m, src)
}
func (m *PushTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_PushTemplateResponse.Size(m)
}
func (m *PushTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateResponse proto.InternalMessageInfo

func (m *PushTemplateResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

//用户订阅消息
type UserSubscribeRequest struct {
	//订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 用户openId
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//订阅消息类型：1咨询提交成功通知（给用户看的） 2订单待支付通知  3医生接诊通知 4医生第一次回复消息通知 5医生回复消息通知 6待接诊取消并退款 7已结束并由后台操作退款 20新咨询单提醒(给医生看的) 21用户回复消息通知
	SubscribeType        string   `protobuf:"bytes,3,opt,name=subscribe_type,json=subscribeType,proto3" json:"subscribe_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSubscribeRequest) Reset()         { *m = UserSubscribeRequest{} }
func (m *UserSubscribeRequest) String() string { return proto.CompactTextString(m) }
func (*UserSubscribeRequest) ProtoMessage()    {}
func (*UserSubscribeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_732b96ca999cc6dc, []int{2}
}

func (m *UserSubscribeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSubscribeRequest.Unmarshal(m, b)
}
func (m *UserSubscribeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSubscribeRequest.Marshal(b, m, deterministic)
}
func (m *UserSubscribeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSubscribeRequest.Merge(m, src)
}
func (m *UserSubscribeRequest) XXX_Size() int {
	return xxx_messageInfo_UserSubscribeRequest.Size(m)
}
func (m *UserSubscribeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSubscribeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserSubscribeRequest proto.InternalMessageInfo

func (m *UserSubscribeRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UserSubscribeRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *UserSubscribeRequest) GetSubscribeType() string {
	if m != nil {
		return m.SubscribeType
	}
	return ""
}

//用户订阅消息
type UserSubscribeResponse struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSubscribeResponse) Reset()         { *m = UserSubscribeResponse{} }
func (m *UserSubscribeResponse) String() string { return proto.CompactTextString(m) }
func (*UserSubscribeResponse) ProtoMessage()    {}
func (*UserSubscribeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_732b96ca999cc6dc, []int{3}
}

func (m *UserSubscribeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSubscribeResponse.Unmarshal(m, b)
}
func (m *UserSubscribeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSubscribeResponse.Marshal(b, m, deterministic)
}
func (m *UserSubscribeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSubscribeResponse.Merge(m, src)
}
func (m *UserSubscribeResponse) XXX_Size() int {
	return xxx_messageInfo_UserSubscribeResponse.Size(m)
}
func (m *UserSubscribeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSubscribeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserSubscribeResponse proto.InternalMessageInfo

func (m *UserSubscribeResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func init() {
	proto.RegisterType((*PushTemplateRequest)(nil), "dgc.PushTemplateRequest")
	proto.RegisterType((*PushTemplateResponse)(nil), "dgc.PushTemplateResponse")
	proto.RegisterType((*UserSubscribeRequest)(nil), "dgc.UserSubscribeRequest")
	proto.RegisterType((*UserSubscribeResponse)(nil), "dgc.UserSubscribeResponse")
}

func init() { proto.RegisterFile("dgc/MINIProgramMSG.proto", fileDescriptor_732b96ca999cc6dc) }

var fileDescriptor_732b96ca999cc6dc = []byte{
	// 277 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x91, 0x51, 0x4f, 0x83, 0x30,
	0x14, 0x85, 0x45, 0xe2, 0x36, 0x6e, 0x9c, 0x31, 0x75, 0x8b, 0x0c, 0x5f, 0x16, 0x12, 0x13, 0x7c,
	0xc1, 0x44, 0x7f, 0x83, 0x51, 0x1e, 0x30, 0x0b, 0xcc, 0x67, 0x32, 0xe8, 0x4d, 0x47, 0x22, 0xb4,
	0xeb, 0x05, 0x93, 0xfd, 0x23, 0x7f, 0xa6, 0x01, 0xdc, 0x32, 0x0c, 0xbe, 0xf5, 0xde, 0xd3, 0x7c,
	0xe7, 0xf4, 0x14, 0x6c, 0x2e, 0xb2, 0xc7, 0x30, 0x78, 0x0f, 0x56, 0x5a, 0x0a, 0xbd, 0x29, 0xc2,
	0xf8, 0xd5, 0x57, 0x5a, 0x56, 0x92, 0x99, 0x5c, 0x64, 0x6e, 0x08, 0x37, 0xab, 0x9a, 0xb6, 0x6b,
	0x2c, 0xd4, 0xe7, 0xa6, 0xc2, 0x08, 0x77, 0x35, 0x52, 0xc5, 0xee, 0xc0, 0x52, 0x35, 0x6d, 0x93,
	0x6a, 0xaf, 0xd0, 0x36, 0x96, 0x86, 0x77, 0x11, 0x4d, 0x9a, 0xc5, 0x7a, 0xaf, 0x90, 0x2d, 0x60,
	0x22, 0x35, 0x47, 0x9d, 0x50, 0x69, 0x9f, 0x2f, 0x0d, 0xcf, 0x8a, 0xc6, 0xed, 0x1c, 0x97, 0xae,
	0x07, 0xb3, 0x3e, 0x8e, 0x94, 0x2c, 0x09, 0xd9, 0x35, 0x98, 0x05, 0x89, 0x96, 0x64, 0x45, 0xcd,
	0xd1, 0xdd, 0xc1, 0xec, 0x83, 0x50, 0xc7, 0x75, 0x4a, 0x99, 0xce, 0xd3, 0xa3, 0xf3, 0x29, 0xdc,
	0xe8, 0xc1, 0xd9, 0x2d, 0x8c, 0xa5, 0xc2, 0x32, 0xc9, 0xf9, 0xaf, 0xed, 0xa8, 0x19, 0x03, 0xce,
	0xee, 0xe1, 0x8a, 0x0e, 0x9c, 0x2e, 0xb2, 0xd9, 0xea, 0xd3, 0xe3, 0xb6, 0xc9, 0xed, 0x3e, 0xc0,
	0xfc, 0x8f, 0xe5, 0x7f, 0xe9, 0x9e, 0xbe, 0x0d, 0x98, 0xf7, 0x4b, 0x8b, 0x51, 0x7f, 0xe5, 0x19,
	0xb2, 0x17, 0xb8, 0x3c, 0x7d, 0x21, 0xb3, 0x7d, 0x2e, 0x32, 0x7f, 0xa0, 0x43, 0x67, 0x31, 0xa0,
	0x74, 0x86, 0xee, 0x19, 0x7b, 0x83, 0x69, 0x2f, 0x0b, 0xeb, 0x6e, 0x0f, 0x55, 0xe2, 0x38, 0x43,
	0xd2, 0x81, 0x94, 0x8e, 0xda, 0xdf, 0x7c, 0xfe, 0x09, 0x00, 0x00, 0xff, 0xff, 0x0b, 0x44, 0x11,
	0x63, 0xe9, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MINIProgramMSGServiceClient is the client API for MINIProgramMSGService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MINIProgramMSGServiceClient interface {
	// @Desc    	发送小程序消息通知
	// <AUTHOR>
	// @Date		2021-10-13
	PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error)
	// @Desc    	用户订阅消息
	// <AUTHOR>
	// @Date		2021-10-16
	UserSubscribe(ctx context.Context, in *UserSubscribeRequest, opts ...grpc.CallOption) (*UserSubscribeResponse, error)
}

type mINIProgramMSGServiceClient struct {
	cc *grpc.ClientConn
}

func NewMINIProgramMSGServiceClient(cc *grpc.ClientConn) MINIProgramMSGServiceClient {
	return &mINIProgramMSGServiceClient{cc}
}

func (c *mINIProgramMSGServiceClient) PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error) {
	out := new(PushTemplateResponse)
	err := c.cc.Invoke(ctx, "/dgc.MINIProgramMSGService/PushTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mINIProgramMSGServiceClient) UserSubscribe(ctx context.Context, in *UserSubscribeRequest, opts ...grpc.CallOption) (*UserSubscribeResponse, error) {
	out := new(UserSubscribeResponse)
	err := c.cc.Invoke(ctx, "/dgc.MINIProgramMSGService/UserSubscribe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MINIProgramMSGServiceServer is the server API for MINIProgramMSGService service.
type MINIProgramMSGServiceServer interface {
	// @Desc    	发送小程序消息通知
	// <AUTHOR>
	// @Date		2021-10-13
	PushTemplate(context.Context, *PushTemplateRequest) (*PushTemplateResponse, error)
	// @Desc    	用户订阅消息
	// <AUTHOR>
	// @Date		2021-10-16
	UserSubscribe(context.Context, *UserSubscribeRequest) (*UserSubscribeResponse, error)
}

// UnimplementedMINIProgramMSGServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMINIProgramMSGServiceServer struct {
}

func (*UnimplementedMINIProgramMSGServiceServer) PushTemplate(ctx context.Context, req *PushTemplateRequest) (*PushTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushTemplate not implemented")
}
func (*UnimplementedMINIProgramMSGServiceServer) UserSubscribe(ctx context.Context, req *UserSubscribeRequest) (*UserSubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserSubscribe not implemented")
}

func RegisterMINIProgramMSGServiceServer(s *grpc.Server, srv MINIProgramMSGServiceServer) {
	s.RegisterService(&_MINIProgramMSGService_serviceDesc, srv)
}

func _MINIProgramMSGService_PushTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MINIProgramMSGServiceServer).PushTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.MINIProgramMSGService/PushTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MINIProgramMSGServiceServer).PushTemplate(ctx, req.(*PushTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MINIProgramMSGService_UserSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MINIProgramMSGServiceServer).UserSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.MINIProgramMSGService/UserSubscribe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MINIProgramMSGServiceServer).UserSubscribe(ctx, req.(*UserSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MINIProgramMSGService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.MINIProgramMSGService",
	HandlerType: (*MINIProgramMSGServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushTemplate",
			Handler:    _MINIProgramMSGService_PushTemplate_Handler,
		},
		{
			MethodName: "UserSubscribe",
			Handler:    _MINIProgramMSGService_UserSubscribe_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/MINIProgramMSG.proto",
}

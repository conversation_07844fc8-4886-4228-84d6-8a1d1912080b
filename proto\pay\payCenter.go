package pay

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	RPC  PayInfoClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

type PlatformChannel struct {
	ChannelId  int
	UserAgent  int
	AppChannel int
}

type GrpcContext struct {
	Channel PlatformChannel
}

func GetPayCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.pay-center")
	}

	//url = "10.1.1.248:7036"
	if url == "" {
		url = "127.0.0.1:7036"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("pay-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewPayInfoClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}

package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"time"
)

type WarehouseRelationShop struct {
	Id            int32     `xorm:"not null pk autoincr comment('自增id') INT(11)"`
	ShopName      string    `xorm:"default 'NULL' comment('门店名称') VARCHAR(255)"`
	ShopId        string    `xorm:"not null comment('门店id') VARCHAR(255)"`
	WarehouseName string    `xorm:"default 'NULL' comment('仓库名称') VARCHAR(255)"`
	WarehouseId   int32     `xorm:"not null comment('仓库id') index INT(11)"`
	CreateTime    time.Time `xorm:"not null comment('创建时间') DATETIME"`
	ChannelId     int32     `xorm:"not null comment('渠道id：2 美团') INT(11)"`
}

// QueryWarehouseId 查询仓库Id
func QueryWarehouseId(s *xorm.Session, shopId string, channelId int32) (id int32, err error) {
	has, err := s.Table("dc_dispatch.warehouse_relation_shop").
		Where("shop_id = ? and channel_id =?", shopId, channelId).
		Select("warehouse_id").Get(&id)
	if err != nil {
		return 0, errors.New("查询仓库出错 " + err.Error())
	} else if !has {
		return 0, errors.New("仓库id未找到")
	}
	return
}

package services

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"order-center/proto/oc"
	"testing"
)

func TestCardService_New(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CardNewReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.CardNewRes
		wantErr bool
	}{
		{
			args: args{in: &oc.CardNewReq{
				CardId:   1,
				ScrmId:   "02e2c06214aa4b7497199922af0e187c",
				UserName: "付**",
				DisId:    634,
				DisType:  2,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{}
			gotOut, err := c.New(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_PayNotify(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardPayNotifyReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardBaseResponse
		wantErr bool
	}{
		{
			args: args{in: &oc.CardPayNotifyReq{
				OrderSn: "****************",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.PayNotify(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_Refund(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardRefundReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardBaseResponse
		wantErr bool
	}{
		{
			args: args{in: &oc.CardRefundReq{
				RefundSn: "50000926591",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.Refund(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_ServicePackActivity(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardServicePackActivityReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardBaseResponse
		wantErr bool
	}{
		{
			args: args{in: &oc.CardServicePackActivityReq{
				MemberId:    "6c833946ff154963b71f2635b02f37d9",
				PetAge:      "6个月",
				PetAvatar:   "",
				PetBirthday: "2023-01-30 00:00:00",
				PetId:       "da775f001c484adab5683fdea3d55263",
				PetKindOf:   "猫",
				PetName:     "大秘密",
				PetSex:      1,
				PetVariety:  "巴厘猫",
				SignId:      245,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.ServicePackActivity(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_QuerySignId(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.QuerySignIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.QuerySignIdRes
		wantErr bool
	}{
		{
			args: args{in: &oc.QuerySignIdReq{
				OrderSn: "****************",
				ScrmId:  "02e2c06214aa4b7497199922af0e187c",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.QuerySignId(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_EquityReceive(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardEquityReceiveReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardBaseResponse
		wantErr bool
	}{
		{
			args: args{in: &oc.CardEquityReceiveReq{
				SignId:   0,
				OrderSn:  "****************",
				Type:     2,
				EquityId: 59,
				Id:       "296",
				ScrmId:   "58fc60189fe645e9943f3e392e7eec8d",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.EquityReceive(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_NewByCode(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardNewByCodeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardBaseResponse
		wantErr bool
	}{
		{
			args: args{
				in: &oc.CardNewByCodeReq{
					Code:      "FY1502007D2E8D0693",
					ScrmId:    "d41e927b67a64184957641d48c4fbefc",
					UserName:  "哈哈哈",
					UserAgent: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.NewByCode(tt.args.ctx, tt.args.in)

			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_CheckCardId(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CheckCardIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CheckCardIdRes
		wantErr bool
	}{
		{
			args: args{in: &oc.CheckCardIdReq{
				CardId: 0,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.CheckCardId(tt.args.ctx, tt.args.in)
			if tt.wantErr == (err != nil) {
				t.Error(err, gotOut)
			} else {
				t.Log(gotOut, err)
			}
		})
	}
}

func TestCardService_NewByStore(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *oc.CardNewByStoreReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.CardNewByStoreRes
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				in: &oc.CardNewByStoreReq{
					StoreId: 1056,
					ScrmId:  "02e2c06214aa4b7497199922af0e187c",
					CardId:  1,
				},
				ctx: context.Background(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := c.NewByStore(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("NewByStore(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "NewByStore(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestCardService_RetrunZiLongCouponCode(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		orderSn  string
		RefundSn string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{name: "退子龙卡"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CardService{
				BaseService: tt.fields.BaseService,
			}
			tt.wantErr(t, c.RetrunZiLongCouponCode("****************", "50000134836"), fmt.Sprintf("RetrunZiLongCouponCode(%v, %v)", tt.args.orderSn, tt.args.RefundSn))
		})
	}
}

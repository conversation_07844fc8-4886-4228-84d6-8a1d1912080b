package models

import "time"

type OrderException struct {
	DeliveryId       string    `xorm:"not null pk comment('配送活动标识(主键)') VARCHAR(36)"`
	MtPeisongId      string    `xorm:"not null comment('美团配送内部订单id') VARCHAR(100)"`
	OrderSn          string    `xorm:"not null default '''' comment('订单id') index(idx_order_sn_exception_time) VARCHAR(36)"`
	ExceptionId      string    `xorm:"default '''' comment('异常ID，用来唯一标识一个订单异常信息') VARCHAR(36)"`
	ExceptionCode    int32     `xorm:"default NULL comment('订单异常代码') INT(11)"`
	ExceptionDescr   string    `xorm:"not null default '''' comment('订单异常详细信息') VARCHAR(500)"`
	ExceptionTime    string    `xorm:"not null default 'current_timestamp()' comment('配送员上报订单异常的时间') index(idx_order_sn_exception_time) DATETIME"`
	CourierName      string    `xorm:"default '''' comment('上报订单异常的配送员姓名') VARCHAR(50)"`
	CourierPhone     string    `xorm:"default '''' comment('报订单异常的配送员电话') VARCHAR(20)"`
	IsShow           int32     `xorm:"not null comment('是否显示') INT(11)"`
	Source           int32     `xorm:"not null default 1 comment('谁推送的，1商家，2配送平台，3骑手上报') INT(11)"`
	OrderStatus      int32     `xorm:"not null default 0 comment('1:未取消 2:未配送，3未送达,4.已送达') INT(11)"`
	DistributionMode string    `xorm:"default ''商家自配'' comment('配送方式') VARCHAR(50)"`
	CreateTime       time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime       time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

type OrderExceptionCombine struct {
	OrderException `xorm:"extends"`
	OldOrderSn     string `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
}

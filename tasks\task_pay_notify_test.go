package tasks

import (
	kit "github.com/tricobbler/rp-kit"
	"order-center/services"
	"testing"
)

func TestPayNotify_pay(t *testing.T) {
	type fields struct {
		OmsService services.OmsService
	}
	type args struct {
		Payed *Payed
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{&Payed{
				OrderSn:     "3000000019336901",
				PaymentCode: "predeposit",
				PaymentTime: kit.GetTimeNow(),
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PayNotify{
				OmsService: tt.fields.OmsService,
			}
			got, err := s.pay(tt.args.Payed)
			if (err != nil) != tt.wantErr {
				t.Errorf("pay() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("pay() got = %v, want %v", got, tt.want)
			}
		})
	}
}

package services

import (
	"context"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	oc "order-center/proto/oc"
	"testing"
)

func TestUpetDjService_QueryUpetDjOrderDetail(t *testing.T) {
	type args struct {
		ctx context.Context
		req *oc.UpetDjOrderDetailRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "QueryUpetDjOrderDetail",
			args: args{
				ctx: context.Background(),
				req: &oc.UpetDjOrderDetailRequest{
					OrderSn:  "4100000014532543",
					OrderId:  "",
					MemberId: "bbdd4d8021ed430ead39153c7f6b28d5",
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &UpetDjService{}

			got, err := service.QueryUpetDjOrderDetail(tt.args.ctx, tt.args.req)
			if err != nil {
				t.Errorf("UpetDjService.QueryUpetDjOrderDetail() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestUpetDjService_QueryPromotonOrderReport(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.PromotonOrderReportRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.PromotionOrderReportReponse
		wantErr bool
	}{
		{
			name: "QueryPromotonOrderReport",
			args: args{
				ctx: context.Background(),
				req: &oc.PromotonOrderReportRequest{
					PromotionId: 306145,
					StartDate:   "2021-03-25",
					EndDate:     "2021-03-26",
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &UpetDjService{
				CommonService: tt.fields.CommonService,
			}
			got, err := service.QueryPromotonOrderReport(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryPromotonOrderReport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}

func TestUpetDjService_QueryUpetDjOrderList(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		req *oc.UpetDjOrderQueryRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *oc.UpetDjOrderQueryResponse
	}{
		{
			name: "小程序订单列表",
			args: args{
				ctx: context.Background(),
				req: &oc.UpetDjOrderQueryRequest{
					PageIndex: 1,
					PageSize:  10,
					ShopId:    []string{},
					MemberId:  "895e3b3a541241a6b9a5c04a4e0f6ec2",
					State:     oc.UpetDjOrderState_unVerify,
				},
			},
		},
		{
			name: "小程序订单列表",
			args: args{
				ctx: context.Background(),
				req: &oc.UpetDjOrderQueryRequest{
					PageIndex: 1,
					PageSize:  10,
					ShopId:    []string{},
					MemberId:  "895e3b3a541241a6b9a5c04a4e0f6ec2",
					State:     oc.UpetDjOrderState_all,
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &UpetDjService{
				CommonService: tt.fields.CommonService,
			}
			got, err := service.QueryUpetDjOrderList(tt.args.ctx, tt.args.req)
			if err != nil {
				t.Fatalf("QueryUpetDjOrderList() error = %v", err)
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestUpetDjService_ConfirmUpetDj(t *testing.T) {
	type args struct {
		ctx context.Context
		req *oc.UpetDjConfirmRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "确认收货",
			args: args{
				ctx: context.Background(),
				req: &oc.UpetDjConfirmRequest{
					OrderId: "33802",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &UpetDjService{}
			got, err := service.ConfirmUpetDj(tt.args.ctx, tt.args.req)
			if err != nil {
				t.Errorf("ConfirmUpetDj() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestUpetDjService_getUpetDjOrderDtoOrderStatus(t *testing.T) {
	type args struct {
		orderInfo *models.Order
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "查询订单状态",
			args: args{
				orderInfo: GetOrderByOrderSn("4000000000226627", "order_main.channel_id,order_main.order_status_child,order_main.is_push_tencent"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &UpetDjService{}
			got := service.getUpetDjOrderDtoOrderStatus(tt.args.orderInfo)
			t.Log(kit.JsonEncode(got))
		})
	}
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/subscribe_message.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PushTemplateRequest struct {
	//用户ID
	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId"`
	//订单编号
	OrderSn string `protobuf:"bytes,2,opt,name=orderSn,proto3" json:"orderSn"`
	//模板ID
	TemplateId string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId"`
	//类型(1=>发送退款成功通知, 2=>发送退款失败通知, 3=>发送退款状态通知, 4=>推送尾款支付提醒通知)
	PushType int32 `protobuf:"varint,4,opt,name=pushType,proto3" json:"pushType"`
	//备注
	Remarks string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks"`
	//发送退款成功通知
	RefundSuccess *RefundSuccess `protobuf:"bytes,6,opt,name=refundSuccess,proto3" json:"refundSuccess"`
	//发送退款失败通知
	RefundFail *RefundFail `protobuf:"bytes,7,opt,name=refundFail,proto3" json:"refundFail"`
	//发送退款状态通知
	RefundStatus *RefundStatus `protobuf:"bytes,8,opt,name=refundStatus,proto3" json:"refundStatus"`
	//预售尾款支付通知
	PreSalePay *PreSalePay `protobuf:"bytes,9,opt,name=preSalePay,proto3" json:"preSalePay"`
	//主体:1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,10,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateRequest) Reset()         { *m = PushTemplateRequest{} }
func (m *PushTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*PushTemplateRequest) ProtoMessage()    {}
func (*PushTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{0}
}

func (m *PushTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateRequest.Unmarshal(m, b)
}
func (m *PushTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateRequest.Marshal(b, m, deterministic)
}
func (m *PushTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateRequest.Merge(m, src)
}
func (m *PushTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_PushTemplateRequest.Size(m)
}
func (m *PushTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateRequest proto.InternalMessageInfo

func (m *PushTemplateRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *PushTemplateRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *PushTemplateRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *PushTemplateRequest) GetPushType() int32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *PushTemplateRequest) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *PushTemplateRequest) GetRefundSuccess() *RefundSuccess {
	if m != nil {
		return m.RefundSuccess
	}
	return nil
}

func (m *PushTemplateRequest) GetRefundFail() *RefundFail {
	if m != nil {
		return m.RefundFail
	}
	return nil
}

func (m *PushTemplateRequest) GetRefundStatus() *RefundStatus {
	if m != nil {
		return m.RefundStatus
	}
	return nil
}

func (m *PushTemplateRequest) GetPreSalePay() *PreSalePay {
	if m != nil {
		return m.PreSalePay
	}
	return nil
}

func (m *PushTemplateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//通用返回
type PushTemplateResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTemplateResponse) Reset()         { *m = PushTemplateResponse{} }
func (m *PushTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*PushTemplateResponse) ProtoMessage()    {}
func (*PushTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{1}
}

func (m *PushTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTemplateResponse.Unmarshal(m, b)
}
func (m *PushTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTemplateResponse.Marshal(b, m, deterministic)
}
func (m *PushTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTemplateResponse.Merge(m, src)
}
func (m *PushTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_PushTemplateResponse.Size(m)
}
func (m *PushTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PushTemplateResponse proto.InternalMessageInfo

func (m *PushTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PushTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PushTemplateResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//发送退款成功通知
type RefundSuccess struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//退款类型
	RefundType string `protobuf:"bytes,2,opt,name=refundType,proto3" json:"refundType"`
	//退款金额
	RefundAmount string `protobuf:"bytes,3,opt,name=refundAmount,proto3" json:"refundAmount"`
	//退款时间
	RefundTime           string   `protobuf:"bytes,4,opt,name=refundTime,proto3" json:"refundTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundSuccess) Reset()         { *m = RefundSuccess{} }
func (m *RefundSuccess) String() string { return proto.CompactTextString(m) }
func (*RefundSuccess) ProtoMessage()    {}
func (*RefundSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{2}
}

func (m *RefundSuccess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundSuccess.Unmarshal(m, b)
}
func (m *RefundSuccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundSuccess.Marshal(b, m, deterministic)
}
func (m *RefundSuccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundSuccess.Merge(m, src)
}
func (m *RefundSuccess) XXX_Size() int {
	return xxx_messageInfo_RefundSuccess.Size(m)
}
func (m *RefundSuccess) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundSuccess.DiscardUnknown(m)
}

var xxx_messageInfo_RefundSuccess proto.InternalMessageInfo

func (m *RefundSuccess) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundSuccess) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

func (m *RefundSuccess) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *RefundSuccess) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

//发送退款失败通知
type RefundFail struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//退款类型
	RefundType string `protobuf:"bytes,2,opt,name=refundType,proto3" json:"refundType"`
	//退款订单
	RefundSn string `protobuf:"bytes,3,opt,name=refundSn,proto3" json:"refundSn"`
	//状态
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status"`
	// 退款金额
	RefundAmount         string   `protobuf:"bytes,5,opt,name=refundAmount,proto3" json:"refundAmount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundFail) Reset()         { *m = RefundFail{} }
func (m *RefundFail) String() string { return proto.CompactTextString(m) }
func (*RefundFail) ProtoMessage()    {}
func (*RefundFail) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{3}
}

func (m *RefundFail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundFail.Unmarshal(m, b)
}
func (m *RefundFail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundFail.Marshal(b, m, deterministic)
}
func (m *RefundFail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundFail.Merge(m, src)
}
func (m *RefundFail) XXX_Size() int {
	return xxx_messageInfo_RefundFail.Size(m)
}
func (m *RefundFail) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundFail.DiscardUnknown(m)
}

var xxx_messageInfo_RefundFail proto.InternalMessageInfo

func (m *RefundFail) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundFail) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

func (m *RefundFail) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

func (m *RefundFail) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *RefundFail) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

//发送退款状态通知
type RefundStatus struct {
	//退款ID
	RefundId string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	//状态
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	//退款类型
	RefundType           string   `protobuf:"bytes,3,opt,name=refundType,proto3" json:"refundType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundStatus) Reset()         { *m = RefundStatus{} }
func (m *RefundStatus) String() string { return proto.CompactTextString(m) }
func (*RefundStatus) ProtoMessage()    {}
func (*RefundStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{4}
}

func (m *RefundStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundStatus.Unmarshal(m, b)
}
func (m *RefundStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundStatus.Marshal(b, m, deterministic)
}
func (m *RefundStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundStatus.Merge(m, src)
}
func (m *RefundStatus) XXX_Size() int {
	return xxx_messageInfo_RefundStatus.Size(m)
}
func (m *RefundStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundStatus.DiscardUnknown(m)
}

var xxx_messageInfo_RefundStatus proto.InternalMessageInfo

func (m *RefundStatus) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundStatus) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *RefundStatus) GetRefundType() string {
	if m != nil {
		return m.RefundType
	}
	return ""
}

type PreSalePay struct {
	//尾款支付开始时间
	StartTime string `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime"`
	//尾款支付结束时间
	EndTime string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`
	//温馨提醒
	Remarks string `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks"`
	//是否虚拟订单 0 否 1是
	IsVirtual            int32    `protobuf:"varint,4,opt,name=isVirtual,proto3" json:"isVirtual"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreSalePay) Reset()         { *m = PreSalePay{} }
func (m *PreSalePay) String() string { return proto.CompactTextString(m) }
func (*PreSalePay) ProtoMessage()    {}
func (*PreSalePay) Descriptor() ([]byte, []int) {
	return fileDescriptor_8dd35fdf2f57ee62, []int{5}
}

func (m *PreSalePay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreSalePay.Unmarshal(m, b)
}
func (m *PreSalePay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreSalePay.Marshal(b, m, deterministic)
}
func (m *PreSalePay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreSalePay.Merge(m, src)
}
func (m *PreSalePay) XXX_Size() int {
	return xxx_messageInfo_PreSalePay.Size(m)
}
func (m *PreSalePay) XXX_DiscardUnknown() {
	xxx_messageInfo_PreSalePay.DiscardUnknown(m)
}

var xxx_messageInfo_PreSalePay proto.InternalMessageInfo

func (m *PreSalePay) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *PreSalePay) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PreSalePay) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *PreSalePay) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func init() {
	proto.RegisterType((*PushTemplateRequest)(nil), "oc.PushTemplateRequest")
	proto.RegisterType((*PushTemplateResponse)(nil), "oc.PushTemplateResponse")
	proto.RegisterType((*RefundSuccess)(nil), "oc.RefundSuccess")
	proto.RegisterType((*RefundFail)(nil), "oc.RefundFail")
	proto.RegisterType((*RefundStatus)(nil), "oc.RefundStatus")
	proto.RegisterType((*PreSalePay)(nil), "oc.PreSalePay")
}

func init() { proto.RegisterFile("oc/subscribe_message.proto", fileDescriptor_8dd35fdf2f57ee62) }

var fileDescriptor_8dd35fdf2f57ee62 = []byte{
	// 497 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x54, 0xcf, 0x8b, 0x13, 0x31,
	0x14, 0x76, 0xda, 0x9d, 0xee, 0xf6, 0xd9, 0x15, 0x8d, 0xab, 0x1b, 0xca, 0x22, 0x65, 0x4e, 0x3d,
	0x55, 0x58, 0x05, 0xcf, 0x22, 0x08, 0x3d, 0x08, 0x25, 0x23, 0x1e, 0x3c, 0xb8, 0xa4, 0x33, 0xcf,
	0x3a, 0xd8, 0x99, 0x8c, 0x49, 0x46, 0x28, 0x78, 0xf3, 0x0f, 0xf0, 0x5f, 0xf0, 0x4f, 0x95, 0xfc,
	0xe8, 0x4c, 0x62, 0xc1, 0x8b, 0xb7, 0x7c, 0x5f, 0xf2, 0xde, 0xf7, 0xf2, 0xbe, 0x97, 0xc0, 0x5c,
	0x14, 0xcf, 0x55, 0xb7, 0x55, 0x85, 0xac, 0xb6, 0x78, 0x57, 0xa3, 0x52, 0x7c, 0x87, 0xab, 0x56,
	0x0a, 0x2d, 0xc8, 0x48, 0x14, 0xd9, 0xcf, 0x31, 0x3c, 0xde, 0x74, 0xea, 0xcb, 0x7b, 0xac, 0xdb,
	0x3d, 0xd7, 0xc8, 0xf0, 0x5b, 0x87, 0x4a, 0x93, 0xa7, 0x30, 0x11, 0x2d, 0x36, 0xeb, 0x92, 0x26,
	0x8b, 0x64, 0x39, 0x65, 0x1e, 0x11, 0x0a, 0xe7, 0x42, 0x96, 0x28, 0xf3, 0x86, 0x8e, 0xec, 0xc6,
	0x11, 0x92, 0x67, 0x00, 0xda, 0x27, 0x59, 0x97, 0x74, 0x6c, 0x37, 0x03, 0x86, 0xcc, 0xe1, 0xa2,
	0x35, 0x42, 0x87, 0x16, 0xe9, 0xd9, 0x22, 0x59, 0xa6, 0xac, 0xc7, 0x26, 0xab, 0xc4, 0x9a, 0xcb,
	0xaf, 0x8a, 0xa6, 0x2e, 0xab, 0x87, 0xe4, 0x15, 0x5c, 0x4a, 0xfc, 0xdc, 0x35, 0x65, 0xde, 0x15,
	0x05, 0x2a, 0x45, 0x27, 0x8b, 0x64, 0x79, 0xff, 0xf6, 0xd1, 0x4a, 0x14, 0x2b, 0x16, 0x6e, 0xb0,
	0xf8, 0x1c, 0x59, 0x01, 0x38, 0xe2, 0x2d, 0xaf, 0xf6, 0xf4, 0xdc, 0x46, 0x3d, 0x18, 0xa2, 0x0c,
	0xcb, 0x82, 0x13, 0xe4, 0x25, 0xcc, 0x7c, 0x02, 0xcd, 0x75, 0xa7, 0xe8, 0x85, 0x8d, 0x78, 0x18,
	0xe8, 0x58, 0x9e, 0x45, 0xa7, 0x8c, 0x4a, 0x2b, 0x31, 0xe7, 0x7b, 0xdc, 0xf0, 0x03, 0x9d, 0x0e,
	0x2a, 0x9b, 0x9e, 0x65, 0xc1, 0x09, 0xf2, 0x04, 0x26, 0x42, 0xee, 0xee, 0xaa, 0x92, 0x82, 0x6d,
	0x41, 0x2a, 0xe4, 0x6e, 0x5d, 0x66, 0x1f, 0xe1, 0x2a, 0x36, 0x41, 0xb5, 0xa2, 0x51, 0x48, 0x08,
	0x9c, 0x15, 0xa2, 0x44, 0xeb, 0x41, 0xca, 0xec, 0xda, 0xf4, 0xca, 0xdb, 0x78, 0x74, 0xc0, 0x43,
	0x72, 0x05, 0x29, 0x4a, 0x29, 0xa4, 0x6f, 0xbe, 0x03, 0xd9, 0xaf, 0x04, 0x2e, 0xa3, 0x4e, 0x19,
	0x27, 0xdc, 0x25, 0x7a, 0x77, 0x7b, 0x6c, 0x5c, 0x74, 0x6b, 0xeb, 0x93, 0x13, 0x08, 0x18, 0x92,
	0x1d, 0xdb, 0xf4, 0xba, 0x16, 0x5d, 0xa3, 0xbd, 0x54, 0xc4, 0x05, 0x39, 0xaa, 0xda, 0x79, 0x3d,
	0xe4, 0xa8, 0x6a, 0xcc, 0x7e, 0x27, 0x00, 0x83, 0x0b, 0xff, 0x55, 0x4e, 0x1f, 0x9b, 0x37, 0xbe,
	0x94, 0x1e, 0x9b, 0x11, 0x56, 0xce, 0x4b, 0x57, 0x82, 0x47, 0x27, 0x57, 0x48, 0x4f, 0xaf, 0x90,
	0x6d, 0x61, 0x16, 0xba, 0xfe, 0xcf, 0x1a, 0x07, 0x9d, 0x51, 0xa4, 0x13, 0xd7, 0x3e, 0xfe, 0xbb,
	0xf6, 0xec, 0x07, 0xc0, 0x30, 0x25, 0xe4, 0x06, 0xa6, 0x4a, 0x73, 0xa9, 0x6d, 0xcf, 0x9c, 0xc4,
	0x40, 0x18, 0xd3, 0xd1, 0xf7, 0xd3, 0x9b, 0xee, 0x61, 0xf8, 0x74, 0xc6, 0xf1, 0xd3, 0xb9, 0x81,
	0x69, 0xa5, 0x3e, 0x54, 0x52, 0x77, 0x7c, 0xef, 0x5f, 0xdc, 0x40, 0xdc, 0x7e, 0x82, 0xeb, 0xfc,
	0xf8, 0x2f, 0xbc, 0x73, 0x03, 0x94, 0xa3, 0xfc, 0x5e, 0x15, 0x48, 0xde, 0xc0, 0x2c, 0x9c, 0x46,
	0x72, 0x6d, 0x07, 0xfa, 0xf4, 0x93, 0x98, 0xd3, 0xd3, 0x0d, 0x37, 0xb8, 0xd9, 0xbd, 0xed, 0xc4,
	0xfe, 0x31, 0x2f, 0xfe, 0x04, 0x00, 0x00, 0xff, 0xff, 0x63, 0x0d, 0xa6, 0xca, 0x81, 0x04, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SubscribeMessageServiceClient is the client API for SubscribeMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SubscribeMessageServiceClient interface {
	// @Desc    	发送消息
	PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error)
}

type subscribeMessageServiceClient struct {
	cc *grpc.ClientConn
}

func NewSubscribeMessageServiceClient(cc *grpc.ClientConn) SubscribeMessageServiceClient {
	return &subscribeMessageServiceClient{cc}
}

func (c *subscribeMessageServiceClient) PushTemplate(ctx context.Context, in *PushTemplateRequest, opts ...grpc.CallOption) (*PushTemplateResponse, error) {
	out := new(PushTemplateResponse)
	err := c.cc.Invoke(ctx, "/oc.SubscribeMessageService/PushTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscribeMessageServiceServer is the server API for SubscribeMessageService service.
type SubscribeMessageServiceServer interface {
	// @Desc    	发送消息
	PushTemplate(context.Context, *PushTemplateRequest) (*PushTemplateResponse, error)
}

// UnimplementedSubscribeMessageServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSubscribeMessageServiceServer struct {
}

func (*UnimplementedSubscribeMessageServiceServer) PushTemplate(ctx context.Context, req *PushTemplateRequest) (*PushTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushTemplate not implemented")
}

func RegisterSubscribeMessageServiceServer(s *grpc.Server, srv SubscribeMessageServiceServer) {
	s.RegisterService(&_SubscribeMessageService_serviceDesc, srv)
}

func _SubscribeMessageService_PushTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeMessageServiceServer).PushTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.SubscribeMessageService/PushTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeMessageServiceServer).PushTemplate(ctx, req.(*PushTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SubscribeMessageService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.SubscribeMessageService",
	HandlerType: (*SubscribeMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushTemplate",
			Handler:    _SubscribeMessageService_PushTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/subscribe_message.proto",
}

package models

import (
	"time"
)

type VipCardVirtual struct {
	CardId       int64     `xorm:"not null pk autoincr comment('卡号') BIGINT(20)"`
	CardPass     string    `xorm:"not null comment('卡密') unique VARCHAR(50)"`
	BatchId      string    `xorm:"not null comment('批次号') VARCHAR(50)"`
	OrgId        int       `xorm:"not null comment('组织ID') INT(11)"`
	OrgName      string    `xorm:"default 'NULL' comment('组织名称') VARCHAR(50)"`
	TemplateId   int       `xorm:"default NULL comment('卡模板ID') INT(11)"`
	Status       int       `xorm:"not null default 0 comment('卡状态,0已卖出，1已激活') INT(11)"`
	UserId       string    `xorm:"default '''' comment('兑换用户id') VARCHAR(32)"`
	UserMobile   string    `xorm:"default '''' comment('兑换用户手机号') VARCHAR(20)"`
	EnUserMobile string    `xorm:"default '''' comment('加密手机号') VARCHAR(50)"`
	UseTime      time.Time `xorm:"default 'NULL' comment('兑换时间') DATETIME"`
	ExpireTime   time.Time `xorm:"default 'NULL' comment('到期时间') DATETIME"`
	CreateTime   time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime   time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	SellType     int       `xorm:"default NULL comment('1内销 2外采') INT(11)"`
}

func (p VipCardVirtual) TableName() string {
	return "datacenter.vip_card_virtual"
}

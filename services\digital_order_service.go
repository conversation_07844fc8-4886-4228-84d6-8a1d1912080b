package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/ctc"
	"order-center/proto/oc"
	"order-center/utils"
	"time"
)

type DigitalOrderService struct {
}

//CreateDigitalOrder 创建数字藏品订单
func (ds *DigitalOrderService) CreateDigitalOrder(ctx context.Context, req *oc.CreateDigitalOrderRequest) (res *oc.DigitalOrderResponse, err error) {
	res = new(oc.DigitalOrderResponse)
	db := GetDBConn()
	orderSn := ds.CreateSn()
	digitalOrder := models.DigitalOrder{
		OrderSn:    orderSn,
		Status:     int(req.Status),
		BuyUserId:  req.BuyUserId,
		SellUserId: req.SellUserId,
		NftId:      req.NftId,
		Price:      float64(req.Price),
		SellType:   int(req.SellType),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	_, err = db.Table("digital_order").Insert(&digitalOrder)
	if err != nil {
		res.Code = 400
		res.Message = err.Error()
		glog.Errorf("创建数字藏品订单失败,error(%s)", err.Error())
		return
	}
	res.Code = 200
	res.Message = "success"
	res.OrderSn = orderSn
	return
}

//UpdateDigitalOrder 更新数字藏品订单
func (ds *DigitalOrderService) UpdateDigitalOrder(ctx context.Context, req *oc.UpdateDigitalOrderRequest) (res *oc.DigitalOrderResponse, err error) {
	res = new(oc.DigitalOrderResponse)
	orderSn := req.OrderSn
	digitalOrderMap := make(map[string]interface{})
	if req.PaySn != "" {
		digitalOrderMap["order_sn"] = req.OrderSn
		digitalOrderMap["status"] = req.Status
		digitalOrderMap["pay_time"] = time.Now()
		digitalOrderMap["pay_amount"] = req.PayAmount
		digitalOrderMap["pay_sn"] = req.PaySn
	}
	db := GetDBConn()
	_, err = db.Table("digital_order").Where("order_sn = ?", orderSn).Update(digitalOrderMap)
	if err != nil {
		res.Code = 400
		res.Message = err.Error()
		glog.Errorf("更新数字藏品订单失败,error(%s)", err.Error())
		return
	}
	res.Code = 200
	res.Message = "success"
	res.OrderSn = orderSn
	return
}

//FindByOrderSnAndUserId 根据订单号和用户id获取订单数据信息
func (ds *DigitalOrderService) FindByOrderSnAndUserId(orderSn, userId string) (digitalOrder *models.DigitalOrder, err error) {
	db := GetDBConn()
	digitalOrder = new(models.DigitalOrder)
	tx := db.Table("digital_order").Where("order_sn = ?", orderSn)
	if userId != "" {
		tx = tx.Where("buy_user_id = ?", userId)
	}
	_, err = tx.Get(digitalOrder)
	return
}

//CreateSn 创建数字藏品订单号
func (ds *DigitalOrderService) CreateSn() string {
	return cast.ToString(GetSn("digital")[0])
}

//PayDigitalOrder 数字藏品订单支付
func (ds *DigitalOrderService) PayDigitalOrder(ctx context.Context, req *oc.PayDigitalOrderRequest) (res *oc.PayDigitalOrderResponse, err error) {
	res = new(oc.PayDigitalOrderResponse)
	res.Code = 400

	redisClient := GetRedisConn()
	key := "digital-order:order-pay:"
	isOk := redisClient.Exists(key + req.OrderSn)
	if isOk.Val() > 0 {
		codeStr := redisClient.Get(key + req.OrderSn).Val() // 获取Redis中的Code
		res.Code = 200
		res.Message = "success"
		res.Data = codeStr
		return
	}
	//查询订单是否存在
	digitalOrder, _ := ds.FindByOrderSnAndUserId(req.OrderSn, req.UserId)
	if digitalOrder == nil || digitalOrder.Id == 0 {
		res.Message = fmt.Sprintf("%s订单不存在", req.OrderSn)
		return
	}
	payForm := dto.DigitalOrderPayForm{}
	payForm.TransType = 1 //微信jsapi支付
	payForm.OutTradeNo = time.Now().Format("20060102150405") + RandomString(18, []rune("0123456789"))
	payForm.OrderId = digitalOrder.OrderSn
	payForm.PayPrice = digitalOrder.Price * 100
	payForm.TotalPrice = digitalOrder.Price * 100
	payForm.OfflineNotifyUrl = config.GetString("nft-notifyurl")
	payForm.Openid = req.Openid
	payForm.ProductId = "nft000001"
	payForm.ProductName = "nft数字藏品购买"
	payForm.ProductDesc = "nft数字藏品购买"
	payForm.MerchantId = utils.MerchantId
	payForm.SubAppId = utils.SubAppId
	payForm.ClientIP = utils.GetClientIp()
	//默认5分钟有效期
	payForm.ValidTime = 5
	jsonForm := kit.JsonEncode(payForm)
	_, formData := utils.PayCenterSign(jsonForm)

	url := "/pay/unifiedorder" //"http://localhost:7035/pay/unifiedorder"
	glog.Info("数字藏品订单支付请求参数：", utils.PayCenterUrl+url, jsonForm)
	payRes, err := utils.HttpPost(utils.PayCenterUrl+url, []byte(formData), utils.ContentTypeToForm)
	glog.Info("数字藏品订单支付结果：", string(payRes))
	if err != nil {
		glog.Error(url, "，支付中心接口调用失败，", err)
		res.Message = err.Error()
		return
	}
	paycenterRes := dto.PayCenterResponse{}
	err = json.Unmarshal(payRes, &paycenterRes)
	if err != nil {
		glog.Error(url, "，支付中心接口解析失败，", err)
		res.Message = err.Error()
		return
	}
	if paycenterRes.Code != 200 {
		res.Message = paycenterRes.Message
		return
	}
	res.Code = 200
	res.Message = "success"
	res.Data = string(payRes)
	//一次支付会话过期时间是8分钟
	redisClient.Set(key+req.OrderSn, string(payRes), 60*time.Second)
	return
}

//PayDigitalOrderNotify 订单支付回调
func (ds *DigitalOrderService) PayDigitalOrderNotify(ctx context.Context, req *oc.PayDigitalOrderNotifyRequest) (res *oc.DigitalOrderResponse, err error) {
	res = new(oc.DigitalOrderResponse)
	res.Code = 400
	//查询订单
	digitalOrder, _ := ds.FindByOrderSnAndUserId(req.OrderSn, "")
	if digitalOrder == nil || digitalOrder.Id == 0 {
		res.Message = fmt.Sprintf("%s订单不存在", req.OrderSn)
		return
	}
	nftId := digitalOrder.NftId
	//支付成功,领取nftId
	client := ctc.GetContentCenterClient()
	defer client.Close()
	pickIn := new(ctc.NftPayResultPickRequest)
	pickIn.NftId = nftId
	pickIn.UserIdentification = digitalOrder.BuyUserId
	pickOut, err := client.DigitRPC.NftPayResultPick(client.Ctx, pickIn)
	if err != nil {
		res.Message = err.Error()
		glog.Errorf("NftPayResultPick 领取nft失败,error(%s)", err.Error())
		return
	}
	if pickOut.Code == 400 {
		res.Message = pickOut.Message
		glog.Errorf("NftPayResultPick 领取nft失败,error(%s)", pickOut.Message)
		return
	}
	//更改订单状态
	upin := new(oc.UpdateDigitalOrderRequest)
	upin.OrderSn = req.OrderSn
	upin.Status = 1
	upin.PaySn = req.PaySn
	upin.PayAmount = cast.ToFloat32(utils.BcDiv(float64(req.PayAmount), 100, 2))
	upout, err := ds.UpdateDigitalOrder(ctx, upin)
	if err != nil {
		res.Message = err.Error()
		glog.Errorf("UpdateDigitalOrder 更新订单(%s)失败,error(%s)", req.OrderSn, err.Error())
		return
	}
	if upout.Code == 400 {
		res.Message = upout.Message
		glog.Errorf("UpdateDigitalOrder 更新订单(%s)失败,error(%s)", req.OrderSn, upout.Message)
		return
	}
	res.Code = 200
	res.Message = "success"
	res.OrderSn = req.OrderSn
	return
}

//OrderPayTest 测试支付
func (ds *DigitalOrderService) OrderPayTest(ctx context.Context, req *oc.OrderPayTestRequest) (res *oc.PayDigitalOrderResponse, err error) {
	res = new(oc.PayDigitalOrderResponse)
	res.Code = 400
	payForm := dto.DigitalOrderPayForm{}
	payForm.TransType = 1 //微信jsapi支付
	payForm.OutTradeNo = time.Now().Format("20060102150405") + RandomString(18, []rune("0123456789"))
	payForm.OrderId = ds.CreateSn()
	payForm.TotalPrice = 1 //测试用0.01
	payForm.PayPrice = 1   //测试用0.01
	payForm.OfflineNotifyUrl = "https://awen.sit.rvet.cn"
	payForm.Openid = req.Openid
	payForm.ProductId = "nft000001"
	payForm.ProductName = "nft数字藏品购买"
	payForm.ProductDesc = "nft数字藏品购买"
	payForm.MerchantId = utils.MerchantId
	payForm.SubAppId = utils.SubAppId
	payForm.ClientIP = utils.GetClientIp()
	//默认5分钟有效期
	payForm.ValidTime = 5
	jsonForm := kit.JsonEncode(payForm)
	_, formData := utils.PayCenterSign(jsonForm)

	url := "/pay/unifiedorder" //"http://localhost:7035/pay/unifiedorder"
	glog.Info("数字藏品订单支付请求参数：", utils.PayCenterUrl+url, jsonForm)
	payRes, err := utils.HttpPost(utils.PayCenterUrl+url, []byte(formData), utils.ContentTypeToForm)
	glog.Info("数字藏品订单支付结果：", string(payRes))
	if err != nil {
		glog.Error(url, "，支付中心接口调用失败，", err)
		res.Message = err.Error()
		return
	}
	paycenterRes := dto.PayCenterResponse{}
	err = json.Unmarshal(payRes, &paycenterRes)
	if err != nil {
		glog.Error(url, "，支付中心接口解析失败，", err)
		res.Message = err.Error()
		return
	}
	if paycenterRes.Code != 200 {
		res.Message = paycenterRes.Message
		return
	}
	res.Code = 200
	res.Message = "success"
	res.Data = string(payRes)
	return
}

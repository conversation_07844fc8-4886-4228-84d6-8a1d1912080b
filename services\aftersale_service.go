package services

import (
	"context"
	"fmt"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"
	"strconv"
	"sync"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 售后模块
type AfterSaleService struct {
	CommonService
}

// 退款管理列表
// 修改该列表的同时要修改导出的列表 RefundOrderExport
// todo 退款单拆单
func (a AfterSaleService) RefundOrderList(ctx context.Context, in *oc.RefundOrderInfoRequest) (*oc.RefundOrderInfoResponse, error) {
	out := oc.RefundOrderInfoResponse{Code: 400}
	Db := GetDBConn()
	var RefundOrders []models.RefundOrderLists

	session := Db.Table("refund_order").
		Join("inner", "`order_main`", "`order_main`.order_sn=refund_order.order_sn").
		Join("left", "refund_order_third_product", "refund_order.refund_sn = refund_order_third_product.refund_sn").
		Join("left", "order_detail", "`order_main`.order_sn=order_detail.order_sn").
		Join("left", "dc_dispatch.warehouse", "order_main.warehouse_id =warehouse.id").
		Where("1=1")

	//订单搜索
	if len(in.Keyword) > 0 {
		switch in.SearchType {
		case 1: //原订单号
			session.And("`refund_order`.order_sn like ?", "%"+in.Keyword+"%")
		case 2: //退款单号
			session.And("`refund_order`.refund_sn like ?", "%"+in.Keyword+"%")
		default: //default case
		}
	}

	//如果是逍宠，则不做权限判断，只查询当前的门店订单
	session.And("order_main.org_id = ?", in.Orgid)

	//退款发起人:1用户 2商家 3客服 4-BD,5-系统,6-开放平台
	if in.UserType > 0 {
		session.And("`refund_order`.apply_op_user_type = ?", in.UserType)
	}

	//退款类型:1为仅退款,2为退款退货
	switch in.RefundType {
	case 1:
		session.And("`refund_order`.refund_type = 1")
	case 2:
		session.And("`refund_order`.refund_type = 2")
	}

	//店铺类型 1新瑞鹏  2TP代运营
	if in.AppChannel > 0 {
		if in.AppChannel == 1 {
			session.And("order_main.app_channel = 1")
		} else {
			session.And("order_main.app_channel !=1")
		}
	}

	//订单类型:1实物  2虚拟
	if in.OrderType > 0 {
		switch in.OrderType {
		case 1: //1实物
			session.And("refund_order_third_product.product_type=1 OR (`refund_order`.channel_id in (1,9) AND `order_main`.is_virtual = 0)")
		case 2: //2虚拟
			session.And("refund_order_third_product.product_type=2 OR (`refund_order`.channel_id in (1,9) AND `order_main`.is_virtual = 1 )")
		default: //default case
		}
	}

	//订单退款状态  1:退款中 2:退款关闭 3:退款成功
	if in.RefundState != 0 {
		if in.RefundState == 2 {
			session.And("`refund_order`.refund_state IN (2, 9)")
		} else {
			session.And("`refund_order`.refund_state = ?", in.RefundState)
		}
	}

	if in.ChannelId > 0 {
		if in.ChannelId > 100 {
			session.And("order_detail.child_channel_id=?", cast.ToString(in.ChannelId))
		} else {
			if in.ChannelId == ChannelJddjId || in.ChannelId == 420 {
				session.And("`order_main`.channel_id = 4 and order_detail.child_channel_id=''")
			} else {
				session.And("`order_main`.channel_id = ?", in.ChannelId)
			}
		}
	} else {
		session.And("`order_main`.channel_id in (1,2,3,4,9,100)")
	}

	//下单时间
	if len(in.StartTime) > 0 {
		session.And("`refund_order`.create_time > ?", in.StartTime)
	}
	if len(in.EndTime) > 0 {
		session.And("`refund_order`.create_time <= ?", in.EndTime)
	}

	if len(in.Shopids) > 0 {
		session.In("`order_main`.shop_id", in.Shopids)
	}

	if in.PushThirdState > 0 {
		if in.PushThirdState == 1 {
			session.And("refund_order.push_third = 1 or refund_order.refund_state != 3")
		} else {
			session.And("refund_order.push_third = 0 and refund_order.refund_state = 3")
		}
	}

	countSession := session.Clone()
	defer countSession.Close()
	var count int32
	//refund_order_third_product.order_sn 需要增加ifnull 否则做关联不上的count出来的数量不正确
	_, err := countSession.Select("COUNT(DISTINCT `refund_order`.`refund_sn`, IFNULL(`refund_order_third_product`.`order_sn`,''))").Get(&count)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return &out, nil
	}
	out.TotalCount = count
	if count == 0 {
		out.Code = 200
		return &out, nil
	}

	err = session.Select(`refund_order.*,IFNULL(refund_order_third_product.order_sn,'') AS child_order_sn,
IFNULL(refund_order_third_product.product_type,0) AS product_type,order_main.user_agent,order_main.shop_name, order_main.source,
order_main.warehouse_name,order_main.warehouse_id, order_main.warehouse_code,order_main.freight,order_main.freight_privilege,order_main.packing_cost,
order_detail.child_channel_name,warehouse.category as warehouse_category,
order_detail.push_third_order,order_main.order_status,order_main.is_virtual`).
		GroupBy("refund_order.refund_sn,refund_order_third_product.order_sn").
		Desc("id").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		Find(&RefundOrders)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return &out, nil
	}

	if len(RefundOrders) == 0 {
		out.Data = []*oc.RefundOrderInfo{}
		return &out, nil
	}

	for _, i2 := range RefundOrders {
		if i2.RefundState == 9 {
			i2.RefundState = 2
		}

		//查询商品信息
		var RefundOrderProductLists []*oc.RefundOrderProductInfo
		var Products []models.RefundOrderProduct
		//是否是实物

		var isReal bool
		//增加i2.ChildOrderSn != ""判断时解决v6.0虚实组合版本之前第三方退款单没有refund_order_third_product表的问题
		if isThirdChannel(i2.ChannelId) && i2.ChildOrderSn != "" {
			//第三方订单显示订单订单号
			i2.OrderSn = i2.ChildOrderSn
			Db.Table("refund_order_third_product").Where("refund_sn = ? AND order_sn=?", i2.RefundSn, i2.ChildOrderSn).
				Find(&Products)
			if i2.ProductType == 1 {
				isReal = true
			}
		} else {
			Db.Table("refund_order_product").Where("refund_sn = ?", i2.RefundSn).
				Find(&Products)
			if i2.IsVirtual == 0 {
				isReal = true
			}
		}

		var sunRefundAmount int
		for _, i3 := range Products {
			var tsd struct {
				ThirdSkuId   string
				LocationCode string
			}
			Db.Table("order_product").Select("third_sku_id, location_code").
				Where("sku_id IN (?) AND order_sn = ?", i3.SkuId, i2.OrderSn).
				Get(&tsd)
			RefundOrderProductLists = append(RefundOrderProductLists, &oc.RefundOrderProductInfo{
				Id:           int32(i3.Id),
				RefundSn:     i3.RefundSn,
				ProductType:  i3.ProductType,
				ParentSkuId:  i3.ParentSkuId,
				ProductName:  i3.ProductName,
				SkuId:        i3.SkuId,
				ThirdSkuId:   tsd.ThirdSkuId,
				MarkingPrice: i3.MarkingPrice,
				ProductPrice: i3.ProductPrice,
				Quantity:     i3.Quantity,
				RefundNum:    i3.Tkcount,
				RefundAmount: i3.RefundAmount,
				RefundPrice:  i3.RefundPrice,
				Spec:         i3.Spec,
				Barcode:      i3.Barcode,
				LocationCode: tsd.LocationCode,
			})
			sunRefundAmount += kit.YuanToFen(cast.ToFloat64(i3.RefundAmount))
		}

		var pushFailReason string
		var canRePush bool
		// 如果正向订单取消了且正向单未推送，那么逆向单也不推送
		if i2.IsVirtual == 0 && (i2.PushThirdOrder > 0 || i2.OrderStatus >= 10) && i2.PushThird < 1 && i2.AppChannel != SaasAppChannel {
			// 售后完成且没有推送的可以重推
			if i2.RefundState == 3 {
				canRePush = true
			}
			if len(i2.PushThirdFailReason) > 0 {
				pushFailReason = fmt.Sprintf("此退款单未能成功从阿闻系统推送至%s系统，失败原因：%s", models.GetOrderSourceChannel(i2.Source), i2.PushThirdFailReason)
			}
		}
		//虚拟订单不需要推送子龙，也不需要显示推送错误信息
		if i2.ProductType == 2 {
			canRePush = false
			pushFailReason = ""
		}

		var data = &oc.RefundOrderInfo{
			OrderSn:             i2.OrderSn,
			RefundType:          i2.RefundType,
			RefundReason:        i2.RefundReason,
			RefundState:         i2.RefundState,
			RefundSn:            i2.RefundSn,
			CreateTime:          i2.CreateTime.Format(kit.DATETIME_LAYOUT),
			ChannelId:           i2.ChannelId,
			ShopId:              i2.ShopId,
			ShopName:            i2.ShopName,
			Source:              i2.Source,
			WarehouseCode:       i2.WarehouseCode,
			WarehouseName:       i2.WarehouseName,
			IsVirtual:           i2.IsVirtual,
			RefundAmount:        i2.RefundAmount,
			UserAgent:           i2.UserAgent,
			Product:             RefundOrderProductLists,
			Category:            i2.WarehouseCategory,
			PushThird:           i2.PushThird,
			PushThirdFailReason: pushFailReason,
			CanRePush:           canRePush,
		}
		//目前存在饿了么取消订单金额为0的情况
		if i2.RefundAmount == "0" || isThirdChannel(i2.ChannelId) {
			data.RefundAmount = cast.ToString(kit.FenToYuan(sunRefundAmount))
		}

		//第三方最后一笔实物退款 需要加上运费与包装费
		//退款商品上加总的退款总费用是不包含运费于包装费的 只有主单上的退款数量是包含了包装非于运费的
		//所以从退款商品拆了之后加总出来的退款费需要加上运费与包装费

		if i2.FullRefund == 1 && isReal && isThirdChannel(i2.ChannelId) && i2.ChildOrderSn != "" {
			//查询是否有配送优惠,有的话需要去掉邮费
			newFreightTotalRefund := kit.YuanToFen(cast.ToFloat64(data.RefundAmount))
			newFreightTotalRefund += int(i2.Freight - i2.FreightPrivilege + i2.PackingCost)
			data.RefundAmount = cast.ToString(kit.FenToYuan(newFreightTotalRefund))
		}

		// 转换通道类型
		if data.ChannelId == ChannelJddjId && len(i2.ChildChannelId) > 0 {
			data.ChannelId = cast.ToInt32(i2.ChildChannelId)
		}

		out.Data = append(out.Data, data)
	}

	out.Code = 200
	return &out, nil
}

// 部分退款获取退款商品信息
// 用于美团、饿了么、京东渠道
// orderGoods 父订单商品
func getPartRefundFoodsList(orderGoods []models.OrderProduct, orderSn string) []*oc.PartRefundFoodsList {
	var res []*oc.PartRefundFoodsList
	if len(orderGoods) == 0 {
		return res
	}
	groupSkuMap := make(map[string][]*oc.PartRefundFoodsList)
	//组合商品子商品在单个组合中的售卖数量
	groupItemNumMap := make(map[string]int32)
	for _, i2 := range orderGoods {
		//虚拟商品已经核销的不可退，需要计算这一部分
		usedNum := int32(0)
		if i2.ProductType == 2 {
			codes := GetValidOrderVerifyCodes(i2.OrderSn, 4)
			usedNum = int32(len(codes))
		}
		//销售数量 - 已经退掉的数量 - 已经使用的数量（虚拟商品才存在） = 可退的数量
		if i2.Number-i2.RefundNum-usedNum > 0 {
			item := &oc.PartRefundFoodsList{
				AppFoodCode:         i2.ProductId,
				FoodName:            i2.ProductName,
				SkuId:               i2.SkuId,
				FoodPrice:           float32(kit.FenToYuan(i2.DiscountPrice)),
				Count:               float32(i2.Number - i2.RefundNum - usedNum), //可退数量
				RefundPrice:         float32(kit.FenToYuan(i2.PayPrice)),
				SaleCount:           float32(i2.Number - i2.RefundNum - usedNum),
				GroupItemNum:        i2.GroupItemNum,
				Spec:                i2.Specs,
				OrderProductId:      i2.Id,
				ParentSkuId:         i2.ParentSkuId,
				ProductType:         i2.ProductType,
				SubBizOrderId:       i2.SubBizOrderId,
				SurplusRefundAmount: float32(kit.FenToYuan(i2.PaymentTotal) - (kit.FenToYuan(i2.PayPrice) * float64(i2.RefundNum+usedNum))),
			}
			//如果是组合商品的话查询出组合商品明细信息
			//子商品
			if i2.ParentSkuId != "" {
				groupItemNumMap[i2.SkuId] = i2.GroupItemNum
				groupSkuMap[i2.ParentSkuId] = append(groupSkuMap[i2.ParentSkuId], item)
			} else {
				res = append(res, item)
			}
		}
	}
	if len(groupSkuMap) > 0 {
		for i, v := range res {
			if v.ProductType == 3 {
				children := groupSkuMap[v.SkuId]
				for _, child := range children {
					//第三方订单组合商品子商品的可退款数量= 组合商品可退款数量*该子商品在单个组合商品中的可售卖数量
					child.Count = res[i].Count * float32(groupItemNumMap[child.SkuId])
				}
				res[i].ChildProducts = groupSkuMap[v.SkuId]
			}
		}
	}
	return res
}

// 部分退款获取退款商品信息
// 用于阿闻到家渠道
// orderGoods 子订单商品
func getAwenPartRefundFoodsList(orderGoods []models.OrderProduct, orderSn string) []*oc.PartRefundFoodsList {
	var res []*oc.PartRefundFoodsList
	if len(orderGoods) == 0 {
		return res
	}
	//组合商品 父商品skuId=>父商品信息
	groupGoodMap := make(map[string]*oc.PartRefundFoodsList)

	for _, i2 := range orderGoods {
		//虚拟商品已经核销的不可退，需要计算这一部分
		usedNum := int32(0)
		if i2.ProductType == 2 {
			codes := GetValidOrderVerifyCodes(i2.OrderSn, 4)
			usedNum = int32(len(codes))
		}
		//销售数量 - 已经退掉的数量 - 已经使用的数量（虚拟商品才存在） = 可退的数量
		if i2.Number-i2.RefundNum-usedNum > 0 {
			item := &oc.PartRefundFoodsList{
				AppFoodCode:         i2.ProductId,
				FoodName:            i2.ProductName,
				SkuId:               i2.SkuId,
				FoodPrice:           float32(kit.FenToYuan(i2.DiscountPrice)),
				Count:               float32(i2.Number - i2.RefundNum - usedNum), //可退数量
				RefundPrice:         float32(kit.FenToYuan(i2.PayPrice)),
				SaleCount:           float32(i2.Number - i2.RefundNum - usedNum),
				Spec:                i2.Specs,
				OrderProductId:      i2.Id,
				ParentSkuId:         i2.ParentSkuId,
				ProductType:         i2.ProductType,
				GroupItemNum:        i2.GroupItemNum,
				SurplusRefundAmount: float32(kit.FenToYuan(i2.PaymentTotal) - (kit.FenToYuan(i2.PayPrice) * float64(i2.RefundNum+usedNum))),
			}
			//不是组合商品的子商品
			if i2.ParentSkuId == "" {
				res = append(res, item)
				continue
			}

			//如果不存在 获取父商品信息
			if _, ok := groupGoodMap[i2.ParentSkuId]; !ok {
				queryGroupGoodsRes, err := GetParentOrderProductByParentSku(orderSn, i2.ParentSkuId)
				if err != nil {
					//如果错误则跳过
					glog.Error(orderSn, "getAwenPartRefundFoodsList-根据子订单查询父订单的组合商品信息出错", err)
					continue
				}
				if len(queryGroupGoodsRes) == 0 {
					glog.Error(orderSn, "getAwenPartRefundFoodsList-根据子订单查询父订单的组合商品信息失败-未查询到父商品信息")
					continue
				}

				groupGoods := getAwenPartRefundFoodsList(queryGroupGoodsRes, orderSn)
				//只获取第一个 因为一个组合商品在一个订单中 只可能存在一条商品信息
				//先放在map里 因为组合商品的子商品循环结束之后才会确定 循环结束之后 再放入结果集 res
				groupGoodMap[i2.ParentSkuId] = groupGoods[0]
			}
			groupGoodMap[i2.ParentSkuId].ChildProducts = append(groupGoodMap[i2.ParentSkuId].ChildProducts, item)
		}
	}

	if len(groupGoodMap) > 0 {
		for _, v := range groupGoodMap {
			res = append(res, v)
		}
	}
	return res
}

// 查询可被部分退款的商品详情 saas-v1.0
func (a AfterSaleService) OrderGetPartRefundFoods(ctx context.Context, in *oc.OrderGetPartRefundFoodsRequest) (*oc.OrderPartRefuFoodsResponse, error) {
	out := oc.OrderPartRefuFoodsResponse{
		Code:    400,
		Message: "失败",
	}
	//判断是否拆单中
	IsSplitRes := OrderIsSplit([]string{in.OrderSn})
	if _, ok := IsSplitRes[in.OrderSn]; ok {
		if IsSplitRes[in.OrderSn] == 0 {
			glog.Info("部分退款拆单中：" + kit.JsonEncode(in))
			out.Message = "拆单中"
			return &out, nil
		}
	}

	a.session = GetDBConn().NewSession()
	defer a.session.Close()

	a.orderMain = new(models.OrderMain)
	//第三方订单in.OrderSn传过来的是第三方订单号  而非阿闻订单号
	ok, err := a.session.Where(" order_status>10 AND (old_order_sn = ? OR order_sn=?)", in.OrderSn, in.OrderSn).Get(a.orderMain)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error(in.OrderSn + err.Error())
		return &out, nil
	}
	if !ok {
		out.Message = "订单不存在"
		glog.Error("订单不存在:" + in.OrderSn)
		return &out, nil
	}
	OrderId, err := strconv.ParseInt(in.OrderSn, 10, 64)
	if err != nil {
		out.Message = err.Error()
		out.Error = err.Error()
		return &out, nil
	}

	if a.orderMain.ChannelId == ChannelAwenId || a.orderMain.ChannelId == ChannelDigitalHealth {
		//下单时候的商品集合
		proList := make([]models.OrderProduct, 0)
		//err = a.session.Where("order_sn = ? AND parent_sku_id ='' AND number>refund_num", a.orderMain.OrderSn).Find(&proList)
		proList = a.GetChildOrderProducts()
		if len(proList) <= 0 {
			out.Message = "订单中没有可退商品"
			out.Error = out.Message
			return &out, nil
		}
		out.Data = getAwenPartRefundFoodsList(proList, a.orderMain.OrderSn)
		out.Code = 200
		out.Message = "ok"
		return &out, nil
	}

	//如果是饿了么,京东到家 的单
	if a.orderMain.ChannelId == ChannelElmId {
		proList := make([]models.OrderProduct, 0)
		err = a.session.Where("order_sn = ? AND number>refund_num", a.orderMain.OrderSn).Find(&proList)
		if err != nil {
			out.Message = err.Error()
			out.Error = err.Error()
			return &out, nil
		}

		if len(proList) <= 0 {
			out.Message = "订单中没有可退商品"
			out.Error = out.Message
			return &out, nil
		}

		var data = getPartRefundFoodsList(proList, a.orderMain.OrderSn)
		//2.0订单逆向补充信息
		etClient := et.GetExternalClient()
		res, err := etClient.ELMORDER.ElmOrderReverseConsult(etClient.Ctx, &et.ElmOrderReverseConsultRequest{
			OrderId:           a.orderMain.OldOrderSn,
			RefundAllSubOrder: true,
			AppChannel:        in.StoreMasterId,
		})
		if err != nil {
			out.Message = err.Error()
			out.Error = err.Error()
			return &out, nil
		}
		var elmPro = make(map[string]int64, 0)
		for _, v := range res.SubOrderConsultResultList {
			elmPro[v.CustomSkuId] = v.PlatformSkuId
		}
		for k, v := range data {
			if platformSkuId, ok := elmPro[v.SkuId]; ok {
				data[k].PlatformSkuId = platformSkuId
			}
		}

		out.Data = data
		out.Code = 200
		out.Message = "ok"
		return &out, nil
	}

	if a.orderMain.ChannelId == ChannelJddjId {
		proList := make([]models.OrderProduct, 0)
		err = a.session.Where("order_sn = ?", a.orderMain.OrderSn).Find(&proList)
		if err != nil {
			out.Message = err.Error()
			out.Error = err.Error()
			return &out, nil
		}

		if len(proList) <= 0 {
			out.Message = "订单中商品不可部分退款"
			out.Error = out.Message
			return &out, nil
		}
		out.Data = getPartRefundFoodsList(proList, a.orderMain.OrderSn)
		out.Code = 200
		out.Message = "ok"
		return &out, nil
	}

	//下面是美团的数据获取逻辑
	etClient := et.GetExternalClient()

	res, err := etClient.MtReturn.OrderGetPartRefundFoods(etClient.Ctx, &et.MtOrderRequest{
		OrderId:       OrderId,
		StoreMasterId: in.StoreMasterId,
	})
	if err != nil {
		out.Message = err.Error()
		out.Error = err.Error()
		return &out, nil
	}
	if res.Code != 200 {
		if res.Message == "不允许发起部分退款" {
			res.Message = "订单中没有可退商品！"
		}
		out.Message = res.Message
		out.Error = res.Error
		return &out, nil
	} else {
		ProList := make([]models.OrderProduct, 0)
		err = a.session.Where("order_sn = ? AND number>refund_num", a.orderMain.OrderSn).Find(&ProList)
		if err != nil {
			out.Message = err.Error()
			out.Error = err.Error()
			return &out, nil
		}
		out.Data = getPartRefundFoodsList(ProList, a.orderMain.OrderSn)
	}

	out.Code = 200
	out.Message = "ok"
	return &out, nil
}

// 发起部分退款
func (a AfterSaleService) OrderApplyPartRefund(ctx context.Context, in *oc.OrderApplyPartRefundRequest) (*oc.BaseResponse, error) {
	out := oc.BaseResponse{Code: 400}
	a.session = GetDBConn().NewSession()
	defer a.session.Close()

	var RefundOrder []models.RefundOrder
	a.session.Where("old_order_sn = ? and refund_state in(1,5,6,7)", in.OrderId).Find(&RefundOrder)
	if len(RefundOrder) > 0 {
		out.Message = "请把所有退款处理完再发起退款！"
		return &out, nil
	}

	a.orderMain = GetOrderMainByOldOrderSn(in.OrderId)

	var err error
	switch a.orderMain.ChannelId {
	case ChannelMtId, ChannelElmId, ChannelJddjId, 420:
		err = NewChannelOrder(&models.Order{
			OrderMain: a.orderMain,
		}).ApplyPartRefund(in)
	}
	if err != nil {
		out.Message = "调取第三方渠道部分退款接口失败," + err.Error()
		out.Error = err.Error()
		return &out, nil
	}

	out.Code = 200
	return &out, nil
}

// 驳回订单退款申请：
// 1.当商家收到用户发起的部分或全额退款申请，如商家拒绝申请，可调用此接口操作驳回退款申请。
// 注意：部分退款成功后不影响订单当前的订单状态；全额退款成功后，订单状态会变更为“订单已取消”(status=9)。
// 2.商家调用此接口驳回用户退款申请的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
// 3.若商家调用接口驳回用户发起的退款申请，平台会向商家系统推送退款消息。
func (a AfterSaleService) MtOrderRefundReject(ctx context.Context, params *oc.MtOrderRefundRequest) (*oc.ExternalResponse, error) {
	glog.Info("驳回订单退款申请请求参数" + kit.JsonEncode(params))
	out := oc.ExternalResponse{Code: 400}

	//先修商品入库数量
	Db := GetDBConn()

	refundOrder := models.RefundOrder{}
	//查询日志
	isOk, err := Db.SQL("select  * from `refund_order` where (refund_sn= ? or old_refund_sn = ?) and refund_state in(1,5,6,7) ", params.Refundsn, params.Refundsn).Get(&refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error("售后单还不存在就接到了其他的回调处理:" + params.Refundsn + err.Error())
		}
		out.Message = "退款单还不存在就接到了其他的回调处理"
		out.Error = "退款单还不存在就接到了其他的回调处理"
		return &out, nil
	}
	// RejectReasonCode必传
	if refundOrder.ServiceType != "" {
		if params.Reason == "" {
			out.Code = 400
			out.Message = "驳回退款申请时，驳回原因必传"
			return &out, nil
		}
	}
	//代运营之前的瑞鹏退款单数据为0  需要置为1
	if refundOrder.AppChannel == 0 {
		refundOrder.AppChannel = 1
	}

	if refundOrder.ChannelId == ChannelAwenId || refundOrder.ChannelId == ChannelMallId || refundOrder.ChannelId == ChannelDigitalHealth {
		service := RefundOrderService{}
		answer := new(oc.RefundOrderAnswerRequest)
		answer.OrderId = refundOrder.OrderSn
		answer.ExternalOrderId = refundOrder.OldOrderSn
		answer.RefundOrderSn = refundOrder.RefundSn
		answer.Reason = params.Reason
		answer.ResultType = 2
		answer.ResultTypeNote = "商家驳回退款请求"
		answer.OperationType = "商家驳回退款请求"
		answer.OperationUser = params.Operationer
		answerResponse, err := service.RefundOrderAnswer(nil, answer)
		if err != nil {
			glog.Error("售后单申请返回错误:" + params.Refundsn + err.Error())
			out.Message = "售后单申请返回错误"
			out.Error = err.Error()
			return &out, nil
		}
		if answerResponse.Code == 400 {
			glog.Error("售后单申请返回错误:" + params.Refundsn + err.Error())
			out.Message = "售后单申请返回错误"
			out.Error = err.Error()
			return &out, nil
		}
		out.Message = answerResponse.Message
		out.Error = answerResponse.Error
		out.Code = answerResponse.Code
		//处理积分问题 注：实物订单才操作积分
		if refundOrder.IsVirtual == 0 {
			//根据退款单的订单号获取订单主单明细
			orderMain := GetOrderMainByOrderSn(refundOrder.OrderSn)
			integralService := OrderIntegralService{}
			//驳回退款，恢复用户积分
			//client := igc.GetIntegralServiceClient()
			recoverUserIntegralReq := &oc.RecoverUserIntegralReq{
				OrderSn:       refundOrder.OrderSn,
				MemberId:      orderMain.MemberId,
				RefundOrderSn: refundOrder.RefundSn,
				OrgId:         orderMain.OrgId,
			}

			//glog.Info("商城驳回退款申请，返回客户积分。请求参数:", kit.JsonEncode(recoverUserIntegralReq))
			integral, err := integralService.RecoverUserIntegral(context.Background(), recoverUserIntegralReq)
			glog.Info("商城驳回退款申请，返回客户积分。请求参数:", kit.JsonEncode(recoverUserIntegralReq), "|返回结果：", kit.JsonEncode(integral))
			if err != nil {
				glog.Error("商城驳回退款申请，返回客户积分。操作失败:" + params.Refundsn + err.Error())
			}
		}

		return &out, nil
	}

	session := Db.NewSession()
	defer session.Close()
	session.Begin()

	//更新退款商品的退款数量
	//传过来的是这个退款单的退款数量
	for _, x := range params.RefundGoodsOrders {
		good := models.RefundOrderThirdProduct{
			Tkcount: x.Tkcount,
		}
		_, err = session.ID(x.Id).Cols("tkcount").Update(&good)
		if err != nil {
			session.Rollback()
			out.Code = 400
			out.Error = err.Error()
			return &out, nil
		}
	}

	_, err = session.Exec("update refund_order set refund_state=5 where refund_sn=?", refundOrder.RefundSn)
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Error = err.Error()
		return &out, nil
	}

	//饿了么的拒绝要最后客服审核结果出来后才可以去修改退款数量
	if refundOrder.ChannelId != ChannelElmId {
		_, err = session.Exec("update refund_order_product set tkcount = 0 where refund_sn = ?", refundOrder.RefundSn)
		if err != nil {
			session.Rollback()
			out.Message = "修改商品退款数量出错"
			out.Error = "修改商品退款数量出错" + err.Error()
			return &out, nil
		}
		_, err = session.Exec("update refund_order_third_product set tkcount = 0 where refund_sn = ?", refundOrder.RefundSn)
		if err != nil {
			session.Rollback()
			out.Message = "修改商品退款数量出错1"
			out.Error = "修改商品退款数量出错1" + err.Error()
			return &out, nil
		}
	}

	//调用美团订退款驳回接口
	if refundOrder.ChannelId == ChannelMtId {
		etClient := et.GetExternalClient()
		defer etClient.Close()
		if refundOrder.ServiceType != "" {
			inpar := et.MtReviewAfterSalesRequest{}
			inpar.WmOrderIdView = cast.ToInt64(params.OrderId)
			inpar.ReviewType = 2
			inpar.RejectReasonCode = params.RejectReasonCode
			inpar.RejectOtherReason = params.Reason
			res, err := etClient.MtOrder.MtReviewAfterSales(etClient.Ctx, &inpar)
			glog.Info("驳回订单退货退款申请返回参数 ", params.OrderId, " ", kit.JsonEncode(res))
			if err != nil {
				session.Rollback()
				out.Code = 400
				out.Error = err.Error()
				return &out, nil
			}
			if res.Code != 200 {
				session.Rollback()
				out.Code = 400
				out.Message = res.Message
				out.Error = res.Error
				return &out, nil
			}
		} else {
			inpar := et.MtOrderRefundRequest{}
			inpar.OrderId = refundOrder.OldOrderSn
			inpar.Reason = params.Reason
			inpar.StoreMasterId = params.StoreMasterId
			res, err := etClient.MtOrder.MtOrderRefundReject(etClient.Ctx, &inpar)

			glog.Info("驳回订单退款申请返回参数 ", params.OrderId, " ", kit.JsonEncode(res))
			if err != nil {
				session.Rollback()
				out.Code = 400
				out.Error = err.Error()
				return &out, nil
			}
			if res.Code != 200 {
				session.Rollback()
				out.Code = 400
				out.Message = res.Message
				out.Error = res.Error
				return &out, nil
			}
		}
	} else if refundOrder.ChannelId == ChannelElmId {
		//调用饿了么拒绝
		etClient := et.GetExternalClient()
		defer etClient.Close()

		inpar := et.ELMOAnswerFundRequest{
			OrderId:       refundOrder.OldOrderSn,
			RefundOrderId: params.Refundsn,
			RefuseReason:  params.Reason,
			AppChannel:    refundOrder.AppChannel,
		}

		//由用户发起的售后单是有第三方退款单号
		if len(refundOrder.OldRefundSn) > 0 {
			inpar.RefundOrderId = refundOrder.OldRefundSn
		} else {
			inpar.RefundOrderId = params.Refundsn
		}
		res, err := etClient.ELMORDER.ELMRefuseFund(etClient.Ctx, &inpar)
		if err != nil {
			session.Rollback()
			out.Code = 400
			out.Error = err.Error()
			return &out, nil
		}
		if res.Code != 200 {
			session.Rollback()
			out.Code = 400
			out.Message = res.Message
			out.Error = res.Error
			return &out, nil
		}
		//redisclient.Set("elm:returnReason:res:"+params.OrderId, params.Reason, 0) // 将Code  存入Redis
		glog.Info("饿了么订单确认退款请求申请返回参数" + kit.JsonEncode(res))
	} else if refundOrder.ChannelId == ChannelJddjId || refundOrder.ChannelId == 420 {
		//京东到家
		etClient := et.GetExternalClient()
		defer etClient.Close()

		if refundOrder.IsCancelOrder == 1 {
			inpar := et.JddjOrderCancelOperateRequest{
				OrderId:       refundOrder.OldOrderSn,
				IsAgreed:      false,
				Operator:      params.Operationer,
				Remark:        params.Reason,
				StoreMasterId: params.StoreMasterId,
			}

			res, err := etClient.JddjOrder.JddjOrderCancelOperate(etClient.Ctx, &inpar)
			glog.Info("MtOrderRefundReject:s:"+refundOrder.OldOrderSn, "，", kit.JsonEncode(res))
			if err != nil {
				session.Rollback()
				out.Code = 400
				out.Error = err.Error()
				return &out, nil
			}
			if res.Code != 200 {
				session.Rollback()
				out.Code = 400
				out.Message = res.Message
				return &out, nil
			}
		} else {
			inpar := et.AfsOpenApproveRequest{}
			inpar.ServiceOrder = params.Refundsn
			inpar.ApproveType = 3
			inpar.RejectReason = params.Reason
			inpar.OptPin = params.Operationer
			inpar.StoreMasterId = params.StoreMasterId
			if len(inpar.OptPin) == 0 {
				inpar.OptPin = "RP"
			}

			//由用户发起的售后单是有第三方退款单号
			if len(refundOrder.OldRefundSn) > 0 {
				inpar.ServiceOrder = refundOrder.OldRefundSn
			} else {
				inpar.ServiceOrder = params.Refundsn
			}

			res, err := etClient.JddjOrder.JddjAfsOpenApprove(etClient.Ctx, &inpar)
			if err != nil {
				session.Rollback()
				out.Code = 400
				out.Error = err.Error()
				return &out, nil
			}
			if res.Code != 200 {
				session.Rollback()
				out.Code = 400
				out.Message = res.Message
				return &out, nil
			}
			glog.Info("京东订单确认退款请求申请返回参数：" + kit.JsonEncode(res))
		}

		service := RefundOrderService{}
		answer := &oc.RefundOrderAnswerRequest{
			OrderId:         params.OrderId,
			ExternalOrderId: params.OrderId,
			RefundOrderSn:   params.Refundsn,
			Reason:          params.Reason,
			ResultType:      2,
			ResultTypeNote:  "商家驳回退款请求",
			OperationType:   "商家驳回退款请求",
			OperationUser:   params.Operationer,
		}
		answerResponse, err := service.RefundOrderAnswer(nil, answer)
		if err != nil {
			glog.Error("售后单申请返回错误:" + params.Refundsn + err.Error())
			out.Message = "售后单申请返回错误"
			out.Error = err.Error()
			return &out, nil
		}
		out.Message = answerResponse.Message
		out.Error = answerResponse.Error
		out.Code = answerResponse.Code

		//redisclient.Set("jd:returnReason:res:"+params.OrderId, params.Reason, 0) // 将Code  存入Redis
	}

	session.Commit()
	//操作人的KEY
	key := "mt:return:"
	redisClient := GetRedisConn()
	redisClient.Set(key+refundOrder.OldOrderSn, params.Operationer, 300)

	out.Code = 200
	return &out, nil
}

// 订单确认退款请求：
// 1.当商家收到用户发起的部分或全额退款申请，如商家同意退款，可调用此接口操作确认退款申请。
// 注意：部分退款成功后不影响订单当前的订单状态；全额退款成功后，订单状态会变更为“订单已取消”(status=9)。
// 2.商家调用此接口同意用户退款申请的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
// 3.若商家调用接口同意用户申请的全额退款，平台会向商家系统推送退款消息，但不会推送取消订单消息；
// 若商家是在商家端后台手动操作同意全额退款，平台会推送退款消息和取消订单消息。
func (a AfterSaleService) MtOrderRefundAgree(ctx context.Context, params *oc.MtOrderRefundRequest) (*oc.ExternalResponse, error) {
	glog.Info("订单确认退款请求参数" + kit.JsonEncode(params))
	out := oc.ExternalResponse{Code: 400}

	a.session = GetDBConn().NewSession()
	defer a.session.Close()

	refundOrder := &models.RefundOrder{}
	//查询日志
	isOk, err := a.session.SQL("select  * from `refund_order` where (refund_sn= ? or old_refund_sn = ?) and refund_state in(1,5,6,7) ", params.Refundsn, params.Refundsn).Get(refundOrder)
	if err != nil || !isOk {
		if err != nil {
			glog.Error("售后单还不存在就接到了其他的回调处理:" + params.Refundsn + err.Error())
		}
		out.Message = "退款单还不存在就接到了其他的回调处理"
		out.Error = "退款单还不存在就接到了其他的回调处理"
		return &out, nil
	}

	if refundOrder.AppChannel == 0 {
		refundOrder.AppChannel = 1
	}

	//来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店
	//阿闻平台 包括阿闻商城 与 阿闻到家
	if refundOrder.ChannelId == ChannelAwenId || refundOrder.ChannelId == ChannelMallId || refundOrder.ChannelId == ChannelDigitalHealth {
		service := RefundOrderService{}
		answer := &oc.RefundOrderAnswerRequest{
			OrderId:         refundOrder.OrderSn,
			ExternalOrderId: refundOrder.OldOrderSn,
			RefundOrderSn:   refundOrder.RefundSn,
			Reason:          params.Reason,
			ResultType:      1,
			ResultTypeNote:  "商家同意退款",
			OperationType:   "商家同意退款",
			OperationUser:   params.Operationer,
		}

		//查询订单信息
		a.orderMain = GetOrderMainByOrderSn(refundOrder.OrderSn)

		//orderInfo := GetOrderMainByOrderSn(refundOrder.OrderSn)
		cancelType := 0
		if refundOrder.ChannelId == ChannelMallId && a.orderMain.IsPushTencent == 1 && a.orderMain.OrderStatus == 20 {
			cancelType = 1
		}

		glog.Info("PushOrderStatusToTencents:", kit.JsonEncode(a.orderMain))

		var answerResponse *oc.BaseResponse
		answerResponse, err = service.RefundOrderAnswer(ctx, answer)
		if err != nil {
			glog.Error(params.Refundsn, ", 售后单申请返回失败, ", err)
			out.Message = "售后单申请返回失败"
			out.Error = err.Error()
			return &out, nil
		}

		if answerResponse.Code != 200 {
			out.Message = answerResponse.Message
			return &out, nil
		}

		//秒杀订单释放库存 v2.9.10新增
		if a.orderMain.OrderType == 12 {
			go FreedSecKillStockByOrderSn(a.orderMain.OrderSn)
		}

		//电商实物订单退款释放库存
		if refundOrder.ChannelId == ChannelMallId {
			parentOrderMain := GetOrderMainByOrderSn(a.orderMain.ParentOrderSn, "old_order_sn")
			glog.Info(a.orderMain.OrderSn, ", ", parentOrderMain.OldOrderSn, ", 订单保障卡权益退回")
			_, err = a.session.Exec("UPDATE datacenter.member_card_relation set vipstatus=3,lastdate=NOW() where mallorderid=?", parentOrderMain.OldOrderSn)
			if err != nil {
				glog.Error(a.orderMain.OrderSn, ", ", parentOrderMain.OldOrderSn, ", 订单保障卡权益退回失败, ", err)
			}
		}

		//腾讯有数退款上报
		if a.orderMain.IsPushTencent == 1 {
			glog.Info("PushOrderStatusToTencent1140", a.orderMain.OldOrderSn)
			if cancelType == 1 {
				go PushOrderStatusToTencent(a.orderMain.OldOrderSn, cancelType)
			}
			go AddReturnOrderToTencent(a.orderMain.OrderSn)
		}

		out.Code = 200
		return &out, nil
	}

	//非阿闻渠道 也就是第三方会继续往下走 包括京东 饿了么  美团的订单
	a.orderMain = GetOrderMainByOldOrderSn(params.OrderId)
	a.session.Begin()

	if refundOrder.RefundState == 1 {
		_, err = a.session.Exec("update refund_order set refund_state=5 where refund_sn=?", params.Refundsn)
		if err != nil {
			a.session.Rollback()
			out.Message = err.Error()
			out.Error = err.Error()
			return &out, nil
		}
	}

	//调用各渠道同意退款接口
	switch a.orderMain.ChannelId {
	case ChannelMtId, ChannelElmId:
		err = NewChannelOrder(&models.Order{
			OrderMain: a.orderMain,
		}).AgreeRefund(params, refundOrder)
	case ChannelJddjId, 420:
		//京东到家特殊处理，逻辑有些不一样
		a.session.Commit()

		err = NewChannelOrder(&models.Order{
			OrderMain: a.orderMain,
		}).AgreeRefund(params, refundOrder)
	}
	if err != nil {
		a.session.Rollback()
		out.Message = err.Error()
		out.Error = err.Error()
		return &out, nil
	}
	a.session.Commit()

	out.Code = 200
	return &out, nil
}

// 获取订单退款信息有多个退款信息
// 退款列表
// 实物子订单子查询实物的退款商品信息 虚拟子订单只查询虚拟的订单信息
func (a AfterSaleService) OrderRetrunGetList(ctx context.Context, request *oc.RetrunOrderListRequest) (*oc.RetrunOrderListResponse, error) {
	out := oc.RetrunOrderListResponse{Code: 400}

	a.session = GetDBConn().NewSession()
	a.orderMain = new(models.OrderMain)
	defer a.session.Close()

	_, err := a.session.SQL("SELECT * FROM order_main WHERE order_sn = ?", request.OrderSn).Get(a.orderMain)
	isVirtual := a.orderMain.IsVirtual

	if err != nil {
		out.Error = "查询订单信息渠道出错" + err.Error()
		return &out, nil
	}

	//是否是第三方子订单
	var isThirdChildOrder bool
	//判断是否有子单
	//如果传过来的是子单
	childOrderSn := []string{}
	refundOrders := []models.RefundOrder{}
	//阿闻渠道
	if a.orderMain.ChannelId == ChannelMallId || a.orderMain.ChannelId == ChannelAwenId || a.orderMain.ChannelId == ChannelDigitalHealth {
		err = a.session.Table("order_main").
			Select("order_sn").
			Where("parent_order_sn=?", request.OrderSn).
			Find(&childOrderSn)
		if err != nil {
			out.Error = "查询是否有子订单出错" + err.Error()
			return &out, nil
		}
		childOrderSn = append(childOrderSn, request.OrderSn)
		a.session = a.session.Where("1=1")
		if len(childOrderSn) > 0 {
			a.session.In("order_sn", childOrderSn)
		}
		err = a.session.Find(&refundOrders)
		if err != nil {
			out.Error = "查询退款单出错" + err.Error()
			return &out, nil
		}
	} else {
		//非阿闻渠道 如果是子单
		//v6.0虚实版本之前 第三方订单没有拆单且 ParentOrderSn是等于OrderSn的
		//所以要要通过a.orderMain.ParentOrderSn != a.orderMain.OrderSn 来过滤 之前的数据 还是走主单逻辑
		if a.orderMain.ParentOrderSn != "" && a.orderMain.ParentOrderSn != a.orderMain.OrderSn {
			isThirdChildOrder = true
			//通过关联退款订单与退款订单商品信息查询出相关的退款单
			a.session = a.session.Table("refund_order").
				Alias("a").
				Select("a.*").
				Join("inner", "refund_order_third_product b", "a.refund_sn = b.refund_sn").GroupBy("a.refund_sn").
				Where("b.order_sn = ?", request.OrderSn)
			err = a.session.Find(&refundOrders)
			if err != nil {
				out.Error = "查询退款单出错" + err.Error()
				return &out, nil
			}
		} else {
			//主单直接查所有退款信息
			childOrderSn = append(childOrderSn, a.orderMain.OrderSn)
			a.session = a.session.Where("1=1")
			if len(childOrderSn) > 0 {
				a.session.In("order_sn", childOrderSn)
			}
			err = a.session.Find(&refundOrders)
			if err != nil {
				out.Error = "查询退款单出错" + err.Error()
				return &out, nil
			}
		}
	}

	//如果是第三方订单 退款的时候是传的主单
	for _, reorderinfos := range refundOrders {
		orderMain := GetOrderMainByOldOrderSn(reorderinfos.OldOrderSn)
		infoitem := oc.RetrunOrderDetail{
			RefundSn:       reorderinfos.RefundSn,
			CreateTime:     kit.GetTimeNow(reorderinfos.CreateTime),
			ChannelId:      reorderinfos.ChannelId,
			ReceiverName:   orderMain.ReceiverName,
			ReceiverPhone:  orderMain.ReceiverPhone,
			ReceiverMobile: orderMain.ReceiverMobile,
			GoodsTotal:     orderMain.GoodsTotal,
			RefundType:     reorderinfos.RefundType,
			RefundReason:   reorderinfos.RefundReason,
			RefundAmount:   reorderinfos.RefundAmount,
			RefundState:    reorderinfos.RefundState,
			ExpressName:    reorderinfos.ExpressName,
			ExpressNum:     reorderinfos.ExpressNum,
			OldOrderSn:     reorderinfos.OldOrderSn,
		}
		if orderMain.DeliveryType == 3 && (orderMain.ChannelId == ChannelAwenId || orderMain.ChannelId == ChannelDigitalHealth) {
			infoitem.ReceiverName = ""
		}

		var integral models.OrderIntegral
		a.session.Table("order_integral").Where("order_sn ='" + reorderinfos.RefundSn + "'").Get(&integral)
		infoitem.RefundIntegral = int64(integral.Integral)

		if reorderinfos.FullRefund == 1 {
			//查询是否有配送优惠,有的话需要去掉邮费
			a.orderMain = orderMain
			freightFloat := a.CalTotalChargeFreight(nil, 1)
			if orderMain.Freight-freightFloat > 0 {
				infoitem.Freight = float64(orderMain.Freight-freightFloat) / 100
			}
		}

		//查询退款日志
		retlogs := make([]models.RefundOrderLog, 0)
		err = a.session.Where("refund_sn=?", reorderinfos.RefundSn).Desc("ctime").Find(&retlogs)
		if err != nil {
			out.Error = "查询退款日志出错" + err.Error()
			return &out, nil
		}
		//填充日志信息
		for _, loginfox := range retlogs {
			retlog := &oc.RetrunOrderLog{
				Ctime:         kit.GetTimeNow(loginfox.Ctime),
				Reason:        loginfox.Reason,
				ResType:       loginfox.ResType,
				OperationType: loginfox.OperationType,
				Operationer:   loginfox.Operationer,
				NotifyType:    loginfox.NotifyType,
				Pictures:      loginfox.Pictures,
			}

			infoitem.LogList = append(infoitem.LogList, retlog)
		}

		//查询商品信息
		retgoods := make([]oc.RefundGoods, 0)

		switch reorderinfos.ChannelId {
		case ChannelAwenId, ChannelDigitalHealth:
			a.session = a.session.SQL(`
		SELECT
		b.*,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.pay_price = b.refund_price 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =? 
		GROUP BY
		b.id`, reorderinfos.RefundSn, reorderinfos.OrderSn)
		case ChannelMtId, ChannelElmId, ChannelJddjId, 420:
			//主单
			if isThirdChildOrder {
				a.session = a.session.SQL(`SELECT * FROM refund_order_third_product WHERE refund_sn =? AND order_sn=?`, reorderinfos.RefundSn, request.OrderSn)
			} else {
				a.session = a.session.SQL(`SELECT * FROM refund_order_third_product WHERE refund_sn =?`, reorderinfos.RefundSn)
			}

		default:
		}

		if err = a.session.Find(&retgoods); err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}

		//第三方订单6.0版本之前的退款单没有refund_order_third_product数据还是使用之前的方式查询
		if a.IsThirdOrder() && len(retgoods) == 0 {
			switch reorderinfos.ChannelId {
			case ChannelElmId:
				a.session = a.session.SQL(`
		SELECT
		b.*,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.sub_biz_order_id = b.sub_biz_order_id 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =?`, reorderinfos.RefundSn, reorderinfos.OrderSn)
			case ChannelJddjId, 420, ChannelMtId:
				a.session = a.session.SQL(`
		SELECT
		b.*,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.discount_price = b.product_price 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =?`, reorderinfos.RefundSn, reorderinfos.OrderSn)
			default:
			}

			if err = a.session.Find(&retgoods); err != nil {
				out.Error = "查询退款商品出错" + err.Error()
				return &out, nil
			}
		}
		//填充商品信息
		//第三方查询出来的是主商品 v6.0 需要根据退款单查询出查出子退款单信息
		var sumTotal int
		for _, goodsx := range retgoods {
			retgood := oc.RefundGoods{}
			retgood.Id = goodsx.Id
			retgood.ProductId = goodsx.ProductId
			retgood.ProductName = goodsx.ProductName
			retgood.Number = goodsx.Number
			retgood.RefundAmount = goodsx.RefundAmount
			retgood.PaymentTotal = goodsx.PaymentTotal
			retgood.Quantity = goodsx.Quantity
			retgood.Tkcount = goodsx.Tkcount
			retgood.ProductType = goodsx.ProductType
			retgood.SkuId = goodsx.SkuId
			retgood.ThirdSkuId = goodsx.ThirdSkuId
			retgood.MarkingPrice = goodsx.MarkingPrice
			retgood.ProductPrice = goodsx.ProductPrice
			// 产品要求美团渠道单价显示退款单价
			if reorderinfos.ChannelId == ChannelMtId {
				retgood.ProductPrice = goodsx.RefundPrice
			}

			//第三方订单
			sumTotal += kit.YuanToFen(cast.ToFloat64(goodsx.RefundAmount))
			infoitem.RefundGoodsOrders = append(infoitem.RefundGoodsOrders, &retgood)
		}
		//饿了么有的退款单上的总金额是0
		if (isThirdChildOrder) || infoitem.RefundAmount == "0" {
			infoitem.RefundAmount = cast.ToString(kit.FenToYuan(sumTotal))
		}
		//商品金额 == 退款商品退款金额加总
		infoitem.GoodsTotal = int32(sumTotal)

		//第三方订单最后一次退款 且退款单已经被拆 且是子订单（如果没有被拆直接读订单退款金额既可 拆了必须使用加总+运费的方式）且是实物订单
		if reorderinfos.FullRefund == 1 && isThirdChildOrder && isVirtual == 0 {
			//查询是否有配送优惠,有的话需要去掉邮费
			newFreightTotalRefund := kit.YuanToFen(cast.ToFloat64(infoitem.RefundAmount))
			newFreightTotalRefund += int(a.orderMain.Freight - a.orderMain.FreightPrivilege + a.orderMain.PackingCost)
			infoitem.RefundAmount = cast.ToString(kit.FenToYuan(newFreightTotalRefund))
		}
		//虚拟订单 运费为0
		if isVirtual == 1 {
			infoitem.Freight = 0
		}

		out.Data = append(out.Data, &infoitem)
	}
	out.Code = 200
	return &out, nil
}

// 获取退款信息(单个)
func (a AfterSaleService) OrderRetrunGet(ctx context.Context, request *oc.OrderRetrunGetRequest) (*oc.OrderRetrunGetResponse, error) {
	out := oc.OrderRetrunGetResponse{Code: 400}

	a.session = GetDBConn().NewSession()
	defer a.session.Close()

	reorderinfos := models.RefundOrder{}
	ishave_, err := a.session.Where("refund_sn=?", request.Refundsn).Get(&reorderinfos)
	if err != nil {
		out.Error = "查询退款单出错" + err.Error()
		return &out, nil
	}
	if !ishave_ {
		out.Message = "没有查询到退款单"
		return &out, nil
	}
	a.orderMain = new(models.OrderMain)
	_, err = a.session.Where("old_order_sn=?", reorderinfos.OldOrderSn).Get(a.orderMain)
	if err != nil {
		out.Error = "查询订单出错" + err.Error()
		return &out, nil
	}
	infoitem := oc.OrderRetrunInfo{}
	infoitem.Refundamount = reorderinfos.RefundAmount
	infoitem.Refundreason = reorderinfos.RefundReason
	infoitem.Createtime = kit.GetTimeNow(reorderinfos.CreateTime)
	infoitem.Refundsn = reorderinfos.RefundSn
	infoitem.OldOrderSn = reorderinfos.OldOrderSn
	infoitem.OrderSn = reorderinfos.OrderSn
	infoitem.Payamount = float32(kit.FenToYuan(a.orderMain.Total))
	infoitem.Refundtype = reorderinfos.RefundType
	infoitem.RefundState = reorderinfos.RefundState
	infoitem.ShopId = reorderinfos.ShopId
	infoitem.Expressnum = reorderinfos.ExpressNum
	infoitem.Expressname = reorderinfos.ExpressName
	infoitem.ApplyPhone = a.orderMain.ReceiverMobile
	infoitem.ApplyUser = a.orderMain.ReceiverName
	if a.orderMain.DeliveryType == 3 && (a.orderMain.ChannelId == ChannelAwenId || a.orderMain.ChannelId == ChannelDigitalHealth) {
		infoitem.ApplyUser = ""
	}
	infoitem.Freight = 0

	var integral models.OrderIntegral
	a.session.Table("order_integral").Where("order_sn ='" + reorderinfos.RefundSn + "'").Get(&integral)
	infoitem.RefundIntegral = int64(integral.Integral)

	if reorderinfos.FullRefund == 1 {
		//查询是否有配送优惠,有的话需要去掉邮费
		FreightFloat := a.FreightCal()
		if a.orderMain.Freight-FreightFloat > 0 {
			infoitem.Freight = float64(a.orderMain.Freight-FreightFloat) / 100
		}
	}

	//查询退款日志
	retlogs := make([]models.RefundOrderLog, 0)
	err = a.session.Where("refund_sn=?", reorderinfos.RefundSn).Desc("ctime").Find(&retlogs)
	if err != nil {
		out.Error = "查询退款日志出错" + err.Error()
		return &out, nil
	}
	//填充日志信息
	for _, loginfox := range retlogs {
		retlog := oc.OrderRetrunLog{}
		retlog.Reason = loginfox.Reason
		retlog.Ctime = kit.GetTimeNow(loginfox.Ctime)
		retlog.Pictures = loginfox.Pictures
		retlog.ResType = loginfox.ResType
		retlog.OperationType = loginfox.OperationType
		retlog.Operationer = loginfox.Operationer
		retlog.NotifyType = loginfox.NotifyType

		infoitem.Loglist = append(infoitem.Loglist, &retlog)
	}

	//查询商品信息
	retgoods := make([]dto.RefundOrderGoodsAndGood, 0)

	//如果是饿了么
	if reorderinfos.ChannelId == ChannelElmId {
		err = a.session.SQL(`
			select b.*,a.number,a.specs,a.promotion_id 
			from refund_order_product b 
			inner join order_product a on a.sub_biz_order_id=b.sub_biz_order_id 
			where refund_sn=? 
			and a.order_sn=?
		`, reorderinfos.RefundSn, reorderinfos.OrderSn).Find(&retgoods)
		if err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}

	} else if reorderinfos.ChannelId == ChannelJddjId || reorderinfos.ChannelId == 420 {
		err = a.session.SQL("select b.*,a.number,a.specs,a.promotion_id from refund_order_product b inner join order_product a on a.sku_id=b.sku_id and a.price=b.product_price where refund_sn=? and a.order_sn=?", reorderinfos.RefundSn, reorderinfos.OrderSn).Find(&retgoods)
		if err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}
	} else if reorderinfos.ChannelId == ChannelAwenId || reorderinfos.ChannelId == ChannelDigitalHealth {
		err = a.session.SQL("select b.*,a.number,a.specs,a.promotion_id from refund_order_product b inner join order_product a on a.sku_id=b.sku_id and a.pay_price=b.refund_price where refund_sn=? and a.order_sn=? group by b.id", reorderinfos.RefundSn, reorderinfos.OrderSn).
			Find(&retgoods)
		if err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}
	} else if reorderinfos.ChannelId == ChannelMtId {
		err = a.session.SQL("select b.*,a.number,a.specs,a.promotion_id from refund_order_product b inner join order_product a on a.sku_id=b.sku_id and a.price=b.product_price where refund_sn=? and a.order_sn=?", reorderinfos.RefundSn, reorderinfos.OrderSn).Find(&retgoods)
		if err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}
	}
	var GoodsTotal int
	//填充商品信息
	for _, goodsx := range retgoods {
		retgood := oc.RefundGoodsOrder{}
		retgood.Id = cast.ToString(goodsx.Id)
		retgood.Tkcount = int32(goodsx.Tkcount)
		retgood.BoxNum = goodsx.BoxNum
		retgood.BoxPrice = float64(goodsx.BoxPrice) / 100
		retgood.RefundPrice = float64(goodsx.RefundPrice) / 100
		retgood.FoodPrice = float32(float64(goodsx.ProductPrice) / 100)
		retgood.Spec = goodsx.Specs
		retgood.GoodsId = goodsx.SkuId //可废弃 后续统一使用skuId
		retgood.SkuId = goodsx.SkuId
		retgood.Quantity = int32(goodsx.Quantity)
		retgood.FoodName = goodsx.ProductName
		retgood.Refundorderid = goodsx.RefundSn
		retgood.Selnumber = int32(goodsx.Number)
		retgood.PromotionId = int32(goodsx.PromotionId)
		retgood.RefundAmount = goodsx.RefundAmount
		GoodsTotal += kit.YuanToFen(cast.ToFloat64(goodsx.RefundAmount))
		infoitem.RefundGoodsOrders = append(infoitem.RefundGoodsOrders, &retgood)
	}

	infoitem.GoodsTotal = kit.FenToYuan(int64(GoodsTotal))
	out.Data = &infoitem

	out.Code = 200
	return &out, nil
}

// 退款订单详情 审核退款商品明细
func (a AfterSaleService) OrderRetrunGetDetail(ctx context.Context, request *oc.RetrunOrderDetailRequest) (*oc.RetrunOrderDetailResponse, error) {
	out := oc.RetrunOrderDetailResponse{Code: 400}

	a.session = GetDBConn().NewSession()
	defer a.session.Close()

	reOrderInfos := models.RefundOrder{}
	isHave_, err := a.session.Where("refund_sn=?", request.RefundSn).Get(&reOrderInfos)
	if err != nil {
		out.Error = "查询退款单出错" + err.Error()
		return &out, nil
	}
	if !isHave_ {
		out.Message = "没有查询到退款单"
		return &out, nil
	}

	orderMain := GetOrderMainByOldOrderSn(reOrderInfos.OldOrderSn)
	infoItem := oc.RetrunOrderDetail{
		ReceiverName:   orderMain.ReceiverName,
		ReceiverPhone:  orderMain.ReceiverPhone,
		ReceiverMobile: orderMain.ReceiverMobile,
		GoodsTotal:     orderMain.GoodsTotal,

		RefundSn:     reOrderInfos.RefundSn,
		CreateTime:   kit.GetTimeNow(reOrderInfos.CreateTime),
		ChannelId:    reOrderInfos.ChannelId,
		RefundType:   reOrderInfos.RefundType,
		RefundReason: reOrderInfos.RefundReason,
		RefundAmount: reOrderInfos.RefundAmount,
		RefundState:  reOrderInfos.RefundState,
		ExpressName:  reOrderInfos.ExpressName,
		ExpressNum:   reOrderInfos.ExpressNum,
		OldOrderSn:   reOrderInfos.OldOrderSn,
		OrderSn:      reOrderInfos.OrderSn,
	}
	if orderMain.DeliveryType == 3 && (orderMain.ChannelId == ChannelAwenId || orderMain.ChannelId == ChannelDigitalHealth) {
		infoItem.ReceiverName = ""
	}

	//团长拼团团长代收时，显示团员收件人信息的信息
	if orderMain.OrderType == 15 {
		orderMainGroup := &models.OrderMainGroup{}
		if _, err := a.session.Where("parent_order_sn=?", orderMain.ParentOrderSn).Get(orderMainGroup); err != nil {
			out.Message = "查询团信息出错" + err.Error()
			return &out, nil
		} else {
			orderGroupActivity := &models.OrderGroupActivity{}
			if _, err := a.session.Where("id=?", orderMainGroup.OrderGroupActivityId).Get(orderGroupActivity); err != nil {
				out.Message = "查询团信息出错" + err.Error()
				return &out, nil
			}
			if orderGroupActivity.FinalTakeType == 1 {
				infoItem.ReceiverName = orderMainGroup.ReceiverName
				infoItem.ReceiverPhone = orderMainGroup.ReceiverMobile
				infoItem.ReceiverMobile = orderMainGroup.ReceiverMobile
			}
		}
	}

	//全额退款的时候才会算运费
	if reOrderInfos.FullRefund == 1 {
		//查询是否有配送优惠,有的话需要去掉邮费
		a.orderMain = orderMain
		freightFloat := a.CalTotalChargeFreight(nil, 1)
		if orderMain.Freight-freightFloat > 0 {
			infoItem.Freight = float64(orderMain.Freight-freightFloat) / 100
		}
	}

	//积分查询
	var integral models.OrderIntegral
	_, err = a.session.Table("order_integral").Where("order_sn ='" + reOrderInfos.RefundSn + "'").Get(&integral)
	if err != nil {
		glog.Errorf("OrderRetrunGetDetail查询积分出错！ RefundSn:%s", request.RefundSn)
		out.Message = "售后单申请返回失败"
		out.Error = err.Error()
		return &out, nil
	}
	infoItem.RefundIntegral = int64(integral.Integral)

	//查询退款日志
	retLogs := make([]models.RefundOrderLog, 0)
	err = a.session.Where("refund_sn=?", reOrderInfos.RefundSn).Desc("id").Find(&retLogs)
	if err != nil {
		glog.Errorf("OrderRetrunGetDetail查询退款日志出错！ RefundSn:%s", request.RefundSn)
		out.Message = "售后单申请返回失败"
		out.Error = err.Error()
		return &out, nil
	}
	//填充日志信息
	for _, logInfo := range retLogs {
		retLog := &oc.RetrunOrderLog{
			Ctime:         kit.GetTimeNow(logInfo.Ctime),
			Reason:        logInfo.Reason,
			ResType:       logInfo.ResType,
			OperationType: logInfo.OperationType,
			Operationer:   logInfo.Operationer,
			NotifyType:    logInfo.NotifyType,
			Pictures:      logInfo.Pictures,
		}

		infoItem.LogList = append(infoItem.LogList, retLog)
	}

	//获取脚印信息
	if err = a.session.Table("order_log").
		Select("*").
		Where("order_sn = ?", orderMain.OrderSn).
		In("log_type", models.ReverseLogSlice).
		Desc("id").
		Find(&infoItem.FootLogList); err != nil {
		glog.Errorf("退款单获取脚印失败！ order_sn:%s ", orderMain.OrderSn)
		out.Message = "售后单申请返回失败"
		out.Error = err.Error()
		return &out, nil
	}
	if len(infoItem.FootLogList) > 0 {
		for _, v := range infoItem.FootLogList {
			v.LogName = models.OrderLodMap[int(v.LogType)]
		}
	}

	//查询商品信息
	retGoods := make([]oc.RefundGoods, 0)
	switch reOrderInfos.ChannelId {
	case ChannelAwenId, ChannelDigitalHealth, ChannelIdOfflineShop:
		a.session.SQL(`
		SELECT
		b.*,
		a.order_sn,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.pay_price = b.refund_price 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =?
		GROUP BY
		b.id`, reOrderInfos.RefundSn, reOrderInfos.OrderSn)
		// 只用外面的商品进行了关联
	case ChannelMtId, ChannelJddjId, ChannelElmId, 420:
		//没有传订单号 表示查看详情 查看退款单所有的商品
		//目前前端穿的订单号OrderSn只用于此处
		if request.OrderSn == "" {
			a.session = a.session.SQL(`SELECT * FROM refund_order_third_product WHERE refund_sn = ?`, reOrderInfos.RefundSn)
		} else {
			//有订单号 则只查询某个子订单下的退款商品信息
			a.session = a.session.SQL(`SELECT * FROM refund_order_third_product WHERE refund_sn =? AND order_sn=?`, reOrderInfos.RefundSn, request.OrderSn)
		}
	default:
		out.Message = "退款单渠道不正确"
		return &out, nil
	}

	if err = a.session.Find(&retGoods); err != nil {
		out.Error = "查询退款商品出错" + err.Error()
		return &out, nil
	}
	//是否找到数据 如果没有找到数据 如果是第三方的订单很可能是6.0之前的版本的数据 需要按照之前的方式查询
	if isThirdChannel(orderMain.ChannelId) && len(retGoods) == 0 {
		switch reOrderInfos.ChannelId {
		case ChannelElmId:
			a.session.SQL(`
		SELECT
		b.*,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.sub_biz_order_id = b.sub_biz_order_id 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =?`, reOrderInfos.RefundSn, reOrderInfos.OrderSn)
		case ChannelJddjId, ChannelMtId, 420:
			a.session.SQL(`
		SELECT
		b.*,
		a.third_sku_id,
		a.number,
		a.payment_total
		FROM
		refund_order_product b
		INNER JOIN order_product a ON a.sku_id = b.sku_id 
		AND a.discount_price = b.product_price 
		WHERE
		b.refund_sn =? 
		AND a.order_sn =?`, reOrderInfos.RefundSn, reOrderInfos.OrderSn)
		default:
			out.Message = "退款单渠道不正确"
			return &out, nil
		}

		if err = a.session.Find(&retGoods); err != nil {
			out.Error = "查询退款商品出错" + err.Error()
			return &out, nil
		}

	}

	//如果是第三方的退款单  因为v6.0退款时增加了退款的商品明细，所以需要此处要查出具体的订单商品明细
	//不是总单的商品 特别是组合商品要返回组合商品的子商品明细
	//同时v6.0 对第三方订单进行了拆单 所以此处需要通过子订单商品的信息替代上面查出的退款商品信息
	//条件len(orderProducts) > 0 只有v6.0之后才会满足 之前版本因为第三方订单商品没有拆出来 按照之前的逻辑处理

	//填充商品信息
	var virtualGoodsOrderIndex = make(map[string]int)
	var virtualOrderSn []string
	var sumRefundAmount int

	for i, goods := range retGoods {
		retGood := oc.RefundGoods{}
		//是否核销 需要将所有的虚拟商品拿出来 找到对应的子订单号 然后在查看每个订单号是否核销
		retGood.Id = goods.Id
		retGood.OrderSn = goods.OrderSn
		retGood.ProductId = goods.ProductId
		retGood.ProductName = goods.ProductName
		retGood.ProductPrice = goods.ProductPrice
		retGood.Number = goods.Number
		retGood.RefundAmount = goods.RefundAmount
		retGood.PaymentTotal = goods.PaymentTotal
		retGood.Quantity = goods.Quantity
		retGood.Tkcount = goods.Tkcount
		retGood.ProductType = goods.ProductType
		retGood.SkuId = goods.SkuId
		retGood.ParentSkuId = goods.ParentSkuId
		retGood.ThirdSkuId = goods.ThirdSkuId
		if goods.ProductType == 2 {
			retGood.IsVirtual = 1
			virtualOrderSn = append(virtualOrderSn, goods.OrderSn)
			virtualGoodsOrderIndex[goods.OrderSn] = i
		}
		//虚拟商品 且是按照子订单查询详细信息的情况
		if goods.ProductType == 2 && request.OrderSn != "" {
			infoItem.Freight = 0
		}
		sumRefundAmount += kit.YuanToFen(cast.ToFloat64(goods.RefundAmount))
		infoItem.RefundGoodsOrders = append(infoItem.RefundGoodsOrders, &retGood)
	}

	infoItem.GoodsTotal = int32(sumRefundAmount)

	if orderMain.ChannelId == ChannelAwenId || orderMain.ChannelId == ChannelDigitalHealth {
		if reOrderInfos.FullRefund == 1 {
			refundAMount := kit.YuanToFen(cast.ToFloat64(infoItem.RefundAmount)) - cast.ToInt(infoItem.GoodsTotal)
			infoItem.Freight = kit.FenToYuan(refundAMount)
		} else {
			infoItem.Freight = 0
		}
	} else if isThirdChannel(orderMain.ChannelId) && request.OrderSn != "" {
		childOrderMain := GetOrderMainByOrderSn(request.OrderSn)

		//第三方渠道全部退完后会退包装费已经运费
		if reOrderInfos.FullRefund == 1 && childOrderMain.IsVirtual == 0 {
			sumRefundAmount += int(orderMain.Freight - orderMain.FreightPrivilege + orderMain.PackingCost)
		} else {
			infoItem.Freight = 0
		}

		//第三方订单 查看子订单的退款情况 需要更新退款总额
		infoItem.RefundAmount = cast.ToString(kit.FenToYuan(sumRefundAmount))
	}

	//2 虚拟订单已经核销了的数量  v6.0添加
	if len(virtualOrderSn) > 0 {
		verified, err := GetVerifiedCountByOrderSn(virtualOrderSn)
		if err != nil {
			out.Error = "查询核销状态出错出错" + err.Error()
			return &out, nil
		}
		for orderSn, count := range verified {
			infoItem.RefundGoodsOrders[virtualGoodsOrderIndex[orderSn]].VerifiedCount = count
		}
	}

	//平台服务费 平台补贴 商家结算金额
	platformService := PlatformService(request.OrderSn, request.RefundSn)
	infoItem.ServiceCharge = platformService.ServiceCharge
	infoItem.MerchantIncome = platformService.MerchantIncome
	infoItem.Allowance = platformService.Allowance

	out.Data = &infoItem
	out.Code = 200
	return &out, nil
}

// 退款管理列表
func (a AfterSaleService) RefundOrderServiceList(ctx context.Context, in *oc.RefundOrderServiceListRequest) (*oc.RefundOrderListResponse, error) {
	out := &oc.RefundOrderListResponse{Code: 400}
	Db := GetDBConn()

	if len(in.MemberId) == 0 {
		out.Message = "会员id不能为空"
		return out, nil
	}
	var RefundOrders []models.RefundOrder

	session := Db.Table("refund_order").Select("refund_order.*").
		Join("inner", "refund_order_product", "refund_order.refund_sn = refund_order_product.refund_sn").
		Join("inner", "`order_main`", "`order_main`.order_sn=refund_order.order_sn").
		Where("order_main.member_id=?", in.MemberId)

	//批量根据订单号查询
	if len(in.OrderSn) > 0 {
		session.In("`refund_order`.order_sn", in.OrderSn)
		//countSession.In("`refund_order`.order_sn", in.OrderSn)
	}
	//不展示电商的退款申请单
	session.And(" refund_order.channel_id != 5 ")
	if in.ChannelId > 0 {
		session.And("refund_order.channel_id = ?", in.ChannelId)
	}
	//根据订单号或退款单号查
	if len(in.Keywords) > 0 {
		session.And("(`refund_order`.order_sn = ? or `refund_order`.refund_sn = ?)", in.Keywords, in.Keywords)
	}
	countSession := session.Clone()
	defer countSession.Close()

	_, err := countSession.Select("count(DISTINCT `refund_order`.id) totalcount").Get(&out.TotalCount)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return nil, err
	}

	err = session.Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).
		GroupBy("refund_order.refund_sn").
		OrderBy("create_time desc").
		Find(&RefundOrders)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return out, nil
	}

	var refundSnSlice []string
	for k := range RefundOrders {
		refundSnSlice = append(refundSnSlice, RefundOrders[k].RefundSn)
	}
	var products []*models.RefundOrderProductExt
	Db.Table("refund_order_product").Alias("a").Select("a.*,b.product_id").
		Join("left", "dc_order.order_product b", "a.order_product_id = b.id").
		In("a.refund_sn", refundSnSlice).Find(&products)

	wg := new(sync.WaitGroup)
	out.Data = make([]*oc.RefundOrderList, len(RefundOrders))
	for k, i2 := range RefundOrders {
		wg.Add(1)
		i2 := i2
		k := k
		go func() {
			defer kit.CatchPanic()
			defer wg.Done()

			var productLists []*oc.RefundOrderProductList
			for _, p := range products {
				if p.RefundSn == i2.RefundSn {
					productLists = append(productLists, &oc.RefundOrderProductList{
						FoodName:     p.ProductName,
						FoodPrice:    p.ProductPrice,
						Quantity:     p.Quantity,
						Refundamount: p.RefundAmount,
						Tkcount:      p.Tkcount,
						Goodsid:      p.SkuId,
						SkuId:        p.SkuId,
						ProductId:    cast.ToInt32(p.ProductId),
					})
				}
			}

			out.Data[k] = &oc.RefundOrderList{
				Refundsn:     i2.RefundSn,
				OrderSn:      i2.OrderSn,
				Createtime:   kit.GetTimeNow(i2.CreateTime),
				Expressnum:   i2.ExpressNum,
				Refundtype:   i2.RefundType,
				RefundState:  i2.RefundState,
				Refundreason: i2.RefundReason,
				Fullrefund:   i2.FullRefund,
				Product:      productLists,
			}
		}()
	}
	wg.Wait()

	out.Code = 200
	return out, nil
}

// 修复第三方退款数据 手动脚本
// v6.0虚实组合版本之前第三方订单没有拆单 退款单也没有拆单
// 上线之后需要修正当天第三方订单的订单以及退款单数据
// 1:对第三方订单进行拆单
// 2:对未进行退款的退款单商品拆到refund_order_third_product
// todo v6.0版本上线稳定之后可删除
func (a AfterSaleService) FixThirdRefundData(ctx context.Context, in *oc.FixThirdRefundDataRequest) (*oc.BaseResponse, error) {
	out := &oc.BaseResponse{Code: 400}
	a.session = GetDBConn().NewSession()
	defer a.session.Close()
	//如果有时间参数 则跑>=时间的订单数据进行拆单
	//第三方订单那拆单
	if in.Step == 0 || in.Step == 1 {
		if in.CreateTime != "" {
			var orderMainList []*models.OrderMain
			err := a.session.SQL("SELECT * FROM  order_main WHERE create_time >=? AND channel_id IN(2,3,4) AND parent_order_sn !='' and order_sn = parent_order_sn", in.CreateTime).Find(&orderMainList)
			if err != nil {
				out.Message = "查询第三方订单数据出错" + err.Error()
				glog.Error("查询第三方订单数据出错")
				return out, err
			}
			if len(orderMainList) == 0 {
				out.Code = 200
				out.Message = "没有要拆的单"
				return out, err
			}
			for _, v := range orderMainList {
				a.orderMain = v
				a.orderDetail = GetOrderDetailByOrderSn(a.orderMain.OrderSn)
				a.orderProducts = GetOrderProductByOrderSn(a.orderMain.OrderSn, "")
				if _, err = a.ThirdSplitOrder(); err != nil {
					out.Message = "订单拆单错误" + a.orderMain.OrderSn + err.Error()
					return out, err
				}
				//更新主单的parent_order_sn 为空
				_, err = a.session.Exec("UPDATE order_main SET parent_order_sn = '' WHERE order_sn = ?", a.orderMain.OrderSn)
				if err != nil {
					out.Message = "更新主单parent_order_sn失败" + a.orderMain.OrderSn + err.Error()
					return out, err
				}
			}
		} else {
			if in.OldOrderSn == "" {
				out.Message = "缺少old_order_sn参数"
				return out, nil
			}
			a.orderMain = GetOrderMainByOldOrderSn(in.OldOrderSn)
			a.orderDetail = GetOrderDetailByOrderSn(a.orderMain.OrderSn)
			a.orderProducts = GetOrderProductByOrderSn(a.orderMain.OrderSn, "")
			if a.orderMain.Id == 0 {
				out.Message = "未查询到订单信息"
				return out, nil
			}
			if _, err := a.ThirdSplitOrder(); err != nil {
				out.Message = "订单拆单错误" + a.orderMain.OrderSn + err.Error()
				return out, err
			}
			//更新主单的parent_order_sn 为空
			_, err := a.session.Exec("UPDATE order_main SET parent_order_sn = '' WHERE old_order_sn = ?", in.OldOrderSn)
			if err != nil {
				out.Message = "更新主单parent_order_sn失败" + a.orderMain.OrderSn + err.Error()
				return out, err
			}
		}
	}

	//退款单拆单
	if in.Step == 0 || in.Step == 2 {
		if in.OldOrderSn == "" || in.RefundSn == "" {
			out.Message = "缺少old_order_sn、RefundSn参数"
			return out, nil
		}
		if a.orderMain == nil {
			a.orderMain = GetOrderMainByOldOrderSn(in.OldOrderSn)
		}
		refundOrderGood := GetRefundOrderProductByRefundSn(in.RefundSn, "")
		if len(refundOrderGood) == 0 {
			out.Code = 200
			out.Message = "没有要拆的退款单"
			return out, nil
		}
		gotSplitRefundGood, err := a.ThirdRefundGoodSplit(refundOrderGood)
		if err != nil {
			out.Message = "退款单拆单失败" + err.Error()
			return out, err
		}
		_, err = a.session.Insert(gotSplitRefundGood)
		if err != nil {
			out.Message = "退款单拆单插入数据失败" + err.Error()
			return out, err
		}
	}
	out.Code = 200
	out.Message = "处理成功"
	return out, nil
}

// RefundRePushThird 手动重推退款单
func (a AfterSaleService) RefundRePushThird(ctx context.Context, in *oc.RefundRePushThirdRequest) (out *oc.BaseResponse, e error) {
	out = &oc.BaseResponse{Code: 400}

	if len(in.RefundSn) < 1 {
		out.Message = "退款单号不能为空"
		return
	}

	refund := new(models.RefundOrder)
	db := GetDBConn()
	if a.session == nil {
		a.session = db.NewSession()
	}

	hasOrder, err := db.Where("refund_sn = ?", in.RefundSn).Get(refund)
	if err != nil {
		out.Message = err.Error()
		return
	}
	if !hasOrder {
		out.Message = "退款单号无效"
		return
	}
	if !(refund.ChannelId == 5 || (refund.OrderSource == 5 && refund.ChannelId == 1)) {
		//已经推送了 算成功 不用返回错误 方便重试
		if refund.PushThird > 0 {
			out.Code = 200
			out.Message = "退款单已经推送到第三方"
			return
		}
	}

	a.orderMain = GetOrderMainByOrderSn(refund.OrderSn)
	if a.orderMain.Id == 0 {
		out.Message = "查询不到对应的订单" + refund.OrderSn
		return
	}

	has := true
	// 1整单退款 2部分退款
	if refund.FullRefund == 2 {
		has = false
	}

	// 1同意、2拒绝
	resultType := int32(1)

	// 巨益oms成功，失败都要推送
	if a.orderMain.ChannelId == 5 {
		if refund.RefundState == 1 || refund.RefundState == 6 {
			out.Message = "进行中的退款单不允许重新推送"
			return
		}
		if refund.RefundState == 2 || refund.RefundState == 9 {
			resultType = 2
		}
	} else {
		if refund.RefundState != 3 {
			out.Message = "退款单不是成功状态"
			return
		}
	}
	glog.Info("zx推送巨益OMS状态:", resultType)
	if err := a.AllChannelRefundNotice(refund.RefundSn, has, resultType, true, a.session); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// 手动推送退款单到子龙
// 只针对第三方订单
func (a AfterSaleService) ManualRefundToZiLong(ctx context.Context, in *oc.ManualRefundToZiLongRequest) (*oc.BaseResponse, error) {
	out := &oc.BaseResponse{Code: 400}
	if in.FullRefund != 0 && in.FullRefund != 1 {
		out.Message = "FullRefund参数错误"
		return out, nil
	}
	if in.OrderSn == "" {
		out.Message = "OrderSn参数错误"
		return out, nil
	}

	a.session = GetDBConn().NewSession()
	defer a.session.Close()
	//如果有时间参数 则跑>=时间的订单数据进行拆单
	//第三方订单那拆单
	realOrder := &CommonService{
		orderMain: new(models.OrderMain),
	}
	realOrder.session = GetDBConn().NewSession()
	defer realOrder.session.Close()
	has, err := realOrder.session.Where("order_sn = ?", in.OrderSn).Get(realOrder.orderMain)
	if err != nil {
		out.Message = "查询订单信息出错"
		return out, err
	}
	if !has {
		out.Message = "未查询到订单信息"
		return out, err
	}
	var (
		sumTkCount        int
		OrderProduct      []dto.OrderProductAndRet
		RefundGoodsOrders []*oc.RefundGoodsOrder
	)

	err = realOrder.session.SQL(`select product_type,sku_id,tkcount,quantity,barcode,refund_amount,parent_sku_id,order_product_id as id  
FROM refund_order_third_product WHERE refund_sn=?`, in.RefundSn).Find(&OrderProduct)
	if err != nil {
		out.Message = "未查询退款商品"
		return out, err
	}

	for _, i2 := range OrderProduct {
		//实物商品才会推送
		if i2.ProductType == 1 {
			if i2.Tkcount == 0 {
				continue
			}
			fl := cast.ToFloat64(i2.RefundAmount)
			sumTkCount += i2.Tkcount
			goods := &oc.RefundGoodsOrder{
				GoodsId:      i2.SkuId,
				Quantity:     int32(i2.Tkcount),
				RefundAmount: fmt.Sprintf("%.2f", fl),
				OcId:         i2.Id, //子单商品id
				Barcode:      i2.BarCode,
				Id:           i2.Id, //子单商品id
			}
			RefundGoodsOrders = append(RefundGoodsOrders, goods)
		}
	}
	if sumTkCount == 0 {
		out.Message = "没有可退数量"
		return out, err
	}

	refundOrderRePush := dto.RefundOrderParameter{
		RefundGoodsOrders: RefundGoodsOrders,
		RefundSn:          in.RefundSn,
		IsCancel:          0,
		AllRefund:         int(in.FullRefund),
	}
	//没有退款单时调用取消,allRefund为是否已经全部退完
	err = realOrder.PushZiLongRefundOrder(&refundOrderRePush, true, a.session)
	if err != nil {
		out.Message = "推送子龙退款单是出错：" + err.Error()
		return out, err
	}
	out.Code = 200
	out.Message = "处理成功"
	return out, nil
}

// 平台服务费 平台补贴 商家结算金额
func PlatformService(orderSn string, refundSn string) *oc.RetrunOrderDetail {
	db := GetDBConn()
	//defer db.Close()

	infoItem := oc.RetrunOrderDetail{}
	orderInfo := models.OrderMain{}
	reOrderInfos := models.RefundOrder{}
	isHave, err := db.Where("refund_sn=?", refundSn).Get(&reOrderInfos)
	if !isHave || err != nil {
		return &infoItem
	}

	db.Table("order_main").Where("order_sn=?", orderSn).Get(&orderInfo) //子单信息
	if orderInfo.ChannelId == ChannelMtId {                             //这3个字段的算法只针对美团渠道的退款单
		var parentOrderSn = ""
		if orderInfo.ParentOrderSn == "" {
			parentOrderSn = orderInfo.OrderSn
		} else {
			parentOrderSn = orderInfo.ParentOrderSn
		}
		//查询有几条退款记录
		refundNum, _ := db.Table("refund_order").Where("order_sn=?", parentOrderSn).Count()
		//查询平台补贴
		ptCharge, _ := db.Table("order_promotion").Where("order_sn=?", parentOrderSn).Sum(models.OrderPromotion{}, "pt_charge")
		//判断是父单还是子单
		if orderInfo.ParentOrderSn == "" { //父单
			//判断是整单退还是部分退
			if reOrderInfos.FullRefund == 1 && refundNum <= 1 { //整单退
				//虚拟订单，商家结算金额=退款金额(商品支付金额)， 平台补贴和平台服务费都为0
				productPay, _ := db.Table("order_product").Where("order_sn=?", orderSn).Sum(models.OrderProduct{}, "pay_price")
				if orderInfo.IsVirtual == 1 {
					infoItem.Allowance = 0     //平台补贴
					infoItem.ServiceCharge = 0 //平台服务费
					infoItem.MerchantIncome = cast.ToInt32(productPay)
				} else {
					//平台服务费、平台补贴、商家结算金额的金额都取原正向单的金额
					infoItem.Allowance = cast.ToInt32(ptCharge)            //平台补贴
					infoItem.ServiceCharge = orderInfo.ServiceCharge       //平台服务费
					infoItem.MerchantIncome = orderInfo.ActualReceiveTotal //- cast.ToInt32(productPay)  //商家结算金额(分)
				}
			} else { //部分退
				//查询最后一次退款的退款单号
				lastRefundOrder := models.RefundOrder{}
				db.Table("refund_order").Where("order_sn=?", parentOrderSn).OrderBy("create_time desc").Limit(1, 0).Get(&lastRefundOrder)
				if lastRefundOrder.FullRefund == 1 && lastRefundOrder.RefundSn == refundSn { //最后一次退
					//1.最后1次平台服务费=平台服务费（父订单&正向单）-前面n次部分退款的平台服务费累加
					infoItem.ServiceCharge = orderInfo.ServiceCharge - BeforePlatformService(parentOrderSn, lastRefundOrder.RefundSn)
					//2.最后1次平台补贴=平台补贴（父订单&正向单）-前面n次部分退款的平台补贴累加
					infoItem.Allowance = cast.ToInt32(ptCharge) - BeforePlatformAllowance(parentOrderSn, lastRefundOrder.RefundSn, cast.ToInt32(ptCharge))
					//3.最后1次商家结算金额：商家预计收入（父订单&正向单）-前面n次部分退款的商家结算金额累加
					infoItem.MerchantIncome = orderInfo.ActualReceiveTotal - BeforeMerchantIncome(parentOrderSn, lastRefundOrder.RefundSn, cast.ToInt32(ptCharge))
				} else { //部分退款&未全单退完
					//统计商品实付金额
					paymentTotal, _ := db.Table("order_product").Where("order_sn=? and bar_code<>''", parentOrderSn).Sum(models.OrderProduct{}, "payment_total")
					if paymentTotal > 0 {
						//平台服务费（父订单&逆向单）=退给用户金额（父订单&逆向单）÷商品实付金额（父订单&正向单）×平台服务费（父订单&正向单）
						infoItem.ServiceCharge = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) * orderInfo.ServiceCharge / cast.ToInt32(paymentTotal)
						//平台补贴（父订单&逆向单）=退给用户金额（父订单&逆向单）÷商品实付金额（父订单&正向单）×平台补贴（父订单&正向单）
						infoItem.Allowance = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) * cast.ToInt32(ptCharge) / cast.ToInt32(paymentTotal)
						//商家结算金额（父订单&逆向单）=退给用户金额（父订单&逆向单）+平台补贴（父订单&逆向单）-平台服务费（父订单&逆向单）
						infoItem.MerchantIncome = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) + infoItem.Allowance - infoItem.ServiceCharge
					}
				}
			}
		} else { //子单
			///productPay, _ := db.Table("order_product").Where("order_sn=? and product_type=2", parentOrderSn).Sum(models.OrderProduct{}, "pay_price")
			//判断是整单退还是部分退
			if reOrderInfos.FullRefund == 1 && refundNum <= 1 { //整单退
				//虚拟订单，商家结算金额=退款金额(商品支付金额)， 平台补贴和平台服务费都为0
				if orderInfo.IsVirtual == 1 {
					//查虚拟单退款金额
					VirProductPay, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2 and order_sn=?", refundSn, orderSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
					infoItem.Allowance = 0     //平台补贴
					infoItem.ServiceCharge = 0 //平台服务费
					infoItem.MerchantIncome = cast.ToInt32(kit.YuanToFen(VirProductPay))
				} else {
					//查虚拟单退款金额
					VirProductPay, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2", refundSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
					//平台服务费、平台补贴、商家结算金额的金额都取原正向单的金额
					infoItem.Allowance = cast.ToInt32(ptCharge)      //平台补贴
					infoItem.ServiceCharge = orderInfo.ServiceCharge //平台服务费
					//商家结算金额（实物子订单&逆向单）=商家结算金额（父订单&逆向单）-退款金额（虚拟子订单&逆向单）
					infoItem.MerchantIncome = GetActualReceiveTotalByOrderSn(parentOrderSn) - cast.ToInt32(kit.YuanToFen(VirProductPay)) //商家结算金额(分)
				}
			} else { //部分退
				//查询最后一次退款的退款单号
				lastRefundOrder := models.RefundOrder{}
				db.Table("refund_order").Where("order_sn=?", parentOrderSn).OrderBy("create_time desc").Limit(1, 0).Get(&lastRefundOrder)
				//查订单商品总金额
				if lastRefundOrder.FullRefund == 1 && lastRefundOrder.RefundSn == refundSn { //最后一次退
					//判断是否为虚拟单
					if orderInfo.IsVirtual == 1 {
						payAmount, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2 and order_sn=?", refundSn, orderSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
						infoItem.ServiceCharge = 0
						infoItem.Allowance = 0
						infoItem.MerchantIncome = cast.ToInt32(kit.YuanToFen(payAmount))
					} else {
						//1.此处“平台服务费”的金额取“父退款单”里面的金额
						infoItem.ServiceCharge = orderInfo.ServiceCharge - BeforePlatformService(parentOrderSn, lastRefundOrder.RefundSn)
						//2.此处“平台补贴”的金额取“父退款单”里面的金额
						infoItem.Allowance = cast.ToInt32(ptCharge) - BeforePlatformAllowance(parentOrderSn, lastRefundOrder.RefundSn, cast.ToInt32(ptCharge))
						//3.最后1次商家结算金额：商家预计收入（父订单&正向单）-前面n次部分退款的商家结算金额累加
						merchantIncome := GetActualReceiveTotalByOrderSn(parentOrderSn) - BeforeMerchantIncome(parentOrderSn, lastRefundOrder.RefundSn, cast.ToInt32(ptCharge))
						payAmount, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2", refundSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
						infoItem.MerchantIncome = merchantIncome - cast.ToInt32(kit.YuanToFen(payAmount))
					}
				} else { //部分退款&未全单退完
					if orderInfo.IsVirtual == 1 {
						//VirProductPay, _ := db.Table("order_product").Where("order_sn=? and product_type=2", orderSn).Sum(models.OrderProduct{}, "pay_price")
						VirProductPay, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2", refundSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
						infoItem.Allowance = 0     //平台补贴
						infoItem.ServiceCharge = 0 //平台服务费
						infoItem.MerchantIncome = cast.ToInt32(kit.YuanToFen(VirProductPay))
					} else {
						//查组合单中虚拟单的支付金额
						payAmount, _ := db.Table("refund_order_third_product").Where("refund_sn=? and product_type=2", refundSn).Sum(models.RefundOrderThirdProduct{}, "refund_amount")
						//统计商品实付金额
						paymentTotal, _ := db.Table("order_product").Where("order_sn=? and bar_code<>''", parentOrderSn).Sum(models.OrderProduct{}, "payment_total")
						if paymentTotal > 0 {
							//此处“平台服务费”的金额取“父退款单”里面的金额
							infoItem.ServiceCharge = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) * orderInfo.ServiceCharge / cast.ToInt32(paymentTotal)
							//此处“平台补贴”的金额取“父退款单”里面的金额
							infoItem.Allowance = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) * cast.ToInt32(ptCharge) / cast.ToInt32(paymentTotal)
							//商家结算金额（实物子订单&逆向单）=父单商家结算金额（父订单&逆向单）-退款金额（虚拟子订单&逆向单）
							infoItem.MerchantIncome = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.RefundAmount))) + infoItem.Allowance - infoItem.ServiceCharge - cast.ToInt32(kit.YuanToFen(payAmount))
						}
					}
				}
			}
		}
	}
	infoItem.Allowance = int32(kit.YuanToFen(cast.ToFloat64(reOrderInfos.ActivityPtAmount)))

	return &infoItem
}

// 计算前面n次部分退款的平台服务费累加
func BeforePlatformService(parentOrderSn string, lastRefundSn string) int32 {
	db := GetDBConn()
	//defer db.Close()

	serviceCharge := int32(0)
	//查询主订单信息
	orderInfo := models.OrderMain{}
	db.Table("order_main").Where("order_sn=?", parentOrderSn).Get(&orderInfo)
	//统计商品实付金额
	paymentTotal, _ := db.Table("order_product").Where("order_sn=? and bar_code<>''", parentOrderSn).Sum(models.OrderProduct{}, "payment_total")
	//查询不包含最后一次退款的退款记录
	refundList := []*models.RefundOrder{}
	db.Table("refund_order").Where("order_sn=? and refund_sn<>?", parentOrderSn, lastRefundSn).Find(&refundList)

	if len(refundList) > 0 && paymentTotal > 0 {
		for _, v := range refundList {
			//平台服务费（父订单&逆向单）=退给用户金额（父订单&逆向单）÷商品实付金额（父订单&正向单）×平台服务费（父订单&正向单）
			serviceCharge += int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount))) * orderInfo.ServiceCharge / cast.ToInt32(paymentTotal)
		}
	}

	return serviceCharge
}

// 计算前面n次部分退款的平台补贴累加
func BeforePlatformAllowance(parentOrderSn string, lastRefundSn string, ptCharge int32) int32 {
	db := GetDBConn()
	//defer db.Close()

	allowance := int32(0)
	//统计商品实付金额
	paymentTotal, _ := db.Table("order_product").Where("order_sn=? and bar_code<>''", parentOrderSn).Sum(models.OrderProduct{}, "payment_total")
	//查询不包含最后一次退款的退款记录
	refundList := []*models.RefundOrder{}
	db.Table("refund_order").Where("order_sn=? and refund_sn<>?", parentOrderSn, lastRefundSn).Find(&refundList)

	if len(refundList) > 0 && paymentTotal > 0 {
		for _, v := range refundList {
			allowance += int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount))) * cast.ToInt32(ptCharge) / cast.ToInt32(paymentTotal)
		}
	}

	return allowance
}

// 计算前面n次部分退款的商家结算金额累加
func BeforeMerchantIncome(parentOrderSn string, lastRefundSn string, ptCharge int32) int32 {
	db := GetDBConn()
	//defer db.Close()

	merchantIncome := int32(0)
	//查询主订单信息
	orderInfo := models.OrderMain{}
	db.Table("order_main").Where("order_sn=?", parentOrderSn).Get(&orderInfo)
	//统计商品实付金额
	paymentTotal, _ := db.Table("order_product").Where("order_sn=? and bar_code<>''", parentOrderSn).Sum(models.OrderProduct{}, "payment_total")
	//查询不包含最后一次退款的退款记录
	refundList := []*models.RefundOrder{}
	db.Table("refund_order").Where("order_sn=? and refund_sn<>?", parentOrderSn, lastRefundSn).Find(&refundList)

	if len(refundList) > 0 && paymentTotal > 0 {
		for _, v := range refundList {
			//平台服务费（父订单&逆向单）=退给用户金额（父订单&逆向单）÷商品实付金额（父订单&正向单）×平台服务费（父订单&正向单）
			serviceCharge := int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount))) * orderInfo.ServiceCharge / cast.ToInt32(paymentTotal)
			//平台补贴（父订单&逆向单）=退给用户金额（父订单&逆向单）÷商品实付金额（父订单&正向单）×平台补贴（父订单&正向单）
			allowance := int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount))) * cast.ToInt32(ptCharge) / cast.ToInt32(paymentTotal)
			merchantIncome += int32(kit.YuanToFen(cast.ToFloat64(v.RefundAmount))) + allowance - serviceCharge
		}
	}

	return merchantIncome
}

// 根据主订单号计算 商家预计收入
func GetActualReceiveTotalByOrderSn(orderSn string) int32 {
	db := GetDBConn()
	actualReceiveTotal := int32(0)
	_, _ = db.Table("order_main").Where("order_sn=?", orderSn).Select("actual_receive_total").Get(&actualReceiveTotal)
	return actualReceiveTotal
}

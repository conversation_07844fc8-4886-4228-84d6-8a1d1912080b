package services

import (
	"context"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"order-center/dto"
	"order-center/proto/oc"
)

type SubscribeMessageService struct {
}

func (s SubscribeMessageService) PushTemplate(ctx context.Context, request *oc.PushTemplateRequest) (*oc.PushTemplateResponse, error) {
	glog.Info("推送消息 PushTemplate 入参：" + kit.JsonEncode(request))
	out := new(oc.PushTemplateResponse)
	out.Code = http.StatusBadRequest
	commonParam := dto.PushSubscribeMessage{
		OpenId:     request.OpenId,
		OrderSn:    request.OrderSn,
		TemplateId: request.TemplateId,
		OrgId:      request.OrgId,
	}
	switch request.PushType {
	case 1:
		err := pushRefundSuccessTemplate(commonParam, request.RefundSuccess.RefundId, request.RefundSuccess.RefundType, request.RefundSuccess.RefundAmount, request.RefundSuccess.RefundTime)
		if err != nil {
			glog.Error("PushTemplate:pushRefundSuccessTemplate:" + err.Error())
			out.Message = "发送退款成功通知"
			out.Error = err.Error()
			return out, nil
		}
	case 2:
		err := pushRefundFailTemplate(commonParam, request.RefundFail.RefundSn, request.RefundFail.RefundId, request.RefundFail.RefundType, request.RefundFail.Status, request.Remarks)
		if err != nil {
			glog.Error("PushTemplate:pushRefundFailTemplate:" + err.Error())
			out.Message = "发送退款失败通知"
			out.Error = err.Error()
			return out, nil
		}
	case 3:
		err := pushRefundStatusTemplate(commonParam, request.RefundStatus.RefundId, request.RefundStatus.Status, request.RefundStatus.RefundType, request.Remarks, request.RefundFail.RefundAmount)
		if err != nil {
			glog.Error("PushTemplate:pushRefundStatusTemplate:" + err.Error())
			out.Message = "发送退款状态通知"
			out.Error = err.Error()
			return out, nil
		}
	case 4:
		err := PreSalePayTemplate(commonParam, request.PreSalePay.StartTime, request.PreSalePay.EndTime, request.PreSalePay.Remarks, request.PreSalePay.IsVirtual)
		if err != nil {
			glog.Error("PushTemplate:preSalePayTemplate:" + err.Error())
			out.Message = "尾款支付提醒通知"
			out.Error = err.Error()
			return out, nil
		}
	}
	out.Code = http.StatusOK
	out.Message = "success"
	return out, nil
}

package models

import (
	"order-center/proto/oc"
	"time"

	"github.com/spf13/cast"
)

type RefundOrder struct {
	Id                  int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OldRefundSn         string    `xorm:"not null default '''' comment('渠道退款单号') VARCHAR(50)"`
	RefundSn            string    `xorm:"not null default '''' comment('退款单号') unique VARCHAR(50)"`
	OrderSn             string    `xorm:"not null default '''' comment('原始订单号') index VARCHAR(50)"`
	OldOrderSn          string    `xorm:"not null default '''' comment('美团订单号（原美团订单号）') index VARCHAR(50)"`
	FullRefund          int32     `xorm:"not null default 1 comment('部分退还是整单退，1整单 2部分') INT(11)"`
	RefundTypeSn        string    `xorm:"default 'NULL' comment('售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货') VARCHAR(50)"`
	ReasonCode          string    `xorm:"not null default ''01'' comment('前段没有判定，管易:007，全渠道:01') VARCHAR(50)"`
	RefundRemark        string    `xorm:"not null default '''' comment('售后单备注') VARCHAR(200)"`
	RefundType          int32     `xorm:"not null default 1 comment('申请类型:1为仅退款,2为退款退货') INT(11)"`
	RefundReason        string    `xorm:"not null default '''' comment('退款原因') VARCHAR(500)"`
	DiscountAmount      string    `xorm:"not null default ''0'' comment('优惠金额，单位元') VARCHAR(20)"`
	Freight             string    `xorm:"not null default ''0'' comment('运费，单位元') VARCHAR(20)"`
	RefundAmount        string    `xorm:"not null default ''0'' comment('退款金额，单位元') VARCHAR(20)"`
	WarehouseCode       string    `xorm:"not null default '''' comment('对应仓库id') VARCHAR(50)"`
	ShopId              string    `xorm:"not null default '''' comment('门店财务编码') VARCHAR(50)"`
	ExpressName         string    `xorm:"not null default '''' comment('退货快递名称') VARCHAR(100)"`
	ExpressNum          string    `xorm:"default 'NULL' comment('退货快递单号') VARCHAR(50)"`
	OrderSource         int32     `xorm:"not null default 1 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店') INT(11)"`
	TradeCode           string    `xorm:"not null default '''' comment('管易销售订单单据编号') VARCHAR(50)"`
	RefundState         int32     `xorm:"default 1 comment('订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款') INT(11)"`
	ApplyOpUserType     string    `xorm:"not null default '''' comment('发起退款角色(仅适用于支持退货退款的商家),1-用户,2-商家,3-客服') VARCHAR(100)"`
	ChannelId           int32     `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店') INT(11)"`
	IsCancelOrder       int32     `xorm:"not null default 0 comment('京东特有字段,1取消订单，0售后订单') INT(11)"`
	ExpectRefundTime    int64     `xorm:"not null default 0 comment('预计退款时间') index INT(10)"`
	IsVirtual           int32     `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	DelExpressInfo      string    `xorm:"not null default '''' comment('快递信息，JSON格式') VARCHAR(500)"`
	CreateTime          time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime          time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	AppChannel          int32     `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	PayRefundId         string    `xorm:"not null default '''' comment('支付中心退款单号') VARCHAR(100)"`
	PayRefundTranNo     string    `xorm:"not null default '''' comment('支付中心退款交易流水号') VARCHAR(100)"`
	PushThird           int32     `xorm:"not null default 0 comment('是否推送第三方0没推、1推了') TINYINT(4)"`
	PushThirdFailReason string    `xorm:"not null default '''' comment('推送失败原因') VARCHAR(256)"`
	RefundedTime        time.Time `xorm:" default 'NULL' comment('退款完成时间') DATETIME"`
	ActivityPtAmount    string    `xorm:" default 'NULL' comment('退款单平台优惠金额') VARCHAR(256)"`
	DeliveryPrice       string    `xorm:" default 'NULL' comment('本次运费退款金额') VARCHAR(256)"`
	ServiceType         string    `xorm:"default '''' comment('区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程') VARCHAR(10)"`
}

func (r RefundOrder) TableName() string {
	return "dc_order.refund_order"
}

type RefundOrderLists struct {
	RefundOrder       `xorm:"extends"`
	ChildChannelId    string `xorm:"not null default '''' comment('子渠道id，命名规则为channel_id+子渠道编码') VARCHAR(10)"`
	ShopName          string `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	Source            int32  `xorm:"not null default 0 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店（子龙）') TINYINT(4)"`
	WarehouseId       int32  `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	WarehouseCategory int32  `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)') index index(idx_code_category) INT(11)"`
	WarehouseName     string `xorm:"not null default '''' comment('仓库名称') VARCHAR(255)"`
	UserAgent         int32  `xorm:"not null default 0 comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它') INT(11)"`
	AppChannel        int32  `xorm:"default 1 comment('店铺类型 1新瑞鹏 2TP代运营') int(11)"`
	ChildOrderSn      string `xorm:"not null default '''' comment('第三方子订单号') VARCHAR(50)"`
	ProductType       int32  `xorm:"not null default 1 comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）') TINYINT(4)"`
	ChildRefundAmount string `xorm:"not null default ''0'' comment('退款金额，单位元') VARCHAR(20)"`
	Freight           int32  `xorm:"not null default 0 comment('总运费(分)') INT(11)"`
	FreightPrivilege  int32  `xorm:"default 0 comment('运费优惠金额(分)') INT(11)"`
	PackingCost       int32  `xorm:"not null default 0 comment('包装费(分)') INT(11)"`
	OrderIsVirtual    int32  `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	ParentOrderSn     string `xorm:"not null comment('父单号') VARCHAR(50)"`
	RefundOrderSn     string `xorm:"not null comment('退款表中的order_sn') VARCHAR(50)"`
	PushThirdOrder    int32  `xorm:"not null default 0 comment('是否推送第三方0没推、1推了') TINYINT(4)"`
	OrderStatus       int32  `xorm:"not null default 0 comment('订单状态：0已取消,10(默认)未付款,20已付款,30已完成') INT(11)"`
	IsVirtual         int32  `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	WarehouseCode     string `xorm:"not null default '''' comment('对应仓库id') VARCHAR(50)"`
	//Id              string    `xorm:"not null pk default ''uuid()'' comment('退款Id') VARCHAR(36)"`
	//Refundsn        string    `xorm:"not null comment('退款单号') VARCHAR(50)"`
	//Ordersn         string    `xorm:"not null comment('原始销售单号') VARCHAR(50)"`
	//Createtime      time.Time `xorm:" default 'current_timestamp()' comment('创建时间') TIMESTAMP"`
	//Status          string    `xorm:"default 'NULL' comment('管家婆审核状态') VARCHAR(50)"`
	//Refundtypesn    string    `xorm:"default 'NULL' comment('售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货') VARCHAR(50)"`
	//Reasoncode      string    `xorm:"default ''01'' comment('前段没有判定，管易:007，全渠道:01') VARCHAR(50)"`
	//Refundremark    string    `xorm:"default 'NULL' comment('售后单备注') VARCHAR(200)"`
	//Refundtype      int       `xorm:"not null default 1 comment('申请类型:1为退款,2为退货，默认为1') INT(11)"`
	//Discountamount  string    `xorm:"not null default ''''0'''' comment('优惠金额') VARCHAR(20)"`
	//Postfee         string    `xorm:"not null default ''''0'''' comment('运费') VARCHAR(20)"`
	//Refundreason    string    `xorm:"not null comment('退款原因') VARCHAR(500)"`
	//Warehouseincode string    `xorm:"default 'NULL' comment('对应仓库id') VARCHAR(50)"`
	//Expressname     string    `xorm:"default 'NULL' comment('退货快递名称') VARCHAR(100)"`
	//Expressnum      string    `xorm:"default 'NULL' comment('退货快递单号') VARCHAR(50)"`
	//Refundamount    string    `xorm:"default ''''0'''' comment('退款金额') VARCHAR(20)"`
	//Ordersource     int       `xorm:"not null default 1 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店') INT(11)"`
	//Tradecode       string    `xorm:"default 'NULL' comment('管易销售订单单据编号') VARCHAR(50)"`
	//RefundState     int       `xorm:"default 1 comment('订单退款状态  1:退款中 2:退款关闭 3:退款成功') INT(11)"`
	//ExpressInfo     string    `xorm:"default 'NULL' comment('快递信息，JSON格式') VARCHAR(500)"`
	//OrderFrom       int       `xorm:"default NULL comment('渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家)') TINYINT(4)"`
	//ApplyOpUserType string    `xorm:"default 'NULL' comment('发起退款角色还是商家；仅适用于支持退货退款的商家。') VARCHAR(100)"`
	//Fullrefund      int       `xorm:"default 1 comment('1整单退款 2部分退款') INT(11)"`
	//ShopId          string    `xorm:"not null default '''' comment('商户或门店id(财务编码)') VARCHAR(80)"`
	//Mtorder         string    `xorm:"default 'NULL' comment('美团单号') VARCHAR(50)"`
	//ChannelId       int       `xorm:"default 'NULL' comment('来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店') INT(11)"`
}

// 装换为 UpetDjRefundDto 实体
func (order *RefundOrder) ToUpetDjRefundDto() *oc.UpetDjRefundDto {
	var dto = new(oc.UpetDjRefundDto)
	dto.RefundNo = order.RefundSn
	dto.RefundMoney = cast.ToFloat64(order.RefundAmount)
	dto.RefundState = order.RefundState
	return dto
}

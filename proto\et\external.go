package et

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock         sync.Mutex
	Conn         *grpc.ClientConn
	Ctx          context.Context
	Cf           context.CancelFunc
	RPC          MtProductServiceClient
	MtOrder      MtOrderServiceClient
	MtStore      MtStoreServiceClient
	MtReturn     MtReturnOrderInfoClient
	ORDER        MtReturnOrderInfoClient
	ELMSTORE     ElmStoreServiceClient
	ELMPRODUCT   ElmProductServiceClient
	ELMORDER     ELMOrderServiceClient
	MPServer     MpServiceClient
	JddjProduct  JddjProductServiceClient
	JddjOrder    JddjOrderServiceClient
	JddjPlatform JddjPlatformServiceClient
	JddjBuss     JddjBussServiceClient
	JddjStore    JddjStoreServiceClient
	ShanSong     IShanSongServiceClient
	WxVideo      WxVideoServiceClient
	Delivery     DeliveryServiceClient
	WeimobCloud  WeimobCloudServiceClient
	ZiLong       ZiLongServiceClient
	DaDa         DaDaServiceClient
	Fn           FnServiceClient
	Tool         ToolServiceClient
	Ew           EwForwardServiceClient
	Blg          BlgServiceClient
	Myt          MaiyatianServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

func GetExternalClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 60*time.Second)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.external")
	//url = "172.30.1.4:11031"
	if url == "" {
		url = "127.0.0.1:11031"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("external，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewMtProductServiceClient(grpcClient.Conn)
		grpcClient.MtOrder = NewMtOrderServiceClient(grpcClient.Conn)
		grpcClient.MtStore = NewMtStoreServiceClient(grpcClient.Conn)
		grpcClient.MtReturn = NewMtReturnOrderInfoClient(grpcClient.Conn)
		grpcClient.ELMSTORE = NewElmStoreServiceClient(grpcClient.Conn)
		grpcClient.ELMPRODUCT = NewElmProductServiceClient(grpcClient.Conn)
		grpcClient.ELMORDER = NewELMOrderServiceClient(grpcClient.Conn)
		grpcClient.MPServer = NewMpServiceClient(grpcClient.Conn)
		grpcClient.JddjProduct = NewJddjProductServiceClient(grpcClient.Conn)
		grpcClient.JddjOrder = NewJddjOrderServiceClient(grpcClient.Conn)
		grpcClient.JddjPlatform = NewJddjPlatformServiceClient(grpcClient.Conn)
		grpcClient.JddjBuss = NewJddjBussServiceClient(grpcClient.Conn)
		grpcClient.JddjStore = NewJddjStoreServiceClient(grpcClient.Conn)
		grpcClient.ShanSong = NewIShanSongServiceClient(grpcClient.Conn)
		grpcClient.WxVideo = NewWxVideoServiceClient(grpcClient.Conn)
		grpcClient.Delivery = NewDeliveryServiceClient(grpcClient.Conn)
		grpcClient.WeimobCloud = NewWeimobCloudServiceClient(grpcClient.Conn)
		grpcClient.ZiLong = NewZiLongServiceClient(grpcClient.Conn)
		grpcClient.DaDa = NewDaDaServiceClient(grpcClient.Conn)
		grpcClient.Fn = NewFnServiceClient(grpcClient.Conn)
		grpcClient.Tool = NewToolServiceClient(grpcClient.Conn)
		grpcClient.Ew = NewEwForwardServiceClient(grpcClient.Conn)
		grpcClient.Blg = NewBlgServiceClient(grpcClient.Conn)
		grpcClient.Myt = NewMaiyatianServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}

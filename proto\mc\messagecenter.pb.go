// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mc/messagecenter.proto

package mc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_401e931b34289d4a, []int{0}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type SubscribeMessageRequest struct {
	Touser           string `protobuf:"bytes,1,opt,name=touser,proto3" json:"touser"`
	TemplateId       string `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	Page             string `protobuf:"bytes,3,opt,name=page,proto3" json:"page"`
	MiniprogramState string `protobuf:"bytes,5,opt,name=miniprogram_state,json=miniprogramState,proto3" json:"miniprogram_state"`
	Lang             string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang"`
	Data             string `protobuf:"bytes,7,opt,name=data,proto3" json:"data"`
	//渠道(0-阿闻 1-阿闻、2-医生端等)
	AppChannel int32 `protobuf:"varint,8,opt,name=app_channel,json=appChannel,proto3" json:"app_channel"`
	//是否极光推送
	IsJpush      int32  `protobuf:"varint,9,opt,name=is_jpush,json=isJpush,proto3" json:"is_jpush"`
	JpushContent string `protobuf:"bytes,10,opt,name=jpush_content,json=jpushContent,proto3" json:"jpush_content"`
	//主体:1-阿闻，2-极宠家，3-福码购 6-宠物saas
	OrgId                int32    `protobuf:"varint,11,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeMessageRequest) Reset()         { *m = SubscribeMessageRequest{} }
func (m *SubscribeMessageRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeMessageRequest) ProtoMessage()    {}
func (*SubscribeMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_401e931b34289d4a, []int{1}
}

func (m *SubscribeMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeMessageRequest.Unmarshal(m, b)
}
func (m *SubscribeMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeMessageRequest.Marshal(b, m, deterministic)
}
func (m *SubscribeMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeMessageRequest.Merge(m, src)
}
func (m *SubscribeMessageRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeMessageRequest.Size(m)
}
func (m *SubscribeMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeMessageRequest proto.InternalMessageInfo

func (m *SubscribeMessageRequest) GetTouser() string {
	if m != nil {
		return m.Touser
	}
	return ""
}

func (m *SubscribeMessageRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *SubscribeMessageRequest) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *SubscribeMessageRequest) GetMiniprogramState() string {
	if m != nil {
		return m.MiniprogramState
	}
	return ""
}

func (m *SubscribeMessageRequest) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *SubscribeMessageRequest) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *SubscribeMessageRequest) GetAppChannel() int32 {
	if m != nil {
		return m.AppChannel
	}
	return 0
}

func (m *SubscribeMessageRequest) GetIsJpush() int32 {
	if m != nil {
		return m.IsJpush
	}
	return 0
}

func (m *SubscribeMessageRequest) GetJpushContent() string {
	if m != nil {
		return m.JpushContent
	}
	return ""
}

func (m *SubscribeMessageRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func init() {
	proto.RegisterType((*BaseResponse)(nil), "mc.BaseResponse")
	proto.RegisterType((*SubscribeMessageRequest)(nil), "mc.SubscribeMessageRequest")
}

func init() { proto.RegisterFile("mc/messagecenter.proto", fileDescriptor_401e931b34289d4a) }

var fileDescriptor_401e931b34289d4a = []byte{
	// 330 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x91, 0xcf, 0x4a, 0x03, 0x31,
	0x10, 0xc6, 0x69, 0x75, 0xb7, 0xed, 0xb4, 0x4a, 0x0d, 0x5a, 0xa3, 0x1e, 0x2c, 0xf5, 0x52, 0x10,
	0x2a, 0xe8, 0x13, 0x68, 0x4f, 0x15, 0xbc, 0xec, 0x3e, 0xc0, 0x92, 0x66, 0x87, 0x6d, 0xa4, 0xf9,
	0x63, 0x92, 0xf5, 0xc5, 0x7c, 0x41, 0x49, 0xb2, 0x85, 0x22, 0x78, 0xfb, 0xbe, 0xdf, 0x24, 0x33,
	0x93, 0x2f, 0x30, 0x93, 0xfc, 0x49, 0xa2, 0x73, 0xac, 0x41, 0x8e, 0xca, 0xa3, 0x5d, 0x19, 0xab,
	0xbd, 0x26, 0x7d, 0xc9, 0x17, 0x05, 0x4c, 0xde, 0x98, 0xc3, 0x02, 0x9d, 0xd1, 0xca, 0x21, 0x21,
	0x70, 0xca, 0x75, 0x8d, 0xb4, 0x37, 0xef, 0x2d, 0xb3, 0x22, 0x6a, 0x42, 0x61, 0xd0, 0x5d, 0xa7,
	0xfd, 0x79, 0x6f, 0x39, 0x2a, 0x0e, 0x96, 0x5c, 0x42, 0x86, 0xd6, 0x6a, 0x4b, 0x4f, 0x22, 0x4f,
	0x66, 0xf1, 0xd3, 0x87, 0xeb, 0xb2, 0xdd, 0x3a, 0x6e, 0xc5, 0x16, 0x3f, 0xd2, 0xd1, 0x02, 0xbf,
	0x5a, 0x74, 0x9e, 0xcc, 0x20, 0xf7, 0xba, 0x75, 0x68, 0xe3, 0x84, 0x51, 0xd1, 0x39, 0x72, 0x0f,
	0x63, 0x8f, 0xd2, 0xec, 0x99, 0xc7, 0x4a, 0xd4, 0xdd, 0x1c, 0x38, 0xa0, 0x4d, 0x1d, 0x16, 0x33,
	0x61, 0x83, 0x34, 0x29, 0x6a, 0xf2, 0x08, 0x17, 0x52, 0x28, 0x61, 0xac, 0x6e, 0x2c, 0x93, 0x95,
	0xf3, 0xcc, 0x23, 0xcd, 0xe2, 0x81, 0xe9, 0x51, 0xa1, 0x0c, 0x3c, 0x34, 0xd8, 0x33, 0xd5, 0xd0,
	0x3c, 0x35, 0x08, 0x3a, 0xb0, 0x9a, 0x79, 0x46, 0x07, 0x89, 0x05, 0x1d, 0x36, 0x61, 0xc6, 0x54,
	0x7c, 0xc7, 0x94, 0xc2, 0x3d, 0x1d, 0xc6, 0x20, 0x80, 0x19, 0xb3, 0x4e, 0x84, 0xdc, 0xc0, 0x50,
	0xb8, 0xea, 0xd3, 0xb4, 0x6e, 0x47, 0x47, 0xb1, 0x3a, 0x10, 0xee, 0x3d, 0x58, 0xf2, 0x00, 0x67,
	0x91, 0x57, 0x5c, 0x2b, 0x8f, 0xca, 0x53, 0x88, 0x8d, 0x27, 0x11, 0xae, 0x13, 0x23, 0x57, 0x90,
	0x6b, 0xdb, 0x84, 0x57, 0x8e, 0xe3, 0xed, 0x4c, 0xdb, 0x66, 0x53, 0x3f, 0x97, 0x70, 0xde, 0x65,
	0x55, 0xa2, 0xfd, 0x16, 0x1c, 0xc9, 0x2b, 0x4c, 0xff, 0xc6, 0x48, 0xee, 0x56, 0x92, 0xaf, 0xfe,
	0x09, 0xf7, 0x76, 0x1a, 0x8a, 0xc7, 0xdf, 0xb9, 0xcd, 0xe3, 0x4f, 0xbf, 0xfc, 0x06, 0x00, 0x00,
	0xff, 0xff, 0xb9, 0x0c, 0x51, 0xfc, 0x03, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MessageServiceClient is the client API for MessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MessageServiceClient interface {
	//发送订阅消息
	SubscribeMessage(ctx context.Context, in *SubscribeMessageRequest, opts ...grpc.CallOption) (*BaseResponse, error)
}

type messageServiceClient struct {
	cc *grpc.ClientConn
}

func NewMessageServiceClient(cc *grpc.ClientConn) MessageServiceClient {
	return &messageServiceClient{cc}
}

func (c *messageServiceClient) SubscribeMessage(ctx context.Context, in *SubscribeMessageRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mc.MessageService/SubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageServiceServer is the server API for MessageService service.
type MessageServiceServer interface {
	//发送订阅消息
	SubscribeMessage(context.Context, *SubscribeMessageRequest) (*BaseResponse, error)
}

// UnimplementedMessageServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMessageServiceServer struct {
}

func (*UnimplementedMessageServiceServer) SubscribeMessage(ctx context.Context, req *SubscribeMessageRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeMessage not implemented")
}

func RegisterMessageServiceServer(s *grpc.Server, srv MessageServiceServer) {
	s.RegisterService(&_MessageService_serviceDesc, srv)
}

func _MessageService_SubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).SubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mc.MessageService/SubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).SubscribeMessage(ctx, req.(*SubscribeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MessageService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mc.MessageService",
	HandlerType: (*MessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubscribeMessage",
			Handler:    _MessageService_SubscribeMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mc/messagecenter.proto",
}

/*
巨益对接相关实体：退货单信息
*/
package dto

//oms返回
type OmsBaseResponse struct {
	//响应码
	Code string `json:"sub_code"`
	//响应结果success|failure
	Flag string `json:"flag"`
	//响应信息
	Message string `json:"sub_message"`
}

// 售后订单同步,售后单信息
type OmsAfterorderOrder struct {
	//退款申请列表
	RefundApplyOrders []RefundApplyOrders `json:"refundApplyOrders"`
}

//退款申请
type RefundApplyOrders struct {
	//店铺编码
	ShopCode string `json:"shopCode,omitempty"`
	//退款申请
	MallRefundStatus string `json:"mallRefundStatus,omitempty"`
	//买家昵称
	BuyerNick string `json:"buyerNick,omitempty"`
	//申请时间
	AppliedTime string `json:"appliedTime,omitempty"`
	//是否退货
	HasGoodReturn string `json:"hasGoodReturn,omitempty"`
	//修改时间
	ModifyTime string `json:"modifyTime,omitempty"`
	//快递单号
	ExpressNo string `json:"expressNo,omitempty"`
	//快递公司名称
	ExpressName string `json:"expressName,omitempty"`
	//申请数量
	Quantity int32 `json:"quantity,omitempty"`
	//申请原因
	Reason string `json:"reason,omitempty"`
	//申请金额
	ApplyAmount string `json:"applyAmount,omitempty"`
	//平台退款申请ID
	RefundId string `json:"refundId,omitempty"`
	//平台交易号
	TradeId string `json:"tradeId,omitempty"`
	//是否整单退款
	IsWhole string `json:"isWhole,omitempty"`
	//商品编码
	GoodsCode string `json:"goodsCode,omitempty"`
	//商品名称
	GoodsName string `json:"goodsName,omitempty"`
	// skuId(与SkuCode二选一)
	SkuId string `json:"skuId,omitempty"`
	//货号
	SkuCode string `json:"skuCode,omitempty"`
	//规格名称
	SkuName string `json:"skuName,omitempty"`
}

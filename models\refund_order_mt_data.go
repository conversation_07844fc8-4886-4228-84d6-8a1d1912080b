package models

import (
	"time"
)

type RefundOrderMtData struct {
	Id            int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	RefundSn      string    `xorm:"not null comment('退款单号') index VARCHAR(50)"`
	Data          string    `xorm:"not null comment('退款申请数据 Json 格式') TEXT"`
	ProcessStatus int32     `xorm:"not null comment('处理状态, 1待处理, 2处理失败, 3已处理') index INT(11)"`
	OrderSn       string    `xorm:"not null comment('美团订单号') VARCHAR(50)"`
	CreateTime    time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime    time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	NotifyType    string    `xorm:"not null comment('通知类型：apply,agree,cancel') VARCHAR(100)"`
	Reason        string    `xorm:"default 'NULL' VARCHAR(500)"`
}

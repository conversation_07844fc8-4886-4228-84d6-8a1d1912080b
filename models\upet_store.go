package models

type UpetStore struct {
	StoreId                   int32   `xorm:"not null pk autoincr comment('店铺索引id') INT(11)"`
	StoreName                 string  `xorm:"not null comment('店铺名称') index VARCHAR(50)"`
	GradeId                   int     `xorm:"not null comment('店铺等级') INT(11)"`
	MemberId                  int     `xorm:"not null comment('会员id') INT(11)"`
	MemberName                string  `xorm:"not null comment('会员名称') VARCHAR(50)"`
	SellerName                string  `xorm:"comment('店主卖家用户名') VARCHAR(50)"`
	ScId                      int     `xorm:"not null default 0 comment('店铺分类') index INT(11)"`
	StoreCompanyName          string  `xorm:"comment('店铺公司名称') VARCHAR(50)"`
	ProvinceId                int     `xorm:"not null default 0 comment('店铺所在省份ID') MEDIUMINT(8)"`
	AreaInfo                  string  `xorm:"not null default '' comment('地区内容，冗余数据') VARCHAR(100)"`
	StoreAddress              string  `xorm:"not null default '' comment('详细地区') VARCHAR(100)"`
	StoreZip                  string  `xorm:"not null default '' comment('邮政编码') VARCHAR(10)"`
	StoreState                int     `xorm:"not null default 2 comment('店铺状态，0关闭，1开启，2审核中') index TINYINT(1)"`
	StoreCloseInfo            string  `xorm:"comment('店铺关闭原因') VARCHAR(255)"`
	StoreSort                 int     `xorm:"not null default 0 comment('店铺排序') INT(11)"`
	StoreTime                 string  `xorm:"not null comment('店铺时间') VARCHAR(10)"`
	StoreEndTime              string  `xorm:"comment('店铺关闭时间') VARCHAR(10)"`
	StoreLabel                string  `xorm:"comment('店铺logo') VARCHAR(255)"`
	StoreBanner               string  `xorm:"comment('店铺横幅') VARCHAR(255)"`
	StoreAvatar               string  `xorm:"comment('店铺头像') VARCHAR(150)"`
	StoreKeywords             string  `xorm:"not null default '' comment('店铺seo关键字') VARCHAR(255)"`
	StoreDescription          string  `xorm:"not null default '' comment('店铺seo描述') VARCHAR(255)"`
	StoreQq                   string  `xorm:"comment('QQ') VARCHAR(50)"`
	StoreWw                   string  `xorm:"comment('阿里旺旺') VARCHAR(50)"`
	StorePhone                string  `xorm:"comment('商家电话') VARCHAR(20)"`
	StoreZy                   string  `xorm:"comment('主营商品') TEXT"`
	StoreDomain               string  `xorm:"comment('店铺二级域名') VARCHAR(50)"`
	StoreDomainTimes          int     `xorm:"default 0 comment('二级域名修改次数') TINYINT(1)"`
	StoreRecommend            int     `xorm:"not null default 0 comment('推荐，0为否，1为是，默认为0') TINYINT(1)"`
	StoreTheme                string  `xorm:"not null default 'default' comment('店铺当前主题') VARCHAR(50)"`
	StoreCredit               int     `xorm:"not null default 0 comment('店铺信用') INT(10)"`
	StoreDesccredit           float32 `xorm:"not null default 0 comment('描述相符度分数') FLOAT"`
	StoreServicecredit        float32 `xorm:"not null default 0 comment('服务态度分数') FLOAT"`
	StoreDeliverycredit       float32 `xorm:"not null default 0 comment('发货速度分数') FLOAT"`
	StoreCollect              int     `xorm:"not null default 0 comment('店铺收藏数量') INT(10)"`
	StoreSlide                string  `xorm:"comment('店铺幻灯片') TEXT"`
	StoreSlideUrl             string  `xorm:"comment('店铺幻灯片链接') TEXT"`
	StoreStamp                string  `xorm:"comment('店铺印章') VARCHAR(200)"`
	StorePrintdesc            string  `xorm:"comment('打印订单页面下方说明文字') VARCHAR(500)"`
	StoreSales                int     `xorm:"not null default 0 comment('店铺销量') INT(10)"`
	StorePresales             string  `xorm:"comment('售前客服') TEXT"`
	StoreAftersales           string  `xorm:"comment('售后客服') TEXT"`
	StoreWorkingtime          string  `xorm:"comment('工作时间') VARCHAR(100)"`
	StoreFreeMaxPrice         float64 `xorm:"not null default 0.00 comment('大于0表示（该订单不免运费，额度为该值）') DECIMAL(10,2)"`
	StoreFreePrice            float64 `xorm:"not null default 0.00 comment('超出该金额免运费，大于0才表示该值有效') DECIMAL(10,2)"`
	StoreDecorationSwitch     int     `xorm:"not null default 0 comment('店铺装修开关(0-关闭 装修编号-开启)') INT(10)"`
	StoreDecorationOnly       int     `xorm:"not null default 0 comment('开启店铺装修时，仅显示店铺装修(1-是 0-否') TINYINT(1)"`
	StoreDecorationImageCount int     `xorm:"not null default 0 comment('店铺装修相册图片数量') INT(10)"`
	IsOwnShop                 int     `xorm:"not null default 0 comment('是否自营店铺 1是 0否') TINYINT(3)"`
	BindAllGc                 int     `xorm:"not null default 0 comment('自营店是否绑定全部分类 0否1是') TINYINT(3)"`
	StoreVrcodePrefix         string  `xorm:"comment('商家兑换码前缀') CHAR(3)"`
	MbTitleImg                string  `xorm:"comment('手机店铺 页头背景图') VARCHAR(150)"`
	MbSliders                 string  `xorm:"comment('手机店铺 轮播图链接地址') TEXT"`
	LeftBarType               int     `xorm:"not null default 1 comment('店铺商品页面左侧显示类型 1默认 2商城相关分类品牌商品推荐') TINYINT(3)"`
	DeliverRegion             string  `xorm:"comment('店铺默认配送区域') VARCHAR(50)"`
	DefaultIm                 int     `xorm:"not null comment('默认客服帐号') INT(10)"`
	OrderPriceModify          int     `xorm:"not null default 0 comment('是否允许修改订单金额') TINYINT(1)"`
	OrderPaidRefund           int     `xorm:"not null default 0 comment('是否允许取消已付款订单') TINYINT(1)"`
	IsChainAllow              int     `xorm:"default 1 comment('是否允许添加门店') TINYINT(1)"`
	AllowChainCount           int     `xorm:"default 5 comment('允许添加门店数量') INT(11)"`
	EarnestMoney              int     `xorm:"default 0 comment('保证金金额[不设置则按照平台统一规则收取]') INT(11)"`
	SpecialBusiness           int     `xorm:"not null default 0 comment('特殊行业资质:0无，1医疗行业，2食品行业，3书籍音像，4酒类制品') TINYINT(2)"`
	StorePresalesOnline       string  `xorm:"default '' comment('接入客服提示语') VARCHAR(255)"`
	StorePresalesOffline      string  `xorm:"default '' comment('客服离线状态提示语') VARCHAR(255)"`
	StoreServiceType          int     `xorm:"comment('店铺服务类型 1：全渠道 2：管易 3：门店') TINYINT(1)"`
}

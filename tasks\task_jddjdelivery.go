package tasks

import (
	"context"
	"strconv"
	"strings"
	"time"

	"order-center/models"
	"order-center/proto/et"
	"order-center/services"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type TaskJddjDelivery struct {
	services.BaseService
}

// 定时任务更新京东到家，美团，阿闻拣货，每10秒执行一次
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("@every 10s", func() {
		service := TaskJddjDelivery{}
		service.TaskAutoJddjDelivery()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}

	taskMt := cron.New(cron.WithSeconds())
	if _, err := taskMt.AddFunc("@every 10s", func() {
		service := TaskJddjDelivery{}
		service.TaskAutoMtPick()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		taskMt.Start()
	}
}

func (s TaskJddjDelivery) TaskAutoJddjDelivery() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:AutoJddjDelivery"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	db := services.GetDBConn()

	var orderList []*models.OrderMain
	err := db.SQL(`
		SELECT order_main.* 
		FROM order_main 
		inner join order_detail on order_main.order_sn=order_detail.order_sn
		WHERE is_picking=0 
		AND channel_id=4 
		AND order_status_child=20102 
		AND order_status=20 
		AND parent_order_sn=''
		AND is_virtual=0
		AND push_third_order=1 
		AND accept_time<=DATE_SUB(SYSDATE(),INTERVAL 5 MINUTE)
	`).Find(&orderList)
	if err != nil {
		glog.Error("数据库查询失败，", err)
	}
	if len(orderList) == 0 {
		return
	}

	//todo tp elm 此处的etClient没用 删除就好
	etClient := et.GetExternalClient()
	defer etClient.Close()

	session := db.NewSession()
	defer session.Close()

	var orderLogs []*models.OrderLog
	for _, i := range orderList {

		storeMasterId, err := services.GetAppChannelByOrderSn(i.OldOrderSn)
		if err != nil {
			glog.Error("TaskAutoJddjDelivery,", "GetAppChannelByOrderSn,", i.OldOrderSn, i.OrderSn)
			continue
		}

		glog.Info("定时任务更新京东到家拣货执行开始", "外部单号："+i.OldOrderSn+" 订单号："+i.OrderSn)

		session.Begin()

		_, err = session.Where("(order_sn =? or parent_order_sn=? ) and is_virtual=0", i.OrderSn, i.OrderSn).Update(&models.OrderMain{
			OrderStatusChild: i.OrderStatusChild,
		})
		if err != nil {
			session.Rollback()
			glog.Error("定时任务更新京东到家拣货失败1！", i.OldOrderSn)
			continue
		}

		//子订单集合
		var orderListchild []string
		err = session.SQL("select order_sn from order_main where order_sn =? or parent_order_sn=?", i.OrderSn, i.OrderSn).Find(&orderListchild)
		if err != nil {
			session.Rollback()
			glog.Error("查询子订单报错！", i.OldOrderSn)
			continue
		}

		_, err = session.Where("order_sn in (" + "'" + strings.Join(orderListchild, "','") + "')").Update(&models.OrderDetail{
			IsPicking:   1,
			PickingTime: time.Now(),
		})
		if err != nil {
			session.Rollback()
			glog.Error("定时任务更新京东到家拣货失败2！", i.OldOrderSn)
			continue
		}

		session.Commit()

		orderLogs = append(orderLogs, []*models.OrderLog{
			{
				OrderSn: i.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: i.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		}...)

		switch i.LogisticsCode {
		case "2938": //自配
			glog.Info("调用京东拣货完成自配接口：", i.OrderSn)
			err = services.JddjOrderSerllerDelivery(i.OldOrderSn, storeMasterId)
		case "9966": //京东众包
			glog.Info("调用京东拣货完成众包接口：", i.OrderSn)
			err = services.JddjOrderJDZBDelivery(i.OldOrderSn, storeMasterId)
		case "9999": //拣货完成且顾客自提接口
			glog.Info("调用京东拣货完成自提接口：", i.OrderSn)
			err = services.JddjOrderSelfMention(i.OldOrderSn, storeMasterId)
		default:
			err = nil
		}

		if err != nil {
			glog.Error("定时任务推送京东到家拣货完成失败！", err, ", ", i.OldOrderSn, ", ", i.LogisticsCode)
			continue
		}
	}

	services.SaveOrderLog(orderLogs)
}

func (s TaskJddjDelivery) TaskAutoMtPick() {
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:AutoMtPick"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	db := services.GetDBConn()

	// todo.v6.0推美团拣货
	// bug 美团订单定时任务手动拣货通知美团错误
	// orderList  会获取子单，下面走手动拣货的时候，会把子单推给美团，美团那边没有子单，就会报错
	// 配送方式为商家自配不推美团拣货

	var orderList []*models.OrderMain
	err := db.SQL(`
		SELECT order_main.*
		FROM order_main
		INNER JOIN order_detail on order_main.order_sn=order_detail.order_sn
		WHERE
       is_picking=0
		AND channel_id in(1,2,3,9)
		AND delivery_type != 5 
		AND (order_status_child=20102 or order_main.channel_id=3)
		AND parent_order_sn=''
		AND is_virtual=0
		AND order_status=20
		AND accept_time<=DATE_SUB(SYSDATE(),INTERVAL 3 MINUTE)
		AND org_id!=6
	`).Find(&orderList)

	if err != nil {
		glog.Error("数据库查询失败，", err)
	}
	if len(orderList) == 0 {
		return
	}

	etClient := et.GetExternalClient()
	defer etClient.Close()

	session := db.NewSession()
	defer session.Close()

	ctx := context.Background()
	var (
		orderId   int64
		res       *et.ExternalResponse
		orderLogs []*models.OrderLog
	)
	for _, i := range orderList {
		glog.Info("定时任务更新美团拣货执行开始", "外部单号："+i.OldOrderSn+" 订单号："+i.OrderSn)

		session.Begin()

		_, err = session.Where("(order_sn =? or parent_order_sn=? ) and is_virtual=0", i.OrderSn, i.OrderSn).Update(&models.OrderMain{
			OrderStatusChild: i.OrderStatusChild,
		})

		//_, err = session.In("order_sn", []string{i.OrderSn, i.ParentOrderSn}).Update(&models.OrderMain{
		//	OrderStatusChild: i.OrderStatusChild,
		//})
		if err != nil {
			session.Rollback()
			glog.Error("定时任务更新美团拣货失败1！", i.OldOrderSn)
			continue
		}
		//子订单集合
		var orderListchild []string
		err = session.SQL("select order_sn from order_main where order_sn =? or parent_order_sn=?", i.OrderSn, i.OrderSn).Find(&orderListchild)
		if err != nil {
			session.Rollback()
			glog.Error("查询子订单报错！", i.OldOrderSn)
			continue
		}
		if i.ChannelId == services.ChannelElmId {

			orderModel := services.GetOrderByOrderSn(i.OldOrderSn, "order_main.*,order_detail.accept_time,order_detail.push_delivery,order_detail.push_third_order,order_detail.is_picking")
			if orderModel.OrderStatus == 0 || orderModel.IsPicking == 1 {
				continue
			}
			err = services.NewChannelOrder(orderModel).PickOrder()

		}

		_, err = session.Where("order_sn in (" + "'" + strings.Join(orderListchild, "','") + "')").Update(&models.OrderDetail{
			IsPicking:   1,
			PickingTime: time.Now(),
		})
		if err != nil {
			session.Rollback()
			glog.Error("定时任务更新美团拣货失败2！", i.OldOrderSn)
			continue
		}

		session.Commit()

		orderLogs = append(orderLogs, []*models.OrderLog{
			{
				OrderSn: i.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: i.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		}...)

		if i.ChannelId == services.ChannelMtId {
			if i.LogisticsCode == "1001" || i.LogisticsCode == "2002" || i.LogisticsCode == "3001" {
				orderId, err = strconv.ParseInt(i.OldOrderSn, 10, 64)
				if err != nil {
					glog.Error("定时任务更新美团拣货失败！(转换美团订单号)", "外部单号："+i.OldOrderSn+" 订单号："+i.OrderSn)
				}

				storeMasterId, err := services.GetAppChannelByOrderSn(cast.ToString(orderId))
				if err != nil {
					glog.Error("TaskAutoMtPick,", "GetAppChannelByOrderSn", orderId, err)
				}
				res, err = etClient.MtOrder.OrderPreparationMealComplete(ctx, &et.PreparationMealComplete{
					OrderId:       orderId,
					StoreMasterId: storeMasterId,
				})
				if err != nil {
					glog.Error("外部单号："+i.OldOrderSn+" 订单号："+i.OrderSn, "，定时任务拣货通知美团错误！", err)
				}
				if res.Code != 200 {
					glog.Info("外部单号："+i.OldOrderSn+" 订单号："+i.OrderSn, "，定时任务手动拣货通知美团错误！", res.Error)
				}
			}

		}

		//数据中心，后台通知状态修改
		go services.MessageUpdate(i.OrderSn)

		// 移除redis备货完成的订单
		if (i.ChannelId == services.ChannelAwenId || i.ChannelId == services.ChannelDigitalHealth) && i.DeliveryType == 3 {
			redisConn.ZRem(services.SelfCollectionPicking, i.OrderSn)
		}
	}

	if len(orderLogs) > 0 {
		services.SaveOrderLog(orderLogs)
	}
}

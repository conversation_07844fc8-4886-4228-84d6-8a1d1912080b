package services

import (
	"context"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

func TestIntegral_IntegralQuery(t *testing.T) {
	type args struct {
		ctx context.Context
		req *oc.OrderIntegralQueryRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.OrderIntegralQueryRequest
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestIntegralQuery",
			args: args{
				ctx: nil,
				req: &oc.OrderIntegralQueryRequest{
					PageIndex: 1,
					PageSize:  20,
					StartTime: "2000-06-01 11:27:39",
					EndTime:   "2021-06-30 11:27:39",
					ShopName:  "优宠商城官方自营",
					//UserName: "客户40366081",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := OrderIntegralService{}
			got, err := p.OrderIntegralQuery(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IntegralQuery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IntegralQuery() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderIntegralService_IntegralSynchronize(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *oc.MemberIntegralRecordRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "订单积分同步",
			args: args{
				ctx: context.Background(),
				request: &oc.MemberIntegralRecordRequest{
					OldOrderSn:    "4000000000440932",
					OrderSn:       "50000000330",
					OrderAmount:   7000,
					ShopCode:      "CX0004",
					Integral:      70,
					IntegralType:  72,
					CreateTime:    kit.GetTimeNow(),
					CurrentStatus: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := OrderIntegralService{}
			got, err := p.IntegralSynchronize(tt.args.ctx, tt.args.request)
			if err != nil {
				t.Errorf("IntegralSynchronize() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

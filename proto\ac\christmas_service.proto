syntax = "proto3";

package ac;

service ChristmasService{
  // 参加抽奖
  rpc Join(ChristmasJoinRequest) returns (ChristmasJoinResponse);
  // 订单列表
  rpc Orders(ChristmasOrdersRequest) returns(ChristmasOrdersResponse);
  // 最近礼品列表
  rpc RecentAwards(ChristmasEmptyRequest) returns (ChristmasRecentAwardsResponse);
}

message ChristmasEmptyRequest{
}

message ChristmasResponse{
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
}

// 参加抽奖
message ChristmasJoinRequest {
  // 1摇一摇、2许愿
  int32 batch = 1;
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 2;
}

message ChristmasJoinAwardData {
  // 奖品id
  int32 id = 1;
  // 奖品名称
  string name = 2;
  // 奖品类型 1商城券、2门店券、3实物
  int32 type = 3;
  // 奖品订单Id
  int32 orderId = 4;
}

// 参加抽奖返回信息
message ChristmasJoinResponse{
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
  // 奖品信息
  ChristmasJoinAwardData data = 3;
}

message ChristmasOrdersRequest{
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 2;
}

message ChristmasOrdersResponse{
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
  // 订单详情
  repeated ChristmasOrderData data = 3;
}

message ChristmasOrderData{
  // 订单id
  int32 id = 1;
  // 收件人姓名
  string name = 2;
  // 收件地址
  string address = 3;
  // 收件号码
  string mobile = 4;
  // 宠物类型 1猫  2狗
  int32 pet_type = 5;
  // 奖品id
  int32 award_id = 6;
  // 奖品类型 1商城券、2门店券、3实物
  int32 award_type = 7;
  // 奖品名称
  string award_name = 8;
  // 获取时间
  string create_time = 9;
}

message ChristmasRecentAwardData{
  // 手机号
  string mobile = 1;
  // 礼品名称
  string name = 2;
  // 数量
  int32 qty = 3;
}

message ChristmasRecentAwardsResponse {
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
  // 最近获的礼品信息
  repeated ChristmasRecentAwardData data = 3;
}
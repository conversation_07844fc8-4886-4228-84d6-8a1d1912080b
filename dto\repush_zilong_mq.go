package dto

import (
	"order-center/proto/oc"
)

// 重推子龙类型
const (
	RePushZiLongOrder       = iota + 1 // 重推子龙正向订单
	RePushZiLongRefundOrder            // 重推子龙逆向订单
)
const RePush_CountLimit = 3 // 重推次数限制
// mq订单重新推送子龙
type MqRePushOrderToZiLong struct {
	Count    int    `json:"count"`
	Type     int    `json:"type"`
	OrderSn  string `json:"order_sn"`
	RefundSn string `json:"refund_sn"`
}

type RefundOrderParameter struct {
	RefundGoodsOrders []*oc.RefundGoodsOrder `json:"refund_goods_orders"`
	RefundSn          string                 `json:"refund_sn"`
	IsCancel          int                    `json:"isCancel"`
	AllRefund         int                    `json:"all_refund"`
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/seckill_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type RetCode int32

const (
	RetCode_UNKNOWN       RetCode = 0
	RetCode_SUCCESS       RetCode = 200
	RetCode_ERROR         RetCode = 400
	RetCode_InvalidParams RetCode = 1001
)

var RetCode_name = map[int32]string{
	0:    "UNKNOWN",
	200:  "SUCCESS",
	400:  "ERROR",
	1001: "InvalidParams",
}

var RetCode_value = map[string]int32{
	"UNKNOWN":       0,
	"SUCCESS":       200,
	"ERROR":         400,
	"InvalidParams": 1001,
}

func (x RetCode) String() string {
	return proto.EnumName(RetCode_name, int32(x))
}

func (RetCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{0}
}

// 获取秒杀活动实时库存 请求
type GetSeckillProductStockRequest struct {
	// 秒杀活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 商品sku id
	SkuId                int32    `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeckillProductStockRequest) Reset()         { *m = GetSeckillProductStockRequest{} }
func (m *GetSeckillProductStockRequest) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductStockRequest) ProtoMessage()    {}
func (*GetSeckillProductStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{0}
}

func (m *GetSeckillProductStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductStockRequest.Unmarshal(m, b)
}
func (m *GetSeckillProductStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductStockRequest.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductStockRequest.Merge(m, src)
}
func (m *GetSeckillProductStockRequest) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductStockRequest.Size(m)
}
func (m *GetSeckillProductStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductStockRequest proto.InternalMessageInfo

func (m *GetSeckillProductStockRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *GetSeckillProductStockRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

// 获取秒杀活动实时库存 响应
type GetSeckillProductStockResponse struct {
	Common               *CommonResponse `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	StockSurplus         int32           `protobuf:"varint,2,opt,name=stock_surplus,json=stockSurplus,proto3" json:"stock_surplus"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSeckillProductStockResponse) Reset()         { *m = GetSeckillProductStockResponse{} }
func (m *GetSeckillProductStockResponse) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductStockResponse) ProtoMessage()    {}
func (*GetSeckillProductStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{1}
}

func (m *GetSeckillProductStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductStockResponse.Unmarshal(m, b)
}
func (m *GetSeckillProductStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductStockResponse.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductStockResponse.Merge(m, src)
}
func (m *GetSeckillProductStockResponse) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductStockResponse.Size(m)
}
func (m *GetSeckillProductStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductStockResponse proto.InternalMessageInfo

func (m *GetSeckillProductStockResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

func (m *GetSeckillProductStockResponse) GetStockSurplus() int32 {
	if m != nil {
		return m.StockSurplus
	}
	return 0
}

// 改变小程序用户的秒杀订阅状态 请求
type SeckillProductNoticeRequest struct {
	// 秒杀活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 商品sku id
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 小程序user_id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 小程序open_id
	OpenId string `protobuf:"bytes,4,opt,name=open_id,json=openId,proto3" json:"open_id"`
	// 操作类型 1:订阅 2:取消
	Type                 int32    `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillProductNoticeRequest) Reset()         { *m = SeckillProductNoticeRequest{} }
func (m *SeckillProductNoticeRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillProductNoticeRequest) ProtoMessage()    {}
func (*SeckillProductNoticeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{2}
}

func (m *SeckillProductNoticeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductNoticeRequest.Unmarshal(m, b)
}
func (m *SeckillProductNoticeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductNoticeRequest.Marshal(b, m, deterministic)
}
func (m *SeckillProductNoticeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductNoticeRequest.Merge(m, src)
}
func (m *SeckillProductNoticeRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillProductNoticeRequest.Size(m)
}
func (m *SeckillProductNoticeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductNoticeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductNoticeRequest proto.InternalMessageInfo

func (m *SeckillProductNoticeRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *SeckillProductNoticeRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SeckillProductNoticeRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SeckillProductNoticeRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SeckillProductNoticeRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// 改变小程序用户的秒杀订阅状态 响应
type SeckillProductNoticeResponse struct {
	Common               *CommonResponse `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SeckillProductNoticeResponse) Reset()         { *m = SeckillProductNoticeResponse{} }
func (m *SeckillProductNoticeResponse) String() string { return proto.CompactTextString(m) }
func (*SeckillProductNoticeResponse) ProtoMessage()    {}
func (*SeckillProductNoticeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{3}
}

func (m *SeckillProductNoticeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductNoticeResponse.Unmarshal(m, b)
}
func (m *SeckillProductNoticeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductNoticeResponse.Marshal(b, m, deterministic)
}
func (m *SeckillProductNoticeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductNoticeResponse.Merge(m, src)
}
func (m *SeckillProductNoticeResponse) XXX_Size() int {
	return xxx_messageInfo_SeckillProductNoticeResponse.Size(m)
}
func (m *SeckillProductNoticeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductNoticeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductNoticeResponse proto.InternalMessageInfo

func (m *SeckillProductNoticeResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

type SeckillResponse struct {
	AffectRows           int64    `protobuf:"varint,1,opt,name=affect_rows,json=affectRows,proto3" json:"affect_rows"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillResponse) Reset()         { *m = SeckillResponse{} }
func (m *SeckillResponse) String() string { return proto.CompactTextString(m) }
func (*SeckillResponse) ProtoMessage()    {}
func (*SeckillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{4}
}

func (m *SeckillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillResponse.Unmarshal(m, b)
}
func (m *SeckillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillResponse.Marshal(b, m, deterministic)
}
func (m *SeckillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillResponse.Merge(m, src)
}
func (m *SeckillResponse) XXX_Size() int {
	return xxx_messageInfo_SeckillResponse.Size(m)
}
func (m *SeckillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillResponse proto.InternalMessageInfo

func (m *SeckillResponse) GetAffectRows() int64 {
	if m != nil {
		return m.AffectRows
	}
	return 0
}

// 获取可以参加秒杀活动的阿闻商城的商品
type GetUPetProductSelectListRequest struct {
	// 秒杀活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品的产品id
	SpuId int32 `protobuf:"varint,3,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 当前多少页 从1开始
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 主体：1-默认，2-极宠家
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUPetProductSelectListRequest) Reset()         { *m = GetUPetProductSelectListRequest{} }
func (m *GetUPetProductSelectListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUPetProductSelectListRequest) ProtoMessage()    {}
func (*GetUPetProductSelectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{5}
}

func (m *GetUPetProductSelectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUPetProductSelectListRequest.Unmarshal(m, b)
}
func (m *GetUPetProductSelectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUPetProductSelectListRequest.Marshal(b, m, deterministic)
}
func (m *GetUPetProductSelectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUPetProductSelectListRequest.Merge(m, src)
}
func (m *GetUPetProductSelectListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUPetProductSelectListRequest.Size(m)
}
func (m *GetUPetProductSelectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUPetProductSelectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUPetProductSelectListRequest proto.InternalMessageInfo

func (m *GetUPetProductSelectListRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *GetUPetProductSelectListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetUPetProductSelectListRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *GetUPetProductSelectListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetUPetProductSelectListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetUPetProductSelectListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetUPetProductSelectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 获取可以参加秒杀活动的阿闻商城的商品
type GetUPetProductSelectListResponse struct {
	Data                 []*SelectUPetProductData `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	Total                int32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUPetProductSelectListResponse) Reset()         { *m = GetUPetProductSelectListResponse{} }
func (m *GetUPetProductSelectListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUPetProductSelectListResponse) ProtoMessage()    {}
func (*GetUPetProductSelectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{6}
}

func (m *GetUPetProductSelectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUPetProductSelectListResponse.Unmarshal(m, b)
}
func (m *GetUPetProductSelectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUPetProductSelectListResponse.Marshal(b, m, deterministic)
}
func (m *GetUPetProductSelectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUPetProductSelectListResponse.Merge(m, src)
}
func (m *GetUPetProductSelectListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUPetProductSelectListResponse.Size(m)
}
func (m *GetUPetProductSelectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUPetProductSelectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUPetProductSelectListResponse proto.InternalMessageInfo

func (m *GetUPetProductSelectListResponse) GetData() []*SelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetUPetProductSelectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 阿闻电商参加周期购活动的商品信息
type SelectUPetProductData struct {
	// 产品 spu id
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品 sku id
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 产品名称
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
	TimeConflict int32 `protobuf:"varint,4,opt,name=time_conflict,json=timeConflict,proto3" json:"time_conflict"`
	// 商品图片
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	// 库存
	Stock int32 `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	// 价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 是否时虚拟产品 1是 0否
	IsVirtual int32 `protobuf:"varint,8,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	// 是否与其他活动有时间上的冲突，有冲突的活动名称
	TimeConflictDesc string `protobuf:"bytes,9,opt,name=time_conflict_desc,json=timeConflictDesc,proto3" json:"time_conflict_desc"`
	//商品类型
	GoodsType int32 `protobuf:"varint,10,opt,name=goods_type,json=goodsType,proto3" json:"goods_type"`
	//组合商品的子商品skuI
	ChildSkuIds          []*SeckillChildRen `protobuf:"bytes,24,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SelectUPetProductData) Reset()         { *m = SelectUPetProductData{} }
func (m *SelectUPetProductData) String() string { return proto.CompactTextString(m) }
func (*SelectUPetProductData) ProtoMessage()    {}
func (*SelectUPetProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{7}
}

func (m *SelectUPetProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectUPetProductData.Unmarshal(m, b)
}
func (m *SelectUPetProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectUPetProductData.Marshal(b, m, deterministic)
}
func (m *SelectUPetProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectUPetProductData.Merge(m, src)
}
func (m *SelectUPetProductData) XXX_Size() int {
	return xxx_messageInfo_SelectUPetProductData.Size(m)
}
func (m *SelectUPetProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectUPetProductData.DiscardUnknown(m)
}

var xxx_messageInfo_SelectUPetProductData proto.InternalMessageInfo

func (m *SelectUPetProductData) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *SelectUPetProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SelectUPetProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *SelectUPetProductData) GetTimeConflict() int32 {
	if m != nil {
		return m.TimeConflict
	}
	return 0
}

func (m *SelectUPetProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *SelectUPetProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *SelectUPetProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *SelectUPetProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *SelectUPetProductData) GetTimeConflictDesc() string {
	if m != nil {
		return m.TimeConflictDesc
	}
	return ""
}

func (m *SelectUPetProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *SelectUPetProductData) GetChildSkuIds() []*SeckillChildRen {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

//组合商品子商品讯息
type SeckillChildRen struct {
	//商品ID
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//规则
	RuleNum int32 `protobuf:"varint,2,opt,name=rule_num,json=ruleNum,proto3" json:"rule_num"`
	//是否为虚拟 0:不是 1：是虚拟
	IsVirtual int32 `protobuf:"varint,3,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//0不是药品仓 1药品仓
	StockWarehouse       int32    `protobuf:"varint,7,opt,name=stock_warehouse,json=stockWarehouse,proto3" json:"stock_warehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillChildRen) Reset()         { *m = SeckillChildRen{} }
func (m *SeckillChildRen) String() string { return proto.CompactTextString(m) }
func (*SeckillChildRen) ProtoMessage()    {}
func (*SeckillChildRen) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{8}
}

func (m *SeckillChildRen) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillChildRen.Unmarshal(m, b)
}
func (m *SeckillChildRen) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillChildRen.Marshal(b, m, deterministic)
}
func (m *SeckillChildRen) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillChildRen.Merge(m, src)
}
func (m *SeckillChildRen) XXX_Size() int {
	return xxx_messageInfo_SeckillChildRen.Size(m)
}
func (m *SeckillChildRen) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillChildRen.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillChildRen proto.InternalMessageInfo

func (m *SeckillChildRen) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SeckillChildRen) GetRuleNum() int32 {
	if m != nil {
		return m.RuleNum
	}
	return 0
}

func (m *SeckillChildRen) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *SeckillChildRen) GetStockWarehouse() int32 {
	if m != nil {
		return m.StockWarehouse
	}
	return 0
}

// 删除秒杀活动商品信息
type DeleteSeckillProductRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSeckillProductRequest) Reset()         { *m = DeleteSeckillProductRequest{} }
func (m *DeleteSeckillProductRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteSeckillProductRequest) ProtoMessage()    {}
func (*DeleteSeckillProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{9}
}

func (m *DeleteSeckillProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSeckillProductRequest.Unmarshal(m, b)
}
func (m *DeleteSeckillProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSeckillProductRequest.Marshal(b, m, deterministic)
}
func (m *DeleteSeckillProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSeckillProductRequest.Merge(m, src)
}
func (m *DeleteSeckillProductRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteSeckillProductRequest.Size(m)
}
func (m *DeleteSeckillProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSeckillProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSeckillProductRequest proto.InternalMessageInfo

func (m *DeleteSeckillProductRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 创建或编辑秒杀活动商品
type CreateOrUpdateSeckillProductRequest struct {
	// 产品spu id
	SpuId int32 `protobuf:"varint,1,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品 sku id
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 秒杀价（单位分）
	Price int32 `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	// 活动库存
	Stock int32 `protobuf:"varint,4,opt,name=stock,proto3" json:"stock"`
	// 活动id
	PromotionId int32 `protobuf:"varint,5,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 产品名称
	ProductName string `protobuf:"bytes,7,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品原价
	MarketPrice int32 `protobuf:"varint,8,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	//org_id  1-默认 2-极宠家
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateOrUpdateSeckillProductRequest) Reset()         { *m = CreateOrUpdateSeckillProductRequest{} }
func (m *CreateOrUpdateSeckillProductRequest) String() string { return proto.CompactTextString(m) }
func (*CreateOrUpdateSeckillProductRequest) ProtoMessage()    {}
func (*CreateOrUpdateSeckillProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{10}
}

func (m *CreateOrUpdateSeckillProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOrUpdateSeckillProductRequest.Unmarshal(m, b)
}
func (m *CreateOrUpdateSeckillProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOrUpdateSeckillProductRequest.Marshal(b, m, deterministic)
}
func (m *CreateOrUpdateSeckillProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOrUpdateSeckillProductRequest.Merge(m, src)
}
func (m *CreateOrUpdateSeckillProductRequest) XXX_Size() int {
	return xxx_messageInfo_CreateOrUpdateSeckillProductRequest.Size(m)
}
func (m *CreateOrUpdateSeckillProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOrUpdateSeckillProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOrUpdateSeckillProductRequest proto.InternalMessageInfo

func (m *CreateOrUpdateSeckillProductRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CreateOrUpdateSeckillProductRequest) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *CreateOrUpdateSeckillProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type PromotionProduct struct {
	// 活动产品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 产品spuid
	SpuId int32 `protobuf:"varint,2,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 活动id
	PromotionId int32 `protobuf:"varint,4,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 活动类型  11-秒杀
	Types int32 `protobuf:"varint,5,opt,name=types,proto3" json:"types"`
	// 渠道
	ChannelId int32 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,7,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 上架下架:0-下架 1-上架
	UpDownState int32 `protobuf:"varint,8,opt,name=up_down_state,json=upDownState,proto3" json:"up_down_state"`
	// 秒杀价
	SeckillPrice int32 `protobuf:"varint,9,opt,name=seckill_price,json=seckillPrice,proto3" json:"seckill_price"`
	// 原价
	MarketPrice int32 `protobuf:"varint,10,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 采购价
	PurchasePrice int32 `protobuf:"varint,21,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price"`
	// 秒杀实时剩余库存
	StockSurplus int32 `protobuf:"varint,11,opt,name=stock_surplus,json=stockSurplus,proto3" json:"stock_surplus"`
	// 商品图片
	ProductImg string `protobuf:"bytes,12,opt,name=product_img,json=productImg,proto3" json:"product_img"`
	// 订阅状态，如果入参有user_id 返回该字段
	SubState int32 `protobuf:"varint,13,opt,name=sub_state,json=subState,proto3" json:"sub_state"`
	// 秒杀总库存
	SeckillStock int32 `protobuf:"varint,16,opt,name=seckill_stock,json=seckillStock,proto3" json:"seckill_stock"`
	// 创建日期
	CreateTime string `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 最后更新时间
	UpdateTime string `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 折扣率
	DiscountRate string `protobuf:"bytes,22,opt,name=discount_rate,json=discountRate,proto3" json:"discount_rate"`
	// 是否异常 1:异常 0：正常
	IsException int32 `protobuf:"varint,19,opt,name=is_exception,json=isException,proto3" json:"is_exception"`
	// 标记状态 0不显示按钮、1显示标记非异常、2显示取消标记
	MarkState            int32    `protobuf:"varint,20,opt,name=mark_state,json=markState,proto3" json:"mark_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionProduct) Reset()         { *m = PromotionProduct{} }
func (m *PromotionProduct) String() string { return proto.CompactTextString(m) }
func (*PromotionProduct) ProtoMessage()    {}
func (*PromotionProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{11}
}

func (m *PromotionProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionProduct.Unmarshal(m, b)
}
func (m *PromotionProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionProduct.Marshal(b, m, deterministic)
}
func (m *PromotionProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionProduct.Merge(m, src)
}
func (m *PromotionProduct) XXX_Size() int {
	return xxx_messageInfo_PromotionProduct.Size(m)
}
func (m *PromotionProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionProduct.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionProduct proto.InternalMessageInfo

func (m *PromotionProduct) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionProduct) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *PromotionProduct) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *PromotionProduct) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionProduct) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionProduct) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PromotionProduct) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PromotionProduct) GetUpDownState() int32 {
	if m != nil {
		return m.UpDownState
	}
	return 0
}

func (m *PromotionProduct) GetSeckillPrice() int32 {
	if m != nil {
		return m.SeckillPrice
	}
	return 0
}

func (m *PromotionProduct) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *PromotionProduct) GetPurchasePrice() int32 {
	if m != nil {
		return m.PurchasePrice
	}
	return 0
}

func (m *PromotionProduct) GetStockSurplus() int32 {
	if m != nil {
		return m.StockSurplus
	}
	return 0
}

func (m *PromotionProduct) GetProductImg() string {
	if m != nil {
		return m.ProductImg
	}
	return ""
}

func (m *PromotionProduct) GetSubState() int32 {
	if m != nil {
		return m.SubState
	}
	return 0
}

func (m *PromotionProduct) GetSeckillStock() int32 {
	if m != nil {
		return m.SeckillStock
	}
	return 0
}

func (m *PromotionProduct) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PromotionProduct) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *PromotionProduct) GetDiscountRate() string {
	if m != nil {
		return m.DiscountRate
	}
	return ""
}

func (m *PromotionProduct) GetIsException() int32 {
	if m != nil {
		return m.IsException
	}
	return 0
}

func (m *PromotionProduct) GetMarkState() int32 {
	if m != nil {
		return m.MarkState
	}
	return 0
}

// 获取秒杀活动列表
type SeckillListRequest struct {
	// 活动id列表，用逗号隔开
	Cids string `protobuf:"bytes,1,opt,name=cids,proto3" json:"cids"`
	// 活动状态：-3待提交 -2待审核 1未开始 2进行中 3已结束 4已终止
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	// 活动名称
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 排序顺序：1-创建时间倒序，2-创建时间升序,3-活动开始时间倒序，4-活动开始时间升序
	OrderBy int32 `protobuf:"varint,6,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	//活动id
	Id                   int32    `protobuf:"varint,7,opt,name=Id,proto3" json:"Id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillListRequest) Reset()         { *m = SeckillListRequest{} }
func (m *SeckillListRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillListRequest) ProtoMessage()    {}
func (*SeckillListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{12}
}

func (m *SeckillListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillListRequest.Unmarshal(m, b)
}
func (m *SeckillListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillListRequest.Marshal(b, m, deterministic)
}
func (m *SeckillListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillListRequest.Merge(m, src)
}
func (m *SeckillListRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillListRequest.Size(m)
}
func (m *SeckillListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillListRequest proto.InternalMessageInfo

func (m *SeckillListRequest) GetCids() string {
	if m != nil {
		return m.Cids
	}
	return ""
}

func (m *SeckillListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SeckillListRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SeckillListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SeckillListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *SeckillListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *SeckillListRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 获取秒杀活动列表
type PromotionListResponse struct {
	// 总的条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	// 活动列表
	Data                 []*Promotion `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PromotionListResponse) Reset()         { *m = PromotionListResponse{} }
func (m *PromotionListResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionListResponse) ProtoMessage()    {}
func (*PromotionListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{13}
}

func (m *PromotionListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionListResponse.Unmarshal(m, b)
}
func (m *PromotionListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionListResponse.Marshal(b, m, deterministic)
}
func (m *PromotionListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionListResponse.Merge(m, src)
}
func (m *PromotionListResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionListResponse.Size(m)
}
func (m *PromotionListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionListResponse proto.InternalMessageInfo

func (m *PromotionListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *PromotionListResponse) GetData() []*Promotion {
	if m != nil {
		return m.Data
	}
	return nil
}

type Promotion struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 渠道id 多个用逗号隔开
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 活动标题
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	// 活动类型 11-秒杀
	Types int32 `protobuf:"varint,4,opt,name=types,proto3" json:"types"`
	// 活动状态 -3:待提交 -2:待审核 1-未开始 2-进行中 3-已结束
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 状态文本：待提交、待审核、未开始、进行中、已结束、已终止（status=3 && is_system_end=1）
	StatusText string `protobuf:"bytes,6,opt,name=status_text,json=statusText,proto3" json:"status_text"`
	// 是否系统结束 0否 1-是
	IsSystemEnd int32 `protobuf:"varint,7,opt,name=is_system_end,json=isSystemEnd,proto3" json:"is_system_end"`
	// 活动开始时间
	BeginTime string `protobuf:"bytes,8,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 活动截止时间
	EndTime string `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//单笔购买上限
	SeckillOrderLimit int32 `protobuf:"varint,10,opt,name=seckill_order_limit,json=seckillOrderLimit,proto3" json:"seckill_order_limit"`
	// 最后更新人Id
	UpdateUserId string `protobuf:"bytes,11,opt,name=update_user_id,json=updateUserId,proto3" json:"update_user_id"`
	// 最后更新人姓名
	UpdateUserName string `protobuf:"bytes,12,opt,name=update_user_name,json=updateUserName,proto3" json:"update_user_name"`
	// 最后更新时间
	UpdateTime string `protobuf:"bytes,13,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 创建人
	CreateUserId string `protobuf:"bytes,14,opt,name=create_user_id,json=createUserId,proto3" json:"create_user_id"`
	// 创建日期
	CreateTime string `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 创建人名称
	CreateUserName string `protobuf:"bytes,16,opt,name=create_user_name,json=createUserName,proto3" json:"create_user_name"`
	//是否显示 1 显示 0 不显示
	IsShow int32 `protobuf:"varint,17,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	// 是否包邮
	IsShippingFree int32 `protobuf:"varint,18,opt,name=is_shipping_free,json=isShippingFree,proto3" json:"is_shipping_free"`
	// 异常商品计数
	ExceptionCount int32 `protobuf:"varint,19,opt,name=exception_count,json=exceptionCount,proto3" json:"exception_count"`
	// 总商品数量
	TotalCount int32 `protobuf:"varint,20,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	// 审核理由
	CheckReason string `protobuf:"bytes,21,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	//单用户该活动下单上限
	SeckillOrderCnt      int32    `protobuf:"varint,22,opt,name=seckill_order_cnt,json=seckillOrderCnt,proto3" json:"seckill_order_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Promotion) Reset()         { *m = Promotion{} }
func (m *Promotion) String() string { return proto.CompactTextString(m) }
func (*Promotion) ProtoMessage()    {}
func (*Promotion) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{14}
}

func (m *Promotion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Promotion.Unmarshal(m, b)
}
func (m *Promotion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Promotion.Marshal(b, m, deterministic)
}
func (m *Promotion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Promotion.Merge(m, src)
}
func (m *Promotion) XXX_Size() int {
	return xxx_messageInfo_Promotion.Size(m)
}
func (m *Promotion) XXX_DiscardUnknown() {
	xxx_messageInfo_Promotion.DiscardUnknown(m)
}

var xxx_messageInfo_Promotion proto.InternalMessageInfo

func (m *Promotion) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Promotion) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *Promotion) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Promotion) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *Promotion) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Promotion) GetStatusText() string {
	if m != nil {
		return m.StatusText
	}
	return ""
}

func (m *Promotion) GetIsSystemEnd() int32 {
	if m != nil {
		return m.IsSystemEnd
	}
	return 0
}

func (m *Promotion) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *Promotion) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *Promotion) GetSeckillOrderLimit() int32 {
	if m != nil {
		return m.SeckillOrderLimit
	}
	return 0
}

func (m *Promotion) GetUpdateUserId() string {
	if m != nil {
		return m.UpdateUserId
	}
	return ""
}

func (m *Promotion) GetUpdateUserName() string {
	if m != nil {
		return m.UpdateUserName
	}
	return ""
}

func (m *Promotion) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *Promotion) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func (m *Promotion) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *Promotion) GetCreateUserName() string {
	if m != nil {
		return m.CreateUserName
	}
	return ""
}

func (m *Promotion) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *Promotion) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *Promotion) GetExceptionCount() int32 {
	if m != nil {
		return m.ExceptionCount
	}
	return 0
}

func (m *Promotion) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *Promotion) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

func (m *Promotion) GetSeckillOrderCnt() int32 {
	if m != nil {
		return m.SeckillOrderCnt
	}
	return 0
}

// 创建/编辑秒杀活动
type SeckillRequest struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 活动开始时间
	BeginTime string `protobuf:"bytes,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 活动结束时间
	EndTime string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 购买上限
	SeckillOrderLimit int32 `protobuf:"varint,5,opt,name=seckill_order_limit,json=seckillOrderLimit,proto3" json:"seckill_order_limit"`
	// 是否免邮费 0否1是
	IsShippingFree int32 `protobuf:"varint,6,opt,name=is_shipping_free,json=isShippingFree,proto3" json:"is_shipping_free"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 用户名称
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 是否显示 1是 0 否
	IsShow int32 `protobuf:"varint,9,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//单用户该活动下单上限
	SeckillOrderCnt      int32    `protobuf:"varint,10,opt,name=seckill_order_cnt,json=seckillOrderCnt,proto3" json:"seckill_order_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillRequest) Reset()         { *m = SeckillRequest{} }
func (m *SeckillRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillRequest) ProtoMessage()    {}
func (*SeckillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{15}
}

func (m *SeckillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillRequest.Unmarshal(m, b)
}
func (m *SeckillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillRequest.Marshal(b, m, deterministic)
}
func (m *SeckillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillRequest.Merge(m, src)
}
func (m *SeckillRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillRequest.Size(m)
}
func (m *SeckillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillRequest proto.InternalMessageInfo

func (m *SeckillRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SeckillRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SeckillRequest) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *SeckillRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *SeckillRequest) GetSeckillOrderLimit() int32 {
	if m != nil {
		return m.SeckillOrderLimit
	}
	return 0
}

func (m *SeckillRequest) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *SeckillRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SeckillRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *SeckillRequest) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *SeckillRequest) GetSeckillOrderCnt() int32 {
	if m != nil {
		return m.SeckillOrderCnt
	}
	return 0
}

// 停止秒杀活动
type StopSeckillRequest struct {
	// 秒杀活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// userName,即登录人姓名
	UserName             string   `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopSeckillRequest) Reset()         { *m = StopSeckillRequest{} }
func (m *StopSeckillRequest) String() string { return proto.CompactTextString(m) }
func (*StopSeckillRequest) ProtoMessage()    {}
func (*StopSeckillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{16}
}

func (m *StopSeckillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopSeckillRequest.Unmarshal(m, b)
}
func (m *StopSeckillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopSeckillRequest.Marshal(b, m, deterministic)
}
func (m *StopSeckillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopSeckillRequest.Merge(m, src)
}
func (m *StopSeckillRequest) XXX_Size() int {
	return xxx_messageInfo_StopSeckillRequest.Size(m)
}
func (m *StopSeckillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StopSeckillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StopSeckillRequest proto.InternalMessageInfo

func (m *StopSeckillRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StopSeckillRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *StopSeckillRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

// 展示或者隐藏秒杀活动
type ShowSeckillRequest struct {
	// 秒杀活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 是否显示 1 显示 0 不显示
	IsShow int32 `protobuf:"varint,2,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// userName,即登录人姓名
	UserName             string   `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowSeckillRequest) Reset()         { *m = ShowSeckillRequest{} }
func (m *ShowSeckillRequest) String() string { return proto.CompactTextString(m) }
func (*ShowSeckillRequest) ProtoMessage()    {}
func (*ShowSeckillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{17}
}

func (m *ShowSeckillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowSeckillRequest.Unmarshal(m, b)
}
func (m *ShowSeckillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowSeckillRequest.Marshal(b, m, deterministic)
}
func (m *ShowSeckillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowSeckillRequest.Merge(m, src)
}
func (m *ShowSeckillRequest) XXX_Size() int {
	return xxx_messageInfo_ShowSeckillRequest.Size(m)
}
func (m *ShowSeckillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowSeckillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShowSeckillRequest proto.InternalMessageInfo

func (m *ShowSeckillRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ShowSeckillRequest) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *ShowSeckillRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ShowSeckillRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

// 获取秒杀活动详情
type GetSeckillDetailRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeckillDetailRequest) Reset()         { *m = GetSeckillDetailRequest{} }
func (m *GetSeckillDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetSeckillDetailRequest) ProtoMessage()    {}
func (*GetSeckillDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{18}
}

func (m *GetSeckillDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillDetailRequest.Unmarshal(m, b)
}
func (m *GetSeckillDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetSeckillDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillDetailRequest.Merge(m, src)
}
func (m *GetSeckillDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetSeckillDetailRequest.Size(m)
}
func (m *GetSeckillDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillDetailRequest proto.InternalMessageInfo

func (m *GetSeckillDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 获取秒杀商品列表
type GetSeckillProductListRequest struct {
	//  活动id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品spu id
	SpuId int32 `protobuf:"varint,3,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 排序顺序：1-创建时间倒叙，2-创建时间升序
	OrderBy int32 `protobuf:"varint,7,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	// 小程序用户ID，后台管理获取秒杀商品列表不需要该字段
	UserId string `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 1异常商品
	Type                 int32    `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeckillProductListRequest) Reset()         { *m = GetSeckillProductListRequest{} }
func (m *GetSeckillProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductListRequest) ProtoMessage()    {}
func (*GetSeckillProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{19}
}

func (m *GetSeckillProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductListRequest.Unmarshal(m, b)
}
func (m *GetSeckillProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductListRequest.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductListRequest.Merge(m, src)
}
func (m *GetSeckillProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductListRequest.Size(m)
}
func (m *GetSeckillProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductListRequest proto.InternalMessageInfo

func (m *GetSeckillProductListRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetSeckillProductListRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *GetSeckillProductListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetSeckillProductListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// 获取秒杀商品列表
type GetSeckillProductListResponse struct {
	// 总的条数
	Total                int32               `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Data                 []*PromotionProduct `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSeckillProductListResponse) Reset()         { *m = GetSeckillProductListResponse{} }
func (m *GetSeckillProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductListResponse) ProtoMessage()    {}
func (*GetSeckillProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{20}
}

func (m *GetSeckillProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductListResponse.Unmarshal(m, b)
}
func (m *GetSeckillProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductListResponse.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductListResponse.Merge(m, src)
}
func (m *GetSeckillProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductListResponse.Size(m)
}
func (m *GetSeckillProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductListResponse proto.InternalMessageInfo

func (m *GetSeckillProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetSeckillProductListResponse) GetData() []*PromotionProduct {
	if m != nil {
		return m.Data
	}
	return nil
}

// 秒杀活动商品详细信息
type GetSeckillProductDetailRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//活动id 用于不知道id的情况 通过活动id与sku进行查询
	PromotionId int32 `protobuf:"varint,2,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 活动商品skuId 用于不知道id的情况 通过活动id与sku进行查询
	SkuId                int64    `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeckillProductDetailRequest) Reset()         { *m = GetSeckillProductDetailRequest{} }
func (m *GetSeckillProductDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductDetailRequest) ProtoMessage()    {}
func (*GetSeckillProductDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{21}
}

func (m *GetSeckillProductDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductDetailRequest.Unmarshal(m, b)
}
func (m *GetSeckillProductDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductDetailRequest.Merge(m, src)
}
func (m *GetSeckillProductDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductDetailRequest.Size(m)
}
func (m *GetSeckillProductDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductDetailRequest proto.InternalMessageInfo

func (m *GetSeckillProductDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetSeckillProductDetailRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *GetSeckillProductDetailRequest) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

// 秒杀活动商品详细信息
type GetSeckillProductDetailResponse struct {
	Data                 *PromotionProduct `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSeckillProductDetailResponse) Reset()         { *m = GetSeckillProductDetailResponse{} }
func (m *GetSeckillProductDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetSeckillProductDetailResponse) ProtoMessage()    {}
func (*GetSeckillProductDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{22}
}

func (m *GetSeckillProductDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillProductDetailResponse.Unmarshal(m, b)
}
func (m *GetSeckillProductDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillProductDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetSeckillProductDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillProductDetailResponse.Merge(m, src)
}
func (m *GetSeckillProductDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetSeckillProductDetailResponse.Size(m)
}
func (m *GetSeckillProductDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillProductDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillProductDetailResponse proto.InternalMessageInfo

func (m *GetSeckillProductDetailResponse) GetData() *PromotionProduct {
	if m != nil {
		return m.Data
	}
	return nil
}

// 用户端-获取秒杀活动列表 Request
type SeckillListCRequest struct {
	// 活动id列表，用逗号隔开
	Cids                 string   `protobuf:"bytes,1,opt,name=cids,proto3" json:"cids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillListCRequest) Reset()         { *m = SeckillListCRequest{} }
func (m *SeckillListCRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillListCRequest) ProtoMessage()    {}
func (*SeckillListCRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{23}
}

func (m *SeckillListCRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillListCRequest.Unmarshal(m, b)
}
func (m *SeckillListCRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillListCRequest.Marshal(b, m, deterministic)
}
func (m *SeckillListCRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillListCRequest.Merge(m, src)
}
func (m *SeckillListCRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillListCRequest.Size(m)
}
func (m *SeckillListCRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillListCRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillListCRequest proto.InternalMessageInfo

func (m *SeckillListCRequest) GetCids() string {
	if m != nil {
		return m.Cids
	}
	return ""
}

// 获取最近秒杀活动
type RecentSeckillListCRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecentSeckillListCRequest) Reset()         { *m = RecentSeckillListCRequest{} }
func (m *RecentSeckillListCRequest) String() string { return proto.CompactTextString(m) }
func (*RecentSeckillListCRequest) ProtoMessage()    {}
func (*RecentSeckillListCRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{24}
}

func (m *RecentSeckillListCRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecentSeckillListCRequest.Unmarshal(m, b)
}
func (m *RecentSeckillListCRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecentSeckillListCRequest.Marshal(b, m, deterministic)
}
func (m *RecentSeckillListCRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecentSeckillListCRequest.Merge(m, src)
}
func (m *RecentSeckillListCRequest) XXX_Size() int {
	return xxx_messageInfo_RecentSeckillListCRequest.Size(m)
}
func (m *RecentSeckillListCRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecentSeckillListCRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecentSeckillListCRequest proto.InternalMessageInfo

// 用户端-获取秒杀活动列表 Response
type SeckillListCResponse struct {
	Common *CommonResponse `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	// 总的条数
	Total int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	// 活动列表
	Data                 []*Seckill `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SeckillListCResponse) Reset()         { *m = SeckillListCResponse{} }
func (m *SeckillListCResponse) String() string { return proto.CompactTextString(m) }
func (*SeckillListCResponse) ProtoMessage()    {}
func (*SeckillListCResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{25}
}

func (m *SeckillListCResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillListCResponse.Unmarshal(m, b)
}
func (m *SeckillListCResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillListCResponse.Marshal(b, m, deterministic)
}
func (m *SeckillListCResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillListCResponse.Merge(m, src)
}
func (m *SeckillListCResponse) XXX_Size() int {
	return xxx_messageInfo_SeckillListCResponse.Size(m)
}
func (m *SeckillListCResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillListCResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillListCResponse proto.InternalMessageInfo

func (m *SeckillListCResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

func (m *SeckillListCResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SeckillListCResponse) GetData() []*Seckill {
	if m != nil {
		return m.Data
	}
	return nil
}

// 用户端-获取最近秒杀活动 Response
type RecentSeckillListCResponse struct {
	Common *CommonResponse `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	// 活动id列表 都好分割
	Data                 []int32  `protobuf:"varint,2,rep,packed,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecentSeckillListCResponse) Reset()         { *m = RecentSeckillListCResponse{} }
func (m *RecentSeckillListCResponse) String() string { return proto.CompactTextString(m) }
func (*RecentSeckillListCResponse) ProtoMessage()    {}
func (*RecentSeckillListCResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{26}
}

func (m *RecentSeckillListCResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecentSeckillListCResponse.Unmarshal(m, b)
}
func (m *RecentSeckillListCResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecentSeckillListCResponse.Marshal(b, m, deterministic)
}
func (m *RecentSeckillListCResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecentSeckillListCResponse.Merge(m, src)
}
func (m *RecentSeckillListCResponse) XXX_Size() int {
	return xxx_messageInfo_RecentSeckillListCResponse.Size(m)
}
func (m *RecentSeckillListCResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecentSeckillListCResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecentSeckillListCResponse proto.InternalMessageInfo

func (m *RecentSeckillListCResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

func (m *RecentSeckillListCResponse) GetData() []int32 {
	if m != nil {
		return m.Data
	}
	return nil
}

type Seckill struct {
	// 活动id
	Cid int32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	// 活动标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 活动状态 1-未开始 2-进行中 3-已结束
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	// 活动开始时间
	BeginTime string `protobuf:"bytes,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 活动截止时间
	EndTime              string   `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Seckill) Reset()         { *m = Seckill{} }
func (m *Seckill) String() string { return proto.CompactTextString(m) }
func (*Seckill) ProtoMessage()    {}
func (*Seckill) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{27}
}

func (m *Seckill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Seckill.Unmarshal(m, b)
}
func (m *Seckill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Seckill.Marshal(b, m, deterministic)
}
func (m *Seckill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Seckill.Merge(m, src)
}
func (m *Seckill) XXX_Size() int {
	return xxx_messageInfo_Seckill.Size(m)
}
func (m *Seckill) XXX_DiscardUnknown() {
	xxx_messageInfo_Seckill.DiscardUnknown(m)
}

var xxx_messageInfo_Seckill proto.InternalMessageInfo

func (m *Seckill) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *Seckill) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Seckill) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Seckill) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *Seckill) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

// 用户端-获取sku同级（相同spu）的商品列表 Request
type SeckillProductSkuListRequest struct {
	//  活动id
	Cid                  int32    `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	SkuId                int32    `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SpuId                int32    `protobuf:"varint,3,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillProductSkuListRequest) Reset()         { *m = SeckillProductSkuListRequest{} }
func (m *SeckillProductSkuListRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillProductSkuListRequest) ProtoMessage()    {}
func (*SeckillProductSkuListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{28}
}

func (m *SeckillProductSkuListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductSkuListRequest.Unmarshal(m, b)
}
func (m *SeckillProductSkuListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductSkuListRequest.Marshal(b, m, deterministic)
}
func (m *SeckillProductSkuListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductSkuListRequest.Merge(m, src)
}
func (m *SeckillProductSkuListRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillProductSkuListRequest.Size(m)
}
func (m *SeckillProductSkuListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductSkuListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductSkuListRequest proto.InternalMessageInfo

func (m *SeckillProductSkuListRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SeckillProductSkuListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SeckillProductSkuListRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

// 用户端-获取sku同级（相同spu）的商品列表 Response
type SeckillProductSkuListResponse struct {
	Common               *CommonResponse   `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	Data                 []*SecKillProduct `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SeckillProductSkuListResponse) Reset()         { *m = SeckillProductSkuListResponse{} }
func (m *SeckillProductSkuListResponse) String() string { return proto.CompactTextString(m) }
func (*SeckillProductSkuListResponse) ProtoMessage()    {}
func (*SeckillProductSkuListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{29}
}

func (m *SeckillProductSkuListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductSkuListResponse.Unmarshal(m, b)
}
func (m *SeckillProductSkuListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductSkuListResponse.Marshal(b, m, deterministic)
}
func (m *SeckillProductSkuListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductSkuListResponse.Merge(m, src)
}
func (m *SeckillProductSkuListResponse) XXX_Size() int {
	return xxx_messageInfo_SeckillProductSkuListResponse.Size(m)
}
func (m *SeckillProductSkuListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductSkuListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductSkuListResponse proto.InternalMessageInfo

func (m *SeckillProductSkuListResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

func (m *SeckillProductSkuListResponse) GetData() []*SecKillProduct {
	if m != nil {
		return m.Data
	}
	return nil
}

// 用户端-获取秒杀商品列表 Request
type SeckillProductListCRequest struct {
	//  活动id,多个用逗号隔开
	Cids string `protobuf:"bytes,1,opt,name=cids,proto3" json:"cids"`
	// 小程序用户ID
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 小程序OpenID
	OpenId string `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	// // 商品spu id
	// int32 spu_id = 4;
	// // 商品sku id
	// int32 sku_id = 5;
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillProductListCRequest) Reset()         { *m = SeckillProductListCRequest{} }
func (m *SeckillProductListCRequest) String() string { return proto.CompactTextString(m) }
func (*SeckillProductListCRequest) ProtoMessage()    {}
func (*SeckillProductListCRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{30}
}

func (m *SeckillProductListCRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductListCRequest.Unmarshal(m, b)
}
func (m *SeckillProductListCRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductListCRequest.Marshal(b, m, deterministic)
}
func (m *SeckillProductListCRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductListCRequest.Merge(m, src)
}
func (m *SeckillProductListCRequest) XXX_Size() int {
	return xxx_messageInfo_SeckillProductListCRequest.Size(m)
}
func (m *SeckillProductListCRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductListCRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductListCRequest proto.InternalMessageInfo

func (m *SeckillProductListCRequest) GetCids() string {
	if m != nil {
		return m.Cids
	}
	return ""
}

func (m *SeckillProductListCRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SeckillProductListCRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SeckillProductListCRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SeckillProductListCRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 用户端-获取秒杀商品列表 Response
type SeckillProductListCResponse struct {
	Common *CommonResponse `protobuf:"bytes,1,opt,name=common,proto3" json:"common"`
	// 总的条数
	Total                int32             `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*SecKillProduct `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SeckillProductListCResponse) Reset()         { *m = SeckillProductListCResponse{} }
func (m *SeckillProductListCResponse) String() string { return proto.CompactTextString(m) }
func (*SeckillProductListCResponse) ProtoMessage()    {}
func (*SeckillProductListCResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{31}
}

func (m *SeckillProductListCResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductListCResponse.Unmarshal(m, b)
}
func (m *SeckillProductListCResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductListCResponse.Marshal(b, m, deterministic)
}
func (m *SeckillProductListCResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductListCResponse.Merge(m, src)
}
func (m *SeckillProductListCResponse) XXX_Size() int {
	return xxx_messageInfo_SeckillProductListCResponse.Size(m)
}
func (m *SeckillProductListCResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductListCResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductListCResponse proto.InternalMessageInfo

func (m *SeckillProductListCResponse) GetCommon() *CommonResponse {
	if m != nil {
		return m.Common
	}
	return nil
}

func (m *SeckillProductListCResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SeckillProductListCResponse) GetData() []*SecKillProduct {
	if m != nil {
		return m.Data
	}
	return nil
}

type SecKillProduct struct {
	// 活动id
	Cid int32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	// 产品spuid
	SpuId int32 `protobuf:"varint,2,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	// 商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 秒杀价
	SeckillPrice int32 `protobuf:"varint,5,opt,name=seckill_price,json=seckillPrice,proto3" json:"seckill_price"`
	// 原价
	OriginalPrice int32 `protobuf:"varint,6,opt,name=original_price,json=originalPrice,proto3" json:"original_price"`
	// 秒杀实时剩余库存
	StockSurplus int32 `protobuf:"varint,7,opt,name=stock_surplus,json=stockSurplus,proto3" json:"stock_surplus"`
	// 秒杀总库存
	StockTotal int32 `protobuf:"varint,8,opt,name=stock_total,json=stockTotal,proto3" json:"stock_total"`
	// 商品图片
	ProductImg string `protobuf:"bytes,9,opt,name=product_img,json=productImg,proto3" json:"product_img"`
	// 订阅状态，如果入参有user_id 返回该字段
	SubState int32 `protobuf:"varint,10,opt,name=sub_state,json=subState,proto3" json:"sub_state"`
	// 活动名称
	Title string `protobuf:"bytes,11,opt,name=title,proto3" json:"title"`
	// 单笔购买上限
	SeckillOrderLimit int32 `protobuf:"varint,12,opt,name=seckill_order_limit,json=seckillOrderLimit,proto3" json:"seckill_order_limit"`
	// 当前时间
	NowDate string `protobuf:"bytes,13,opt,name=now_date,json=nowDate,proto3" json:"now_date"`
	// 活动开始时间
	BeginDate string `protobuf:"bytes,14,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	// 活动结束时间
	EndDate              string   `protobuf:"bytes,15,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SecKillProduct) Reset()         { *m = SecKillProduct{} }
func (m *SecKillProduct) String() string { return proto.CompactTextString(m) }
func (*SecKillProduct) ProtoMessage()    {}
func (*SecKillProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{32}
}

func (m *SecKillProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecKillProduct.Unmarshal(m, b)
}
func (m *SecKillProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecKillProduct.Marshal(b, m, deterministic)
}
func (m *SecKillProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecKillProduct.Merge(m, src)
}
func (m *SecKillProduct) XXX_Size() int {
	return xxx_messageInfo_SecKillProduct.Size(m)
}
func (m *SecKillProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_SecKillProduct.DiscardUnknown(m)
}

var xxx_messageInfo_SecKillProduct proto.InternalMessageInfo

func (m *SecKillProduct) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SecKillProduct) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *SecKillProduct) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SecKillProduct) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *SecKillProduct) GetSeckillPrice() int32 {
	if m != nil {
		return m.SeckillPrice
	}
	return 0
}

func (m *SecKillProduct) GetOriginalPrice() int32 {
	if m != nil {
		return m.OriginalPrice
	}
	return 0
}

func (m *SecKillProduct) GetStockSurplus() int32 {
	if m != nil {
		return m.StockSurplus
	}
	return 0
}

func (m *SecKillProduct) GetStockTotal() int32 {
	if m != nil {
		return m.StockTotal
	}
	return 0
}

func (m *SecKillProduct) GetProductImg() string {
	if m != nil {
		return m.ProductImg
	}
	return ""
}

func (m *SecKillProduct) GetSubState() int32 {
	if m != nil {
		return m.SubState
	}
	return 0
}

func (m *SecKillProduct) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SecKillProduct) GetSeckillOrderLimit() int32 {
	if m != nil {
		return m.SeckillOrderLimit
	}
	return 0
}

func (m *SecKillProduct) GetNowDate() string {
	if m != nil {
		return m.NowDate
	}
	return ""
}

func (m *SecKillProduct) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *SecKillProduct) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

type CommonResponse struct {
	Code                 RetCode  `protobuf:"varint,1,opt,name=code,proto3,enum=ac.RetCode" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonResponse) Reset()         { *m = CommonResponse{} }
func (m *CommonResponse) String() string { return proto.CompactTextString(m) }
func (*CommonResponse) ProtoMessage()    {}
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{33}
}

func (m *CommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResponse.Unmarshal(m, b)
}
func (m *CommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResponse.Marshal(b, m, deterministic)
}
func (m *CommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResponse.Merge(m, src)
}
func (m *CommonResponse) XXX_Size() int {
	return xxx_messageInfo_CommonResponse.Size(m)
}
func (m *CommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResponse proto.InternalMessageInfo

func (m *CommonResponse) GetCode() RetCode {
	if m != nil {
		return m.Code
	}
	return RetCode_UNKNOWN
}

func (m *CommonResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetSeckillUpetGoodsRequest struct {
	// 商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeckillUpetGoodsRequest) Reset()         { *m = GetSeckillUpetGoodsRequest{} }
func (m *GetSeckillUpetGoodsRequest) String() string { return proto.CompactTextString(m) }
func (*GetSeckillUpetGoodsRequest) ProtoMessage()    {}
func (*GetSeckillUpetGoodsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{34}
}

func (m *GetSeckillUpetGoodsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillUpetGoodsRequest.Unmarshal(m, b)
}
func (m *GetSeckillUpetGoodsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillUpetGoodsRequest.Marshal(b, m, deterministic)
}
func (m *GetSeckillUpetGoodsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillUpetGoodsRequest.Merge(m, src)
}
func (m *GetSeckillUpetGoodsRequest) XXX_Size() int {
	return xxx_messageInfo_GetSeckillUpetGoodsRequest.Size(m)
}
func (m *GetSeckillUpetGoodsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillUpetGoodsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillUpetGoodsRequest proto.InternalMessageInfo

func (m *GetSeckillUpetGoodsRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetSeckillUpetGoodsRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GetSeckillUpetGoodsResponse struct {
	Data                 *SelectUPetProductData `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSeckillUpetGoodsResponse) Reset()         { *m = GetSeckillUpetGoodsResponse{} }
func (m *GetSeckillUpetGoodsResponse) String() string { return proto.CompactTextString(m) }
func (*GetSeckillUpetGoodsResponse) ProtoMessage()    {}
func (*GetSeckillUpetGoodsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{35}
}

func (m *GetSeckillUpetGoodsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeckillUpetGoodsResponse.Unmarshal(m, b)
}
func (m *GetSeckillUpetGoodsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeckillUpetGoodsResponse.Marshal(b, m, deterministic)
}
func (m *GetSeckillUpetGoodsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeckillUpetGoodsResponse.Merge(m, src)
}
func (m *GetSeckillUpetGoodsResponse) XXX_Size() int {
	return xxx_messageInfo_GetSeckillUpetGoodsResponse.Size(m)
}
func (m *GetSeckillUpetGoodsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeckillUpetGoodsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeckillUpetGoodsResponse proto.InternalMessageInfo

func (m *GetSeckillUpetGoodsResponse) GetData() *SelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type SeckillProductExportReq struct {
	// 秒杀活动id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillProductExportReq) Reset()         { *m = SeckillProductExportReq{} }
func (m *SeckillProductExportReq) String() string { return proto.CompactTextString(m) }
func (*SeckillProductExportReq) ProtoMessage()    {}
func (*SeckillProductExportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{36}
}

func (m *SeckillProductExportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductExportReq.Unmarshal(m, b)
}
func (m *SeckillProductExportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductExportReq.Marshal(b, m, deterministic)
}
func (m *SeckillProductExportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductExportReq.Merge(m, src)
}
func (m *SeckillProductExportReq) XXX_Size() int {
	return xxx_messageInfo_SeckillProductExportReq.Size(m)
}
func (m *SeckillProductExportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductExportReq.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductExportReq proto.InternalMessageInfo

func (m *SeckillProductExportReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SeckillProductExportRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	File                 []byte   `protobuf:"bytes,3,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeckillProductExportRes) Reset()         { *m = SeckillProductExportRes{} }
func (m *SeckillProductExportRes) String() string { return proto.CompactTextString(m) }
func (*SeckillProductExportRes) ProtoMessage()    {}
func (*SeckillProductExportRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_d832a5ee28a837c7, []int{37}
}

func (m *SeckillProductExportRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeckillProductExportRes.Unmarshal(m, b)
}
func (m *SeckillProductExportRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeckillProductExportRes.Marshal(b, m, deterministic)
}
func (m *SeckillProductExportRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeckillProductExportRes.Merge(m, src)
}
func (m *SeckillProductExportRes) XXX_Size() int {
	return xxx_messageInfo_SeckillProductExportRes.Size(m)
}
func (m *SeckillProductExportRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SeckillProductExportRes.DiscardUnknown(m)
}

var xxx_messageInfo_SeckillProductExportRes proto.InternalMessageInfo

func (m *SeckillProductExportRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SeckillProductExportRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SeckillProductExportRes) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

func init() {
	proto.RegisterEnum("ac.RetCode", RetCode_name, RetCode_value)
	proto.RegisterType((*GetSeckillProductStockRequest)(nil), "ac.GetSeckillProductStockRequest")
	proto.RegisterType((*GetSeckillProductStockResponse)(nil), "ac.GetSeckillProductStockResponse")
	proto.RegisterType((*SeckillProductNoticeRequest)(nil), "ac.SeckillProductNoticeRequest")
	proto.RegisterType((*SeckillProductNoticeResponse)(nil), "ac.SeckillProductNoticeResponse")
	proto.RegisterType((*SeckillResponse)(nil), "ac.SeckillResponse")
	proto.RegisterType((*GetUPetProductSelectListRequest)(nil), "ac.GetUPetProductSelectListRequest")
	proto.RegisterType((*GetUPetProductSelectListResponse)(nil), "ac.GetUPetProductSelectListResponse")
	proto.RegisterType((*SelectUPetProductData)(nil), "ac.SelectUPetProductData")
	proto.RegisterType((*SeckillChildRen)(nil), "ac.SeckillChildRen")
	proto.RegisterType((*DeleteSeckillProductRequest)(nil), "ac.DeleteSeckillProductRequest")
	proto.RegisterType((*CreateOrUpdateSeckillProductRequest)(nil), "ac.CreateOrUpdateSeckillProductRequest")
	proto.RegisterType((*PromotionProduct)(nil), "ac.PromotionProduct")
	proto.RegisterType((*SeckillListRequest)(nil), "ac.SeckillListRequest")
	proto.RegisterType((*PromotionListResponse)(nil), "ac.PromotionListResponse")
	proto.RegisterType((*Promotion)(nil), "ac.Promotion")
	proto.RegisterType((*SeckillRequest)(nil), "ac.SeckillRequest")
	proto.RegisterType((*StopSeckillRequest)(nil), "ac.StopSeckillRequest")
	proto.RegisterType((*ShowSeckillRequest)(nil), "ac.ShowSeckillRequest")
	proto.RegisterType((*GetSeckillDetailRequest)(nil), "ac.GetSeckillDetailRequest")
	proto.RegisterType((*GetSeckillProductListRequest)(nil), "ac.GetSeckillProductListRequest")
	proto.RegisterType((*GetSeckillProductListResponse)(nil), "ac.GetSeckillProductListResponse")
	proto.RegisterType((*GetSeckillProductDetailRequest)(nil), "ac.GetSeckillProductDetailRequest")
	proto.RegisterType((*GetSeckillProductDetailResponse)(nil), "ac.GetSeckillProductDetailResponse")
	proto.RegisterType((*SeckillListCRequest)(nil), "ac.SeckillListCRequest")
	proto.RegisterType((*RecentSeckillListCRequest)(nil), "ac.RecentSeckillListCRequest")
	proto.RegisterType((*SeckillListCResponse)(nil), "ac.SeckillListCResponse")
	proto.RegisterType((*RecentSeckillListCResponse)(nil), "ac.RecentSeckillListCResponse")
	proto.RegisterType((*Seckill)(nil), "ac.Seckill")
	proto.RegisterType((*SeckillProductSkuListRequest)(nil), "ac.SeckillProductSkuListRequest")
	proto.RegisterType((*SeckillProductSkuListResponse)(nil), "ac.SeckillProductSkuListResponse")
	proto.RegisterType((*SeckillProductListCRequest)(nil), "ac.SeckillProductListCRequest")
	proto.RegisterType((*SeckillProductListCResponse)(nil), "ac.SeckillProductListCResponse")
	proto.RegisterType((*SecKillProduct)(nil), "ac.SecKillProduct")
	proto.RegisterType((*CommonResponse)(nil), "ac.CommonResponse")
	proto.RegisterType((*GetSeckillUpetGoodsRequest)(nil), "ac.GetSeckillUpetGoodsRequest")
	proto.RegisterType((*GetSeckillUpetGoodsResponse)(nil), "ac.GetSeckillUpetGoodsResponse")
	proto.RegisterType((*SeckillProductExportReq)(nil), "ac.SeckillProductExportReq")
	proto.RegisterType((*SeckillProductExportRes)(nil), "ac.SeckillProductExportRes")
}

func init() { proto.RegisterFile("ac/seckill_service.proto", fileDescriptor_d832a5ee28a837c7) }

var fileDescriptor_d832a5ee28a837c7 = []byte{
	// 2315 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x59, 0x49, 0x73, 0xdc, 0xc6,
	0x15, 0xce, 0x6c, 0x9c, 0x99, 0x37, 0x8b, 0x46, 0x4d, 0x4a, 0x1a, 0x0e, 0x4d, 0x91, 0x04, 0x65,
	0x9b, 0x56, 0xc5, 0x4a, 0x95, 0x52, 0xa9, 0x4a, 0xe5, 0x90, 0x43, 0x86, 0xb4, 0x6a, 0x2c, 0x16,
	0xc5, 0xc2, 0x88, 0x76, 0x39, 0x51, 0x05, 0x01, 0x81, 0xe6, 0x10, 0xe1, 0x0c, 0x00, 0xa3, 0x1b,
	0x22, 0xe9, 0x6b, 0x0e, 0x39, 0xe4, 0x90, 0x9c, 0x72, 0x8b, 0xff, 0x44, 0xce, 0x39, 0xa6, 0x2a,
	0xbf, 0x22, 0xe7, 0xa4, 0xf2, 0x13, 0x72, 0x49, 0xf5, 0x02, 0x4c, 0x37, 0x96, 0xd1, 0x62, 0x5f,
	0x72, 0x03, 0x5e, 0x3f, 0xbc, 0xee, 0xfe, 0xfa, 0x7d, 0x6f, 0x69, 0xc0, 0xd0, 0x76, 0x7e, 0x44,
	0xb0, 0x73, 0xe5, 0xcd, 0xe7, 0x16, 0xc1, 0xd1, 0x6b, 0xcf, 0xc1, 0x4f, 0xc2, 0x28, 0xa0, 0x01,
	0xaa, 0xda, 0x8e, 0xf1, 0x15, 0x6c, 0x3f, 0xc3, 0x74, 0x2a, 0xc6, 0x4f, 0xa3, 0xc0, 0x8d, 0x1d,
	0x3a, 0xa5, 0x81, 0x73, 0x65, 0xe2, 0xaf, 0x63, 0x4c, 0x28, 0xda, 0x83, 0x6e, 0x18, 0x05, 0x8b,
	0x80, 0x7a, 0x81, 0x6f, 0x79, 0xee, 0xb0, 0xb2, 0x5b, 0x39, 0x68, 0x98, 0x9d, 0x54, 0x36, 0x71,
	0xd1, 0x3d, 0x58, 0x23, 0x57, 0x31, 0x1b, 0xac, 0xf2, 0xc1, 0x06, 0xb9, 0x8a, 0x27, 0xae, 0xf1,
	0x35, 0x3c, 0x2c, 0x33, 0x4d, 0xc2, 0xc0, 0x27, 0x18, 0x3d, 0x86, 0x35, 0x27, 0x58, 0x2c, 0x02,
	0x9f, 0x5b, 0xed, 0x3c, 0x45, 0x4f, 0x6c, 0xe7, 0xc9, 0x98, 0x4b, 0x12, 0x1d, 0x53, 0x6a, 0xa0,
	0x7d, 0xe8, 0x11, 0xf6, 0xb1, 0x45, 0xe2, 0x28, 0x9c, 0xc7, 0x44, 0xce, 0xd5, 0xe5, 0xc2, 0xa9,
	0x90, 0x19, 0x7f, 0xa9, 0xc0, 0x96, 0x3e, 0xe1, 0x49, 0x40, 0x3d, 0x07, 0x7f, 0xe7, 0xcd, 0xa0,
	0x07, 0xd0, 0x8c, 0x09, 0x8e, 0x98, 0xbc, 0xb6, 0x5b, 0x39, 0x68, 0x9b, 0x6b, 0xec, 0x55, 0x0c,
	0x04, 0x21, 0xe6, 0xd6, 0xea, 0x62, 0x80, 0xbd, 0x4e, 0x5c, 0x84, 0xa0, 0x4e, 0x6f, 0x43, 0x3c,
	0x6c, 0x70, 0x33, 0xfc, 0xd9, 0xf8, 0x1c, 0x3e, 0x28, 0x5e, 0xde, 0xbb, 0x03, 0x62, 0x3c, 0x85,
	0x3b, 0xd2, 0x56, 0xfa, 0xf9, 0x0e, 0x74, 0xec, 0x8b, 0x0b, 0xec, 0x50, 0x2b, 0x0a, 0xae, 0x09,
	0xb7, 0x51, 0x33, 0x41, 0x88, 0xcc, 0xe0, 0x9a, 0x18, 0xff, 0xaa, 0xc0, 0xce, 0x33, 0x4c, 0xcf,
	0x4e, 0x31, 0x4d, 0x0e, 0x04, 0xcf, 0xb1, 0x43, 0x8f, 0x3d, 0x42, 0xdf, 0x01, 0x23, 0xa1, 0xc2,
	0x3e, 0xb7, 0x7c, 0x7b, 0x81, 0x39, 0x52, 0x6d, 0xae, 0xc2, 0xf7, 0x64, 0x2f, 0x30, 0x87, 0x31,
	0x8c, 0x13, 0xb8, 0x18, 0x8c, 0x61, 0xac, 0xa1, 0x5b, 0x57, 0xd1, 0xdd, 0x06, 0x08, 0xed, 0x19,
	0xb6, 0x3c, 0xdf, 0xc5, 0x37, 0x12, 0xb1, 0x36, 0x93, 0x4c, 0x98, 0x00, 0x6d, 0x01, 0x7f, 0xb1,
	0x88, 0xf7, 0x0d, 0x1e, 0xae, 0xf1, 0xd1, 0x16, 0x13, 0x4c, 0xbd, 0x6f, 0xf8, 0x4c, 0x41, 0x34,
	0x63, 0x26, 0x9b, 0xc2, 0x64, 0x10, 0xcd, 0x26, 0xae, 0x31, 0x83, 0xdd, 0xf2, 0x9d, 0x4a, 0xbc,
	0x3e, 0x85, 0xba, 0x6b, 0x53, 0x7b, 0x58, 0xd9, 0xad, 0x1d, 0x74, 0x9e, 0x6e, 0x32, 0xb0, 0x85,
	0x96, 0xf2, 0xd9, 0xa1, 0x4d, 0x6d, 0x93, 0xab, 0xa1, 0x0d, 0x68, 0xd0, 0x80, 0xda, 0xf3, 0xc4,
	0x33, 0xf8, 0x8b, 0xf1, 0xdf, 0x2a, 0xdc, 0x2b, 0xfc, 0x4a, 0xc1, 0xa0, 0x52, 0x8c, 0x81, 0xe6,
	0x61, 0x59, 0x50, 0x6b, 0x79, 0x50, 0xf7, 0xa1, 0x47, 0xbd, 0x05, 0xb6, 0x9c, 0xc0, 0xbf, 0x98,
	0x7b, 0x0e, 0x95, 0x20, 0x76, 0x99, 0x70, 0x2c, 0x65, 0x68, 0x00, 0xb5, 0xd0, 0x73, 0x38, 0x88,
	0x6d, 0x93, 0x3d, 0xb2, 0x75, 0x73, 0x96, 0x48, 0xe8, 0xc4, 0x0b, 0x9b, 0x6f, 0x61, 0x47, 0x57,
	0x98, 0x5a, 0x61, 0xe4, 0x39, 0x58, 0xa2, 0xd7, 0x11, 0xb2, 0x53, 0x26, 0x62, 0xc7, 0xe2, 0x11,
	0xeb, 0xb5, 0x17, 0xd1, 0xd8, 0x9e, 0x0f, 0x5b, 0xe2, 0x58, 0x3c, 0xf2, 0x85, 0x10, 0xa0, 0x1f,
	0x02, 0xd2, 0x96, 0x63, 0xb9, 0x98, 0x38, 0xc3, 0x36, 0x9f, 0x78, 0xa0, 0xae, 0xe9, 0x10, 0x13,
	0x87, 0x19, 0x9b, 0x05, 0x81, 0x4b, 0x2c, 0xce, 0x0a, 0x10, 0xc6, 0xb8, 0xe4, 0xe5, 0x6d, 0x88,
	0xd1, 0x4f, 0xa0, 0xe3, 0x5c, 0x7a, 0x73, 0x77, 0xca, 0xc0, 0x20, 0xc3, 0x21, 0x3f, 0x92, 0x75,
	0x71, 0x24, 0xdc, 0xcb, 0xc7, 0x6c, 0xd4, 0xc4, 0xbe, 0xa9, 0xea, 0x19, 0x7f, 0xa8, 0xa4, 0x34,
	0x48, 0x14, 0x14, 0x80, 0x2b, 0x2a, 0xc0, 0x9b, 0xd0, 0x8a, 0xe2, 0x39, 0xb6, 0xfc, 0x78, 0x21,
	0x91, 0x6f, 0xb2, 0xf7, 0x93, 0x78, 0x91, 0xd9, 0x68, 0x2d, 0xbb, 0xd1, 0x8f, 0xe1, 0x8e, 0x88,
	0x3d, 0xd7, 0x76, 0x84, 0x2f, 0x83, 0x98, 0x24, 0x68, 0xf5, 0xb9, 0xf8, 0xcb, 0x44, 0x6a, 0x7c,
	0x0a, 0x5b, 0x87, 0x78, 0x8e, 0x29, 0xd6, 0x59, 0x9e, 0x50, 0xab, 0x0f, 0xd5, 0x74, 0x51, 0x55,
	0xcf, 0x35, 0xbe, 0xad, 0xc2, 0xfe, 0x38, 0xc2, 0x36, 0xc5, 0x2f, 0xa2, 0xb3, 0xd0, 0xb5, 0xcb,
	0xbe, 0x7b, 0x37, 0x47, 0xda, 0x80, 0x86, 0x38, 0x51, 0xc9, 0x3c, 0xfe, 0xb2, 0x74, 0x82, 0x7a,
	0xc6, 0x09, 0x34, 0xb2, 0x37, 0xf2, 0x64, 0xdf, 0x06, 0x70, 0x2e, 0x6d, 0xdf, 0xc7, 0x73, 0xa6,
	0x20, 0x5c, 0xa8, 0x2d, 0x25, 0x05, 0x6e, 0xdb, 0xcc, 0xbb, 0x6d, 0xd6, 0xd3, 0x5a, 0x79, 0x4f,
	0x5b, 0x92, 0xb8, 0xad, 0x92, 0xf8, 0x8f, 0x0d, 0x18, 0x9c, 0x26, 0x6b, 0x91, 0xa0, 0x64, 0x51,
	0x54, 0xd0, 0xa9, 0x16, 0xa3, 0x53, 0xcb, 0xd3, 0x6c, 0xb9, 0xe3, 0x7a, 0x7e, 0xc7, 0x8c, 0xe7,
	0xb7, 0x21, 0x26, 0x12, 0x0d, 0xf1, 0xf2, 0x3d, 0xe0, 0x60, 0x40, 0x2f, 0x0e, 0x2d, 0x37, 0xb8,
	0xf6, 0x2d, 0x42, 0x6d, 0x9a, 0x02, 0x11, 0x87, 0x87, 0xc1, 0xb5, 0x3f, 0x65, 0x22, 0x9e, 0xe6,
	0x64, 0xb2, 0x16, 0x60, 0xb5, 0x65, 0x9a, 0x4b, 0x1c, 0x83, 0xa1, 0x95, 0x05, 0x14, 0xf2, 0x80,
	0x7e, 0x08, 0xfd, 0x30, 0x8e, 0x9c, 0x4b, 0x9b, 0x60, 0xa9, 0x74, 0x8f, 0x2b, 0xf5, 0x12, 0xa9,
	0x50, 0xcb, 0x65, 0xd5, 0x4e, 0x3e, 0xab, 0xb2, 0xb4, 0x92, 0x6c, 0xcd, 0x5b, 0xcc, 0x86, 0x5d,
	0xbe, 0x33, 0x90, 0xa2, 0xc9, 0x62, 0xc6, 0xe2, 0x33, 0x89, 0xcf, 0xe5, 0xa6, 0x7a, 0x22, 0x3e,
	0x93, 0xf8, 0x3c, 0xb7, 0x23, 0xe1, 0x80, 0x03, 0x6d, 0x47, 0xbc, 0x22, 0x60, 0x53, 0x38, 0x9c,
	0x08, 0x16, 0x8b, 0x1b, 0xc3, 0xbb, 0x62, 0x0a, 0x21, 0x7a, 0xe9, 0x2d, 0x78, 0x6a, 0x8b, 0x39,
	0x43, 0x84, 0x02, 0x12, 0x0a, 0x42, 0xc4, 0x15, 0xf6, 0xa1, 0xe7, 0x7a, 0xc4, 0x09, 0x62, 0x9f,
	0x5a, 0x11, 0x5b, 0xc7, 0x7d, 0xae, 0xd2, 0x4d, 0x84, 0x26, 0x5b, 0xcb, 0x1e, 0x74, 0x3d, 0x62,
	0xe1, 0x1b, 0x07, 0x87, 0xec, 0xac, 0x87, 0xeb, 0x02, 0x38, 0x8f, 0x1c, 0x25, 0x22, 0x76, 0xcc,
	0x0c, 0x47, 0xb9, 0x99, 0x0d, 0x71, 0xcc, 0x4c, 0xc2, 0x77, 0x63, 0xfc, 0xad, 0x02, 0x48, 0x92,
	0x54, 0x4d, 0x9a, 0x08, 0xea, 0x8e, 0xe7, 0x8a, 0x94, 0xdb, 0x36, 0xf9, 0x33, 0xba, 0x0f, 0x6b,
	0xcc, 0x48, 0x5a, 0xaa, 0xc8, 0x37, 0xee, 0x5e, 0x1e, 0x9d, 0x27, 0x11, 0x5e, 0xbc, 0x64, 0x52,
	0x60, 0x7d, 0x65, 0x0a, 0x6c, 0x64, 0x52, 0xe0, 0x26, 0xb4, 0x82, 0xc8, 0xc5, 0x91, 0x75, 0x7e,
	0x2b, 0x1d, 0xb3, 0xc9, 0xdf, 0x7f, 0x71, 0xcb, 0xc8, 0x32, 0x49, 0x32, 0x63, 0x75, 0xe2, 0x1a,
	0xa7, 0x70, 0x2f, 0x25, 0x94, 0x96, 0x0b, 0xd3, 0xe4, 0x56, 0x51, 0x92, 0x1b, 0xda, 0x93, 0x19,
	0xb2, 0xca, 0xc3, 0x71, 0x8f, 0x85, 0xe3, 0xf4, 0x73, 0x91, 0x15, 0x8d, 0xff, 0x34, 0xa0, 0x9d,
	0xca, 0x72, 0xe4, 0xd4, 0x59, 0x23, 0x0a, 0x05, 0x85, 0x35, 0xc5, 0x58, 0xa4, 0x04, 0xac, 0xab,
	0x04, 0x5c, 0xe2, 0xd9, 0xd0, 0xf0, 0xdc, 0x81, 0x8e, 0x78, 0xb2, 0x28, 0xbe, 0xa1, 0x1c, 0x80,
	0xb6, 0x09, 0x42, 0xf4, 0x12, 0xdf, 0x50, 0xc6, 0x3b, 0x8f, 0x58, 0xe4, 0x96, 0x50, 0xbc, 0xb0,
	0xb0, 0x9f, 0xc0, 0xd1, 0xf1, 0xc8, 0x94, 0xcb, 0x8e, 0x7c, 0xbe, 0xce, 0x73, 0x3c, 0xf3, 0x7c,
	0xe1, 0x5e, 0x2d, 0xb1, 0x4e, 0x2e, 0xe1, 0xde, 0xb5, 0x09, 0x2d, 0xec, 0xbb, 0x62, 0x50, 0x24,
	0xb8, 0x26, 0xf6, 0x5d, 0x3e, 0xf4, 0x04, 0xd6, 0x13, 0xff, 0x16, 0x87, 0x30, 0xf7, 0x16, 0x1e,
	0x95, 0x9c, 0xbc, 0x2b, 0x87, 0x5e, 0xb0, 0x91, 0x63, 0x36, 0x80, 0x1e, 0x41, 0x5f, 0x7a, 0x72,
	0x52, 0x50, 0x76, 0x84, 0xa7, 0x0a, 0xe9, 0x99, 0x28, 0x2b, 0x0f, 0x60, 0xa0, 0x6a, 0xf1, 0x90,
	0x22, 0x88, 0xd7, 0x5f, 0xea, 0xf1, 0xa8, 0x92, 0x61, 0x46, 0x2f, 0xc7, 0x8c, 0x47, 0xd0, 0x97,
	0xdc, 0x4a, 0x26, 0xec, 0x8b, 0x09, 0x85, 0x54, 0x4e, 0x98, 0x61, 0xe0, 0x9d, 0x1c, 0x03, 0x0f,
	0x60, 0xa0, 0x9a, 0xe1, 0x2b, 0x1a, 0x88, 0x15, 0x2d, 0x0d, 0xf1, 0x15, 0x3d, 0x80, 0x26, 0xc3,
	0xfb, 0x32, 0xb8, 0xe6, 0x44, 0x6e, 0x98, 0x6b, 0x1e, 0x99, 0x5e, 0x06, 0xd7, 0xcc, 0x04, 0x1f,
	0xf0, 0xc2, 0xd0, 0xf3, 0x67, 0xd6, 0x45, 0x84, 0x05, 0x93, 0x1b, 0x66, 0x9f, 0x69, 0x08, 0xf1,
	0x67, 0x11, 0xc6, 0x2c, 0xe3, 0xa6, 0x2c, 0xb5, 0x38, 0x7f, 0x25, 0x57, 0xfb, 0xa9, 0x78, 0xcc,
	0xa4, 0x6c, 0xd9, 0xdc, 0x53, 0xa5, 0x92, 0xe0, 0x2b, 0x70, 0x91, 0x50, 0xd8, 0x83, 0xae, 0x73,
	0x89, 0x9d, 0x2b, 0x2b, 0xc2, 0x36, 0x09, 0x7c, 0x1e, 0x06, 0xdb, 0xac, 0x86, 0xc0, 0xac, 0x11,
	0x61, 0x22, 0xf4, 0x18, 0xee, 0xea, 0x27, 0xe8, 0xf8, 0x94, 0x87, 0x8f, 0x86, 0x79, 0x47, 0x3d,
	0xbf, 0xb1, 0x4f, 0x8d, 0xbf, 0x57, 0xa1, 0x9f, 0x96, 0xdd, 0x85, 0x59, 0x7d, 0xe9, 0xd3, 0xd5,
	0x0c, 0xbf, 0x15, 0x07, 0xab, 0xad, 0x72, 0xb0, 0xfa, 0x5b, 0x39, 0x58, 0xa3, 0xcc, 0xc1, 0x8a,
	0x50, 0x5e, 0x2b, 0x44, 0x59, 0x69, 0x6a, 0x9a, 0x5a, 0x53, 0xb3, 0x05, 0xed, 0xe5, 0x21, 0x0b,
	0x32, 0xb4, 0xe2, 0x82, 0xe3, 0x6d, 0x6b, 0xc7, 0x5b, 0x88, 0x23, 0x14, 0xe3, 0xf8, 0x4b, 0x40,
	0x53, 0x1a, 0x84, 0x6f, 0x80, 0x52, 0x59, 0x60, 0xb5, 0x7c, 0x81, 0x35, 0x7d, 0x81, 0x06, 0x01,
	0xc4, 0xd6, 0xf3, 0x66, 0xdb, 0xc9, 0x36, 0xaa, 0xda, 0x36, 0x4a, 0x5b, 0x3d, 0x6d, 0xd2, 0x7a,
	0x66, 0xd2, 0x4f, 0xe0, 0xc1, 0xb2, 0xdb, 0x3d, 0xc4, 0xd4, 0xf6, 0xca, 0x66, 0x36, 0xfe, 0x5c,
	0x85, 0x0f, 0x72, 0x9d, 0xf1, 0xff, 0x73, 0x0b, 0xa6, 0xe6, 0x9f, 0xa6, 0x9e, 0x7f, 0x14, 0x30,
	0x5b, 0x1a, 0x98, 0x49, 0x7b, 0x0c, 0x4a, 0x7b, 0x6c, 0x15, 0x5c, 0x46, 0xbc, 0x45, 0x92, 0x3a,
	0xd0, 0x92, 0xd4, 0x86, 0x96, 0xa4, 0x92, 0x4a, 0x5a, 0xe4, 0xaa, 0xdf, 0x16, 0x5c, 0x49, 0xac,
	0x3c, 0xab, 0xdc, 0x51, 0x54, 0x57, 0xdd, 0x18, 0xd4, 0x78, 0xc3, 0x2d, 0xaf, 0x3f, 0x9e, 0xf3,
	0x56, 0xbb, 0x78, 0x2e, 0xb9, 0x9d, 0x83, 0xb4, 0xff, 0xac, 0xbc, 0x61, 0xe1, 0x9f, 0xc0, 0xba,
	0x52, 0x75, 0x8c, 0x57, 0x94, 0x1d, 0xc6, 0x16, 0x6c, 0x9a, 0xd8, 0xc1, 0x3e, 0x2d, 0xf8, 0xc0,
	0xb8, 0x85, 0x0d, 0x5d, 0xfc, 0x1e, 0x37, 0x31, 0x85, 0x6d, 0x30, 0xda, 0x91, 0x7b, 0xa9, 0xf1,
	0x43, 0xe8, 0x28, 0x8d, 0x9b, 0xdc, 0xc2, 0x2b, 0x18, 0x15, 0xad, 0xeb, 0x3d, 0x16, 0x80, 0x94,
	0xf3, 0x6e, 0x48, 0xeb, 0xbf, 0xab, 0x40, 0x53, 0x1a, 0x66, 0x1d, 0xb0, 0x93, 0x1e, 0x22, 0x7b,
	0x2c, 0x09, 0xc9, 0xcb, 0x82, 0xa2, 0xa6, 0x15, 0x14, 0x7a, 0xa8, 0xae, 0xaf, 0x0a, 0xd5, 0x0d,
	0x2d, 0x54, 0x1b, 0xbf, 0xce, 0xde, 0xef, 0x4c, 0xaf, 0x62, 0x95, 0xd8, 0xf9, 0x95, 0x95, 0xf4,
	0x70, 0xc5, 0xdc, 0x35, 0x08, 0x6c, 0x97, 0xd8, 0x7f, 0x0f, 0x18, 0x3f, 0xd2, 0x68, 0x83, 0xe4,
	0x89, 0x3d, 0x57, 0xda, 0x4f, 0x01, 0xed, 0xb7, 0x15, 0x18, 0xe5, 0x39, 0xb9, 0xca, 0x07, 0xcb,
	0xe3, 0xb6, 0x72, 0x5b, 0x56, 0xd3, 0x6e, 0xcb, 0xbe, 0x43, 0xf9, 0x6b, 0xfc, 0x3e, 0x77, 0xeb,
	0xf7, 0x7d, 0x3b, 0xf7, 0x47, 0x9a, 0x73, 0x97, 0x43, 0xf5, 0xcf, 0x1a, 0xaf, 0x0e, 0x94, 0x81,
	0x92, 0x23, 0x7f, 0xd7, 0x7e, 0x75, 0x19, 0xe8, 0xeb, 0x85, 0xd7, 0x42, 0x7a, 0xcf, 0xd8, 0x28,
	0xe8, 0x19, 0x3f, 0x84, 0x7e, 0x10, 0x79, 0x33, 0xcf, 0xb7, 0x13, 0x2d, 0x11, 0xc5, 0x7b, 0x89,
	0xb4, 0xa4, 0x21, 0x6c, 0x16, 0x37, 0x84, 0x42, 0x49, 0x40, 0x25, 0xda, 0x58, 0xe0, 0xa2, 0x97,
	0x32, 0x18, 0x68, 0x1d, 0x63, 0x7b, 0x75, 0xc7, 0x08, 0x99, 0x8e, 0x31, 0x65, 0x6b, 0x47, 0x65,
	0x6b, 0x49, 0x19, 0xd4, 0x2d, 0x2b, 0x83, 0x36, 0xa1, 0xe5, 0x07, 0xd7, 0x96, 0x9b, 0xf4, 0xa4,
	0x6d, 0xb3, 0xe9, 0x07, 0xd7, 0x87, 0x6c, 0x82, 0x94, 0xe0, 0x7c, 0xb0, 0xaf, 0x10, 0x9c, 0x0f,
	0x4b, 0x82, 0xf3, 0xc1, 0x3b, 0x29, 0xc1, 0xd9, 0x90, 0x31, 0x86, 0xbe, 0xee, 0x38, 0x2c, 0xee,
	0x39, 0x81, 0x8b, 0xf9, 0x01, 0xf7, 0x45, 0xdc, 0x33, 0x31, 0x1d, 0x07, 0x2e, 0x36, 0xf9, 0x00,
	0x73, 0x80, 0x05, 0x99, 0x49, 0x1e, 0xb0, 0x47, 0xe3, 0x73, 0x18, 0x2d, 0x33, 0xc3, 0x59, 0x88,
	0xe9, 0xb3, 0x20, 0x70, 0x89, 0x7a, 0xd9, 0x53, 0xe0, 0x07, 0xcb, 0x1b, 0x92, 0xba, 0x7a, 0x43,
	0x72, 0x0c, 0x5b, 0x85, 0xb6, 0x72, 0x37, 0x9c, 0x95, 0xb7, 0xb8, 0xe1, 0x64, 0x45, 0x8c, 0x4e,
	0xa4, 0xa3, 0x9b, 0x30, 0x88, 0x58, 0xf8, 0xca, 0x15, 0x31, 0xbf, 0x2a, 0x53, 0x25, 0x3c, 0x22,
	0x24, 0x90, 0x34, 0x24, 0x0a, 0x43, 0x68, 0x2e, 0x30, 0x21, 0xf6, 0x2c, 0x89, 0xc1, 0xc9, 0x2b,
	0xd3, 0xbe, 0xf0, 0x64, 0x07, 0xd8, 0x35, 0xf9, 0xf3, 0xe3, 0x43, 0x68, 0x4a, 0x10, 0x51, 0x07,
	0x9a, 0x67, 0x27, 0xcf, 0x4f, 0x5e, 0x7c, 0x79, 0x32, 0xf8, 0x01, 0xea, 0x42, 0x73, 0x7a, 0x36,
	0x1e, 0x1f, 0x4d, 0xa7, 0x83, 0x7f, 0x54, 0x10, 0x40, 0xe3, 0xc8, 0x34, 0x5f, 0x98, 0x83, 0x3f,
	0xd5, 0x10, 0x82, 0xde, 0xc4, 0x7f, 0x6d, 0xcf, 0x3d, 0xf7, 0xd4, 0x8e, 0xec, 0x05, 0x19, 0xfc,
	0xbb, 0xf9, 0xf4, 0xaf, 0xdd, 0xb4, 0x56, 0x9f, 0x8a, 0x1f, 0x1f, 0xe8, 0xa7, 0xd0, 0x13, 0x17,
	0x6e, 0x49, 0xae, 0x40, 0x6a, 0xa2, 0x12, 0x27, 0x30, 0x5a, 0xd7, 0x64, 0x12, 0xc9, 0x09, 0x6c,
	0x28, 0xe9, 0xdc, 0x9e, 0x79, 0xfe, 0x8c, 0x45, 0x19, 0x74, 0x5f, 0x51, 0x56, 0x42, 0xfd, 0x68,
	0x53, 0xcb, 0xe6, 0x5a, 0x90, 0x3e, 0x4d, 0x93, 0xb0, 0x06, 0x1d, 0xda, 0x52, 0x4c, 0x65, 0xf1,
	0x1f, 0xad, 0x18, 0x24, 0x6c, 0x5b, 0xda, 0xfd, 0xe1, 0xdb, 0x6f, 0xeb, 0x67, 0xd0, 0x51, 0xea,
	0x70, 0xb9, 0x9b, 0x5c, 0x61, 0x5e, 0xfe, 0xed, 0xb2, 0xce, 0x96, 0xdf, 0xe6, 0x0a, 0xef, 0xe2,
	0x6f, 0x7f, 0x0e, 0x83, 0x6c, 0xb9, 0x2c, 0xf6, 0x5f, 0x52, 0x44, 0x8f, 0xf4, 0xbb, 0x07, 0xf4,
	0x0a, 0xee, 0x15, 0x96, 0x8a, 0x68, 0x57, 0x37, 0x92, 0xaf, 0xae, 0x47, 0x7b, 0x2b, 0x34, 0xe4,
	0xea, 0xce, 0xd5, 0x62, 0x5e, 0xab, 0xdd, 0x90, 0x51, 0xf8, 0xb5, 0xbe, 0xd6, 0xfd, 0x95, 0x3a,
	0x72, 0x8e, 0x57, 0xf0, 0xc1, 0xaa, 0xbb, 0x5f, 0xf4, 0x31, 0xcf, 0x52, 0x6f, 0xbe, 0x1d, 0x2e,
	0xc6, 0xf7, 0x18, 0x36, 0x8a, 0x6e, 0xa2, 0xd1, 0x0e, 0x53, 0x5e, 0x71, 0x47, 0x5d, 0x6c, 0x6d,
	0xae, 0xd6, 0xb2, 0x85, 0xff, 0x54, 0x50, 0xb2, 0xe7, 0x55, 0xff, 0x96, 0x46, 0x8f, 0x56, 0x2b,
	0xc9, 0xd9, 0x9e, 0xc3, 0x7d, 0x85, 0x50, 0x9f, 0x05, 0xd1, 0x38, 0x26, 0x34, 0x58, 0xe0, 0x08,
	0x3d, 0xc8, 0x90, 0x2d, 0x29, 0x42, 0x46, 0xc3, 0xfc, 0x80, 0x34, 0xf6, 0x9b, 0x6c, 0xc9, 0x94,
	0xb5, 0xf9, 0x30, 0x4f, 0x2c, 0xcd, 0xf4, 0x4e, 0xe9, 0xb8, 0x9c, 0x01, 0xc3, 0xc3, 0x4c, 0x51,
	0x66, 0x2f, 0xf0, 0x31, 0x7e, 0x8d, 0xe7, 0xb2, 0x3a, 0x13, 0x3e, 0xb9, 0xaa, 0x30, 0x14, 0x3e,
	0xb9, 0xba, 0xb4, 0xb3, 0xe0, 0x7e, 0xf1, 0xef, 0x54, 0x54, 0xec, 0xd0, 0xea, 0x5f, 0xdc, 0x91,
	0xb1, 0x4a, 0x45, 0x4e, 0xf0, 0x55, 0x36, 0x2c, 0x89, 0x9f, 0x93, 0xa8, 0x00, 0x00, 0xed, 0xaf,
	0xea, 0x68, 0xb7, 0x5c, 0x41, 0x9a, 0xfe, 0x02, 0xd6, 0x0b, 0xb2, 0x94, 0x80, 0xbe, 0x3c, 0x15,
	0x0a, 0xe8, 0x57, 0xa5, 0x37, 0x13, 0xee, 0xe6, 0x7a, 0x0a, 0xb4, 0x2d, 0x72, 0x70, 0x49, 0x0b,
	0x34, 0x7a, 0x58, 0x36, 0x2c, 0x6c, 0x9e, 0xaf, 0xf1, 0x9f, 0xe3, 0x3f, 0xfe, 0x5f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x26, 0x4d, 0x23, 0x94, 0x38, 0x1f, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SeckillServiceClient is the client API for SeckillService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SeckillServiceClient interface {
	// 创建秒杀活动
	CreateSeckill(ctx context.Context, in *SeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 获取秒杀活动分页列表（boss后台管理用）
	GetSeckillPagingList(ctx context.Context, in *SeckillListRequest, opts ...grpc.CallOption) (*PromotionListResponse, error)
	// 秒杀商品导出
	SeckillProductExport(ctx context.Context, in *SeckillProductExportReq, opts ...grpc.CallOption) (*SeckillProductExportRes, error)
	// 编辑秒杀活动
	UpdateSeckill(ctx context.Context, in *SeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 停止秒杀活动
	StopSeckill(ctx context.Context, in *StopSeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 停止秒杀活动
	ShowSeckill(ctx context.Context, in *ShowSeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 获取秒杀活动详情
	GetSeckillDetail(ctx context.Context, in *GetSeckillDetailRequest, opts ...grpc.CallOption) (*Promotion, error)
	// 获取秒杀商品列表
	GetSeckillProductList(ctx context.Context, in *GetSeckillProductListRequest, opts ...grpc.CallOption) (*GetSeckillProductListResponse, error)
	// 秒杀活动商品详细信息
	GetSeckillProductDetail(ctx context.Context, in *GetSeckillProductDetailRequest, opts ...grpc.CallOption) (*GetSeckillProductDetailResponse, error)
	// 创建或者编辑秒杀活动商品
	CreateOrUpdateSeckillProduct(ctx context.Context, in *CreateOrUpdateSeckillProductRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 删除秒杀活动商品信息
	DeleteSeckillProduct(ctx context.Context, in *DeleteSeckillProductRequest, opts ...grpc.CallOption) (*SeckillResponse, error)
	// 获取可以参加秒杀活动的阿闻商城的商品
	GetSeckillUPetProductSelectList(ctx context.Context, in *GetUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetUPetProductSelectListResponse, error)
	// 用户端-获取秒杀活动列表
	SeckillListForCustomer(ctx context.Context, in *SeckillListCRequest, opts ...grpc.CallOption) (*SeckillListCResponse, error)
	// 用户端-获取秒杀商品列表
	SeckillProductListForCustomer(ctx context.Context, in *SeckillProductListCRequest, opts ...grpc.CallOption) (*SeckillProductListCResponse, error)
	// 用户端-获取sku同级（相同spu）的商品列表
	SeckillProductSameLevelSkuList(ctx context.Context, in *SeckillProductSkuListRequest, opts ...grpc.CallOption) (*SeckillProductSkuListResponse, error)
	// 获取秒杀活动实时库存
	GetSeckillProductStock(ctx context.Context, in *GetSeckillProductStockRequest, opts ...grpc.CallOption) (*GetSeckillProductStockResponse, error)
	// 改变小程序用户的秒杀订阅状态
	SeckillProductNotice(ctx context.Context, in *SeckillProductNoticeRequest, opts ...grpc.CallOption) (*SeckillProductNoticeResponse, error)
	// 获取电商商品信息
	GetSeckillUpetGoods(ctx context.Context, in *GetSeckillUpetGoodsRequest, opts ...grpc.CallOption) (*GetSeckillUpetGoodsResponse, error)
	// 最近的秒杀活动
	RecentSeckillList(ctx context.Context, in *RecentSeckillListCRequest, opts ...grpc.CallOption) (*RecentSeckillListCResponse, error)
}

type seckillServiceClient struct {
	cc *grpc.ClientConn
}

func NewSeckillServiceClient(cc *grpc.ClientConn) SeckillServiceClient {
	return &seckillServiceClient{cc}
}

func (c *seckillServiceClient) CreateSeckill(ctx context.Context, in *SeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/CreateSeckill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillPagingList(ctx context.Context, in *SeckillListRequest, opts ...grpc.CallOption) (*PromotionListResponse, error) {
	out := new(PromotionListResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillPagingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) SeckillProductExport(ctx context.Context, in *SeckillProductExportReq, opts ...grpc.CallOption) (*SeckillProductExportRes, error) {
	out := new(SeckillProductExportRes)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/SeckillProductExport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) UpdateSeckill(ctx context.Context, in *SeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/UpdateSeckill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) StopSeckill(ctx context.Context, in *StopSeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/StopSeckill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) ShowSeckill(ctx context.Context, in *ShowSeckillRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/ShowSeckill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillDetail(ctx context.Context, in *GetSeckillDetailRequest, opts ...grpc.CallOption) (*Promotion, error) {
	out := new(Promotion)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillProductList(ctx context.Context, in *GetSeckillProductListRequest, opts ...grpc.CallOption) (*GetSeckillProductListResponse, error) {
	out := new(GetSeckillProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillProductDetail(ctx context.Context, in *GetSeckillProductDetailRequest, opts ...grpc.CallOption) (*GetSeckillProductDetailResponse, error) {
	out := new(GetSeckillProductDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillProductDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) CreateOrUpdateSeckillProduct(ctx context.Context, in *CreateOrUpdateSeckillProductRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/CreateOrUpdateSeckillProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) DeleteSeckillProduct(ctx context.Context, in *DeleteSeckillProductRequest, opts ...grpc.CallOption) (*SeckillResponse, error) {
	out := new(SeckillResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/DeleteSeckillProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillUPetProductSelectList(ctx context.Context, in *GetUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetUPetProductSelectListResponse, error) {
	out := new(GetUPetProductSelectListResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillUPetProductSelectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) SeckillListForCustomer(ctx context.Context, in *SeckillListCRequest, opts ...grpc.CallOption) (*SeckillListCResponse, error) {
	out := new(SeckillListCResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/SeckillListForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) SeckillProductListForCustomer(ctx context.Context, in *SeckillProductListCRequest, opts ...grpc.CallOption) (*SeckillProductListCResponse, error) {
	out := new(SeckillProductListCResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/SeckillProductListForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) SeckillProductSameLevelSkuList(ctx context.Context, in *SeckillProductSkuListRequest, opts ...grpc.CallOption) (*SeckillProductSkuListResponse, error) {
	out := new(SeckillProductSkuListResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/SeckillProductSameLevelSkuList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillProductStock(ctx context.Context, in *GetSeckillProductStockRequest, opts ...grpc.CallOption) (*GetSeckillProductStockResponse, error) {
	out := new(GetSeckillProductStockResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillProductStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) SeckillProductNotice(ctx context.Context, in *SeckillProductNoticeRequest, opts ...grpc.CallOption) (*SeckillProductNoticeResponse, error) {
	out := new(SeckillProductNoticeResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/SeckillProductNotice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) GetSeckillUpetGoods(ctx context.Context, in *GetSeckillUpetGoodsRequest, opts ...grpc.CallOption) (*GetSeckillUpetGoodsResponse, error) {
	out := new(GetSeckillUpetGoodsResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/GetSeckillUpetGoods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seckillServiceClient) RecentSeckillList(ctx context.Context, in *RecentSeckillListCRequest, opts ...grpc.CallOption) (*RecentSeckillListCResponse, error) {
	out := new(RecentSeckillListCResponse)
	err := c.cc.Invoke(ctx, "/ac.SeckillService/RecentSeckillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SeckillServiceServer is the server API for SeckillService service.
type SeckillServiceServer interface {
	// 创建秒杀活动
	CreateSeckill(context.Context, *SeckillRequest) (*SeckillResponse, error)
	// 获取秒杀活动分页列表（boss后台管理用）
	GetSeckillPagingList(context.Context, *SeckillListRequest) (*PromotionListResponse, error)
	// 秒杀商品导出
	SeckillProductExport(context.Context, *SeckillProductExportReq) (*SeckillProductExportRes, error)
	// 编辑秒杀活动
	UpdateSeckill(context.Context, *SeckillRequest) (*SeckillResponse, error)
	// 停止秒杀活动
	StopSeckill(context.Context, *StopSeckillRequest) (*SeckillResponse, error)
	// 停止秒杀活动
	ShowSeckill(context.Context, *ShowSeckillRequest) (*SeckillResponse, error)
	// 获取秒杀活动详情
	GetSeckillDetail(context.Context, *GetSeckillDetailRequest) (*Promotion, error)
	// 获取秒杀商品列表
	GetSeckillProductList(context.Context, *GetSeckillProductListRequest) (*GetSeckillProductListResponse, error)
	// 秒杀活动商品详细信息
	GetSeckillProductDetail(context.Context, *GetSeckillProductDetailRequest) (*GetSeckillProductDetailResponse, error)
	// 创建或者编辑秒杀活动商品
	CreateOrUpdateSeckillProduct(context.Context, *CreateOrUpdateSeckillProductRequest) (*SeckillResponse, error)
	// 删除秒杀活动商品信息
	DeleteSeckillProduct(context.Context, *DeleteSeckillProductRequest) (*SeckillResponse, error)
	// 获取可以参加秒杀活动的阿闻商城的商品
	GetSeckillUPetProductSelectList(context.Context, *GetUPetProductSelectListRequest) (*GetUPetProductSelectListResponse, error)
	// 用户端-获取秒杀活动列表
	SeckillListForCustomer(context.Context, *SeckillListCRequest) (*SeckillListCResponse, error)
	// 用户端-获取秒杀商品列表
	SeckillProductListForCustomer(context.Context, *SeckillProductListCRequest) (*SeckillProductListCResponse, error)
	// 用户端-获取sku同级（相同spu）的商品列表
	SeckillProductSameLevelSkuList(context.Context, *SeckillProductSkuListRequest) (*SeckillProductSkuListResponse, error)
	// 获取秒杀活动实时库存
	GetSeckillProductStock(context.Context, *GetSeckillProductStockRequest) (*GetSeckillProductStockResponse, error)
	// 改变小程序用户的秒杀订阅状态
	SeckillProductNotice(context.Context, *SeckillProductNoticeRequest) (*SeckillProductNoticeResponse, error)
	// 获取电商商品信息
	GetSeckillUpetGoods(context.Context, *GetSeckillUpetGoodsRequest) (*GetSeckillUpetGoodsResponse, error)
	// 最近的秒杀活动
	RecentSeckillList(context.Context, *RecentSeckillListCRequest) (*RecentSeckillListCResponse, error)
}

// UnimplementedSeckillServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSeckillServiceServer struct {
}

func (*UnimplementedSeckillServiceServer) CreateSeckill(ctx context.Context, req *SeckillRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeckill not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillPagingList(ctx context.Context, req *SeckillListRequest) (*PromotionListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillPagingList not implemented")
}
func (*UnimplementedSeckillServiceServer) SeckillProductExport(ctx context.Context, req *SeckillProductExportReq) (*SeckillProductExportRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeckillProductExport not implemented")
}
func (*UnimplementedSeckillServiceServer) UpdateSeckill(ctx context.Context, req *SeckillRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeckill not implemented")
}
func (*UnimplementedSeckillServiceServer) StopSeckill(ctx context.Context, req *StopSeckillRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopSeckill not implemented")
}
func (*UnimplementedSeckillServiceServer) ShowSeckill(ctx context.Context, req *ShowSeckillRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowSeckill not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillDetail(ctx context.Context, req *GetSeckillDetailRequest) (*Promotion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillDetail not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillProductList(ctx context.Context, req *GetSeckillProductListRequest) (*GetSeckillProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillProductList not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillProductDetail(ctx context.Context, req *GetSeckillProductDetailRequest) (*GetSeckillProductDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillProductDetail not implemented")
}
func (*UnimplementedSeckillServiceServer) CreateOrUpdateSeckillProduct(ctx context.Context, req *CreateOrUpdateSeckillProductRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateSeckillProduct not implemented")
}
func (*UnimplementedSeckillServiceServer) DeleteSeckillProduct(ctx context.Context, req *DeleteSeckillProductRequest) (*SeckillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSeckillProduct not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillUPetProductSelectList(ctx context.Context, req *GetUPetProductSelectListRequest) (*GetUPetProductSelectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillUPetProductSelectList not implemented")
}
func (*UnimplementedSeckillServiceServer) SeckillListForCustomer(ctx context.Context, req *SeckillListCRequest) (*SeckillListCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeckillListForCustomer not implemented")
}
func (*UnimplementedSeckillServiceServer) SeckillProductListForCustomer(ctx context.Context, req *SeckillProductListCRequest) (*SeckillProductListCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeckillProductListForCustomer not implemented")
}
func (*UnimplementedSeckillServiceServer) SeckillProductSameLevelSkuList(ctx context.Context, req *SeckillProductSkuListRequest) (*SeckillProductSkuListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeckillProductSameLevelSkuList not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillProductStock(ctx context.Context, req *GetSeckillProductStockRequest) (*GetSeckillProductStockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillProductStock not implemented")
}
func (*UnimplementedSeckillServiceServer) SeckillProductNotice(ctx context.Context, req *SeckillProductNoticeRequest) (*SeckillProductNoticeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeckillProductNotice not implemented")
}
func (*UnimplementedSeckillServiceServer) GetSeckillUpetGoods(ctx context.Context, req *GetSeckillUpetGoodsRequest) (*GetSeckillUpetGoodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeckillUpetGoods not implemented")
}
func (*UnimplementedSeckillServiceServer) RecentSeckillList(ctx context.Context, req *RecentSeckillListCRequest) (*RecentSeckillListCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecentSeckillList not implemented")
}

func RegisterSeckillServiceServer(s *grpc.Server, srv SeckillServiceServer) {
	s.RegisterService(&_SeckillService_serviceDesc, srv)
}

func _SeckillService_CreateSeckill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).CreateSeckill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/CreateSeckill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).CreateSeckill(ctx, req.(*SeckillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillPagingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillPagingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillPagingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillPagingList(ctx, req.(*SeckillListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_SeckillProductExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillProductExportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).SeckillProductExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/SeckillProductExport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).SeckillProductExport(ctx, req.(*SeckillProductExportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_UpdateSeckill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).UpdateSeckill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/UpdateSeckill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).UpdateSeckill(ctx, req.(*SeckillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_StopSeckill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopSeckillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).StopSeckill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/StopSeckill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).StopSeckill(ctx, req.(*StopSeckillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_ShowSeckill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowSeckillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).ShowSeckill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/ShowSeckill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).ShowSeckill(ctx, req.(*ShowSeckillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeckillDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillDetail(ctx, req.(*GetSeckillDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeckillProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillProductList(ctx, req.(*GetSeckillProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillProductDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeckillProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillProductDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillProductDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillProductDetail(ctx, req.(*GetSeckillProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_CreateOrUpdateSeckillProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateSeckillProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).CreateOrUpdateSeckillProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/CreateOrUpdateSeckillProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).CreateOrUpdateSeckillProduct(ctx, req.(*CreateOrUpdateSeckillProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_DeleteSeckillProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSeckillProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).DeleteSeckillProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/DeleteSeckillProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).DeleteSeckillProduct(ctx, req.(*DeleteSeckillProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillUPetProductSelectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUPetProductSelectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillUPetProductSelectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillUPetProductSelectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillUPetProductSelectList(ctx, req.(*GetUPetProductSelectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_SeckillListForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillListCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).SeckillListForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/SeckillListForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).SeckillListForCustomer(ctx, req.(*SeckillListCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_SeckillProductListForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillProductListCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).SeckillProductListForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/SeckillProductListForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).SeckillProductListForCustomer(ctx, req.(*SeckillProductListCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_SeckillProductSameLevelSkuList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillProductSkuListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).SeckillProductSameLevelSkuList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/SeckillProductSameLevelSkuList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).SeckillProductSameLevelSkuList(ctx, req.(*SeckillProductSkuListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillProductStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeckillProductStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillProductStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillProductStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillProductStock(ctx, req.(*GetSeckillProductStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_SeckillProductNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeckillProductNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).SeckillProductNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/SeckillProductNotice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).SeckillProductNotice(ctx, req.(*SeckillProductNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_GetSeckillUpetGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeckillUpetGoodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).GetSeckillUpetGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/GetSeckillUpetGoods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).GetSeckillUpetGoods(ctx, req.(*GetSeckillUpetGoodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeckillService_RecentSeckillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecentSeckillListCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeckillServiceServer).RecentSeckillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.SeckillService/RecentSeckillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeckillServiceServer).RecentSeckillList(ctx, req.(*RecentSeckillListCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SeckillService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.SeckillService",
	HandlerType: (*SeckillServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSeckill",
			Handler:    _SeckillService_CreateSeckill_Handler,
		},
		{
			MethodName: "GetSeckillPagingList",
			Handler:    _SeckillService_GetSeckillPagingList_Handler,
		},
		{
			MethodName: "SeckillProductExport",
			Handler:    _SeckillService_SeckillProductExport_Handler,
		},
		{
			MethodName: "UpdateSeckill",
			Handler:    _SeckillService_UpdateSeckill_Handler,
		},
		{
			MethodName: "StopSeckill",
			Handler:    _SeckillService_StopSeckill_Handler,
		},
		{
			MethodName: "ShowSeckill",
			Handler:    _SeckillService_ShowSeckill_Handler,
		},
		{
			MethodName: "GetSeckillDetail",
			Handler:    _SeckillService_GetSeckillDetail_Handler,
		},
		{
			MethodName: "GetSeckillProductList",
			Handler:    _SeckillService_GetSeckillProductList_Handler,
		},
		{
			MethodName: "GetSeckillProductDetail",
			Handler:    _SeckillService_GetSeckillProductDetail_Handler,
		},
		{
			MethodName: "CreateOrUpdateSeckillProduct",
			Handler:    _SeckillService_CreateOrUpdateSeckillProduct_Handler,
		},
		{
			MethodName: "DeleteSeckillProduct",
			Handler:    _SeckillService_DeleteSeckillProduct_Handler,
		},
		{
			MethodName: "GetSeckillUPetProductSelectList",
			Handler:    _SeckillService_GetSeckillUPetProductSelectList_Handler,
		},
		{
			MethodName: "SeckillListForCustomer",
			Handler:    _SeckillService_SeckillListForCustomer_Handler,
		},
		{
			MethodName: "SeckillProductListForCustomer",
			Handler:    _SeckillService_SeckillProductListForCustomer_Handler,
		},
		{
			MethodName: "SeckillProductSameLevelSkuList",
			Handler:    _SeckillService_SeckillProductSameLevelSkuList_Handler,
		},
		{
			MethodName: "GetSeckillProductStock",
			Handler:    _SeckillService_GetSeckillProductStock_Handler,
		},
		{
			MethodName: "SeckillProductNotice",
			Handler:    _SeckillService_SeckillProductNotice_Handler,
		},
		{
			MethodName: "GetSeckillUpetGoods",
			Handler:    _SeckillService_GetSeckillUpetGoods_Handler,
		},
		{
			MethodName: "RecentSeckillList",
			Handler:    _SeckillService_RecentSeckillList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/seckill_service.proto",
}

package services

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jordan-wright/email"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"net/smtp"
	"order-center/dto"
	"order-center/models"
	"order-center/pkg/http/zilong"
	"order-center/proto/oc"
	"order-center/utils"
	"strconv"
	"strings"
	"sync"
	"time"
)

// 税控中台文档 https://docs.qq.com/doc/DQ3pZc0tGVUdFV1Fz

type InvoiceService struct {
	CommonService
}

func (iv *InvoiceService) InvoiceTitleEdit(ctx context.Context, request *oc.InvoiceTitleEditRequest) (*oc.InvoiceTitleEditResponse, error) {
	resp := &oc.InvoiceTitleEditResponse{
		Code: 200,
	}
	_, err := zilong.BjPost("tax-control-api/outer/customer/updateInfo", map[string]interface{}{
		"customerPhone": request.CustomerPhone,
		"buyerTaxNum":   request.BuyerTaxNum,
		"notifyEmail":   request.NotifyEmail,
		"scrmId":        request.ScrmId,
		"buyerName":     request.BuyerName,
		"customerId":    request.CustomerId,
	}, "")

	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	}

	return resp, nil
}

func (iv *InvoiceService) InvoiceTitleList(ctx context.Context, request *oc.InvoiceTitleListRequest) (*oc.InvoiceTitleListResponse, error) {
	resp := &oc.InvoiceTitleListResponse{
		Code: 200,
	}
	var invoiceTitleListDataList []dto.InvoiceTitleListData

	_, err := zilong.BjPost("tax-control-api/outer/customer/list", map[string]interface{}{
		"scrmId": request.ScrmId,
	}, &invoiceTitleListDataList)

	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	}

	for _, invoiceTitleListData := range invoiceTitleListDataList {
		resp.Data = append(resp.Data, &oc.InvoiceTitleListResponse_InvoiceTitleData{
			CustomerId:    invoiceTitleListData.CustomerId,
			CustomerPhone: invoiceTitleListData.CustomerPhone,
			BuyerTaxNum:   invoiceTitleListData.BuyerTaxNum,
			BuyerName:     invoiceTitleListData.BuyerName,
			NotifyEmail:   invoiceTitleListData.NotifyEmail,
			UpdateTime:    invoiceTitleListData.UpdateTime,
		})
	}

	return resp, nil
}

func (iv *InvoiceService) InvoiceTitleAdd(ctx context.Context, request *oc.InvoiceTitleAddRequest) (*oc.InvoiceTitleAddResponse, error) {
	resp := &oc.InvoiceTitleAddResponse{
		Code: 200,
	}
	_, err := zilong.BjPost("tax-control-api/outer/customer/saveInfo", map[string]interface{}{
		"customerPhone": request.CustomerPhone,
		"buyerTaxNum":   request.BuyerTaxNum,
		"notifyEmail":   request.NotifyEmail,
		"scrmId":        request.ScrmId,
		"buyerName":     request.BuyerName,
	}, "")

	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	}
	return resp, nil
}

// QueryInvoiceCompany 模糊查询公司名称
func (iv *InvoiceService) QueryInvoiceCompany(ctx context.Context, in *oc.QueryInvoiceCompanyRequest) (*oc.QueryInvoiceCompanyResponse, error) {
	resp := &oc.QueryInvoiceCompanyResponse{
		Code: 200,
	}
	_, err := zilong.Post("tax-control-api/output/tax/queryCompanyName", map[string]interface{}{
		"q": in.Keyword,
	}, &resp.Data)
	// 即使出错也返回200，避免弹窗
	if err != nil {
		resp.Message = err.Error()
	}
	return resp, nil
}

// InvoiceCompanyInfo 通过code查询企业信息
func (iv *InvoiceService) InvoiceCompanyInfo(ctx context.Context, in *oc.InvoiceCompanyInfoRequest) (*oc.InvoiceCompanyInfoResponse, error) {
	resp := &oc.InvoiceCompanyInfoResponse{
		Code: 200,
	}
	_, err := zilong.Post("tax-control-api/output/tax/queryInvoiceCompany", map[string]interface{}{
		"speedCode": in.Code,
	}, &resp.Data)

	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	}
	return resp, nil
}

// InvoiceApply 开票申请
func (iv *InvoiceService) InvoiceApply(ctx context.Context, in *oc.CreateInvoiceRequest) (resp *oc.InvoiceResponse, rpcErr error) {
	resp = &oc.InvoiceResponse{Code: 400}
	db := GetDBConn()

	orderMain, err := getOrderMainByInvoiceOrderSn(in.OrderSn, in.ScrmId)

	if err != nil {
		resp.Message = err.Error()
	} else if orderMain.IsPay > 0 && orderMain.OrderStatus == 0 {
		resp.Message = "全额退款的订单不能开票"
	} else if orderMain.ConfirmTime.IsZero() || orderMain.OrderStatus != 30 {
		resp.Message = "只有订单已完成才能开票"
	} else if orderMain.ConfirmTime.AddDate(0, 0, 180).Before(time.Now()) {
		resp.Message = "订单完成后180天内才允许开票"
	} else if canInvoice, err := CheckCanInvoiceByOrder(orderMain); err != nil {
		resp.Message = err.Error()
	} else if !canInvoice {
		resp.Message = "订单不能开票"
	}

	if resp.Message != "" {
		return
	}

	in.OrderSn = orderMain.OrderSn
	base, rpcErr := iv.CreateInvoice(in)
	resp.Code = base.Code
	resp.Message = base.Message

	if resp.Code == 200 {
		_, err = db.Insert(&models.OrderInvoice{
			OrderSn:     in.OrderSn,
			OrderNo:     base.Data.OrderNo,
			Status:      models.InvoiceStatusIng,
			CompanyCode: base.Data.CompanyCode,
			Source:      cast.ToInt(base.Data.Source),
			Apply:       in,
			Type:        cast.ToInt(in.InvoiceTt),
		})
		if err != nil {
			resp.Code = 400
			resp.Message = "插入发票数据出错 " + err.Error()
		}
	}
	return
}

// InvoiceStatus 发票状态
func (iv *InvoiceService) InvoiceStatus(ctx context.Context, in *oc.InvoiceStatusRequest) (resp *oc.InvoiceStatusResponse, rpcErr error) {
	resp = &oc.InvoiceStatusResponse{Code: 200}

	orderMain, err := getOrderMainByInvoiceOrderSn(in.OrderSn, in.ScrmId)
	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
		return
	}

	// 920活动，免单订单关闭开票入口
	closeInvoiceOrder := config.GetString("activity92_close_invoice")
	if strings.Contains(closeInvoiceOrder, orderMain.OrderSn) {
		resp.InvoiceStatus = models.InvoiceStatusDisabled
		return
	}

	db := GetDBConn()
	// 开过发票的展示发票状态
	orderInvoice := &models.OrderInvoice{}
	if has, err := db.Table("dc_order.order_invoice").Where("order_sn = ?", orderMain.OrderSn).
		OrderBy("id desc").Get(orderInvoice); err != nil {
		resp.Code = 400
		resp.Message = "查询发票状态出错 " + err.Error()
		return
	} else if has {
		resp.InvoiceStatus = cast.ToInt32(orderInvoice.Status)
		return
	}

	// 发票禁用
	//开票配置读取redis中相关的值 v6.3.7修改  之前读取的是配置中心的invoice_enable参数
	redisClient := GetRedisConn()

	var invoiceEnable string
	if orderMain.ChannelId == ChannelMallId {
		invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_mall").Val()
	} else {
		invoiceEnable = redisClient.HGet("set:invoice_set", "invoice_enable_o2o").Val()
	}

	if invoiceEnable != "1" {
		resp.InvoiceStatus = models.InvoiceStatusDisabled
	} else if canInvoice, err := CheckCanInvoiceByOrder(orderMain); err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	} else if !canInvoice {
		resp.InvoiceStatus = models.InvoiceStatusDisabled
	} else if !orderMain.ConfirmTime.IsZero() &&
		orderMain.ConfirmTime.AddDate(0, 0, 180).Before(time.Now()) {
		resp.InvoiceStatus = models.InvoiceStatusExpire
	}

	return
}

// InvoiceDetail 发票详情
func (iv *InvoiceService) InvoiceDetail(ctx context.Context, in *oc.InvoiceDetailRequest) (resp *oc.InvoiceDetailResponse, rpcErr error) {
	resp = &oc.InvoiceDetailResponse{Code: 400}
	orderMain, err := getOrderMainByInvoiceOrderSn(in.OrderSn, in.ScrmId)
	if err != nil {
		resp.Message = err.Error()
		return
	}

	orderInvoice := &models.OrderInvoice{}
	db := GetDBConn()
	if _, err = db.Table("dc_order.order_invoice").Where("order_sn = ?", orderMain.OrderSn).
		OrderBy("id desc").Get(orderInvoice); err != nil {
		resp.Message = err.Error()
		return
	}
	resp.Code = 200
	// 没有开发票
	if orderInvoice.OrderSn == "" {
		resp.Data = &oc.InvoiceDetailData{}
		return
	}

	resp.Data = &oc.InvoiceDetailData{
		Apply:      orderInvoice.Apply,
		Status:     cast.ToInt32(orderInvoice.Status),
		FailReason: orderInvoice.FailReason,
		CreatedAt:  orderInvoice.CreatedAt.Format("2006/01/02 15:04:05"),
		Invoices:   orderInvoice.Invoices,
	}
	return
}

// InvoiceSendEmail 重新发送电子邮件
func (iv *InvoiceService) InvoiceSendEmail(ctx context.Context, in *oc.InvoiceSendEmailRequest) (resp *oc.InvoiceResponse, rpcErr error) {
	resp = &oc.InvoiceResponse{Code: 400}
	orderMain, err := getOrderMainByInvoiceOrderSn(in.OrderSn, in.ScrmId)
	if err != nil {
		resp.Message = err.Error()
		return
	}
	orderInvoice := &models.OrderInvoice{}
	db := GetDBConn()
	has, err := db.Table("dc_order.order_invoice").
		Where("order_sn = ?", orderMain.OrderSn).
		Where("status = ?", models.InvoiceStatusSuccess).Get(orderInvoice)
	if err != nil {
		resp.Message = err.Error()
		return
	}
	if has == false {
		resp.Message = "未申请发票或当前状态不允许发送邮件"
		return
	}

	lockKey := "invoice:send-email:" + orderMain.OrderSn
	// 每隔2分钟才可以点击一次
	redis := GetRedisConn()
	if !redis.SetNX(lockKey, 1, time.Minute*2).Val() {
		resp.Message = "请不要重复发送邮件"
		return
	}

	host := strings.Trim(config.GetString("mail.host"), " ")
	emailUserName := strings.Trim(config.GetString("mail.username"), " ")
	emailPassword := strings.Trim(config.GetString("mail.password"), " ")
	if len(host) == 0 || len(emailUserName) == 0 || len(emailPassword) == 0 {
		resp.Message = "发送邮件失败, 邮件配置错误"
		return
	}

	// 开始发送邮件，邮件账号密码应该写入配置中心
	e := email.NewEmail()
	e.From = fmt.Sprintf("阿闻商城 <%s>", emailUserName)
	e.To = []string{in.Email}
	e.Subject = "您收到一张【阿闻商城】开具的发票"
	html := "<p>尊敬的客户，您好！</p>" +
		"<p>附件是您在阿闻电商平台申请的订单 " + orderMain.OldOrderSn + " 发票，请下载查看。</p>"
	//client := &http.Client{
	//	Timeout: time.Second * 10,
	//}
	for ix, url := range strings.Split(orderInvoice.Invoices, ",") {
		httpResp, err := utils.ClientNoTransport10S.Get(url)
		// 如果下载出现异常，发票转成链接
		if err != nil {
			html = html + fmt.Sprintf("<a href='%s'>%s</a>", url, url)
			continue
		}
		if httpResp.StatusCode == 200 {
			_, err = e.Attach(httpResp.Body, fmt.Sprintf("%s_%v.pdf", orderMain.OldOrderSn, ix+1), "application/pdf")
		}
		if err != nil || httpResp.StatusCode != 200 {
			html = html + fmt.Sprintf("<a href='%s'>%s</a>", url, url)
		}
		// 这里不使用用defer是为了避免内存泄露警告
		_ = httpResp.Body.Close()
	}

	e.HTML = []byte(html)
	err = e.SendWithTLS(host+":465", smtp.PlainAuth("", emailUserName, emailPassword, host), &tls.Config{ServerName: host})
	if err != nil {
		redis.Del(lockKey)
		resp.Message = "发送邮件失败 " + err.Error()
		return
	}

	resp.Code = 200
	resp.Message = "邮件发送成功，请注意查收"
	return
}

// 开票
func (iv *InvoiceService) CreateInvoice(in *oc.CreateInvoiceRequest) (*oc.CreateInvoiceResponse, error) {
	out := new(oc.CreateInvoiceResponse)
	db := GetDBConn()

	// 防止重复点击开票
	redis := GetRedisConn()
	if !redis.SetNX("invoice:"+in.OrderSn, 1, time.Second*5).Val() {
		out.Code = 400
		out.Message = "操作间隔太短，请稍后再试！"
		return out, nil
	}
	// A、是否开过票，主订单信息判断
	if has, _ := db.Where("order_sn = ?", in.OrderSn).In("status", []int32{1, 2}).Exist(&models.OrderInvoice{}); has {
		out.Code = 400
		out.Message = "请勿重复开票！"
		return out, nil
	}
	var orderMain models.OrderMain
	db.Where("order_sn = ? and order_status = ?", in.OrderSn, 30).Get(&orderMain)
	if orderMain.Id < 1 {
		out.Code = 400
		out.Message = "未完成订单不支持开票！"
		return out, nil
	}
	if orderMain.IsVirtual == 1 {
		out.Code = 400
		out.Message = "只支持实物订单开票！"
		return out, nil
	}

	// B-1、获取渠道和对应的财务编码，不同渠道，获取编码途径不同，阿闻分销编码需要去电商数据库查询
	in.Channel, in.CompanyCode = channelAndCompanyCode(orderMain, getOrderChannel)

	// B-2、推送开票接口前，计算组装接口详情字段数据  zx 配送费税率
	orderMain.ShopId = in.CompanyCode
	detailsData, res := iv.CalOrderDetailData(orderMain)
	if res.Code == 400 {
		out.Message = res.Message
		return out, nil
	}

	// C、推送第三方数据中台
	in.PayTime = orderMain.PayTime.Unix()
	result, data := iv.PushThirdInvoiceApply(in, detailsData)
	if result.Code == 400 {
		out.Code = 400
		out.Message = result.Message
		return out, nil
	}
	out.Data = &oc.CreateInvoiceResponseData{
		Source:      in.Channel, // 退款红冲会用到
		CompanyCode: in.CompanyCode,
		OrderNo:     data.Result.InvoiceNo,
	}

	out.Code = 200
	out.Message = "success"
	return out, nil
}

/*
*
渠道和财务编码 https://docs.qq.com/sheet/DRWt3ZnZ1dFRjR0pr
阿闻商城-分销订单对应的主体：
1.对于第一种情况：内部分销员进行分销，某A门店的人进行分销的，那么这个订单开票主体就是A门店所对应的开票主体。
2.对于第二种情况：外部分销员进行分销，这种就是社会上的人（并非门店的人）进行分销，这种情况开票主体是算深圳巨星公司
3.还有第三种特殊情况，内部分销员进行分销的助力商品和巨星药品仓发货的商品，这种情况开票主体是算深圳巨星公司
*/
func channelAndCompanyCode(orderMain models.OrderMain, f func(main models.OrderMain) int32) (channelId int32, shopId string) {
	channelId = f(orderMain)
	shopId = orderMain.ShopId
	switch channelId {
	case 3: // 阿闻商城-巨星公司
		shopId = "JX0001"
	case 4: // 阿闻商城-分销，门店、区域公司
		upetDb := GetUPetDBConn()
		var orderChain dto.UpetOrderChain
		upetDb.SQL("select c.account_id,o.warehouse_type from upet_orders o LEFT JOIN upet_chain c ON o.chain_id = c.chain_id WHERE o.order_sn = ?", orderMain.OrderSn).Get(&orderChain)
		shopId = orderChain.AccountId
		if len(shopId) <= 2 || orderMain.OrderType == 99 { // 外部分销或助力活动
			shopId = "JX0001"
		} else if orderChain.WarehouseType == 1 { // 内部分销，药品仓商品主体算巨星
			shopId = "JX0001"
		}
	case 6: // 前置仓，前置虚拟仓（6、7），宜嘉公司
		shopId = "GYL029"
	case 7:
		shopId = "GYL029"
	}
	// 需求变更：本地生活-前置仓/门店仓/虚拟仓的都传4，阿闻商城及分销的都传3，相当于把本地生活的都汇总展示，把阿闻商城的汇总展示
	if channelId == 3 || channelId == 4 {
		channelId = 3
	} else if channelId == 5 || channelId == 6 || channelId == 7 {
		channelId = 4
	}

	return
}

// 获取订单渠道，发票接口申请来源：1 子龙 2 R1 3 阿闻商城 4 阿闻商城-分销 5 本地生活-门店仓 6  本地生活-前置仓 7 本地生活-前置虚拟仓
func getOrderChannel(orderMain models.OrderMain) int32 {
	if orderMain.ChannelId == ChannelMallId { // 阿闻电商
		if orderMain.OrderPayType == "04" {
			return 4
		}
		return 3
	} else { // 本地生活
		// 本地生活-门店仓
		if orderMain.Source == 3 {
			return 5
		}
		// 本地生活-前置虚拟仓，竖屏+门店自提
		if orderMain.UserAgent == 7 && orderMain.OrderType == 3 {
			return 7
		}
	}
	return 6
}

// 开票-数据推送税控中台
func (iv *InvoiceService) PushThirdInvoiceApply(req *oc.CreateInvoiceRequest, details []dto.DetailsData) (out oc.BaseResponse, data dto.ThirdCreateInvoiceResponse) {

	// 税控中台开票接口参数组装，推送
	thirdInvoiceParams := dto.ThirdInvoiceParams{
		InvoiceApplySource: req.Channel,
		RelationOrderNo:    req.OrderSn,
		InvocieType:        11, //默认是类型是11  如果只有1，没有11的话，那就改成1
		CompanyCode:        req.CompanyCode,
		PayedTime:          time.Unix(req.PayTime, 0).Format(kit.DATETIME_LAYOUT),
		Details:            details,
	}

	if req.InvoiceTt == 1 {
		thirdInvoiceParams.BuyerName = "个人"
		thirdInvoiceParams.CustomerPhone = req.Mobile
		thirdInvoiceParams.NotifyPhone = req.Mobile
		thirdInvoiceParams.NotifyEmail = req.Email
	} else {
		thirdInvoiceParams.BuyerName = req.CompanyName
		thirdInvoiceParams.BuyerTaxNum = req.IdentifyNumber
		thirdInvoiceParams.NotifyEmail = req.Email
		thirdInvoiceParams.BuyerBank = req.BankName
		thirdInvoiceParams.BuyerAccount = req.BankNumber
		thirdInvoiceParams.BuyerAdress = req.CompanyAddress
	}
	strSubjectParams, _ := json.Marshal(thirdInvoiceParams)

	//推送第三方中台的时候要先查询一下这个公司支持开什么票
	//other/invoice/subject/info?companyCode=CX3423
	subjectUrl := config.GetString("bj-scrm-outside-url") + "tax-control-api/other/invoice/subject/info"
	_, res, err := utils.HttpPostZl(subjectUrl, strSubjectParams, "")
	glog.Info("查询开票类请求参数：", string(strSubjectParams), " 返回结果:"+string(res))
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("查询开票类型失败：" + err.Error() + subjectUrl)
		return
	}
	json.Unmarshal(res, &data)
	if !data.Success {
		out.Code = 400
		out.Message = data.Message
		glog.Error("查询开票类型返回结果：" + string(res) + string(strSubjectParams))
		return
	}
	types := strings.Split(data.Result.InvoiceType, ",")

	if len(types) > 0 {
		//判断是否存在1和11类型
		haveType1 := false
		haveType11 := false
		for _, item := range types {

			//普通发票（电子）
			if item == "1" {
				haveType1 = true
			}
			//	全电普票（电子）
			if item == "11" {
				haveType11 = true
			}
		}
		//如果不存在类型11只有类型1的话，才需要改类型
		if (!haveType11) && haveType1 {
			thirdInvoiceParams.InvocieType = 1
		}

	}
	strParams, _ := json.Marshal(thirdInvoiceParams)
	requestUrl := config.GetString("bj-scrm-outside-url") + "tax-control-api/other/invoice/apply/record/insert"
	_, res, err = utils.HttpPostZl(requestUrl, strParams, "")
	glog.Info("开票推送数据中台请求参数：", string(strParams), " 返回结果:"+string(res))
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("开票推送数据中台失败：" + err.Error() + string(strParams))
		return
	}

	json.Unmarshal(res, &data)
	if !data.Success {
		out.Code = 400
		out.Message = data.Message
		glog.Error("开票推送数据中台返回结果：" + string(res) + string(strParams))
		return
	}
	glog.Error("开票推送数据记录：request_url-", requestUrl, string(strParams))

	return
}

// 开票-计算订单详情数据，用于推送税控中台
func (iv *InvoiceService) CalOrderDetailData(orderMain models.OrderMain) (thirdDetailData []dto.DetailsData, out oc.BaseResponse) {
	glog.Info("CalOrderDetailData 订单")
	db := GetDBConn()

	// A、获取主订单下所有实物商品数据
	var orderProducts []models.OrderProduct
	db.Where("order_sn = ? and product_type = ?", orderMain.OrderSn, 1).Find(&orderProducts)

	var cowg sync.WaitGroup
	cowg.Add(2)

	// B-1、退款单数据
	refundData := make(map[string][]dto.RefundData)
	go func() {
		defer func() {
			cowg.Done()
		}()
		refundData = getRefundData(orderMain.OrderSn)
	}()

	arrTaxCode := make(map[string]string)
	//最大商品税率
	maxTaxValue := float32(-1)
	//最大商品税收编码
	maxTaxCode := ""
	// B-2、批量查询税收编码
	go func() {
		defer func() {
			cowg.Done()
		}()
		var arrThirdSkuId []string
		for k, _ := range orderProducts {
			// R1、子龙税收编码查询均用third_sku_id
			arrThirdSkuId = append(arrThirdSkuId, orderProducts[k].ThirdSkuId)
		}

		if len(arrThirdSkuId) > 0 {
			iv.orderMain = &orderMain
			res, _ := iv.QueryTaxCode(&oc.TaxCodeRequest{
				StructCode:  orderMain.ShopId,
				ThirdSkuIds: arrThirdSkuId,
				Source:      orderMain.Source,
				Page:        1,
				Size:        20,
			})
			if len(res.Data) > 0 {
				for k, _ := range res.Data {
					arrTaxCode[res.Data[k].ThirdSkuId] = res.Data[k].TaxCode
					if res.Data[k].TaxValue > maxTaxValue {
						maxTaxValue = res.Data[k].TaxValue
						maxTaxCode = res.Data[k].TaxCode
					}
				}
			}
		}
	}()
	cowg.Wait()

	if len(arrTaxCode) < 1 {
		out.Code = 400
		out.Message = "此订单商品未查到税收编码，暂不能开票"
		glog.Error("开票推送数据组装中，此单未查询到税收编码:order_sn:", orderMain.OrderSn)
		return
	}

	// C、开票商品详情数据组装
	for k, _ := range orderProducts {
		// 计算商品均价，数量
		refund, _ := refundData[orderProducts[k].SkuId]
		avgPrice, number := getAvgPriceAndNumber(orderProducts[k], refund)
		if avgPrice == 0 {
			continue
		}
		if avgPrice < 0 {
			out.Code = 400
			out.Message = "此订单异常，暂不能开票"
			glog.Error("开票推送数据组装中，异常订单：order_sn:", orderProducts[k].OrderSn, ",avgPrice:", avgPrice, ",number:", number)
			return
		}

		// 财税编码
		taxCodeStr, has := arrTaxCode[orderProducts[k].ThirdSkuId]
		if !has || taxCodeStr == "" {
			out.Code = 400
			out.Message = "此订单商品未查到税收编码，暂不能开票"
			glog.Error("开票推送数据组装中，未查询到税收编码:third_sku_id:", orderProducts[k].ThirdSkuId, ",order_sn:", orderMain.OrderSn)
			return
		}

		// 单价保留八位，总价保留两位（四舍五入）
		productPrice, _ := decimal.NewFromFloat(avgPrice).Div(decimal.NewFromInt32(100)).Float64()
		productPrice = cast.ToFloat64(strconv.FormatFloat(productPrice, 'f', 8, 64))
		productTotalAmount, _ := decimal.NewFromFloat(productPrice).Mul(decimal.NewFromInt32(number)).Float64()
		productTotalAmount = cast.ToFloat64(strconv.FormatFloat(productTotalAmount, 'f', 2, 64))
		thirdDetailData = append(thirdDetailData, dto.DetailsData{
			RpOrderNo:       orderProducts[k].OrderSn,
			ProductName:     orderProducts[k].ProductName,
			ProductCode:     orderProducts[k].BarCode,
			TaxCode:         taxCodeStr,
			ProductPrice:    productPrice,
			ProductQuantity: cast.ToFloat64(number),
			ProductAmount:   productTotalAmount,
			ProductUnit:     "1",
			SpecType:        orderProducts[k].Specs,
		})
	}

	// C-2、包装费和运费
	if orderMain.Freight-orderMain.FreightPrivilege > 0 || orderMain.PackingCost > 0 {
		thirdDetailData = calFreightAndPackingCost(thirdDetailData, orderMain, maxTaxCode)
	}

	if len(thirdDetailData) < 1 {
		out.Code = 400
		out.Message = "此订单没有可开票金额，暂不能开票"
		return
	}

	out.Code = 200
	return
}

// 开票-获取退款数据（实物，且退款成功的)，一个单号可能有多次退款
func getRefundData(orderSn string) map[string][]dto.RefundData {
	refundData := make(map[string][]dto.RefundData)
	db := GetDBConn()

	var refundOrders []models.RefundOrder
	// 只查询 退款成功的 实物订单
	db.Where("order_sn = ? AND refund_state = ? AND is_virtual = ?", orderSn, 3, 0).Find(&refundOrders)
	if len(refundOrders) < 1 {
		return refundData
	}
	var refundSns []string
	refundTypes := make(map[string]int32)
	for k, _ := range refundOrders {
		refundSns = append(refundSns, refundOrders[k].RefundSn)
		refundTypes[refundOrders[k].RefundSn] = refundOrders[k].RefundType
	}

	// 实物商品退款数据组装
	var refundOrderProducts []models.RefundOrderProduct
	db.In("refund_sn", refundSns).Where("product_type = ?", 1).Find(&refundOrderProducts)
	if len(refundOrderProducts) > 0 {
		for k, _ := range refundOrderProducts {
			refundData[refundOrderProducts[k].SkuId] = append(refundData[refundOrderProducts[k].SkuId], dto.RefundData{
				Amount:     refundOrderProducts[k].RefundAmount,
				Number:     refundOrderProducts[k].Tkcount,
				SkuId:      refundOrderProducts[k].SkuId,
				RefundType: refundTypes[refundOrderProducts[k].RefundSn],
			})
		}
	}
	return refundData
}

// 开票-计算包装费和运费
func calFreightAndPackingCost(thirdDetailData []dto.DetailsData, orderMain models.OrderMain, maxTaxCode string) []dto.DetailsData {
	// 配送费和包装费税收编码
	_, packingTaxCode := getFreightAndPackingCode(orderMain.ShopId, orderMain.Source)

	finalFreight := orderMain.Freight - orderMain.FreightPrivilege
	//配送费直接用外面传进来商品最大的税收编码
	if finalFreight > 0 {
		finalFreightAmount := kit.FenToYuan(finalFreight)
		thirdDetailData = append(thirdDetailData, dto.DetailsData{
			RpOrderNo:       orderMain.OrderSn,
			ProductName:     "配送费",
			TaxCode:         maxTaxCode,
			ProductQuantity: 1,
			ProductAmount:   finalFreightAmount,
			ProductPrice:    finalFreightAmount,
			ProductUnit:     "1",
		})
	}
	if orderMain.PackingCost > 0 {
		packAmount := kit.FenToYuan(orderMain.PackingCost)
		thirdDetailData = append(thirdDetailData, dto.DetailsData{
			RpOrderNo:       orderMain.OrderSn,
			ProductName:     "包装费",
			TaxCode:         packingTaxCode,
			ProductPrice:    packAmount,
			ProductQuantity: 1,
			ProductAmount:   packAmount,
			ProductUnit:     "1",
		})
	}
	return thirdDetailData
}

// 获取配送费和包装费税收编码
func getFreightAndPackingCode(shopId string, source int32) (freightTaxCode string, packingTaxCode string) {
	// 单独获取配送费,包装费税收编码，包装费nuan_id : 50000302 配送费nuan_id:50000303 ,“其他服务费目录”、“包装费”、“配送费” 都是统一的税收编码： *********
	freightTaxCode = "*********"
	packingTaxCode = "*********"
	var iv InvoiceService
	res, _ := iv.QueryTaxCode(&oc.TaxCodeRequest{
		StructCode: shopId,
		NuanId:     []string{"50000303", "50000302"},
		Source:     source,
		Page:       1,
		Size:       20,
	})
	if len(res.Data) > 0 {
		for k, _ := range res.Data {
			if res.Data[k].ThirdSkuId == "50000303" {
				freightTaxCode = res.Data[k].TaxCode
			} else if res.Data[k].ThirdSkuId == "50000302" {
				packingTaxCode = res.Data[k].TaxCode
			}
		}
	}
	return
}

// 开票-计算单个sku商品均价和数量，有退款要扣掉退款
func getAvgPriceAndNumber(orderProduct models.OrderProduct, refundData []dto.RefundData) (float64, int32) {
	totalAmount := cast.ToFloat64(orderProduct.PaymentTotal)
	totalNumber := orderProduct.Number
	if len(refundData) > 0 {
		for k, _ := range refundData {
			//减去退款金额
			tmpAmount := decimal.NewFromFloat(cast.ToFloat64(refundData[k].Amount))
			tmpAmountValue := tmpAmount.Mul(decimal.NewFromInt32(100))
			totalAmount, _ = decimal.NewFromFloat(totalAmount).Sub(tmpAmountValue).Float64()
			// 退款退货需减去数量，退款不退货，数量不变
			if refundData[k].RefundType == 2 {
				totalNumber = totalNumber - refundData[k].Number
			}
		}
	}

	avgPriceValue := decimal.NewFromFloat(totalAmount).Div(decimal.NewFromFloat(cast.ToFloat64(totalNumber)))
	avgPrice, _ := avgPriceValue.Float64()
	return avgPrice, totalNumber
}

// 退款-发票红冲
func (iv *InvoiceService) RefundInvoice(ctx context.Context, in *oc.RefundInvoiceRequest) (*oc.InvoiceResponse, error) {
	out := new(oc.InvoiceResponse)
	db := GetDBConn()

	// 防止重复点击开票
	redis := GetRedisConn()
	if !redis.SetNX("invoice:refund:"+in.RefundSn, 1, time.Second*5).Val() {
		out.Code = 400
		out.Message = "操作间隔太短，请稍后再试！"
		glog.Error("发票红冲操作，", out.Message, ",refund_sn:", in.RefundSn)
		return out, nil
	}
	// A、退款订单核实，refund_state = 3的条件去掉，本地生活退款流程会更新refund_state，然后在查询会有主从问题，待定
	var refundOrder models.RefundOrder
	db.Where("refund_sn = ? AND is_virtual = ?", in.RefundSn, 0).Get(&refundOrder)
	if refundOrder.Id < 1 {
		out.Code = 400
		out.Message = "未查询到退款成功的实物订单"
		glog.Error("发票红冲操作，", out.Message, ",refund_sn:", in.RefundSn)
		return out, nil
	}

	// B、查询开票表，开票成功的订单才能发票红冲操作,PS：第三方平台是用主单号申请退款，需要做下转换
	var orderMains []models.OrderMain
	db.Where("parent_order_sn = ? or order_sn = ?", refundOrder.OrderSn, refundOrder.OrderSn).Find(&orderMains)
	if len(orderMains) > 0 {
		for k, _ := range orderMains {
			if len(orderMains[k].ParentOrderSn) > 0 {
				refundOrder.OrderSn = orderMains[k].OrderSn    // 替换为子单号
				refundOrder.OrderSource = orderMains[k].Source // refund_order表order_source字段都是0值，用order_main.source替换
				break
			}
		}
	}
	var orderInvoice models.OrderInvoice
	db.Where("order_sn = ?", refundOrder.OrderSn).Get(&orderInvoice)
	if orderInvoice.Status != 1 {
		statusMsg := map[int]string{0: "未开票", 1: "开票成功", 2: "开票中", 3: "开票失败"}
		msg, _ := statusMsg[orderInvoice.Status]
		out.Code = 400
		out.Message = msg + "，不允许发票红冲"
		glog.Error("发票红冲操作，", out.Message, "refund_sn:", in.RefundSn, ", order_sn:", refundOrder.OrderSn)
		return out, nil
	}

	// C、计算退款明细，一个订单可能有多次退款
	detailsData, res := iv.CalRefundDetailData(refundOrder)
	if res.Code == 400 {
		return &res, nil
	}

	// D、发票红冲推送数据中台
	resPush := iv.PushThirdRefundSync(refundOrder, detailsData, orderInvoice)
	if resPush.Code == 400 {
		return &resPush, nil
	}

	out.Code = 200
	return out, nil
}

// 退款-订单同步到税控中台
func (iv *InvoiceService) PushThirdRefundSync(refundOrder models.RefundOrder, details []dto.DetailsData, orderInvoice models.OrderInvoice) (out oc.InvoiceResponse) {
	db := GetDBConn()
	// A、查询蓝票（开票成功的）明细信息，可能会有多张票的情况
	var invoiceDetails []models.OrderInvoiceDetail
	db.Where("order_sn = ? AND status = ? AND type = ?", refundOrder.OrderSn, 1, 1).Find(&invoiceDetails)
	if len(invoiceDetails) < 1 {
		out.Code = 400
		glog.Error("发票红冲操作，未查询到蓝票信息,order_sn:", refundOrder.OrderSn)
		out.Message = "未查询到蓝票信息"
		return
	}

	// B、遍历发票去红冲，一张不够分摊到下一张发票，退款单信息，如果是一个订单对应多张发票需要全部退款，则退款单信息是多个
	var thirdRequestParams []dto.ThirdRefundInvoiceParams
	canCountData := make(map[int]float64, len(invoiceDetails)) // 可退金额统计
	for _, invoiceDetail := range invoiceDetails {
		detailCanRefundTotalAmount := cast.ToFloat64(invoiceDetail.CanRefundTotalAmount)
		// 发票没有可用退款金额就跳过
		if detailCanRefundTotalAmount <= 0.01 {
			continue
		}

		// 蓝票json信息解析
		var info dto.InvoiceCallbackData
		err := json.Unmarshal([]byte(invoiceDetail.Data), &info)
		if err != nil {
			glog.Error("发票红冲推送，发票信息解析失败，order_invoice_detail的id：", invoiceDetail.Id)
			out.Code = 400
			out.Message = "发票信息解析失败"
			return
		}

		// 统计details明细中总金额是否超过当前发票总金额
		var detailData []dto.DetailsData
		var nextDetails []dto.DetailsData // 记录剩余明细，用于下一张发票
		var detailDataTotalAmount float64 = 0
		var tAmount float64 = 0
		var TotalOrderRefundAmount float64 = 0
		for k, _ := range details {
			if tAmount < detailCanRefundTotalAmount { // 当前可退金额大于总退款金额
				detailData = append(detailData, details[k])
				detailDataTotalAmount = detailDataTotalAmount + details[k].ProductAmount
				TotalOrderRefundAmount = TotalOrderRefundAmount + details[k].RefundAmount
				// 有可能满足条件的最后一条金额叠加会超过可退金额，做拆分处理
				if detailDataTotalAmount > detailCanRefundTotalAmount {
					TotalOrderRefundAmount = detailCanRefundTotalAmount
					pAmount, _ := decimal.NewFromFloat(detailCanRefundTotalAmount).Add(decimal.NewFromFloat(detailData[k].RefundAmount)).Sub(decimal.NewFromFloat(detailDataTotalAmount)).Value()
					detailData[k].ProductAmount = cast.ToFloat64(pAmount)
					detailData[k].RefundAmount = detailData[k].ProductAmount
					pPrice, _ := decimal.NewFromFloat(detailData[k].ProductAmount).Div(decimal.NewFromFloat(detailData[k].ProductQuantity)).Float64()
					detailData[k].ProductPrice = cast.ToFloat64(strconv.FormatFloat(pPrice, 'f', 8, 64))

					// 多余金额记录到下一张票
					tmpDetail := details[k]
					tmPAmount, _ := decimal.NewFromFloat(detailDataTotalAmount).Sub(decimal.NewFromFloat(detailCanRefundTotalAmount)).Value()
					tmpDetail.ProductAmount = cast.ToFloat64(tmPAmount)
					tmpDetail.RefundAmount = tmpDetail.ProductAmount
					tmpDetail.ProductPrice = tmpDetail.ProductAmount
					tmpDetail.ProductQuantity = 1
					tmpDetail.ProductUnit = "1"
					nextDetails = append(nextDetails, tmpDetail)
				}
			} else {
				nextDetails = append(nextDetails, details[k])
			}
			tAmount = tAmount + details[k].ProductAmount
		}
		details = nextDetails // 剩余明细到下一张票红冲

		if len(detailData) < 1 {
			continue
		}
		// 推送参数组装
		thirdRequestParams = append(thirdRequestParams, dto.ThirdRefundInvoiceParams{
			OrderSource:       orderInvoice.Source,
			CompanyCode:       orderInvoice.CompanyCode,
			RpOrderNo:         invoiceDetail.OrderSn,
			RefundOrderNo:     refundOrder.RefundSn,
			CustomerPhone:     info.BuyerPhone,
			CustomerName:      info.BuyerName,
			RefundTime:        refundOrder.CreateTime.Format(kit.DATETIME_LAYOUT),
			OrderAmount:       TotalOrderRefundAmount,
			OrderRefundAmount: TotalOrderRefundAmount,
			InvoiceOrderNos:   orderInvoice.OrderNo,
			InvoiceId:         invoiceDetail.InvoiceId,
			InvoiceCode:       info.InvoiceCode,
			InvoiceNumber:     info.InvoiceNumber,
			Details:           detailData,
		})

		canCountData[invoiceDetail.Id] = 0.00
		if detailCanRefundTotalAmount > detailDataTotalAmount { // 记录剩余可以红冲金额
			canCountData[invoiceDetail.Id] = detailCanRefundTotalAmount - detailDataTotalAmount
		}
	}

	if len(thirdRequestParams) < 1 {
		glog.Error("发票红冲推送，没有可退金额，refund_sn", refundOrder.RefundSn)
		out.Code = 400
		out.Message = "发票没有可退金额"
		return
	}

	strParams, _ := json.Marshal(thirdRequestParams)
	requestUrl := config.GetString("bj-scrm-outside-url") + "tax-control-api/other/refund/order/insert"
	_, res, err := utils.HttpPostZl(requestUrl, strParams, "")
	glog.Info("发票红冲推送数据中台请求参数：", string(strParams), " 返回结果:"+string(res))
	if err != nil {
		glog.Error("发票红冲推送数据中台失败：", err.Error(), string(strParams))
		out.Code = 400
		out.Message = err.Error()
		return
	}

	var data dto.ThirdCreateInvoiceResponse
	json.Unmarshal(res, &data)
	if !data.Success {
		glog.Error("发票红冲推送数据中台返回结果：", string(res), string(strParams))
		out.Code = 400
		out.Message = data.Message
		return
	}
	glog.Error("发票红冲推送数据记录：", string(strParams))

	// C、红冲成功需要存储红冲记录，并更新order_invoice_detail表中的can_refund_total_amount字段
	if len(canCountData) > 0 {
		for id, amount := range canCountData {
			db.Where("id = ?", id).Update(&models.OrderInvoiceDetail{
				CanRefundTotalAmount: cast.ToString(amount),
				UpdatedAt:            time.Now(),
			})
		}
	}

	return
}

// 退款-计算组装退款订单数据，用于推送税控中台
func (iv *InvoiceService) CalRefundDetailData(refundOrder models.RefundOrder) (thirdDetailData []dto.DetailsData, out oc.InvoiceResponse) {
	db := GetDBConn()

	// A、查询退款订单下所有退款商品明细
	var refundProducts []models.RefundOrderProduct
	db.Where("refund_sn = ? AND product_type = ?", refundOrder.RefundSn, 1).Find(&refundProducts)
	if len(refundProducts) < 1 {
		glog.Error("发票红冲推送数据中台组装中，该订单未查询到退款明细，refund_sn:", refundOrder.RefundSn)
		out.Code = 400
		out.Message = "该订单未查询到退款明细"
		return
	}

	// B、批量查询税收编码,需要通过third_sku_id去查询
	var orderProducts []models.OrderProduct
	db.Select("sku_id,third_sku_id").Where("order_sn = ?", refundOrder.OrderSn).Find(&orderProducts)
	if len(orderProducts) < 1 {
		glog.Error("发票红冲推送数据中台组装中，未查询相关订单商品，order_sn:", refundOrder.OrderSn)
		out.Code = 400
		out.Message = "未查询相关订单商品"
		return
	}
	var arrThirdSkuIds []string
	arrSkuAndThird := make(map[string]string) // sku_id和third_sku_id映射关系
	for k, _ := range orderProducts {
		arrThirdSkuIds = append(arrThirdSkuIds, orderProducts[k].ThirdSkuId)
		arrSkuAndThird[orderProducts[k].SkuId] = orderProducts[k].ThirdSkuId
	}
	arrTaxCode := make(map[string]string)
	res, _ := iv.QueryTaxCode(&oc.TaxCodeRequest{
		StructCode:  refundOrder.ShopId,
		ThirdSkuIds: arrThirdSkuIds,
		Source:      refundOrder.OrderSource,
		Page:        1,
		Size:        20,
	})
	if len(res.Data) < 1 {
		glog.Error("发票红冲推送数据中台组装中，此订未查询到商品税收编码，refund_sn:", refundOrder.RefundSn)
		out.Code = 400
		out.Message = "此订未查询到商品税收编码，暂不能开退款发票"
		return
	}
	//最大商品税率
	maxTaxValue := float32(-1)
	//最大商品税收编码
	maxTaxCode := ""
	for k, _ := range res.Data {
		arrTaxCode[res.Data[k].ThirdSkuId] = res.Data[k].TaxCode
		if res.Data[k].TaxValue > maxTaxValue {
			maxTaxValue = res.Data[k].TaxValue
			maxTaxCode = res.Data[k].TaxCode
		}
	}

	// C、退款数据中，相同sku可能会有多条退款记录，对相同sku做组装
	refundData := make(map[string]map[string]int32)
	for k, _ := range refundProducts {
		refundAmount := kit.YuanToFen(cast.ToFloat64(refundProducts[k].RefundAmount))
		if refundData[refundProducts[k].SkuId]["total_amount"] == 0 {
			refundData[refundProducts[k].SkuId] = map[string]int32{
				"total_amount": cast.ToInt32(refundAmount),
				"total_number": refundProducts[k].Tkcount,
			}
		} else {
			refundData[refundProducts[k].SkuId]["total_amount"] = refundData[refundProducts[k].SkuId]["total_amount"] + cast.ToInt32(refundAmount)
			refundData[refundProducts[k].SkuId]["total_number"] = refundData[refundProducts[k].SkuId]["total_number"] + refundProducts[k].Tkcount
		}
	}

	// D、推送第三方退款明细数据组装
	strSkuId := ""
	for k, _ := range refundProducts {
		// 防止重复计算
		if strings.Contains(strSkuId, refundProducts[k].SkuId) {
			continue
		}

		// 税收编码
		taxCodeStr := ""
		if thirdSkuId, has := arrSkuAndThird[refundProducts[k].SkuId]; has {
			if taxCode, hasCode := arrTaxCode[thirdSkuId]; hasCode {
				taxCodeStr = taxCode
			}
		}
		if taxCodeStr == "" {
			glog.Error("发票红冲推送数据中台组装中，此商品未查询到商品税收编码，order_sn:", refundOrder.OrderSn)
			out.Code = 400
			out.Message = "此订单商品未查到税收编码，暂不能退款"
			return
		}

		// 单价保留八位，总价保留两位（四舍五入）
		number := refundData[refundProducts[k].SkuId]["total_number"]
		avgPrice, _ := decimal.NewFromInt32(refundData[refundProducts[k].SkuId]["total_amount"]).Div(decimal.NewFromInt32(number)).Div(decimal.NewFromInt32(100)).Float64()
		avgPrice = cast.ToFloat64(strconv.FormatFloat(avgPrice, 'f', 8, 64))
		totalAmount, _ := decimal.NewFromFloat(avgPrice).Mul(decimal.NewFromInt32(number)).Float64()
		totalAmount = cast.ToFloat64(strconv.FormatFloat(totalAmount, 'f', 2, 64))
		thirdDetailData = append(thirdDetailData, dto.DetailsData{
			RpOrderNo:       refundOrder.OrderSn,
			ProductName:     refundProducts[k].ProductName,
			ProductCode:     refundProducts[k].Barcode,
			TaxCode:         taxCodeStr,
			ProductPrice:    avgPrice,
			ProductQuantity: cast.ToFloat64(number),
			ProductAmount:   totalAmount,
			ProductUnit:     "1",
			SpecType:        refundProducts[k].Skucode,
			RefundAmount:    totalAmount,
		})
		strSkuId = strSkuId + "," + refundProducts[k].SkuId
	}

	// D-2 配送费、包装费
	if refundOrder.Freight > "0.01" {
		//freightTaxCode, _ := getFreightAndPackingCode(refundOrder.ShopId, refundOrder.OrderSource)
		thirdDetailData = append(thirdDetailData, dto.DetailsData{
			RpOrderNo:       refundOrder.OrderSn,
			ProductName:     "配送费",
			TaxCode:         maxTaxCode,
			ProductPrice:    cast.ToFloat64(refundOrder.Freight),
			ProductQuantity: 1,
			ProductAmount:   cast.ToFloat64(refundOrder.Freight),
			ProductUnit:     "1",
			RefundAmount:    cast.ToFloat64(refundOrder.Freight),
		})
	}

	out.Code = 200
	return
}

// 税收编码
func (iv *InvoiceService) QueryTaxCode(in *oc.TaxCodeRequest) (*oc.TaxCodeResponse, error) {
	//glog.Info("开票税收编码查询参数,", kit.JsonEncode(in), iv.orderMain.OrderSn)
	if in.Source == 3 { // 子龙门店
		return taxCodeForZl(in)
	}
	// R1系统
	return taxCodeForR1(in)
}

// 子龙系统税收编码查询
func taxCodeForZl(in *oc.TaxCodeRequest) (*oc.TaxCodeResponse, error) {
	out := new(oc.TaxCodeResponse)

	url := config.GetString("bj-scrm-outside-url") + "api/gcenter/outer/product/d-list"
	timestamp := time.Now().Unix()
	apiStr := utils.RandStr(16)
	apiId := config.GetString("BJAuthOutside.AppId")
	apiSecret := config.GetString("BJAuthOutside.Secret")
	strSign := makeSign(apiId, apiSecret, apiStr, timestamp)
	params := map[string]interface{}{
		"struct_code":  in.StructCode,
		"product_code": in.ThirdSkuIds,
		"nuan_id":      in.NuanId,
		"number":       in.Page,
		"size":         in.Size,
		"apiId":        apiId,
		"apiSecret":    apiSecret,
		"apiStr":       apiStr,
		"timestamp":    timestamp,
		"sign":         strSign,
	}

	dataJson := kit.JsonEncodeByte(params)
	_, res, err := utils.HttpPostZl(url, dataJson, "")
	glog.Info("子龙税收编码查询接口：requestUrl-", url, ",请求参数:", string(dataJson), ", 返回参数:", string(res))
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("子龙税收编码查询接口：requestUrl-", url, ",params-", string(dataJson), ", err-", err.Error())
		return out, nil
	}

	var resData dto.TaxCodeResponse
	json.Unmarshal(res, &resData)
	if resData.Code != 0 {
		out.Code = 400
		out.Message = resData.Message
		glog.Error("子龙税收编码查询接口：返回错误信息：", string(res))
		return out, nil
	}

	if len(resData.Data.List) < 1 {
		return out, nil
	}

	// 记录TaxCode为空的值，再去查询目录信息接口
	categoryIds := make([]string, 0)
	for _, v := range resData.Data.List {
		out.Data = append(out.Data, &oc.TaxCodeData{
			ThirdSkuId:   v.ItemCode,
			TaxCode:      v.TaxCode,
			CategoryCode: v.CategoryCode,
			TaxValue:     cast.ToFloat32(v.TaxRate),
		})
		if v.TaxCode == "" {
			categoryIds = append(categoryIds, v.CategoryCode)
		}
	}

	if len(categoryIds) > 0 {
		categoryUrl := config.GetString("bj-scrm-outside-url") + "api/gcenter/outer/category/d-list"
		categoryParams := map[string]interface{}{
			"struct_code": in.StructCode,
			"category_id": categoryIds,
			"apiId":       apiId,
			"apiSecret":   apiSecret,
			"apiStr":      apiStr,
			"timestamp":   timestamp,
			"sign":        strSign,
		}
		dataJson := kit.JsonEncodeByte(categoryParams)
		_, res, err := utils.HttpPostZl(categoryUrl, dataJson, "")
		if err != nil {
			glog.Error("子龙税收编码查询接口,目录信息接口药品查询：", string(res), ",error:", err.Error())
		} else {
			var resData dto.TaxCodeCategoryResponse
			json.Unmarshal(res, &resData)
			if resData.Code != 0 {
				glog.Error("子龙税收编码查询接口,目录信息接口药品查询，返回错误信息：", string(res))
			}
			tmpData := make(map[string]string)
			for _, v := range resData.Data {
				tmpData[v.CategoryCode] = v.TaxCode
			}
			for k, _ := range out.Data {
				if tCode, has := tmpData[out.Data[k].CategoryCode]; has {
					out.Data[k].TaxCode = tCode
					out.Data[k].TaxValue = 0
				}
			}
		}
	}

	return out, nil
}

// R1系统税收编码查询
func taxCodeForR1(in *oc.TaxCodeRequest) (*oc.TaxCodeResponse, error) {
	out := new(oc.TaxCodeResponse)

	appKey := config.GetString("R1Auth.AppId")
	timeStamp := time.Now().UnixNano()
	params, _ := json.Marshal(map[string]interface{}{
		"skuNoList": in.ThirdSkuIds,
	})
	sign := utils.A8Sign(map[string]string{
		"appKey":    appKey,
		"timestamp": cast.ToString(timeStamp),
	}, string(params), config.GetString("R1Auth.Secret"))

	url := fmt.Sprintf("%sscm/open/api/sku/selectTax?appKey=%s&timestamp=%d&sign=%s", config.GetString("r1-open-url"), appKey, timeStamp, sign)
	glog.Info("taxCodeForR1 查询参数:", kit.JsonEncode(in), url, string(params))
	res, err := utils.HttpPost(url, params, "application/json")
	glog.Info("taxCodeForR1 查询结果:", url, string(res), err)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("R1税收编码查询接口：requestUrl-"+url+",params-"+string(params), ", err-", err.Error())
		return out, nil
	}

	var resData dto.R1TaxCodeResponse
	json.Unmarshal(res, &resData)
	if resData.Code != 200 {
		out.Code = 400
		out.Message = resData.Message
		glog.Error("R1税收编码查询接口：返回错误信息：", string(res))
		return out, nil
	}

	if len(resData.Data) > 0 {
		for _, v := range resData.Data {
			if v.TaxCategoryCode == "" {
				continue
			}
			out.Data = append(out.Data, &oc.TaxCodeData{
				ThirdSkuId: v.SkuNo,
				TaxCode:    v.TaxCategoryCode,
				TaxValue:   v.TaxValue,
			})
		}
	}

	return out, nil
}

// 生成签名
func makeSign(apiId string, apiSecret string, apiStr string, timestamp int64) string {
	str := "apiSecret=" + apiSecret + "&apiStr=" + apiStr + "&apiId=" + apiId + "&timestamp=" + cast.ToString(timestamp) + "&apiSecret=" + apiSecret
	return strings.ToUpper(kit.GetMd5(str))
}

// 开票回调数据处理
func (iv *InvoiceService) InvoiceCallback(ctx context.Context, in *oc.InvoiceCallbackRequest) (*oc.InvoiceResponse, error) {
	out := new(oc.InvoiceResponse)
	glog.Info("发票回调数据处理 参数：", in.ParamsJson)
	// A、数据验证，是否有订单号返回
	var data dto.InvoiceDetailData
	err := json.Unmarshal([]byte(in.ParamsJson), &data)
	if err != nil {
		out.Message = "请求参数解析失败"
		out.Code = 400
		glog.Error("发票回调数据处理：参数解析失败：", in.ParamsJson)
		return out, err
	}
	data.Data = in.ParamsJson // 完整的回调json数据
	db := GetDBConn()
	var orderInvoice models.OrderInvoice
	// 多张票回调，orderNo是orderNo-0001，orderNo-0002，order_invoice表只记录了总的orderNo
	arrStr := strings.Split(data.OrderNo, "-")
	db.Where("order_no = ?", arrStr[0]).Get(&orderInvoice)
	if orderInvoice.Id < 1 {
		out.Message = "未查询到开票信息"
		out.Code = 400
		glog.Error("发票回调数据处理：未查询到开票信息order_no：", data.OrderNo)
		return out, err
	}

	// B、更新order_invoice表相关字段信息
	go syncOrderInvoice(orderInvoice, data)

	// C、发票信息存储
	var invoiceDetail models.OrderInvoiceDetail
	db.Where("order_no = ? AND invoice_id = ?", data.OrderNo, data.InvoiceId).Get(&invoiceDetail)
	// InvoiceType 诺税通是没有返回，税控中台会去处理，防止未作处理，根据金额正负判断，以免影响后续红冲操作
	if data.InvoiceType != 1 && data.InvoiceType != 2 {
		data.InvoiceType = 1
		if data.TaxAmountTotal < 0 {
			data.InvoiceType = 2
		}
	}
	invoiceDetail.OrderNo = data.OrderNo
	invoiceDetail.OrderSn = orderInvoice.OrderSn
	invoiceDetail.InvoiceCode = data.InvoiceCode
	invoiceDetail.InvoiceId = data.InvoiceId
	invoiceDetail.TotalAmount = cast.ToString(data.TaxAmountTotal)
	invoiceDetail.CanRefundTotalAmount = cast.ToString(data.TaxAmountTotal)
	invoiceDetail.PdfUrl = data.PdfUrl
	invoiceDetail.Type = data.InvoiceType
	invoiceDetail.Status = data.InvoiceStatus
	invoiceDetail.Data = data.Data
	if invoiceDetail.Id > 0 {
		invoiceDetail.UpdatedAt = time.Now()
		_, err = db.Where("id = ?", invoiceDetail.Id).Update(&invoiceDetail)
	} else {
		_, err = db.Insert(&invoiceDetail)
	}

	if err != nil {
		out.Code = 400
		out.Message = "发票信息存储失败"
		glog.Error("发票回调数据处理：invoice_detail保存失败：", err.Error())
		return out, nil
	}
	out.Code = 200
	out.Message = "success"
	return out, nil
}

// 更新order_invoice表相关字段信息
func syncOrderInvoice(orderInvoice models.OrderInvoice, data dto.InvoiceDetailData) {
	db := GetDBConn()
	// 开票和红冲状态值都是一样
	if data.InvoiceStatus == 1 { // 开票成功
		orderInvoice.Status = 1
		if !strings.Contains(orderInvoice.Invoices, data.PdfUrl) { // 发票pdf地址
			orderInvoice.Invoices = strings.Trim(orderInvoice.Invoices+","+data.PdfUrl, ",")
		}
	} else { // 失败或作废
		// 是开票回调才更新，红冲回调不更新
		if data.InvoiceType == 1 {
			orderInvoice.Status = 3
		}
		orderInvoice.FailReason = data.ErrorMessage
	}

	orderInvoice.UpdatedAt = time.Now()
	if _, err := db.Where("id = ?", orderInvoice.Id).Update(&orderInvoice); err != nil {
		glog.Error("发票回调数据处理：更新order_invoice表失败：", err.Error())
	}
}

// 通过发票请求获取订单信息
func getOrderMainByInvoiceOrderSn(orderSn string, scrmId string) (orderMain *models.OrderMain, err error) {
	db := GetDBConn()
	orderMain = &models.OrderMain{}

	query := db.Table("dc_order.order_main").Where("is_virtual = 0")
	// 扫码过来的，需要判断订单号及时间戳符合
	if strings.Contains(orderSn, "_") {
		strs := strings.Split(orderSn, "_")
		query.Where("parent_order_sn = ?", strs[0]).
			Where("member_tel = ?", strs[1])
	} else {
		if len(scrmId) == 0 {
			err = errors.New("用户不能为空")
			return
		}
		query.Where("member_id = ?", scrmId).
			And("order_sn = ? or old_order_sn = ?", orderSn, orderSn)
	}
	has, err := query.Get(orderMain)

	if err != nil {
		err = errors.New("查询订单出错 " + err.Error())
		return
	}
	if !has {
		err = errors.New("订单信息无效")
	}
	return
}

// CheckCanInvoiceByOrder 通过订单查询是否能开票
func CheckCanInvoiceByOrder(order *models.OrderMain) (canInvoice bool, err error) {

	if kit.IsDebug {
		glog.Info("测试环境跳过判断是否可以开票！")
		return true, nil
	}
	// 仅瑞鹏类型的店铺可以开票
	if order.AppChannel != 1 {
		return
	}
	_, companyCode := channelAndCompanyCode(*order, getOrderChannel)

	_, err = zilong.Post("tax-control-api/other/invoice/subject/check", map[string]interface{}{
		"companyCode": companyCode,
	}, &canInvoice)

	return
}

package models

import "time"

// CommissionPerformance 员工业绩表
type CommissionPerformance struct {
	Id              int64     `xorm:"pk autoincr BIGINT" json:"id"`               // 主键ID
	OrderId         string    `xorm:"not null default '' VARCHAR(64)" json:"order_id"`  // 订单ID
	OrderNo         string    `xorm:"not null default '' VARCHAR(64)" json:"order_no"`  // 订单编号
	EmployeeId      int64     `xorm:"not null default 0 BIGINT" json:"employee_id"`    // 员工ID
	RealName        string    `xorm:"not null default '' VARCHAR(64)" json:"real_name"` // 员工姓名
	CustomerName    string    `xorm:"not null default '' VARCHAR(64)" json:"customer_name"` // 客户姓名
	StoreId         int64     `xorm:"not null default 0 BIGINT" json:"store_id"`     // 店铺ID
	ProductType     int       `xorm:"not null default 0 INT" json:"product_type"`    // 商品类型: 1-商品, 2-服务, 3-寄养, 4-活体
	SkuId           string    `xorm:"not null default '' VARCHAR(64)" json:"sku_id"`   // 商品SKU ID
	ProductName     string    `xorm:"not null default '' VARCHAR(128)" json:"product_name"` // 商品名称
	UnitPrice       int       `xorm:"not null default 0 INT" json:"unit_price"`     // 单价(分)
	Quantity        int       `xorm:"not null default 0 INT" json:"quantity"`      // 数量
	SalesAmount     int64     `xorm:"not null default 0 BIGINT" json:"sales_amount"`  // 销售金额(分)
	CommissionRate  float64       `xorm:"not null default 0 INT" json:"commission_rate"` // 提成比例(万分比)
	CommissionAmount int64    `xorm:"not null default 0 BIGINT" json:"commission_amount"` // 提成金额(分)
	SetupId         int64     `xorm:"not null default 0 BIGINT" json:"setup_id"`     // 提成设置ID
	SetupName       string    `xorm:"not null default '' VARCHAR(128)" json:"setup_name"` // 提成设置名称
	OrderTime       time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' DATETIME" json:"order_time"` // 订单时间
	Operator        string    `xorm:"not null default '' VARCHAR(64)" json:"operator"`  // 操作人
	OperatorId      int64     `xorm:"not null default 0 BIGINT" json:"operator_id"`   // 操作人ID
	CreatedTime     time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' DATETIME created" json:"created_time"` // 创建时间
	UpdatedTime     time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' DATETIME updated" json:"updated_time"` // 更新时间
}

// TableName 返回表名
func (c *CommissionPerformance) TableName() string {
	return "eshop.commission_performance"
} 
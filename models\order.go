package models

import (
	"fmt"
	//"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"runtime"
	//"strings"
	"time"

	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
)

type Order struct {
	*OrderMain              `xorm:"extends"`
	ChildChannelId          string    `xorm:"not null default '''' comment('子渠道id，命名规则为channel_id+子渠道编码') VARCHAR(10)"`
	ChildChannelName        string    `xorm:"not null comment('子渠道名称') VARCHAR(20)"`
	PerformanceStaffName    string    `xorm:"not null default '''' comment('业绩所属员工姓名') VARCHAR(30)"`
	PerformanceOperatorName string    `xorm:"not null default '''' comment('业绩操作人姓名') VARCHAR(30)"`
	PerformanceOperatorTime time.Time `xorm:"default 'NULL' comment('业绩操作时间') DATETIME"`
	NoticeTime              int64     `xorm:"not null default 0 comment('订单拣货提醒通知时间') INT(11)"`
	Invoice                 string    `xorm:"not null default '''' comment('发票信息') VARCHAR(255)"`
	BuyerMemo               string    `xorm:"not null default '''' comment('买家留言') VARCHAR(255)"`
	SellerMemo              string    `xorm:"not null default '''' comment('卖家留言') VARCHAR(255)"`
	GyDeliverStatus         int32     `xorm:"not null default 0 comment('管易发货状态,0未发货,1已发货,2部分发货') TINYINT(4)"`
	DeliveryRemark          string    `xorm:"not null default '''' comment('配送备注') VARCHAR(255)"`
	PushDelivery            int32     `xorm:"not null default 0 comment('是否推送美团配送,1是0否') TINYINT(4)"`
	PushDeliveryReason      string    `xorm:"not null default '''' comment('推送美团配送失败原因') VARCHAR(255)"`
	PushThirdOrder          int32     `xorm:"not null default 0 comment('是否推送子龙或全渠道,1是0否') TINYINT(4)"`
	PushThirdOrderReason    string    `xorm:"not null default '''' comment('推送子龙或全渠道失败原因') TEXT"`
	SplitOrderResult        int32     `xorm:"not null default 0 comment('拆分订单结果,0拆单中1成功2失败') TINYINT(4)"`
	SplitOrderFailReason    string    `xorm:"not null default '''' comment('拆分订单失败原因') VARCHAR(255)"`
	AcceptUsername          string    `xorm:"not null default '''' comment('接单人') VARCHAR(50)"`
	AcceptTime              time.Time `xorm:"default 'NULL' comment('美团接单时间') DATETIME"`
	IsPicking               int32     `xorm:"not null default 0 comment('是否拣货,0否1是') TINYINT(4)"`
	PickingTime             time.Time `xorm:"default 'NULL' comment('美团拣货时间') DATETIME"`
	ExpectedTime            time.Time `xorm:"default 'NULL' comment('预计送达时间') DATETIME"`
	LockedStock             int32     `xorm:"not null default 0 comment('是否锁定库存,0否1是') TINYINT(4)"`
	Extras                  string    `xorm:"not null default '''' comment('美团附加优惠信息json') VARCHAR(3000)"`
	RemindTime              string    `xorm:"not null default '''' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255)"`
	Latitude                float64   `xorm:"not null default 0.000000 comment('收货地址纬度') DOUBLE(10,6)"`
	Longitude               float64   `xorm:"not null default 0.000000 comment('收货地址经度') DOUBLE(10,6)"`
	PickupCode              string    `xorm:"not null default '''' comment('取货码') VARCHAR(30)"`
	PayType                 string    `xorm:"not null default '''' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20)"`
	IsAdjust                int32     `xorm:"not null default 0 comment('是否订单调整，1是0否') TINYINT(4)"`
	PowerId                 int32     `xorm:"not null default 0 comment('电商助力id') INT(11)"`
	GoodsReturnDeliveryId   int64     `xorm:"not null default 0 comment('需要确认商品返回的配送单号（有需要确认的才会有值，没有则不需要）') BIGINT(32)"`
	PickupStationId         int32     `xorm:"default 0 comment('社区团购自提点id') INT(11)"`
	ConsultOrderSn          string    `xorm:"not null default '''' comment('医疗互联网订单号\处方ID\推荐ID') index VARCHAR(50)"`
}

type DelOrder struct {
	OrderId          string `xorm:"not null pk default ''uuid()'' comment('订单id') VARCHAR(36)"`
	OldOrderSn       string `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
	OrderSn          string `xorm:"not null default '''' comment('订单号') index VARCHAR(50)"`
	OrderStatus      int    `xorm:"not null default 0 comment('订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成') index index(order_status_2) INT(11)"`
	OrderStatusChild int    `xorm:"default NULL comment('子状态：
														20101(美团默认)未接单;
														20102已接单;
														20103配送中;
														20104已送达;
														20105已取货;
														20106已完成;
														20107已取消;
														10201(电商默认)未付款;
														20201待发货;
														20202全部发货;
														20203确认收货;
														20204部分发货;') INT(11)"`
	ShopId           string    `xorm:"not null default '''' comment('商户或门店id(财务编码)') index(idx_shop_id_create_time) VARCHAR(80)"`
	ShopName         string    `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	MemberId         string    `xorm:"not null default '''' comment('会员id') index VARCHAR(50)"`
	MemberName       string    `xorm:"not null default '''' comment('会员名称') VARCHAR(50)"`
	MemberTel        string    `xorm:"not null default '''' comment('会员手机号') VARCHAR(20)"`
	ReceiverName     string    `xorm:"not null default '''' comment('收件人') VARCHAR(50)"`
	ReceiverState    string    `xorm:"not null default '''' comment('收件省') VARCHAR(50)"`
	ReceiverCity     string    `xorm:"not null default '''' comment('收件市') VARCHAR(50)"`
	ReceiverDistrict string    `xorm:"not null default '''' comment('收件区') VARCHAR(50)"`
	ReceiverAddress  string    `xorm:"not null default '''' comment('收件地址') VARCHAR(255)"`
	ReceiverPhone    string    `xorm:"not null default '''' comment('收件电话') VARCHAR(30)"`
	Privilege        int       `xorm:"not null default 0 comment('总优惠金额') INT(11)"`
	PayType          string    `xorm:"default '''' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20)"`
	ReceiverMobile   string    `xorm:"not null default '''' comment('收件手机') VARCHAR(30)"`
	GjpStatus        string    `xorm:"not null default '''' comment('管家婆状态NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货') VARCHAR(20)"`
	Total            int       `xorm:"not null default 0 comment('总金额（实际付款金额。加运费，加包装费，减优惠金额）') INT(11)"`
	GoodsTotal       int       `xorm:"not null default 0 comment('商品总金额（未加运费，未加包装费，服务费,减优惠金额）') INT(11)"`
	IsPay            int       `xorm:"not null default 0 comment('是否支付0否  1是') TINYINT(4)"`
	CreateTime       time.Time `xorm:" default 'current_timestamp()' comment('创建时间') index(idx_shop_id_create_time) index(order_status_2) DATETIME"`
	ConfirmTime      time.Time `xorm:"default 'NULL' comment('美团送达或电商确认收货时间(已完成)') DATETIME"`
	AcceptUsername   string    `xorm:"not null default '''' comment('接单人') VARCHAR(50)"`
	AcceptTime       time.Time `xorm:"default 'NULL' comment('美团接单时间') DATETIME"`
	IsPicking        int       `xorm:"default 0 comment('是否拣货1是 0否') TINYINT(4)"`
	PickingTime      time.Time `xorm:"default 'NULL' comment('美团拣货时间') DATETIME"`
	ExpectedTime     time.Time `xorm:"default 'NULL' comment('预计送达时间') DATETIME"`
	DeliverTime      time.Time `xorm:"default 'NULL' comment('美团配送或电商发货时间') DATETIME"`
	PayTime          time.Time `xorm:"default 'NULL' comment('支付时间') DATETIME"`
	PaySn            string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	OrderType        int       `xorm:"not null default 1 comment('订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送') INT(11)"`
	Freight          int       `xorm:"not null default 0 comment('总运费') INT(11)"`
	Source           int       `xorm:"not null default 0 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店（子龙）') TINYINT(4)"`
	Invoice          string    `xorm:"not null default '''' comment('发票信息') VARCHAR(255)"`
	WarehouseCode    string    `xorm:"not null default '''' comment('仓库代码') VARCHAR(255)"`
	BuyerMemo        string    `xorm:"not null default '''' comment('买家留言') VARCHAR(255)"`
	SellerMemo       string    `xorm:"not null default '''' comment('卖家留言') VARCHAR(255)"`
	GyDeliverStatus  int       `xorm:"not null default 0 comment('管易发货状态0未发货 1已发货 2部分发货') TINYINT(4)"`
	GyOrderSn        string    `xorm:"not null default '''' comment('管易订单号、第三方订单号') VARCHAR(50)"`
	//OrderFrom            int       `xorm:"not null default 1 comment('1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团 10饿了么') TINYINT(4)"`
	DeliveryType         int       `xorm:"not null default 1 comment('1快递 2外卖 3自提 4同城送') TINYINT(4)"`
	DeliveryRemark       string    `xorm:"not null default '''' comment('配送备注') VARCHAR(255)"`
	PushDelivery         int       `xorm:"default 0 comment('是否推送美团配送1是 0否') TINYINT(4)"`
	PushThirdOrder       int       `xorm:"default 0 comment('是否推送子龙或全渠道1是 0否') TINYINT(4)"`
	PushDeliveryReason   string    `xorm:"not null default '''' comment('推送美团配送失败原因') VARCHAR(255)"`
	PushThirdOrderReason string    `xorm:"not null default '''' comment('推送子龙或全渠道失败原因') VARCHAR(2000)"`
	Extras               string    `xorm:"not null default '''' comment('美团附加优惠信息json') VARCHAR(3000)"`
	PackingCost          int       `xorm:"not null default 0 comment('包装费') INT(11)"`
	ServiceCharge        int       `xorm:"not null default 0 comment('服务费') INT(11)"`
	CancelReason         string    `xorm:"not null default '''' comment('取消订单原因') VARCHAR(255)"`
	CancelTime           time.Time `xorm:"default 'NULL' comment('取消订单时间') DATETIME"`
	RemindTime           string    `xorm:"not null default '''' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255)"`
	Latitude             float64   `xorm:"not null default 0.000000 comment('收货地址纬度') DOUBLE(10,6)"`
	Longitude            float64   `xorm:"not null default 0.000000 comment('收货地址经度') DOUBLE(10,6)"`
	PickupCode           string    `xorm:"not null default '''' comment('取货码') VARCHAR(30)"`
	TotalWeight          int       `xorm:"not null default 0 comment('总重量') INT(11)"`
	LogisticsCode        string    `xorm:"not null default '''' comment('配送方式编码,如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等 饿了么物流类型：1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包 5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送') VARCHAR(20)"`
	LockedStock          int       `xorm:"not null default 0 comment('是否锁定库存0否 1是') TINYINT(4)"`
	ChannelId            int       `xorm:"default 0 comment('渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店') INT(11)"`
	UserAgent            int       `xorm:"default 0 comment('来源') INT(11)"`
	WarehouseName        string    `xorm:"default '''' comment('仓库名称') VARCHAR(255)"`
	IsAdjust             int       `xorm:"not null default 0 comment('是否订单调整1：是 0否') TINYINT(4)"`
	FreightPrivilege     int       `xorm:"not null default 0 comment('运费优惠金额') INT(11)"`
	DisId                int       `xorm:"not null default 0 comment('分销员ID') INT(11)"`
}

func (order *Order) ToUpetDjOrderDto() *oc.UpetDjOrderDto {

	defer func() {
		if err := recover(); err != nil {
			glog.Error("异常信息捕获pw：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER] %v %s\n", err, stack[:length])

		}
	}()
	var dto = new(oc.UpetDjOrderDto)
	dto.OrderId = cast.ToString(order.OrderMain.Id)
	dto.OrderNo = order.OrderMain.OrderSn
	dto.ParentOrderSn = order.OrderMain.ParentOrderSn
	dto.ShopId = order.ShopId
	dto.ShopName = order.ShopName
	dto.Remarks = order.BuyerMemo
	dto.PickupCode = order.PickupCode
	dto.TotalMoney = float32(kit.FenToYuan(order.Total))
	dto.Privilege = kit.FenToYuan(order.Privilege)
	dto.CreateDateTime = order.CreateTime.Format(kit.DATETIME_LAYOUT)
	dto.IsVirtual = order.IsVirtual
	//dto.ConfirmTime=order.ConfirmTime.Format(utils.DATETIME_LAYOUT)
	dto.PayMode = order.PayMode

	dto.IsPay = order.IsPay
	dto.ChannelId = order.ChannelId

	// 到家返回处方单号
	if dto.ChannelId == 1 {
		dto.ConsultOrderSn = order.ConsultOrderSn
	}

	//str := config.GetString("awen.order.exclude.shop")
	//split := strings.Split(str, ",")
	//flag := false
	//
	//
	//for _, v := range split {
	//	if v == order.ShopId {
	//		flag = true
	//	}
	//}
	//
	//
	//// 是配置的门店 && 付过款的
	//if order != nil {
	//	if flag && order.IsPay == 1 { // 在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款
	//		dto.NoDisplayAfterPayment = 1
	//	}
	//}

	return dto
}

func (order *Order) ToUpetDjDeliveryDto() *oc.UpetDjDeliveryDto {
	var deliveryDto = new(oc.UpetDjDeliveryDto)
	deliveryDto.Receiveraddress = order.ReceiverAddress
	deliveryDto.Freight = kit.FenToYuan(order.Freight)
	deliveryDto.Receivername = order.ReceiverName
	if (order.ChannelId == 1 || order.ChannelId == 9) && order.DeliveryType == 3 {
		deliveryDto.Receivername = ""
	}
	deliveryDto.Receivermobile = order.ReceiverPhone
	deliveryDto.Receiverstate = order.ReceiverState
	deliveryDto.Receivercity = order.ReceiverCity
	deliveryDto.Receiverdistrict = order.ReceiverDistrict
	deliveryDto.DeliveryType = order.DeliveryType
	//DeliveryTypeName为空字符串时，前端使用自己的逻辑显示
	deliveryDto.DeliveryTypeName = order.GetDeliveryTypeText()
	if order.ExpectedTime.Year() >= 2020 {
		deliveryDto.DeliveryTime = fmt.Sprintf("%s %s", order.ExpectedTime.Format(kit.DATE_LAYOUT_SHORT_CN), order.ExpectedTime.Format(kit.TIME_LAYOUT_SHORT))
	}
	if order.ExpectedTime.Year() >= 2020 && order.DeliveryType == 3 && (order.ChannelId == 1 || order.ChannelId == 9) {
		if time.Now().Format("01-02") == order.ExpectedTime.Format("01-02") {
			deliveryDto.DeliveryTime = order.ExpectedTime.Format("15:04")
		} else {
			deliveryDto.DeliveryTime = order.ExpectedTime.Format("01-02 15:04")
		}
	}
	if (order.ChannelId == 1 || order.ChannelId == 9) && order.PickupStationId > 0 {
		deliveryDto.DeliveryTypeName = "无接触配送"
		deliveryDto.DeliveryTime = order.ExpectedTime.Add(1 * time.Second).Format("2006年01月02日前")
	}

	deliveryDto.Latitude = order.Latitude
	deliveryDto.Longitude = order.Longitude
	return deliveryDto
}

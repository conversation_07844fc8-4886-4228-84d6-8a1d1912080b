package tasks

import (
	"fmt"
	"time"

	"order-center/models"
	"order-center/services"
	"order-center/utils"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

type TaskOrderPickNotice struct {
	services.BaseService
}

func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("0 */1 * * * *", func() {
		service := TaskOrderPickNotice{}
		service.GetOrderPickNoticeResult()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s *TaskOrderPickNotice) GetOrderPickNoticeResult() {
	redisConn := services.GetRedisConn()
	//兼容同步库存的逻辑
	lockMessage := "task:lock:syncmessage"
	delRedisSetNx := utils.DelRedisSetNx(redisConn, lockMessage, 15) //5分钟的判断
	if delRedisSetNx {
		lockRes := redisConn.SetNX(lockMessage, time.Now().Unix(), 0).Val()
		if !lockRes {
			return
		}
	} else {
		return
	}
	defer redisConn.Del(lockMessage)

	db := services.GetDBConn()

	now := time.Now()
	m, _ := time.ParseDuration("75m")
	expectedTime := kit.GetTimeNow(now.Add(m))

	var orderList []models.OrderMain
	db.SQL(`
		SELECT order_main.*
		FROM order_main 
		INNER JOIN order_detail ON order_main.order_sn = order_detail.order_sn
		WHERE order_type = 2 
		AND parent_order_sn > 0 
		AND is_virtual = 0 
		AND order_status = 20 
		AND notice_time=0 
		AND expected_time <= ? 
		AND expected_time > ?
	`, expectedTime, now).Find(&orderList)
	if len(orderList) == 0 {
		return
	}

	var err error
	for _, v := range orderList {
		if err = services.MessageCreate(&models.Message{
			OrderId:     v.OrderSn,
			FinanceCode: v.ShopId,
			MessageType: 2,
			Msg:         fmt.Sprintf("【拣货提醒】您的订单：%s需要拣货！", v.OrderSn),
		}); err == nil {
			if _, err = db.In("order_sn", []string{v.ParentOrderSn, v.OrderSn}).Update(&models.OrderDetail{
				NoticeTime: time.Now().Unix(),
			}); err != nil {
				glog.Error(v.OrderSn, ", 订单拣货提醒通知更新失败！", err)
			}
		}
	}
}

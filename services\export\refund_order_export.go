package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/oc"
	"order-center/services"
	"strconv"
	"time"
)

/*
退货退款管理相关逻辑
*/
type RefundOrderExport struct {
	F           *excelize.File
	SheetName   string
	taskParams  *oc.RefundOrderInfoRequest
	OrgId       int32
	FinanceCode string
}

// 逻辑
// 退款订单导出
func (e *RefundOrderExport) DataExport(taskParams string) (nums int, err error) {
	glog.Info("阿闻管家退款订单列表导出数据参数入口：", taskParams)
	e.taskParams = new(oc.RefundOrderInfoRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var refundOrderList, details []*oc.RefundOrderExport

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	e.taskParams.Orgid = cast.ToInt64(e.OrgId)
	e.taskParams.FinancialCode = e.FinanceCode
	for {
		details, err = services.RefundOrderExport(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		refundOrderList = append(refundOrderList, details...)
		glog.Info("details length - pagesize length", len(details), e.taskParams.PageSize)
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(refundOrderList))
	nums = len(refundOrderList)
	var n string
	for k := range refundOrderList {
		//平台服务费 平台补贴 商家结算金额
		platformService := services.PlatformService(refundOrderList[k].OrderSn, refundOrderList[k].RefundSn)
		n = strconv.Itoa(k + 2)
		//退款单号
		e.F.SetCellValue(e.SheetName, "A"+n, refundOrderList[k].RefundSn)
		//订单类型 实物订单还是虚拟订单
		e.F.SetCellValue(e.SheetName, "B"+n, refundOrderList[k].OrderTypeName)
		//子订单号
		e.F.SetCellValue(e.SheetName, "C"+n, refundOrderList[k].OrderSn)
		//父订单号
		if refundOrderList[k].ChannelId == 1 {
			e.F.SetCellValue(e.SheetName, "D"+n, refundOrderList[k].ParentOrderSn) //OldOrderSn
		} else {
			e.F.SetCellValue(e.SheetName, "D"+n, refundOrderList[k].RefundOrderSn) //OldOrderSn
		}
		//外部订单号
		e.F.SetCellValue(e.SheetName, "E"+n, refundOrderList[k].GyOrderSn)
		//退款方式
		e.F.SetCellValue(e.SheetName, "F"+n, services.Refundtype[refundOrderList[k].RefundType])
		//退款金额
		e.F.SetCellValue(e.SheetName, "G"+n, refundOrderList[k].RefundAmount)
		//商家结算金额
		if refundOrderList[k].ChannelId == 2 {
			e.F.SetCellValue(e.SheetName, "H"+n, kit.FenToYuan(platformService.MerchantIncome))
			//平台补贴
			e.F.SetCellValue(e.SheetName, "I"+n, kit.FenToYuan(platformService.Allowance))
			//平台服务费
			e.F.SetCellValue(e.SheetName, "J"+n, kit.FenToYuan(platformService.ServiceCharge))
		}
		//快递单号
		e.F.SetCellValue(e.SheetName, "K"+n, refundOrderList[k].ExpressNum)
		//订单来源
		e.F.SetCellValue(e.SheetName, "L"+n, services.OrderFrom[refundOrderList[k].ChannelId])
		//退款状态
		e.F.SetCellValue(e.SheetName, "M"+n, services.RefundState[refundOrderList[k].RefundState])
		//创建时间
		e.F.SetCellValue(e.SheetName, "N"+n, refundOrderList[k].CreateTime)
		//完成时间
		e.F.SetCellValue(e.SheetName, "O"+n, refundOrderList[k].UpdateTime)
		//退款商品名
		e.F.SetCellValue(e.SheetName, "P"+n, refundOrderList[k].ProductName)
		//备注
		e.F.SetCellValue(e.SheetName, "Q"+n, refundOrderList[k].RefundRemark)
		//财务编码
		e.F.SetCellValue(e.SheetName, "R"+n, refundOrderList[k].ShopId)
		//店铺名称
		e.F.SetCellValue(e.SheetName, "S"+n, refundOrderList[k].ShopName)
		//仓库类型(门店类型)
		e.F.SetCellValue(e.SheetName, "T"+n, refundOrderList[k].Category)
		//仓库名称
		e.F.SetCellValue(e.SheetName, "U"+n, refundOrderList[k].WarehouseName)
		//店铺类型
		var ShopType string
		if refundOrderList[k].AppChannel == 1 {
			ShopType = "新瑞鹏"
		} else if refundOrderList[k].AppChannel == 12 {
			ShopType = "宠物SAAS平台"
		} else {
			ShopType = "TP代运营"
		}
		e.F.SetCellValue(e.SheetName, "V"+n, ShopType)
	}
	e.F.Save()

	return
}

// 设置表头
func (e *RefundOrderExport) SetSheetName() {
	nameList := []string{"退款单号", "订单类型", "子订单号", "父订单号", "外部订单号", "退款方式", "退给用户金额", "商家结算金额", "平台补贴", "平台服务费", "快递单号", "订单来源", "退款状态", "创建时间", "完成时间", "退款商品", "备注", "财务编码", "店铺名称", "所属仓库类型", "仓库名称", "店铺类型"}
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *RefundOrderExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("退款订单(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

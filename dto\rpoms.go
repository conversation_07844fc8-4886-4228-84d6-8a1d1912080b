package dto

type AddOmsOrderHttpRequest struct {
	OmsOrderMain
	OmsOrderDetail
	OrderProduct   []*OmsOrderProduct   `json:"order_product"`
	OrderPromotion []*OmsOrderPromotion `json:"order_promotion"`
	OrderPay       *OmsOrderPay         `json:"order_pay"`
}

//创建订单时上传的订单参数
type OmsOrderMain struct {
	OrderSn               string `json:"order_sn"`                // 渠道订单号(推送给oms平台的推送方订单，如阿闻订单号)
	ChannelOrderSn        string `json:"channel_order_sn"`        // 渠道订单号(推送给oms平台的推送方订单，如阿闻订单号)
	TradeOrderSn          string `json:"trade_order_sn"`          // 交易单号（在美团等第三方没有对接oms时 取美团订单号阿闻 就取阿闻的订单号）
	OrderStatus           int    `json:"order_status"`            // 订单状态：0已取消,1(默认)待审核,2待审核,3待出库 4审核拒绝 5全部出库 6已作废
	OrderSource           int32  `json:"order_source"`            // 订单来源 1阿闻到家,2美团,3饿了么,4京东到家 5物竞天择
	ShopId                string `json:"shop_id"`                 // 店铺id(财务编码)
	ShopName              string `json:"shop_name"`               // 商户名称
	SaleOrgCode           string `json:"sale_org_code"`           // 销售组织id
	SaleChannelId         int    `json:"sale_channel_id"`         // 销售渠道id 1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-竖屏 7其他
	DeliveryOrgCode       string `json:"delivery_org_code"`       // 发货组织id
	DeliveryWarehouseCode string `json:"delivery_warehouse_code"` // 发货仓库id
	SaleOrgName           string `json:"sale_org_name"`           // 销售组织名称
	DeliveryOrgName       string `json:"delivery_org_name"`       // 发货组织名称
	DeliverWarehouseName  string `json:"deliver_warehouse_name"`  // 发货仓库名称
	MemberId              string `json:"member_id"`               // 客户id 阿闻订单取阿闻的顾客id
	MemberName            string `json:"member_name"`             // 客户名称
	Total                 int32  `json:"total"`                   // 商品总金额
	PayTotal              int32  `json:"pay_total"`               // 实际支付金额
	Income                int32  `json:"income"`                  // 实际支付金额
	GoodsTotal            int32  `json:"goods_total"`             //商品实际支付金额
	Privilege             int32  `json:"privilege"`               // 总优惠金额(分)
	PlatformPrivilege     int32  `json:"platform_privilege"`      // 平台惠金额(分)
	Freight               int32  `json:"freight"`                 // 物流费(分)
	FreightPrivilege      int32  `json:"freight_privilege"`       //运费优惠金额
	PackingFee            int32  `json:"packing_fee"`             // 包装费(分)
	ServiceFee            int32  `json:"service_fee"`             // 服务费(分)
	ContractFee           int32  `json:"contract_fee"`            //第三方平台履约费
	OrderType             int    `json:"order_type"`              // 订单类型1B2C订单(默认) 2:预定订单
	DeliveryType          int    `json:"delivery_type"`           // 配送类型,1快递,2外卖,3自提,4同城送
	PayType               int    `json:"pay_type"`                // 交易类型 1付款发货 2货到付款
	IsVirtual             int32  `json:"is_virtual"`              // 是否是虚拟订单，0否1是
	CreateUser            string `json:"create_user"`             // 创建人
	CreateType            int    `json:"create_type"`             // 创建类型 1系统推送 2手动创建 3批量导入 4:手动退款关联
	OrderTime             string `json:"order_time"`              // 单据日期 （接单时间）
	UserSubmitTime        string `json:"user_submit_time"`        // 用户下单时间
}

type OmsOrderDetail struct {
	ReceiverName     string  `json:"receiver_name"`     //收件人
	ReceiverProvince string  `json:"receiver_province"` //收件省
	ReceiverCity     string  `json:"receiver_city"`     //收件市
	ReceiverDistrict string  `json:"receiver_district"` //收件区
	ReceiverAddress  string  `json:"receiver_address"`  //收件地址
	ReceiverPhone    string  `json:"receiver_phone"`    //收件电话
	Latitude         float64 `json:"latitude"`          // 收货地址纬度
	Longitude        float64 `json:"longitude"`         // 收货地址经度
	BuyerMemo        string  `json:"buyer_memo"`        //买家留言
	SellerMemo       string  `json:"seller_memo"`       //卖家留言
	Note             string  `json:"note"`              // 备注
	LogisticsName    string  `json:"logistics_name"`    // 物流名称(物流类型)
	LogisticsId      string  `json:"logistics_id"`      // 物流单号
	TaxRate          int     `json:"tax_rate"`          // 税率 特指物流税率
	ArriveTime       string  `json:"arrive_time"`       // 预计到达时间
	CarNumber        string  `json:"car_number"`        // 车牌号
}

type OmsOrderProduct struct {
	ItemNum          string `json:"third_sku_id"`       // 货号
	MarkingPrice     int32  `json:"marking_price"`      // 商品原单价
	ChannelProductId string `json:"channel_product_id"` // 外部商品id 用于区分相同商品不同记录
	PayPrice         int32  `json:"pay_price"`          // 优惠后的单价
	Total            int32  `json:"total"`              // 优惠前金额 marking_price * number
	PaymentTotal     int32  `json:"payment_total"`      // 实付金额
	PrivilegeTotal   int32  `json:"privilege_total"`    // 优惠金额  优惠前金额-实付金额
	SkuPayTotal      int32  `json:"sku_pay_total"`      // sku实付总金额
	Number           int32  `json:"number"`             // 数量
	IsFree           int32  `json:"is_free"`            // 是否赠品 0 否 1是
}

type RpOmsBaseResponse struct {
	Message string `json:"message"` // 1收件人
	Code    int    `json:"code"`
}

// 退款单推送
//创建接口
type OrderRefundHttpRequest struct {
	CreateType          int    `json:"create_type"`           // 1系统推送
	ChannelRefundSn     string `json:"channel_refund_sn"`     // 渠道退款号
	ChannelOrderSn      string `json:"channel_order_sn"`      // 渠道订单号(推送给oms平台的推送方订单，如阿闻订单号)
	RefundType          int32  `json:"refund_type"`           // 退款类型 1退款 2退货退款
	RefundWarehouseCode string `json:"refund_warehouse_code"` // 退货仓库CODE
	RefundReason        string `json:"refund_reason"`         // 退款原因
	RefundAmount        int32  `json:"refund_amount"`         // 退款金额
	RefundFreight       int32  `json:"refund_freight"`        //退的运费
	RefundPackingFee    int32  `json:"refund_packing_fee"`    //退的包装费金额
	RefundServiceFee    int32  `json:"refund_service_fee"`    //退的平台服务费
	RefundContractFee   int32  `json:"refund_contract_fee"`   //退的履约服务费金额
	PlatformPrivilege   int32  `json:"platform_privilege"`    //退的平台优惠
	FreightPrivilege    int32  `json:"freight_privilege"`     //配送费优惠
	Note                string `json:"note"`                  // 备注
	Entire              bool   `json:"entire"`                //是否全部退款 是 true ; 否 false
	//商品信息
	OrderRefundProduct []*OrderRefundProduct `json:"order_refund_detail"`
}

type OrderRefundProduct struct {
	ItemNum      string `json:"third_sku_id"`  // 退款商品货号
	RefundNumber int32  `json:"refund_number"` // 退款数量
	RefundPrice  int32  `json:"refund_price"`  // 退款价格
	RefundAmount int32  `json:"refund_amount"` // 退款总计
	IsFree       int    `json:"is_free"`       // 是否赠品 0 否 1是
	Id           int    `json:"id"`            // order_product 主键id
}

type OmsOrderPromotion struct {
	PromotionId    int    `json:"promotion_id"`    // 优惠活动id
	PromotionCode  string `json:"promotion_code"`  // 活动编码
	PromotionType  int    `json:"promotion_type"`  // 优惠活动类型
	PromotionTitle string `json:"promotion_title"` // 活动名称
	PoiCharge      int    `json:"poi_charge"`      // 商家优惠
	PtCharge       int    `json:"pt_charge"`       // 平台优惠
	PromotionFee   int    `json:"promotion_fee"`   // 总优惠金额(商家加平台总和)
	StartTime      string `json:"start_time"`      // 开始时间
	EndTime        string `json:"end_time"`        // 结束时间
}

type OmsOrderPay struct {
	OrderSn   string `json:"order_sn"`   // 订单号
	PayType   int    `json:"pay_type"`   // 交易类型 1付款发货 2货到付款
	PaySn     string `json:"pay_sn"`     // 支付单号
	PayMode   int32  `json:"pay_mode"`   // 支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付
	PayStatus int    `json:"pay_status"` // 支付状态 1未支付 2已支付
	Currency  int    `json:"currency"`   // 币种 1人民币 2美元
	PayTime   string `json:"pay_time"`   // 支付时间
	PayAmount int32  `json:"pay_amount"` // 实际支付金额
}

type GenerateSign struct {
	AppId    int32
	Secret   string
	IsJson   bool
	Param    map[string]string
	JsonBody string
}

func RefundReasonMap(reason string) int {
	switch reason {
	case "计划有变，我不想要了":
		return 1
	case "我买错了/填错了":
		return 2
	case "商家联系我说没货了":
		return 3
	case "商家错送、漏送":
		return 4
	case "商品出问题（质量、重量、图片不符等)":
		return 5
	case "商家无法提供服务":
		return 6
	case "配送太慢，不要了":
		return 7
	case "商家其他原因":
		return 8
	}
	return 0
}

package services

import (
	"order-center/models"
	"testing"
)

func TestSendWebhookNotify(t *testing.T) {
	type args struct {
		task      *models.OrderRedoTask
		RedoLimit int
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "发送机器人消息"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &models.OrderRedoTask{}
			app.RedoCount = 4
			app.OrderSn = "12121111"
			app.RedoType = 3
			SendWebhookNotify(app, 5)
		})
	}
}

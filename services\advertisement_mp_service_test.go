package services

import (
	"context"
	"order-center/models"
	"order-center/proto/oc"
	"reflect"
	"testing"
)

func TestAdvertisementMpService_AddAdvertisementMpRecord(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.AddAdvertisementMpRecordRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.MpBaseResponse
		wantErr bool
	}{
		{name: "添加广告MP记录接口"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AdvertisementMpService{}
			rep := oc.AddAdvertisementMpRecordRequest{
				OrderSn:    "4100000008679166",
				UserId:     "656d18c2dcf044eba69c49a53bb7fa31",
				ActionType: 3,
				Url:        "",
				ClickId:    "wx0kwl7vk4stuzjg",
				UserAgent:  5,
				ActionTime: 1630402254,
				ChannelId:  5,
			}
			got, err := a.AddAdvertisementMpRecord(context.Background(), &rep)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddAdvertisementMpRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddAdvertisementMpRecord() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPushMpRecordNotify(t *testing.T) {

	engine := GetDBConn()
	var paynotifylist []models.AdvertisementMpRecord
	engine.Table("advertisement_mp_record").Where("deal_status = 0 and deal_num < 5").Find(&paynotifylist)
	if len(paynotifylist) == 0 {
		return
	}

	for _, item := range paynotifylist {
		PushMpRecordNotify(&item)
	}
	//type args struct {
	//	in *models.AdvertisementMpRecord
	//}
	//tests := []struct {
	//	name string
	//	args args
	//}{
	//	// TODO: Add test cases.
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//	})
	//}
}

func Test_getPushData(t *testing.T) {
	type args struct {
		in *oc.AddAdvertisementMpRecordRequest
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getPushData(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("getPushData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getPushData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

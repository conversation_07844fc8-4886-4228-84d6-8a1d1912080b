//被import进别的proto文件时候，同名message会被覆写
syntax = "proto3";
package pay;

message BaseResponse{
   //状态码
   int32 code = 1;
   //消息
   string message = 2;
   //错误信息
   string error = 3;
}

message ScanHeadBase{
    //商户号
    string merc_id = 1;
	//机构号
	string org_id = 2;
	//传机具编号（tsn）
	string trm_sn =3;
	//终端号，传标准终端绑定接口返回的dyTermNo
	string trm_id =4;
}

message PayHeadBase{
    //消息类型,交易类型：消费、撤销、退货、查询的消息类型统一上送 0200;签名主密钥获取接口是 0800
    string msgType = 1;
    //机具编号,智能 POS 终端的机具编号
    string termidm = 2;
    //商户号,电银内部商户号
    string mercode = 3;
    //终端号,电银内部终端号
    string termcde = 4;
    //设备唯一标识,终端设备的唯一标识
    string imei = 5;
    //交易发起时间,时间格式:年月日时分秒YYYYMMDDHHMMSS
    string sendTime = 6;
    //基站信息,格式：MCC|MNC|LAC|CID|STH,wifi信号时为MAC|STH (STH 为信号强度)
    string stationInfo = 7;
}
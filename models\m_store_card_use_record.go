package models

import (
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/shopspring/decimal"
)

type MStoreCardUseRecord struct {
	Id                  int64     `json:"id" xorm:"pk autoincr not null comment('唯一数据id') BIGINT 'id'"`
	ChainId             int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId            int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RecordId            int64     `json:"record_id" xorm:"not null default 0 comment('卡记录id') BIGINT 'record_id'"`
	CardId              int64     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo              string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName            string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	OperateType         string    `json:"operate_type" xorm:"not null default '' comment('操作类型:CONSUMPTION消费,TOP_UP充值,REFUND退款,RECONCILIATION调账,RETURN_CARD退卡') VARCHAR(16) 'operate_type'"`
	BalanceChange       float64   `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	PrincipalChange     float64   `json:"principal_change" xorm:"not null default '0.0000' comment('本金变动') DECIMAL(18) 'principal_change'"`
	GiftChange          float64   `json:"gift_change" xorm:"not null default '0.0000' comment('赠送金变动') DECIMAL(18) 'gift_change'"`
	Balance             float64   `json:"balance" xorm:"not null default '0.0000' comment('本金余额') DECIMAL(18) 'balance'"`
	GiftBalance         float64   `json:"gift_balance" xorm:"not null default '0.0000' comment('赠送金余额') DECIMAL(18) 'gift_balance'"`
	ReturnableGift      float64   `json:"returnable_gift" xorm:"not null default '0.0000' comment('可退赠金') DECIMAL(18) 'returnable_gift'"`
	ReturnablePrincipal float64   `json:"returnable_principal" xorm:"not null default '0.0000' comment('可退本金') DECIMAL(18) 'returnable_principal'"`
	OrderId             int64     `json:"order_id" xorm:"not null default 0 comment('订单id') BIGINT 'order_id'"`
	OrderNo             string    `json:"order_no" xorm:"not null default '' comment('订单编号') VARCHAR(32) 'order_no'"`
	CustomerId          int64     `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	EmployeeId          int64     `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	GiftPackage         string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	Remark              string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(200) 'remark'"`
	Status              string    `json:"status" xorm:"not null default '' comment('数据状态') VARCHAR(16) 'status'"`
	IsDeleted           bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy           int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime         time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy           int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime         time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (om *MStoreCardUseRecord) TableName() string {
	return "eshop_saas.m_store_card_use_record"
}

// 操作类型
const (
	OperateTypeConsumption    = "CONSUMPTION"    // 消费
	OperateTypeTopUp          = "TOP_UP"         // 充值
	OperateTypeRefund         = "REFUND"         // 退款
	OperateTypeReconciliation = "RECONCILIATION" // 调账
	OperateTypeReturnCard     = "RETURN_CARD"    // 退卡

	PIN_CARD     = "PIN_CARD"
	USE          = "USE"          // 使用中
	FREEZE       = "FREEZE"       // 冻结
	NOT_USE      = "NOT_USE"      // 不可用
	INEFFECTIVE  = "INEFFECTIVE"  // 未生效
	IN_THE_CARD  = "IN_THE_CARD"  // 退卡中
	RETIRED_CARD = "RETIRED_CARD" // 已退卡

)

// RestoreStoreCardAmount 恢复储值卡金额
func RestoreStoreCardAmount(session *xorm.Session, record *MStoreCardUseRecord, operatorId int64) error {
	// 1. 恢复储值卡金额
	// 金额取绝对值
	balanceChange := decimal.NewFromFloat(math.Abs(record.BalanceChange))
	giftChange := decimal.NewFromFloat(math.Abs(record.GiftChange))
	totalAmount := balanceChange.Add(giftChange)

	sql := `UPDATE eshop_saas.m_store_card_record 
		SET total_amount = total_amount + ?,
			principal = principal + ?,
			gift_amount = gift_amount + ?,
			refund_amount = refund_amount + ?,
			updated_by = ?,
			updated_time = NOW()
		WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE'`

	result, err := session.Exec(sql,
		totalAmount,
		balanceChange,
		giftChange,
		totalAmount,
		operatorId,
		record.CardNo,
		record.CustomerId)

	if err != nil {
		return fmt.Errorf("恢复储值卡金额失败: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新影响行数失败: %v", err)
	}

	if affected == 0 {
		return errors.New("储值卡记录已被修改,请重试")
	}

	return nil
}

// RefundStoreCard 创建储值卡退款记录
func RefundStoreCard(session *xorm.Session, record *MStoreCardUseRecord, operateInfo *OperateInfo) error {
	useRecord := &MStoreCardUseRecord{
		TenantId:      record.TenantId,
		ChainId:       record.ChainId,
		RecordId:      record.RecordId,
		CardId:        record.CardId,
		CardNo:        record.CardNo,
		CardName:      record.CardName,
		OperateType:   "REFUND",
		BalanceChange: math.Abs(record.BalanceChange),
		PrincipalChange: math.Abs(record.PrincipalChange),
		GiftChange:    math.Abs(record.GiftChange),
		Balance:       decimal.NewFromFloat(record.Balance).Add(decimal.NewFromFloat(math.Abs(record.BalanceChange))).InexactFloat64(),
		GiftBalance:   decimal.NewFromFloat(record.GiftBalance).Add(decimal.NewFromFloat(math.Abs(record.GiftChange))).InexactFloat64(),
		OrderId:       int64(operateInfo.OrderId),
		OrderNo:       operateInfo.OrderNo,
		CustomerId:    record.CustomerId,
		EmployeeId:    record.EmployeeId,
		Remark:        "订单退款",
		Status:        "SUCCESS",
		CreatedBy:     operateInfo.OperatorId,
		CreatedTime:   time.Now(),
		UpdatedBy:     operateInfo.OperatorId,
		UpdatedTime:   time.Now(),
	}

	if _, err := session.Insert(useRecord); err != nil {
		return fmt.Errorf("创建储值卡退款记录失败: %v", err)
	}

	return nil
}

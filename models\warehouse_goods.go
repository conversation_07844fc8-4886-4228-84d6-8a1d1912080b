package models

import (
	"time"
)

type WarehouseGoods struct {
	Id          int       `xorm:"not null pk autoincr comment('自增') INT(11)"`
	Goodsid     string    `xorm:"not null default ''0'' comment('商品id') VARCHAR(40)"`
	Stock       int       `xorm:"not null default 0 comment('商品库存') INT(11)"`
	Lastdate    time.Time `xorm:" default 'current_timestamp()' comment('最后更新时间') TIMESTAMP"`
	WarehouseId int       `xorm:"not null default 0 comment('仓库id') INT(11)"`
}

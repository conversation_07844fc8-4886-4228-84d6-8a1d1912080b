package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
	"order-center/proto/cc"
	"order-center/utils"
	"os"
	"time"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"google.golang.org/grpc/metadata"
	"order-center/models"
	"order-center/proto/oc"
)

//定时处理 美团订单数据
func TimingProcessingMtSubmitOrder(isRealTime bool) bool {
	db := GetDBConn()
	strSql := "select  *  from `order_mt_data` a  where a.`process_status`=1 and create_time<=DATE_SUB(SYSDATE(3),INTERVAL 10 SECOND)  order by `create_time` asc"
	if isRealTime {
		strSql = "select  *  from `order_mt_data` a  where a.`process_status`=1   order by `create_time` asc"
	}
	mtOrderData := make([]models.OrderMtData, 0)
	err := db.SQL(strSql).Find(&mtOrderData)
	if err != nil {
		glog.Error("定时处理获取美团订单数据错误:F:" + err.Error())
	}
	if len(mtOrderData) <= 0 {
		return false
	}

	//连接池勿关闭
	redisConn := GetRedisConn()

	for k, item := range mtOrderData {
		lockCard := "task:lock:mt_submit_order_data:" + item.OrderSn
		lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
		if !lockRes {
			continue
		}

		params := new(oc.MtAddOrderRequest)
		err = json.Unmarshal([]byte(item.Data), params)
		if err != nil {
			glog.Error("定时处理获取美团订单数据错误:J:"+item.OrderSn, err)
			mtOrderData[k].ProcessStatus = 2
			mtOrderData[k].Reason = "定时处理获取美团订单数据转换JSON错误"
		} else {
			//加入context渠道信息
			grpcContext := models.GrpcContext{Channel: models.PlatformChannel{ChannelId: 2, UserAgent: 6}}
			ctx := metadata.AppendToOutgoingContext(kit.SetTimeoutCtx(context.Background()), "grpc_context", kit.JsonEncode(grpcContext))

			glog.Info("定时处理获取美团订单开始：" + item.OrderSn)
			cart := CartService{}
			out, err := cart.MtSubmitOrder(ctx, params)

			if out != nil {
				glog.Info("定时处理获取美团订单返回："+item.OrderSn, kit.JsonEncode(out))
			} else {
				glog.Info("定时处理获取美团订单返回："+item.OrderSn, " nil")
			}

			if err != nil || out.Code != 200 {
				mtOrderData[k].ProcessStatus = 2
				if err != nil {
					mtOrderData[k].Reason = "调用MtSubmitOrder失败"
					glog.Error("定时处理获取美团订单数据错误:E2"+item.OrderSn, err.Error())
				} else {
					mtOrderData[k].Reason = out.Message
				}
			} else {
				mtOrderData[k].ProcessStatus = 3
			}
		}
		strSql = "update order_mt_data set `process_status`=?,reason=?  where order_sn=? "
		_, err = db.Exec(strSql, mtOrderData[k].ProcessStatus, mtOrderData[k].Reason, mtOrderData[k].OrderSn)
		if err != nil {
			glog.Error("定时处理获取美团订单数据错误:U2:"+item.OrderSn, err.Error())
		}

		redisConn.Del(lockCard)
	}
	return true
}

//获取门店自提码
func getPickCode(orderMain *models.OrderMain) string {
	redis := GetRedisConn()

	preKey := "order:order-day-number:" + time.Now().Format("20060102")
	rdsKey := preKey + ":shop:" + orderMain.ShopId
	numberKey := redis.Incr(rdsKey).Val()

	//门店每天第一单时设置过期时间
	if numberKey == 1 {
		redis.Expire(rdsKey, 24*time.Hour)
	}

	return cast.ToString(numberKey)
}

func getFinanceCodeByChannel(channelId int, channelStoreId string) string {
	return HashGet(RelationKeyMap[channelId][1], channelStoreId)
}

// 社区团购验证站点
func pickupValidateStation(ctx *models.GrpcContext, in *oc.MtAddOrderRequest) error {
	// 自提不校验
	if in.DeliveryType == 3 {
		return nil
	}
	db := GetDBConn()
	psc, err := models.GetPickupShopConfig(db, in.ShopId)
	if err != nil {
		glog.Info("社区团购 提交订单查询配置出错 :" + err.Error())
		return nil
	}

	if psc == nil {
		// 未启用社区团购，但是选中了站点
		if in.PickupStationId > 0 {
			return errors.New("社区团购已下线")
		}
		return nil
	}

	//if ctx.Channel.UserAgent == 1 || ctx.Channel.UserAgent == 2 {
	//	return errors.New("app暂不支持社区团购，请到阿闻宠物小程序下单")
	//}

	// 店铺启用了社区团购，但是没有选中站点
	if in.PickupStationId < 1 {
		return errors.New("请选择自提站点")
	}

	// 选择了站点，计算站点合法性
	// st_distance 计算的结果单位是 度，需要乘111195（地球半径6371000*PI/180） 是将值转化为米
	// ST_Distance_Sphere 开发环境版本不支持
	query := db.Table("datacenter.pickup_station").Where("status = 1 and id = ?", in.PickupStationId)

	// 测试环境不支持srid及ST_Distance_Sphere，用不走索引的方法
	if kit.EnvIsTest() || os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		// 不用having是因为这里需要兼容list、count查询
		query.Where("ST_Distance(POINT(lng,lat),POINT(?, ?))*111195 <= ?", in.Longitude, in.Latitude, psc.MaxDistance)
	} else {
		query.Where("ST_Distance_Sphere(ST_SRID(POINT(?,?),4326), geo_point) <= ?", in.Longitude, in.Latitude, psc.MaxDistance)
	}

	if has, err := query.Exist(); err != nil {
		glog.Info("社区团购 提交订单查询站点出错 :" + err.Error())
		return nil
	} else if !has {
		return errors.New("选择的站点失效，请重新选择")
	}

	return nil
}

// 提交订单检查黑名单
func submitCheckBlackList(params *oc.MtAddOrderRequest) (err error) {
	ccClient := cc.GetCustomerCenterLongClient()
	checkReq := &cc.CheckBlackListReq{
		Mobile: params.MemberTel,
	}
	if len(params.ReceiverPhone) > 0 {
		checkReq.ReceiverMobile = params.ReceiverPhone
	}

	if out, err := ccClient.User.CheckBlackList(context.Background(), checkReq); err != nil {
		return err
	} else if out.Code != 200 {
		return errors.New(out.Message)
	}
	return
}

// 检查订单风险
func checkOrderRisk(orderMain *models.OrderMain, ops []*models.OrderProduct) (err error) {
	// 新人、积分订单、虚拟订单 不判断
	if orderMain.OrderType == 8 || orderMain.OrderType == 10 || orderMain.IsVirtual == 1 {
		return
	}

	defer func() {
		if err != nil {
			glog.Warning("checkOrderRisk 出错：" + err.Error())
		}
	}()

	c := CheckOrderRisk{
		OrderMain:  orderMain,
		Ops:        ops,
		Db:         GetDBConn(),
		Management: new(models.RiskManagement),
	}

	if isException, err := c.IsException(); err != nil {
		return err
	} else if !isException {
		return nil
	}

	// 异常，再看看账号白名单
	if has, err := c.Db.Table("dc_customer.risk_whitelist").
		Where("mobile = ? and status = 1", utils.MobileDecrypt(orderMain.EnMemberTel)).
		Exist(); err != nil {
		return err
	} else if has {
		return nil
	}

	has, err := c.Db.Get(c.Management)

	session := c.Db.NewSession()
	defer session.Close()

	session.Begin()

	if err != nil {
		return err
	} else if has {
		condition := "user_mobile = '" + utils.MobileDecrypt(orderMain.EnMemberTel) + "'"
		if len(orderMain.ReceiverPhone) > 0 { // 考虑收货手机号为空的情况
			condition += " or receiver_mobile ='" + utils.MobileDecrypt(orderMain.EnReceiverPhone) + "'"
		}
		// 目前判断风险订单只需要当天
		if err = c.Db.Where("("+condition+") and create_time > ?", time.Now().Format(kit.DATE_LAYOUT)).Find(&c.OrderRisk); err != nil {
			return
		}

		if err = c.Check(session); err != nil {
			_ = session.Rollback()
			return
		}
	}

	// 插入风险订单
	if _, err = session.Insert(&models.OrderRisk{
		OldOrderSn:     orderMain.OldOrderSn,
		UserMobile:     utils.MobileDecrypt(orderMain.EnMemberTel),
		ReceiverMobile: utils.MobileDecrypt(orderMain.EnReceiverPhone),
		ChannelId:      orderMain.ChannelId,
		State:          10,
	}); err != nil {
		_ = session.Rollback()
		return
	}

	err = session.Commit()

	return
}

type CheckOrderRisk struct {
	OrderMain  *models.OrderMain
	Ops        []*models.OrderProduct
	Db         *xorm.Engine
	Management *models.RiskManagement
	OrderRisk  []*models.OrderRisk
}

// IsException 订单商品是否是负毛利（异常订单）
func (c CheckOrderRisk) IsException() (isException bool, err error) {
	var skuIds []string
	for _, op := range c.Ops {
		// 赠品、组合商品子商品、虚拟商品、非实实组合 不处理
		if op.PayPrice == 0 ||
			(op.ProductType == 1 && op.ParentSkuId != "") ||
			op.ProductType == 2 {
			continue
		}
		skuIds = append(skuIds, op.SkuId)
	}
	if len(skuIds) == 0 {
		return
	}

	var skus []*models.Sku
	if err = c.Db.Where("r1_purchase_price > 0").In("id", skuIds).Select("id,r1_purchase_price").Find(&skus); err != nil {
		return
	} else if len(skus) == 0 {
		return
	}

	skuMap := make(map[string]*models.Sku, len(skus))
	for _, sku := range skus {
		skuMap[cast.ToString(sku.Id)] = sku
	}

	for _, op := range c.Ops {
		if sku, has := skuMap[op.SkuId]; has {
			if op.PayPrice > 0 && op.PayPrice < sku.R1PurchasePrice {
				isException = true // 有一个商品异常则为异常
				break
			}
		}
	}

	return
}

// Check 检查规则
func (c CheckOrderRisk) Check(session *xorm.Session) (err error) {
	// 判断收货手机号规则
	receiver, receiverLockReason, err := c.ReceiverRule()
	if err != nil {
		return
	}
	// 账号规则
	account, accountLockReason, err := c.AccountRule()
	if err != nil {
		return
	}

	var logs []*models.RiskUserLog

	if account.Status == 1 {
		// 手机号相同
		if account.Mobile == receiver.Mobile && receiver.Status == 1 {
			account.Type = account.Type | receiver.Type
			// 收货手机号风控时间更长，则风控理由变更，
			if receiver.UnlockTime.After(account.UnlockTime) {
				account.UnlockTime = receiver.UnlockTime
				// 根据时间先后 先插入账号记录
				logs = append(logs, &models.RiskUserLog{
					Ruid:   account.Id,
					Opter:  "系统",
					Reason: accountLockReason,
				})
				accountLockReason = receiverLockReason
			} else {
				// 根据时间先后 先插入收货手机号记录
				logs = append(logs, &models.RiskUserLog{
					Ruid:   account.Id,
					Opter:  "系统",
					Reason: receiverLockReason,
				})
			}
		}

		if account.Id > 0 {
			_, err = session.ID(account.Id).Update(account)
		} else {
			_, err = session.Insert(account)
		}

		logs = append(logs, &models.RiskUserLog{
			Ruid:   account.Id,
			Opter:  "系统",
			Reason: accountLockReason,
		})
	}

	if receiver.Status == 1 && account.Mobile != receiver.Mobile {
		if receiver.Id > 0 {
			_, err = session.ID(receiver.Id).Update(receiver)
		} else {
			_, err = session.Insert(receiver)
		}

		logs = append(logs, &models.RiskUserLog{
			Ruid:   receiver.Id,
			Opter:  "系统",
			Reason: receiverLockReason,
		})
	}

	if err != nil {
		return
	} else if len(logs) > 0 {
		_, err = session.Insert(logs)
	}

	return
}

// AccountRule 用户手机号规则
func (c CheckOrderRisk) AccountRule() (user *models.RiskUser, reason string, err error) {
	user = new(models.RiskUser)
	count := 1

	if _, err = c.Db.Where("mobile = ?", utils.MobileDecrypt(c.OrderMain.EnMemberTel)).
		Select("id,user_cumulative_times,last_unlock_time").Get(user); err != nil {
		return
	}

	for _, risk := range c.OrderRisk {
		if risk.UserMobile != utils.MobileDecrypt(c.OrderMain.EnMemberTel) {
			continue
		}
		// 订单在解封时间后
		if risk.CreateTime.After(user.LastUnlockTime) {
			count++
		}
	}

	// 未到达风控次数
	if count <= c.Management.OrderDayMax {
		return
	}

	user.Mobile = utils.MobileDecrypt(c.OrderMain.EnMemberTel)
	user.UserCumulativeTimes++
	user.Status = 1
	user.Type = 1
	user.LockTime = time.Now()

	// 封90天规则
	if c.Management.CumulativeTimes > 0 && user.UserCumulativeTimes%c.Management.CumulativeTimes == 0 {
		user.UnlockTime = time.Now().AddDate(0, 0, 90)
		reason = fmt.Sprintf("被限制%d次账号，90个自然日后可继续下单", c.Management.CumulativeTimes)
	} else {
		user.UnlockTime = time.Now().Add(24 * time.Hour)
		reason = fmt.Sprintf("相同账号每日下异常订单大于%d单，限制账号下单，24小时后可继续下单", c.Management.OrderDayMax)
	}

	return
}

// ReceiverRule 收货手机号规则
func (c CheckOrderRisk) ReceiverRule() (user *models.RiskUser, reason string, err error) {
	user = new(models.RiskUser)
	if c.OrderMain.ReceiverPhone == "" {
		return
	}

	count := 1
	if _, err = c.Db.Where("mobile = ?", utils.MobileDecrypt(c.OrderMain.EnReceiverPhone)).
		Select("id,user_cumulative_times,last_receiver_unlock_time").
		Get(user); err != nil {
		return
	}

	for _, risk := range c.OrderRisk {
		if risk.ReceiverMobile != utils.MobileDecrypt(c.OrderMain.EnReceiverPhone) {
			continue
		}
		// 订单在解封时间后
		if risk.CreateTime.After(user.LastReceiverUnlockTime) {
			count++
		}
	}

	// 未到达风控次数
	if count <= c.Management.ReceviveDayMax {
		return
	}

	user.Mobile = utils.MobileDecrypt(c.OrderMain.EnReceiverPhone)
	user.UserCumulativeTimes++
	user.Status = 1
	user.Type = 2
	user.LockTime = time.Now()

	// 封90天规则
	if c.Management.CumulativeTimes > 0 && user.UserCumulativeTimes%c.Management.CumulativeTimes == 0 {
		user.UnlockTime = time.Now().AddDate(0, 0, 90)
		reason = fmt.Sprintf("被限制%d次收货手机号，90个自然日后可继续下单", c.Management.CumulativeTimes)
	} else {
		user.UnlockTime = time.Now().Add(24 * time.Hour)
		reason = fmt.Sprintf("相同收货手机号每日下异常订单大于%d单，限制账号下单，24小时后可继续下单", c.Management.ReceviveDayMax)
	}

	return
}

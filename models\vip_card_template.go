package models

import (
	"time"
)

type VipCardTemplate struct {
	Id              int32     `xorm:"not null pk autoincr INT(11)"`
	OrId            int       `xorm:"not null default 0 comment('所属大区') unique(uni_cycle_oid) TINYINT(2)"`
	CardName        string    `xorm:"not null comment('卡名称') VARCHAR(100)"`
	CardType        int32     `xorm:"not null comment('卡类型 1-付费卡 2-试用会员卡') TINYINT(1)"`
	CardCycle       int32     `xorm:"not null comment('付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡') unique(uni_cycle_oid) TINYINT(1)"`
	DurationDate    int32     `xorm:"not null comment('时长(天)') INT(11)"`
	MemberPrice     float32   `xorm:"default 0.00 comment('会员原价') DECIMAL(10,2)"`
	MemberDiscPrice float32   `xorm:"default 0.00 comment('会员折扣价') DECIMAL(10,2)"`
	DisRate         float32   `xorm:"default 0.00 comment('分销佣金比例') DECIMAL(4,2)"`
	Type            int32     `xorm:"not null default 0 comment('来源类型：1-会员卡 2-服务包') unique(uni_cycle_oid) TINYINT(1)"`
	WebId           int32     `xorm:"not null default 0 comment('微页面id') INT(11)"`
	TipTitle        string    `xorm:"not null default '''' comment('显示副标题') VARCHAR(200)"`
	CreateTime      time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime      time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
	SkuId           int32     `json:"sku_id" xorm:"not null default '' comment('商品sku_id') INT(11) 'sku_id'"`
}

func (c *VipCardTemplate) TableName() string {
	return "datacenter.vip_card_template"
}

package tasks

import (
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
	"order-center/utils"
	"testing"
)

func Test_refundOrderTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "售后订单定时任务",
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//refundOrderTask()
			//开启连接
			conn := utils.NewMqConn()
			defer conn.Close()
			ch := utils.NewMqChannel(conn)
			defer ch.Close()

			// 声明一个延时交换机
			err := ch.ExchangeDeclare(
				"zhou_delayed_exchange",                // 名称
				"x-delayed-message",                    // 类型
				true,                                   // durable
				false,                                  // auto-deleted
				false,                                  // internal
				false,                                  // no wait
				amqp.Table{"x-delayed-type": "direct"}, // arguments
			)
			if err != nil {
				//log.Fatalf("Failed to declare an exchange: %s", err)
			}

			// 声明队列
			q, err := ch.Queue<PERSON>lare(
				"delayed_queue", // 队列名
				true,            // durable
				false,           // delete when unused
				false,           // exclusive
				false,           // no-wait
				nil,             // arguments
			)
			if err != nil {
				//log.Fatalf("Failed to declare a queue: %s", err)
			}

			// 将队列绑定到延时交换机
			err = ch.QueueBind(
				q.Name,                  // 队列名
				"zhou_routing",          // routing key
				"zhou_delayed_exchange", // 交换机名
				false,
				nil, // arguments
			)
			if err != nil {
				//log.Fatalf("Failed to bind a queue: %s", err)
			}

			// 发送延时消息
			delay := 20000 // 延迟时间（毫秒）
			body := "Hello, delayed message111!"
			err = ch.Publish(
				"zhou_delayed_exchange", // 交换机
				"zhou_routing",          // routing key
				false,                   // mandatory
				false,                   // immediate
				amqp.Publishing{
					DeliveryMode: amqp.Persistent,
					ContentType:  "text/plain",
					Body:         []byte(body),
					Headers: amqp.Table{
						"x-delay": delay, // 在这里设置延迟
					},
				},
			)
			if err != nil {
				//	log.Fatalf("Failed to publish a message: %s", err)
			}

		})
	}
}

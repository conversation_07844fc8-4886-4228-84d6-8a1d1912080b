package models

import "time"

type PinOrderGroupProduct struct {
	Id           int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	PinOrderSn   string    `xorm:"not null default '''' comment('拼团订单ID（取的是订单中心订单编号逻辑）') VARCHAR(55)"`
	SkuId        string    `xorm:"not null default '''' comment('商品SKUID') VARCHAR(55)"`
	ParentSkuId  string    `xorm:"not null default '''' comment('父商品SKUID') VARCHAR(55)"`
	ProductType  int32     `xorm:"not null default 0 comment('商品类别（1-实物商品，2-虚拟商品）') VARCHAR(1)"`
	ProductName  string    `xorm:"not null default '''' comment('商品名称') VARCHAR(200)"`
	ProductImage string    `xorm:"not null default '''' comment('商品图片') VARCHAR(255)"`
	SpecName     string    `xorm:"not null default '''' comment('规格值') VARCHAR(60)"`
	MarkingPrice int32     `xorm:"not null default 0 comment('商品原单价') VARCHAR(11)"`
	PinPrice     int32     `xorm:"not null default 0 comment('商品拼团单价') VARCHAR(11)"`
	PayPrice     int32     `xorm:"not null default 0 comment('商品均摊后实际支付单价') VARCHAR(11)"`
	Number       int32     `xorm:"not null default 0 comment('数量') INT(11)"`
	CreateTime   time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	UpdateTime   time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
}

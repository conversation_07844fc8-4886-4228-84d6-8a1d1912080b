package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"time"
)

type Pickup struct {
	Id               int32     `xorm:"not null pk autoincr INT(10)"`
	UserNo           string    `xorm:"default 'NULL' comment('创建人') VARCHAR(50)"`
	StoreFinanceCode string    `xorm:"not null comment('门店财务编码') unique VARCHAR(50)"`
	StationDailyMin  float64   `xorm:"not null comment('每日最少成团金额') INT(10)"`
	EndTime          string    `xorm:"not null default '''' comment('站点截止时间') VARCHAR(50)"`
	DeliverDays      int32     `xorm:"not null default 0 comment('最长送货天数') INT(10)"`
	CreatedAt        time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdatedAt        time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

type PickupShopConfig struct {
	*Pickup
	MaxDistance    int32
	StatusTemplate string // 状态通知模板
}

// GetPickupShopConfig 获取社区团购店铺配置
func GetPickupShopConfig(db *xorm.Engine, shopId string) (*PickupShopConfig, error) {
	psc := &PickupShopConfig{Pickup: new(Pickup)}

	if has, err := db.Table("datacenter.pickup").Where("store_finance_code = ?", shopId).Get(psc.Pickup); err != nil {
		return nil, errors.New("查询配置出错 " + err.Error())
	} else if !has {
		// 没有参加活动
		return nil, nil
	}

	if pc, err := GetPickupConfig(db); err != nil {
		return nil, err
	} else {
		psc.MaxDistance = pc.MaxDistance
		psc.StatusTemplate = pc.StatusTemplate
	}

	return psc, nil
}

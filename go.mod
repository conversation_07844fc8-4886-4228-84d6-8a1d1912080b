module order-center

go 1.13

require (
	github.com/Shopify/sarama v1.19.0
	github.com/ahmetb/go-linq v3.0.0+incompatible
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.6.0
	github.com/go-xorm/xorm v0.7.9
	github.com/gogf/gf v1.13.0
	github.com/golang-module/carbon/v2 v2.2.3
	github.com/golang/protobuf v1.5.2
	github.com/google/uuid v1.3.0
	github.com/jordan-wright/email v4.0.1-0.20210109023952-943e75fe5223+incompatible
	github.com/json-iterator/go v1.1.12
	github.com/labstack/echo/v4 v4.1.16
	github.com/labstack/gommon v0.3.0
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.0.0-20210928064228-9506732eb074
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.32
	github.com/ppkg/kit v0.0.0-20210928070906-2e2b70f489af
	github.com/robfig/cron/v3 v3.0.1
	github.com/satori/go.uuid v1.2.0
	github.com/shopspring/decimal v1.3.1
	github.com/spf13/cast v1.5.0
	github.com/streadway/amqp v0.0.0-20200108173154-1c71cc93ed71
	github.com/stretchr/testify v1.8.1
	github.com/techoner/gophp v0.2.0
	github.com/tricobbler/rp-kit v0.0.0-20210413075252-45df7834f17a
	github.com/xuri/excelize/v2 v2.5.0
	golang.org/x/net v0.5.0 // indirect
	google.golang.org/genproto v0.0.0-20230109162033-3c3c17ce83e6
	google.golang.org/grpc v1.51.0
	google.golang.org/protobuf v1.28.1
	xorm.io/xorm v1.3.2
)

replace order-center => ./

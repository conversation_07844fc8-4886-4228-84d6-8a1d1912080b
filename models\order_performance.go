package models

import "time"

type OrderPerformance struct {
	Id                  int64     `xorm:"not null pk autoincr INT(11)"`
	OrderSn             string    `xorm:"not null default '''' comment('订单编号') index(idx_order_sn_status) VARCHAR(50)"`
	StaffId             string    `xorm:"not null default '''' comment('员工id') VARCHAR(30)"`
	StaffName           string    `xorm:"not null default '''' comment('员工姓名') VARCHAR(30)"`
	PerformanceStatus   int32     `xorm:"not null default 1 comment('是否有效，1有效，0无效') index(idx_order_sn_status) TINYINT(1)"`
	CreateTime          time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime          time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	OperatorId          string    `xorm:" default '''' comment('操作人ID') VARCHAR(30)"`
	OperatorName        string    `xorm:" default '''' comment('操作人姓名') VARCHAR(30)"`
	PerformanceMemberId string    `xorm:" default '''' comment('分销员scrm_user_id') VARCHAR(50)"`
	PerformanceChainId  int32     `xorm:"not null default 0 comment('分销员所属门店id') INT(11)"`
}

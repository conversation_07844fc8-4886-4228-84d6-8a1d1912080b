syntax = "proto3";

package cc;

//宠物相关服务
service PetService {
  //获取宠物列表
  rpc GetPetList (PetListRequest) returns (PetListResponse);

  // 新版获取宠物列表数据
  rpc GetPetListNew (PetListRequest) returns (PetListNewRes);

  // 获取宠物花色
  rpc GetPetFlower (GetPetFlowerRequest) returns (GetPetFlowerRes);
  // 互联网医院-病例列表
  rpc MedRecordList(MedRecordListReq) returns(MedRecordListRes);
  // 互联网医院-病例详情
  rpc MedRecordInfo(MedRecordInfoReq) returns(MedRecordInfoRes);

  // 新增编辑记录
  rpc CreateOrUpdateRecord (CreateOrUpdateRecordReq) returns (PetBaseResponse);
  // 删除记录
  rpc DeleteRecord (DeleteRecordReq) returns (PetBaseResponse);

  // 获取记录数据
  rpc PetRecordList (PetRecordListReq) returns (PetRecordListBaseResponse);

  // 获取记录的时间，前端展示
  rpc GetNewPetDate (PetListRequest) returns (NewPetRecordDateBaseResponse);
  //APP 调用接口
  rpc GetPetDate (PetListRequest) returns (PetRecordDateBaseResponse);
}

message PetListRequest{
  string petId = 1;
  string userId = 2;
  string token = 3;
}

message PetListResponse{
  repeated PetInfo data = 1;
}

message GetPetFlowerRequest{
  string token=1;
}
message GetPetFlowerRes{

 repeated PetFlowerDict data=1;
 
}
message PetFlowerDict{
  int32  id = 1;
  string code = 2; //花色编码
  string name = 3; //宠物花色
  int32 status = 4;
  int32  orders = 5;
  string english_name = 6;
  string path = 7;
  int32  parent_code = 8;
}

message PetInfo {
  int32 id = 1;
  //宠物id
  string petId = 2;
  //用户id
  string userId = 3;
  //宠物名称
  string petName = 4;
  //宠物性别 0未知 1公 2母
  int32 petSex = 5;
  //宠物品种 -1未知 1000猫 1001狗 1002其他
  int32 petKindof = 6;
  //宠物品种
  int32 petVariety = 7;
  //是否绝育 -1未知,0未绝育,1已绝育
  int32 petNeutering = 8;
  //疫苗 -1未知,0未接种,1已接种
  int32 petVaccinated = 9;
  //驱虫  -1未知,0未驱虫,1 已驱虫
  int32 petDeworming = 10;
  //宠物体重,单位g
  int32 petWeight = 11;
  //宠物体长,单位mm
  int32 petLong = 12;
  //宠物体高,单位mm
  int32 petHeight = 13;
  //宠物来源
  int32 petSource = 14;
  //宠物状态 0正常,1死亡,2走失,4送人,8隐藏
  int32 petStatus = 15;
  //宠物头像
  string petAvatar = 16;
  //宠物生日
  string petBirthday = 17;
  //备注
  string petRemark = 18;
  //创建时间
  string createTime = 19;
  //更新时间
  string updateTime = 20;
  //宠物faceId
  string faceId = 21;
  string enterSource = 22;
  //宠物品种 未知 猫 狗 其他
  string petVarietyName = 23;
  //宠物品种
  string petKindOfName = 24;
  //最后一次驱虫记录
  VaccinateRecord lastExpellingParasiteRecord = 25;
  //最后一次免疫记录
  VaccinateRecord lastImmunityRecord = 26;
  //最后一次就诊记录
  LastDiagnosis lastDiagnosisRecord = 27;
  //年龄
  string ageStr = 28;
  //年龄换算
  string ageConversionStr = 29;
  //0:就诊记录 1:上次就诊 2:体检提醒
  int32 diagnosisRemind = 30;
  //是否展示绝育 1是 0否
  int32 neuteringRemind = 31;
  //是否展示疫苗 1是 0否
  int32 immunityRemind = 32;
  //是否展示驱虫 1是 0否
  int32 expellingParasiteRemind = 33;

  int32 sortWeight = 34;
}

message VaccinateRecord  {
  //有值代表是手动添加的数据，否则是医院端记录数据
  int64 id = 1;
  //日期
  string operationDate = 2;
  //医院名称
  string shopName = 3;
  //产品名称
  string productName = 4;
  //1疫苗记录 2驱虫记录
  int32 type = 5;
  //创建时间
  string createTime =6;
}

message LastDiagnosis {
  //疾病诊断
  string mainSymptom = 1;
  //开始时间
  string startTime = 2;
  //结束时间
  string endTime = 3;
  //机构id
  int64 orgid = 4;
  //挂号id
  int64 regId = 5;
  // 病例类型 【0-门诊；1-住院】
  int32 medType = 6;
}
message NextImmunity  {
  string age = 1;
  string recordTime = 2;
  string content = 3;
  string contentDetail = 4;
  string tip = 5;

}
message NextExpellingParasite  {
  string date = 1;
  string tip = 2;
}

message MedRecordListReq {
  //宠物id
  string pet_id = 1;
  //当前页数
  int32 page_index = 2;
  //每页显示的数量
  int32 page_size = 3;
}

message MedRecordListRes {
  int32 total = 1;
  int32 page_index = 2;
  int32 page_size = 3;
  repeated MedRecordList data = 4;
}

message MedRecordList {
  // 机构id
  int64 orgid = 1;
  // 挂号id
  int64 reg_id = 2;
  // 病例类型 【0-门诊；1-住院】
  int32 med_type = 3;
  // 医生id
  int64 physician_id = 4;
  // 医生名称
  string physician_name = 5;
  // 客户id
  int64 cus_logicid = 6;
  // 客户名称
  string cus_name = 7;
  //  宠物id
  int64 pet_logicid = 8;
  // 宠物名称
  string pet_name = 9;
  // 开始时间
  string create_time = 10;
  // 结束时间
  string end_time = 11;
  // 挂号类型【1020 初诊； 1021-复诊】
  int32 record_type = 12;
  // 机构名称
  string hospital_name = 13;
  // 挂号类型文本
  string record_type_text = 14;
  // 诊断信息
  string main_symptom = 15;
}

message MedRecordInfoReq {
  //挂号id
  int64 reg_id = 1;
  //当前病例的机构id
  int32 orgid = 2;
}

message MedRecordInfoRes {
  // 机构id
  int64 orgid = 1;
  // 病例分类 【0 门诊； 1 住院】
  string med_type = 2;
  // 挂号id
  int64 reg_id = 3;
  // 医生id
  int64 physician_id = 4;
  // 医生名称
  string physician_name = 5;
  // 诊断
  string main_symptom = 6;
  // 客户id
  int64 cus_logicid = 7;
  // 客户名称
  string cus_name = 8;
  // 宠物名称
  string pet_name = 9;
  // 宠物id
  int64 pet_logicid = 10;
  // 开始时间
  string create_time = 11;
  // 结束时间
  string end_time = 12;
  // 主诉
  string chief_complaint = 13;
  // 既往史
  string past_history = 14;
  //体况描述
  string physical_description = 15;
  // 治疗计划
  string treatment_opinion = 16;
  // 医嘱
  string doctor_advice = 17;
  // 医院名称
  string hospital_name = 18;
  // 病例等级
  string med_level_text = 19;
  // 病情紧急程度
  string disease_urgency_text = 20;
  // 体况等级
  string physical_level_text = 21;
  // 治疗结果
  string med_result_text = 22;
  // 出院诊断
  string leave_symptom = 23;
  // 病例分类 【0 门诊； 1 住院】
  string med_type_text = 24;
  // 目前体况
  string illness_desc = 25;
  // 住院病例描述
  string physical_desc = 26;
  repeated PhysicalExamination physical_examination = 27;
  repeated MedMedias med_medias = 28;
  //具体治疗
  repeated SpecificTreatments specific_treatments = 29;
  //宠物信息
  MdPetInfo pet_info = 30;
  //住院医嘱
  string inpatient_doctor_advice = 31;
}

message PhysicalExamination {
  // 体重
  string weight = 1;
  // 温度
  string temperature = 2;
  // 呼吸频率
  string breathing_rate = 3;
  // 心跳
  string heart_rate = 4;
}

message MedMedias {
  int64 id = 1;
  string pic_url = 2;
  string remark = 3;
}

message SpecificTreatments {
  // 开单医生id
  int64 physician_id = 1;
  // 开单医生名称
  string physician_name = 2;
  // 执行医生id
  string exec_physician_id = 3;
  // 执行医生名称
  string exec_physician_name = 4;
  // 治疗时间
  string insert_time = 15;
  // 病情描述
  string description = 16;
}

message MdPetInfo {
  //宠物名称
  string pet_name = 1;
  //宠物ID
  int64 pet_logicid = 2;
  //宠物性别
  string pet_gender_text = 3;
  //宠物品种
  string pet_kindof_text = 5;
  //宠物种类
  string pet_variety_text = 6;
  //宠物颜色
  string color_txt = 7;
  //宠物年龄
  string pet_age = 8;
  //宠物体重
  float pet_weight = 9;
  //绝育状态
  string pet_neutering_text = 10;
  //宠物状态
  string pet_status_text = 11;
  //接种状态
  string is_vaccinated_txt = 12;
  //驱虫状态
  string is_deworming_txt = 13;
}


message CreateOrUpdateRecordReq{
  // 记录id
  int64 id =1;
  // 宠物id
  string pet_id = 2;
  // 接种日期
  string operation_date = 3;
  // 接诊年份
  string operation_year = 4;
  // 接种机构
  string shop_name = 5;
  // 产品名称
  string product_name = 6;
  // 记录类型 1疫苗记录 2驱虫记录 3口腔 4体检  5洗护 6体况评分 7三围 8体重
  int64  record_type = 7;
  //记录拍照
  string record_photo =8;
}

message DeleteRecordReq{
  int64 id = 1;
}


message PetBaseResponse{
  int64 code = 1;
  string msg = 2;
  string data = 3;
}


// 新版宠物接口返回
message PetListRes {

}


message PetRecordListReq {
  string pet_id =1;
  int64 record_type =2;
}

message PetRecordListBaseResponse {
  int64 code = 1;
  string msg = 2;
  repeated PetRecordListRes data = 3;
  int32 total =4;
}


message NewPetRecordDateBaseResponse {
  int64 code = 1;
  string msg = 2;
  repeated NewPetRecordDateRes data = 3;
}

message PetRecordDateBaseResponse {
  int64 code = 1;
  string msg = 2;
  PetRecordDateRes data = 3;
}

message NewPetRecordDateRes {
  //创建时间
  string create_time =1;
  //体况拍照记录
  string record_photo =2;
  //产品名称/BCS评分/三围/体重
  string product_name =3;
  //接种日期/驱虫日期
  string operation_date =4;
  //记录类型 0 绝育  1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重
  int64 record_type =5;
  //红点展示
  bool show = 6;
  //距离3天提醒
  bool remind = 7;
  //天数
  int32 day_num =8;
}

// 记录返回
message PetRecordListRes {
  int64 id =1;
  string pet_id =2;
  // 年
  string operation_year =3;
  // 接诊日期
  string  operation_date = 4;
  // 接诊医院
  string shop_name = 5;
  // 接诊商品
  string product_name = 6;
  // 记录类型 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重
  int64 record_type = 7;
  string create_time = 8;
  string update_time = 9;
  //记录拍照
  string record_photo =10;
}

message PetListNewRes {
  int64 code =1;
  string msg =2;
  repeated PetInfoNew data = 3;
}

message PetInfoNew {
  int32 id = 1;
  //宠物id
  string pet_id = 2;
  //用户id
  string user_id = 3;
  //宠物名称
  string pet_name = 4;
  //宠物性别 0未知 1公 2母
  int32 pet_sex = 5;
  //宠物品种 -1未知 1000猫 1001狗 1002其他
  int32 pet_kindof = 6;
  //宠物品种 -1未知 1000猫 1001狗 1002其他
  string pet_kindof_str = 7;
  //宠物品种
  int32 pet_variety = 8;
  //宠物类别
  string pet_variety_str = 9;

  //是否绝育 -1未知,0未绝育,1已绝育
  int32 pet_neutering = 10;
  //疫苗 -1未知,0未接种,1已接种
  int32 pet_vaccinated = 11;
  //驱虫  -1未知,0未驱虫,1 已驱虫
  int32 pet_deworming = 12;
  //宠物体重,单位g
  int32 pet_weight = 13;
  //宠物体长,单位mm
  int32 pet_long = 14;
  //宠物体高,单位mm
  int32 pet_height = 15;
  //宠物来源
  int32 pet_source = 16;
  //宠物状态 0正常,1死亡,2走失,4送人,8隐藏
  int32 pet_status = 17;
  //宠物头像
  string pet_avatar = 18;
  //宠物生日
  string pet_birthday = 19;
  // 到家日期
  string pet_homeday = 20;
  //备注
  string pet_remark = 21;
  //创建时间
  string create_time = 22;
  //更新时间
  string update_time = 23;
  //宠物faceId
  string face_id = 24;

  // 鼻纹识别宠物code
  string pet_code = 25;

  // sendTime这个字段是  t_scrm_pet_photo表的create_time字段取第一个
  string sendTime = 26;
  // 保险宠物faceId
  string insurance_face_Id = 27;

  //  这个字段其实就是判断这个宠物有没有开通宠物保障卡
  bool ensure_card = 28;
  // t_scrm_pet_photo表的create_time字段取第一个
  repeated string scrm_pet_photo = 29;
  //年龄
  string ageStr = 30;
  //年龄换算
  string ageConversionStr = 31;

  //最后一次驱虫记录
  string last_expelling_parasite_record_time = 32;
  //最后一次免疫记录
  string last_immunity_record_time = 33;
  //最后一次口腔记录时间
  string last_mouth_record_time = 34;
  // 最后一次体检记录时间
  string last_examination_record_time = 35;
  // 宠物花色
  string pet_flower =36;
  //花色编码
  string flower_code = 37;

}

message PetRecordDateRes {
  //最后一次驱虫记录
  string last_expelling_parasite_record_time = 1;
  //驱虫红点显示
  bool expelling_parasite_record_show = 2;
  //最后一次免疫记录
  string last_immunity_record_time = 3;
  //免疫红点显示
  bool immunity_record_time_show = 4;
  //最后一次口腔记录时间
  string last_mouth_record_time = 5;
  //口腔红点显示
  bool mouth_record_show =6;
  // 最后一次体检记录时间
  string last_examination_record_time = 7;
  //体检 -红点显示
  bool examination_record_show =8;
  // 绝育红点展示
  bool sterilization_show =9;

}
package models

type UpetExpress struct {
	Id          int    `xorm:"not null pk autoincr comment('索引ID') unique TINYINT(3)"`
	EName       string `xorm:"not null comment('公司名称') VARCHAR(50)"`
	EState      string `xorm:"not null default '1' comment('状态') ENUM('0','1')"`
	ECode       string `xorm:"not null comment('编号') VARCHAR(50)"`
	ECodeKdniao string `xorm:"comment('快递鸟快递公司代码') VARCHAR(50)"`
	ELetter     string `xorm:"not null comment('首字母') CHAR(1)"`
	EOrder      string `xorm:"not null default '2' comment('1常用2不常用') ENUM('1','2')"`
	EUrl        string `xorm:"not null comment('公司网址') VARCHAR(100)"`
	EZtState    int    `xorm:"default 0 comment('是否支持服务站配送0否1是') TINYINT(4)"`
}

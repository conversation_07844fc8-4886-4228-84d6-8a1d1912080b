package models

type UpetOrder<PERSON><PERSON><PERSON> struct {
	OrderId           int     `xorm:"not null comment('订单索引id') INT(11)"`
	StoreId           int32   `xorm:"not null comment('店铺ID') INT(10)"`
	ShippingTime      int     `xorm:"not null default 0 comment('配送时间') INT(10)"`
	ShippingExpressId int     `xorm:"not null default 0 comment('配送公司ID') TINYINT(1)"`
	EvaluationTime    int     `xorm:"not null default 0 comment('评价时间') INT(10)"`
	OrderMessage      string  `xorm:"default 'NULL' comment('订单留言') VARCHAR(300)"`
	OrderPointscount  int     `xorm:"not null default 0 comment('订单赠送积分') INT(11)"`
	VoucherPrice      int     `xorm:"default NULL comment('代金券面额') INT(11)"`
	VoucherCode       string  `xorm:"default 'NULL' comment('代金券编码') VARCHAR(32)"`
	DeliverExplain    string  `xorm:"default 'NULL' comment('发货备注') TEXT"`
	DaddressId        int     `xorm:"not null default 0 comment('发货地址ID') MEDIUMINT(9)"`
	ReciverName       string  `xorm:"not null comment('收货人姓名') VARCHAR(50)"`
	ReciverInfo       string  `xorm:"not null comment('收货人其它信息') VARCHAR(500)"`
	ReciverProvinceId int     `xorm:"not null default 0 comment('收货人省级ID') MEDIUMINT(8)"`
	ReciverCityId     int     `xorm:"not null default 0 comment('收货人市级ID') MEDIUMINT(8)"`
	InvoiceInfo       string  `xorm:"default '''' comment('发票信息') VARCHAR(500)"`
	PromotionInfo     string  `xorm:"default '''' comment('促销信息备注') VARCHAR(800)"`
	DlyoPickupCode    string  `xorm:"default 'NULL' comment('提货码') VARCHAR(6)"`
	PromotionTotal    float64 `xorm:"default 0 comment('订单总优惠金额（代金券，满减，平台红包）') DECIMAL(10,2)"`
	Discount          int     `xorm:"default 0 comment('会员折扣x%') TINYINT(4)"`
	ReciverDateMsg    string  `xorm:"default '''' comment('希望送货时间') VARCHAR(20)"`
}
type UpetOrderCommonSimple struct {
	OrderId           int `xorm:"not null comment('订单索引id') INT(11)"`
	ShippingTime      int `xorm:"not null default 0 comment('配送时间') INT(10)"`
	ShippingExpressId int `xorm:"not null default 0 comment('配送公司ID') TINYINT(1)"`
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sh/voucher.proto

package sh

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type IssueAwardByMallCouponRequest struct {
	//用户id
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//商城券id ,号分割的字符串
	CouponId             string   `protobuf:"bytes,2,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssueAwardByMallCouponRequest) Reset()         { *m = IssueAwardByMallCouponRequest{} }
func (m *IssueAwardByMallCouponRequest) String() string { return proto.CompactTextString(m) }
func (*IssueAwardByMallCouponRequest) ProtoMessage()    {}
func (*IssueAwardByMallCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{0}
}

func (m *IssueAwardByMallCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssueAwardByMallCouponRequest.Unmarshal(m, b)
}
func (m *IssueAwardByMallCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssueAwardByMallCouponRequest.Marshal(b, m, deterministic)
}
func (m *IssueAwardByMallCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssueAwardByMallCouponRequest.Merge(m, src)
}
func (m *IssueAwardByMallCouponRequest) XXX_Size() int {
	return xxx_messageInfo_IssueAwardByMallCouponRequest.Size(m)
}
func (m *IssueAwardByMallCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IssueAwardByMallCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IssueAwardByMallCouponRequest proto.InternalMessageInfo

func (m *IssueAwardByMallCouponRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *IssueAwardByMallCouponRequest) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

type IssueAwardByMallCouponResponse struct {
	Code                 int64               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*IssueAwardCoupon `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *IssueAwardByMallCouponResponse) Reset()         { *m = IssueAwardByMallCouponResponse{} }
func (m *IssueAwardByMallCouponResponse) String() string { return proto.CompactTextString(m) }
func (*IssueAwardByMallCouponResponse) ProtoMessage()    {}
func (*IssueAwardByMallCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{1}
}

func (m *IssueAwardByMallCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssueAwardByMallCouponResponse.Unmarshal(m, b)
}
func (m *IssueAwardByMallCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssueAwardByMallCouponResponse.Marshal(b, m, deterministic)
}
func (m *IssueAwardByMallCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssueAwardByMallCouponResponse.Merge(m, src)
}
func (m *IssueAwardByMallCouponResponse) XXX_Size() int {
	return xxx_messageInfo_IssueAwardByMallCouponResponse.Size(m)
}
func (m *IssueAwardByMallCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssueAwardByMallCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssueAwardByMallCouponResponse proto.InternalMessageInfo

func (m *IssueAwardByMallCouponResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IssueAwardByMallCouponResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *IssueAwardByMallCouponResponse) GetData() []*IssueAwardCoupon {
	if m != nil {
		return m.Data
	}
	return nil
}

type IssueAwardCoupon struct {
	CouponCode           string   `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code"`
	CouponEndTime        int64    `protobuf:"varint,3,opt,name=coupon_end_time,json=couponEndTime,proto3" json:"coupon_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssueAwardCoupon) Reset()         { *m = IssueAwardCoupon{} }
func (m *IssueAwardCoupon) String() string { return proto.CompactTextString(m) }
func (*IssueAwardCoupon) ProtoMessage()    {}
func (*IssueAwardCoupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{2}
}

func (m *IssueAwardCoupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssueAwardCoupon.Unmarshal(m, b)
}
func (m *IssueAwardCoupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssueAwardCoupon.Marshal(b, m, deterministic)
}
func (m *IssueAwardCoupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssueAwardCoupon.Merge(m, src)
}
func (m *IssueAwardCoupon) XXX_Size() int {
	return xxx_messageInfo_IssueAwardCoupon.Size(m)
}
func (m *IssueAwardCoupon) XXX_DiscardUnknown() {
	xxx_messageInfo_IssueAwardCoupon.DiscardUnknown(m)
}

var xxx_messageInfo_IssueAwardCoupon proto.InternalMessageInfo

func (m *IssueAwardCoupon) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *IssueAwardCoupon) GetCouponEndTime() int64 {
	if m != nil {
		return m.CouponEndTime
	}
	return 0
}

type Response struct {
	Code                 int64    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{3}
}

func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Response) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type RecoverUserVoucher struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	VoucherTIds          []int64  `protobuf:"varint,2,rep,packed,name=voucher_t_ids,json=voucherTIds,proto3" json:"voucher_t_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserVoucher) Reset()         { *m = RecoverUserVoucher{} }
func (m *RecoverUserVoucher) String() string { return proto.CompactTextString(m) }
func (*RecoverUserVoucher) ProtoMessage()    {}
func (*RecoverUserVoucher) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{4}
}

func (m *RecoverUserVoucher) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserVoucher.Unmarshal(m, b)
}
func (m *RecoverUserVoucher) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserVoucher.Marshal(b, m, deterministic)
}
func (m *RecoverUserVoucher) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserVoucher.Merge(m, src)
}
func (m *RecoverUserVoucher) XXX_Size() int {
	return xxx_messageInfo_RecoverUserVoucher.Size(m)
}
func (m *RecoverUserVoucher) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserVoucher.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserVoucher proto.InternalMessageInfo

func (m *RecoverUserVoucher) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *RecoverUserVoucher) GetVoucherTIds() []int64 {
	if m != nil {
		return m.VoucherTIds
	}
	return nil
}

type RecoverUserVoucherRequest struct {
	Timestemp            int64                 `protobuf:"varint,1,opt,name=timestemp,proto3" json:"timestemp"`
	Sign                 string                `protobuf:"bytes,2,opt,name=sign,proto3" json:"sign"`
	UserVoucher          []*RecoverUserVoucher `protobuf:"bytes,3,rep,name=user_voucher,json=userVoucher,proto3" json:"user_voucher"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RecoverUserVoucherRequest) Reset()         { *m = RecoverUserVoucherRequest{} }
func (m *RecoverUserVoucherRequest) String() string { return proto.CompactTextString(m) }
func (*RecoverUserVoucherRequest) ProtoMessage()    {}
func (*RecoverUserVoucherRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{5}
}

func (m *RecoverUserVoucherRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserVoucherRequest.Unmarshal(m, b)
}
func (m *RecoverUserVoucherRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserVoucherRequest.Marshal(b, m, deterministic)
}
func (m *RecoverUserVoucherRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserVoucherRequest.Merge(m, src)
}
func (m *RecoverUserVoucherRequest) XXX_Size() int {
	return xxx_messageInfo_RecoverUserVoucherRequest.Size(m)
}
func (m *RecoverUserVoucherRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserVoucherRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserVoucherRequest proto.InternalMessageInfo

func (m *RecoverUserVoucherRequest) GetTimestemp() int64 {
	if m != nil {
		return m.Timestemp
	}
	return 0
}

func (m *RecoverUserVoucherRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *RecoverUserVoucherRequest) GetUserVoucher() []*RecoverUserVoucher {
	if m != nil {
		return m.UserVoucher
	}
	return nil
}

type FindUsedVoucherUserMobileRequest struct {
	VoucherTIds          []int64  `protobuf:"varint,1,rep,packed,name=voucher_t_ids,json=voucherTIds,proto3" json:"voucher_t_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FindUsedVoucherUserMobileRequest) Reset()         { *m = FindUsedVoucherUserMobileRequest{} }
func (m *FindUsedVoucherUserMobileRequest) String() string { return proto.CompactTextString(m) }
func (*FindUsedVoucherUserMobileRequest) ProtoMessage()    {}
func (*FindUsedVoucherUserMobileRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{6}
}

func (m *FindUsedVoucherUserMobileRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindUsedVoucherUserMobileRequest.Unmarshal(m, b)
}
func (m *FindUsedVoucherUserMobileRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindUsedVoucherUserMobileRequest.Marshal(b, m, deterministic)
}
func (m *FindUsedVoucherUserMobileRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindUsedVoucherUserMobileRequest.Merge(m, src)
}
func (m *FindUsedVoucherUserMobileRequest) XXX_Size() int {
	return xxx_messageInfo_FindUsedVoucherUserMobileRequest.Size(m)
}
func (m *FindUsedVoucherUserMobileRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindUsedVoucherUserMobileRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindUsedVoucherUserMobileRequest proto.InternalMessageInfo

func (m *FindUsedVoucherUserMobileRequest) GetVoucherTIds() []int64 {
	if m != nil {
		return m.VoucherTIds
	}
	return nil
}

type UsedVoucherUserMobile struct {
	VoucherTId           int64    `protobuf:"varint,1,opt,name=voucher_t_id,json=voucherTId,proto3" json:"voucher_t_id"`
	Mobiles              []string `protobuf:"bytes,2,rep,name=mobiles,proto3" json:"mobiles"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UsedVoucherUserMobile) Reset()         { *m = UsedVoucherUserMobile{} }
func (m *UsedVoucherUserMobile) String() string { return proto.CompactTextString(m) }
func (*UsedVoucherUserMobile) ProtoMessage()    {}
func (*UsedVoucherUserMobile) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{7}
}

func (m *UsedVoucherUserMobile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UsedVoucherUserMobile.Unmarshal(m, b)
}
func (m *UsedVoucherUserMobile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UsedVoucherUserMobile.Marshal(b, m, deterministic)
}
func (m *UsedVoucherUserMobile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UsedVoucherUserMobile.Merge(m, src)
}
func (m *UsedVoucherUserMobile) XXX_Size() int {
	return xxx_messageInfo_UsedVoucherUserMobile.Size(m)
}
func (m *UsedVoucherUserMobile) XXX_DiscardUnknown() {
	xxx_messageInfo_UsedVoucherUserMobile.DiscardUnknown(m)
}

var xxx_messageInfo_UsedVoucherUserMobile proto.InternalMessageInfo

func (m *UsedVoucherUserMobile) GetVoucherTId() int64 {
	if m != nil {
		return m.VoucherTId
	}
	return 0
}

func (m *UsedVoucherUserMobile) GetMobiles() []string {
	if m != nil {
		return m.Mobiles
	}
	return nil
}

type FindUsedVoucherUserMobileResponse struct {
	Code                 int64                    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	List                 []*UsedVoucherUserMobile `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *FindUsedVoucherUserMobileResponse) Reset()         { *m = FindUsedVoucherUserMobileResponse{} }
func (m *FindUsedVoucherUserMobileResponse) String() string { return proto.CompactTextString(m) }
func (*FindUsedVoucherUserMobileResponse) ProtoMessage()    {}
func (*FindUsedVoucherUserMobileResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d88fc8742b4733e7, []int{8}
}

func (m *FindUsedVoucherUserMobileResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindUsedVoucherUserMobileResponse.Unmarshal(m, b)
}
func (m *FindUsedVoucherUserMobileResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindUsedVoucherUserMobileResponse.Marshal(b, m, deterministic)
}
func (m *FindUsedVoucherUserMobileResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindUsedVoucherUserMobileResponse.Merge(m, src)
}
func (m *FindUsedVoucherUserMobileResponse) XXX_Size() int {
	return xxx_messageInfo_FindUsedVoucherUserMobileResponse.Size(m)
}
func (m *FindUsedVoucherUserMobileResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FindUsedVoucherUserMobileResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FindUsedVoucherUserMobileResponse proto.InternalMessageInfo

func (m *FindUsedVoucherUserMobileResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FindUsedVoucherUserMobileResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FindUsedVoucherUserMobileResponse) GetList() []*UsedVoucherUserMobile {
	if m != nil {
		return m.List
	}
	return nil
}

func init() {
	proto.RegisterType((*IssueAwardByMallCouponRequest)(nil), "sh.IssueAwardByMallCouponRequest")
	proto.RegisterType((*IssueAwardByMallCouponResponse)(nil), "sh.IssueAwardByMallCouponResponse")
	proto.RegisterType((*IssueAwardCoupon)(nil), "sh.IssueAwardCoupon")
	proto.RegisterType((*Response)(nil), "sh.Response")
	proto.RegisterType((*RecoverUserVoucher)(nil), "sh.RecoverUserVoucher")
	proto.RegisterType((*RecoverUserVoucherRequest)(nil), "sh.RecoverUserVoucherRequest")
	proto.RegisterType((*FindUsedVoucherUserMobileRequest)(nil), "sh.FindUsedVoucherUserMobileRequest")
	proto.RegisterType((*UsedVoucherUserMobile)(nil), "sh.UsedVoucherUserMobile")
	proto.RegisterType((*FindUsedVoucherUserMobileResponse)(nil), "sh.FindUsedVoucherUserMobileResponse")
}

func init() { proto.RegisterFile("sh/voucher.proto", fileDescriptor_d88fc8742b4733e7) }

var fileDescriptor_d88fc8742b4733e7 = []byte{
	// 482 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0x95, 0xe3, 0xa8, 0x6d, 0x26, 0x29, 0x54, 0x2b, 0x28, 0x4e, 0xa0, 0xe0, 0xae, 0x00, 0xe5,
	0x42, 0x90, 0xca, 0x05, 0x8e, 0xa5, 0xa2, 0x52, 0x0e, 0xbd, 0xb8, 0x0d, 0x07, 0x38, 0x58, 0xae,
	0x77, 0x54, 0xaf, 0x14, 0x7b, 0x83, 0xc7, 0x0e, 0x70, 0xe3, 0xc8, 0xaf, 0xe0, 0xb7, 0xa2, 0xfd,
	0x88, 0x4c, 0x89, 0xd3, 0x4a, 0xb9, 0x79, 0x66, 0x9e, 0xdf, 0xbc, 0x79, 0xb3, 0xbb, 0x70, 0x40,
	0xd9, 0xdb, 0xa5, 0xaa, 0xd3, 0x0c, 0xcb, 0xc9, 0xa2, 0x54, 0x95, 0x62, 0x1d, 0xca, 0xf8, 0x0c,
	0x8e, 0xa6, 0x44, 0x35, 0x9e, 0x7e, 0x4f, 0x4a, 0xf1, 0xf1, 0xe7, 0x45, 0x32, 0x9f, 0x9f, 0xa9,
	0x7a, 0xa1, 0x8a, 0x08, 0xbf, 0xd5, 0x48, 0x15, 0x7b, 0x02, 0xbb, 0x35, 0x61, 0x19, 0x4b, 0x11,
	0x78, 0xa1, 0x37, 0xee, 0x45, 0x3b, 0x3a, 0x9c, 0x0a, 0xf6, 0x14, 0x7a, 0xa9, 0x41, 0xea, 0x52,
	0xc7, 0x94, 0xf6, 0x6c, 0x62, 0x2a, 0xf8, 0x0f, 0x78, 0xbe, 0x89, 0x96, 0x16, 0xaa, 0x20, 0x64,
	0x0c, 0xba, 0xa9, 0x12, 0x68, 0x48, 0xfd, 0xc8, 0x7c, 0xb3, 0x00, 0x76, 0x73, 0x24, 0x4a, 0x6e,
	0xd0, 0x11, 0xae, 0x42, 0x36, 0x86, 0xae, 0x48, 0xaa, 0x24, 0xf0, 0x43, 0x7f, 0xdc, 0x3f, 0x79,
	0x34, 0xa1, 0x6c, 0xd2, 0xf0, 0x3b, 0x66, 0x83, 0xe0, 0x5f, 0xe1, 0xe0, 0xff, 0x0a, 0x7b, 0x01,
	0x7d, 0x27, 0xd5, 0xb4, 0xb4, 0xdc, 0x60, 0x53, 0x67, 0xba, 0xf1, 0x6b, 0x78, 0xe8, 0x00, 0x58,
	0x88, 0xb8, 0x92, 0x39, 0x06, 0xbe, 0xd1, 0xb5, 0x6f, 0xd3, 0x9f, 0x0a, 0x71, 0x25, 0x73, 0xe4,
	0xef, 0x61, 0x6f, 0xbb, 0x01, 0xf8, 0x17, 0x60, 0x11, 0xa6, 0x6a, 0x89, 0xe5, 0x8c, 0xb0, 0xfc,
	0x6c, 0xf7, 0xc0, 0x42, 0x18, 0x50, 0x5a, 0xe6, 0xf1, 0x6d, 0x87, 0x41, 0xe7, 0x66, 0xd6, 0x65,
	0x0e, 0xfb, 0x6e, 0x69, 0x71, 0x15, 0x4b, 0x41, 0x41, 0x27, 0xf4, 0xc7, 0x7e, 0xd4, 0x77, 0xc9,
	0xab, 0xa9, 0x20, 0xfe, 0xdb, 0x83, 0xe1, 0x3a, 0xf9, 0x6a, 0x81, 0xcf, 0xa0, 0xa7, 0x07, 0xa2,
	0x0a, 0xf3, 0x85, 0x13, 0xdb, 0x24, 0xf4, 0x14, 0x24, 0x6f, 0x0a, 0x27, 0xd7, 0x7c, 0xb3, 0x0f,
	0x30, 0x30, 0x82, 0x5c, 0x0f, 0x67, 0xfa, 0xa1, 0x36, 0xbd, 0xa5, 0x4d, 0xbf, 0x6e, 0x02, 0x7e,
	0x0e, 0xe1, 0xb9, 0x2c, 0xc4, 0x8c, 0x50, 0xb8, 0x94, 0x86, 0x5e, 0xa8, 0x6b, 0x39, 0xc7, 0x95,
	0xa0, 0xb5, 0x91, 0xbc, 0xf5, 0x91, 0x2e, 0xe1, 0x71, 0x2b, 0x87, 0x76, 0xec, 0xdf, 0x9f, 0xdd,
	0x40, 0xd0, 0xfc, 0x6b, 0x76, 0x60, 0xb0, 0xd6, 0x2b, 0xbd, 0x03, 0x1b, 0xf2, 0x5f, 0x1e, 0x1c,
	0xdf, 0xa1, 0x6e, 0xab, 0x83, 0xf9, 0x06, 0xba, 0x73, 0x49, 0x95, 0xf3, 0x68, 0xa8, 0x3d, 0x6a,
	0xa7, 0x37, 0xb0, 0x93, 0x3f, 0x1d, 0x78, 0xe0, 0x6a, 0x97, 0x58, 0x2e, 0x65, 0x8a, 0xec, 0xb4,
	0xf5, 0x64, 0x1c, 0x6d, 0x70, 0xdb, 0x7a, 0x38, 0x1a, 0xd8, 0xb2, 0x93, 0x9c, 0xc1, 0x70, 0xe3,
	0x5c, 0xec, 0xa5, 0x86, 0xde, 0xb7, 0x94, 0xd1, 0xab, 0x7b, 0x50, 0xae, 0x53, 0x0c, 0x87, 0xed,
	0xf7, 0x9a, 0x1d, 0xdf, 0xbe, 0x93, 0x2d, 0x4f, 0xc9, 0x88, 0xdf, 0x05, 0xb1, 0x0d, 0xae, 0x77,
	0xcc, 0xd3, 0xf4, 0xee, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x52, 0xb4, 0xcd, 0x1d, 0xae, 0x04,
	0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VoucherServiceClient is the client API for VoucherService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VoucherServiceClient interface {
	// 收回用户优惠券
	RecoverUserVoucher(ctx context.Context, in *RecoverUserVoucherRequest, opts ...grpc.CallOption) (*Response, error)
	// 根据优惠券模板id查找已核销的用户手机号
	FindUsedVoucherUserMobile(ctx context.Context, in *FindUsedVoucherUserMobileRequest, opts ...grpc.CallOption) (*FindUsedVoucherUserMobileResponse, error)
	//发商城优惠券
	IssueAwardByMallCoupon(ctx context.Context, in *IssueAwardByMallCouponRequest, opts ...grpc.CallOption) (*IssueAwardByMallCouponResponse, error)
}

type voucherServiceClient struct {
	cc *grpc.ClientConn
}

func NewVoucherServiceClient(cc *grpc.ClientConn) VoucherServiceClient {
	return &voucherServiceClient{cc}
}

func (c *voucherServiceClient) RecoverUserVoucher(ctx context.Context, in *RecoverUserVoucherRequest, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/sh.VoucherService/RecoverUserVoucher", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) FindUsedVoucherUserMobile(ctx context.Context, in *FindUsedVoucherUserMobileRequest, opts ...grpc.CallOption) (*FindUsedVoucherUserMobileResponse, error) {
	out := new(FindUsedVoucherUserMobileResponse)
	err := c.cc.Invoke(ctx, "/sh.VoucherService/FindUsedVoucherUserMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) IssueAwardByMallCoupon(ctx context.Context, in *IssueAwardByMallCouponRequest, opts ...grpc.CallOption) (*IssueAwardByMallCouponResponse, error) {
	out := new(IssueAwardByMallCouponResponse)
	err := c.cc.Invoke(ctx, "/sh.VoucherService/IssueAwardByMallCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoucherServiceServer is the server API for VoucherService service.
type VoucherServiceServer interface {
	// 收回用户优惠券
	RecoverUserVoucher(context.Context, *RecoverUserVoucherRequest) (*Response, error)
	// 根据优惠券模板id查找已核销的用户手机号
	FindUsedVoucherUserMobile(context.Context, *FindUsedVoucherUserMobileRequest) (*FindUsedVoucherUserMobileResponse, error)
	//发商城优惠券
	IssueAwardByMallCoupon(context.Context, *IssueAwardByMallCouponRequest) (*IssueAwardByMallCouponResponse, error)
}

// UnimplementedVoucherServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVoucherServiceServer struct {
}

func (*UnimplementedVoucherServiceServer) RecoverUserVoucher(ctx context.Context, req *RecoverUserVoucherRequest) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverUserVoucher not implemented")
}
func (*UnimplementedVoucherServiceServer) FindUsedVoucherUserMobile(ctx context.Context, req *FindUsedVoucherUserMobileRequest) (*FindUsedVoucherUserMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUsedVoucherUserMobile not implemented")
}
func (*UnimplementedVoucherServiceServer) IssueAwardByMallCoupon(ctx context.Context, req *IssueAwardByMallCouponRequest) (*IssueAwardByMallCouponResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IssueAwardByMallCoupon not implemented")
}

func RegisterVoucherServiceServer(s *grpc.Server, srv VoucherServiceServer) {
	s.RegisterService(&_VoucherService_serviceDesc, srv)
}

func _VoucherService_RecoverUserVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverUserVoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).RecoverUserVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.VoucherService/RecoverUserVoucher",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).RecoverUserVoucher(ctx, req.(*RecoverUserVoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_FindUsedVoucherUserMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUsedVoucherUserMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).FindUsedVoucherUserMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.VoucherService/FindUsedVoucherUserMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).FindUsedVoucherUserMobile(ctx, req.(*FindUsedVoucherUserMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_IssueAwardByMallCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssueAwardByMallCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).IssueAwardByMallCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.VoucherService/IssueAwardByMallCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).IssueAwardByMallCoupon(ctx, req.(*IssueAwardByMallCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VoucherService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sh.VoucherService",
	HandlerType: (*VoucherServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecoverUserVoucher",
			Handler:    _VoucherService_RecoverUserVoucher_Handler,
		},
		{
			MethodName: "FindUsedVoucherUserMobile",
			Handler:    _VoucherService_FindUsedVoucherUserMobile_Handler,
		},
		{
			MethodName: "IssueAwardByMallCoupon",
			Handler:    _VoucherService_IssueAwardByMallCoupon_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sh/voucher.proto",
}

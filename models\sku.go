package models

import "time"

type Sku struct {
	Id              int32     `xorm:"not null pk comment('SKU ID') INT(11)"`
	ProductId       int32     `xorm:"default NULL comment('商品库中的商品ID') index INT(11)"`
	MarketPrice     int32     `xorm:"default NULL comment('市场价') INT(11)"`
	RetailPrice     int32     `xorm:"default NULL comment('建议价格') INT(11)"`
	R1PurchasePrice int32     `xorm:"not null default 0 comment('r1采购价 单位分') INT(11)"`
	BarCode         string    `xorm:"default 'NULL' comment('商品条码') VARCHAR(36)"`
	WeightForUnit   float64   `xorm:"default NULL comment('重量（kg）') DOUBLE(8,2)"`
	UpdateDate      time.Time `xorm:"default 'current_timestamp()' DATETIME"`
}

func (s Sku) TableName() string {
	return "dc_product.sku"
}

type SkuThird struct {
	Id            int32  `xorm:"not null pk autoincr INT(11)"`
	SkuId         int32  `xorm:"not null comment('商品库SKU ID') INT(11)"`
	ThirdSkuId    string `xorm:"default NULL comment('第三方SKU ID') VARCHAR(36)"`
	ThirdSpuId    string `xorm:"default NULL comment('第三方SPU ID') VARCHAR(36)"`
	ErpId         int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	ProductId     int32  `xorm:"default NULL comment('商品ID') INT(11)"`
	ThirdSpuSkuId string `xorm:"default NULL comment('拼接的货号，作唯一键') VARCHAR(73)"`
}

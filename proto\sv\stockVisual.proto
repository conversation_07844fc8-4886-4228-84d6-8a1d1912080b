syntax = "proto3";
package sv;

// 库存可视化模块
service Visual {
  //区域仓库存列表
  rpc RegionWarehouseStockList (WarehouseLStockistRequest) returns (RegionWarehouseStockResponse);
  //前置仓库存列表
  rpc PreposeWarehouseStockList (WarehouseLStockistRequest) returns (PreposeWarehouseStockResponse);

  // 查询权限下的仓库列表
  rpc GetWarehouseList (WarehouseListRequest) returns (WarehouseListResponse);

  //创建异步导出任务
  rpc CreateTask (CreateTaskRequest) returns (BaseResponse);
  //查询异步任务列表
  rpc GetTaskList (TaskListRequest) returns (TaskListResponse);

  // 保存仓库配置信息
  rpc WarehouseRuleConfigurationAdd(WarehouseRuleConfigurationVo) returns (ResponseData);
  // 查询仓库规则信息
  rpc GetWarehouseRuleConfiguration(GetWarehouseIdVo) returns (WarehouseConfigurationResponse);

  // 搜索渠道商品信息
  rpc SearchChannelProduct(GetChannelProductVo) returns (ChannelProductResponseResponse);

  // 保存商品规则信息
  rpc SaveProductRule(ProductResVo)returns (ProductConfigurationResponse);

  // 校验excle商品规则则信息
  rpc CheckProductRule(ProductResVo)returns (ChannelProductResponseResponse);

  // 删除商品信息
  rpc DeleteProductRule(DeleteProduct) returns (ResponseData);
  // 批量启用停用
  rpc BatchUpdateProduct(BatchUpdateProductVo) returns(ResponseData);
  //查询商品信息
  rpc SearchProductRules(ProductRuleConfigurationVo)returns (ProductConfigurationResponse);
  // 查询效期列表信息
  rpc SearchEffectiveList(EffectiveManagementResVo)returns (EffectiveManagementResponseData);

  // 新增权限设置
  rpc AddAuthInfo(AddAuthInfoRequest) returns(BaseResponse);
  // 新增权限设置
  rpc UpdateAuthInfo(UpdateAuthInfoRequest) returns(BaseResponse);
  // 新增权限设置
  rpc AuthUserInfoList(AuthInfoListRequest) returns(AuthInfoListResponse);
  // 新增权限设置
  rpc GetAuthInfoDetail(AuthInfoDetailRequest) returns(AuthInfoDetailResponse);

  // 调用处理库存可视化任务
  rpc HandleStockVisualTask(Empty) returns(BaseResponse);

  // rpc 处理下单和退款的保存接口
  rpc InsertIntoSalesRecord(SalesRecordVos) returns (ResponseData );


  // 查询北京的预测销量的接口
  rpc GetDiscountFromBeijing(DiscountFromBeijingRequest) returns (DisCountBeijingResponse );


  // 初始化折扣数据的接口
  rpc InitDiscountTask(DiscountTaskRequest) returns (BaseResponse );

  // 增量折扣数据处理
  rpc IncrementPromotionDiscountTask(IncrementPromotionDiscountRes) returns (BaseResponse );


  // 增量按钮权限
  rpc AddButtonPermissions(AddButtonPermissionsVo) returns (BaseResponse );
}

// 空参数
message Empty {
}

//通用返回
message BaseResponse {
  //
  int32 code = 1;
  //消息
  string msg = 2;
}

// 库存可视化列表请求参数
message WarehouseLStockistRequest {
  // 区域仓或前置仓id，多个用逗号分隔
  repeated int32 warehouse_id = 1;
  // 商品sku
  int32 sku_id = 2;
  // 商品A8编码
  string third_sku_id = 3;
  // 库存状态 默认0全部，1 是，2 否
  int32 is_warning = 4;
  // 库存数量
  repeated int32 stock_num = 5;
  // 补货数量
  repeated int32 goods_num = 6;
  // 前置仓所在城市
  string city = 7;
  // 分页页码
  int32 page_index = 8;
  // 分页数据量
  int32 page_size = 9;
  // 账面库存排序 1升序 2降序
  int32 book_inventory_order = 10;
}

// 区域仓库存可视化响应数据
message RegionWarehouseStockResponse {
  int32 code = 1;
  // 数据
  repeated RegionWarehouseStockData data = 2;
  // 消息
  string msg = 3;
  // 总数
  int32 total = 4;
}

// 前置仓库存可视化响应数据
message PreposeWarehouseStockResponse {
  int32 code = 1;
  // 消息
  string msg = 2;
  // 总数
  int32 total = 3;
  // 数据
  repeated PreposeWarehouseStockData data = 4;
}

// 区域库存可视化数据
message RegionWarehouseStockData {
  // 商品sku
  int32 sku_id = 1;
  // A8编码
  string third_sku_id = 2;
  // 商品名称
  string product_name = 3;
  // 所属大仓id
  int32 warehouse_id = 4;
  // 所属大仓名称
  string warehouse_name = 5;
  // 账目库存
  int32 book_inventory = 6;
  // 可用库存
  int32 available_stock = 7;
  // 安全库存水位
  int32 safe_stock_level = 8;
  // 库存是否预警 1 是，2否
  int32 stock_warning = 9;
  // 建议补货量
  int32 suggested_stock = 10;
  // 历史销量
  // 7天
  int32 history_7_sv = 11;
  // 15天
  int32 history_15_sv = 12;
  // 30天
  int32 history_30_sv = 13;
  // 60天
  int32 history_60_sv = 14;
  // 90天
  int32 history_90_sv = 15;
  // 预计销量
  // 7天
  int32 expect_7_sv = 16;
  // 15天
  int32 expect_15_sv = 17;
  // 30天
  int32 expect_30_sv = 18;
  // 60天
  int32 expect_60_sv = 19;
  // 90天
  int32 expect_90_sv = 20;
}

// 前置库存可视化数据
message PreposeWarehouseStockData {
  // 商品sku
  int32 sku_id = 1;
  // A8编码
  string third_sku_id = 2;
  // 商品名称
  string product_name = 3;
  // 前置仓id
  int32 warehouse_id = 4;
  // 前置仓名称
  string warehouse_name = 5;
  // 账目库存
  int32 book_inventory = 6;
  // 占用库存
  int32 occupy_stock = 7;
  // 可用库存
  int32 available_stock = 8;
  // 安全库存水位
  int32 safe_stock_level = 9;
  // 高库存水位
  int32 high_stock_level = 10;
  // 库存是否预警 1 是，2否
  int32 stock_warning = 11;
  // 触发库存预警的类型,1安全库存 2高库存
  int32 warning_type = 12;
  // 所属区域仓名称
  string region_name = 13;
  // 所属区域仓可用库存
  int32 region_usable_stock = 14;
  // 建议补货量
  int32 suggested_stock = 15;
  // 历史销量
  // 7天
  int32 history_7_sv = 16;
  // 15天
  int32 history_15_sv = 17;
  // 30天
  int32 history_30_sv = 18;
  // 60天
  int32 history_60_sv = 19;
  // 90天
  int32 history_90_sv = 20;
  // 预计销量
  // 7天
  int32 expect_7_sv = 21;
  // 15天
  int32 expect_15_sv = 22;
  // 30天
  int32 expect_30_sv = 23;
  // 60天
  int32 expect_60_sv = 24;
  // 90天
  int32 expect_90_sv = 25;

//  产品标签
  string tag = 26;

//  预测7天销量
  int32  fore_cast_Day_7 = 27;
//  预测15天销量
  int32  fore_cast_Day_15 = 28;
//  预测30天销量
  int32  fore_cast_Day_30 = 29;

  // AI销量预测建议补货量
  int64  ai_suggested_stock = 30;


}

// 任务列表请求参数
message TaskListRequest {
  // 任务: 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
  int32 task_content = 1;
  // 查询人id
  string user_no = 2;
  // 分页页码
  int32 page_index = 3;
  // 分页数据量
  int32 page_size = 4;
  // 任务状态
  int32 task_status = 5;
}

// 任务列表响应参数
message TaskListResponse {
  int32 code = 1;
  // 消息
  string msg = 2;
  // 总数
  int32 total = 3;
  // 数据
  repeated TaskData data = 4;
}

// 异步任务创建
message CreateTaskRequest {
  // 任务类型 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
  int32 task_content = 1;
  // 操作文件路径或者参数
  string operation_file_url = 2;
  // 操作请求的token值，类似userinfo
  string request_header = 3;
  // 创建人id
  string create_id = 4;
}

// 任务数据
message TaskData {
  int32 id = 1;
  // 任务类型 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
  int32 task_content = 2;
  // 任务状态 1 进行中 2 已完成 3 失败
  int32 task_status = 3;
  // 操作文件路径或者参数
  string operation_file_url = 4;
  // 操作请求的token值，类似userinfo
  string request_header = 5;
  // 操作结果文件路径
  string resulte_file_url = 6;
  // 创建时间
  string create_time = 7;
  // 更新时间
  string modify_time = 8;
  // 创建人id
  string create_id = 9;
  // 任务详情
  string task_detail = 10;
}

//仓库列表请求参数
message WarehouseListRequest {
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
  string category = 1;
  //页码
  int32 pageindex = 2;
  //每页行数
  int32 pagesize = 3;
  //搜索关键词
  string keyWord = 4;
  // 所属城市
  string city = 5;
  // 是否查询前置仓和前置虚拟仓, 默认 0 否，1 是
  int32 prepose_category = 6;
}

//仓库列表返回数据
message WarehouseListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库数组
  repeated WarehouseList WarehouseAarray = 5;
}

//仓库列表数据集
message WarehouseList {
  //ID
  int32 id = 1;
  //第三方仓库ID 例如a8id,管易ID
  string thirdid = 2;
  //仓库编号
  string code = 3;
  //仓库名称
  string name = 4;
  //仓库归属(1-A8,2-管易)
  int32 comefrom = 5;
  //仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
  int32 level = 6;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 7;
  //仓库地址
  string address = 8;
  //仓库联系人
  string contacts = 9;
  //仓库联系方式
  string tel = 10;
  //仓库状态（0-禁用，1-启用）
  int32 status = 11;
  //创建时间
  string createdate = 12;
  //最后更新时间
  string lastdate = 13;
  //所属系统 0:默认,1:ERP,2:子龙
  int32 subsystem = 14;
  //仓库比例
  int32 ratio = 15;
  //关联门店信息
  repeated string warehouse_info = 16;
  //仓库经度
  int64 lng = 17;
  //仓库纬度
  int64 lat = 18;
  // 所属区域
  string region = 19;
  // 所属城市
  string city = 20;
}

//====================== 权限设置 =========================

// 新增权限信息请求
message AddAuthInfoRequest {
  repeated AddAuthRequstList data = 1;
}

message AddAuthRequstList {
  // 用户id
  string user_no = 1;
  // 用户名
  string user_name = 2;
  // 用户中文名
  string staff_name_cn = 3;
  // 用户手机号
  string user_mobile = 4;
}

// 编辑权限信息请求
message UpdateAuthInfoRequest {
  // 用户id
  string user_no = 1;
  // 仓库id,逗号分隔
  string warehouse_id = 2;
}

// 用户权限详细信息请求
message AuthInfoDetailRequest {
  // user_no
  string user_no = 1;
}

// 用户权限详细信息响应
message AuthInfoDetailResponse {
  int32 code = 1;
  // 返回的message
  string msg = 2;
  AuthInfo data = 3;// 用户code
}

// 用户权限列表信息请求
message AuthInfoListRequest {
  // 页码
  int32 page_index = 1;
  // 页面数据量
  int32 page_size = 2;

  // 查询用户的字段，可以是name，user_no,或者mobile
  string find_str = 3;
}

// 用户权限列表信息响应
message AuthInfoListResponse {
  int32 code = 1;
  // 返回的message
  string msg = 2;
  repeated AuthInfo data = 3;// 用户code
  // 总数
  int32 total = 4;
}

// 用户权限信息
message AuthInfo {
  string user_no = 1;// 用户code
  string user_name = 2; // 用户名
  string staff_name_cn = 3;// 用户中文名
  string user_mobile = 4;// 用户手机号
  string update_date = 5; // 最后编辑时间
  repeated RegionWarehouseInfo region_warehouse = 6; // 区域仓信息列表
  repeated PreposeWarehouseInfo prepose_warehouse = 7; // 前置仓信息列表
  int32 button_permission  = 8; // 是否开启按钮权限
  int32 id = 9;
  int32 del_pro_permission = 10;  //是否有删除商品权限
}

// 区域仓信息
message RegionWarehouseInfo {
  // 仓库id
  int32 warehouse_id = 1;
  // 仓库编码
  string warehouse_code = 2;
  // 仓库名称
  string warehouse_name = 3;
}

// 前置仓信息
message PreposeWarehouseInfo {
  // 仓库id
  int32 warehouse_id = 1;
  // 仓库编码
  string warehouse_code = 2;
  // 仓库名称
  string warehouse_name = 3;
}

//===============================================



message ResponseData {

  int32 code = 1;
  string data = 2;
  string error = 3;
  string msg = 4;
}

message WarehouseConfigurationResponse {

  int32 total = 1;
  WarehouseRuleConfigurationResponse data = 2;
  string msg = 3;
  int32 code = 4;

}

// 仓库配置表
message WarehouseRuleConfigurationResponse {
  // id
  int32 id = 1;
  // 仓库的id
  int32 warehouse_id = 2;
  // 安全库存
  int32 safe_stock  =3;
//  安全周转天数
  int32 safe_days   =4;
//  状态 1启用 0 禁用
  int32 status     = 5;

  string create_date = 6;
  string update_date = 7;

  int32  type  = 8;
  int32 dead_stock = 9;
  int32 dead_stock_days = 10;
}


message ProductConfigurationResponse  {
  int32 total  =1 ;
  repeated ProductRuleConfigurationResponse data = 2;
  string msg = 3;
  int32 code =4;
}

message ProductResVo  {
  int32 warehouse_id = 1;
  int32 type = 2;  // 类型(1:安全规则预警 2:高库存预警 3:最小起订量 4:补货系数)
  repeated ProductRuleConfigurationResponse data = 3;
  // 操作人
  string operator = 4;
}

// 商品规则配置表
message ProductRuleConfigurationResponse  {
   int32 id  = 1;
   int32 warehouse_id = 2;
   int32 type = 3;
   string sku_id = 4;
   string third_sku_id = 5;
   // 商品名称
   string product_name = 6 ;
//   安全库存
   int32 safe_stock = 7;
//   安全周转天数
   int32 safe_days = 8;
//   滞销库存
   int32 dead_stock = 9;
//   滞销周转天数
   int32 dead_stock_days = 10;
//   最小补货数
   int32 minimum_order_quantity = 11;
  //   规则状态 1启用 2 禁用
   int32 status = 12;
   string operator = 13;
   string create_date = 14;
   string update_date = 15;
   // 补货系数
   float replenishment_rate = 16;
}

message ProductRuleConfigurationVo {
  int32 WarehouseId = 1;
  int32 Type =2; // 类型(1:安全规则预警 2:高库存预警 3:最小起订量  4:补货系数)
  string SkuId =3;
  string ThirdSkuId =4;
  int32 Status =5; // 启用1 禁用0
  int32 PageIndex = 6;
  int32 PageSize  =7;
}


message EffectiveManagementResponseData{
  int32 total = 1;
  string msg = 2;
  string error = 3;
  repeated  EffectiveManagementResponse data = 4;
}

// 效期列表返回
message EffectiveManagementResponse {
  int32 id = 1;
  // skuid
  string sku_id = 2;
  // 第三方货号
  string third_sku_id =3;
  // 产品名称
  string product_name =4;

  int32 warehouse_id =5;
  // 仓库名称
  string warehouse_name = 6;
  // 入库数量
  int32 storage_stock =7;
  //入库的单号
  string storage_order = 8;
  // 入库时间
  string storage_date =9;
  // 生产时间
  string ProductionDate =10;
  // 生产批次
  string ProductionBatch = 11;
  // 失效时间
  string ExpirationDate =12;
  // 效期天数
  int32 effective_days  = 13;
  // 1优先出货 2重点关注 3二级预警 4一级预警 5即将过期 6已过期
  int32 effective_state =14;
  // 当前库存
  int32 current_stock =15;
  // 已售库存
  int32 off_stock =16;
  // 导入时间
  string create_date  = 17;
  // 更新时间
  string update_date = 18;
}


message EffectiveManagementResVo  {
  string warehouse_id = 1;
  string sku_id = 2;
  string third_sku_id  =3;
  int32 status =4;
  int32 start_day =5;
  int32 end_day = 6;
  int32 pageSize =7;
  int32 PageIndex =8;
  // 过滤权限
  string operator = 9;
}


message WarehouseRuleConfigurationVo {

  int32 id  = 1;
  int32 warehouse_id = 2;
  // 安全库存
  int32 safe_stock = 3;
  // 安全周转天数
  int32 safe_days = 4;
  // '启用1 禁用2'
  int32 status = 5;

  // 类型 1:安全规则预警 2:高库存预警
  int32 type  = 6;
  // 滞销库存数量
  int32 dead_stock = 7;
  // 滞销库存周转天数
  int32 dead_stock_days = 8;
  string operator = 9;
}



message GetWarehouseIdVo {
  int32 warehouse_id = 1;
  // 类型 1:安全规则预警 2:高库存预警
  int32 type = 2;
}

message GetChannelProductVo {
  string sku_id = 1;
  string third_sku_id = 2;
  int32 type = 3;
  int32 page_index = 4;
  int32 page_size = 5;
  int32 warehouse_id = 6;
}


message GetChannelProductRes {
  string sku_id = 1;
  string third_sku_id = 2;
  string product_name = 3;
}


message ChannelProductResponseResponse  {
  int32 code = 1;
  string msg = 2;
  repeated  GetChannelProductRes  data = 3;
  int32 total = 4;
}


message DeleteProduct  {
  repeated int32  Ids = 1;
}



message BatchUpdateProductVo  {

  repeated int32  Ids = 1;
  int32 status = 2;
}

message SalesRecordVos {
  repeated SalesRecordVo data = 1;
}

message SalesRecordVo {

  // 订单号
  string order_sn = 1;
  // 退款单号
  string refund_order_sn = 2;
  // skuid
  string sku_id = 3;
//   status  1: 下单  2 退款
  int32 status = 4;
  // 下单数量 num
  int32 num = 5;

}


message CheckSkuidOrThirSkuidVo {



}




// 初始化折扣数据的接口
message DiscountTaskRequest {

  // 开始日期,以当前日期开始往后算30天的折扣数据
  string begin_date = 1;
}



// 获取折扣数据查询北京的预测数据的接口
message DiscountFromBeijingRequest {

  repeated DiscountRequest data = 1;

}

message DiscountRequest {
  // sku_id
  string sku_id = 1;
  // 仓库的id
  string warehouse_id =2;

}


message DisCountBeijingResponse  {
  int32 code = 1;
  string msg = 2;
   Resp  data = 3;
  int32 total = 4;
}


message Resp  {

  int32 status = 1;
  repeated  RespDataBeijing contents = 2;
}


message RespDataBeijing {

  int32 SkuId =1;
  int32 WarehouseId =2;
  string Tag = 3;
  int32  ForecastDay7 = 4;
  int32 ForecastDay15 = 5;
  int32 ForecastDay30 =6;

}


message IncrementPromotionDiscountRes {
  int32 promotion_id = 1;
  string  update_date = 2;

}


message AddButtonPermissionsVo {
  int32 is_permission = 1;
  int32 id = 2;
  int32 del_pro_permission = 3;
}
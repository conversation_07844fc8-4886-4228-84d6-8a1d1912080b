package zilong

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"io/ioutil"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// Response 请求响应体
type Response struct {
	Message       string      `json:"message"`       // 异常信息
	SystemError   string      `json:"systemError"`   // 未捕获系统异常
	BusinessError string      `json:"businessError"` // 返回业务异常信息
	StatusCode    int         `json:"statusCode"`    // 返回业务异常信息
	Result        interface{} `json:"result"`
	Success       bool        `json:"success"`
}

// Clients and Transports are safe for concurrent use by multiple goroutines and for efficiency should only be created once and re-used.
var client *http.Client

// 因为出错静默会记录日志，但一些情况即使出错也不要记录日志
// 比如开票模糊查询公司，3个汉字以内都直接报错 statusCode = 5002
var errorDontLogPaths = []string{
	"tax-control-api/output/tax/queryCompanyName",
}

func init() {
	client = &http.Client{
		Timeout: time.Second * 60,
	}
}

// Post 请求北京接口
func Post(path string, data map[string]interface{}, result interface{}) (res *Response, err error) {
	return Request("POST", path, data, result)
}

// Request 请求北京接口
// 这里result是为了接口值的自动写入，避免到处无意义的json.Unmarshal，res会返回全部的响应信息
func Request(method string, path string, data map[string]interface{}, result interface{}) (res *Response, err error) {
	resBody, _, err := Do(method, path, data)
	defer func() {
		// 出错时记录请求日志
		if err != nil && errorShouldLog(path) {
			glog.Error(fmt.Sprintf("调用子龙接口(%s)出错，返回内容%s:%s，接口参数:%s", path, err.Error(), string(resBody), kit.JsonEncode(data)))
		}
	}()
	if err != nil {
		return
	}
	res = &Response{}
	// 如果不是空字符串，则自动处理结果赋值
	if result != "" {
		if reflect.ValueOf(result).Kind() != reflect.Ptr {
			return nil, errors.New("result参数必须是指针类型")
		}
		res.Result = result
	}
	if err = json.Unmarshal(resBody, res); err != nil {
		err = errors.New("解析响应body出错 " + err.Error())
	} else if !res.Success {
		err = errors.New(res.Message)
	}

	return
}

// Do 通用原始请求处理
func Do(method string, path string, data map[string]interface{}) (resBody []byte, contentType string, err error) {
	fullUrl, err := getFullUrl(path)
	if err != nil {
		return
	}
	body, _ := json.Marshal(data)
	req, err := http.NewRequest(method, fullUrl, bytes.NewBuffer(body))
	if err != nil {
		return
	}

	// 请求前置处理，设置签名
	beforeRequest(req)
	httpResp, err := client.Do(req)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	contentType = httpResp.Header.Get("Content-Type")
	resBody, err = ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}
	// 子龙接口不会将 http状态码错误 的具体错误信息放在body中，所有直接返回
	if httpResp.StatusCode >= 400 {
		err = errors.New("请求出错 " + httpResp.Status)
		return
	}

	return
}

// 获取完整的链接路径
func getFullUrl(path string) (string, error) {
	// 是完整的链接不处理
	if strings.HasPrefix(path, "http") {
		return path, nil
	}
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	// 获取北京访问的基础地址，部分服务url带了path，需要移除
	domain := config.GetString("bj-scrm-outside-url")
	if strings.TrimSpace(domain) == "" {
		return "", errors.New("读取北京url配置出错 " + domain)
	}
	parse, err := url.Parse(domain)
	if err != nil {
		return "", errors.New("解析域名北京url错误 " + err.Error())
	}
	return parse.Scheme + "://" + parse.Host + "/" + path, nil
}

// 请求库前置处理，添加请求头
func beforeRequest(req *http.Request) {
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	appId := config.GetString("BJAuth.AppId")
	appVersion := config.GetString("BJAuth.Version")

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("focus-auth-appid", appId)
	req.Header.Set("focus-auth-version", appVersion)
	req.Header.Set("focus-auth-timestamp", timestamp)
	req.Header.Set("focus-auth-userid", "0")
	req.Header.Set("focus-auth-username", "0")
	req.Header.Set("focus-auth-url", req.URL.Path)

	// 签名字符串
	str := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s",
		appId, config.GetString("BJAuth.Secret"), req.URL.Path, timestamp, appVersion)
	h := md5.New()
	h.Write([]byte(str))
	sign := strings.ToUpper(fmt.Sprintf("%x", h.Sum(nil)))
	req.Header.Set("focus-auth-sign", sign)
}

// 是否需要记录日志
func errorShouldLog(path string) bool {
	for _, dont := range errorDontLogPaths {
		if dont == path {
			return false
		}
	}
	return true
}

func BjPost(path string, data map[string]interface{}, result interface{}) (res *Response, err error) {
	return BjRequest("POST", path, data, result)
}

// Request 请求北京接口
// 这里result是为了接口值的自动写入，避免到处无意义的json.Unmarshal，res会返回全部的响应信息
func BjRequest(method string, path string, data map[string]interface{}, result interface{}) (res *Response, err error) {
	resBody, _, err := BjDo(method, path, data)
	defer func() {
		// 出错时记录请求日志
		if err != nil && errorShouldLog(path) {
			glog.Error(fmt.Sprintf("调用子龙接口(%s)出错，返回内容%s:%s，接口参数:%s", path, err.Error(), string(resBody), kit.JsonEncode(data)))
		}
	}()
	if err != nil {
		return
	}
	res = &Response{}
	// 如果不是空字符串，则自动处理结果赋值
	if result != "" {
		if reflect.ValueOf(result).Kind() != reflect.Ptr {
			return nil, errors.New("result参数必须是指针类型")
		}
		res.Result = result
	}
	if err = json.Unmarshal(resBody, res); err != nil {
		err = errors.New("解析响应body出错 " + err.Error())
	} else if !res.Success {
		err = errors.New(res.Message)
	}

	return
}

// Do 通用原始请求处理
func BjDo(method string, path string, data map[string]interface{}) (resBody []byte, contentType string, err error) {
	fullUrl, err := getFullUrl(path)
	if err != nil {
		return
	}
	body, _ := json.Marshal(data)
	req, err := http.NewRequest(method, fullUrl, bytes.NewBuffer(body))
	if err != nil {
		return
	}

	// 请求前置处理，设置签名
	BjbeforeRequest(req)
	httpResp, err := client.Do(req)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	contentType = httpResp.Header.Get("Content-Type")
	resBody, err = ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}
	// 子龙接口不会将 http状态码错误 的具体错误信息放在body中，所有直接返回
	if httpResp.StatusCode >= 400 {
		err = errors.New("请求出错 " + httpResp.Status)
		return
	}

	return
}

func BjbeforeRequest(req *http.Request) {
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	appId := config.GetString("bj.auth.appid")
	secret := config.GetString("bj.auth.secret")

	//appId = "rpcenter"
	//secret = "rpcenter@2014"

	//appId = "sz_rppet"
	//secret = "c4f7351598d1447"

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("apiId", appId)
	req.Header.Set("timestamp", timestamp)

	// 签名字符串
	str := fmt.Sprintf("apiSecret=%s&apiId=%s&timestamp=%s",
		secret, appId, timestamp)
	h := md5.New()
	h.Write([]byte(str))
	sign := strings.ToUpper(fmt.Sprintf("%x", h.Sum(nil)))
	req.Header.Set("sign", sign)

	//domainUrl := strings.Split(url, "//")[1]
	//baseUrl := strings.Split(domainUrl, "/")[0]
	//method := strings.Split(url, baseUrl)[1]
	//Timestamp := strconv.Itoa(int(time.Now().Unix()))
	//sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.Get("bj.auth.appid"), config.Get("bj.auth.secret"), method, Timestamp, config.Get("bj.auth.version"))
	//h := md5.New()
	//h.Write([]byte(sign))
	//md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	//arr := make(map[string]string)
	//arr["focus-auth-appid"] = config.Get("bj.auth.appid")
	//arr["focus-auth-userid"] = "0"
	//arr["focus-auth-username"] = "0"
	//arr["focus-auth-version"] = config.Get("bj.auth.version")
	//arr["focus-auth-url"] = method
	//arr["focus-auth-timestamp"] = Timestamp
	//arr["focus-auth-sign"] = md5sign
	//return arr

}

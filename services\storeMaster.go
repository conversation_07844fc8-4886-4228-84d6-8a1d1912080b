package services

import (
	"context"
	"errors"
	"order-center/proto/oc"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//根据渠道订单号(ordersn) 获取门店的storeMasterId(appChannel)
func GetAppChannelByOrderSn(ordersn string) (int32, error) {
	if len(ordersn) <= 0 {
		return 0, errors.New("ordersn 不合法")
	}
	redisConn := GetRedisConn()
	key := OrderAppChannelRedisKey + ordersn
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去orderCenter查询db获取
	if len(appChannel) <= 0 {
		// clientOrderCenter := oc.GetOrderServiceClient()
		// defer clientOrderCenter.Close()
		service := OrderService{}
		request := &oc.QueryAppChannelByOrderSnReq{OrderSn: ordersn}
		outQuery, err := service.QueryAppChannelByOrderSn(context.Background(), request)
		if err != nil {
			glog.Error("QueryAppChannelByOrderSn rpc err", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}
		if outQuery.Code != 200 {
			glog.Error("QueryAppChannelByOrderSn rpc failed", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}

		return outQuery.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

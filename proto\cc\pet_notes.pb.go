// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/pet_notes.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/golang/protobuf/ptypes/struct"
	_ "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetNotesByUserIdRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotesByUserIdRequest) Reset()         { *m = GetNotesByUserIdRequest{} }
func (m *GetNotesByUserIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetNotesByUserIdRequest) ProtoMessage()    {}
func (*GetNotesByUserIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{0}
}

func (m *GetNotesByUserIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotesByUserIdRequest.Unmarshal(m, b)
}
func (m *GetNotesByUserIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotesByUserIdRequest.Marshal(b, m, deterministic)
}
func (m *GetNotesByUserIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotesByUserIdRequest.Merge(m, src)
}
func (m *GetNotesByUserIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetNotesByUserIdRequest.Size(m)
}
func (m *GetNotesByUserIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotesByUserIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotesByUserIdRequest proto.InternalMessageInfo

func (m *GetNotesByUserIdRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type GetNotesByUserIdResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	List                 []*PetNotes `protobuf:"bytes,4,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetNotesByUserIdResponse) Reset()         { *m = GetNotesByUserIdResponse{} }
func (m *GetNotesByUserIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetNotesByUserIdResponse) ProtoMessage()    {}
func (*GetNotesByUserIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{1}
}

func (m *GetNotesByUserIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotesByUserIdResponse.Unmarshal(m, b)
}
func (m *GetNotesByUserIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotesByUserIdResponse.Marshal(b, m, deterministic)
}
func (m *GetNotesByUserIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotesByUserIdResponse.Merge(m, src)
}
func (m *GetNotesByUserIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetNotesByUserIdResponse.Size(m)
}
func (m *GetNotesByUserIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotesByUserIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotesByUserIdResponse proto.InternalMessageInfo

func (m *GetNotesByUserIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetNotesByUserIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetNotesByUserIdResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetNotesByUserIdResponse) GetList() []*PetNotes {
	if m != nil {
		return m.List
	}
	return nil
}

type PetNotes struct {
	//id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	//便签内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	//是否设置提醒 1 未设置 2 设置
	IsRemind int32 `protobuf:"varint,6,opt,name=is_remind,json=isRemind,proto3" json:"is_remind"`
	//是否被删除 1 未删除 2 已删除
	IsDeleted int32 `protobuf:"varint,7,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	//提醒时间
	RemindTime string `protobuf:"bytes,8,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	//创建时间
	CreateTime string `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//创建人id
	CreateId string `protobuf:"bytes,10,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//更新时间
	UpdateTime string `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//更新人id
	UpdateId             string   `protobuf:"bytes,12,opt,name=update_id,json=updateId,proto3" json:"update_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetNotes) Reset()         { *m = PetNotes{} }
func (m *PetNotes) String() string { return proto.CompactTextString(m) }
func (*PetNotes) ProtoMessage()    {}
func (*PetNotes) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{2}
}

func (m *PetNotes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetNotes.Unmarshal(m, b)
}
func (m *PetNotes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetNotes.Marshal(b, m, deterministic)
}
func (m *PetNotes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetNotes.Merge(m, src)
}
func (m *PetNotes) XXX_Size() int {
	return xxx_messageInfo_PetNotes.Size(m)
}
func (m *PetNotes) XXX_DiscardUnknown() {
	xxx_messageInfo_PetNotes.DiscardUnknown(m)
}

var xxx_messageInfo_PetNotes proto.InternalMessageInfo

func (m *PetNotes) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetNotes) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetNotes) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetNotes) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PetNotes) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PetNotes) GetIsRemind() int32 {
	if m != nil {
		return m.IsRemind
	}
	return 0
}

func (m *PetNotes) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

func (m *PetNotes) GetRemindTime() string {
	if m != nil {
		return m.RemindTime
	}
	return ""
}

func (m *PetNotes) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetNotes) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *PetNotes) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *PetNotes) GetUpdateId() string {
	if m != nil {
		return m.UpdateId
	}
	return ""
}

//宠物便签——分页获取宠物便签列表——Request
type GetPetNotesListRequest struct {
	//当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页显示数据条数
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//便签内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	//是否设置提醒 1 未设置 2 设置
	IsRemind int32 `protobuf:"varint,6,opt,name=is_remind,json=isRemind,proto3" json:"is_remind"`
	//主键id
	Id                   int32    `protobuf:"varint,7,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetNotesListRequest) Reset()         { *m = GetPetNotesListRequest{} }
func (m *GetPetNotesListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPetNotesListRequest) ProtoMessage()    {}
func (*GetPetNotesListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{3}
}

func (m *GetPetNotesListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetNotesListRequest.Unmarshal(m, b)
}
func (m *GetPetNotesListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetNotesListRequest.Marshal(b, m, deterministic)
}
func (m *GetPetNotesListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetNotesListRequest.Merge(m, src)
}
func (m *GetPetNotesListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPetNotesListRequest.Size(m)
}
func (m *GetPetNotesListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetNotesListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetNotesListRequest proto.InternalMessageInfo

func (m *GetPetNotesListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetPetNotesListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPetNotesListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetPetNotesListRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *GetPetNotesListRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetPetNotesListRequest) GetIsRemind() int32 {
	if m != nil {
		return m.IsRemind
	}
	return 0
}

func (m *GetPetNotesListRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//宠物便签——分页获取宠物便签列表——Response
type GetPetNotesListResponse struct {
	Code                 int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total                int32            `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	Details              []*PetNotesModel `protobuf:"bytes,5,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPetNotesListResponse) Reset()         { *m = GetPetNotesListResponse{} }
func (m *GetPetNotesListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetNotesListResponse) ProtoMessage()    {}
func (*GetPetNotesListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{4}
}

func (m *GetPetNotesListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetNotesListResponse.Unmarshal(m, b)
}
func (m *GetPetNotesListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetNotesListResponse.Marshal(b, m, deterministic)
}
func (m *GetPetNotesListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetNotesListResponse.Merge(m, src)
}
func (m *GetPetNotesListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetNotesListResponse.Size(m)
}
func (m *GetPetNotesListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetNotesListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetNotesListResponse proto.InternalMessageInfo

func (m *GetPetNotesListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetNotesListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetNotesListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPetNotesListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetPetNotesListResponse) GetDetails() []*PetNotesModel {
	if m != nil {
		return m.Details
	}
	return nil
}

//宠物便签——分页获取宠物便签列表——Details Models
type PetNotesModel struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//便签内容
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	//便签内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	//是否设置提醒 1 未设置 2 设置
	IsRemind int32 `protobuf:"varint,6,opt,name=is_remind,json=isRemind,proto3" json:"is_remind"`
	//提醒时间
	RemindTime string `protobuf:"bytes,7,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	//创建人id
	CreateId string `protobuf:"bytes,8,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//创建时间
	CreateTime string `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//更新人id
	UpdateId string `protobuf:"bytes,10,opt,name=update_id,json=updateId,proto3" json:"update_id"`
	//更新时间
	UpdateTime           string   `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetNotesModel) Reset()         { *m = PetNotesModel{} }
func (m *PetNotesModel) String() string { return proto.CompactTextString(m) }
func (*PetNotesModel) ProtoMessage()    {}
func (*PetNotesModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{5}
}

func (m *PetNotesModel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetNotesModel.Unmarshal(m, b)
}
func (m *PetNotesModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetNotesModel.Marshal(b, m, deterministic)
}
func (m *PetNotesModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetNotesModel.Merge(m, src)
}
func (m *PetNotesModel) XXX_Size() int {
	return xxx_messageInfo_PetNotesModel.Size(m)
}
func (m *PetNotesModel) XXX_DiscardUnknown() {
	xxx_messageInfo_PetNotesModel.DiscardUnknown(m)
}

var xxx_messageInfo_PetNotesModel proto.InternalMessageInfo

func (m *PetNotesModel) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetNotesModel) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetNotesModel) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetNotesModel) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PetNotesModel) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PetNotesModel) GetIsRemind() int32 {
	if m != nil {
		return m.IsRemind
	}
	return 0
}

func (m *PetNotesModel) GetRemindTime() string {
	if m != nil {
		return m.RemindTime
	}
	return ""
}

func (m *PetNotesModel) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *PetNotesModel) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetNotesModel) GetUpdateId() string {
	if m != nil {
		return m.UpdateId
	}
	return ""
}

func (m *PetNotesModel) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

//宠物便签——获取宠物便签详情——Request
type GetPetNotesDetailRequest struct {
	//主键id
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	//用户id
	UserIds string `protobuf:"bytes,2,opt,name=user_ids,json=userIds,proto3" json:"user_ids"`
	//宠物id
	PetIds               string   `protobuf:"bytes,3,opt,name=pet_ids,json=petIds,proto3" json:"pet_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetNotesDetailRequest) Reset()         { *m = GetPetNotesDetailRequest{} }
func (m *GetPetNotesDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetPetNotesDetailRequest) ProtoMessage()    {}
func (*GetPetNotesDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{6}
}

func (m *GetPetNotesDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetNotesDetailRequest.Unmarshal(m, b)
}
func (m *GetPetNotesDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetNotesDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetPetNotesDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetNotesDetailRequest.Merge(m, src)
}
func (m *GetPetNotesDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetPetNotesDetailRequest.Size(m)
}
func (m *GetPetNotesDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetNotesDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetNotesDetailRequest proto.InternalMessageInfo

func (m *GetPetNotesDetailRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *GetPetNotesDetailRequest) GetUserIds() string {
	if m != nil {
		return m.UserIds
	}
	return ""
}

func (m *GetPetNotesDetailRequest) GetPetIds() string {
	if m != nil {
		return m.PetIds
	}
	return ""
}

//宠物便签——获取宠物便签详情——Response
type GetPetNotesDetailResponse struct {
	Code                 int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details              []*PetNotesModel `protobuf:"bytes,5,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPetNotesDetailResponse) Reset()         { *m = GetPetNotesDetailResponse{} }
func (m *GetPetNotesDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetNotesDetailResponse) ProtoMessage()    {}
func (*GetPetNotesDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{7}
}

func (m *GetPetNotesDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetNotesDetailResponse.Unmarshal(m, b)
}
func (m *GetPetNotesDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetNotesDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetPetNotesDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetNotesDetailResponse.Merge(m, src)
}
func (m *GetPetNotesDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetNotesDetailResponse.Size(m)
}
func (m *GetPetNotesDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetNotesDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetNotesDetailResponse proto.InternalMessageInfo

func (m *GetPetNotesDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetNotesDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetNotesDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPetNotesDetailResponse) GetDetails() []*PetNotesModel {
	if m != nil {
		return m.Details
	}
	return nil
}

//宠物便签——创建——Request
type CreatePetNotesRequest struct {
	//用户id
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//标题
	Tittle string `protobuf:"bytes,3,opt,name=tittle,proto3" json:"tittle"`
	//便签内容
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content"`
	//是否设置提醒 1 未设置 2 设置
	IsRemind int32 `protobuf:"varint,5,opt,name=is_remind,json=isRemind,proto3" json:"is_remind"`
	//提醒时间
	RemindTime string `protobuf:"bytes,6,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	//创建人id
	CreateId             string   `protobuf:"bytes,7,opt,name=create_id,json=createId,proto3" json:"create_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePetNotesRequest) Reset()         { *m = CreatePetNotesRequest{} }
func (m *CreatePetNotesRequest) String() string { return proto.CompactTextString(m) }
func (*CreatePetNotesRequest) ProtoMessage()    {}
func (*CreatePetNotesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{8}
}

func (m *CreatePetNotesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePetNotesRequest.Unmarshal(m, b)
}
func (m *CreatePetNotesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePetNotesRequest.Marshal(b, m, deterministic)
}
func (m *CreatePetNotesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePetNotesRequest.Merge(m, src)
}
func (m *CreatePetNotesRequest) XXX_Size() int {
	return xxx_messageInfo_CreatePetNotesRequest.Size(m)
}
func (m *CreatePetNotesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePetNotesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePetNotesRequest proto.InternalMessageInfo

func (m *CreatePetNotesRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreatePetNotesRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *CreatePetNotesRequest) GetTittle() string {
	if m != nil {
		return m.Tittle
	}
	return ""
}

func (m *CreatePetNotesRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CreatePetNotesRequest) GetIsRemind() int32 {
	if m != nil {
		return m.IsRemind
	}
	return 0
}

func (m *CreatePetNotesRequest) GetRemindTime() string {
	if m != nil {
		return m.RemindTime
	}
	return ""
}

func (m *CreatePetNotesRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

//宠物便签——创建——Response
type CreatePetNotesResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePetNotesResponse) Reset()         { *m = CreatePetNotesResponse{} }
func (m *CreatePetNotesResponse) String() string { return proto.CompactTextString(m) }
func (*CreatePetNotesResponse) ProtoMessage()    {}
func (*CreatePetNotesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{9}
}

func (m *CreatePetNotesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePetNotesResponse.Unmarshal(m, b)
}
func (m *CreatePetNotesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePetNotesResponse.Marshal(b, m, deterministic)
}
func (m *CreatePetNotesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePetNotesResponse.Merge(m, src)
}
func (m *CreatePetNotesResponse) XXX_Size() int {
	return xxx_messageInfo_CreatePetNotesResponse.Size(m)
}
func (m *CreatePetNotesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePetNotesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePetNotesResponse proto.InternalMessageInfo

func (m *CreatePetNotesResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreatePetNotesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreatePetNotesResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//宠物便签——更新——Request
type UpdatePetNotesRequest struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//标题
	Tittle string `protobuf:"bytes,4,opt,name=tittle,proto3" json:"tittle"`
	//便签内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	//是否设置提醒 0 设置 1 未设置
	IsRemind int32 `protobuf:"varint,6,opt,name=is_remind,json=isRemind,proto3" json:"is_remind"`
	//提醒时间
	RemindTime string `protobuf:"bytes,7,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	//更新人id
	UpdateId             string   `protobuf:"bytes,8,opt,name=update_id,json=updateId,proto3" json:"update_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePetNotesRequest) Reset()         { *m = UpdatePetNotesRequest{} }
func (m *UpdatePetNotesRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePetNotesRequest) ProtoMessage()    {}
func (*UpdatePetNotesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{10}
}

func (m *UpdatePetNotesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePetNotesRequest.Unmarshal(m, b)
}
func (m *UpdatePetNotesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePetNotesRequest.Marshal(b, m, deterministic)
}
func (m *UpdatePetNotesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePetNotesRequest.Merge(m, src)
}
func (m *UpdatePetNotesRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePetNotesRequest.Size(m)
}
func (m *UpdatePetNotesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePetNotesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePetNotesRequest proto.InternalMessageInfo

func (m *UpdatePetNotesRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdatePetNotesRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UpdatePetNotesRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *UpdatePetNotesRequest) GetTittle() string {
	if m != nil {
		return m.Tittle
	}
	return ""
}

func (m *UpdatePetNotesRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UpdatePetNotesRequest) GetIsRemind() int32 {
	if m != nil {
		return m.IsRemind
	}
	return 0
}

func (m *UpdatePetNotesRequest) GetRemindTime() string {
	if m != nil {
		return m.RemindTime
	}
	return ""
}

func (m *UpdatePetNotesRequest) GetUpdateId() string {
	if m != nil {
		return m.UpdateId
	}
	return ""
}

//宠物便签——更新——Response
type UpdatePetNotesResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePetNotesResponse) Reset()         { *m = UpdatePetNotesResponse{} }
func (m *UpdatePetNotesResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePetNotesResponse) ProtoMessage()    {}
func (*UpdatePetNotesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{11}
}

func (m *UpdatePetNotesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePetNotesResponse.Unmarshal(m, b)
}
func (m *UpdatePetNotesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePetNotesResponse.Marshal(b, m, deterministic)
}
func (m *UpdatePetNotesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePetNotesResponse.Merge(m, src)
}
func (m *UpdatePetNotesResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePetNotesResponse.Size(m)
}
func (m *UpdatePetNotesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePetNotesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePetNotesResponse proto.InternalMessageInfo

func (m *UpdatePetNotesResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdatePetNotesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpdatePetNotesResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//宠物便签——删除（软删除）——Request
type DeletePetNotesRequest struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//更新人id
	UpdateId             string   `protobuf:"bytes,2,opt,name=update_id,json=updateId,proto3" json:"update_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePetNotesRequest) Reset()         { *m = DeletePetNotesRequest{} }
func (m *DeletePetNotesRequest) String() string { return proto.CompactTextString(m) }
func (*DeletePetNotesRequest) ProtoMessage()    {}
func (*DeletePetNotesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{12}
}

func (m *DeletePetNotesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePetNotesRequest.Unmarshal(m, b)
}
func (m *DeletePetNotesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePetNotesRequest.Marshal(b, m, deterministic)
}
func (m *DeletePetNotesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePetNotesRequest.Merge(m, src)
}
func (m *DeletePetNotesRequest) XXX_Size() int {
	return xxx_messageInfo_DeletePetNotesRequest.Size(m)
}
func (m *DeletePetNotesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePetNotesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePetNotesRequest proto.InternalMessageInfo

func (m *DeletePetNotesRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeletePetNotesRequest) GetUpdateId() string {
	if m != nil {
		return m.UpdateId
	}
	return ""
}

//宠物便签——删除（软删除）——Response
type DeletePetNotesResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePetNotesResponse) Reset()         { *m = DeletePetNotesResponse{} }
func (m *DeletePetNotesResponse) String() string { return proto.CompactTextString(m) }
func (*DeletePetNotesResponse) ProtoMessage()    {}
func (*DeletePetNotesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_71022f658e79c2f5, []int{13}
}

func (m *DeletePetNotesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePetNotesResponse.Unmarshal(m, b)
}
func (m *DeletePetNotesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePetNotesResponse.Marshal(b, m, deterministic)
}
func (m *DeletePetNotesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePetNotesResponse.Merge(m, src)
}
func (m *DeletePetNotesResponse) XXX_Size() int {
	return xxx_messageInfo_DeletePetNotesResponse.Size(m)
}
func (m *DeletePetNotesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePetNotesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePetNotesResponse proto.InternalMessageInfo

func (m *DeletePetNotesResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DeletePetNotesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DeletePetNotesResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*GetNotesByUserIdRequest)(nil), "cc.GetNotesByUserIdRequest")
	proto.RegisterType((*GetNotesByUserIdResponse)(nil), "cc.GetNotesByUserIdResponse")
	proto.RegisterType((*PetNotes)(nil), "cc.PetNotes")
	proto.RegisterType((*GetPetNotesListRequest)(nil), "cc.GetPetNotesListRequest")
	proto.RegisterType((*GetPetNotesListResponse)(nil), "cc.GetPetNotesListResponse")
	proto.RegisterType((*PetNotesModel)(nil), "cc.PetNotesModel")
	proto.RegisterType((*GetPetNotesDetailRequest)(nil), "cc.GetPetNotesDetailRequest")
	proto.RegisterType((*GetPetNotesDetailResponse)(nil), "cc.GetPetNotesDetailResponse")
	proto.RegisterType((*CreatePetNotesRequest)(nil), "cc.CreatePetNotesRequest")
	proto.RegisterType((*CreatePetNotesResponse)(nil), "cc.CreatePetNotesResponse")
	proto.RegisterType((*UpdatePetNotesRequest)(nil), "cc.UpdatePetNotesRequest")
	proto.RegisterType((*UpdatePetNotesResponse)(nil), "cc.UpdatePetNotesResponse")
	proto.RegisterType((*DeletePetNotesRequest)(nil), "cc.DeletePetNotesRequest")
	proto.RegisterType((*DeletePetNotesResponse)(nil), "cc.DeletePetNotesResponse")
}

func init() { proto.RegisterFile("cc/pet_notes.proto", fileDescriptor_71022f658e79c2f5) }

var fileDescriptor_71022f658e79c2f5 = []byte{
	// 789 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x96, 0xcd, 0x6e, 0xd3, 0x4a,
	0x14, 0xc7, 0x65, 0x27, 0x4e, 0x9c, 0xd3, 0xde, 0x7e, 0x8c, 0x6e, 0x52, 0xd7, 0x69, 0xef, 0xad,
	0xbc, 0xaa, 0x74, 0xa5, 0x56, 0xea, 0x7d, 0x03, 0xa8, 0x14, 0x05, 0x51, 0x54, 0xb9, 0x74, 0x57,
	0x29, 0xa4, 0x9e, 0x43, 0x34, 0x52, 0x12, 0x1b, 0xcf, 0x84, 0x8f, 0xae, 0x78, 0x00, 0x1e, 0x82,
	0xd7, 0xe0, 0x19, 0x58, 0xc2, 0x86, 0x3d, 0x0f, 0x82, 0xe6, 0xc3, 0x8d, 0xed, 0x38, 0x8d, 0x80,
	0x2c, 0xd8, 0x79, 0xce, 0x97, 0xcf, 0xfc, 0xe7, 0x37, 0xc7, 0x06, 0x12, 0x45, 0xa7, 0x09, 0x8a,
	0xc1, 0x34, 0x16, 0xc8, 0x4f, 0x92, 0x34, 0x16, 0x31, 0xb1, 0xa3, 0xc8, 0xff, 0x67, 0x14, 0xc7,
	0xa3, 0x31, 0x9e, 0x2a, 0xcb, 0xed, 0xec, 0xe5, 0xe9, 0x9b, 0x74, 0x98, 0x24, 0x98, 0x9a, 0x18,
	0xff, 0xa0, 0xec, 0xe7, 0x22, 0x9d, 0x45, 0x42, 0x7b, 0x83, 0x33, 0xd8, 0xeb, 0xa1, 0x78, 0x26,
	0x6b, 0x3e, 0x7a, 0x77, 0xcd, 0x31, 0xed, 0xd3, 0x10, 0x5f, 0xcd, 0x90, 0x0b, 0xb2, 0x07, 0xcd,
	0x19, 0xc7, 0x74, 0xc0, 0xa8, 0x67, 0x1d, 0x59, 0xc7, 0xad, 0xb0, 0x31, 0x53, 0xfe, 0xe0, 0xbd,
	0x05, 0xde, 0x62, 0x12, 0x4f, 0xe2, 0x29, 0x47, 0x42, 0xa0, 0x1e, 0xc5, 0x14, 0x55, 0x8a, 0x13,
	0xaa, 0x67, 0xe2, 0x41, 0x73, 0x82, 0x9c, 0x0f, 0x47, 0xe8, 0xd9, 0xaa, 0x52, 0xb6, 0x24, 0x7f,
	0x83, 0x83, 0x69, 0x1a, 0xa7, 0x5e, 0x4d, 0xd9, 0xf5, 0x82, 0x1c, 0x41, 0x7d, 0xcc, 0xb8, 0xf0,
	0xea, 0x47, 0xb5, 0xe3, 0x8d, 0xb3, 0xcd, 0x93, 0x28, 0x3a, 0xb9, 0x34, 0xef, 0x0b, 0x95, 0x27,
	0xf8, 0x62, 0x83, 0x9b, 0x99, 0xc8, 0x16, 0xd8, 0xa6, 0x47, 0x27, 0xb4, 0x19, 0xcd, 0x37, 0x6e,
	0xe7, 0x1b, 0x27, 0x6d, 0x68, 0x48, 0x05, 0x19, 0xcd, 0x5e, 0x97, 0xa0, 0xe8, 0x53, 0xd9, 0x84,
	0x60, 0x62, 0x8c, 0x5e, 0x5d, 0x5b, 0xd5, 0x42, 0x36, 0x1d, 0xc5, 0x53, 0x81, 0x53, 0xe1, 0x39,
	0xba, 0x69, 0xb3, 0x24, 0x5d, 0x68, 0x31, 0x3e, 0x48, 0x71, 0xc2, 0xa6, 0xd4, 0x6b, 0xa8, 0xd7,
	0xba, 0x8c, 0x87, 0x6a, 0x4d, 0x0e, 0x01, 0x18, 0x1f, 0x50, 0x1c, 0xa3, 0x40, 0xea, 0x35, 0x95,
	0xb7, 0xc5, 0xf8, 0xb9, 0x36, 0x90, 0x7f, 0x61, 0x43, 0x27, 0x0e, 0x04, 0x9b, 0xa0, 0xe7, 0xaa,
	0xca, 0xa0, 0x4d, 0xcf, 0xd9, 0x04, 0x65, 0x40, 0x94, 0xe2, 0x50, 0xa0, 0x0e, 0x68, 0xe9, 0x00,
	0x6d, 0x52, 0x01, 0x5d, 0x68, 0x99, 0x00, 0x46, 0x3d, 0x50, 0x6e, 0x57, 0x1b, 0xfa, 0xaa, 0xfc,
	0x2c, 0xa1, 0xf7, 0xd9, 0x1b, 0x3a, 0x5b, 0x9b, 0xb2, 0x6c, 0x13, 0xc0, 0xa8, 0xb7, 0xa9, 0xb3,
	0xb5, 0xa1, 0x4f, 0x83, 0xcf, 0x16, 0x74, 0x7a, 0x28, 0x32, 0x61, 0x9f, 0x32, 0x2e, 0x32, 0x18,
	0x0e, 0x01, 0x92, 0xe1, 0x08, 0x07, 0x6c, 0x4a, 0xf1, 0xad, 0xd1, 0xba, 0x25, 0x2d, 0x7d, 0x69,
	0x90, 0x65, 0x95, 0x9b, 0xb3, 0x3b, 0x7d, 0xc6, 0x4e, 0xe8, 0x4a, 0xc3, 0x15, 0xbb, 0xc3, 0xfc,
	0x79, 0xd4, 0x96, 0x9c, 0x47, 0x3d, 0x7f, 0x1e, 0xbf, 0xa8, 0xbc, 0xc6, 0xa0, 0x99, 0x61, 0x10,
	0x7c, 0xb4, 0x14, 0xdb, 0xc5, 0xdd, 0xac, 0x91, 0x52, 0x89, 0x4d, 0x2c, 0x86, 0x63, 0xd5, 0xbc,
	0x13, 0xea, 0x05, 0xf9, 0x0f, 0x9a, 0x14, 0xc5, 0x90, 0x8d, 0xb9, 0xe7, 0x28, 0x7c, 0x77, 0xf3,
	0xf8, 0x5e, 0xc4, 0x14, 0xc7, 0x61, 0x16, 0x11, 0x7c, 0xb2, 0xe1, 0xaf, 0x82, 0xeb, 0xcf, 0x62,
	0xb9, 0x04, 0x6b, 0x73, 0x01, 0xd6, 0x02, 0x8b, 0xee, 0x22, 0x8b, 0x2b, 0x49, 0x9e, 0xb3, 0x08,
	0x45, 0x16, 0x57, 0x92, 0x1c, 0xbc, 0x50, 0x43, 0x28, 0x53, 0xef, 0x5c, 0x29, 0x9a, 0xd1, 0xba,
	0x03, 0x35, 0x46, 0xb9, 0x19, 0x5b, 0xf2, 0x91, 0xec, 0x83, 0x6b, 0x74, 0xe4, 0xd9, 0xe9, 0x6a,
	0x21, 0xb9, 0x94, 0x58, 0x2b, 0xc9, 0x33, 0x3c, 0x95, 0x94, 0x3c, 0xf8, 0x60, 0xc1, 0x7e, 0xc5,
	0x2b, 0xd6, 0x88, 0xd0, 0x4f, 0xc1, 0xf2, 0xd5, 0x82, 0xf6, 0x63, 0xa5, 0xde, 0xfd, 0x30, 0x5c,
	0x31, 0xa9, 0x73, 0x90, 0xd8, 0x79, 0x48, 0x3a, 0xd0, 0x10, 0x4c, 0x48, 0x4a, 0xcc, 0x86, 0xf5,
	0x2a, 0x8f, 0x49, 0xfd, 0x01, 0x4c, 0x9c, 0x87, 0x31, 0x69, 0x3c, 0x8c, 0x49, 0xb3, 0x88, 0x49,
	0x70, 0x03, 0x9d, 0xf2, 0xae, 0xd6, 0xa7, 0x70, 0xf0, 0xdd, 0x82, 0xf6, 0xb5, 0x82, 0xa6, 0x2c,
	0xda, 0xef, 0xde, 0xb4, 0xb9, 0x88, 0xf5, 0x65, 0x22, 0xae, 0xfb, 0xae, 0xcd, 0x6f, 0x8b, 0x5b,
	0x9a, 0xdc, 0x37, 0xd0, 0x29, 0xef, 0x72, 0x8d, 0x22, 0x9e, 0x43, 0x5b, 0x7f, 0xbf, 0x56, 0x69,
	0x58, 0xe8, 0xd1, 0x5e, 0xec, 0xb1, 0x5c, 0x65, 0x7d, 0x3d, 0x9e, 0x7d, 0xab, 0xc1, 0x76, 0x56,
	0xf8, 0x0a, 0xd3, 0xd7, 0x2c, 0x42, 0x72, 0x01, 0x3b, 0xe5, 0xff, 0x14, 0xd2, 0x95, 0x37, 0x6c,
	0xc9, 0x2f, 0x8f, 0x7f, 0x50, 0xed, 0x34, 0x6d, 0x3e, 0x81, 0xed, 0xd2, 0xf7, 0x84, 0xf8, 0x26,
	0xa1, 0xe2, 0x93, 0xe9, 0x77, 0x2b, 0x7d, 0xa6, 0xd6, 0x25, 0xec, 0x2e, 0x8c, 0x16, 0x72, 0x50,
	0xca, 0x28, 0x0c, 0x35, 0xff, 0x70, 0x89, 0xd7, 0x54, 0xec, 0xc1, 0x56, 0xf1, 0x1e, 0x91, 0x7d,
	0x99, 0x50, 0x39, 0x31, 0x7c, 0xbf, 0xca, 0x35, 0x2f, 0x54, 0x64, 0x49, 0x17, 0xaa, 0xbc, 0x45,
	0xba, 0xd0, 0x12, 0xf4, 0x7a, 0xb0, 0x55, 0x3c, 0x70, 0x5d, 0xa8, 0x12, 0x25, 0x5d, 0xa8, 0x9a,
	0x8f, 0xdb, 0x86, 0xfa, 0x57, 0xfd, 0xff, 0x47, 0x00, 0x00, 0x00, 0xff, 0xff, 0x91, 0xa5, 0x12,
	0x92, 0x03, 0x0b, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PetNotesServiceClient is the client API for PetNotesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PetNotesServiceClient interface {
	GetNotesByUserId(ctx context.Context, in *GetNotesByUserIdRequest, opts ...grpc.CallOption) (*GetNotesByUserIdResponse, error)
	//宠物便签——分页获取宠物便签列表
	GetPetNotesList(ctx context.Context, in *GetPetNotesListRequest, opts ...grpc.CallOption) (*GetPetNotesListResponse, error)
	//宠物便签——获取宠物便签详情
	GetPetNotesDetail(ctx context.Context, in *GetPetNotesDetailRequest, opts ...grpc.CallOption) (*GetPetNotesDetailResponse, error)
	//宠物便签——创建
	CreatePetNotes(ctx context.Context, in *CreatePetNotesRequest, opts ...grpc.CallOption) (*CreatePetNotesResponse, error)
	//宠物便签——更新
	UpdatePetNotes(ctx context.Context, in *UpdatePetNotesRequest, opts ...grpc.CallOption) (*UpdatePetNotesResponse, error)
	//宠物便签——删除（软删除）
	DeletePetNotes(ctx context.Context, in *DeletePetNotesRequest, opts ...grpc.CallOption) (*DeletePetNotesResponse, error)
}

type petNotesServiceClient struct {
	cc *grpc.ClientConn
}

func NewPetNotesServiceClient(cc *grpc.ClientConn) PetNotesServiceClient {
	return &petNotesServiceClient{cc}
}

func (c *petNotesServiceClient) GetNotesByUserId(ctx context.Context, in *GetNotesByUserIdRequest, opts ...grpc.CallOption) (*GetNotesByUserIdResponse, error) {
	out := new(GetNotesByUserIdResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/GetNotesByUserId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petNotesServiceClient) GetPetNotesList(ctx context.Context, in *GetPetNotesListRequest, opts ...grpc.CallOption) (*GetPetNotesListResponse, error) {
	out := new(GetPetNotesListResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/GetPetNotesList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petNotesServiceClient) GetPetNotesDetail(ctx context.Context, in *GetPetNotesDetailRequest, opts ...grpc.CallOption) (*GetPetNotesDetailResponse, error) {
	out := new(GetPetNotesDetailResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/GetPetNotesDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petNotesServiceClient) CreatePetNotes(ctx context.Context, in *CreatePetNotesRequest, opts ...grpc.CallOption) (*CreatePetNotesResponse, error) {
	out := new(CreatePetNotesResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/CreatePetNotes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petNotesServiceClient) UpdatePetNotes(ctx context.Context, in *UpdatePetNotesRequest, opts ...grpc.CallOption) (*UpdatePetNotesResponse, error) {
	out := new(UpdatePetNotesResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/UpdatePetNotes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petNotesServiceClient) DeletePetNotes(ctx context.Context, in *DeletePetNotesRequest, opts ...grpc.CallOption) (*DeletePetNotesResponse, error) {
	out := new(DeletePetNotesResponse)
	err := c.cc.Invoke(ctx, "/cc.PetNotesService/DeletePetNotes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetNotesServiceServer is the server API for PetNotesService service.
type PetNotesServiceServer interface {
	GetNotesByUserId(context.Context, *GetNotesByUserIdRequest) (*GetNotesByUserIdResponse, error)
	//宠物便签——分页获取宠物便签列表
	GetPetNotesList(context.Context, *GetPetNotesListRequest) (*GetPetNotesListResponse, error)
	//宠物便签——获取宠物便签详情
	GetPetNotesDetail(context.Context, *GetPetNotesDetailRequest) (*GetPetNotesDetailResponse, error)
	//宠物便签——创建
	CreatePetNotes(context.Context, *CreatePetNotesRequest) (*CreatePetNotesResponse, error)
	//宠物便签——更新
	UpdatePetNotes(context.Context, *UpdatePetNotesRequest) (*UpdatePetNotesResponse, error)
	//宠物便签——删除（软删除）
	DeletePetNotes(context.Context, *DeletePetNotesRequest) (*DeletePetNotesResponse, error)
}

// UnimplementedPetNotesServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPetNotesServiceServer struct {
}

func (*UnimplementedPetNotesServiceServer) GetNotesByUserId(ctx context.Context, req *GetNotesByUserIdRequest) (*GetNotesByUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotesByUserId not implemented")
}
func (*UnimplementedPetNotesServiceServer) GetPetNotesList(ctx context.Context, req *GetPetNotesListRequest) (*GetPetNotesListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetNotesList not implemented")
}
func (*UnimplementedPetNotesServiceServer) GetPetNotesDetail(ctx context.Context, req *GetPetNotesDetailRequest) (*GetPetNotesDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetNotesDetail not implemented")
}
func (*UnimplementedPetNotesServiceServer) CreatePetNotes(ctx context.Context, req *CreatePetNotesRequest) (*CreatePetNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetNotes not implemented")
}
func (*UnimplementedPetNotesServiceServer) UpdatePetNotes(ctx context.Context, req *UpdatePetNotesRequest) (*UpdatePetNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetNotes not implemented")
}
func (*UnimplementedPetNotesServiceServer) DeletePetNotes(ctx context.Context, req *DeletePetNotesRequest) (*DeletePetNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetNotes not implemented")
}

func RegisterPetNotesServiceServer(s *grpc.Server, srv PetNotesServiceServer) {
	s.RegisterService(&_PetNotesService_serviceDesc, srv)
}

func _PetNotesService_GetNotesByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotesByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).GetNotesByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/GetNotesByUserId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).GetNotesByUserId(ctx, req.(*GetNotesByUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetNotesService_GetPetNotesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetNotesListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).GetPetNotesList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/GetPetNotesList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).GetPetNotesList(ctx, req.(*GetPetNotesListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetNotesService_GetPetNotesDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetNotesDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).GetPetNotesDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/GetPetNotesDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).GetPetNotesDetail(ctx, req.(*GetPetNotesDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetNotesService_CreatePetNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).CreatePetNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/CreatePetNotes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).CreatePetNotes(ctx, req.(*CreatePetNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetNotesService_UpdatePetNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).UpdatePetNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/UpdatePetNotes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).UpdatePetNotes(ctx, req.(*UpdatePetNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetNotesService_DeletePetNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetNotesServiceServer).DeletePetNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.PetNotesService/DeletePetNotes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetNotesServiceServer).DeletePetNotes(ctx, req.(*DeletePetNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PetNotesService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.PetNotesService",
	HandlerType: (*PetNotesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNotesByUserId",
			Handler:    _PetNotesService_GetNotesByUserId_Handler,
		},
		{
			MethodName: "GetPetNotesList",
			Handler:    _PetNotesService_GetPetNotesList_Handler,
		},
		{
			MethodName: "GetPetNotesDetail",
			Handler:    _PetNotesService_GetPetNotesDetail_Handler,
		},
		{
			MethodName: "CreatePetNotes",
			Handler:    _PetNotesService_CreatePetNotes_Handler,
		},
		{
			MethodName: "UpdatePetNotes",
			Handler:    _PetNotesService_UpdatePetNotes_Handler,
		},
		{
			MethodName: "DeletePetNotes",
			Handler:    _PetNotesService_DeletePetNotes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/pet_notes.proto",
}

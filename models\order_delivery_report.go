package models

import "time"

type OrderDeliveryReport struct {
	Id               int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	OrderSn          string    `json:"order_sn" xorm:"not null comment('子订单号') VARCHAR(50) 'order_sn'"`
	OldOrderSn       string    `json:"old_order_sn" xorm:"not null comment('第三方订单号') VARCHAR(50) 'old_order_sn'"`
	ParentOrderSn    string    `json:"parent_order_sn" xorm:"default 'null' comment('父订单号') VARCHAR(50) 'parent_order_sn'"`
	Distance         int       `json:"distance" xorm:"not null default 0 comment('配送距离，单位（米）') INT 'distance'"`
	LastDelivery     string    `json:"last_delivery" xorm:"not null comment('最终承运名称') VARCHAR(50) 'last_delivery'"`
	LastDeliveryType int       `json:"last_delivery_type" xorm:"not null comment('最终承运类型') INT 'last_delivery_type'"`
	CreateTime       time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index DATETIME created"`
	UpdateTime       time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
}

type OrderDeliveryReportPrice struct {
	Id            int    `json:"id" xorm:"pk autoincr not null INT 'id'"`
	DeliveryType  int    `json:"delivery_type" xorm:"default 'null' comment('配送类型') INT 'delivery_type'"`
	DeliveryPrice string `json:"delivery_price" xorm:"default 'null' comment('配送报价') VARCHAR(50) 'delivery_price'"`
	OrderSn       string `json:"order_sn" xorm:"default 'null' comment('子订单号') VARCHAR(50) 'order_sn'"`
}

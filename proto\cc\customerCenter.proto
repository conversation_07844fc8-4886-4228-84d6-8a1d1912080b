syntax = "proto3";

package cc;

// 通用基础服务
service customerCenterService {
  // 查询标签信息
  rpc QueryTags(tagsQueryRequest) returns(tagsQueryResponse);
  // 查询宠物标签信息
  rpc QueryPetTag(petTagQueryRequest) returns(petTagQueryResponse);
  // 根据宠物ID生成标签
  rpc GeneratePetTag(petTagGenerateRequest) returns(baseResponse);
  // 查询用户的推荐商品--常购记录
  rpc QueryUsuallyRecordProduct(recommendProductQueryRequest) returns(QueryUsuallyRecordProductResponse);

  // 查询用户的推荐商品--常购记录  -- 手动执行任务接口
  rpc ManualQueryUsuallyRecordProduct(ManualQueryUsuallyRecordProductRequest) returns(ManualQueryUsuallyRecordProductResponse);
  //添加收件地址
  rpc AddAddress (AddressInfoRequest) returns (ResResponse);
  //更新收件地址
  rpc UpdateAddress (AddressInfoRequest) returns (ResResponse);
  //根据ID获取收件地址
  rpc GetAddressInfo (GetAddressInfoRequest) returns (GetAddressInfoResponse);
  //删除收件地址
  rpc DelAddress (GetAddressInfoRequest) returns (ResResponse);
  //获取收货收件地址列表
  rpc GetAddressList (GetAddressListRequest) returns (GetAddressListResponse);
  //上传用户请求日志，C端埋点用
  rpc UploadUserRequestLog (UserRequestLogRequest) returns (UserRequestLogResonse);
  // 标签操作，增删改查
  rpc TagOperate(TagOperateRequest) returns(TagOperateResponse);
  // 内容标签查询
  rpc QueryContentTag(ContentTagRequest) returns(ContentTagResponse);

  //会员等级列表
  rpc UserLevelList (UserLevelListReq) returns (UserLevelListRes);
  //会员等级启用停用
  rpc UserLevelSet (UserLevelSetReq) returns (Response);
  //会员权益可编辑项
  rpc UserEditEquityList(EmptyReq) returns (UserEditEquityListRes);
  //会员等级编辑
  rpc UserLevelEdit (UserLevelEditReq) returns (Response);
  //会员等级关联的权益
  rpc UserLevelDetail(UserLevelDetailReq) returns (UserLevelDetailRes);
  //更新会员等级
  rpc RefreshUserLevel(EmptyReq) returns (Response);
  //所有会员等级对应的权益列表
  rpc AllUserLevelEquityList(EmptyReq) returns (AllUserLevelEquityListRes);
  //加减用户健康值
  rpc AddUserHealthVal(AddUserHealthValReq) returns (Response);
  //开启、关闭微信消息通知
  rpc SetNotificationState(SetNotificationStateReq) returns (Response);
  //加减微信消息订阅数
  rpc AddNotificationMessage(AddNotificationMessageReq) returns (Response);
  //用户是否可以发送订阅消息
  rpc CanSendWechatSubscribeMessage(CanSendWechatSubscribeMessageReq) returns (Response);
  //用户订阅开关状态
  rpc UserNotification(UserNotificationReq) returns (UserNotificationRes);
  //发送微信订阅消息
  rpc SendSubscribeMessage(SendSubscribeMessageReq) returns (Response);

  //任务列表
  rpc TaskList (TaskListReq) returns (TaskListRes);
  //任务保存
  rpc TaskSave (TaskSaveReq) returns (BaseResponseNew);
  //会员权益列表
  rpc MemberEquityList (MemberEquityListReq) returns (MemberEquityListRes);
  //会员权益详情
  rpc MemberEquityDetail (MemberEquityDetailReq) returns (MemberEquityDetailRes);
  //会员权益编辑
  rpc MemberEquityEdit (MemberEquityEditReq) returns (BaseResponseNew);
  // 会员等级权益
  rpc UserLevelEquities (UserLevelEquitiesReq) returns (UserLevelEquitiesRes);
  //会员权益显示或隐藏
  rpc MemberEquitySet(MemberEquitySetReq) returns(BaseResponseNew);

  //会员体系-任务中心
  rpc MemberTaskList(MemberTaskListReq) returns(MemberTaskListRes);
  //个人中心-健康值
  rpc MemberHealthVal(MemberHealthValReq) returns(MemberHealthValRes);
  //任务中心-健康值明细
  rpc MemberHealthDetail(MemberHealthDetailReq) returns(MemberHealthDetailRes);
  //权益详情-特权列表
  rpc UserEquity(UserEquityReq) returns(UserEquityRes);
  //权益详情-领券
  rpc EquityGetcoupon(EquityGetcouponReq) returns(BaseResponseNew);
  //完成任务
  rpc TaskFinish(TaskFinishReq) returns(BaseResponseNew);
  //会员中心主页
  rpc EquityIndex(EquityIndexReq) returns(EquityIndexRes);
  //获取会员折扣，没有配置当前会员折扣则取有折扣的最高等级的折扣
  rpc MemberProductDiscount(MemberProductDiscountRequest) returns(MemberProductDiscountResponse);

  //获取订阅通知详情
  rpc MessageInfo(MessageInfoRequest) returns(MessageInfoResponse);
  //获取用户发券通知
  rpc MemberCouponsMessage(MessageInfoRequest) returns(MemberCouponsMessageResponse);
  //获取最新消息
  rpc MemberNewMessage(MemberNewMessageRequest) returns(MemberNewMessageResponse);
  //用户信息更新
  rpc InfoUpdate(InfoUpdateRequest) returns(BaseResponseNew);
  //宠物信息更新
  rpc PetInfoUpdate(PetInfoUpdateRequest) returns(BaseResponseNew);
  //宠物信息添加
  rpc PetInfoAdd(PetInfoUpdateRequest) returns(PetInfoUpdateResponse);
}

message EmptyReq {

}

message Response {
  int64 code = 1;
  string message = 2;
}

message BaseResponseNew {
  string msg = 1;
}

message EquityGetcouponReq{
  //券ID
  int32 coupon_id = 1;
  //券类型 1门店券 2优惠券(商城券)
  int32 coupon_type = 2;
  //用户ID（前端不用传）
  string scrm_userid = 3;
}

message MemberEquityDetailReq {
  int32 id = 1;
}

message MemberEquitySetReq {
  //权益ID
  int32 id = 1;
  //操作 1显示 0隐藏
  int32 is_display = 2;
}

message MemberEquityDetailRes {
  string msg = 1;
  int32 id = 2;
  //权益名称
  string equity_name = 3;
  //权益图标
  string icon = 4;
  //权益简介
  string equity_info = 5;
  //规则
  string equity_rules = 6;
  //1显示  0隐藏
  int32 is_display = 7;
  //是否有赠券 1是 0否
  int32 is_voucher = 8;
}

message EquityIndexReq {
  //用户ID，前端不用传
  string scrm_userid = 1;
}

message EquityIndexRes {
  string msg = 1;
  //用户当前处于什么级别
  int32 level_id = 2;
  //用户当前级别会员过期时间
  int32 user_level_etime = 3;
  //当前健康值
  int32 health_val = 4;
  //冻结健康值
  int32 freeze_health_val = 5;
  //通知状态，1开启，0关闭
  int32 notification_state = 6;
  //各等级详情
  repeated EquityIndex data = 7;
}

message EquityIndex{
  //等级ID
  int32 level_id = 1;
  //等级名称
  string level_name = 2;
  //当前级别需要的健康值
  int32 health_val = 3;
  //等级背景
  string background = 4;
  // 个人中心背景
  string center_background = 6;
  //当前享受的权益
  repeated EquityDataList equity_list = 5;
  //等级图标
  string level_icon =7;
}

message EquityDataList {
  //权益名称
  string equity_name = 1;
  //图标
  string icon = 2;
  //是否点亮 1是 0否
  int32 is_select = 3;
  //权益ID
  int32 id = 4;
}

message TaskFinishReq {
  //任务ID
  int32 task_id = 1;
  //操作类型 1去完成 2去领取
  int32 type = 2;
  //userid, 前端不用传
  string scrm_userid = 3;
}

message UserEquityReq {
  //userid,前端不用传
  string scrm_userid = 1;
}

message UserEquityRes {
  string msg = 1;
  repeated UserEquityData data = 2;
}

message UserEquityData {
  int32 id = 1;
  //权益名称
  string equity_name = 2;
  //权益简介
  string equity_info = 3;
  //权益等级
  string equity_level = 4;
  //权益规则
  string equity_rules = 5;
  //是否有赠券 1是 0否
  int32 is_voucher = 6;
  //图标
  string icon = 7;
  //商城券列表
  repeated EquityCoupon coupon_list = 8;
  //门店券列表
  repeated EquityCoupon store_vouchers = 9;
}

message EquityCoupon{
  //券ID
  string coupon_id = 1;
  //券名称
  string coupon_name = 2;
  //券状态 1未领取 2已领取 3已失效 4已过期 5已抢光
  int32 status = 3;
  //券金额（分）
  int32 voucher_t_price = 4;
  //代金券使用时的订单限额(元)
  float voucher_t_limit = 5;
  //适用范围 1全部 其他的是部分
  int32 applicable_scope = 6;
  //代金券模版有效期开始时间
  string voucher_start_date_text = 7;
  //代金券模版有效期结束时间
  string voucher_end_date_text = 8;
  //多少天内可用
  int64 voucher_days = 9;
  //有效期类型，type=2表示领取后after_day天，有效期period_validity天，type=1表示begin_time到end_time
  int32 type =10;
}

message MemberHealthDetailReq {
  //类别 0全部 1=收入 2=支出 3冻结
  int32 type = 1;
  int32 page_size = 2;
  int32 page_index = 3;
  //scrm_userid,前端不用传
  string scrm_userid = 4;
  int32 org_id=5;
}

message MemberHealthDetailRes {
  string msg = 1;
  int32 total = 2;
  repeated MemberHealthDetailData data = 3;
}

message MemberHealthDetailData {
  //类别 1=收入 2=支出 3冻结
  int32 type = 1;
  //标题
  string title = 2;
  //支付金额
  float pay_amount = 3;
  //退款金额
  float refund_amount = 4;
  //订单号
  string order_sn = 5;
  //店铺名称
  string shop_name = 6;
  //明细
  string content = 7;
  //健康值
  int32 health_val = 8;
  //健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效
  int32 health_type = 9;
  //生效时间
  string effect_time = 10;
  //获取时间
  string create_time = 11;
  //支付时间
  string pay_time = 12;
}

message MemberHealthValReq {
  //用户ID，前端不用传
  string scrm_user_id = 1;
}

message MemberHealthValRes {
  //当前健康值
  int32 health_val = 1;
  //还差多少值可升级
  int32 gap_val = 2;
  string msg = 3;
  //当前级别
  int32 current_level = 4;
  //下一等级的门槛值
  int32 next_level_val = 5;
  //当前等级的门槛值
  int32 current_level_val = 6;
  //当前级别名称
  string current_level_name = 7;
}

message MemberTaskListReq {
  //用户ID，前端不用传
  string scrm_userid = 1;
}

message MemberTaskListRes {
  string msg = 1;
  MemberTaskListData data = 2;
}

message MemberTaskListData{
  //当前健康值
  int32 health_val = 1;
  //当前级别
  int32 level_id = 2;
  repeated MemberTaskList task_list = 3;
  repeated LevelList level_list = 4;
}

message LevelList{
  //等级ID
  int32 level_id = 1;
  //等级名称
  string level_name = 2;
  //健康值
  int32 health_val = 3;
}

message MemberTaskList {
  //任务ID
  int32 task_id = 1;
  //任务名称
  string task_name = 2;
  //任务值
  int32 task_val = 3;
  //任务状态 0待完成 1待领取 2已领取
  int32 status = 4;
  //图标
  string icon = 5;
}

message UserLevelEquitiesReq {
  // 会员等级id
  int64 level_id = 1;
}

message UserEquity{
  int64 id = 1;
  // 权益名称
  string equity_name = 2;
  // 权益icon
  string icon = 3;
  // 权益简介
  string equity_info = 4;
}

message UserLevelEquitiesRes {
  // 错误信息
  string msg = 1;
  // 会员权益列表
  repeated UserEquity list = 2;
}

message MemberEquityEditReq {
  int32 id = 1;
  //名称
  string equity_name = 2;
  //图标，传JSON，例：[{"level_id":1, "icon":"https://www.aliyun.com/sss"}, {"level_id":2, "icon":"https://www.aliyun.com/ccc"}]
  string equity_icon = 3;
  //权益简介
  string equity_info = 4;
  //详细规则
  string equity_rules = 5;
  //1显示 0隐藏
  int32 is_display = 6;
  //是否有赠券 1是 0否
  int32 is_voucher = 7;
}

message MemberEquityListReq{
  //名称
  string equity_name = 1;
  int32 page_index = 2;
  int32 page_size = 3;
  //1显示 2隐藏 0全部
  int32 is_display = 4;
}

message MemberEquityListRes {
  string msg = 1;
  int32 total = 2;
  repeated MemberEquityListData data = 3;
}

message MemberEquityListData{
  int32 id = 1;
  //名称
  string equity_name = 2;
  //图标
  string icon = 3;
  //简介
  string equity_info = 4;
  //1显示  0隐藏
  int32 is_display = 5;
}

message TaskSaveReq {
  repeated TaskList list = 1;
}

message TaskListReq {

}

message TaskListRes {
  string msg = 1;
  repeated TaskList data = 2;
}

message TaskList {
  int32 id = 1;
  //任务名称
  string task_name = 2;
  //任务值
  int32 task_val = 3;
  //是否勾选 1是 0否
  int32 is_select = 4;
}

message UserEditEquity {
  int64 id = 1;
  int64 equity_type = 2;
  string equity_name = 3;
  int64 is_voucher = 4;
}

message UserEditEquityListRes {
  int64 code = 1;
  string message = 2;
  // 可编辑权益项
  repeated UserEditEquity list = 3;
}

message UserLevelEditReq {
  int64 id = 1;
  //名称
  string level_name = 2;
  //背景
  string background = 3;
  //获利等级条件
  int64 health_val = 4;
  //权益id
  string privilege_ids = 5;
  //商城升级券，多个,分割
  string goods_upgrade_vouchers = 6;
  //会员折扣，1.0~9.9
  string member_price = 8;
  //到店礼券升级券，子龙那边的券，多个,分割
  string store_upgrade_vouchers = 9;
  //到店礼券周特权券，子龙那边的券，多个,分割
  string store_week_vouchers = 10;
  // 个人中心背景
  string center_background = 11;
  //图标
  string level_icon = 12;
}

message UserLevelDetailReq {
  //会员等级id
  int64 user_level_id = 1;
}

message LevelDetailEquity{
  int64 id = 1;
  int64 equity_type = 2;
  string equity_name = 3;
}

message UserLevelDetailRes {
  int64 code = 1;
  string message = 2;
  int64 level_id = 3;
  string level_name = 4;
  string background = 5;
  // 个人中心背景
  string center_background = 14;
  int64 health_val = 6;
  int64 level_status = 7;
  string privilege_ids = 8;
  string goods_upgrade_vouchers = 9;
  string member_price = 10;
  string store_upgrade_vouchers = 11;
  string store_week_vouchers = 12;
  repeated LevelDetailEquity equity_list = 13;
  //等级图标
  string level_icon = 15;
}

message UserLevelEquityList {
  int64 level_id = 1;
  repeated UserEquity equity_list = 2;
}

message AllUserLevelEquityListRes{
  int64 code = 1;
  string message = 2;
  //会员权益
  repeated UserLevelEquityList level_equity_list = 3;
}

message AddUserHealthValReq {
  // 用户scrm_user_id
  string user_id = 1;
  // 健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效 5退款
  int64 health_type = 2;
  // 默认0,1=收入，2=支出 3=冻结
  int64 type = 3;
  // 明细标题
  string title = 4;
  // 明细说明
  string content = 5;
  // 订单编号
  string order_sn = 6;
  // 支付金额
  string pay_amount = 7;
  // 退款金额
  string refund_amount = 8;
  // 门店财务编码
  string shop_id = 9;
  // 门店名称
  string shop_name = 10;
  // 生效时间
  string effect_time = 11;
  // 支付时间
  string pay_time = 12;
}

message NotificationState {
  // 订阅类型：all 所有通知总开关、user_level 用户会员等级通知、integral 积分通知、voucher 优惠券通知
  string notification_type = 1;
  // 是否开启，1开启，0关闭
  int64 state = 2;
}

message SetNotificationStateReq {
  //用户id
  string scrm_user_id = 1;
  // all 所有通知总开关，1开启，2关闭
  int64 all = 2;
  // user_level 用户会员等级通知，1开启，2关闭
  int64 user_level = 3;
  // integral 积分通知，1开启，2关闭
  int64 integral = 4;
  // voucher 优惠券通知，1开启，2关闭
  int64 voucher = 5;
  //register 预约挂号通知，1开启，2关闭
  int64 register = 7;
  // queuing 医院挂号排队通知，1开启，2关闭
  int64 queuing = 6;
  // 核销码使用
  int64 vr_code_use = 9;
  // 核销码过期提醒
  int64 vr_code_expire = 10;
  // 会员权益过期通知
  int64 vip_card_expire =11;
  //小程序主体：1：默认，2-极宠家
  int32 org_id = 12;
}

message NotificationMessage {
   //微信订阅消息模板
   string template_id = 2;
   //订阅类型，1订阅，0取消订阅
   int64 type = 3;
}

message AddNotificationMessageReq {
  string scrm_user_id = 1;
  //订阅类型，数组
  repeated NotificationMessage messages = 2;
}

message CanSendWechatSubscribeMessageReq {
  //用户id
  string scrm_user_id = 1;
  //订阅类型，user_level会员等级、integral积分、voucher优惠券
  string subscribe_type = 2;
  //微信消息模板id
  string template_id = 3;
}

message UserNotificationReq {
  //用户id
  string scrm_user_id = 1;
  //小程序主体：1：默认，2-极宠家
  int32 org_id = 2;
}

message UserNotificationRes {
  int64 code = 1;
  string message = 2;
  //全部订阅通知开关，1开启 !=1关闭
  int64 all = 3;
  //会员等级通知
  int64 user_level = 4;
  //用户积分通知
  int64 integral = 5;
  //优惠券通知
  int64 voucher = 6;
  //预约挂号通知
  int64 register = 7;
  //医院挂号排队通知
  int64 queuing = 8;
  //子龙美容服务通知，1开启、2关闭
  int64 service  = 9;
  // 核销码使用
  int64 vr_code_use = 10;
  // 核销码过期提醒
  int64 vr_code_expire = 11;
  //会员权益过期通知
  int64  vip_card_expire =12;
}

message MessageValue {
  //消息参数数据类型，int、string、float
  string type = 1;
  string value = 2;
}

message SendSubscribeMessageReq {
  //用户id
  string scrm_user_id = 1;
  //订阅类型：user_level会员等级通知、integral积分通知、voucher优惠券通知、queuing医院挂号排队通知
  string subscribe_type = 2;
  //微信消息key：register-queuing预约队列
  string template_key = 3;
  //微信订阅消息参数值
  repeated MessageValue values = 4;
  //微信页面跳转参数
  string page_params = 5;
  // 指定页面
  string page = 6;
  //小程序主体：1：默认，2-极宠家
  int32 org_id = 7;
}

message UserLevelSetReq {
  int64 id = 1;
  //状态 0停用 1启用
  int64 status = 2;
}

message UserLevelListReq {
  int64 page_index = 1;
  int64 page_size = 2;
}

message UserLevelListRes {
  int64 code = 1;
  string message = 2;
  int64 total = 3;
  //会员等级状态，0无状态，可以编辑会员等级、1更新了会员规则、2会员等级更新中
  int64 level_state = 4;
  //会员等级列表
  repeated UserLevelList list = 5;
}

message UserLevelList {
  int64 id = 1;
  //会员等级
  int64 level_id = 2;
  //名称
  string level_name = 3;
  //获利等级条件
  int64 health_val = 4;
  //卡面
  string background = 5;
  //状态 0停用 1启用
  int64 level_status = 6;
  //等级图标
  string level_icon = 7;
}

message UserRequestLogRequest{
  string to = 1;
  string msg = 2;
}
message UserRequestLogResonse{
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message ManualQueryUsuallyRecordProductRequest{
  //开始时间
  string start_time = 1;
  //结束时间
  string end_time = 2;
}


message ManualQueryUsuallyRecordProductResponse{
  int32 code = 1;
  string message = 2;
}

message AddressInfoRequest {
  //姓名
  string true_name = 1;
  //城市ID
  string city_id = 2;
  //区域ID
  string area_id = 3;
  //区域信息
  string area_info = 4;
  //地址
  string address = 5;
  //地图地址说明
  string address_desc = 6;
  //电话
  string tel_phone = 7;
  //手机
  string mob_phone = 8;
  //是否默认地址  1是  0否
  string is_default = 9;
  //chain_id
  string chain_id = 10;
  //adcode
  string adcode = 11;
  //tx_lat
  float tx_lat = 12;
  //tx_lng
  float tx_lng = 13;
  //house_info
  string house_info = 14;
  //地址ID
  string address_id = 15;
  //自提点ID
  string dlyp_id = 16;
  //城市编码
  string area_adcode = 17;
  //地图地址
  string map_address = 18;
  //ScrmuserId(无需前端传)
  string scrm_user_id = 19;
  //主体id
  int32 OrgId = 20;
}

message GetAddressInfoRequest {
  int32 address_id = 1;
}

message GetAddressInfoResponse {
  int32 code = 1;
  string message = 2;
  AddressInfoRequest data = 3;
}

message GetAddressListResponse {
  int32 code = 1;
  string message = 2;
  repeated AddressList data = 3;
}

message AddressList {
  string address_id = 1;
  string member_id = 2;
  string true_name = 3;
  string area_id = 4;
  string city_id = 5;
  string area_info = 6;
  string address = 7;
  string tel_phone = 8;
  string mob_phone = 9;
  string is_default = 10;
  string dlyp_id = 11;
  string area_lat = 12;
  string area_lng = 13;
  string area_txlat = 14;
  string area_txlng = 15;
  string isdeal = 16;
  string address_desc = 17;
  string house_info = 18;
  string area_adcode = 19;
  string map_address = 20;
}

message GetAddressListRequest {
  string scrm_user_id = 1;
  int32 org_id = 2;
}

message baseResponse {
  int32 code = 1;
  string message = 2;
}

message ResResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message tagsDto {
  // 名称
  string name = 1;
  // 值,逗号分割
  string value = 2;
  // 排序
  int32 sort = 3;
  // 是否必填
  int32 hasAsterisk = 4;
  // 对于选择数据，提供一个获取数据的url
  string dataUrl = 5;
}

// 数据请求
message tagsQueryRequest {
  // 分组
  int32 groups = 1;
  // 请求来源，content-内容中心
  string from = 2;
}

// 数据响应
message tagsQueryResponse {
  int32 code = 1;
  string message = 2;
  // 标签列表
  repeated tagsDto data = 3;
}

// 推荐商品查询
message recommendProductQueryRequest {
  // 页索引
  int32 pageIndex = 1;
  // 页大小
  int32 pageSize = 2;
  // 用户Id
  string userId = 3;
  // 宠物id
  string petId = 4;
}
// 推荐商品模型
message recommendProductDto {
  // sku
  string skuId = 1;
}
// 推荐商品数据响应
message recommendProductQueryResponse {
  int32 code = 1;
  string message = 2;
  // 总条数
  int32 total = 3;
  // 标签列表
  repeated recommendProductDto data = 4;
  repeated int32 sku_id = 5;

}

// 宠物标签请求
message petTagQueryRequest {
  string userId = 1;
  string petId = 2;
}
// 宠物标签生成请求
message petTagGenerateRequest {
  string userId = 1;
  string petId = 2;
}
message petTagDto {
  string userId = 1;
  string petId = 2;
  string tagName = 3;
  string tagValue = 4;
}
// 宠物标签响应
message petTagQueryResponse {
  int32 code = 1;
  string message = 2;
  repeated petTagDto data = 3;
}


message QueryUsuallyRecordProductResponse{
  int32 code = 1;
  string message = 2;
  // 总条数
  int32 total = 3;
  // 标签列表
  repeated ProductInfo data = 4;
}


message ProductInfo{
  int32 sku_id = 1;
  int32 channel_id = 2;
  int32 sales_volume = 3;
}

// 标签操作
message TagOperateRequest {
  // 组标签名称
  string group_name = 1;
  // 标签名称
  string name = 2;
  // 更新前的标签名称
  string old_name = 3;
  // 操作，add-新增，update-更新，delete-删除
  string opt = 4;
  //主体id
  int32 org_id = 5;
}
message TagOperateResponse {
  // 响应码
  int32 code = 1;
  // 返回信息
  string message = 2;
  // 文章id集合
  repeated int32 article_ids = 3;
}
// 内容标签查询
message ContentTagRequest {
  //主体id
  int32 org_id = 3;
}
message ContentTagResponse {
  // 响应码
  int32 code = 1;
  // 返回信息
  string message = 2;
  repeated TagQueryData  data = 3;
}
message TagQueryData {
  string name = 1;
  string tags = 2;
}

message MemberProductDiscountRequest{
  string user_id = 1;
}
message MemberProductDiscountResponse{
  string discount =1;
}

message MessageInfoRequest {
  //用户ID
  string user_id = 1;
  //主体：1-阿闻，2-极宠家 3-宠商云 4-百林康源+宠利扫
  int32 org_id = 2;
}

message MessageInfoResponse{
    //身份
    repeated MessageInfo status_data = 1;
    //资产
    repeated MessageInfo property_data = 2;
    //业务
    repeated MessageInfo business_data = 3;
}
message MessageInfo{
    //模板id
    string template_id = 1;
    //名称
    string message_title = 2;
    //类型 1、用户等级通知 2、积分通知 3、优惠券通知 4、医院挂号排队通知 5、预约挂号通知 6子龙美容服务通知 7核销码使用 8核销码过期提醒 9会员权益过期通知
    int32 message_type = 3;
    //次数
    int32 number = 4;
    //类型键值
    string template_key = 5;
}
message MemberCouponsMessageResponse{
    repeated MemberCouponsMessage data =1;
}
message MemberCouponsMessage{
    //1、升级券 2、周特权
    int32 message_type = 1;
    //券类型：1门店券  2商品券
    int32 coupon_type = 2;
    //内容
    string message_body = 3;
}

message MemberNewMessageRequest {
  //用户ID
  string user_id = 1;
  //token
  string token = 2;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 3;
}

message MemberNewMessageResponseData {
  message NewMessageData {
    //时间
    string datetime = 1;
    //内容
    string content = 2;
  }
  //病例
  NewMessageData med_record = 1;
  //健康值
  NewMessageData health = 2;
  //积分
  NewMessageData integral = 3;
}

message MemberNewMessageResponse{
  MemberNewMessageResponseData data = 3;
}

message InfoUpdateRequest {
  //用户ID
  string user_id = 1;
  //昵称
  string user_name = 2;
  //性别
  int32 user_sex = 3;
  //手机号码
  string user_mobile = 4;
  //生日
  string user_birthday = 5;
  //初次养宠物时间
  string first_raises_pet = 6;
  //头像
  string user_avatar = 7;
  //省
  string province = 8;
  //市
  string city = 9;
  //区
  string area = 10;
  //
  int32 remote_treat_status = 11;
  //
  string token = 12;
}

message PetInfoUpdateRequest {
  //ID
  int32 id = 1;
  //用户ID
  string user_id = 2;
  //宠物ID
  string pet_id = 3;
  //宠物头像
  string pet_avatar = 4;
  //宠物名称
  string pet_name = 5;
  //宠物生日
  string pet_birthday = 6;
  string pet_home_day = 7;
  int32 pet_sex = 8;
  int32 pet_kindof = 9;
  int32 pet_variety = 10;
  int32 pet_long = 11;
  int32 pet_weight = 12;
  int32 pet_neutering = 13;
  int32 pet_vaccinated = 14;
  int32 pet_deworming = 15;
  int32 pet_height = 16;
  int32 pet_source = 17;
  int32 pet_status = 18;
  string pet_remark = 19;
  string pet_variety_str = 20;
  string pet_kindof_str = 21;
  string dog_licence_code = 22;
  string dog_vaccinate_code = 23;
  string face_id = 24;
  string pet_code = 25;
  string send_time = 26;
  string insurance_face_id = 27;
  bool ensure_card = 28;
  string age_str = 30;
  string age_conversion_str = 31;
  string token = 32;
  string open_id = 33;
  string pet_flower = 34;
  string flower_code = 35;
}

message PetInfoUpdateResponse {
  string msg = 1;
  string data = 2;
}
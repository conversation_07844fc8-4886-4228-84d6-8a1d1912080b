package services

import (
	"context"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/ext"
	"order-center/proto/oc"
	"time"
)

type AdvertisementMpService struct {
}

//添加广告转化记录
func (a AdvertisementMpService) AddAdvertisementMpRecord(ctx context.Context, in *oc.AddAdvertisementMpRecordRequest) (*oc.MpBaseResponse, error) {
	glog.Info("添加广告转化记录：", in.OrderSn, " ", kit.JsonEncode(in))
	out := oc.MpBaseResponse{Code: 400}

	pushData, err := getPushData(in)

	if err != nil {
		out.Message = err.Error()
		return &out, err
	}

	//连接池勿关闭
	db := GetDBConn()
	model := models.AdvertisementMpRecord{
		OrderSn:    in.OrderSn,
		UserId:     in.UserId,
		Url:        in.Url,
		ClickId:    in.ClickId,
		ActionType: in.ActionType,
		PushJson:   pushData,
		ChannelId:  in.ChannelId,
		CreateTime: time.Now().Local(),
	}
	ok, err := db.Insert(model)
	if err != nil {
		out.Message = "添加广告转化记录异常," + err.Error()
		return &out, nil
	}
	if ok <= 0 {
		out.Message = "添加广告转化记录失败"
		return &out, nil
	}

	//马上处理一次
	//go PushMpRecordNotify(&model)

	out.Code = 200
	return &out, nil
}

func PushMpRecordNotify(in *models.AdvertisementMpRecord){
	var (
		res *ext.MpBaseResponse
		err error
	)
	db := GetDBConn()

	//更新处理次数
	db.Exec("update advertisement_mp_record set deal_num = deal_num + 1 where id = ?", in.Id)

	extClient := ext.GetExternalTencentClient()
	defer extClient.Close()
	failMsg := ""

	if in.ChannelId == ChannelMallId {
		var rep *ext.PushMpRequest
		err1 := kit.JsonDecode([]byte(in.PushJson),&rep)
		if err1 !=nil {
			glog.Error("推送广告PushMpRecordNotify 解析推送内容异常",in.OrderSn,err.Error())
			failMsg = "解析推送内容异常"
		}
		res,err =extClient.MP.PushMp(context.Background(),rep)
	} else{
		var rep *ext.PushMpForTakeOutRequest
		err1 := kit.JsonDecode([]byte(in.PushJson),&rep)
		if err1 !=nil {
			glog.Error("推送广告PushMpRecordNotify 解析推送内容异常",in.OrderSn,err.Error())
			failMsg = "解析推送内容异常"
		}
		res,err = extClient.MP.PushMpForTakeOut(context.Background(),rep)
	}
	if err !=nil {
		glog.Error("推送广告PushMpRecordNotify 推送异常",in.OrderSn,err.Error())
		failMsg = "推送异常"
	}
	glog.Infof("推送广告PushMpRecordNotify:order_sn：%s,参数：%s，返回结果%s", in.OrderSn, in.PushJson,kit.JsonEncode(res))
	if res.Code != 200{
		glog.Error("推送广告PushMpRecordNotify 推送失败",in.OrderSn,res.Message)
		failMsg = "推送失败"
	}

	if len(failMsg) <=0{
		//更新处理状态
		db.Exec("update advertisement_mp_record set deal_status = 1,update_time =? ,deal_msg = ? where id = ?", kit.GetTimeNow(), "成功",in.Id)
	}else{
		db.Exec("update advertisement_mp_record set deal_msg = ? where id = ?", failMsg,in.Id)
	}
}

func getPushData(in *oc.AddAdvertisementMpRecordRequest) (string, error) {
	if len(in.Url) <= 0 {
		in.Url = "http://www.qq.com"
	}
	pushData := ""
	var err error

	//拼团订单需要特殊处理
	if in.OrderType == 4 && in.ActionType == 2{
		order := GetPinOrderGroupByPinOrderSn(in.OrderSn)
		if order != nil && order.Id > 0 {
			outerActionId := "orderSubmit-" + in.OrderSn
			if in.ActionType == 3 {
				outerActionId = "orderPay-" + in.OrderSn
			}
			pushModel := ext.PushMpRequest{
				Data: &ext.PushMpData{
					Url:           in.Url,
					ActionTime:    in.ActionTime,
					ActionType:    MpActionType[in.ActionType],
					Trace: &ext.MpTrace{
						ClickId:       in.ClickId,
					},
					OuterActionId: outerActionId,
				},
				UserAgent:     in.UserAgent,
			}
			pushModel.Data.ActionParam = new(ext.ActionParam)

			pushModel.Data.ActionParam.Value = cast.ToInt32(order.PayPrice)

			pushData = kit.JsonEncode(pushModel)
		}else {
			err = errors.New("找不到订单")
		}
	}else{
		if in.ActionType == 1 {
			pushModel := ext.PushMpRequest{
				Data: &ext.PushMpData{
					Url:           in.Url,
					ActionTime:    in.ActionTime,
					ActionType:    MpActionType[in.ActionType],
					Trace: &ext.MpTrace{
						ClickId:       in.ClickId,
					},

					OuterActionId: "userVisit",
				},
				UserAgent:     in.UserAgent,
			}
			pushData = kit.JsonEncode(pushModel)
		} else if in.ActionType == 2 || in.ActionType == 3 {
			order := GetOrderByOrderSn(in.OrderSn)
			if order != nil && order.Id > 0 {
				if in.ChannelId == ChannelMallId {
					outerActionId := "orderSubmit-" + in.OrderSn
					if in.ActionType == 3 {
						outerActionId = "orderPay-" + in.OrderSn
					}
					pushModel := ext.PushMpRequest{
						Data: &ext.PushMpData{
							Url:           in.Url,
							ActionTime:    in.ActionTime,
							ActionType:    MpActionType[in.ActionType],
							Trace: &ext.MpTrace{
								ClickId:       in.ClickId,
							},
							OuterActionId: outerActionId,
						},
						UserAgent:     in.UserAgent,
					}
					pushModel.Data.ActionParam = new(ext.ActionParam)

					pushModel.Data.ActionParam.Value = order.Total

					pushData = kit.JsonEncode(pushModel)
				} else {

					pushModel := ext.PushMpForTakeOutRequest{
						Data: &ext.PushMpForTakeOutData{
							Url:        in.Url,
							ActionTime: in.ActionTime,
							ActionType: MpActionType[in.ActionType],
							Trace: &ext.MpTrace{
								ClickId:    in.ClickId,
							},
						},

						UserAgent:  in.UserAgent,
					}
					pushModel.Data.ActionParam = new(ext.ActionParamForTakeOut)

					pushModel.Data.ActionParam.Value = order.Total
					orderProduct := GetOrderProductByOrderSn(order.OrderSn, "")
					var productArr []string

					for _, product := range orderProduct {
						if len(product.ParentSkuId) <= 0 {
							str := fmt.Sprintf("%v_%d_%d", product.ProductName, product.PayPrice, product.Number)
							productArr = append(productArr, str)
						}
					}
					pushModel.Data.ActionParam.OrderDetail = productArr

					pushData = kit.JsonEncode(pushModel)
				}
			} else {
				err = errors.New("找不到订单")
			}
		}
	}

	return pushData, err
}

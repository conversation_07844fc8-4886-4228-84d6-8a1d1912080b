package models

type UpetGoods struct {
	GoodsId              int     `xorm:"not null pk autoincr comment('商品id(SKU)') INT(10)"`
	GoodsCommonid        int     `xorm:"not null comment('商品公共表id') index INT(10)"`
	GoodsName            string  `xorm:"not null comment('商品名称（+规格名称）') VARCHAR(50)"`
	GoodsJingle          string  `xorm:"default '' comment('商品广告词') VARCHAR(150)"`
	StoreId              int32   `xorm:"not null comment('店铺id') INT(10)"`
	StoreName            string  `xorm:"not null comment('店铺名称') VARCHAR(50)"`
	GcId                 int     `xorm:"not null comment('商品分类id') INT(10)"`
	GcId1                int     `xorm:"not null comment('一级分类id') INT(10)"`
	GcId2                int     `xorm:"not null comment('二级分类id') INT(10)"`
	GcId3                int     `xorm:"not null comment('三级分类id') INT(10)"`
	BrandId              int     `xorm:"default 0 comment('品牌id') INT(10)"`
	GoodsPrice           float64 `xorm:"not null comment('商品价格') index DECIMAL(10,2)"`
	GoodsPromotionPrice  float64 `xorm:"not null comment('商品促销价格') DECIMAL(10,2)"`
	GoodsPromotionType   int     `xorm:"not null default 0 comment('促销类型 0无促销，1团购，2限时折扣') TINYINT(3)"`
	GoodsMarketprice     float64 `xorm:"not null comment('市场价') DECIMAL(10,2)"`
	GoodsSerial          string  `xorm:"default '' comment('商品货号') index VARCHAR(50)"`
	GoodsStorageAlarm    int     `xorm:"not null comment('库存报警值') TINYINT(3)"`
	GoodsBarcode         string  `xorm:"default '' comment('商品条形码') VARCHAR(20)"`
	GoodsClick           int     `xorm:"not null default 0 comment('商品点击数量') INT(10)"`
	GoodsSalenum         int32   `xorm:"not null default 0 comment('销售数量') INT(10)"`
	GoodsCollect         int     `xorm:"not null default 0 comment('收藏数量') INT(10)"`
	SpecName             string  `xorm:"not null comment('规格名称') VARCHAR(255)"`
	GoodsSpec            string  `xorm:"not null comment('商品规格序列化') TEXT"`
	GoodsStorage         int32   `xorm:"not null default 0 comment('商品库存') INT(10)"`
	GoodsImage           string  `xorm:"not null default '' comment('商品主图') VARCHAR(100)"`
	GoodsBody            string  `xorm:"not null comment('商品描述') TEXT"`
	MobileBody           string  `xorm:"not null comment('手机端商品描述') TEXT"`
	GoodsState           int     `xorm:"not null comment('商品状态 0下架，1正常，10违规（禁售）') TINYINT(3)"`
	GoodsVerify          int     `xorm:"not null comment('商品审核 1通过，0未通过，10审核中') TINYINT(3)"`
	GoodsAddtime         int     `xorm:"not null comment('商品添加时间') INT(10)"`
	GoodsEdittime        int     `xorm:"not null comment('商品编辑时间') INT(10)"`
	Areaid1              int     `xorm:"not null comment('一级地区id') INT(10)"`
	Areaid2              int     `xorm:"not null comment('二级地区id') INT(10)"`
	ColorId              int     `xorm:"not null default 0 comment('颜色规格id') INT(10)"`
	TransportId          int     `xorm:"not null comment('运费模板id') MEDIUMINT(8)"`
	GoodsFreight         float64 `xorm:"not null default 0.00 comment('运费 0为免运费') DECIMAL(10,2)"`
	GoodsVat             int     `xorm:"not null default 0 comment('是否开具增值税发票 1是，0否') TINYINT(3)"`
	GoodsCommend         int     `xorm:"not null default 0 comment('商品推荐 1是，0否 默认0') TINYINT(3)"`
	GoodsStcids          string  `xorm:"default '' comment('店铺分类id 首尾用,隔开') VARCHAR(255)"`
	EvaluationGoodStar   int     `xorm:"not null default 5 comment('好评星级') TINYINT(3)"`
	EvaluationCount      int     `xorm:"not null default 0 comment('评价数') INT(10)"`
	IsVirtual            int     `xorm:"not null default 0 comment('是否为虚拟商品 1是，0否') TINYINT(3)"`
	VirtualIndate        int     `xorm:"not null comment('虚拟商品有效期') INT(10)"`
	VirtualLimit         int32   `xorm:"not null comment('虚拟商品购买上限') INT(10)"`
	VirtualInvalidRefund int     `xorm:"not null default 1 comment('是否允许过期退款， 1是，0否') TINYINT(3)"`
	VirtualRefundDay     int     `xorm:"comment('限制退款天数') TINYINT(3)"`
	IsFcode              int     `xorm:"not null default 0 comment('是否为F码商品 1是，0否') TINYINT(4)"`
	IsPresell            int     `xorm:"not null default 0 comment('是否是预售商品 1是，0否') TINYINT(3)"`
	PresellDeliverdate   int     `xorm:"not null default 0 comment('预售商品发货时间') INT(11)"`
	IsBook               int     `xorm:"not null default 0 comment('是否为预定商品，1是，0否') TINYINT(4)"`
	BookDownPayment      float64 `xorm:"not null default 0.00 comment('定金金额') DECIMAL(10,2)"`
	BookFinalPayment     float64 `xorm:"not null default 0.00 comment('尾款金额') DECIMAL(10,2)"`
	BookDownTime         int     `xorm:"not null default 0 comment('预定结束时间') INT(11)"`
	BookBuyers           int     `xorm:"default 0 comment('预定人数') MEDIUMINT(9)"`
	HaveGift             int     `xorm:"not null default 0 comment('是否拥有赠品') TINYINT(3)"`
	IsOwnShop            int     `xorm:"not null default 0 comment('是否为平台自营') TINYINT(3)"`
	Contract1            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract2            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract3            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract4            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract5            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract6            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract7            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract8            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract9            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract10           int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	IsChain              int     `xorm:"not null default 0 comment('是否为门店商品 1是，0否') TINYINT(3)"`
	GoodsTransV          float64 `xorm:"not null default 0.00 comment('重量或体积') DECIMAL(10,2)"`
	IsDis                int     `xorm:"not null default 0 comment('是否分销') TINYINT(1)"`
	DisCommisRate        float64 `xorm:"not null default 0 comment('分销佣金比例') TINYINT(1)"`
	IsBatch              int     `xorm:"default 0 comment('是否批发商品 0零售  1批发') TINYINT(1)"`
	BatchPrice           string  `xorm:"comment('批发阶梯价') TEXT"`
	GoodsInv             int     `xorm:"not null default 1 comment('是否开发票') TINYINT(1)"`
	MemberPrice1         float64 `xorm:"not null default 0.00 comment('会员等级价v1') DECIMAL(10,2)"`
	MemberPrice2         float64 `xorm:"not null default 0.00 comment('会员等级价v2') DECIMAL(10,2)"`
	MemberPrice3         float64 `xorm:"not null default 0.00 comment('会员等级价v3') DECIMAL(10,2)"`
	GoodsLimit           int     `xorm:"default 0 comment('限购：0不限购') INT(11)"`
	GoodsRecommendNum    int     `xorm:"comment('APP推荐次数') INT(11)"`
	GSearchStatus        int     `xorm:"not null default 0 comment('是否是允许搜索商品 1否，0是') TINYINT(1)"`
	GCType               int     `xorm:"default 0 comment('1猫站，2狗站') TINYINT(2)"`
	Freight              int     `xorm:"default 0 comment('是否包邮0为不包邮，2为包邮') TINYINT(1)"`
	ChainId              int32   `xorm:"default 0 comment('记录商品归属门店id') INT(10)"`
	RegionId             int     `xorm:"default 0 comment('大区标识') INT(4)"`
	GoodPercent          int     `xorm:"default 0 comment('好评率') TINYINT(3)"`
	GoodsSku             string  `xorm:"comment('中心货号') index VARCHAR(100)"`
	GoodsSkuType         int     `xorm:"default 1 comment('货号类型 1全渠道 2管易 3门店') TINYINT(1)"`
	IsVip                int     `xorm:"default 0 comment('是否限制会员卡用户购买，0否，1是') TINYINT(1)"`
	IsBzk                int     `xorm:"default 0 comment('是否限制保障卡用户购买，0否，1是') TINYINT(1)"`
	RelevanceId          int     `xorm:"default 0 comment('关联索引ID') INT(11)"`
	GoodsType            int     `xorm:"default 0 comment('0普通商品，1实实组合，2虚虚组合，3虚实组合') TINYINT(2)"`
	WarehouseType        int32   `xorm:"comment('药品仓类型：0:默认否, 1:巨星药品仓') TINYINT(4)"`
}

const (
	//商品状态
	UPETGOODSSTATE1  = 1  // 出售中
	UPETGOODSSTATE0  = 0  // 下架
	UPETGOODSSTATE10 = 10 // 违规

	//审核状态
	UPETGOODSVERIFY1  = 1  // 审核通过
	UPETGOODSVERIFY0  = 0  // 审核失败
	UPETGOODSVERIFY10 = 10 // 等待审核
)

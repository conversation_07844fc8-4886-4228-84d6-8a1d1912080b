package tasks

import (
	"order-center/utils"
	"testing"
	"time"
)

func Test_consumeDeliveryNoPickCancelTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: "测试延时队列"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ss := `
 {
	"mt_peisong_id":"131232132",
	"delivery_id":21321321,
	"order_id":12321321,
        "delivery_type":0,
       "parent_order_sn":"21312321321",
	"delivery_count":1
}`
			//配送没接单取消切换队列
			//DeliveryOrderNoPickExchange := "ordercenter.dlx"
			DeliveryOrderNoPickRoute := "order.delivery.noPick"
			//DeliveryOrderNoPickQueue := "order-center:orderDelivery-no-pick-queue-dlx"
			if err := utils.PublishRabbitMQV2(DeliveryOrderNoPickRoute, "ordercenter", ss, int64(1*time.Minute/time.Millisecond)); err != nil {
				//glog.Error(c.orderMain.OrderSn, "，配送订单加入5分钟没接单取消切换队列失败，", err)
			}
		})
	}
}

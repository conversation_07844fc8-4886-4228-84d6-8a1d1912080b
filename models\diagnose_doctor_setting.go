package models

import "time"

//在线问诊医生接单设置表
type DiagnoseDoctorSetting struct {
	//自增id
	Id int32
	//医生编号
	DoctorCode string `json:"doctor_code"`
	//医生类别：1门店医生，2互联网医生
	DoctorType int32 `json:"doctor_type"`
	//是否开启在线问诊功能:0-否，1-是
	OpenDiagnose int32 `json:"open_diagnose"`
	//是否开启在线问诊服务:0-否，1-是
	OpenDiagnoseService int32 `json:"open_diagnose_service"`
	//医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
	DiagnoseForms string `json:"diagnose_forms"`
	//是否被禁用：0-否，1-是
	IsForbidden int32 `json:"is_forbidden"`
	//是否接受抢单：0-否，1-是
	AcceptPreempt int32 `json:"accept_preempt"`
	//是否接受派单(指用户能找到该医生)：0-否，1-是
	AcceptSend int32 `json:"accept_send"`
	//后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
	WorkOnOff int32 `json:"work_on_off"`
	//设置可电话/视频的开始时间
	StartTime string `json:"start_time"`
	//设置可电话/视频的结束时间
	EndTime string `json:"end_time"`
	//快速图文问诊费用（单位分）
	QuickImageTextPrice int32 `json:"quick_image_text_price"`
	//图文问诊费用（单位分）
	ImageTextPrice int32 `json:"image_text_price"`
	//电话问诊费用（单位分）
	PhonePrice int32 `json:"phone_price"`
	//视频问诊费用（单位分）
	VideoPrice int32 `json:"video_price"`
	//创建日期
	CreateTime time.Time `json:"create_time" xorm:"<-"`
	//最后更新时间
	UpdateTime time.Time `json:"update_time" xorm:"<-"`
}

//是否接受派单(指用户能找到该医生)：0-否，1-是
const (
	AcceptSendNo = iota
	AcceptSendYes
)

//是否接受抢单：0-否，1-是
const (
	AcceptPreemptNo = iota
	AcceptPreemptYes
)

//后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
const (
	DoctorWorkOff = iota
	DoctorWorkOn
)

//是否被禁用：0-否，1-是
const (
	DoctorIsForbiddenNo = iota
	DoctorIsForbiddenYes
)

package models

type UpetOrderSnapshot struct {
	RecId         int    `xorm:"not null pk comment('主键') INT(11)"`
	GoodsId       int64  `xorm:"not null comment('商品ID') INT(11)"`
	CreateTime    int64  `xorm:"not null comment('生成时间') INT(11)"`
	GoodsAttr     string `xorm:"comment('属性') TEXT"`
	FileDir       string `xorm:"default '' comment('文件目录') VARCHAR(80)"`
	PlateidTop    string `xorm:"not null comment('顶部关联板式') TEXT"`
	PlateidBottom string `xorm:"not null comment('底部关联板式') TEXT"`
	GoodsBody     string `xorm:"comment('商品内容') TEXT"`
	Source        int    `xorm:"default 0 comment('0(默认)商城写入 1 平台写入') TINYINT(4)"`
}

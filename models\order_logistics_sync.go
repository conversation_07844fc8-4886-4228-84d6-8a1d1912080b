package models

import (
	"time"
)

type OrderLogisticsSync struct {
	Id              int64     `xorm:"not null pk autoincr INT(11)"`
	OrderSn         string    `xorm:"not null default '''' comment('订单号') index VARCHAR(20)"`
	OldOrderSn      string    `xorm:"not null default '''' comment('渠道订单号') index VARCHAR(50)"`
	ChannelId       int32     `xorm:"not null default 0 comment('1 阿闻到家 2美团 3饿了么 4京东 5阿闻商城') TINYINT(1)"`
	LogisticsStatus int32     `xorm:"not null default 0 comment('回调的配送状态码，0：待调度 20：已接单 30：已取货 50：已送达 99：已取消') INT(11)"`
	DeliveryId      int64     `xorm:"not null default 0 comment('配送id') index INT(11)"`
	Status          int       `xorm:"not null default 0 comment('1 同步成功 2 同步失败') TINYINT(1)"`
	ErrInfo         string    `xorm:"default '''' comment('同步出错信息') VARCHAR(200)"`
	Data            string    `xorm:"default 'NULL' comment('推送的数据') TEXT"`
	DataFull        int       `xorm:"default 1 comment('同步的数据是否完整') TINYINT(1)"`
	ReSyncCnt       int       `xorm:"not null default 0 comment('重推次数') INT(11)"`
	CreateTime      time.Time `xorm:"created not null DATETIME"`
	UpdateTime      time.Time `xorm:"updated not null DATETIME"`
}

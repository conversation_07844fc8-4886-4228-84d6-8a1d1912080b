syntax = "proto3";

package ac;

import "ac/activity_model.proto";

service GroupChatService {
  //新增\修改社区群
  rpc EditGroupChat(EditGroupChatRequest)returns(BaseResponseNew);
  //查询社区群
  rpc QueryGroupChat(QueryGroupChatRequest)returns(QueryGroupChatResponse);
  //删除社区群
  rpc DeleteGroupChat(DeleteGroupChatRequest)returns(BaseResponseNew);
  //生成加群链接
  rpc AddJoinWay(AddJoinWayReq)returns(BaseResponseNew);
  //群列表(管理后台生成加群链接)
  rpc ChatList(ChatListReq)returns(ChatListRes);
  //刷新群列表（所有群）
  rpc WxChatList(ChatListReq)returns(BaseResponseNew);
  // 设置门店微信
  rpc SetHospitalWechat(SetHospitalWechatRequest)returns(baseResponse);
  //关联、取消微信群医院
  rpc LinkHospital(LinkHospitalRequest)returns(baseResponse);
  //获取医院微信群列表
  rpc HospitalGroupChatList(HospitalGroupChatListRequest)returns(HospitalGroupChatListResponse);
}

message EditGroupChatRequest{
  int64 id = 1;
  string chat_name = 2;
  string chat_link = 3;
  int64 province_id = 4;
  string province_name = 5;
  int64 city_id = 6;
  string city_name = 7;
  int64 area_id = 8;
  string area_name = 9;
  int64 street_id = 10;
  string street_name = 11;
  int32 type = 12;
}

message DeleteGroupChatRequest{
  int64 id = 1;
}

message QueryGroupChatRequest{
  int64 id = 1;
  int32 type = 2;
  int32 page_index = 3;
  int32 page_size = 4;
}
message QueryGroupChatResponse{
  int32 total_count = 1;
  repeated GroupChat data = 2;
}
message GroupChat{
  int64 id = 1;
  string chat_name = 2;
  string chat_link = 3;
  int64 province_id = 4;
  string province_name = 5;
  int64 city_id = 6;
  string city_name = 7;
  int64 area_id = 8;
  string area_name = 9;
  int64 street_id = 10;
  string street_name = 11;
  repeated string hospital_codes = 12;
}

message AddJoinWayReq {
  //活动类型 1，宠友服务群 2，抗疫补给站
  int32 type = 1;
  //群名称
  string chat_name = 2;
  //省id
  int32 province_id = 3;
  //省名称
  string province_name = 4;
  //市id
  int32 city_id = 5;
  //市名称
  string city_name = 6;
  //区id
  int32 area_id = 7;
  //区名称
  string area_name = 8;
  //街道id
  int32 street_id = 9;
  //街道名称
  string street_name = 10;
  //群ID，多个逗号分隔
  string chat_id = 11;
}

message ChatListReq {
  //群名称
  string chat_name = 1;
  //群主名称
  string chat_master = 2;
  int32 page_index = 3;
  int32 page_size = 4;
}

message ChatListRes {
  int32 total = 1;
  repeated ChatListData data = 2;
}

message ChatListData {
  //群ID
  string chat_id = 1;
  //群名称
  string chat_name = 2;
  //群公告
  string notice = 3;
  //群创建时间
  int64 chat_create_time = 4;
  //群主名称
  string owner_name = 5;
}

message SetHospitalWechatRequest{
  // 医院code
  string hospital_code = 1;
  // 微信二维码地址
  string wechat_qrcode = 2;
}

message LinkHospitalRequest {
  int64 group_chat_id = 1;
  repeated string hospital_codes = 2;
}

message HospitalGroupChatListRequest{
  string hospital_code = 1;
}

message HospitalGroupChatListResponse{
  int64 code = 1;
  string message = 2;
  // 门店微信
  string wechat = 3; 
  message GroupChat {
    // 微信群id
    int64 group_chat_id = 1; 
    // 微信群名称
    string chat_name = 2;
    // 微信群链接
    string chat_link = 3;
  }
  // 门店微信群
  repeated GroupChat group_list = 4;
}
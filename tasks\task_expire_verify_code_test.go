package tasks

import (
	kit "github.com/tricobbler/rp-kit"
	"testing"
)

func Test_handleExpireVerifyCode(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "核销码到期自动退款",
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handleExpireVerifyCode()
		})
	}
}

func Test_verifyCodeExpireNotice(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			verifyCodeExpireNotice()
		})
	}
}

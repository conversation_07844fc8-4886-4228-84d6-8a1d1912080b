package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"strconv"
	"strings"
	"time"
)

// 实物订单-导出(含商品明细)数据
type OrderProductExport struct {
	F          *excelize.File
	SheetName  string
	storeMap   map[string]*dac.StoreInfo
	taskParams *oc.AwenMaterOrderListRequest
}

// 逻辑
func (e *OrderProductExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.AwenMaterOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	var orderList, details []*oc.AwenOrderProductExport
	var combinedProduct map[string]string

	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 5000
	for {
		details, combinedProduct, err = services.AwenOrderExportProduct(e.taskParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		orderList = append(orderList, details...)
		glog.Info("details length - pagesize length", len(details), e.taskParams.PageSize)
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.Shopids)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	glog.Info(e.taskParams.UserNo, ", 导出文件循环填充数据开始, ", len(orderList))
	nums = len(orderList)
	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}
	var n string
	for k := range orderList {
		n = strconv.Itoa(k + 2)
		// 订单号
		e.F.SetCellValue(e.SheetName, "A"+n, orderList[k].OrderSn)
		// 父订单号
		e.F.SetCellValue(e.SheetName, "B"+n, orderList[k].ParentOrderSn)
		//外部订单号
		e.F.SetCellValue(e.SheetName, "C"+n, orderList[k].OldOrderSn)
		//收货人
		e.F.SetCellValue(e.SheetName, "D"+n, orderList[k].ReceiverName)
		// 收货人联系方式
		// 收货人地址
		if _, ok := phoneExportMap[orderList[k].ShopId]; ok {
			createTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, orderList[k].CreateTime)
			compareStrTime := "2022-04-01  00:00:00"
			compareTime := utils.GetTimeStr(kit.DATETIME_LAYOUT, compareStrTime)
			if createTime.After(compareTime) {
				e.F.SetCellValue(e.SheetName, "E"+n, services.MobileDecrypt(orderList[k].EnReceiverMobile, e.taskParams.UserNo, e.taskParams.Ip))
				e.F.SetCellValue(e.SheetName, "F"+n, orderList[k].ReceiverAddress)
			} else {
				e.F.SetCellValue(e.SheetName, "E"+n, "")
				e.F.SetCellValue(e.SheetName, "F"+n, "")
			}
		} else {
			e.F.SetCellValue(e.SheetName, "E"+n, "")
			e.F.SetCellValue(e.SheetName, "F"+n, "")
		}
		// 备注
		e.F.SetCellValue(e.SheetName, "G"+n, orderList[k].BuyerMemo)
		// SkuId
		e.F.SetCellValue(e.SheetName, "H"+n, orderList[k].SkuId)
		// 商品名称
		e.F.SetCellValue(e.SheetName, "I"+n, orderList[k].ProductName)
		// 店内分类
		e.F.SetCellValue(e.SheetName, "J"+n, orderList[k].ChannelCategoryName)
		// 组合商品SkuId
		e.F.SetCellValue(e.SheetName, "K"+n, orderList[k].ParentSkuId)
		// 组合商品名称
		if len(orderList[k].ParentSkuId) > 0 {
			if _, ok := combinedProduct[orderList[k].ParentSkuId]; ok {
				e.F.SetCellValue(e.SheetName, "L"+n, combinedProduct[orderList[k].ParentSkuId])
			}
		}
		// 订单状态
		e.F.SetCellValue(e.SheetName, "M"+n, services.OrderStatusMap[orderList[k].OrderStatusChild])
		// 发货数量
		e.F.SetCellValue(e.SheetName, "N"+n, orderList[k].Number)
		// 订单金额
		e.F.SetCellValue(e.SheetName, "O"+n, kit.FenToYuan(orderList[k].PaymentTotal))
		// 退款数量
		e.F.SetCellValue(e.SheetName, "P"+n, orderList[k].RefundNumber)
		// 退款金额
		e.F.SetCellValue(e.SheetName, "Q"+n, kit.FenToYuan(orderList[k].RefundTotal))
		/*if orderList[k].OrderStatus != 10 && orderList[k].RefundState == 3 {
			amount := services.OrderRetrunGetList(orderList[k].OrderSn)
			key := orderList[k].ProductName + orderList[k].SkuId + cast.ToString(orderList[k].PaymentTotal)
			if _, ok := amount[key]; ok {
				e.F.SetCellValue(e.SheetName, "K"+n, amount[key])
			}
			//amount := services.GetSkuRefundAmount(orderList[k].OrderSn)
			//if _, ok := amount[orderList[k].SkuId]; ok {
			//	e.F.SetCellValue(e.SheetName, "K"+n, amount[orderList[k].SkuId])
			//}
		}*/
		// 支付时间
		e.F.SetCellValue(e.SheetName, "R"+n, orderList[k].PayTime)
		// 店铺ID(财务编码)
		e.F.SetCellValue(e.SheetName, "S"+n, orderList[k].ShopId)
		// 门店名称
		e.F.SetCellValue(e.SheetName, "T"+n, orderList[k].ShopName)
		// 业绩所属员工姓名
		e.F.SetCellValue(e.SheetName, "U"+n, orderList[k].PerformanceStaffName)
		//店铺类型
		ShopType := "新瑞鹏"
		if orderList[k].AppChannel != 1 {
			ShopType = "TP代运营"
		}
		e.F.SetCellValue(e.SheetName, "V"+n, ShopType)
		// 支付方式
		e.F.SetCellValue(e.SheetName, "W"+n, services.PayMode[orderList[k].PayMode])
		//提货点名称
		e.F.SetCellValue(e.SheetName, "X"+n, orderList[k].PickupStationName)
		// 提货点地址
		e.F.SetCellValue(e.SheetName, "Y"+n, orderList[k].PickupStationAddress)
		// 送达时间
		e.F.SetCellValue(e.SheetName, "Z"+n, orderList[k].ExpectedTime)
		// 送达时间
		e.F.SetCellValue(e.SheetName, "AA"+n, kit.FenToYuan(orderList[k].PrivilegePt))
	}
	e.F.Save()

	return
}

// 设置表头
func (e *OrderProductExport) SetSheetName() {
	/*nameList := []string{
		"订单号", "父订单号", "SKUID", "商品名称", "店内分类", "组合商品SKUID",
		"组合商品名称", "订单状态", "销售数量", "支付金额", "退款金额", "支付时间", "财务编码", "门店名称", "业绩归属人", "店铺类型", "支付方式",
	}*/
	nameList := []string{
		"订单号", "父订单号", "外部订单号", "收货人", "收货人联系方式", "收货人地址", "配送备注", "SKUID", "商品名称", "店内分类", "组合商品SKUID",
		"组合商品名称", "订单状态", "销售数量", "支付金额", "退款数量", "退款金额", "支付时间", "财务编码", "门店名称", "业绩归属人", "店铺类型", "支付方式",
		"提货点名称", "提货点地址", "送达时间", "平台补贴分摊",
	}
	for i := 0; i < len(nameList); i++ {
		if i > 25 {
			j := i - 26
			e.F.SetCellValue(e.SheetName, "A"+string(rune(65+j))+"1", nameList[i])
		} else {
			e.F.SetCellValue(e.SheetName, string(rune(65+i))+"1", nameList[i])
		}
	}
}

// 上传至oss生成下载链接
func (e *OrderProductExport) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("实物订单-导出商品明细(%s%d)", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return generateDownUrl(e.F, fileName)
}

package dto

type QueryStockReq struct {
	// 商品sku
	SkuIds []int64 `json:"sku_ids"`
	// 商品id
	ProductIds []int64 `json:"product_ids"`
	// 渠道id 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗 10-自提
	ChannelId int64 `json:"channel_id"`
	// 门店财务编码，channel_id = 5 时不用传
	ShopId string `json:"shop_id"`
}

type QueryStockRes struct {
	// 200 成功  400 失败
	Code    int32  `json:"code"`
	Message string `json:"message"`
	// 库存，map，key为skuid，value为库存
	Stock map[int64]VInventory `json:"stock"`
}

// 库存结构体
type VInventory struct {
	SkuId int64 `json:"sku_id"`
	//可用库存
	AvailableNum int32 `json:"available_num"`
	// 库位
	LocationCode string `json:"location_code"`
}

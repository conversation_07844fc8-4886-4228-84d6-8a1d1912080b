syntax = "proto3";

package dgc;

service ImService {
    // @Desc    消息回调
    // <AUTHOR>
    // @Date		2021-09-30
    rpc CallbackMessage(CallbackMessageRequest) returns (CallbackMessageResponse);
    // @Desc    	对话记录获取
    // <AUTHOR>
    // @Date		2021-09-30
    rpc GetMessage(GetMessageRequest) returns (GetMessageResponse);
    // @Desc    	消息发送
    // <AUTHOR>
    // @Date		2021-09-30
    rpc SendAcceptsMessage(SendAcceptsMessageRequest) returns (SendAcceptsMessageResponse);
    // @Desc    	用户注册
    // <AUTHOR>
    // @Date		2021-09-30
    rpc UserRegistration(UserRegistrationRequest) returns (UserRegistrationResponse);
}
//============================ 用户注册
message UserRegistrationRequest{
    //用户id
    string user_id = 1;
}

message UserRegistrationResponse{}
//============================ 对话记录写入
message CallbackMessageRequest{
    //回调类型  1 消息前回调 2 消息后回调
    int32 im_type = 1;
    //环信消息体
    string im_data = 2;
}
message CallbackMessageResponse{}

//============================ 对话记录获取
message GetMessageRequest{
    //医生id
    string doctor_id = 1;
    //用户id
    string user_id = 2;
    //订单编号
    string order_sn = 3;
    //页码
    int32 page_index = 4;
    //页数
    int32 page_size = 5;
    //截止时间 unix时间戳
    int64 end_time = 6;
}
message GetMessageResponse{
    //获取消息结构体
    message GetMessageList{
        //唯一id
        int32 id = 1;
        //环信消息id
        string im_msg_id = 2;
        //消息类型及数据
        string im_bodies = 3;
        //消息拓展信息
        string im_ext = 4;
        //医生id
        string doctor_id = 5;
        //用户Id
        string user_id = 6;
        //订单编号
        string order_sn = 7;
        //消息产生时间
        int64 im_timestamp = 8;
        //发送者
        string from = 9;
        //接收到
        string to = 10;
        //消息类型
        string im_msg_type = 11;
        //发送者类型
        string from_type = 12;
    }
    //总条数
    int32 total = 1;
    //列表数据
    repeated  GetMessageList list = 6;
}

//============================ 透传消息发送
message SendAcceptsMessageRequest{
    //应用场景 1.问诊开始 2问诊状态
    int32 scenario = 1;
    //拓展字段例如 ： {"order_sn":"xxxxx"}
    string ext = 2;
}
message SendAcceptsMessageResponse{}

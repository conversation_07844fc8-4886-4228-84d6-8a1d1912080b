package services

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/sv"
	"strings"
	"time"

	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"

	"github.com/maybgit/glog"
)

//TODO 从配置文件读取
//var appkeyCon string = "000000200321085863"
//var appsecretCon string = "e29841979ffb437ab9bf92ed6c3fc0a4"
//var token string = "c1246fbe87944deda1bd2afdcad9be99"
//var apiUrl = "http://local.gjpqqd.com:5918/Service/ERPService.asmx/EMallApi" + "?"

type AllChannelService struct {
	BaseService
}

//售后订单同步 emall.afterorder.synchronize  1
//注：电商渠道的售后订单同步，电商那边审核同意退货调用该接口，推送退货信息给全渠道
func (ac AllChannelService) AfterOrderSynchronize(ctx context.Context, params *oc.AfterorderRequest) (*oc.AfterorderResponse, error) {
	var out oc.AfterorderResponse
	initqqd()
	inpar := dto.AfterorderOrder{}
	for _, x := range params.Orders {

		aitem := dto.AfterorderOrderInfo{}
		aitem.Rtid = x.Rtid
		aitem.Tid = x.Tid
		aitem.Total = x.Total
		aitem.Privilege = x.Privilege
		aitem.Postfee = x.Postfee
		aitem.Created = x.Created
		aitem.Status = x.Status
		aitem.Aftsaletype = x.Aftsaletype
		aitem.Reasoncode = x.Reasoncode
		aitem.Logistbillcode = x.Logistbillcode
		aitem.Aftsaleremark = x.Aftsaleremark
		aitem.Postfee = x.Postfee

		for _, x1 := range x.Details {
			aditem := dto.AfterorderOrderInfoDetails{}
			aditem.Oid = x1.Oid
			aditem.Eshopgoodsname = x1.Eshopgoodsname
			aditem.Eshopskuname = x1.Eshopskuname
			aditem.Backqty = x1.Backqty
			aditem.Backtotal = x1.Backtotal
			aditem.Outeriid = x1.Outeriid

			aitem.Details = append(aitem.Details, aditem)
		}
		inpar.Orders = append(inpar.Orders, aitem)

	}

	signByte := kit.JsonEncodeByte(inpar)
	signStr := string(signByte)
	//订单同步
	parStr := utils.Sign(signStr, "emall.afterorder.synchronize", token, appkeyCon, appsecretCon)
	glog.Info("zx退款订单请求参数：" + signStr)
	respBytes, err := utils.HttpPost(apiUrl+parStr, signByte, "application/json;charset=UTF-8")
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("售后订单添加请求错误：" + err.Error())
		return &out, nil
	}
	glog.Info("zx退款订单请求结果：" + string(respBytes))
	retmes := new(*dto.RetAfterorder)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("售后订单添加转JSON错误：" + err.Error())
		return &out, nil
	}
	glog.Info("zx退款订单返回参数：" + string(respBytes))
	if (*retmes).Code != 0 {
		out.Code = 400
		out.Message = (*retmes).Message
		return &out, nil
	}

	for i := 0; i < len((*retmes).Orders); i++ {
		outord := oc.AfterorderRe{}
		outord.Message = (*retmes).Orders[0].Message
		outord.Tid = (*retmes).Orders[0].Tid
		outord.Billcode = (*retmes).Orders[0].Billcode
		outord.Iserror = (*retmes).Orders[0].Iserror
		if outord.Iserror {
			out.Code = 400
			out.Message = outord.Message
		} else {
			out.Code = 200
		}
		out.ReOrderList = append(out.ReOrderList, &outord)
	}
	return &out, nil
}

//售后订单同步 emall.afterorder.synchronize   1
//注：除电商渠道的其他渠道(美团，京东，饿了么，阿闻到家)的售后订单同步，各个渠道平台后台退款退货时调用该接口，不需要审核，推送退货信息给全渠道
func (ac AllChannelService) AfterOrderSynchronizeNew(ctx context.Context, params *oc.AfterorderRequest) (*oc.AfterorderResponse, error) {
	var out oc.AfterorderResponse
	initqqd()
	inpar := dto.AfterorderOrder{}

	vos := make([]*sv.SalesRecordVo, 0)

	for _, x := range params.Orders {

		aitem := dto.AfterorderOrderInfo{}
		aitem.Rtid = x.Rtid
		aitem.Tid = x.Tid
		aitem.Total = x.Total
		aitem.Privilege = x.Privilege
		aitem.Postfee = x.Postfee
		aitem.Created = x.Created
		aitem.Status = x.Status
		aitem.Aftsaletype = x.Aftsaletype
		aitem.Reasoncode = x.Reasoncode
		aitem.Logistbillcode = x.Logistbillcode
		aitem.Aftsaleremark = x.Aftsaleremark
		aitem.Postfee = x.Postfee

		for _, x1 := range x.Details {
			vo := sv.SalesRecordVo{}
			aditem := dto.AfterorderOrderInfoDetails{}
			aditem.Oid = x1.Oid
			aditem.Eshopgoodsname = x1.Eshopgoodsname
			aditem.Eshopskuname = x1.Eshopskuname
			aditem.Backqty = x1.Backqty
			aditem.Backtotal = x1.Backtotal
			aditem.Outeriid = x1.Outeriid

			aitem.Details = append(aitem.Details, aditem)

			vo.OrderSn = aitem.Tid
			vo.RefundOrderSn = aitem.Rtid
			vo.SkuId = aditem.Outeriid
			vo.Status = 2
			vo.Num = cast.ToInt32(aditem.Backqty)
			vos = append(vos, &vo)
		}
		inpar.Orders = append(inpar.Orders, aitem)

	}

	signByte, _ := json.Marshal(inpar)
	signStr := string(signByte)
	//订单同步
	parStr := utils.Sign(signStr, "emall.afterorder.synchronize", tokenNew, appkeyConNew, appsecretConNew)
	glog.Info("zx退款订单请求参数：" + signStr)
	respBytes, err := utils.HttpPost(apiUrl+parStr, signByte, "application/json;charset=UTF-8")
	if err != nil {
		//panic(err)
		out.Code = 400
		out.Message = err.Error()
		glog.Error("售后订单添加请求错误：" + err.Error())
		return &out, nil
	}

	recordVos := sv.SalesRecordVos{
		Data: vos,
	}
	client := sv.GetStockVisualClient()
	defer client.Close()

	glog.Info("退款可视化参数：", kit.JsonEncode(recordVos))
	_, err = client.RPC.InsertIntoSalesRecord(client.Ctx, &recordVos) // 可视化退款插入
	if err != nil {
		glog.Error("client.RPC.InsertIntoSalesRecord(client.Ctx, &recordVos)", err.Error())
	}

	retmes := new(*dto.RetAfterorder)
	err = json.Unmarshal([]byte(respBytes), retmes)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		glog.Error("售后订单添加转JSON错误：" + err.Error())
		return &out, nil
	}
	glog.Info("zx退款订单返回参数：" + string(respBytes))
	if (*retmes).Code != 0 {
		out.Code = 400
		out.Message = (*retmes).Message
		return &out, nil
	}

	for i := 0; i < len((*retmes).Orders); i++ {
		outord := oc.AfterorderRe{}
		outord.Message = (*retmes).Orders[0].Message
		outord.Tid = (*retmes).Orders[0].Tid
		outord.Billcode = (*retmes).Orders[0].Billcode
		outord.Iserror = (*retmes).Orders[0].Iserror
		if outord.Iserror {
			out.Code = 400
			out.Message = outord.Message
		} else {
			out.Code = 200
		}
		out.ReOrderList = append(out.ReOrderList, &outord)
	}
	return &out, nil
}

//订单状态同步 emall.orderstatus.synchronize   1
func (ac AllChannelService) OrderStatusSynchronize(ctx context.Context, params *oc.OrderStatusRequest) (*oc.OrderStatusResponse, error) {
	initqqd()
	var out oc.OrderStatusResponse

	inpar := dto.OrderStatusSynchronize{}
	for _, x := range params.Orders {
		item := dto.OrderStatusInfo{}
		item.Tid = x.Tid
		item.Status = x.Status
		item.Refundstatus = x.Refundstatus
		inpar.Orders = append(inpar.Orders, item)
	}

	signByte, _ := json.Marshal(inpar)
	signStr := string(signByte)
	//订单同步
	parStr := utils.Sign(signStr, "emall.orderstatus.synchronize", token, appkeyCon, appsecretCon)
	respBytes, err := utils.HttpPost(apiUrl+parStr, signByte, "application/json;charset=UTF-8")
	if err != nil {
		glog.Error("订单状态同步错误：", err.Error())
		out.Code = 400
		out.Message = "订单状态同步错误：" + err.Error()
		return &out, nil
	}
	retmes := new(*dto.ReOrders)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		glog.Error("订单状态同步转JSON错误：", err.Error())
		out.Code = 400
		out.Message = "订单状态同步转JSON错误：" + err.Error()
		return &out, nil
	}
	out.Code = (*retmes).Code
	if out.Code == 0 {
		out.Code = 200
	} else {
		out.Code = 400
		out.Message = (*retmes).Message
	}
	for i := 0; i < len((*retmes).Orders); i++ {
		outord := oc.OrderStatusOrders{}
		outord.Message = (*retmes).Orders[0].Message
		outord.Tid = (*retmes).Orders[0].Tid
		outord.Status = (*retmes).Orders[0].Status
		out.Orders = append(out.Orders, &outord)
	}
	return &out, nil
}

//订单发货
func (ac AllChannelService) SelfmallOrderSend(ctx context.Context, params *oc.OrderSendRequest) (*oc.OrderSendResponse, error) {
	initqqd()
	var issplit = 1
	if params.Issplit == "1" {
		issplit = 0
	}

	var out oc.OrderSendResponse
	//重新定义通用发货mq数据
	var dispatch = dto.DispatchResponse{
		OrderId:       params.Tid,
		LogisticsCode: params.Companycode,
		LogisticsNum:  params.Outsid,
		SendDate:      time.Now().Format("2006/01/02 15:04:05"),
		Freight:       "0",
		PackageInfo: dto.PackageInfo{
			PackageDetail: []dto.PackageDetail{},
		},
	}

	//调用订单发货函数结构体
	var delp = dto.DeliverParam{
		Source:        1,
		OrderSn:       params.Tid,
		IsEntire:      issplit,
		DeliverDetail: []dto.DeliverDetail{},
	}

	//连接池勿关闭
	db := GetDBConn()

	var par1 []models.OrderProduct
	if params.Issplit == "0" {
		db.Where("order_sn=?", params.Tid).Find(&par1)
		for _, nowpar := range par1 {
			var packageDetail = dto.PackageDetail{
				GoodsID: nowpar.SkuId,
				Num:     cast.ToString(nowpar.Number),
			}
			var DeliverDetail = dto.DeliverDetail{
				GoodsSku: nowpar.SkuId,
				Num:      nowpar.Number,
			}
			dispatch.PackageInfo.PackageDetail = append(dispatch.PackageInfo.PackageDetail, packageDetail)
			delp.DeliverDetail = append(delp.DeliverDetail, DeliverDetail)
		}
	} else {

		arr := strings.Split(params.Subtid, ",")
		for _, gooditem := range arr {
			par := models.OrderProduct{}
			_, err := db.Where("id=?", gooditem).Get(&par)
			if err != nil {
				glog.Error("查询数据库失败：" + err.Error())
			}

			var packageDetail = dto.PackageDetail{
				GoodsID: par.SkuId,
				Num:     cast.ToString(par.Number),
			}
			var DeliverDetail = dto.DeliverDetail{
				GoodsSku: par.SkuId,
				Num:      par.Number,
			}
			dispatch.PackageInfo.PackageDetail = append(dispatch.PackageInfo.PackageDetail, packageDetail)
			delp.DeliverDetail = append(delp.DeliverDetail, DeliverDetail)
		}

	}

	RequestJson, _ := json.Marshal(dispatch)
	RequestJsonFH, _ := json.Marshal(delp)
	isok, _ := OrderStatus(&delp)
	if !isok {
		glog.Error("调用 发货函数失败：" + string(RequestJsonFH))

	}

	mqInfo := new(models.MqInfo)
	mqInfo.Exchange = "ordercenter"
	mqInfo.Quene = "dc-sz-qqd-dispatch"
	mqInfo.Content = string(RequestJson)
	//mqInfo.Lastdate=time.Now().In(cstSh)

	mqInfo.Ispush = 0
	_, err := db.Insert(mqInfo)
	out.Tid = params.Tid
	out.Issplit = params.Issplit
	out.Subtids = params.Subtid
	out.Code = "0"
	out.Message = "OK"
	if err != nil {
		out.Code = "0"
		out.Message = "写入数据库失败"
		glog.Error("发货通知写入数据库失败：", err.Error()+"|"+string(RequestJson))
	}

	return &out, err
}

//售后订单状态同步
func (ac AllChannelService) AfterorderStatus(ctx context.Context, params *oc.AfterorderStatusRequest) (*oc.AfterorderStatusResponse, error) {
	initqqd()

	inpar := dto.AfterorderStatus{}
	inpar.Status = params.Status
	inpar.Tid = params.Tid

	var out oc.AfterorderStatusResponse
	RequestJson, _ := json.Marshal(inpar)
	mqInfo := new(models.MqInfo)
	mqInfo.Exchange = "ordercenter"
	mqInfo.Quene = "dc-sz-qqd-aftermarket"
	mqInfo.Content = string(RequestJson)
	mqInfo.Ispush = 0
	_, err := GetDBConn().Insert(mqInfo)

	out.Code = "200"
	out.Message = "OK"
	out.Iserror = "false"
	out.Errormsg = ""
	if err != nil {
		out.Code = "400"
		out.Message = "NG"
		out.Errormsg = "写入数据库失败" + err.Error()
		glog.Error("售后订单同步入数据库失败：", err.Error()+"|"+string(RequestJson))
	}

	return &out, nil

}

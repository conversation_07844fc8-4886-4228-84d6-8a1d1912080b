// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/hospital.proto

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//医院请求数据
type GetHospitalRequest struct {
	//页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//页数
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//城市
	HospitalCity string `protobuf:"bytes,3,opt,name=hospital_city,json=hospitalCity,proto3" json:"hospital_city"`
	//医院类型 1中心医院，2专科医院，3全科医院
	HospitalType string `protobuf:"bytes,4,opt,name=hospital_type,json=hospitalType,proto3" json:"hospital_type"`
	//坐标longitude
	Longitude string `protobuf:"bytes,5,opt,name=longitude,proto3" json:"longitude"`
	//坐标latitude
	Latitude             string   `protobuf:"bytes,6,opt,name=latitude,proto3" json:"latitude"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHospitalRequest) Reset()         { *m = GetHospitalRequest{} }
func (m *GetHospitalRequest) String() string { return proto.CompactTextString(m) }
func (*GetHospitalRequest) ProtoMessage()    {}
func (*GetHospitalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2809caf7c264a3e5, []int{0}
}

func (m *GetHospitalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHospitalRequest.Unmarshal(m, b)
}
func (m *GetHospitalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHospitalRequest.Marshal(b, m, deterministic)
}
func (m *GetHospitalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHospitalRequest.Merge(m, src)
}
func (m *GetHospitalRequest) XXX_Size() int {
	return xxx_messageInfo_GetHospitalRequest.Size(m)
}
func (m *GetHospitalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHospitalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHospitalRequest proto.InternalMessageInfo

func (m *GetHospitalRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetHospitalRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetHospitalRequest) GetHospitalCity() string {
	if m != nil {
		return m.HospitalCity
	}
	return ""
}

func (m *GetHospitalRequest) GetHospitalType() string {
	if m != nil {
		return m.HospitalType
	}
	return ""
}

func (m *GetHospitalRequest) GetLongitude() string {
	if m != nil {
		return m.Longitude
	}
	return ""
}

func (m *GetHospitalRequest) GetLatitude() string {
	if m != nil {
		return m.Latitude
	}
	return ""
}

//医院返回数据
type GetHospitalResponse struct {
	//总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	//列表数据
	List                 []*GetHospitalResponse_HospitalList `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetHospitalResponse) Reset()         { *m = GetHospitalResponse{} }
func (m *GetHospitalResponse) String() string { return proto.CompactTextString(m) }
func (*GetHospitalResponse) ProtoMessage()    {}
func (*GetHospitalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2809caf7c264a3e5, []int{1}
}

func (m *GetHospitalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHospitalResponse.Unmarshal(m, b)
}
func (m *GetHospitalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHospitalResponse.Marshal(b, m, deterministic)
}
func (m *GetHospitalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHospitalResponse.Merge(m, src)
}
func (m *GetHospitalResponse) XXX_Size() int {
	return xxx_messageInfo_GetHospitalResponse.Size(m)
}
func (m *GetHospitalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHospitalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHospitalResponse proto.InternalMessageInfo

func (m *GetHospitalResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetHospitalResponse) GetList() []*GetHospitalResponse_HospitalList {
	if m != nil {
		return m.List
	}
	return nil
}

//医院结构体
type GetHospitalResponse_HospitalList struct {
	//医院code
	HospitalCode string `protobuf:"bytes,1,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//医院名称
	HospitalName string `protobuf:"bytes,2,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	//医院简称
	HospitalShortName string `protobuf:"bytes,3,opt,name=hospital_short_name,json=hospitalShortName,proto3" json:"hospital_short_name"`
	//医院特长
	HospitalSpeciality string `protobuf:"bytes,4,opt,name=hospital_speciality,json=hospitalSpeciality,proto3" json:"hospital_speciality"`
	//医院标签
	TagName string `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	//医院地址
	HospitalAddress string `protobuf:"bytes,6,opt,name=hospital_address,json=hospitalAddress,proto3" json:"hospital_address"`
	//医院头像
	HospitalImg string `protobuf:"bytes,7,opt,name=hospital_img,json=hospitalImg,proto3" json:"hospital_img"`
	//医院电话
	HospitalPhone string `protobuf:"bytes,8,opt,name=hospital_phone,json=hospitalPhone,proto3" json:"hospital_phone"`
	//医院距离
	Distance string `protobuf:"bytes,9,opt,name=distance,proto3" json:"distance"`
	//医院坐标Longitude
	HospitalLongitude string `protobuf:"bytes,10,opt,name=hospital_longitude,json=hospitalLongitude,proto3" json:"hospital_longitude"`
	//医院坐标Latitude
	HospitalLatitude string `protobuf:"bytes,11,opt,name=hospital_latitude,json=hospitalLatitude,proto3" json:"hospital_latitude"`
	//医院类型 1中心医院，2专科医院，3全科医院
	HospitalType         int32    `protobuf:"varint,12,opt,name=hospital_type,json=hospitalType,proto3" json:"hospital_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHospitalResponse_HospitalList) Reset()         { *m = GetHospitalResponse_HospitalList{} }
func (m *GetHospitalResponse_HospitalList) String() string { return proto.CompactTextString(m) }
func (*GetHospitalResponse_HospitalList) ProtoMessage()    {}
func (*GetHospitalResponse_HospitalList) Descriptor() ([]byte, []int) {
	return fileDescriptor_2809caf7c264a3e5, []int{1, 0}
}

func (m *GetHospitalResponse_HospitalList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHospitalResponse_HospitalList.Unmarshal(m, b)
}
func (m *GetHospitalResponse_HospitalList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHospitalResponse_HospitalList.Marshal(b, m, deterministic)
}
func (m *GetHospitalResponse_HospitalList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHospitalResponse_HospitalList.Merge(m, src)
}
func (m *GetHospitalResponse_HospitalList) XXX_Size() int {
	return xxx_messageInfo_GetHospitalResponse_HospitalList.Size(m)
}
func (m *GetHospitalResponse_HospitalList) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHospitalResponse_HospitalList.DiscardUnknown(m)
}

var xxx_messageInfo_GetHospitalResponse_HospitalList proto.InternalMessageInfo

func (m *GetHospitalResponse_HospitalList) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalShortName() string {
	if m != nil {
		return m.HospitalShortName
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalSpeciality() string {
	if m != nil {
		return m.HospitalSpeciality
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalAddress() string {
	if m != nil {
		return m.HospitalAddress
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalImg() string {
	if m != nil {
		return m.HospitalImg
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalPhone() string {
	if m != nil {
		return m.HospitalPhone
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetDistance() string {
	if m != nil {
		return m.Distance
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalLongitude() string {
	if m != nil {
		return m.HospitalLongitude
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalLatitude() string {
	if m != nil {
		return m.HospitalLatitude
	}
	return ""
}

func (m *GetHospitalResponse_HospitalList) GetHospitalType() int32 {
	if m != nil {
		return m.HospitalType
	}
	return 0
}

func init() {
	proto.RegisterType((*GetHospitalRequest)(nil), "dgc.GetHospitalRequest")
	proto.RegisterType((*GetHospitalResponse)(nil), "dgc.GetHospitalResponse")
	proto.RegisterType((*GetHospitalResponse_HospitalList)(nil), "dgc.GetHospitalResponse.HospitalList")
}

func init() { proto.RegisterFile("dgc/hospital.proto", fileDescriptor_2809caf7c264a3e5) }

var fileDescriptor_2809caf7c264a3e5 = []byte{
	// 452 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x93, 0x5f, 0x8b, 0xd3, 0x40,
	0x14, 0xc5, 0xa9, 0xfd, 0xb3, 0xed, 0x6d, 0x75, 0xdd, 0xbb, 0x82, 0x63, 0x55, 0xa8, 0x2b, 0x0b,
	0x15, 0x31, 0x0b, 0xeb, 0x93, 0x6f, 0x8a, 0x0f, 0xba, 0x50, 0x44, 0x5a, 0xdf, 0xc3, 0x98, 0xb9,
	0xa4, 0x03, 0x69, 0x26, 0x76, 0xee, 0x8a, 0xdd, 0x2f, 0xe9, 0x8b, 0x9f, 0xc1, 0xcf, 0x21, 0x99,
	0xe9, 0x4c, 0x13, 0xd6, 0x7d, 0xbc, 0xe7, 0xfc, 0x32, 0xc9, 0x3d, 0x73, 0x02, 0xa8, 0xf2, 0xec,
	0x62, 0x6d, 0x6c, 0xa5, 0x59, 0x16, 0x49, 0xb5, 0x35, 0x6c, 0xb0, 0xab, 0xf2, 0xec, 0xec, 0x4f,
	0x07, 0xf0, 0x13, 0xf1, 0xe7, 0xbd, 0xb5, 0xa4, 0x1f, 0xd7, 0x64, 0x19, 0x9f, 0x03, 0x54, 0x32,
	0xa7, 0x54, 0x97, 0x8a, 0x7e, 0x89, 0xce, 0xac, 0x33, 0xef, 0x2f, 0x47, 0xb5, 0x72, 0x55, 0x0b,
	0xf8, 0x14, 0xdc, 0x90, 0x5a, 0x7d, 0x43, 0xe2, 0x9e, 0x73, 0x87, 0xb5, 0xb0, 0xd2, 0x37, 0x84,
	0x2f, 0xe1, 0x7e, 0x78, 0x53, 0x9a, 0x69, 0xde, 0x89, 0xee, 0xac, 0x33, 0x1f, 0x2d, 0x27, 0x41,
	0xfc, 0xa8, 0x79, 0xd7, 0x82, 0x78, 0x57, 0x91, 0xe8, 0xb5, 0xa1, 0x6f, 0xbb, 0x8a, 0xf0, 0x19,
	0x8c, 0x0a, 0x53, 0xe6, 0x9a, 0xaf, 0x15, 0x89, 0xbe, 0x03, 0x0e, 0x02, 0x4e, 0x61, 0x58, 0x48,
	0xf6, 0xe6, 0xc0, 0x99, 0x71, 0x3e, 0xfb, 0xdd, 0x83, 0xd3, 0xd6, 0x5a, 0xb6, 0x32, 0xa5, 0x25,
	0x7c, 0x04, 0x7d, 0x36, 0x2c, 0x8b, 0xfd, 0x4a, 0x7e, 0xc0, 0x77, 0xd0, 0x2b, 0xb4, 0x65, 0x31,
	0x98, 0x75, 0xe7, 0xe3, 0xcb, 0xf3, 0x44, 0xe5, 0x59, 0xf2, 0x9f, 0xa7, 0x93, 0x20, 0x2c, 0xb4,
	0xe5, 0xa5, 0x7b, 0x64, 0xfa, 0xb7, 0x0b, 0x93, 0xa6, 0xdc, 0xde, 0xde, 0x28, 0x72, 0x6f, 0x6a,
	0x6e, 0x6f, 0x54, 0x3b, 0xa2, 0x52, 0x6e, 0x7c, 0x86, 0x0d, 0xe8, 0x8b, 0xdc, 0x10, 0x26, 0x70,
	0x1a, 0x21, 0xbb, 0x36, 0x5b, 0xf6, 0xa8, 0x4f, 0xf3, 0x24, 0x58, 0xab, 0xda, 0x71, 0xfc, 0x45,
	0x93, 0xaf, 0x28, 0xd3, 0xb2, 0xa8, 0xd3, 0xf7, 0xc1, 0x62, 0xe4, 0xa3, 0x83, 0x4f, 0x60, 0xc8,
	0x32, 0xf7, 0xa7, 0xfa, 0x74, 0x8f, 0x58, 0xe6, 0xee, 0xac, 0x57, 0xf0, 0x30, 0x9e, 0x25, 0x95,
	0xda, 0x92, 0xb5, 0xfb, 0x8c, 0x8f, 0x83, 0xfe, 0xc1, 0xcb, 0xf8, 0x02, 0xe2, 0x67, 0xa7, 0x7a,
	0x93, 0x8b, 0x23, 0x87, 0x8d, 0x83, 0x76, 0xb5, 0xc9, 0xf1, 0x1c, 0x1e, 0x44, 0xa4, 0x5a, 0x9b,
	0x92, 0xc4, 0xd0, 0x41, 0x31, 0x84, 0xaf, 0xb5, 0x58, 0x5f, 0xa8, 0xd2, 0x96, 0x65, 0x99, 0x91,
	0x18, 0xf9, 0x0b, 0x0d, 0x33, 0xbe, 0x81, 0xb8, 0x41, 0x7a, 0xe8, 0x04, 0xb4, 0xb3, 0x58, 0xc4,
	0x6e, 0xbc, 0x86, 0x93, 0x03, 0x1e, 0x4a, 0x32, 0x76, 0x74, 0x5c, 0x6c, 0xb1, 0xd7, 0x6f, 0x77,
	0x71, 0xe2, 0xca, 0xd1, 0xea, 0xe2, 0xe5, 0x0a, 0x8e, 0xc3, 0x3d, 0xaf, 0x68, 0xfb, 0x53, 0x67,
	0x84, 0xef, 0x61, 0xdc, 0x68, 0x09, 0x3e, 0xbe, 0xdd, 0x1b, 0xf7, 0x33, 0x4d, 0xc5, 0x5d, 0x85,
	0xfa, 0x3e, 0x70, 0x7f, 0xe2, 0xdb, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x07, 0xb0, 0x65, 0x44,
	0x9f, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// HospitalServiceClient is the client API for HospitalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type HospitalServiceClient interface {
	// @Desc    获取医院列表
	// <AUTHOR>
	// @Date		2021-10-12
	GetHospital(ctx context.Context, in *GetHospitalRequest, opts ...grpc.CallOption) (*GetHospitalResponse, error)
}

type hospitalServiceClient struct {
	cc *grpc.ClientConn
}

func NewHospitalServiceClient(cc *grpc.ClientConn) HospitalServiceClient {
	return &hospitalServiceClient{cc}
}

func (c *hospitalServiceClient) GetHospital(ctx context.Context, in *GetHospitalRequest, opts ...grpc.CallOption) (*GetHospitalResponse, error) {
	out := new(GetHospitalResponse)
	err := c.cc.Invoke(ctx, "/dgc.HospitalService/GetHospital", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HospitalServiceServer is the server API for HospitalService service.
type HospitalServiceServer interface {
	// @Desc    获取医院列表
	// <AUTHOR>
	// @Date		2021-10-12
	GetHospital(context.Context, *GetHospitalRequest) (*GetHospitalResponse, error)
}

// UnimplementedHospitalServiceServer can be embedded to have forward compatible implementations.
type UnimplementedHospitalServiceServer struct {
}

func (*UnimplementedHospitalServiceServer) GetHospital(ctx context.Context, req *GetHospitalRequest) (*GetHospitalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHospital not implemented")
}

func RegisterHospitalServiceServer(s *grpc.Server, srv HospitalServiceServer) {
	s.RegisterService(&_HospitalService_serviceDesc, srv)
}

func _HospitalService_GetHospital_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHospitalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HospitalServiceServer).GetHospital(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.HospitalService/GetHospital",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HospitalServiceServer).GetHospital(ctx, req.(*GetHospitalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _HospitalService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.HospitalService",
	HandlerType: (*HospitalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHospital",
			Handler:    _HospitalService_GetHospital_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/hospital.proto",
}

package services

import (
	"errors"
	"order-center/models"
	"order-center/proto/et"
	"order-center/proto/oc"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type channelJddjOrder struct {
	order      *models.Order
	oldOrderSn string
	etClient   *et.Client
}

func (c channelJddjOrder) PickOrder() (err error) {
	//自配
	/*	if c.order.PushThirdOrder != 1 {
		return errors.New("推送子龙或全渠道失败，不可通知京东到家拣货完成")
	}*/

	if c.order.OrderType != 2 {
		if c.order.LogisticsCode == "2938" {
			storeMasterId := c.order.AppChannel
			//发配送前调用拣货完成且商家自送接口
			err = JddjOrderSerllerDelivery(c.oldOrderSn, storeMasterId)
			if err != nil {
				glog.Error("手动拣货推送京东到家拣货完成且商家自送接口失败！", c.order.OrderSn, err.Error())
			}

		}
		//京东众包
		if c.order.LogisticsCode == "9966" {
			storeMasterId := c.order.AppChannel
			err = JddjOrderJDZBDelivery(c.oldOrderSn, storeMasterId)
			if err != nil {
				glog.Error("手动拣货推送京东到家拣货完成且众包配送接口失败！", "外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, err.Error())
			}
		}

	}

	if c.order.DeliveryType == 3 && c.order.OrderType == 2 {
		//拣货完成且顾客自提接口
		if c.order.LogisticsCode == "9999" {
			storeMasterId := c.order.AppChannel
			err = JddjOrderSelfMention(c.oldOrderSn, storeMasterId)
			if err != nil {
				glog.Error("手动拣货推送京东到家拣货完成且顾客自提接口失败！", "外部单号: "+c.oldOrderSn+" 订单号: "+c.order.OrderSn, err.Error())
			}

		}
	}

	return
}

func (c channelJddjOrder) SyncDeliveryNode(params *oc.DeliveryNodeRequest) (err error) {
	//京东到家只通知订单送达
	if params.Status != 50 {
		return
	}

	var res *et.JddjBaseResponse
	res, err = c.etClient.JddjOrder.JddjDeliveryEndOrder(c.etClient.Ctx, &et.JddjOrderSerllerDeliveryRequest{
		OrderId:       c.oldOrderSn,
		StoreMasterId: c.order.AppChannel,
	})
	if err != nil {
		glog.Error(c.oldOrderSn, ", 通知京东到家订单送达错误, ", err.Error())
	} else if res.Code != 200 {
		glog.Error(c.oldOrderSn, ", 通知京东到家订单送达错误, ", kit.JsonEncode(res))
		err = errors.New(res.Message)
	} else {
		glog.Info(c.oldOrderSn, ", 通知京东到家订单送达成功")
	}

	return
}

func (c channelJddjOrder) CancelOrder(params *oc.CancelAcceptOrderRequest) (err error) {
	if len(params.OperationUser) == 0 {
		params.OperationUser = "RP"
	}

	//todo 取消订单为什么要接单？
	//if c.order.OrderStatus == 20 && c.order.OrderStatusChild == 20101 {
	//	var res *et.JddjOrderConfirmlResponse
	//	res, err = c.etClient.JddjOrder.JddjOrderAcceptOperate(c.etClient.Ctx, &et.JddjOrderConfirmRequest{
	//		OrderId:  c.order.oldOrderSn,
	//		IsAgreed: false,
	//	})
	//	if err != nil {
	//		glog.Error(c.order.oldOrderSn, ", 调用京东到家接单接口失败,", err)
	//	}
	//	if res.Code != 200 {
	//		err = errors.New(res.Error + "-" + res.Message)
	//		glog.Error(c.order.oldOrderSn, ", 调用京东到家接单接口失败,", kit.JsonEncode(res))
	//	}
	//} else {
	var res *et.JddjBaseResponse
	res, err = c.etClient.JddjOrder.JddjOrderCancelAndRefund(c.etClient.Ctx, &et.JddjOrderCancelRequest{
		OrderId:       cast.ToInt64(c.oldOrderSn),
		OperPin:       params.OperationUser,
		OperRemark:    params.Reason,
		StoreMasterId: c.order.AppChannel,
	})

	if err != nil {
		glog.Error(c.oldOrderSn, ", 调用京东到家取消订单接口失败,", err)
	} else if res.Code != 200 {
		err = errors.New(res.Message)
		glog.Error(c.oldOrderSn, ", 调用京东到家取消订单接口失败,", kit.JsonEncode(res))
	}
	//}

	return
}

func (c channelJddjOrder) ApplyPartRefund(params *oc.OrderApplyPartRefundRequest) (err error) {

	if c.order.OrderStatus != 30 {
		err = errors.New("订单未完成，商家不允许发起退款")
		return
	}

	jdRefundOrder := &et.JddjMerchantInitiateAfterSaleRequest{
		OrderId:          c.oldOrderSn,
		Pin:              params.OperationUser,
		QuestionTypeCode: params.RefundCode, //201:商家通知我缺货
		QuestionDesc:     params.Reason,
		StoreMasterId:    c.order.AppChannel,
	}

	for _, i2 := range params.FoodData {
		sku := et.JddVenderAfsSkuDto{
			SkuId:         cast.ToInt64(i2.SubBizOrderId), //skuid 需要用京东的skuId
			SkuCount:      int32(i2.Count),
			PromotionType: i2.PromotionType,
		}
		jdRefundOrder.SkuList = append(jdRefundOrder.SkuList, &sku)
	}

	glog.Info(c.oldOrderSn, ", 调用京东到家发起部分退款接口入参, ", kit.JsonEncode(jdRefundOrder))

	var res *et.JddjBaseResponse
	res, err = c.etClient.JddjOrder.JddjMerchantInitiateAfterSale(c.etClient.Ctx, jdRefundOrder)
	if err != nil {
		glog.Error(c.oldOrderSn, ", 调用京东到家发起部分退款接口错误, ", err)
	} else if res.Code != 200 {
		err = errors.New(res.Message)
		glog.Error(c.oldOrderSn, ", 调用京东到家发起部分退款接口错误, ", kit.JsonEncode(res))
	} else {
		glog.Info(c.oldOrderSn, ", 调用京东到家发起部分退款接口成功 ", kit.JsonEncode(res))
	}

	return
}

func (c channelJddjOrder) AgreeRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	//初审通过，确认收到用户退回来的商品时候调用
	if refundOrder.RefundState == 6 {
		afs := &et.JddjConfirmReceiptRequest{
			Pin:           params.Operationer,
			StoreMasterId: c.order.AppChannel,
		}
		//由用户发起的售后单是有第三方退款单号
		if len(refundOrder.OldRefundSn) > 0 {
			afs.AfsServiceOrder = refundOrder.OldRefundSn
		} else {
			afs.AfsServiceOrder = params.Refundsn
		}

		var res *et.JddjBaseResponse
		res, err = c.etClient.JddjOrder.JddjConfirmReceipt(c.etClient.Ctx, afs)
		glog.Info("京东订单确认退款请求申请返回参数:"+params.Refundsn, kit.JsonEncode(res))

		if err != nil {
			glog.Error(c.oldOrderSn, "调用京东到家确认收货接口失败, ", err)
		} else if res.Code != 200 {
			err = errors.New(res.Message)
			glog.Error(c.oldOrderSn, "调用京东到家确认收货接口失败, ", kit.JsonEncode(res), err)
		}
	} else {
		if refundOrder.IsCancelOrder == 1 {
			var res *et.JddjBaseResponse
			res, err = c.etClient.JddjOrder.JddjOrderCancelOperate(c.etClient.Ctx, &et.JddjOrderCancelOperateRequest{
				OrderId:       c.oldOrderSn,
				IsAgreed:      true,
				Operator:      params.Operationer,
				StoreMasterId: c.order.AppChannel,
			})
			if err != nil {
				glog.Error(c.oldOrderSn, "调用京东到家审核用户取消申请接口失败, ", err)
			} else if res.Code != 200 {
				err = errors.New(res.Message)
				glog.Error(c.oldOrderSn, "调用京东到家审核用户取消申请接口失败, ", kit.JsonEncode(res), err)
			} else {
				glog.Info(c.oldOrderSn, "调用京东到家审核用户取消申请接口成功, ", kit.JsonEncode(res))
			}
		} else {
			inpar := et.AfsOpenApproveRequest{
				ServiceOrder:  params.Refundsn,
				ApproveType:   refundOrder.RefundType,
				OptPin:        params.Operationer,
				StoreMasterId: c.order.AppChannel,
			}
			//由用户发起的售后单是有第三方退款单号
			if len(refundOrder.OldRefundSn) > 0 {
				inpar.ServiceOrder = refundOrder.OldRefundSn
			} else {
				inpar.ServiceOrder = params.Refundsn
			}
			if len(inpar.OptPin) == 0 {
				inpar.OptPin = "RP"
			}
			var res *et.JddjBaseResponse
			res, err = c.etClient.JddjOrder.JddjAfsOpenApprove(c.etClient.Ctx, &inpar)
			if err != nil {
				glog.Error(c.oldOrderSn, "调用京东到家申请售后单审核接口失败, ", err)
			} else if res.Code != 200 {
				err = errors.New(res.Message)
				glog.Error(c.oldOrderSn, "调用京东到家申请售后单审核接口失败, ", kit.JsonEncode(res))
			}

			return
		}
	}

	var res *oc.BaseResponse
	res, err = (&RefundOrderService{}).RefundOrderAnswer(nil, &oc.RefundOrderAnswerRequest{
		OrderId:         params.OrderId,
		ExternalOrderId: params.OrderId,
		RefundOrderSn:   params.Refundsn,
		Reason:          params.Reason,
		ResultType:      1,
		ResultTypeNote:  "商家同意退款",
		OperationType:   "商家同意退款",
		OperationUser:   params.Operationer,
	})
	if err != nil {
		glog.Error(c.oldOrderSn, ", ", params.Refundsn, ", 售后单申请返回错误, ", err)
	} else if res.Code != 200 {
		err = errors.New(res.Error + "-" + res.Message)
		glog.Error(c.oldOrderSn, ", ", params.Refundsn, ", 售后单申请返回错误, ", kit.JsonEncode(res))
	} else {
		glog.Info(c.oldOrderSn, ", ", params.Refundsn, ", 售后单申请返回成功 ", kit.JsonEncode(res))
	}

	return
}

func (c channelJddjOrder) RejectRefund(params *oc.MtOrderRefundRequest, refundOrder *models.RefundOrder) (err error) {
	return
}

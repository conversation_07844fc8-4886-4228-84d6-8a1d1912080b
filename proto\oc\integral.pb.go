// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/integral.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// @Desc    积分查询请求
// <AUTHOR>
// @Date	2020-08-18
type AwIntegralQueryRequest struct {
	StartTime            string   `protobuf:"bytes,1,opt,name=StartTime,proto3" json:"StartTime"`
	EndTime              string   `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwIntegralQueryRequest) Reset()         { *m = AwIntegralQueryRequest{} }
func (m *AwIntegralQueryRequest) String() string { return proto.CompactTextString(m) }
func (*AwIntegralQueryRequest) ProtoMessage()    {}
func (*AwIntegralQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{0}
}

func (m *AwIntegralQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwIntegralQueryRequest.Unmarshal(m, b)
}
func (m *AwIntegralQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwIntegralQueryRequest.Marshal(b, m, deterministic)
}
func (m *AwIntegralQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwIntegralQueryRequest.Merge(m, src)
}
func (m *AwIntegralQueryRequest) XXX_Size() int {
	return xxx_messageInfo_AwIntegralQueryRequest.Size(m)
}
func (m *AwIntegralQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AwIntegralQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AwIntegralQueryRequest proto.InternalMessageInfo

func (m *AwIntegralQueryRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *AwIntegralQueryRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

// @Desc   	阿闻实物订单积分请求
// <AUTHOR>
// @Date	2020-08-18
type AwOrderIntegralRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=OrderSn,proto3" json:"OrderSn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwOrderIntegralRequest) Reset()         { *m = AwOrderIntegralRequest{} }
func (m *AwOrderIntegralRequest) String() string { return proto.CompactTextString(m) }
func (*AwOrderIntegralRequest) ProtoMessage()    {}
func (*AwOrderIntegralRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{1}
}

func (m *AwOrderIntegralRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwOrderIntegralRequest.Unmarshal(m, b)
}
func (m *AwOrderIntegralRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwOrderIntegralRequest.Marshal(b, m, deterministic)
}
func (m *AwOrderIntegralRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwOrderIntegralRequest.Merge(m, src)
}
func (m *AwOrderIntegralRequest) XXX_Size() int {
	return xxx_messageInfo_AwOrderIntegralRequest.Size(m)
}
func (m *AwOrderIntegralRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AwOrderIntegralRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AwOrderIntegralRequest proto.InternalMessageInfo

func (m *AwOrderIntegralRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

// @Desc    获取阿闻订单所属部门请求
// <AUTHOR>
// @Date	2020-08-18
type AwOrderStoreRequest struct {
	StoreId              string   `protobuf:"bytes,1,opt,name=StoreId,proto3" json:"StoreId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwOrderStoreRequest) Reset()         { *m = AwOrderStoreRequest{} }
func (m *AwOrderStoreRequest) String() string { return proto.CompactTextString(m) }
func (*AwOrderStoreRequest) ProtoMessage()    {}
func (*AwOrderStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{2}
}

func (m *AwOrderStoreRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwOrderStoreRequest.Unmarshal(m, b)
}
func (m *AwOrderStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwOrderStoreRequest.Marshal(b, m, deterministic)
}
func (m *AwOrderStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwOrderStoreRequest.Merge(m, src)
}
func (m *AwOrderStoreRequest) XXX_Size() int {
	return xxx_messageInfo_AwOrderStoreRequest.Size(m)
}
func (m *AwOrderStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AwOrderStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AwOrderStoreRequest proto.InternalMessageInfo

func (m *AwOrderStoreRequest) GetStoreId() string {
	if m != nil {
		return m.StoreId
	}
	return ""
}

// @Desc    根据城市查询城市编号
// <AUTHOR>
// @Date	2020-08-18
type GetStoreCodeRequest struct {
	CityName             string   `protobuf:"bytes,1,opt,name=CityName,proto3" json:"CityName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoreCodeRequest) Reset()         { *m = GetStoreCodeRequest{} }
func (m *GetStoreCodeRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoreCodeRequest) ProtoMessage()    {}
func (*GetStoreCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{3}
}

func (m *GetStoreCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoreCodeRequest.Unmarshal(m, b)
}
func (m *GetStoreCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoreCodeRequest.Marshal(b, m, deterministic)
}
func (m *GetStoreCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreCodeRequest.Merge(m, src)
}
func (m *GetStoreCodeRequest) XXX_Size() int {
	return xxx_messageInfo_GetStoreCodeRequest.Size(m)
}
func (m *GetStoreCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreCodeRequest proto.InternalMessageInfo

func (m *GetStoreCodeRequest) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

// @Desc    阿闻积分请求结果响应
// <AUTHOR>
// @Date	2020-08-18
type AwIntegralResultResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Jsondata             string   `protobuf:"bytes,3,opt,name=jsondata,proto3" json:"jsondata"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwIntegralResultResponse) Reset()         { *m = AwIntegralResultResponse{} }
func (m *AwIntegralResultResponse) String() string { return proto.CompactTextString(m) }
func (*AwIntegralResultResponse) ProtoMessage()    {}
func (*AwIntegralResultResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{4}
}

func (m *AwIntegralResultResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwIntegralResultResponse.Unmarshal(m, b)
}
func (m *AwIntegralResultResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwIntegralResultResponse.Marshal(b, m, deterministic)
}
func (m *AwIntegralResultResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwIntegralResultResponse.Merge(m, src)
}
func (m *AwIntegralResultResponse) XXX_Size() int {
	return xxx_messageInfo_AwIntegralResultResponse.Size(m)
}
func (m *AwIntegralResultResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AwIntegralResultResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AwIntegralResultResponse proto.InternalMessageInfo

func (m *AwIntegralResultResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AwIntegralResultResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AwIntegralResultResponse) GetJsondata() string {
	if m != nil {
		return m.Jsondata
	}
	return ""
}

//通用返回
type BaseRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRes) Reset()         { *m = BaseRes{} }
func (m *BaseRes) String() string { return proto.CompactTextString(m) }
func (*BaseRes) ProtoMessage()    {}
func (*BaseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{5}
}

func (m *BaseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRes.Unmarshal(m, b)
}
func (m *BaseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRes.Marshal(b, m, deterministic)
}
func (m *BaseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRes.Merge(m, src)
}
func (m *BaseRes) XXX_Size() int {
	return xxx_messageInfo_BaseRes.Size(m)
}
func (m *BaseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRes.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRes proto.InternalMessageInfo

func (m *BaseRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type RecoverUserIntegralReq struct {
	OrderSn       string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	MemberId      string `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	RefundOrderSn string `protobuf:"bytes,3,opt,name=refund_order_sn,json=refundOrderSn,proto3" json:"refund_order_sn"`
	//主体ID
	OrgId                int32    `protobuf:"varint,4,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserIntegralReq) Reset()         { *m = RecoverUserIntegralReq{} }
func (m *RecoverUserIntegralReq) String() string { return proto.CompactTextString(m) }
func (*RecoverUserIntegralReq) ProtoMessage()    {}
func (*RecoverUserIntegralReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{6}
}

func (m *RecoverUserIntegralReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserIntegralReq.Unmarshal(m, b)
}
func (m *RecoverUserIntegralReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserIntegralReq.Marshal(b, m, deterministic)
}
func (m *RecoverUserIntegralReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserIntegralReq.Merge(m, src)
}
func (m *RecoverUserIntegralReq) XXX_Size() int {
	return xxx_messageInfo_RecoverUserIntegralReq.Size(m)
}
func (m *RecoverUserIntegralReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserIntegralReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserIntegralReq proto.InternalMessageInfo

func (m *RecoverUserIntegralReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *RecoverUserIntegralReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *RecoverUserIntegralReq) GetRefundOrderSn() string {
	if m != nil {
		return m.RefundOrderSn
	}
	return ""
}

func (m *RecoverUserIntegralReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GetIntegralListByMemberIdReq struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	IntegralType         int32    `protobuf:"varint,3,opt,name=integral_type,json=integralType,proto3" json:"integral_type"`
	MemberId             string   `protobuf:"bytes,4,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIntegralListByMemberIdReq) Reset()         { *m = GetIntegralListByMemberIdReq{} }
func (m *GetIntegralListByMemberIdReq) String() string { return proto.CompactTextString(m) }
func (*GetIntegralListByMemberIdReq) ProtoMessage()    {}
func (*GetIntegralListByMemberIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{7}
}

func (m *GetIntegralListByMemberIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntegralListByMemberIdReq.Unmarshal(m, b)
}
func (m *GetIntegralListByMemberIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntegralListByMemberIdReq.Marshal(b, m, deterministic)
}
func (m *GetIntegralListByMemberIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntegralListByMemberIdReq.Merge(m, src)
}
func (m *GetIntegralListByMemberIdReq) XXX_Size() int {
	return xxx_messageInfo_GetIntegralListByMemberIdReq.Size(m)
}
func (m *GetIntegralListByMemberIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntegralListByMemberIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntegralListByMemberIdReq proto.InternalMessageInfo

func (m *GetIntegralListByMemberIdReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetIntegralListByMemberIdReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetIntegralListByMemberIdReq) GetIntegralType() int32 {
	if m != nil {
		return m.IntegralType
	}
	return 0
}

func (m *GetIntegralListByMemberIdReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *GetIntegralListByMemberIdReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GetIntegralListByMemberIdRes struct {
	Code                 int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	IntegralTotal        int64           `protobuf:"varint,3,opt,name=integral_total,json=integralTotal,proto3" json:"integral_total"`
	IntegralExpiredTotal int64           `protobuf:"varint,4,opt,name=integral_expired_total,json=integralExpiredTotal,proto3" json:"integral_expired_total"`
	IntegralListCount    int64           `protobuf:"varint,5,opt,name=integral_list_count,json=integralListCount,proto3" json:"integral_list_count"`
	IntegralList         []*IntegralList `protobuf:"bytes,6,rep,name=integral_list,json=integralList,proto3" json:"integral_list"`
	IntegralExpiredDate  string          `protobuf:"bytes,7,opt,name=integral_expired_date,json=integralExpiredDate,proto3" json:"integral_expired_date"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetIntegralListByMemberIdRes) Reset()         { *m = GetIntegralListByMemberIdRes{} }
func (m *GetIntegralListByMemberIdRes) String() string { return proto.CompactTextString(m) }
func (*GetIntegralListByMemberIdRes) ProtoMessage()    {}
func (*GetIntegralListByMemberIdRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{8}
}

func (m *GetIntegralListByMemberIdRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntegralListByMemberIdRes.Unmarshal(m, b)
}
func (m *GetIntegralListByMemberIdRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntegralListByMemberIdRes.Marshal(b, m, deterministic)
}
func (m *GetIntegralListByMemberIdRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntegralListByMemberIdRes.Merge(m, src)
}
func (m *GetIntegralListByMemberIdRes) XXX_Size() int {
	return xxx_messageInfo_GetIntegralListByMemberIdRes.Size(m)
}
func (m *GetIntegralListByMemberIdRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntegralListByMemberIdRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntegralListByMemberIdRes proto.InternalMessageInfo

func (m *GetIntegralListByMemberIdRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetIntegralListByMemberIdRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetIntegralListByMemberIdRes) GetIntegralTotal() int64 {
	if m != nil {
		return m.IntegralTotal
	}
	return 0
}

func (m *GetIntegralListByMemberIdRes) GetIntegralExpiredTotal() int64 {
	if m != nil {
		return m.IntegralExpiredTotal
	}
	return 0
}

func (m *GetIntegralListByMemberIdRes) GetIntegralListCount() int64 {
	if m != nil {
		return m.IntegralListCount
	}
	return 0
}

func (m *GetIntegralListByMemberIdRes) GetIntegralList() []*IntegralList {
	if m != nil {
		return m.IntegralList
	}
	return nil
}

func (m *GetIntegralListByMemberIdRes) GetIntegralExpiredDate() string {
	if m != nil {
		return m.IntegralExpiredDate
	}
	return ""
}

type IntegralList struct {
	IntegralName         string   `protobuf:"bytes,1,opt,name=integral_name,json=integralName,proto3" json:"integral_name"`
	IntegralCount        int64    `protobuf:"varint,2,opt,name=integral_count,json=integralCount,proto3" json:"integral_count"`
	IntegralDate         string   `protobuf:"bytes,3,opt,name=integral_date,json=integralDate,proto3" json:"integral_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntegralList) Reset()         { *m = IntegralList{} }
func (m *IntegralList) String() string { return proto.CompactTextString(m) }
func (*IntegralList) ProtoMessage()    {}
func (*IntegralList) Descriptor() ([]byte, []int) {
	return fileDescriptor_253962f68e8a392b, []int{9}
}

func (m *IntegralList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntegralList.Unmarshal(m, b)
}
func (m *IntegralList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntegralList.Marshal(b, m, deterministic)
}
func (m *IntegralList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntegralList.Merge(m, src)
}
func (m *IntegralList) XXX_Size() int {
	return xxx_messageInfo_IntegralList.Size(m)
}
func (m *IntegralList) XXX_DiscardUnknown() {
	xxx_messageInfo_IntegralList.DiscardUnknown(m)
}

var xxx_messageInfo_IntegralList proto.InternalMessageInfo

func (m *IntegralList) GetIntegralName() string {
	if m != nil {
		return m.IntegralName
	}
	return ""
}

func (m *IntegralList) GetIntegralCount() int64 {
	if m != nil {
		return m.IntegralCount
	}
	return 0
}

func (m *IntegralList) GetIntegralDate() string {
	if m != nil {
		return m.IntegralDate
	}
	return ""
}

func init() {
	proto.RegisterType((*AwIntegralQueryRequest)(nil), "oc.AwIntegralQueryRequest")
	proto.RegisterType((*AwOrderIntegralRequest)(nil), "oc.AwOrderIntegralRequest")
	proto.RegisterType((*AwOrderStoreRequest)(nil), "oc.AwOrderStoreRequest")
	proto.RegisterType((*GetStoreCodeRequest)(nil), "oc.GetStoreCodeRequest")
	proto.RegisterType((*AwIntegralResultResponse)(nil), "oc.AwIntegralResultResponse")
	proto.RegisterType((*BaseRes)(nil), "oc.BaseRes")
	proto.RegisterType((*RecoverUserIntegralReq)(nil), "oc.RecoverUserIntegralReq")
	proto.RegisterType((*GetIntegralListByMemberIdReq)(nil), "oc.GetIntegralListByMemberIdReq")
	proto.RegisterType((*GetIntegralListByMemberIdRes)(nil), "oc.GetIntegralListByMemberIdRes")
	proto.RegisterType((*IntegralList)(nil), "oc.IntegralList")
}

func init() { proto.RegisterFile("oc/integral.proto", fileDescriptor_253962f68e8a392b) }

var fileDescriptor_253962f68e8a392b = []byte{
	// 631 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xdd, 0x52, 0x13, 0x4d,
	0x10, 0x25, 0x7f, 0x24, 0x69, 0xe0, 0xe3, 0x63, 0x02, 0xb8, 0x44, 0xac, 0xa2, 0xc6, 0xd2, 0xe2,
	0x2a, 0x94, 0x51, 0xef, 0x15, 0xa4, 0xa8, 0x54, 0x89, 0xe8, 0x06, 0xaf, 0x53, 0xc3, 0x4e, 0x9b,
	0x1a, 0x2b, 0xd9, 0x89, 0x33, 0x13, 0x24, 0xdc, 0xf8, 0x06, 0xbe, 0x8c, 0xaf, 0xe0, 0x7b, 0x69,
	0xcd, 0xcf, 0x6e, 0x36, 0x08, 0x5a, 0xdc, 0xed, 0xe9, 0x9e, 0x3e, 0x7d, 0x4e, 0xef, 0x4c, 0xc3,
	0x86, 0x4c, 0x0e, 0x44, 0x6a, 0x70, 0xa8, 0xd8, 0xa8, 0x33, 0x51, 0xd2, 0x48, 0x52, 0x96, 0x09,
	0x7d, 0x0f, 0xdb, 0xaf, 0xbf, 0xf6, 0x42, 0xfc, 0xc3, 0x14, 0xd5, 0x2c, 0xc6, 0x2f, 0x53, 0xd4,
	0x86, 0xec, 0x42, 0xb3, 0x6f, 0x98, 0x32, 0xe7, 0x62, 0x8c, 0x51, 0x69, 0xaf, 0xb4, 0xdf, 0x8c,
	0xe7, 0x01, 0x12, 0x41, 0xfd, 0x38, 0xe5, 0x2e, 0x57, 0x76, 0xb9, 0x0c, 0xd2, 0xae, 0x65, 0x3c,
	0x53, 0x1c, 0x55, 0x46, 0x9b, 0x31, 0x46, 0x50, 0x77, 0xf1, 0x7e, 0x1a, 0xf8, 0x32, 0x48, 0x0f,
	0xa0, 0x15, 0x6a, 0xfa, 0x46, 0x2a, 0x2c, 0x14, 0x38, 0xdc, 0xe3, 0x59, 0x41, 0x80, 0xf4, 0x19,
	0xb4, 0x4e, 0xd0, 0x38, 0x74, 0x24, 0x79, 0x5e, 0xd0, 0x86, 0xc6, 0x91, 0x30, 0xb3, 0x77, 0x2c,
	0x97, 0x9c, 0x63, 0xca, 0x21, 0x9a, 0x3b, 0x8d, 0x51, 0x4f, 0x47, 0x26, 0x46, 0x3d, 0x91, 0xa9,
	0x46, 0x42, 0xa0, 0x9a, 0x48, 0xee, 0x6b, 0x6a, 0xb1, 0xfb, 0xb6, 0xcd, 0xc7, 0xa8, 0x35, 0x1b,
	0xe6, 0x0e, 0x03, 0xb4, 0x5d, 0x3e, 0x6b, 0x99, 0x72, 0x66, 0x58, 0x54, 0xf1, 0x5d, 0x32, 0x4c,
	0x4f, 0xa1, 0x7e, 0xc8, 0x34, 0xc6, 0xa8, 0xef, 0x49, 0xba, 0x09, 0x35, 0x54, 0x4a, 0xaa, 0xc0,
	0xe8, 0x01, 0xfd, 0x5e, 0x82, 0xed, 0x18, 0x13, 0x79, 0x89, 0xea, 0xa3, 0x5e, 0x98, 0x28, 0xd9,
	0x81, 0x86, 0xb4, 0x13, 0x1b, 0xe8, 0x7c, 0x9c, 0xd2, 0x8f, 0x93, 0x3c, 0x84, 0xe6, 0x18, 0xc7,
	0x17, 0xa8, 0x06, 0x82, 0x87, 0x3e, 0x0d, 0x1f, 0xe8, 0x71, 0xf2, 0x14, 0xd6, 0x15, 0x7e, 0x9a,
	0xa6, 0x7c, 0x90, 0x97, 0xfb, 0x96, 0x6b, 0x3e, 0x1c, 0xfe, 0x89, 0x15, 0x74, 0xa6, 0x86, 0x3d,
	0x1e, 0x55, 0x9d, 0x7e, 0x0f, 0xe8, 0x8f, 0x12, 0xec, 0x9e, 0xa0, 0xc9, 0x84, 0xbc, 0x15, 0xda,
	0x1c, 0xce, 0x4e, 0x03, 0xb7, 0x95, 0xf5, 0x08, 0x60, 0xc2, 0x86, 0x38, 0x10, 0x29, 0xc7, 0xab,
	0xe0, 0xbd, 0x69, 0x23, 0x3d, 0x1b, 0xb0, 0xd2, 0x5c, 0x5a, 0x8b, 0x6b, 0x3f, 0x82, 0x5a, 0xdc,
	0xb0, 0x81, 0xbe, 0xb8, 0x46, 0xf2, 0x18, 0xd6, 0xb2, 0x2b, 0x3a, 0x30, 0xb3, 0x09, 0x3a, 0x61,
	0xb5, 0x78, 0x35, 0x0b, 0x9e, 0xcf, 0x26, 0xb8, 0x68, 0xae, 0x7a, 0xc3, 0xdc, 0x16, 0x2c, 0x4b,
	0x35, 0xb4, 0x99, 0x9a, 0x57, 0x2d, 0x9d, 0xea, 0x9f, 0xe5, 0xbf, 0xaa, 0xbe, 0xef, 0xbf, 0x7a,
	0x02, 0xff, 0xcd, 0x75, 0x4a, 0xc3, 0x46, 0x4e, 0x68, 0x25, 0xce, 0xd5, 0x9f, 0xdb, 0x20, 0x79,
	0x01, 0xdb, 0xf9, 0x31, 0xbc, 0x9a, 0x08, 0x85, 0x3c, 0x1c, 0xaf, 0xba, 0xe3, 0x9b, 0x59, 0xf6,
	0xd8, 0x27, 0x7d, 0x55, 0x07, 0x5a, 0x79, 0xd5, 0x48, 0x68, 0x33, 0x48, 0xe4, 0x34, 0x35, 0xce,
	0x4f, 0x25, 0xde, 0x10, 0x05, 0x0b, 0x47, 0x36, 0x41, 0x5e, 0x16, 0x86, 0x66, 0xcf, 0x47, 0xcb,
	0x7b, 0x95, 0xfd, 0x95, 0xee, 0xff, 0x1d, 0x99, 0x74, 0x8a, 0x86, 0xe7, 0x63, 0xb4, 0x88, 0x74,
	0x61, 0xeb, 0x0f, 0x71, 0x9c, 0x19, 0x8c, 0xea, 0xce, 0x6b, 0xeb, 0x86, 0xb6, 0x37, 0xcc, 0x20,
	0xfd, 0x06, 0xab, 0x45, 0xc6, 0x85, 0xff, 0x95, 0xce, 0xdf, 0x5c, 0xde, 0xc8, 0xbe, 0xbb, 0x85,
	0x61, 0x79, 0x2b, 0xe5, 0xc5, 0x61, 0x79, 0x1b, 0x45, 0x2e, 0xa7, 0xa3, 0xb2, 0xc8, 0x65, 0x05,
	0x74, 0x7f, 0x95, 0x60, 0x3d, 0x53, 0xd0, 0x47, 0x75, 0x29, 0x12, 0x24, 0x27, 0xb0, 0x5a, 0x5c,
	0x05, 0xe4, 0x81, 0x35, 0x7e, 0xcb, 0x72, 0x68, 0xef, 0xda, 0xc4, 0x5d, 0x2b, 0x80, 0x2e, 0x11,
	0x06, 0x3b, 0x77, 0xde, 0x11, 0xb2, 0x17, 0x58, 0xef, 0xbc, 0xf8, 0xed, 0x7f, 0x9d, 0xd0, 0x74,
	0x89, 0xbc, 0x82, 0xd6, 0x2d, 0xaf, 0x99, 0xb4, 0x6d, 0xe9, 0xed, 0xcf, 0xbc, 0xbd, 0x62, 0x73,
	0x61, 0xa5, 0xd0, 0xa5, 0x8b, 0x65, 0xb7, 0xba, 0x9f, 0xff, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x1d,
	0xcc, 0x39, 0x43, 0xcf, 0x05, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IntegralServiceClient is the client API for IntegralService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IntegralServiceClient interface {
	// @Desc      根据城市查询城市编号
	// <AUTHOR>
	// @Date      2020-08-18
	GetStoreCode(ctx context.Context, in *GetStoreCodeRequest, opts ...grpc.CallOption) (*AwIntegralResultResponse, error)
	// @Desc      根据用户id获取用户的积分明细
	// <AUTHOR>
	// @Date      2021-07-21
	GetIntegralListByMemberId(ctx context.Context, in *GetIntegralListByMemberIdReq, opts ...grpc.CallOption) (*GetIntegralListByMemberIdRes, error)
	// @Desc      根据用户id获取用户的积分明细
	// <AUTHOR>
	// @Date      2021-07-21
	RecoverUserIntegral(ctx context.Context, in *RecoverUserIntegralReq, opts ...grpc.CallOption) (*BaseRes, error)
}

type integralServiceClient struct {
	cc *grpc.ClientConn
}

func NewIntegralServiceClient(cc *grpc.ClientConn) IntegralServiceClient {
	return &integralServiceClient{cc}
}

func (c *integralServiceClient) GetStoreCode(ctx context.Context, in *GetStoreCodeRequest, opts ...grpc.CallOption) (*AwIntegralResultResponse, error) {
	out := new(AwIntegralResultResponse)
	err := c.cc.Invoke(ctx, "/oc.IntegralService/GetStoreCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralServiceClient) GetIntegralListByMemberId(ctx context.Context, in *GetIntegralListByMemberIdReq, opts ...grpc.CallOption) (*GetIntegralListByMemberIdRes, error) {
	out := new(GetIntegralListByMemberIdRes)
	err := c.cc.Invoke(ctx, "/oc.IntegralService/GetIntegralListByMemberId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralServiceClient) RecoverUserIntegral(ctx context.Context, in *RecoverUserIntegralReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/oc.IntegralService/RecoverUserIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntegralServiceServer is the server API for IntegralService service.
type IntegralServiceServer interface {
	// @Desc      根据城市查询城市编号
	// <AUTHOR>
	// @Date      2020-08-18
	GetStoreCode(context.Context, *GetStoreCodeRequest) (*AwIntegralResultResponse, error)
	// @Desc      根据用户id获取用户的积分明细
	// <AUTHOR>
	// @Date      2021-07-21
	GetIntegralListByMemberId(context.Context, *GetIntegralListByMemberIdReq) (*GetIntegralListByMemberIdRes, error)
	// @Desc      根据用户id获取用户的积分明细
	// <AUTHOR>
	// @Date      2021-07-21
	RecoverUserIntegral(context.Context, *RecoverUserIntegralReq) (*BaseRes, error)
}

// UnimplementedIntegralServiceServer can be embedded to have forward compatible implementations.
type UnimplementedIntegralServiceServer struct {
}

func (*UnimplementedIntegralServiceServer) GetStoreCode(ctx context.Context, req *GetStoreCodeRequest) (*AwIntegralResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStoreCode not implemented")
}
func (*UnimplementedIntegralServiceServer) GetIntegralListByMemberId(ctx context.Context, req *GetIntegralListByMemberIdReq) (*GetIntegralListByMemberIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegralListByMemberId not implemented")
}
func (*UnimplementedIntegralServiceServer) RecoverUserIntegral(ctx context.Context, req *RecoverUserIntegralReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverUserIntegral not implemented")
}

func RegisterIntegralServiceServer(s *grpc.Server, srv IntegralServiceServer) {
	s.RegisterService(&_IntegralService_serviceDesc, srv)
}

func _IntegralService_GetStoreCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoreCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralServiceServer).GetStoreCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.IntegralService/GetStoreCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralServiceServer).GetStoreCode(ctx, req.(*GetStoreCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralService_GetIntegralListByMemberId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntegralListByMemberIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralServiceServer).GetIntegralListByMemberId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.IntegralService/GetIntegralListByMemberId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralServiceServer).GetIntegralListByMemberId(ctx, req.(*GetIntegralListByMemberIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralService_RecoverUserIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverUserIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralServiceServer).RecoverUserIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.IntegralService/RecoverUserIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralServiceServer).RecoverUserIntegral(ctx, req.(*RecoverUserIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _IntegralService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.IntegralService",
	HandlerType: (*IntegralServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStoreCode",
			Handler:    _IntegralService_GetStoreCode_Handler,
		},
		{
			MethodName: "GetIntegralListByMemberId",
			Handler:    _IntegralService_GetIntegralListByMemberId_Handler,
		},
		{
			MethodName: "RecoverUserIntegral",
			Handler:    _IntegralService_RecoverUserIntegral_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/integral.proto",
}

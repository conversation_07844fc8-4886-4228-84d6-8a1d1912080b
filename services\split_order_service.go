package services

import (
	"errors"
	"fmt"
	"github.com/gogf/gf/os/glog"
	logger "github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"order-center/dto"
	"order-center/models"
	"sort"
	"strconv"
)

//拆分订单
//1、判断是否存在虚拟商品，虚拟商品-单个SKU拆分成一张订单。
//2、判断是否是组合商品(包含虚拟商品的组合)，则每个组合下的虚拟商品-单个SKU拆分成一张订单。
//3、实物订单根据仓库进行拆分订单。
//comeFrom ： 来源0-电商，1-本地生活   isDrug是否药品仓
func SplitThirdOrder(splitOrderReqs []dto.SplitThirdOrderReq, orderSN, warehouseCode string) ([]dto.SplitThirdOrderResp, error) {
	//todo 加锁
	glog.Info(orderSN, "实际进行拆单", kit.JsonEncode(splitOrderReqs))
	//虚拟商品，单个SKU独立一单，组合内的也是独立一单
	orderList := make([]dto.SplitThirdOrderResp, 0)
	//实物的大单
	matterOrder := dto.SplitThirdOrderResp{
		Ordersn:         orderSN,
		Warehousecode:   warehouseCode,
		SplitOrderGoods: []dto.SplitThirdOrderGoods{},
	}
	for _, v := range splitOrderReqs {
		//虚拟商品--直接按照个数成立一单
		// 不同的组合 相同的sku_id  是多条记录 因为不同的组合相同的sku 在主订单商品splitOrderReqs中是多条记录
		if v.Isvirtual == 1 {
			splitOrderResp := dto.SplitThirdOrderResp{
				Ordersn:         orderSN,
				Warehousecode:   warehouseCode,
				SplitOrderGoods: []dto.SplitThirdOrderGoods{},
			}
			splitOrderGoods := dto.SplitThirdOrderGoods{
				OrderProductId: v.OrderProductId,
				Skuid:          v.Skuid,
				Stock:          v.Stock,
				Isgroup:        v.Isgroup,
				Isvirtual:      v.Isvirtual,
				Groupskuid:     v.Groupskuid,
				Price:          v.Price,
			}
			splitOrderResp.SplitOrderGoods = append(splitOrderResp.SplitOrderGoods, splitOrderGoods)
			orderList = append(orderList, splitOrderResp)
		} else {
			splitOrderGoods := dto.SplitThirdOrderGoods{
				OrderProductId: v.OrderProductId,
				Skuid:          v.Skuid,
				Stock:          v.Stock,
				Isgroup:        v.Isgroup,
				Isvirtual:      v.Isvirtual,
				Groupskuid:     v.Groupskuid,
				Price:          v.Price,
			}
			//实物商品
			matterOrder.SplitOrderGoods = append(matterOrder.SplitOrderGoods, splitOrderGoods)
		}
	}

	//根据仓库信息获取库存信息
	//阿闻本地生活不需要拆单，则将实物的大单直接追加到订单列表中 所有实物商品都在同一个单
	if len(matterOrder.SplitOrderGoods) > 0 {
		orderList = append(orderList, matterOrder)
	}
	return orderList, nil
}

//拆分订单
//1、判断是否存在虚拟商品，虚拟商品-单个SKU拆分成一张订单。
//2、判断是否是组合商品(包含虚拟商品的组合)，则每个组合下的虚拟商品-单个SKU拆分成一张订单。
//3、实物订单根据仓库进行拆分订单。
//comeFrom ： 来源0-电商，1-本地生活   isDrug是否药品仓
func SplitOrder(splitOrderReqs []dto.SplitOrderReq, mainOrder *models.OrderMain, comeFrom int, isDrug bool, channelId int32) ([]dto.SplitOrderResp, error) {
	//todo 加锁
	glog.Info(mainOrder.OrderSn, "实际进行拆单", kit.JsonEncode(splitOrderReqs))
	//虚拟商品，单个SKU独立一单，组合内的也是独立一单
	orderList := make([]dto.SplitOrderResp, 0)
	//map[skuid]int32{}-组合标记
	groupMarkSkuidMap := make(map[int32]int32, 0)
	//skuid+groupId=>stock
	groupMarkSkuidStockMap := make(map[string]dto.SplitOrderGoods, 0)
	//所有组合商品里的sku=>组合商品sku 比如sku_id=111 同时存在与多个组合，则不同组合里的该sku需要拆成不同的记录
	skuIdGroupMap := make(map[int32][]dto.SplitOrderGoods, 0)
	//非组合商品里的实物 sku =>数量加总 不在组合里的商品 需要合并数量
	notGroupMatterSku := make(map[int32]int32, 0)

	matterStockMap := make(map[int32]int32, 0)
	matterSkuidsArray := make([]int32, 0)

	//实物的大单
	matterOrder := dto.SplitOrderResp{
		Ordersn:         mainOrder.OrderSn,
		Warehousecode:   mainOrder.WarehouseCode,
		SplitOrderGoods: []dto.SplitOrderGoods{},
	}

	for _, v := range splitOrderReqs {
		//虚拟商品--直接按照个数成立一单
		// 不同的组合 相同的sku_id  是多条记录 因为不同的组合相同的sku 在主订单商品splitOrderReqs中是多条记录
		if v.Isvirtual == 1 {
			splitOrderResp := dto.SplitOrderResp{
				Ordersn:         mainOrder.OrderSn,
				Warehousecode:   mainOrder.WarehouseCode,
				SplitOrderGoods: []dto.SplitOrderGoods{},
			}
			splitOrderGoods := dto.SplitOrderGoods{
				Skuid:      v.Skuid,
				Stock:      v.Stock,
				Isgroup:    v.Isgroup,
				Isvirtual:  v.Isvirtual,
				Groupskuid: v.Groupskuid,
				Price:      v.Price,
			}
			splitOrderResp.SplitOrderGoods = append(splitOrderResp.SplitOrderGoods, splitOrderGoods)
			orderList = append(orderList, splitOrderResp)
		} else {
			matterSkuidsArray = append(matterSkuidsArray, v.Skuid)
			splitOrderGoods := dto.SplitOrderGoods{
				Skuid:      v.Skuid,
				Stock:      v.Stock,
				Isgroup:    v.Isgroup,
				Isvirtual:  v.Isvirtual,
				Groupskuid: v.Groupskuid,
				Price:      v.Price,
			}
			//实物商品
			matterOrder.SplitOrderGoods = append(matterOrder.SplitOrderGoods, splitOrderGoods)
			if v.Isgroup == 1 {
				groupMarkSkuidMap[v.Skuid] = v.Groupskuid
				mapKey := fmt.Sprintf("%d-%d", v.Groupskuid, v.Skuid)
				skuIdGroupMap[v.Skuid] = append(skuIdGroupMap[v.Skuid], splitOrderGoods)
				groupMarkSkuidStockMap[mapKey] = splitOrderGoods
			} else {
				notGroupMatterSku[v.Skuid] += v.Stock
			}
			//skuId 的库存
			if _, ok := matterStockMap[v.Skuid]; !ok {
				matterStockMap[v.Skuid] = v.Stock
			} else {
				matterStockMap[v.Skuid] += v.Stock
			}
		}
	}

	for sku, stock := range notGroupMatterSku {
		skuIdGroupMap[sku] = append(skuIdGroupMap[sku], dto.SplitOrderGoods{
			Skuid:      sku,
			Stock:      stock,
			Isgroup:    0,
			Isvirtual:  0,
			Groupskuid: 0,
		})
	}

	// 根据仓库信息获取库存信息
	needSplitWarehouse := comeFrom == 0 && len(matterOrder.SplitOrderGoods) > 0
	// 预售订单开启虚拟库存不处理真实库存
	if needSplitWarehouse && mainOrder.OrderType == 11 {
		var useVirtualStock int32
		if has, err := GetUPetDBConn().Table("upet_orders").Where("order_sn = ?", mainOrder.OldOrderSn).
			Select("is_use_virtual_stock").Get(&useVirtualStock); err != nil {
			glog.Info(mainOrder.OrderSn, "，", mainOrder.OldOrderSn, " 预售订单查询是否开启虚拟库存出错：", err.Error())
		} else if !has {
			glog.Info(mainOrder.OrderSn, "，", mainOrder.OldOrderSn, " 预售订单查询是否开启虚拟库存出错：未找到订单")
		}
		needSplitWarehouse = useVirtualStock == 0
	}

	if needSplitWarehouse {
		//获取当前订单中所有的可发货仓库
		mapWarehouse := make(map[int]dto.WarehouseModel, 0)
		mapWarehouseAreaLevel := make(map[int]int, 0) //区域等级level
		mapWarehouseLevel := make(map[int]int, 0)     //仓库等级level
		var warehouses []*dto.WarehouseModel

		//互联网医院直接查对应仓库
		if channelId == ChannelDigitalHealth {
			warehouses = GetWarehousebyCode(1, mainOrder.WarehouseCode)
		} else {
			if isDrug { // 药品仓查询调整
				warehouses = GetWarehousebyCode(1, "JXYQC01")
			} else {
				warehouses = GetWarehouse(1, mainOrder.ReceiverState)
			}
		}

		glog.Info(mainOrder.OrderSn, "商城订单拆单查询仓库数据结果", warehouses, mainOrder.ReceiverState)
		warehousesIdsArray := make([]int, 0)
		//循环商品明细注意可能存在赠品
		mapGoodsStock := make(map[string]int32, 0)
		//sku=>stock 商品总库存
		mapWarehouseGoodsStocks := make(map[string]int32, 0)
		//warehouseid -> goodsid -> 库存
		mapWarehouseGoodsStock := make(map[int]map[string]int32, 0)
		warehouseStock := 0

		for _, w := range warehouses {
			mapWarehouse[w.Id] = *w
			mapWarehouseLevel[w.Id] = w.Level
			mapWarehouseAreaLevel[w.Id] = w.Arealevel
			warehousesIdsArray = append(warehousesIdsArray, w.Id)
		}
		if len(mapWarehouse) == 0 {
			return []dto.SplitOrderResp{}, errors.New("仓库信息不存在，请联系管理员")
		}

		warehouseGoods := GetWarehouseGoodsByCondition(warehousesIdsArray, matterSkuidsArray)

		//if len(warehouseGoods) != len(matterSkuidsArray) {
		//	return []dto.SplitOrderResp{}, errors.New("仓库商品与订单商品数量不匹配")
		//}
		//根据商品信息循环得到库存中的数据
		for _, v := range warehouseGoods {
			mapWarehouseGoodsStocks[v.Goodsid] = mapWarehouseGoodsStocks[v.Goodsid] + int32(v.Stock)
			if _, ok := mapWarehouseGoodsStock[v.WarehouseId]; ok {
				_mapGoodsStock := mapWarehouseGoodsStock[v.WarehouseId]
				//如果同一个仓库 同一个skuid 有多个记录会覆盖
				_mapGoodsStock[v.Goodsid] = int32(v.Stock)
			} else {
				_mapGoodsStock := make(map[string]int32, 0)
				_mapGoodsStock[v.Goodsid] = int32(v.Stock)
				mapWarehouseGoodsStock[v.WarehouseId] = _mapGoodsStock
			}
		}

		//判断总库存是否正常
		orderStock := int32(0)
		//循环实物订单的商品信息
		for k, v := range matterStockMap {
			orderStock += v
			mapGoodsStock[strconv.Itoa(int(k))] += v
		}
		//判断仓库总库存是否充足
		for goodsid, goodsStock := range mapGoodsStock {
			warehouseGoodStock := int32(0)
			if _, ok := mapWarehouseGoodsStocks[goodsid]; ok {
				warehouseGoodStock = mapWarehouseGoodsStocks[goodsid]
			}
			//仓库里的总库存小于需求量
			if warehouseGoodStock-goodsStock < 0 {
				return []dto.SplitOrderResp{}, errors.New(goodsid + "仓库总库存不足，请重新拆单")
			}
		}

		//重头戏 -- 拆单模块(递归判断)
		//1、先将取订单中商品在库存中最大的数量，然后将所有订单库存累加，得到所有库存中最大值。如果有多个值一样的话，判断区域等级，再判断仓库等级
		//2、如果只有一个值，则扣除订单中的库存，默认发第一个仓库。
		//3、再次执行1的逻辑，依次循环

		//lastWarehouseGoodsStock 仓库id=>sku=>stock
		//因为lastWarehouseGoodsStock的数据结构里没有区分sku的组合商品skuid 所以在下面的处理中需要使用skuIdGroupMap进行拆单后的商品数量处理
		lastWarehouseGoodsStock := make(map[int]map[string]int32, 0)
		_, _ = ConvertGoodsStock(mapWarehouse, mapWarehouseGoodsStock, lastWarehouseGoodsStock, mapGoodsStock, int32(warehouseStock), orderStock, mapWarehouseAreaLevel, mapWarehouseLevel)

		logger.Info("拆单递归结果：", lastWarehouseGoodsStock)

		if len(lastWarehouseGoodsStock) == 0 {
			return []dto.SplitOrderResp{}, errors.New("拆单失败，请重试")
		}

		//循环拆单后的结果
		for warehouseId, v := range lastWarehouseGoodsStock {
			warehouse := mapWarehouse[warehouseId]
			_childrenMatterOrder := dto.SplitOrderResp{
				Ordersn:         mainOrder.OrderSn,
				Warehousecode:   warehouse.Code,
				SplitOrderGoods: []dto.SplitOrderGoods{},
			}
			//v为该仓库下 分配拿了哪些skuId 以及这些skuId分配了多少库存
			//k:skuId m:库存
			for k, m := range v {
				if m == 0 {
					continue
				}
				_skuId, _ := strconv.Atoi(k)
				vSkuId := int32(_skuId)

				//处理组合标记
				if _, ok := skuIdGroupMap[vSkuId]; ok {
					for _, groupSkuInfo := range skuIdGroupMap[vSkuId] {
						splitOrderGoods := dto.SplitOrderGoods{
							Skuid:      vSkuId,
							Stock:      groupSkuInfo.Stock,
							Isgroup:    groupSkuInfo.Isgroup,
							Isvirtual:  groupSkuInfo.Isvirtual,
							Groupskuid: groupSkuInfo.Groupskuid,
						}
						_childrenMatterOrder.SplitOrderGoods = append(_childrenMatterOrder.SplitOrderGoods, splitOrderGoods)
					}
				}
			}
			orderList = append(orderList, _childrenMatterOrder)
		}

	} else {
		//阿闻本地生活不需要拆单，则将实物的大单直接追加到订单列表中 所有实物商品都在同一个单
		if len(matterOrder.SplitOrderGoods) > 0 {
			orderList = append(orderList, matterOrder)
		}
	}
	return orderList, nil
}

//ConvertGoodsStock 递归处理拆库存
//@param mapWarehouse 仓库id=>仓库信息
//@param mapWarehouseAreaLevel 仓库区域等级 仓库id=>仓库区域等级
//@param mapWarehouseLevel 仓库等级 仓库id=>仓库等级
//@param mapWarehouseGoodsStock 某仓库中某个sku的库存 warehouseId -> goodsId -> 库存
//@param lastMapWarehouseGoodsStock 空map
//@param warehouseStock 初始值0
//@param mapGoodsStock skuId=>订单需求库存(sku加总)
//@param orderStock 订单需求总库存
func ConvertGoodsStock(mapWarehouse map[int]dto.WarehouseModel, mapWarehouseGoodsStock, lastMapWarehouseGoodsStock map[int]map[string]int32, mapGoodsStock map[string]int32, warehouseStock, orderStock int32, mapWarehouseAreaLevel, mapWarehouseLevel map[int]int) (bool, error) {
	//初始化参数
	_mapWarehouseAreaLevel := make(map[int]int, 0)
	_mapWarehouseLevel := make(map[int]int, 0)
	_mapWarehouse := make(map[int]dto.WarehouseModel, 0)
	for key, value := range mapWarehouseAreaLevel {
		_mapWarehouseAreaLevel[key] = value
	}
	for key, value := range mapWarehouseLevel {
		_mapWarehouseLevel[key] = value
	}
	for key, value := range mapWarehouse {
		_mapWarehouse[key] = value
	}

	mapWarehouseLastGoodsType := make(map[int]int32, 0)
	mapWarehouseLastGoodsStock := make(map[int]int32, 0)
	warehouseOrderStock := make(map[int]int32, 0)
	warehouseWarehouseStock := make(map[int]int32, 0)
	_newMapWarehouseGoodsStock := make(map[int]map[string]int32, 0)
	_lastMapWarehouseGoodsStock := make(map[int]map[string]int32, 0)
	//循环仓库
	for _, w := range _mapWarehouse {
		//判断仓库中是否该仓库
		if _, ok := mapWarehouseGoodsStock[w.Id]; ok {
			_newMapGoodsStock := make(map[string]int32, 0)
			//获取该仓库下的所有商品库存信息
			warehouseGoodsStock := mapWarehouseGoodsStock[w.Id]
			//初始化参数
			_mapGoodsStock := make(map[string]int32, 0)
			for key, value := range mapGoodsStock {
				_mapGoodsStock[key] = value
			}
			_orderStock := orderStock
			_warehouseStock := warehouseStock

			for i, v := range warehouseGoodsStock {
				goodsid := i
				nowWarehouseGoodsStock := v
				if _, ok := _mapGoodsStock[goodsid]; ok {
					//当前商品的库存
					newStock := _mapGoodsStock[goodsid]
					//比较仓库中的库存数据跟订单中的库存大小
					if newStock-nowWarehouseGoodsStock > 0 {
						mapWarehouseLastGoodsStock[w.Id] += nowWarehouseGoodsStock
						_mapGoodsStock[goodsid] = newStock - nowWarehouseGoodsStock
						_newMapGoodsStock[goodsid] = nowWarehouseGoodsStock
						_orderStock = _orderStock - nowWarehouseGoodsStock
					} else {
						mapWarehouseLastGoodsType[w.Id] += 1 //判断整单满足
						mapWarehouseLastGoodsStock[w.Id] += newStock
						_mapGoodsStock[goodsid] = 0
						_newMapGoodsStock[goodsid] = newStock
						_orderStock = _orderStock - newStock
					}
					_warehouseStock = _warehouseStock - nowWarehouseGoodsStock // 减去仓库总库存
				}
			}
			warehouseOrderStock[w.Id] = _orderStock
			warehouseWarehouseStock[w.Id] = _warehouseStock
			_newMapWarehouseGoodsStock[w.Id] = _mapGoodsStock
			_lastMapWarehouseGoodsStock[w.Id] = _newMapGoodsStock
		}
	}
	if len(mapWarehouseLastGoodsType) > 0 {
		get_Map_MaxMap_int32(mapWarehouseLastGoodsType) // 获取最大值。map[仓库id]商品类别
	}
	if len(mapWarehouseLastGoodsType) == 1 {
		for k, _ := range mapWarehouseLastGoodsType {
			lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
			delete(mapWarehouseGoodsStock, k)
			delete(_mapWarehouse, k)
			delete(mapWarehouseAreaLevel, k)
			delete(mapWarehouseLevel, k)
			mapGoodsStock = _newMapWarehouseGoodsStock[k]
			orderStock = warehouseOrderStock[k]
			warehouseStock = warehouseWarehouseStock[k]
		}
	} else {
		//根据仓库类别目录最多的
		for k, _ := range mapWarehouseLastGoodsStock {
			if len(mapWarehouseLastGoodsType) > 0 {
				if _, ok := mapWarehouseLastGoodsType[k]; !ok {
					delete(mapWarehouseLastGoodsStock, k)
				}
			}
		}
		if len(mapWarehouseLastGoodsStock) > 0 {
			get_Map_MaxMap_int32(mapWarehouseLastGoodsStock) // 获取最大值。map[仓库id]仓库库存
		}
		if len(mapWarehouseLastGoodsStock) == 1 {
			for k, _ := range mapWarehouseLastGoodsStock {
				lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
				delete(mapWarehouseGoodsStock, k)
				delete(_mapWarehouse, k)
				delete(mapWarehouseAreaLevel, k)
				delete(mapWarehouseLevel, k)
				mapGoodsStock = _newMapWarehouseGoodsStock[k]
				orderStock = warehouseOrderStock[k]
				warehouseStock = warehouseWarehouseStock[k]
			}
		} else {
			//1、判断配送区域等级
			for k, _ := range _mapWarehouseAreaLevel {
				if len(mapWarehouseLastGoodsStock) > 0 {
					if _, ok := mapWarehouseLastGoodsStock[k]; !ok {
						delete(_mapWarehouseAreaLevel, k)
					}
				}
			}
			if len(_mapWarehouseAreaLevel) > 0 {
				get_Map_MinMap_int(_mapWarehouseAreaLevel)
			}
			if len(_mapWarehouseAreaLevel) == 1 {
				for k, _ := range _mapWarehouseAreaLevel {
					lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
					delete(mapWarehouseGoodsStock, k)
					delete(_mapWarehouse, k)
					delete(mapWarehouseAreaLevel, k)
					delete(mapWarehouseLevel, k)
					mapGoodsStock = _newMapWarehouseGoodsStock[k]
					orderStock = warehouseOrderStock[k]
					warehouseStock = warehouseWarehouseStock[k]
				}
			} else {
				//2、判断仓库等级，基于上个信息
				for k, _ := range _mapWarehouseLevel {
					if len(_mapWarehouseAreaLevel) > 0 {
						if _, ok := _mapWarehouseAreaLevel[k]; !ok {
							delete(_mapWarehouseLevel, k)
						}
					}
				}
				if len(_mapWarehouseLevel) > 0 {
					get_Map_MinMap_int(_mapWarehouseLevel)
				}
				if len(_mapWarehouseLevel) == 1 {
					for k, _ := range _mapWarehouseLevel {
						lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
						delete(mapWarehouseGoodsStock, k)
						delete(_mapWarehouse, k)
						delete(mapWarehouseAreaLevel, k)
						delete(mapWarehouseLevel, k)
						mapGoodsStock = _newMapWarehouseGoodsStock[k]
						orderStock = warehouseOrderStock[k]
						warehouseStock = warehouseWarehouseStock[k]
					}
				} else {
					for k, _ := range _mapWarehouseLevel {
						lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
						delete(mapWarehouseGoodsStock, k)
						delete(_mapWarehouse, k)
						delete(mapWarehouseAreaLevel, k)
						delete(mapWarehouseLevel, k)
						mapGoodsStock = _newMapWarehouseGoodsStock[k]
						orderStock = warehouseOrderStock[k]
						warehouseStock = warehouseWarehouseStock[k]
						break
					}
				}
			}
		}
	}
	//当订单的库存扣完了，则返回结果
	if orderStock == 0 {
		return true, nil
	}
	return ConvertGoodsStock(_mapWarehouse, mapWarehouseGoodsStock, lastMapWarehouseGoodsStock, mapGoodsStock, warehouseStock, orderStock, mapWarehouseAreaLevel, mapWarehouseLevel)
}

// 获取仓库主表数据
func GetWarehouse(comeFrom int, province string) []*dto.WarehouseModel {
	var warehouseModels []*dto.WarehouseModel
	sql := `SELECT a.id,a.thirdid,a.code,a.name,a.comefrom,a.level,a.category,b.level AS areaLevel,b.areaid,c.area_name
			FROM dc_dispatch.warehouse a 
			INNER JOIN dc_dispatch.warehouse_area b ON b.warehouseid=a.id 
			INNER JOIN dc_dispatch.base_area c ON b.areaid=c.area_id
			WHERE a.status=1 AND a.comefrom=? AND c.area_name=? AND a.id != 6 
			ORDER BY b.level,a.level;`
	err := GetDBConn().SQL(sql, comeFrom, province).Find(&warehouseModels)
	if err != nil {
		logger.Error("GetWarehouse 数据库错误信息：", err)
		var _warehouseModels []*dto.WarehouseModel
		return _warehouseModels
	}
	return warehouseModels
}

/**
根据仓库code查询仓库信息
@comeFrom int 仓库归属(1-A8,2-管易 3-门店（美团）)
@province 省份
@code string 仓库的code
*/
func GetWarehousebyCode(comeFrom int, code string) []*dto.WarehouseModel {
	var warehouseModels []*dto.WarehouseModel
	sql := `SELECT a.id,a.thirdid,a.code,a.name,a.comefrom,a.level,a.category,b.level AS areaLevel,b.areaid,c.area_name
                        FROM dc_dispatch.warehouse a
                        left JOIN dc_dispatch.warehouse_area b ON b.warehouseid=a.id
                        left JOIN dc_dispatch.base_area c ON b.areaid=c.area_id
                        WHERE a.status=1 AND a.comefrom=?  AND a.code=? AND a.id != 6
                        ORDER BY b.level,a.level;`
	err := GetDBConn().SQL(sql, comeFrom, code).Find(&warehouseModels)
	if err != nil {
		logger.Error("GetWarehousebyCode 数据库错误信息：", err)
		return []*dto.WarehouseModel{}
	}
	return warehouseModels
}

//根据条件获取商品仓库库存信息
func GetWarehouseGoodsByCondition(warehouseIds []int, skuids []int32) []models.WarehouseGoods {
	warehouseGoods := []models.WarehouseGoods{}
	err := GetDBConn().Table("dc_order.warehouse_goods").In("warehouse_id", warehouseIds).In("goodsid", skuids).Find(&warehouseGoods)
	if err != nil {
		logger.Error("数据库错误信息：", err)
		return warehouseGoods
	}
	return warehouseGoods
}

//获取map中最大的map数据，根据值返回
func get_Map_MaxMap_int32(mp map[int]int32) {
	var newMp = make([]int, 0)
	for _, v := range mp {
		newMp = append(newMp, int(v))
	}
	sort.Ints(newMp)
	var maxValue = newMp[len(newMp)-1]
	for k, v := range mp {
		if v != int32(maxValue) {
			delete(mp, k)
		}
	}
}

//获取map中最小的map数据，根据值返回
func get_Map_MinMap_int(mp map[int]int) {
	var newMp = make([]int, 0)
	for _, v := range mp {
		newMp = append(newMp, int(v))
	}
	sort.Ints(newMp)
	var minValue = newMp[0]
	for k, v := range mp {
		if v != minValue {
			delete(mp, k)
		}
	}
}

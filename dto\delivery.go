package dto

type CourierLocation struct {
	Lat int64 `json:"lat"`
	Lng int64 `json:"lng"`
}

//查询价格或者下单返回的数据
type DeliverPriceRes struct {
	MtPeisongId string `json:"mt_peisong_id"`
	DeliveryId  int64  `json:"delivery_id"`
	OrderId     string `json:"order_id"`
	//距离，单位米
	DeliveryDistance int64 `json:"delivery_distance"`
	//
	DeliveryFee float64 `json:"delivery_fee"`
	//配送类型  配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
	DeliveryType int `json:"delivery_type" xorm:"default 'null' comment('//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风') INT(11) 'delivery_type'"`

	//是否成功获取到价格了
	IsOk bool
	//父单号，用于重新发配送用
	ParentOrderSn string `json:"parent_order_sn" xorm:"not null default '''' comment('拆单前父订单号') index VARCHAR(50)"`
	//当前重试次数
	DeliveryCount int `json:"delivery_count" `
	//是否指定了选择的配送
	SelDeliverType string `json:"sel_deliver_type" `
	//要排除的渠道，也就上次是这个渠道发的配送，这次不用这个了
	PcDeliverType string `json:"pc_deliver_type" `
	//错误信息
	Err string `json:"err" `
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/virtual_card.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type VirtualCardListReq struct {
	BatchId              string   `protobuf:"bytes,1,opt,name=batch_id,json=batchId,proto3" json:"batch_id"`
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	TemplateId           int32    `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	UserId               string   `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	UserMobile           string   `protobuf:"bytes,5,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	UseTimeStart         string   `protobuf:"bytes,6,opt,name=use_time_start,json=useTimeStart,proto3" json:"use_time_start"`
	UseTimeEnd           string   `protobuf:"bytes,7,opt,name=use_time_end,json=useTimeEnd,proto3" json:"use_time_end"`
	CreateTimeStart      string   `protobuf:"bytes,8,opt,name=create_time_start,json=createTimeStart,proto3" json:"create_time_start"`
	CreateTimeEnd        string   `protobuf:"bytes,9,opt,name=create_time_end,json=createTimeEnd,proto3" json:"create_time_end"`
	CardIds              []int64  `protobuf:"varint,10,rep,packed,name=card_ids,json=cardIds,proto3" json:"card_ids"`
	PageIndex            int32    `protobuf:"varint,11,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,12,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	UserNo               string   `protobuf:"bytes,13,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	Status               int32    `protobuf:"varint,14,opt,name=status,proto3" json:"status"`
	CardId               int64    `protobuf:"varint,15,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	CardStr              string   `protobuf:"bytes,16,opt,name=card_str,json=cardStr,proto3" json:"card_str"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualCardListReq) Reset()         { *m = VirtualCardListReq{} }
func (m *VirtualCardListReq) String() string { return proto.CompactTextString(m) }
func (*VirtualCardListReq) ProtoMessage()    {}
func (*VirtualCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{0}
}

func (m *VirtualCardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCardListReq.Unmarshal(m, b)
}
func (m *VirtualCardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCardListReq.Marshal(b, m, deterministic)
}
func (m *VirtualCardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCardListReq.Merge(m, src)
}
func (m *VirtualCardListReq) XXX_Size() int {
	return xxx_messageInfo_VirtualCardListReq.Size(m)
}
func (m *VirtualCardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCardListReq proto.InternalMessageInfo

func (m *VirtualCardListReq) GetBatchId() string {
	if m != nil {
		return m.BatchId
	}
	return ""
}

func (m *VirtualCardListReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *VirtualCardListReq) GetTemplateId() int32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *VirtualCardListReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *VirtualCardListReq) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *VirtualCardListReq) GetUseTimeStart() string {
	if m != nil {
		return m.UseTimeStart
	}
	return ""
}

func (m *VirtualCardListReq) GetUseTimeEnd() string {
	if m != nil {
		return m.UseTimeEnd
	}
	return ""
}

func (m *VirtualCardListReq) GetCreateTimeStart() string {
	if m != nil {
		return m.CreateTimeStart
	}
	return ""
}

func (m *VirtualCardListReq) GetCreateTimeEnd() string {
	if m != nil {
		return m.CreateTimeEnd
	}
	return ""
}

func (m *VirtualCardListReq) GetCardIds() []int64 {
	if m != nil {
		return m.CardIds
	}
	return nil
}

func (m *VirtualCardListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *VirtualCardListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *VirtualCardListReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *VirtualCardListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *VirtualCardListReq) GetCardId() int64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *VirtualCardListReq) GetCardStr() string {
	if m != nil {
		return m.CardStr
	}
	return ""
}

type VirtualCardListRes struct {
	PageCount            int64          `protobuf:"varint,1,opt,name=page_count,json=pageCount,proto3" json:"page_count"`
	List                 []*VirtualCard `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *VirtualCardListRes) Reset()         { *m = VirtualCardListRes{} }
func (m *VirtualCardListRes) String() string { return proto.CompactTextString(m) }
func (*VirtualCardListRes) ProtoMessage()    {}
func (*VirtualCardListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{1}
}

func (m *VirtualCardListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCardListRes.Unmarshal(m, b)
}
func (m *VirtualCardListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCardListRes.Marshal(b, m, deterministic)
}
func (m *VirtualCardListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCardListRes.Merge(m, src)
}
func (m *VirtualCardListRes) XXX_Size() int {
	return xxx_messageInfo_VirtualCardListRes.Size(m)
}
func (m *VirtualCardListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCardListRes.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCardListRes proto.InternalMessageInfo

func (m *VirtualCardListRes) GetPageCount() int64 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *VirtualCardListRes) GetList() []*VirtualCard {
	if m != nil {
		return m.List
	}
	return nil
}

type VirtualCard struct {
	CardId               int64    `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	BatchId              string   `protobuf:"bytes,2,opt,name=batch_id,json=batchId,proto3" json:"batch_id"`
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	OrgName              string   `protobuf:"bytes,4,opt,name=org_name,json=orgName,proto3" json:"org_name"`
	TemplateId           int32    `protobuf:"varint,5,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	Status               int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	UserId               string   `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id"`
	UserMobile           string   `protobuf:"bytes,8,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	UseTime              string   `protobuf:"bytes,9,opt,name=use_time,json=useTime,proto3" json:"use_time"`
	ExpireTime           string   `protobuf:"bytes,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time"`
	CreateTime           string   `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime           string   `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	SellType             int32    `protobuf:"varint,13,opt,name=sell_type,json=sellType,proto3" json:"sell_type"`
	StatusName           string   `protobuf:"bytes,14,opt,name=status_name,json=statusName,proto3" json:"status_name"`
	EnUserMobile         string   `protobuf:"bytes,15,opt,name=en_user_mobile,json=enUserMobile,proto3" json:"en_user_mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualCard) Reset()         { *m = VirtualCard{} }
func (m *VirtualCard) String() string { return proto.CompactTextString(m) }
func (*VirtualCard) ProtoMessage()    {}
func (*VirtualCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{2}
}

func (m *VirtualCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCard.Unmarshal(m, b)
}
func (m *VirtualCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCard.Marshal(b, m, deterministic)
}
func (m *VirtualCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCard.Merge(m, src)
}
func (m *VirtualCard) XXX_Size() int {
	return xxx_messageInfo_VirtualCard.Size(m)
}
func (m *VirtualCard) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCard.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCard proto.InternalMessageInfo

func (m *VirtualCard) GetCardId() int64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *VirtualCard) GetBatchId() string {
	if m != nil {
		return m.BatchId
	}
	return ""
}

func (m *VirtualCard) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *VirtualCard) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *VirtualCard) GetTemplateId() int32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *VirtualCard) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *VirtualCard) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *VirtualCard) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *VirtualCard) GetUseTime() string {
	if m != nil {
		return m.UseTime
	}
	return ""
}

func (m *VirtualCard) GetExpireTime() string {
	if m != nil {
		return m.ExpireTime
	}
	return ""
}

func (m *VirtualCard) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VirtualCard) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *VirtualCard) GetSellType() int32 {
	if m != nil {
		return m.SellType
	}
	return 0
}

func (m *VirtualCard) GetStatusName() string {
	if m != nil {
		return m.StatusName
	}
	return ""
}

func (m *VirtualCard) GetEnUserMobile() string {
	if m != nil {
		return m.EnUserMobile
	}
	return ""
}

type CancelVirtualCardReq struct {
	FileUrl              string   `protobuf:"bytes,1,opt,name=file_url,json=fileUrl,proto3" json:"file_url"`
	CancelRemark         string   `protobuf:"bytes,2,opt,name=cancel_remark,json=cancelRemark,proto3" json:"cancel_remark"`
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	OrgName              string   `protobuf:"bytes,4,opt,name=org_name,json=orgName,proto3" json:"org_name"`
	UserNo               string   `protobuf:"bytes,5,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	UserName             string   `protobuf:"bytes,6,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelVirtualCardReq) Reset()         { *m = CancelVirtualCardReq{} }
func (m *CancelVirtualCardReq) String() string { return proto.CompactTextString(m) }
func (*CancelVirtualCardReq) ProtoMessage()    {}
func (*CancelVirtualCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{3}
}

func (m *CancelVirtualCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelVirtualCardReq.Unmarshal(m, b)
}
func (m *CancelVirtualCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelVirtualCardReq.Marshal(b, m, deterministic)
}
func (m *CancelVirtualCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelVirtualCardReq.Merge(m, src)
}
func (m *CancelVirtualCardReq) XXX_Size() int {
	return xxx_messageInfo_CancelVirtualCardReq.Size(m)
}
func (m *CancelVirtualCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelVirtualCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelVirtualCardReq proto.InternalMessageInfo

func (m *CancelVirtualCardReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *CancelVirtualCardReq) GetCancelRemark() string {
	if m != nil {
		return m.CancelRemark
	}
	return ""
}

func (m *CancelVirtualCardReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *CancelVirtualCardReq) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *CancelVirtualCardReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *CancelVirtualCardReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type VirtualCardCancelListReq struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	UserNo               string   `protobuf:"bytes,3,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualCardCancelListReq) Reset()         { *m = VirtualCardCancelListReq{} }
func (m *VirtualCardCancelListReq) String() string { return proto.CompactTextString(m) }
func (*VirtualCardCancelListReq) ProtoMessage()    {}
func (*VirtualCardCancelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{4}
}

func (m *VirtualCardCancelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCardCancelListReq.Unmarshal(m, b)
}
func (m *VirtualCardCancelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCardCancelListReq.Marshal(b, m, deterministic)
}
func (m *VirtualCardCancelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCardCancelListReq.Merge(m, src)
}
func (m *VirtualCardCancelListReq) XXX_Size() int {
	return xxx_messageInfo_VirtualCardCancelListReq.Size(m)
}
func (m *VirtualCardCancelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCardCancelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCardCancelListReq proto.InternalMessageInfo

func (m *VirtualCardCancelListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *VirtualCardCancelListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *VirtualCardCancelListReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

type VirtualCardCancelListRes struct {
	PageCount            int64                `protobuf:"varint,1,opt,name=page_count,json=pageCount,proto3" json:"page_count"`
	List                 []*VirtualCardCancel `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *VirtualCardCancelListRes) Reset()         { *m = VirtualCardCancelListRes{} }
func (m *VirtualCardCancelListRes) String() string { return proto.CompactTextString(m) }
func (*VirtualCardCancelListRes) ProtoMessage()    {}
func (*VirtualCardCancelListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{5}
}

func (m *VirtualCardCancelListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCardCancelListRes.Unmarshal(m, b)
}
func (m *VirtualCardCancelListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCardCancelListRes.Marshal(b, m, deterministic)
}
func (m *VirtualCardCancelListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCardCancelListRes.Merge(m, src)
}
func (m *VirtualCardCancelListRes) XXX_Size() int {
	return xxx_messageInfo_VirtualCardCancelListRes.Size(m)
}
func (m *VirtualCardCancelListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCardCancelListRes.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCardCancelListRes proto.InternalMessageInfo

func (m *VirtualCardCancelListRes) GetPageCount() int64 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *VirtualCardCancelListRes) GetList() []*VirtualCardCancel {
	if m != nil {
		return m.List
	}
	return nil
}

type VirtualCardCancel struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	FileUrl              string   `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url"`
	CancelRemark         string   `protobuf:"bytes,3,opt,name=cancel_remark,json=cancelRemark,proto3" json:"cancel_remark"`
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	OrgName              string   `protobuf:"bytes,5,opt,name=org_name,json=orgName,proto3" json:"org_name"`
	UserId               string   `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id"`
	UserName             string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name"`
	CancelNum            int32    `protobuf:"varint,8,opt,name=cancel_num,json=cancelNum,proto3" json:"cancel_num"`
	CancelStatus         int32    `protobuf:"varint,9,opt,name=cancel_status,json=cancelStatus,proto3" json:"cancel_status"`
	CancelResult         string   `protobuf:"bytes,10,opt,name=cancel_result,json=cancelResult,proto3" json:"cancel_result"`
	CreateTime           string   `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	ErrorNum             int32    `protobuf:"varint,12,opt,name=error_num,json=errorNum,proto3" json:"error_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualCardCancel) Reset()         { *m = VirtualCardCancel{} }
func (m *VirtualCardCancel) String() string { return proto.CompactTextString(m) }
func (*VirtualCardCancel) ProtoMessage()    {}
func (*VirtualCardCancel) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{6}
}

func (m *VirtualCardCancel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualCardCancel.Unmarshal(m, b)
}
func (m *VirtualCardCancel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualCardCancel.Marshal(b, m, deterministic)
}
func (m *VirtualCardCancel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualCardCancel.Merge(m, src)
}
func (m *VirtualCardCancel) XXX_Size() int {
	return xxx_messageInfo_VirtualCardCancel.Size(m)
}
func (m *VirtualCardCancel) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualCardCancel.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualCardCancel proto.InternalMessageInfo

func (m *VirtualCardCancel) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VirtualCardCancel) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *VirtualCardCancel) GetCancelRemark() string {
	if m != nil {
		return m.CancelRemark
	}
	return ""
}

func (m *VirtualCardCancel) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *VirtualCardCancel) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *VirtualCardCancel) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *VirtualCardCancel) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *VirtualCardCancel) GetCancelNum() int32 {
	if m != nil {
		return m.CancelNum
	}
	return 0
}

func (m *VirtualCardCancel) GetCancelStatus() int32 {
	if m != nil {
		return m.CancelStatus
	}
	return 0
}

func (m *VirtualCardCancel) GetCancelResult() string {
	if m != nil {
		return m.CancelResult
	}
	return ""
}

func (m *VirtualCardCancel) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VirtualCardCancel) GetErrorNum() int32 {
	if m != nil {
		return m.ErrorNum
	}
	return 0
}

type VirtualBaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualBaseResponse) Reset()         { *m = VirtualBaseResponse{} }
func (m *VirtualBaseResponse) String() string { return proto.CompactTextString(m) }
func (*VirtualBaseResponse) ProtoMessage()    {}
func (*VirtualBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{7}
}

func (m *VirtualBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualBaseResponse.Unmarshal(m, b)
}
func (m *VirtualBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualBaseResponse.Marshal(b, m, deterministic)
}
func (m *VirtualBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualBaseResponse.Merge(m, src)
}
func (m *VirtualBaseResponse) XXX_Size() int {
	return xxx_messageInfo_VirtualBaseResponse.Size(m)
}
func (m *VirtualBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualBaseResponse proto.InternalMessageInfo

func (m *VirtualBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VirtualBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type CreateRequest struct {
	//组织ID
	OrgId int32 `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	//组织名称
	OrgName string `protobuf:"bytes,2,opt,name=org_name,json=orgName,proto3" json:"org_name"`
	//卡模板ID
	TemplateId int32 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	//生成卡券数量
	CardCount int32 `protobuf:"varint,4,opt,name=card_count,json=cardCount,proto3" json:"card_count"`
	//单张金额
	CardPrice int32 `protobuf:"varint,5,opt,name=card_price,json=cardPrice,proto3" json:"card_price"`
	//创建人ID
	UserId string `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//创建人名称
	UserName string `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//销售形式  1内销 2外采
	SellType             int32    `protobuf:"varint,8,opt,name=sell_type,json=sellType,proto3" json:"sell_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRequest) Reset()         { *m = CreateRequest{} }
func (m *CreateRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRequest) ProtoMessage()    {}
func (*CreateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f7f3e9c7ac7047a9, []int{8}
}

func (m *CreateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRequest.Unmarshal(m, b)
}
func (m *CreateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRequest.Marshal(b, m, deterministic)
}
func (m *CreateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRequest.Merge(m, src)
}
func (m *CreateRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRequest.Size(m)
}
func (m *CreateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRequest proto.InternalMessageInfo

func (m *CreateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func (m *CreateRequest) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *CreateRequest) GetTemplateId() int32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *CreateRequest) GetCardCount() int32 {
	if m != nil {
		return m.CardCount
	}
	return 0
}

func (m *CreateRequest) GetCardPrice() int32 {
	if m != nil {
		return m.CardPrice
	}
	return 0
}

func (m *CreateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CreateRequest) GetSellType() int32 {
	if m != nil {
		return m.SellType
	}
	return 0
}

func init() {
	proto.RegisterType((*VirtualCardListReq)(nil), "cc.VirtualCardListReq")
	proto.RegisterType((*VirtualCardListRes)(nil), "cc.VirtualCardListRes")
	proto.RegisterType((*VirtualCard)(nil), "cc.VirtualCard")
	proto.RegisterType((*CancelVirtualCardReq)(nil), "cc.CancelVirtualCardReq")
	proto.RegisterType((*VirtualCardCancelListReq)(nil), "cc.VirtualCardCancelListReq")
	proto.RegisterType((*VirtualCardCancelListRes)(nil), "cc.VirtualCardCancelListRes")
	proto.RegisterType((*VirtualCardCancel)(nil), "cc.VirtualCardCancel")
	proto.RegisterType((*VirtualBaseResponse)(nil), "cc.VirtualBaseResponse")
	proto.RegisterType((*CreateRequest)(nil), "cc.CreateRequest")
}

func init() { proto.RegisterFile("cc/virtual_card.proto", fileDescriptor_f7f3e9c7ac7047a9) }

var fileDescriptor_f7f3e9c7ac7047a9 = []byte{
	// 953 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0x4b, 0x8f, 0xe3, 0x44,
	0x10, 0x56, 0xec, 0x49, 0xe2, 0xd4, 0xe4, 0x41, 0x1a, 0x66, 0xd6, 0x33, 0x01, 0x11, 0x65, 0x57,
	0x28, 0x70, 0xc8, 0x48, 0xcb, 0x91, 0x03, 0x82, 0x68, 0x84, 0x22, 0xc1, 0x08, 0x39, 0xbb, 0x88,
	0x9b, 0xe5, 0x71, 0xd7, 0x06, 0x0b, 0xbf, 0xa6, 0xbb, 0xbd, 0xda, 0xd9, 0x1f, 0xc7, 0x99, 0xbf,
	0x03, 0x9c, 0xb8, 0xa1, 0x7e, 0xd8, 0xb1, 0xf3, 0x1a, 0xb1, 0xb7, 0xf4, 0x57, 0x8f, 0xae, 0xae,
	0xfa, 0xbe, 0x72, 0xe0, 0x22, 0x0c, 0x6f, 0xde, 0x46, 0x4c, 0x14, 0x41, 0xec, 0x87, 0x01, 0xa3,
	0x8b, 0x9c, 0x65, 0x22, 0x23, 0x56, 0x18, 0x5e, 0x4f, 0x36, 0x59, 0xb6, 0x89, 0xf1, 0x46, 0x21,
	0xf7, 0xc5, 0x9b, 0x1b, 0x4c, 0x72, 0xf1, 0xa8, 0x1d, 0x66, 0xff, 0xda, 0x40, 0x7e, 0xd1, 0x71,
	0xcb, 0x80, 0xd1, 0x1f, 0x23, 0x2e, 0x3c, 0x7c, 0x20, 0x57, 0xe0, 0xdc, 0x07, 0x22, 0xfc, 0xcd,
	0x8f, 0xa8, 0xdb, 0x9a, 0xb6, 0xe6, 0x3d, 0xaf, 0xab, 0xce, 0x2b, 0x4a, 0x2e, 0xa0, 0x93, 0xb1,
	0x8d, 0x34, 0x58, 0xd3, 0xd6, 0xbc, 0xed, 0xb5, 0x33, 0xb6, 0x59, 0x51, 0xf2, 0x39, 0x9c, 0x0b,
	0x4c, 0xf2, 0x38, 0x10, 0x28, 0x6d, 0xb6, 0xb2, 0x41, 0x09, 0xad, 0x28, 0x79, 0x06, 0xdd, 0x82,
	0x23, 0x93, 0xc6, 0x33, 0x95, 0xb1, 0x23, 0x8f, 0x3a, 0x52, 0x19, 0x92, 0xec, 0x3e, 0x8a, 0xd1,
	0x6d, 0x2b, 0x23, 0x48, 0xe8, 0x27, 0x85, 0x90, 0x17, 0x30, 0x2c, 0x38, 0xfa, 0x22, 0x4a, 0xd0,
	0xe7, 0x22, 0x60, 0xc2, 0xed, 0x28, 0x9f, 0x7e, 0xc1, 0xf1, 0x55, 0x94, 0xe0, 0x5a, 0x62, 0x64,
	0x0a, 0xfd, 0xca, 0x0b, 0x53, 0xea, 0x76, 0xab, 0x3c, 0xd2, 0xe7, 0x36, 0xa5, 0xe4, 0x2b, 0x18,
	0x87, 0x0c, 0x65, 0x81, 0xb5, 0x54, 0x8e, 0x72, 0x1b, 0x69, 0xc3, 0x36, 0xdb, 0x17, 0x30, 0xaa,
	0xfb, 0xca, 0x84, 0x3d, 0xe5, 0x39, 0xd8, 0x7a, 0xca, 0x9c, 0x57, 0xe0, 0xc8, 0x76, 0xfb, 0x11,
	0xe5, 0x2e, 0x4c, 0xed, 0xb9, 0xed, 0x75, 0xe5, 0x79, 0x45, 0x39, 0xf9, 0x0c, 0x20, 0x0f, 0x36,
	0xe8, 0x47, 0x29, 0xc5, 0x77, 0xee, 0xb9, 0x6a, 0x48, 0x4f, 0x22, 0x2b, 0x09, 0x90, 0x09, 0xa8,
	0x83, 0xcf, 0xa3, 0xf7, 0xe8, 0xf6, 0x95, 0xd5, 0x91, 0xc0, 0x3a, 0x7a, 0x8f, 0x55, 0xb3, 0xd2,
	0xcc, 0x1d, 0x6c, 0x9b, 0x75, 0x97, 0x91, 0x4b, 0xe8, 0x70, 0x11, 0x88, 0x82, 0xbb, 0x43, 0x15,
	0x62, 0x4e, 0x32, 0xc0, 0xd4, 0xe1, 0x8e, 0xa6, 0xad, 0xb9, 0xed, 0x75, 0x74, 0x19, 0x55, 0x81,
	0x5c, 0x30, 0xf7, 0x23, 0x3d, 0x49, 0x79, 0x5e, 0x0b, 0x36, 0xfb, 0xf5, 0xc0, 0xe8, 0xb7, 0x65,
	0x87, 0x59, 0x91, 0x0a, 0x35, 0x7c, 0x5b, 0x97, 0xbd, 0x94, 0x00, 0x79, 0x0e, 0x67, 0x71, 0xc4,
	0x85, 0x6b, 0x4d, 0xed, 0xf9, 0xf9, 0xcb, 0xd1, 0x22, 0x0c, 0x17, 0xb5, 0x24, 0x9e, 0x32, 0xce,
	0xfe, 0xb4, 0xe1, 0xbc, 0x86, 0xd6, 0xab, 0x6b, 0xed, 0x56, 0x57, 0xf1, 0xcc, 0x3a, 0xc6, 0x33,
	0xbb, 0xce, 0xb3, 0x2b, 0x70, 0x24, 0x9c, 0x06, 0x09, 0x1a, 0x1e, 0x75, 0x33, 0xb6, 0xb9, 0x0b,
	0x12, 0xdc, 0xa5, 0x60, 0x7b, 0x8f, 0x82, 0xdb, 0xe6, 0x75, 0x76, 0x9b, 0x57, 0x52, 0xb3, 0x7b,
	0x8a, 0x9a, 0xce, 0x1e, 0x35, 0xaf, 0xc0, 0x29, 0x49, 0x67, 0xf8, 0xd1, 0x35, 0x84, 0x93, 0xb1,
	0xf8, 0x2e, 0x8f, 0x98, 0xb1, 0x82, 0x8e, 0xd5, 0x50, 0xe9, 0x50, 0xa3, 0x98, 0x22, 0x48, 0xcf,
	0x83, 0x2d, 0xbd, 0xd4, 0xed, 0x39, 0xad, 0x1c, 0xfa, 0xe6, 0x76, 0x05, 0x29, 0x87, 0x09, 0xf4,
	0x38, 0xc6, 0xb1, 0x2f, 0x1e, 0x73, 0x54, 0x3c, 0x69, 0x7b, 0x8e, 0x04, 0x5e, 0x3d, 0xe6, 0x2a,
	0x5a, 0x3f, 0x4f, 0xf7, 0x6a, 0xa8, 0xa3, 0x35, 0xa4, 0xda, 0xf5, 0x02, 0x86, 0x98, 0xfa, 0xf5,
	0xf7, 0x8d, 0xb4, 0xac, 0x30, 0x7d, 0x5d, 0xbd, 0x70, 0xf6, 0x47, 0x0b, 0x3e, 0x59, 0x06, 0x69,
	0x88, 0x71, 0x7d, 0xcc, 0x7a, 0x45, 0xbc, 0x89, 0x62, 0xf4, 0x0b, 0x16, 0x97, 0x2b, 0x42, 0x9e,
	0x5f, 0xb3, 0x98, 0x3c, 0x87, 0x41, 0xa8, 0x42, 0x7c, 0x86, 0x49, 0xc0, 0x7e, 0x37, 0xa3, 0xed,
	0x6b, 0xd0, 0x53, 0xd8, 0x07, 0xcc, 0xb7, 0x26, 0x8a, 0x76, 0x43, 0x14, 0x13, 0xe8, 0x69, 0x83,
	0x0c, 0xd2, 0xbb, 0xc1, 0x51, 0xa6, 0x20, 0xc1, 0x59, 0x06, 0x6e, 0xad, 0x72, 0xfd, 0x94, 0x72,
	0xcd, 0x35, 0x25, 0xda, 0x3a, 0x29, 0x51, 0xeb, 0xb8, 0x44, 0xed, 0x7a, 0x35, 0x33, 0x7a, 0xf4,
	0xc2, 0x27, 0xc5, 0xf5, 0x65, 0x43, 0x5c, 0x17, 0x3b, 0xe2, 0xd2, 0xa9, 0x8c, 0xc4, 0xfe, 0xb6,
	0x60, 0xbc, 0x67, 0x23, 0x43, 0xb0, 0x2a, 0x8d, 0x59, 0x11, 0x6d, 0x0c, 0xc9, 0x7a, 0x62, 0x48,
	0xf6, 0xc9, 0x21, 0x9d, 0x1d, 0x1b, 0x52, 0xfb, 0xf0, 0x90, 0x22, 0x6a, 0x26, 0x51, 0x6a, 0xa9,
	0x31, 0xa4, 0x6e, 0x73, 0x48, 0xb2, 0x2f, 0xa6, 0x98, 0xb4, 0x48, 0x94, 0xce, 0xda, 0x5e, 0x4f,
	0x23, 0x77, 0x45, 0x52, 0xab, 0xd5, 0xe8, 0xb7, 0xa7, 0x3c, 0x4c, 0xad, 0x6b, 0xad, 0xe2, 0xfa,
	0x83, 0x78, 0x11, 0x0b, 0x23, 0xb9, 0xea, 0x41, 0x12, 0x7b, 0x5a, 0x74, 0x13, 0xe8, 0x21, 0x63,
	0x19, 0x53, 0x85, 0x98, 0xb5, 0xac, 0x80, 0xbb, 0x22, 0x99, 0x2d, 0xe1, 0x63, 0xd3, 0xf3, 0xef,
	0x03, 0x8e, 0x1e, 0xf2, 0x3c, 0x4b, 0x39, 0x12, 0x02, 0x67, 0x61, 0x46, 0xd1, 0x10, 0x48, 0xfd,
	0x26, 0x2e, 0x74, 0x13, 0xe4, 0x3c, 0xd8, 0x60, 0xd9, 0x78, 0x73, 0x9c, 0xfd, 0xd3, 0x82, 0xc1,
	0x52, 0x5d, 0xe8, 0xe1, 0x43, 0x81, 0x5c, 0xd4, 0xba, 0xdc, 0x3a, 0xd6, 0x65, 0xeb, 0xe4, 0xaa,
	0xdb, 0xff, 0xda, 0xaa, 0x86, 0x32, 0x6a, 0x88, 0x76, 0x56, 0x36, 0x94, 0x51, 0x4d, 0xb4, 0xd2,
	0x9c, 0xb3, 0x28, 0x44, 0xb3, 0x29, 0x95, 0xf9, 0x67, 0x09, 0x7c, 0xe0, 0x10, 0x1b, 0xeb, 0xc8,
	0x69, 0xae, 0xa3, 0x97, 0x7f, 0x59, 0x8d, 0xaf, 0xcd, 0x1a, 0xd9, 0x5b, 0x79, 0xd3, 0xb7, 0x30,
	0xd6, 0xbd, 0xa8, 0x7f, 0x2e, 0xc6, 0x92, 0xf8, 0x8d, 0x16, 0x5d, 0x3f, 0xab, 0x69, 0xa1, 0xd1,
	0xfb, 0x6f, 0x60, 0xf0, 0x03, 0x8a, 0xdb, 0x87, 0x22, 0x12, 0x8f, 0x52, 0x65, 0xff, 0x2b, 0xf8,
	0x16, 0xc6, 0x7b, 0xbb, 0x8d, 0xb8, 0x2a, 0xc1, 0x81, 0x95, 0x77, 0x7d, 0xb9, 0xd0, 0x7f, 0xa5,
	0x16, 0xe5, 0x5f, 0xa9, 0xc5, 0xad, 0xfc, 0x2b, 0x45, 0xd6, 0x70, 0x71, 0x50, 0xf1, 0xe4, 0xd3,
	0x83, 0x0a, 0x36, 0xdb, 0xe7, 0xfa, 0x94, 0x95, 0x93, 0xef, 0x60, 0xb4, 0xf3, 0x75, 0x26, 0x97,
	0x3b, 0x01, 0x65, 0xa2, 0xc3, 0x38, 0xbf, 0xef, 0xa8, 0x3a, 0xbf, 0xfe, 0x2f, 0x00, 0x00, 0xff,
	0xff, 0x85, 0xd6, 0x50, 0x3d, 0x1d, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualCardServiceClient is the client API for VirtualCardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualCardServiceClient interface {
	// 创建虚拟卡任务
	CreateVirtualCard(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*VirtualBaseResponse, error)
	// 创建会员卡
	GetEquityList(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*VirtualBaseResponse, error)
	// 申请注销虚拟卡券
	CancelVirtualCard(ctx context.Context, in *CancelVirtualCardReq, opts ...grpc.CallOption) (*empty.Empty, error)
	// 获取申请注销虚拟卡券记录列表
	VirtualCardCancelList(ctx context.Context, in *VirtualCardCancelListReq, opts ...grpc.CallOption) (*VirtualCardCancelListRes, error)
	// 获取申请注销虚拟卡券记录列表
	VirtualCardList(ctx context.Context, in *VirtualCardListReq, opts ...grpc.CallOption) (*VirtualCardListRes, error)
}

type virtualCardServiceClient struct {
	cc *grpc.ClientConn
}

func NewVirtualCardServiceClient(cc *grpc.ClientConn) VirtualCardServiceClient {
	return &virtualCardServiceClient{cc}
}

func (c *virtualCardServiceClient) CreateVirtualCard(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*VirtualBaseResponse, error) {
	out := new(VirtualBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VirtualCardService/CreateVirtualCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualCardServiceClient) GetEquityList(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*VirtualBaseResponse, error) {
	out := new(VirtualBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VirtualCardService/GetEquityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualCardServiceClient) CancelVirtualCard(ctx context.Context, in *CancelVirtualCardReq, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/cc.VirtualCardService/CancelVirtualCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualCardServiceClient) VirtualCardCancelList(ctx context.Context, in *VirtualCardCancelListReq, opts ...grpc.CallOption) (*VirtualCardCancelListRes, error) {
	out := new(VirtualCardCancelListRes)
	err := c.cc.Invoke(ctx, "/cc.VirtualCardService/VirtualCardCancelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualCardServiceClient) VirtualCardList(ctx context.Context, in *VirtualCardListReq, opts ...grpc.CallOption) (*VirtualCardListRes, error) {
	out := new(VirtualCardListRes)
	err := c.cc.Invoke(ctx, "/cc.VirtualCardService/VirtualCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualCardServiceServer is the server API for VirtualCardService service.
type VirtualCardServiceServer interface {
	// 创建虚拟卡任务
	CreateVirtualCard(context.Context, *CreateRequest) (*VirtualBaseResponse, error)
	// 创建会员卡
	GetEquityList(context.Context, *CreateRequest) (*VirtualBaseResponse, error)
	// 申请注销虚拟卡券
	CancelVirtualCard(context.Context, *CancelVirtualCardReq) (*empty.Empty, error)
	// 获取申请注销虚拟卡券记录列表
	VirtualCardCancelList(context.Context, *VirtualCardCancelListReq) (*VirtualCardCancelListRes, error)
	// 获取申请注销虚拟卡券记录列表
	VirtualCardList(context.Context, *VirtualCardListReq) (*VirtualCardListRes, error)
}

// UnimplementedVirtualCardServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVirtualCardServiceServer struct {
}

func (*UnimplementedVirtualCardServiceServer) CreateVirtualCard(ctx context.Context, req *CreateRequest) (*VirtualBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVirtualCard not implemented")
}
func (*UnimplementedVirtualCardServiceServer) GetEquityList(ctx context.Context, req *CreateRequest) (*VirtualBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquityList not implemented")
}
func (*UnimplementedVirtualCardServiceServer) CancelVirtualCard(ctx context.Context, req *CancelVirtualCardReq) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelVirtualCard not implemented")
}
func (*UnimplementedVirtualCardServiceServer) VirtualCardCancelList(ctx context.Context, req *VirtualCardCancelListReq) (*VirtualCardCancelListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VirtualCardCancelList not implemented")
}
func (*UnimplementedVirtualCardServiceServer) VirtualCardList(ctx context.Context, req *VirtualCardListReq) (*VirtualCardListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VirtualCardList not implemented")
}

func RegisterVirtualCardServiceServer(s *grpc.Server, srv VirtualCardServiceServer) {
	s.RegisterService(&_VirtualCardService_serviceDesc, srv)
}

func _VirtualCardService_CreateVirtualCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualCardServiceServer).CreateVirtualCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VirtualCardService/CreateVirtualCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualCardServiceServer).CreateVirtualCard(ctx, req.(*CreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualCardService_GetEquityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualCardServiceServer).GetEquityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VirtualCardService/GetEquityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualCardServiceServer).GetEquityList(ctx, req.(*CreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualCardService_CancelVirtualCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelVirtualCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualCardServiceServer).CancelVirtualCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VirtualCardService/CancelVirtualCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualCardServiceServer).CancelVirtualCard(ctx, req.(*CancelVirtualCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualCardService_VirtualCardCancelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VirtualCardCancelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualCardServiceServer).VirtualCardCancelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VirtualCardService/VirtualCardCancelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualCardServiceServer).VirtualCardCancelList(ctx, req.(*VirtualCardCancelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualCardService_VirtualCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VirtualCardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualCardServiceServer).VirtualCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VirtualCardService/VirtualCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualCardServiceServer).VirtualCardList(ctx, req.(*VirtualCardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualCardService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.VirtualCardService",
	HandlerType: (*VirtualCardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVirtualCard",
			Handler:    _VirtualCardService_CreateVirtualCard_Handler,
		},
		{
			MethodName: "GetEquityList",
			Handler:    _VirtualCardService_GetEquityList_Handler,
		},
		{
			MethodName: "CancelVirtualCard",
			Handler:    _VirtualCardService_CancelVirtualCard_Handler,
		},
		{
			MethodName: "VirtualCardCancelList",
			Handler:    _VirtualCardService_VirtualCardCancelList_Handler,
		},
		{
			MethodName: "VirtualCardList",
			Handler:    _VirtualCardService_VirtualCardList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/virtual_card.proto",
}

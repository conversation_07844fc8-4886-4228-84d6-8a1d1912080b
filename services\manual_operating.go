package services

import (
	"context"
	"order-center/proto/oc"
	"time"
)

/*
定时任务执行失败，手动处理的任务集合
*/

//手动给订单打标签
func (o *OrderService) MarkingTagForOrder(ctx context.Context, req *oc.MarkingTagForOrderReq) (*oc.BaseResponse, error) {
	MakingTagForOrder(req.OrderMarkingTime)
	return &oc.BaseResponse{
		Code:    200,
		Message: "",
		Error:   "",
	}, nil
}

//给订单打标签
func MakingTagForOrder(newCustomerTimeStr string) {
	NowTimeStr := time.Now().Format("2006-01-02 15:04:05")
	//连接池勿关闭
	db := GetDBConn()
	redisConn := GetRedisConn()
	orderList, err := db.Query(`SELECT b.order_sn,a.create_time,a.member_id FROM dc_order.order_main a
	INNER JOIN dc_order.order_detail b ON a.order_sn = b.order_sn
	WHERE a.channel_id = 1 AND b.is_new_customer = 0 AND a.create_time BETWEEN ? AND ? ORDER BY a.create_time ASC; `, newCustomerTimeStr, NowTimeStr)
	if err != nil {
		return
	}
	if len(orderList) > 0 {
		for _, v := range orderList {
			//判断是否存在
			total := 0
			sql := "SELECT COUNT(1) total FROM dc_order.order_main WHERE channel_id in (1,9) and order_status in (20,30) and member_id = ? AND create_time < ?;"
			_, err := db.SQL(sql, v["member_id"], v["create_time"]).Get(&total)
			if err != nil {
				NowTimeStr = string(v["create_time"])
				break
			}
			if total > 0 {
				sql = "UPDATE dc_order.order_detail SET is_new_customer = 2 WHERE order_sn = ?;"
			} else {
				sql = "UPDATE dc_order.order_detail SET is_new_customer = 1 WHERE order_sn = ?;"
			}
			_, err = db.Exec(sql, string(v["order_sn"]))
			if err != nil {
				NowTimeStr = string(v["create_time"])
				break
			}
			NowTimeStr = string(v["create_time"])
		}
	}
	redisConn.Set("ordercenter:newcustomer:timestr", NowTimeStr, -1)
}

package export

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/dac"
	"order-center/proto/oc"
	"order-center/services"
	"order-center/utils"
	"os"
	"strings"
	"sync"
	"time"
)

type CommunityGroupMemberChildOrderExport struct {
	F              *excelize.File
	SheetName      string
	exportFileName string
	storeMap       map[string]*dac.StoreInfo
	taskParams     *oc.CommunityGroupOrderListRequest
	writer         *excelize.StreamWriter
}

func (e *CommunityGroupMemberChildOrderExport) DataExport(taskParams string) (nums int, err error) {
	e.taskParams = new(oc.CommunityGroupOrderListRequest)
	err = json.Unmarshal([]byte(taskParams), e.taskParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	e.taskParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.taskParams.PageSize = 10000
	// 导出文件名称
	typeName := "实物"
	if e.taskParams.RequestFrom == 2 {
		typeName = "虚拟"
	}
	e.exportFileName = "团长拼团-导出团员" + typeName + "订单数据(" + time.Now().Format("20060102150405") + ").xlsx"

	//获取门店信息
	e.storeMap, err = createStoreInfoToMap(e.taskParams.ShopIds)
	if err != nil {
		err = errors.New("获取门店信息失败, " + err.Error())
		return
	}
	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()

	phoneExport, _ := config.Get("export-phone-shop")
	phoneExportMap := make(map[string]struct{})
	if phoneExport != "" {
		phoneExportSlice := strings.Split(phoneExport, ",")
		for _, v := range phoneExportSlice {
			phoneExportMap[v] = struct{}{}
		}
	}

	var details []*oc.AwenCommunityGroupMemberOrderExport
	var cg CommunityGroupMemberOrderExport
	k := 0
	for {
		details, err = awenCommunityGroupMemberOrderExport(e.taskParams, e.taskParams.RequestFrom)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return
		}
		e.taskParams.PageIndex += 1
		for i := 0; i < len(details); i++ {
			k++
			storeInfo := e.getStoreInfo(details[i].ShopId)
			_, ok := phoneExportMap[details[i].ShopId]
			axis := fmt.Sprintf("A%d", k+1)
			//店铺类型
			ShopType := "新瑞鹏"
			if details[i].AppChannel != 1 {
				ShopType = "TP代运营"
			}

			_ = e.writer.SetRow(axis, []interface{}{
				details[i].OrderSn,                                        // 订单号
				details[i].ParentOrderSn,                                  // 父订单号
				cg.getGroupTypeText(details[i].GroupType),                 // 商品筛选
				details[i].CreateTime,                                     // 下单时间
				details[i].PaySn,                                          // 支付流水号
				details[i].PayTime,                                        // 支付时间
				kit.FenToYuan(details[i].Total),                           // 实收金额（实收金额）
				kit.FenToYuan(details[i].ActualReceiveTotal),              // 商家预计收入
				kit.FenToYuan(details[i].GoodsTotal + details[i].Freight), // 订单原价
				"-" + cast.ToString(kit.FenToYuan(details[i].Privilege-details[i].PlatformPayedAmount)), // 商家补贴
				// kit.FenToYuan(details[i].Privilege-details[i].PlatformPayedAmount), // 优惠金额
				details[i].RefundAmount, // 退款金额
				kit.FenToYuan(details[i].Total - details[i].PackingCost - (details[i].Freight - details[i].FreightPrivilege)), // 商品实付金额
				kit.FenToYuan(details[i].Freight),                           // 配送费
				kit.FenToYuan(details[i].PackingCost),                       // 包装费
				0 - kit.FenToYuan(details[i].FreightPrivilege),              // 配送费优惠
				0 - kit.FenToYuan(details[i].PlatformPayedAmount),           // 平台补贴
				0 - kit.FenToYuan(details[i].ServiceCharge),                 // 平台服务费
				0 - kit.FenToYuan(details[i].ContractFee),                   // 履约服务费
				services.PayMode[details[i].PayMode],                        // 支付方式 1支付宝 2微信 3美团支付
				storeInfo.Bigregion,                                         // 大区
				storeInfo.City,                                              // 省
				storeInfo.Name,                                              // 门店名称
				storeInfo.FinanceCode,                                       // 财务编码
				services.OrderFrom[details[i].ChannelId],                    // 订单来源
				cg.getUserAgent(details[i].UserAgent),                       // 销售渠道
				details[i].MemberId,                                         // 用户ID
				cg.getCustomerTag(details[i]),                               // 顾客类型，默认0,1-新顾客，2-老顾客
				cg.getReceiverName(details[i]),                              // 收货人姓名
				cg.getReceiverMobile(ok, details[i]),                        // 收货人联系方式
				cg.getReceiverAddress(ok, details[i]),                       // 收货人地址
				details[i].BuyerMemo,                                        // 备注
				services.DeliveryType[details[i].DeliveryType],              // 配送方式
				services.OrderStatusMap[details[i].OrderStatusChild],        // 订单状态
				details[i].ActivityType,                                     // 活动类型
				details[i].PerformanceStaffName,                             // 业务员
				details[i].PerformanceOperatorName,                          // 业绩分配人
				details[i].PerformanceOperatorTime,                          // 业绩分配时间
				details[i].Category,                                         // 仓库类型(门店类型)
				details[i].WarehouseName,                                    // 仓库名称
				details[i].StorePayDeliveryAmount,                           // 店铺支付配送费
				ShopType,                                                    // 店铺类型
				details[i].PickupStationName,                                // 提货点名称
				details[i].PickupStationAddress,                             // 提货点地址
				details[i].ExpectedTime,                                     // 送达时间
				cg.getGroupStatusText(details[i].GroupActivityModel.Status), // 拼团状态
				details[i].GroupActivityModel.StaffName,                     // 分销员姓名
				details[i].GroupActivityModel.DisChainName,                  // 分销员单位
				details[i].ShopDisModel.StaffName,                           // 业绩所属人
				details[i].ShopDisModel.ChainName,                           // 业绩所属单位
				details[i].ShopDisModel.MemberAreaName,                      // 业绩所属人所在城市
				details[i].ShopDisModel.FinanceCode,                         // 业绩所属单位财务编码
				details[i].GroupActivityModel.MemberName,                    // 团长名
				details[i].GroupActivityModel.ReceiverMobile,                // 团长电话
				details[i].GroupActivityModel.ReceiverAddress,               // 团长地址
			})
		}
		if len(details) < int(e.taskParams.PageSize) {
			break
		}
	}
	nums = k
	_ = e.writer.Flush()
	return
}

func (e *CommunityGroupMemberChildOrderExport) SetSheetName() {
	nameList := []interface{}{
		"订单号", "父订单号", "商品筛选", "下单时间", "支付流水号", "支付时间", "用户实付", "商家预计收入", "订单原价", "优惠金额", "退款金额",
		"商品实付总金额", "配送费", "包装费", "配送费优惠", "平台补贴", "平台服务费", "履约服务费", "支付方式", "大区",
		"城市", "店铺名称", "财务编码", "订单来源", "销售渠道", "用户ID", "顾客类型", "收货人", "收货人联系方式", "收货人地址",
		"配送备注", "配送方式", "订单状态", "活动类型", "业绩归属人", "业绩分配人", "业绩分配时间",
		"仓库类型", "仓库名称", "门店支付配送费", "店铺类型", "提货点名称", "提货点地址", "送达时间",
		"拼团状态", "分销员姓名", "分销员单位", "业绩所属人", "业绩所属单位", "业绩所属人所在城市", "业绩所属单位财务编码",
		"团长名", "团长电话", "团长地址",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e *CommunityGroupMemberChildOrderExport) GenerateDownUrl() (string, error) {
	fileName := fmt.Sprintf("团长拼团-导出团员订单数据(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	if e.exportFileName != "" {
		fileName = e.exportFileName
	}
	err := e.F.SaveAs(fileName)
	if err != nil {
		return "", errors.New("文件创建失败, " + err.Error())
	}
	defer os.Remove(fileName)

	//同步文件到七牛云
	url, err := utils.UploadExcelToQiNiu(fileName)
	if err != nil {
		return "", errors.New("文件上传失败, " + err.Error())
	}
	return url, nil
}

func (e *CommunityGroupMemberChildOrderExport) getStoreInfo(ShopId string) *dac.StoreInfo {
	storeInfo := &dac.StoreInfo{}
	if value, ok := e.storeMap[ShopId]; ok {
		storeInfo = value
	}
	return storeInfo
}

// 阿闻管家团长制拼团订单-导出团员订单数据, t 0-默认父订单，1-实物子订单，2-虚拟子订单
func awenCommunityGroupMemberOrderExport(params *oc.CommunityGroupOrderListRequest, t int32) (details []*oc.AwenCommunityGroupMemberOrderExport, err error) {
	glog.Info("阿闻管家团长制拼团订单-导出团员订单数据：", kit.JsonEncode(params))
	dbConn := services.NewSlaveDbConn()
	//dbConn.ShowSQL(true)
	defer dbConn.Close()
	//订单表，商品表，品牌表, 业绩表关联查询
	session := dbConn.Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Join("left", "datacenter.pickup_station", "pickup_station.id=order_detail.pickup_station_id")
	if t == 1 || t == 2 {
		session.Join("inner", "order_main_group", "order_main_group.parent_order_sn = order_main.parent_order_sn")
	} else {
		session.Join("inner", "order_main_group", "order_main_group.parent_order_sn = order_main.order_sn")
	}
	session.Join("inner", "order_group_activity", "order_main_group.order_group_activity_id = order_group_activity.id").
		Join("left", "order_product", "order_main.order_sn=order_product.order_sn").
		Where("order_main.order_type = 15")

	// 导出类型
	switch t {
	case 1: // 实物子订单
		session.Where("order_main.parent_order_sn != order_main.order_sn and order_main.is_virtual = 0")
	case 2: // 虚拟子订单
		session.Where("order_main.parent_order_sn != order_main.order_sn and order_main.is_virtual = 1")
	default:
		session.Where("order_main.parent_order_sn = order_main.order_sn or order_main.parent_order_sn = ''")
	}

	session.OrderBy("order_main.create_time DESC")

	if params.GroupStatus != "" {
		session.Where("order_group_activity.status=?", params.GroupStatus)
	}
	params.Keyword = strings.Trim(params.Keyword, " ")
	if len(params.Keyword) > 0 {
		switch params.SearchType {
		case "1": //团长手机
			var scrmUserIds []string
			err = services.GetUPetDBConn().Table("upet_member").
				Select("scrm_user_id").
				Where("member_mobile like ?", "%"+params.Keyword+"%").
				Where("scrm_user_id != ?", "").
				Find(&scrmUserIds)
			if err != nil {
				err = errors.New("订单导出查询订单列表错误, " + err.Error())
				return
			}
			if len(scrmUserIds) > 0 {
				session.In("order_group_activity.member_id", scrmUserIds)
			} else {
				session.Where("1=0")
			}
		case "2": //团编码
			session.Where("order_group_activity.id = ?", params.Keyword)
		case "3": //财务编码
			session.Where("order_group_activity.dis_chain_finance_code like ?", "%"+params.Keyword+"%")
		case "4": //团长单位名
			session.Where("order_group_activity.dis_chain_name like ?", "%"+params.Keyword+"%")
		}
	}
	if params.GroupTakeType != "" {
		session.Where("order_group_activity.final_take_type = ?", params.GroupTakeType)
	}
	switch params.TimeType {
	case "1":
		session.Where("order_group_activity.created_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
	case "2":
		session.Where("order_group_activity.status = 1 and order_group_activity.end_time BETWEEN ? AND ?", params.StartTime, params.EndTime)
	case "3":
		session.Where("order_group_activity.status = 2 and order_group_activity.end_time BETWEEN ? AND ?", params.StartTime, params.EndTime)
	}
	//筛选用户权限门店
	if len(params.UserNo) > 0 {
		session.Join("inner", "datacenter.store_user_authority sua", "order_main.shop_id=sua.finance_code AND sua.user_no=?", params.UserNo)
	}

	//登录用户有权限的所有门店id(财务编码)
	if len(params.ShopIds) > 0 {
		session.In("order_main.shop_id", params.ShopIds)
	}
	if err = session.Select(`CASE
		child_channel_id 
		WHEN '' THEN
		order_main.channel_id ELSE child_channel_id 
		END channel_id,
		order_main.*,
		order_main.old_order_sn gy_order_sn,
		order_detail.push_delivery,
		order_detail.push_delivery_reason,
		order_detail.push_third_order,
		order_detail.push_third_order_reason,
		order_detail.accept_time,
		order_detail.delivery_remark,
		order_detail.buyer_memo,
		order_detail.is_picking,
		order_detail.picking_time,
		order_detail.extras,
		order_detail.pickup_code serial_number,
		order_detail.performance_staff_name,
		order_detail.performance_operator_name,
		order_detail.is_new_customer,
		order_detail.performance_operator_time,
		order_detail.expected_time,
		order_product.product_id,
        MAX(order_product.combine_type) group_type,
		pickup_station.name as pickup_station_name,
		pickup_station.address as pickup_station_address,
		order_group_activity.id as order_group_activity_id,
		order_group_activity.member_id as order_group_member_id,
		order_detail.shop_dis_member_id,
		order_detail.shop_dis_chain_id,
		order_main_group.receiver_name as group_member_receiver_name,
		order_main_group.receiver_mobile as group_member_receiver_mobile,
		order_main_group.receiver_address as group_member_receiver_address
		`).
		Limit(int(params.PageSize), int(params.PageIndex*params.PageSize)-int(params.PageSize)).
		GroupBy("order_main.id").
		OrderBy("if(order_main.order_status_child=20101, 0, 1), if(order_detail.push_third_order=0 and order_main.order_status=20, 0, 1), if(order_main.order_type in (2,3) and order_main.order_status NOT IN (0, 30), 0, 1)").
		Find(&details); err != nil {
		glog.Error("zx订单导出报20221124")
		err = errors.New("订单导出查询订单列表错误, " + err.Error())
		return
	}
	if len(details) == 0 {
		return
	}
	//查询出来的是主单的退款
	var orderSns []string
	var orderGroupActivityIds []int32
	var shopDisMemberIds []string
	var awenOrderSn, thirdOrderSn []string
	var shopDisChainId []int32
	for _, v := range details {
		orderSns = append(orderSns, v.OrderSn)
		if isThirdChannel(v.ChannelId) {
			thirdOrderSn = append(thirdOrderSn, v.OrderSn)
		} else {
			awenOrderSn = append(awenOrderSn, v.OrderSn)
		}
		if v.OrderGroupActivityId > 0 {
			orderGroupActivityIds = append(orderGroupActivityIds, v.OrderGroupActivityId)
		}
		if len(v.ShopDisMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.ShopDisMemberId)
		}
		if len(v.OrderGroupMemberId) > 0 {
			shopDisMemberIds = append(shopDisMemberIds, v.OrderGroupMemberId)
		}
		if v.ShopDisChainId > 0 {
			shopDisChainId = append(shopDisChainId, v.ShopDisChainId)
		}
	}

	//查询退款信息
	mainOrderRefund := &services.MainOrderRefund{
		AwenOrderSn:  awenOrderSn,
		ThirdOrderSn: thirdOrderSn,
	}
	//计算退款金额
	refundAmountMap := mainOrderRefund.CalRefundAmount()

	//查询订单优惠信息
	orderPromotionMap := map[string][]*models.OrderPromotion{}
	if err = func() error {
		var orderPromotion []*models.OrderPromotion
		if err = dbConn.Select("order_sn,promotion_type,pt_charge").In("order_sn", orderSns).Find(&orderPromotion); err != nil {
			return err
		}

		//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
		for k := range orderPromotion {
			orderPromotionMap[orderPromotion[k].OrderSn] = append(orderPromotionMap[orderPromotion[k].OrderSn], orderPromotion[k])
		}

		return nil
	}(); err != nil {
		err = errors.New("查询订单优惠信息失败, " + err.Error())
		return
	}

	orderGroupActivityMap := map[int32]*models.OrderGroupActivity{}
	upetMemberChainMap := map[string]*models.UpetMemberChain{}
	disCommissionMap := map[string]*models.OrderDis{}
	upetChainMap := map[int32]*models.UpetChain{}

	//查询订单优惠信息
	var deliveryRecords []*models.OrderDeliveryRecord

	var allChildOrderSn []string
	parentAndChildRelationMap := make(map[string][]string)
	//运费信息中的订单号为子订单号 需要根据子订单查询运费
	//查询子订单
	var childOrders []*models.OrderMain

	wg := sync.WaitGroup{}
	wg.Add(5)
	// 补全团长制拼团的信息
	go func() {
		defer wg.Done()
		if len(orderGroupActivityIds) > 0 {
			var orderGroupActivity []*models.OrderGroupActivity
			if err = dbConn.Select("").
				Table(&models.OrderGroupActivity{}).
				In("id", orderGroupActivityIds).
				Find(&orderGroupActivity); err != nil {
				glog.Error("订单列表查询团长制拼团的信息失败，", err.Error())
			}
			for k, v := range orderGroupActivity {
				orderGroupActivityMap[cast.ToInt32(v.Id)] = orderGroupActivity[k]
			}
		}
	}()

	// 补全业绩 与 绑定门店的信息
	go func() {
		defer wg.Done()
		if len(shopDisMemberIds) > 0 {
			upetMemberChain := &models.UpetMemberChain{}
			upetMemberChainList, err := upetMemberChain.FindInScrmUserId(services.GetUPetDBConn(), shopDisMemberIds)
			if err != nil {
				glog.Error("订单列表查询业绩会员信息查询失败，", err.Error())
			}
			for k, v := range upetMemberChainList {
				upetMemberChainMap[v.ScrmUserId] = upetMemberChainList[k]
			}
		}
	}()

	// 补全佣金信息
	go func() {
		defer wg.Done()
		var orderDis []*models.OrderDis
		if err = dbConn.Table("order_dis").
			In("parent_order_sn", orderSns).
			Select("parent_order_sn, sum(commission) as commission").
			GroupBy("parent_order_sn").
			Find(&orderDis); err != nil {
			glog.Error("订单列表查询数据库查询失败，", err.Error())
		}
		for k, v := range orderDis {
			disCommissionMap[v.ParentOrderSn] = orderDis[k]
		}
	}()

	go func() {
		defer wg.Done()
		err = dbConn.Select("order_sn,parent_order_sn,old_order_sn").In("parent_order_sn", orderSns).Find(&childOrders)
		if err == nil {
			if len(childOrders) > 0 {
				for _, v := range childOrders {
					allChildOrderSn = append(allChildOrderSn, v.OrderSn)
					if _, has := parentAndChildRelationMap[v.ParentOrderSn]; has {
						parentAndChildRelationMap[v.ParentOrderSn] = append(parentAndChildRelationMap[v.ParentOrderSn], v.OldOrderSn)
					} else {
						parentAndChildRelationMap[v.ParentOrderSn] = []string{v.OrderSn}
					}
				}
			}
		}
	}()

	go func() {
		defer wg.Done()
		var upetChainList []*models.UpetChain
		err := services.GetUPetDBConn().Table("upet_chain").
			Select("chain_id,chain_name,account_id").
			In("chain_id", shopDisChainId).
			Find(&upetChainList)
		if err == nil {
			for i := 0; i < len(upetChainList); i++ {
				upetChainMap[upetChainList[i].ChainId] = upetChainList[i]
			}
		}
	}()

	wg.Wait()

	if len(allChildOrderSn) > 0 {
		//取配送流程最新一条记录并且骑手姓名不为空且状态不为99的数据
		if err := dbConn.Table("order_delivery_record").
			Select("order_delivery_record.*").
			Join("inner", "order_delivery_node", "order_delivery_record.delivery_id = order_delivery_node.delivery_id").
			Where("order_delivery_node.courier_name != '' AND order_delivery_node.delivery_status !=99").
			In("order_delivery_record.order_sn", allChildOrderSn).
			GroupBy("order_delivery_record.order_sn").
			OrderBy("order_delivery_record.id DESC,order_delivery_node.create_time DESC").
			Find(&deliveryRecords); err != nil {
			glog.Error("订单导出查询订单列表错误！ ", err.Error())
		}
	}

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	deliveryRecordsMap := make(map[string]*models.OrderDeliveryRecord, len(deliveryRecords))

	//range第二个参数为值复制, 大号切片在循环时可选择忽略, 提高性能
	for _, v := range deliveryRecords {
		deliveryRecordsMap[v.OrderSn] = v
	}

	var activityList []string
	var platformPayedAmount int32
	for _, v := range details {
		activityList = activityList[:0]
		platformPayedAmount = 0
		//优惠活动解析
		for _, promotion := range orderPromotionMap[v.OrderSn] {
			activityList = append(activityList, dto.OrderPrivilegeActiveType(promotion.PromotionType).String())
			platformPayedAmount += promotion.PtCharge
		}
		// 添加平台补贴
		v.PlatformPayedAmount = platformPayedAmount
		if len(activityList) > 0 {
			v.ActivityType = strings.Join(activityList, ";")
		} else {
			v.ActivityType = ""
		}
		//前端的商品总额是不包含所有其他优惠运费服务费的, 而后端的商品总额是减去运费的, 所以要加上
		//门店营收金额 = 客户实付金额+(优惠明细)美团承担的成本-平台服务费
		v.ActualReceiveTotal = v.Total + platformPayedAmount - v.ServiceCharge
		switch v.Source {
		case 1, 4:
			v.Source = 4
		case 5:
			v.Source = 1
		default:
			v.Source = 3
		}
		v.Category = services.Category[v.Source]
		//if params.CombineType == 1 || params.CombineType == 2 {
		//	v.GroupType = params.CombineType
		//} else if params.CombineType == 4 {
		//	v.GroupType = 0
		//}
		childInfo, _ := parentAndChildRelationMap[v.OrderSn]
		if len(deliveryRecordsMap) > 0 {
			//配送费转成元，没有则为空
			for _, childOrderSn := range childInfo {
				if deliveryInfo, has := deliveryRecordsMap[childOrderSn]; has {
					if len(deliveryInfo.TotalFeeAfter) > 0 {
						intStorePayDeliveryAmount := cast.ToFloat64(v.StorePayDeliveryAmount)
						intTotalFeeAfter := kit.FenToYuan(cast.ToInt32(deliveryInfo.TotalFeeAfter))
						v.StorePayDeliveryAmount = cast.ToString(intStorePayDeliveryAmount + intTotalFeeAfter)
					}
				}
			}
		}
		if refundAmount, ok := refundAmountMap[v.OrderSn]; ok {
			v.RefundAmount = cast.ToString(refundAmount)
		}

		//团长拼团制的一些信息
		v.GroupActivityModel = &oc.SimpleOrderGroupActivity{}
		if v1, ok := orderGroupActivityMap[v.OrderGroupActivityId]; ok {
			v.GroupActivityModel.FinalTakeType = v1.FinalTakeType
			v.GroupActivityModel.Status = v1.Status
			v.GroupActivityModel.DisChainName = v1.DisChainName
			v.GroupActivityModel.MemberName = v1.MemberName
			v.GroupActivityModel.ReceiverAddress = v1.ReceiverAddress
			//导出的时候手机带星
			v.GroupActivityModel.ReceiverMobile = v1.ReceiverMobile
			v.GroupActivityModel.EnReceiverMobile = v1.EnReceiverMobile
		}
		if v1, ok := disCommissionMap[v.OrderSn]; ok {
			v.GroupActivityModel.DisCommission = cast.ToInt32(v1.Commission)
		}
		if v1, ok := upetMemberChainMap[v.OrderGroupMemberId]; ok {
			if len(v1.BillUserName) > 0 {
				v.GroupActivityModel.StaffName = v1.BillUserName
			} else {
				v.GroupActivityModel.StaffName = services.MobileReplaceWithStar(v1.MemberMobile)
			}
		}
		//业绩的一些信息
		v.ShopDisModel = &oc.SimpleOrderShopDis{}
		if v1, ok := upetMemberChainMap[v.ShopDisMemberId]; ok {
			if len(v1.BillUserName) > 0 {
				v.ShopDisModel.StaffName = v1.BillUserName
			} else {
				v.ShopDisModel.StaffName = services.MobileReplaceWithStar(v1.MemberMobile)
			}
			v.ShopDisModel.MemberAreaName = v1.MemberAreaName
		}
		if v1, ok := upetChainMap[v.ShopDisChainId]; ok {
			v.ShopDisModel.ChainName = v1.ChainName
			v.ShopDisModel.FinanceCode = v1.AccountId
		}

		//如果非团长待收，处理一下团员地址
		if v.GroupActivityModel.FinalTakeType == 0 {
			v.GroupMemberReceiverName = v.ReceiverName
			v.GroupMemberReceiverMobile = v.ReceiverMobile
			v.GroupMemberReceiverAddress = v.ReceiverAddress
		}
	}
	return
}

func isThirdChannel(channelId int32) bool {
	if channelId == services.ChannelMtId || channelId == services.ChannelJddjId || channelId == services.ChannelElmId {
		return true
	} else {
		return false
	}
}

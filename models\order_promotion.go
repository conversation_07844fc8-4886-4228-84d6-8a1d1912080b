package models

import "time"

type OrderPromotion struct {
	Id             int64     `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn        string    `xorm:"not null default '''' comment('订单号码') index VARCHAR(32)"`
	PromotionId    int64     `xorm:"not null default 0 comment('活动Id') BIGINT(20)"`
	PromotionType  int32     `xorm:"not null default 1 comment('活动类型 1满减') INT(11)"`
	PromotionTitle string    `xorm:"not null default '''' comment('活动名称') VARCHAR(256)"`
	PoiCharge      int32     `xorm:"not null default 0 comment('商家优惠') INT(11)"`
	PtCharge       int32     `xorm:"not null default 0 comment('平台优惠') INT(11)"`
	PromotionFee   int32     `xorm:"not null default 0 comment('总优惠金额(商家加平台总和)') INT(11)"`
	CreateTime     time.Time `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime     time.Time `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	CardId         string    `json:"card_id"`    //卡或者券ID
	ChannelId      int       `json:"channel_id"` //1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,100线下门店',

}

package models

import (
	"time"
)

type VipCardOrder struct {
	Id             int32     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	CardId         int       `xorm:"not null comment('卡模板id') INT(11)"`
	OrderSn        string    `json:"order_sn" xorm:"not null comment('订单id') VARCHAR(50) 'order_sn'"`
	OrderAmount    int32     `json:"order_amount" xorm:"not null default 0 comment('订单金额(分)') INT(11) 'order_amount'"`
	UserId         string    `json:"user_id" xorm:"not null comment('手机号') VARCHAR(32) 'user_id'"`
	UserMobile     string    `json:"user_mobile" xorm:"not null comment('加密手机号') VARCHAR(20) 'user_mobile'"`
	EnUserMobile   string    `json:"en_user_mobile" xorm:"not null comment('用户id') VARCHAR(50) 'en_user_mobile'"`
	UserName       string    `json:"user_name" xorm:"not null comment('用户名称') VARCHAR(50) 'user_name'"`
	UserLevelId    int32     `json:"user_level_id" xorm:"not null default 0 comment('用户等级') TINYINT(1) 'user_level_id'"`
	CardName       string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(20) 'card_name'"`
	CardType       int32     `json:"card_type" xorm:"not null default 0 comment('卡类型') TINYINT(1) 'card_type'"`
	CardCycle      int32     `json:"card_cycle" xorm:"not null default 0 comment('周期') TINYINT(1) 'card_cycle'"`
	PayTime        time.Time `json:"pay_time" xorm:"default 'null' comment('支付时间') DATETIME 'pay_time'"`
	State          int32     `json:"state" xorm:"default 0 comment('状态 10-已支付  20-已退款') TINYINT(1) 'state'"`
	ExpiryDate     time.Time `json:"expiry_date" xorm:"default 'null' comment('会员卡有效期') DATETIME  'expiry_date'"`
	RefundAmount   int32     `json:"refund_amount" xorm:"default 0 comment('退款金额(分)') INT(11) 'refund_amount'"`
	RefundTime     time.Time `json:"refund_time" xorm:"default 'null' comment('退款时间') DATETIME 'refund_time'"`
	CardPass       string    `xorm:"default '''' comment('卡密') VARCHAR(32)"`
	VirtualCardId  int64     `xorm:"not null default 0 comment('卡号') BIGINT(20)"`
	Source         int32     `xorm:"default 0 comment('来源 0主动购买 1分销购买，2虚拟卡券兑换') TINYINT(4)"`
	Region         string    `xorm:"default '''' comment('大区') VARCHAR(50)"`
	Province       string    `xorm:"default '''' comment('省') VARCHAR(50)"`
	City           string    `xorm:"default '''' comment('市') VARCHAR(50)"`
	DisMemberName  string    `xorm:"default '''' comment('分销人名称') VARCHAR(50)"`
	DisMemberId    int32     `xorm:"default 0 comment('分销人id') INT(11)"`
	DisCommission  int32     `xorm:"default 0 comment('分销佣金，分') INT(11)"`
	StoreId        int32     `xorm:"default 0 comment('门店开卡对应门店id') INT(11)"`
	CreateTime     time.Time `json:"create_time" xorm:"created default 'CURRENT_TIMESTAMP' DATETIME 'create_time'"`
	UpdateTime     time.Time `json:"update_time" xorm:"updated default 'CURRENT_TIMESTAMP' DATETIME 'update_time'"`
	CollectionType int32     `json:"collection_type" xorm:"not null default '' comment('订单类型：1-首次开卡、2-续费开卡 3-加购开卡') INT(11) 'collection_type'"`
	EntityOrderSn  string    `json:"entity_order_sn" xorm:"not null default '' comment('实体订单号') VARCHAR(20) 'entity_order_sn'"`

	////下次需要发放月度卡的时间
	//GrantTime time.Time `json:"grant_time" xorm:"created default 'CURRENT_TIMESTAMP' DATETIME 'grant_time'"`
}

////月度发券表
//type VipUserEquityMonth struct {
//	Id              int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
//	OrderSn         string    `json:"order_sn" xorm:"not null default '' comment('订单号') VARCHAR(50) 'order_sn'"`
//	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('订单的创建时间') DATETIME 'create_time'"`
//	EquityId        int8      `json:"equity_id" xorm:"default 'null' comment('权益id') TINYINT 'equity_id'"`
//	EquityCount     int       `json:"equity_count" xorm:"default 'null' comment('领取权益次数') INT 'equity_count'"`
//	BeginTime       time.Time `json:"begin_time" xorm:"default 'CURRENT_TIMESTAMP' comment('下次发券时间') DATETIME 'begin_time'"`
//	EquityShortName string    `json:"equity_short_name" xorm:"default '' comment('领券开始时间') VARCHAR(50) 'equity_short_name'"`
//	EndTime         time.Time `json:"end_time" xorm:"default 'CURRENT_TIMESTAMP' comment('领券结束时间') DATETIME 'end_time'"`
//	Status          int       `json:"status" xorm:"default 0 comment('0未领取，1已领取') INT 'status'"`
//}
//
//func (v VipUserEquityMonth) TableName() string {
//	return "datacenter.vip_user_equity_month"
//}

type VipCardEquity struct {
	Id              int32     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	OrIds           string    `json:"or_ids" xorm:"not null default '' comment('所属大区') varchar(20) 'or_ids'"`
	EquityIcon      string    `json:"equity_icon" xorm:"not null default '' comment('权益icon') VARCHAR(200) 'equity_icon'"`
	EquityName      string    `json:"equity_name" xorm:"default '' comment('权益名称') VARCHAR(50) 'equity_name'"`
	EquityShortName string    `json:"equity_short_name" xorm:"default '' comment('权益短名称') VARCHAR(50) 'equity_short_name'"`
	EquityCopy      string    `json:"equity_copy" xorm:"default 'null' comment('权益方案') TEXT 'equity_copy'"`
	EquityPrice     float64   `json:"equity_price" xorm:"default '0.00' comment('权益价值') DECIMAL(10) 'equity_price'"`
	EquityType      int32     `json:"equity_type" xorm:"default 0 comment('权益类型') TINYINT(1) 'equity_type'"`
	MatchType       int32     `json:"match_type" xorm:"default 0 comment('匹配结果 1-单项填写,2-多项填写') TINYINT(1) 'match_type'"`
	IssueType       int32     `json:"issue_type" xorm:"default 0 comment('发放日期 1-开卡立即下发') TINYINT(1) 'issue_type'"`
	CollectionIds   string    `json:"collection_ids" xorm:"default '' comment('领取类型 1-首次开卡、2-续费开卡') VARCHAR(10) 'collection_ids'"`
	Status          int32     `json:"status" xorm:"default 0 comment('显示状态：0-未生效即将上线，1-生效正常显示') TINYINT(1) 'status'"`
	JumpUrl         string    `json:"jump_url" xorm:"not null default '' comment('跳转链接') VARCHAR(255) 'jump_url'"`
	EquityInfo      string    `json:"equity_info" xorm:"default 'null' comment('权益介绍') TEXT 'equity_info'"`
	EquityRule      string    `json:"equity_rule" xorm:"default 'null' comment('权益规则') TEXT 'equity_rule'"`
	EquityImg       string    `json:"equity_img" xorm:"default 'null' comment('权益宣传图') VARCHAR(255) 'equity_img'"`
	ExpiryDay       int32     `json:"expiry_day" xorm:"default 0 comment('权益有效期') TINYINT(2) 'expiry_day'"`
	ReceiveType     int32     `json:"receive_type" xorm:"default 0 comment('领取个数类型 1-多选一 2-多选多') TINYINT(2) 'receive_type'"`
	IsActive        int32     `json:"is_active" xorm:"default 0 comment('1.主动领取 2.被动领取') TINYINT(2) 'is_active'"`
	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime      time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

func (v VipCardOrder) TableName() string {
	return "datacenter.vip_card_order"
}

type RefundDetailData struct {
	// 权益名称
	EquityId string `json:"equity_id"`
	// 权益名称
	EquityName string `json:"equity_name"`
	// 权益类型
	EquityType int32 `json:"equity_type"`
	// 订单类型
	CollectionType int32 `json:"collection_type"`
	// 领取类型 1-首次开卡、2-续费开卡、3-加购开卡（英文逗号分隔）
	CollectionIds string `json:"collection_ids"`
	// 开卡时间
	PayTime time.Time `json:"pay_time"`
}

type EquityUsedRecord struct {
	// 权益值
	PrivilegeId string `json:"privilege_id"`
	// 礼包电商父订单号
	GiftOrderSn string `json:"gift_order_sn"`
	// 状态:1-投保中 2-投保成功 3-投保失败
	Status int32 `json:"status"`
}

type HealthFee struct {
	// 商品skuId
	GoodsId int32 `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 抵扣额度
	ExpensePrice int32 `json:"expense_price"`
	// 订单号
	OrderSn string `json:"order_sn"`
	// 订单状态：0(已取消)10(默认):未付款;20:已付款;40:已完成;
	OrderState string `json:"order_state"`
	// 商城订单号
	LinkOrderNumber string `json:"link_order_number"`
}

package services

import (
	"order-center/models"
	"strings"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// 根据订单号查询订单主信息
func GetOrderMainByOrderSn(orderSn string, fields ...string) *models.OrderMain {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.OrderMain{}
	if _, err := GetDBConn().Select(fields[0]).Where("order_sn=?", orderSn).Get(m); err != nil {
		glog.Error(orderSn, ", 查询订单主信息失败, ", err)
		return m
	}
	return m
}

// 查询订单商品信息
func GetThirdOrderProduct(orderSn string) (orderProducts []models.OrderProduct) {
	session := GetDBConn().NewSession()
	defer session.Close()
	err := session.Where("order_sn = ? AND parent_sku_id = ''", orderSn).Find(&orderProducts)
	if err != nil {
		glog.Error(orderSn, ", 查询第三方订单主商品信息失败：", err, ", ", kit.RunFuncName(2))
		return
	}
	return
}

// GetChildOrderProductByOrderSn 根据订父订单号查询子订单商品信息
// @param orderSn 父订单号
func GetChildOrderProductByOrderSn(orderSn string) (orderProducts []models.OrderProduct, err error) {
	//step 1 查询所有的子订单号
	var childOrderSn []string
	db := GetDBConn()
	err = db.SQL("SELECT order_sn FROM order_main WHERE parent_order_sn = ? AND order_sn != ?", orderSn, orderSn).Find(&childOrderSn)
	if err != nil {
		return
	}
	if len(childOrderSn) == 0 {
		return
	}
	//step2 根据子订单号查询商品
	err = db.SQL("SELECT * FROM order_product WHERE order_sn IN ('" + strings.Join(childOrderSn, "','") + "')").Find(&orderProducts)
	return
}

// 查询实物子单
func GetChildRealOrderByOrderSn(orderSn string, fields ...string) (realOrder *models.OrderMain, err error) {
	//step 1 查询所有的子订单号
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	realOrder = &models.OrderMain{}
	if _, err = GetDBConn().Select(fields[0]).Where("parent_order_sn=? AND is_virtual = 0", orderSn).Get(realOrder); err != nil {
		return realOrder, err
	}
	return realOrder, nil
}

// GetChildOrderProductByParentSku 根据组合商品sku 与父订单号 查询组合商品的子商品信息
// @param orderSn 父订单号
func GetChildOrderProductByParentSku(orderSn string, parentSkuId string) (orderProducts []models.OrderProduct, err error) {
	//step 1 查询所有的子订单号
	db := GetDBConn()
	err = db.SQL("select a.* FROM order_product a "+
		"INNER JOIN `order_main` b ON a.order_sn=b.order_sn "+
		"WHERE b.parent_order_sn=? AND a.parent_sku_id=? ", orderSn, parentSkuId).Find(&orderProducts)
	return
}

// GetParentOrderProductByParentSku 根据parentSkuId 与父订单号 查询订单中的组合商品父商品信息
// @param orderSn 父订单号
func GetParentOrderProductByParentSku(orderSn string, groupSkuId string) (orderProducts []models.OrderProduct, err error) {
	//step 1 查询所有的子订单号
	db := GetDBConn()
	err = db.SQL("select a.* FROM order_product a "+
		"INNER JOIN `order_main` b ON a.order_sn=b.order_sn "+
		"WHERE b.order_sn=? AND a.sku_id=? ", orderSn, groupSkuId).Find(&orderProducts)
	return
}

// 根据渠道订单号查询订单主信息
func GetOrderMainByOldOrderSn(oldOrderSn string, fields ...string) *models.OrderMain {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.OrderMain{}
	if _, err := GetDBConn().Select(fields[0]).Where("old_order_sn=? or order_sn =?", oldOrderSn, oldOrderSn).Desc("id").Get(m); err != nil {
		glog.Error(oldOrderSn, ", 查询订单主信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询订单详情
func GetOrderDetailByOrderSn(orderSn string, fields ...string) *models.OrderDetail {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.OrderDetail{}
	if _, err := GetDBConn().Select(fields[0]).Where("order_sn=?", orderSn).Get(m); err != nil {
		glog.Error(orderSn, ", 查询订单详情信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询订单总信息
func GetOrderByOrderSnAndMemberId(orderSn, memberId string, fields ...string) *models.Order {
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	m := &models.Order{}
	if _, err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("(order_main.order_sn=?  or old_order_sn=?) and member_id=? ", orderSn, orderSn, memberId).Get(m); err != nil {
		glog.Error(orderSn, ", 查询订单总信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询订单总信息
func GetOrderByOrderSn(orderSn string, fields ...string) *models.Order {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.Order{}
	if _, err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("order_main.order_sn=?  or old_order_sn=?", orderSn, orderSn).Get(m); err != nil {
		glog.Error(orderSn, ", 查询订单信息出错, ", err)
		return m
	}
	return m
}

// 根据订单号查询订单商品信息
func GetOrderProductByOrderSn(orderSn string, fields string) []*models.OrderProduct {
	if fields == "" {
		fields = "*"
	}

	var m []*models.OrderProduct
	if err := GetDBConn().Select(fields).Table("order_product").
		Where("order_sn=?", orderSn).Find(&m); err != nil {
		glog.Error(orderSn, ", 查询订单商品信息失败, ", err)
		return m
	}

	return m
}

// GetOrderPromotionByOrderSn 根据订单号查询订单的活动信息
// promotionType 1 满减活动 2 限时折扣 3 满减运费 。。。11 秒杀
// v2.9.10 添加
func GetOrderPromotionByOrderSn(orderSn string, promotionType int32, fields string) []*models.OrderPromotion {
	if fields == "" {
		fields = "*"
	}
	var m []*models.OrderPromotion
	if err := GetDBConn().Select(fields).Table("order_promotion").
		Where("order_sn=? AND promotion_type=?", orderSn, promotionType).Find(&m); err != nil {
		glog.Error(orderSn, ", 查询订单活动信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询退款订单商品信息
func GetRefundOrderProductByOrderSn(orderSn string, fields string) []*models.RefundOrderProductFreight {
	if fields == "" {
		fields = "*"
	}

	var m []*models.RefundOrderProductFreight
	if err := GetDBConn().Select(fields).Table("refund_order_product").
		Join("inner", "refund_order", "refund_order.refund_sn=refund_order_product.refund_sn").
		Where("refund_order.order_sn=?", orderSn).Find(&m); err != nil {
		glog.Error(orderSn, ", 查询退款订单商品信息失败, ", err)
		return m
	}

	return m
}

// 根据退款单号查询退款订单商品信息
func GetRefundOrderProductByRefundSn(refundSn string, fields string) []*models.RefundOrderProduct {
	if fields == "" {
		fields = "*"
	}

	var m []*models.RefundOrderProduct
	if err := GetDBConn().Select(fields).Table("refund_order_product").
		Where("refund_sn=?", refundSn).Find(&m); err != nil {
		glog.Error(refundSn, ", 通过退款单号查询退款订单商品信息失败, ", err)
		return m
	}

	return m
}

// 根据渠道订单号查询订单总信息
func GetOrderByOldOrderSn(oldOrderSn string, fields ...string) *models.Order {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.Order{}
	if _, err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("order_main.old_order_sn=?", oldOrderSn).Desc("id").Get(m); err != nil {
		glog.Error(oldOrderSn, ", 查询订单总信息失败, ", err)
		return m
	}
	return m
}

// 根据订单id查询订单总信息
func GetOrderById(id int64, fields ...string) *models.Order {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.Order{}
	if _, err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("order_main.id=?", id).Get(m); err != nil {
		glog.Error(id, ", 查询订单总信息失败, ", err)
		return m
	}
	return m
}

// 根据订单id查询订单总信息
func GetOrderByIdAndMemberId(id int64, memberId string, fields ...string) *models.Order {
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	m := &models.Order{}
	if _, err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("order_main.id=? and order_main.member_id=?", id, memberId).Get(m); err != nil {
		glog.Error(id, ", 查询订单总信息失败, ", err)
		return m
	}
	return m
}

// 根据父订单号查询子订单总信息
func GetOrderByParentOrderSn(parentOrderSn string, fields ...string) []*models.Order {
	if len(parentOrderSn) == 0 {
		return nil
	}

	if len(fields) == 0 {
		fields = []string{"*"}
	}

	var m []*models.Order
	if err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("parent_order_sn=?", parentOrderSn).Find(&m); err != nil {
		glog.Error(parentOrderSn, ", 查询子订单信息失败, ", err)
		return m
	}
	return m
}

// 根据父订单号查询子订单总信息
func GetUpetOrderByOldOrderSn(parentOrderSn string, fields ...string) []*models.Order {
	if len(parentOrderSn) == 0 {
		return nil
	}

	if len(fields) == 0 {
		fields = []string{"*"}
	}

	var m []*models.Order
	if err := GetDBConn().Select(fields[0]).Table("order_main").
		Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn").
		Where("old_order_sn=?", parentOrderSn).Find(&m); err != nil {
		glog.Error(parentOrderSn, ", 查询子订单信息失败, ", err)
		return m
	}
	return m
}

// 根据父订单号查询退款单信息
// 适用于阿闻后台取消订单
// 第三方查询主订单 阿闻查询子订单
func GetRefundOrderByParentOrderSn(OrderSn string, channelId int32) []*models.Order {
	var m []*models.Order
	session := GetDBConn().NewSession()
	defer session.Close()
	session.Table("order_main").Join("inner", "order_detail", "order_main.order_sn=order_detail.order_sn")
	if channelId == ChannelMtId || channelId == ChannelElmId || channelId == ChannelJddjId {
		session.Where("order_main.order_sn=?", OrderSn)
	} else {
		session.Where("order_main.parent_order_sn=?", OrderSn)
	}
	err := session.Find(&m)
	if err != nil {
		glog.Error(OrderSn, ",后台取消订单查询子单信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询退款单信息
func GetRefundOrderByOrderSn(orderSn string, fields ...string) []*models.RefundOrder {
	var m []*models.RefundOrder
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	db := GetDBConn()
	err := db.Table("refund_order").Select(fields[0]).Where("order_sn = ?", orderSn).Find(&m)

	if err != nil {
		glog.Error(orderSn, ",查询订单号退款信息, ", err)
		return m
	}
	return m
}

// 根据父订单号查询实物子订单总信息
func GetRealOrderByParentOrderSn(parentOrderSn string, fields ...string) (*models.Order, error) {
	order := new(models.Order)
	if len(parentOrderSn) == 0 {
		return order, nil
	}
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	_, err := GetDBConn().Select(fields[0]).Table("order_main").
		Where("parent_order_sn=?", parentOrderSn).
		And("is_virtual=?", 0).Get(order)
	return order, err
}
func GetOrderSubscribeMessageList(orderSn string, templateId string, fields ...string) []*models.OrderSubscribeMessage {
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	var m []*models.OrderSubscribeMessage
	if err := GetDBConn().Select(fields[0]).Where("order_sn=? and template_id = ?", orderSn, templateId).GroupBy("open_id").Find(&m); err != nil {
		glog.Error(orderSn, ", 查询模板消息, ", err)
		return m
	}
	return m
}

func GetOrderSubMessages(orderSn string, msgType int, fields ...string) []*models.OrderSubscribeMessage {
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	var m []*models.OrderSubscribeMessage
	if err := GetDBConn().Select(fields[0]).Where("order_sn=? and type = ?", orderSn, msgType).GroupBy("open_id").Find(&m); err != nil {
		glog.Error(orderSn, ", 查询模板消息, ", err)
		return m
	}
	return m
}

// 获取订阅消息模板
func GetWechatSubscribeMsgTemplate(templateKey string, orgId int32) (models.WechatSubscribeMessageTemplate, error) {
	var template models.WechatSubscribeMessageTemplate
	_, err := GetDcDBConn().Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=?", templateKey, orgId).Get(&template)
	if err != nil {
		glog.Error("GetWechatSubscribeMsgTemplate error: ", err)
		return template, err
	}
	return template, nil
}

func GetPinOrderGroupByPinOrderSn(pinOrderSn string, fields ...string) *models.PinOrderGroup {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.PinOrderGroup{}
	if _, err := GetDBConn().Select(fields[0]).Where("pin_order_sn=?", pinOrderSn).Desc("id").Get(m); err != nil {
		glog.Error(pinOrderSn, ", 查询拼团订单信息失败, ", err)
		return m
	}
	return m
}

// 根据订单号查询在线问诊订单
func GetDiagnoseInfoByOrderSn(orderSn string, fields ...string) *models.DiagnoseInfo {
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	m := &models.DiagnoseInfo{}
	if _, err := GetDBConn().Select(fields[0]).Where("order_sn=?", orderSn).Get(m); err != nil {
		glog.Error(orderSn, ", 查询问诊订单失败, ", err)
		return m
	}
	return m
}

// GetVerifiedCountByOrderSn 根据订单号（子订单）查询已经核销了的数量
func GetVerifiedCountByOrderSn(orderSn []string) (map[string]int32, error) {
	type resType struct {
		OrderSn        string
		VerifiedNumber int32
	}
	var (
		queryRes []resType
		err      error
	)
	res := make(map[string]int32)

	db := GetDBConn()
	err = db.SQL("SELECT order_sn,count(id) as verified_number FROM order_verify_code " +
		"WHERE order_sn IN ('" + strings.Join(orderSn, "','") + "') AND verify_status = 1 " +
		"GROUP BY order_sn").Find(&queryRes)
	if err != nil {
		return res, err
	}
	if len(queryRes) > 0 {
		for _, v := range queryRes {
			res[v.OrderSn] = v.VerifiedNumber
		}
	}
	return res, err
}

// 通过订单号 获取配送信息
// orderSn 子订单订单号
func GetDeliveryByOrderSn(orderSn string, fields ...string) (*models.OrderDeliveryRecord, error) {
	res := new(models.OrderDeliveryRecord)
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	_, err := GetDBConn().Select(fields[0]).Where("order_sn=?", orderSn).OrderBy("id DESC").Get(res)
	if err != nil {
		return res, err
	}
	return res, err
}

// 通过订单号查询最后一个节点
func GetLastDeliveryNodeByOrderSn(orderSn string, fields ...string) (*models.OrderDeliveryNode, error) {
	res := new(models.OrderDeliveryNode)
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	_, err := GetDBConn().Select(fields[0]).Where("order_sn=?", orderSn).OrderBy("id DESC").Get(res)
	if err != nil {
		return res, err
	}
	return res, err
}

// 通过DeliveryId查询最后一个节点
func GetLastDeliveryNodeById(deliveryId int64, fields ...string) (*models.OrderDeliveryNode, error) {
	res := new(models.OrderDeliveryNode)
	if len(fields) == 0 {
		fields = []string{"*"}
	}
	_, err := GetDBConn().Select(fields[0]).Where("delivery_id=?", deliveryId).OrderBy("id DESC").Get(res)
	if err != nil {
		return res, err
	}
	return res, err
}

// 通过DeliveryId查询改配送单是否被接单
func CheckDeliveryOrderIsAcceptedById(deliveryId int64) (bool, error) {
	res := new(models.OrderDeliveryNode)
	has, err := GetDBConn().Where("delivery_id=? AND delivery_status IN(15,20,30,50)", deliveryId).Exist(res)
	return has, err
}

// 获取虚拟订单的实付金额综合
// orderSn 父订单号
func GetVirtualOrderSumTotal(orderSn string) (int32, error) {
	var total int32
	_, err := GetDBConn().SQL("SELECT SUM(total) FROM order_main WHERE parent_order_sn=? AND is_virtual=1", orderSn).Get(&total)
	return total, err
}

// 获取有效的业绩信息 优先获取有效的，否则取最新的
func FindOrderPerformanceMapByOrderSn(conn *xorm.Engine, orderSns []string) (map[string]*models.OrderPerformance, error) {
	orderPerformanceMap := make(map[string]*models.OrderPerformance)
	if len(orderSns) == 0 {
		return orderPerformanceMap, nil
	}
	var orderPerformanceList []*models.OrderPerformance
	err := conn.In("order_sn", orderSns).OrderBy("performance_status DESC, id DESC").Find(&orderPerformanceList)
	for i := 0; i < len(orderPerformanceList); i++ {
		if _, ok := orderPerformanceMap[orderPerformanceList[i].OrderSn]; !ok {
			orderPerformanceMap[orderPerformanceList[i].OrderSn] = orderPerformanceList[i]
		}
	}
	return orderPerformanceMap, err
}

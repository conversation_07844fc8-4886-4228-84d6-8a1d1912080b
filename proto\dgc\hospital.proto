syntax = "proto3";

package dgc;

// @Desc    	在线问诊 医院模块
// <AUTHOR>
// @Date		2021-10-12
service HospitalService {
    // @Desc    获取医院列表
    // <AUTHOR>
    // @Date		2021-10-12
    rpc GetHospital(GetHospitalRequest) returns (GetHospitalResponse);
}

//医院请求数据
message GetHospitalRequest{
    //页码
    int32 page_index = 1;
    //页数
    int32 page_size = 2;
    //城市
    string hospital_city = 3;
    //医院类型 1中心医院，2专科医院，3全科医院
    string hospital_type = 4;
    //坐标longitude
    string longitude = 5;
    //坐标latitude
    string latitude = 6;
}

//医院返回数据
message GetHospitalResponse{
    //医院结构体
    message HospitalList{
        //医院code
        string hospital_code = 1;
        //医院名称
        string hospital_name = 2;
        //医院简称
        string hospital_short_name = 3;
        //医院特长
        string hospital_speciality = 4;
        //医院标签
        string tag_name = 5;
        //医院地址
        string hospital_address = 6;
        //医院头像
        string hospital_img = 7;
        //医院电话
        string hospital_phone = 8;
        //医院距离
        string distance = 9;
        //医院坐标Longitude
        string hospital_longitude = 10;
        //医院坐标Latitude
        string hospital_latitude = 11;
        //医院类型 1中心医院，2专科医院，3全科医院
        int32 hospital_type = 12;
    }
    //总条数
    int32 total = 1;
    //列表数据
    repeated  HospitalList list = 6;
}

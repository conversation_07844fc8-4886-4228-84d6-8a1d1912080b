package models

import (
	"time"
)

type MemberCardRelation struct {
	Id              int64     `xorm:"pk autoincr comment('主键') BIGINT(20)"`
	Userid          string    `xorm:"default 'NULL' comment('会员id(实时查询用户基础信息)') index VARCHAR(32)"`
	Createsource    int       `xorm:"default NULL comment('创建来源') INT(4)"`
	Financecode     string    `xorm:"default 'NULL' comment('财务编码-查询门店信息') VARCHAR(32)"`
	Batchcode       string    `xorm:"default 'NULL' comment('子龍批次號') VARCHAR(32)"`
	Orderid         string    `xorm:"default 'NULL' comment('结算单号(erp的单号)') VARCHAR(32)"`
	Mallorderid     string    `xorm:"default 'NULL' comment('mall商城订单号') VARCHAR(50)"`
	Vipcode         string    `xorm:"default 'NULL' comment('会员卡id') index VARCHAR(32)"`
	Cardtype        int       `xorm:"default NULL comment('卡类型(1-保障卡，2-会员卡)') INT(4)"`
	Cardlevel       int       `xorm:"default NULL comment('卡等级 保障卡（1001-198 1002-298 1003-398  1004-498） 会员卡（ 2001-198 2002-298 2003-398 2004-498）') INT(4)"`
	Vipstatus       int       `xorm:"default 0 comment('会员卡状态(0-开卡中，1-正常，2-退款中，3-已退款，4-已过期)') INT(4)"`
	Expirystartdate time.Time `xorm:"default 'NULL' comment('卡有效期开始时间') TIMESTAMP"`
	Expiryenddate   time.Time `xorm:"default 'NULL' comment('卡有效期结束时间') TIMESTAMP"`
	Presellcode     string    `xorm:"default 'NULL' comment('预售单号') VARCHAR(32)"`
	Presellname     string    `xorm:"default 'NULL' comment('预售名称') VARCHAR(300)"`
	Presellcount    int       `xorm:"default 1 comment('预售数量') INT(4)"`
	Presellunit     string    `xorm:"default 'NULL' comment('预售单位') VARCHAR(8)"`
	Createdate      time.Time `xorm:"default 'current_timestamp()' comment('创建时间') TIMESTAMP"`
	Lastdate        time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') TIMESTAMP"`
}

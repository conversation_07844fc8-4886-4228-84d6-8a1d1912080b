// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/im.proto

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//============================ 用户注册
type UserRegistrationRequest struct {
	//用户id
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRegistrationRequest) Reset()         { *m = UserRegistrationRequest{} }
func (m *UserRegistrationRequest) String() string { return proto.CompactTextString(m) }
func (*UserRegistrationRequest) ProtoMessage()    {}
func (*UserRegistrationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{0}
}

func (m *UserRegistrationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRegistrationRequest.Unmarshal(m, b)
}
func (m *UserRegistrationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRegistrationRequest.Marshal(b, m, deterministic)
}
func (m *UserRegistrationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRegistrationRequest.Merge(m, src)
}
func (m *UserRegistrationRequest) XXX_Size() int {
	return xxx_messageInfo_UserRegistrationRequest.Size(m)
}
func (m *UserRegistrationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRegistrationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserRegistrationRequest proto.InternalMessageInfo

func (m *UserRegistrationRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type UserRegistrationResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRegistrationResponse) Reset()         { *m = UserRegistrationResponse{} }
func (m *UserRegistrationResponse) String() string { return proto.CompactTextString(m) }
func (*UserRegistrationResponse) ProtoMessage()    {}
func (*UserRegistrationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{1}
}

func (m *UserRegistrationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRegistrationResponse.Unmarshal(m, b)
}
func (m *UserRegistrationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRegistrationResponse.Marshal(b, m, deterministic)
}
func (m *UserRegistrationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRegistrationResponse.Merge(m, src)
}
func (m *UserRegistrationResponse) XXX_Size() int {
	return xxx_messageInfo_UserRegistrationResponse.Size(m)
}
func (m *UserRegistrationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRegistrationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserRegistrationResponse proto.InternalMessageInfo

//============================ 对话记录写入
type CallbackMessageRequest struct {
	//回调类型  1 消息前回调 2 消息后回调
	ImType int32 `protobuf:"varint,1,opt,name=im_type,json=imType,proto3" json:"im_type"`
	//环信消息体
	ImData               string   `protobuf:"bytes,2,opt,name=im_data,json=imData,proto3" json:"im_data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CallbackMessageRequest) Reset()         { *m = CallbackMessageRequest{} }
func (m *CallbackMessageRequest) String() string { return proto.CompactTextString(m) }
func (*CallbackMessageRequest) ProtoMessage()    {}
func (*CallbackMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{2}
}

func (m *CallbackMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CallbackMessageRequest.Unmarshal(m, b)
}
func (m *CallbackMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CallbackMessageRequest.Marshal(b, m, deterministic)
}
func (m *CallbackMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CallbackMessageRequest.Merge(m, src)
}
func (m *CallbackMessageRequest) XXX_Size() int {
	return xxx_messageInfo_CallbackMessageRequest.Size(m)
}
func (m *CallbackMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CallbackMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CallbackMessageRequest proto.InternalMessageInfo

func (m *CallbackMessageRequest) GetImType() int32 {
	if m != nil {
		return m.ImType
	}
	return 0
}

func (m *CallbackMessageRequest) GetImData() string {
	if m != nil {
		return m.ImData
	}
	return ""
}

type CallbackMessageResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CallbackMessageResponse) Reset()         { *m = CallbackMessageResponse{} }
func (m *CallbackMessageResponse) String() string { return proto.CompactTextString(m) }
func (*CallbackMessageResponse) ProtoMessage()    {}
func (*CallbackMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{3}
}

func (m *CallbackMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CallbackMessageResponse.Unmarshal(m, b)
}
func (m *CallbackMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CallbackMessageResponse.Marshal(b, m, deterministic)
}
func (m *CallbackMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CallbackMessageResponse.Merge(m, src)
}
func (m *CallbackMessageResponse) XXX_Size() int {
	return xxx_messageInfo_CallbackMessageResponse.Size(m)
}
func (m *CallbackMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CallbackMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CallbackMessageResponse proto.InternalMessageInfo

//============================ 对话记录获取
type GetMessageRequest struct {
	//医生id
	DoctorId string `protobuf:"bytes,1,opt,name=doctor_id,json=doctorId,proto3" json:"doctor_id"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//订单编号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//页码
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//页数
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//截止时间 unix时间戳
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMessageRequest) Reset()         { *m = GetMessageRequest{} }
func (m *GetMessageRequest) String() string { return proto.CompactTextString(m) }
func (*GetMessageRequest) ProtoMessage()    {}
func (*GetMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{4}
}

func (m *GetMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMessageRequest.Unmarshal(m, b)
}
func (m *GetMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMessageRequest.Marshal(b, m, deterministic)
}
func (m *GetMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMessageRequest.Merge(m, src)
}
func (m *GetMessageRequest) XXX_Size() int {
	return xxx_messageInfo_GetMessageRequest.Size(m)
}
func (m *GetMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMessageRequest proto.InternalMessageInfo

func (m *GetMessageRequest) GetDoctorId() string {
	if m != nil {
		return m.DoctorId
	}
	return ""
}

func (m *GetMessageRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetMessageRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetMessageRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetMessageRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetMessageRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetMessageResponse struct {
	//总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	//列表数据
	List                 []*GetMessageResponse_GetMessageList `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetMessageResponse) Reset()         { *m = GetMessageResponse{} }
func (m *GetMessageResponse) String() string { return proto.CompactTextString(m) }
func (*GetMessageResponse) ProtoMessage()    {}
func (*GetMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{5}
}

func (m *GetMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMessageResponse.Unmarshal(m, b)
}
func (m *GetMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMessageResponse.Marshal(b, m, deterministic)
}
func (m *GetMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMessageResponse.Merge(m, src)
}
func (m *GetMessageResponse) XXX_Size() int {
	return xxx_messageInfo_GetMessageResponse.Size(m)
}
func (m *GetMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMessageResponse proto.InternalMessageInfo

func (m *GetMessageResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetMessageResponse) GetList() []*GetMessageResponse_GetMessageList {
	if m != nil {
		return m.List
	}
	return nil
}

//获取消息结构体
type GetMessageResponse_GetMessageList struct {
	//唯一id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//环信消息id
	ImMsgId string `protobuf:"bytes,2,opt,name=im_msg_id,json=imMsgId,proto3" json:"im_msg_id"`
	//消息类型及数据
	ImBodies string `protobuf:"bytes,3,opt,name=im_bodies,json=imBodies,proto3" json:"im_bodies"`
	//消息拓展信息
	ImExt string `protobuf:"bytes,4,opt,name=im_ext,json=imExt,proto3" json:"im_ext"`
	//医生id
	DoctorId string `protobuf:"bytes,5,opt,name=doctor_id,json=doctorId,proto3" json:"doctor_id"`
	//用户Id
	UserId string `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//订单编号
	OrderSn string `protobuf:"bytes,7,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//消息产生时间
	ImTimestamp int64 `protobuf:"varint,8,opt,name=im_timestamp,json=imTimestamp,proto3" json:"im_timestamp"`
	//发送者
	From string `protobuf:"bytes,9,opt,name=from,proto3" json:"from"`
	//接收到
	To string `protobuf:"bytes,10,opt,name=to,proto3" json:"to"`
	//消息类型
	ImMsgType string `protobuf:"bytes,11,opt,name=im_msg_type,json=imMsgType,proto3" json:"im_msg_type"`
	//发送者类型
	FromType             string   `protobuf:"bytes,12,opt,name=from_type,json=fromType,proto3" json:"from_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMessageResponse_GetMessageList) Reset()         { *m = GetMessageResponse_GetMessageList{} }
func (m *GetMessageResponse_GetMessageList) String() string { return proto.CompactTextString(m) }
func (*GetMessageResponse_GetMessageList) ProtoMessage()    {}
func (*GetMessageResponse_GetMessageList) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{5, 0}
}

func (m *GetMessageResponse_GetMessageList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMessageResponse_GetMessageList.Unmarshal(m, b)
}
func (m *GetMessageResponse_GetMessageList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMessageResponse_GetMessageList.Marshal(b, m, deterministic)
}
func (m *GetMessageResponse_GetMessageList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMessageResponse_GetMessageList.Merge(m, src)
}
func (m *GetMessageResponse_GetMessageList) XXX_Size() int {
	return xxx_messageInfo_GetMessageResponse_GetMessageList.Size(m)
}
func (m *GetMessageResponse_GetMessageList) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMessageResponse_GetMessageList.DiscardUnknown(m)
}

var xxx_messageInfo_GetMessageResponse_GetMessageList proto.InternalMessageInfo

func (m *GetMessageResponse_GetMessageList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetMessageResponse_GetMessageList) GetImMsgId() string {
	if m != nil {
		return m.ImMsgId
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetImBodies() string {
	if m != nil {
		return m.ImBodies
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetImExt() string {
	if m != nil {
		return m.ImExt
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetDoctorId() string {
	if m != nil {
		return m.DoctorId
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetImTimestamp() int64 {
	if m != nil {
		return m.ImTimestamp
	}
	return 0
}

func (m *GetMessageResponse_GetMessageList) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetTo() string {
	if m != nil {
		return m.To
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetImMsgType() string {
	if m != nil {
		return m.ImMsgType
	}
	return ""
}

func (m *GetMessageResponse_GetMessageList) GetFromType() string {
	if m != nil {
		return m.FromType
	}
	return ""
}

//============================ 透传消息发送
type SendAcceptsMessageRequest struct {
	//应用场景 1.问诊开始 2问诊状态
	Scenario int32 `protobuf:"varint,1,opt,name=scenario,proto3" json:"scenario"`
	//拓展字段例如 ： {"order_sn":"xxxxx"}
	Ext                  string   `protobuf:"bytes,2,opt,name=ext,proto3" json:"ext"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAcceptsMessageRequest) Reset()         { *m = SendAcceptsMessageRequest{} }
func (m *SendAcceptsMessageRequest) String() string { return proto.CompactTextString(m) }
func (*SendAcceptsMessageRequest) ProtoMessage()    {}
func (*SendAcceptsMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{6}
}

func (m *SendAcceptsMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAcceptsMessageRequest.Unmarshal(m, b)
}
func (m *SendAcceptsMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAcceptsMessageRequest.Marshal(b, m, deterministic)
}
func (m *SendAcceptsMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAcceptsMessageRequest.Merge(m, src)
}
func (m *SendAcceptsMessageRequest) XXX_Size() int {
	return xxx_messageInfo_SendAcceptsMessageRequest.Size(m)
}
func (m *SendAcceptsMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAcceptsMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendAcceptsMessageRequest proto.InternalMessageInfo

func (m *SendAcceptsMessageRequest) GetScenario() int32 {
	if m != nil {
		return m.Scenario
	}
	return 0
}

func (m *SendAcceptsMessageRequest) GetExt() string {
	if m != nil {
		return m.Ext
	}
	return ""
}

type SendAcceptsMessageResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAcceptsMessageResponse) Reset()         { *m = SendAcceptsMessageResponse{} }
func (m *SendAcceptsMessageResponse) String() string { return proto.CompactTextString(m) }
func (*SendAcceptsMessageResponse) ProtoMessage()    {}
func (*SendAcceptsMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2482619b0686b3e, []int{7}
}

func (m *SendAcceptsMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAcceptsMessageResponse.Unmarshal(m, b)
}
func (m *SendAcceptsMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAcceptsMessageResponse.Marshal(b, m, deterministic)
}
func (m *SendAcceptsMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAcceptsMessageResponse.Merge(m, src)
}
func (m *SendAcceptsMessageResponse) XXX_Size() int {
	return xxx_messageInfo_SendAcceptsMessageResponse.Size(m)
}
func (m *SendAcceptsMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAcceptsMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendAcceptsMessageResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*UserRegistrationRequest)(nil), "dgc.UserRegistrationRequest")
	proto.RegisterType((*UserRegistrationResponse)(nil), "dgc.UserRegistrationResponse")
	proto.RegisterType((*CallbackMessageRequest)(nil), "dgc.CallbackMessageRequest")
	proto.RegisterType((*CallbackMessageResponse)(nil), "dgc.CallbackMessageResponse")
	proto.RegisterType((*GetMessageRequest)(nil), "dgc.GetMessageRequest")
	proto.RegisterType((*GetMessageResponse)(nil), "dgc.GetMessageResponse")
	proto.RegisterType((*GetMessageResponse_GetMessageList)(nil), "dgc.GetMessageResponse.GetMessageList")
	proto.RegisterType((*SendAcceptsMessageRequest)(nil), "dgc.SendAcceptsMessageRequest")
	proto.RegisterType((*SendAcceptsMessageResponse)(nil), "dgc.SendAcceptsMessageResponse")
}

func init() { proto.RegisterFile("dgc/im.proto", fileDescriptor_d2482619b0686b3e) }

var fileDescriptor_d2482619b0686b3e = []byte{
	// 584 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0x55, 0x9c, 0xc6, 0x89, 0x27, 0x55, 0x29, 0x2b, 0x68, 0x5c, 0xf7, 0x83, 0xe2, 0x03, 0xea,
	0xa9, 0x48, 0xe5, 0x86, 0xc4, 0x81, 0x2f, 0x21, 0xa3, 0x56, 0x48, 0x4e, 0x7b, 0xb6, 0xb6, 0xde,
	0xc1, 0x1a, 0x91, 0xf5, 0x1a, 0xef, 0x16, 0xb5, 0xfd, 0x51, 0xfc, 0x01, 0x2e, 0x5c, 0xf8, 0x5f,
	0x68, 0xd7, 0x86, 0xba, 0x49, 0x7c, 0xf3, 0xcc, 0xf3, 0xcc, 0xce, 0x7b, 0xf3, 0x76, 0x61, 0x53,
	0x14, 0xf9, 0x4b, 0x92, 0x27, 0x55, 0xad, 0x8c, 0x62, 0x43, 0x51, 0xe4, 0xf1, 0x29, 0xcc, 0x2e,
	0x35, 0xd6, 0x29, 0x16, 0xa4, 0x4d, 0xcd, 0x0d, 0xa9, 0x32, 0xc5, 0xef, 0xd7, 0xa8, 0x0d, 0x9b,
	0xc1, 0xf8, 0x5a, 0x63, 0x9d, 0x91, 0x08, 0x07, 0x47, 0x83, 0xe3, 0x20, 0xf5, 0x6d, 0x98, 0x88,
	0x38, 0x82, 0x70, 0xb5, 0x46, 0x57, 0xaa, 0xd4, 0x18, 0x7f, 0x86, 0x9d, 0xf7, 0x7c, 0xb1, 0xb8,
	0xe2, 0xf9, 0xb7, 0x73, 0xd4, 0x9a, 0x17, 0xd8, 0x69, 0x47, 0x32, 0x33, 0xb7, 0x15, 0xba, 0x76,
	0xa3, 0xd4, 0x27, 0x79, 0x71, 0x5b, 0x61, 0x0b, 0x08, 0x6e, 0x78, 0xe8, 0x35, 0xe7, 0x90, 0xfc,
	0xc0, 0x0d, 0x8f, 0x77, 0x61, 0xb6, 0xd2, 0xab, 0x3d, 0xe6, 0xd7, 0x00, 0x1e, 0x7f, 0x42, 0xb3,
	0x74, 0xc4, 0x1e, 0x04, 0x42, 0xe5, 0x46, 0x75, 0x66, 0x9e, 0x34, 0x89, 0x44, 0x74, 0xe9, 0x78,
	0x5d, 0x3a, 0x6c, 0x17, 0x26, 0xaa, 0x16, 0x58, 0x67, 0xba, 0x0c, 0x87, 0x0e, 0x19, 0xbb, 0x78,
	0x5e, 0xb2, 0x03, 0x80, 0x8a, 0x17, 0x98, 0x51, 0x29, 0xf0, 0x26, 0xdc, 0x70, 0x63, 0x07, 0x36,
	0x93, 0xd8, 0x84, 0x3d, 0xcf, 0xc1, 0x9a, 0xee, 0x30, 0x1c, 0x39, 0x74, 0x62, 0x13, 0x73, 0xba,
	0x43, 0xdb, 0x16, 0x4b, 0x91, 0x19, 0x92, 0x18, 0xfa, 0x47, 0x83, 0xe3, 0x61, 0x3a, 0xc6, 0x52,
	0x5c, 0x90, 0xc4, 0xf8, 0xe7, 0x10, 0x58, 0x77, 0xfa, 0x86, 0x14, 0x7b, 0x02, 0x23, 0xa3, 0x0c,
	0x5f, 0xb4, 0xfa, 0x34, 0x01, 0x7b, 0x0d, 0x1b, 0x0b, 0xd2, 0x26, 0xf4, 0x8f, 0x86, 0xc7, 0xd3,
	0xd3, 0x17, 0x27, 0xa2, 0xc8, 0x4f, 0x56, 0x8b, 0x3b, 0xa9, 0x33, 0xd2, 0x26, 0x75, 0x35, 0xd1,
	0x1f, 0x0f, 0xb6, 0x1e, 0x02, 0x6c, 0x0b, 0xbc, 0x56, 0x9c, 0x51, 0xea, 0x91, 0x60, 0x11, 0x04,
	0x24, 0x33, 0xa9, 0x8b, 0x7b, 0x61, 0xc6, 0x24, 0xcf, 0x75, 0x91, 0x08, 0xcb, 0x8f, 0x64, 0x76,
	0xa5, 0x04, 0xa1, 0x6e, 0xa5, 0x99, 0x90, 0x7c, 0xe7, 0x62, 0xf6, 0x14, 0x7c, 0x92, 0x19, 0xde,
	0x18, 0xa7, 0x4b, 0x90, 0x8e, 0x48, 0x7e, 0xbc, 0x59, 0xda, 0xc1, 0xa8, 0x7f, 0x07, 0x7e, 0xef,
	0x0e, 0xc6, 0x0f, 0x77, 0xf0, 0x1c, 0x36, 0xad, 0x6f, 0x48, 0xa2, 0x36, 0x5c, 0x56, 0xe1, 0xc4,
	0x69, 0x39, 0x25, 0x79, 0xf1, 0x2f, 0xc5, 0x18, 0x6c, 0x7c, 0xad, 0x95, 0x0c, 0x03, 0x57, 0xe9,
	0xbe, 0x2d, 0x4f, 0xa3, 0x42, 0x70, 0x19, 0xcf, 0x28, 0x76, 0x08, 0xd3, 0x96, 0xa7, 0xb3, 0xe0,
	0xd4, 0x01, 0x81, 0x63, 0xea, 0x5c, 0xb8, 0x07, 0x81, 0xad, 0x6b, 0xd0, 0xcd, 0x66, 0x6e, 0x9b,
	0xb0, 0x60, 0x9c, 0xc0, 0xee, 0x1c, 0x4b, 0xf1, 0x36, 0xcf, 0xb1, 0x32, 0x7a, 0xc9, 0x75, 0x11,
	0x4c, 0x74, 0x8e, 0x25, 0xaf, 0x49, 0xb5, 0xba, 0xfe, 0x8f, 0xd9, 0x36, 0x0c, 0xad, 0x42, 0x8d,
	0xae, 0xf6, 0x33, 0xde, 0x87, 0x68, 0x5d, 0xab, 0x66, 0x8b, 0xa7, 0xbf, 0x3d, 0x08, 0x12, 0x39,
	0xc7, 0xfa, 0x07, 0xe5, 0xc8, 0xce, 0xe0, 0xd1, 0xd2, 0x05, 0x60, 0x7b, 0x6e, 0xff, 0xeb, 0xaf,
	0x58, 0xb4, 0xbf, 0x1e, 0x6c, 0xed, 0xf5, 0x06, 0xe0, 0xde, 0x0b, 0x6c, 0x67, 0xc5, 0x48, 0x4d,
	0x8f, 0x59, 0x8f, 0xc1, 0xd8, 0x25, 0xb0, 0xd5, 0xc1, 0xd9, 0xa1, 0xfb, 0xbd, 0x57, 0x9c, 0xe8,
	0x59, 0x2f, 0xde, 0xb6, 0xfd, 0x02, 0xdb, 0xcb, 0x8f, 0x09, 0x6b, 0x78, 0xf4, 0xbc, 0x4b, 0xd1,
	0x41, 0x0f, 0xda, 0x34, 0xbc, 0xf2, 0xdd, 0xeb, 0xf6, 0xea, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x2e, 0x02, 0xfc, 0x41, 0xed, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ImServiceClient is the client API for ImService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ImServiceClient interface {
	// @Desc    消息回调
	// <AUTHOR>
	// @Date		2021-09-30
	CallbackMessage(ctx context.Context, in *CallbackMessageRequest, opts ...grpc.CallOption) (*CallbackMessageResponse, error)
	// @Desc    	对话记录获取
	// <AUTHOR>
	// @Date		2021-09-30
	GetMessage(ctx context.Context, in *GetMessageRequest, opts ...grpc.CallOption) (*GetMessageResponse, error)
	// @Desc    	消息发送
	// <AUTHOR>
	// @Date		2021-09-30
	SendAcceptsMessage(ctx context.Context, in *SendAcceptsMessageRequest, opts ...grpc.CallOption) (*SendAcceptsMessageResponse, error)
	// @Desc    	用户注册
	// <AUTHOR>
	// @Date		2021-09-30
	UserRegistration(ctx context.Context, in *UserRegistrationRequest, opts ...grpc.CallOption) (*UserRegistrationResponse, error)
}

type imServiceClient struct {
	cc *grpc.ClientConn
}

func NewImServiceClient(cc *grpc.ClientConn) ImServiceClient {
	return &imServiceClient{cc}
}

func (c *imServiceClient) CallbackMessage(ctx context.Context, in *CallbackMessageRequest, opts ...grpc.CallOption) (*CallbackMessageResponse, error) {
	out := new(CallbackMessageResponse)
	err := c.cc.Invoke(ctx, "/dgc.ImService/CallbackMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imServiceClient) GetMessage(ctx context.Context, in *GetMessageRequest, opts ...grpc.CallOption) (*GetMessageResponse, error) {
	out := new(GetMessageResponse)
	err := c.cc.Invoke(ctx, "/dgc.ImService/GetMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imServiceClient) SendAcceptsMessage(ctx context.Context, in *SendAcceptsMessageRequest, opts ...grpc.CallOption) (*SendAcceptsMessageResponse, error) {
	out := new(SendAcceptsMessageResponse)
	err := c.cc.Invoke(ctx, "/dgc.ImService/SendAcceptsMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imServiceClient) UserRegistration(ctx context.Context, in *UserRegistrationRequest, opts ...grpc.CallOption) (*UserRegistrationResponse, error) {
	out := new(UserRegistrationResponse)
	err := c.cc.Invoke(ctx, "/dgc.ImService/UserRegistration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImServiceServer is the server API for ImService service.
type ImServiceServer interface {
	// @Desc    消息回调
	// <AUTHOR>
	// @Date		2021-09-30
	CallbackMessage(context.Context, *CallbackMessageRequest) (*CallbackMessageResponse, error)
	// @Desc    	对话记录获取
	// <AUTHOR>
	// @Date		2021-09-30
	GetMessage(context.Context, *GetMessageRequest) (*GetMessageResponse, error)
	// @Desc    	消息发送
	// <AUTHOR>
	// @Date		2021-09-30
	SendAcceptsMessage(context.Context, *SendAcceptsMessageRequest) (*SendAcceptsMessageResponse, error)
	// @Desc    	用户注册
	// <AUTHOR>
	// @Date		2021-09-30
	UserRegistration(context.Context, *UserRegistrationRequest) (*UserRegistrationResponse, error)
}

// UnimplementedImServiceServer can be embedded to have forward compatible implementations.
type UnimplementedImServiceServer struct {
}

func (*UnimplementedImServiceServer) CallbackMessage(ctx context.Context, req *CallbackMessageRequest) (*CallbackMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackMessage not implemented")
}
func (*UnimplementedImServiceServer) GetMessage(ctx context.Context, req *GetMessageRequest) (*GetMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMessage not implemented")
}
func (*UnimplementedImServiceServer) SendAcceptsMessage(ctx context.Context, req *SendAcceptsMessageRequest) (*SendAcceptsMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendAcceptsMessage not implemented")
}
func (*UnimplementedImServiceServer) UserRegistration(ctx context.Context, req *UserRegistrationRequest) (*UserRegistrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserRegistration not implemented")
}

func RegisterImServiceServer(s *grpc.Server, srv ImServiceServer) {
	s.RegisterService(&_ImService_serviceDesc, srv)
}

func _ImService_CallbackMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallbackMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImServiceServer).CallbackMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.ImService/CallbackMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImServiceServer).CallbackMessage(ctx, req.(*CallbackMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImService_GetMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImServiceServer).GetMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.ImService/GetMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImServiceServer).GetMessage(ctx, req.(*GetMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImService_SendAcceptsMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAcceptsMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImServiceServer).SendAcceptsMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.ImService/SendAcceptsMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImServiceServer).SendAcceptsMessage(ctx, req.(*SendAcceptsMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImService_UserRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRegistrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImServiceServer).UserRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.ImService/UserRegistration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImServiceServer).UserRegistration(ctx, req.(*UserRegistrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ImService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.ImService",
	HandlerType: (*ImServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CallbackMessage",
			Handler:    _ImService_CallbackMessage_Handler,
		},
		{
			MethodName: "GetMessage",
			Handler:    _ImService_GetMessage_Handler,
		},
		{
			MethodName: "SendAcceptsMessage",
			Handler:    _ImService_SendAcceptsMessage_Handler,
		},
		{
			MethodName: "UserRegistration",
			Handler:    _ImService_UserRegistration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/im.proto",
}

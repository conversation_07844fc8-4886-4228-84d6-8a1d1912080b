// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/advertisement_mp.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type AddAdvertisementMpRecordRequest struct {
	//订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//用户ID
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//转化行为 1 首页访问 2 下单 3 付费
	ActionType int32 `protobuf:"varint,3,opt,name=action_type,json=actionType,proto3" json:"action_type"`
	//转化Url
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	//落地页Click_id
	ClickId string `protobuf:"bytes,5,opt,name=click_id,json=clickId,proto3" json:"click_id"`
	//用户来源渠道
	UserAgent int32 `protobuf:"varint,6,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	//行为发生时
	ActionTime int64 `protobuf:"varint,7,opt,name=action_time,json=actionTime,proto3" json:"action_time"`
	//渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
	ChannelId int32 `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划订单,7保险订单,8积分订单 9秒杀订单
	OrderType            int32    `protobuf:"varint,9,opt,name=order_type,json=orderType,proto3" json:"order_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAdvertisementMpRecordRequest) Reset()         { *m = AddAdvertisementMpRecordRequest{} }
func (m *AddAdvertisementMpRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddAdvertisementMpRecordRequest) ProtoMessage()    {}
func (*AddAdvertisementMpRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_be383b4a1651714d, []int{0}
}

func (m *AddAdvertisementMpRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAdvertisementMpRecordRequest.Unmarshal(m, b)
}
func (m *AddAdvertisementMpRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAdvertisementMpRecordRequest.Marshal(b, m, deterministic)
}
func (m *AddAdvertisementMpRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAdvertisementMpRecordRequest.Merge(m, src)
}
func (m *AddAdvertisementMpRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddAdvertisementMpRecordRequest.Size(m)
}
func (m *AddAdvertisementMpRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAdvertisementMpRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAdvertisementMpRecordRequest proto.InternalMessageInfo

func (m *AddAdvertisementMpRecordRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *AddAdvertisementMpRecordRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddAdvertisementMpRecordRequest) GetActionType() int32 {
	if m != nil {
		return m.ActionType
	}
	return 0
}

func (m *AddAdvertisementMpRecordRequest) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AddAdvertisementMpRecordRequest) GetClickId() string {
	if m != nil {
		return m.ClickId
	}
	return ""
}

func (m *AddAdvertisementMpRecordRequest) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

func (m *AddAdvertisementMpRecordRequest) GetActionTime() int64 {
	if m != nil {
		return m.ActionTime
	}
	return 0
}

func (m *AddAdvertisementMpRecordRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddAdvertisementMpRecordRequest) GetOrderType() int32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

//通用返回
type MpBaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MpBaseResponse) Reset()         { *m = MpBaseResponse{} }
func (m *MpBaseResponse) String() string { return proto.CompactTextString(m) }
func (*MpBaseResponse) ProtoMessage()    {}
func (*MpBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_be383b4a1651714d, []int{1}
}

func (m *MpBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MpBaseResponse.Unmarshal(m, b)
}
func (m *MpBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MpBaseResponse.Marshal(b, m, deterministic)
}
func (m *MpBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MpBaseResponse.Merge(m, src)
}
func (m *MpBaseResponse) XXX_Size() int {
	return xxx_messageInfo_MpBaseResponse.Size(m)
}
func (m *MpBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MpBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MpBaseResponse proto.InternalMessageInfo

func (m *MpBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MpBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MpBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*AddAdvertisementMpRecordRequest)(nil), "oc.AddAdvertisementMpRecordRequest")
	proto.RegisterType((*MpBaseResponse)(nil), "oc.MpBaseResponse")
}

func init() { proto.RegisterFile("oc/advertisement_mp.proto", fileDescriptor_be383b4a1651714d) }

var fileDescriptor_be383b4a1651714d = []byte{
	// 323 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x51, 0x41, 0x4f, 0x32, 0x31,
	0x10, 0xfd, 0x16, 0x58, 0x96, 0x9d, 0x2f, 0x31, 0x66, 0x62, 0xb4, 0x98, 0x18, 0x08, 0x5e, 0x38,
	0x61, 0xa2, 0xbf, 0x00, 0x6f, 0x1c, 0xb8, 0x2c, 0x78, 0xde, 0xac, 0xed, 0x04, 0x1b, 0xd9, 0xb6,
	0xb6, 0x85, 0x84, 0x9f, 0xe3, 0x3f, 0x35, 0x6d, 0xd1, 0xa0, 0x89, 0xf1, 0xd6, 0xf7, 0x66, 0xe6,
	0xbd, 0xe9, 0x1b, 0x18, 0x6a, 0x7e, 0xd7, 0x88, 0x3d, 0x59, 0x2f, 0x1d, 0xb5, 0xa4, 0x7c, 0xdd,
	0x9a, 0x99, 0xb1, 0xda, 0x6b, 0xec, 0x68, 0x3e, 0x79, 0xef, 0xc0, 0x68, 0x2e, 0xc4, 0xfc, 0xb4,
	0x63, 0x69, 0x2a, 0xe2, 0xda, 0x8a, 0x8a, 0xde, 0x76, 0xe4, 0x3c, 0x0e, 0x61, 0xa0, 0xad, 0x20,
	0x5b, 0x3b, 0xc5, 0xb2, 0x71, 0x36, 0x2d, 0xab, 0x22, 0xe2, 0x95, 0xc2, 0x2b, 0x28, 0x76, 0x8e,
	0x6c, 0x2d, 0x05, 0xeb, 0xc4, 0x4a, 0x3f, 0xc0, 0x85, 0xc0, 0x11, 0xfc, 0x6f, 0xb8, 0x97, 0x5a,
	0xd5, 0xfe, 0x60, 0x88, 0x75, 0xc7, 0xd9, 0x34, 0xaf, 0x20, 0x51, 0xeb, 0x83, 0x21, 0x3c, 0x87,
	0xee, 0xce, 0x6e, 0x59, 0x2f, 0x4e, 0x85, 0x67, 0xb0, 0xe1, 0x5b, 0xc9, 0x5f, 0x83, 0x58, 0x9e,
	0x6c, 0x22, 0x5e, 0x08, 0xbc, 0x01, 0x88, 0x36, 0xcd, 0x86, 0x94, 0x67, 0xfd, 0x28, 0x56, 0x06,
	0x66, 0x1e, 0x88, 0x53, 0x33, 0xd9, 0x12, 0x2b, 0xc6, 0xd9, 0xb4, 0xfb, 0x65, 0x26, 0x5b, 0x0a,
	0xf3, 0xfc, 0xa5, 0x51, 0x8a, 0xb6, 0x41, 0x7c, 0x90, 0xe6, 0x8f, 0x4c, 0x92, 0x4f, 0x1f, 0x8c,
	0xbb, 0x96, 0xa9, 0x1c, 0x99, 0xb0, 0xea, 0x64, 0x0d, 0x67, 0x4b, 0xf3, 0xd8, 0x38, 0xaa, 0xc8,
	0x19, 0xad, 0x1c, 0x21, 0x42, 0x8f, 0x6b, 0x41, 0x31, 0x8d, 0xbc, 0x8a, 0x6f, 0x64, 0x50, 0xb4,
	0xe4, 0x5c, 0xb3, 0xa1, 0x63, 0x14, 0x9f, 0x10, 0x2f, 0x20, 0x27, 0x6b, 0xb5, 0x8d, 0x29, 0x94,
	0x55, 0x02, 0xf7, 0x1a, 0x2e, 0x7f, 0xa4, 0xbe, 0x22, 0xbb, 0x97, 0x9c, 0xf0, 0x09, 0xd8, 0x6f,
	0x27, 0xc1, 0xdb, 0x99, 0xe6, 0xb3, 0x3f, 0x0e, 0x76, 0x8d, 0xa1, 0xe9, 0xfb, 0xca, 0x93, 0x7f,
	0xcf, 0xfd, 0x78, 0xf5, 0x87, 0x8f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xd3, 0x23, 0x3b, 0x12,
	0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AdvertisementMpServiceClient is the client API for AdvertisementMpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdvertisementMpServiceClient interface {
	// @Desc    	新增广告MP记录
	AddAdvertisementMpRecord(ctx context.Context, in *AddAdvertisementMpRecordRequest, opts ...grpc.CallOption) (*MpBaseResponse, error)
}

type advertisementMpServiceClient struct {
	cc *grpc.ClientConn
}

func NewAdvertisementMpServiceClient(cc *grpc.ClientConn) AdvertisementMpServiceClient {
	return &advertisementMpServiceClient{cc}
}

func (c *advertisementMpServiceClient) AddAdvertisementMpRecord(ctx context.Context, in *AddAdvertisementMpRecordRequest, opts ...grpc.CallOption) (*MpBaseResponse, error) {
	out := new(MpBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.AdvertisementMpService/AddAdvertisementMpRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdvertisementMpServiceServer is the server API for AdvertisementMpService service.
type AdvertisementMpServiceServer interface {
	// @Desc    	新增广告MP记录
	AddAdvertisementMpRecord(context.Context, *AddAdvertisementMpRecordRequest) (*MpBaseResponse, error)
}

// UnimplementedAdvertisementMpServiceServer can be embedded to have forward compatible implementations.
type UnimplementedAdvertisementMpServiceServer struct {
}

func (*UnimplementedAdvertisementMpServiceServer) AddAdvertisementMpRecord(ctx context.Context, req *AddAdvertisementMpRecordRequest) (*MpBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAdvertisementMpRecord not implemented")
}

func RegisterAdvertisementMpServiceServer(s *grpc.Server, srv AdvertisementMpServiceServer) {
	s.RegisterService(&_AdvertisementMpService_serviceDesc, srv)
}

func _AdvertisementMpService_AddAdvertisementMpRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAdvertisementMpRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdvertisementMpServiceServer).AddAdvertisementMpRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.AdvertisementMpService/AddAdvertisementMpRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdvertisementMpServiceServer).AddAdvertisementMpRecord(ctx, req.(*AddAdvertisementMpRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AdvertisementMpService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.AdvertisementMpService",
	HandlerType: (*AdvertisementMpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddAdvertisementMpRecord",
			Handler:    _AdvertisementMpService_AddAdvertisementMpRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/advertisement_mp.proto",
}

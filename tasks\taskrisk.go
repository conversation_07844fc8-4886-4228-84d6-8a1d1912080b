package tasks

import (
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
	"order-center/models"
	"order-center/proto/ac"
	"order-center/services"
	"time"
)

// 风控相关定时任务
func init() {
	if !kit.EnvCanCron() {
		return
	}
	c := cron.New()
	// 异常订单每日0点通知
	if _, err := c.AddFunc("0 0 * * ?", func() {
		riskOrderDayNotify()
	}); err != nil {
		glog.Info("riskOrderDayNotify 创建任务出错：", err.Error())
	}

	// 异常订单每5分钟通知
	if _, err := c.AddFunc("0/5 * * * ?", func() {
		riskOrderNotify()
	}); err != nil {
		glog.Info("riskOrderNotify 创建任务出错：", err.Error())
	}

	// 风险用户每1分钟自动解封
	if _, err := c.AddFunc("0/1 * * * ?", func() {
		riskUserAutoUnlock()
	}); err != nil {
		glog.Info("riskUserAutoUnlock 创建任务出错：", err.Error())
	}

	c.Start()
}

// 异常订单每日0点通知
func riskOrderDayNotify() (err error) {
	redis := services.GetRedisConn()
	lockKey := "order-center:task:riskOrderDayNotify"
	if !redis.SetNX(lockKey, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer redis.Del(lockKey)

	defer func() {
		if err != nil {
			glog.Warning("riskOrderDayNotify " + err.Error())
		}
	}()

	type Count struct {
		RiskCount int32 // 异常订单数
		MarkCount int32 // 标记为正常订单数
	}

	count := new(Count)
	db := services.GetDBConn()

	now := time.Now()
	start := now.Add(-24 * time.Hour).Format(kit.DATE_LAYOUT)
	end := now.Format(kit.DATE_LAYOUT)

	if _, err = db.Table("order_risk").Where("create_time >= ? and create_time < ?", start, end).
		Select("count(if(state = 10,1,null)) as risk_count,count(if(state = 20,1,null)) as mark_count").
		Get(count); err != nil {
		return
	} else if count.RiskCount == 0 && count.MarkCount == 0 {
		return
	}

	client := ac.GetActivityCenterClient()
	rs, err := client.Base.SendWebhookMarkdownNotify(client.Ctx, &ac.SendWebhookMarkdownNotifyReq{
		Key: "101b301d-8c90-4bea-8ff1-107fecab5644",
		Content: fmt.Sprintf("## %s异常订单：\n"+
			"> 今日异常订单数：**<font color=\"red\">%d</font>**\n"+
			"> 今日标记为非异常订单数：**<font color=\"green\">%d</font>**",
			start, count.RiskCount, count.MarkCount),
	})
	if err != nil {
		return
	} else if rs.Code != 200 {
		err = errors.New(rs.Message)
	}

	return
}

// 异常订单每5分钟通知
func riskOrderNotify() (err error) {
	redis := services.GetRedisConn()
	lockKey := "order-center:task:riskOrderNotify"
	if !redis.SetNX(lockKey, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer redis.Del(lockKey)

	defer func() {
		if err != nil {
			glog.Warning("riskOrderNotify " + err.Error())
		}
	}()

	type Count struct {
		RiskCount int32 // 异常订单数
		MarkCount int32 // 标记为正常订单数
		LastCount int32 // 最近5分钟订单数
	}

	count := new(Count)
	db := services.GetDBConn()

	if has, err := db.Table("order_risk").Where("create_time > ?", time.Now().Format(kit.DATE_LAYOUT)).
		Select("count(if(state = 10,1,null)) as risk_count," +
			"count(if(state = 20,1,null)) as mark_count,count(if(state = 10 and create_time >='" +
			time.Now().Add(-5*time.Minute).Format(kit.DATETIME_LAYOUT) + "',1,null)) as last_count").
		Get(count); err != nil {
		return err
	} else if !has || count.LastCount == 0 {
		return nil
	}

	client := ac.GetActivityCenterClient()
	rs, err := client.Base.SendWebhookMarkdownNotify(client.Ctx, &ac.SendWebhookMarkdownNotifyReq{
		Key: "101b301d-8c90-4bea-8ff1-107fecab5644",
		Content: fmt.Sprintf("## 异常订单播报：\n"+
			"> 新增异常订单数：**<font color=\"red\">%d</font>**\n"+
			"> 今日异常订单数：**<font color=\"red\">%d</font>**\n"+
			"> 今日标记为非异常订单数：**<font color=\"green\">%d</font>**",
			count.LastCount, count.RiskCount, count.MarkCount),
	})
	if err != nil {
		return
	} else if rs.Code != 200 {
		return errors.New(rs.Message)
	}
	return
}

// 风险用户自动解封
func riskUserAutoUnlock() (err error) {
	redis := services.GetRedisConn()
	lockKey := "order-center:task:riskUserAutoUnlock"
	if !redis.SetNX(lockKey, time.Now().Unix(), 1*time.Minute).Val() {
		return
	}
	defer redis.Del(lockKey)

	defer func() {
		if err != nil {
			glog.Warning("riskUserAutoUnlock " + err.Error())
		}
	}()

	var logs []*models.RiskUserLog
	var users []*models.RiskUser

	db := services.GetDBConn()
	if err = db.Where("status = 1 and unlock_time <= now()").Find(&users); err != nil {
		return
	}
	if len(users) == 0 {
		return
	}

	session := db.NewSession()
	defer session.Close()
	session.Begin()

	for _, user := range users {
		logs = append(logs, &models.RiskUserLog{
			Type:   1,
			Ruid:   user.Id,
			Opter:  "系统",
			Reason: "自动解封",
		})

		update := map[string]interface{}{
			"status":      0,
			"unlock_time": nil,
		}
		if (user.Type & 1) > 0 {
			update["last_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		}
		if (user.Type & 2) > 0 {
			update["last_receiver_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		}
		if _, err = session.Table("dc_customer.risk_user").Where("id = ?", user.Id).Update(update); err != nil {
			session.Rollback()
			return
		}
	}

	if _, err = session.Insert(logs); err != nil {
		session.Rollback()
		return
	}

	return session.Commit()
}

package main

import (
	"flag"
	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/reflection"
	"net/http"
	_ "net/http/pprof"
	. "order-center/proto/oc"
	"order-center/services"
	"order-center/tasks"
	"os"
	"runtime"
	"strings"
	"time"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	glog.Info("获取环境变量数据", env)
	if env == "production" || env == "pro" {
		kit.IsDebug = false
	} else {
		kit.IsDebug = true
	}
}

func main() {

	defer func() {
		if err := recover(); err != nil {
			glog.Error("异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()

	go func() {
		err := http.ListenAndServe(":11121", nil)
		if err != nil {
			return
		}
	}() //pprof性能分析
	defer glog.Flush()
	flag.Parse()

	//创建数据库连接
	services.SetupDbConn()
	defer services.CloseDbConn()
	//启动定时任务
	tasks.InitTask()

	micro := micro.MicService{}
	micro.NewServer()

	//购物车
	RegisterCartServiceServer(micro.GrpcServer, &services.CartService{})
	//boss订单模块
	RegisterOrderServiceServer(micro.GrpcServer, &services.OrderService{})
	//售后模块
	RegisterAfterSaleServiceServer(micro.GrpcServer, &services.AfterSaleService{})
	//oms售后模块
	RegisterOmsOrderServiceServer(micro.GrpcServer, &services.OmsService{})

	RegisterRefundOrderServiceServer(micro.GrpcServer, &services.RefundOrderService{})

	RegisterAllChannelServiceServer(micro.GrpcServer, &services.AllChannelService{})
	RegisterOrderExceptionServiceServer(micro.GrpcServer, &services.OrderExceptionService{})
	//订单积分
	RegisterOrderIntegralServiceServer(micro.GrpcServer, &services.OrderIntegralService{})
	// 阿闻到家
	RegisterUpetDjServiceServer(micro.GrpcServer, &services.UpetDjService{})
	//订阅消息
	RegisterSubscribeMessageServiceServer(micro.GrpcServer, &services.SubscribeMessageService{})

	RegisterAdvertisementMpServiceServer(micro.GrpcServer, &services.AdvertisementMpService{})
	// 发票服务
	RegisterInvoiceServiceServer(micro.GrpcServer, &services.InvoiceService{})
	// 社区团购服务
	RegisterCommunityGroupServiceServer(micro.GrpcServer, &services.CommunityGroupService{})

	RegisterOcTaskServer(micro.GrpcServer, &tasks.OcTask{})

	//创建数字藏品订单
	RegisterDigitalOrderServiceServer(micro.GrpcServer, &services.DigitalOrderService{})

	// 会员卡、服务包
	RegisterCardServiceServer(micro.GrpcServer, &services.CardService{})

	//付费会员订单
	RegisterVipCardOrderServiceServer(micro.GrpcServer, &services.VipCardOrderService{})
	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	glog.Info("order-center zx20221125 服务启动...")

	micro.Start()
}

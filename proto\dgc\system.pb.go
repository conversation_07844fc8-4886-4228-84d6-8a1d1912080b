// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/system.proto

// 在线问诊 系统配置

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SystemConfigRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SystemConfigRequest) Reset()         { *m = SystemConfigRequest{} }
func (m *SystemConfigRequest) String() string { return proto.CompactTextString(m) }
func (*SystemConfigRequest) ProtoMessage()    {}
func (*SystemConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4e60507901f5311e, []int{0}
}

func (m *SystemConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SystemConfigRequest.Unmarshal(m, b)
}
func (m *SystemConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SystemConfigRequest.Marshal(b, m, deterministic)
}
func (m *SystemConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SystemConfigRequest.Merge(m, src)
}
func (m *SystemConfigRequest) XXX_Size() int {
	return xxx_messageInfo_SystemConfigRequest.Size(m)
}
func (m *SystemConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SystemConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SystemConfigRequest proto.InternalMessageInfo

// 系统配置
type SystemConfigResponse struct {
	//医生排班时间内不接单开关：0关，1开
	WorkOnOff int32 `protobuf:"varint,1,opt,name=work_on_off,json=workOnOff,proto3" json:"work_on_off"`
	//免费图文问诊时长（单位分钟）
	FreeImageTextDuration int32 `protobuf:"varint,2,opt,name=free_image_text_duration,json=freeImageTextDuration,proto3" json:"free_image_text_duration"`
	//快速图文问诊时长（单位分钟）
	QuickImageTextDuration int32 `protobuf:"varint,3,opt,name=quick_image_text_duration,json=quickImageTextDuration,proto3" json:"quick_image_text_duration"`
	//找医生图文问诊时长（单位分钟）
	FindImageTextDuration int32 `protobuf:"varint,4,opt,name=find_image_text_duration,json=findImageTextDuration,proto3" json:"find_image_text_duration"`
	//找医生电话问诊时长（单位分钟）
	FindPhoneDuration int32 `protobuf:"varint,5,opt,name=find_phone_duration,json=findPhoneDuration,proto3" json:"find_phone_duration"`
	//找医生视频问诊时长（单位分钟）
	FindVideoDuration int32 `protobuf:"varint,6,opt,name=find_video_duration,json=findVideoDuration,proto3" json:"find_video_duration"`
	//快速图文问诊价格（单位分）
	QuickImageTextPrice  int32    `protobuf:"varint,7,opt,name=quick_image_text_price,json=quickImageTextPrice,proto3" json:"quick_image_text_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SystemConfigResponse) Reset()         { *m = SystemConfigResponse{} }
func (m *SystemConfigResponse) String() string { return proto.CompactTextString(m) }
func (*SystemConfigResponse) ProtoMessage()    {}
func (*SystemConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4e60507901f5311e, []int{1}
}

func (m *SystemConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SystemConfigResponse.Unmarshal(m, b)
}
func (m *SystemConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SystemConfigResponse.Marshal(b, m, deterministic)
}
func (m *SystemConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SystemConfigResponse.Merge(m, src)
}
func (m *SystemConfigResponse) XXX_Size() int {
	return xxx_messageInfo_SystemConfigResponse.Size(m)
}
func (m *SystemConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SystemConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SystemConfigResponse proto.InternalMessageInfo

func (m *SystemConfigResponse) GetWorkOnOff() int32 {
	if m != nil {
		return m.WorkOnOff
	}
	return 0
}

func (m *SystemConfigResponse) GetFreeImageTextDuration() int32 {
	if m != nil {
		return m.FreeImageTextDuration
	}
	return 0
}

func (m *SystemConfigResponse) GetQuickImageTextDuration() int32 {
	if m != nil {
		return m.QuickImageTextDuration
	}
	return 0
}

func (m *SystemConfigResponse) GetFindImageTextDuration() int32 {
	if m != nil {
		return m.FindImageTextDuration
	}
	return 0
}

func (m *SystemConfigResponse) GetFindPhoneDuration() int32 {
	if m != nil {
		return m.FindPhoneDuration
	}
	return 0
}

func (m *SystemConfigResponse) GetFindVideoDuration() int32 {
	if m != nil {
		return m.FindVideoDuration
	}
	return 0
}

func (m *SystemConfigResponse) GetQuickImageTextPrice() int32 {
	if m != nil {
		return m.QuickImageTextPrice
	}
	return 0
}

func init() {
	proto.RegisterType((*SystemConfigRequest)(nil), "dgc.SystemConfigRequest")
	proto.RegisterType((*SystemConfigResponse)(nil), "dgc.SystemConfigResponse")
}

func init() { proto.RegisterFile("dgc/system.proto", fileDescriptor_4e60507901f5311e) }

var fileDescriptor_4e60507901f5311e = []byte{
	// 280 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x91, 0x41, 0x4f, 0x2a, 0x31,
	0x14, 0x85, 0x1f, 0xf0, 0xc0, 0x78, 0xd5, 0x44, 0x3b, 0x42, 0x06, 0x17, 0xc6, 0xcc, 0xca, 0xd5,
	0x98, 0xc8, 0xc2, 0xb8, 0x56, 0x17, 0xae, 0x20, 0x60, 0xd8, 0x4e, 0xb0, 0xbd, 0x1d, 0x1b, 0x42,
	0xef, 0xd0, 0x76, 0x10, 0xff, 0xb9, 0x4b, 0xd3, 0x42, 0x1c, 0xc9, 0xcc, 0xf6, 0x7c, 0xe7, 0x4b,
	0x73, 0x7b, 0xe0, 0x5c, 0xe4, 0xfc, 0xce, 0x7e, 0x59, 0x87, 0xab, 0xb4, 0x30, 0xe4, 0x88, 0x75,
	0x44, 0xce, 0x93, 0x3e, 0x44, 0xb3, 0x10, 0x3e, 0x91, 0x96, 0x2a, 0x9f, 0xe2, 0xba, 0x44, 0xeb,
	0x92, 0xef, 0x36, 0x5c, 0x1e, 0xe6, 0xb6, 0x20, 0x6d, 0x91, 0x5d, 0xc3, 0xc9, 0x27, 0x99, 0x65,
	0x46, 0x3a, 0x23, 0x29, 0xe3, 0xd6, 0x4d, 0xeb, 0xb6, 0x3b, 0x3d, 0xf6, 0xd1, 0x58, 0x8f, 0xa5,
	0x64, 0x0f, 0x10, 0x4b, 0x83, 0x98, 0xa9, 0xd5, 0x22, 0xc7, 0xcc, 0xe1, 0xd6, 0x65, 0xa2, 0x34,
	0x0b, 0xa7, 0x48, 0xc7, 0xed, 0x50, 0xee, 0x7b, 0xfe, 0xea, 0xf1, 0x1b, 0x6e, 0xdd, 0xf3, 0x1e,
	0xb2, 0x47, 0x18, 0xae, 0x4b, 0xc5, 0x97, 0x8d, 0x66, 0x27, 0x98, 0x83, 0x50, 0xa8, 0xab, 0xfe,
	0x4d, 0xa5, 0x45, 0xa3, 0xf9, 0x7f, 0xff, 0xa6, 0xd2, 0xa2, 0x2e, 0xa6, 0x10, 0x05, 0xb1, 0xf8,
	0x20, 0x8d, 0x95, 0xd3, 0x0d, 0xce, 0x85, 0x47, 0x13, 0x4f, 0x6a, 0xfd, 0x8d, 0x12, 0x48, 0x55,
	0xbf, 0x57, 0xf5, 0xe7, 0x9e, 0xfc, 0xf6, 0x47, 0x30, 0xa8, 0xdd, 0x54, 0x18, 0xc5, 0x31, 0x3e,
	0x0a, 0x4a, 0x74, 0x78, 0xd0, 0xc4, 0xa3, 0xfb, 0x39, 0x9c, 0xed, 0x7e, 0x7e, 0x86, 0x66, 0xa3,
	0x38, 0xb2, 0x17, 0x38, 0xfd, 0x3b, 0x05, 0x8b, 0x53, 0x91, 0xf3, 0xb4, 0x61, 0xb5, 0xab, 0x61,
	0x03, 0xd9, 0xed, 0x96, 0xfc, 0x7b, 0xef, 0x85, 0xd5, 0x47, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xeb, 0x6a, 0x70, 0xa0, 0x09, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SystemServiceClient is the client API for SystemService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SystemServiceClient interface {
	// @Desc    	系统配置
	// <AUTHOR>
	// @Date		2021-10-13
	SystemConfig(ctx context.Context, in *SystemConfigRequest, opts ...grpc.CallOption) (*SystemConfigResponse, error)
}

type systemServiceClient struct {
	cc *grpc.ClientConn
}

func NewSystemServiceClient(cc *grpc.ClientConn) SystemServiceClient {
	return &systemServiceClient{cc}
}

func (c *systemServiceClient) SystemConfig(ctx context.Context, in *SystemConfigRequest, opts ...grpc.CallOption) (*SystemConfigResponse, error) {
	out := new(SystemConfigResponse)
	err := c.cc.Invoke(ctx, "/dgc.SystemService/SystemConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SystemServiceServer is the server API for SystemService service.
type SystemServiceServer interface {
	// @Desc    	系统配置
	// <AUTHOR>
	// @Date		2021-10-13
	SystemConfig(context.Context, *SystemConfigRequest) (*SystemConfigResponse, error)
}

// UnimplementedSystemServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSystemServiceServer struct {
}

func (*UnimplementedSystemServiceServer) SystemConfig(ctx context.Context, req *SystemConfigRequest) (*SystemConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SystemConfig not implemented")
}

func RegisterSystemServiceServer(s *grpc.Server, srv SystemServiceServer) {
	s.RegisterService(&_SystemService_serviceDesc, srv)
}

func _SystemService_SystemConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SystemConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SystemServiceServer).SystemConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.SystemService/SystemConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SystemServiceServer).SystemConfig(ctx, req.(*SystemConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SystemService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.SystemService",
	HandlerType: (*SystemServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SystemConfig",
			Handler:    _SystemService_SystemConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/system.proto",
}

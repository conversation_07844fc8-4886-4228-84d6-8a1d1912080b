package tasks

import (
	"order-center/models"
	"order-center/services"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

type TaskJddjDeliverySelf struct {
	services.BaseService
}

//定时任务更新京东到家自提,每10秒执行一次
func init() {
	if !kit.EnvCanCron() {
		return
	}
	glog.Info("task run...")

	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("@every 10s", func() {
		service := TaskJddjDeliverySelf{}
		service.JddjDeliverySelf()
	}); err != nil {
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func (s TaskJddjDeliverySelf) JddjDeliverySelf() {
	//连接池勿关闭
	redisConn := services.GetRedisConn()

	lockCard := "task:lock:AutoTaskJddjDeliverySelf"
	lockRes := redisConn.SetNX(lockCard, time.Now().Unix(), 15*time.Minute).Val()
	if !lockRes {
		return
	}
	defer redisConn.Del(lockCard)

	session := services.GetDBConn().NewSession()
	defer session.Close()

	var orderList []*models.OrderMain
	session.SQL(`
		SELECT order_main.* 
		FROM order_main
		INNER JOIN order_detail ON order_main.order_sn=order_detail.order_sn
		WHERE is_picking=0 
		AND channel_id=4 
		AND order_status_child=20102
		AND order_status=20 
		AND parent_order_sn>0 
		AND is_virtual=0
		AND order_type=2 
		AND push_third_order=1 
		AND delivery_type=3 
		AND accept_time<=DATE_SUB(SYSDATE(),INTERVAL 5 MINUTE) 
	`).Find(&orderList)
	if len(orderList) == 0 {
		return
	}

	var err error
	var orderLogs []*models.OrderLog
	var storeMasterId int32
	for _, i := range orderList {

		storeMasterId, err = services.GetAppChannelByOrderSn(i.OldOrderSn)
		if err != nil {
			glog.Error("JddjDeliverySelf,", "GetAppChannelByOrderSn,", i.OldOrderSn, i.OrderSn)
			continue
		}

		session.Begin()

		glog.Info(i.OrderSn, ", 定时任务更新京东到家拣货执行开始, ", i.OldOrderSn)
		_, err = session.In("order_sn", []string{i.OrderSn, i.ParentOrderSn}).Update(&models.OrderMain{
			OrderStatusChild: i.OrderStatusChild,
		})
		if err != nil {
			session.Rollback()
			glog.Error(i.OldOrderSn, ", 更新主订单信息失败, ", err)
			continue
		}

		_, err = session.In("order_sn", []string{i.OrderSn, i.ParentOrderSn}).Update(&models.OrderDetail{
			IsPicking:   1,
			PickingTime: time.Now(),
		})
		if err != nil {
			session.Rollback()
			glog.Error(i.OldOrderSn, ", 更新订单详情信息失败, ", err)
			continue
		}

		//拣货完成且顾客自提接口
		if i.LogisticsCode == "9999" {
			err = services.JddjOrderSelfMention(i.OldOrderSn, storeMasterId)
			if err != nil {
				session.Rollback()
				glog.Error(i.OldOrderSn, ", 定时任务推送京东到家拣货完成且顾客自提接口失败！", err)
				continue
			}
		}

		session.Commit()

		orderLogs = append(orderLogs, []*models.OrderLog{
			{
				OrderSn: i.OrderSn,
				LogType: models.OrderLogPickedOrder,
			},
			{
				OrderSn: i.ParentOrderSn,
				LogType: models.OrderLogPickedOrder,
			},
		}...)
	}

	services.SaveOrderLog(orderLogs)
}

package es

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/olivere/elastic/v7"
)

func TestProductDict_IkAnalyze(t *testing.T) {
	type args struct {
		keyword string
	}
	tests := []struct {
		name string
		p    *ProductDict
		args args
	}{
		{
			name: "",
			p:    &ProductDict{},
			args: args{
				keyword: "欧帝亿 IMPERIAL PAW  小型老年犬天然粮 比利时原装进口",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.p.IkAnalyze(tt.args.keyword); len(got) == 0 {
				t.Error("len 0")
			} else {
				for _, v := range got {
					if len(v.Token) >= 6 {
						println(v.Token)
					}

				}
			}
		})
	}
}

// func TestRegroupDict(t *testing.T) {
// 	engine := GetDBConn("root:root@(127.0.0.1:3306)/?charset=utf8")

// 	var productName []string
// 	if err := engine.SQL("SELECT name FROM dc_product.product").Find(&productName); err != nil {
// 		panic(err)
// 	}
// 	if err := engine.SQL("SELECT goods_name as name FROM dc_product.upet_goods").Find(&productName); err != nil {
// 		panic(err)
// 	}

// 	client := NewEsClient()

// 	var wg sync.WaitGroup
// 	ch := make(chan int, 12)

// 	var p *ProductDict
// 	for _, name := range productName {
// 		ch <- 1
// 		wg.Add(1)
// 		go func(v string) {
// 			defer func() {
// 				<-ch
// 				wg.Done()
// 			}()

// 			if res, err := client.IndexAnalyze().Analyzer("ik_smart").Text(v).Do(context.Background()); err != nil {
// 				glog.Error(err)
// 			} else {
// 				dict := p.RegroupDict(res.Tokens)
// 				for _, v2 := range dict {
// 					engine.Exec("insert datacenter.analyze_token(token,product_name) value(?,?)", v2, v)
// 				}
// 			}

// 		}(name)
// 	}
// 	wg.Wait()
// }

// func GetDBConn(dataSourceName ...string) *xorm.Engine {

// 	var mySqlStr string
// 	if len(dataSourceName) == 1 {
// 		mySqlStr = dataSourceName[0]
// 	} else {
// 		mySqlStr = config.GetString("mysql.dc_product")
// 	}

// 	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/dc_product?charset=utf8"
// 	//mySqlStr = "root:Rp000000@(10.1.1.245:3306)/dc_product?charset=utf8"

// 	if len(mySqlStr) == 0 {
// 		panic("can't find mysql url")
// 	}

// 	engine, err := xorm.NewEngine("mysql", mySqlStr)
// 	if err != nil {
// 		panic(err)
// 	}

// 	//空闲关闭时间
// 	engine.SetConnMaxLifetime(120 * time.Second)
// 	//最大空闲连接
// 	engine.SetMaxIdleConns(10)
// 	//最大连接数
// 	engine.SetMaxOpenConns(1000)

// 	engine.SetTZLocation(time.Local)

// 	return engine
// }

func TestToken(t *testing.T) {
	client := NewEsClient()
	tp := "product"
	// tp = "store"
	keyword := "猫liang"
	keyword = "猫粮"
	// keyword = "maoliang"
	keyword = "mao liang"

	bq := elastic.NewBoolQuery()
	if tp != "" {
		bq.Filter(elastic.NewMatchQuery("type.keyword", tp))
	}

	q := elastic.NewMatchQuery("token", keyword)
	q2 := elastic.NewMatchQuery("token.pinyin", keyword)
	bq.Should(q, q2)

	searchResult, err := client.Search("product_dict").Query(bq).From(0).Size(10).Do(context.Background())
	if err != nil {
		t.Error(err)
	}
	//添加排序
	productsRes := ConvertToWord(searchResult.Hits)
	for _, v := range productsRes {
		println(v.Token)
	}
}

func TestPinyin(t *testing.T) {

	py := "zhuan ye pei fang"
	pys := strings.Split(py, " ")

	keyword := "专业 治疗"
	var p ProductDict
	res := p.PinyinAnalyze(keyword)

	dict := p.RegroupDict(pys)
	dict2 := p.RegroupDict(p.TokensToArray(res))

	fmt.Println(dict, dict2, p.CompareDict(dict, dict2))

	return
	for _, v := range res {
		fmt.Println(v.Token)
	}

}

func TestProductDict_QueryWord(t *testing.T) {
	var keyword string
	keyword = "maoliang"
	keyword = "猫liang"
	keyword = "猫 粮"
	keyword = "美国"
	keyword = "皇家"
	// keyword = "白 儿"
	// keyword = "驱虫"
	// keyword = "驱虫"
	// keyword = "qu chong"
	// keyword = "chon wu qu chong"

	type args struct {
		keyword string
		style   string
		from    int
		size    int
	}
	tests := []struct {
		name    string
		p       *ProductDict
		args    args
		want    []*AssociationalWord
		wantErr bool
	}{
		{
			name: "联想词",
			p:    &ProductDict{},
			args: args{
				keyword: keyword,
				style:   "product",
				from:    0,
				size:    10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.QueryWord(tt.args.keyword, tt.args.style, tt.args.from, tt.args.size)
			// for _, v := range got {
			// 	fmt.Println(v.Token)
			// }
			if (err != nil) != tt.wantErr {
				t.Errorf("ProductDict.QueryWord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProductDict.QueryWord() = %v, want %v", got, tt.want)
			}
		})
	}
}

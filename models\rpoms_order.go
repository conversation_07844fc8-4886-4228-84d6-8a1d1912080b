package models

//OMS订单状态
const (
	OmsOrderStatusWaitSubmit  = iota + 1 // 待提交
	OmsOrderStatusWaitAuth               // 待审核
	OmsOrderStatusWaitDeliver            // 待出库
	OmsOrderStatusAuthRefuse             // 审核拒绝
	OmsOrderStatusDelivered              // 全部出库
	OmsOrderStatusCanceled               // 已取消
)

//销售渠道id 1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-竖屏 7其他
const (
	OmsSaleChannelAndroid = iota + 1
	OmsSaleChannelIOS
	OmsSaleChannelMiniProgram
	OmsSaleChannelWechatAccount
	OmsSaleChannelWeb
	OmsSaleChannelVerticalScreen
	OmsSaleChannelOther
)

//创建类型 1系统推送 2手动创建 3批量导入 4:手动退款关联
const (
	OmsCreateTypePush = iota + 1
	OmsCreateTypeManual
	OmsCreateTypeImport
	OmsCreateTypeManualRefund
)

//订单类型 1:B2C订单(默认) 2:预定订单
const (
	OmsOrderTypeB2C = iota + 1
	OmsOrderTypeReserve
)

//交易类型 1付款发货 2货到付款
const (
	OmsPayTypePayFirst = iota + 1
	OmsPayTypePayAfter
)

//配送类型,1物流发货,2快递发货,3车辆配送,4客户自提 5骑手配送 6自配送
//,1快递,2外卖,3自提,4同城送
const (
	OmsDeliveryTypeLogistics = iota + 1
	OmsDeliveryTypeExpress
	OmsDeliveryTypeCityDeliver
	OmsDeliveryTypeSelfPick
	OmsDeliveryTypeCourierDeliver
	OmsDeliveryTypeShopDeliver
)

//业务状态码
const (
	OmsOrderOk         = 1000 //订单正常
	OmsOrderExist      = 1001 //订单已存在
	OmsOrderAddSuccess = 1002 //订单落地成功
	OmsOrderAddFail    = 1003 //订单落地失败
	OmsOrderPushR1Fail = 1004 //推送r1失败
)

//根据阿闻的 user_agent字段转换成oms 的saleChannel字段
//除了 6与7 是与oms相反的  其他类型一样
func ParseOmsSaleChannel(userAgent int32) int {
	if userAgent == 6 {
		return OmsSaleChannelOther
	} else if userAgent == 7 {
		return OmsSaleChannelVerticalScreen
	} else {
		return int(userAgent)
	}
}

//根据阿闻的 user_agent字段转换成oms 的saleChannel字段
//除了 预订单都是 B2C订单
func ParseOmsOrderType(orderType int32) int {
	if orderType == 2 {
		return OmsOrderTypeReserve
	} else {
		return OmsOrderTypeB2C
	}
}

//根据阿闻的 deliver_type字段转换成oms 的deliver_type字段
//除了 预订单都是 B2C订单
//awen 1快递,2外卖,3自提,4同城送
func ParseOmsOrderDeliverType(deliverType int32, isThirdDeliver bool) int {
	switch deliverType {
	case 1:
		return OmsDeliveryTypeExpress
	case 2:
		if isThirdDeliver { //第三方骑手送货
			return OmsDeliveryTypeCourierDeliver
		} else { //自配送
			return OmsDeliveryTypeShopDeliver
		}
	case 3:
		return OmsDeliveryTypeSelfPick
	case 4:
		return OmsDeliveryTypeCityDeliver
	case 5:
		return OmsDeliveryTypeShopDeliver
	default:
		return 0
	}
}

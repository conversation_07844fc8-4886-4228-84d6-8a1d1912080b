package models

import "time"

type AbnormalOrderRecord struct {
	Id            int       `xorm:"not null pk autoincr comment('主键id') INT(11)"`
	OperateType   int       `xorm:"default NULL comment('操作类型，操作类型，1部分退款 2 手动退款 3 手动已完成') INT(11)"`
	OrderSn       string    `xorm:"default 'NULL' comment('订单号') VARCHAR(100)"`
	RefundOrderSn string    `xorm:"default 'NULL' comment('退款单号') VARCHAR(100)"`
	RefundAmount  string    `xorm:"default 'NULL' comment('退款金额') VARCHAR(30)"`
	OrderDescribe string    `xorm:"default 'NULL' comment('退款时间描述') VARCHAR(100)"`
	OperateReason string    `xorm:"default 'NULL' comment('退款原因') VARCHAR(10000)"`
	UserNo        string    `xorm:"default 'NULL' comment('操作人') VARCHAR(50)"`
	IpAddr        string    `xorm:"default 'NULL' comment('操作人ip地址') VARCHAR(100)"`
	UserName      string    `xorm:"default 'NULL' comment('操作人姓名') VARCHAR(100)"`
	Status        int       `xorm:"default NULL comment('退款状态 1进行中 2 退款失败 3退款成功') INT(11)"`
	Detail        string    `xorm:"default '''' comment('退款描述tip') VARCHAR(1000)"`
	RecordData    string    `xorm:"default 'NULL' comment('退款参数') TEXT"`
	CreateTime    time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime    time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	OrgId         int64     `json:"org_id"`
}

package models

import (
	"time"
)

type OrderInvoiceDetail struct {
	Id                   int       `json:"id"`
	OrderSn              string    `json:"order_sn"`                // 订单号
	OrderNo              string    `json:"order_no"`                // 申请单号
	InvoiceCode          string    `json:"invoice_code"`            // 发票代码，开票和红冲时一样
	TotalAmount          string    `json:"total_amount"`            // 开票金额，不含税总金额
	CanRefundTotalAmount string    `json:"can_refund_total_amount"` // 发票剩余可退总金额
	PdfUrl               string    `json:"pdf_url"`                 // '发票pdf图片地址
	Type                 int       `json:"type"`                    // 发票类型，0-未知，1-蓝票，2-红票
	Status               int       `json:"status"`                  // 发票处理结果:   1:开票成功、 2:开票失败、3:作废成
	InvoiceId            string    `json:"invoice_id"`              // 发票流水号
	Data                 string    `json:"data"`                    // 发票回调接收的全部数据
	CreatedAt            time.Time `xorm:"DATETIME created" json:"created_at"`
	UpdatedAt            time.Time `xorm:"DATETIME updated" json:"updated_at"`
}

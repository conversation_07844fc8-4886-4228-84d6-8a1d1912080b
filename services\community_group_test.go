package services

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	kit "github.com/tricobbler/rp-kit"
	"order-center/proto/oc"
	"testing"
)

func TestCommunityGroupService_ActivityList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CommunityGroupActivityListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.CommunityGroupActivityListResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				in: &oc.CommunityGroupActivityListRequest{
					FinanceCode: "133343",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommunityGroupService{}
			gotOut, err := c.ActivityList(tt.args.ctx, tt.args.in)
			if err != nil || gotOut.Code != 200 {

				t.Errorf("ActivityList err=%v got=%s", err, gotOut.Message)
				return
			}
		})
	}
}

func TestCommunityGroupService_Detail(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CommunityGroupDetailRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.CommunityGroupDetailResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				in: &oc.CommunityGroupDetailRequest{
					Id:           1,
					ReturnDetail: 1,
					ScrmId:       "5c1ad0f2e4e94f39a43c263c9b287d6d",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommunityGroupService{}
			gotOut, err := c.Detail(tt.args.ctx, tt.args.in)
			if err != nil || gotOut.Code != 200 {
				t.Errorf("ActivityList err=%v got=%s", err, gotOut.Message)
				return
			}
		})
	}
}

func TestCommunityGroupService_ParticipantList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CommunityGroupParticipantListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.CommunityGroupParticipantListResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				ctx: context.Background(),
				in: &oc.CommunityGroupParticipantListRequest{
					FinanceCode: "133343",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommunityGroupService{}
			gotOut, err := c.ParticipantList(tt.args.ctx, tt.args.in)
			if err != nil || gotOut.Code != 200 {
				t.Errorf("ParticipantList err=%v got=%s", err, gotOut.Message)
				return
			}
		})
	}
}

func TestCommunityGroupService_MessageSubscribe(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CommunityGroupMessageSubscribeRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.BaseResponseNew
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				in: &oc.CommunityGroupMessageSubscribeRequest{
					Type:   0,
					Number: "10",
					ScrmId: "1333",
				},
			},
		},
		{
			args: args{
				in: &oc.CommunityGroupMessageSubscribeRequest{
					Type:   1,
					Number: "4100000014584164",
					ScrmId: "1333",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommunityGroupService{}
			gotOut, err := c.MessageSubscribe(tt.args.ctx, tt.args.in)
			if err != nil || gotOut.Msg != "" {
				t.Errorf("MessageSubscribe err=%v got=%s", err, gotOut.Msg)
				return
			}
		})
	}
}

func TestCommunityGroupService_IsGroup(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.IsGroupReq
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.IsGroupRes
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				in: &oc.IsGroupReq{
					MemberId: "5c1ad0f2e4e94f39a43c263c9b287d6d",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommunityGroupService{}
			got, err := c.IsGroup(tt.args.ctx, tt.args.in)
			if err != nil || got.Msg != "" {
				t.Errorf("IsGroup err=%v got=%s", err, got.Msg)
				return
			}
		})
	}
}

func TestCommunityGroupService_GroupCompleted(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.GroupCompletedReq
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.BaseResponseNew
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "completed",
			args: args{
				ctx: context.Background(),
				in: &oc.GroupCompletedReq{
					Id: 150,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommunityGroupService{}
			got, err := c.GroupCompleted(tt.args.ctx, tt.args.in)
			t.Log(got)
			if !tt.wantErr(t, err, fmt.Sprintf("GroupCompleted(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			//assert.Equalf(t, tt.want, got, "GroupCompleted(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestCommunityGroupService_OrderList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.CommunityGroupOrderListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *oc.CommunityGroupOrderListResponse
		wantErr bool
	}{
		{
			args: args{
				in: &oc.CommunityGroupOrderListRequest{
					GroupStatus: "1",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommunityGroupService{}
			gotOut, err := c.OrderList(tt.args.ctx, tt.args.in)
			if err != nil || gotOut.Code != 200 {
				t.Errorf("OrderList err=%v got=%s", err, gotOut.Message)
				return
			}
		})
	}
}

func TestCommunityGroupService_MerberOrder(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.MerberOrderReq
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.MerberOrderRes
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: context.Background(),
				in: &oc.MerberOrderReq{
					Id: 18,
					//Key: "15118811999",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommunityGroupService{}
			got, err := c.MerberOrder(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("MerberOrder(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.want, got, "MerberOrder(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestCommunityGroupService_MyGroup(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.MyGroupReq
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.MyGroupRes
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: context.Background(),
				in: &oc.MyGroupReq{
					Year:     "2022",
					Month:    "05",
					MemberId: "57ccfc6ac6e84eb5a3ad04cbce2ef13e",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommunityGroupService{}
			got, err := c.MyGroup(tt.args.ctx, tt.args.in)
			if !tt.wantErr(t, err, fmt.Sprintf("MyGroup(%v, %v)", tt.args.ctx, tt.args.in)) {
				return
			}
			assert.Equalf(t, tt.want, got, "MyGroup(%v, %v)", tt.args.ctx, tt.args.in)
		})
	}
}

func TestCommunityGroupService_MyGroupList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *oc.MyGroupListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *oc.MyGroupListRes
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: context.Background(),
				in: &oc.MyGroupListReq{
					IsDis:    1,
					MemberId: "57ccfc6ac6e84eb5a3ad04cbce2ef13e",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := CommunityGroupService{}
			got, err := c.MyGroupList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(got))
			if err != nil {
				t.Errorf("MyGroupList() error = %v", err)
				return
			}
		})
	}
}

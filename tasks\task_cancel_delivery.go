package tasks

import (
	"encoding/json"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	"github.com/streadway/amqp"
	"order-center/dto"
	"order-center/models"
	"order-center/services"
	"order-center/utils"
)

//配送订单超时接单取消配送
func consumeDeliveryNoPickCancelTask() {
	utils.Consume(services.DeliveryOrderNoPickQueue, services.DeliveryOrderNoPickRoute, services.DeliveryOrderNoPickExchange, func(d amqp.Delivery) (response string, err error) {
		defer kit.CatchPanic()

		funcName := "配送5分钟超时没接单"
		orderJson := string(d.Body)
		glog.Info(funcName, "，MQ消息，", orderJson)
		MqInfo := dto.DeliverPriceRes{}
		err = json.Unmarshal(d.Body, &MqInfo)
		if err != nil {
			glog.Error(funcName, "，解析数据失败:,"+orderJson)
		}
		TypeName := services.DeliveryTypeKeyMap[MqInfo.DeliveryType]

		//先查询是否接单，没有接单的话就取消订单
		var node models.OrderDeliveryNode
		db := services.GetDBConn()
		ok, err := db.Table("order_delivery_node").Where("delivery_id = ?", MqInfo.DeliveryId).OrderBy("id desc").Get(&node)
		if err != nil {
			glog.Error(TypeName+"超时未接单自动取消失败", "订单号："+MqInfo.OrderId, "配送单号："+cast.ToString(MqInfo.DeliveryId), err.Error())
			d.Ack(false)
			return
		}

		if ok {
			if node.DeliveryStatus != 0 {
				d.Ack(false)
				return
			}
		}
		//判断当前要取消的订单是不是最新的配送这次的
		last_delivery_id := -1
		ok, err = db.Table("order_delivery_record").Select("delivery_id").Where("order_sn = ?", MqInfo.OrderId).OrderBy("create_time desc").Get(&last_delivery_id)
		if err != nil {
			glog.Error(TypeName+"超时未接单自动取消不是最新配送跳过", "订单号："+MqInfo.OrderId, "配送单号："+cast.ToString(MqInfo.DeliveryId), err.Error())
			d.Ack(false)
			return
		}
		//说明已经重新发了配送了。不需要处理了
		if cast.ToInt64(last_delivery_id) != MqInfo.DeliveryId {
			glog.Info(TypeName+"超时未接单自动取消，取消", "订单号："+MqInfo.OrderId, "配送单号："+cast.ToString(MqInfo.DeliveryId), "最新配送:", last_delivery_id)
			d.Ack(false)
			return
		}

		glog.Info(TypeName+"超时未接单自动取消，取消", "订单号："+MqInfo.OrderId, "配送单号："+cast.ToString(MqInfo.DeliveryId))
		var c services.CommonService

		addException := 0
		//表示已经发过3次配送了。要进入异常订单了
		if MqInfo.DeliveryCount >= 3 {
			addException = 1
		}
		MqInfo.PcDeliverType = cast.ToString(MqInfo.DeliveryType)
		err = services.CancelDeliveryCommon(MqInfo.OrderId, "阿闻超时未接单自动取消", MqInfo.DeliveryId, addException)
		if err == nil {
			glog.Info(TypeName+"超时未接单自动取消，取消成功", "订单号："+MqInfo.OrderId, "配送单号："+cast.ToString(MqInfo.DeliveryId))
			if addException == 0 {

				c.AotuPushDelivery(MqInfo.OrderId, MqInfo)
			}
		}
		d.Ack(false)
		return
	})
}

//改为走延时队列处理
func cancelNoAcceptMTDelivery() {
	////连接池勿关闭
	//redis := services.GetRedisConn()
	//
	//lockCard := "task:lock:cancel_no_Accept_MT_Delivery_9Min"
	//lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	//if !lockRes {
	//	return
	//}
	//defer redis.Del(lockCard)
	////连接池勿关闭
	//db := services.GetDBConn()
	//outTime := config.GetString("out_delivery_time")
	//
	//var deliveryRecords []models.OrderDeliveryRecord
	//err := db.SQL("select * from (select od.* from order_delivery_record od inner join order_main om on om.`order_sn` = od.`order_sn` and om.`order_status_child` = 20102 and om.`order_status` = 20 inner join order_detail oo on oo.`order_sn` = om.`order_sn`where od.`delivery_service_code` <> 5001 and ((om.`channel_id` = 3 and om.`logistics_code` = '6') or (om.`channel_id` = 4 and om.`logistics_code` = '2938') or (om.`channel_id` = 2 and om.`logistics_code` != '2002' and om.`logistics_code` != '1001' and om.`logistics_code` != '1004' and om.`logistics_code` != '2010' AND om.`logistics_code` != '3001') or om.`channel_id` in (1,9)) and oo.`push_delivery` = 1 and od.create_time > '2021-04-27 12:00:00' and DATE_ADD(od.create_time, INTERVAL ? MINUTE) < now() ORDER BY od.id desc LIMIT 100000) o GROUP BY o.order_sn", cast.ToInt32(outTime)).Find(&deliveryRecords)
	//
	//if err != nil {
	//	return
	//}
	//for _, v := range deliveryRecords {
	//	//先查配送记录是不是待调度
	//	var node models.OrderDeliveryNode
	//	ok, err := db.Table("order_delivery_node").Where("delivery_id = ?", v.DeliveryId).OrderBy("id desc").Get(&node)
	//	if err != nil {
	//		continue
	//	}
	//
	//	if ok {
	//		if node.DeliveryStatus != 0 {
	//			continue
	//		}
	//	}
	//	//再查配送是否有异常
	//	var exception models.OrderException
	//	ok, err = db.Table("order_exception").Where("order_sn = ? and is_show = 1", v.OrderSn).Get(&exception)
	//	if err != nil {
	//		continue
	//	}
	//	if ok {
	//		continue
	//	}
	//
	//	glog.Info("美配超时未接单自动取消，取消美配", "订单号："+v.OrderSn, "配送单号："+cast.ToString(v.DeliveryId))
	//var c services.CommonService
	//	err = services.CancelDeliveryCommon(v.OrderSn, "超时未接单自动取消", v.DeliveryId, 0)
	//	if err == nil {
	//		glog.Info("美配超时未接单自动取消，取消美配成功", "订单号："+v.OrderSn, "配送单号："+cast.ToString(v.DeliveryId))
	//c.OnlyPushShanSong(v.OrderSn)
	//	}
	//}
}

//改为走延时队列处理
func cancelNoAcceptSSDelivery() {
	////连接池勿关闭
	//redis := services.GetRedisConn()
	//
	//lockCard := "task:lock:cancel_no_Accept_SS_Delivery_15Min"
	//lockRes := redis.SetNX(lockCard, time.Now().Unix(), 5*time.Minute).Val()
	//if !lockRes {
	//	return
	//}
	//defer redis.Del(lockCard)
	//
	////连接池勿关闭
	//db := services.GetDBConn()
	//
	//outTime := config.GetString("out_ss_delivery_time")
	//
	//var deliveryRecords []models.OrderDeliveryRecord
	//err := db.SQL("select od.* from order_delivery_record od inner join order_main om on om.`order_sn` = od.`order_sn` and om.`order_status_child` = 20102 and om.`order_status` = 20 inner join order_detail oo on oo.`order_sn` = om.`order_sn` where od.`delivery_service_code` = 5001 and ((om.`channel_id` = 3 and om.`logistics_code` = '6') or (om.`channel_id` = 4 and om.`logistics_code` = '2938') or (om.`channel_id` = 2 and om.`logistics_code` != '2002' and om.`logistics_code` = '1001' and om.`logistics_code` = '1004' and om.`logistics_code` = '2010' AND om.`logistics_code` = '3001') or om.`channel_id` in (1,9)) and oo.`push_delivery` = 1 and od.create_time > '2021-04-27 12:00:00' and DATE_ADD(od.create_time, INTERVAL ? MINUTE) < now()", cast.ToInt32(outTime)).Find(&deliveryRecords)
	//
	//if err != nil {
	//	return
	//}
	//for _, v := range deliveryRecords {
	//	//先查配送记录是不是待调度
	//	var node models.OrderDeliveryNode
	//	ok, err := db.Table("order_delivery_node").Where("delivery_id = ?", v.DeliveryId).OrderBy("id desc").Get(&node)
	//	if err != nil {
	//		continue
	//	}
	//
	//	if ok {
	//		if node.DeliveryStatus != 0 {
	//			continue
	//		}
	//	}
	//	//再查配送是否有异常
	//	var exception models.OrderException
	//	ok, err = db.Table("order_exception").Where("order_sn = ? and is_show = 1", v.OrderSn).Get(&exception)
	//	if err != nil {
	//		continue
	//	}
	//	if ok {
	//		continue
	//	}
	//
	//	glog.Info("闪送超时未接单自动取消，取消闪送", "订单号："+v.OrderSn, "配送单号："+cast.ToString(v.DeliveryId))
	//
	//	err = services.CancelDeliveryCommon(v.OrderSn, "超时未接单自动取消", v.DeliveryId, 1)
	//
	//	glog.Info("闪送超时未接单自动取消，取消闪送成功", "订单号："+v.OrderSn, "配送单号："+cast.ToString(v.DeliveryId))
	//
	//}
}

syntax = "proto3";
package et;

service EwForwardService{
  // 企微添加联系我方式
  rpc AddContactWay(AddContactWayReq) returns (AddContactWayResp);
}

message AddContactWayReq{
  int32 type = 1;
  int32 scene = 2;
  string remark = 3;
  bool skip_verify = 4;
  string state = 5;
  repeated string user = 6;
  repeated int32 party = 7;
  bool is_temp = 8;
  int32 expires_in = 9;
  int32 chat_expires_in = 10;
  string unionid = 11;
  bool is_exclusive = 12;
}

message AddContactWayResp{
  int32 errcode = 1;
  string errmsg = 2;
  string config_id = 3;
  string qr_code = 4;
}
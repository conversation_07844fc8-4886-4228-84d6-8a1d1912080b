package services

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"order-center/dto"
	"order-center/models"
	"order-center/proto/oc"
	"reflect"
	"testing"
	"time"
)

func TestOrderService_WrittenOffVirtualOrder(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *oc.WrittenOffVirtualOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.VirtualOrderResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "核销",
			args: args{
				ctx: nil,
				request: &oc.WrittenOffVirtualOrderRequest{
					VerifyCode: "001003274671",
					StoreId:    "8597",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.WrittenOffVirtualOrder(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("WrittenOffVirtualOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WrittenOffVirtualOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNotifyWrittenOffStatus(t *testing.T) {
	type args struct {
		oldOrderDetail dto.OldOrderDetail
		verifyShop     string
		verifyCode     string
		verifyTime     string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "核销通知",
			args: args{
				oldOrderDetail: dto.OldOrderDetail{
					Petid:               "",
					Isneedpost:          false,
					Recipient:           "",
					Address:             "",
					Mobile:              "",
					Expressno:           "",
					Expresscode:         "",
					Expressstate:        0,
					Cancelltime:         "",
					PayTime:             "",
					RefundTime:          "",
					Platform:            "",
					Membermobile:        "",
					Payway:              "",
					Expressmoney:        0,
					Ordertypedetail:     0,
					Orderid:             "860670537600226905",
					Memberid:            "",
					Ordermoney:          "",
					Ordertype:           0,
					Ordertypename:       "",
					Useragent:           0,
					Orderstate:          0,
					Orderchildenstate:   0,
					Orderdetail:         "",
					Platformid:          0,
					Belonghospitalid:    0,
					Createtime:          "",
					Lasttime:            "",
					Sumquantity:         0,
					Memberintegrals:     nil,
					Isevaluate:          0,
					Ispostupet:          0,
					Isnotify:            0,
					Id:                  "",
					Sku:                 "",
					Goodsid:             "",
					Barcode:             "",
					Goodsimage:          "",
					Siglegoodsimage:     "",
					Name:                "",
					Univalence:          0,
					Sellprice:           0,
					Quantity:            0,
					Unit:                "",
					Applyhospitalid:     "",
					Chargeoff:           0,
					Chargeoffcode:       "",
					Expiredate:          "",
					Chargeoffobject:     "",
					Chargeoffobjectname: "",
					Chargeofftime:       "",
					Chargeoffhospitalid: "",
					Chargeoffmemberid:   "",
					Isneedpush:          0,
					ChannelId:           0,
				},
				verifyShop: "11001",
				verifyCode: "312390505449028834",
				verifyTime: time.Now().Format("2006-01-02 15:04:05"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NotifyWrittenOffStatus(tt.args.oldOrderDetail, tt.args.verifyShop, tt.args.verifyCode, tt.args.verifyTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("NotifyWrittenOffStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("NotifyWrittenOffStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_GetVirtualOrderDetail(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *oc.GetVirtualOrderDetailRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.VirtualOrderResponse
		wantErr bool
	}{

		{name: "查询核销码", args: args{
			ctx: nil,
			request: &oc.GetVirtualOrderDetailRequest{
				WrittenOffCode: "92943E240C2C66D5",
			},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetVirtualOrderDetail(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVirtualOrderDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetVirtualOrderDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_GetVerifyCodes(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *oc.GetVerifyCodesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GetVerifyCodesResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.TODO(),
				request: &oc.GetVerifyCodesRequest{
					OldOrderSn:   "27010324153390405",
					VerifyStatus: 2,
					PageSize:     5,
					PageIndex:    1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetVerifyCodes(tt.args.ctx, tt.args.request)
			fmt.Println(got, err)
		})
	}
}

func TestGetVerifyCodes(t *testing.T) {
	type args struct {
		orderSn   string
		isVirtual int32
	}
	tests := []struct {
		name            string
		args            args
		wantQueryResult []*models.OrderVerifyCode
		wantErr         bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderSn:   "4000000001343348",
				isVirtual: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotQueryResult, err := GetVerifyCodes(tt.args.orderSn, tt.args.isVirtual)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVerifyCodes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotQueryResult, tt.wantQueryResult) {
				t.Errorf("GetVerifyCodes() gotQueryResult = %v, want %v", gotQueryResult, tt.wantQueryResult)
			}
		})
	}
}

func TestOrderService_QueryMallVirtualOrderExtendInfo(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *oc.QueryMallVirtualOrderExtendInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.QueryMallVirtualOrderExtendInfoResponse
		wantErr bool
	}{
		{
			args: args{
				request: &oc.QueryMallVirtualOrderExtendInfoRequest{
					OrderSn: "740702637840593745",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.QueryMallVirtualOrderExtendInfo(tt.args.ctx, tt.args.request)
			if err != nil {
				t.Errorf("QueryMallVirtualOrderExtendInfo() error = %s", err.Error())
				return
			}
			if gotOut != nil && gotOut.Code != 200 {
				t.Errorf("QueryMallVirtualOrderExtendInfo() gotOut = %v", gotOut)
			}
		})
	}
}

func TestOrderService_ExtendMallVirtualOrderVerifyCodeExpiryDate(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *oc.ExtendMallVirtualOrderVerifyCodeExpiryDateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseResponse
		wantErr bool
	}{
		{
			args: args{
				request: &oc.ExtendMallVirtualOrderVerifyCodeExpiryDateRequest{
					OrderSn: "740702637840593745",
					Date:    "2023-02-06",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.ExtendMallVirtualOrderVerifyCodeExpiryDate(tt.args.ctx, tt.args.request)
			if err != nil {
				t.Errorf("ExtendMallVirtualOrderVerifyCodeExpiryDate() error = %s", err.Error())
				return
			}
			if gotOut != nil && gotOut.Code != 200 {
				t.Errorf("ExtendMallVirtualOrderVerifyCodeExpiryDate() gotOut = %v", gotOut)
			}
		})
	}
}

func TestOrderService_QueryMallVirtualOrderWriteOffCodes(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *oc.QueryMallVirtualOrderWriteOffCodesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.QueryMallVirtualOrderWriteOffCodesResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				in: &oc.QueryMallVirtualOrderWriteOffCodesRequest{
					ScrmUserId: "02e2c06214aa4b7497199922af0e187c",
					VrOrderSn:  "",
				},
			},
		},
		{
			args: args{
				ctx: context.Background(),
				in: &oc.QueryMallVirtualOrderWriteOffCodesRequest{
					ScrmUserId: "02e2c06214aa4b7497199922af0e187c",
					VrOrderSn:  "740702637840593745", //根据订单号查
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{
				CommonService: tt.fields.CommonService,
			}
			gotOut, err := o.QueryMallVirtualOrderWriteOffCodes(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Errorf("QueryMallVirtualOrderWriteOffCodes() error = %s", err.Error())
				return
			}
			if gotOut.Code != 200 {
				t.Errorf("QueryMallVirtualOrderWriteOffCodes() gotOut = %s:%s", gotOut.Message, gotOut.Error)
			}
		})
	}
}

func TestBlgVerify(t *testing.T) {
	type args struct {
		detail  dto.OldOrderDetail
		request *oc.WrittenOffVirtualOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			args: args{
				detail: dto.OldOrderDetail{
					Orderid:             "4100000024627133",
					Chargeoffhospitalid: "2082",
					Chargeofftime:       "2024-03-05 18:35:37",
					Sku:                 "1051388001",
					Name:                "ws测试洗美团单 s",
					Memberid:            "99fe9cbdc3a94e59a5649020da415ca9",
					OldOrderSn:          "420762986521233056",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantErr(t, BlgVerify(tt.args.detail, "2024-03-05 18:35:37", "2082"), fmt.Sprintf("BlgVerify(%v, %v)", tt.args.detail, tt.args.request))
		})
	}
}

package vip_card

import (
	"errors"
	"fmt"
	"order-center/models"
	"order-center/proto/oc"
	"order-center/utils"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	"github.com/techoner/gophp/serialize"
	"github.com/xuri/excelize/v2"
)

func PhysicalVipCardOrderExportOp(session *xorm.Session, taskId int32) error {

	VipCardTask := models.VipCardTask{Id: taskId}
	logPrefix := fmt.Sprintf("PhysicalVipCardOrderExportOp===入参：%d", taskId)
	glog.Info(logPrefix)
	if has, err := session.Table("datacenter.vip_card_task").Get(&VipCardTask); err != nil {
		glog.Error(logPrefix, "获取任务失败：", err.Error())
		return errors.New("获取任务失败")
	} else if !has {
		glog.Error(logPrefix, "任务数据不存在")
		return errors.New("任务数据不存在")
	} else if VipCardTask.State != models.VCTaskStateIng {
		glog.Error(logPrefix, "任务不是处理中")
		return errors.New("任务不是处理中")
	}
	req := &oc.GetPhysicalVipCardOrderListRequest{}
	if err := kit.JsonDecode([]byte(VipCardTask.Req), req); err != nil {
		glog.Error(logPrefix, "解析入参失败：", err.Error())
		return errors.New("解析入参失败")
	}

	file := excelize.NewFile()
	writer, _ := file.NewStreamWriter("Sheet1")

	_ = writer.SetRow("A1", []interface{}{
		"订单编号", "会员名称", "购买时间", "状态", "类型",
		"卡号", "物流单号", "物流编码", "发货时间", "完成时间",
		"来源", "订单留言", "收货人", "联系电话", "会员手机", "收货地址", "商品名称",
	})

	//env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	//if env == "production" || env == "pro" {
	req.PageIndex = 1
	req.PageSize = 1000
	//}

	row := 0
	for {
		PhysicalVipCardOrders, _, err := GetPhysicalVipCardOrderList(session, req)
		if err != nil {
			msg := fmt.Sprintf("获取会员卡实体卡订单列表失败：%s", err.Error())
			glog.Error(logPrefix, msg)
			VipCardTask.Result = msg
			VipCardTask.State = models.VCTaskStateFail
			session.Table("datacenter.vip_card_task").Cols("state,result").ID(taskId).Update(&VipCardTask)
		}
		for _, v := range PhysicalVipCardOrders {
			ReciverInfo, _ := serialize.UnMarshal([]byte(v.UpetOrderCommon.ReciverInfo))
			reciverInfoStr := kit.JsonEncode(ReciverInfo)
			reciverInfoStruct := models.ReciverInfo{}
			if err := kit.JsonDecode([]byte(reciverInfoStr), &reciverInfoStruct); err != nil {
				glog.Error(logPrefix, "解析收获信息失败:", err.Error())
				return errors.New("解析收获信息失败")
			}
			VirtualCardId := ""
			if v.UpetOrders.VirtualCardId > 0 {
				VirtualCardId = fmt.Sprintf("FY%d", v.UpetOrders.VirtualCardId)
			}
			_ = writer.SetRow("A"+cast.ToString(row+2), []interface{}{
				fmt.Sprintf("%d", v.UpetOrders.OrderSn),
				v.UpetMember.MemberName,
				utils.UnixToTimeFormat(kit.DATETIME_LAYOUT, int64(v.UpetOrders.PaymentTime)),
				models.UpetOrderStateMap[v.UpetOrders.OrderState],
				"实物商品",
				VirtualCardId,
				v.UpetOrders.ShippingCode,
				v.UpetExpress.ECodeKdniao, //物流编码
				utils.UnixToTimeFormat(kit.DATETIME_LAYOUT, int64(v.UpetOrderCommon.ShippingTime)), //发货时间
				utils.UnixToTimeFormat(kit.DATETIME_LAYOUT, int64(v.UpetOrders.FinnshedTime)),
				models.UpetOrderFromMap[v.UpetOrders.OrderFrom],
				v.UpetOrderCommon.OrderMessage,
				v.UpetOrderCommon.ReciverName,
				reciverInfoStruct.MobPhone,
				v.UpetOrders.BuyerPhone,
				reciverInfoStruct.Address,
				v.UpetOrderGoods.GoodsName,
			})
			row++
		}
		if len(PhysicalVipCardOrders) < int(req.PageSize) {
			break
		}
		req.PageIndex++
	}

	if err := writer.Flush(); err != nil {
		msg := fmt.Sprintf("数据写入excel文件失败：%s", err.Error())
		glog.Error(logPrefix, msg)
		VipCardTask.Result = msg
		VipCardTask.State = models.VCTaskStateFail
		session.Table("datacenter.vip_card_task").Cols("state,result").ID(taskId).Update(&VipCardTask)
	}

	// 上传excel文件
	if url, err := utils.UploadExcelToQiNiu1(file, ""); err != nil {
		msg := fmt.Sprintf("上传excel到七牛云失败：%s", err.Error())
		glog.Error(logPrefix, msg)
		VipCardTask.Result = msg
		VipCardTask.State = models.VCTaskStateFail
		session.Table("datacenter.vip_card_task").Cols("state,result").ID(taskId).Update(&VipCardTask)
	} else {
		VipCardTask.Result = "成功"
		VipCardTask.State = models.VCTaskStateSuccess
		VipCardTask.Url = url
		session.Table("datacenter.vip_card_task").Cols("state,result,url").ID(taskId).Update(&VipCardTask)
	}

	//查询数据
	return nil
}

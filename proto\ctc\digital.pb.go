// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ctc/digital.proto

package ctc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 用户实名注册入参
type RealRegisterRequest struct {
	PersonName           string   `protobuf:"bytes,1,opt,name=person_name,json=personName,proto3" json:"person_name"`
	Mobile               string   `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	IdCard               string   `protobuf:"bytes,3,opt,name=id_card,json=idCard,proto3" json:"id_card"`
	Openid               string   `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid"`
	ScrmUserId           string   `protobuf:"bytes,5,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RealRegisterRequest) Reset()         { *m = RealRegisterRequest{} }
func (m *RealRegisterRequest) String() string { return proto.CompactTextString(m) }
func (*RealRegisterRequest) ProtoMessage()    {}
func (*RealRegisterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{0}
}

func (m *RealRegisterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRegisterRequest.Unmarshal(m, b)
}
func (m *RealRegisterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRegisterRequest.Marshal(b, m, deterministic)
}
func (m *RealRegisterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRegisterRequest.Merge(m, src)
}
func (m *RealRegisterRequest) XXX_Size() int {
	return xxx_messageInfo_RealRegisterRequest.Size(m)
}
func (m *RealRegisterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRegisterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RealRegisterRequest proto.InternalMessageInfo

func (m *RealRegisterRequest) GetPersonName() string {
	if m != nil {
		return m.PersonName
	}
	return ""
}

func (m *RealRegisterRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *RealRegisterRequest) GetIdCard() string {
	if m != nil {
		return m.IdCard
	}
	return ""
}

func (m *RealRegisterRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *RealRegisterRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

// 用户实名注册返回值
type RealRegisterResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	UserIdentification   string   `protobuf:"bytes,3,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RealRegisterResponse) Reset()         { *m = RealRegisterResponse{} }
func (m *RealRegisterResponse) String() string { return proto.CompactTextString(m) }
func (*RealRegisterResponse) ProtoMessage()    {}
func (*RealRegisterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{1}
}

func (m *RealRegisterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRegisterResponse.Unmarshal(m, b)
}
func (m *RealRegisterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRegisterResponse.Marshal(b, m, deterministic)
}
func (m *RealRegisterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRegisterResponse.Merge(m, src)
}
func (m *RealRegisterResponse) XXX_Size() int {
	return xxx_messageInfo_RealRegisterResponse.Size(m)
}
func (m *RealRegisterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRegisterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RealRegisterResponse proto.InternalMessageInfo

func (m *RealRegisterResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RealRegisterResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RealRegisterResponse) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

// 素材上传参数
type DigitalImageUploadRequest struct {
	FilePath             string   `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DigitalImageUploadRequest) Reset()         { *m = DigitalImageUploadRequest{} }
func (m *DigitalImageUploadRequest) String() string { return proto.CompactTextString(m) }
func (*DigitalImageUploadRequest) ProtoMessage()    {}
func (*DigitalImageUploadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{2}
}

func (m *DigitalImageUploadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalImageUploadRequest.Unmarshal(m, b)
}
func (m *DigitalImageUploadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalImageUploadRequest.Marshal(b, m, deterministic)
}
func (m *DigitalImageUploadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalImageUploadRequest.Merge(m, src)
}
func (m *DigitalImageUploadRequest) XXX_Size() int {
	return xxx_messageInfo_DigitalImageUploadRequest.Size(m)
}
func (m *DigitalImageUploadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalImageUploadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalImageUploadRequest proto.InternalMessageInfo

func (m *DigitalImageUploadRequest) GetFilePath() string {
	if m != nil {
		return m.FilePath
	}
	return ""
}

func (m *DigitalImageUploadRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

// 素材上传返回值
type DigitalImageUploadResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DigitalImageUploadResponse) Reset()         { *m = DigitalImageUploadResponse{} }
func (m *DigitalImageUploadResponse) String() string { return proto.CompactTextString(m) }
func (*DigitalImageUploadResponse) ProtoMessage()    {}
func (*DigitalImageUploadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{3}
}

func (m *DigitalImageUploadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalImageUploadResponse.Unmarshal(m, b)
}
func (m *DigitalImageUploadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalImageUploadResponse.Marshal(b, m, deterministic)
}
func (m *DigitalImageUploadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalImageUploadResponse.Merge(m, src)
}
func (m *DigitalImageUploadResponse) XXX_Size() int {
	return xxx_messageInfo_DigitalImageUploadResponse.Size(m)
}
func (m *DigitalImageUploadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalImageUploadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalImageUploadResponse proto.InternalMessageInfo

func (m *DigitalImageUploadResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DigitalImageUploadResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DigitalImageUploadResponse) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 发行数字藏品参数
type NftPublishDigitalRequest struct {
	Author               string   `protobuf:"bytes,1,opt,name=author,proto3" json:"author"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	DisplayUrl           string   `protobuf:"bytes,4,opt,name=display_url,json=displayUrl,proto3" json:"display_url"`
	Desc                 string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc"`
	Flag                 string   `protobuf:"bytes,6,opt,name=flag,proto3" json:"flag"`
	UserIdentification   string   `protobuf:"bytes,7,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	PublishCount         int32    `protobuf:"varint,8,opt,name=publish_count,json=publishCount,proto3" json:"publish_count"`
	SeriesId             string   `protobuf:"bytes,9,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	SeriesBeginIndex     int32    `protobuf:"varint,10,opt,name=series_begin_index,json=seriesBeginIndex,proto3" json:"series_begin_index"`
	SellStatus           int32    `protobuf:"varint,11,opt,name=sell_status,json=sellStatus,proto3" json:"sell_status"`
	SellCount            int32    `protobuf:"varint,12,opt,name=sell_count,json=sellCount,proto3" json:"sell_count"`
	PackageType          string   `protobuf:"bytes,13,opt,name=package_type,json=packageType,proto3" json:"package_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPublishDigitalRequest) Reset()         { *m = NftPublishDigitalRequest{} }
func (m *NftPublishDigitalRequest) String() string { return proto.CompactTextString(m) }
func (*NftPublishDigitalRequest) ProtoMessage()    {}
func (*NftPublishDigitalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{4}
}

func (m *NftPublishDigitalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPublishDigitalRequest.Unmarshal(m, b)
}
func (m *NftPublishDigitalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPublishDigitalRequest.Marshal(b, m, deterministic)
}
func (m *NftPublishDigitalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPublishDigitalRequest.Merge(m, src)
}
func (m *NftPublishDigitalRequest) XXX_Size() int {
	return xxx_messageInfo_NftPublishDigitalRequest.Size(m)
}
func (m *NftPublishDigitalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPublishDigitalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPublishDigitalRequest proto.InternalMessageInfo

func (m *NftPublishDigitalRequest) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetDisplayUrl() string {
	if m != nil {
		return m.DisplayUrl
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetFlag() string {
	if m != nil {
		return m.Flag
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetPublishCount() int32 {
	if m != nil {
		return m.PublishCount
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetSeriesBeginIndex() int32 {
	if m != nil {
		return m.SeriesBeginIndex
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSellStatus() int32 {
	if m != nil {
		return m.SellStatus
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSellCount() int32 {
	if m != nil {
		return m.SellCount
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetPackageType() string {
	if m != nil {
		return m.PackageType
	}
	return ""
}

//发行数字藏品返回值
type NftPublishDigitalResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPublishDigitalResponse) Reset()         { *m = NftPublishDigitalResponse{} }
func (m *NftPublishDigitalResponse) String() string { return proto.CompactTextString(m) }
func (*NftPublishDigitalResponse) ProtoMessage()    {}
func (*NftPublishDigitalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{5}
}

func (m *NftPublishDigitalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPublishDigitalResponse.Unmarshal(m, b)
}
func (m *NftPublishDigitalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPublishDigitalResponse.Marshal(b, m, deterministic)
}
func (m *NftPublishDigitalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPublishDigitalResponse.Merge(m, src)
}
func (m *NftPublishDigitalResponse) XXX_Size() int {
	return xxx_messageInfo_NftPublishDigitalResponse.Size(m)
}
func (m *NftPublishDigitalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPublishDigitalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftPublishDigitalResponse proto.InternalMessageInfo

func (m *NftPublishDigitalResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftPublishDigitalResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftPublishDigitalResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//声明系列入参
type NftSeriesClaimRequest struct {
	SeriesName           string   `protobuf:"bytes,1,opt,name=series_name,json=seriesName,proto3" json:"series_name"`
	TotalCount           int32    `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	CoverUrl             string   `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,5,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesClaimRequest) Reset()         { *m = NftSeriesClaimRequest{} }
func (m *NftSeriesClaimRequest) String() string { return proto.CompactTextString(m) }
func (*NftSeriesClaimRequest) ProtoMessage()    {}
func (*NftSeriesClaimRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{6}
}

func (m *NftSeriesClaimRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesClaimRequest.Unmarshal(m, b)
}
func (m *NftSeriesClaimRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesClaimRequest.Marshal(b, m, deterministic)
}
func (m *NftSeriesClaimRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesClaimRequest.Merge(m, src)
}
func (m *NftSeriesClaimRequest) XXX_Size() int {
	return xxx_messageInfo_NftSeriesClaimRequest.Size(m)
}
func (m *NftSeriesClaimRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesClaimRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesClaimRequest proto.InternalMessageInfo

func (m *NftSeriesClaimRequest) GetSeriesName() string {
	if m != nil {
		return m.SeriesName
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *NftSeriesClaimRequest) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

//声明系列返回值
type NftSeriesClaimResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesClaimResponse) Reset()         { *m = NftSeriesClaimResponse{} }
func (m *NftSeriesClaimResponse) String() string { return proto.CompactTextString(m) }
func (*NftSeriesClaimResponse) ProtoMessage()    {}
func (*NftSeriesClaimResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{7}
}

func (m *NftSeriesClaimResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesClaimResponse.Unmarshal(m, b)
}
func (m *NftSeriesClaimResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesClaimResponse.Marshal(b, m, deterministic)
}
func (m *NftSeriesClaimResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesClaimResponse.Merge(m, src)
}
func (m *NftSeriesClaimResponse) XXX_Size() int {
	return xxx_messageInfo_NftSeriesClaimResponse.Size(m)
}
func (m *NftSeriesClaimResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesClaimResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesClaimResponse proto.InternalMessageInfo

func (m *NftSeriesClaimResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSeriesClaimResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSeriesClaimResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//nft数字藏品列表藏品入参
type NftSeriesListRequest struct {
	Offset               int64    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset"`
	Limit                int64    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit"`
	SeriesId             string   `protobuf:"bytes,3,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	UserIdentification   string   `protobuf:"bytes,4,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesListRequest) Reset()         { *m = NftSeriesListRequest{} }
func (m *NftSeriesListRequest) String() string { return proto.CompactTextString(m) }
func (*NftSeriesListRequest) ProtoMessage()    {}
func (*NftSeriesListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{8}
}

func (m *NftSeriesListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesListRequest.Unmarshal(m, b)
}
func (m *NftSeriesListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesListRequest.Marshal(b, m, deterministic)
}
func (m *NftSeriesListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesListRequest.Merge(m, src)
}
func (m *NftSeriesListRequest) XXX_Size() int {
	return xxx_messageInfo_NftSeriesListRequest.Size(m)
}
func (m *NftSeriesListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesListRequest proto.InternalMessageInfo

func (m *NftSeriesListRequest) GetOffset() int64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *NftSeriesListRequest) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *NftSeriesListRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftSeriesListRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftInfo struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	Flag                 string   `protobuf:"bytes,2,opt,name=flag,proto3" json:"flag"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,5,opt,name=url,proto3" json:"url"`
	DisplayUrl           string   `protobuf:"bytes,6,opt,name=display_url,json=displayUrl,proto3" json:"display_url"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc"`
	SeriesName           string   `protobuf:"bytes,8,opt,name=series_name,json=seriesName,proto3" json:"series_name"`
	SeriesId             string   `protobuf:"bytes,9,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	SellStatus           int32    `protobuf:"varint,10,opt,name=sell_status,json=sellStatus,proto3" json:"sell_status"`
	SeriesTotalNum       int32    `protobuf:"varint,11,opt,name=series_total_num,json=seriesTotalNum,proto3" json:"series_total_num"`
	SeriesIndexId        int32    `protobuf:"varint,12,opt,name=series_index_id,json=seriesIndexId,proto3" json:"series_index_id"`
	SellCount            int32    `protobuf:"varint,13,opt,name=sell_count,json=sellCount,proto3" json:"sell_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfo) Reset()         { *m = NftInfo{} }
func (m *NftInfo) String() string { return proto.CompactTextString(m) }
func (*NftInfo) ProtoMessage()    {}
func (*NftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{9}
}

func (m *NftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfo.Unmarshal(m, b)
}
func (m *NftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfo.Marshal(b, m, deterministic)
}
func (m *NftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfo.Merge(m, src)
}
func (m *NftInfo) XXX_Size() int {
	return xxx_messageInfo_NftInfo.Size(m)
}
func (m *NftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfo proto.InternalMessageInfo

func (m *NftInfo) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftInfo) GetFlag() string {
	if m != nil {
		return m.Flag
	}
	return ""
}

func (m *NftInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *NftInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *NftInfo) GetDisplayUrl() string {
	if m != nil {
		return m.DisplayUrl
	}
	return ""
}

func (m *NftInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftInfo) GetSeriesName() string {
	if m != nil {
		return m.SeriesName
	}
	return ""
}

func (m *NftInfo) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftInfo) GetSellStatus() int32 {
	if m != nil {
		return m.SellStatus
	}
	return 0
}

func (m *NftInfo) GetSeriesTotalNum() int32 {
	if m != nil {
		return m.SeriesTotalNum
	}
	return 0
}

func (m *NftInfo) GetSeriesIndexId() int32 {
	if m != nil {
		return m.SeriesIndexId
	}
	return 0
}

func (m *NftInfo) GetSellCount() int32 {
	if m != nil {
		return m.SellCount
	}
	return 0
}

//nft数字藏品列表返回值
type NftSeriesListResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32      `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	NftInfo              []*NftInfo `protobuf:"bytes,4,rep,name=nft_info,json=nftInfo,proto3" json:"nft_info"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NftSeriesListResponse) Reset()         { *m = NftSeriesListResponse{} }
func (m *NftSeriesListResponse) String() string { return proto.CompactTextString(m) }
func (*NftSeriesListResponse) ProtoMessage()    {}
func (*NftSeriesListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{10}
}

func (m *NftSeriesListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesListResponse.Unmarshal(m, b)
}
func (m *NftSeriesListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesListResponse.Marshal(b, m, deterministic)
}
func (m *NftSeriesListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesListResponse.Merge(m, src)
}
func (m *NftSeriesListResponse) XXX_Size() int {
	return xxx_messageInfo_NftSeriesListResponse.Size(m)
}
func (m *NftSeriesListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesListResponse proto.InternalMessageInfo

func (m *NftSeriesListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSeriesListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSeriesListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *NftSeriesListResponse) GetNftInfo() []*NftInfo {
	if m != nil {
		return m.NftInfo
	}
	return nil
}

//查询各类结果入参
type NftSearchResultRequest struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Method               string   `protobuf:"bytes,2,opt,name=method,proto3" json:"method"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSearchResultRequest) Reset()         { *m = NftSearchResultRequest{} }
func (m *NftSearchResultRequest) String() string { return proto.CompactTextString(m) }
func (*NftSearchResultRequest) ProtoMessage()    {}
func (*NftSearchResultRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{11}
}

func (m *NftSearchResultRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSearchResultRequest.Unmarshal(m, b)
}
func (m *NftSearchResultRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSearchResultRequest.Marshal(b, m, deterministic)
}
func (m *NftSearchResultRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSearchResultRequest.Merge(m, src)
}
func (m *NftSearchResultRequest) XXX_Size() int {
	return xxx_messageInfo_NftSearchResultRequest.Size(m)
}
func (m *NftSearchResultRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSearchResultRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSearchResultRequest proto.InternalMessageInfo

func (m *NftSearchResultRequest) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *NftSearchResultRequest) GetMethod() string {
	if m != nil {
		return m.Method
	}
	return ""
}

//查询各类结果返回值
type NftSearchResultResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSearchResultResponse) Reset()         { *m = NftSearchResultResponse{} }
func (m *NftSearchResultResponse) String() string { return proto.CompactTextString(m) }
func (*NftSearchResultResponse) ProtoMessage()    {}
func (*NftSearchResultResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{12}
}

func (m *NftSearchResultResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSearchResultResponse.Unmarshal(m, b)
}
func (m *NftSearchResultResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSearchResultResponse.Marshal(b, m, deterministic)
}
func (m *NftSearchResultResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSearchResultResponse.Merge(m, src)
}
func (m *NftSearchResultResponse) XXX_Size() int {
	return xxx_messageInfo_NftSearchResultResponse.Size(m)
}
func (m *NftSearchResultResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSearchResultResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSearchResultResponse proto.InternalMessageInfo

func (m *NftSearchResultResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSearchResultResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSearchResultResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//积分查询入参
type NftPointQueryRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPointQueryRequest) Reset()         { *m = NftPointQueryRequest{} }
func (m *NftPointQueryRequest) String() string { return proto.CompactTextString(m) }
func (*NftPointQueryRequest) ProtoMessage()    {}
func (*NftPointQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{13}
}

func (m *NftPointQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPointQueryRequest.Unmarshal(m, b)
}
func (m *NftPointQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPointQueryRequest.Marshal(b, m, deterministic)
}
func (m *NftPointQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPointQueryRequest.Merge(m, src)
}
func (m *NftPointQueryRequest) XXX_Size() int {
	return xxx_messageInfo_NftPointQueryRequest.Size(m)
}
func (m *NftPointQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPointQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPointQueryRequest proto.InternalMessageInfo

func (m *NftPointQueryRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

//积分查询返回值
type NftPointQueryResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Count                int32    `protobuf:"varint,3,opt,name=count,proto3" json:"count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPointQueryResponse) Reset()         { *m = NftPointQueryResponse{} }
func (m *NftPointQueryResponse) String() string { return proto.CompactTextString(m) }
func (*NftPointQueryResponse) ProtoMessage()    {}
func (*NftPointQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{14}
}

func (m *NftPointQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPointQueryResponse.Unmarshal(m, b)
}
func (m *NftPointQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPointQueryResponse.Marshal(b, m, deterministic)
}
func (m *NftPointQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPointQueryResponse.Merge(m, src)
}
func (m *NftPointQueryResponse) XXX_Size() int {
	return xxx_messageInfo_NftPointQueryResponse.Size(m)
}
func (m *NftPointQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPointQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftPointQueryResponse proto.InternalMessageInfo

func (m *NftPointQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftPointQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftPointQueryResponse) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

//数字藏品详细信息参数
type NftInfoRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfoRequest) Reset()         { *m = NftInfoRequest{} }
func (m *NftInfoRequest) String() string { return proto.CompactTextString(m) }
func (*NftInfoRequest) ProtoMessage()    {}
func (*NftInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{15}
}

func (m *NftInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfoRequest.Unmarshal(m, b)
}
func (m *NftInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfoRequest.Marshal(b, m, deterministic)
}
func (m *NftInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfoRequest.Merge(m, src)
}
func (m *NftInfoRequest) XXX_Size() int {
	return xxx_messageInfo_NftInfoRequest.Size(m)
}
func (m *NftInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfoRequest proto.InternalMessageInfo

func (m *NftInfoRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

//数字藏品详细信息参数返回值
type NftInfoResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	NftInfo              *NftInfo `protobuf:"bytes,3,opt,name=nft_info,json=nftInfo,proto3" json:"nft_info"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfoResponse) Reset()         { *m = NftInfoResponse{} }
func (m *NftInfoResponse) String() string { return proto.CompactTextString(m) }
func (*NftInfoResponse) ProtoMessage()    {}
func (*NftInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{16}
}

func (m *NftInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfoResponse.Unmarshal(m, b)
}
func (m *NftInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfoResponse.Marshal(b, m, deterministic)
}
func (m *NftInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfoResponse.Merge(m, src)
}
func (m *NftInfoResponse) XXX_Size() int {
	return xxx_messageInfo_NftInfoResponse.Size(m)
}
func (m *NftInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfoResponse proto.InternalMessageInfo

func (m *NftInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftInfoResponse) GetNftInfo() *NftInfo {
	if m != nil {
		return m.NftInfo
	}
	return nil
}

//nft购买参数
type NftBuyRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	ApplyScore           int32    `protobuf:"varint,3,opt,name=apply_score,json=applyScore,proto3" json:"apply_score"`
	OfferCount           int32    `protobuf:"varint,4,opt,name=offer_count,json=offerCount,proto3" json:"offer_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftBuyRequest) Reset()         { *m = NftBuyRequest{} }
func (m *NftBuyRequest) String() string { return proto.CompactTextString(m) }
func (*NftBuyRequest) ProtoMessage()    {}
func (*NftBuyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{17}
}

func (m *NftBuyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftBuyRequest.Unmarshal(m, b)
}
func (m *NftBuyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftBuyRequest.Marshal(b, m, deterministic)
}
func (m *NftBuyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftBuyRequest.Merge(m, src)
}
func (m *NftBuyRequest) XXX_Size() int {
	return xxx_messageInfo_NftBuyRequest.Size(m)
}
func (m *NftBuyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftBuyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftBuyRequest proto.InternalMessageInfo

func (m *NftBuyRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftBuyRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftBuyRequest) GetApplyScore() int32 {
	if m != nil {
		return m.ApplyScore
	}
	return 0
}

func (m *NftBuyRequest) GetOfferCount() int32 {
	if m != nil {
		return m.OfferCount
	}
	return 0
}

//数字藏品购买返回值
type NftBuyResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftBuyResponse) Reset()         { *m = NftBuyResponse{} }
func (m *NftBuyResponse) String() string { return proto.CompactTextString(m) }
func (*NftBuyResponse) ProtoMessage()    {}
func (*NftBuyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{18}
}

func (m *NftBuyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftBuyResponse.Unmarshal(m, b)
}
func (m *NftBuyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftBuyResponse.Marshal(b, m, deterministic)
}
func (m *NftBuyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftBuyResponse.Merge(m, src)
}
func (m *NftBuyResponse) XXX_Size() int {
	return xxx_messageInfo_NftBuyResponse.Size(m)
}
func (m *NftBuyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftBuyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftBuyResponse proto.InternalMessageInfo

func (m *NftBuyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftBuyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftBuyResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//nft销售状态变更参数
type NftStatusRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	TransStatus          int32    `protobuf:"varint,3,opt,name=trans_status,json=transStatus,proto3" json:"trans_status"`
	TransPrice           int32    `protobuf:"varint,4,opt,name=trans_price,json=transPrice,proto3" json:"trans_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftStatusRequest) Reset()         { *m = NftStatusRequest{} }
func (m *NftStatusRequest) String() string { return proto.CompactTextString(m) }
func (*NftStatusRequest) ProtoMessage()    {}
func (*NftStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{19}
}

func (m *NftStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftStatusRequest.Unmarshal(m, b)
}
func (m *NftStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftStatusRequest.Marshal(b, m, deterministic)
}
func (m *NftStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftStatusRequest.Merge(m, src)
}
func (m *NftStatusRequest) XXX_Size() int {
	return xxx_messageInfo_NftStatusRequest.Size(m)
}
func (m *NftStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftStatusRequest proto.InternalMessageInfo

func (m *NftStatusRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftStatusRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftStatusRequest) GetTransStatus() int32 {
	if m != nil {
		return m.TransStatus
	}
	return 0
}

func (m *NftStatusRequest) GetTransPrice() int32 {
	if m != nil {
		return m.TransPrice
	}
	return 0
}

//nft销售状态变更返回值
type NftStatusResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftStatusResponse) Reset()         { *m = NftStatusResponse{} }
func (m *NftStatusResponse) String() string { return proto.CompactTextString(m) }
func (*NftStatusResponse) ProtoMessage()    {}
func (*NftStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{20}
}

func (m *NftStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftStatusResponse.Unmarshal(m, b)
}
func (m *NftStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftStatusResponse.Marshal(b, m, deterministic)
}
func (m *NftStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftStatusResponse.Merge(m, src)
}
func (m *NftStatusResponse) XXX_Size() int {
	return xxx_messageInfo_NftStatusResponse.Size(m)
}
func (m *NftStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftStatusResponse proto.InternalMessageInfo

func (m *NftStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftStatusResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//判断获取openid
type UserOpenidRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOpenidRequest) Reset()         { *m = UserOpenidRequest{} }
func (m *UserOpenidRequest) String() string { return proto.CompactTextString(m) }
func (*UserOpenidRequest) ProtoMessage()    {}
func (*UserOpenidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{21}
}

func (m *UserOpenidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOpenidRequest.Unmarshal(m, b)
}
func (m *UserOpenidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOpenidRequest.Marshal(b, m, deterministic)
}
func (m *UserOpenidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOpenidRequest.Merge(m, src)
}
func (m *UserOpenidRequest) XXX_Size() int {
	return xxx_messageInfo_UserOpenidRequest.Size(m)
}
func (m *UserOpenidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOpenidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserOpenidRequest proto.InternalMessageInfo

func (m *UserOpenidRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type UserOpenidResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOpenidResponse) Reset()         { *m = UserOpenidResponse{} }
func (m *UserOpenidResponse) String() string { return proto.CompactTextString(m) }
func (*UserOpenidResponse) ProtoMessage()    {}
func (*UserOpenidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{22}
}

func (m *UserOpenidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOpenidResponse.Unmarshal(m, b)
}
func (m *UserOpenidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOpenidResponse.Marshal(b, m, deterministic)
}
func (m *UserOpenidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOpenidResponse.Merge(m, src)
}
func (m *UserOpenidResponse) XXX_Size() int {
	return xxx_messageInfo_UserOpenidResponse.Size(m)
}
func (m *UserOpenidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOpenidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserOpenidResponse proto.InternalMessageInfo

func (m *UserOpenidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserOpenidResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserOpenidResponse) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

//创建平台数字藏品参数
type NftCreateRequest struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	ImageUrl             string   `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,4,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	Price                float32  `protobuf:"fixed32,5,opt,name=price,proto3" json:"price"`
	SeriesId             string   `protobuf:"bytes,6,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Detail               string   `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail"`
	DigitalType          int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftCreateRequest) Reset()         { *m = NftCreateRequest{} }
func (m *NftCreateRequest) String() string { return proto.CompactTextString(m) }
func (*NftCreateRequest) ProtoMessage()    {}
func (*NftCreateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{23}
}

func (m *NftCreateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftCreateRequest.Unmarshal(m, b)
}
func (m *NftCreateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftCreateRequest.Marshal(b, m, deterministic)
}
func (m *NftCreateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftCreateRequest.Merge(m, src)
}
func (m *NftCreateRequest) XXX_Size() int {
	return xxx_messageInfo_NftCreateRequest.Size(m)
}
func (m *NftCreateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftCreateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftCreateRequest proto.InternalMessageInfo

func (m *NftCreateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftCreateRequest) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *NftCreateRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftCreateRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftCreateRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftCreateRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftCreateRequest) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftCreateRequest) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

//平台数字藏品返回值
type NftResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftResponse) Reset()         { *m = NftResponse{} }
func (m *NftResponse) String() string { return proto.CompactTextString(m) }
func (*NftResponse) ProtoMessage()    {}
func (*NftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{24}
}

func (m *NftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftResponse.Unmarshal(m, b)
}
func (m *NftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftResponse.Marshal(b, m, deterministic)
}
func (m *NftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftResponse.Merge(m, src)
}
func (m *NftResponse) XXX_Size() int {
	return xxx_messageInfo_NftResponse.Size(m)
}
func (m *NftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftResponse proto.InternalMessageInfo

func (m *NftResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

//更新数字藏品信息
type NftUpdateRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,5,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	Price                float32  `protobuf:"fixed32,6,opt,name=price,proto3" json:"price"`
	Status               int32    `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	SeriesId             string   `protobuf:"bytes,8,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Detail               string   `protobuf:"bytes,9,opt,name=detail,proto3" json:"detail"`
	DigitalType          int32    `protobuf:"varint,10,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftUpdateRequest) Reset()         { *m = NftUpdateRequest{} }
func (m *NftUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*NftUpdateRequest) ProtoMessage()    {}
func (*NftUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{25}
}

func (m *NftUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftUpdateRequest.Unmarshal(m, b)
}
func (m *NftUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftUpdateRequest.Marshal(b, m, deterministic)
}
func (m *NftUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftUpdateRequest.Merge(m, src)
}
func (m *NftUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_NftUpdateRequest.Size(m)
}
func (m *NftUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftUpdateRequest proto.InternalMessageInfo

func (m *NftUpdateRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NftUpdateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftUpdateRequest) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *NftUpdateRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftUpdateRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftUpdateRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftUpdateRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NftUpdateRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftUpdateRequest) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftUpdateRequest) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

type NftDeleteRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftDeleteRequest) Reset()         { *m = NftDeleteRequest{} }
func (m *NftDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*NftDeleteRequest) ProtoMessage()    {}
func (*NftDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{26}
}

func (m *NftDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftDeleteRequest.Unmarshal(m, b)
}
func (m *NftDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftDeleteRequest.Marshal(b, m, deterministic)
}
func (m *NftDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftDeleteRequest.Merge(m, src)
}
func (m *NftDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_NftDeleteRequest.Size(m)
}
func (m *NftDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftDeleteRequest proto.InternalMessageInfo

func (m *NftDeleteRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type NftRuiPengListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftRuiPengListRequest) Reset()         { *m = NftRuiPengListRequest{} }
func (m *NftRuiPengListRequest) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengListRequest) ProtoMessage()    {}
func (*NftRuiPengListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{27}
}

func (m *NftRuiPengListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengListRequest.Unmarshal(m, b)
}
func (m *NftRuiPengListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengListRequest.Marshal(b, m, deterministic)
}
func (m *NftRuiPengListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengListRequest.Merge(m, src)
}
func (m *NftRuiPengListRequest) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengListRequest.Size(m)
}
func (m *NftRuiPengListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengListRequest proto.InternalMessageInfo

//瑞鹏数据藏品返回值
type NftRuiPengListResponse struct {
	Code                 int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*RuiPengList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *NftRuiPengListResponse) Reset()         { *m = NftRuiPengListResponse{} }
func (m *NftRuiPengListResponse) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengListResponse) ProtoMessage()    {}
func (*NftRuiPengListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{28}
}

func (m *NftRuiPengListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengListResponse.Unmarshal(m, b)
}
func (m *NftRuiPengListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengListResponse.Marshal(b, m, deterministic)
}
func (m *NftRuiPengListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengListResponse.Merge(m, src)
}
func (m *NftRuiPengListResponse) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengListResponse.Size(m)
}
func (m *NftRuiPengListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengListResponse proto.InternalMessageInfo

func (m *NftRuiPengListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftRuiPengListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftRuiPengListResponse) GetData() []*RuiPengList {
	if m != nil {
		return m.Data
	}
	return nil
}

//瑞鹏数据藏品列表数据展示
type RuiPengList struct {
	Name                  string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	ImageUrl              string   `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                  string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Price                 float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	SeriesId              string   `protobuf:"bytes,5,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	PublishIdentification string   `protobuf:"bytes,6,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	Detail                string   `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail"`
	DigitalType           int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *RuiPengList) Reset()         { *m = RuiPengList{} }
func (m *RuiPengList) String() string { return proto.CompactTextString(m) }
func (*RuiPengList) ProtoMessage()    {}
func (*RuiPengList) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{29}
}

func (m *RuiPengList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuiPengList.Unmarshal(m, b)
}
func (m *RuiPengList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuiPengList.Marshal(b, m, deterministic)
}
func (m *RuiPengList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuiPengList.Merge(m, src)
}
func (m *RuiPengList) XXX_Size() int {
	return xxx_messageInfo_RuiPengList.Size(m)
}
func (m *RuiPengList) XXX_DiscardUnknown() {
	xxx_messageInfo_RuiPengList.DiscardUnknown(m)
}

var xxx_messageInfo_RuiPengList proto.InternalMessageInfo

func (m *RuiPengList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RuiPengList) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *RuiPengList) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *RuiPengList) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *RuiPengList) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *RuiPengList) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *RuiPengList) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *RuiPengList) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

//购买nft入惨
type UserBuyNftRequest struct {
	UserIdentification    string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	PublishIdentification string   `protobuf:"bytes,2,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	SeriesId              string   `protobuf:"bytes,3,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Price                 float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	SellType              int32    `protobuf:"varint,5,opt,name=sell_type,json=sellType,proto3" json:"sell_type"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserBuyNftRequest) Reset()         { *m = UserBuyNftRequest{} }
func (m *UserBuyNftRequest) String() string { return proto.CompactTextString(m) }
func (*UserBuyNftRequest) ProtoMessage()    {}
func (*UserBuyNftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{30}
}

func (m *UserBuyNftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBuyNftRequest.Unmarshal(m, b)
}
func (m *UserBuyNftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBuyNftRequest.Marshal(b, m, deterministic)
}
func (m *UserBuyNftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBuyNftRequest.Merge(m, src)
}
func (m *UserBuyNftRequest) XXX_Size() int {
	return xxx_messageInfo_UserBuyNftRequest.Size(m)
}
func (m *UserBuyNftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBuyNftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserBuyNftRequest proto.InternalMessageInfo

func (m *UserBuyNftRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *UserBuyNftRequest) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *UserBuyNftRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *UserBuyNftRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UserBuyNftRequest) GetSellType() int32 {
	if m != nil {
		return m.SellType
	}
	return 0
}

//购买nft返回值
type UserBuyNftResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBuyNftResponse) Reset()         { *m = UserBuyNftResponse{} }
func (m *UserBuyNftResponse) String() string { return proto.CompactTextString(m) }
func (*UserBuyNftResponse) ProtoMessage()    {}
func (*UserBuyNftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{31}
}

func (m *UserBuyNftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBuyNftResponse.Unmarshal(m, b)
}
func (m *UserBuyNftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBuyNftResponse.Marshal(b, m, deterministic)
}
func (m *UserBuyNftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBuyNftResponse.Merge(m, src)
}
func (m *UserBuyNftResponse) XXX_Size() int {
	return xxx_messageInfo_UserBuyNftResponse.Size(m)
}
func (m *UserBuyNftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBuyNftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserBuyNftResponse proto.InternalMessageInfo

func (m *UserBuyNftResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserBuyNftResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserBuyNftResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//瑞鹏平台详情页参数
type NftRuiPengDetailRequest struct {
	SeriesId             string   `protobuf:"bytes,1,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftRuiPengDetailRequest) Reset()         { *m = NftRuiPengDetailRequest{} }
func (m *NftRuiPengDetailRequest) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengDetailRequest) ProtoMessage()    {}
func (*NftRuiPengDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{32}
}

func (m *NftRuiPengDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengDetailRequest.Unmarshal(m, b)
}
func (m *NftRuiPengDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengDetailRequest.Marshal(b, m, deterministic)
}
func (m *NftRuiPengDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengDetailRequest.Merge(m, src)
}
func (m *NftRuiPengDetailRequest) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengDetailRequest.Size(m)
}
func (m *NftRuiPengDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengDetailRequest proto.InternalMessageInfo

func (m *NftRuiPengDetailRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftRuiPengDetailRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftDetail struct {
	Detail                string   `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail"`
	Price                 float32  `protobuf:"fixed32,2,opt,name=price,proto3" json:"price"`
	PublishIdentification string   `protobuf:"bytes,3,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	SeriesId              string   `protobuf:"bytes,4,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Flag                  int32    `protobuf:"varint,5,opt,name=flag,proto3" json:"flag"`
	Name                  string   `protobuf:"bytes,6,opt,name=name,proto3" json:"name"`
	Desc                  string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc"`
	DigitalType           int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *NftDetail) Reset()         { *m = NftDetail{} }
func (m *NftDetail) String() string { return proto.CompactTextString(m) }
func (*NftDetail) ProtoMessage()    {}
func (*NftDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{33}
}

func (m *NftDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftDetail.Unmarshal(m, b)
}
func (m *NftDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftDetail.Marshal(b, m, deterministic)
}
func (m *NftDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftDetail.Merge(m, src)
}
func (m *NftDetail) XXX_Size() int {
	return xxx_messageInfo_NftDetail.Size(m)
}
func (m *NftDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_NftDetail.DiscardUnknown(m)
}

var xxx_messageInfo_NftDetail proto.InternalMessageInfo

func (m *NftDetail) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftDetail) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftDetail) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *NftDetail) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftDetail) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

func (m *NftDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftDetail) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

//瑞鹏平台详情页返回值
type NftRuiPengDetailResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *NftDetail `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NftRuiPengDetailResponse) Reset()         { *m = NftRuiPengDetailResponse{} }
func (m *NftRuiPengDetailResponse) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengDetailResponse) ProtoMessage()    {}
func (*NftRuiPengDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{34}
}

func (m *NftRuiPengDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengDetailResponse.Unmarshal(m, b)
}
func (m *NftRuiPengDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengDetailResponse.Marshal(b, m, deterministic)
}
func (m *NftRuiPengDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengDetailResponse.Merge(m, src)
}
func (m *NftRuiPengDetailResponse) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengDetailResponse.Size(m)
}
func (m *NftRuiPengDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengDetailResponse proto.InternalMessageInfo

func (m *NftRuiPengDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftRuiPengDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftRuiPengDetailResponse) GetData() *NftDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

//数字藏品订单支付参数
type UserNftPayRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNftPayRequest) Reset()         { *m = UserNftPayRequest{} }
func (m *UserNftPayRequest) String() string { return proto.CompactTextString(m) }
func (*UserNftPayRequest) ProtoMessage()    {}
func (*UserNftPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{35}
}

func (m *UserNftPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNftPayRequest.Unmarshal(m, b)
}
func (m *UserNftPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNftPayRequest.Marshal(b, m, deterministic)
}
func (m *UserNftPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNftPayRequest.Merge(m, src)
}
func (m *UserNftPayRequest) XXX_Size() int {
	return xxx_messageInfo_UserNftPayRequest.Size(m)
}
func (m *UserNftPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNftPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserNftPayRequest proto.InternalMessageInfo

func (m *UserNftPayRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *UserNftPayRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//数字藏品订单支付返回值
type UserNftPayResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNftPayResponse) Reset()         { *m = UserNftPayResponse{} }
func (m *UserNftPayResponse) String() string { return proto.CompactTextString(m) }
func (*UserNftPayResponse) ProtoMessage()    {}
func (*UserNftPayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{36}
}

func (m *UserNftPayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNftPayResponse.Unmarshal(m, b)
}
func (m *UserNftPayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNftPayResponse.Marshal(b, m, deterministic)
}
func (m *UserNftPayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNftPayResponse.Merge(m, src)
}
func (m *UserNftPayResponse) XXX_Size() int {
	return xxx_messageInfo_UserNftPayResponse.Size(m)
}
func (m *UserNftPayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNftPayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserNftPayResponse proto.InternalMessageInfo

func (m *UserNftPayResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserNftPayResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserNftPayResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//数字藏品购买领取
type NftPayResultPickRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	NftId                string   `protobuf:"bytes,2,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	Price                float32  `protobuf:"fixed32,3,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPayResultPickRequest) Reset()         { *m = NftPayResultPickRequest{} }
func (m *NftPayResultPickRequest) String() string { return proto.CompactTextString(m) }
func (*NftPayResultPickRequest) ProtoMessage()    {}
func (*NftPayResultPickRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{37}
}

func (m *NftPayResultPickRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPayResultPickRequest.Unmarshal(m, b)
}
func (m *NftPayResultPickRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPayResultPickRequest.Marshal(b, m, deterministic)
}
func (m *NftPayResultPickRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPayResultPickRequest.Merge(m, src)
}
func (m *NftPayResultPickRequest) XXX_Size() int {
	return xxx_messageInfo_NftPayResultPickRequest.Size(m)
}
func (m *NftPayResultPickRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPayResultPickRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPayResultPickRequest proto.InternalMessageInfo

func (m *NftPayResultPickRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftPayResultPickRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftPayResultPickRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

//获取auth openid
type UserAuthOpenidRequest struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAuthOpenidRequest) Reset()         { *m = UserAuthOpenidRequest{} }
func (m *UserAuthOpenidRequest) String() string { return proto.CompactTextString(m) }
func (*UserAuthOpenidRequest) ProtoMessage()    {}
func (*UserAuthOpenidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{38}
}

func (m *UserAuthOpenidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthOpenidRequest.Unmarshal(m, b)
}
func (m *UserAuthOpenidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthOpenidRequest.Marshal(b, m, deterministic)
}
func (m *UserAuthOpenidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthOpenidRequest.Merge(m, src)
}
func (m *UserAuthOpenidRequest) XXX_Size() int {
	return xxx_messageInfo_UserAuthOpenidRequest.Size(m)
}
func (m *UserAuthOpenidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthOpenidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthOpenidRequest proto.InternalMessageInfo

func (m *UserAuthOpenidRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

//数字藏品订单支付返回值
type UserAuthOpenidResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAuthOpenidResponse) Reset()         { *m = UserAuthOpenidResponse{} }
func (m *UserAuthOpenidResponse) String() string { return proto.CompactTextString(m) }
func (*UserAuthOpenidResponse) ProtoMessage()    {}
func (*UserAuthOpenidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{39}
}

func (m *UserAuthOpenidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthOpenidResponse.Unmarshal(m, b)
}
func (m *UserAuthOpenidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthOpenidResponse.Marshal(b, m, deterministic)
}
func (m *UserAuthOpenidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthOpenidResponse.Merge(m, src)
}
func (m *UserAuthOpenidResponse) XXX_Size() int {
	return xxx_messageInfo_UserAuthOpenidResponse.Size(m)
}
func (m *UserAuthOpenidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthOpenidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthOpenidResponse proto.InternalMessageInfo

func (m *UserAuthOpenidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserAuthOpenidResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserAuthOpenidResponse) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

//用户转换参数
type ChangeUserIdRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeUserIdRequest) Reset()         { *m = ChangeUserIdRequest{} }
func (m *ChangeUserIdRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeUserIdRequest) ProtoMessage()    {}
func (*ChangeUserIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{40}
}

func (m *ChangeUserIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserIdRequest.Unmarshal(m, b)
}
func (m *ChangeUserIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserIdRequest.Marshal(b, m, deterministic)
}
func (m *ChangeUserIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserIdRequest.Merge(m, src)
}
func (m *ChangeUserIdRequest) XXX_Size() int {
	return xxx_messageInfo_ChangeUserIdRequest.Size(m)
}
func (m *ChangeUserIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserIdRequest proto.InternalMessageInfo

func (m *ChangeUserIdRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

//用户转换返回值
type ChangeUserIdResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	UserIdentification   string   `protobuf:"bytes,3,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeUserIdResponse) Reset()         { *m = ChangeUserIdResponse{} }
func (m *ChangeUserIdResponse) String() string { return proto.CompactTextString(m) }
func (*ChangeUserIdResponse) ProtoMessage()    {}
func (*ChangeUserIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e6263347b0672d28, []int{41}
}

func (m *ChangeUserIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserIdResponse.Unmarshal(m, b)
}
func (m *ChangeUserIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserIdResponse.Marshal(b, m, deterministic)
}
func (m *ChangeUserIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserIdResponse.Merge(m, src)
}
func (m *ChangeUserIdResponse) XXX_Size() int {
	return xxx_messageInfo_ChangeUserIdResponse.Size(m)
}
func (m *ChangeUserIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserIdResponse proto.InternalMessageInfo

func (m *ChangeUserIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChangeUserIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChangeUserIdResponse) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func init() {
	proto.RegisterType((*RealRegisterRequest)(nil), "ctc.RealRegisterRequest")
	proto.RegisterType((*RealRegisterResponse)(nil), "ctc.RealRegisterResponse")
	proto.RegisterType((*DigitalImageUploadRequest)(nil), "ctc.DigitalImageUploadRequest")
	proto.RegisterType((*DigitalImageUploadResponse)(nil), "ctc.DigitalImageUploadResponse")
	proto.RegisterType((*NftPublishDigitalRequest)(nil), "ctc.NftPublishDigitalRequest")
	proto.RegisterType((*NftPublishDigitalResponse)(nil), "ctc.NftPublishDigitalResponse")
	proto.RegisterType((*NftSeriesClaimRequest)(nil), "ctc.NftSeriesClaimRequest")
	proto.RegisterType((*NftSeriesClaimResponse)(nil), "ctc.NftSeriesClaimResponse")
	proto.RegisterType((*NftSeriesListRequest)(nil), "ctc.NftSeriesListRequest")
	proto.RegisterType((*NftInfo)(nil), "ctc.NftInfo")
	proto.RegisterType((*NftSeriesListResponse)(nil), "ctc.NftSeriesListResponse")
	proto.RegisterType((*NftSearchResultRequest)(nil), "ctc.NftSearchResultRequest")
	proto.RegisterType((*NftSearchResultResponse)(nil), "ctc.NftSearchResultResponse")
	proto.RegisterType((*NftPointQueryRequest)(nil), "ctc.NftPointQueryRequest")
	proto.RegisterType((*NftPointQueryResponse)(nil), "ctc.NftPointQueryResponse")
	proto.RegisterType((*NftInfoRequest)(nil), "ctc.NftInfoRequest")
	proto.RegisterType((*NftInfoResponse)(nil), "ctc.NftInfoResponse")
	proto.RegisterType((*NftBuyRequest)(nil), "ctc.NftBuyRequest")
	proto.RegisterType((*NftBuyResponse)(nil), "ctc.NftBuyResponse")
	proto.RegisterType((*NftStatusRequest)(nil), "ctc.NftStatusRequest")
	proto.RegisterType((*NftStatusResponse)(nil), "ctc.NftStatusResponse")
	proto.RegisterType((*UserOpenidRequest)(nil), "ctc.UserOpenidRequest")
	proto.RegisterType((*UserOpenidResponse)(nil), "ctc.UserOpenidResponse")
	proto.RegisterType((*NftCreateRequest)(nil), "ctc.NftCreateRequest")
	proto.RegisterType((*NftResponse)(nil), "ctc.NftResponse")
	proto.RegisterType((*NftUpdateRequest)(nil), "ctc.NftUpdateRequest")
	proto.RegisterType((*NftDeleteRequest)(nil), "ctc.NftDeleteRequest")
	proto.RegisterType((*NftRuiPengListRequest)(nil), "ctc.NftRuiPengListRequest")
	proto.RegisterType((*NftRuiPengListResponse)(nil), "ctc.NftRuiPengListResponse")
	proto.RegisterType((*RuiPengList)(nil), "ctc.RuiPengList")
	proto.RegisterType((*UserBuyNftRequest)(nil), "ctc.UserBuyNftRequest")
	proto.RegisterType((*UserBuyNftResponse)(nil), "ctc.UserBuyNftResponse")
	proto.RegisterType((*NftRuiPengDetailRequest)(nil), "ctc.NftRuiPengDetailRequest")
	proto.RegisterType((*NftDetail)(nil), "ctc.NftDetail")
	proto.RegisterType((*NftRuiPengDetailResponse)(nil), "ctc.NftRuiPengDetailResponse")
	proto.RegisterType((*UserNftPayRequest)(nil), "ctc.UserNftPayRequest")
	proto.RegisterType((*UserNftPayResponse)(nil), "ctc.UserNftPayResponse")
	proto.RegisterType((*NftPayResultPickRequest)(nil), "ctc.NftPayResultPickRequest")
	proto.RegisterType((*UserAuthOpenidRequest)(nil), "ctc.UserAuthOpenidRequest")
	proto.RegisterType((*UserAuthOpenidResponse)(nil), "ctc.UserAuthOpenidResponse")
	proto.RegisterType((*ChangeUserIdRequest)(nil), "ctc.ChangeUserIdRequest")
	proto.RegisterType((*ChangeUserIdResponse)(nil), "ctc.ChangeUserIdResponse")
}

func init() { proto.RegisterFile("ctc/digital.proto", fileDescriptor_e6263347b0672d28) }

var fileDescriptor_e6263347b0672d28 = []byte{
	// 1797 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcd, 0x6e, 0xdc, 0x46,
	0x12, 0x06, 0xe7, 0x7f, 0x6a, 0x24, 0x59, 0x6e, 0x49, 0xa3, 0xd1, 0xf8, 0x9f, 0xbb, 0x58, 0x1b,
	0xd8, 0x85, 0x8c, 0xf5, 0xee, 0x62, 0x81, 0x04, 0x39, 0x44, 0x12, 0x12, 0x0c, 0x60, 0xc8, 0xca,
	0xc8, 0x4a, 0x00, 0x1b, 0xc9, 0x80, 0x22, 0x9b, 0x33, 0x84, 0x39, 0xe4, 0x84, 0x6c, 0x3a, 0xd1,
	0x3d, 0x4f, 0x90, 0x5b, 0x2e, 0x06, 0x72, 0xc8, 0x53, 0xe4, 0x11, 0xf2, 0x02, 0x79, 0x88, 0x9c,
	0xe3, 0x6b, 0xd0, 0xd5, 0x4d, 0xb2, 0xf9, 0x37, 0x71, 0x18, 0x21, 0xb7, 0xe9, 0x6a, 0x76, 0x77,
	0xd5, 0x57, 0xf5, 0x75, 0x55, 0xf5, 0xc0, 0x4d, 0x93, 0x99, 0x8f, 0x2d, 0x67, 0xee, 0x30, 0xc3,
	0x3d, 0x5c, 0x05, 0x3e, 0xf3, 0x49, 0xd3, 0x64, 0xa6, 0xfe, 0xbd, 0x06, 0x3b, 0x53, 0x6a, 0xb8,
	0x53, 0x3a, 0x77, 0x42, 0x46, 0x83, 0x29, 0xfd, 0x32, 0xa2, 0x21, 0x23, 0xf7, 0x60, 0xb0, 0xa2,
	0x41, 0xe8, 0x7b, 0x33, 0xcf, 0x58, 0xd2, 0x91, 0x76, 0x5f, 0x7b, 0xd4, 0x9f, 0x82, 0x10, 0x9d,
	0x1a, 0x4b, 0x4a, 0x86, 0xd0, 0x59, 0xfa, 0x97, 0x8e, 0x4b, 0x47, 0x0d, 0x9c, 0x93, 0x23, 0xb2,
	0x0f, 0x5d, 0xc7, 0x9a, 0x99, 0x46, 0x60, 0x8d, 0x9a, 0x62, 0xc2, 0xb1, 0x8e, 0x8d, 0xc0, 0xe2,
	0x0b, 0xfc, 0x15, 0xf5, 0x1c, 0x6b, 0xd4, 0x12, 0x72, 0x31, 0x22, 0xf7, 0x61, 0x23, 0x34, 0x83,
	0xe5, 0x2c, 0x0a, 0x69, 0x30, 0x73, 0xac, 0x51, 0x5b, 0x1c, 0xc5, 0x65, 0x17, 0x21, 0x0d, 0x26,
	0x96, 0x1e, 0xc1, 0x6e, 0x56, 0xc5, 0x70, 0xe5, 0x7b, 0x21, 0x25, 0x04, 0x5a, 0xa6, 0x6f, 0x09,
	0xe5, 0xda, 0x53, 0xfc, 0x4d, 0x46, 0xd0, 0x5d, 0xd2, 0x30, 0x34, 0xe6, 0xb1, 0x5e, 0xf1, 0x90,
	0x3c, 0x86, 0x1d, 0x79, 0x04, 0xf5, 0x98, 0x63, 0x3b, 0xa6, 0xc1, 0x1c, 0xdf, 0x93, 0x4a, 0x92,
	0x08, 0x8f, 0x52, 0x67, 0x74, 0x07, 0x0e, 0x4e, 0x04, 0x60, 0x93, 0xa5, 0x31, 0xa7, 0x17, 0x2b,
	0xd7, 0x37, 0xac, 0x18, 0x9f, 0x5b, 0xd0, 0xb7, 0x1d, 0x97, 0xce, 0x56, 0x06, 0x5b, 0x48, 0x74,
	0x7a, 0x5c, 0x70, 0x66, 0xb0, 0x45, 0xd5, 0x51, 0x8d, 0xca, 0xa3, 0xe6, 0x30, 0x2e, 0x3b, 0xaa,
	0x96, 0x9d, 0xb7, 0xa0, 0xef, 0xf0, 0x4d, 0x66, 0x51, 0xe0, 0x4a, 0xeb, 0x7a, 0x28, 0xb8, 0x08,
	0x5c, 0xfd, 0x87, 0x26, 0x8c, 0x4e, 0x6d, 0x76, 0x16, 0x5d, 0xba, 0x4e, 0xb8, 0x90, 0x67, 0xc6,
	0x36, 0x0d, 0xa1, 0x63, 0x44, 0x6c, 0xe1, 0x07, 0xd2, 0x20, 0x39, 0xe2, 0xe7, 0x63, 0x10, 0x88,
	0x83, 0xf0, 0x37, 0xd9, 0x86, 0x66, 0xba, 0x3f, 0xff, 0xc9, 0x23, 0xc6, 0x72, 0xc2, 0x95, 0x6b,
	0x5c, 0xe1, 0xc9, 0xc2, 0xc9, 0x20, 0x45, 0x17, 0x81, 0xcb, 0xb7, 0xb1, 0x68, 0x68, 0x4a, 0x07,
	0xe3, 0x6f, 0x2e, 0xb3, 0x5d, 0x63, 0x3e, 0xea, 0x08, 0x19, 0xff, 0x5d, 0x85, 0x5e, 0xb7, 0x0a,
	0x3d, 0xf2, 0x37, 0xd8, 0x5c, 0x09, 0x83, 0x66, 0xa6, 0x1f, 0x79, 0x6c, 0xd4, 0x43, 0xa0, 0x36,
	0xa4, 0xf0, 0x98, 0xcb, 0x38, 0x2c, 0x21, 0x0d, 0x1c, 0x1a, 0xf2, 0x18, 0xeb, 0x0b, 0x58, 0x84,
	0x60, 0x62, 0x91, 0x7f, 0x01, 0x91, 0x93, 0x97, 0x74, 0xee, 0x78, 0x33, 0xc7, 0xb3, 0xe8, 0xd7,
	0x23, 0xc0, 0x6d, 0xb6, 0xc5, 0xcc, 0x11, 0x9f, 0x98, 0x70, 0x39, 0xb7, 0x34, 0xa4, 0xae, 0x3b,
	0x0b, 0x99, 0xc1, 0xa2, 0x70, 0x34, 0xc0, 0xcf, 0x80, 0x8b, 0xce, 0x51, 0x42, 0xee, 0x00, 0x8e,
	0xa4, 0x36, 0x1b, 0x38, 0xdf, 0xe7, 0x12, 0xa1, 0xca, 0x03, 0xd8, 0x58, 0x19, 0xe6, 0x2b, 0xee,
	0x23, 0x76, 0xb5, 0xa2, 0xa3, 0x4d, 0xd4, 0x66, 0x20, 0x65, 0xcf, 0xaf, 0x56, 0x54, 0xbf, 0x84,
	0x83, 0x12, 0x37, 0xd5, 0x8a, 0x87, 0x7d, 0xe8, 0x32, 0x23, 0x7c, 0xc5, 0xcd, 0x96, 0x84, 0xe4,
	0xc3, 0x89, 0xa5, 0xff, 0xa8, 0xc1, 0xde, 0xa9, 0xcd, 0xce, 0xd1, 0xbc, 0x63, 0xd7, 0x70, 0x96,
	0x0a, 0xf9, 0x25, 0x1c, 0x2a, 0xf9, 0x85, 0x08, 0xc9, 0x7f, 0x0f, 0x06, 0xcc, 0x67, 0x46, 0x6c,
	0x61, 0x43, 0x20, 0x80, 0xa2, 0x04, 0x6d, 0xd3, 0x7f, 0x4d, 0x03, 0x35, 0x08, 0x51, 0xa0, 0x06,
	0x42, 0x4b, 0x09, 0x84, 0x0a, 0xa7, 0xb7, 0x2b, 0x29, 0x33, 0x83, 0x61, 0x5e, 0xf9, 0xeb, 0x85,
	0xe7, 0x5b, 0x0d, 0x76, 0x93, 0x13, 0x9e, 0x3a, 0x21, 0x53, 0x68, 0xe2, 0xdb, 0x76, 0x48, 0x19,
	0x9e, 0xd0, 0x9c, 0xca, 0x11, 0xd9, 0x85, 0xb6, 0xeb, 0x2c, 0x1d, 0x01, 0x47, 0x73, 0x2a, 0x06,
	0xd9, 0xb8, 0x6b, 0xe6, 0xe2, 0xae, 0xc2, 0xea, 0x56, 0xa5, 0xd5, 0x6f, 0x1b, 0xd0, 0x3d, 0xb5,
	0xd9, 0xc4, 0xb3, 0x7d, 0xb2, 0x07, 0x1d, 0xcf, 0x66, 0x7c, 0x5b, 0xe1, 0xa0, 0xb6, 0x67, 0xb3,
	0x89, 0x95, 0x50, 0xaa, 0xa1, 0x50, 0x2a, 0x65, 0x76, 0xb3, 0x94, 0xd9, 0xad, 0x22, 0xb3, 0xdb,
	0x95, 0xcc, 0xee, 0x54, 0x32, 0xbb, 0xab, 0x38, 0x34, 0x17, 0x43, 0xbd, 0x42, 0x0c, 0xad, 0x25,
	0x64, 0x8e, 0x62, 0x50, 0xa0, 0xd8, 0x23, 0x90, 0xbc, 0x9c, 0x89, 0x40, 0xf4, 0xa2, 0xa5, 0x24,
	0xe2, 0x96, 0x90, 0x3f, 0xe7, 0xe2, 0xd3, 0x68, 0x49, 0xfe, 0x01, 0x37, 0xe2, 0x73, 0x38, 0x7b,
	0xf9, 0x69, 0x82, 0x91, 0x9b, 0xf2, 0x34, 0x2e, 0x9d, 0x58, 0x39, 0xd2, 0x6e, 0xe6, 0x48, 0xab,
	0x7f, 0xa3, 0xb2, 0x45, 0x84, 0x43, 0xad, 0x78, 0xdb, 0x85, 0x36, 0x6a, 0x8c, 0x9e, 0x68, 0x4f,
	0xc5, 0x80, 0x3c, 0x84, 0x1e, 0xfa, 0xd2, 0xb3, 0xfd, 0x51, 0xeb, 0x7e, 0xf3, 0xd1, 0xe0, 0xc9,
	0xc6, 0xa1, 0xc9, 0xcc, 0x43, 0xe9, 0xeb, 0x69, 0xd7, 0x13, 0x3f, 0xf4, 0x89, 0x0c, 0x7b, 0x23,
	0x30, 0x17, 0x53, 0x1a, 0x46, 0x6e, 0x12, 0x96, 0x4a, 0x20, 0x6b, 0x6a, 0x20, 0x63, 0xa6, 0xa6,
	0x6c, 0xe1, 0x5b, 0x49, 0xa6, 0xc6, 0x91, 0xfe, 0x12, 0xf6, 0x0b, 0x5b, 0xd5, 0x32, 0x89, 0xbb,
	0xdf, 0x60, 0x86, 0x8c, 0x2d, 0xfc, 0xad, 0x7f, 0x8c, 0xe4, 0x39, 0xf3, 0x1d, 0x8f, 0x7d, 0x12,
	0xd1, 0xe0, 0x2a, 0xd6, 0xb2, 0x22, 0xe2, 0xb5, 0xca, 0x88, 0x7f, 0x89, 0xb0, 0xab, 0x1b, 0xd5,
	0x85, 0x5d, 0x38, 0x56, 0xc2, 0x8e, 0x03, 0xfd, 0x21, 0x6c, 0xc5, 0x08, 0x4b, 0xfd, 0xca, 0x49,
	0xa5, 0x2f, 0xe0, 0x46, 0xf2, 0x61, 0xad, 0xf3, 0x55, 0x07, 0x73, 0x15, 0x2a, 0x1d, 0xfc, 0x9d,
	0x06, 0x9b, 0xa7, 0x36, 0x3b, 0x8a, 0xae, 0xd6, 0xab, 0xf4, 0x87, 0x8b, 0x0c, 0xce, 0x29, 0x63,
	0xb5, 0x72, 0xaf, 0x66, 0xa1, 0xe9, 0x07, 0x54, 0x02, 0x01, 0x28, 0x3a, 0xe7, 0x12, 0xfe, 0x81,
	0x6f, 0xdb, 0x34, 0x90, 0x14, 0x68, 0x89, 0x0f, 0x50, 0x24, 0x38, 0xf0, 0x19, 0xc2, 0x85, 0xaa,
	0x5d, 0xef, 0x5d, 0xfb, 0x46, 0x83, 0x6d, 0x1e, 0x8b, 0xc8, 0xed, 0xeb, 0xb6, 0xfb, 0x01, 0x6c,
	0xb0, 0xc0, 0xf0, 0xc2, 0xf8, 0x32, 0x11, 0x86, 0x0f, 0x50, 0x26, 0x6f, 0x13, 0x9e, 0xcf, 0xf0,
	0x93, 0x55, 0xe0, 0x98, 0x34, 0xb6, 0x1c, 0x45, 0x67, 0x5c, 0xa2, 0xbf, 0x80, 0x9b, 0x8a, 0x7e,
	0xd7, 0x6b, 0xfc, 0x09, 0xdc, 0xe4, 0x85, 0xee, 0x33, 0x2c, 0x87, 0x6b, 0xf3, 0xe4, 0x39, 0x10,
	0x75, 0x97, 0x5a, 0x2a, 0x16, 0x8a, 0x3a, 0xfd, 0x57, 0xe1, 0x98, 0xe3, 0x80, 0x1a, 0x8c, 0xc6,
	0xba, 0xc5, 0x59, 0x43, 0x53, 0xb2, 0x46, 0xa6, 0xea, 0x6c, 0x64, 0xab, 0xce, 0x24, 0x3f, 0x34,
	0x7f, 0x3f, 0xe1, 0x57, 0xa6, 0x3e, 0xce, 0x60, 0xe1, 0x1d, 0x9e, 0x99, 0x1a, 0x53, 0x31, 0xc8,
	0x66, 0x91, 0x4e, 0x2e, 0x8b, 0x0c, 0xa1, 0x63, 0x51, 0x66, 0x38, 0xae, 0xcc, 0x4c, 0x72, 0xc4,
	0x23, 0x42, 0xb6, 0x42, 0xa2, 0x00, 0x13, 0xf5, 0xe2, 0x40, 0xca, 0xb0, 0x00, 0x7b, 0x1f, 0x06,
	0xa7, 0x76, 0xcd, 0x0b, 0x51, 0x7f, 0xd3, 0x40, 0xd4, 0x2e, 0x56, 0x96, 0x82, 0xda, 0x16, 0x34,
	0x64, 0x28, 0xb7, 0xa7, 0x0d, 0xc7, 0x2a, 0xad, 0xaa, 0xd7, 0xd5, 0xee, 0xd7, 0x52, 0x36, 0xa5,
	0x28, 0x76, 0x54, 0x14, 0x87, 0xd0, 0x91, 0xe4, 0xe8, 0xa2, 0x7e, 0x72, 0x94, 0x45, 0xb7, 0x57,
	0x89, 0x6e, 0x7f, 0x2d, 0xba, 0x50, 0x44, 0x57, 0x47, 0x7c, 0x4e, 0xa8, 0x4b, 0x2b, 0xf1, 0xd1,
	0xf7, 0xf1, 0xe2, 0x9f, 0x46, 0xce, 0x19, 0xf5, 0xe6, 0x4a, 0xfd, 0xa5, 0xbb, 0x98, 0x02, 0x33,
	0x13, 0xb5, 0xa2, 0xfd, 0xef, 0x49, 0xda, 0xe2, 0xf9, 0x76, 0x1b, 0xaf, 0x63, 0x75, 0x57, 0x91,
	0xc8, 0xde, 0x6a, 0x30, 0x50, 0xa4, 0xd7, 0x13, 0xfc, 0x89, 0x17, 0x5a, 0x95, 0xb1, 0xdc, 0xce,
	0xa1, 0xfd, 0x3f, 0x18, 0xc6, 0x4d, 0x4e, 0xce, 0xd9, 0x22, 0xea, 0xf7, 0xe4, 0x6c, 0xce, 0xdf,
	0x7f, 0x82, 0x02, 0x3f, 0x69, 0xe2, 0x62, 0x3a, 0x8a, 0xae, 0x90, 0x0a, 0xf5, 0x2e, 0xa6, 0x35,
	0x8a, 0x37, 0xd6, 0x29, 0xbe, 0xb6, 0x6e, 0x5e, 0x83, 0x9f, 0x2b, 0x0d, 0x6a, 0xa3, 0x41, 0x3d,
	0x2e, 0x40, 0x6b, 0x3e, 0x17, 0xf7, 0x63, 0x6c, 0x4c, 0xad, 0x88, 0x39, 0x80, 0x9e, 0x1f, 0x58,
	0x34, 0x98, 0x85, 0xf1, 0xbb, 0x41, 0x17, 0xc7, 0xe7, 0xbc, 0x83, 0xdf, 0x4f, 0x83, 0xf2, 0x04,
	0x31, 0x56, 0x9e, 0x0a, 0x52, 0x4b, 0xb4, 0x77, 0xeb, 0x00, 0xaa, 0x9f, 0x0a, 0x7e, 0xd1, 0xa0,
	0x8f, 0xdc, 0x41, 0x37, 0xa6, 0xee, 0xd5, 0x32, 0xee, 0x4d, 0x00, 0x6a, 0xa8, 0x00, 0x55, 0xbb,
	0xa2, 0xf9, 0xce, 0xae, 0x68, 0xe5, 0x0c, 0x88, 0xdb, 0x0d, 0x81, 0xb7, 0x68, 0x37, 0x62, 0x8e,
	0x74, 0x14, 0x8e, 0x94, 0xf5, 0x08, 0xef, 0x10, 0x84, 0x2e, 0xbe, 0x57, 0xe4, 0x70, 0xad, 0xe5,
	0x3c, 0x5d, 0xa9, 0x52, 0x07, 0x4f, 0xb6, 0xe2, 0xea, 0x4b, 0xee, 0x29, 0xc8, 0x3e, 0x13, 0x11,
	0xcf, 0x0b, 0x4e, 0xa3, 0x76, 0xc9, 0x9a, 0x09, 0x93, 0x46, 0x36, 0x4c, 0x3e, 0x15, 0x51, 0x18,
	0x1f, 0x70, 0x6d, 0xe5, 0xf6, 0x57, 0x18, 0x7e, 0x62, 0xcf, 0xc8, 0x65, 0x67, 0x8e, 0xf9, 0xaa,
	0xb6, 0xfa, 0x69, 0xdd, 0xd5, 0x50, 0xeb, 0xae, 0x24, 0xa4, 0x9a, 0x4a, 0x48, 0xe9, 0xff, 0x84,
	0x3d, 0x6e, 0xd0, 0x87, 0x11, 0x5b, 0x64, 0x0b, 0x18, 0xd5, 0xa6, 0xbe, 0xb0, 0x49, 0xff, 0x02,
	0x86, 0xf9, 0x8f, 0x6b, 0x21, 0x90, 0x3e, 0x25, 0x36, 0xd5, 0xa7, 0x44, 0xfd, 0xff, 0xb0, 0x73,
	0xbc, 0x30, 0xbc, 0x39, 0x15, 0x0f, 0x87, 0xb1, 0x2a, 0xf9, 0x17, 0x46, 0xad, 0xec, 0x85, 0x31,
	0xbb, 0xf0, 0x2f, 0x79, 0x61, 0x7c, 0xf2, 0x33, 0xc0, 0x96, 0x7c, 0xdc, 0x39, 0xa7, 0xc1, 0x6b,
	0x4e, 0xd1, 0x63, 0xd8, 0x50, 0xdf, 0x3a, 0xc9, 0x48, 0xa4, 0xa5, 0xe2, 0x0b, 0xed, 0xf8, 0xa0,
	0x64, 0x46, 0xaa, 0x7d, 0x01, 0xa4, 0xf8, 0x9c, 0x48, 0xee, 0xe2, 0x82, 0xca, 0x27, 0xcd, 0xf1,
	0xbd, 0xca, 0x79, 0xb9, 0xed, 0x14, 0x8b, 0xe0, 0xec, 0xa3, 0x14, 0xb9, 0x13, 0x13, 0xa9, 0xf4,
	0x4d, 0x71, 0x7c, 0xb7, 0x6a, 0x5a, 0xee, 0x39, 0xc1, 0x96, 0x42, 0x79, 0xc6, 0x21, 0xe3, 0x78,
	0x45, 0xf1, 0x61, 0x6a, 0x7c, 0xab, 0x74, 0x4e, 0x6e, 0xf5, 0x11, 0x36, 0x4e, 0x69, 0x83, 0x4e,
	0x0e, 0xb2, 0x5f, 0x2b, 0x35, 0xc4, 0x78, 0x5c, 0x36, 0x25, 0xf7, 0xf9, 0x6f, 0xfa, 0xc4, 0xb2,
	0x93, 0xe9, 0xd1, 0xe4, 0xda, 0xdd, 0xac, 0x50, 0xae, 0x7a, 0x8a, 0x1d, 0xa2, 0xda, 0x4d, 0x13,
	0x45, 0xdb, 0x42, 0xbb, 0x3e, 0xbe, 0x5d, 0x3e, 0x99, 0xb1, 0x25, 0xed, 0x7a, 0x53, 0x5b, 0x0a,
	0x2d, 0x75, 0x6a, 0x4b, 0x49, 0x93, 0xfc, 0x6f, 0xe8, 0x88, 0x8e, 0x8d, 0x90, 0xf8, 0xab, 0xb4,
	0xb3, 0x1c, 0xef, 0x64, 0x64, 0x72, 0xc9, 0x7b, 0x98, 0x5f, 0x64, 0x63, 0xb4, 0x97, 0x68, 0xa9,
	0xb6, 0x66, 0xe3, 0x61, 0x5e, 0x9c, 0x40, 0xd7, 0x4f, 0xba, 0x85, 0x74, 0x6d, 0xa6, 0x7b, 0x18,
	0x6f, 0xc7, 0xe2, 0xdc, 0x2a, 0x51, 0x2d, 0xa7, 0xab, 0x32, 0xd5, 0x73, 0xe5, 0x2a, 0x51, 0x43,
	0xa6, 0xab, 0x32, 0x35, 0x65, 0xc9, 0x2a, 0x11, 0x6f, 0x6a, 0x41, 0x97, 0xc0, 0x57, 0x2c, 0x35,
	0xd3, 0x78, 0x2b, 0xab, 0x36, 0x9f, 0x61, 0x11, 0x9b, 0x49, 0x4d, 0xe4, 0x76, 0x6e, 0x41, 0xa6,
	0x12, 0x18, 0xdf, 0xa9, 0x98, 0x95, 0x1b, 0x7e, 0x00, 0x90, 0xb6, 0x70, 0x44, 0x60, 0x5c, 0xe8,
	0x0c, 0xc7, 0xfb, 0x05, 0x79, 0x6a, 0x5a, 0xf6, 0x76, 0x95, 0xa6, 0x95, 0xde, 0xcf, 0xd2, 0xb4,
	0x8a, 0xeb, 0x58, 0x6a, 0x22, 0x8a, 0x25, 0x45, 0x93, 0x4c, 0x29, 0xa8, 0x68, 0x92, 0xab, 0xaa,
	0xe4, 0x72, 0x91, 0x91, 0x94, 0xe5, 0x99, 0xbc, 0xaa, 0x2c, 0xcf, 0xa5, 0xc3, 0x23, 0x04, 0x36,
	0x93, 0xcc, 0x52, 0x60, 0xcb, 0x72, 0x5c, 0xd1, 0xcf, 0x47, 0xdd, 0x17, 0xed, 0xc3, 0xc7, 0x26,
	0x33, 0x2f, 0x3b, 0xf8, 0x67, 0xd7, 0x7f, 0x7e, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x7b, 0x24, 0xf5,
	0xde, 0x01, 0x1b, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DigitalServiceClient is the client API for DigitalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DigitalServiceClient interface {
	//用户实名注册
	RealRegister(ctx context.Context, in *RealRegisterRequest, opts ...grpc.CallOption) (*RealRegisterResponse, error)
	//素材上传致信链
	DigitalImageUpload(ctx context.Context, in *DigitalImageUploadRequest, opts ...grpc.CallOption) (*DigitalImageUploadResponse, error)
	//发行数字藏品
	NftPublishDigital(ctx context.Context, in *NftPublishDigitalRequest, opts ...grpc.CallOption) (*NftPublishDigitalResponse, error)
	//发行数字藏品系列
	NftSeriesClaim(ctx context.Context, in *NftSeriesClaimRequest, opts ...grpc.CallOption) (*NftSeriesClaimResponse, error)
	//查询数字藏品列表(系列查询)
	NftSeriesList(ctx context.Context, in *NftSeriesListRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error)
	//查询数字藏品详细信息
	NftInfo(ctx context.Context, in *NftInfoRequest, opts ...grpc.CallOption) (*NftInfoResponse, error)
	//查询各类结果接口
	NftSearchResult(ctx context.Context, in *NftSearchResultRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error)
	//nft积分查询接口
	NftPointQuery(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftPointQueryResponse, error)
	//nft购买
	NftBuy(ctx context.Context, in *NftBuyRequest, opts ...grpc.CallOption) (*NftBuyResponse, error)
	//nft销售状态变更
	NftStatus(ctx context.Context, in *NftStatusRequest, opts ...grpc.CallOption) (*NftStatusResponse, error)
	//新增数字藏品到瑞鹏平台数据
	NftCreate(ctx context.Context, in *NftCreateRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//更新瑞鹏平台数据藏品
	NftUpdate(ctx context.Context, in *NftUpdateRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//删除瑞鹏平台数据藏品
	NftDelete(ctx context.Context, in *NftDeleteRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//展示瑞鹏平台数据藏品
	NftRuiPengList(ctx context.Context, in *NftRuiPengListRequest, opts ...grpc.CallOption) (*NftRuiPengListResponse, error)
	//瑞鹏平台数据藏品详情数据
	NftRuiPengDetail(ctx context.Context, in *NftRuiPengDetailRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error)
	//判断用户是否获取openid
	UserOpenid(ctx context.Context, in *UserOpenidRequest, opts ...grpc.CallOption) (*UserOpenidResponse, error)
	//获取openid
	UserAuthOpenid(ctx context.Context, in *UserAuthOpenidRequest, opts ...grpc.CallOption) (*UserAuthOpenidResponse, error)
	//用户购买或免费领取数字藏品
	UserBuyNft(ctx context.Context, in *UserBuyNftRequest, opts ...grpc.CallOption) (*UserBuyNftResponse, error)
	//数字藏品订单支付
	UserNftPay(ctx context.Context, in *UserNftPayRequest, opts ...grpc.CallOption) (*UserNftPayResponse, error)
	//支付结果领取nft
	NftPayResultPick(ctx context.Context, in *NftPayResultPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
}

type digitalServiceClient struct {
	cc *grpc.ClientConn
}

func NewDigitalServiceClient(cc *grpc.ClientConn) DigitalServiceClient {
	return &digitalServiceClient{cc}
}

func (c *digitalServiceClient) RealRegister(ctx context.Context, in *RealRegisterRequest, opts ...grpc.CallOption) (*RealRegisterResponse, error) {
	out := new(RealRegisterResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/RealRegister", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) DigitalImageUpload(ctx context.Context, in *DigitalImageUploadRequest, opts ...grpc.CallOption) (*DigitalImageUploadResponse, error) {
	out := new(DigitalImageUploadResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/DigitalImageUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPublishDigital(ctx context.Context, in *NftPublishDigitalRequest, opts ...grpc.CallOption) (*NftPublishDigitalResponse, error) {
	out := new(NftPublishDigitalResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftPublishDigital", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSeriesClaim(ctx context.Context, in *NftSeriesClaimRequest, opts ...grpc.CallOption) (*NftSeriesClaimResponse, error) {
	out := new(NftSeriesClaimResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftSeriesClaim", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSeriesList(ctx context.Context, in *NftSeriesListRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error) {
	out := new(NftSeriesListResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftSeriesList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftInfo(ctx context.Context, in *NftInfoRequest, opts ...grpc.CallOption) (*NftInfoResponse, error) {
	out := new(NftInfoResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSearchResult(ctx context.Context, in *NftSearchResultRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error) {
	out := new(NftSearchResultResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftSearchResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPointQuery(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftPointQueryResponse, error) {
	out := new(NftPointQueryResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftPointQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftBuy(ctx context.Context, in *NftBuyRequest, opts ...grpc.CallOption) (*NftBuyResponse, error) {
	out := new(NftBuyResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftStatus(ctx context.Context, in *NftStatusRequest, opts ...grpc.CallOption) (*NftStatusResponse, error) {
	out := new(NftStatusResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftCreate(ctx context.Context, in *NftCreateRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftUpdate(ctx context.Context, in *NftUpdateRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftDelete(ctx context.Context, in *NftDeleteRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftRuiPengList(ctx context.Context, in *NftRuiPengListRequest, opts ...grpc.CallOption) (*NftRuiPengListResponse, error) {
	out := new(NftRuiPengListResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftRuiPengList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftRuiPengDetail(ctx context.Context, in *NftRuiPengDetailRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error) {
	out := new(NftRuiPengDetailResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftRuiPengDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserOpenid(ctx context.Context, in *UserOpenidRequest, opts ...grpc.CallOption) (*UserOpenidResponse, error) {
	out := new(UserOpenidResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/UserOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserAuthOpenid(ctx context.Context, in *UserAuthOpenidRequest, opts ...grpc.CallOption) (*UserAuthOpenidResponse, error) {
	out := new(UserAuthOpenidResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/UserAuthOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserBuyNft(ctx context.Context, in *UserBuyNftRequest, opts ...grpc.CallOption) (*UserBuyNftResponse, error) {
	out := new(UserBuyNftResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/UserBuyNft", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserNftPay(ctx context.Context, in *UserNftPayRequest, opts ...grpc.CallOption) (*UserNftPayResponse, error) {
	out := new(UserNftPayResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/UserNftPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPayResultPick(ctx context.Context, in *NftPayResultPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/ctc.DigitalService/NftPayResultPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DigitalServiceServer is the server API for DigitalService service.
type DigitalServiceServer interface {
	//用户实名注册
	RealRegister(context.Context, *RealRegisterRequest) (*RealRegisterResponse, error)
	//素材上传致信链
	DigitalImageUpload(context.Context, *DigitalImageUploadRequest) (*DigitalImageUploadResponse, error)
	//发行数字藏品
	NftPublishDigital(context.Context, *NftPublishDigitalRequest) (*NftPublishDigitalResponse, error)
	//发行数字藏品系列
	NftSeriesClaim(context.Context, *NftSeriesClaimRequest) (*NftSeriesClaimResponse, error)
	//查询数字藏品列表(系列查询)
	NftSeriesList(context.Context, *NftSeriesListRequest) (*NftSeriesListResponse, error)
	//查询数字藏品详细信息
	NftInfo(context.Context, *NftInfoRequest) (*NftInfoResponse, error)
	//查询各类结果接口
	NftSearchResult(context.Context, *NftSearchResultRequest) (*NftSearchResultResponse, error)
	//nft积分查询接口
	NftPointQuery(context.Context, *NftPointQueryRequest) (*NftPointQueryResponse, error)
	//nft购买
	NftBuy(context.Context, *NftBuyRequest) (*NftBuyResponse, error)
	//nft销售状态变更
	NftStatus(context.Context, *NftStatusRequest) (*NftStatusResponse, error)
	//新增数字藏品到瑞鹏平台数据
	NftCreate(context.Context, *NftCreateRequest) (*NftResponse, error)
	//更新瑞鹏平台数据藏品
	NftUpdate(context.Context, *NftUpdateRequest) (*NftResponse, error)
	//删除瑞鹏平台数据藏品
	NftDelete(context.Context, *NftDeleteRequest) (*NftResponse, error)
	//展示瑞鹏平台数据藏品
	NftRuiPengList(context.Context, *NftRuiPengListRequest) (*NftRuiPengListResponse, error)
	//瑞鹏平台数据藏品详情数据
	NftRuiPengDetail(context.Context, *NftRuiPengDetailRequest) (*NftRuiPengDetailResponse, error)
	//判断用户是否获取openid
	UserOpenid(context.Context, *UserOpenidRequest) (*UserOpenidResponse, error)
	//获取openid
	UserAuthOpenid(context.Context, *UserAuthOpenidRequest) (*UserAuthOpenidResponse, error)
	//用户购买或免费领取数字藏品
	UserBuyNft(context.Context, *UserBuyNftRequest) (*UserBuyNftResponse, error)
	//数字藏品订单支付
	UserNftPay(context.Context, *UserNftPayRequest) (*UserNftPayResponse, error)
	//支付结果领取nft
	NftPayResultPick(context.Context, *NftPayResultPickRequest) (*NftResponse, error)
}

// UnimplementedDigitalServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDigitalServiceServer struct {
}

func (*UnimplementedDigitalServiceServer) RealRegister(ctx context.Context, req *RealRegisterRequest) (*RealRegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RealRegister not implemented")
}
func (*UnimplementedDigitalServiceServer) DigitalImageUpload(ctx context.Context, req *DigitalImageUploadRequest) (*DigitalImageUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DigitalImageUpload not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPublishDigital(ctx context.Context, req *NftPublishDigitalRequest) (*NftPublishDigitalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPublishDigital not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSeriesClaim(ctx context.Context, req *NftSeriesClaimRequest) (*NftSeriesClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSeriesClaim not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSeriesList(ctx context.Context, req *NftSeriesListRequest) (*NftSeriesListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSeriesList not implemented")
}
func (*UnimplementedDigitalServiceServer) NftInfo(ctx context.Context, req *NftInfoRequest) (*NftInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftInfo not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSearchResult(ctx context.Context, req *NftSearchResultRequest) (*NftSearchResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSearchResult not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPointQuery(ctx context.Context, req *NftPointQueryRequest) (*NftPointQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPointQuery not implemented")
}
func (*UnimplementedDigitalServiceServer) NftBuy(ctx context.Context, req *NftBuyRequest) (*NftBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftBuy not implemented")
}
func (*UnimplementedDigitalServiceServer) NftStatus(ctx context.Context, req *NftStatusRequest) (*NftStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftStatus not implemented")
}
func (*UnimplementedDigitalServiceServer) NftCreate(ctx context.Context, req *NftCreateRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftCreate not implemented")
}
func (*UnimplementedDigitalServiceServer) NftUpdate(ctx context.Context, req *NftUpdateRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftUpdate not implemented")
}
func (*UnimplementedDigitalServiceServer) NftDelete(ctx context.Context, req *NftDeleteRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftDelete not implemented")
}
func (*UnimplementedDigitalServiceServer) NftRuiPengList(ctx context.Context, req *NftRuiPengListRequest) (*NftRuiPengListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftRuiPengList not implemented")
}
func (*UnimplementedDigitalServiceServer) NftRuiPengDetail(ctx context.Context, req *NftRuiPengDetailRequest) (*NftRuiPengDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftRuiPengDetail not implemented")
}
func (*UnimplementedDigitalServiceServer) UserOpenid(ctx context.Context, req *UserOpenidRequest) (*UserOpenidResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserOpenid not implemented")
}
func (*UnimplementedDigitalServiceServer) UserAuthOpenid(ctx context.Context, req *UserAuthOpenidRequest) (*UserAuthOpenidResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAuthOpenid not implemented")
}
func (*UnimplementedDigitalServiceServer) UserBuyNft(ctx context.Context, req *UserBuyNftRequest) (*UserBuyNftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBuyNft not implemented")
}
func (*UnimplementedDigitalServiceServer) UserNftPay(ctx context.Context, req *UserNftPayRequest) (*UserNftPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserNftPay not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPayResultPick(ctx context.Context, req *NftPayResultPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPayResultPick not implemented")
}

func RegisterDigitalServiceServer(s *grpc.Server, srv DigitalServiceServer) {
	s.RegisterService(&_DigitalService_serviceDesc, srv)
}

func _DigitalService_RealRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RealRegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).RealRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/RealRegister",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).RealRegister(ctx, req.(*RealRegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_DigitalImageUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DigitalImageUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).DigitalImageUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/DigitalImageUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).DigitalImageUpload(ctx, req.(*DigitalImageUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPublishDigital_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPublishDigitalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPublishDigital(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftPublishDigital",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPublishDigital(ctx, req.(*NftPublishDigitalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSeriesClaim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSeriesClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSeriesClaim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftSeriesClaim",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSeriesClaim(ctx, req.(*NftSeriesClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSeriesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSeriesListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSeriesList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftSeriesList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSeriesList(ctx, req.(*NftSeriesListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftInfo(ctx, req.(*NftInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSearchResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSearchResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSearchResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftSearchResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSearchResult(ctx, req.(*NftSearchResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPointQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPointQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPointQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftPointQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPointQuery(ctx, req.(*NftPointQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftBuy(ctx, req.(*NftBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftStatus(ctx, req.(*NftStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftCreate(ctx, req.(*NftCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftUpdate(ctx, req.(*NftUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftDelete(ctx, req.(*NftDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftRuiPengList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftRuiPengListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftRuiPengList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftRuiPengList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftRuiPengList(ctx, req.(*NftRuiPengListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftRuiPengDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftRuiPengDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftRuiPengDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftRuiPengDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftRuiPengDetail(ctx, req.(*NftRuiPengDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserOpenidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/UserOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserOpenid(ctx, req.(*UserOpenidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserAuthOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAuthOpenidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserAuthOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/UserAuthOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserAuthOpenid(ctx, req.(*UserAuthOpenidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserBuyNft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBuyNftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserBuyNft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/UserBuyNft",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserBuyNft(ctx, req.(*UserBuyNftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserNftPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserNftPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserNftPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/UserNftPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserNftPay(ctx, req.(*UserNftPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPayResultPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPayResultPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPayResultPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.DigitalService/NftPayResultPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPayResultPick(ctx, req.(*NftPayResultPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DigitalService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.DigitalService",
	HandlerType: (*DigitalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RealRegister",
			Handler:    _DigitalService_RealRegister_Handler,
		},
		{
			MethodName: "DigitalImageUpload",
			Handler:    _DigitalService_DigitalImageUpload_Handler,
		},
		{
			MethodName: "NftPublishDigital",
			Handler:    _DigitalService_NftPublishDigital_Handler,
		},
		{
			MethodName: "NftSeriesClaim",
			Handler:    _DigitalService_NftSeriesClaim_Handler,
		},
		{
			MethodName: "NftSeriesList",
			Handler:    _DigitalService_NftSeriesList_Handler,
		},
		{
			MethodName: "NftInfo",
			Handler:    _DigitalService_NftInfo_Handler,
		},
		{
			MethodName: "NftSearchResult",
			Handler:    _DigitalService_NftSearchResult_Handler,
		},
		{
			MethodName: "NftPointQuery",
			Handler:    _DigitalService_NftPointQuery_Handler,
		},
		{
			MethodName: "NftBuy",
			Handler:    _DigitalService_NftBuy_Handler,
		},
		{
			MethodName: "NftStatus",
			Handler:    _DigitalService_NftStatus_Handler,
		},
		{
			MethodName: "NftCreate",
			Handler:    _DigitalService_NftCreate_Handler,
		},
		{
			MethodName: "NftUpdate",
			Handler:    _DigitalService_NftUpdate_Handler,
		},
		{
			MethodName: "NftDelete",
			Handler:    _DigitalService_NftDelete_Handler,
		},
		{
			MethodName: "NftRuiPengList",
			Handler:    _DigitalService_NftRuiPengList_Handler,
		},
		{
			MethodName: "NftRuiPengDetail",
			Handler:    _DigitalService_NftRuiPengDetail_Handler,
		},
		{
			MethodName: "UserOpenid",
			Handler:    _DigitalService_UserOpenid_Handler,
		},
		{
			MethodName: "UserAuthOpenid",
			Handler:    _DigitalService_UserAuthOpenid_Handler,
		},
		{
			MethodName: "UserBuyNft",
			Handler:    _DigitalService_UserBuyNft_Handler,
		},
		{
			MethodName: "UserNftPay",
			Handler:    _DigitalService_UserNftPay_Handler,
		},
		{
			MethodName: "NftPayResultPick",
			Handler:    _DigitalService_NftPayResultPick_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/digital.proto",
}

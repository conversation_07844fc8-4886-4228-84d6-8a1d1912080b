package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"order-center/models"
	proto "order-center/proto/oc"
	"reflect"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestRefundOrderService_RefundOrderApply(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *proto.RefundOrderApplyRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "RefundOrderApply",
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{}

			v := []byte(`{"order_id":"","external_order_id":"27010043888111216","reason":"商家其他原因","refund_type":1,"refund_order_goods_data":null,"shop_id":"","full_refund":1,"refund_remark":"","refund_amount":0,"pictures":"","operation_user":"美团客人","res_type":"等待处理中","applyOpUserType":"","order_from":2,"operation_type":"用户申请退款","refund_order_sn":"","channel_id":2,"refund_code":"","is_cancal_order":0,"old_refund_sn":"45246448265"}`)
			params := new(proto.RefundOrderApplyRequest)
			json.Unmarshal(v, params)
			got, err := r.RefundOrderApply(tt.args.ctx, params)
			if err != nil {
				t.Errorf("RefundOrderService.RefundOrderApply() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestRefundOrderService_RefundOrderAnswer(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *proto.RefundOrderAnswerRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "应答售后单",
			args: args{
				ctx: context.Background(),
				params: &proto.RefundOrderAnswerRequest{
					OrderId:         "4000000000804066",
					ExternalOrderId: "4000000000804066",
					RefundOrderSn:   "50000002986",
					Reason:          "同意退款",
					ResultType:      1,
					ResultTypeNote:  "商家同意退款",
					OperationType:   "huangzw",
					OperationUser:   "商家同意退款",
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{}
			jsonstr := `{"order_id":"","external_order_id":"27010322156968102","refund_order_sn":"44131065609","reason":"3","result_type":1,"operation_type":"商家同意退款","operation_user":"","result_type_note":"商家同意退款","old_refund_sn":""}`
			json.Unmarshal([]byte(jsonstr), &tt.args.params)
			got, err := r.RefundOrderAnswer(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("RefundOrderService.RefundOrderAnswer() error = %v", err)
				return
			}
			t.Log(got)
		})
	}
}

func TestRefundOrderService_RefundOrderCancel(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx    context.Context
		params *proto.RefundOrderCancelRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		{name: "RefundOrderCancel"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{}

			params := new(proto.RefundOrderCancelRequest)

			params.RefundOrderSn = "1591264865464685"
			params.ResType = "用户撤销售后单"
			params.OperationType = "用户撤销售后单"
			params.OperationUser = "234sdfsdsdf"

			got, err := r.RefundOrderCancel(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("RefundOrderService.RefundOrderCancel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}

func TestRefundOrderService_RefundOrderPay(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *proto.RefundOrderPayRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "RefundOrderPay",
			args: args{
				ctx: context.Background(),
				params: &proto.RefundOrderPayRequest{
					RefundOrderSn: "50000001134",
					ResType:       "商家发起退款",
					OperationType: "商家发起退款",
				},
			},
		},
	}
	kit.IsDebug = true
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{}
			got, err := r.RefundOrderPay(tt.args.ctx, tt.args.params)
			if err != nil {
				t.Errorf("RefundOrderService.RefundOrderPay() error = %v", err)
				return
			}
			t.Log(kit.JsonEncode(got))
		})
	}
}

func TestRefundOrderService_GetTest(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		orderSn string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{name: "GetTest"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			orderSn := ""

			var refundOrder []models.RefundOrder
			db := GetDBConn()
			db.Where("order_sn = ? and refund_state in(1,5,6,7)", orderSn).Find(&refundOrder)
			if len(refundOrder) > 0 {
				fmt.Println("请把所有退款处理完再发起退款！")
			}
			fmt.Println(refundOrder)
		})
	}
}

func TestSetRefundOrderLog(t *testing.T) {
	type args struct {
		externalOrderId string
		reason          string
		pictures        string
		applyOpUserType string
		operationType   string
		notifyType      string
		resType         string
		refundSn        string
		operationUser   string
	}
	tests := []struct {
		name      string
		args      args
		wantModel models.RefundOrderLog
	}{
		// TODO: Add test cases.
		{name: "退款日志"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotModel := SetRefundOrderLog("2131030952397548765", "", "", "1", "商家同意", "agree", "agree", "21312321321", "2332"); !reflect.DeepEqual(gotModel, tt.wantModel) {
				t.Errorf("SetRefundOrderLog() = %v, want %v", gotModel, tt.wantModel)
			}
		})
	}
}

func TestRefundOrderService_SaveMtRefundOrderData(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *proto.OrderRetrunRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		{name: "SaveMtRefundOrderData"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{
				CommonService: tt.fields.CommonService,
			}

			params := new(proto.OrderRetrunRequest)
			params.OrderId = "3300852013041931654"
			params.RefundId = "66364102473"
			params.Reason = "部分退款部分退款"
			params.Money = "0.02"
			params.Pictures = ""
			params.NotifyType = "agree"
			params.ResType = "终审已申诉"
			//params.RefundGoodsOrders=[]
			params.Status = "21"
			params.ApplyOpUserType = "6"
			params.LogisticsInfo = ""
			params.OrderFrom = 2
			params.RefunType = 2
			params.OperationType = ""
			params.Operationer = ""
			params.ApplyType = "订单取消自动确认退款"
			params.ServiceType = "2"

			got, err := r.SaveMtRefundOrderData(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("RefundOrderService.SaveMtRefundOrderData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RefundOrderService.SaveMtRefundOrderData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRefundOrderService_TimingProcessingMtRefundOrderData(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		CommonService CommonService
	}
	type args struct {
		isRealTime bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{name: "TimingProcessingMtRefundOrderData"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TimingProcessingMtRefundOrderData(false); got != tt.want {
				t.Errorf("RefundOrderService.TimingProcessingMtRefundOrderData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRefundOrderService_CancelOrderByJd(t *testing.T) {
	type fields struct {
		BaseService   BaseService
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *proto.CancelOrderByJdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		{name: "CancelOrderByJd"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			params := new(proto.CancelOrderByJdRequest)
			params.ExternalOrderSn = "2022083550000732"

			got, err := r.CancelOrderByJd(tt.args.ctx, params)
			if (err != nil) != tt.wantErr {
				t.Errorf("RefundOrderService.CancelOrderByJd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RefundOrderService.CancelOrderByJd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderService_GetRefundOrderProducts(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetOneOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetRefundOrderProductResponse
		wantErr bool
	}{
		{
			name: "TestOrderService_GetRefundOrderProducts",
			args: args{
				ctx: context.TODO(),
				in: &proto.GetOneOrderRequest{
					OrderSn: "1606642656040703",
				},
			},
		}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := o.GetRefundOrderProducts(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRefundOrderProducts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRefundOrderProducts() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAddReturnOrderToTencent(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "TestAddReturnOrderToTencent"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AddReturnOrderToTencent("4000000001701325")
		})
	}
}

func TestRefundOrderService_ThirdRefundGoodSplit(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		refundOrderGoods []*models.RefundOrderProduct
	}
	//查询退款单商品
	/*refundGoodJson := []byte(`[{"Id":0,"RefundSn":"50000010432","SkuId":"1031469099","OrderProductId":0,"ParentSkuId":"","ProductName":"组合-子龙货号正常-pw","ProductType":3,"ProductPrice":639,"MarkingPrice":639,"Quantity":2,"Tkcount":2,"RefundAmount":"9.44","RefundPrice":472,"Itemcode":"","Skucode":"","OcId":"","Barcode":"","Spec":"","BoxPrice":0,"BoxNum":0,"SubBizOrderId":"","VerifyCodes":"","CreateTime":"0001-01-01T00:00:00Z","UpdateTime":"0001-01-01T00:00:00Z"}]`)
	var refundOrderGood []*models.RefundOrderProduct
	_ = json.Unmarshal(refundGoodJson, &refundOrderGood)*/
	refundOrderGood := GetRefundOrderProductByRefundSn("50000132357", "")
	tests := []struct {
		name                string
		fields              fields
		args                args
		wantSplitRefundGood []*models.RefundOrderThirdProduct
		wantErr             bool
	}{
		// TODO: Add test cases.
		{
			name: "退款拆单",
			args: args{
				refundOrderGoods: refundOrderGood,
			},
			fields: fields{
				CommonService: CommonService{
					session:   GetDBConn().NewSession(),
					orderMain: GetOrderMainByOrderSn("************9696"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			gotSplitRefundGood, err := r.ThirdRefundGoodSplit(tt.args.refundOrderGoods)
			//_, err = r.session.Insert(gotSplitRefundGood)
			if (err != nil) != tt.wantErr {
				t.Errorf("ThirdRefundGoodSplit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSplitRefundGood, tt.wantSplitRefundGood) {
				t.Errorf("ThirdRefundGoodSplit() gotSplitRefundGood = %v, want %v", gotSplitRefundGood, tt.wantSplitRefundGood)
			}
		})
	}
}

//RefundOrderFinish

func TestRefundOrderService_RefundOrderFinish(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		Ctx   context.Context
		Param proto.RefundOrderPayVo
	}
	tests := []struct {
		name                string
		fields              fields
		args                args
		wantSplitRefundGood []*models.RefundOrderThirdProduct
		wantErr             bool
	}{
		// TODO: Add test cases.
		{
			name: "退款拆单",
			args: args{
				Ctx: context.Background(),
				Param: proto.RefundOrderPayVo{
					OrderSn:       "1213131",
					ResType:       "2",
					OperationType: "3",
					OperationUser: "pengwei",
					Reason:        "ceshi",
					IpAddr:        "测试完成",
					UserName:      "pengwei",
					UserNo:        "o_wne",
				},
			},
			fields: fields{
				CommonService: CommonService{
					session:   GetDBConn().NewSession(),
					orderMain: GetOrderMainByOrderSn("4100000007086444"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			gotSplitRefundGood, err := r.RefundOrderFinish(tt.args.Ctx, &tt.args.Param)
			//_, err = r.session.Insert(gotSplitRefundGood)
			if (err != nil) != tt.wantErr {
				t.Errorf("ThirdRefundGoodSplit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSplitRefundGood, tt.wantSplitRefundGood) {
				t.Errorf("ThirdRefundGoodSplit() gotSplitRefundGood = %v, want %v", gotSplitRefundGood, tt.wantSplitRefundGood)
			}
		})
	}
}

func TestRefundOrderService_BatchRefundDeliveryFee(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx context.Context

		params *proto.BatchRefundDeliveryFeeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		{name: "批量退配送费",
			args: args{
				ctx: context.Background(),
				params: &proto.BatchRefundDeliveryFeeRequest{
					FileUrl:  "http://file.vetscloud.com/f09d278052fdd8c630c1ac0a5bf4215a.xlsx",
					UserNo:   "1",
					UserName: "1",
					UserIp:   "2",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := r.BatchRefundDeliveryFee(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchRefundDeliveryFee() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestRefundOrderService_GetRefundDeliveryFee(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *proto.GerRefundDeliveryFeeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GerRefundDeliveryFeeResponse
		wantErr bool
	}{
		{name: "获取退配送费记录",
			args: args{
				ctx: context.Background(),
				params: &proto.GerRefundDeliveryFeeRequest{
					Keyword: "",
				},
			}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := r.GetRefundDeliveryFee(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRefundDeliveryFee() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestRefundOrderService_ExportRefundDeliveryFee(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx    context.Context
		params *proto.GerRefundDeliveryFeeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.ExportRefundDeliveryFeeResponse
		wantErr bool
	}{
		{name: "导出退配送费记录",
			args: args{
				ctx: context.Background(),
				params: &proto.GerRefundDeliveryFeeRequest{
					Keyword: "",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			got, err := r.ExportRefundDeliveryFee(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportRefundDeliveryFee() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestRefundOrderService_SplitOrderRefundGoods(t *testing.T) {
	type fields struct {
		CommonService CommonService
	}
	type args struct {
		ctx     context.Context
		request *proto.RefundOrderApplyRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.SplitOrderRefundGoodsResponse
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RefundOrderService{
				CommonService: tt.fields.CommonService,
			}
			jsonstr := `{"order_id":"9964128297517169","external_order_id":"9964128297517169","reason":"章思新单55555：退款a   (1.25*2)   =2.5","refund_type":1,"refund_order_goods_data":[{"sku_id":"92","quantity":2,"refund_amount":"2.5","sub_biz_order_id":"","goods_name":"暖阳-TCCO0027猫胸背牵绳 水粉-S 水粉","spec":"规格：水粉","refund_price":1.25,"refund_reality_price":1.25,"app_food_code":"96","promotion_type":0,"order_product_id":9422896,"parent_sku_id":"","platform_sku_id":0}],"shop_id":"","full_refund":1,"refund_remark":"","refund_amount":250,"pictures":"","operation_user":"测试","res_type":"","applyOpUserType":"2","order_from":0,"operation_type":"","refund_order_sn":"","channel_id":1,"refund_code":"","is_cancal_order":0,"old_refund_sn":"","activity_pt_amount":0,"delivery_price":0,"service_type":""}`
			json.Unmarshal([]byte(jsonstr), &tt.args.request)
			got, err := r.SplitOrderRefundGoods(tt.args.ctx, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("SplitOrderRefundGoods(%v, %v)", tt.args.ctx, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "SplitOrderRefundGoods(%v, %v)", tt.args.ctx, tt.args.request)
		})
	}
}

package models

type UpetOrders struct {
	OrderId              int     `xorm:"not null pk autoincr comment('订单索引id') INT(11)"`
	OrderSn              int64   `xorm:"not null comment('订单编号') index BIGINT(20)"`
	PaySn                int64   `xorm:"not null comment('支付单号') BIGINT(20)"`
	PaySnold             int64   `xorm:"comment('旧的支付单号') BIGINT(20)"`
	PaySn1               int64   `xorm:"comment('预定订单支付订金时的支付单号') BIGINT(20)"`
	StoreId              int32   `xorm:"not null comment('卖家店铺id') INT(11)"`
	StoreName            string  `xorm:"not null comment('卖家店铺名称') VARCHAR(50)"`
	BuyerId              int32   `xorm:"not null comment('买家id') index INT(11)"`
	BuyerName            string  `xorm:"not null comment('买家姓名') VARCHAR(50)"`
	BuyerEmail           string  `xorm:"comment('买家电子邮箱') VARCHAR(80)"`
	BuyerPhone           string  `xorm:"not null default 0 comment('买家手机') VARCHAR(20)"`
	EncryptMobile        string  `xorm:"not null default 0 comment('加密手机号') VARCHAR(20)"`
	AddTime              int64   `xorm:"not null default 0 comment('订单生成时间') index(idx_add_time_chain_id) INT(10)"`
	AddTimeStamp         int     `xorm:"not null default 0 comment('时间毫秒数') INT(13)"`
	PaymentCode          string  `xorm:"not null default '' comment('支付方式 offline货到付款,online在线付款,ali_native:支付宝移动支付,alipay:支付宝,tenpay:财付通,chinabank:网银在线,predeposit:站内余额支付,wxpay:微信支付,wx_jsapi:微信支付H5,wx_saoma:微信扫码,chain：门店支付') CHAR(10)"`
	PaymentTime          int     `xorm:"default 0 comment('支付(付款)时间') index INT(10)"`
	FinnshedTime         int     `xorm:"not null default 0 comment('订单完成时间') INT(10)"`
	GoodsAmount          float64 `xorm:"not null default 0.00 comment('商品总价格') DECIMAL(10,2)"`
	OrderAmount          float64 `xorm:"not null default 0.00 comment('订单总价格') DECIMAL(10,2)"`
	RcbAmount            float64 `xorm:"not null default 0.00 comment('充值卡支付金额') DECIMAL(10,2)"`
	PdAmount             float64 `xorm:"not null default 0.00 comment('预存款支付金额') DECIMAL(10,2)"`
	ShippingFee          float64 `xorm:"default 0.00 comment('运费') DECIMAL(10,2)"`
	EvaluationState      int     `xorm:"default 0 comment('评价状态 0未评价，1已评价，2已过期未评价') TINYINT(4)"`
	EvaluationAgainState int     `xorm:"not null default 0 comment('追加评价状态 0未评价，1已评价，2已过期未评价') TINYINT(3)"`
	OrderState           int     `xorm:"not null default 10 comment('订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;') index TINYINT(4)"`
	RefundState          int     `xorm:"default 0 comment('退款状态:0是无退款,1是部分退款,2是全部退款') TINYINT(4)"`
	LockState            int     `xorm:"default 0 comment('锁定状态:0是正常,大于0是锁定,默认是0') TINYINT(4)"`
	DeleteState          int     `xorm:"not null default 0 comment('删除状态0未删除1放入回收站2彻底删除') TINYINT(4)"`
	RefundAmount         float64 `xorm:"default 0.00 comment('退款金额') DECIMAL(10,2)"`
	DelayTime            int     `xorm:"default 0 comment('延迟时间,默认为0') INT(10)"`
	OrderFrom            int     `xorm:"not null default 1 comment('1:PC端,2:移动端,3:宠医云,4:ERP,5:智慧门店,6:有赞,7:阿闻宠物,8:阿闻商城') TINYINT(4)"`
	ShippingCode         string  `xorm:"default '' comment('物流单号') VARCHAR(50)"`
	OrderType            int32   `xorm:"default 1 comment('订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售   99助力订单') TINYINT(4)"`
	ApiPayTime           int     `xorm:"default 0 comment('在线支付动作时间,只要向第三方支付平台提交就会更新') INT(10)"`
	ChainId              int32   `xorm:"not null default 0 comment('自提门店ID') index(idx_add_time_chain_id) index INT(10)"`
	ChainCode            int     `xorm:"not null default 0 comment('门店提货码') MEDIUMINT(6)"`
	RptAmount            float64 `xorm:"default 0.00 comment('红包值') DECIMAL(10,2)"`
	TradeNo              string  `xorm:"comment('外部交易订单号') VARCHAR(50)"`
	IsDis                int     `xorm:"not null default 0 comment('是否分销订单') TINYINT(1)"`
	ChainSenderState     int     `xorm:"not null default 0 comment('门店发货:0待分派,10待确认,20已确认,30已发货') TINYINT(1)"`
	ErpTime              int     `xorm:"comment('ERP同步时间') INT(11)"`
	ErpStatus            int     `xorm:"comment('是否同步') TINYINT(4)"`
	ErpOrderStatus       int     `xorm:"comment('ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)') TINYINT(4)"`
	ErpOrderId           string  `xorm:"default '0' comment('ERP订单号') index VARCHAR(50)"`
	ErpMobile            string  `xorm:"comment('ERP手机号码') VARCHAR(11)"`
	Billcode             string  `xorm:"default '0' comment('管家婆订单') VARCHAR(50)"`
	GjpTime              int     `xorm:"comment('管家婆订单生产时间') INT(11)"`
	WxpayPrepayid        string  `xorm:"VARCHAR(100)"`
	OrderDemolition      int     `xorm:"default 0 comment('是否拆单0,1拆单') index TINYINT(2)"`
	OrderFather          int     `xorm:"default 0 comment('父订单ID') INT(10)"`
	DOrderId             int     `xorm:"default 0 comment('拆单订单ID') INT(10)"`
	DemolitionFrom       int     `xorm:"default 0 comment('1全渠道  2管易 3门店') TINYINT(3)"`
	PaymentFrom          int     `xorm:"default 0 comment('0默认1电银') TINYINT(1)"`
	DisType              int32   `xorm:"default 0 comment('0自主购买，1分享连接，2扫码') TINYINT(2)"`
	IsLive               int     `xorm:"default 0 comment('0默认1直播下单') TINYINT(1)"`
	FirstOrder           int     `xorm:"default 0 comment('0默认1首次下单') TINYINT(1)"`
	WarehouseType        int     `xorm:"default 0 comment('药品仓类型：0:默认否, 1:巨星药品仓') TINYINT(4)"`
	CycleNum             int     `xorm:"not null default 0 comment('周期购期数1 3 6 12期') TINYINT(2)"`
	IsHead               int     `xorm:"not null default 0 comment('是否周期购主订单 1是 0 否') INT(1)"`
	VirtualCardId        int64   `json:"virtual_card_id"` //虚拟卡号
}
type UpetOrdersSimple struct {
	OrderId       int64  `json:"order_id"`
	OrderSn       int64  `json:"order_sn"`
	OrderState    int32  `json:"order_state"`
	VirtualCardId int64  `json:"virtual_card_id"`
	OrderType     int32  `json:"order_type"`
	OrderFather   int64  `json:"order_father"`
	PaymentCode   string `json:"payment_code"` //支付方式名称代码
	RefundState   int32  `json:"refund_state"`
	LockState     int32  `json:"lock_state"`
}

type UpetOrdersPaynotice struct {
	OrderId     int64  `json:"order_id"`
	OrderSn     int64  `json:"order_sn"`
	OrderState  int32  `json:"order_state"`
	OrderType   int32  `json:"order_type"`
	PaymentCode string `json:"payment_code"` //支付方式名称代码
	PaymentTime int    `json:"payment_time"`
	TradeNo     string `json:"trade_no"`
	PaymentFrom int    `json:"payment_from"`
}

const (
	OrderStateCanceled = 0
	OrderStateUnpay    = 10
	OrderStatePayed    = 20
	OrderStateShipped  = 30
	OrderStateReceived = 40
)

var (
	UpetOrderStateMap = map[int]string{
		OrderStateCanceled: "已取消",
		OrderStateUnpay:    "未付款",
		OrderStatePayed:    "已付款",
		OrderStateShipped:  "已发货",
		OrderStateReceived: "已完成",
	}

	UpetOrderFromMap = map[int]string{
		2:  "移动端",
		3:  "APP",
		5:  "阿闻小程序",
		7:  "阿闻宠物",
		8:  "阿闻商城",
		11: "支付宝小程序",
		18: "百度小程序",
	}

	UpetOrdersPaymentCode = map[string]string{
		"alipay":        "支付宝",
		"wxpay":         "微信支付[客户端]",
		"wxpay_jsapi":   "微信支付[jsapi]",
		"alipay_native": "支付宝移动支付",
		"online":        "在线付款",
		"chain":         "门店支付",
		"wx_saoma":      "微信扫码",
		"predeposit":    "站内余额支付",
		"card":          "储值卡支付",
		"ali_native":    "支付宝移动支付",
		"bd_pay":        "百度支付",
		"wx_jsapi":      "微信支付H5",
		"offline":       "货到付款",
		"tenpay":        "财付通",
		"chinabank":     "网银在线",
	}
)

// ParsePromotionTypeFromOrderType
// 将订单类型orderType转化为活动类型 promotion表中的type
// @version v2.9.10 当前仅做了秒杀订单的订单类型转换，后续根据需要进行添加扩展
func ParsePromotionTypeFromOrderType(orderType int32) int32 {
	switch orderType {
	//秒杀订单类型为9 活动类型为11
	case 12:
		return 11
		//默认返回入参
	default:
		return orderType
	}
}

type PhysicalVipCardOrders struct {
	*UpetOrders      `xorm:"extends"`
	*UpetMember      `xorm:"extends"`
	*UpetOrderCommon `xorm:"extends"`
	*UpetOrderGoods  `xorm:"extends"`
	*UpetExpress     `xorm:"extends"`
}

type ReciverInfo struct {
	Address       string `json:"address"`
	EncryptMobile string `json:"encrypt_mobile"`
	MobPhone      string `json:"mob_phone"`
}

type PhysicalVipCardOrders2 struct {
	UpetOrders      *UpetOrdersSimple      `xorm:"extends"`
	UpetOrderGoods  *UpetOrderGoods        `xorm:"extends"`
	UpetOrderCommon *UpetOrderCommonSimple `xorm:"extends"`
	UpetExpress     *UpetExpress           `xorm:"extends"`
}

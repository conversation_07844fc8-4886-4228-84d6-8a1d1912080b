package utils

import "testing"

func TestBD09ToGCJ02(t *testing.T) {
	type args struct {
		lon float64
		lat float64
	}
	tests := []struct {
		name  string
		args  args
		want  float64
		want1 float64
	}{
		// TODO: Add test cases.
		{
			name: "百度转火星",
			args: args{
				lon: 114.027169,
				lat: 22.530015,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := BD09ToGCJ02(tt.args.lon, tt.args.lat)
			if got != tt.want {
				t.Errorf("BD09ToGCJ02() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.<PERSON>rf("BD09ToGCJ02() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGCJ02ToBD09(t *testing.T) {
	type args struct {
		lon float64
		lat float64
	}
	tests := []struct {
		name  string
		args  args
		want  float64
		want1 float64
	}{
		// TODO: Add test cases.
		{
			name: "火星转百度",
			args: args{
				lon: 114.027169,
				lat: 22.530015,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GCJ02ToBD09(tt.args.lon, tt.args.lat)
			if got != tt.want {
				t.Errorf("GCJ02ToBD09() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GCJ02ToBD09() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

package models

import "time"

type TEmployee struct {
	Id           int64     `json:"id" xorm:"pk not null comment('ID') BIGINT 'id'"`
	TenantId     int64     `json:"tenant_id" xorm:"default 'null' comment('所属店铺') BIGINT 'tenant_id'"`
	UserId       int64     `json:"user_id" xorm:"not null default 0 comment('用户ID') BIGINT 'user_id'"`
	OrgId        int64     `json:"org_id" xorm:"not null default 0 comment('部门ID') BIGINT 'org_id'"`
	RoleId       int64     `json:"role_id" xorm:"default 0 comment('角色ID') BIGINT 'role_id'"`
	Account      string    `json:"account" xorm:"not null default '' comment('账号') VARCHAR(11) 'account'"`
	RealName     string    `json:"real_name" xorm:"not null default '' comment('真实姓名') VARCHAR(255) 'real_name'"`
	EmployeeNo   string    `json:"employee_no" xorm:"default '' comment('店员编号') VARCHAR(32) 'employee_no'"`
	Mobile       string    `json:"mobile" xorm:"not null default '' comment('手机;1开头11位纯数字') VARCHAR(11) 'mobile'"`
	State        int       `json:"state" xorm:"default 1 comment('状态;[0-禁用 1-启用]') BIT(1) 'state'"`
	IsDefault    int       `json:"is_default" xorm:"default 0 comment('是否默认员工;[0-否 1-是]') BIT(1) 'is_default'"`
	OperatorName string    `json:"operator_name" xorm:"default '' comment('操作人') VARCHAR(64) 'operator_name'"`
	IsDeleted    int       `json:"is_deleted" xorm:"default 0 comment('删除状态;0-未删除 1-已删除') BIT(1) 'is_deleted'"`
	CreatedBy    int64     `json:"created_by" xorm:"default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime  time.Time `json:"created_time" xorm:"default 'null' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy    int64     `json:"updated_by" xorm:"default 0 comment('最后更新人') BIGINT 'updated_by'"`
	UpdatedTime  time.Time `json:"updated_time" xorm:"default 'null' comment('最后更新时间') DATETIME 'updated_time'"`
	DeleteRemark string    `json:"delete_remark" xorm:"default '' comment('删除备注') VARCHAR(50) 'delete_remark'"`
}
